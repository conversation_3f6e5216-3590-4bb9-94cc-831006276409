#!/bin/bash
set -e  # Exit on error

# Create log directory if it doesn't exist
LOG_DIR="data/tmp/logs"
mkdir -p "$LOG_DIR"

# Generate timestamp for log file
TIMESTAMP=$(date "+%Y%m%d_%H%M%S")
LOG_FILE="$LOG_DIR/preset_answer_$TIMESTAMP.log"

# Function to log output
log_output() {
    echo "$(date "+%Y-%m-%d %H:%M:%S") - $@" | tee -a "$LOG_FILE"
}

# Function to run command and log output
run_command() {
    log_output "Running: $@"
    if ! "$@" 2>&1 | tee -a "$LOG_FILE"; then
        log_output "Error: Command failed: $@"
        exit 1
    fi
}

#run_command inv predictor.preset-answer -m 34
#run_command inv predictor.preset-answer -m 33
#run_command inv predictor.preset-answer -m 18
#run_command inv predictor.preset-answer -m 35
run_command inv predictor.preset-answer -m 15 -p B98 -p B99 --no-predict-crude-elements
run_command inv predictor.preset-answer -m 31
run_command inv predictor.preset-answer -m 32