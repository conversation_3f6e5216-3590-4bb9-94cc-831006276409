debug: False
logging:
  level: "info"

app:
  app_id: "remarkable_hkex_pre"
  secret_key: "hello_remarkable"
  simple_token: "1e8c7f6e1a6bb4c4"
  token_expire: 120
  cookie_secret: "am.i.clear?"
  jwt_secret_key: "rqXnDL0AU1UANK4BZs8LSySrzWlzBk"
  single_sign_limit: False # 单点登录限制，默认不限制
  session_expire: 7200 # session过期时间, 不配置默认600s
  rate_limit_expire: 7200 # 接口访问频率限制时间, 不配默认60s(暂未启用)
  max_fail_count: 100 # session有效期内最大尝试登录次数, 不配默认10次, 超过限制次数继续登录会被持续封禁
  auth:
    label:
      app_id: "internal"
      secret_key: "show-me-the-money"
      token_expire: 300
    roster:
      app_id: "cheftin-roster"
      secret_key: "you-never-know-it"
      token_expire: 300
      url: "http://u.paodingai.com"
    pdfinsight:
      app_id: "pdfinsight"
      secret_key: "show-me-the-money"
      token_expire: 300
      url: "http://scriber-pdfinsight-hkex.b2.cheftin.cn:1080"
      column: 1
web:
  support_http_partial_content: False
  http_port: 8080
  scheme: "http"
  domain: "scriber:8000"
  plugins:
    - "fileapi"
    - "ext_api"
    - "hkex"
    - "mm"
    - "debug"
  apis:
    sync_host: "http://***********:8000"
  pdf_converter: '{{project_root}}/bin/converter/word2pdf.exe'
  tmp_dir: "{{project_root}}/data/tmp/"
  data_dir: '{{project_root}}/data/files/'
  default_question_health: 2
  classes:
    answer_comparer: "remarkable.plugins.hkex.compare_answers_with_count.ScriberAnswerComparerWithCount"
    answer_converter:
      Jura 3.0 Ratio Checking: "remarkable.converter.hkex.RatioConverter"
    answer_inspector:
      LRs: "remarkable.rule.hkex_rules.HkexInspector"  # Rule A
      Jura 2.0 Listing Rules: "remarkable.rule.hkex_rules.HkexInspector"  # Rule B
      Jura 2.0 Additional Rules: "remarkable.rule.hkex_rules.HkexInspector"  # Rule C
      Jura 3.0 Disclosure Checking: "remarkable.rule.hkex_disclosure_rules.HkexDisclosureInspector"  # 合规检查
      POLL: "remarkable.rule.hkex_rules.POLLInspector"  # poll
    answer_predictor:
      LRs: "remarkable.plugins.hkex.check_compliance.PreparePresetAnswer"
      Jura 2.0 Listing Rules: "remarkable.plugins.predict.config.hkex_listing_rule.HKEXListingRulePredictor"
      Additional Documents: "remarkable.plugins.predict.config.hkex_additional_documents.AdditionalDocumentsPredictor"
      Jura3.0 AR Disclosure Checking: "remarkable.plugins.predict.config.disclosure_check.DisclosureCheckPredictor"
  preset_answer: True
  inspect_rules: True
  mode_conflict_treatment: 3
  mode_unlimited_answers: True
  support_multiple_molds: True
  show_merge_answer: True
  show_marker: True
  use_syllabus_inspect: True # 合规检查时是否使用目录模型
  xsrf_cookies: False # xsrf验证，默认关闭
  trust_routes: # 跳过auth&xsrf check的路由列表
    - "/"
    - "/login"
    - "/plugins/debug/.*"
    - "/plugins/hkex/document"
    - "/plugins/hkex/companies"
    - "/plugins/hkex/scrapping_summary/.*"
    - "/plugins/fileapi/file/[0-9]+"
    - "/plugins/fileapi/file/[0-9]+/pdf"
    - "/plugins/fileapi/file/[0-9]+/export"
    - "/plugins/fileapi/file/[0-9]+/pdfinsight"
    - "/plugins/debug/files/[0-9]+/run"
  binary_key: ""
  # 标注环境默认不加密
  encrypted_request_routes: []
  encrypted_response_routes: []
  default_page: 'hkex'


db:
  host: "postgres"
  port: 5432
  dbname: "scriber"
  user: "postgres"
  password: "scriber"
  max_connections: 100

embedding_db:
  host: "{{db.host}}"
  port: "{{db.port}}"
  dbname: "{{db.dbname}}"
  user: "{{db.user}}"
  password: "{{db.password}}"
  max_connections:  "{{db.max_connections}}"

redis:
  host: "redis"
  port: 6379
  password: "scriber"
  db: 1

client:
  autoreload: False
  name: "hkex"
  # remark_progress_type: "level1" # all
  language: "en_US"
  use_new_tag: True
  check_schema_base_type: True
  show_prompt_element_button: True
  show_all_predict_items: True # 是否展示多个预测答案（指多选）
  export_label_data: False # 是否支持导出训练数据
  stat_accuracy: False # 是否支持统计模型正确率
  env: "hkex"
  ocr:
    enable: True
    service: "aliyun" # ocr服务, 可选aliyun pai abbyy
  optimize_text_box: False  # 自动缩框

feature:  # 功能特性相关配置
  new_project: True  # 新建项目
  new_project_popup_default_molds: True  # 新建项目时选择默认Schema
  zip_upload: True  # 上传文件压缩包
  label_tools: True  # 标注工具条显示"元素块提取"
  secondary_submit: True  # 在「别人的答案」基础上提交答案
  answer_batch_edit: True # 答案树支持"批量修改"
  file_list_page:  # 文件列表页相关
    new_buttons: True  # 新建文件夹、上传文件、上传zip的按钮
    compare_button:  True  # 文件列表操作列显示【对比】按钮
  quick_schema_switching: True # 通用标注界面快速切换schema
  sso: True
  restore_user_answer: True
  review_show_element_index: True # review 界面是否显示元素块 index
  process_jura61_module: True

worker:
  app_name: "remarkable"
  default_queue: "celery"
  broker: "redis://:scriber@redis:6379/1"
  crontab_tasks:
    # 每天凌晨5点更新hkex_companies_info表记录
    update_company_info:
      task: "remarkable.worker.hkex_tasks.update_company_info"
      schedule:
        minute: 0
        hour: 5
    # 每天凌晨2:17点爬上市日期
    crawl_listing_date_of_company:
      task: "remarkable.worker.hkex_tasks.crawl_listing_date_of_company"
      schedule:
        minute: 17
        hour: 2

prompter:
  workers: 2
  batch_size: 5000
  auto_build_ignores: "5,*"
  answer_version: 2.2
  training_data_status: "1, 2, 5, 10, 100"
  score_threshold: 0
  top_n: 5
  mode: "v2"
  use_syllabuses: True
  tokenization: null
  context_length: 1
  post_process:
    - 'A12-Biography'
    - 'A12-List of directors'
    - 'A14'
    - 'A20'
    - 'A21-A21.1'
    - 'A22'
    - 'A23'
    - 'A24-Table for related party transaction'
    - 'A26'
    - 'A29-A29.3'
    - 'A8'
  element_classes: # 初步定位时选择的元素块类型 可以在训练时根据文档实际情况和效果调整
    - 'paragraphs'
    - 'tables'
    - 'page_headers'
    - 'shapes'
    - 'images'
    - 'infographics'
    - 'footnotes'
#    - 'page_footers'  # 添加 page_footers 会降低准确率

ai:
  openai:
    api_key: "sk-wXJbGeoxlCirvk0xF373Fe81251c4b7cB450Df6809833c28"
    base_url: "https://oneapi.cheftin.com/v1"
    model: "o3-mini"
    embedding_model: "text-embedding-3-small"
    timeout: 180

prophet:
  package_name: "hkex_predictor"
  config_map:
    LRs: "lrs"
    Jura 2.0 Listing Rules: "list_rule"
    Jura 3.0 Disclosure Checking: "disclosure_check"
    Jura 3.0 Ratio Checking: "ratio_check"
    Jura4 Annual Report ESG: "ar_esg"
    Jura4 ESG Report: "esg"
    Jura5 CG Report: "cg"
    Jura 2.0 Additional Rules: "c_rule"
    helper: "helper"
    HKEX Financial Results Demo: "financial_results"
    Policy-AR-ESG: "policy_ar"
    Policy-ESG: "policy_esg"
    AGM: "agm"
    POLL: "poll_result"
    Jura6 NDDR: "nddr"
    Jura6.1 MR: "jura61_mr"
    Jura6.1 AGM: "jura61_agm"
    Jura6.1 POLL: "jura61_poll"


# jura_version对应的schema_id
schema:
  v1:
    id: 5
    name: 'LRs'
    mode: 'v3'
    model_version: 'jura'
  v2:
    id: 15
    name: 'Jura 2.0 Listing Rules'
    mode: 'v3'
    model_version: 'jura2'
  v2a: # Additional Documents
    id: 12
    name: 'Additional Documents'
    mode: 'v2'
  v3:
    id: 18
    name: 'Jura 2.0 Additional Rules'
    mode: 'v3'
    model_version: 'jura2p'
  v3d:
    id: 22
    name: 'Jura3.0 AR Disclosure Checking'
    mode: 'v2'
    model_version: 'jura3d'
  v3r:
    id: 24
    name: 'Jura 3.0 Ratio Checking'
    mode: 'v2'
    model_version: 'jura3r'
  v3dr:
    id: 26
    name: 'Jura 3.0 Disclosure Checking'
    mode: 'v2'
    model_version: 'v3dr'
    rule_label_map:
      Disclosure1: "Modified opinion"
      Disclosure2: "Potential fraud/internal control breakdown"
      Disclosure3: "Potential legal issue"
      Disclosure4: "Audited or agreed with the auditors"
      Disclosure5: "Statement of profit or loss"
      Disclosure6: "Statement of financial position"
      Disclosure7: "Note of revenue"
      Disclosure8: "Note of taxation"
      Disclosure9: "Note of EPS"
      Disclosure10: "Note of dividend"
      Disclosure11: "Purchase, Sales or Redemption of Listed Securities"
      Disclosure12: "CG Code"
      Disclosure13: "Significant changes in accounting policies"
      Disclosure14: "Prior period adjustment"
      Disclosure15: "Discontinued operation"
      Disclosure16: "Section 436(3) of HKCO"
      Disclosure17: "Money lending, indent trading or proprietary securities trading"
      Disclosure18: "Comment on segment"
      Disclosure19: "Reviewed by AC"

jura21_helper_mold:
  id: 29
  name: 'helper'
  mode: 'v2'
  model_version: 'jura21_helper'


cg_mold:
  id: 28
  name: 'Jura5 CG Report'
  mode: 'v2'
  model_version: 'jura5'


esg_molds:
  AR:
    id: 1
    name: 'Jura4 Annual Report ESG'
    mode: 'v2'  # 效果不好再换v3
    model_version: 'jura4_ar'
  ESG:
    id: 2
    name: 'Jura4 ESG Report'
    mode: 'v2'
    model_version: 'jura4_esg'
  Policy-AR:
    id: 31
    name: 'Policy-AR-ESG'
    mode: 'v2'
    model_version: 'jura6_ar'
  Policy-ESG:
    id: 32
    name: 'Policy-ESG'
    mode: 'v2'
    model_version: 'jura6_esg'

v6_agm_id:
  id: 33
  name: "AGM"
  mode: 'v2'
  model_version: "jura6_agm"

v6_poll_id:
  id: 34
  name: "POLL"
  mode: 'v2'
  model_version: "jura6_poll"


threshold: # 港交所阈值上下限
  RuleA1:
    top: 0.9454
    bottom: 0.1828
  RuleA10A4:
    top: 0.2242
    bottom: 0.0155
  RuleA10A3:
    top: 0.6657
    bottom: 0.0400
  RuleA10A2:
    top: 0.4418
    bottom: 0.0130
  RuleA10A6:
    top: 0.1079
    bottom: 0.0012
  RuleA10A1:
    top: 0.6745
    bottom: 0.0587
  RuleA10A5:
    top: 0.6749
    bottom: 0.1300
  RuleA11A1:
    top: 0.0139
    bottom: 0.0033
  RuleA11A2:
    top: 0.5
    bottom: 0.1
  RuleA11A3:
    top: 0.7176
    bottom: 0.0144
  RuleA12A2:
    top: 0.1757
    bottom: 0.0706
  RuleA12A1:
    top: 0.0521
    bottom: 0.0252
  RuleA13:
    top: 0.2837
    bottom: 0.0058
  RuleA14:
    top: 0.0667
    bottom: 0.0253
  RuleA15:
    top: 0.0451
    bottom: 0.0104
  RuleA16:
    top: 0.3963
    bottom: 0.0429
  RuleA17:
    top: 0.4333
    bottom: 0.020
  RuleA18:
    top: 0.3719
    bottom: 0.0451
  RuleA19A1:
    top: 0.0697
    bottom: 0.0095
  RuleA19A2:
    top: 0.0550
    bottom: 0.0068
  RuleA2:
    top: 0.5542
    bottom: 0.0102
  RuleA20:
    top: 0.0307
    bottom: 0.0145
  RuleA21A1:
    top: 0.0223
    bottom: 0.0126
  RuleA21A2:
    top: 0.0493
    bottom: 0.0114
  RuleA21A3:
    top: 0.0195
    bottom: 0.0045
  RuleA21A4:
    top: 0.0730
    bottom: 0.0114
  RuleA21A5:
    top: 0.0792
    bottom: 0.0047
  RuleA22:
    top: 0.0863
    bottom: 0.0219
  RuleA23:
    top: 0.0069
    bottom: 0.0031
  RuleA24A2:
    top: 0.0596
    bottom: 0.0130
  RuleA24A1:
    top: 0.0776
    bottom: 0.0383
  RuleA25:
    top: 0.0004
    bottom: 0.0002
  RuleA26:
    top: 0.0182
    bottom: 0.0003
  RuleA27:
    top: 0.5
    bottom: 0.1
  RuleA28A3:
    top: 0.0000
    bottom: 0.0000
  RuleA28A2:
    top: 0.0002
    bottom: 0.0001
  RuleA28A1:
    top: 0.0056
    bottom: 0.0010
  RuleA29A1:
    top: 0.0017
    bottom: 0.0004
  RuleA29A2:
    top: 0.0001
    bottom: 0.0001
  RuleA29A3:
    top: 0.0000
    bottom: 0.0000
  RuleA3:
    top: 0.7927
    bottom: 0.0078
  RuleA30:
    top: 0.1900
    bottom: 0.0058
  RuleA31:
    top: 0.2769
    bottom: 0.0066
  RuleA32:
    top: 0.0645
    bottom: 0.0101
  RuleA33:
    top: 0.3382
    bottom: 0.0057
  RuleA4:
    top: 0.2253
    bottom: 0.0134
  RuleA5:
    top: 0.0499
    bottom: 0.0095
  RuleA6:
    top: 0.0890
    bottom: 0.0063
  RuleA7:
    top: 0.2293
    bottom: 0.0045
  RuleA8:
    top: 0.0145
    bottom: 0.0061
  RuleA9:
    top: 0.2398
    bottom: 0.0029

crawler:
  name: 'headstream.tasks.crawl'
  queue: 'crawl'
  notify_to:
#    - '<EMAIL>'
#    - '<EMAIL>'

contact_person_info_url: 'https://www.hkex.com.hk/-/media/HKEX-Market/Listing/Rules-and-Guidance/Other-Resources/Listed-Issuers/Contact-Persons-in-HKEX-Listing-Department-for-Listed-Companies/Excel-Protected-File/listing.xlsx'
isin_info_url: 'https://www.hkex.com.hk/eng/services/trading/securities/securitieslists/ListOfSecurities.xlsx'
director_list_url: 'https://www3.hkexnews.hk/reports/dirsearch/dirlist/Documents/Director_List.xlsx'


notification:
  switch: True
  channel: "hkex_test_exception"
  tail: "@wangyangbin"

# red_flag_threshold just for ext api result_announcement
ratio_check:
  ratio1:
    label: "Cash Ratio"
    red_flag_threshold:
      - ">0.9"
  ratio2:
    label: "Impairment - Revenue Ratio\nImpairment - Total Assets Ratio"
    red_flag_threshold:
      - "≥0.3"
      - "≥0.3"
  ratio3:
    label: "Profit Fluctuation"
    red_flag_threshold:
      - "<-0.3"
  ratio4:
    label: "Significant Investment"
    red_flag_threshold:
      - "≥0.05"
  ratio5:
    label: "Gain/ Loss on Disposal"
    red_flag_threshold:
      - "≥0.3"
      - "≥0.3"
  ratio6:
    label: "Assets Fluctuation"
    red_flag_threshold:
      - "≥0.25"
  ratio7:
    label: "Revenue/ Gross Profit Level"
    red_flag_threshold:
      - "≥0.8"
      - "<HK$50M"
      - "<0"
      - "<0"

email:
  smtp_host: 'smtpdm.aliyun.com'
  smtp_port: 465
  from_addr: '<EMAIL>'
  from_passwd: ''


build:
  docker:
    cli_pipe: # 部署命令
      - "export PDFPARSER_CONFIG_OCR_PAI_CACHE=true"
      - "export PDFPARSER_CONFIG_RPC_CLIENT_PAI_TARGET='bj.cheftin.cn:11090'"
      - "export PDFPARSER_CONFIG_OCR_CACHE_TYPE=minio"
      - "export PDFPARSER_CONFIG_MINIO_URL='bj.cheftin.com:55950'"
      - "export PDFPARSER_CONFIG_MINIO_ACCESS_KEY='xbFjTDx6r48bt7xD'"
      - "export PDFPARSER_CONFIG_MINIO_SECRET_KEY='EXpMYbqpER48T3YLOuPsdh3vrRkbHz9h'"
      - "export PDFPARSER_CONFIG_OCR_ALIYUN_URL='https://ocrapi-advanced.taobao.com/ocrservice/advanced'"
      - "export PDFPARSER_CONFIG_OCR_ALIYUN_APP_KEY='25979225'"
      - "export PDFPARSER_CONFIG_OCR_ALIYUN_APP_SECRET='b9ff9829a22b492d20f6a94804e76e7d'"
      - "export PDFPARSER_CONFIG_OCR_ALIYUN_APP_CODE='7eae1570f9e042a18ed698bc22610eba'"

speedy:
  application: "scriber"
  application_name: "Scriber"
  expiration_timestamp: 0 # 过期所有API
  enable_docs: True
  xsrf_cookies:
    enable: True
    name: "_xsrf"
    header: "X-Csrftoken"
    secure: False
    httponly: True
    samesite: "lax"
    trust_routes:
      - "/"
      - "/login"
      - "/plugins/hkex/document"
      - "/plugins/hkex/companies"
      - "/plugins/fileapi/file/[0-9]+"
      - "/plugins/fileapi/file/[0-9]+/pdf"
      - "/plugins/fileapi/file/[0-9]+/export"
      - "/plugins/fileapi/file/[0-9]+/pdfinsight"
      - "/plugins/debug/files"
      - "/plugins/mm/reload-file"
      - "/plugins/mm/delist-code"
      - "/plugins/mm/rule-query"
      - "/api/v./saml/acs"
      - "/api/v2/files/sync"
  i18n:
    enable: True
    default_locale: "en"
    domain: "Scriber-Backend"
    localedir: "{{project_root}}/i18n/locales"

limit_jura6_new_rule: True
