debug: False
logging:
  level: "info"
  cef:
    device_vendor: "JURA-UAT"
    device_product: "JURA-UAT"
    waf:
      region_id: cn-hangzhou
      access_key_id: LTAI5t7aMLBtkYkYEvpoCz66
      access_key_secret: ******************************
      project: waf-project-1555695663391023-cn-hangzhou
      logstore: waf-logstore

app:
  app_id: "remarkable_hkex_uat_esg"
  secret_key: "hello_remarkable"
  simple_token: "c30d1dab114f3ea9"
  token_expire: 120
  cookie_secret: "ciezaifo?f7an3Le"
  jwt_secret_key: "Om2Vnyu19bBSuy53yBB3Y4hJBG6c4wdK"
  single_sign_limit: False # 单点登录限制，默认不限制
  session_expire: 7200 # session过期时间, 不配置默认600s
  rate_limit_expire: 7200 # 接口访问频率限制时间, 不配默认60s(暂未启用)
  max_fail_count: 10 # session有效期内最大尝试登录次数, 不配默认10次, 超过限制次数继续登录会被持续封禁
  auth:
    label:
      app_id: "internal"
      secret_key: "show-me-the-money"
      token_expire: 300
    roster:
      app_id: "cheftin-roster"
      secret_key: "you-never-know-it"
      token_expire: 300
      url: "http://u.paodingai.com"
    pdfinsight:
      app_id: "pdfinsight"
      secret_key: "show-me-the-money"
      token_expire: 300
      url: "http://scriber-pdfinsight-hkex.b2.cheftin.cn:1080"
      column: 1
web:
  support_http_partial_content: False
  http_port: 8080
  scheme: "https"
  domain: "jura-uat.paodingai.com"
  plugins:
    - "fileapi"
    - "ext_api"
    - "label_assistant"
    - "hkex"
    - "mm"
    - "debug"
  apis:
    sso: "http://tl.paodingai.com/op/admin/login/subsys"
    login_callback: "http://tl.paodingai.com/lrcapi/v1/user/login/by_token"
    get_label_user: "http://tl.paodingai.com/adminuser/%d"
    get_label_user_by_cn_list: "https://tl.paodingai.com/adminuser"
    sync_host: "http://************:55647"
  pdf_converter: "{{project_root}}/bin/converter/word2pdf.exe"
  tmp_dir: "{{project_root}}/data/tmp/"
  data_dir: "{{project_root}}/data/files/"
  default_question_health: 2
  classes:
    answer_comparer: "remarkable.plugins.hkex.compare_answers_with_count.ScriberAnswerComparerWithCount"
    answer_converter:
      Jura 3.0 Ratio Checking: "remarkable.converter.hkex.RatioConverter"
    answer_inspector:
      LRs: "remarkable.rule.hkex_rules.HkexInspector"  # Rule A
      Jura 2.0 Listing Rules: "remarkable.rule.hkex_rules.HkexInspector"  # Rule B
      Jura 2.0 Additional Rules: "remarkable.rule.hkex_rules.HkexInspector"  # Rule C
      Jura 3.0 Disclosure Checking: "remarkable.rule.hkex_disclosure_rules.HkexDisclosureInspector" # 合规检查
      POLL: "remarkable.rule.hkex_rules.POLLInspector"  # poll
    answer_predictor:
      LRs: "remarkable.plugins.hkex.check_compliance.PreparePresetAnswer"
      Jura 2.0 Listing Rules: "remarkable.plugins.predict.config.hkex_listing_rule.HKEXListingRulePredictor"
      Additional Documents: "remarkable.plugins.predict.config.hkex_additional_documents.AdditionalDocumentsPredictor"
      Jura3.0 AR Disclosure Checking: "remarkable.plugins.predict.config.disclosure_check.DisclosureCheckPredictor"
  preset_answer: True
  inspect_rules: True
  mode_conflict_treatment: 3
  mode_unlimited_answers: True
  support_multiple_molds: True
  show_merge_answer: True
  show_marker: True
  use_syllabus_inspect: True # 合规检查时是否使用目录模型
  xsrf_cookies: True # xsrf验证，默认关闭
  trust_routes: # 跳过auth&xsrf check的路由列表
    - "/"
    - "/login"
    - "/plugins/debug/.*"
    - "/plugins/hkex/document"
    - "/plugins/hkex/companies"
    - "/plugins/hkex/scrapping_summary/.*"
    - "/plugins/fileapi/file/[0-9]+"
    - "/plugins/fileapi/file/[0-9]+/pdf"
    - "/plugins/fileapi/file/[0-9]+/export"
    - "/plugins/fileapi/file/[0-9]+/pdfinsight"
    - "/plugins/debug/files/[0-9]+/run"
    - "/plugins/mm/.*"
  binary_key: "0b168d3bb0828b5f6242cb3a9f144a23"
  encrypted_request_routes:
    - 'all'
  encrypted_response_routes:
    - 'all'
  encrypted_skip_routes:
    - "/plugins/debug/.*"
    - "/plugins/hkex/document"
    - "/plugins/hkex/companies"
    - "/plugins/hkex/scrapping_summary/.*"
    - "/plugins/fileapi/file/[0-9]+"
    - "/plugins/fileapi/file/[0-9]+/pdf"
    - "/plugins/fileapi/file/[0-9]+/export"
    - "/plugins/fileapi/file/[0-9]+/pdfinsight"
    - "/saml/acs"
    - "/files/sync"
    - "/plugins/mm/.*"
    - "/metrics"
  encrypted_skip_routes_patterns:
    - "^/external_api/.*"
  default_page: "hkex"
  disable_generic_route: True  # 禁用通用界面路由

db:
  host: "postgres"
  port: 5432
  dbname: "scriber"
  user: "postgres"
  password: "z4v1i1entHEjuat"
  max_connections: 100
  # TLS/SSL connection settings
  sslmode: "verify-ca"
  sslkey: "{{project_root}}/data/ssl/client.key"
  sslcert: "{{project_root}}/data/ssl/client.crt"
  sslrootcert: "{{project_root}}/data/ssl/root.crt"

embedding_db:
  host: "{{db.host}}"
  port: "{{db.port}}"
  dbname: "{{db.dbname}}"
  user: "{{db.user}}"
  password: "{{db.password}}"
  max_connections:  "{{db.max_connections}}"
  # TLS/SSL connection settings
  sslmode: "verify-ca"
  sslkey: "{{project_root}}/data/ssl/client.key"
  sslcert: "{{project_root}}/data/ssl/client.crt"
  sslrootcert: "{{project_root}}/data/ssl/root.crt"

redis:
  host: "redis"
  port: 6379
  password: "scriberuat"
  db: 1

client:
  autoreload: False
  name: "hkex"
  # remark_progress_type: "level1" # all
  language: "en_US"
  use_new_tag: True
  check_schema_base_type: True
  show_prompt_element_button: True
  show_all_predict_items: True # 是否展示多个预测答案（指多选）
  export_label_data: False # 是否支持导出训练数据
  stat_accuracy: False # 是否支持统计模型正确率
  env: "hkex"
  ocr:
    enable: True
    service: "aliyun" # ocr服务, 可选aliyun pai abbyy
  optimize_text_box: False  # 自动缩框

feature:  # 功能特性相关配置
  new_project: True  # 新建项目
  new_project_popup_default_molds: True  # 新建项目时选择默认Schema
  zip_upload: True  # 上传文件压缩包
  label_tools: True  # 标注工具条显示"元素块提取"
  secondary_submit: True  # 在「别人的答案」基础上提交答案
  answer_batch_edit: True # 答案树支持"批量修改"
  file_list_page:  # 文件列表页相关
    new_buttons: True  # 新建文件夹、上传文件、上传zip的按钮
    compare_button:  True  # 文件列表操作列显示【对比】按钮
  quick_schema_switching: True # 通用标注界面快速切换schema
  sso: True

worker:
  app_name: "remarkable"
  default_queue: "celery"
  broker: "redis://:scriberuat@redis:6379/1"
  # 定时任务配置
  crontab_tasks:
    # 每天凌晨3点半同步阿里云的waf日志（cef格式）到cef_history表
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/4342
    waf_block_cef:
      task: remarkable.worker.hkex_tasks.waf_block_cef
      schedule:
        minute: 45
        hour: 3

    # 每天凌晨2:46点爬上市日期
    crawl_listing_date_of_company:
      task: "remarkable.worker.hkex_tasks.crawl_listing_date_of_company"
      schedule:
        minute: 46
        hour: 2

prompter:
  workers: 2
  batch_size: 5000
  auto_build_ignores: "5,*"
  answer_version: 2.2
  training_data_status: "1, 2, 5, 10, 100"
  score_threshold: 0
  top_n: 5
  mode: "v2"
  use_syllabuses: True
  tokenization: null
  context_length: 1
  post_process:
    - "A12-Biography"
    - "A12-List of directors"
    - "A14"
    - "A20"
    - "A21-A21.1"
    - "A22"
    - "A23"
    - "A24-Table for related party transaction"
    - "A26"
    - "A29-A29.3"
    - "A8"

prophet:
  package_name: "hkex_predictor"
  config_map:
    LRs: "lrs"
    Jura 2.0 Listing Rules: "list_rule"
    Jura 3.0 Disclosure Checking: "disclosure_check"
    Jura 3.0 Ratio Checking: "ratio_check"
    Jura4 Annual Report ESG: "ar_esg"
    Jura4 ESG Report: "esg"
    Jura5 CG Report: "cg"
    Jura 2.0 Additional Rules: "c_rule"
    helper: "helper"
    Policy-AR-ESG: "policy_ar"
    Policy-ESG: "policy_esg"
    AGM: "agm"
    POLL: "poll_result"
    Jura6 NDDR: "nddr"
    Jura6.1 MR: "jura61_mr"
    Jura6.1 AGM: "jura61_agm"
    Jura6.1 POLL: "jura61_poll"


ai:
  openai:
    api_key: "sk-wXJbGeoxlCirvk0xF373Fe81251c4b7cB450Df6809833c28"
    base_url: "https://oneapi.cheftin.com/v1"
    model: "o3-mini"
    embedding_model: "text-embedding-3-small"
    timeout: 180

# jura_version对应的schema_id
schema:
  v1:
    id: 5
    name: "LRs"
    mode: "v3"
    model_version: "jura"
  v2:
    id: 15
    name: "Jura 2.0 Listing Rules"
    mode: "v3"
    model_version: "jura2"
  v2a: # Additional Documents
    id: 12
    name: "Additional Documents"
    mode: "v2"
  v3:
    id: 18
    name: "Jura 2.0 Additional Rules"
    mode: "v3"
    model_version: "jura2p"
  v3d:
    id: 22
    name: "Jura3.0 AR Disclosure Checking"
    mode: "v2"
    model_version: "jura3d"
  v3r:
    id: 24
    name: "Jura 3.0 Ratio Checking"
    mode: "v2"
    model_version: "jura3r"
  v3dr:
    id: 26
    name: "Jura 3.0 Disclosure Checking"
    mode: "v2"
    model_version: "v3dr"
    rule_label_map:
      Disclosure1: "Modified opinion"
      Disclosure2: "Potential fraud/internal control breakdown"
      Disclosure3: "Potential legal issue"
      Disclosure4: "Audited or agreed with the auditors"
      Disclosure5: "Statement of profit or loss"
      Disclosure6: "Statement of financial position"
      Disclosure7: "Note of revenue"
      Disclosure8: "Note of taxation"
      Disclosure9: "Note of EPS"
      Disclosure10: "Note of dividend"
      Disclosure11: "Purchase, Sales or Redemption of Listed Securities"
      Disclosure12: "CG Code"
      Disclosure13: "Significant changes in accounting policies"
      Disclosure14: "Prior period adjustment"
      Disclosure15: "Discontinued operation"
      Disclosure16: "Section 436(3) of HKCO"
      Disclosure17: "Money lending, indent trading or proprietary securities trading"
      Disclosure18: "Comment on segment"
      Disclosure19: "Reviewed by AC"

jura21_helper_mold:
  id: 29
  name: 'helper'
  mode: 'v2'
  model_version: 'jura21_helper'

cg_mold:
  id: 28
  name: 'Jura5 CG Report'
  mode: 'v2'
  model_version: 'jura5'

esg_molds:
  AR:
    id: 1
    name: 'Jura4 Annual Report ESG'
    mode: 'v2'
    model_version: 'jura4_ar'
  ESG:
    id: 2
    name: 'Jura4 ESG Report'
    mode: 'v2'
    model_version: 'jura4_esg'
  Policy-AR:
    id: 31
    name: 'Policy-AR-ESG'
    mode: 'v2'
    model_version: 'jura6_ar'
  Policy-ESG:
    id: 32
    name: 'Policy-ESG'
    mode: 'v2'
    model_version: 'jura6_esg'

v6_agm_id:
  id: 33
  name: "AGM"
  mode: 'v2'
  model_version: "jura6_agm"

v6_poll_id:
  id: 34
  name: "POLL"
  mode: 'v2'
  model_version: "jura6_poll"


threshold:  # 港交所阈值上下限
  RuleA1:
    top: 0.9454
    bottom: 0.1828
  RuleA10A4:
    top: 0.2242
    bottom: 0.0155
  RuleA10A3:
    top: 0.6657
    bottom: 0.0400
  RuleA10A2:
    top: 0.4418
    bottom: 0.0130
  RuleA10A6:
    top: 0.1079
    bottom: 0.0012
  RuleA10A1:
    top: 0.6745
    bottom: 0.0587
  RuleA10A5:
    top: 0.6749
    bottom: 0.1300
  RuleA11A1:
    top: 0.0139
    bottom: 0.0033
  RuleA11A2:
    top: 0.5
    bottom: 0.1
  RuleA11A3:
    top: 0.7176
    bottom: 0.0144
  RuleA12A2:
    top: 0.1757
    bottom: 0.0706
  RuleA12A1:
    top: 0.0521
    bottom: 0.0252
  RuleA13:
    top: 0.2837
    bottom: 0.0058
  RuleA14:
    top: 0.0667
    bottom: 0.0253
  RuleA15:
    top: 0.0451
    bottom: 0.0104
  RuleA16:
    top: 0.3963
    bottom: 0.0429
  RuleA17:
    top: 0.4333
    bottom: 0.020
  RuleA18:
    top: 0.3719
    bottom: 0.0451
  RuleA19A1:
    top: 0.0697
    bottom: 0.0095
  RuleA19A2:
    top: 0.0550
    bottom: 0.0068
  RuleA2:
    top: 0.5542
    bottom: 0.0102
  RuleA20:
    top: 0.0307
    bottom: 0.0145
  RuleA21A1:
    top: 0.0223
    bottom: 0.0126
  RuleA21A2:
    top: 0.0493
    bottom: 0.0114
  RuleA21A3:
    top: 0.0195
    bottom: 0.0045
  RuleA21A4:
    top: 0.0730
    bottom: 0.0114
  RuleA21A5:
    top: 0.0792
    bottom: 0.0047
  RuleA22:
    top: 0.0863
    bottom: 0.0219
  RuleA23:
    top: 0.0069
    bottom: 0.0031
  RuleA24A2:
    top: 0.0596
    bottom: 0.0130
  RuleA24A1:
    top: 0.0776
    bottom: 0.0383
  RuleA25:
    top: 0.0004
    bottom: 0.0002
  RuleA26:
    top: 0.0182
    bottom: 0.0003
  RuleA27:
    top: 0.5
    bottom: 0.1
  RuleA28A3:
    top: 0.0000
    bottom: 0.0000
  RuleA28A2:
    top: 0.0002
    bottom: 0.0001
  RuleA28A1:
    top: 0.0056
    bottom: 0.0010
  RuleA29A1:
    top: 0.0017
    bottom: 0.0004
  RuleA29A2:
    top: 0.0001
    bottom: 0.0001
  RuleA29A3:
    top: 0.0000
    bottom: 0.0000
  RuleA3:
    top: 0.7927
    bottom: 0.0078
  RuleA30:
    top: 0.1900
    bottom: 0.0058
  RuleA31:
    top: 0.2769
    bottom: 0.0066
  RuleA32:
    top: 0.0645
    bottom: 0.0101
  RuleA33:
    top: 0.3382
    bottom: 0.0057
  RuleA4:
    top: 0.2253
    bottom: 0.0134
  RuleA5:
    top: 0.0499
    bottom: 0.0095
  RuleA6:
    top: 0.0890
    bottom: 0.0063
  RuleA7:
    top: 0.2293
    bottom: 0.0045
  RuleA8:
    top: 0.0145
    bottom: 0.0061
  RuleA9:
    top: 0.2398
    bottom: 0.0029

crawler:
  name: "headstream.tasks.crawl"
  queue: "crawl"
  notify_to: []

dpp:
  farm_env: "farm_prod"
  farm: "http://bj.cheftin.cn:39003"
  roster: "https://u.paodingai.com/api/user/login_and_getoff"
  data_hub: "{{project_root}}/data"
  tags:
    # hunter_json_from_scriber_kcb: 169 # pdfinsight
    # scriber_kcb_answer: 170 # 答案
    scriber_hkex_pdf: 196 # pdf

contact_person_info_url: "https://www.hkex.com.hk/-/media/HKEX-Market/Listing/Rules-and-Guidance/Other-Resources/Listed-Issuers/Contact-Persons-in-HKEX-Listing-Department-for-Listed-Companies/Excel-Protected-File/listing.xlsx"
isin_info_url: 'https://www.hkex.com.hk/eng/services/trading/securities/securitieslists/ListOfSecurities.xlsx'
director_list_url: 'https://www3.hkexnews.hk/reports/dirsearch/dirlist/Documents/Director_List.xlsx'


notification:
  switch: False
  channel: "hkex_exception"
  tail: "@wangyangbin"
  esg_need_label_tail: "@xiahuayang @jinjin @wangyangbin"
  esg_need_label_switch: False

# red_flag_threshold just for ext api result_announcement
ratio_check:
  ratio1:
    label: "Cash Ratio"
    red_flag_threshold:
      - ">0.9"
  ratio2:
    label: "Impairment - Revenue Ratio\nImpairment - Total Assets Ratio"
    red_flag_threshold:
      - "≥0.3"
      - "≥0.3"
  ratio3:
    label: "Profit Fluctuation"
    red_flag_threshold:
      - "<-0.3"
  ratio4:
    label: "Significant Investment"
    red_flag_threshold:
      - "≥0.05"
  ratio5:
    label: "Gain/ Loss on Disposal"
    red_flag_threshold:
      - "≥0.3"
      - "≥0.3"
  ratio6:
    label: "Assets Fluctuation"
    red_flag_threshold:
      - "≥0.25"
  ratio7:
    label: "Revenue/ Gross Profit Level"
    red_flag_threshold:
      - "≥0.8"
      - "<HK$50M"
      - "<0"
      - "<0"

email:
  smtp_host: 'smtpdm.aliyun.com'
  smtp_port: 465
  from_addr: '<EMAIL>'
  from_passwd: ''

speedy:
  application: "scriber"
  application_name: "Scriber"
  expiration_timestamp: 0 # 过期所有API
  enable_docs: False
  xsrf_cookies:
    enable: True
    name: "_xsrf"
    header: "X-Csrftoken"
    secure: True
    httponly: True
    samesite: "lax"
    trust_routes:
      - "/"
      - "/login"
      - "/plugins/hkex/document"
      - "/plugins/hkex/companies"
      - "/plugins/fileapi/file/[0-9]+"
      - "/plugins/fileapi/file/[0-9]+/pdf"
      - "/plugins/fileapi/file/[0-9]+/export"
      - "/plugins/fileapi/file/[0-9]+/pdfinsight"
      - "/plugins/debug/files"
      - "/plugins/mm/reload-file"
      - "/plugins/mm/delist-code"
      - "/plugins/mm/rule-query"
      - "/api/v./saml/acs"
      - "/api/v2/files/sync"
  i18n:
    enable: True
    default_locale: "en"
    domain: "Scriber-Backend"
    localedir: "{{project_root}}/i18n/locales"


sso:
  saml:
    ms_idp_id: "9a2210e1-5a7b-465c-b7b2-f0127f926e66"
    strict: true
    debug: true
    sp:
      entityId: "{{web.scheme}}://{hostname}/api/v2/saml/metadata"
      assertionConsumerService:
        url: "{{web.scheme}}://{hostname}/api/v2/saml/acs"
        binding: "urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST"
      singleLogoutService:
        url: "{{web.scheme}}://{hostname}/api/v2/saml/sls"
        binding: "urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Redirect"
      NameIDFormat: "urn:oasis:names:tc:SAML:1.1:nameid-format:unspecified"
      # It's better to use env variable to store the certificate.
      x509cert: |-
        MIIDPTCCAiWgAwIBAgIUMrnLjqxtDP6Zh5sz+GbCU0jU7YIwDQYJKoZIhvcNAQEL
        BQAwLjELMAkGA1UEBhMCQ04xDTALBgNVBAgMBFhpYW4xEDAOBgNVBAoMB1BBSSBE
        RVYwHhcNMjMxMDI3MDIxMzQ0WhcNMjQxMDI2MDIxMzQ0WjAuMQswCQYDVQQGEwJD
        TjENMAsGA1UECAwEWGlhbjEQMA4GA1UECgwHUEFJIERFVjCCASIwDQYJKoZIhvcN
        AQEBBQADggEPADCCAQoCggEBAKJIrlXEMrS9cQCLI5XUB05k+MK9znHeDPNYqR1h
        +L5E3FCYh5gaF3Aw2/7Uax7++gyfKTUzlt6UCLx9/3iNMmxUZA7t2GzQlU5PTxKm
        iZQPYMJEaTb+IyAylMSc4eR0lU+u8UOVVCc5uTfgr0PGOYWtrOH4X0b9oRfq2wmT
        w+0mRkom1p+QqWHA5ax/X6MrGVFpL8nuDN0TQdFJsfZRrxSRRLjgJc/+LzhLcfuC
        FyRA/GtwbJ0X5aJR4EXGbMY46ziBDc9jzFYKawfQ2qx3hImeNsAMxcpR+JZQ6ZvX
        oXHk3xUgfFmg1nEnXEj/Wv/GzZ3vGWcH+Jaq7O9QcKpDZ1sCAwEAAaNTMFEwHQYD
        VR0OBBYEFBx5H6XF5VcqZYXWJHh4qoyH+ZlNMB8GA1UdIwQYMBaAFBx5H6XF5Vcq
        ZYXWJHh4qoyH+ZlNMA8GA1UdEwEB/wQFMAMBAf8wDQYJKoZIhvcNAQELBQADggEB
        ACvuSe3Gt6BClffq3EIsUogz7I9x7dh7mSw/TYrvW9Ho6yzGovAdDQGNJ4zGvtC4
        3Y3zcS1Lg41ObxCMNj2MuoEzjMkIrFlJAJ6nX9f1vOqYLF5dYUh2NpwzXKXN+aqo
        XQDICwl+PPEMGJvnhBarjZTKgA9NFOnDfYEBciOuC9p1/1Cs4LvwjK5MS8eUgpv0
        ANFXSAdT+9Hqtl9DJk5fh67IcVwSK/Njj7SGFEJj3pJmELs/9bKycp8kgniCHq0E
        R99pX99n0XTM4gaf3kNAoMYE8rBVxzAKNEP8RarVuQYIGZ2gcyFPxgwQj+wsQMu0
        GNFASjR95R6cSWEFmfeV8N4=
    idp:
      entityId: "https://sts.windows.net/{{sso.saml.ms_idp_id}}/"
      singleSignOnService:
        url: "https://login.microsoftonline.com/{{sso.saml.ms_idp_id}}/saml2"
        binding: "urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Redirect"
      singleLogoutService:
        url: "https://login.microsoftonline.com/{{sso.saml.ms_idp_id}}/saml2"
        binding: "urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Redirect"
      x509cert: "MIIC8DCCAdigAwIBAgIQGbXK+aoL779GBSij0lVzWjANBgkqhkiG9w0BAQsFADA0MTIwMAYDVQQDEylNaWNyb3NvZnQgQXp1cmUgRmVkZXJhdGVkIFNTTyBDZXJ0aWZpY2F0ZTAeFw0yMzA5MjcwODA1MjFaFw0yNjA5MjcwODA1MjBaMDQxMjAwBgNVBAMTKU1pY3Jvc29mdCBBenVyZSBGZWRlcmF0ZWQgU1NPIENlcnRpZmljYXRlMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAuDBg7yt4GETNed05ZxPzEJrZSmsIp1cDeUaoI/ixYErkp99MRZbT2Uh99EdXItZc0UWkJnlIZOF1KCXpW8oB4omcrUozQiXCGh5+5VT5IeHSI1lvRZse+mcJiRRdZFbmqjWGC8w/bNK1PeqbwgIFfwaT/+1Oym8QaRRdeXyMXVdRzeN2JcuKkh40NXML8ZvN/SmSdMBAkio6QGJLWH4ao1CfktUlpsdZSBZX/wCPmHodTkLkJkwzVUqqT+nw4lWPDvl4AWLTI9u2AnQwb/RFc7gsjahEviPMr6/GekjloeeRItVmUCm9yOtq+iZ2y9ePUN31nwHwIIu7oQMmV4SaiQIDAQABMA0GCSqGSIb3DQEBCwUAA4IBAQBl2RWl2WL+5E/KC18/BwO0VoZguZDvq1yeNEICODhUQ7Lkez8wxGkrpNv1eLzTmMsvPinn0/OaPp5jgDNrmHRB8/ni7OU/Zv7JnENHaGgRdDQvLKw1Htk5dDngRLXkuS1t7BhwB+RR6iNTqe53nrGOW1TlhTdh74vQ22YP2KDzy7WvgW5gVMIoZWC80OmTgpDJyi+g0ZrKN33On1kLpC3QV69XLDWJsPKzQwBWC+E3Ndsp5N+QgSKDxyukBazrMSm0MefMJotJz/AtX0TrlhLlZq45Wa29Xrg48Zi5QCTPSwui1U4vXw2EgCXXNPdgGcFXKbTg4fYTDRXzj8YKk2w7"
    security:
      nameIdEncrypted: false
      authnRequestsSigned: false
      logoutRequestSigned: false
      logoutResponseSigned: false
      signMetadata: false
      wantMessagesSigned: false
      wantAssertionsSigned: false
      wantNameId: true
      wantNameIdEncrypted: false
      wantAssertionsEncrypted: false
      allowSingleLabelDomains: false
      signatureAlgorithm: "http://www.w3.org/2001/04/xmldsig-more#rsa-sha256"
      digestAlgorithm: "http://www.w3.org/2001/04/xmlenc#sha256"
      rejectDeprecatedAlgorithm: true
      metadataValidUntil: "2025-12-31T08:00:00Z"
      metadataCacheDuration: "PT604800S"
