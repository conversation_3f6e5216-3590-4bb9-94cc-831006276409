debug: False # 测试环境可以打开以方便调试
logging:
  level: "info"
_new_entry:
  'new.key.path': 'old.key.path' # 用于兼容旧的环境变量注入

app:
  app_id: "remarkable" # scriber应用id, 充当cookie前缀字段, 如remarkable_user_id, 以标识应用类型, 默认值: remarkable
  secret_key: "hello_remarkable" # 密钥, 用于加密token
  cookie_secret: "hello~world!"
  # openssl rand -base64 24 | tr -dc 'a-zA-Z0-9' | head -c 32
  jwt_secret_key: "hello_remarkable"
  token_expire: 120 # token过期时间, 单位(秒)
  single_sign_limit: False # 单点登录限制，默认不限制
  session_expire: 7200 # session过期时间, 不配置默认600s
  rate_limit_expire: 7200 # 接口访问频率限制时间, 不配默认60s(暂未启用)
  max_fail_count: 100 # session有效期内最大尝试登录次数, 不配默认10次, 超过限制次数继续登录会被持续封禁
  auth: # 这里记录各个回调服务的信息, 如密钥, 回调地址, app_id等
    label:
      app_id: "internal"
      secret_key: "show-me-the-money"
      token_expire: 300
    roster:
      app_id: "cheftin-roster"
      secret_key: "you-never-know-it"
      token_expire: 300
      url: "http://u.paodingai.com"
    pdfinsight:
      app_id: "pdfinsight"
      secret_key: "show-me-the-money"
      token_expire: 120
      url: "http://bj.cheftin.cn:51896"
    autodoc:
      app_id: "autodoc"
      secret_key: "show-me-the-money"
      token_expire: 300
      url: "http://bj.cheftin.cn:65178"

web:
  http_port: 8000 # 服务监听端口
  scheme: "http"
  domain: "scriber" # 绑定主机名
  plugins: # 各种功能插件, 位于remarkable/plugins目录下
    - "fileapi"
    - "hkex"
    - "ext_api"
    - "debug"
  classes:
    answer_converter:
      Jura 3.0 Ratio Checking: "remarkable.converter.hkex.RatioConverter"
      Jura6.1 POLL: "remarkable.converter.hkex.PollGMLConverter"
    answer_inspector:
      LRs: "remarkable.rule.hkex_rules.HkexInspector"  # Rule A
      Jura 2.0 Listing Rules: "remarkable.rule.hkex_rules.HkexInspector"  # Rule B
      Jura 2.0 Additional Rules: "remarkable.rule.hkex_rules.HkexInspector"  # Rule C
      Jura 3.0 Disclosure Checking: "remarkable.rule.hkex_disclosure_rules.HkexDisclosureInspector" # 合规检查
      POLL: "remarkable.rule.hkex_rules.POLLInspector"  # POLL
      AGM: "remarkable.rule.hkex_rules.AGMInspector"  # AGM
    answer_predictor:
      LRs: "remarkable.plugins.hkex.check_compliance.PreparePresetAnswer"
      Jura 2.0 Listing Rules: "remarkable.plugins.predict.config.hkex_listing_rule.HKEXListingRulePredictor"
      Additional Documents: "remarkable.plugins.predict.config.hkex_additional_documents.AdditionalDocumentsPredictor"
      Jura3.0 AR Disclosure Checking: "remarkable.plugins.predict.config.disclosure_check.DisclosureCheckPredictor"
  apis:
    sso: "http://tl.paodingai.com/op/admin/login/subsys" # 与 label 系统对接的单点登录接口
    login_callback: "http://tl.paodingai.com/lrcapi/v1/user/login/by_token" # 与 label 系统对接的单点登录接口（本系统的回调地址）
    get_label_user: "http://tl.paodingai.com/adminuser/%d" # 获取 label 系统用户
    get_label_user_by_cn_list: "https://tl.paodingai.com/adminuser" # 获取 label 系统用户列表
    sync_host: "http://jefferson.paodingai.com:5887" # 线上数据同步接口地址(需要开启debug插件才有效)
  inspect_rules: True # 完备性审核开关
  parser: # 文档解析类型, 目前有pdf/docx两种
    mode: "pdf" # 默认pdf, 只有海通环境才是docx
  doc2docx_converter: "{{project_root}}/bin/converter/doc2docx.exe" # doc 转 docx 工具
  pdf_converter: "{{project_root}}/bin/converter/word2pdf.exe" # doc/docx 转 pdf 工具
  wordinsight: "{{project_root}}/bin/wordinsight/wordinsight.dll" # wordinsight 工具
  revision_tools: "{{project_root}}/bin/dr/docx_revision.dll" # docx 批注工具
  tmp_dir: "{{project_root}}/data/tmp/"
  data_dir: "{{project_root}}/data/files/" # 存储文件夹
  default_question_health: 1 # 几人答题
  preset_answer: False # 预测答案开关
  predict_crude_elements: True # 生成crude_answer预测元素块开关
  user_answer_black_list: "25" # uid， 这些用户的答案不会作为缓存答案
  limit_of_preset_num: 10 # 限制预测答案的个数
  limit_of_crude_elts: 5 # 限制读取预测元素块的个数
  auto_merge_answers: False # 答案分段合并(对应前端"合并答案"按钮功能)
  sleep_when_match_cache: False # 命中缓存答案后随机延迟开关
  folder_permission: True # 是否开启文件夹权限控制(海通)
  compare_term_template: True # 科创板完备性审核答案是否跟模板比较
  independent_file_perm: True # 是否开启文件权限控制(海通)
  predict_from_memory: # 每个属性的表头信息缓存下来，作为预测功能的补充信息（深交所）
    switch: True
    data_dir: "{{project_root}}/data/tbl_headers/"
  mode_unlimited_answers: True # 无限答题模式（答题次数不受限制）
  mode_conflict_treatment: 1 # 冲突处理方式：1-人工处理、2-以最新为准、3-合并答案
  show_export_answer: True # 显示/隐藏"导出的答案"按钮(上交所)
  show_marker: True # 显示/隐藏标记人员（上交所）
  answer_convert: # 答案转换: 标注schema -> 导出schema(上交所)
    科创板招股说明书信息抽取: "科创板招股说明书信息导出json"
  data_abroad: False # 数据生产（对接外部数据库）
  model_abroad: False # 模型接入（接入外部模型）
  xsrf_cookies: False # xsrf验证，默认关闭
  trust_routes: # 跳过auth&xsrf check的路由列表
    - "/login"
    - "/plugins/fileapi/file/[0-9]+/pdfinsight"
  default_page: 'hkex'
  # 标注环境默认不加密
  encrypted_request_routes: [ ]
  encrypted_response_routes: [ ]

db:
  host: "postgres"
  port: 5432
  dbname: "scriber"
  user: "postgres"
  password: ""
  max_connections: 100
  # 防断线 refer https://dba.stackexchange.com/a/275443/278337
  keepalives_idle: 30
  # 除正式环境 db 在 SSD 上之外，其他环境都是 HDD，连接超时时间设置长一点，防止后台长耗时查询任务超时失败
  connect_timeout: 600
  ## TLS/SSL connection settings
  #sslmode: "verify-ca"
  #sslkey: "{{project_root}}/data/ssl/client.key"
  #sslcert: "{{project_root}}/data/ssl/client.crt"
  #sslrootcert: "{{project_root}}/data/ssl/root.crt"

embedding_db:
  host: "{{db.host}}"
  port: "{{db.port}}"
  dbname: "{{db.dbname}}"
  user: "{{db.user}}"
  password: "{{db.password}}"
  max_connections: "{{db.max_connections}}"
  keepalives_idle: "{{db.keepalives_idle}}"
  connect_timeout: "{{db.connect_timeout}}"
  ## TLS/SSL connection settings
  #sslmode: "verify-ca"
  #sslkey: "{{project_root}}/data/ssl/client.key"
  #sslcert: "{{project_root}}/data/ssl/client.crt"
  #sslrootcert: "{{project_root}}/data/ssl/root.crt"

redis: # redis 数据库，目前用于模型训练
  host: "127.0.0.1"
  port: 6379
  db: 0
  password: ""

client:
  name: "hkex"
  remark_progress_type: "level1" # all
  language: "en_US"
  hide_rough_fields: True # 隐藏效果不好的字段（需求来自深交所）
  show_prompt_element_button: True # 前端AI推荐位置按钮开关
  check_schema_base_type: False # TODO: ?
  export_label_data: True # 是否支持导出训练数据
  stat_accuracy: True # 是否支持统计模型正确率
  use_new_tag: True # 是否开启新版本入口
  show_all_predict_items: True # 是否展示多个预测答案（指多选）
  answer_default_display_level: 1 # 答案默认展开级别, 数字代表展开到第n级(上交所)
  export_single_answer: False # 是否显示导出标注/预测答案按钮(上交所)
  ocr: # ocr服务, 用于对扫描件进行标注画框
    enable: False # 默认不开启
    service: "baidu" # 默认使用百度ocr, 可选abbyy, aliyun, onlyou等

feature:  # 功能特性相关配置
  new_project: True  # 新建项目
  new_project_popup_default_molds: True  # 新建项目时选择默认Schema
  zip_upload: True  # 上传文件压缩包
  label_tools: True  # 标注工具条显示"元素块提取"
  supported_suffixes: ['.zip', '.doc', '.docx', '.jpeg', '.jpg', '.pdf', '.png', '.txt', '.xlsx', '.xls']  # 支持的后缀
  secondary_submit: True  # 在「别人的答案」基础上提交答案
  answer_batch_edit: True # 答案树支持"批量修改"
  file_list_page:  # 文件列表页相关
    new_buttons: True  # 新建文件夹、上传文件、上传zip的按钮
    compare_button:  True  # 文件列表操作列显示【对比】按钮
  quick_schema_switching: False # 通用标注界面快速切换schema
  common_permissions:
    browse:
      name: "Browse"
      definition: "Can browse the project"
    remark:
      name: "Information extraction"
      definition: "Can annotate documents and view extraction results"
    manage_prj:
      name: "Project management"
      definition: "Can edit and delete other users' projects"
    manage_user:
      name: "User management"
      definition: "Can add, delete, and modify system user accounts"
    manage_mold:
      name: "Schema management"
      definition: "Can edit schema"
    manage_model:
      name: "Model management"
      definition: "Can create model versions, set extraction modes, train, enable, and delete models"
  additional_permissions: # Additional user permissions
    remark_management:
      name: "Annotation management"
      definition: "Annotation management"
    manage_stock:
      name: "Stock code management"
      definition: "Maintain stock code data"
  basic_permissions: # 默认拥有的权限, 前端现在很多地方绑定了remark权限, 去掉remark需谨慎
    - 'browse'
    - 'remark'
  restore_user_answer: False  # 是否支持恢复用户答案
  b1_b7_event_types:  # 针对规则B1-B7 https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/5464#note_601899
    - "Open Offer"
    - "Placing Shares"
    - "Shares Subscription"
    - "Rights Issue"
    - "Issue of Convertible Securities"
    - "Issue of Warrants"
    - "Issue of Preference Shares"
    - "IPO"
    - "Others"
  # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6679#note_691205
  non_sso_read_only: False  # 非SSO用户只读
  review_show_element_index: False # review 界面是否显示元素块 index
  label_show_element_index: True # label 界面是否显示元素块 index
  process_jura61_module: False # 是否处理 JURA61 相关的数据


worker: # celery worker 相关配置
  app_name: "remarkable"
  default_queue: "celery"
  broker: "redis://127.0.0.1:6379"
  # 定时任务配置
  crontab_tasks:
    # 每天凌晨1点生成ratio cache数据
    ratio_cache_by_rule:
      task: "remarkable.worker.hkex_tasks.ratio_cache_by_rule"
      schedule:
        minute: 0
        hour: 1

    # 每天凌晨2点生成disclosure cache数据
    disclosure_cache_by_rule:
      task: "remarkable.worker.hkex_tasks.disclosure_cache_by_rule"
      schedule:
        minute: 0
        hour: 2

    # 每天早上8点生成hkex_file_meta表中stat_res数据
    update_stat_res:
      task: "remarkable.worker.hkex_tasks.update_stat_res_for_all"
      schedule:
        minute: 0
        hour: 8

    # 每天早上8点半生成 by_issuer数据
    update_exist_issuer_info:
      task: "remarkable.worker.hkex_tasks.update_exist_issuer_info"
      schedule:
        minute: 30
        hour: 8

    # 每天 03:03 清理过期的导出结果缓存
    clean_up_exporting_cache:
      task: "remarkable.worker.esg_tasks.clean_up_exporting_cache"
      schedule:
        minute: 3
        hour: 3

    # 每天凌晨2点爬上市日期
    crawl_listing_date_of_company:
      task: "remarkable.worker.hkex_tasks.crawl_listing_date_of_company"
      schedule:
        minute: 0
        hour: 2

# jura_version对应的schema_id
schema:
  v1:
    id: 5
    name: 'LRs'
    mode: 'v3'
    model_version: 'jura'
  v2:
    id: 15
    name: 'Jura 2.0 Listing Rules'
    mode: 'v3'
    model_version: 'jura2'
  v2a: # Additional Documents
    id: 12
    name: 'Additional Documents'
    mode: 'v2'
  v3:
    id: 18
    name: 'Jura 2.0 Additional Rules'
    mode: 'v3'
    model_version: 'jura2p'
  v3d:
    id: 22
    name: 'Jura3.0 AR Disclosure Checking'
    mode: 'v2'
    model_version: 'jura3d'
  v3r:
    id: 24
    name: 'Jura 3.0 Ratio Checking'
    mode: 'v2'
    model_version: 'jura3r'
  v3dr:
    id: 26
    name: 'Jura 3.0 Disclosure Checking'
    mode: 'v2'
    model_version: 'v3dr'
    rule_label_map:
      Disclosure1: "Modified opinion"
      Disclosure2: "Potential fraud/internal control breakdown"
      Disclosure3: "Potential legal issue"
      Disclosure4: "Reviewed by AC and agreed or reviewed by auditors"
      Disclosure5: "Statement of profit or loss"
      Disclosure6: "Statement of financial position"
      Disclosure7: "Note of revenue"
      Disclosure8: "Note of taxation"
      Disclosure9: "Note of EPS"
      Disclosure10: "Note of dividend"
      Disclosure11: "Purchase, Sale or redemption"
      Disclosure12: "CG Code"
      Disclosure13: "Significant changes in accounting policies"
      Disclosure14: "Prior period adjustment"
      Disclosure15: "Discountinued operation"
      Disclosure16: "Section 436(3) of HKCO"
      Disclosure17: "Money lending, indent trading or proprietary securities trading"
      Disclosure18: "Comment on segment"
      Disclosure19: "Audited or agreed with the auditors"

esg_molds:
  AR:
    id: 1
    name: 'Jura4 Annual Report ESG'
    mode: 'v2'
    model_version: 'jura4_ar'
  ESG:
    id: 2
    name: 'Jura4 ESG Report'
    mode: 'v2'
    model_version: 'jura4_esg'
  Policy-AR:
    id: 31
    name: 'Policy-AR-ESG'
    mode: 'v2'
    model_version: 'jura6_ar'
  Policy-ESG:
    id: 32
    name: 'Policy-ESG'
    mode: 'v2'
    model_version: 'jura6_esg'

v6_agm_id:
  id: 33
  name: "AGM"
  mode: 'v2'
  model_version: "jura6_agm"

v6_poll_id:
  id: 34
  name: "POLL"
  mode: 'v2'
  model_version: "jura6_poll"

v6_nddr:
  id: 35
  name: "Jura6 NDDR"

v6_1_mr:
  id: 36
  mode: 'v2'
  model_version: "jura6_1_mr"
  name: "Jura6.1 MR"

v6_1_agm:
  id: 37
  mode: 'v2'
  model_version: "jura6_1_agm"
  name: "Jura6.1 AGM"

v6_1_poll:
  id: 38
  mode: 'v2'
  model_version: "jura6_1_poll"
  name: "Jura6.1 POLL"



# red_flag_threshold just for ext api result_announcement
ratio_check:
  ratio1:
    label: "Cash Ratio"
    red_flag_threshold: "0.95"
    definition: "To assist our consideration regarding:\nRule 14.82: Cash company issue"
  ratio2:
    label: "Impairment - Revenue Ratio\nImpairment - Total Assets Ratio"
    red_flag_threshold: "0.3 0.3"
    definition: "To assist our consideration regarding:\nRule 13.24: Sufficiency of operations and assets of sufficient value; and\nRule 13.09: Potential delay in publication of inside information"
  ratio3:
    label: "Profit Fluctuation"
    red_flag_threshold: "-0.3"
    definition: "To assist our consideration regarding:\nRule 13.24: Sufficiency of operations and assets of sufficient value;\nRule 13.09: Potential delay in publication of inside information; and\nRule 2.13: Accuracy of the IPO prospectus or submitted forecast"
  ratio4:
    label: "Significant Investment"
    red_flag_threshold: "0.05"
    definition: "To assist our consideration regarding:\nApp16(32)(4), (4A): Disclosure in relation to significant investment"
  ratio5:
    label: "Gain/ Loss on Disposal"
    red_flag_threshold: "0.3 0.3"
    definition: "To assist our consideration regarding:\nRule 13.24: Sufficiency of operations and assets of sufficient value;\nRule 13.09: Potential delay in publication of inside information; and\nCh.14 and 14A: Potential non-compliance with notifiable and/or connected transaction requirements"
  ratio6:
    label: "Assets Fluctuation"
    red_flag_threshold: "0.25"
    definition: "To assist our consideration regarding:\n13.09: Potential delay in publication of inside information; and\nCh. 14 and 14A: Potential non-compliance with notifiable and/or connected transaction requirements"
  ratio7:
    label: "Revenue/ Gross Profit Level"
    red_flag_threshold: "0.8 HK$50M 0 0"
    definition: "To assist our consideration regarding:\nRule 13.24: Sufficiency of operations and assets of sufficient value"


threshold: # 港交所阈值上下限
  RuleA1:
    top: 0.9454
    bottom: 0.1828
  RuleA10A4:
    top: 0.2242
    bottom: 0.0155
  RuleA10A3:
    top: 0.6657
    bottom: 0.1
  RuleA10A2:
    top: 0.4418
    bottom: 0.0763
  RuleA10A6:
    top: 0.1079
    bottom: 0.0012
  RuleA10A1:
    top: 0.6745
    bottom: 0.0587
  RuleA10A5:
    top: 0.6749
    bottom: 0.1300
  RuleA11A1:
    top: 0.0139
    bottom: 0.0033
  RuleA11A2:
    top: 0.5
    bottom: 0.1
  RuleA11A3:
    top: 0.7176
    bottom: 0.0144
  RuleA12A2:
    top: 0.1757
    bottom: 0.0706
  RuleA12A1:
    top: 0.0521
    bottom: 0.0252
  RuleA13:
    top: 0.2837
    bottom: 0.0058
  RuleA14:
    top: 0.0667
    bottom: 0.0253
  RuleA15:
    top: 0.0451
    bottom: 0.0104
  RuleA16:
    top: 0.3963
    bottom: 0.0960
  RuleA17:
    top: 0.4333
    bottom: 0.04
  RuleA18:
    top: 0.3719
    bottom: 0.0913
  RuleA19A1:
    top: 0.0697
    bottom: 0.0095
  RuleA19A2:
    top: 0.0550
    bottom: 0.0068
  RuleA2:
    top: 0.5542
    bottom: 0.0102
  RuleA20:
    top: 0.0307
    bottom: 0.0145
  RuleA21A1:
    top: 0.0223
    bottom: 0.0126
  RuleA21A2:
    top: 0.0493
    bottom: 0.0114
  RuleA21A3:
    top: 0.0195
    bottom: 0.0045
  RuleA21A4:
    top: 0.0730
    bottom: 0.0114
  RuleA21A5:
    top: 0.0792
    bottom: 0.0047
  RuleA22:
    top: 0.0863
    bottom: 0.0219
  RuleA23:
    top: 0.0069
    bottom: 0.0031
  RuleA24A2:
    top: 0.0596
    bottom: 0.0130
  RuleA24A1:
    top: 0.0776
    bottom: 0.0383
  RuleA25:
    top: 0.0004
    bottom: 0.0002
  RuleA26:
    top: 0.0182
    bottom: 0.0003
  RuleA27:
    top: 0.5
    bottom: 0.1
  RuleA28A3:
    top: 0.0000
    bottom: 0.0000
  RuleA28A2:
    top: 0.0002
    bottom: 0.0001
  RuleA28A1:
    top: 0.0056
    bottom: 0.0010
  RuleA29A1:
    top: 0.0017
    bottom: 0.0004
  RuleA29A2:
    top: 0.0001
    bottom: 0.0001
  RuleA29A3:
    top: 0.0000
    bottom: 0.0000
  RuleA3:
    top: 0.7927
    bottom: 0.0078
  RuleA30:
    top: 0.1900
    bottom: 0.0058
  RuleA31:
    top: 0.2769
    bottom: 0.0066
  RuleA32:
    top: 0.0645
    bottom: 0.0101
  RuleA33:
    top: 0.3382
    bottom: 0.0057
  RuleA4:
    top: 0.2253
    bottom: 0.0134
  RuleA5:
    top: 0.0499
    bottom: 0.0095
  RuleA6:
    top: 0.0890
    bottom: 0.0063
  RuleA7:
    top: 0.2293
    bottom: 0.0045
  RuleA8:
    top: 0.0145
    bottom: 0.0061
  RuleA9:
    top: 0.2398
    bottom: 0.0029

prophet:
  package_name: "hkex_predictor"
  config_map:
    LRs: "lrs"
    Jura 2.0 Listing Rules: "list_rule"
    Jura 3.0 Disclosure Checking: "disclosure_check"
    Jura 3.0 Ratio Checking: "ratio_check"
    Jura4 Annual Report ESG: "ar_esg"
    Jura4 ESG Report: "esg"
    Jura5 CG Report: "cg"
    Jura 2.0 Additional Rules: "c_rule"
    helper: "helper"
    Policy-AR-ESG: "policy_ar"
    Policy-ESG: "policy_esg"
    AGM: "agm"
    POLL: "poll_result"
    Jura6 NDDR: "nddr"
    Jura6.1 MR: "jura61_mr"
    Jura6.1 AGM: "jura61_agm"
    Jura6.1 POLL: "jura61_poll"

prompter: # 定位模型相关配置
  top_n: 5
  workers: 4 # 训练/预测 时使用的进程数
  #  mode: "v2"  # 定位模型版本: v1 or v2
  #  use_syllabuses: True    # 是否使用目录信息
  #  tokenization: "jieba"   # 分词方法，为 null 则按空白分词（英文）
  #  context_length: 1       # 上下文范围
  batch_size: 100
  auto_build_ignores: "*" # 不自动训练的 schema
  answer_version: 2.2 # 系统使用的答案格式版本
  training_data_status: "2, 5, 10, 100" # 这些 status 的题目认为标注完成（用于 训练、统计）
  element_classes: # 初步定位时选择的元素块类型 可以在训练时根据文档实际情况和效果调整
    - 'paragraphs'
    - 'tables'
    - 'page_headers'
    - 'shapes'
    - 'images'
    - 'infographics'
    - 'footnotes'
#    - 'page_footers'  # 添加 page_footers 会降低准确率

dpp:
  farm_env: "farm_prod"
  farm: "http://192.168.1.30:9003"
  roster: "https://u.paodingai.com/api/user/login_and_getoff"
  data_hub: "{{project_root}}/data"
  tags:
    hunter_json_from_scriber_kcb: 169 # pdfinsight
    scriber_kcb_answer: 170 # 答案
    scriber_kcb_pdf: 171 # pdf

notification:
  leanchat_switch: False # MM通知开关, 默认不开启
  channel: "hkex_exception" # 发送对象, 可以是频道也可以是具体的人, 比如@张全蛋
  mattermost: "http://mm.paodingai.com/hooks/zxg3ncokc3yuxfymyrco7zctta" # MM web hooks url


no_ai_rules:
  - "B63.2"
  - "B73.1"

has_director_data_rules:
  - "M26-length of tenure of each INED when all INED serving more than nine years"  # AC1.B.2.4(a)   all + 9 years INED
  - "M27-appoint a new INED when all INED serving more than nine years"  # AC1.B.2.4(b)  all + 9 years INED
#  - "M32-confirmation of independence for a new INED"  # 13.74 - 13.51(2)   all + only  INED
  - "M40-any person appointed by the directors to fill a casual vacancy on or as an addition to the board"  # AA1.4(2)  all + new directors
  - "M41-AC1.B.2.3"  # AC1.B.2.3 all + 9 years INED

need_show_ratio4_rules:
  - "C2.1.1"
  - "C2.2.1"
  - "C2.3"
  - "C2.4"
  - "C2.5"
  - "C2.6"
  - "C2.7"

ai:
  crude_location:
    address: "grpc-ai-crude.b1.cheftin.cn:1081"
  openai:
    api_key: "sk-xxx"
    base_url: "https://oneapi.cheftin.com/v1"
    model: "o3-mini"
    embedding_model: "text-embedding-3-small"
    timeout: 180

custom_company_info:
  # stock_code, name_en, name_cn, team_id
  - ["02497", "FUJING HOLDINGS", "FUJING HOLDINGS", "4"]
  - ["02509", "QYUNS-B", "QYUNS-B", "1"]
  - ["02540", "LESI GROUP", "LESI GROUP", "26"]
  - ["02598", "LIANLIAN", "LIANLIAN", "7"]
  - ["02535", "WK GROUP", "WK GROUP", "11"]

speedy:
  application: "scriber"
  application_name: "Scriber"
  expiration_timestamp: 0 # 过期所有API
  enable_docs: True
  xsrf_cookies:
    enable: False
    name: "_xsrf"
    header: "X-Csrftoken"
    secure: False
    httponly: True
    samesite: "lax"
    trust_routes:
      - "/"
      - "/api/v./"
      - "/api/v./login"
      - "/api/v./plugins/hkex/document"
      - "/api/v./plugins/hkex/companies"
      - "/api/v./plugins/fileapi/file/[0-9]+"
      - "/api/v./plugins/fileapi/file/[0-9]+/pdf"
      - "/api/v./plugins/fileapi/file/[0-9]+/export"
      - "/api/v./plugins/fileapi/file/[0-9]+/pdfinsight"
      - "/api/v./plugins/debug/files"
      - "/api/v./saml/acs"
      - "/api/v./saml/sso"
  i18n:
    enable: True
    default_locale: "en"
    domain: "Scriber-Backend"
    localedir: "{{project_root}}/i18n/locales"
    locales:
      - "en"
      - "zh"


# /project列表中，如果需要创建一些特殊的项目，且对上传的文件类型有特殊要求，可以在这里配置
# 默认的支持类型： pdf、doc、docx、txt、jpg、jpeg、png
project_list:
  addition_files:
    name: "Market Eye Files"
