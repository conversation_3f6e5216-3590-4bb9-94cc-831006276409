import json
import zipfile
from dataclasses import dataclass, field
from pathlib import Path

import pytest

from remarkable.pdfinsight.chapter_fixer import ChapterFixer, calc_fixed_index, UnifiedChapter
from remarkable.pdfinsight.reader import PdfinsightReader
from remarkable.pdfinsight.reader_util import P_BLANK


@dataclass
class ExpectedItem:
    # 文件id
    fid: int
    # 预期目录
    contents: dict[int, str]
    # 新增的章节
    added_chapters: set[int]
    # 修正后的章节名称
    title_changed: dict[int, str] | None = None
    # 被删除的章节
    removed_chapters: set[int] | None = None
    # level变更
    level_changed: dict[int, int] | None = None
    # start变更
    start_changed: dict[int, int] | None = None
    # end变更
    end_changed: dict[int, int] | None = None
    # 父章节变更: 用element_index
    parent_changed: dict[int, int] | None = None
    # children变更: 用element_index
    children_changed: dict[int, list] | None = None


EXPECTED = [
    # 68823
    ExpectedItem(
        66435,
        contents={
            2: "COMPANY PROFILE",
            4: "CORPORATE INFORMATION",
            7: "CHAIRMAN’S STATEMENT",
            10: "FINANCIAL HIGHLIGHTS",
            12: "BUSINESS HIGHLIGHTS",
            17: "MANAGEMENT DISCUSSION AND ANALYSIS",
            49: "PROFILES OF DIRECTORS AND SENIOR MANAGEMENT",
            61: "CORPORATE GOVERNANCE REPORT",
            89: "DIRECTORS’ REPORT",
            120: "INDEPENDENT AUDITOR’S REPORT",
            131: "CONSOLIDATED STATEMENT OF COMPREHENSIVE INCOME",
            132: "CONSOLIDATED STATEMENT OF FINANCIAL POSITION",
            134: "CONSOLIDATED STATEMENT OF CHANGES IN EQUITY",
            135: "CONSOLIDATED STATEMENT OF CASH FLOWS",
            137: "NOTES TO THE CONSOLIDATED FINANCIAL STATEMENTS",
            255: "FINANCIAL SUMMARY",
            256: "DEFINITIONS AND GLOSSARY OF TECHNICAL TERMS",
        },
        added_chapters=set(),
        title_changed={
            3: "• Pyrilutamide (KX-826)",
            4: "• AR-PROTAC Compound (GT20029)",
            5: "• Pruxelutamide (GT0918)",
            6: "• Hedgehog/SMO Inhibitor (GT1708F)",
            7: "• Detorsertib (GT0486)",
            8: "• ALK-1 Antibody (GT90001)",
            9: "• PD-L1/TGF-ß (GT90008)",
            40: "AR-PROTAC (GT20029)",
            48: "• Pyrilutamide (KX-826)",
            54: "• AR-PROTAC Compound (GT20029)",
            55: "• Pruxelutamide (GT0918)",
            58: "• GT1708F (Hedgehog/SMO Inhibitor)",
            61: "• ALK-1 Antibody (GT90001)",
            228: "2 SUMMARY OF SIGNIFICANT ACCOUNTING POLICIES",
            272: "3 FINANCIAL RISK MANAGEMENT",
            291: "5 SEGMENT AND REVENUE INFORMATION",
            299: "7 EXPENSES BY NATURE",
            301: "9 FINANCE COSTS",
        },
        removed_chapters={281, 319, 320, 321, 322, 352},
        level_changed={},
    ),
    ExpectedItem(
        66449,
        contents={
            5: "CORPORATE INFORMATION",
            7: "DEFINITIONS",
            13: "MAJOR EVENTS AND ACCOLADES",
            21: "CHAIRMAN’S STATEMENT",
            26: "BIOGRAPHICAL DETAILS OF DIRECTORS AND SENIOR MANAGEMENT",
            36: "MANAGEMENT DISCUSSION AND ANALYSIS",
            59: "CORPORATE GOVERNANCE REPORT",
            89: "DIRECTORS’ REPORT",
            122: "INDEPENDENT AUDITOR’S REPORT",
            135: "CONSOLIDATED STATEMENT OF COMPREHENSIVE INCOME",
            138: "CONSOLIDATED BALANCE SHEET",
            141: "CONSOLIDATED STATEMENT OF CHANGES IN EQUITY",
            143: "CONSOLIDATED STATEMENT OF CASH FLOWS",
            145: "NOTES TO THE CONSOLIDATED FINANCIAL STATEMENTS",
            347: "FINANCIAL SUMMARY",
            348: "PARTICULARS OF INVESTMENT PROPERTY",
        },
        added_chapters={27},
        title_changed={
           24: "MAJOR EVENTS AND ACCOLADES",
           80: "1 THE ACQUISITION OF 100% INTEREST IN HANOVER FAMILY BUILDERS, LLC",
           81: "2 THE CREDIT AGREEMENT",
           82: "3 THE DISPOSAL (NANJING XINBEISHENG INVESTMENT MANAGEMENT LIMITED*)",
           83: "4 THE ACQUISITIONS OF THE REMAINING 45% EQUITY INTEREST IN (SUZHOU LANGKUN PROPERTIES COMPANY LIMITED*)",
           84: "5 THE DISPOSAL OF 10.7% INTEREST IN LANDSEA HOMES",
           85: "6 SHARE REPURCHASE BY LANDSEA HOMES (THE “SHARE REPURCHASE”)",
           86: "7 THE DISPOSAL OF 50% EQUITY INTEREST IN A JOINT VENTURE",
           87: "8 THE DISPOSAL OF 100% INTEREST IN (SHANGHAI LANGSONG ENTERPRISES COMPANY LIMITED*)",
           133: "DIRECTORS' REPORT",
           180: "INDEPENDENT AUDITOR'S REPORT",
           196: "1 GENERAL INFORMATION",
           197: "2 SUMMARY OF SIGNIFICANT ACCOUNTING POLICIES",
           273: "3 FINANCIAL RISK MANAGEMENT",
           284: "4 CRITICAL ACCOUNTING ESTIMATES AND JUDGEMENTS",
           296: "5 REVENUE",
           303: "6 SEGMENT INFORMATION",
           304: "7 OTHER INCOME",
           305: "8 OTHER LOSSES — NET",
           306: "9 EXPENSES BY NATURE",
           334: "20 PROPERTY, PLANT AND EQUIPMENT",
           338: "22 GOODWILL",
           341: "25 PROPERTIES UNDER DEVELOPMENT",
           352: "30 TRADE AND OTHER PAYABLES",
           367: "36 RESERVES",
           368: "37 NON-CONTROLLING INTERESTS",
           406: "PARTICULARS OF INVESTMENT PROPERTY",
        },
        removed_chapters={1, 169, 171, 202, 203, 255, 345, 348, 389, 402},
        level_changed={170: 3, 256: 4, 257: 4},
    ),
    ExpectedItem(
        66624,
        contents={
            3: "CORPORATE INFORMATION",
            5: "FINANCIAL HIGHLIGHTS",
            7: "PRODUCTION BASES",
            8: "COMPANY PRODUCTS",
            9: "CORPORATE STRUCTURE",
            10: "LETTER TO THE SHAREHOLDERS",
            13: "MANAGEMENT DISCUSSION AND ANALYSIS",
            26: "REPORT OF THE DIRECTORS",
            50: "BOARD OF DIRECTORS AND SENIOR MANAGEMENT",
            55: "CORPORATE GOVERNANCE REPORT",
            73: "INDEPENDENT AUDITOR\u2019S REPORT",
            78: "CONSOLIDATED STATEMENT OF COMPREHENSIVE INCOME",
            80: "CONSOLIDATED BALANCE SHEET",
            82: "CONSOLIDATED STATEMENT OF CHANGES IN EQUITY",
            84: "CONSOLIDATED STATEMENT OF CASH FLOWS",
            85: "NOTES TO THE CONSOLIDATED FINANCIAL STATEMENTS",
            157: "FINANCIAL SUMMARY",
        },
        added_chapters={107, 117},
        title_changed={
            191: "1 General information",
            192: "2 Summary of significant accounting policies",
            243: "3 Financial risk management",
            263: "4 Critical estimates and judgements",
            269: "5 Revenue and segment information",
            274: "6 Other income and other (losses)/gains, net",
            275: "7 Expenses by nature",
            276: "8 Employee benefits expense",
        },
        removed_chapters={19, 65, 252, 303, 304, 318},
        level_changed={253: 6},
        start_changed={},
        end_changed={34: 327, 115: 560, 317: 1771, 334: 1830},
        parent_changed={66: 170, 67: 170, 253: 1277, 319: 1760},  # TODO 255[1305]: 1277
        children_changed={
            18: [],
            34: [171, 198, 213, 294, 320, 323],
            # 63: [296, 305, 310],
            106: [497, 499, 510, 512, 514, 523, 525],
            113: [552, 554, 560, 565, 567, 576, 578, 581],
            190: [
                1007, 1013, 1242, 1353, 1368, 1403, 1410, 1415, 1435, 1453, 1455, 1473, 1478, 1495, 1513, 1523, 1536,
                1559, 1562, 1567, 1585, 1594, 1600, 1607, 1706, 1713, 1716, 1739, 1745, 1753, 1760, 1771, 1796, 1800, 1815,
            ],
            251: [1288, 1290],
            302: [],
            317: [1769],
        },
    ),
    ExpectedItem(
        66635,
        contents={
            2: "CORPORATE INFORMATION",
            4: "CHAIRMAN’S STATEMENT",
            7: "MANAGEMENT DISCUSSION AND ANALYSIS",
            11: "CORPORATE GOVERNANCE REPORT",
            25: "ENVIRONMENTAL, SOCIAL AND GOVERNANCE REPORT",
            53: "DIRECTORS AND SENIOR MANAGEMENT",
            58: "DIRECTORS’ REPORT",
            71: "INDEPENDENT AUDITOR’S REPORT",
            76: "CONSOLIDATED STATEMENT OF PROFIT OR LOSS AND OTHER COMPREHENSIVE INCOME",
            77: "CONSOLIDATED STATEMENT OF FINANCIAL POSITION",
            79: "CONSOLIDATED STATEMENT OF CHANGES IN EQUITY",
            81: "CONSOLIDATED STATEMENT OF CASH FLOWS",
            83: "NOTES TO THE CONSOLIDATED FINANCIAL STATEMENTS",
            170: "FINANCIAL SUMMARY",
        },
        added_chapters=set(),
        title_changed={
            166: "Consolidated Statement of Financial Position",
            168: "Consolidated Statement of Changes in Equity",
            169: "Consolidated Statement of Cash Flows",
            170: "Notes to the Consolidated Financial Statements",
        },
        removed_chapters={11, 21, 167, 171, 272, 273},
        level_changed={},
        start_changed={168: 858, 169: 873},
        end_changed={166: 858, 168: 873, 170: 1983, 313: 1990},
        parent_changed={
            172: 883,
            173: 883,
            179: 883,
            238: 883,
            245: 883,
            251: 883,
            252: 883,
            253: 883,
            254: 883,
            255: 883,
            256: 883,
            259: 883,
            260: 883,
            261: 883,
            262: 883,
            264: 883,
            266: 883,
            267: 883,
            269: 883,
            270: 883,
            271: 883,
            274: 883,
            275: 883,
            276: 883,
            277: 883,
            278: 883,
            279: 883,
            280: 883,
            281: 883,
            282: 883,
            283: 883,
            284: 883,
            285: 883,
            301: 883,
            302: 883,
            304: 883,
            307: 883,
            308: 883,
            309: 883,
            311: 883,
            312: 883,
        },
        children_changed={
            # 1: [12, 22, 27, 33, 39, 41, 47, 53, 59, 66, 70, 76, 83, 88, 105, 107],
            10: [],
            20: [],
            170: [
                886, 891, 938, 1367, 1395, 1429, 1432, 1434, 1440, 1447, 1465, 1504, 1508, 1518, 1527, 1539, 1562, 1568,
                1592, 1603, 1606, 1618, 1626, 1635, 1638, 1646, 1654, 1668, 1681, 1689, 1691, 1695, 1703, 1856, 1859,
                1873, 1948, 1961, 1967, 1975, 1980,
            ],
            271: [],
        },
    ),
    ExpectedItem(
        66645,
        contents={
            3: "MILESTONES",
            5: "CORPORATE PROFILE",
            8: "CORPORATE INFORMATION",
            9: "FINANCIAL HIGHLIGHTS",
            11: "CHAIRMAN\u2019S STATEMENT",
            17: "MANAGEMENT DISCUSSION AND ANALYSIS",
            30: "CORPORATE GOVERNANCE REPORT",
            44: "ENVIRONMENTAL, SOCIAL AND GOVERNANCE REPORT",
            59: "PROFILE OF DIRECTORS AND SENIOR MANAGEMENT",
            63: "DIRECTORS\u2019 REPORT",
            76: "INDEPENDENT AUDITOR\u2019S REPORT",
            81: "CONSOLIDATED STATEMENT OF PROFIT OR LOSS AND OTHER COMPREHENSIVE INCOME",
            82: "CONSOLIDATED STATEMENT OF FINANCIAL POSITION",
            83: "CONSOLIDATED STATEMENT OF CHANGES IN EQUITY",
            84: "CONSOLIDATED STATEMENT OF CASH FLOWS",
            86: "NOTES TO THE CONSOLIDATED FINANCIAL STATEMENTS",
            147: "FINANCIAL SUMMARY",
        },
        added_chapters={1},
        title_changed={
            137: "Mr. Chu Hui , Chairman and Chief Executive Officer",
            138: "Ms. Xia Yafang , executive vice president",
            139: "Mr. Jiang Yongwei , vice president",
            141: "Mr. He Zhisong",
            142: "Mr. Yang Rongkai",
            143: "Mr. Fok Ming Fuk",
            209: "Amendments to HKAS 1 “Classification of Liabilities as Current or Non-current” (the “2020 Amendments”) and Amendments to HKAS 1 “Non-current Liabilities with Covenants” (the “2022 Amendments”)",
        },
        removed_chapters={3, 210, 211, 212},
        level_changed={},
        start_changed={},
        end_changed={1: 45, 209: 997, 326: 1831},
        parent_changed={},
        children_changed={208: [984, 1002, 1013]},
    ),
    ExpectedItem(
        66669,
        contents={
            2: "CORPORATE INFORMATION",
            5: "CHAIRMAN’S STATEMENT",
            23: "FINANCIAL HIGHLIGHTS",
            24: "MANAGEMENT DISCUSSION AND ANALYSIS",
            35: "DIRECTORS AND SENIOR MANAGEMENT PROFILE",
            49: "CORPORATE GOVERNANCE REPORT",
            75: "REPORT OF DIRECTORS",
            100: "INDEPENDENT AUDITOR’S REPORT",
            113: "CONSOLIDATED STATEMENT OF PROFIT OR LOSS",
            114: "CONSOLIDATED STATEMENT OF PROFIT OR LOSS AND OTHER COMPREHENSIVE INCOME",
            115: "CONSOLIDATED STATEMENT OF FINANCIAL POSITION",
            117: "CONSOLIDATED STATEMENT OF CHANGES IN EQUITY",
            119: "CONSOLIDATED CASH FLOW STATEMENT",
            120: "NOTES TO THE CONSOLIDATED FINANCIAL STATEMENTS",
            248: "FIVE-YEAR FINANCIAL SUMMARY",
        },
        added_chapters={168, 214, 236, 266},
        title_changed={
            33: "4. Continuously Promote the Cooperation with WuXi AppTec (Shanghai) Co., Ltd.* (“WuXi AppTec”) and Chengdu Brilliant Pharmaceutical Co., Ltd.* (“Brilliant Pharmaceutical”) in Research and Development",
            36: "2. Completed the Deregistration of Inner Mongolia Kangyuan Pharmaceutical Co.* (“Inner Mongolia Kangyuan”)",
            166: "Consolidated Statement of Profit or Loss and Other Comprehensive Income",
            170: "1 SIGNIFICANT ACCOUNTING POLICIES",
            221: "2 SOURCES OF ESTIMATION UNCERTAINTY",
            224: "3 REVENUE AND SEGMENT REPORTING",
            230: "4 OTHER INCOME",
            233: "5 PROFIT BEFORE TAXATION",
            236: "6 INCOME TAX IN THE CONSOLIDATED STATEMENT OF PROFIT OR LOSS",
            240: "7 DIRECTORS’ EMOLUMENTS",
            241: "8 INDIVIDUALS WITH HIGHEST EMOLUMENTS",
            242: "9 EARNINGS PER SHARE",
        },
        removed_chapters={1, 17, 19, 28, 173, 193, 194, 195, 205, 219, 231, 232, 238, 239, 267, 274},
        level_changed={},
        start_changed={},
        end_changed={167: 1523, 204: 2137},
        parent_changed={},
        children_changed={
            16: [],
            18: [],
            27: [],
            172: [],
            192: [],
            203: [2111, 2137],
            212: [2245, 2263, 2265, 2282],
            218: [],
            230: [],
            233: [2521, 2538, 2554],
            237: [],
            262: [2947, 2959, 2969, 2979],
            266: [],
            273: [],
        },
    ),
    ExpectedItem(
        66691,
        contents={
            2: "COMPANY PROFILE",
            3: "DEFINITIONS",
            6: "CORPORATE INFORMATION",
            8: "CHAIRWOMAN’S STATEMENT",
            10: "FINANCIAL HIGHLIGHTS",
            12: "MANAGEMENT DISCUSSION AND ANALYSIS",
            33: "DIRECTORS AND SENIOR MANAGEMENT",
            39: "REPORT OF DIRECTORS",
            71: "CORPORATE GOVERNANCE REPORT",
            88: "INDEPENDENT AUDITOR’S REPORT",
            94: "CONSOLIDATED STATEMENT OF PROFIT OR LOSS AND OTHER COMPREHENSIVE INCOME",
            96: "CONSOLIDATED STATEMENT OF FINANCIAL POSITION",
            98: "CONSOLIDATED STATEMENT OF CHANGES IN EQUITY",
            100: "CONSOLIDATED STATEMENT OF CASH FLOWS",
            102: "NOTES TO FINANCIAL STATEMENTS",
            170: "FIVE-YEAR FINANCIAL SUMMARY",
        },
        added_chapters={37, 55},
        title_changed={
            69: "• Zhongshan Torch Development District Manufacturing Site: GMP-compliant manufacturing capacity of 3,500L."
        },
        removed_chapters={332, 333, 346, 367, 368, 381},
        level_changed={
            37: 6,
            38: 6,
            40: 6,
            41: 6,
            42: 6,
            43: 5,
            44: 5,
            47: 5,
            48: 5,
            51: 5,
            53: 5,
            54: 6,
            55: 6,
            56: 6,
            59: 6,
            60: 6,
        },
        start_changed={},
        end_changed={39: 254, 44: 265, 48: 273, 51: 279, 53: 286, 58: 302, 393: 2111},
        parent_changed={
            37: 212,
            38: 212,
            40: 230,
            41: 230,
            42: 230,
            43: 211,
            44: 211,
            45: 260,
            46: 260,
            47: 211,
            48: 211,
            49: 268,
            50: 268,
            51: 211,
            52: 273,
            53: 211,
            54: 286,
            55: 286,
            56: 286,
            59: 294,
            60: 294,
        },
        children_changed={
            36: [212, 230, 254, 260, 265, 268, 273, 279, 286],
            39: [231, 242, 250],
            44: [261, 263],
            48: [269, 271],
            51: [274],
            57: [294, 302, 310, 315],
            58: [295, 297],
            331: [],
            345: [],
            366: [],
            380: [],
        },
    ),
    ExpectedItem(
        66818,
        contents={
            3: "CORPORATE INFORMATION & KEY DATES",
            4: "FINANCIAL HIGHLIGHTS",
            7: "2018-2022 FINANCIAL SUMMARY",
            8: "LOGISTICS FACILITIES",
            13: "CHAIRMAN\u2019S STATEMENT",
            15: "RESULTS OVERVIEW",
            16: "MANAGEMENT DISCUSSION AND ANALYSIS",
            22: "AWARDS AND CITATIONS",
            30: "CORPORATE GOVERNANCE REPORT",
            51: "DIRECTORS AND SENIOR MANAGEMENT",
            64: "REPORT OF DIRECTORS",
            91: "INDEPENDENT AUDITOR\u2019S REPORT",
            98: "STATEMENT OF ACCOUNTS",
            196: "DEFINITIONS",
        },
        added_chapters={87},
        title_changed={
            1: "CORPORATE INFORMATION & KEY DATES",
            20: "FINANCIAL HIGHLIGHTS",
            25: "2018-2022 FINANCIAL SUMMARY",
            53: "CORPORATE GOVERNANCE REPORT",
            56: "A THE BOARD",
            57: "1 RESPONSIBILITIES OF THE BOARD",
            58: "2 DELEGATION OF MANAGEMENT FUNCTION",
            59: "3 BOARD COMPOSITION",
            64: "5 INDUCTION AND CONTINUING DEVELOPMENT FOR DIRECTORS",
            65: "6 BOARD MEETINGS AND GENERAL MEETINGS",
            70: "C BOARD COMMITTEES",
            71: "1 REMUNERATION COMMITTEE",
            72: "2 AUDIT AND COMPLIANCE COMMITTEE",
            73: "3 NOMINATION COMMITTEE",
            75: "4 FINANCE COMMITTEE",
            76: "5 RISK MANAGEMENT COMMITTEE",
            77: "6 SUSTAINABILITY COMMITTEE",
            79: "E DIRECTORS’ RESPONSIBILITIES FOR FINANCIAL REPORTING",
            81: "G RISK MANAGEMENT AND INTERNAL CONTROLS",
            83: "I SHAREHOLDERS’ RIGHTS",
            84: "J DIVIDEND POLICY",
            85: "K COMPANY SECRETARY",
            86: "L CONSTITUTIONAL DOCUMENTS",
            161: "1 ACQUISITION OF THE TARGET COMPANY IN CALIFORNIA",
            162: "2 ACQUISITION OF MINORITY INTEREST IN A SUBSIDIARY IN HONG KONG",
            163: "3 ACQUISITION OF CERTAIN TRANSPORTATION AND OPERATING ASSETS",
            165: "1 FRAMEWORK AGREEMENTS WITH SFTS",
            167: "3 FRAMEWORK SERVICES AGREEMENT WITH KHL",
            168: "4 FRAMEWORK AGREEMENTS WITH KPL",
            201: "STATEMENT OF ACCOUNTS",
            204: "CONSOLIDATED STATEMENT OF COMPREHENSIVE INCOME",
            206: "CONSOLIDATED STATEMENT OF FINANCIAL POSITION",
            207: "CONSOLIDATED STATEMENT OF CASH FLOWS",
            208: "CONSOLIDATED STATEMENT OF CHANGES IN EQUITY",
            210: "1 GENERAL INFORMATION",
            211: "2 SUMMARY OF SIGNIFICANT ACCOUNTING POLICIES",
            273: "3 FINANCIAL RISK MANAGEMENT",
            313: "8 FINANCE EXPENSES",
            346: "17 ASSOCIATES AND JOINT VENTURES",
            351: "22 ACCOUNTS RECEIVABLE, PREPAYMENTS AND DEPOSITS",
            356: "25 ACCOUNTS PAYABLE, DEPOSITS RECEIVED AND ACCRUED CHARGES",
        },
        removed_chapters={2, 26, 40, 54, 202, 205, 286, 308, 316, 339, 365, 367, 390},
        level_changed=None,
        # start_changed={},
        # end_changed={},
        # parent_changed={
        #     3:31, 5:31, 6:31, 7:31, 8:31, 9:31, 10:31, 11:31, 12:31, 13:31, 14:31, 15:31, 16:31, 17:31, 18:31, 19:31,
        #     55:275, 56:275, 57:275, 58:275, 59:275, 63:275, 64:275, 65:275, 68:275, 69:275,
        #     70:275, 71:275, 72:275, 73:275, 75:275, 76:275, 77:275, 78:275, 79:275, 80:275, 81:275, 82:275,
        #     83:275, 84:275, 85:275, 86:275, 87:481, 88:488, 89:481, 90:494, 91:481, 92:503, 93:503, 94:503, 95:481,
        #     96:520, 97:520, 98:520, 99:520, 100:520, 101:481, 102:549, 103:549, 104:549, 105:549, 106:549, 107:549,
        #     108:549, 109:580, 110:549, 203:1006, 204:1006,206:1006, 207:1006, 208:1006, 209:1006,
        #     287:1418, 288:1418, 289:1418, 290:1418, 291:1418, 292:1418, 317:1042, 318:1042, 319:1583, 320:1583,
        #     321:1042, 329:1042, 330:1641, 331:1641, 337:1042, 340:1674, 341:1042, 345:1042, 346:1042, 347:1042,
        #     348:1042, 349:1042, 350:1042, 351:1042, 352:1042, 353:1042, 356:1042, 357:1042, 358:1042, 359:1042,
        #     360:1042, 361:1042, 362:1042, 366:1042, 370:1042, 371:1042, 372:1042, 373:1042, 382:1042, 385:1042,
        #     388:1042, 389:2039, 391:1042, 392:1042,
        # },
        # children_changed={
        #     1: [33, 50, 54, 60, 66, 70, 73, 76, 78, 81, 83, 85, 87, 91, 95, 101],
        #     39:[], 53: [278, 282, 353, 359, 430, 433, 438, 443, 458, 466, 472, 474, 477],
        #     87:[489], 89:[495], 91: [504, 508, 514], 95: [521, 525, 530, 536, 542],
        #     101: [550, 556, 561, 566, 570, 576, 580, 585, 588],
        #     201: [1008, 1012, 1017, 1028, 1035, 1042],
        #     209: [1043, 1048, 1354, 1460, 1502, 1543, 1548, 1552, 1554, 1574, 1583, 1594, 1641, 1674, 1705, 1724,
        #           1736, 1757, 1763, 1766, 1770, 1774, 1793, 1798, 1811, 1822, 1825, 1841, 1845, 1857, 1877, 1909, 1931,
        #           1942, 1952, 1958, 2020, 2028, 2039, 2049, 2059],
        #     285: [1434, 1444, 1447, 1449, 1451, 1453], 306:  [1513, 1538, 1541], 329: [1648, 1654],
        #     366: [1910, 1914, 1916, 1924, 1928], 388: [2040],
        #
        # },
    ),
    ExpectedItem(
        67609,
        contents={
            3: "CORPORATE INFORMATION",
            5: "CHAIRMAN\u2019S STATEMENT",
            7: "OPERATION HIGHLIGHTS",
            16: "MANAGEMENT DISCUSSION AND ANALYSIS",
            48: "REPORT OF THE BOARD OF DIRECTORS",
            72: "REPORT OF THE BOARD OF SUPERVISORS",
            73: "CORPORATE GOVERNANCE REPORT",
            87: "BIOGRAPHICAL DETAILS OF DIRECTORS, SUPERVISORS AND SENIOR MANAGEMENT",
            95: "INDEPENDENT AUDITOR\u2019S REPORT",
            101: "CONSOLIDATED STATEMENT OF PROFIT OR LOSS",
            102: "CONSOLIDATED STATEMENT OF COMPREHENSIVE INCOME",
            103: "CONSOLIDATED STATEMENT OF FINANCIAL POSITION",
            104: "CONSOLIDATED STATEMENT OF CHANGES IN EQUITY",
            105: "CONSOLIDATED STATEMENT OF CASH FLOWS",
            107: "NOTES TO FINANCIAL STATEMENTS",
            180: "DEFINITIONS",
        },
        added_chapters=set(),
        title_changed={
            31: "1 HANQUYOU (trastuzumab for injection, European brand name: Zercepac®):",
            32: "2 HANSIZHUANG (serplulimab injection):",
            33: "3 HANLIKANG (rituximab injection):",
            34: "4 HANDAYUAN (adalimumab injection):",
            35: "5 HANBEITAI (bevacizumab injection):",
            36: "6 Business Expansion:",
            37: "7 Efficient Advancement on Clinical Study Projects both Domestically and Internationally:",
            45: "International commercialisation process of HANQUYOU (trastuzumab for injection, European brand name: Zercepac®) (a therapeutic product for breast cancer and gastric cancer)",
        },
        removed_chapters={46, 52, 325, 326, 377, 378},
        # level_changed={},
        # start_changed={},
        # end_changed={},
        # parent_changed={},
        # children_changed={},
    ),
    ExpectedItem(
        68760,
        contents={
            3: "CORPORATE MISSION",
            4: "CORPORATE PROFILE",
            5: "BUSINESS STRUCTURE",
            6: "PROJECT LOCATION MAP",
            7: "CHAIRMAN\u2019S STATEMENT",
            11: "FINANCIAL REVIEW",
            14: "MANAGEMENT\u2019S DISCUSSION AND ANALYSIS",
            20: "PROJECT OVERVIEW",
            39: "DIRECTORS\u2019 AND SENIOR MANAGEMENT\u2019S PROFILE",
            51: "CORPORATE INFORMATION",
            54: "CORPORATE GOVERNANCE REPORT",
            112: "DIRECTORS\u2019 STATEMENT",
            123: "INDEPENDENT AUDITOR\u2019S REPORT",
            129: "CONSOLIDATED STATEMENT OF PROFIT OR LOSS",
            130: "CONSOLIDATED STATEMENT OF PROFIT OR LOSS AND OTHER COMPREHENSIVE INCOME",
            131: "STATEMENTS OF FINANCIAL POSITION",
            133: "STATEMENTS OF CHANGES IN EQUITY",
            138: "CONSOLIDATED STATEMENT OF CASH FLOWS",
            140: "NOTES TO FINANCIAL STATEMENTS",
            246: "STATISTICS OF SHAREHOLDINGS",
        },
        added_chapters={3, 5},
        title_changed={
            13: "(B) BOT, TOT, BOO and TOO Projects Provide Stable Cash Flow, increasing Visibility of the Group’s Future Earnings BOT、TOT、BOOTOO",
            139: "1 Principal activities",
            140: "2 Business Review",
            141: "3 Environment, Social and Governance",
            142: "4 Charitable Donations",
            143: "5 Directors",
            144: "6 Arrangements to Enable Directors to Acquire Benefits by Means of the Acquisition of Shares and Debentures",
            145: "7 Directors’ Interests in Ordinary Shares, Share Options and Debentures",
            146: "8 Directors’ and Chief Executive’s Interests and/or Short Positions in the Shares, Underlying Shares and Debentures of the Company or Any Associated Corporation",
            147: "9 Substantial Shareholders",
            # 178: 'IS I C E N V I R O N M E N T H O L D I N G S L T .D',
            230: "4. REVENUE",
        },
        removed_chapters={121, 177, 179, 231, 253, 327, 328},
        # level_changed={},
        # start_changed={},
        # end_changed={},
        # parent_changed={},
        # children_changed={},
    ),
    ExpectedItem(
        70136,
        contents={
            2: "CORPORATE INFORMATION",
            4: "CHAIRMAN’S STATEMENT",
            8: "MANAGEMENT DISCUSSION AND ANALYSIS",
            22: "PROFILE OF DIRECTORS AND SENIOR MANAGEMENT",
            26: "CORPORATE GOVERNANCE REPORT",
            45: "DIRECTORS’ REPORT",
            62: "INDEPENDENT AUDITOR’S REPORT",
            79: "CONSOLIDATED STATEMENT OF PROFIT OR LOSS AND OTHER COMPREHENSIVE INCOME",
            82: "CONSOLIDATED STATEMENT OF FINANCIAL POSITION",
            84: "CONSOLIDATED STATEMENT OF CHANGES IN EQUITY",
            86: "CONSOLIDATED STATEMENT OF CASH FLOWS",
            89: "NOTES TO THE CONSOLIDATED FINANCIAL STATEMENTS",
            287: "FINANCIAL SUMMARY",
        },
        added_chapters=set(),
        title_changed={
            1: "CORPORATE INFORMATION",
            21: "CHAIRMAN’S STATEMENT",
            30: "MANAGEMENT DISCUSSION AND ANALYSIS",
            54: "PROFILE OF DIRECTORS AND SENIOR MANAGEMENT",
            59: "CORPORATE GOVERNANCE REPORT",
            91: "DIRECTORS’ REPORT",
            136: "CONSOLIDATED STATEMENT OF PROFIT OR LOSS AND OTHER COMPREHENSIVE INCOME",
            138: "CONSOLIDATED STATEMENT OF FINANCIAL POSITION",
            139: "CONSOLIDATED STATEMENT OF CHANGES IN EQUITY",
            140: "CONSOLIDATED STATEMENT OF CASH FLOWS",
            141: "NOTES TO THE CONSOLIDATED FINANCIAL STATEMENTS",
            157: "Revenue and other income from other sources",
        },
        removed_chapters={2, 22, 31, 33, 55, 60, 92, 137, 142, 156, 158, 159, 201, 202, 203, 244, 248, 250, 262, 263, 275, 284, 289, 321},
        # level_changed={},
        # start_changed={},
        # end_changed={},
        # parent_changed={},
        # children_changed={},
    ),
    ExpectedItem(
        83431,
        contents={
            8: "COMPANY INTRODUCTION",
            9: "CORPORATE INFORMATION",
            10: "FINANCIAL SUMMARY",
            12: "CHAIRMAN’S STATEMENT",
            13: "MANAGEMENT DISCUSSION AND ANALYSIS",
            22: "DIRECTORS AND SENIOR MANAGEMENT",
            27: "DIRECTORS’ REPORT",
            45: "CORPORATE GOVERNANCE REPORT",
            61: "ENVIRONMENTAL, SOCIAL AND GOVERNANCE REPORT",
            105: "INDEPENDENT AUDITOR’S REPORT",
            109: "CONSOLIDATED STATEMENT OF COMPREHENSIVE INCOME",
            110: "CONSOLIDATED BALANCE SHEET",
            112: "CONSOLIDATED STATEMENT OF CHANGES IN EQUITY",
            113: "CONSOLIDATED STATEMENT OF CASH FLOWS",
            114: "NOTES TO THE CONSOLIDATED FINANCIAL STATEMENTS",
        },
        added_chapters={5, 9},
        title_changed={
            1: "Company Introduction",
            2: "INFORMATION ON JNBY GROUP",
            39: "DIRECTORS AND SENIOR MANAGEMENT",
            187: "1 INTRODUCTION",
            191: "2 SUSTAINABILITY MANAGEMENT",
            204: "• Product quality inspection",
            208: " Protecting children’s growth with skin-friendly textiles",
            214: "• Sustainable fabric — recycled polyester & recycled nylon",
            215: "• Sustainable fabric — regenerated cellulose fibre",
            218: "• Sesame Lab",
            220: "• Textile possibility",
            232: "4 BUILDING A COMPLIANCE CULTURE FOR RESPONSIBLE OPERATION",
            # 243: '5 TRANSITION TO LOW CARBON FOR GREEN DEVELOPMENT',
            251: "6 BUILDING HAPPY WORKPLACES WITH A HUMAN TOUCH",
            257: " Campus recruitment by JNBY",
            266: " ESG “Fabrics Talk”",
            270: "7 FOCUSING ON PUBLIC WELFARE AND SHARING BEAUTIFUL LIFE",
            271: " Central Saint Martins College of Art & Design Scholarship",
            272: " Rural Targeted Aid Programme in Guangyuan City, Sichuan Province",
            273: " Children’s Safety Escort Plan",
            274: " “My First Poem” Autism Day Activities",
            275: " LESS & “ONE NIGHT FOR CHILDREN” Public Welfare Concert",
            297: "CONSOLIDATED STATEMENT OF CHANGES IN EQUITY",
        },
        removed_chapters={
            13, 16, 20, 30, 40, 43, 46, 48, 49, 56, 60, 64, 72, 88, 95, 100, 101, 108, 117, 120, 123, 139, 144, 147,
            150, 154, 158, 159, 161, 163, 167, 173, 179, 183, 193, 196, 198, 203, 205, 209, 211, 213, 216, 221, 226,
            229, 231, 234, 235, 237, 240, 244, 246, 247, 248, 250, 255, 258, 260, 261, 265, 268, 269, 276, 278, 279,
            287, 294, 298, 300, 306, 315, 321, 327, 339, 353, 356, 361, 366, 371, 381, 383, 387, 393, 396, 402, 411,
            414, 422, 431, 437, 444, 449, 452, 455, 459, 462, 463, 467, 475, 478, 481, 482,
        },
        # level_changed={},
        # start_changed={},
        # end_changed={},
        # parent_changed={},
        # children_changed={},
    ),
    # stock=00016, year=2023
    ExpectedItem(
        83437,
        contents={
            3: "BOARD OF DIRECTORS AND COMMITTEES",
            4: "CORPORATE INFORMATION AND INFORMATION FOR SHAREHOLDERS",
            5: "FINANCIAL HIGHLIGHTS AND LAND BANK",
            7: "FIVE-YEAR FINANCIAL SUMMARY",
            8: "BUSINESS STRUCTURE",
            9: "CHAIRMAN’S STATEMENT",
            23: "BUSINESS MODEL AND STRATEGIC DIRECTION",
            25: "REVIEW OF OPERATIONS",
            95: "FINANCIAL REVIEW",
            101: "INVESTOR RELATIONS",
            103: "SUSTAINABLE DEVELOPMENT",
            113: "CORPORATE GOVERNANCE REPORT",
            132: "DIRECTORS’ REPORT",
            157: "DIRECTORS’ BIOGRAPHICAL INFORMATION",
            169: "EXECUTIVE COMMITTEE",
            170: "INDEPENDENT AUDITOR'S REPORT AND CONSOLIDATED FINANCIAL STATEMENTS",
        },
        added_chapters=set(),
        title_changed={
            60: "REVIEW OF OPERATIONS",
            83: "Projects under Development in Hong Kong by Year of Completion(1)",
            90: "Gross Rental Income in Hong Kong(1)",
            91: "Gross Rental Income in Hong Kong by Sector(1)",
            # 287: "(d) Each of Messrs. Kwok Ping-luen, Raymond, Kwok Kai-fai, Adam, Kwok Kai-wang, Christopher, Kwok Kai-chun, Geoffrey and Kwok Ho-lai, Edward had the following interests in shares of the following associated corporations:",
        },
        # TODO: 应该删除738（章节129）, 745, chapter_index=145 需要拆成两行，是两个元素块，146就可以删除。。。不能判定为1个，（是不是merge逻辑有问题）
        removed_chapters={113, 119, 122, 124, 134, 137, 146, 430, 437, 502, 503},
        # level_changed={},
        # start_changed={},
        # end_changed={},
        # parent_changed={},
        # children_changed={},
    ),
    ExpectedItem(
        83589,
        contents={
            2: "CORPORATE INFORMATION",
            4: "CHAIRMAN’S STATEMENT",
            10: "BIOGRAPHICAL DETAILS OF DIRECTORS AND SENIOR MANAGEMENT",
            14: "MANAGEMENT DISCUSSION AND ANALYSIS",
            23: "DIRECTORS’ REPORT",
            32: "CORPORATE GOVERNANCE REPORT",
            53: "ENVIRONMENTAL, SOCIAL AND GOVERNANCE REPORT",
            97: "INDEPENDENT AUDITOR’S REPORT",
            104: "CONSOLIDATED STATEMENT OF COMPREHENSIVE INCOME",
            106: "CONSOLIDATED STATEMENT OF FINANCIAL POSITION",
            108: "CONSOLIDATED STATEMENT OF CHANGES IN EQUITY",
            109: "CONSOLIDATED STATEMENT OF CASH FLOWS",
            111: "NOTES TO THE CONSOLIDATED FINANCIAL STATEMENTS",
            220: "FIVE YEARS FINANCIAL SUMMARY",
        },
        added_chapters=set(),
        title_changed={362: "8. FINANCE COSTS"},
        removed_chapters={26, 27, 28, 34, 35, 37, 41, 43, 49, 55, 59, 61, 62, 78, 100, 117, 118, 129, 133,
            136, 140, 143, 147, 150, 153, 158, 171, 173, 176, 181, 182, 183, 187, 189, 192, 195, 199, 203, 204, 206,
            208, 210, 224, 225, 227, 229, 231, 234, 235, 237, 246, 250, 258, 265, 274, 277, 286, 287, 288, 292, 305,
            308, 310, 315, 319, 321, 328, 329, 339, 343, 348, 351, 354, 357, 365, 369, 370, 372, 375, 378, 379, 382,
            383, 387, 392, 402, 407, 411, 417, 418, 422, 425, 427, 430, 435, 436, 443, 445, 447, 450, 451, 452, 455,
        },
        # level_changed={},
        # start_changed={},
        # end_changed={},
        # parent_changed={},
        # children_changed={},
    ),
    ExpectedItem(
        83654,
        contents={
            2: "FINANCIAL HIGHLIGHTS",
            3: "CHAIRMAN’S STATEMENT",
            6: "MANAGEMENT DISCUSSION AND ANALYSIS",
            15: "CORPORATE INFORMATION",
            17: "CORPORATE GOVERNANCE REPORT",
            32: "ENVIRONMENTAL, SOCIAL AND GOVERNANCE REPORT",
            89: "REPORT OF THE DIRECTORS",
            108: "INDEPENDENT AUDITOR’S REPORT",
            114: "CONSOLIDATED STATEMENT OF PROFIT OR LOSS",
            115: "CONSOLIDATED STATEMENT OF COMPREHENSIVE INCOME",
            116: "CONSOLIDATED STATEMENT OF FINANCIAL POSITION",
            117: "CONSOLIDATED STATEMENT OF CHANGES IN EQUITY",
            118: "CONSOLIDATED STATEMENT OF CASH FLOWS",
            120: "NOTES TO FINANCIAL STATEMENTS",
        },
        added_chapters=set(),
        title_changed={
            55: "ENVIRONMENTAL, SOCIAL AND GOVERNANCE REPORT",
            105: "• GMP Certification",
        },
        removed_chapters={58, 139, 153, 166, 183, 193, 194, 244, 275},
        # level_changed={},
        # start_changed={},
        # end_changed={},
        # parent_changed={},
        # children_changed={},
    ),
    ExpectedItem(
        83666,
        contents={
            2: "CORPORATE INFORMATION",
            3: "CHAIRMAN STATEMENT",
            4: "MANAGEMENT DISCUSSION AND ANALYSIS",
            18: "DIRECTORS’ REPORT",
            25: "PROFILE OF DIRECTORS AND SENIOR MANAGEMENT",
            28: "CORPORATE GOVERNANCE REPORT",
            40: "ENVIRONMENTAL, SOCIAL AND GOVERNANCE REPORT",
            50: "INDEPENDENT AUDITORS’ REPORT",
            56: "CONSOLIDATED STATEMENT OF PROFIT OR LOSS AND OTHER COMPREHENSIVE INCOME",
            57: "CONSOLIDATED STATEMENT OF FINANCIAL POSITION",
            59: "CONSOLIDATED STATEMENT OF CHANGES IN EQUITY",
            61: "CONSOLIDATED STATEMENT OF CASH FLOWS",
            63: "NOTES TO THE CONSOLIDATED FINANCIAL STATEMENTS",
            136: "FIVE YEAR FINANCIAL SUMMARY",
        },
        added_chapters=set(),
        title_changed={169: "Consolidated Statement of Profit or Loss and Other Comprehensive Income"},
        removed_chapters={105, 106, 107, 115, 170, 251, 279, 281, 291, 305},
        # level_changed={},
        # start_changed={},
        # end_changed={},
        # parent_changed={},
        # children_changed={},
    ),
    ExpectedItem(
        86364,
        contents={
            7: "STRATEGIC AND FINANCIAL HIGHLIGHTS",
            16: "CHAIRMAN’S STATEMENT",
            20: "CHIEF EXECUTIVE OFFICER’S REVIEW",
            29: "BOARD AND COMMITTEES",
            31: "BOARD OF DIRECTORS AND SENIOR MANAGEMENT",
            42: "MANAGEMENT COMMITTEE",
            45: "BUSINESS REVIEW",
            72: "FINANCIAL REVIEW",
            78: "10-YEAR FINANCIAL STATISTICS",
            81: "CORPORATE GOVERNANCE REPORT",
            99: "NOMINATION AND GOVERNANCE COMMITTEE REPORT",
            103: "AUDIT COMMITTEE REPORT",
            106: "RISK COMMITTEE REPORT",
            112: "REMUNERATION COMMITTEE REPORT",
            121: "CORPORATE SOCIAL RESPONSIBILITY COMMITTEE REPORT",
            123: "DIRECTORS’ REPORT",
            131: "AUDITOR’S REPORT",
            136: "CONSOLIDATED INCOME STATEMENT",
            137: "CONSOLIDATED STATEMENT OF COMPREHENSIVE INCOME",
            138: "CONSOLIDATED STATEMENT OF FINANCIAL POSITION",
            139: "CONSOLIDATED STATEMENT OF CHANGES IN EQUITY",
            140: "CONSOLIDATED STATEMENT OF CASH FLOWS",
            141: "NOTES TO THE CONSOLIDATED FINANCIAL STATEMENTS",
            224: "SHAREHOLDER INFORMATION",
            227: "GLOSSARY",
        },
        added_chapters={73, 133},
        title_changed={
            2: "Strategic and Financial Highlights",
            45: "CHAIRMAN’S STATEMENT",
            53: "CHIEF EXECUTIVE OFFICER’S REVIEW",
            100: "International Wrought Copper Council – director (2013~)",
            263: "Nomination and Governance Committee Report",
            308: "Corporate Social Responsibility Committee Report",
            411: "33. Margin Deposits, Mainland Security and Settlement Deposits, and Cash Collateral from Participants",
            489: "Hong Kong Exchanges and Clearing Limited",
        },
        removed_chapters={1, 54, 55, 72, 189, 190, 220, 264, 309, 345, 354, 378, 412, 456, 473},
        # level_changed={},
        # start_changed={},
        # end_changed={},
        # parent_changed={},
        # children_changed={},
    ),
    ExpectedItem(
        88698,
        contents={
            2: "STATEMENT FROM THE CHAIRMAN",
            3: "MANAGEMENT DISCUSSION & ANALYSIS",
            6: "DIRECTORS AND SENIOR MANAGEMENT",
            9: "REPORT OF THE DIRECTORS",
            28: "CORPORATE GOVERNANCE REPORT",
            43: "ENVIRONMENTAL, SOCIAL AND GOVERNANCE REPORT",
            67: "INDEPENDENT AUDITOR’S REPORT",
            73: "CONSOLIDATED INCOME STATEMENT",
            74: "CONSOLIDATED STATEMENT OF COMPREHENSIVE INCOME",
            75: "CONSOLIDATED STATEMENT OF FINANCIAL POSITION",
            76: "CONSOLIDATED CASH FLOW STATEMENT",
            77: "CONSOLIDATED STATEMENT OF CHANGES IN EQUITY",
            79: "NOTES TO THE FINANCIAL STATEMENTS",
            129: "FIVE YEAR FINANCIAL SUMMARY",
        },
        added_chapters=set(),
        title_changed={109: "Identification", 159: "1 General Information", 217: "9 Income Tax Expense"},
        removed_chapters={75, 154, 188, 218, 231, 232, 265, 266},
        # level_changed={},
        # start_changed={},
        # end_changed={},
        # parent_changed={},
        # children_changed={},
    ),
    ExpectedItem(
        90196,
        contents={
            2: "CO-CHAIRMEN’S AND CEO’S STATEMENTS",
            4: "CORPORATE INFORMATION",
            8: "FINANCIAL AND OPERATIONAL HIGHLIGHTS",
            10: "MANAGEMENT DISCUSSION AND ANALYSIS",
            40: "BIOGRAPHIES OF DIRECTORS AND SENIOR MANAGEMENT",
            46: "REPORT OF DIRECTORS",
            113: "CORPORATE GOVERNANCE REPORT",
            159: "INDEPENDENT AUDITOR’S REPORT",
            168: "CONSOLIDATED STATEMENT OF PROFIT OR LOSS",
            169: "CONSOLIDATED STATEMENT OF COMPREHENSIVE INCOME",
            170: "CONSOLIDATED STATEMENT OF FINANCIAL POSITION",
            172: "CONSOLIDATED STATEMENT OF CHANGES IN EQUITY",
            174: "CONSOLIDATED STATEMENT OF CASH FLOWS",
            176: "NOTES TO FINANCIAL STATEMENTS",
            309: "DEFINITIONS",
            315: "GLOSSARY OF TECHNICAL TERMS",
        },
        added_chapters=set(),
        # TODO 横向页面，如果文字方向为横向，则不提取title
        title_changed={
            147: "n)n. fIper Shareaveragec•Fair Immediatelyclosing priceDi",
            148: "N Nvalue of befor Ue theper Sharee",
        },
        removed_chapters={128, 129, 130, 387, 261, 393, 394, 150, 251, 339, 347, 348, 380, 382, 383},
        # level_changed={},
        # start_changed={},
        # end_changed={},
        # parent_changed={},
        # children_changed={},
    ),
]


@pytest.mark.parametrize("expected_item", EXPECTED, ids=lambda x: f"test_chapter_fixer_{x.fid}")
def test_chapter_fixer(expected_item):
    """
    测试ChapterFixer()相关方法
    """
    zip_file = Path(f"data/tests/test_chapter_fixer/{expected_item.fid}.zip")
    assert zip_file.is_file()
    with zipfile.ZipFile(zip_file, "r") as zip_ref:
        pdfinsight = PdfinsightReader(
            "",
            data=json.loads(zip_ref.read("pdfinsight.json")),
            pdf_contents=json.loads(zip_ref.read("pdf_contents.json")),
        )
        fixer = ChapterFixer(pdfinsight, pdfinsight.syllabuses)
        # 验证目录提取
        assert fixer.chapter_from_contents == expected_item.contents
        fixer.fix_titles()
        fixer.remove_invalid_chapters()
        fixer.fix_root_chapters()
        # TODO 所有用例补充完成后删除该条件
        if expected_item.parent_changed is not None:
            fixer.fix_child_chapters()
        fixer.fix_unknown_parent_chapters()
        fixer.fix_levels()
        origin_chapter_map = {c["index"]: UnifiedChapter(c, pdfinsight) for c in pdfinsight.syllabuses}
        fixed_chapter_map = {c["index"]: UnifiedChapter(c, pdfinsight) for c in fixer.get_chapters()}
        removed_indices = set()
        fixed_titles, level_changed, start_changed, end_changed, parent_changed, children_changed = (
            {},
            {},
            {},
            {},
            {},
            {},
        )
        for index, orig_chapter in origin_chapter_map.items():
            fixed_index = calc_fixed_index(index, fixer.added_indices)
            if fixed_index not in fixed_chapter_map:
                removed_indices.add(index)
                continue
            fixed = fixed_chapter_map[fixed_index]
            if fixed.title != P_BLANK.sub(" ", orig_chapter.title).strip():
                fixed_titles[index] = fixed.title
            if orig_chapter.level != fixed.level:
                level_changed[index] = fixed.level
            if orig_chapter.start != fixed.start:
                start_changed[index] = fixed.start
            if orig_chapter.end != fixed.end:
                end_changed[index] = fixed.end
            old_parent = (
                origin_chapter_map[orig_chapter.parent_index].element_index if orig_chapter.parent_index != -1 else -1
            )
            new_parent = fixed_chapter_map[fixed.parent_index].element_index if fixed.parent_index != -1 else -1
            if old_parent != new_parent:
                parent_changed[index] = new_parent
            old_children = [origin_chapter_map[idx].element_index for idx in orig_chapter.children_indices]
            new_children = [fixed_chapter_map[idx].element_index for idx in fixed.children_indices]
            if old_children != new_children:
                children_changed[index] = new_children

        # 验证已修正的标题
        if expected_item.title_changed is not None:
            assert fixed_titles == expected_item.title_changed
        # 验证已删除目录
        if expected_item.removed_chapters is not None:
            assert removed_indices == expected_item.removed_chapters
        # 验证新增的目录
        if expected_item.added_chapters is not None:
            assert fixer.added_indices == expected_item.added_chapters
        # 验证level变更
        if expected_item.level_changed is not None:
            assert level_changed == expected_item.level_changed
        # 验证start变更
        if expected_item.start_changed is not None:
            assert start_changed == expected_item.start_changed
        # 验证end变更
        if expected_item.end_changed is not None:
            assert end_changed == expected_item.end_changed
        # 验证父章节变更
        if expected_item.parent_changed is not None:
            assert parent_changed == expected_item.parent_changed
        # 验证children变更
        if expected_item.children_changed is not None:
            assert children_changed == expected_item.children_changed
