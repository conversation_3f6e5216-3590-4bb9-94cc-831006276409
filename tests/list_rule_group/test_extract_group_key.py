from remarkable.predictor.share_group_utils import extract_group_key


class TestExtractGroupKey:
    """
    测试函数group_utils.extract_group_key()
    """

    def test_alias_1(self):
        text = "the shareholders of the Company duly approved the relevant resolutions to adopt a new share option scheme (the “Scheme”)"
        assert extract_group_key(text, "option", multi=True) == {"shareoptionscheme"}
        assert extract_group_key(text, "award", multi=True) == set()

    def test_alias_2(self):
        text = "the shareholders of the Company duly approved the relevant resolutions to adopt a new share award scheme (the “Scheme”)"
        assert extract_group_key(text, "option", multi=True) == set()
        assert extract_group_key(text, "award", multi=True) == {"shareawardscheme"}

    def test_incentive_1(self):
        text = "1. Pre-IPO Share Incentive Plan"
        assert extract_group_key(text, "option") == {"pre-iposhareincentiveplan"}
        assert extract_group_key(text, "award") == {"pre-iposhareincentiveplan"}

    def test_incentive_2(self):
        text = "Remaining Life of the Pre-IPO Share Incentive Plan"
        assert extract_group_key(text, "option", multi=True) == {"pre-iposhareincentiveplan"}
        assert extract_group_key(text, "award") == {"pre-iposhareincentiveplan"}

    def test_incentive_3(self):
        text = "Post-IPO ESOP"
        # assert extract_group_key(text, "option") == set()
        # assert extract_group_key(text, "award", multi=True) == set()
        assert extract_group_key(text, "option") == {"post-ipoesop"}
        assert extract_group_key(text, "award", multi=True) == {"post-ipoesop"}

    def test_incentive_4(self):
        text = "Each participant of the Employee Incentive Scheme (the “Participant”) waives any right to contest, Persons eligible to receive Awards under the Employee Incentive Scheme (“Eligible Persons”) "
        assert extract_group_key(text, "option", multi=True) == {"employeeincentivescheme"}
        assert extract_group_key(text, "award", multi=True) == {"employeeincentivescheme"}

    def test_incentive_5(self):
        text = "Employee Share Purchase Plan (“ESPP”)"
        assert extract_group_key(text, "option", multi=True) == {"employeesharepurchaseplan", "espp"}
        assert extract_group_key(text, "award", multi=True) == {"employeesharepurchaseplan", "espp"}

    def test_incentive_6(self):
        text = "The number of Matching RSUs available for grant under the ESPP as of"
        assert extract_group_key(text, "option", multi=True) == {"espp"}
        assert extract_group_key(text, "award", multi=True) == {"espp"}

    def test_incentive_7(self):
        text = "2015 Equity Incentive Plan (the “2015 Plan”)"
        assert extract_group_key(text, "option") == {"2015equityincentiveplan", "2015plan"}
        assert extract_group_key(text, "award") == {"2015equityincentiveplan", "2015plan"}
        assert extract_group_key(text, "option", multi=True) == {"2015equityincentiveplan", "2015plan"}
        assert extract_group_key(text, "award", multi=True) == {"2015equityincentiveplan", "2015plan"}

    def test_incentive_8(self):
        text = "The Amended and Restated Co-Ownership Plan IV"
        assert extract_group_key(text, "option", multi=True) == {"co-ownershipplaniv"}
        assert extract_group_key(text, "award", multi=True) == {"co-ownershipplaniv"}

    def test_incentive_9(self):
        text = "35 EMPLOYEE SHARE SCHEME"
        assert extract_group_key(text, "award", multi=True) == {"employeesharescheme"}
        assert extract_group_key(text, "option") == {"employeesharescheme"}

    def test_incentive_10(self):
        text = """An Award may be granted in the form of RSA or RSU under the Employee Incentive Scheme."""
        assert extract_group_key(text, "option", multi=True) == {"employeeincentivescheme"}
        assert extract_group_key(text, "award") == {"employeeincentivescheme"}

    def test_incentive_11(self):
        text = "Stock incentive plan of subsidiaries"
        assert extract_group_key(text, "option", multi=True) == {"stockincentiveplanofsubsidiaries"}
        assert extract_group_key(text, "award", multi=True) == {"stockincentiveplanofsubsidiaries"}

    def test_incentive_12(self):
        text = "The first lock-up period for the First Grant under the Restricted A Share Incentive Scheme for 2019 of the Company expired on 6 January 2022"
        assert extract_group_key(text, "option") == set()
        assert extract_group_key(text, "award") == {"restrictedashareincentiveschemefor2019"}

    def test_incentive_13(self):
        text = "30,000,000 Restricted Shares shall be granted under the Incentive Scheme (the “Restricted Shares”), accounting for approximately 0.97% of the Company’s total share capital of 3,090,803,431 shares as at the date of the announcement of the draft Incentive Scheme. Specifically, 29,000,000 shares shall be granted at the initial grant, accounting for approximately 0.94% of the Company’s total share capital of 3,090,803,431 shares as at the date of the announcement of the draft Incentive Scheme, and accounting for approximately 96.67% of the total Restricted Shares available under the Incentive Scheme; and 1,000,000 shares shall be reserved, accounting for approximately 0.03% of the Company’s total share capital of 3,090,803,431 shares as at the date of the announcement of the draft Incentive Scheme, and accounting for approximately 3.33% of the total Restricted Shares available under the Incentive Scheme."
        assert extract_group_key(text, "option", multi=True) == set()
        assert extract_group_key(text, "award", multi=True) == {"restrictedshares"}

    def test_incentive_14(self):
        text = "(4) Co-Ownership Plan III Plus"
        assert extract_group_key(text, "option", multi=True) == {"co-ownershipplaniiiplus"}
        assert extract_group_key(text, "award", multi=True) == {"co-ownershipplaniiiplus"}

    def test_option_1(self):
        text = "Share Option Scheme 2012 Share Option Scheme 2022 Share Award Scheme Purpose Participants Maximum entitlement"
        assert extract_group_key(text, "option") == {"shareoptionscheme2012"}
        assert extract_group_key(text, "option", multi=True) == {"shareoptionscheme2012", "shareoptionscheme2022"}
        assert extract_group_key(text, "award", multi=True) == {"ShareAwardScheme"}

    def test_option_2(self):
        text = "Under the Share Option Scheme 2012, the Scheme Option Scheme 2022 and the Share Award Scheme, there are no:"
        assert extract_group_key(text, "option") == {"shareoptionscheme2012", "optionscheme2022"}
        assert extract_group_key(text, "option", multi=True) == {"shareoptionscheme2012", "optionscheme2022"}
        assert extract_group_key(text, "award", multi=True) == {"ShareAwardScheme"}

    def test_option_3(self):
        text = "Pursuant to an ordinary resolution passed at the annual general meeting of the Company held on 25 April 2012, the old share option scheme (the “Share Option Scheme 2012”) was adopted by the Company. The Company operates the Share Option Scheme 2012 for the purpose of providing incentives and reward to eligible participants who contribute to the success of the Group’s operations. The Share Option Scheme 2012 has been valid and effective for a period of 10 years from the date of adoption on 25 April 2012 and expired on 25 April 2022. On 1 April 2022, the Company offered to grant an aggregate of 53,300,000 share options (the “Share Options”), to a director of the Company and certain employees of the Group. As at the 31 December 2022, there were 53,300,000 outstanding options granted under the Share Option Scheme 2012 but yet to be exercised. These Share Options are valid for ten years from the date of grant on 1 April 2022 and vested on 1 April 2023. The Share Options were out-of-the-money, as the exercise price of the Share Options exceeded the average market price of the Company’ common stock."
        assert extract_group_key(text, "option") == {"shareoptionscheme2012"}
        assert extract_group_key(text, "option", multi=True) == {"shareoptionscheme2012", "oldshareoptionscheme"}
        assert extract_group_key(text, "award", multi=True) == set()

    def test_option_4(self):
        text = "The Company adopted its share option scheme (the “2013 Scheme”) on its 2013 annual general meeting held on 12 August 2013."
        assert extract_group_key(text, "option") == {"2013scheme"}
        assert extract_group_key(text, "option", multi=True) == {"2013scheme"}
        assert extract_group_key(text, "award", multi=True) == set()

    def test_option_5(self):
        text = "(i) 2021 Share Option Scheme (“2021 Scheme”)"
        assert extract_group_key(text, "option", multi=True) == {"2021shareoptionscheme", "2021scheme"}
        assert extract_group_key(text, "award", multi=True) == set()

    def test_option_6(self):
        # http://************:55647/#/project/remark/232965?treeId=4451&fileId=66449&schemaId=15&projectId=17&schemaKey=B76
        text = "The share option scheme was adopted and became effective upon passing relevant ordinary resolution at the AGM of the Company held on 25 April 2012 (the “Share Option Scheme 2012”) "
        assert extract_group_key(text, "option", multi=True) == {"shareoptionscheme2012"}
        assert extract_group_key(text, "award", multi=True) == set()

    def test_option_7(self):
        text = " the Share Option Scheme under the 2020 Scheme Mandate Limit "
        assert extract_group_key(text, "option") == {"ShareOptionScheme"}
        assert extract_group_key(text, "option", multi=True) == {"ShareOptionScheme", "2020schememandatelimit"}
        assert extract_group_key(text, "award", multi=True) == set()

    def test_option_8(self):
        text = "the resolutions for refreshment of Share Option Scheme mandate limit (“2020 Scheme Mandate Limit”)"
        assert extract_group_key(text, "option") == {"2020schememandatelimit"}
        assert extract_group_key(text, "option", multi=True) == {"ShareOptionScheme", "2020schememandatelimit"}
        assert extract_group_key(text, "award", multi=True) == set()

    def test_option_10(self):
        text = """Unless approved by the Shareholders in general meeting, the total number of Shares issued and to be 
        issued upon the exercise of Options granted and to be granted under the 2020 Share Incentive Plan and any other
         plan of our Company to an eligible Participant within any 12-month period shall not exceed 1% of the Shares
          issued and outstanding at the date of any grant."""
        assert extract_group_key(text, "option", multi=True) == {"2020shareincentiveplan"}
        assert extract_group_key(text, "award", multi=True) == {"2020shareincentiveplan"}

    def test_option_11(self):
        text = "Details of movements of the share options granted under the 2022 Scheme during the year ended 31 March 2023 were as follows:"
        assert extract_group_key(text, "option", multi=True) == {"2022scheme"}
        assert extract_group_key(text, "award", multi=True) == set()

    def test_option_12(self):
        text = "2021 scheme"
        assert extract_group_key(text, "option", multi=True) == {"2021scheme"}
        assert extract_group_key(text, "award", multi=True) == {"2021scheme"}

    def test_option_13(self):
        text = "Following the expiration of the Share Option Scheme, the Shareholders approved the adoption of a new share option scheme of the Company (the “New Share Option Scheme”) at the annual general meeting of the Company held on 29th July, 2022 "
        assert extract_group_key(text, "option", multi=True) == {"ShareOptionScheme", "newshareoptionscheme"}
        assert extract_group_key(text, "award", multi=True) == set()

    def test_option_14(self):
        text = "–Share option scheme of the Company adopted on 8 May 2014 (the “2014 Scheme”)."
        assert extract_group_key(text, "option") == {"2014scheme"}
        assert extract_group_key(text, "option", multi=True) == {"2014scheme"}
        assert extract_group_key(text, "award") == set()
        assert extract_group_key(text, "award", multi=True) == set()

    def test_option_15(self):
        text = "No share options/Share Stapled Unit options have been granted under the 2014 Scheme and the HKT 2021-2031 Option Scheme"
        assert extract_group_key(text, "option") == {"2014scheme", "hkt2021-2031optionscheme"}
        assert extract_group_key(text, "option", multi=True) == {"2014scheme", "hkt2021-2031optionscheme"}
        assert extract_group_key(text, "award") == set()
        assert extract_group_key(text, "award", multi=True) == set()

    def test_option_16(self):
        text = "–Share Stapled Units option scheme of HKT Trust and HKT adopted on 7 May 2021 (the “HKT 2021-2031 Option Scheme”)"
        assert extract_group_key(text, "option") == {"hkt2021-2031optionscheme"}
        assert extract_group_key(text, "option", multi=True) == {"hkt2021-2031optionscheme"}
        assert extract_group_key(text, "award") == set()
        assert extract_group_key(text, "award", multi=True) == set()

    def test_option_17(self):
        text = "Stock option plan"
        assert extract_group_key(text, "option", multi=True) == {"stockoptionplan"}
        assert extract_group_key(text, "award", multi=True) == set()

    def test_award_2(self):
        text = "The Company adopted a share award plan on 12 June 2020 (the “SA Plan”) "
        assert extract_group_key(text, "award", multi=True) == {"saplan", "shareawardplan"}
        assert extract_group_key(text, "option", multi=True) == set()

    def test_award_3(self):
        text = "the RSA Scheme for Core Connected Persons as of July 1, 2022"
        assert extract_group_key(text, "award", multi=True) == {"coreconnectedpersonsrsa", "rsascheme"}
        assert extract_group_key(text, "option", multi=True) == set()

    def test_award_4(self):
        text = "The Company adopted the share award plan (the “Share Award Plan”) on 23 April 2014"
        assert extract_group_key(text, "award", multi=True) == {"shareawardplan"}
        assert extract_group_key(text, "option", multi=True) == set()

    def test_award_5(self):
        text = "the share award plan shall be valid"
        assert extract_group_key(text, "award", multi=True) == {"shareawardplan"}
        assert extract_group_key(text, "option", multi=True) == set()

    def test_award_6(self):
        text = "RSU SCHEME"
        assert extract_group_key(text, "award", multi=True) == {"rsuscheme"}
        assert extract_group_key(text, "option", multi=True) == set()

    def test_award_8(self):
        # http://************:55647/#/project/remark/?treeId=6372&fileId=66430&schemaId=15&projectId=17&schemaKey=B93.2
        text = """The Company may from time to time, allot and issue new shares in the share capital of the Company to
        the trustee as directed by the Board and/or share award committee, which shall constitute part of the trust fund,
        for the grant of restricted shares to selected participant(s) as set out in the rules of the Share Award Scheme
        and the trust deed. The Board shall not make any further award which will result in the aggregated number of
        restricted shares granted pursuant to the Share Award Scheme (excluding restricted shares that have been forfeited
        in accordance with the Share Award Scheme) exceeding 10% of the issued share capital of the Company as at 18 May 2021,
        that is 387,010,265 shares of the Company.
        """
        assert extract_group_key(text, "award", multi=True) == {"ShareAwardScheme"}
        assert extract_group_key(text, "option", multi=True) == set()

    def test_award_9(self):
        text = "The First Restricted Share Award Scheme"
        assert extract_group_key(text, "award", multi=True) == {"firstrestrictedshareawardscheme"}
        assert extract_group_key(text, "option", multi=True) == set()

    def test_award_10(self):
        text = "Share grant scheme"
        assert extract_group_key(text, "award") == {"sharegrantscheme"}

    def test_award_11(self):
        # http://************:55647/#/project/remark/236162?treeId=37716&fileId=66982&schemaId=15&projectId=17&schemaKey=B76
        text = "Details of the Restricted Share Units granted"
        assert extract_group_key(text, "award", multi=True) == {"restrictedshareunits"}
        assert extract_group_key(text, "option", multi=True) == set()

    def test_award_12(self):
        # http://************:55647/#/project/remark/236162?treeId=37716&fileId=66982&schemaId=15&projectId=17&schemaKey=B76
        text = "Restricted Share Units Scheme"
        assert extract_group_key(text, "award", multi=True) == {"restrictedshareunitsscheme"}
        assert extract_group_key(text, "option", multi=True) == set()

    def test_award_13(self):
        text = "(8) Amount payable on acceptance of the RSU"
        assert extract_group_key(text, "award", multi=True) == {"rsu"}
        assert extract_group_key(text, "option", multi=True) == set()

    def test_award_15(self):
        text = "Status for 2022 of the Restricted Share Incentive Scheme for 2019 – First Grant of Restricted Shares"
        assert extract_group_key(text, "award", multi=True) == {"restrictedshareincentiveschemefor2019"}
        assert extract_group_key(text, "option", multi=True) == set()

    def test_award_16(self):
        text = "Restricted share incentive plan adopted by Sunshine Guojian"
        assert extract_group_key(text, "award", multi=True) == {"restrictedshareincentiveplanofsunshineguojian"}
        assert extract_group_key(text, "option", multi=True) == set()

    def test_award_17(self):
        text = (
            "On 19 July 2021 (the “Adoption Date”), the Company has adopted the new RSU scheme (the “New RSU Scheme”)"
        )
        assert extract_group_key(text, "award", multi=True) == {"newrsuscheme"}
        assert extract_group_key(text, "option", multi=True) == set()

    def test_award_18(self):
        text = "On 17 September 2013, the Company set up a RSU scheme (the “RSU Scheme”)"
        assert extract_group_key(text, "award", multi=True) == {"rsuscheme"}
        assert extract_group_key(text, "option", multi=True) == set()

    def test_award_19(self):
        text = "The Share Award Scheme does not involve the grant of options over any new Shares, and does not constitute a share option scheme pursuant to the then Chapter 23 of the Listing Rules in effect during the financial year ended 31 March 2023, but constitutes a share scheme of the Company under the new Chapter 23 of the Listing Rules which has come in effect on 1 January 2023."
        assert extract_group_key(text, "award") == {"ShareAwardScheme"}
        assert extract_group_key(text, "option") == {"shareoptionscheme"}

    def test_award_20(self):
        text = "The plan administrator may, from time to time, select from among all Participants to whom awards in the form of share options (the “Options”), a right to purchase restricted Shares (the “Restricted Shares”) or a right to purchase RSUs (collectively, the “Awards”), will be granted and will determine the nature and amount of each Awards."
        # assert extract_group_key(text, "option", multi=True) == set()
        # assert extract_group_key(text, "award") == {"restrictedshares"}
        assert extract_group_key(text, "award", multi=True) == {"restrictedshares", "rsus"}

    def test_award_21(self):
        text = """
        The Company has not granted further Restricted Shares under the 2020 Share Incentive Plan after the Listing Date. 
        """
        assert extract_group_key(text, "option", multi=True) == {"2020shareincentiveplan"}
        assert extract_group_key(text, "award") == {"restrictedsharesunderthe2020shareincentiveplan"}

    def test_award_22(self):
        text = "On August 27, 2018, the board of directors approved the grant of restricted shares of the Company to certain employees of the Group. Some of the restricted shares"
        assert extract_group_key(text, "option", multi=True) == set()
        assert extract_group_key(text, "award") == {"restrictedshares"}
        assert extract_group_key(text, "award", multi=True) == {"restrictedshares"}

    def test_award_23(self):
        text = "(1) share awards"
        assert extract_group_key(text, "option", multi=True) == set()
        assert extract_group_key(text, "award", multi=True) == set()

    def test_award_24(self):
        text = "Details of the restricted shares and performance shares awarded under the Share Award Scheme during the year ended 31 March 2022 are as follows:"
        assert extract_group_key(text, "option", multi=True) == set()
        assert extract_group_key(text, "award", multi=True) == {"ShareAwardScheme"}

    def test_award_25(self):
        text = "(4) 2021 STAR Market Restricted Share Incentive Scheme – A Share"
        assert extract_group_key(text, "option", multi=True) == set()
        assert extract_group_key(text, "award", multi=True) == {"2021starmarketrestrictedshareincentivescheme"}

    def test_award_26(self):
        text = "(a) senior management share grant scheme"
        assert extract_group_key(text, "option", multi=True) == set()
        assert extract_group_key(text, "award", multi=True) == {"sharegrantscheme"}

    def test_award_27(self):
        text = "The maximum number of shares that may be awarded under the Share Award Scheme (“Awarded Shares”) during its term "
        assert extract_group_key(text, "option", multi=True) == set()
        assert extract_group_key(text, "award", multi=True) == {"ShareAwardScheme"}

    def test_award_28(self):
        text = "A Share Award Scheme of the Company (the “Shimao Services Share Award Scheme”) was adopted by the Board on 28 June 2021 (the “Adoption Date II”)"
        assert extract_group_key(text, "option", multi=True) == set()
        assert extract_group_key(text, "award", multi=True) == {"ShareAwardScheme", "shimaoservicesshareawardscheme"}

    def test_award_29(self):
        text = "3. Shimao Services Shares Award Scheme"
        assert extract_group_key(text, "option", multi=True) == set()
        assert extract_group_key(text, "award", multi=True) == {"shimaoservicessharesawardscheme"}

    def test_award_30(self):
        text = "On 19 May 2017, a trust deed (the “Trust Deed”) was entered into between the Company as settlor and Bank of Communications Trustee Limited as trustee (the “Trustee”) in relation to the establishment of a trust (the “Trust”) and adoption of a share award plan (the “Plan”)."
        assert extract_group_key(text, "option", multi=True) == set()
        assert extract_group_key(text, "award", multi=True) == {"shareawardplan"}
