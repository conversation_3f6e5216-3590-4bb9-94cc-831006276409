*.DS_Store
*.pyc
*.swp
*.swo
*.ipynb
watch.json
dump.rdb
my_python/
*.o303
*.pdb
*.db
data/pg_data
data/ssl
data/files
data/training_cache
data/rule_answers
data/prompter
tmp
*~

.idea/

# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
# *.so

# Distribution / packaging
.Python
env/
./build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*,cover
.hypothesis/
tests/test_api.lock

# Translations
*.mo

# Django stuff:
*.log
local_settings.py

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# celery beat schedule file
celerybeat-schedule*

# dotenv
.env

# virtualenv
.direnv/
.envrc
.venv/
venv/
ENV/

# Spyder project settings
.spyderproject

# Rope project settings
.ropeproject

# vscode
.vs/
.vscode/

# node
node_modules/

# local config
config/config-local*.yml
config/config-usr*.yml

data/**/*.xls
error_reports/
history_reports/
.run/
.pytest_cache/
.ruff_cache/
.claude/
.kiro/