# List of revisions that are hidden from git blame annotations.
# This concerns massive code re-formatting, renaming, large changes
# that were later reverted, etc.
#
# Configure your git so that it always ignores the revisions listed
# in this file:
#
#     git config --local blame.ignoreRevsFile .git-blame-ignore-revs
#
# When adding a new revision to the list, please put its commit message
# in a comment above it.

# style: Format the source code by `ruff`
08a1fc8ab4a926d90280e62704d1f98660630ea0
b11a95857a7462804ed5077920233ba4295c058c
18cb2e7a201653e27376989c67f4a7949eed8c18
2c439077061eb82c43d489ca3cc2a9519d5ffc65
6d5bce55a322635f73f3047c0524f897e39de21f
0325d5cdfe5cdb2eaccba1b53824196d591a9708
40d166821b51bf8fad700eec8270b99708ea0ae9
# style: remove all `pylint` qa comments
edff75aa4af4f4621360cf45702c13996aa9998a
# style: format code with ruff
3dd240cab07668eebce800f3c3878fa2888b8fc5
# style: format code with ruff version `0.4.0`
ccb11d8e196d816de57863a468243b1e286071af
# style: format code with ruff version `0.9.3`
d97b7faf53f40fc42d990dc9bd3e0852a45db683
