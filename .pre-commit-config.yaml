default_stages: [ pre-commit ]
fail_fast: true

repos:
  - repo: local
    hooks:
      - id: ruff-format
        name: ruff-format
        entry: ruff format
        args: [ --verbose, --force-exclude, --respect-gitignore ]
        language: system
        types: [ python ]
        description: "Ruff code formatter"

      - id: ruff
        name: ruff
        description: "Ruff code checker"
        entry: ruff check
        args: [ --fix, --exit-non-zero-on-fix, --verbose, --force-exclude, --respect-gitignore ]
        language: system
        types: [ python ]

      - id: check-untracked-alembic-migrations
        name: check-untracked-alembic-migrations
        description: Check the untracked alembic migrations.
        entry: check-untracked-alembic-migrations
        args: [ '-c', 'misc/alembic.ini' ]
        language: python
        types: [ python ]
        pass_filenames: false  # don't pass filenames to the hook

      - id: pytest
        name: pytest
        entry: bash -c 'inv code.test'
        language: system
        stages: [ pre-push ]
        pass_filenames: false

  - repo: https://github.com/commitizen-tools/commitizen
    rev: v3.17.0
    hooks:
      - id: commitizen
        stages: [ commit-msg ]

  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
      - id: check-added-large-files
        stages: [ pre-commit ]
