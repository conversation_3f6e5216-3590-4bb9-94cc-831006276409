[project]
name = "Jura"
version = "6.0"
requires-python = ">=3.12,<3.13"
dependencies = [
    "pdfparser==2.3.38; sys_platform == 'linux'",
    "palladium==0.5.19; sys_platform == 'linux'",
    "pdfparser==2.3.24 ; sys_platform == 'darwin'",
    "palladium==0.5.7 ; sys_platform == 'darwin'",
    "aipod~=1.3.3",
    "interdoc~=0.0.27",
    "speedy==0.1.88",
    "tornado==6.4.2",
    "utensils~=0.1.57",
    "PyYAML~=6.0.1",
    "peewee-async[postgresql]~=0.12.2",
    "SQLAlchemy~=1.4.52",
    "Babel~=2.7.0",
    "celery[redis]~=5.5.1",
    "scikit-learn~=1.4.2",
    "xlrd~=2.0.0",
    "jieba-fast~=0.53",
    "Mako~=1.1.0",
    "openpyxl~=3.1.0",
    "xlwt~=1.3.0",
    "webargs~=8.4.0",
    "nltk~=3.8.0",
    "attrs~=23.2.0",
    "dateparser~=1.2.0",
    "python-redis-lock~=4.0.0",
    "gunicorn~=23.0.0",
    "pyparsing~=3.1.2",
    "httpx[zstd]~=0.28.1",
    "plusminus~=0.7.0",
    "thefuzz~=0.19.0",
    "python-Levenshtein~=0.25.1",
    "msgspec~=0.19.0",
    "aiofile~=3.8.8",
    "PyMuPDF~=1.24.4",
    "pyspellchecker~=0.8.1",
    "fire~=0.7.0",
    "openai~=1.63.2",
    "tiktoken~=0.8.0",
    "fastapi[standard]~=0.115.0",
    "pydantic~=2.10.4",
    "fastapi-permissions~=0.2.7",
    "python-multipart~=0.0.7",
    "uvicorn[standard]~=0.34.0",
    "uvicorn-worker~=0.3.0",
    "a2wsgi==1.10.0",
    "traceback-with-variables~=2.2.0",
    "beautifulsoup4~=4.12.3",
    "python3-saml~=1.16.0",
    "lxml~=5.4.0",
    "more_itertools~=10.3.0",
    "numpy~=1.26.4",
    "magika==0.6.1",
    "pyjwt~=2.9.0",
    "cefevent~=0.5.6",
    "alibabacloud_sls20201230==5.6.0",
    "polars[calamine,pandas,xlsxwriter]~=1.24.0",
    "python-dotenv~=1.0.1",
    "pgvector~=0.3.2",
    "bcrypt~=4.2.0",
    "Jinja2~=3.1.4",
    "xmlsec~=1.3.15",
]

[dependency-groups]
dev = [
    "matplotlib~=3.10.1",
    "granian~=2.0.1",
    { include-group = "lint" },
    { include-group = "test" },
    { include-group = "ops" },
]
ops = [
    "alembic~=1.15.2",
    "cython~=3.0.12",
    "supervisor~=6.6.9",
    "jinja2-cli",
    "invoke",
    "rust-just~=1.39.0",
]
lint = [
    "pre-commit>=4.2.0",
    "ruff~=0.11.2",
]
test = [
    "pytest~=8.3.5",
    "pytest-asyncio~=0.24.0",
    "pytest-xdist~=3.6.1",
]

[tool.uv]
package = false
default-groups = ["ops"]
allow-insecure-host = ["**********", "mirrors.tuna.tsinghua.edu.cn", "repo.huaweicloud.com"]
# lxml https://github.com/SAML-Toolkits/python3-saml#note
no-binary-package = ["speedy", "interdoc", "utensils", "lxml", "xmlsec"]

[tool.uv.sources]
speedy = { index = "pai-pypi-dev" }
aipod = { index = "pai-pypi" }
pdfparser = { index = "pai-pypi" }
palladium = { index = "pai-pypi" }
utensils = { index = "pai-pypi" }
invoke = { index = "pai-pypi" }
supervisor = { index = "pai-pypi" }
interdoc = { index = "pai-pypi" }

[[tool.uv.index]]
name = "pai-pypi"
url = "http://**********:3141/cheftin/pypi"
explicit = true

[[tool.uv.index]]
name = "tsinghua-pypi"
url = "https://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple"

[[tool.uv.index]]
# https://docs.astral.sh/uv/configuration/indexes/#pinning-a-package-to-an-index
name = "pai-pypi-dev"
url = "http://**********:3141/cheftin/dev"
default = true

[tool.pytest.ini_options]
minversion = "6.0"
addopts = "--maxfail=1 --disable-warnings -n auto --dist loadgroup -ra -q -vv"
asyncio_default_fixture_loop_scope = "session"
asyncio_mode = "auto"
testpaths = [
    "tests",
]

[tool.ruff]
# Allow unused variables when underscore-prefixed.
target-version = "py312"
builtins = ["_"]
line-length = 120
exclude = [
    "tests",
    "template",
    "scripts",
    "misc",
    "docker",
]

[tool.ruff.lint]
dummy-variable-rgx = "^(_+|(_+[a-zA-Z0-9_]*[a-zA-Z0-9]+?))$"
ignore = [
    "B008", # do not perform function calls in argument defaults
    "C901", # too complex
    "B905", # zip() strict parameter checking
    "RET503", # missing explicit return
    "RET504", # unnecessary assignment
    "RET505", # unnecessary else/elif after return statement
    "W191", # indentation contains tabs(ruff will fix it)
    "E501", # line too long(ruff will fix it)
]
select = [
    "E", # pycodestyle errors
    "W", # pycodestyle warnings
    "F", # pyflakes
    "I", # isort
    "C", # flake8-comprehensions
    "B", # flake8-bugbear
]
# Allow autofix for all enabled rules (when `--fix`) is provided.
unfixable = []

[tool.ruff.format]
# Like Black, use double quotes for strings.
quote-style = "double"
# Like Black, indent with spaces, rather than tabs.
indent-style = "space"
# Like Black, automatically detect the appropriate line ending.
line-ending = "auto"

[tool.ruff.lint.isort]
known-third-party = ["pdfparser", "speedy", "utensils"]

[tool.ruff.lint.per-file-ignores]
"__init__.py" = ["F401"]
