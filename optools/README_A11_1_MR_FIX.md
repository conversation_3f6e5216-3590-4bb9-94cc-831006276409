# A11.1-MR关联修复脚本

## 问题描述

根据业务逻辑，当启用`feature.process_jura61_module`功能时，如果存在type=A11.1的文件记录，对应也应该有一条type=MR的记录。但是由于某些原因，一些A11.1文件没有正确关联到MR记录。

相关代码逻辑：
```python
if get_config("feature.process_jura61_module") and "A11.1" in doc_types and "MR" not in doc_types:
    doc_types.append("MR")
```

## 脚本功能

### 主脚本：`fix_a11_1_mr_association.py`

主要功能：
1. 查找所有type=A11.1但是没有对应MR的记录，可以按照时间筛选，默认所有
2. 在hkex_file表中创建type=MR的记录
3. 在NewFile表中更新mold_list，添加MR的mold ID
4. 调用NewQuestion.create_question生成question的记录

### 测试脚本：`test_fix_a11_1_mr_association.py`

用于验证环境和数据库连接，以及分析当前数据状态。

## 使用方法

### 1. 运行测试脚本（推荐先运行）

```bash
python optools/test_fix_a11_1_mr_association.py
```

这个脚本会：
- 测试数据库连接
- 显示A11.1和MR文件的统计信息
- 分析样本数据
- 验证常量值

### 2. 试运行主脚本

```bash
# 查看所有需要修复的A11.1文件
python optools/fix_a11_1_mr_association.py --dry-run

# 查看指定时间范围内需要修复的文件
python optools/fix_a11_1_mr_association.py --start-date 2024-01-01 --end-date 2024-12-31 --dry-run
```

### 3. 执行修复

```bash
# 修复所有A11.1文件
python optools/fix_a11_1_mr_association.py

# 修复指定时间范围内的文件
python optools/fix_a11_1_mr_association.py --start-date 2024-01-01 --end-date 2024-12-31

# 强制执行（忽略process_jura61_module配置检查）
python optools/fix_a11_1_mr_association.py --force
```

## 参数说明

- `--start-date YYYY-MM-DD`: 开始日期，根据release_time字段筛选
- `--end-date YYYY-MM-DD`: 结束日期，根据release_time字段筛选
- `--dry-run`: 试运行模式，不实际修改数据，只显示将要处理的文件
- `--force`: 强制执行，忽略`feature.process_jura61_module`配置检查

## 脚本逻辑详解

### 查找逻辑

1. 查询所有type=A11.1的HKEXFile记录
2. 对于每个A11.1文件，检查是否存在相同fid且type=MR的记录
3. 如果不存在，则将该文件加入待修复列表

### 修复逻辑

对于每个需要修复的A11.1文件：

1. **创建HKEXFile记录**：
   - 使用相同的fid
   - type设置为"MR"
   - 其他字段复制自A11.1记录，但修改tu_hash以避免冲突

2. **更新NewFile记录**：
   - 在mold_list中添加`special_mold.v6_1_mr_id`（值为36）

3. **创建NewQuestion记录**：
   - 调用`NewQuestion.create_question(fid, special_mold.v6_1_mr_id)`
   - 这会创建对应的question记录

### 注意事项

- **HKEXFileMeta表**：由于fid字段是unique的，脚本不会在此表中创建新记录
- **事务处理**：所有修改都在数据库事务中执行，确保数据一致性
- **冲突处理**：如果MR记录已存在，脚本会跳过创建步骤
- **配置检查**：默认会检查`feature.process_jura61_module`配置，可用`--force`跳过

## 数据库表结构

### HKEXFile表
- 存储文件基本信息
- `type`字段区分文档类型（"A11.1", "MR"等）
- 同一个fid可以有多条不同type的记录

### HKEXFileMeta表
- 存储文件元数据
- `fid`字段是unique的，一个fid只能有一条记录
- `doc_type`字段存储DocType枚举值

### NewFile表
- 文件表，存储文件的基本信息
- `mold_list`字段存储支持的schema类型列表

### NewQuestion表
- 问题表，通过`fid`关联到文件
- `mold`字段指定schema类型

## 常量值

- `DocType.MR = 36`
- `special_mold.v6_1_mr_id = 36`

## 安全性

- 脚本包含试运行模式，可以先查看将要修改的数据
- 所有修改都在事务中执行
- 包含详细的日志记录
- 执行前会要求用户确认

## 故障排除

如果脚本执行失败，请检查：

1. 数据库连接是否正常
2. 相关表是否存在
3. 权限是否足够
4. 配置文件是否正确加载

可以先运行测试脚本来诊断问题：
```bash
python optools/test_fix_a11_1_mr_association.py
```
