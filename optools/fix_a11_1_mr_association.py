#!/usr/bin/env python3
"""
脚本功能：修复A11.1文件没有正确关联到MR的问题

主要功能：
1. 查找所有type=A11.1但是没有对应MR的记录，可以按照时间筛选，默认所有
2. 在hkex_file表中创建type=MR的记录
3. 在hkex_file_meta表中创建这条记录
4. 在file表中创建这条记录
5. 调用make_question生成question的记录

使用方法：
python optools/fix_a11_1_mr_association.py [--start-date YYYY-MM-DD] [--end-date YYYY-MM-DD] [--dry-run] [--force]
"""

import argparse
import asyncio
import logging
import sys
from datetime import datetime
from typing import List, Optional

sys.path.append(".")

from remarkable.common.constants import DocType
from remarkable.config import get_config
from remarkable.db import pw_db
from remarkable.models.hkex_file import HKEXFile
from remarkable.models.hkex_file_meta import HKEXFileMeta
from remarkable.models.mold import special_mold
from remarkable.models.new_file import NewFile
from remarkable.models.new_question import NewQuestion
from remarkable.security.authtoken import generate_timestamp

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


async def find_a11_1_without_mr(start_date: Optional[str] = None, end_date: Optional[str] = None) -> List[dict]:
    """
    查找所有type=A11.1但是没有对应MR的记录
    
    Args:
        start_date: 开始日期 (YYYY-MM-DD)
        end_date: 结束日期 (YYYY-MM-DD)
    
    Returns:
        List[dict]: A11.1文件记录列表
    """
    logger.info("开始查找A11.1文件但没有对应MR记录的文件...")
    
    # 构建查询条件
    conditions = [HKEXFile.type == "A11.1"]
    
    if start_date:
        start_timestamp = int(datetime.strptime(start_date, "%Y-%m-%d").timestamp())
        conditions.append(HKEXFile.release_time >= start_timestamp)
        
    if end_date:
        end_timestamp = int(datetime.strptime(end_date, "%Y-%m-%d").timestamp())
        conditions.append(HKEXFile.release_time <= end_timestamp)
    
    # 查询所有A11.1文件
    a11_1_query = (
        HKEXFile.select(
            HKEXFile.fid,
            HKEXFile.stock_code,
            HKEXFile.company,
            HKEXFile.name,
            HKEXFile.headline,
            HKEXFile.url,
            HKEXFile.release_time,
            HKEXFile.hash,
            HKEXFile.size,
            HKEXFile.tu_hash,
            HKEXFile.pdf_hash,
            HKEXFile.meta
        )
        .where(*conditions)
        .order_by(HKEXFile.release_time.desc())
    )
    
    a11_1_files = await pw_db.execute(a11_1_query)
    logger.info(f"找到 {len(a11_1_files)} 个A11.1文件")
    
    # 检查哪些A11.1文件没有对应的MR记录
    missing_mr_files = []
    
    for a11_1_file in a11_1_files:
        # 检查同一个hash是否有对应的MR记录
        # 根据业务逻辑，同一个hash应该既有A11.1记录也有MR记录
        mr_exists = await pw_db.scalar(
            HKEXFile.select(1)
            .where(
                HKEXFile.hash == a11_1_file.hash,
                HKEXFile.type == "MR"
            )
            .limit(1)
        )

        if not mr_exists:
            missing_mr_files.append({
                'fid': a11_1_file.fid,
                'stock_code': a11_1_file.stock_code,
                'company': a11_1_file.company,
                'name': a11_1_file.name,
                'headline': a11_1_file.headline,
                'url': a11_1_file.url,
                'release_time': a11_1_file.release_time,
                'hash': a11_1_file.hash,
                'size': a11_1_file.size,
                'tu_hash': a11_1_file.tu_hash,
                'pdf_hash': a11_1_file.pdf_hash,
                'meta': a11_1_file.meta
            })
    
    logger.info(f"找到 {len(missing_mr_files)} 个A11.1文件没有对应的MR记录")
    return missing_mr_files


async def create_mr_records(a11_1_file: dict, dry_run: bool = False) -> bool:
    """
    为A11.1文件创建对应的MR记录
    
    Args:
        a11_1_file: A11.1文件信息
        dry_run: 是否为试运行模式
    
    Returns:
        bool: 是否成功创建
    """
    try:
        stock_code = a11_1_file['stock_code']
        logger.info(f"为股票代码 {stock_code} 创建MR记录...")
        
        if dry_run:
            logger.info(f"[DRY RUN] 将为股票代码 {stock_code} 创建MR记录")
            return True
        
        # 获取原始文件信息
        original_file = await NewFile.get_by_id(a11_1_file['fid'])
        if not original_file:
            logger.error(f"找不到fid={a11_1_file['fid']}的文件记录")
            return False
        
        # 获取原始文件的meta信息
        original_meta = await HKEXFileMeta.find_by_fid(a11_1_file['fid'])
        if not original_meta:
            logger.error(f"找不到fid={a11_1_file['fid']}的meta记录")
            return False
        
        async with pw_db.atomic():
            # 1. 在hkex_file表中创建MR记录
            # 注意：这里创建的是同一个fid的不同type记录
            mr_hkex_file_data = {
                'name': a11_1_file['name'].replace('A11.1', 'MR') if 'A11.1' in a11_1_file['name'] else f"MR - {a11_1_file['name']}",
                'fid': a11_1_file['fid'],  # 使用相同的fid
                'company': a11_1_file['company'],
                'stock_code': stock_code,
                'type': 'MR',  # 关键：这里是MR类型
                'headline': a11_1_file['headline'].replace('A11.1', 'MR') if 'A11.1' in a11_1_file['headline'] else f"MR - {a11_1_file['headline']}",
                'url': a11_1_file['url'],
                'result': {},
                'release_time': a11_1_file['release_time'],
                'finished_utc': 0,
                'hash': a11_1_file['hash'],
                'size': a11_1_file['size'],
                'tu_hash': f"{a11_1_file['tu_hash']}_MR",  # 修改tu_hash以避免冲突
                'pdf_hash': a11_1_file['pdf_hash'],
                'meta': a11_1_file['meta'],
                'status': 0,
                'created_utc': generate_timestamp(),
                'updated_utc': generate_timestamp(),
                'deleted_utc': 0
            }
            
            # 检查是否已存在MR记录
            existing_mr = await pw_db.first(
                HKEXFile.select()
                .where(
                    HKEXFile.fid == a11_1_file['fid'],
                    HKEXFile.type == 'MR'
                )
            )

            if not existing_mr:
                # 直接插入新记录，因为fid+type的组合应该是唯一的
                await pw_db.execute(HKEXFile.insert(**mr_hkex_file_data))
                logger.info(f"成功在hkex_file表中创建MR记录，fid={a11_1_file['fid']}")
            else:
                logger.info(f"hkex_file表中已存在MR记录，fid={a11_1_file['fid']}")
            
            # 2. 注意：HKEXFileMeta表中fid是unique的，所以我们不创建新记录
            # 我们只需要确保现有记录支持MR类型的处理
            # 实际上，根据业务逻辑，HKEXFileMeta记录应该已经存在，我们不需要修改它
            logger.info(f"HKEXFileMeta记录已存在，fid={a11_1_file['fid']}，不需要创建新记录")
            
            # 3. 检查NewFile表中是否需要更新mold_list
            if special_mold.v6_1_mr_id not in (original_file.mold_list or []):
                new_mold_list = (original_file.mold_list or []) + [special_mold.v6_1_mr_id]
                await pw_db.execute(
                    NewFile.update(mold_list=new_mold_list)
                    .where(NewFile.id == a11_1_file['fid'])
                )
                logger.info(f"成功更新NewFile表的mold_list，fid={a11_1_file['fid']}")

            # 4. 创建question记录
            question = await NewQuestion.find_by_fid_mid(a11_1_file['fid'], special_mold.v6_1_mr_id)
            if not question:
                question = await NewQuestion.create_question(a11_1_file['fid'], special_mold.v6_1_mr_id)
                logger.info(f"成功创建question记录，qid={question.id}, fid={a11_1_file['fid']}")
            else:
                logger.info(f"question记录已存在，qid={question.id}, fid={a11_1_file['fid']}")
        
        return True
        
    except Exception as e:
        logger.error(f"为股票代码 {stock_code} 创建MR记录时发生错误: {str(e)}")
        return False


async def main():
    parser = argparse.ArgumentParser(description='修复A11.1文件没有正确关联到MR的问题')
    parser.add_argument('--start-date', help='开始日期 (YYYY-MM-DD)')
    parser.add_argument('--end-date', help='结束日期 (YYYY-MM-DD)')
    parser.add_argument('--dry-run', action='store_true', help='试运行模式，不实际修改数据')
    parser.add_argument('--force', action='store_true', help='强制执行，忽略配置检查')

    args = parser.parse_args()

    logger.info("开始执行A11.1-MR关联修复脚本...")

    # 检查process_jura61_module配置
    if not args.force and not get_config("feature.process_jura61_module", False):
        logger.warning("process_jura61_module功能未启用，如果确认要执行请使用--force参数")
        return
    
    # 查找需要修复的A11.1文件
    missing_mr_files = await find_a11_1_without_mr(args.start_date, args.end_date)
    
    if not missing_mr_files:
        logger.info("没有找到需要修复的A11.1文件")
        return
    
    logger.info(f"找到 {len(missing_mr_files)} 个需要修复的A11.1文件")

    # 显示统计信息
    stock_codes = set(file_info['stock_code'] for file_info in missing_mr_files)
    logger.info(f"涉及 {len(stock_codes)} 个不同的股票代码")

    if args.dry_run:
        logger.info("=== 试运行模式 ===")
        for file_info in missing_mr_files:
            logger.info(f"股票代码: {file_info['stock_code']}, 文件名: {file_info['name']}, fid: {file_info['fid']}")
        logger.info(f"涉及的股票代码: {sorted(stock_codes)}")
        return
    
    # 确认是否继续
    response = input(f"确认要为 {len(missing_mr_files)} 个A11.1文件创建MR记录吗？(y/N): ")
    if response.lower() != 'y':
        logger.info("操作已取消")
        return
    
    # 执行修复
    success_count = 0
    failed_count = 0
    
    for file_info in missing_mr_files:
        success = await create_mr_records(file_info, dry_run=False)
        if success:
            success_count += 1
        else:
            failed_count += 1
    
    logger.info(f"修复完成！成功: {success_count}, 失败: {failed_count}")


if __name__ == "__main__":
    asyncio.run(main())
