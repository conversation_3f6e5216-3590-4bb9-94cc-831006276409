#!/usr/bin/env python3
"""
测试脚本：验证A11.1-MR关联修复脚本的基本功能
"""

import asyncio
import logging
import sys

sys.path.append(".")

from remarkable.common.constants import DocType
from remarkable.db import pw_db
from remarkable.models.hkex_file import HKEXFile
from remarkable.models.hkex_file_meta import HKEXFileMeta
from remarkable.models.mold import special_mold
from remarkable.models.new_file import NewFile
from remarkable.models.new_question import NewQuestion

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


async def test_database_connections():
    """测试数据库连接"""
    try:
        # 测试基本查询
        count = await pw_db.scalar(HKEXFile.select(HKEXFile.id.count()))
        logger.info(f"HKEXFile表中共有 {count} 条记录")
        
        count = await pw_db.scalar(HKEXFileMeta.select(HKEXFileMeta.id.count()))
        logger.info(f"HKEXFileMeta表中共有 {count} 条记录")
        
        count = await pw_db.scalar(NewFile.select(NewFile.id.count()))
        logger.info(f"NewFile表中共有 {count} 条记录")
        
        count = await pw_db.scalar(NewQuestion.select(NewQuestion.id.count()))
        logger.info(f"NewQuestion表中共有 {count} 条记录")
        
        return True
    except Exception as e:
        logger.error(f"数据库连接测试失败: {str(e)}")
        return False


async def test_a11_1_files():
    """测试A11.1文件查询"""
    try:
        # 查询A11.1文件
        a11_1_count = await pw_db.scalar(
            HKEXFile.select(HKEXFile.id.count())
            .where(HKEXFile.type == "A11.1")
        )
        logger.info(f"找到 {a11_1_count} 个A11.1文件")
        
        # 查询MR文件
        mr_count = await pw_db.scalar(
            HKEXFile.select(HKEXFile.id.count())
            .where(HKEXFile.type == "MR")
        )
        logger.info(f"找到 {mr_count} 个MR文件")
        
        # 查询同时有A11.1和MR的fid
        query = """
        SELECT COUNT(DISTINCT a.fid) 
        FROM hkex_file a 
        INNER JOIN hkex_file m ON a.fid = m.fid 
        WHERE a.type = 'A11.1' AND m.type = 'MR'
        """
        both_count = await pw_db.scalar_sql(query)
        logger.info(f"同时有A11.1和MR记录的fid数量: {both_count}")
        
        # 查询只有A11.1没有MR的fid
        query = """
        SELECT COUNT(DISTINCT a.fid) 
        FROM hkex_file a 
        WHERE a.type = 'A11.1' 
        AND NOT EXISTS (
            SELECT 1 FROM hkex_file m 
            WHERE m.fid = a.fid AND m.type = 'MR'
        )
        """
        missing_mr_count = await pw_db.scalar_sql(query)
        logger.info(f"只有A11.1没有MR记录的fid数量: {missing_mr_count}")
        
        return True
    except Exception as e:
        logger.error(f"A11.1文件查询测试失败: {str(e)}")
        return False


async def test_constants():
    """测试常量值"""
    try:
        logger.info(f"DocType.MR = {DocType.MR}")
        logger.info(f"DocType.MR.value = {DocType.MR.value}")
        logger.info(f"special_mold.v6_1_mr_id = {special_mold.v6_1_mr_id}")
        
        return True
    except Exception as e:
        logger.error(f"常量测试失败: {str(e)}")
        return False


async def test_sample_data():
    """测试样本数据"""
    try:
        # 获取一个A11.1文件样本
        sample_a11_1 = await pw_db.first(
            HKEXFile.select()
            .where(HKEXFile.type == "A11.1")
            .limit(1)
        )
        
        if sample_a11_1:
            logger.info(f"样本A11.1文件: fid={sample_a11_1.fid}, stock_code={sample_a11_1.stock_code}")
            
            # 检查是否有对应的MR记录
            mr_exists = await pw_db.scalar(
                HKEXFile.select(1)
                .where(
                    HKEXFile.fid == sample_a11_1.fid,
                    HKEXFile.type == "MR"
                )
                .limit(1)
            )
            logger.info(f"该文件是否有MR记录: {bool(mr_exists)}")
            
            # 检查NewFile记录
            new_file = await NewFile.get_by_id(sample_a11_1.fid)
            if new_file:
                logger.info(f"NewFile记录存在，mold_list: {new_file.mold_list}")
                has_mr_mold = special_mold.v6_1_mr_id in (new_file.mold_list or [])
                logger.info(f"是否包含MR mold: {has_mr_mold}")
            else:
                logger.info("NewFile记录不存在")
            
            # 检查HKEXFileMeta记录
            file_meta = await HKEXFileMeta.find_by_fid(sample_a11_1.fid)
            if file_meta:
                logger.info(f"HKEXFileMeta记录存在，doc_type: {file_meta.doc_type}")
            else:
                logger.info("HKEXFileMeta记录不存在")
        else:
            logger.info("没有找到A11.1文件样本")
        
        return True
    except Exception as e:
        logger.error(f"样本数据测试失败: {str(e)}")
        return False


async def main():
    logger.info("开始测试A11.1-MR关联修复脚本...")
    
    tests = [
        ("数据库连接", test_database_connections),
        ("常量值", test_constants),
        ("A11.1文件查询", test_a11_1_files),
        ("样本数据", test_sample_data),
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        logger.info(f"\n=== 测试: {test_name} ===")
        try:
            result = await test_func()
            if result:
                logger.info(f"✓ {test_name} 测试通过")
                passed += 1
            else:
                logger.error(f"✗ {test_name} 测试失败")
                failed += 1
        except Exception as e:
            logger.error(f"✗ {test_name} 测试异常: {str(e)}")
            failed += 1
    
    logger.info(f"\n=== 测试结果 ===")
    logger.info(f"通过: {passed}, 失败: {failed}")
    
    if failed == 0:
        logger.info("所有测试通过！脚本应该可以正常运行。")
    else:
        logger.warning("部分测试失败，请检查环境配置。")


if __name__ == "__main__":
    asyncio.run(main())
