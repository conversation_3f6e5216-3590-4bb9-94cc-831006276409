#!/bin/bash

set -xe

run() {
  "$@"
  _exit_code=$?
  if [ ${_exit_code} -ne 0 ]; then
    echo "Error: exec $* with exit code ${_exit_code}"
    exit ${_exit_code}
  fi
}

# deploy j2 ==> hkex uat-prod ==> https://jura-uat.paodingai.com

# git
echo "${GO_REVISION_SCRIBER_BACKEND_HKEX:0:8}" >.git_revision
echo "${GO_MATERIAL_BRANCH_SCRIBER_BACKEND_HKEX}" >>.git_revision

# frontend
run rsync -avz --progress --delete ../scriber_front/dist_hkex/ ./remarkable/static/

# backend
run rsync -avzh --progress --delete \
  --exclude=/data/files --exclude=/data/training_cache --exclude=/data/export_answer_data \
  --exclude=/data/tmp \
  ./ ci@***********:/data/scriber_hkex_uat2_web/code_src/Scriber-Backend/

# server
run ssh ci@*********** "sudo docker exec -i scriber_hkex_uat2_web bash -c '
  pip install --upgrade -r misc/prod-requirements.txt &&
  rm -rf data/tmp/celerybeat-schedule.db &&
  gosu scriber inv db.upgrade &&
  gosu scriber supervisorctl start all &&
  gosu scriber supervisorctl restart scriber:'"

# send messages
if [ -f "/data/ci/fitout/autodoc/send_mm_msg.sh" ]; then
  bash /data/ci/fitout/autodoc/send_mm_msg.sh http://mm.paodingai.com/hooks/xffd4wkndpnjubqd9z9puzoxaa scriber \[Scriber港交所UAT2环境\]\(https://jura-uat2.paodingai.com\)已更新\:\ \`前端\:${GO_REVISION_SCRIBER_FRONT:0:8}\(${GO_MATERIAL_BRANCH_SCRIBER_FRONT}\)\`\ \`后端\:${GO_REVISION_SCRIBER_BACKEND_HKEX:0:8}\(${GO_MATERIAL_BRANCH_SCRIBER_BACKEND_HKEX}\)\`
fi
