import hashlib
import logging
import os
import re
import shutil
from collections import defaultdict
from copy import deepcopy
from tempfile import TemporaryDirectory

from aipod.rpc import decode_data, encode_data
from pdfparser.pdftools.count_page_num import (
    get_page_num as get_pdf_pages,
)
from pdfparser.pdftools.pdf_util import PDFUtil

from remarkable.common.constants import PDFFlag, PDFParseStatus
from remarkable.common.exceptions import CustomError, PdfInsightNotFound
from remarkable.common.storage import localstorage
from remarkable.common.util import (
    chars_in_box_by_center,
    get_page_rotation,
    merge_char_rects,
    read_zip_first_file,
    run_singleton_task,
    subprocess_exec,
)
from remarkable.config import get_config
from remarkable.db import pw_db
from remarkable.models.new_file import NewFile
from remarkable.services.file import _Doc

logger = logging.getLogger(__name__)


class MalformedPDFError(Exception):
    pass


class ChapterNode:
    def __init__(self, index=-1, **kwargs):
        self.index = index
        for key, value in kwargs.items():
            setattr(self, key, value)
        self.children = []

    def to_dict(self):
        ret = {}
        for key, value in self.__dict__.items():
            ret[key] = value if key != "children" else [node.to_dict() for node in value]
        return ret


async def create_pdf_cache(file, force=False):
    file.pdf_parse_status = PDFParseStatus.CACHING
    await pw_db.update(file, only=["pdf_parse_status"])

    try:
        pdf_cache = PDFCache(file)
        pdf_cache.build(force)
    except Exception:
        file.pdf_parse_status = PDFParseStatus.FAIL
        await pw_db.update(file, only=["pdf_parse_status"])
        raise
    else:
        file.pdf_parse_status = pdf_cache.get_pdf_parse_status()
        await pw_db.update(file, only=["pdf_parse_status"])
    finally:
        # 更新所有相同文档PDF解析状态
        await pw_db.execute(NewFile.update(pdf_parse_status=file.pdf_parse_status).where(NewFile.hash == file.hash))


async def convert2pdf(file):
    with TemporaryDirectory() as tmp_dir:
        in_path = os.path.join(tmp_dir, str(file.id))
        out_path = in_path + ".pdf"
        shutil.copy(localstorage.mount(file.path()), in_path)
        try:
            out = subprocess_exec('mono {} "{}"'.format(get_config("web.pdf_converter"), in_path), timeout=60)
        except Exception as e:  # noqa
            logging.exception(e)

        if not os.path.exists(out_path) or os.path.getsize(out_path) == 0:
            logging.error("convert2pdf failed: %s", file.id)
            file.pdf_flag = PDFParseStatus.FAIL.value
            file.pdf_parse_status = PDFParseStatus.FAIL.value
            await pw_db.update(file, only=["pdf_flag", "pdf_parse_status"])
            raise Exception("convert2pdf failed")

        logging.info(out)
        with open(out_path, "rb") as out_fp:
            data = out_fp.read()
            file.pdf = hashlib.md5(data).hexdigest()
            localstorage.write_file(file.pdf_path(), data)
            page = get_pdf_pages(data)
        file.pdf_flag = PDFFlag.CONVERTED.value
        file.page = page
        file.pdf_parse_status = PDFParseStatus.PENDING.value
        await pw_db.update(file, only=["pdf_flag", "page", "pdf_parse_status"])


class PDFCache:
    SEARCH_MAP_SIZE_PER_FILE = 1000

    def __init__(self, file: NewFile):
        self.file: NewFile = file

    def _cached_file_path(self, filename, absolute=True):
        path = os.path.join(self.file.search_index_dir, filename)
        if absolute:
            return localstorage.mount(path)
        return path

    def _doc_page_cache_path(self, filename):
        return self._cached_file_path(filename)

    @property
    def search_string_path(self):
        return self._doc_page_cache_path("search_string")

    def search_map_path(self, key):
        return self._doc_page_cache_path("search_map_{}".format(key))

    @property
    def page_info_path(self):
        return self._doc_page_cache_path("page_info.json.zst")

    @property
    def chapter_info_path(self):
        return self._doc_page_cache_path("chapter_info.json.zst")

    @property
    def char_idx_range_path(self):
        return self._doc_page_cache_path("char_idx_range_on_page")

    def _save_search_string(self, search_string):
        path = self.search_string_path
        if localstorage.exists(path):
            os.remove(path)

        with open(path, mode="wb") as file_obj:
            file_obj.write(encode_data(search_string))

    def _save_search_map(self, search_map):
        split_map = defaultdict(dict)
        for key, value in search_map.items():
            split_map[key // self.SEARCH_MAP_SIZE_PER_FILE][key] = value
        for key, value in split_map.items():
            path = self.search_map_path(key)
            if localstorage.exists(path):
                os.remove(path)

            with open(path, "wb") as file_obj:
                file_obj.write(encode_data(value))

    def _save_char_idx_range(self, char_idx_range_on_page):
        path = self.char_idx_range_path
        if localstorage.exists(path):
            os.remove(path)
        with open(path, "wb") as file_obj:
            file_obj.write(encode_data(char_idx_range_on_page))

    def get_search_string(self):
        path = self.search_string_path
        if not localstorage.exists(path):
            return None
        with open(path, mode="rb") as file_obj:
            search_string = decode_data(file_obj.read())
        return search_string

    def get_char_in_search_map(self, idx, cache_map):
        cache_key = idx // self.SEARCH_MAP_SIZE_PER_FILE
        if cache_key in cache_map:
            cache_data = cache_map[cache_key]
        else:
            cache_data = self.get_search_map(cache_key, cache_map)
        return cache_data.get(str(idx))

    def get_search_map(self, cache_key, cache_map=None):
        path = self.search_map_path(cache_key)
        if not localstorage.exists(path):
            logging.exception(f"No search map cache found for fid: {self.file.id}")
            return {}
        with open(path, mode="rb") as file_obj:
            cache_data = decode_data(file_obj.read())

        if isinstance(cache_map, dict):
            cache_map[cache_key] = cache_data
        return cache_data

    def get_char_idx_range(self, page):
        """
        给定page所包含的char在全文档chars中的index range
        range左闭右开
        :param page:
        :return:
        """
        path = self.char_idx_range_path
        if not localstorage.exists(path):
            return None

        with open(path, mode="rb") as file_obj:
            data = decode_data(file_obj.read())

        idx_range = data.get(page, [])
        return idx_range

    def get_page_info(self):
        path = self.page_info_path
        if not localstorage.exists(path):
            return None
        with open(path, mode="rb") as file_obj:
            cache_data = decode_data(file_obj.read())
        return cache_data

    def get_chapter_info(self):
        path = self.chapter_info_path
        if not localstorage.exists(path):
            raise CustomError(_("The document is being parsed and catalog is not ready."))
        with open(path, mode="rb") as file_obj:
            cache_data = decode_data(file_obj.read())

        chapters = sorted([ChapterNode(**c) for c in cache_data], key=lambda c: c.index)
        chapter_dict = {chapter.index: chapter for chapter in chapters}
        root = ChapterNode(index=-1)
        for chapter in chapters:
            parent = chapter_dict.get(chapter.parent, root)
            parent.children.append(chapter)
        return root.to_dict()

    @staticmethod
    def _gen_search_string(pdfinsight: _Doc, separate="#_#"):
        separate_len = len(separate)
        para_separate = "\n"
        para_separate_len = len(para_separate)

        search_string = ""
        search_map = {}
        char_idx_range_on_page = defaultdict(list)
        index = 0
        for element in pdfinsight.all_elements:
            element_page = element["page"]
            char_idx_range_on_page[element_page].append(index)
            if "cells" in element:
                for cell_idx in sorted(element["cells"].keys(), key=lambda x: [int(i) for i in x.split("_")]):
                    for char in element["cells"][cell_idx]["chars"]:
                        text = char["text"]
                        if not text:
                            continue
                        search_string += text
                        search_map[index] = {
                            "page": char["page"],
                            "text": text,
                            "box": char["box"],
                            "index": element["index"],
                            "index_type": "TABLE",
                            "cell_idx": cell_idx,
                        }
                        index += 1
                    search_string += separate
                    index += separate_len
            else:
                last_char = None
                # pdfparser 的排序仅仅根据char的位置排序 会忽略单元格的信息 所以table中的chars 排序不需要调用 get_sorted_chars
                chars = PDFUtil.get_sorted_chars(element.get("chars", []))
                for char in chars:
                    text = char["text"]
                    if not text:
                        continue
                    search_string += text
                    search_map[index] = {
                        "page": char["page"],
                        "text": text,
                        "box": char["box"],
                        "index": element["index"],
                        "index_type": "PARAGRAPH",
                    }
                    last_char = search_map[index]
                    index += 1
                if last_char:
                    search_string += para_separate
                    search_map[index] = deepcopy(last_char)
                    search_map[index]["text"] = para_separate
                    index += para_separate_len
            char_idx_range_on_page[element_page].append(index)
        char_idx_range_on_page = {k: [min(v), max(v)] for k, v in char_idx_range_on_page.items()}
        return search_string, search_map, char_idx_range_on_page

    def search(self, keyword):
        search_string = self.get_search_string()
        if search_string is None:
            logger.warning("Search cache missing, will rebuilding...")
            self.build()
            logger.info("Rebuild search cache done.")
            search_string = self.get_search_string()

        search_map_data = {}
        find_res = []

        for item in re.finditer(re.escape("".join(keyword.split())), search_string):
            start = item.start()
            end = item.end()
            item_chars = []
            for idx in range(start, end):
                _char = self.get_char_in_search_map(idx, search_map_data)
                item_chars.append(_char)
            merged_chars = merge_char_rects(item_chars, pos_key="box")  # TODO
            search_items = []
            for page, rects in merged_chars.items():
                search_items.append({"page": page, "outlines": [[rect.x, rect.y, rect.xx, rect.yy] for rect in rects]})
            find_res.append({"items": search_items})

        return find_res

    def get_text_in_box(self, box, get_text_func=None):
        if not get_text_func:
            get_text_func = PDFUtil.get_text_from_chars_with_white

        page = str(box["page"])
        char_idx_range = self.get_char_idx_range(page)
        if char_idx_range is None:
            raise CustomError("The document is being parsed and cannot get text from it.")
        if not char_idx_range:
            logging.info(f"no page cache for file_id:{self.file.id}, page:{page}")
            return None, None

        chars = []
        cache_key_start = char_idx_range[0] // self.SEARCH_MAP_SIZE_PER_FILE
        cache_key_end = (char_idx_range[1] - 1) // self.SEARCH_MAP_SIZE_PER_FILE
        for cache_key in range(cache_key_start, cache_key_end + 1):
            chars_in_cache_file = self.get_search_map(cache_key)
            chars.extend(
                {
                    idx: char
                    for idx, char in chars_in_cache_file.items()
                    if char_idx_range[0] <= int(idx) < char_idx_range[1]
                }.values()
            )
        chars = chars_in_box_by_center(box["box"], texts=chars, with_white_chars=True)
        if (
            chars and chars[-1]["text"] == "\n"
        ):  # gen_search_string在每个段落后面补了一个换行符,使框选了多个段落时起到分隔作用
            chars.pop()

        text = get_text_func(chars)
        return text, chars

    def build(self, force=False):
        lock_key = "create_pdf_cache:" + (self.file.pdf or self.file.hash)
        get_lock, lock, *_ = run_singleton_task(lambda: None, lock_key=lock_key)
        try:
            if get_lock:
                self._build(force=force)
            else:
                logging.warning(f"No need to create PDF cache in a short time for fid: {self.file.id}")
        except PdfInsightNotFound as exp:
            raise exp
        except Exception as exp:
            logging.exception(exp)
        finally:
            if get_lock:
                lock.release()

    def _build(self, force):
        if not (self.file.pdfinsight and localstorage.exists(self.file.pdfinsight_path())):
            from remarkable.worker.tasks import request_interdoc

            request_interdoc.delay(self.file.id)
            raise PdfInsightNotFound(f"No pdfinsight for {self.file.id}, will re-fetch it.")

        if force and localstorage.exists(self.file.search_index_dir):
            localstorage.delete_dir(self.file.search_index_dir)

        if localstorage.exists(self.file.search_index_dir):
            logging.info(f"no need rebuild pdf_cache: {self.file.id}")
            return

        logging.info(f"begin creating pdf_cache for {self.file.id}")
        localstorage.create_dir(self.file.search_index_dir)
        pdfinsight = read_zip_first_file(self.file.pdfinsight_path(abs_path=True), msgspec_type=_Doc)
        self.create_page_info_cache(pdfinsight)
        self.create_chapter_info_cache(pdfinsight)
        self.create_pdf_search_cache(pdfinsight)
        logging.info(f"pdf_cache created for {self.file.id}")

    def create_pdf_search_cache(self, pdfinsight):
        search_string, search_map, char_idx_range_on_page = self._gen_search_string(pdfinsight)
        self._save_search_string(search_string)
        self._save_search_map(search_map)
        self._save_char_idx_range(char_idx_range_on_page)

    def create_chapter_info_cache(self, pdfinsight):
        info = []
        max_level = 6
        for syllabus in pdfinsight.syllabuses:
            page, box = None, None
            if (syllabus.get("level") or 0) > max_level:
                continue
            syll_dest = syllabus.get("dest")
            if syll_dest:
                page = syll_dest.get("page_index")
                box = syll_dest.get("box")
            parent = syllabus.get("parent")
            info.append(
                {
                    "file_id": self.file.id,
                    "index": syllabus.get("index"),
                    "parent": parent,
                    "title": syllabus.get("title"),
                    "start": syllabus.get("range")[0],
                    "end": syllabus.get("range")[1],
                    "page": page,
                    "box": box,
                    "level": syllabus.get("level"),
                }
            )

        with open(self.chapter_info_path, "wb") as file_obj:
            file_obj.write(encode_data(info))

    def create_page_info_cache(self, pdfinsight):
        info = []
        for idx, page in pdfinsight.pages.items():
            info.append(
                {
                    "page": int(idx),
                    "width": page["size"][0],
                    "height": page["size"][1],
                    "rotate": get_page_rotation(page),
                    "meta": {
                        "ocr": page["statis"].get("ocr", False),
                        "statis": page.get("statis", {}),
                    },
                }
            )

        with open(self.page_info_path, "wb") as file_obj:
            file_obj.write(encode_data(info))

    def get_pdf_parse_status(self):
        status = PDFParseStatus.PARSING
        if localstorage.exists(self.page_info_path):
            status = PDFParseStatus.PAGE_CACHED
        if localstorage.exists(self.chapter_info_path):
            status = PDFParseStatus.COMPLETE
        return status
