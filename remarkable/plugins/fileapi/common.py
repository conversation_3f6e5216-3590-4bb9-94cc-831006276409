# import re
import hashlib
import logging
import os
import random
import shutil
import time
from urllib.parse import urlsplit

import attr
import requests

from remarkable.common.constants import DocType, PDFParseStatus
from remarkable.common.exceptions import PdfinsightError, PushError
from remarkable.common.storage import localstorage
from remarkable.common.util import run_singleton_task, subprocess_exec
from remarkable.config import get_config
from remarkable.db import pw_db
from remarkable.models.hkex_file_meta import HKEXFileMeta
from remarkable.models.new_file import NewFile
from remarkable.security import authtoken

# rxcountpages = re.compile(r"/Type\s*/Page([^s]|$)", re.MULTILINE|re.DOTALL)
from remarkable.services import pdf

logger = logging.getLogger(__name__)


async def gen_interdoc(file: NewFile):
    mode = get_config("web.parser.mode", "pdf")
    logging.info(f"Generating interdoc for file({file.id}:{file.name}) with mode({mode})")
    if mode == "pdf":
        lock_key = f"gen_interdoc:{file.id}:{file.pdf}"
        get_lock, lock, *_ = run_singleton_task(lambda: None, lock_key=lock_key, lock_expired=600)
        if not get_lock:
            logging.warning(f"No need to create interdoc in a short time for file: {file.id}:{file.name}")
            return
        try:
            make_pdfinsight(file.pdf_path(), file.id)
        except PushError as exp:
            lock.release()
            raise exp
        else:
            file.pdf_parse_status = PDFParseStatus.PARSING
            await pw_db.update(
                file,
                only=[
                    "pdf_parse_status",
                ],
            )
    elif mode == "docx":
        wordinsight(file)
    else:
        raise RuntimeError(f"Unknown parser mode: {mode}, please check the config file.")


@attr.define
class QueryParam:
    column = attr.field(default=get_config("app.auth.pdfinsight.column", 1))  # 默认分栏
    colorful = attr.field(default=0)
    title_by_model = attr.field(default=1)
    garbled_file_handle = attr.field(default=0)  # 0: 不处理乱码文件，1：只检测不处理，2：处理乱码文件
    force_ocr = attr.field(default=0)
    auto_ocr = attr.field(default=1)  # auto_ocr=0，是直接不走ocr的，默认是1，由程序判断是否走ocr
    ocr_name = attr.field(default=get_config("ocr.service", "pai"))
    force_ocr_pages = attr.field(default=None)
    outline_version = attr.field(default=None)
    column_version = attr.field(default=None)  # https://mm.paodingai.com/cheftin/pl/hw37cxbb37g18qatxkmche3iuo
    texts_in_shape = attr.field(default=1)  # texts_in_shape=1 提取shape内的文本

    def __str__(self):
        return "&".join(f"{k}={v}" for k, v in attr.asdict(self).items() if v is not None)

    @classmethod
    def load(cls, fid, pdf_path, **kwargs):
        param = cls(**kwargs)
        meta = HKEXFileMeta.sync_find_by_fid(fid)
        if not meta:
            # NOTE: 需要尽可能避免文档无meta信息
            return param
        if meta.doc_type in DocType.qr_values() and not pdf.PDF.is_dual_column_pdf(pdf_path):
            # 季报自动判断是否启用分栏模型
            param.column = 0
        if meta.doc_type == DocType.ESG:
            # 独立ESG报告专用模型
            # https://mm.paodingai.com/cheftin/pl/65rorfcn47r6mnjxyi7mjwogrw
            logger.info(f'Got ESG file: {meta}, use "infographic" model')
            param.outline_version = "infographic"
            param.column_version = "-esg"
            param.auto_ocr = 0
            if pdf.PDF.is_image_pdf(pdf_path):
                logger.info(f"Got ESG file: {meta}, set auto_ocr=1")
                param.auto_ocr = 1

        # todo: Jura2.1的ocr_name考虑设置为pai
        return param


def make_pdfinsight(
    filepath,
    key,
    colorful=0,
    interdoc=None,
    run_predict=True,
    force_ocr=0,
    auto_ocr=1,
    ocr_name=None,
    garbled_file_handle=0,
    force_ocr_pages=None,
    **ext_kwargs,
):
    """
    force_ocr: 0 or 1, force process the whole doc with OCR without detect
    """

    def get_pdfinsight_api(param: QueryParam):
        appid = get_config("app.auth.pdfinsight.app_id")
        secret = get_config("app.auth.pdfinsight.secret_key")
        url = get_config("app.auth.pdfinsight.url")
        api = f"{url}/api/v1/interdoc/pdf/analysis?{param}"
        return authtoken.encode_url(api, appid, secret)

    logger.debug(f"start to make pdfinsight request for file: {filepath}")
    callback_url = (
        f"{get_config('web.scheme', 'https')}://{get_config('web.domain')}/api/v1/plugins/fileapi/file"
        f"/{key}/pdfinsight?run_predict={1 if run_predict else 0}"
    )
    mid = ext_kwargs.pop("mid", None)
    if mid:
        callback_url += f"&mid={mid}"

    postdata = {
        "app": get_config("app.app_id"),
        "callback_type": urlsplit(callback_url).scheme,
        "callback": callback_url,
        "key": key,
        "state": None,
    }
    postdata.update(ext_kwargs)
    files = {"file": localstorage.read_file(filepath)}
    if interdoc:
        files["interdoc"] = localstorage.read_file(interdoc)
    query_param = QueryParam.load(
        key,
        localstorage.mount(filepath),
        colorful=colorful,
        force_ocr=force_ocr,
        auto_ocr=auto_ocr,
        ocr_name=ocr_name,
        garbled_file_handle=int(garbled_file_handle),
        force_ocr_pages=force_ocr_pages,
    )
    try:
        ret = requests.post(url=get_pdfinsight_api(query_param), data=postdata, files=files, timeout=120.0)
    except Exception as exp:
        raise PushError("pre analysis service request error: {}".format(exp)) from exp
    if ret.status_code != 200:
        raise PushError("pre analysis service response error: {} \n {}".format(ret.status_code, ret.text))
    if ret.json().get("status", "") == "error":
        raise PdfinsightError(f"pdfinsight parsing failed: \n{ret.text}")


def wordinsight(file):
    tmp_dir = get_config("web.tmp_dir")
    if not os.path.exists(tmp_dir):
        os.makedirs(tmp_dir)

    origin_path = localstorage.mount(file.path())
    random_name = "%s_%s" % (int(time.time()), random.randint(1000, 9999))
    tmp_doc_path = os.path.join(tmp_dir, random_name + ".doc")
    tmp_docx_path = os.path.join(tmp_dir, random_name + ".docx")
    tmp_colorful_docx_path = os.path.join(tmp_dir, random_name + ".colorful.docx")
    tmp_colorful_pdf_path = os.path.join(tmp_dir, random_name + ".colorful.pdf")
    tmp_docx_interdoc_path = os.path.join(tmp_dir, random_name + ".docx.interdoc")

    # 1. doc => docx
    if file.name.lower().endswith(".docx"):
        shutil.copy(origin_path, tmp_docx_path)
    elif file.name.lower().endswith(".doc"):
        shutil.copy(origin_path, tmp_doc_path)
        doc2docx_exe = get_config("web.doc2docx_converter")
        subprocess_exec("mono %s %s %s" % (doc2docx_exe, tmp_doc_path, tmp_docx_path))
        with open(tmp_docx_path, "rb") as outputfile:
            data = outputfile.read()
            file.docx = hashlib.md5(data).hexdigest()
            localstorage.write_file(file.docx_path(), data)
            with pw_db.allow_sync():
                NewFile.update(docx=file.docx).where(NewFile.id == file.id).execute()
    else:
        raise Exception("can't convert file %s to pdf" % file.id)

    # 2. docx => docx_interdoc & colorful docx
    word_insight_dll = get_config("web.wordinsight")
    subprocess_exec("dotnet %s -i %s -o %s --colorful" % (word_insight_dll, tmp_docx_path, tmp_docx_interdoc_path))

    # 3. colorful docx => colorful pdf
    word2pdf_exe = get_config("web.pdf_converter")
    subprocess_exec("mono %s %s %s" % (word2pdf_exe, tmp_colorful_docx_path, tmp_colorful_pdf_path))

    # 4. colorful pdf & docx_interdoc => pdf_interdoc (async)
    make_pdfinsight(tmp_colorful_pdf_path, file.id, colorful=1, interdoc=tmp_docx_interdoc_path)

    clear_tmp_files(tmp_doc_path, tmp_docx_path, tmp_colorful_docx_path, tmp_colorful_pdf_path, tmp_docx_interdoc_path)


def clear_tmp_files(*paths):
    for _path in paths:
        if os.path.exists(_path):
            os.remove(_path)
