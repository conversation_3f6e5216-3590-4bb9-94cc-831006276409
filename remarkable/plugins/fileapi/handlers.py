import asyncio
import hashlib
import http
import itertools
import json
import logging
import os
import random
import shutil
import time
from typing import List
from zipfile import ZipFile

import celery
import httpx
import webargs
from pdfparser.pdftools.count_page_num import get_page_num as get_pdf_pages
from peewee import fn
from speedy.peewee_plus import orm
from tornado.concurrent import run_on_executor
from tornado.websocket import WebSocketHandler
from webargs import fields
from webargs.tornadoparser import use_args, use_kwargs

from remarkable.base_handler import (
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    CustomError,
    DbQueryHandler,
    peewee_transaction_wrapper,
)
from remarkable.common.common import Schema
from remarkable.common.constants import (
    AIStatus,
    HistoryAction,
    PDFParseStatus,
    QuestionStatus,
)
from remarkable.common.exceptions import PdfInsightNotFound
from remarkable.common.pagination import AsyncPagination
from remarkable.common.storage import localstorage
from remarkable.common.util import (
    fix_aid,
    fix_zip_filename_encoding,
    mm_notify,
    optimize_outline,
    stream_download,
)
from remarkable.config import get_config
from remarkable.db import init_rdb, pw_db
from remarkable.models.accuracy_record import AccuracyRecord
from remarkable.models.file_project import NewFileProject
from remarkable.models.file_tree import NewFileTree
from remarkable.models.hkex_file import HKEXFile
from remarkable.models.hkex_file_meta import HKEXFileMeta
from remarkable.models.mold import special_mold
from remarkable.models.new_file import NewFile
from remarkable.models.new_history import NewHistory
from remarkable.models.new_mold import NewMold
from remarkable.models.new_question import NewQuestion, NewQuestionWithFile
from remarkable.models.user import AdminUser
from remarkable.plugins.fileapi import external_plugin, plugin
from remarkable.plugins.fileapi.new_perm_check import NewCheckPerm, NewPermCheckHandler
from remarkable.plugins.fileapi.worker import PDFCache
from remarkable.plugins.hkex.ma_schemas import box_to_text_key
from remarkable.prompter.schema import attribute_id
from remarkable.services.file import get_crumbs, get_or_create_outlines
from remarkable.services.new_file import NewFileService
from remarkable.services.pdf import get_text_from_chars_with_white, get_text_in_box_with_ocr
from remarkable.util.working_cache import working_question_cache
from remarkable.worker.tasks import (
    fix_syllabus_range,
    make_question,
    parse_ar_year_end,
    preset_answer,
    preset_question_answer,
    recruit_crud_answer,
    save_embeddings,
    set_agm_meta_info,
    set_mr_meta_info,
    set_poll_meta_info,
)

supported_exts = [".pdf"]
logger = logging.getLogger(__name__)


async def update_file_mold(file, form_mold):
    """
    :param conn: db连接
    :param file: file对象
    :param form_mold: schema 类型
    :return: True 更新schema/ False 不满足更新条件
    """
    if not (file := await NewFile.get_by_id(file.id)):
        return False

    question = await NewQuestion.get_by_id(file.qid, fields=("status",))
    if not question or question.status != QuestionStatus.TODO:
        logger.debug("file: %s, QuestionStatus: %s != %s", file.id, question.status, QuestionStatus.TODO)
        return False

    if file.mold is None:
        parent_mold = await NewFileTree.find_default_mold(file.tree_id)
        mold = parent_mold or form_mold
        await pw_db.execute(NewFile.update(mold=mold).where(NewFile.id == file.id))
        await restart_preset_answer(file)
        return True

    return False


async def restart_preset_answer(file):
    """修改schema后，重新执行file的预测答案"""
    logger.debug("file: %s, schema changed: %s, run task", file.id, file.mold)
    run_predict = get_config("web.preset_answer", False)
    if run_predict:
        questions = await pw_db.execute(NewQuestion.select().where(NewQuestion.fid == file.id))
        for question in questions:
            await pw_db.execute(
                NewQuestion.update(preset_answer=None, crude_answer=None, ai_status=0).where(
                    NewQuestion.id == question.id
                )
            )
            preset_question_answer.delay(question.id)


@plugin.route(r"/project")
class ProjectListHandler(BaseHandler):
    params = {
        "name": webargs.fields.Str(required=True),
        "tags": webargs.fields.List(webargs.fields.Int(), load_default=[], data_key="default_tags"),
        "default_molds": webargs.fields.List(webargs.fields.Int(), load_default=[]),
        "preset_answer_model": webargs.fields.Str(load_default=None),
        "is_public": webargs.fields.Bool(load_default=False),
    }

    @Auth(["manage_prj"])
    @use_kwargs(params, location="json")
    async def post(self, name, tags, default_molds, preset_answer_model, is_public):
        project = await self.create_project(name, tags, default_molds, preset_answer_model)
        self.data(project.to_dict())

    @peewee_transaction_wrapper
    async def create_project(self, name, tags, default_molds, preset_answer_model):
        if await pw_db.first(NewFileProject.select(NewFileProject.id).where(NewFileProject.name == name)):
            raise CustomError("project name is existed")
        project = await NewFileProject.create(
            name=name, default_tags=tags, default_mold=default_molds, preset_answer_model=preset_answer_model
        )
        await NewHistory.save_operation_history(
            None,
            self.current_user.id,
            HistoryAction.CREATE_PROJECT.value,
            self.current_user.name,
            meta=project.to_dict(),
        )
        return project

    @Auth(["browse"])
    async def get(self, *args, **kwargs):
        projects = await pw_db.execute(NewFileProject.select())
        return self.data([p.to_dict() for p in projects])


@plugin.route(r"/preset_answer_models")
class PresetAnswerModelHandler(BaseHandler):
    @Auth("browse")
    async def get(self, *args, **kwargs):
        self.data(get_config("web.preset_answer_models", []))


@plugin.route(r"/project/(\d+)")
class ProjectHandler(NewPermCheckHandler):
    @Auth("browse")
    async def get(self, *args, **kwargs):
        pid = int(args[0])
        await self.check_project_permission(["browse"], pid)
        project = await NewFileProject.find_by_id(pid)
        if not project:
            return self.error(_("not found project"))
        return self.data(project.to_dict())

    @Auth(["manage_prj"])
    @peewee_transaction_wrapper
    async def delete(self, *args, **kwargs):
        pid = int(args[0])
        project: NewFileProject = await NewFileProject.find_by_id(pid)
        if not project:
            raise CustomError(_("not found project"))

        await NewHistory.save_operation_history(
            None,
            self.current_user.id,
            HistoryAction.DELETE_PROJECT.value,
            self.current_user.name,
            meta=project.to_dict(),
        )
        await project.delete()

        return self.data({})

    @peewee_transaction_wrapper
    async def put(self, *args, **kwargs):
        pid = int(args[0])
        await self.check_project_permission(["manage_prj"], pid)
        form = self.get_json_body()
        allowed = ["default_tags", "name", "default_mold"]  # , "preset_answer_model"
        params = {k: form[k] for k in form if k in allowed and form[k] is not None}
        if params:
            await pw_db.execute(NewFileProject.update(**params).where(NewFileProject.id == pid))

        if not (project := await NewFileProject.find_by_id(pid)):
            raise CustomError("not found project")
        if "name" in form:
            await pw_db.execute(NewFileTree.update(name=form["name"]).where(NewFileTree.id == project.rtree_id))
        # fixme project mold 前端添加该参数
        await self.associate_project_schema(project, form)

        await NewHistory.save_operation_history(
            None,
            self.current_user.id,
            HistoryAction.MODIFY_PROJECT.value,
            self.current_user.name,
            meta=project.to_dict(),
        )

        return self.data(project.to_dict())

    @staticmethod
    async def associate_project_schema(project, form):
        """修改项目的schema后，如果里面的文件或文件夹没有关联schema，则自动关联为项目的schema
        :param conn: db连接对象
        :param project: 项目对象
        :param form: 表单 json
        :return:
        """

        async def _update_project_mold():
            """更新项目的mold"""
            await pw_db.execute(NewFileTree.update(default_mold=form_mold).where(NewFileTree.id == project.rtree_id))

        async def _update_trees_mold():
            """更新子文件夹的mold"""
            all_trees = await pw_db.execute(NewFileTree.select().where(NewFileTree.pid == project.id))
            for tree in all_trees:
                if not tree.ptree_id:
                    continue
                if tree.default_mold is None:
                    await pw_db.execute(NewFileTree.update(default_mold=form_mold).where(NewFileTree.id == tree.id))

        async def _update_files_mold():
            """更新项目内未指定schema的文件"""
            all_files = await pw_db.execute(NewFile.select().where(NewFile.pid == project.id))
            for file in all_files:
                await update_file_mold(file, form_mold)

        default_mold = await NewFileTree.find_default_mold(project.rtree_id)
        form_mold = form.get("default_mold", None)
        change_mold = default_mold != form_mold

        logger.info("project default_mold: %s, form mold: %s", default_mold, form_mold)
        if change_mold:
            await _update_project_mold()
            await _update_trees_mold()
            await _update_files_mold()


@plugin.route(r"/accuracy")
class AccuracyHandler(NewPermCheckHandler):
    @Auth("browse")
    async def get(self, *args, **kwargs):
        type_ = self.get_query_argument("type", "1")
        test_ = self.get_query_argument("test", "1")
        mold_ = self.get_query_argument("mold", "")
        if not mold_:
            return self.error(_("required args: [MOLD]"))

        record = await pw_db.first(
            AccuracyRecord.select(AccuracyRecord.data)
            .where(AccuracyRecord.test == test_, AccuracyRecord.type == type_, AccuracyRecord.mold == mold_)
            .order_by(AccuracyRecord.created_utc)
        )
        return self.data(record.data) if record else self.data({})


@plugin.route(r"/tree/(\d+)")
class TreeHandler(NewPermCheckHandler):
    @Auth("browse")
    async def get(self, *args, **kwargs):
        tid = int(args[0])
        if not (tree := await NewFileTree.find_by_id(tid)):
            return self.error(_("not found tree"))
        await self.check_project_permission(["browse"], tree.pid)

        res = tree.to_dict()

        # NOTE: 分页目的是解决前端展示卡顿
        page = int(self.get_argument("page", "1"))
        size = int(self.get_argument("size", "20"))

        trees = await NewFileTree.list_by_tree(tid)
        start = (page - 1) * size
        end = page * size
        res["trees"] = [t.to_dict() for t in trees[start:end]]

        # NOTE: hard code for project 19 (Jura 2.0 new), fetch deleted files and replace to 1.0 document
        async def replace_to_existed_file(_file):
            _filter = "file.hash = %(hash)s and file.deleted_utc = 0"
            _, substitutes = await NewFile.file_query(_filter, self.current_user.id, {"hash": _file["hash"]})
            if substitutes:
                return substitutes[0]
            else:
                return None

        include_deleted = tree.pid == 19
        files = await NewFile.list_by_tree(tid, self.current_user.id, include_deleted=include_deleted)
        if len(res["trees"]) < size:
            file_end = end - len(trees)
            file_start = max(file_end - size + len(res["trees"]), 0)
            res["files"] = fill_lock_info(files[file_start:file_end])
            if tree.pid == 19:
                substitute_files = []
                for _file in res["files"]:
                    substitute = await replace_to_existed_file(_file)
                    if substitute:
                        substitute_files.append(substitute)
                res["files"] = substitute_files
        else:
            res["files"] = []
        res["page"] = page
        res["total"] = len(files) + len(trees)

        res["crumbs"] = await get_crumbs(tree.id)

        return self.data(res)

    @Auth("browse")
    @peewee_transaction_wrapper
    async def delete(self, *args, **kwargs):
        tid = int(args[0])
        if not (tree := await NewFileTree.find_by_id(tid)):
            return self.error(_("not found tree"))
        pid = tree.pid
        await self.check_project_permission(
            [], pid, any_permission=["remark", "remark_management", "manage_prj", "manage_user", "manage_mold"]
        )

        await NewHistory.save_operation_history(
            None,
            self.current_user.id,
            HistoryAction.DELETE_TREE.value,
            self.current_user.name,
            meta=tree.to_dict(),
        )
        await tree.delete()
        return self.data({})

    @Auth("browse")
    @peewee_transaction_wrapper
    async def put(self, *args, **kwargs):
        tid = int(args[0])
        form = self.get_json_body()
        await self.check_tree_permission(["remark"], tid)
        allowed = ["name", "ptree_id", "default_mold"]
        params = {k: form[k] for k in form if k in allowed}
        if params:
            await pw_db.execute(NewFileTree.update(**params).where(NewFileTree.id == tid))
        tree = await NewFileTree.find_by_id(tid)
        if not tree:
            raise CustomError(_("not found tree"))

        await self.associate_tree_schema(tree, form)

        await NewHistory.save_operation_history(
            None,
            self.current_user.id,
            HistoryAction.MODIFY_TREE.value,
            self.current_user.name,
            meta=tree.to_dict(),
        )

        return self.data(tree.to_dict())

    @staticmethod
    async def associate_tree_schema(tree, form):
        """修改子文件夹schema后，如果里面的文件或文件夹没有关联schema，则自动关联为当前文件夹的schema
        :param conn: db连接对象
        :param tree: 子文件夹的对象
        :param form: 表单 json
        :return:
        """

        async def _update_trees_mold(parent_tree):
            """更新当前子文件夹内部所有未关联schema的文件夹"""
            all_trees = await NewFileTree.list_by_tree(parent_tree.id)
            for tree in all_trees:
                if tree.default_mold is None:
                    logger.debug("update tree %s, form mold: %s", tree.id, form_molds)
                    await pw_db.execute(NewFileTree.update(default_mold=form_molds).where(NewFileTree.id == tree.id))
                    await _update_trees_mold(tree)

        async def _update_files_mold(parent_tree):
            all_files = await pw_db.execute(NewFile.select().where(NewFile.tree_id == parent_tree.id))
            trees = await NewFileTree.list_by_tree(parent_tree.id)

            for file in all_files:
                await update_file_mold(file, form_molds)
            for child_tree in trees:
                await _update_files_mold(child_tree)

        form_molds = form.get("default_molds", None)
        await _update_trees_mold(tree)
        await _update_files_mold(tree)


@plugin.route(r"/tree/(\d+)/name/(.*)")
class TreeNameHandler(BaseHandler):
    @Auth("browse")
    async def get(self, *args, **kwargs):
        (tid, name) = args
        if not (exists := await NewFileTree.exists(tid, name)):
            exists = await NewFile.exists(tid, name)

        return self.data({"exists": exists})


@plugin.route(r"/tree/(\d+)/file")
class UploadFileHandler(NewPermCheckHandler):
    @Auth("browse")
    async def post(self, *args, **kwargs):
        tid = int(args[0])
        file_metas = self.request.files.get("file", None)
        mold = self.get_argument("mold", None)
        if not file_metas:
            return self.error(_("not found upload document"))
        if not (tree := await NewFileTree.find_by_id(tid)):
            raise CustomError(_("can't find the tree"))
        if not (project := await NewFileProject.find_by_id(tree.pid)):
            raise CustomError(_("can't find the project"))
        if not NewFileService.is_allowed_tree_id(tid, project):
            raise CustomError(
                _("The files must be uploaded to a folder."), resp_status_code=http.HTTPStatus.BAD_REQUEST
            )

        await self.check_project_permission(
            [], project.id, any_permission=["remark", "remark_management", "manage_prj", "manage_user", "manage_mold"]
        )

        if not mold:
            mold = await NewFileTree.find_default_mold(tid)

        meta = file_metas[0]
        page = None
        if os.path.splitext(meta["filename"])[-1].lower() not in get_config(
            "feature.supported_suffixes", supported_exts
        ):
            return self.error(_("Unsupported file type detected"))
        is_pdf = meta["filename"].lower().endswith(".pdf")
        # if is_pdf:
        #     page = get_pdf_pages(meta["body"])
        newfile = await NewFileService.create_file(is_pdf, meta, mold, page, project, tree, self.current_user)
        # cache pdf file after gen pdf, now in the func `make_question`
        NewFileService.start_task(newfile.id, project, tree.id)
        return self.data(newfile.to_dict())


@plugin.route(r"/tree/(\d+)/tree")
class CreateTreeHandler(NewPermCheckHandler):
    @Auth("browse")
    @peewee_transaction_wrapper
    async def post(self, *args, **kwargs):
        """创建目录"""
        tid = int(args[0])

        form = self.get_json_body()

        if not (tree := await NewFileTree.find_by_id(tid)):
            raise CustomError(_("can't find the tree"))

        if not (project := await NewFileProject.find_by_id(tree.pid)):
            raise CustomError(_("can't find the project"))
        await self.check_project_permission(["remark"], project.id)

        default_mold = await NewFileTree.find_default_mold(tid)
        newtree = await NewFileTree.create(
            **{
                "ptree_id": tree.id,
                "pid": project.id,
                "name": form.get("name"),
                "default_mold": form.get("default_mold", None) or default_mold,
            },
        )

        await NewHistory.save_operation_history(
            None,
            self.current_user.id,
            HistoryAction.CREATE_TREE.value,
            self.current_user.name,
            meta=newtree.to_dict(),
        )

        return self.data(newtree.to_dict())


@plugin.route(r"/tree/(\d+)/zip")
class ZipWSHandler(WebSocketHandler, BaseHandler):
    """ws上传zip文件"""

    def __init__(self, application, request, **kwargs):
        super().__init__(application, request, **kwargs)
        self.file_len = None  # 客户端报告的文件长度
        self.received_len = 0  # 当前已经接收到的字节数
        self.zipfilepath = ""  # zip文件在服务器上的存放路径
        self.zipdir = ""  # 解压zip文件用到的临时文件夹
        self.zipfile = None  # 接受数据时, 写入文件用到的文件对象

    @run_on_executor
    def _extract_zip(self, zipfilepath, zipdir):
        ignore_dir = ["__MACOSX"]
        ignore_file = [".DS_Store"]

        def filter_(entry, typ="file"):
            if entry.split("/")[0] in ignore_dir:
                return False

            if typ == "file":
                return (
                    not entry.split("/")[-1].startswith("~$")
                    and entry.split("/")[-1] not in ignore_file
                    and not entry.endswith("/")
                )
            elif typ == "dir":
                return entry.endswith("/")
            else:
                return False

        with ZipFile(zipfilepath) as zfp:
            names = zfp.namelist()

            files = {name: fix_zip_filename_encoding(name) for name in names if filter_(name, typ="file")}

            dirs = {name: fix_zip_filename_encoding(name) for name in names if filter_(name, typ="dir")}

            os.makedirs(zipdir)
            for dir_ in dirs.values():
                path = os.path.join(zipdir, dir_)
                if not os.path.isdir(path):
                    os.makedirs(path)

            for fin, fout in files.items():
                path = os.path.join(zipdir, fout)
                with zfp.open(fin, mode="r") as fpin, open(path, mode="wb") as fpout:
                    fpout.write(fpin.read())

    @run_on_executor
    def _open_file(self, full_path):
        with open(full_path, "rb") as file_obj:
            return file_obj.read()

    @run_on_executor
    def _get_pdf_pages(self, data):
        page = get_pdf_pages(data)
        return page

    @run_on_executor
    def _store_file(self, path, data):
        localstorage.write_file(path, data)

    def check_origin(self, origin):
        return True

    @Auth("browse")
    def open(self, *args, **kwargs):
        logger.info("open")
        self.tree_id = int(args[0])
        ziproot = get_config("web.tmp_dir")
        if not os.path.exists(ziproot):
            os.makedirs(ziproot)

        zipname = "{}{}".format(str(int(time.time())), str(random.randint(1000, 9999)))
        self.zipdir = os.path.join(ziproot, zipname)
        self.zipfilepath = self.zipdir + ".zip"
        self.zipfile = open(self.zipfilepath, "ab")

    async def handle_zip_file(self):
        self.zipfile.close()
        logger.info("extracting zip")
        await self._extract_zip(self.zipfilepath, self.zipdir)
        logger.info("extracted")
        os.remove(self.zipfilepath)

        return self.zipdir

    async def import_zipdir(self, tree_id, zipdir):
        logger.info("import zipdir")
        rtree = await NewFileTree.get_by_id(tree_id)
        project = await NewFileProject.get_by_id(rtree.pid)
        if not NewFileService.is_allowed_tree_id(tree_id, project):
            # TODO 这里需要和前端约定
            await self.write_message({"error": True, "stage": "The files must be uploaded to a folder."})
            return
        mold = await NewFileTree.find_default_mold(tree_id)
        if isinstance(mold, list):
            mold_list = mold
            mold = mold_list[0] if mold_list else None
        elif mold is None:
            mold_list = []
        else:
            mold_list = [mold]

        def count_files(_dir):
            total = 0
            for *_, files in os.walk(_dir):
                total += len(files)
            return total

        total_files = count_files(zipdir)

        progress = 0

        async def import_dir(ptree_id, _dir):
            nonlocal progress
            root = _dir
            for entry in os.listdir(_dir):
                full_path = os.path.join(root, entry)
                if os.path.isfile(full_path):
                    filename = entry
                    progress += 1
                    data = await self._open_file(full_path)
                    page = None
                    is_pdf = filename.lower().endswith(".pdf")
                    if is_pdf:
                        page = await self._get_pdf_pages(data)

                    fhash = hashlib.md5(data).hexdigest()
                    same_file = await NewFile.find_by_hash(fhash)
                    if same_file:
                        logger.info(
                            "find same file %d, with pdf: %s, pdfinsight: %s",
                            same_file.id,
                            same_file.pdf,
                            same_file.pdfinsight,
                        )

                    newfile = await NewFile.create(
                        **{
                            "tree_id": ptree_id,
                            "pid": project.id,
                            "uid": self.current_user.id,
                            "name": filename,
                            "hash": fhash,
                            "pdf": fhash if is_pdf else None,
                            "docx": fhash if filename.lower().endswith(".docx") else None,
                            "pdfinsight": same_file.pdfinsight if same_file else None,
                            "default_tags": project.default_tags,
                            "size": len(data),
                            "page": page,
                            "mold": mold,
                            "mold_list": mold_list,
                            "pdf_parse_status": PDFParseStatus.PENDING,
                        },
                    )
                    await self._store_file(newfile.path(), data)
                    NewFileService.start_task(newfile.id, project, tree_id)
                    await self.write_message(
                        {"stage": "IMPORT", "filename": filename, "progress": progress, "total": total_files}
                    )

                else:
                    subdir = entry
                    newtree = await NewFileTree.create(**{"ptree_id": ptree_id, "pid": project.id, "name": subdir})
                    await import_dir(newtree.id, full_path)

        await import_dir(rtree.id, zipdir)

    async def on_message(self, message):
        if self.file_len is None:
            self.file_len = int(message)
            await self.write_message({"stage": "RECEIVE"})
            return
        else:
            self.zipfile.write(message)
            self.received_len += len(message)
            if int(self.received_len) >= int(self.file_len):
                logger.info("=== %s/%s", self.received_len, self.file_len)
                await self.process_data()

    async def process_data(self):
        zipdir = await self.handle_zip_file()
        await self.write_message({"stage": "UNPACK"})
        async with pw_db.atomic():
            await self.import_zipdir(self.tree_id, zipdir)

        await self.write_message({"stage": "FINISHED"})
        logger.info("import complete")
        shutil.rmtree(zipdir)
        logger.info(zipdir)

    def on_close(self):
        logger.info("websocket closed")


@plugin.route(r"/file/(\d+)")
class FileHandler(NewPermCheckHandler):
    args = {
        "name": fields.String(required=True),
        "molds": fields.List(fields.Int(), required=True),
        "tags": fields.List(fields.Int(), load_default=[]),
    }

    @Auth("browse")
    async def get(self, *args, **kwargs):
        """文件下载"""

        fid = int(args[0])

        file: NewFile = await NewFile.find_by_id(fid)
        if not file:
            raise CustomError(_("not found file"))
        await self.check_project_permission(["browse"], file.pid)

        abs_path = localstorage.mount(file.path())

        return await self.export(abs_path, file.name)

    @use_kwargs(args, location="json")
    @Auth("browse")
    @peewee_transaction_wrapper
    async def put(self, fid, name, molds, tags):
        await self.check_file_permission(["remark"], fid)
        file = await NewFile.find_by_id(fid)
        if not file:
            raise CustomError(_("not found file"))
        mold_changed = sorted(file.mold_list) != sorted(molds)
        logger.info(f"Change mold, old: {file.mold_list}, new: {molds}, {mold_changed}")

        file = await self._updated_file(fid, name, molds, tags)
        if mold_changed:
            make_question.delay(fid)
        await NewHistory.save_operation_history(
            None,
            self.current_user.id,
            HistoryAction.MODIFY_FILE.value,
            self.current_user.name,
            meta=file.to_dict(),
        )
        return self.data(file.to_dict())

    async def _updated_file(self, fid, name, molds, tags):
        await NewFile.update_attr(fid, name=name, mold_list=molds, tags=tags)
        return await NewFile.find_by_id(fid)

    @Auth("browse")
    @peewee_transaction_wrapper
    async def delete(self, *args, **kwargs):
        fid = int(args[0])
        if not (file := await NewFile.find_by_id(fid)):
            return self.error(_("not found file"))
        pid = file.pid
        await self.check_project_permission(
            [], pid, any_permission=["remark", "remark_management", "manage_prj", "manage_user", "manage_mold"]
        )

        await NewHistory.save_operation_history(
            None,
            self.current_user.id,
            HistoryAction.DELETE_FILE.value,
            self.current_user.name,
            meta=file.to_dict(),
        )
        await file.cascade_delete()
        return self.data({})


@plugin.route(r"/files/(\d+)")
class FilesHandler(NewPermCheckHandler):
    @Auth("browse")
    async def get(self, fid):
        fid = int(fid)
        file = await NewFile.find_by_id(fid)
        if not file:
            raise CustomError(_("not found file"))
        return self.data({**file.to_dict(), "crumbs": await get_crumbs(file.tree_id)})


@plugin.route(r"/file/(\d+)/make_question")
class MakeQuestionHandler(NewPermCheckHandler):
    @Auth("browse")
    async def get(self, *args, **kwargs):
        fid = int(args[0])

        if not (file := await NewFile.find_by_id(fid)):
            raise CustomError(_("not found file"))
        make_question.delay(fid)

        self.write(file.to_dict())


@plugin.route(r"/file/(\d+)/pdf")
class PdfFileHandler(NewPermCheckHandler):
    @Auth("browse")
    async def get(self, fid):
        fid = int(fid)
        file = await NewFile.find_by_id(fid)
        if not file:
            raise CustomError(_("File not found."), resp_status_code=http.HTTPStatus.NOT_FOUND)

        if not file.pdf:
            raise CustomError(_("the pdf file is not ready"))

        if self.is_first_fetch_file():
            file_meta = await pw_db.first(HKEXFileMeta.select().where(HKEXFileMeta.fid == fid))
            meta = {}
            if file_meta:
                meta = {
                    "report_type": file_meta.doc_type,
                    "stock_code": file_meta.stock_code,
                    "year": file_meta.report_year,
                }
            await NewHistory.save_operation_history_by_user(
                file.qid, self.current_user, HistoryAction.OPEN_PDF, meta=meta
            )

        pdf_path = localstorage.mount(file.pdf_path())
        if not os.path.exists(pdf_path):
            # Note: 预发布环境的文件可能会丢失，需要重新下载
            logger.warning(f'pdf file not found: {file.id}:"{file.name}", will try to fetch it.')
            hkex_file: HKEXFile | None = await pw_db.first(HKEXFile.select().where(HKEXFile.fid == fid))
            if any(
                (
                    not hkex_file,
                    not hkex_file.url,
                    "hkexnews" not in hkex_file.url and "jura" not in hkex_file.url,
                )
            ):
                raise CustomError(_("File not found."), resp_status_code=http.HTTPStatus.NOT_FOUND)
            try:
                await stream_download(hkex_file.url, pdf_path)
            except httpx.HTTPStatusError as e:
                raise CustomError(
                    _("Something wrong with the re-fetching task."), resp_status_code=http.HTTPStatus.NOT_FOUND
                ) from e
            preset_answer.delay(file.id)
        return await self.export(pdf_path, file.name)


@plugin.route(r"/file/(\d+)/pdfinsight")
class PdfinsightCallbackHandler(NewPermCheckHandler):
    @use_args(
        {"run_predict": webargs.fields.Bool(load_default=True), "mid": webargs.fields.Integer(load_default=None)},
        location="query",
    )
    @peewee_transaction_wrapper
    async def post(self, fid, args):
        need_run_predict = args["run_predict"]
        mid = args["mid"]
        file = await NewFile.find_by_id(fid)
        if not file:
            raise CustomError(_("File not found(file_id:%s)") % fid)

        file_metas = self.request.files.get("file", None)
        if not file_metas:
            msg = f"No interdoc found for file: {fid}, please check"
            logger.error(msg)
            if self.request.headers.get("content-type") == "application/x-www-form-urlencoded":
                try:
                    arg_dict = {
                        k: "".join(i.decode() if isinstance(i, bytes) else str(i) for i in v)
                        for k, v in self.request.body_arguments.items()
                    }
                    arg_str = json.dumps(arg_dict, indent=2, ensure_ascii=False)
                except (json.JSONDecodeError, UnicodeDecodeError, AttributeError):
                    arg_str = str(self.request.body_arguments)
                msg += f"""\n```json\n{arg_str}\n```"""
            mm_notify(msg, error=True, tags=("interdoc_error", "not_found"))
            file.pdf_parse_status = PDFParseStatus.FAIL.value
            await pw_db.update(file, only=["pdf_parse_status"])
            await pw_db.execute(
                NewQuestion.update(ai_status=AIStatus.FAILED).where(
                    NewQuestion.fid == file.id, NewQuestion.ai_status.not_in([AIStatus.FINISH, AIStatus.NOPREDICT])
                )
            )
            return self.error(_("not found upload document"))

        meta = file_metas[0]
        doc_size = len(meta["body"])
        if doc_size <= 4 * 1024:
            msg = f"Interdoc({fid}) size: {doc_size} too small, please check"
            mm_notify(
                msg,
                error=True,
                tags=("interdoc_error", "invalid_size"),
            )
            logger.warning(msg)
            return self.error(_(msg))

        if file.pdfinsight:
            localstorage.delete_file(file.pdfinsight_path())
            localstorage.delete_dir(file.label_cache_dir)

        file.pdfinsight = hashlib.md5(meta["body"]).hexdigest()
        localstorage.write_file(file.pdfinsight_path(), meta["body"])
        file.pdf_parse_status = PDFParseStatus.COMPLETE
        await pw_db.update(file, only=["pdfinsight", "pdf_parse_status"])
        if need_run_predict:
            pdfinsight_path = file.pdfinsight_path(abs_path=True)
            file_mold = sorted(file.mold_list)[0]
            if file_mold in special_mold.need_embedding_mids:
                tasks = [
                    fix_syllabus_range.si(fid, pdfinsight_path),
                    parse_ar_year_end.si(fid),
                    save_embeddings.si(fid, need_sub_element=True),
                ]
                if special_mold.v6_agm_id in file.mold_list:
                    # note: set_agm_meta_info 需要用到 embedding 信息, 从 remark 挪到这里
                    tasks += [set_agm_meta_info.si(fid)]
                if special_mold.v6_poll_id in file.mold_list and get_config("feature.process_jura61_module"):
                    tasks += [set_poll_meta_info.si(fid)]
                tasks += [
                    recruit_crud_answer.si(fid, mid),
                    make_question.si(fid, mid),
                ]
            elif file_mold == special_mold.v6_1_mr_id:
                tasks = [set_mr_meta_info.si(fid)]
            else:
                # QR NDDR 目前不需要使用Embedding数据  QR 经常会集中发布 NDDR则数量众多
                tasks = (
                    fix_syllabus_range.si(fid, pdfinsight_path),
                    parse_ar_year_end.si(fid),
                    recruit_crud_answer.si(fid, mid),
                    make_question.si(fid, mid),
                )
            celery.chain(*tasks)()

        return self.write(file.to_dict())

    @Auth("browse")
    async def get(self, *args, **kwargs):
        fid = int(args[0])
        file = await NewFile.find_by_id(fid)
        if not file:
            raise CustomError(_("not found file"))
        await asyncio.ensure_future(self.check_project_permission(["browse"], file.pid))

        if not file.pdfinsight:
            raise CustomError(_("the pdfinsight file is not ready"))

        return await self.export(file.pdfinsight_path(abs_path=True), f"{file.name}.zip")


@plugin.route(r"/file/(\d+)/history")
class HistoryHandler(NewPermCheckHandler):
    @Auth("browse")
    async def get(self, fid, **kwargs):
        """查询文件操作历史"""
        page = int(self.get_argument("page", "1"))
        size = int(self.get_argument("size", "20"))
        if size > self.MAX_PAGE_SIZE:
            size = self.MAX_PAGE_SIZE

        file = await NewFile.find_by_id(int(fid))
        if not file:
            return self.data({"page": page, "size": size, "total": 0, "items": []})

        await self.check_project_permission(["browse"], file.pid)

        query = (
            NewHistory.select(
                NewHistory.uid, NewHistory.action, NewHistory.action_time, NewHistory.user_name.alias("name")
            )
            .where(NewHistory.qid == file.qid)
            .order_by(NewHistory.id.desc())
        )
        pagination = AsyncPagination(query, page, size)
        return self.data(await pagination.data())


@plugin.route(r"/files/(\d+)/outlines")
class GetOutlinesHandler(BaseHandler):
    @Auth("browse")
    @use_kwargs({"page": webargs.fields.Int(required=True)}, location="query")
    async def get(self, fid, page):
        file = await NewFile.find_by_id(int(fid))
        if not file:
            return self.error(_("File not found"))
        outlines = await self.run_in_executor(get_or_create_outlines, file)
        return self.data(outlines.get(str(page), []))


@plugin.route(r"/file/(\d+)/text_in_box")
class PDFTocTextInBoxHandler(NewPermCheckHandler):
    async def get_text_in_box(self, file, box, with_box: bool):
        get_text_func = get_text_from_chars_with_white if with_box else None

        pdf_cache = PDFCache(file)
        char_idx_range_path = pdf_cache.char_idx_range_path
        if not localstorage.exists(char_idx_range_path):
            logger.warning("char_idx_range_path: %s not found, rebuilding...", char_idx_range_path)
            await self.run_in_executor(pdf_cache.build, True)
            logger.info("char_idx_range_path: %s rebuilt", char_idx_range_path)
        text, chars = await self.run_in_executor(pdf_cache.get_text_in_box, box, get_text_func)

        if not text and get_config("client.ocr.enable", False):
            text, chars = await get_text_in_box_with_ocr(box, file.pdf_path(abs_path=True), get_text_func)

        if text and get_config("client.optimize_text_box", True):
            box["box"] = optimize_outline(chars)
        return {"box": box, "text": text or ""}

    @Auth("browse")
    @use_kwargs({"with_box": webargs.fields.Bool(load_default=False)}, location="query")
    async def post(self, fid, with_box):
        file = await NewFile.get_by_id(fid)
        if not file:
            raise CustomError(_("not found file"), resp_status_code=http.HTTPStatus.NOT_FOUND)
        await self.check_project_permission([], file.pid, any_permission=["remark", "remark_management", "manage_prj"])
        result = []
        boxes = self.get_json_body()
        rdb = init_rdb()
        try:
            for box in boxes:
                data = await self.get_text_in_box(file, box, with_box)
                # [78.61789999999999, 162.05270000000002, 541.1015, 203.49779999999998] -> [78.62, 162.05, 541.10, 203.50]
                data["box"]["box"] = [round(i, 2) for i in data["box"]["box"]]
                rdb.set(
                    box_to_text_key(file.hash, data["box"]),
                    data["text"],
                    ex=7200,
                )
                result.append(data)
        except Exception as e:
            logger.exception("get_text_in_box error")
            raise CustomError(_("get_text_in_box error"), resp_status_code=http.HTTPStatus.INTERNAL_SERVER_ERROR) from e
        await NewHistory.save_operation_history(
            None,
            self.current_user.id,
            HistoryAction.TEXT_IN_BOX.value,
            self.current_user.name,
            meta={"fid": file.id, "boxes": boxes},
        )
        return self.data(result)


@plugin.route(r"/file/(\d+)/search")
class PdfFileSearchHandler(NewPermCheckHandler):
    keyword_schema = {"keyword": fields.Str(required=True)}

    @Auth("browse")
    @use_kwargs(keyword_schema, location="query")
    async def get(self, fid, keyword):
        file = await NewFile.get_by_id(fid)
        if not file:
            raise CustomError(_("not found file"), resp_status_code=http.HTTPStatus.NOT_FOUND)
        await self.check_project_permission([], file.pid, any_permission=["remark", "remark_management", "manage_prj"])
        find_res = await self.run_in_executor(PDFCache(file).search, keyword)
        return self.data({"keyword": keyword, "results": find_res})


@plugin.route(r"/file/(\d+)/pageinfo")
class PdfPageInfoHandler(NewPermCheckHandler):
    @Auth("browse")
    async def get(self, fid):
        file = await NewFile.get_by_id(fid)
        if not file:
            raise CustomError(_("not found file"), resp_status_code=http.HTTPStatus.NOT_FOUND)
        await self.check_project_permission([], file.pid, any_permission=["remark", "remark_management", "manage_prj"])

        if not file.pdf:
            raise CustomError(_("Page-info data only for file.pdf"), resp_status_code=http.HTTPStatus.NOT_FOUND)

        pdf_cache = PDFCache(file)
        page_info_path = pdf_cache.page_info_path
        if not localstorage.exists(page_info_path):
            logger.warning("page_info_path: %s not found, rebuilding...", page_info_path)
            try:
                await self.run_in_executor(pdf_cache.build, True)
            except PdfInsightNotFound as exp:
                raise CustomError(
                    _("Interdoc data not found, need to wait for callback data"),
                    resp_status_code=http.HTTPStatus.NOT_FOUND,
                ) from exp
            logger.info("page_info_path: %s rebuilt", page_info_path)
        return self.data(pdf_cache.get_page_info())


@plugin.route(r"/file/(\d+)/chapter-info")
class PdfChapterInfoHandler(NewPermCheckHandler):
    @Auth("browse")
    async def get(self, fid):
        file = await NewFile.get_by_id(fid)
        if not file:
            raise CustomError(_("not found file"))
        await self.check_project_permission([], file.pid, any_permission=["remark", "remark_management", "manage_prj"])

        if not file.pdf:
            raise CustomError(_("Chapter-info data only for file.pdf"))

        pdf_cache = PDFCache(file)
        info_path = pdf_cache.chapter_info_path
        if not localstorage.exists(info_path):
            logger.warning("chapter_info_path: %s not found, rebuilding...", info_path)
            await self.run_in_executor(pdf_cache.build, True)
            logger.info("chapter_info_path: %s rebuilt", info_path)
        return self.data(pdf_cache.get_chapter_info())


@plugin.route(r"/project/(?P<project_id>\d+)/file")
class ProjectFilesHandler(NewPermCheckHandler):
    @NewCheckPerm(required_and=["browse"], project_id_param="project_id")
    async def get(self, *args, **kwargs):
        pid = kwargs["project_id"]
        page = int(self.get_argument("page", "1"))
        size = int(self.get_argument("size", "20"))
        mold_id = int(self.get_argument("mold_id", "-1"))
        conflicted = self.get_argument("conflicted", None) is not None
        qstatus = self.get_argument("status", None)

        default_health = get_config("web.default_question_health", 2)

        query = (
            NewFile.select()
            .where(
                NewFile.pid == pid,
                NewFile.mold == mold_id if mold_id != -1 else orm.TRUE,
                fn.EXISTS(
                    NewQuestion.select(1).where(
                        NewQuestion.fid == NewFile.id,
                        NewQuestion.status == qstatus if qstatus is not None else orm.TRUE,
                        NewQuestion.status == 4 if conflicted else orm.TRUE,
                    )
                ),
            )
            .order_by(NewFile.id.desc())
        )

        res = await AsyncPagination(query, page, size).data(
            NewQuestionWithFile.select(
                NewQuestionWithFile.id,
                NewQuestionWithFile.file,
                NewQuestionWithFile.mold,
                NewQuestionWithFile.ai_status,
                NewQuestionWithFile.health,
                NewQuestionWithFile.origin_health,
                NewQuestionWithFile.updated_utc,
                NewQuestionWithFile.progress,
            ),
            fields="id,tree_id,pid,name,hash,pdf,pdf_flag,tags,qid,page,size,mold,pdf_parse_status,mark_uids,mark_users,last_mark_utc,pdfinsight,docx,uid,annotation_path,farm_id,updated_utc,mold_list,questions",
        )
        res["files"] = res.pop("items")
        fill_lock_info(res["files"])
        for file in res["files"]:
            if file["questions"]:
                question = file["questions"][0]
                file["question_status"] = question["status"]
                file["question_ai_status"] = question["ai_status"]
                file["question_health"] = question["health"]
                file["preset_answer"] = question["preset_answer"] is not None
                file["origin_health"] = question["origin_health"] or default_health
        return self.data(res)


@plugin.route(r"/project/(\d+)/summary")
class SummaryHandler(NewPermCheckHandler):
    @Auth("browse")
    async def get(self, prj_id, **kwargs):
        """
        查询汇总统计数字:
        当前项目or目录总文档数, 总页数, 标注完成数, 未标注完成数,
        每个用户的标注数量, 登录访问次数
        """
        tree_id = self.get_argument("tree_id", None)
        page = int(self.get_argument("page", "1"))
        size = int(self.get_argument("size", "20"))

        if tree_id and tree_id.isdigit():
            tree_id = tree_id
        else:
            project = await NewFileProject.get_by_id(prj_id)
            tree_id = project.rtree_id
        trees = await NewFileTree.list_by_tree(tree_id)
        start = (page - 1) * size
        end = page * size
        tree_ids = [t.id for t in trees[start:end]]
        exclude_fids = []
        subtree_ids = [subtree.id for subtree in await NewFileTree.get_subtrees(tree_ids)]
        files = list(await NewFile.list_by_tree(tree_id, 1, extra_info=False))
        if len(tree_ids) < size:
            # 当前页有文件的情况
            subtree_ids.append(tree_id)
            file_end = end - len(tree_ids)
            file_start = max(file_end - size + len(tree_ids), 0)
            exclude_fids = [file.id for file in files[file_end:] + files[:file_start]]

        filter_by_file = orm.and_(
            NewFile.id.not_in(exclude_fids),
            NewFile.pid == prj_id,
            NewFile.tree_id.in_(subtree_ids) if subtree_ids else orm.TRUE,
        )
        total_file = await pw_db.count(NewFile.select().where(filter_by_file))
        total_page = (
            await pw_db.first(NewFile.select(fn.SUM(NewFile.page).alias("total_page")).where(filter_by_file))
        ).total_page
        rank = fn.ROW_NUMBER().over(partition_by=[NewFile.id], order_by=[NewQuestion.updated_utc.desc()]).alias("rank")
        query = (
            NewQuestion.select(rank, NewQuestion.status)
            .join(NewFile, on=(NewFile.id == NewQuestion.fid))
            .where(filter_by_file)
        )
        cte_q = query.cte("q")
        total_question = await pw_db.count(cte_q.select_from(1).where(cte_q.c.rank == 1).with_cte(cte_q))

        cte_stat = query.where(NewQuestion.status.in_([2, 4, 5, 10])).cte("stat")
        record = await pw_db.execute(
            cte_stat.select_from(cte_stat.c.status, fn.COUNT(1).alias("count"))
            .where(cte_stat.c.rank == 1)
            .group_by(cte_stat.c.status)
            .with_cte(cte_stat)
        )

        count_by_status = {r.status: r.count for r in record}
        marked = count_by_status.get(2, 0)
        conflicted = count_by_status.get(4, 0)
        finished = count_by_status.get(5, 0) + count_by_status.get(10, 0)

        mark_summary = await self.get_mark_summary(prj_id, tree_ids)
        return self.data(
            {
                "total_file": total_file,
                "total_question": total_question,
                "total_page": total_page if total_page else 0,
                "finished": finished,
                "conflicted": conflicted,
                "marked": marked,
                "users": mark_summary,
                "page": page,
                "total": len(tree_ids + files),
            }
        )

    @staticmethod
    async def get_mark_summary(prj_id, tree_ids):
        users = await pw_db.execute(AdminUser.select(AdminUser))
        user_by_id = {}
        for user in users:
            user_by_id[user.id] = {"name": user.name, "login_count": user.login_count}

        sql_user_summary = """
            select a.uid, count(*) as count\
            from answer a join question q on a.qid = q.id \
              join file f on q.id=f.qid \
            where pid=%(pid)s and f.deleted_utc=0 and f.tree_id{}\
              and q.status!=%(question_status_todo)s group by a.uid;
            """.format(" in ({})".format(", ".join(str(tid) for tid in tree_ids)) if tree_ids else ">-1")
        records = await pw_db.execute(
            sql_user_summary, {"question_status_todo": QuestionStatus.TODO.value, "pid": prj_id}
        )
        mark_summary = []
        for record in records:
            name = ""
            login_count = -1
            if record.uid in user_by_id:
                name = user_by_id[record.uid]["name"]
                login_count = user_by_id[record.uid]["login_count"]
            mark_summary.append(
                {"uid": record.uid, "markcount": record.count, "name": name, "login_count": login_count}
            )
        return mark_summary


def fill_lock_info(files: List[dict]) -> List[dict]:
    question_lock_mapping = {v: k for k, v in working_question_cache.items()}
    for _file in files:
        # logger.debug("%s", _file)
        _file["working_by"] = question_lock_mapping.get(_file["qid"], None)

    return files


@plugin.route(r"/file/(\d+)/prompt/(.+)")
class PromptHandler(NewPermCheckHandler):
    req_schema = {
        "question_id": webargs.fields.Int(validate=lambda x: x > 0, required=True),
        "groupby": webargs.fields.Int(validate=lambda x: x in ("page", "item"), load_default="item"),
        "size": webargs.fields.Int(validate=lambda x: x > 0, load_default=get_config("prompter.top_n", 5)),
        "threshold": webargs.fields.Float(
            validate=lambda x: x > 0, load_default=get_config("prompter.score_threshold", 0)
        ),
        "sub_rule": webargs.fields.Str(load_default=""),
    }

    @Auth("browse")
    @use_args(req_schema, location="query")
    async def get(self, fid, key, req_args):
        question_id = req_args["question_id"]
        group_by = req_args["groupby"]
        topn = req_args["size"]
        threshold = req_args["threshold"]
        items = []
        aid = attribute_id(key.split("|"))
        # # owa_util.signature attack?
        # if not special_mold.is_valid_rule(key):
        #     raise CustomError(_('Invalid request param'))
        question = await NewQuestion.get_by_id(question_id)
        aid = fix_aid(aid, question.mold, req_args["sub_rule"])
        if question.crude_answer:
            items = question.crude_answer.get(aid)
        else:
            # 重跑预测任务
            if question.ai_status not in (AIStatus.DOING.value, AIStatus.NOPREDICT.value):
                question.ai_status = AIStatus.DOING.value
                await pw_db.update(
                    question,
                    only=[
                        "ai_status",
                    ],
                )
                preset_question_answer.delay(question_id)
        # None或空直接返回
        if not items:
            return self.data([])
        items = [item for item in items if item["score"] >= threshold][:topn]

        if group_by == "page":
            grouped_items = [
                (key, list(items))
                for key, items in itertools.groupby(sorted(items, key=lambda p: p["page"]), lambda p: p["page"])
            ]
            groups = sorted(
                [
                    {
                        "score": sum([item["score"] for item in page_items]),
                        "text": "PAGE %s" % (page + 1,),
                        "page": page,
                        "outlines": [item["outline"] for item in page_items],
                    }
                    for page, page_items in grouped_items
                ],
                key=lambda g: g["score"],
                reverse=True,
            )
            return self.data(groups)
        else:
            return self.data(
                [
                    {
                        "score": item["score"],
                        "text": item.get("element_text") or item["text"],
                        "page": item["page"],
                        "outlines": [item["outline"]],
                    }
                    for item in items
                ]
            )


@plugin.route(r"/file/search")
class PageSearchHandler(DbQueryHandler):
    @Auth("browse")
    async def get(self, *args, **kwargs):
        filename = self.get_query_argument("filename", "")
        fileid = self.get_query_argument("fileid", "")
        username = self.get_query_argument("username", "")
        samefileid = self.get_query_argument("samefileid", "")

        search_sql = """SELECT {} FROM file LEFT JOIN file_project ON file.pid = file_project.id """
        if filename:
            filename = filename.replace("=", "==").replace("%", "=%").replace("_", "=_")
            search_condition = "WHERE file.name ILIKE %(filename)s ESCAPE '='"
        elif fileid:
            try:
                int(fileid)
            except ValueError as e:
                raise CustomError(_("Input search condition is invalid")) from e
            search_condition = "WHERE file.id=%(fileid)s"
        elif samefileid:
            search_condition = "WHERE file.hash=(select hash from file where id=%(samefileid)s)"
        elif username:
            user = await pw_db.first(AdminUser.select().where(AdminUser.name == username))
            if not user:
                return self.data({"page": 1, "size": 20, "total": 0, "items": []})
            search_condition = "JOIN answer ON file.qid=answer.qid WHERE answer.uid=%s" % user.id
        else:
            raise CustomError(_("Input search condition is invalid"))

        columns = [
            "file.id",
            "file.qid",
            "file.tree_id",
            "file.pid",
            "file.name",
            "file.mold_list",
            "hash",
            "pdf",
            "pdf_flag",
            "file.tags",
            "page",
            "size",
            "mold",
            "pdf_parse_status",
            "mark_uids",
            "mark_users",
            "last_mark_utc",
            "pdfinsight",
            """array(select row_to_json(t) from (
                select id, mold, ai_status, health, origin_health, updated_utc, progress from question where fid=file.id) as t
                ) as questions""",
        ]

        sql = (
            search_sql
            + search_condition
            + " AND file.deleted_utc = 0 AND file_project.deleted_utc = 0 AND file.tree_id != 0;"
        )
        pagedata = await self.pagedata_from_request(
            self,
            sql.format(",".join(columns)),
            columns,
            params={
                "filename": "%" + filename + "%",
                "fileid": fileid,
                "uid": self.current_user.id,
                "samefileid": samefileid,
            },
            orderby="ORDER BY id DESC",
        )
        question_lock_mapping = {v: k for k, v in working_question_cache.items()}
        await self.get_question_data(pagedata["items"], question_lock_mapping)
        return self.data(pagedata)

    @staticmethod
    async def get_question_data(items, question_lock_mapping):
        for item in items:
            qid = item["qid"]
            item["working_by"] = question_lock_mapping.get(qid, None)
            if qid:
                question_data = await pw_db.first(
                    NewQuestion.select(
                        NewQuestion.status,
                        NewQuestion.health,
                        NewQuestion.preset_answer.is_null(False).alias("preset_answer"),
                        NewQuestion.origin_health,
                    )
                    .where(NewQuestion.id == qid)
                    .dicts()
                )
                if question_data:
                    item.update(question_data)
            else:
                item.update(
                    {"question_status": None, "question_health": None, "preset_answer": None, "origin_health": None}
                )


@external_plugin.route(r"/file/(?P<fid>\d+)/prompt/element")
class CrudeAnswerHandler(BaseHandler):
    @Auth("browse")
    async def get(self, fid, *args, **kwargs):
        key = self.get_query_argument("key", "")
        group_by = self.get_query_argument("groupby", "page" if "hkex" in os.environ.get("ENV", "dev") else "item")
        if not fid or not key:
            raise CustomError(_("need fid and key"))

        key_path = [path.strip() for path in key.split("|")]
        aid = attribute_id(key_path)
        file = await NewFile.get_by_id(fid)
        if not file:
            raise CustomError(_("can't find file"))

        mold_data = await NewMold.get_by_id(file.mold, fields=("data",))

        for path in key_path[1:]:
            if not any(
                path in orders
                for orders in itertools.chain([schema["orders"] for schema in mold_data.get("schemas", [])])
            ):
                raise CustomError(_("key error (%s)" % path))

        question = await NewQuestion.get_by_id(file.qid, fields=("crude_answer",))
        items = predict_element(file, aid, group_by, cached_crude_answer=question.crude_answer)
        for item in items:
            for key in ["score", "outlines"]:
                if key in item:
                    item.pop(key)

        return self.data(items)


@external_plugin.route(r"/file/(?P<fid>\d+)/prompt/elements")
class AllCrudeAnswerHandler(BaseHandler):
    @Auth("browse")
    async def get(self, fid, *args, **kwargs):
        res = {}
        # group_by = self.get_query_argument('groupby', 'page' if 'hkex' in os.environ.get('ENV', 'dev') else 'item')
        group_by = self.get_query_argument("groupby", "item")
        topn = int(self.get_query_argument("size", get_config("prompter.top_n", 5)))
        threshold = float(self.get_query_argument("threshold", get_config("prompter.score_threshold", 0)))
        if not fid:
            raise CustomError(_("need fid"))

        file = await NewFile.get_by_id(fid)
        if not file:
            raise CustomError(_("can't find file"))

        question = await NewQuestion.get_by_id(file.qid, fields=("crude_answer",))
        crude_answer = question.crude_answer

        mold = await NewMold.get_by_id(file.mold, fields=("data",))
        schema = Schema(mold.data)
        for path in schema.leaf_iter():
            aid = attribute_id(path)
            items = predict_element(
                file, aid, group_by, cached_crude_answer=crude_answer, topn=topn, score_threshold=threshold
            )
            for item in items:
                for key in ["score", "outlines"]:
                    if key in item:
                        item.pop(key)
            res["|".join(path)] = items

        return self.data(res)


def predict_element(_file, aid, group_by="item", cached_crude_answer=None, topn=5, score_threshold=0):
    items = None
    if cached_crude_answer:
        items = cached_crude_answer.get(aid)
    else:
        # prompter = prompter_manager.get_schema_prompter(_file.mold)
        # if prompter:
        #     res = prompter.prompt(_file, aid)
        #     items = [{
        #         "score": item[0],
        #         "text": item[1].get("text") or (item[1].get("title") or "TABLE"),
        #         "page": item[1].get("page"),
        #     } for item in res]
        # TODO: predict in web process ?
        raise CustomError(_("not run predict yet"))
    items = [item for item in items or [] if item["score"] >= score_threshold][:topn]
    if group_by == "page":
        grouped_items = [
            (key, list(items))
            for key, items in itertools.groupby(sorted(items, key=lambda p: p["page"]), lambda p: p["page"])
        ]
        groups = sorted(
            [
                {
                    "score": sum([item["score"] for item in page_items]),
                    "text": "PAGE %s" % (page + 1,),
                    "page": page,
                    "outlines": [item["outline"] for item in page_items],
                }
                for page, page_items in grouped_items
            ],
            key=lambda g: g["score"],
            reverse=True,
        )
        return groups
    else:
        return [
            {"score": item["score"], "text": item["text"], "page": item["page"], "outlines": [item["outline"]]}
            for item in items
        ]


@external_plugin.route(r"/file/(?P<fhash>[^/]+)")
class FileUrlHandler(BaseHandler):
    @Auth("browse")
    async def get(self, fhash, *args, **kwargs):
        res = {}
        file = await NewFile.find_by_hash(fhash)
        if file:
            params = {"domain": get_config("web.domain"), "id": file.id}
            res = {
                "pdf": "http://%(domain)s/api/v1/plugins/fileapi/external/file/%(id)s/pdf" % params,
                "pdfinsight": "http://%(domain)s/api/v1/plugins/fileapi/external/file/%(id)s/pdfinsight" % params,
            }

        return self.data(res)


@external_plugin.route(r"/file/(\d+)/pdf")
class PdfFileExternalHandler(NewPermCheckHandler):
    @Auth("browse")
    async def get(self, *args, **kwargs):
        fid = int(args[0])
        file = await NewFile.find_by_id(fid)
        if not file:
            raise CustomError(_("not found file"))

        if not file.pdf:
            raise CustomError(_("the pdf file is not ready"))

        return await self.export(file.pdf_path(abs_path=True), f"{file.name}.pdf")


@external_plugin.route(r"/file/(\d+)/pdfinsight")
class PdfinsightExternaHandler(NewPermCheckHandler):
    @Auth("browse")
    async def get(self, *args, **kwargs):
        fid = int(args[0])
        file = await NewFile.find_by_id(fid)
        if not file:
            raise CustomError(_("not found file"))

        if not file.pdfinsight:
            raise CustomError(_("the pdfinsight file is not ready"))

        return await self.export(file.pdfinsight_path(abs_path=True), f"{file.name}.zip")
