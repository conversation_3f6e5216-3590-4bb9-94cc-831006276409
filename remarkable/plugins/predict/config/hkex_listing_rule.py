import re
from collections import defaultdict
from enum import IntEnum
from pathlib import Path

import yaml

from remarkable.common.pattern import PatternCollection
from remarkable.common.util import clean_txt
from remarkable.config import project_root
from remarkable.plugins.predict.common import is_paragraph_elt, is_table_elt
from remarkable.plugins.predict.models.regex_pattern import (
    PartialRegPredictor,
    RegPredictor,
)
from remarkable.plugins.predict.predictor import AIAnswerPredictor

PERCENTAGE_NUMBER_PATTERN = r"\d{1,3}(\.\d+)?%"
COMMON_DENY_PATTERN = r"no(?! doubt)|neither|nor|none|nil(?!\))|N\.M\."
R_MONEY_TYPE = r"(RMB|(HK|US)[DS]?\$?)"


class DisclosureEnum(IntEnum):
    PS = 0
    NS = 1
    ND = 2


deny_regs = re.compile(rf"\b(?P<dst>({COMMON_DENY_PATTERN}))\b", re.I)
deny_reg_with_not = re.compile(rf"\b(?P<dst>(insufficient|not|{COMMON_DENY_PATTERN}))\b", re.I)
deny_reg_with_less_than = re.compile(rf"\b(?P<dst>({COMMON_DENY_PATTERN}|less than))\b", re.I)
b35_keyword_pattern = (
    re.compile(r"\b(?P<dst>ultimate\s+(parent|control(ling)?|hold(ing)?))\b", re.I),
    re.compile(r"\b(?P<dst>((parent\s+)?holding|controlling\s+shareholder)\s?(of the)?\s?company)\b", re.I),
)
b57_neg_pattern = re.compile(r"(no|not).*? (significant )?(investments?|events|material acquisitions)")

b11_neg_pattern = re.compile(
    rf"\b({COMMON_DENY_PATTERN})\b.*Directors.*(has|had|have).*(long position|short positions?|interests|shares)", re.I
)

b15_neg_pattern = re.compile(r"(?:no|not).{0,50}(borrowings|bank loans?)")
b15_2_neg_patterns = (
    re.compile(r"(?:no|not)[\w\s]*?(?<!bank )(loans?|borrowings)"),
    # 对应"no interest-bearing borrowings"
    re.compile(r"no(ne?)? (interests?-bear(ing)?|external) (bank|loan|borrowing)s?", re.I),
    # 对应"did not have any bank/commit borrowings"
    re.compile(r"d(id|oes|o) not ha(s|ve)[\w\s]+\b(bank|commit(ted)?) (loan|borrowing)s?", re.I),
    # 对应"had no borrowings"
    re.compile(r"ha(ve|s|d) no (loan|borrowing)s?", re.I),
)
b24_31_regs = re.compile(
    r"\b(?P<dst>(contribution|pension|Mandatory\s*?Provident\s*?Fund\s*?Scheme|養老|benefit).*?)", re.I
)
SIX_TEXT = r"(six |\(?6\)? ){1,2}"
B6_REG_TEXT = rf"not? (less|fewer) than {SIX_TEXT}(independent|share)?\s?(placees?|professional)|at (less|fewer) {SIX_TEXT}|independent third part(y|ies)"
b6_regs = [re.compile(B6_REG_TEXT, re.I)]

# 有时表头内容过多, 用于描述金额/单位/附注的文字会被挤到下一行, 这种cell是不需要提取的
exclude_cell_patterns = [
    re.compile(r"^HK\$$"),
    re.compile(r"^['`’]0+"),
    re.compile(r"^period", re.I),
    re.compile(r"^HK\$\s?['`’]0+"),
    re.compile(r"於|年[內内]|行使|註銷|授出|失效"),
    re.compile(r"^([\(（]?note|附)", re.I),
]
b15_other_borrowing_pos_pattern = re.compile(r"(?<!bank )(loans?|borrowings)")
b15_bank_borrowing_pos_pattern = re.compile(r"bank loans?")
b55_neg_pattern = re.compile(r"not?[\w\s]* contingent liabilities(?![\w\s]*not[\w\s]*\.)")
b53_neg_pattern = re.compile(r"\b(?P<dst>(no|neither|nor|none|N\.M\.))\b", re.I)
cell_neg_pattern = re.compile(r"^((-|—|–|N/A|0(?!.)|0%|not applicable|nil)\s?)+$|(^$)", re.I)
# b1_10_pass_pattern = re.compile(r'Subscription|agreement', re.I)
b8_content_pattern = re.compile(
    r"((use|utili[sz]e|intende|propose)d?\s?of\s?(proceed|share)s?|warrant\s?share|(gross|net)\s?proceeds?).*?"
    r"(?=share|subscription|plac)",
    re.I,
)
b19_exclude_pattern = re.compile(r"Directors?[’']?\s?(pay|emolu)ments?|(pay|emolu)ments?\s?of[^,.]*?Director", re.I)
b19_include_pattern = re.compile(r"Five highest|top 5|五名最高薪", re.I)
b6n_share_option_pattern = re.compile(
    r"share.*option|option.*scheme|share.*compensation|option.*outstanding|terms.*condition.*grant", re.I
)
b62_pattern = re.compile(r"(loss|gain).*?on disposals?", re.I)
b69_content_pattern = re.compile(
    r"minimum.*period|exercise period.*vesting period|vesting period.*exercise period", re.I
)
b36_deny_regs = re.compile(r"\b(?P<dst>(no(?!( (sign|non)|n))|neither|nor|none|nil(?!\))|N\.M\.))\b", re.I)
b12_deny_reg_with_not = re.compile(
    r"\b(?P<dst>(insufficient|not|no(?! doubt)|neither|nor|none(?! of)|nil(?!\))|N\.M\.))\b", re.I
)
b50_deny_reg_with_less_than = re.compile(
    rf"\b(?P<dst>({COMMON_DENY_PATTERN}|(less than|not? more than)\s?30%?)|due to the nature|as follow)", re.I
)
b58_deny_regs = re.compile(rf"\b(?P<dst>({COMMON_DENY_PATTERN}|not))\b", re.I)
b51_title_pattern = re.compile(r"^(MAJOR|MAIN)?\s?(CUSTOMER|SUPPLIER)S?\s?AND\s?(CUSTOMER|SUPPLIER)S?$")
b14_paragraph_pattern = re.compile(
    r"(incorporat(ed)?|domicil).*?(in|at)\s?(?P<content>.*?)([,\.]|\band\b|\bon\b)",
    re.I,
)

b45_paragraph_pattern = (
    re.compile(
        r"(largest|major|supplier|top five|top 5)\b.*?(?P<content>[\d.]+%)",
        re.I,
    ),
    re.compile(
        r"(?P<content>[\d.]+%)\s*and\s*[\d.]+%.*(largest|major)\s?supplier\s?and\s?(five largest|top 5|top five)\s?suppliers",
        re.I,
    ),
)

b46_paragraph_pattern = (
    re.compile(
        rf"(supplier|purchase|供應商|採購)[^.\d]*?(?P<content>{PERCENTAGE_NUMBER_PATTERN})",
        re.I,
    ),
    re.compile(
        r"[\d.]+%\s*and\s*(?P<content>[\d.]+%).*(largest|major)\s?supplier\s?and\s?(five largest|top 5|top five)\s?suppliers",
        re.I,
    ),
)
b46_deny_reg_with_not = re.compile(
    r"\b(?P<dst>(insufficient|not|no(?! (doubt|time during))|neither|nor|none|nil(?!\))|N\.M\.))\b", re.I
)
b47_paragraph_pattern = re.compile(
    r"(largest|major|top five|top 5|five|5)\s*?(customer|client|largest|major).*?(?P<content>[\d.]+%)",
    re.I,
)
b48_paragraph_pattern = re.compile(
    rf"(\(?(five|5)\)?\s?largest|top\s?five)\s?(customers?|clients?)[^.\d].*?(?P<content>{PERCENTAGE_NUMBER_PATTERN})",
    re.I,
)

B49_black_feature_pattern = PatternCollection(
    [
        r"attributable to the Group’s five largest customers",
        r"a director of certain private companies",
        r"is one of the discretionary beneficiaries of certain discretionary trusts",
    ],
    re.I,
)

b52_paragraph_pattern = [
    re.compile(
        r"gearing ratio[^.]*from.*?to (approximately )?"
        r"(?P<content>nil|zero|N\/A|(\(?-?\d+(\.\d+)?\)?%)|(\d{1,3}(\.\d+)?))",
        re.I,
    ),
    re.compile(
        r"(gearing|equity) ratio[^.]*?(were|was|is|approximately) "
        r"(?P<content>nil|zero|N\/A|(\(?-?\d+(\.\d+)?\)?%)|(\d{1,3}(\.\d+)?))",
        re.I,
    ),
    re.compile(
        r"(rate|ratio)[^,]*?(decreas|amount|debt|asset)[^,]*?"
        r"\b(approximately|((to|on|is|was|are)(?!.*appr*)))\b\s?(?P<content>[\d.]*?%)",
        re.I,
    ),
    re.compile(
        r"(?P<content>gearing ratio|debt ratio|debt[\s\-]to[\s\-]asset|liability[\s\-]to[\s\-]asset|負債比率|槓桿比率)",
        re.I,
    ),
]
b64p6_paragraph_patterns = [
    re.compile(
        rf"clos(e|ing)\s?price[^.]*?shares?[^.]*?(is|was|were)\s?(?P<content>{R_MONEY_TYPE}?\s?\d+\.?\d+)",
        re.I,
    ),
]
b49_paragraph_pattern = [
    re.compile(r"related\s?party\s?transactions|transactions\s?with\s?its\s?related\s?parties", re.I),
]

b5_content_pattern = [re.compile(rf"approximately\s(?P<dst>{R_MONEY_TYPE}[\d.]+)\sper", re.I)]


class EnumDictator:
    enum_patterns = {
        "Negative Statement": [],
        "Positive Statement": [],
    }

    @classmethod
    def adjudicate_enum_value(cls, text="", enum_value="", answers=None):
        if enum_value and enum_value not in cls.enum_patterns:
            return enum_value
        if not text and answers:
            for items in answers:
                for item in items.data:
                    if not getattr(item, "chars", None):
                        continue
                    text += "".join([char["text"] for char in item.chars])
        text = clean_txt(text)
        for value, patterns in cls.enum_patterns.items():
            if enum_value != value and any(p.search(text) for p in patterns):
                return value
        return enum_value


class B11EnumDictator(EnumDictator):
    enum_patterns = {
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/1135#note_258985
        "Positive Statement": [
            re.compile(r"Save as disclosed above.*?none of the Directors?", re.I),
        ],
        "Negative Statement": [re.compile(r"none of")],
    }


class B50EnumDictator(EnumDictator):
    enum_patterns = {
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/846#note_220631
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/1408#note_275489
        "Positive Statement": [re.compile(r"five.*?supplier.*?[1-2][0-9]\.[0-9]{1,2}%")],
        "Negative Statement": [b50_deny_reg_with_less_than],
    }


class B52EnumDictator(EnumDictator):
    """
    关于0，nil，NA(not applicable)，这种描述都框选NS
    https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/1417#note_276347
    """

    enum_patterns = {
        "Positive Statement": [],
        "Negative Statement": [re.compile(r"(nil|NA)", re.I)],
    }


class B54EnumDictator(EnumDictator):
    """披露判断方面：

    B54有两个方面，是否有Foreign currency risk+是否有hedge policy

    当这两点都是否定描述时，为NS；
    任一点不是否定描述即为PS
    合规判断方面：

    PS/NS —— C
    ND —— NC
    """

    enum_patterns = {
        "Negative Statement": [
            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/1426#note_276249
            re.compile(
                r"The Group had not entered into any derivative contracts to hedge against the foreign exchange rate risk"
            ),
            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/1426#note_276252
            re.compile(
                r"the management monitors foreign exchange exposure and will consider hedging significant foreign currency exposure should the need arise"
            ),
            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/1426#note_276253
            re.compile(r"^\(ii\) Foreign exchange risk$"),
        ],
        "Positive Statement": [],
    }


class B69EnumDictator(EnumDictator):
    enum_patterns = {
        "Negative Statement": [re.compile(r"there\s?(is|was|were|are)\s?no\b[^,.]*?period", re.I)],
        "Positive Statement": [],
    }


class B63p1EnumDictator(EnumDictator):
    enum_patterns = {
        "Negative Statement": [re.compile(r"there\s?(is|was|were|are)\s?no\b[^,.]*?share\s?options?\b", re.I)],
        "Positive Statement": [],
    }


def find_syll_elts(pdfinsight, index, levels=2):
    """找最近的目录元素块"""
    res = []
    for i in pdfinsight.find_syllabuses_by_index(index)[-levels:]:
        try:
            _, element = pdfinsight.find_element_by_index(i["element"])
        except IndexError:
            continue
        if not element:
            continue
        res.append(element)
    return res


def find_eng_para_by_index_range(pdfinsight, elt, start, end):
    """找到给定范围内英文内容多的段落,允许在最近的一页找"""
    elts = []
    factor = -1 if start < 0 else 1
    index, limit_page = elt["index"], elt["page"]
    paras_nearby = pdfinsight.find_elements_by_index_range(index, start, end, etype="PARAGRAPH")
    for para in paras_nearby:
        if len(para["english_chars"]) > len(para["chinese_chars"]):
            elts.append(para)
    while not elts:
        index = index + factor
        if index < 0:
            break
        paras_nearby = pdfinsight.find_elements_by_index_range(index, start, end, etype="PARAGRAPH")
        if paras_nearby and abs(limit_page - paras_nearby[::factor][-1]["page"]) > 1:
            break
        for para in paras_nearby:
            if len(para["english_chars"]) > len(para["chinese_chars"]):
                elts.append(para)
    elts.extend(find_syll_elts(pdfinsight, index))
    return elts


def para_filter(element, pattern=None):
    if not is_paragraph_elt(element):
        return True
    return pattern.search(clean_txt(element["text"])) is not None


def b8_element_filter(element, **kwargs):
    if is_table_elt(element):
        paras_nearby = find_eng_para_by_index_range(kwargs["pdfinsight"], element, -2, 0)
        if any(b8_content_pattern.search(ele["text"]) for ele in paras_nearby):
            return True
    return para_filter(element, b8_content_pattern)


def b11_element_filter(element, **kwargs):
    return para_filter(element, re.compile(r"Directors.*(long position|short position|interests|shares)", re.I))


def b19_element_filter(element, **kwargs):
    if is_table_elt(element):
        paras_nearby = find_eng_para_by_index_range(kwargs["pdfinsight"], element, -2, 0)
        # return not any(b19_exclude_pattern.search(ele["text"]) for ele in paras_nearby)
        return any(b19_include_pattern.search(ele["text"]) for ele in paras_nearby)
    return False


def b46_element_filter(element, **kwargs):
    return para_filter(element, re.compile(r"less than 30", re.I))


def b45_element_filter(element, **kwargs):
    return para_filter(element, re.compile(r"large[^.]*?supplier\b|largest and[^.]*?suppliers", re.I))


def b47_element_filter(element, **kwargs):
    return para_filter(element, re.compile(r"large[^.]*?customer\b|largest and[^.]*?customers", re.I))


def b49_element_filter(element, **kwargs):
    return not B49_black_feature_pattern.nexts(clean_txt(element.get("text") or element.get("title", "")))


def b50_element_filter(element, **kwargs):
    # 先看标题, 然后再看正文
    paras_nearby = find_eng_para_by_index_range(kwargs["pdfinsight"], element, -2, 0)
    return any(para_filter(elt, b51_title_pattern) for elt in paras_nearby) or (
        para_filter(element, re.compile(r"less than 30", re.I))
        and para_filter(element, re.compile(r"((5|five) largest|major)[^.]*?suppliers?", re.I))
    )


def b51_element_filter(element, **kwargs):
    # 先看标题, 然后再看正文
    paras_nearby = find_eng_para_by_index_range(kwargs["pdfinsight"], element, -2, 0)
    return any(para_filter(elt, b51_title_pattern) for elt in paras_nearby) or (
        para_filter(element, re.compile(r"less than 30", re.I))
        and para_filter(element, re.compile(r"((5|five) largest|major)[^.]*?customer|consumer?", re.I))
    )


def b62_filter(element, **kwargs):
    if is_paragraph_elt(element) and not para_filter(element, b62_pattern):
        return False
    return True


def b6n_filter(element, **kwargs):
    if is_table_elt(element):
        paras_nearby = find_eng_para_by_index_range(kwargs["pdfinsight"], element, -2, 0)
    else:
        paras_nearby = find_eng_para_by_index_range(kwargs["pdfinsight"], element, -1, 1)
    return any(para_filter(ele, b6n_share_option_pattern) for ele in paras_nearby)


def b69_filter(element, **kwargs):
    # 在本段及上一段内容中找
    paras_nearby = find_eng_para_by_index_range(kwargs["pdfinsight"], element, -2, 1)
    return any(para_filter(elt, b69_content_pattern) for elt in paras_nearby)


predictors = [
    {
        "path": ["B1", "Issue reason"],
        "model": "partial_text",
        "regs": [re.compile(r"(?P<dst>the group.*loan)", re.I)],
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/1133#note_258934
        "ignore_pattern": [
            r"pursuant to a placing agreement and certain subscription agreements dated 20 May 2019",
            r"The Conversion Shares were issued on 7 February 2022",
            r"The latest independent actuarial valuation of the plan was at 31 December 2021 and was prepared by qualified staff of Towers Watson Hong Kong Limited",
        ],
    },
    {
        "path": ["B2", "Class of equity securities"],
        "model": "partial_text",
        "ignore_pattern": [
            r"pursuant to which the subscribers subscribed for a total of 139,500,000 Shares at a price of"
        ],
        "min_score": 0.009,
        "location_threshold": {"paragraph": 0.009},
    },
    {
        "path": ["B3", "Number of issued"],
        "model": ["partial_text", "syllabus_partial_text"],
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/1061#note_253057 特例 这个需要忽略掉
        "ignore_pattern": [r"On 31 July 2018.*?BaoQiao Partners Capital Limited.*?74,000,000"],
        "regs": [re.compile(r"(?P<dst>[\d,]+)\s*?(placing|new) shares", re.I)],
        "specified_year": True,
        "only_inject_features": True,
        "inject_syllabus_features": [
            r"__regex__Basic earnings per share$",
            r"__regex__31. SHARE CAPITAL$",
        ],
        "syllabus_partial_text_args": {
            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/1061#note_253053
            "regs": [
                # code=01410 fileId=37621&schemaId=5&rule=B3
                r"ordinary shares.*?2020.*?(?P<dst>[\d,]+).*?ordinary shares",
                # code=01566 fileId=37318&schemaId=5&rule=B3
                r"On 15 September 2020,.*?(?P<dst>[\d,]+).*?new ordinary shares",
            ]
        },
    },
    {
        "path": ["B4", "Issue price"],
        "model": "partial_text",
        "regs": [re.compile(rf"of\s(?P<dst>{R_MONEY_TYPE}[\d.]+)\sper", re.I)],
    },
    {
        "path": ["B5", "Net price"],
        "model": ["syllabus_partial_text", "partial_text"],
        "ignore_pattern": [
            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/1406#note_275027
            r"the November Placing completed and the Company issued and allotted the maximum 46,000,000",
            r"The Subscription Shares have a market value of approximately HK\$540.9 million based on the closing price of the shares as at 11 February 2020",
        ],
        "regs": b5_content_pattern,
        "inject_syllabus_features": ["__regex__Placing of new shares"],
        "syllabus_partial_text_args": {"regs": b5_content_pattern},
    },
    {
        "path": ["B6", "Names of allottes"],
        "model": "partial_text",
        "skip_diff_year_data": True,
        "regs": b6_regs,
    },
    {
        "path": ["B7", "Market price"],
        "model": "partial_text",
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/3874
        "regs": [re.compile(rf"closing price.{{1,20}}\s(?P<dst>{R_MONEY_TYPE}[\d.]+)\sper", re.I)],
        "skip_diff_year_data": True,
    },
    {
        "path": ["B8", "Detailed breakdown and description"],
        "model": ["table_tuple_select", "regex_pattern"],
        "patterns": [RegPredictor(deny_regs, 1, is_positive=False)],
        "select_by": "column",
        "multi_elements": True,
        "element_filter": b8_element_filter,
        "passed_result": 0,
    },
    {
        "path": ["B9", "Detailed breakdown and description of utilized amount"],
        "model": ["table_tuple_select", "partial_text", "regex_pattern"],
        "patterns": [RegPredictor(deny_regs, 1, is_positive=False)],
        "passed_result": 0,
    },
    {
        "path": ["B10", "Use of proceeds"],
        "model": "regex_pattern",
        "patterns": [RegPredictor(deny_regs, 1, is_positive=False)],
        "element_filter": b8_element_filter,
        "passed_result": 0,
    },
    {
        "path": ["B11", "Content"],
        "model": ["syllabus_elt_v2", "regex_pattern"],
        "element_filter": b11_element_filter,
        "patterns": [
            RegPredictor(b11_neg_pattern, DisclosureEnum.NS, is_positive=False),
            RegPredictor(
                re.compile(r"directors.*(long position|short position|interests|shares)", re.I),
                DisclosureEnum.PS,
            ),
        ],
        "only_inject_features": True,
        "inject_syllabus_features": [
            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/1135#note_258983
            r"__regex__15. Directors’ and General Director’s interests in the Shares and in the shares of associated corporations of the Company$",
            r"__regex__DIRECTORS’ AND CHIEF EXECUTIVES’ INTERESTS IN SECURITIES$",  # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/1135#note_258982
            r"__regex__Interests of Directors, Supervisors and chief executive in shares$",
            r"__regex__DIRECTORS’ AND CHIEF EXECUTIVE’S INTERESTS IN SHARES AND UNDERLYING SHARES$",
        ],
        "passed_result": 0,
        "include_table": True,
        "location_threshold": {"paragraph": 0.008},
        "enum_dictator": B11EnumDictator,
    },
    {
        "path": ["B12", "Name"],
        "model": ["partial_text", "table_tuple_select", "regex_pattern"],
        "patterns": [
            RegPredictor(
                re.compile(r"\b(?P<dst>(interest|short\s*?position|long\s*?position))\b", re.I),
                0,
                include_non_paragraph=False,
            ),
        ],
        "neg_pattern": b12_deny_reg_with_not,
        "passed_result": 0,
    },
    {
        "path": ["B12", "Interest or short position"],
        "model": ["partial_text", "table_tuple_select"],
        "neg_pattern": b12_deny_reg_with_not,
    },
    {
        "path": ["B12", "Class"],
        "model": ["partial_text", "b12_class"],
        "dump_filename": "B12_Number of securities",
        # TODO: 引入 LLM 来纠正否定正则判定错误的问题
        # 1. https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/4730#note_530264
        "neg_pattern": b12_deny_reg_with_not,
    },
    {
        "path": ["B12", "Number of securities"],
        "model": ["table_tuple_select"],
        "select_by": "column",
        "multi_elements": True,
    },
    {
        "path": ["B13", "Content"],
        "model": ["syllabus_elt_v2", "regex_pattern"],
        "patterns": [
            RegPredictor(deny_regs, 1, is_positive=False),
        ],
        "only_inject_features": True,
        "inject_syllabus_features": [
            r"__regex__SUBSTANTIAL\s?SHAREHOLDERS’\s?INTERESTS$",
            r"__regex__INTERESTS IN THE SHARES AND UNDERLYING SHARES OF SUBSTANTIAL SHAREHOLDERS",
        ],
        "break_para_pattern": [r"DIRECTORS’ REPORT"],
        "include_table": True,
        "passed_result": 0,
    },
    {
        "path": ["B14", "Pre-emptive right"],
        "model": "regex_pattern",
        "patterns": [
            RegPredictor(deny_regs, 1, is_positive=False),
        ],
        "passed_result": 0,
        "location_threshold": {"paragraph": 0.024},
    },
    {
        "path": ["B14", "Incorporated place"],
        "model": ["partial_text", "para_match"],
        "paragraph_pattern": b14_paragraph_pattern,
        "content_pattern": b14_paragraph_pattern,
    },
    {
        "path": ["B15", "Bank loans and overdrafts"],
        "model": ["table_tuple_select", "regex_pattern"],
        "patterns": [
            # ConfidencePredictor(0.5),
            RegPredictor(b15_neg_pattern, 1, is_positive=False, include_non_paragraph=False),
            RegPredictor(b15_bank_borrowing_pos_pattern, 2, include_non_paragraph=False),
        ],
        "passed_result": 0,
        "select_by": "both",
        "cell_neg_pattern": cell_neg_pattern,
        # "cell_patterns": [re.compile(r'^(\d{1,3},)*\d{1,3}$'), re.compile(r'^(—|–)$')],
        "exclude_header_patterns": [
            re.compile(r"\d{4}"),
            re.compile(r"^\s*$"),
            re.compile(r"^(RMB?|HK\$)’000((?!\|).)$|(?<!\|)(RMB?|HK\$)’000$"),
            re.compile("^D_date$"),
            re.compile("^total$", re.I),
        ],
        "inject_table_features": [
            r"__regex__(?i)Bank\s*?borrowing|__regex__largest_year_minus_0|__regex__(?i)^Current",
        ],
        "location_threshold": {"table": 0.4},
    },
    {
        "path": ["B15", "Other borrowings"],
        "model": ["table_tuple_select", "regex_pattern"],
        "patterns": [
            # ConfidencePredictor(0.5),
            RegPredictor(re.compile(r"not?\b.*?indebtedness"), 1, is_positive=False, include_non_paragraph=False),
            RegPredictor(
                re.compile(r"(other\s+borrowings).*?(set\s+out)"), 2, is_positive=False, include_non_paragraph=False
            ),
            RegPredictor(b15_2_neg_patterns, 1, is_positive=False, include_non_paragraph=False),
            RegPredictor(b15_other_borrowing_pos_pattern, 2, include_non_paragraph=False),
        ],
        "passed_result": 0,
        "select_by": "both",
        "cell_neg_pattern": cell_neg_pattern,
        # "cell_patterns": [re.compile(r'^(\d{1,3},)*\d{1,3}$'), re.compile(r'^(—|–)$')],
        "exclude_header_patterns": [
            re.compile(r"\d{4}"),
            re.compile(r"^\s*$"),
            re.compile(r"^(RMB?|HK\$)’000((?!\|).)$|(?<!\|)(RMB?|HK\$)’000$"),
            re.compile("^D_date$"),
            re.compile("^total$", re.I),
        ],
        "exclude_table_title_patterns": [
            re.compile(r"((long|short)-term )?bank ?(loans?|borrowings)$", re.I),
            re.compile(r"Maturity of borrowings"),
        ],
        "location_threshold": {"table": 0.4},
    },
    {
        "path": ["B15", "Aggregate amount"],
        "model": ["table_tuple_select", "regex_pattern"],
        "patterns": [
            # ConfidencePredictor(0.5),
            RegPredictor(b15_neg_pattern, 2, include_non_paragraph=False),
        ],
        "passed_result": 1,
        "select_by": "both",
        "location_threshold": {"table": 0.4},
    },
    {
        "path": ["B16", "Content"],
        "model": "regex_pattern",
        "patterns": [
            RegPredictor(deny_reg_with_not, DisclosureEnum.NS, is_positive=False),
            RegPredictor(
                # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/1138#note_259028
                re.compile(
                    r"\b(?P<dst>((?=.*reserve)(?=.*distribut)|(?<!profits )retained(?! profits)|distribut).*?)", re.I
                ),
                DisclosureEnum.PS,
                include_non_paragraph=False,
                match_all_element=True,
            ),
        ],
        "passed_result": 0,
        "location_threshold": {"paragraph": 0.027},
    },
    {
        "path": ["B17", "Amount"],
        "model": ["b17_table_tuple_select", "b17_b18_class"],
        "select_by": "column",
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/1139#note_259039
        "inject_table_features": [
            r"__regex__largest_year_minus_0|__regex__rmb",
            r"__regex__salary|__regex__rmb",
            r"__regex__other benefits|__regex__rmb",
        ],
        # 优先匹配下列标题
        "select_table_title_patterns": [re.compile("Five highest paid employees|五名最高薪僱員", re.I)],
        "matching_keys_cell": [re.compile("salar(ies|y)|allowance|other benefits", re.I)],
        "mismatching_keys_cell": [re.compile("total", re.I)],
    },
    {
        "path": ["B18", "Amount"],
        "model": ["b17_b18_b19_class", "table_tuple_select", "b17_b18_class"],
        "select_by": "column",
        "exclude_cell_patterns": exclude_cell_patterns,
        "inject_table_features": [
            r"__regex__largest_year_minus_0",
        ],
        "multi_elements": True,
    },
    {
        "path": ["B19", "Amount"],
        "model": ["b17_b18_b19_class", "table_tuple_select"],
        "select_by": "both",
        "cell_neg_pattern": cell_neg_pattern,
        "header_pattern": re.compile(r"bonus|bonuses|incentive\s+payments"),
        # "element_filter": b19_element_filter,
        "exclude_cell_patterns": exclude_cell_patterns,
        "inject_table_features": [
            r"__regex__(?i)Discretionary bonus(es)?|__regex__largest_year_minus_0",
        ],
        "multi_elements": True,
        "location_threshold": {
            "table": 0.3,
        },
    },
    {
        "path": ["B20", "Content"],
        "model": "regex_pattern",
        "patterns": [
            RegPredictor(deny_reg_with_not, 1, is_positive=False),
        ],
        "passed_result": 0,
    },
    {
        "path": ["B21", "Content"],
        "model": "regex_pattern",
        "patterns": [
            RegPredictor(deny_reg_with_not, 1, is_positive=False),
        ],
        "passed_result": 0,
    },
    # B22.1 需要Five highes paid individuals下分析不同薪酬范围人数的表格，需要是emolument brands的整个表。
    # B22.2 需提取上述表格中的每行数据上线值
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/1050#note_253771
    {
        "path": ["B22", "Table"],
        "model": ["score_filter", "b22_class"],
        "location_threshold": {
            "table": 0.06,
        },
    },
    {"path": ["B22", "Upper limit"], "model": ["table_column_content", "b22_class"], "filter_upper_limit": True},
    {
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/1053#note_253850
        # 常提取retirement scheme，retirement benefit， pension scheme等相关标题下，
        # 讲retirement或者pension计划相关的组成或如何计算，一般在note 里面有专门的标题，
        # 或者 accounting policies会计政策章节
        "path": ["B23", "Brief outline"],
        "model": ["syllabus_elt_v2", "partial_text"],
        "only_inject_features": True,
        "inject_syllabus_features": [
            r"__regex__Retirement\s?benefits?$",
            r"__regex__Retirement\s?benefit\s?schemes?$",
        ],
        "break_para_pattern": [r"[\u4e00-\u9fa5]"],
    },
    {
        "path": ["B23", "Pension scheme elsewhere"],
        "model": "table_tuple_select",
        "select_by": "both",
        "header_pattern": re.compile(r"retire(ment)?s?.*?benefit", re.I),
    },
    {
        "path": ["B24", "Detail of forfeited contributions"],
        "model": "regex_pattern",
        "patterns": [
            RegPredictor(deny_regs, 1, is_positive=False),
            RegPredictor(b24_31_regs, 0, include_non_paragraph=False),
        ],
        "passed_result": 0,
    },
    {
        "path": ["B25", "Name of actuary"],
        "model": "partial_text",
        "regs": [b24_31_regs],
    },
    {
        "path": ["B26", "Qualification of actuary"],
        "model": "partial_text",
        "regs": [b24_31_regs],
    },
    {
        "path": ["B27", "Actuarial method "],
        "model": "partial_text",
        "regs": [b24_31_regs],
    },  # NOTE: 注意此处应多个空格
    {
        "path": ["B28", "Description of actuarial assumptions"],
        "model": "regex_pattern",
        "patterns": [RegPredictor(deny_regs, 1, is_positive=False), RegPredictor(b24_31_regs, 0)],
        "passed_result": 0,
    },
    {
        "path": ["B29", "Market value "],
        "model": ["table_tuple_select", "partial_text"],
        "select_by": "both",
        "regs": [b24_31_regs],
    },  # NOTE: 注意此处应多个空格
    {
        "path": ["B30", "Level of funding"],
        "model": ["table_tuple_select", "partial_text"],
        "select_by": "both",
        "regs": [b24_31_regs],
    },
    {
        "path": ["B31", "Comments"],
        "model": "regex_pattern",
        "patterns": [RegPredictor(deny_regs, 1, is_positive=False), RegPredictor(b24_31_regs, 0)],
        "passed_result": 0,
    },
    {
        "path": ["B32", "Content"],
        "model": ["b32_class", "regex_pattern"],
        "patterns": [RegPredictor(deny_regs, 1, is_positive=False)],
        "passed_result": 0,
        "location_threshold": {"paragraph": 0.0015, "table": 0.0015},
    },
    {
        "path": ["B33", "Content"],
        "model": "regex_pattern",
        "patterns": [RegPredictor(deny_regs, 1, is_positive=False)],
        "passed_result": 0,
    },
    {
        "path": ["B34", "Table"],
        "model": ["b34_class", "score_filter", "regex_pattern"],
        "patterns": [
            RegPredictor(
                re.compile(r'\b(?P<dst>(position|balance|asset|debt|capital|financial|財務|債|益").*?)', re.I),
                0,
            )
        ],
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/1062
        "inject_syllabus_features": [
            r"__regex__38.SUMMARYFINANCIALINFORMATIONOFTHECOMPANY",
            r"__regex__61. SUMMARY OF FINANCIAL INFORMATION OF THE COMPANY",
            r"__regex__II. FINANCIAL STATEMENTS",
            r"__regex__Statement of Financial Position",
            r"__regex__35.1.Company balance sheet$",
        ],
        "only_inject_features": True,
        "include_table": True,
        "passed_result": 0,
        "location_threshold": {"table": 0.025},
    },
    {
        "path": ["B35", "Content"],
        "model": "regex_pattern",
        "patterns": [
            RegPredictor(deny_regs, 1, is_positive=False),
            RegPredictor(b35_keyword_pattern, 2),
        ],
        "passed_result": 0,
    },
    {
        "path": ["B35", "Controlling interest"],
        "model": ["table_tuple_select", "regex_pattern"],
        "patterns": [
            RegPredictor(deny_regs, 1, is_positive=False, include_non_paragraph=False),
            RegPredictor(b35_keyword_pattern, 2, include_non_paragraph=False),
        ],
        "passed_result": 0,
        "select_by": "column",
        # "header_pattern": b35_keyword_pattern,
        "exclude_cell_patterns": exclude_cell_patterns,
        "exclude_header_patterns": [
            re.compile(r"^Number of", re.I),
        ],
        "location_threshold": {"table": 0.4},
    },
    {
        "path": ["B36", "Content"],
        "model": ["syllabus_elt_v2", "b36_class", "regex_pattern"],
        "patterns": [
            RegPredictor(b36_deny_regs, 1, is_positive=False),
            RegPredictor(
                re.compile(
                    r"(?P<dst>(remuner|audit|fee\b|核數|酬金|費用|薪酬|auditors).*?)",  # 内容不再精确
                    re.I,
                ),
                0,
                parse_table_title=True,
            ),
        ],
        "only_inject_features": True,
        "inject_syllabus_features": [
            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/1076#note_255385
            r"__regex__10.AUDITORS’REMUNERATION$",
        ],
        "passed_result": 0,
        "location_threshold": {"table": 0.05, "paragraph": 0.08},
    },
    {
        "path": ["B37", "Content"],
        "model": "regex_pattern",
        "patterns": [RegPredictor(deny_regs, 1, is_positive=False)],
        "passed_result": 0,
    },
    {
        "path": ["B38", "Content"],
        "model": "regex_pattern",
        "patterns": [
            PartialRegPredictor(deny_regs, 1, is_positive=False),
        ],
        "passed_result": 0,
    },
    {
        "path": ["B41", "Content"],
        "model": "regex_pattern",
        "patterns": [RegPredictor(deny_regs, 1, is_positive=False)],
        "passed_result": 0,
    },
    {
        "path": ["B42", "Content"],
        "model": "regex_pattern",
        "patterns": [RegPredictor(deny_regs, 1, is_positive=False)],
        "passed_result": 0,
    },
    {
        "path": ["B43", "Management contract"],  # TODO: 效果不好
        "model": "regex_pattern",
        "patterns": [
            RegPredictor(deny_reg_with_not, 1, is_positive=False),
            RegPredictor(
                re.compile(
                    r"\b(?P<dst>(report of the director|service contract|service agreement|none of the director|no director|appoint|commence).*?)",
                    re.I,
                ),
                0,
            ),
        ],
        "passed_result": 0,
    },
    {
        "path": ["B43", "Service contract"],
        "model": "regex_pattern",
        "patterns": [RegPredictor(deny_regs, 1, is_positive=False)],
        "passed_result": 0,
    },
    {
        "path": ["B43", "Director list"],
        "model": "regex_pattern",
        "patterns": [RegPredictor(deny_regs, 1, is_positive=False)],
        "passed_result": 0,
    },
    {
        "path": ["B44", "Content"],
        "model": "regex_pattern",
        "patterns": [RegPredictor(deny_regs, 1, is_positive=False)],
        "passed_result": 0,
        "location_threshold": {"paragraph": 0.4, "table": 0.4},
    },
    {
        "path": ["B45", "Percentage"],
        "model": ["syllabus_table_cell", "table_tuple_select", "multi_percents", "para_match", "regex_pattern"],
        "patterns": [RegPredictor(deny_regs, 1, is_positive=False)],
        "paragraph_pattern": b45_paragraph_pattern,
        "content_pattern": b45_paragraph_pattern,
        "passed_result": 0,
        "element_filter": b45_element_filter,
        "location_threshold": {"table": 0.1, "paragraph": 0.1},
        "inject_syllabus_features": [
            r"__regex__FIVE YEAR SUMMARY$",
        ],
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/1088#note_255960
        "syllabus_table_header": re.compile(r"\bThe largest supplier\b", re.I),
    },
    {
        "path": ["B46", "Percentage"],
        "model": ["syllabus_table_cell", "table_tuple_select", "multi_percents", "para_match", "regex_pattern"],
        # "patterns": [ConfidencePredictor(0.5),],
        "patterns": [
            RegPredictor(b46_deny_reg_with_not, 1, is_positive=False),
        ],
        "paragraph_pattern": b46_paragraph_pattern,
        "content_pattern": b46_paragraph_pattern,
        "passed_result": 2,
        "element_filter": b46_element_filter,
        "inject_syllabus_features": [
            r"__regex__FIVE YEAR SUMMARY$",
        ],
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/1126
        "syllabus_table_header": re.compile(r"\bFive largest suppliers in aggregate\b", re.I),
    },
    {
        "path": ["B47", "Percentage"],
        "model": ["table_tuple_select", "multi_percents", "para_match", "regex_pattern"],
        "patterns": [
            RegPredictor(b46_deny_reg_with_not, 1, is_positive=False),
        ],
        "paragraph_pattern": b47_paragraph_pattern,
        "content_pattern": b47_paragraph_pattern,
        "element_filter": b47_element_filter,
        "location_threshold": {"table": 0.4, "paragraph": 0.06},
        "passed_result": 0,
    },
    {
        "path": ["B48", "Percentage"],
        "model": [
            "table_tuple_select",
            "multi_percents",
            "para_match",
            "regex_pattern",
            "multi_percents_from_syllabus",
        ],
        "patterns": [
            RegPredictor(b46_deny_reg_with_not, 1, is_positive=False),
        ],
        "inject_table_features": [
            r"__regex__five largest customers|__regex__percentage (%)|__regex__largest_year_minus_0",
        ],
        "paragraph_pattern": b48_paragraph_pattern,
        "content_pattern": b48_paragraph_pattern,
        "passed_result": 2,
        "syllabus_pattern": [r"^MAJORCUSTOMERSANDSUPPLIERS$"],
        "location_threshold": {"paragraph": 0.09},
    },
    {
        "path": ["B49", "Content"],
        "model": ["para_match", "regex_pattern"],
        "element_filter": b49_element_filter,
        "patterns": [RegPredictor(deny_regs, 1, is_positive=False)],
        "paragraph_pattern": b49_paragraph_pattern,
        "neg_pattern": deny_regs,
        "passed_result": 0,
        "location_threshold": {"paragraph": 0.007},
    },
    {
        "path": ["B50", "Content"],
        "model": ["syllabus_elt_v2", "regex_pattern"],
        "patterns": [
            RegPredictor(b50_deny_reg_with_less_than, DisclosureEnum.NS, is_positive=False),
        ],
        "only_inject_features": True,
        "inject_syllabus_features": [
            r"__regex__Major Suppliers$",
            r"__regex__Major Customers and Suppliers$",
            r"__regex__Relationship with suppliers$",
            r"__regex__Purchases from major suppliers$",
            r"__regex__MAJOR SUBCONTRACTORS$",  # 59656
            r"__regex__GENERAL INFORMATION$",  # 37454
        ],
        "passed_result": 0,
        "element_filter": b50_element_filter,
        "break_para_pattern": [r"獲准許的彌償條文"],  # hkex 50766
        "location_threshold": {"table": 1, "paragraph": 0.019},
        "enum_dictator": B50EnumDictator,
    },
    {
        "path": ["B51", "Content"],
        "model": ["syllabus_elt_v2", "regex_pattern"],
        "patterns": [
            RegPredictor(b50_deny_reg_with_less_than, 1, is_positive=False),
        ],
        "regex_ignore_pattern": [
            r"the Group’s major customers and suppliers were as follows:$",
            r"MAJOR CUSTOMERS AND SUPPLIERS$",
        ],
        "passed_result": 0,
        "element_filter": b51_element_filter,
        "enum_dictator": B50EnumDictator,
        "only_inject_features": True,
        "inject_syllabus_features": [
            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/1408#note_275138
            r"__regex__Information about major customers$",
        ],
    },
    {
        "path": ["B52", "Ratio"],
        "model": ["syllabus_partial_text", "table_tuple_select", "para_match", "regex_pattern"],
        "patterns": [
            RegPredictor(re.compile(r"not applicable|inapplicable"), 1, is_positive=False),
        ],
        "passed_result": 0,
        "cell_neg_pattern": cell_neg_pattern,
        "paragraph_pattern": b52_paragraph_pattern,
        "content_pattern": b52_paragraph_pattern,
        "location_threshold": {"table": 0.3, "paragraph": 0.09},
        "only_inject_features": True,
        "enum_dictator": B52EnumDictator,
        "inject_syllabus_features": [
            r"__regex__^LIQUIDITY, FINANCIAL RESOURCES AND CAPITAL STRUCTURE",
            r"__regex__^Liquidity and financial resources",
            r"__regex__^(?:15. )?Gearing Ratio$",
            r"__regex__^Pledge of Assets",
        ],
        "multi": True,
        "syllabus_partial_text_args": {
            "regs": [
                r"the (?:Group’s)?\s?gearing ratio.*?was (?P<dst>(nil|\d+%))",  # stock code:02682
                r"The current gearing ratio.*?(?P<dst>[\d.]+) times",
                r"gearing ratio.*?from.*?to\s(?P<dst>([\d.]+%))",
            ]
        },
    },
    {
        "path": ["B53", "Content"],
        "model": "regex_pattern",
        "patterns": [
            RegPredictor(b53_neg_pattern, 1, is_positive=False),
        ],
        "passed_result": 0,
        "location_threshold": {"paragraph": 0.084},
    },
    {
        "path": ["B54", "Content"],
        "model": ["syllabus_elt_v2", "regex_pattern"],
        "patterns": [
            RegPredictor(
                [re.compile("foreign exchange risk aris"), deny_regs],
                DisclosureEnum.NS,
                is_positive=False,
            ),
        ],
        "enum_dictator": B54EnumDictator,
        "only_inject_features": True,
        "inject_syllabus_features": [
            r"__regex__foreign exchange exposure$",
            r"__regex__foreign currency risk$",
        ],
        "passed_result": 0,
        "location_threshold": {
            "paragraph": 0.005,
        },
    },
    {
        "path": ["B55", "Content"],
        "model": "regex_pattern",
        "patterns": [
            RegPredictor(b55_neg_pattern, 1, is_positive=False),
        ],
        "passed_result": 0,
    },
    {
        "path": ["B56", "Content"],
        "model": ["syllabus_elt_v2", "para_match", "regex_pattern"],
        "patterns": [
            RegPredictor(deny_regs, 1, is_positive=False),
            RegPredictor(
                re.compile(
                    r"vision|anticipat|believ|expect|ahead|further|outlook|continu|will|new|term|展望|前景", re.I
                ),
                0,
                is_positive=True,
            ),
        ],
        "paragraph_pattern": [
            re.compile(r"telitacicept to expand its clinical application"),
            re.compile(r"In the future.*?will.*?new business"),
        ],
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/1429#note_276322
        "location_threshold": {"paragraph": 0.012},
        "only_inject_features": True,
        "inject_syllabus_features": [
            r"__regex__^FUTURE OUTLOOK$",
            r"__regex__^Outlook and Future Plan$",
        ],
        "passed_result": 0,
    },
    # {
    #     "path": ["B57", "Significant investments"],
    #     "model": "partial_text",
    #     "neg_pattern": b57_neg_pattern,
    #     "location_threshold": {
    #         "table": 0.05,
    #         "paragraph": 0.05,
    #     }
    # },
    {
        "path": ["B57", "Significant investments"],
        "model": "regex_pattern",
        "patterns": [
            RegPredictor(b57_neg_pattern, 1, is_positive=False),
        ],
        "passed_result": 0,
    },
    # {"path": ["B58", "Summary of segmental information"], "model": "partial_text",},  # TODO: 取表头单元格，没有合适的模型
    {"path": ["B58", "Summary of segmental information"], "model": "segment_info"},
    {
        "path": ["B58", "Detail of segmental information"],
        "model": "regex_pattern",
        "patterns": [
            RegPredictor(deny_regs, 1, is_positive=False),
        ],
        "passed_result": 0,
    },
    {
        "path": ["B59", "Content"],
        "model": ["syllabus_elt_v2", "para_match", "regex_pattern"],
        "patterns": [
            RegPredictor(deny_regs, 1, is_positive=False),
        ],
        "passed_result": 0,
        "paragraph_pattern": [
            re.compile(r"emolument.*?employe(es)?"),
            re.compile(r"remuneration polic.*?employe(es)?"),
        ],
        "only_inject_features": True,
        "enabled_supplement_answer": True,
        "supplement_answer_regex": [r"A total of 83 people were employed during the year"],
        "inject_syllabus_features": [
            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/1460#note_279298
            r"__regex__^IV. PARTICULARS OF EMPLOYEES$",
            r"__regex__Remuneration of employees$",
            r"__regex__^EMPLOYEES$",
            r"__regex__^Remuneration$",
        ],
        "break_para_pattern": [r"^KEY RISKS FACTORS AND UNCERTAINTIES", r"3. Employee Training$"],
        "location_threshold": {"paragraph": 0.019},
    },
    {
        "path": ["B60", "Content"],
        "model": "regex_pattern",
        "patterns": [
            RegPredictor(deny_reg_with_not, 1, is_positive=False),
        ],
        "passed_result": 0,
    },
    {
        "path": ["B61", "Content"],
        "model": "regex_pattern",
        "patterns": [
            RegPredictor(deny_reg_with_not, 1, is_positive=False),
        ],
        "passed_result": 0,
    },
    {
        "path": ["B62", "Content"],
        "model": ["table_tuple_select", "regex_pattern"],
        "patterns": [
            RegPredictor(deny_regs, 1, is_positive=False),
        ],
        "passed_result": 0,
        "select_by": "both",
        "multi_elements": True,
        "cell_neg_pattern": cell_neg_pattern,
        "header_pattern": b62_pattern,
        "element_filter": b62_filter,
    },
    {
        "path": ["B63", "Number of options"],
        "model": ["table_tuple_select", "regex_pattern"],
        "patterns": [RegPredictor(deny_reg_with_not, 0)],
        "passed_result": 1,
        "select_by": "column",
        "multi_elements": True,
        "element_filter": b6n_filter,
        "enum_dictator": B63p1EnumDictator,
        "cell_neg_pattern": cell_neg_pattern,
        "exclude_cell_patterns": exclude_cell_patterns,
        "exclude_header_patterns": [
            re.compile(r"Grant|(Vest(ing|ed)?|Exercis(e|able)?)\s?(Period|Price)", re.I),
            re.compile(r"^(Forfeit|Grant|Vest(ing|ed)?|Exercis(e|able)?|Lapse|Exercis|Cancel)", re.I),
        ],
        "location_threshold": {"paragraph": 0.38, "table": 0.38},
    },
    {
        "path": ["B63", "Date of grant"],
        "model": ["table_tuple_select"],
        "select_by": "column",
        "brother_index": 0,
        "multi_elements": True,
        "exclude_header_patterns": [
            re.compile(r"^(Outstand|Exercise|Lapse|Forfeit|Cancel)", re.I),
            re.compile(r"Period|Price", re.I),
        ],
        "location_threshold": {"table": 0.4},
    },
    {
        "path": ["B63", "Vesting period"],
        "model": ["table_tuple_select", "regex_pattern"],
        "patterns": [
            RegPredictor(deny_regs, 1, is_positive=False),
        ],
        "passed_result": 0,
        "element_filter": b6n_filter,
        "header_pattern": re.compile(r"vest", re.I),
        "brother_index": 0,
        "exclude_cell_patterns": exclude_cell_patterns,
    },
    {
        "path": ["B63", "Exercise period"],
        "model": ["table_tuple_select", "regex_pattern"],
        "patterns": [
            RegPredictor(deny_regs, 1, is_positive=False),
        ],
        "passed_result": 0,
        "element_filter": b6n_filter,
        "header_pattern": re.compile(r"period", re.I),
        "brother_index": 0,
        "exclude_cell_patterns": exclude_cell_patterns,
    },
    {
        "path": ["B63", "Exercise price"],
        "model": ["table_tuple_select"],
        "select_by": "column",
        "multi_elements": True,
        "element_filter": b6n_filter,
        "brother_index": 0,
        "exclude_cell_patterns": exclude_cell_patterns,
    },
    {
        "path": ["B63", "Beginning amount"],
        "model": ["table_tuple_select"],
        "select_by": "both",
        "cell_neg_pattern": cell_neg_pattern,
        "multi_elements": True,
        "element_filter": b6n_filter,
        "brother_index": 0,
    },
    {
        "path": ["B63", "Ending amount"],
        "model": ["table_tuple_select"],
        "select_by": "both",
        "multi_elements": True,
        "element_filter": b6n_filter,
        "brother_index": 0,
    },
    {
        "path": ["B64", "Number of options"],
        "model": ["table_tuple_select", "regex_pattern"],
        "patterns": [
            RegPredictor(deny_regs, 1, include_non_paragraph=False),
        ],
        "cell_neg_pattern": cell_neg_pattern,
        # "cell_patterns": [re.compile(r'^(\d{1,3},)*\d{1,3}$'), re.compile(r'^(—|–)$')],
        "passed_result": 1,
        "header_pattern": re.compile(r"grant|granted", re.I),
        "select_by": "column",
        "element_filter": b6n_filter,
        "multi_elements": True,
        "location_threshold": {"table": 0.33},
        "exclude_header_patterns": [
            re.compile(r"Outstand|Exercise|Lapse|Forfeit|Cancel", re.I),
        ],
        "exclude_cell_patterns": exclude_cell_patterns
        + [re.compile(r"(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[a-z]*")],
    },
    {
        "path": ["B64", "Date of grant"],
        "model": ["table_tuple_select", "regex_pattern"],
        "patterns": [
            RegPredictor(deny_regs, 1, include_non_paragraph=False),
        ],
        "select_by": "column",
        "cell_neg_pattern": cell_neg_pattern,
        "header_pattern": re.compile(r"grant|granted", re.I),
        "multi_elements": True,
        "element_filter": b6n_filter,
        "passed_result": 0,
        "brother_index": 0,
    },
    {
        "path": ["B64", "Vesting period"],
        "model": ["table_tuple_select", "regex_pattern"],
        "patterns": [
            RegPredictor(deny_regs, 1, is_positive=False),
        ],
        "passed_result": 0,
        "cell_neg_pattern": cell_neg_pattern,
        "element_filter": b6n_filter,
        "header_pattern": re.compile(r"vest", re.I),
        "brother_index": 0,
        "exclude_cell_patterns": exclude_cell_patterns,
    },
    {
        "path": ["B64", "Exercise period"],
        "model": ["table_tuple_select", "regex_pattern"],
        "patterns": [
            RegPredictor(deny_regs, 1, is_positive=False),
        ],
        "passed_result": 0,
        "element_filter": b6n_filter,
        "brother_index": 0,
        "cell_neg_pattern": cell_neg_pattern,
        "header_pattern": re.compile(r"Exercise|period|date", re.I),
        "exclude_header_patterns": [
            re.compile(r"vest|Balance|Adjust|Grant|Lapse|Forfeit|Cancel|price", re.I),
        ],
        "exclude_cell_patterns": exclude_cell_patterns,
    },
    {
        "path": ["B64", "Exercise price"],
        "model": ["table_tuple_select"],
        "select_by": "column",
        "multi_elements": True,
        "element_filter": b6n_filter,
        "brother_index": 0,
        "cell_neg_pattern": cell_neg_pattern,
        "exclude_cell_patterns": exclude_cell_patterns,
        "exclude_header_patterns": [
            re.compile(r"^(Adjust|Outstand)", re.I),
        ],
        "location_threshold": {"table": 0.33},
    },
    {
        "path": ["B64", "Closing price"],
        "model": ["table_tuple_select", "para_match", "regex_pattern"],
        "patterns": [
            RegPredictor(deny_regs, 1, is_positive=False),
        ],
        "passed_result": 0,
        "select_by": "column",
        "element_filter": b6n_filter,
        "brother_index": 0,
        "cell_neg_pattern": cell_neg_pattern,
        "exclude_cell_patterns": exclude_cell_patterns,
        "location_threshold": {"table": 0.3, "paragraph": 0.3},
        "exclude_header_patterns": [
            re.compile(r"^(Adjust|Outstand)", re.I),
        ],
        "paragraph_pattern": b64p6_paragraph_patterns,
        "content_pattern": b64p6_paragraph_patterns,
    },
    {
        "path": ["B65", "Number of options"],
        "model": ["table_tuple_select", "regex_pattern"],
        "patterns": [RegPredictor(deny_regs, 0, include_non_paragraph=False)],
        "header_pattern": re.compile(r"exercised?", re.I),
        "passed_result": 1,
        "select_by": "column",
        # "cell_patterns": [re.compile(r'^(\d{1,3},)*\d{1,3}$'), re.compile(r'^(—|–)$')],
        "exclude_cell_patterns": exclude_cell_patterns,
        "multi_elements": True,
        "cell_neg_pattern": cell_neg_pattern,
        "element_filter": b6n_filter,
        "location_threshold": {"table": 0.3, "paragraph": 0.3},
    },
    {
        "path": ["B65", "Exercise price"],
        "model": ["table_tuple_select"],
        "select_by": "column",
        "exclude_header_patterns": [
            re.compile(r"^(Adjust|Outstand)", re.I),
            re.compile(r"period|date", re.I),
        ],
        "exclude_cell_patterns": exclude_cell_patterns,
        "multi_elements": True,
        "element_filter": b6n_filter,
        "brother_index": 0,
        "location_threshold": {"table": 0.3, "paragraph": 0.3},
    },
    {
        "path": ["B65", "Closing price"],
        "model": ["table_tuple_select", "para_match", "regex_pattern"],
        "select_by": "column",
        "exclude_cell_patterns": exclude_cell_patterns,
        "multi_elements": True,
        "element_filter": b6n_filter,
        "brother_index": 0,
        "patterns": [
            RegPredictor(deny_regs, 1, is_positive=False, include_non_paragraph=False),
        ],
        "passed_result": 0,
        "cell_neg_pattern": cell_neg_pattern,
        "location_threshold": {"table": 0.3, "paragraph": 0.3},
        "exclude_header_patterns": [
            re.compile(r"^(Adjust|Outstand|Lapse|Exercise)", re.I),
        ],
        "paragraph_pattern": b64p6_paragraph_patterns,
        "content_pattern": b64p6_paragraph_patterns,
    },
    {
        "path": ["B66", "Number of options"],
        "model": ["table_tuple_select", "regex_pattern"],
        "patterns": [
            RegPredictor(deny_regs, 1, is_positive=False, include_non_paragraph=False),
        ],
        "passed_result": 0,
        "header_pattern": re.compile(r"cancel", re.I),
        "cell_neg_pattern": cell_neg_pattern,
        # "cell_patterns": [re.compile(r'^(\d{1,3},)*\d{1,3}$'), re.compile(r'^(—|–)$')],
        "select_by": "column",
        "exclude_cell_patterns": exclude_cell_patterns,
        "multi_elements": True,
        "element_filter": b6n_filter,
        "exclude_header_patterns": [re.compile(r"^Former", re.I)],
        "location_threshold": {"table": 0.3, "paragraph": 0.3},
    },
    {
        "path": ["B66", "Exercise price"],
        "model": ["table_tuple_select"],
        # "header_pattern": re.compile(r'cancel|canceled', re.I),
        "cell_neg_pattern": cell_neg_pattern,
        "select_by": "column",
        "multi_elements": True,
        "element_filter": b6n_filter,
        "brother_index": 0,
        "exclude_header_patterns": [
            re.compile(r"^(Adjust|Outstand)", re.I),
            re.compile(r"period|date", re.I),
        ],
        "exclude_cell_patterns": exclude_cell_patterns,
        "location_threshold": {"table": 0.3, "paragraph": 0.3},
    },
    {
        "path": ["B67", "Number of options"],
        "model": ["table_tuple_select", "regex_pattern"],
        "patterns": [
            RegPredictor(deny_regs, 1, is_positive=False, include_non_paragraph=False),
        ],
        "passed_result": 0,
        "header_pattern": re.compile(r"lapse|forfeit", re.I),
        "cell_neg_pattern": cell_neg_pattern,
        "select_by": "column",
        "multi_elements": True,
        "element_filter": b6n_filter,
        "location_threshold": {"table": 0.3, "paragraph": 0.3},
        "exclude_cell_patterns": exclude_cell_patterns,
    },
    {
        "path": ["B68", "Content"],
        "model": "regex_pattern",
        "patterns": [RegPredictor(deny_regs, 0, is_positive=False)],
        "passed_result": 0,
    },
    {
        "path": ["B69", "Content"],
        "model": "regex_pattern",
        "element_filter": b69_filter,
        "enum_dictator": B69EnumDictator,
        "patterns": [RegPredictor(deny_regs, 0, is_positive=False)],
        "passed_result": 0,
        "location_threshold": {"paragraph": 0.4, "table": 1},
    },
    {
        "path": ["B70", "Amount"],
        "model": ["partial_text", "table_tuple_select"],
    },
    {
        "path": ["B70", "Period"],
        "model": ["partial_text", "table_tuple_select"],
    },
    {
        "path": ["B71", "Content"],
        "model": "regex_pattern",
        "patterns": [RegPredictor(deny_regs, 0, is_positive=False)],
        "passed_result": 0,
    },
    {
        "path": ["B72", "Content"],
        "model": "regex_pattern",
        "patterns": [RegPredictor(deny_regs, 0, is_positive=False)],
        "passed_result": 0,
    },
]


def update_threshold(predictors_config, mold):
    """
    阈值优先级：
        1. predictor["location_threshold"]
        2. data/mold_element_threshold/15.yml
    """
    rule_threshold_path = Path(project_root) / f"data/mold_element_threshold/{mold}.yml"
    rule_threshold = defaultdict(dict)
    if rule_threshold_path.exists():
        with open(rule_threshold_path, encoding="utf-8") as file_obj:
            rule_threshold.update(yaml.safe_load(file_obj))
    for predictor in predictors_config:
        rule_name = "-".join(predictor["path"])
        stat_threshold = {
            k: min(rule_threshold[rule_name].get("bottom", 0.1), predictor.get("location_threshold", {}).get(k, 1), 0.1)
            if predictor.get("location_threshold", {}).get(k) != 1
            else 1
            for k in ("table", "paragraph")
        }
        predictor["location_threshold"] = stat_threshold
        predictor["top_threshold"] = rule_threshold[rule_name].get("top", 0.9)
    return predictors_config


PREDICTORS = update_threshold(predictors, mold="15")


class HKEXListingRulePredictor(AIAnswerPredictor):
    """港交所上市规则预测"""

    predictors = PREDICTORS

    def __init__(self, *args, **kwargs):
        if special_rules := kwargs.get("special_rules"):
            self.predictors = [i for i in PREDICTORS if i["path"][0] in special_rules]
        kwargs["predictors"] = self.predictors
        kwargs["default_model"] = "empty"
        super().__init__(*args, **kwargs)
