import http

from marshmallow import fields, validate
from webargs.tornadoparser import use_kwargs

from remarkable.base_handler import Au<PERSON>, BaseHandler
from remarkable.common.util import release_lock_keys
from remarkable.models.new_file import NewFile
from remarkable.models.new_question import NewQuestion
from remarkable.plugins.debug import plugin
from remarkable.plugins.fileapi.common import make_pdfinsight
from remarkable.worker.tasks import (
    inspect_rule,
    preset_answer,
)


@plugin.route(r"/files/(\d+)/run")
class RunTaskHandler(BaseHandler):
    task_schema = {
        "task": fields.Str(required=True, validate=validate.OneOf(["preset", "inspect", "pdfinsight"])),
        "mid": fields.Int(required=True),
        "force_ocr": fields.Int(validate=validate.OneOf([0, 1]), load_default=0),
    }

    @Auth(["browse"])
    @use_kwargs(task_schema, location="query")
    async def get(self, *args, **kwargs):
        """重跑预测/合规任务"""
        fid = int(args[0])
        mid = kwargs["mid"]
        force_ocr = kwargs["force_ocr"]
        release_lock_keys()
        if kwargs["task"] == "preset":
            # 会顺路跑合规
            preset_answer.delay(fid, mid)
        if kwargs["task"] == "inspect":
            # 只跑合规
            if question := await NewQuestion.find_by_fid_mid(mid, fid):
                inspect_rule.delay(question.id)
            else:
                self.error("NewQuestion not exists", http.HTTPStatus.BAD_REQUEST)
        if kwargs["task"] == "pdfinsight":
            file = await NewFile.find_by_id(fid)
            make_pdfinsight(file.pdf_path(), fid, run_predict=True, force_ocr=force_ocr)
        return self.data(f"file: {fid}, task queued!")
