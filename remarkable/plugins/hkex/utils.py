# -*- coding: utf-8 -*-
import datetime
import json
import logging
import re
from collections import defaultdict
from dataclasses import dataclass
from functools import lru_cache
from pathlib import Path
from typing import Any, Iterator, Self, Tuple, Union

import webargs

from remarkable.common.common import (
    JURA21_A_COLS,
    J<PERSON><PERSON><PERSON>_COLS,
    RULE_D_DISCLOSURE_VALUE_MAP,
    SPECIAL_SUB_RULES,
    THIRD_WORDS,
    V1_RULE_PATTERN,
    EmptyAnswer,
    HKEXSchema,
)
from remarkable.common.constants import E6Scenario, MarketType, PolicyEsgRules, Scope3Categories
from remarkable.common.pattern import PatternCollection
from remarkable.common.util import (
    box_to_outline,
    clean_txt,
    is_valid_report_year_for_qr,
    match_ext,
)
from remarkable.models.mold import special_mold
from remarkable.services.chatgpt import OpenAIClient

logger = logging.getLogger(__name__)

DISCLOSURE_COMPLIANCE_VALUE_MAP = {
    "Compliance": "Compliance/Without Red Flag",
    "Potential Non-Compliance": "Non-Compliance/With Red Flag",
}
P_ZERO = re.compile(r"[oO〇](\d+)")
P_STRIP_PAGE_NO = re.compile(r"^(\d+\s+?)\d+|\d+(\s+?\d+)$")
R_REPORT_YEAR = r"(?:FY|AR|.*?Reports?|年度[報报]告|年[報报])"
R_YEAR_RANGE = r"\s*?\d{2,4}[\-/—－–]\d{2,4}"
P_REPORT_YEAR_RANGE = re.compile(R_REPORT_YEAR + R_YEAR_RANGE, re.I)
P_REPORT_YEARS = PatternCollection(
    [
        R_REPORT_YEAR + r"(?P<year>)([0-2]{2})?(?:\d{2}[/—－–]+)(?:\d{2})?(\d{2})",  # 19/20, 2019/20, 2019/2020
        r"(?P<year>)([0-2]{2})?(?:\d{2}[/—－–]+)(?:\d{2})?(\d{2})" + R_REPORT_YEAR,
        r"\D?(?P<year>2[0-1]\d{2})" + R_REPORT_YEAR,  # 2020FY, 2020AR, 2020AnnualReport
        R_REPORT_YEAR + r"(?P<year>2[0-1]\d{2})(?![/—－–])",
    ],
    re.I,
)
P_ADD_SEP = re.compile(r"(\d{2})[\-/—－–]+(\d{2})")


def get_rule_version(schema_label, rule_name=None):
    if schema_label in JURA21_A_COLS:
        return 1
    if schema_label in JURA21_COLS:
        return 2
    if rule_name and schema_label in SPECIAL_SUB_RULES and V1_RULE_PATTERN.nexts(rule_name):
        return 1
    if rule_name and not V1_RULE_PATTERN.nexts(rule_name or ""):
        return 2
    return 1 if V1_RULE_PATTERN.nexts(schema_label) else 2


class RuleAnswer:
    compliance_name_pattern = re.compile(r"\s*(.+)\s*")

    def __init__(self, data, root_schema):
        self.root_schema = root_schema
        self._data = data

    @property
    def schema(self):
        return self._data["schema"]

    @property
    def schema_label(self):
        label = self.schema["data"]["label"]
        version = get_rule_version(label, rule_name=self.rule_name)
        if version == 1:
            if label in SPECIAL_SUB_RULES:
                return self.root_schema.name_rule_map.get(self.rule_name, None)
            return self.root_schema.name_rule_map.get(label, None)
        return label

    @property
    def data(self):
        return self._data

    @property
    def is_virtual(self):
        return self.root_schema.is_virtual(self.schema["data"]["label"])

    @property
    def compliance_answer_key(self):
        """Unique key to distinguish compliance answer, like A1, A11.1, B1.1 and B12.1"""
        if self.root_schema.jura_version == 1:
            label = self.schema["data"]["label"]
            if self.root_schema.is_virtual(label):
                return label

            if self.is_compliance:
                label = label.strip("()")
                if self.level == 2:
                    return label
                else:
                    names = self.path_names[1:]
                    items = self.root_schema.root_rules_map.get(names[0])
                    index = items.index(label) // 2 + 1
                    return "{}.{}".format(names[0], index)
            else:
                if self.level == 2:
                    return label
                else:
                    names = self.path_names[1:]
                    if self.is_special_content:
                        return names[0]

                    items = self.root_schema.root_rules_map.get(names[0])
                    index = items.index(label) // 2 + 1
                    return "{}.{}".format(names[0], index)

        return self.path_names[1]

    @property
    def disclosure_answer_key(self):
        """Unique key to distinguish disclosure answer, like A1, A11.1, B1.1 and B12.1"""
        if self.root_schema.jura_version == 1:
            return self.compliance_answer_key

        label = self.schema["data"]["label"]
        if self.root_schema.is_virtual(label):
            return label

        names = self.path_names[1:]
        items = self.root_schema.root_rules_map.get(names[0])
        index = items.index(label) + 1
        return "{}.{}".format(names[0], index)

    @property
    def key_path(self):
        return json.loads(self.key)

    @property
    def path_names(self):
        return [i.split(":")[0] for i in self.key_path]

    @property
    def rule_name(self):
        rule_pre = self.key_path[1].split(":")[0]
        key_index = 1
        if rule_pre.startswith("A"):
            key_index = -1
        rule_name = self.key_path[key_index].split(":")[0]
        if rule_name in SPECIAL_SUB_RULES:  # HARD CODE
            return rule_pre
        return rule_name

    @property
    def version(self):
        return get_rule_version(self.schema_label, self.rule_name)

    @property
    def key(self):
        return self.data["key"]

    @property
    def crude_key(self):
        # rule_name = self.root_schema.name_rule_map[self.schema_label]
        # items = self.root_schema.rule2crude_key(rule_name).split('-')
        return self.schema_label

    @property
    def compliance_key(self):
        return json.dumps(self.compliance_key_path)

    @property
    def compliance_key_path(self):
        compliance_path = self.key_path
        if self.version == 1:
            key = "({}):{}".format(self.schema_label, self.key_path[-1].split(":")[-1])
            compliance_path = self.key_path[:-1] + [key]
        return compliance_path

    @property
    def level(self):
        return len(self.key_path)

    @property
    def sort_score(self):
        base_score = 2**self.level
        sub_base_score = 10**-6
        sub_score = sub_base_score * (self.last_key_index + 1)
        return base_score + sub_score

    @property
    def last_key_index(self):
        items = self.key_path[-1].split(":")
        return int(items[-1])

    @property
    def is_compliance(self):
        if self.root_schema.jura_version != 1:
            raise AttributeError("Only valid when jura_version is 1")

        label = self.schema["data"].get("label")
        return label.startswith("(") and label.endswith(")")

    @property
    def is_special_content(self):
        """True for disclosure answer of A34-A41"""
        if self.root_schema.jura_version != 1:
            raise AttributeError("Only valid when jura_version is 1")

        label = self.schema["data"].get("label")
        return len(self.key_path) == 3 and label == SPECIAL_SUB_RULES[0]

    @property
    def marked_text(self):
        value = self.data.get("value", None)
        if value:
            return value
        data = self.data.get("data")
        if not data:
            return ""
        return data[0].get("value", "")

    def is_sub_item(self, item: Self):
        if self.level > item.level:
            return False
        if self.level == item.level:
            return self.key_path == item.key_path
        return self.key_path == item.key_path[slice(self.level)]

    def is_sub_path(self, item: Self):
        if len(self.path_names) > len(item.path_names):
            return False
        return self.path_names == item.path_names[: len(self.path_names)]

    def update_answer_data(self, data):
        data["key"] = json.dumps(data["key"])
        self._data = data

    def simple_text(self, clear=True, enum=False):
        if not self.data or not (self.data.get("data") or self.data.get("value") or self.data.get("text")):
            return ""

        texts = []
        if enum and self.data.get("value"):
            texts.append(self.data.get("value"))
        else:
            if hasattr(self.data, "text") and self.data.get("text"):
                texts.append(self.data.get("text"))
            else:
                for data in self.data.get("data"):
                    if isinstance(data, str):
                        _text = data
                    else:
                        _text = data["text"] if data.get("text") else "|".join([box["text"] for box in data["boxes"]])
                    texts.append(_text)
        if clear:
            texts = [
                re.sub(r"[\n\r]", "", each) if isinstance(each, str) else [re.sub(r"[\n\r]", "", x) for x in each]
                for each in texts
            ]
        return texts[0] if texts else ""


class QuestionAnswer:
    def __init__(self, items, schema):
        # Which case can cause the `value` key missing?
        for item in items:
            value = item.get("value", "") or ""
            if isinstance(value, list):
                value = value[0]
            item["value"] = value
        self.items = [RuleAnswer(i, schema) for i in items]
        self.mold_schema = schema

    @staticmethod
    def from_items(items, schema):
        instance = QuestionAnswer([], schema)
        instance.items = items
        return instance

    @property
    def data(self):
        return [item.data for item in self.items]

    def get_scheme_path_answer_mapper(self, is_compliance=False):
        answers = defaultdict(dict)
        for item in self.items:
            if item.is_virtual:
                continue

            compliance_key = item.compliance_answer_key
            is_v1_compliance = self.mold_schema.jura_version == 1 and item.is_compliance
            if is_v1_compliance or is_compliance:
                answers[compliance_key]["compliance"] = item
            else:
                if "disclosure" not in answers[compliance_key]:
                    answers[compliance_key]["disclosure"] = defaultdict(list)

                disclosure_data = answers[compliance_key].get("disclosure")
                disclosure_key = compliance_key
                if not is_compliance and self.mold_schema.jura_version == 2:
                    disclosure_key = item.disclosure_answer_key
                disclosure_data[disclosure_key].append(item)

        return answers

    def find_item_by_name(self, name):
        try:
            return next(item for item in self.items if item.schema_label == name)
        except StopIteration:
            logger.error(f"Cannot find {name} in {len(self.items)=}")
            raise RuntimeError(f"Find item answer of rule({name})") from StopIteration

    def find_item_by_path(self, key_path):
        try:
            return next(item for item in self.items if item.key_path == key_path)
        except StopIteration:
            logger.error(f"Cannot find {key_path} in {len(self.items)}")
            raise RuntimeError(f"Find item answer of rule({key_path})") from StopIteration

    def collect_rule_answer(self, item, label):
        # TODO, should collect sub items depends on item type
        sub_items = [i for i in self.items if item.is_sub_item(i)]
        sub_items.sort(key=lambda m: m.sort_score)
        return [sub_item.data for sub_item in sub_items if not label or sub_item.schema_label == label]

    def collect_all_rule_answer(self, item, label):
        """
        根据item的path_names判断来过滤items，确保提取指定规则的所有分组答案
        """
        sub_items = [itm for itm in self.items if item.is_sub_path(itm)]
        sub_items.sort(key=lambda m: m.sort_score)
        return [sub_item.data for sub_item in sub_items if not label or sub_item.schema_label == label]

    def update_items_answer(self, answer_items):
        for i in answer_items:
            self.update_item_answer(i)

    def update_item_answer(self, data):
        item = self.find_item_by_path(data["key"])
        item.update_answer_data(data)
        return item


class MoldAnswer:
    def __init__(self, answer):
        answer = EmptyAnswer(answer["schema"]).merge(answer)
        self._answer = answer
        answer_data = answer["userAnswer"]
        self.schema = HKEXSchema(answer["schema"])
        rule_result = answer.get("rule_result", {})
        self._answer_data = answer_data
        self._rule_result = rule_result
        answer_items = self._answer_data.get("items", [])
        result_items = self._rule_result.get("items", [])
        self.user_answer = QuestionAnswer(answer_items, self.schema)
        # TODO, only correct when self.scheme.jura_version == 2
        self.compliance_answer = QuestionAnswer(result_items, self.schema)

    def get_rule_answer(self, name, multi=False):
        return self._build_rule_answer(name, multi=multi)

    def update_related_answer(self, name, form, multi=False):
        version = get_rule_version(name)
        answer_items = form["answer"]
        rule_result = form["rule_result"]
        self.user_answer.update_items_answer(answer_items)
        self.update_item_result(version, rule_result)

        return self._build_rule_answer(name, multi=multi)

    def get_basic_item(self, name):
        version = get_rule_version(name)
        if version == 2:
            item = self.find_compliance_item_by_name(name, version)
        else:
            converted_name = self.schema.rule_name_map[name]
            item = self.user_answer.find_item_by_name(converted_name)
        return item

    def _build_rule_answer(self, name, multi=False):
        version = get_rule_version(name)
        compliance_item_name = name if version == 2 else f"({name})"
        compliance_item = self.find_compliance_item_by_name(compliance_item_name, version)
        answer = self.collect_all_answers(name) if multi else self.collect_answer(name)
        return {"answer": answer, "rule_result": compliance_item.data}

    def find_compliance_item_by_name(self, name, version):
        if version == 2:
            return self.compliance_answer.find_item_by_name(name)
        converted_name = self.schema.rule_name_map[name]
        return self.user_answer.find_item_by_name(converted_name)

    def collect_answer(self, name, label=None):
        """
        仅获取一组答案
        """
        item = self.get_basic_item(name)
        return self.user_answer.collect_rule_answer(item, label)

    def collect_all_answers(self, name, label=None):
        """
        获取规则下的多组答案
        """
        item = self.get_basic_item(name)
        return self.user_answer.collect_all_rule_answer(item, label)

    def update_item_result(self, version, result_data):
        if version == 1:
            self.user_answer.update_item_answer(result_data)
        else:
            self.compliance_answer.update_item_answer(result_data)

    def get_compliance_items(self, version=1):
        if version == 1:
            return [i for i in self.user_answer.items if i.is_compliance]
        return self.compliance_answer.items

    def get_compliance_answer(self):
        version = self.schema.jura_version
        if version == 1:
            items = self.get_compliance_items(version=version)
            return QuestionAnswer.from_items(items, self.schema)

        return self.compliance_answer

    def dumps_answer(self):
        return {
            "userAnswer": {"items": self.user_answer.data, "version": "2.2"},
            "rule_result": {"items": self.compliance_answer.data, "version": "2.2"},
        }

    def merge_rule_answers(self):
        answers = self.user_answer.get_scheme_path_answer_mapper()
        if self.schema.jura_version == 1:
            return answers
        else:
            compliance_answers = self.compliance_answer.get_scheme_path_answer_mapper(True)
            for compliance_key in compliance_answers:
                answers[compliance_key]["compliance"] = compliance_answers[compliance_key]["compliance"]

        return answers


def validate_date(date_string: str, check_format="%Y"):
    if isinstance(date_string, int):
        date_string = str(date_string)
    try:
        datetime.datetime.strptime(date_string.strip(), check_format)
    except ValueError:
        return False
    return True


def validate_report_year(report_year):
    if report_year == "ALL":
        return True
    if is_valid_report_year_for_qr(report_year):
        return True
    return False


def convert_company_name(name):
    # 去掉空格, 末尾附注等无用文字
    name = re.sub(r"-\s*?[A-Z](\s?[Ss]hares?)?$", "", str(name).strip())
    return clean_txt(name)


@dataclass
class StockItem:
    stock_code: str
    company_name_en: str
    listing_date: int

    def __post_init__(self):
        self.stock_code = f"{int(self.stock_code)}"
        if not self.stock_code.isdigit():
            raise ValueError(f"Invalid stock code: {self.stock_code}")
        self.stock_code = standard_stock(self.stock_code)
        self.company_name_en = convert_company_name(self.company_name_en)


def excel_row_iter(path, sheet_index=0, skip_rows=0, values_only=False) -> Iterator[Tuple[Any]]:
    """Iterate rows in excel file(both xls and xlsx)"""
    date_mode = None
    if match_ext(path, "xlsx"):
        import openpyxl

        with open(path, "rb") as fp:
            work_book = openpyxl.load_workbook(fp)
        work_sheet = work_book.worksheets[sheet_index]
        rows = work_sheet.iter_rows(min_row=skip_rows + 1, values_only=values_only)
    else:
        import xlrd

        work_book = xlrd.open_workbook(path)
        work_sheet = work_book.sheet_by_index(sheet_index)
        date_mode = work_book.datemode
        rows = work_sheet.get_rows()
        for _ in range(skip_rows):
            next(rows)

    for row in rows:
        if date_mode is not None:
            # legacy code, for xlrd only
            for cell in row:
                cell.date_mode = date_mode
            if values_only:
                row = tuple(cell.value for cell in row)
        yield row


def get_market_type(name: str | None = None, stock_code: str | None = None) -> MarketType:
    assert name or stock_code, "name or stock_code must be provided"
    if not stock_code:
        return MarketType.GEM if name.lower().startswith("e_newlisting") else MarketType.MAIN_BOARD
    return MarketType.GEM if standard_stock(stock_code).startswith("08") else MarketType.MAIN_BOARD


def standard_stock(code_str):
    return code_str.strip().rjust(5, "0")


class HeaderColIndex:
    _hits = 0
    listing_date_idx = 0
    stock_idx = 1
    name_idx = 2

    def parse_col_index(self, row):
        for idx, col in enumerate(cell.value for cell in row):
            col = clean_txt(str(col)).lower()
            if col.startswith("stock"):
                self.stock_idx = idx
                self._hits += 1
            elif col.startswith("company"):
                self.name_idx = idx
                self._hits += 1
            elif col.startswith("date of listing"):
                self.listing_date_idx = idx
                self._hits += 1

    @property
    def ready(self):
        return self._hits >= 2


def stock_iter(path: Union[str, Path]) -> Iterator[StockItem]:
    from xlrd import xldate_as_datetime

    if isinstance(path, str):
        path = Path(path)
    col_index = HeaderColIndex()
    for row in excel_row_iter(path):
        if not col_index.ready:
            col_index.parse_col_index(row)
            continue
        stock_code = row[col_index.stock_idx].value
        company_name = row[col_index.name_idx].value
        listing_date = row[col_index.listing_date_idx]
        ctype = getattr(listing_date, "ctype", None)
        if ctype:
            if ctype != 3:  # 3: xldate type
                continue
            listing_date = xldate_as_datetime(listing_date.value, listing_date.date_mode).timestamp()
        elif isinstance(listing_date.value, datetime.datetime):
            listing_date = listing_date.value.timestamp()
        elif isinstance(listing_date.value, str) and validate_date(listing_date.value, check_format="%Y/%m/%d"):
            listing_date = datetime.datetime.strptime(listing_date.value, "%Y/%m/%d").timestamp()
        else:
            continue
        try:
            yield StockItem(stock_code, company_name, listing_date)
        except ValueError:
            pass


@lru_cache()
def find_ar_report_year(text: str, *, use_llm=False) -> int | None:
    if use_llm:
        logger.info(f'Use LLM to find report year: "{text}"')
        next_year = datetime.datetime.now().year + 1
        try:
            possible_year = OpenAIClient().send_message(
                [
                    {
                        "role": "system",
                        "content": f"""你是一个专门用于从文件名或文本中提取年份的AI助手。给定一个字符串，你的任务是分析并返回其中最可能代表年度报告或财务年度的年份。
规则如下:

1. 优先考虑4位数的年份。
2. 对于跨年度的表示(如2020/21、2020-2021等)，返回结束年份。
3. 如果有多个年份，选择最新的一个。
4. 年份通常在2000-{next_year}之间。
5. 注意跨年度表示中可能的缩写形式，如：21代表2021。
6. 考虑各种分隔符，如/、-、_等。
7. 兼容中英文表示，如"年报"、"Annual Report"等。
8. 只返回4位数字年份，不需要其他解释。
9. 如果无法找到年份，返回`0`""",
                    },
                    {"role": "user", "content": "2020/21-Annual-Report.pdf"},
                    {"role": "assistant", "content": "2021"},
                    {"role": "user", "content": "Annual-Report-2020/21.pdf"},
                    {"role": "assistant", "content": "2021"},
                    {"role": "user", "content": "年報-19//2021.pdf"},
                    {"role": "assistant", "content": "2021"},
                    {"role": "user", "content": "2023-2024-ANNUAL-REPORT.pdf"},
                    {"role": "assistant", "content": "2024"},
                    {"role": "user", "content": "Today is another day.pdf"},
                    {"role": "assistant", "content": "0"},
                    {"role": "user", "content": text},
                ],
                options={"temperature": 0.0},
            )
            possible_year = int(possible_year)
            if possible_year < next_year:
                return possible_year
        except Exception as e:
            logger.warning(e)

    # [oO] -> 0
    text = P_ZERO.sub(r"0\1", text.strip())

    # 切掉左右可能的页码
    # "https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/3024#note_389324"
    if match := P_STRIP_PAGE_NO.search(text):
        text = text.replace(match.group(1) or match.group(2), "", 1)

    if match := P_REPORT_YEAR_RANGE.search(text):
        text = match.group()

    # '00016_SHK-PPT_2016-17-Annual-Report.pdf' -> '00016_SHK-PPT_2016/17-Annual-Report.pdf'
    text = P_ADD_SEP.sub(r"\1/\2", clean_txt(text, remove_blank=True))
    # '00016_SHK-PPT_2016/17-Annual-Report.pdf' -> '2016/17AnnualReport.pdf'
    text = "".join(text.split("_")[-1].split("-"))

    if match := P_REPORT_YEARS.nexts(text):
        if match.group("year"):
            year_str = match.group("year")
        else:
            year_str = "".join(s for s in match.groups() if s).rjust(3, "0").rjust(4, "2")
        return min(int(year_str), datetime.datetime.now().year)
    # logger.debug(f'Can not find report year in: "{text}"')
    return None


@dataclass
class AdjustmentInfo:
    rule: str
    rule_subtab: str
    adjustment: str
    box_change: dict


def gen_adjustment_info(
    rule: str, third_word: str, old_value: str | list, new_value: str | list, adjustment: str, is_box=False
):
    if not old_value:
        old_value = "None"
    if not new_value:
        new_value = "None"
    if old_value == new_value:
        return None
    return AdjustmentInfo(
        rule, third_word, adjustment, {"old_boxes": old_value, "current_boxes": new_value} if is_box else {}
    )


def gen_adjustment_for_history(rule, old_answer, current_answer, mid, multi_group=False) -> list[AdjustmentInfo]:
    """
    勾选或取消勾选人工披露值 Disclosure Location
    勾选/取消勾选合规值 Compliance Assessment
    人工标注或删除人工标注 Manual Tagged Information
    """

    def get_compliance_result(answer):
        ret = None
        if answer["rule_result"].get("manual"):
            ret = answer["rule_result"]["value"]
        if ret == "":
            ret = None
        if ret:
            ret = ret.title()
        if rule.startswith("Disclosure") and ret:
            ret = DISCLOSURE_COMPLIANCE_VALUE_MAP.get(ret, ret)
        if not rule.startswith("Disclosure") and ret:
            ret = ret.replace("Potential", "").strip()
        return ret

    ret = []
    third_words = THIRD_WORDS.get(rule, [rule])  # 默认返回【rule】 用于在保存历史数据时不填写rule_subtab
    for third_word in third_words:
        if multi_group:
            # 规则B1-B7或者带group_name的答案，需要按组对比 https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/5464
            ret.extend(get_adjustment_info_by_group(rule, third_word, old_answer, current_answer))
        elif rule in (PolicyEsgRules.E6.value, PolicyEsgRules.E8.value, PolicyEsgRules.E9.value):
            ret.extend(get_adjustment_info_esg689(rule, old_answer, current_answer))
        else:
            ret.extend(get_adjustment_info(rule, third_word, old_answer, current_answer, mid))

    # 合规与否是对于整个rule
    old_compliance_result = get_compliance_result(old_answer) or None
    current_compliance_result = get_compliance_result(current_answer) or None
    if adjustment := gen_adjustment_info(
        rule,
        "",
        old_compliance_result,
        current_compliance_result,
        f'Compliance Assessment from "<{old_compliance_result}>" to "<{current_compliance_result}>"',
    ):
        ret.append(adjustment)
    return ret


def get_disclosure_result(rule, answer, third_word):
    if third_word == rule:
        return [item["value"] for item in answer["answer"] if item.get("special_ui") and item["value"] != ""]
    return [
        item["value"]
        for item in answer["answer"]
        if item["schema"]["data"]["label"] == third_word and item.get("special_ui") and item["value"] != ""
    ]


def get_answer_data(rule, answer, third_word):
    if third_word == rule:
        return [item["data"] for item in answer["answer"] if item.get("special_ui")]
    return [
        item["data"]
        for item in answer["answer"]
        if item["schema"]["data"]["label"] == third_word and item.get("special_ui")
    ]


def get_adjustment_info_esg689(rule: str, old_answer: dict, current_answer: dict):
    def get_manual_answer(answer_dict: dict) -> dict:
        manual_answers = [item for item in answer_dict["answer"] if item.get("special_ui")]
        return manual_answers[0] if manual_answers else {}

    ret = []
    categories = (E6Scenario if rule == PolicyEsgRules.E6.value else Scope3Categories).abbreviations()
    if rule == PolicyEsgRules.E9.value:
        categories = [Scope3Categories.display_emission(phrase, "") for phrase in Scope3Categories.phrases()]
    manual_old = get_manual_answer(old_answer)
    manual_current = get_manual_answer(current_answer)

    category_old = {v["category"]: v for v in manual_old.get("meta", {}).get("categories", {})}
    category_new = {v["category"]: v for v in manual_current.get("meta", {}).get("categories", {})}
    for cat in categories:
        cat_old_disclosure_result = category_old.get(cat, {})
        cat_current_disclosure_result = category_new.get(cat, {})
        cat_old_answer_value = cat_old_disclosure_result.get("enum")
        cat_new_answer_value = cat_current_disclosure_result.get("enum")
        if adjustment := gen_adjustment_info(
            rule,
            cat,
            cat_old_answer_value,
            cat_new_answer_value,
            f'Classification from "<{cat_old_answer_value}>" to "<{cat_new_answer_value}>"',
        ):
            ret.append(adjustment)

        old_boxes, current_boxes = compare_label_answer(
            cat_old_disclosure_result.get("data"), cat_current_disclosure_result.get("data")
        )

        adjustment_desc = (
            "Manually delete the tagged information"
            if (old_boxes and not current_boxes)
            else "Manual Tagged Information"
        )
        if adjustment := gen_adjustment_info(rule, cat, old_boxes, current_boxes, f"{adjustment_desc}", is_box=True):
            ret.append(adjustment)

    old_disclosure_result = get_disclosure_result(rule, old_answer, rule)
    current_disclosure_result = get_disclosure_result(rule, current_answer, rule)
    old_answer_value = old_disclosure_result[0] if old_disclosure_result else "None"
    new_answer_value = current_disclosure_result[0] if current_disclosure_result else "None"
    if adjustment := gen_adjustment_info(
        rule,
        "",
        old_answer_value.title(),
        new_answer_value.title(),
        f'Disclosure Location from "<{old_answer_value.title()}>" to "<{new_answer_value.title()}>"',
    ):
        ret.append(adjustment)
    return ret


def get_adjustment_info(rule, third_word, old_answer, current_answer, mid) -> list[AdjustmentInfo]:
    ret = []
    old_disclosure_result = get_disclosure_result(rule, old_answer, third_word)
    current_disclosure_result = get_disclosure_result(rule, current_answer, third_word)
    old_answer_value = old_disclosure_result[0] if old_disclosure_result else "None"
    new_answer_value = current_disclosure_result[0] if current_disclosure_result else "None"
    if mid == special_mold.v3d_id:
        old_answer_value = RULE_D_DISCLOSURE_VALUE_MAP.get(old_answer_value, old_answer_value)
        new_answer_value = RULE_D_DISCLOSURE_VALUE_MAP.get(new_answer_value, new_answer_value)
    old_answer_value = old_answer_value.title()
    new_answer_value = new_answer_value.title()
    if adjustment := gen_adjustment_info(
        rule,
        third_word,
        old_answer_value,
        new_answer_value,
        f'Disclosure Location from "<{old_answer_value}>" to "<{new_answer_value}>"',
    ):
        ret.append(adjustment)

    if old_label_result := get_answer_data(rule, old_answer, third_word):
        old_label_result = old_label_result[0]
    if current_label_result := get_answer_data(rule, current_answer, third_word):
        current_label_result = current_label_result[0]
    old_boxes, current_boxes = compare_label_answer(old_label_result, current_label_result)
    if adjustment := gen_adjustment_info(
        rule, third_word, old_boxes, current_boxes, "Manual Tagged Information", is_box=True
    ):
        ret.append(adjustment)
    return ret


def get_disclosure_result_by_group(rule, answer, third_word):
    result = {}
    for item in answer["answer"]:
        if not (item.get("manual") and third_word in {rule, item["schema"]["data"]["label"]}):
            continue
        if value := item["value"]:
            result[item["group_name"]] = value.title()
        else:
            result[item["group_name"]] = "None"
    return result


def get_answer_data_by_group(rule, answer, third_word):
    return {
        item["group_name"]: item["data"]
        for item in answer["answer"]
        if item.get("manual") and third_word in {rule, item["schema"]["data"]["label"]}
    }


def get_adjustment_info_by_group(rule, third_word, old_answer, current_answer):
    ret = []
    old_disclosure_result = get_disclosure_result_by_group(rule, old_answer, third_word)
    current_disclosure_result = get_disclosure_result_by_group(rule, current_answer, third_word)
    adjust_text = 'Event Type "<{group_name}>": Disclosure Location from "<{old_value}>" to "<{new_value}>"'
    for group_name, old_value in old_disclosure_result.items():
        new_value = current_disclosure_result.get(group_name) or "None"
        if adjustment := gen_adjustment_info(
            rule,
            third_word,
            old_value,
            new_value,
            adjust_text.format(group_name=group_name, old_value=old_value, new_value=new_value),
        ):
            ret.append(adjustment)
    for group_name, new_value in current_disclosure_result.items():
        if group_name in old_disclosure_result:
            continue
        if adjustment := gen_adjustment_info(
            rule,
            third_word,
            "None",
            new_value,
            adjust_text.format(group_name=group_name, old_value="None", new_value=new_value),
        ):
            ret.append(adjustment)

    old_label_result = get_answer_data_by_group(rule, old_answer, third_word)
    current_label_result = get_answer_data_by_group(rule, current_answer, third_word)
    adjust_text = 'Event Type "<{group_name}>": Manual Tagged Information'
    for group_name, old_data in old_label_result.items():
        old_boxes, current_boxes = compare_label_answer(old_data, current_label_result.get(group_name))
        if adjustment := gen_adjustment_info(
            rule, third_word, old_boxes, current_boxes, adjust_text.format(group_name=group_name)
        ):
            ret.append(adjustment)
    for group_name, new_data in current_label_result.items():
        if group_name in old_label_result:
            continue
        old_boxes, current_boxes = compare_label_answer([], new_data)
        if adjustment := gen_adjustment_info(
            rule, third_word, old_boxes, current_boxes, adjust_text.format(group_name=group_name)
        ):
            ret.append(adjustment)
    return ret


def compare_label_answer(old_answer, current_answer):
    # 返回新旧答案是否有差异
    if not old_answer and not current_answer:
        return [], []
    old_boxes = []
    if old_answer:
        for item in old_answer:
            old_boxes.extend(item["boxes"])
    current_boxes = []
    if current_answer:
        for item in current_answer:
            current_boxes.extend(item["boxes"])
    old_boxes.sort(key=lambda x: (x["page"], x["box"]["box_top"]))
    current_boxes.sort(key=lambda x: (x["page"], x["box"]["box_top"]))
    return old_boxes, current_boxes


def gen_adjustment_for_ratio(old_answer, current_currency_info, current_formulas, current_brief_summary):
    # old_brief_summary = old_answer['brief_summary']
    # if old_brief_summary != brief_summary: # Save the data of the Red Flag
    #     res.append('Red Flag Assessment')
    old_label_answer = old_answer.get("manual", [])
    old_manual_currency_info = old_answer.get("manual_currency_info", {})
    all_labels = parse_label_answer(old_label_answer, current_formulas, current_brief_summary)
    if format_currency_unit(old_manual_currency_info.get("unit", {})) != format_currency_unit(
        current_currency_info.get("unit", {})
    ):
        all_labels.append("Unit")
    if format_currency_unit(old_manual_currency_info.get("currency", {})) != format_currency_unit(
        current_currency_info.get("currency", {})
    ):
        all_labels.append("Currency")
    all_labels.sort()
    return "&".join(f"[{item}]" for item in all_labels)


def parse_label_answer(old_label_answer, current_label_answer, current_brief_summary):
    all_labels = set()
    old_answer_map = defaultdict(list)
    current_answer_map = defaultdict(list)
    ret = []
    for item in old_label_answer:
        for section in item["sections"]:
            all_labels.add(section["label"])
            old_answer_map[section["label"]].append(section)

    for item in current_label_answer:
        for section in item["sections"]:
            all_labels.add(section["label"])
            current_answer_map[section["label"]].append(section)

    for label in all_labels:
        if label == "Material Impairment":
            continue
        old_answer = old_answer_map.get(label)
        current_answer = current_answer_map.get(label)
        old_items_sets = gen_item_info(old_answer)
        current_items_sets = gen_item_info(current_answer)
        if old_items_sets != current_items_sets:
            ret.append(label)

    if "Material Impairment" in all_labels:
        old_answer = old_answer_map.get("Material Impairment")
        current_answer = current_answer_map.get("Material Impairment")
        format_old_answer = get_material_from_answer(old_answer)
        format_current_answer = get_material_from_answer(current_answer)
        current_from_material = get_material_from_summary(current_brief_summary)
        if not format_current_answer and current_from_material:
            format_current_answer = current_from_material
        if format_old_answer != format_current_answer:
            ret.append("Material Impairment")
    return ret


def gen_item_info(answers):
    ret = set()
    if not answers:
        return ret
    for answer in answers:
        for item in answer["items"]:
            account = item["account"]
            value = item["value"]
            account_outline = box_to_outline(account["box"]) if account["box"] else ()
            value_outline = box_to_outline(value["box"]) if value["box"] else ()
            ret.add((account_outline, account["text"], value_outline, value["text"]))
    return ret


def format_currency_unit(item):
    outline = box_to_outline(item["box"]) if item.get("box") else ()
    if outline == (-1, -1, -1, -1):
        outline = ()
    value = item["text"] if item.get("text") else ""
    return outline, value


def get_material_from_summary(brief_summary):
    reason = brief_summary.get("reason")
    if not reason:
        return ""
    if reason.get("attr", "") != "Material Impairment":
        return ""
    manual_item = reason.get("manual")
    if not manual_item:
        return ""
    return format_currency_unit(manual_item)


def get_material_from_answer(answers):
    ret = ""
    if not answers:
        return ret
    for answer in answers:
        for item in answer["items"]:
            account = item["account"]
            account_outline = box_to_outline(account["box"]) if account["box"] else ()
            if account_outline == (-1, -1, -1, -1):
                account_outline = ()
            return account_outline, account["text"]
    return ret


delist_schema = {"delist": webargs.fields.Bool(load_default=False)}
