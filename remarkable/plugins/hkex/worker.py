import asyncio
import datetime
import logging
import os
import random
import re
import shutil
import subprocess
import tempfile
import time
from copy import deepcopy
from dataclasses import asdict
from functools import reduce, wraps
from pathlib import Path
from typing import List, Union

from pdfparser.pdftools.count_page_num import get_page_num
from pdfparser.pdftools.pdf_doc import PDFDoc
from peewee import EXCLUDED, fn
from utensils.hash import md5sum_for_file

from remarkable import logger
from remarkable.base_handler import peewee_transaction_wrapper
from remarkable.common.common import elt_text_list, filter_str
from remarkable.common.constants import (
    DocType,
    HKEXFileParseStatus,
    JuraVersion,
    PDFFlag,
    PDFParseStatus,
)
from remarkable.common.hkex_util import set_meta_info
from remarkable.common.multiprocess import is_coroutine_func
from remarkable.common.storage import hkex_files_storage, tmp_dir_storage
from remarkable.common.util import clean_txt, mm_notify, stream_download
from remarkable.config import get_config
from remarkable.db import pw_db
from remarkable.models.file_project import NewFileProject
from remarkable.models.file_tree import NewFileTree
from remarkable.models.hkex_company import HKEXCompany
from remarkable.models.hkex_file import HKEXFile
from remarkable.models.hkex_file_meta import HKEXFileMeta
from remarkable.models.mold import special_mold
from remarkable.models.new_file import NewFile
from remarkable.models.new_mold import NewMold
from remarkable.models.new_question import NewQuestion
from remarkable.models.share_purchase_report import HKEXSharePurchaseReport
from remarkable.pdfinsight.reader import PdfinsightReader
from remarkable.plugins.hkex.utils import (
    StockItem,
    standard_stock,
    stock_iter,
)
from remarkable.services.esg import update_activated_state
from remarkable.services.file_meta import split_texts_by_paragraph
from remarkable.services.stock import get_stock_meta, srrpt2df


class SafetyCall:
    """保证解析遇到任何异常均可以正常返回预设值"""

    __ret_map = {
        "_parse_mlr": {
            "preset": True,
            "items": ["share_options"],
            "share_options": {
                "headers": [
                    "Particulars_of_share_option_scheme",
                    "EGM_approval_date",
                    "Class_of_shares_issuable",
                    "Movement-during-the-month_Granted",
                    "Movement-during-the-month_Exercised",
                    "Movement-during-the-month_Cancelled",
                    "Movement-during-the-month_Lapsed",
                    "No_of_new_shares_of_issuer_issued_during_the_month_pursuant_thereto",
                    "No_of_new_shares_of_issuer_which_may_be_issued_pursuant_thereto_as_at_close_of_the_month",
                    "Ordinary_shares",
                    "Preference_shares",
                    "Other_class",
                    "Total_funds",
                ],
                "index": [2],  # 通常在第三页
                "records": [],
            },
        },
        "_parse_wac": {
            "preset": True,
            "items": ["share_options", "warrants", "convertibles"],
            "share_options": {
                "headers": [
                    "Particulars_of_share_option_scheme",
                    "EGM_approval_date",
                    "Class_of_shares_issuable",
                    "Movement-during-the-month_Granted",
                    "Movement-during-the-month_Exercised",
                    "Movement-during-the-month_Cancelled",
                    "Movement-during-the-month_Lapsed",
                    "No_of_new_shares_of_issuer_issued_during_the_month_pursuant_thereto",
                    "No_of_new_shares_of_issuer_which_may_be_issued_pursuant_thereto_as_at_close_of_the_month",
                    "Ordinary_shares",
                    "Preference_shares",
                    "Other_class",
                    "Total_funds",
                ],
                "index": [2],  # 通常在第三页
                "records": [],
            },
            "warrants": {
                "headers": [
                    "Description_of_warrants",
                    "Currency_of_nominal_value",
                    "Nominal_value_at_close_of_preceding_month",
                    "Exercised_during_the_month",
                    "Nominal_value_at_close_of_the_month",
                    "No_of_new_shares_of_issuer_issued_during_the_month_pursuant_thereto",
                    "No_of_new_shares_of_issuer_which_may_be_issued_pursuant_thereto_as_at_close_of_the_month",
                    "Date_of_expiry",
                    "Stock_code",
                    "Class_of_shares_issuable",
                    "Subscription_price",
                    "EGM_approval_date",
                    "Ordinary_shares",
                    "Preference_shares",
                    "Other_class",
                ],
                "index": [3],  # 通常在第四页
                "records": [],
            },
            "convertibles": {
                "headers": [
                    "Class_and_description",
                    "Currency_of_amount_outstanding",
                    "Amount_at_close_of_preceding_month",
                    "Converted_during_the_month",
                    "Amount_at_close_of_the_month",
                    "No_of_new_shares_of_issuer_issued_during_the_month_pursuant_thereto",
                    "No_of_new_shares_of_issuer_which_may_be_issued_pursuant_thereto_as_at_close_of_the_month",
                    "Stock_code",
                    "Class_of_shares_issuable",
                    "Subscription_price",
                    "EGM_approval_date",
                    "Ordinary_shares",
                    "Preference_shares",
                    "Other_class",
                ],
                "index": [4],  # 通常在第五页
                "records": [],
            },
        },
        "_parse_fac_agr": {
            "preset": True,
            "items": ["A29"],
            "A29": {"headers": ["date", "years", "shares", "context"], "index": [0], "records": []},
        },
        "_parse_mct": {
            "preset": True,
            "items": ["A22"],
            "A22": {"headers": ["date", "parties", "currency", "amount", "context"], "index": [0], "records": []},
        },
        "_parse_shortfall": {
            "preset": True,
            "items": ["shortfall"],
            "shortfall": {"headers": ["currency", "amount", "context"], "index": [0], "records": []},
        },
        "_remark": {
            "preset": True,
            "items": [
                "Reasons",
                "Class of equity",
                "Number issued",
                "Issued price",
                "Net price",
                "Name of allotters",
                "Market price",
                "Process of subscription",
            ],
            "Reasons": {"headers": ["_content"], "index": [-1], "records": []},
            "Class of equity": {"headers": ["_content"], "index": [-1], "records": []},
            "Number issued": {"headers": ["_content"], "index": [-1], "records": []},
            "Issued price": {"headers": ["_content"], "index": [-1], "records": []},
            "Net price": {"headers": ["_content"], "index": [-1], "records": []},
            "Name of allotters": {"headers": ["_content"], "index": [-1], "records": []},
            "Market price": {"headers": ["_content"], "index": [-1], "records": []},
            "Process of subscription": {"headers": ["_content"], "index": [-1], "records": []},
        },
    }

    def __init__(self, func_name=None):
        self.default_ret = deepcopy(self.__ret_map.get(func_name, {"preset": True, "items": []}))

    def __call__(self, func):
        @wraps(func)
        def __inner(*args, **kwargs):
            self.default_ret = deepcopy(self.__ret_map.get(func.__name__, {"preset": True, "items": []}))
            _file = args[0]
            try:
                ret = func(*args, **kwargs)
            except Exception as e:
                logging.exception("File parse error: %s, %s\n%s", _file["id"], _file["url"], e)
                self.default_ret["reason"] = "{}".format(e)
                return self.default_ret
            else:
                logging.info("File parsed successfully: %s", _file["id"])
                ret["preset"] = False
                return ret

        return __inner


async def doc2pdf(file):
    pdf_hash = file["pdf_hash"] if file["pdf_hash"] else ""
    pdf_path = hkex_files_storage.mount(os.path.join(pdf_hash[:2], pdf_hash[2:]))
    if os.path.isfile(pdf_path) and pdf_hash == md5sum_for_file(pdf_path):
        return pdf_hash

    src_fp = hkex_files_storage.mount(file["path"])
    in_fp = tmp_dir_storage.mount("{}{}".format(int(time.time()), random.randint(1000, 9999)))
    shutil.copy(src_fp, in_fp)

    out_fp = in_fp + ".pdf"
    command = 'mono {} "{}"'.format(get_config("web.pdf_converter"), in_fp)
    with subprocess.Popen(command, stdout=subprocess.PIPE, stderr=subprocess.PIPE, shell=True) as cmd_process:
        out, err = cmd_process.communicate()
    os.remove(in_fp)

    if os.path.isfile(out_fp) and os.path.getsize(
        out_fp
    ):  # 有些文档转换可能会有warning警告, 但实际是转换成功的, 需要换种判断方式
        logging.info(out)
        pdf_hash = md5sum_for_file(out_fp)
        des_fp = hkex_files_storage.mount(os.path.join(pdf_hash[:2], pdf_hash[2:]))
        if not os.path.exists(os.path.dirname(des_fp)):
            os.makedirs(os.path.dirname(des_fp))
        if not os.path.exists(des_fp):
            shutil.move(out_fp, des_fp)
        else:
            os.remove(out_fp)
        file["pdf_hash"] = pdf_hash
        await pw_db.execute(HKEXFile.update(pdf_hash=pdf_hash).where(HKEXFile.id == file["id"]))
    else:
        logging.error(err)
        await pw_db.execute(
            HKEXFile.update(finished_utc=HKEXFileParseStatus.CON_FAILED.value).where(HKEXFile.id == file["id"])
        )
    return pdf_hash


async def fetch_newly_stocks(url: str) -> list[StockItem]:
    with tempfile.TemporaryDirectory(dir=get_config("web.tmp_dir")) as tmp_dir:
        path = os.path.join(tmp_dir, os.path.basename(url))
        await stream_download(url, path)
        return list(stock_iter(path))


async def parse_listing_doc(file):
    new_stocks, valid_stocks = await asyncio.gather(
        fetch_newly_stocks(file["url"]),
        get_stock_meta(),
    )
    rows = [asdict(i) for i in new_stocks if i.stock_code in valid_stocks]  # noqa
    on_conflict = {
        "conflict_target": [HKEXCompany.stock_code],
        "update": {
            HKEXCompany.company_name_en: EXCLUDED.company_name_en,
            HKEXCompany.listing_date: EXCLUDED.listing_date,
        },
    }
    await HKEXCompany.bulk_insert(rows, on_conflict=on_conflict)
    logger.info(f"Inserted {len(rows)} records.")
    await pw_db.execute(
        HKEXFile.update(headline="Listing Docs", finished_utc=int(time.time())).where(HKEXFile.id == file["id"])
    )


async def parse_xls(file):
    """
    年报当年内是否有股票回购, 对应规则A3&A11.3
    """
    new_header = [
        "company",
        "stock_code",
        "type",
        "trading_date",
        "number_of_securities_purchased",
        "share_price",
        "lowest_price",
        "total_paid",
        "purch_method",
        "annual_trans",
        "issued_shares_percent",
    ]
    expected_len = len(new_header)
    xls_path = hkex_files_storage.mount(file["path"])
    df = srrpt2df(xls_path)

    async with pw_db.atomic():
        if df.empty:
            logger.info("No records found in this file: %s", file["id"])
        else:
            # 删除可能存在的解析记录
            await pw_db.execute(HKEXSharePurchaseReport.delete().where(HKEXSharePurchaseReport.file_id == file["id"]))

            if df.shape[1] > expected_len:
                logger.warning(f"Columns mismatch, we will only keep the first {len(new_header)} columns.")
                df = df.iloc[:, :expected_len]
            df.columns = new_header
            df["file_id"] = file["id"]
            await pw_db.execute(HKEXSharePurchaseReport.insert_many(df.to_dict(orient="records")).on_conflict_ignore())
            logger.info(f"File {file['id']} parsed successfully, {len(df)} records inserted.")
        await pw_db.execute(
            HKEXFile.update(headline="Share Purchase Report", finished_utc=int(time.time())).where(
                HKEXFile.id == file["id"]
            )
        )


async def parse_pdf(hkex_file: dict):
    """解析外部PDF, 不同规则解析方法都不同"""
    rule_map = {
        "A28.2": _parse_shortfall,
        "A22": _parse_mct,
        "A23": _return_nothing,
        "A24.2": _return_nothing,
        "A25": _return_nothing,
        "A27": _return_nothing,
        "A28.1": _return_nothing,
        "A29.1": _parse_fac_agr,
        "A29.2": _parse_fac_agr,
        "A30": _parse_mlr,
        "A31": _parse_mlr,
        "A32": _parse_mlr,
        "A33": _parse_mlr,
        "A11.1": _parse_wac,
        "A11.2": _parse_wac,
        "ar": remark,  # annual report
        "ESG": remark,  # ESG report
        "B1-10": remark,
        # Jura3.0 业绩公告
        "Final": remark,
        "Interim": remark,
        "Q1": remark,
        "Q3": remark,
        # Ratio3 外部文档
        "ProfitWarning": parse_profit_warning,
        "B63-72": _return_nothing,
        "AGM": remark,
        "POLL": remark,
        "NDDR": remark,
        "MR": remark,
    }

    func = rule_map[hkex_file["type"]]
    if is_coroutine_func(func):
        res = await func(hkex_file)
    else:
        res = func(hkex_file)
    await pw_db.execute(
        HKEXFile.update(
            result=res, finished_utc=int(time.time()) if "finished_utc" not in res else res["finished_utc"]
        ).where(HKEXFile.id == hkex_file["id"])
    )


def strip_date_time(txt):
    """清除日期空格及左右括号"""
    for _ch in ("(", ")", " "):
        txt = txt.replace(_ch, "")
    return txt


def get_index_by_pattern(doc, pattern, skip=None):
    """
    获取正则匹配到的文字框索引号(或页码)及该文本框内容
    :param doc: PDFDoc.pages[page_no]['texts'] obj
    :param pattern: re pattern
    :param skip: int
    :return: index int, txt(去除首尾空字符及数字间千分位逗号后的结果) str
    """
    if isinstance(doc, PDFDoc):
        for page, content in doc.pages.items():
            for box in content["texts"]:
                txt = re.sub(r"(\d),", r"\1", box["text"].strip())
                match = re.search(pattern, txt)
                if match:
                    return page, txt
    elif isinstance(doc, list):
        for index, txt in enumerate(doc):
            if skip and index <= skip:
                continue
            txt = re.sub(r"(\d),", r"\1", txt.strip())
            match = re.search(pattern, txt)
            if match:
                return index, txt
    else:
        raise TypeError("Unsupported type: %s" % doc)
    raise Exception("Nothing matched, please check your regexp rule: %s" % pattern)


def get_tbox(doc):
    """
    将PDFParser解析文本框, 页码, 定位坐标(box)放入列表
    {
        'index': 0,
        'text': 'Hong Kong Exchanges and Clearing Limited and The Stock Exchange of',
        'page': 0,
        'box': (56.5827, 56.8675, 434.5396, 67.4862)
    }
    """
    height = 800  # 暂定页面高度
    texts = []
    for page in doc.pages:
        for text_box in doc.pages[page]["texts"]:
            box = text_box["box"]
            texts.append(
                {
                    "index": text_box["index"],
                    "text": filter_str(text_box["text"]),
                    "page": page,
                    "box": (box[0], box[1] + page * height, box[2], box[3] + page * height),
                }
            )
    return texts


def put_txt_in_list(doc, page_range=None, clean_func=None):
    """
    将PDFParser解析文字内容提取按顺序放入列表
    为降低提取金额正则表达式复杂度, 删除了源文数字间的千分位逗号
    同时将字符串中多余空格替换为一个
    """
    texts = []
    if isinstance(doc, PdfinsightReader):
        for index in doc.data["_index"]:
            _, ele = doc.find_element_by_index(index)
            if not ele:
                continue
            if ele.get("class") != "TABLE":
                texts.append(" ".join(elt_text_list(ele)))
    else:
        page_range = sorted(page_range) if page_range else None
        for page in doc.pages:
            if page_range and page not in list(range(page_range[0], page_range[-1] + 1)):
                # 提供页码时, 只处理指定页面的文本
                continue
            for txt in doc.pages[page]["texts"]:
                txt = (clean_func or filter_str)(txt["text"])
                texts.append(txt)
    return texts


def get_txt_between_keywords(texts, txt1, txt2):
    """取得两关键字之间的所有内容(只取首个匹配结果, 不包括关键字本身)"""
    start, end = None, 0
    for index, txt in enumerate(texts):
        if start is not None and end > start:
            break
        if re.search(txt1, txt):
            start = index
        if re.search(txt2, txt):
            end = index
    else:
        return ""
    return " ".join(texts[start:end])


def parse_opo_values(texts, letter):
    """
    取得指定页面右下角
        Total [ABC]. (Ordinary shares)
        (Preference shares)
        (Other class)
    三个条目的值
    """
    # 定位 Total [ABC]. 位置
    index, txt = get_index_by_pattern(texts, r"^Total\s?{}\s?\.".format(letter))

    # 取 Ordinary shares
    # 实际文档单词任意字母间均有可能出现空格
    # 退一步匹配, 以防 Total [ABC]. 与 xxx shares 未在一个 text box 的情况
    index, txt = get_index_by_pattern(texts, r"\(\s?O\s?r\s?d\s?i\s?n\s?a\s?r\s?y", index - 1)
    if txt.endswith("s") or txt.endswith(")"):  # 括号可能会被覆盖掉
        ordinary_shares = texts[index + 1]
    else:  # 后项值黏连, 按空格拆分取最后项
        ordinary_shares = txt.split(" ")[-1].strip()

    # 取 Preference shares
    index, txt = get_index_by_pattern(texts, r"\(\s?P\s?r\s?e\s?f\s?e\s?r\s?e\s?n\s?c\s?e", index)
    if txt.endswith("s") or txt.endswith(")"):
        preference_shares = texts[index + 1]
    else:
        preference_shares = txt.split(" ")[-1].strip()

    # 取 Other class
    index, txt = get_index_by_pattern(texts, r"\(\s?O\s?t\s?h\s?e\s?r", index)
    if txt.endswith("s") or txt.endswith(")"):
        other_class = texts[index + 1]
    else:
        other_class = txt.split(" ")[-1].strip()
    return ordinary_shares, preference_shares, other_class


@SafetyCall()
def _parse_shortfall(_file):
    """从目标文档中抽取业绩承诺差额, 目前正则可以精确匹配如下字段情形:
    1. Shortfall =  HK$3916084
    2. Hence, the shortfall amount was HK$3916084. Pursuant to the Agreement,...
    """
    res = SafetyCall("_parse_shortfall").default_ret
    pdf_path = hkex_files_storage.mount(_file["path"])
    doc = PDFDoc(pdf_path)
    shortfall_p = re.compile(
        r"""
        \b[Ss]hortfall\s?=?\s?  # 匹配shortfall关键字
        (?:(?!\bcapped\b)\D)*  # capped意为封顶, 跳过不匹配
        (?P<currency>HK\$|US\$|RMB)  # 匹配货币单位
        (?P<amount>\d+)\.?\b  # 匹配金额, 目前未见有小数位, 故暂不考虑
        """,
        re.X,
    )
    txt = " ".join(put_txt_in_list(doc))
    match = shortfall_p.search(txt)
    if match:
        res["shortfall"]["records"] = [
            {"currency": match.group("currency"), "amount": match.group("amount"), "context": match.group()}
        ]

    return res


@SafetyCall()
def _parse_mct(_file):
    """Major / Connected Transaction 主要/关连交易"""
    unit_map = {
        re.compile(r"Thousands?", re.I): 10**3,
        re.compile(r"millions?", re.I): 10**6,
        re.compile(r"billions?", re.I): 10**9,
        re.compile(r"trillions?", re.I): 10**12,
    }
    res = SafetyCall("_parse_mct").default_ret
    mct_p = re.compile(
        r"""
        Considerations?\D*?  # 匹配关键字Consideration
        (?P<currency>HK\$|US\$|RMB|\$|USD|HKD)\s?  # TODO: 可能会有更多的货币匹配情况
        (?P<amount>\d+\.?\d+).*?  # 金额(千分位逗号已提前去掉)
        (?P<unit>\w+)  # 单位, 可能有thousand, million, billion等
    """,
        re.X | re.I,
    )
    excludes = (
        "DELAY",
        "COMPLETION",
        "CLARIFICATION",
        "TERMINATION",
        "EXTENSION OF LONG STOP DATE",
        "CAPITAL INJECTION",
    )
    for keyword in excludes:
        if keyword in _file["name"].upper():
            logging.warning("No need to parse, file id = %s", _file["id"])
            return {"finished_utc": HKEXFileParseStatus.EXCLUDE.value}  # 需要排除的文档

    doc = PDFDoc(hkex_files_storage.mount(_file["path"]))
    if len(doc.pages) < 5:  # 页码过少, 通常认为是update, delay, cancel等补充公告, 不作处理
        return {"finished_utc": HKEXFileParseStatus.EXCLUDE.value}

    p_str = [i["text"] for i in split_texts_by_paragraph(get_tbox(doc))]

    # 交易金额
    for text in p_str:
        match = mct_p.search(text)
        if match:
            context = text
            break
    else:
        # 找不到交易金额就不再继续
        raise Exception("Keyword not found: Consideration")

    # 找单位
    for pattern, value in unit_map.items():
        if pattern.search(match.group("unit")):
            unit = value
            break
    else:
        unit = 1

    def _get_txt(tbox, re_p):
        """取关键字所在段落余下部分内容或者下一段内容"""
        idx, txt = get_index_by_pattern(tbox, re_p)
        txt = re.split(re_p, txt)[-1].strip()
        return txt if txt else tbox[idx + 1]

    # 交易日期
    date = _get_txt(p_str, r"^Date\s?:?")

    # 提取Parties信息, 可能在本段有, 也可能在下两段
    index, txt = get_index_by_pattern(p_str, r"^Parties?\s?:?")
    parties = re.split(r"^Parties?\s?:?", txt)[-1].strip()
    if parties:  # 本段有内容, 多取一段
        parties += " {}".format(p_str[index + 1])
    else:  # 本段无内容, 取下两段
        parties += "{} {}".format(p_str[index + 1], p_str[index + 2])

    res["A22"]["records"] = [
        {
            "date": date,
            "parties": parties,
            "currency": match.group("currency"),
            "amount": "{}".format(float(match.group("amount")) * unit),
            "context": context,
        }
    ]
    return res


@SafetyCall()
def _parse_fac_agr(_file):
    """股权质押的数量，类别，以及质押的债项款额等"""
    res = SafetyCall("_parse_fac_agr").default_ret
    word2num = {
        "one": "1",
        "two": "2",
        "three": "3",
        "four": "4",
        "five": "5",
        "six": "6",
        "seven": "7",
        "eight": "8",
        "nine": "9",
    }
    years_p = re.compile(r"\b[Tt]erm\s+of\s+(?P<years>\d+|\w+)\s+year[s]?\b")
    shares_p = re.compile(r"\b(?P<shares>\d+)\sshares\s\b")
    doc = PDFDoc(hkex_files_storage.mount(_file["path"]))
    texts = put_txt_in_list(doc)
    # 公告发行日期
    date = get_txt_between_keywords(texts, "Date", "Parties")
    date = re.sub(r"[;,.:]", "", date).strip()  # 删除日期中可能存在的特殊标点
    # 期限/到期日
    exp_context = get_txt_between_keywords(texts, "Parties", "SPECIFIC")
    exp_match = years_p.search(exp_context)
    if exp_match:
        years = exp_match.group("years")
        if not years.isdigit():
            years = word2num.get(years)
    else:
        years = None
    # 质押的股票数量
    shares_str = get_txt_between_keywords(texts, "CONTROLLING SHAREHOLDERS", "DEFINITIONS")
    match = shares_p.search(shares_str)
    if all((date, years, match)):
        res["A29"]["records"] = [
            {
                "date": date,
                "years": years,
                "shares": match.group("shares"),
                "context": "{}\n{}".format(exp_context, shares_str),
            }
        ]  # 段落文本

    return res


def _share_option(preset, doc):
    """定位解析 Share Options 表格
    Share Options(under Share Option Schemes of the Issuer)"""
    ret = deepcopy(preset)

    # 定位表格起始页码
    start, txt = get_index_by_pattern(doc, r"Details of Movements in Issued Share Capital$")
    end, txt = get_index_by_pattern(doc, r"^Total\s?A\.")

    # 记录表格页码
    ret["index"] = [start] if start == end else list(range(start, end + 1))
    # 提取指定页全部文字框
    texts = put_txt_in_list(doc, (start, end))

    # 从 Total A. 位置起取 ordinary_shares, preference_shares, other_class
    summary = {}
    summary.update(dict(zip(ret["headers"][9:12], parse_opo_values(texts, "A"))))

    # 取 Total funds, 一般以 Total founds 开头, 然后会折行, 会有括号包含一些内容, 下一个元素即所需值
    index, txt = get_index_by_pattern(texts, r"^[Tt]otal\sfunds\s?", end)
    index, txt = get_index_by_pattern(texts, r"\(", index)
    summary["Total_funds"] = texts[index + 1]

    # 确定表格行数
    index, txt = get_index_by_pattern(texts, r"^\d+\s?\.")
    start = int(txt.split(".")[0].strip())
    index, txt = get_index_by_pattern(texts[::-1], r"^\d+\s?\.")
    end = int(txt.split(".")[0].strip())

    # 解析 Share Options 表格
    for row in range(start, end + 1):
        share_option = {}
        start, txt = get_index_by_pattern(texts, r"^{}\s?\.".format(row))
        # 序号后无内容(可能为空, 也可能是 N/A 或 Nil, 即此行无记录, 换下一行
        if len(txt) <= 3 or "N/A" in txt or "Nil" in txt:
            continue
        # EGM approval date(dd/mm/yyyy) 获准时间
        try:
            index, date = get_index_by_pattern(texts, r"\(?\s*\d{1,2}\s*/\s*\d{1,2}\s*/\s*\d{4}\s*\)?", start)
            share_option["EGM_approval_date"] = strip_date_time(date)
        except Exception:
            share_option["EGM_approval_date"] = "N/A"

        # 这个时间可能没有, 选取 Date of grant: 或 Exercise period:做分隔
        # 也可能是个孤立的年份信息, 比如 June 2009
        if share_option["EGM_approval_date"] == "N/A":
            try:
                index, txt = get_index_by_pattern(texts, r"(?:[td]\s?:$|\w+\s?\d{4}$)", start)
            except Exception:
                continue  # 没有匹配到任何与日期相关字段, 换下一行

        # Particulars of share option scheme, 通常会换行, 利用序号和时间夹逼定位
        share_option["Particulars_of_share_option_scheme"] = " ".join(texts[start : index + 1])

        # 根据序号与(Note 1)定位每行起止位置
        end, txt = get_index_by_pattern(texts, r"^\(Note 1\)$", index)

        # 取3-9列, 可能会有两列内容连在一起的情况, 目前根据空格来分隔
        cols = ret["headers"][2:9]

        col_3_9 = []
        # 用关键字 shares/Share/share 分隔可能的第三列和第四到九列
        txt = "#".join(texts[index + 1 : end])
        try:
            col_3, col_4_9 = re.split(r"[sS]hares?", txt)
        except Exception:
            continue
        # 疑似第三列
        col_3_9.append(" ".join(col_3.split("#")).strip())
        # 补上剩余六列
        col_3_9.extend(reduce(lambda x, y: x + y.split(" "), [i for i in col_4_9.strip().split("#") if i], []))

        if len(col_3_9) == len(cols):
            share_option.update(dict(zip(cols, col_3_9)))
            share_option.update(summary)
            ret["records"].append(share_option)
    return ret


def _warrants(preset, doc):
    ret = deepcopy(preset)
    cols = ret["headers"]
    start, txt = get_index_by_pattern(doc, r"(?i)Warrants to Issue Shares of the Issuer which are to be Listed")
    end, txt = get_index_by_pattern(doc, r"^Total\s?B\.")
    ret["index"] = [start] if start == end else list(range(start, end + 1))  # 记录表格所在页码
    texts = put_txt_in_list(doc, (start, end))

    # 从 Total B. 位置起取 ordinary_shares, preference_shares, other_class
    summary = dict(zip(cols[12:15], parse_opo_values(texts, "B")))

    # 确定表格行数
    index, txt = get_index_by_pattern(texts, r"^\d+\s?\.")
    start = int(txt.split(".")[0].strip())
    index, txt = get_index_by_pattern(texts[::-1], r"^\d+\s?\.")
    end = int(txt.split(".")[0].strip())

    # 解析 1-12 列
    for row in range(start, end + 1):
        dic = {}
        start, txt = get_index_by_pattern(texts, r"^{}\.".format(row))
        end, txt = get_index_by_pattern(texts, r"^Stock code\s?\(", start)
        row = texts[start:end]
        row = [i for i in row if i not in ("/", ")", "(")]  # 去掉空的日期占位符( / )
        if len(row) < 3:  # 截取长度不够, 基本可以认为没有任何内容, 跳出循环不再继续提取
            break

        # 第八列日期类型固定, 先提取
        index, txt = get_index_by_pattern(row, r"\(?\s*\d{1,2}\s*/\s*\d{1,2}\s*/\s*\d{4}\s*\)?")
        row.pop(index)
        dic[cols[7]] = strip_date_time(txt)

        start, txt = get_index_by_pattern(
            row[::-1], r"^HK\$|^HKD|^US\$|^USD|^RMB"
        )  # 倒序取第二列index, 也许会和第三列混在一起
        sub_end, txt = get_index_by_pattern(
            row[::-1], r"\d+$"
        )  # 倒序取第七列index, 只要有记录绝对有值, 也许会和第六列混在一起
        # 2-7 列内容
        txt = row[-start - 1 : (-sub_end if sub_end else None)]  # sub_end 有可能是 0, 所以需要特殊处理下
        # 剩余项去掉货币类型单元后拼接即为第一列内容
        dic[cols[0]] = " ".join(
            [i for i in row if i not in txt and not re.search(r"^HK\$$|^HKD$|^US\$$|^USD$|^RMB$", i)]
        )[2:].strip()
        if len(txt) < 6:  # 列数不够, 必然有多列混在一起的情况, 需要分开, 这里用空格拆分
            txt = reduce(lambda lst, item: lst + item.split(" "), txt, [])
        if len(txt) > 6:  # 列数超限, 拆分/提取失败不再继续
            break
        dic.update(dict(zip(cols[1:7], txt)))

        # Stock_code
        start = end
        end, txt = get_index_by_pattern(texts, r"^Class of shares", start)
        dic[cols[8]] = " ".join(texts[start + 1 : end])

        # Class_of_shares_issuable
        start, txt = get_index_by_pattern(texts, r"issuable \(\s?Note 1\s?\)", end)
        end, txt = get_index_by_pattern(texts, r"Subscription price", start)
        dic[cols[9]] = " ".join(texts[start + 1 : end])

        # Subscription_price
        start = end
        end, txt = get_index_by_pattern(texts, r"EGM approval date", start)
        dic[cols[10]] = " ".join(texts[start + 1 : end])

        # EGM_approval_date
        start, txt = get_index_by_pattern(texts, r"^\(dd/mm/yyyy\)", end)
        end, txt = get_index_by_pattern(texts, r"\)$", start)
        txt = "".join(texts[start + 1 : end + 1])  # 取得可能是日期格式的内容
        if re.search(r"\(?\s*\d{1,2}\s*/\s*\d{1,2}\s*/\s*\d{4}\s*\)?", txt):
            dic[cols[11]] = strip_date_time(txt)
        else:
            dic[cols[11]] = "N/A"

        dic.update(summary)  # 添加右下角三行信息
        ret["records"].append(dic)
    return ret


def _covertibles(preset, doc):
    ret = deepcopy(preset)
    cols = ret["headers"]
    start, txt = get_index_by_pattern(doc, r"(?i)Convertibles\s?\(i\.e\.\s?.*Listed\s?\)$")
    end, txt = get_index_by_pattern(doc, r"^Total\s?C\.")
    ret["index"] = [start] if start == end else list(range(start, end + 1))  # 记录表格所在页码
    texts = put_txt_in_list(doc, (start, end))

    # 从 Total C. 位置起取 ordinary_shares, preference_shares, other_class
    summary = dict(zip(cols[12:15], parse_opo_values(texts, "C")))

    # 确定表格行数
    index, txt = get_index_by_pattern(texts, r"^\d+\s?\.")
    start = int(txt.split(".")[0].strip())
    index, txt = get_index_by_pattern(texts[::-1], r"^\d+\s?\.")
    end = int(txt.split(".")[0].strip())

    # 解析 1-11 列
    for row in range(start, end + 1):
        dic = {}
        start, txt = get_index_by_pattern(texts, r"^{}\.".format(row))
        end, txt = get_index_by_pattern(
            texts, r"Stock code\s?\(", start - 1
        )  # 可能与数字在一个文本框内, 故回退一步取内容
        row = texts[start:end]
        if len(row) < 3:  # 截取长度不够, 基本可以认为该行没有任何内容, 跳出循环不再继续提取
            break

        start, txt = get_index_by_pattern(
            row[::-1], r"^HK\$|^HKD|^US\$|^USD|^RMB"
        )  # 第二列index, 也许会和第三列混在一起
        sub_end, txt = get_index_by_pattern(
            row[::-1], r"\d+$"
        )  # 倒序取第七列index, 只要有记录绝对有值, 也许会和第六列混在一起

        # 2-7列内容
        txt = row[-start - 1 : (-sub_end if sub_end else None)]
        # 剩余项去掉货币类型单文本框后拼接即为第一列内容
        dic[cols[0]] = " ".join(
            [i for i in row if i not in txt and not re.search(r"^HK\$$|^HKD$|^US\$$|^USD$|^RMB$", i)]
        )[2:].strip()

        if len(txt) < 6:  # 列数不够, 必然有多列混在一起的情况, 需要分开, 这里用空格拆分
            txt = reduce(lambda lst, item: lst + item.split(" "), txt, [])
        if len(txt) > 6:  # 列数超限, 拆分/提取失败不再继续
            break
        dic.update(dict(zip(cols[1:7], txt)))

        # Stock_code
        start = end
        end, txt = get_index_by_pattern(texts, r"^Class of shares", start)
        dic[cols[7]] = " ".join(texts[start + 1 : end])

        # Class_of_shares_issuable
        start, txt = get_index_by_pattern(texts, r"issuable \(\s?Note 1\s?\)", end)
        end, txt = get_index_by_pattern(texts, r"Subscription price", start)

        # ...
        # Class of shares
        # issuable (Note 1)     Ordinary
        #                       HK$0.175
        # Subscription price    (subject to adjustment)
        # EGM approval date
        # ...
        txt = texts[start + 1 : end]
        if re.search(r"^HK\$|^HKD|^US\$|^USD|^RMB", txt[-1]):
            # 如果有货币出现, 则应该赋给下一列
            dic[cols[8]] = " ".join(txt[:-1])
            dic[cols[9]] = txt[-1] + " "
        else:
            dic[cols[8]] = " ".join(txt)
            dic[cols[9]] = ""

        # Subscription_price
        start = end
        end, txt = get_index_by_pattern(texts, r"EGM approval date", start)
        dic[cols[9]] += " ".join(texts[start + 1 : end])

        # EGM_approval_date
        start, txt = get_index_by_pattern(texts, r"^\(dd/mm/yyyy\)", end)
        end, txt = get_index_by_pattern(texts, r"\)$", start)
        txt = "".join(texts[start + 1 : end + 1])  # 取得可能是日期格式的内容
        if re.search(r"\(?\s*\d{1,2}\s*/\s*\d{1,2}\s*/\s*\d{4}\s*\)?", txt):
            dic[cols[10]] = strip_date_time(txt)
        else:
            dic[cols[10]] = "N/A"

        # 添加右下角三行信息
        dic.update(summary)
        ret["records"].append(dic)
    return ret


@SafetyCall()
def _parse_mlr(_file):
    """股份认购权的 目的、参与人、发行证券总数及百分比 及个人可获权益上限"""
    res = SafetyCall("_parse_mlr").default_ret
    doc = PDFDoc(hkex_files_storage.mount(_file["path"]))

    res["share_options"] = _share_option(res["share_options"], doc)

    return res


@SafetyCall()
def _parse_wac(_file):
    """
    当年是否发行了可转换债券，认证，期权, 包含三张表
    table1: Share Options(under Share Option Schemes of the Issuer)
    table2: Warrants to Issue Shares of the Issuer which are to be Listed
    table3: Convertibles (i.e. Convertible into Shares of the Issuer which are to be Listed)
    """
    blacklist_p = re.compile(r"(?i)cancell?(ed)?\s*?(and)?\s*?reiss?u(ed)?")
    name = _file["name"]
    if blacklist_p.search(name):
        logging.warning("No need to parse, file id = %s", _file["id"])
        return {"finished_utc": HKEXFileParseStatus.EXCLUDE.value}  # 需要排除的文档

    doc = PDFDoc(hkex_files_storage.mount(_file["path"]))
    res = SafetyCall("_parse_wac").default_ret

    # 处理 Share Options 表
    res["share_options"] = _share_option(res["share_options"], doc)

    # 处理 warrants 表
    res["warrants"] = _warrants(res["warrants"], doc)

    # 处理 Convertibles 表
    res["convertibles"] = _covertibles(res["convertibles"], doc)

    return res


async def create_project_by_name(prj_name, mold_obj=None):
    prj_obj = await pw_db.first(
        NewFileProject.select().where(NewFileProject.name == prj_name, NewFileProject.deleted_utc == 0)
    )
    if prj_obj:
        return prj_obj
    return await NewFileProject.create(
        name=prj_name,
        default_tags=[],
        default_mold=[mold_obj.id] if mold_obj else None,  # for create_tree
        preset_answer_model="",
    )


async def create_child_dir_by_name(
    ptree_id: int, pid: int, mold_id: Union[int, List[int]] | None, name: str
) -> NewFileTree:
    p_trees = await NewFileTree.list_by_tree(ptree_id)
    for tree in p_trees:
        if tree.name == name:
            break
    else:
        if mold_id is None:
            molds = []
        else:
            molds = [mold_id] if isinstance(mold_id, int) else mold_id
        tree = await NewFileTree.create(ptree_id=ptree_id, pid=pid, name=name, default_mold=molds)
    return tree


def copy_to_up_level(src):
    # copy到上层目录files/hkex_files/xx/xxx -> files/xx/xxx
    if not src:
        return
    src_path = hkex_files_storage.mount(src)
    if not os.path.exists(src_path):
        return
    dst_path = hkex_files_storage.mount(os.path.join(os.path.dirname(hkex_files_storage.root), src))
    dst_dir = os.path.dirname(dst_path)
    if not os.path.exists(dst_dir):
        os.makedirs(dst_dir)
    shutil.copy(src_path, dst_path)
    return


async def create_file(project, tree, mold_id, meta) -> NewFile:
    # 重命名年报, 向下与v1兼容
    # 例: 2018-04-16T16-42_06116_LA-CHAPELLE_ANNUAL-REPORT-2017.pdf
    if meta["type"] == "ar":
        mold_list = special_mold.ar_mids_with_esg_cg + [special_mold.jura21_helper_id]
    elif meta["type"] in DocType.qr_types():
        mold_list = special_mold.qr_mids
    elif meta["type"] == DocType.ESG.name:
        mold_list = [special_mold.esg_id, special_mold.policy_esg_id]
    elif meta["type"] == DocType.AGM.name:
        mold_list = [mold_id] if mold_id else []
        if get_config("feature.process_jura61_module"):
            mold_list = [special_mold.v6_agm_id, special_mold.v6_1_agm_id]
    elif meta["type"] == DocType.POLL.name:
        mold_list = [mold_id] if mold_id else []
        if get_config("feature.process_jura61_module"):
            mold_list = [special_mold.v6_poll_id, special_mold.v6_1_poll_id]
    else:
        mold_list = [mold_id] if mold_id else []
    pub_time = datetime.datetime.fromtimestamp(int(meta["release_time"]))
    pub_time_str = pub_time.strftime("%Y-%m-%dT%H-%M")
    pattern = re.compile(r"_|\s+")
    if meta["name"].startswith(pub_time_str):
        # 已经是格式化后的文件名就不再重复格式化
        name = meta["name"]
    else:
        name = "_".join(
            [
                pub_time_str,
                meta["stock_code"],
                pattern.sub("-", meta["company"]),
                pattern.sub("-", meta["name"]),
            ]  # date  # stock code  # company name
        )

    meta_info = {
        "tree_id": tree.id,
        "pid": project.id,
        "name": name,
        "hash": meta.get("pdf_hash") or meta["hash"],
        "tags": project.default_tags,
        "size": meta["size"],
        "mold": mold_id,
        "pdf": meta.get("pdf_hash") or meta["hash"],
        "pdf_flag": PDFFlag.CONVERTED.value,
        "pdf_parse_status": PDFParseStatus.COMPLETE.value,
        "mold_list": mold_list,
    }
    try:
        meta_info["page"] = get_page_num(
            Path(hkex_files_storage.mount(os.path.join(meta_info["pdf"][:2], meta_info["pdf"][2:]))).read_bytes()
        )
    except:  # noqa
        logger.exception(f"get page num failed for {meta_info['pdf']}")

    cond = (NewFile.deleted_utc == 0) & (NewFile.hash == meta["hash"])
    if mold_id:
        cond &= NewFile.mold_list.contains(mold_id)
    else:
        cond &= NewFile.mold_list.is_null()
    if exist_fid := await pw_db.scalar(
        NewFile.select(NewFile.id).where(cond).order_by(NewFile.pdfinsight, NewFile.pdf).limit(1)
    ):
        meta_info["id"] = exist_fid
    file = await NewFile.insert_or_update(**meta_info)
    # TODO: need a more disk efficient way to store file,
    # maybe use a symlink(but not support some file system, like smb)
    copy_to_up_level(file.pdf_path())
    return file


async def make_question_by_hkex_file(hkex_file_id: int) -> None:
    from remarkable.worker.tasks import _make_question

    hkex_file = await HKEXFile.find_by_id(hkex_file_id)
    if not hkex_file:
        logging.warning(f"hkex file record not found for fid: {hkex_file_id}")
        return
    if hkex_file.type not in {
        "ar",
        DocType.ESG.name,
        "B1-10",
        "Final",
        "Interim",
        "Q1",
        "Q3",
        "AGM",
        "POLL",
        "NDDR",
        "MR",
    }:
        return
    await _make_question(hkex_file.fid)


@peewee_transaction_wrapper
async def create_file_by_ver(hkex_file) -> NewFile:
    """
    :param hkex_file: dict
    :return: File object
    """
    match hkex_file["type"]:
        case "MR":
            project_name = mold_name = get_config("v6_1_mr.name")
        case "NDDR":
            project_name = mold_name = get_config("v6_nddr.name")
        case "ESG":
            project_name = mold_name = get_config("esg_molds.ESG.name")
        case "AGM":
            project_name = mold_name = get_config("v6_agm_id.name")
        case "POLL":
            project_name = mold_name = get_config("v6_poll_id.name")
        case _:
            if hkex_file["type"] == "ar":
                version = "v1"
            elif hkex_file["type"] in ("Final", "Interim", "Q1", "Q3"):
                version = "v3"
            else:
                version = "v2a"
            mold_name = JuraVersion.get(version, "schema")
            project_name = JuraVersion.get(version)

    mold = await NewMold.find_by_name(mold_name)
    assert mold, f"mold: {mold_name} not found"

    project = await create_project_by_name(project_name, mold)
    assert project, f"project: {project_name} creation failed"

    root_dir = await create_child_dir_by_name(project.rtree_id, project.id, mold.id, mold.name)
    stock_code = standard_stock(hkex_file["stock_code"])
    stock_dir = await create_child_dir_by_name(root_dir.id, root_dir.pid, root_dir.default_mold, stock_code)

    # # Additional Documents -> Additional Documents -> stock_code -> year
    # if version == 'v2a':
    #     year = datetime.datetime.fromtimestamp(meta['release_time']).year
    # else:
    #     year = get_crude_fy(datetime.datetime.fromtimestamp(meta['release_time'])).year

    new_file = await create_file(project, stock_dir, mold.id, hkex_file)
    return new_file


async def remark(hkex_file: dict):
    """
    1. B1-10: 需要标注的外部文档
    2. ar: 年报
    3. 'Final', 'Interim', 'Q1', 'Q3': 业绩公告
    4. ESG: ESG报告
    5. AGM: AGM报告
    6. POLL: POLL报告
    7. NDDR: Next Day Disclosure Returns
    8. MR: Monthly Returns
    """
    res = (
        {"preset": False, "items": []}
        if hkex_file["type"] in {"ar", "Final", "Interim", "Q1", "Q3", "ESG", "AGM", "POLL", "NDDR", "MR"}
        else SafetyCall("_remark").default_ret
    )
    if hkex_file["type"] == "AGM":
        await check_multi_agm_file(hkex_file)
    new_file: NewFile = await create_file_by_ver(hkex_file)
    await pw_db.execute(HKEXFile.update(fid=new_file.id).where(HKEXFile.id == hkex_file["id"]))
    await make_question_by_hkex_file(hkex_file["id"])

    if hkex_file["type"] not in {"B1-10", "NDDR"}:
        # parse ar or qr meta info
        questions = await NewQuestion.find_by_fid(new_file.id)
        for question in questions:
            if question.mold in set(
                [special_mold.v1_id, special_mold.v3r_id, special_mold.v6_agm_id, special_mold.v6_poll_id]
                + special_mold.esg_mids
                + [special_mold.v6_1_mr_id]
            ):
                await set_meta_info(new_file.name, new_file.id, question.id, hkex_file)
                break

    if hkex_file["type"] in [DocType.ESG.name, DocType.AR.name.lower()]:
        await update_activated_state(new_file.id)

    return res


async def check_multi_agm_file(hkex_file: dict):
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7135
    current_file_release_day = datetime.datetime.fromtimestamp(hkex_file["release_time"]).date()
    stock_code = standard_stock(hkex_file["stock_code"])
    # 查询HKEXFile表中该stock_code同一天发了两份AGM的情况
    query = HKEXFile.select().where(
        (HKEXFile.stock_code == stock_code)
        & (HKEXFile.type == "AGM")
        & (fn.date(fn.to_timestamp(HKEXFile.release_time)) == current_file_release_day),
    )
    agm_hkex_files = await pw_db.execute(query)
    if len(agm_hkex_files) > 1:
        file_ids = [f.id for f in agm_hkex_files]
        msg = f"Duplicate AGM files were found: {stock_code=}, release_day={current_file_release_day}, hkex_file_id {file_ids}"
        logger.info(msg)
        mm_notify(msg, tags=("duplicate_agm",))


def _return_nothing(_file):
    """对于A23 A24.2 A27 A28.1 B63-72这些规则文档, 只做存档备查, 不做解析"""
    return {"preset": False, "items": []}


@peewee_transaction_wrapper
async def parse_profit_warning(hkex_file):
    """Profit Warning"""
    project_name = "Profit Warning"  # HARDCODE
    project = await create_project_by_name(project_name)
    # Profit Warning -> Profit Warning
    root_dir = await create_child_dir_by_name(project.rtree_id, project.id, None, project_name)
    # Profit Warning -> Profit Warning -> stock_code
    stock_code = standard_stock(hkex_file["stock_code"])
    stock_dir = await create_child_dir_by_name(root_dir.id, root_dir.pid, root_dir.default_mold, stock_code)
    new_file = await create_file(project, stock_dir, None, hkex_file)
    await pw_db.execute(HKEXFile.update(fid=new_file.id).where(HKEXFile.id == hkex_file["id"]))

    report_year, doc_type = parse_quarter_year(hkex_files_storage.mount(hkex_file["path"]), hkex_file["name"])
    if not (report_year and doc_type):
        report_year = str(datetime.datetime.fromtimestamp(int(hkex_file["release_time"])).year)
        doc_type = DocType.PROFITWARNING

    await HKEXFileMeta.insert_or_update(
        conflict_target=[HKEXFileMeta.fid],
        fid=new_file.id,
        doc_type=doc_type,
        report_year=report_year,
        stock_code=stock_code,
    )
    return {"preset": False, "items": []}


def parse_quarter_year(pdf_path, file_name=""):
    pt_map = {
        "Three": DocType.Q1_PW,
        "Six": DocType.INTERIM_PW,
        "Nine": DocType.Q3_PW,
        "Year": DocType.FINAL_PW,
        "First": DocType.Q1_PW,
        "Interim": DocType.INTERIM_PW,
        "Third": DocType.Q3_PW,
        "Final": DocType.FINAL_PW,
    }
    # unaudited 2021 third quarterly report.pdf
    re_pattern = re.compile(r"(?P<year>[1-3]\d{3})\s*?(?P<quarter>First|Interim|Third|Final)\s*?quart", re.I)
    match = re_pattern.search(file_name)
    if match:
        return match.group("year"), pt_map[match.group("quarter").capitalize()]

    re_pattern = re.compile(
        r"(?:\s*For\s*the\s*)?(?P<quarter>Three|Six|Nine|year).*?end(?:ed)?.*?(?P<year>\d{4})", re.I
    )
    match = re_pattern.search(" ".join(put_txt_in_list(PDFDoc(pdf_path), clean_func=clean_txt)))
    if match:
        return match.group("year"), pt_map[match.group("quarter").capitalize()]
    return None, None
