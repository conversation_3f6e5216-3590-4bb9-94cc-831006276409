import collections
import hashlib
import http
import logging
import os
import re
import time
from datetime import datetime

import filetype
import webargs
from marshmallow import validate
from webargs.tornadoparser import use_kwargs

from remarkable.base_handler import <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>r, DbQueryH<PERSON>ler
from remarkable.common.constants import (
    INVALID_STOCK_CODE_MSG,
    HistoryAction,
    HKEXFileParseStatus,
)
from remarkable.common.decorator_util import pull_statistics_data
from remarkable.common.pattern import PatternCollection
from remarkable.common.storage import hkex_files_storage
from remarkable.common.util import get_pub_date_from_string
from remarkable.config import get_config
from remarkable.db import pw_db
from remarkable.models.hkex_company import HKEXCompany
from remarkable.models.hkex_file import HKEXFile
from remarkable.models.hkex_stock import HKEXCompaniesInfo, HKEXStock
from remarkable.models.new_history import HistoryMeta, NewHistory
from remarkable.models.rule_reference import RuleReference
from remarkable.plugins.hkex import plugin
from remarkable.plugins.hkex.hkex_meta_health import HKEXMetaHealth
from remarkable.plugins.hkex.op_excel import ExportExcel
from remarkable.plugins.hkex.utils import standard_stock, validate_date
from remarkable.services.statistics import get_rule_summary
from remarkable.worker.hkex_tasks import non_compliance_issuers_task

valid_hkex_sites = PatternCollection([re.compile(r"https?://.*hkexnews\.hk/.*\.pdf$", re.I)])


@plugin.route(r"/self_upload")
class SelfUploadHandler(BaseHandler):
    req_schema = {
        "stock_code": webargs.fields.Str(validate=validate.Regexp(r"\d+"), required=True),
        "financial_year": webargs.fields.Int(validate=validate_date, load_default=datetime.now().year),
        "published_time": webargs.fields.Str(validate=validate.Regexp(r"\d+"), load_default=""),
        "doc_type": webargs.fields.Str(load_default="ar", data_key="dt", validate=validate.OneOf(["ar", "esg"])),
        "report_type": webargs.fields.Str(load_default="", validate=validate.OneOf(["standalone", "in_ar"])),
        "overwrite": webargs.fields.Bool(load_default=False),
    }

    @Auth(["browse"])
    @use_kwargs(req_schema, location="form")
    @use_kwargs({"file": webargs.fields.Raw(required=True)}, location="files")
    async def post(self, stock_code, financial_year, published_time, doc_type, file, report_type, overwrite):
        if doc_type == "esg":
            doc_type = doc_type.upper()
            if not report_type:
                return self.error('"report_type" is required for ESG report.', status_code=http.HTTPStatus.BAD_REQUEST)
            if report_type == "in_ar":
                doc_type = "ar"
        elif doc_type != "ar":
            doc_type = doc_type.capitalize()
        stock_code = standard_stock(stock_code)
        if not HKEXCompany.is_valid_code_sync(stock_code):
            return self.error(INVALID_STOCK_CODE_MSG, status_code=http.HTTPStatus.NOT_FOUND)

        published_time = datetime.fromtimestamp(float(published_time))
        # 一些必要的参数检查
        self.check_time_by_filename(financial_year, published_time, file["filename"])

        md5sum = None
        if not overwrite:
            self.check_pdf_format(file["body"])
        elif re.search(rb"[a-f0-9]{32}", file["body"][:32]):
            md5sum = file["body"][:32].decode()
        if not md5sum:
            md5sum = hashlib.md5(file["body"]).hexdigest()

        meta = {
            "name": file["filename"],
            "stock_code": stock_code,
            "release_time": int(published_time.timestamp()),
            "type": doc_type,
            "company": await HKEXCompaniesInfo.fetch_company_name(stock_code),
            "hash": md5sum,
            "meta": {"financial_year": financial_year},
            "path": os.path.join(md5sum[:2], md5sum[2:]),
        }
        if not hkex_files_storage.exists(meta["path"]):
            hkex_files_storage.write_file(meta["path"], file["body"])
        meta["size"] = os.path.getsize(hkex_files_storage.mount(meta["path"]))
        meta["url"] = "{}://{}/api/v1/plugins/hkex/hash/{}".format(
            get_config("web.scheme", "http"), get_config("web.domain"), meta["hash"]
        )
        # 季报headline取文档名
        meta["headline"] = "Annual Report" if doc_type == "ar" else file["filename"].split(".")[0]
        meta["tu_hash"] = hashlib.md5(f"{doc_type}{meta['hash']}".encode()).hexdigest()
        file_record: HKEXFile = await HKEXFile.fetch_existing_record(stock_code, doc_type, published_time)

        if report_type == "in_ar":
            meta["headline"] = (
                "Financial Statements/ESG Information - "
                "[Annual Report / Environmental, Social and Governance Information/Report]"
            )
            if file_record and file_record.type == "ar" and meta["tu_hash"] != file_record.tu_hash:
                # 年报中的ESG报告，需要更新年报的headline，以便在独立ESG dashboard中显示。
                overwrite = True

        if file_record:
            logging.info(f"stock code: {file_record.stock_code}, publish: {file_record.published_at} already exists")
            if overwrite:
                same_file = await pw_db.first(
                    HKEXFile.select().filter(HKEXFile.type == doc_type, HKEXFile.hash == meta["hash"])
                )
                if same_file:
                    file_record = same_file
                file_record = HKEXFile(**{**file_record.to_dict(), **meta})
                await pw_db.update(file_record)
        else:
            same_file = await pw_db.first(
                HKEXFile.select().filter(HKEXFile.type == doc_type, HKEXFile.hash == meta["hash"])
            )
            if same_file:
                if overwrite:
                    file_record = same_file
                    # 重置为待解析状态
                    file_record.finished_utc = HKEXFileParseStatus.TODO.value
                    file_record.status = HKEXFileParseStatus.TODO.value
                    file_record = HKEXFile(**{**file_record.to_dict(), **meta})
                    await pw_db.update(file_record)
                else:
                    logging.warning(f"Create new hkex file failed and find same tu_hash: {meta['tu_hash']}")
                    return self.data({"need_overwrite": True, "md5sum": md5sum})
            else:
                # NOTE: https://mm.paodingai.com/cheftin/pl/jc4rdnjs1fddirt4zhfwn5oibo
                # 不应该携带id创建新的记录，否则会导致id重复
                meta.pop("id", None)
                file_record = await pw_db.create(HKEXFile, **meta)
        # note: 该方法会调用 prepare_parse_file
        need_overwrite, msg = await HKEXMetaHealth.repair(file_record, overwrite)
        if not need_overwrite and msg:
            raise CustomError(_(msg))

        await NewHistory.save_operation_history_by_user(
            None,
            self.current_user,
            HistoryAction.UPLOAD_AR if doc_type == "ar" else HistoryAction.UPLOAD_ESG,
            meta=HistoryMeta(
                year=str(financial_year),
                stock_code=stock_code,
            ),
        )
        return self.data({"need_overwrite": need_overwrite, "md5sum": md5sum})

    @staticmethod
    def check_pdf_format(body):
        type_ins = filetype.guess(body)
        if not type_ins or type_ins.mime != "application/pdf":
            raise CustomError(_("We currently only support PDF documents."))

    def check_time_by_filename(self, financial_year: int, published_time: datetime, filename: str):
        # 校验年报时间
        if financial_year > published_time.year:
            raise CustomError(_("FY date may not match the publish date."))
        # 校验文件名中的时间是否与published_time一致
        if publish := get_pub_date_from_string(filename):
            if published_time > publish:
                raise CustomError(_("Publish date may not match the AR file."))


@plugin.route(r"/rule_summary_v2")
class RuleSummaryV2Handler(DbQueryHandler):
    async def get(self, *args, **kwargs):
        rule = self.get_argument("rule", "B1")
        rule_reference = await RuleReference.find_by_rule(rule)
        meta = {
            "rule": rule,
            "main_alias": rule_reference.main_alias,
            "system_function": ["Annual Report Checking", "Result Analysis", "by rule"],
        }
        await NewHistory.save_operation_history(
            qid=None,
            uid=self.current_user.id,
            action=HistoryAction.LOAD_PAGE,
            user_name=self.current_user.name,
            meta=HistoryMeta(**meta),
        )
        new_list_time = self.get_argument("new_list", "2019-01-01")
        try:
            ret = await get_rule_summary(rule, new_list_time)
        except ValueError:
            return self.error("valid rule")

        is_export = self.get_query_argument("is_export", "false")
        if is_export == "true":
            getvalue = self.do_export(ret["body"])
            return await self.export(getvalue, "result_analysis_by_rule.xlsx")

        return self.data(ret)

    @staticmethod
    def parse_datas(multi_datas):
        """
        :param multi_datas:
        :return:
        """
        columns = ["available", "compliance", "non_compliance"]
        column_titles = {
            "available": "Available Annual Reports",
            "compliance": "Compliant Issuers",
            "non_compliance": "Non-Compliant Issuers",
        }
        row_titles = ["Total", "MB", "GEM", "NewlyListed"]
        ret = []
        for data in multi_datas:
            # data = {'year': 2019, 'data': {'Total': {}}}
            for col in columns:
                line = [data["year"], column_titles[col]]
                for title in row_titles:
                    line.append(data["data"][title][col])
                ret.append(line)
        return ret

    def do_export(self, data):
        blank = ""
        excel = ExportExcel()
        try:
            excel_data = self.parse_datas(data)
            excel.open()
            excel.create_ws_by_name("Result Analysis By Rule")
            head = [[blank, blank, "Total", "Main Board", "GEM", "Newly Listed"]]
            excel.write(head)
            excel.write(excel_data)
            excel.merge_col_by_index(min_row=2, col_index=1)
            excel.merge_row_by_index(row_index=1, min_col=1)
            excel.set_col_width("B", 25)
            excel.save()
        except Exception as exp:
            raise CustomError(_("Excel file export failed.")) from exp
        else:
            data_bytes = excel.getvalue
        finally:
            excel.close()
        return data_bytes


@plugin.route(r"/export_non_compliance_issuers")
class ExportNonComplianceHandler(DbQueryHandler):
    async def get(self, *args, **kwargs):
        rules = self.get_arguments("rules")
        report_years = self.get_arguments("report_years")
        new_list_time = self.get_argument("new_list", "2019-01-01")
        is_export = self.get_query_argument("is_export", "true")
        new_list_stamp = int(time.mktime(datetime.strptime(new_list_time, "%Y-%m-%d").timetuple()))
        new_list_stocks = await HKEXStock.get_recent_hkex_stocks(new_list_stamp)
        new_list_stocks = [new_list_stock.stock_code for new_list_stock in new_list_stocks]
        non_compliance_issuers_key = "non_compliance_issuers*"
        redis_data, flag = pull_statistics_data(non_compliance_issuers_key, int(time.time()))
        rows = []
        table_header = (
            "Stock Code",
            "Financial year(s)",
            "LR(s) Violated",
            "Financial Year-end",
            "AR Released Date",
            "Board Type",
            "Is Reviewed",
        )
        rows.append(table_header)
        if not redis_data or flag:
            non_compliance_issuers_task.delay(non_compliance_issuers_key)
        cols = [
            "stock_code",
            "report_years",
            "lrs_violated",
            "report_year_end",
            "ar_released_date",
            "board_type",
            "is_reviewed",
        ]
        NonComplianceData = collections.namedtuple("NonComplianceInfo", cols)
        redis_data.pop("data_time")
        redis_data.pop("create_flag")
        for _year, _rows in redis_data.items():
            if "ALL" in report_years or _year in report_years:
                for row in _rows:
                    non_compliance_data = NonComplianceData._make(row)
                    lrs = (
                        set(non_compliance_data.lrs_violated)
                        if "ALL" in rules
                        else set(non_compliance_data.lrs_violated).intersection(set(rules))
                    )
                    if not lrs:
                        continue
                    for rule in sorted(lrs):
                        new_non_compliance_data = non_compliance_data._replace(lrs_violated=rule)
                        if non_compliance_data.stock_code in new_list_stocks:
                            new_non_compliance_data = new_non_compliance_data._replace(board_type="Newly Listed")
                        rows.append(new_non_compliance_data)

        if is_export == "true":
            getvalue = self.do_export(rows)
            return await self.export(getvalue, "List of Non-Compliant Issuers.xlsx")
        return self.data(rows)

    @staticmethod
    def do_export(data):
        excel = ExportExcel()
        try:
            # excel_data = self.parse_datas(data)
            excel.open()
            excel.create_ws_by_name("List of Non-Compliant Issuers")
            excel.write(data)
            excel.save()
        except Exception as exp:
            raise CustomError(_("Excel file export failed.")) from exp
        else:
            data_bytes = excel.getvalue
        finally:
            excel.close()
        return data_bytes
