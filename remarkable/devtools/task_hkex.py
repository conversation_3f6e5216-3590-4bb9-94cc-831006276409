import collections
import csv
import datetime
import gzip
import json
import logging
import os
import re
import shutil
import sys
import time
from collections import defaultdict
from copy import deepcopy
from dataclasses import dataclass
from functools import partial
from pathlib import Path

import httpx
import peewee
import xlwt
from bs4 import BeautifulSoup
from invoke import task
from speedy.peewee_plus import orm
from speedy.peewee_plus.orm import fn
from tqdm import tqdm

from remarkable.common.common import EmptyAnswer, HKEXSchema
from remarkable.common.constants import (
    DocType,
    UserStatus,
)
from remarkable.common.decorator_util import pull_statistics_data, push_data
from remarkable.common.hkex_util import (
    set_meta_info,
)
from remarkable.common.multiprocess import run_by_batch
from remarkable.common.storage import localstorage
from remarkable.config import project_root
from remarkable.db import init_rdb, pw_db
from remarkable.devtools import InvokeWrapper
from remarkable.models.answer import Answer
from remarkable.models.file_tree import NewFileTree
from remarkable.models.hkex_file import HKEXFile
from remarkable.models.hkex_file_meta import HKEXFileMeta
from remarkable.models.hkex_stock import HKEXGroup
from remarkable.models.mold import special_mold
from remarkable.models.new_file import NewFile
from remarkable.models.new_mold import NewMold
from remarkable.models.new_question import NewQuestion
from remarkable.models.rule_reference import RuleReference
from remarkable.plugins.fileapi.worker import create_pdf_cache
from remarkable.plugins.hkex.utils import MoldAnswer, excel_row_iter
from remarkable.schemas.addition_data import (
    load_addition_data_by_fid,
    load_addition_data_by_fid_tid,
    load_addition_data_by_tree_id,
)
from remarkable.services import esg_result
from remarkable.services.analysis import RuleReport
from remarkable.services.ar import gen_results_for_question, sync_ar_answer_result
from remarkable.services.company import sync_active_company, sync_delisted_company
from remarkable.services.disclosure import update_disclosure_result_for_all_files
from remarkable.services.export.by_rule import export_by_rule
from remarkable.services.export.stat_res import collect_compliance_answer, run_stat_res
from remarkable.services.file import SyncBakery, fetch_and_sync
from remarkable.services.file_meta import batch_update_file_meta, exit_hkex
from remarkable.services.statistics import get_rule_summary
from remarkable.worker.hkex_tasks import (
    compliance_score,
    compliance_score_task,
    export_for_issuer,
    export_from_rule_task,
    non_compliance_issuers,
    non_compliance_issuers_task,
    review_summary_task,
    rule_summary_task,
)
from remarkable.worker.tasks import (
    _make_question,
    run_predict_question_answer,
)

# fix "_csv.Error: field larger than field limit (131072)"
# via https://stackoverflow.com/a/15063941
csv.field_size_limit(sys.maxsize)
logger = logging.getLogger(__name__)


@task(iterable=["report_years", "file_ids", "stock_codes"])
def update_hkex_meta(ctx, report_years=None, file_ids=None, stock_codes=None, worker=4):
    """批量解析已有年报year-end信息"""
    from remarkable.models.hkex_file_meta import HKEXFileMeta as HKEXFileMeta_
    from remarkable.plugins.hkex.utils import standard_stock
    from remarkable.worker.tasks import _parse_ar_year_end

    tasks = []
    select = HKEXFileMeta_.select(HKEXFileMeta_.fid, HKEXFileMeta_.year_end)
    cond = HKEXFileMeta_.doc_type == DocType.AR
    if report_years:
        cond &= HKEXFileMeta_.report_year.in_(report_years)
    if file_ids:
        cond &= HKEXFileMeta_.fid.in_([int(fid) for fid in file_ids])
    if stock_codes:
        cond &= HKEXFileMeta_.stock_code.in_([standard_stock(code) for code in stock_codes])
    with pw_db.allow_sync():
        for fid, year_end in select.where(cond).order_by(HKEXFileMeta_.fid.desc()).tuples().execute():
            tasks.append((fid, year_end))
    if tasks:
        for _ in run_by_batch(
            _parse_ar_year_end, tasks, workers=worker, callback=lambda x, y: print(f"In param: {x} -> Result: {y}")
        ):
            pass
    else:
        logging.info("No hkex file meta to update")


@task(klass=InvokeWrapper)
async def import_rule_reference(_, ref_dir="data/hkex/jura_rules", ref_path=""):
    """
    导入 rule_reference 信息
    """
    from remarkable.models.rule_reference import RuleReference

    columns = ["main_rule", "gem_rule", "rule", "rule_description", "batch"]
    clean_space_p = re.compile(r"[\r\t\f\v ]+")  # 去掉除换行符外的空白字符
    for ref in (Path(ref_path),) if ref_path else Path(ref_dir).glob("*.xls*"):
        for order, row in enumerate(excel_row_iter(ref, skip_rows=3), start=1):
            await RuleReference.insert_or_update(
                conflict_target=[RuleReference.rule],
                **{
                    **dict(
                        zip(
                            columns,
                            [clean_space_p.sub(" ", str(r.value or "")).strip() for r in row],
                        )
                    ),
                    "order": order,
                },
            )
        logging.info(f"Import or update rule reference[{order}] from {ref} done")


@task(klass=InvokeWrapper)
async def import_issue_info(ctx):
    """
    导入 listing_date.xls 信息
    导入hkex_stock表中
    可重复执行导入
    """
    relative_path = "data/hkex/listing_date.xls"
    excel_file = os.path.join(project_root, relative_path)
    rows = get_issue_rows(excel_file)
    columns = ["stock_code", "market_time", "issue_place"]
    conflict_keys = ["stock_code"]
    sql = generate_sql(rows, columns, conflict_keys, "hkex_stock")
    res = await pw_db.execute(sql)
    if len(res) == len(rows):
        print("success")
    else:
        print("import failed, please check!")


def get_issue_rows(excel_file):
    rows = []
    for row in excel_row_iter(excel_file, skip_rows=1):
        if row[0].ctype == 0 and row[1].ctype == 0:
            break
        stock_code = row[2].value.zfill(5)
        market_time = row[3].value
        issue_place = row[4].value
        if not market_time:
            print(f"stock {stock_code} has some error, please check excel file")
            continue
        rows.append((stock_code, market_time, issue_place))
    return rows


def remove_conflict_rows(columns, rows, conflict_keys):
    if not conflict_keys:
        return rows
    conflict_row_values = []
    for row in rows[:]:
        conflict_values = []
        for column, value in zip(
            columns,
            row,
        ):
            if column not in conflict_keys:
                continue
            conflict_values.append(str(value))
        conflict_value = "@".join(conflict_values)
        if conflict_value in conflict_row_values:
            rows.remove(row)
        else:
            conflict_row_values.append(conflict_value)
    return rows


def format_value(value):
    if isinstance(value, (str, bytes)):
        if "'" in value:
            value = value.replace("'", "''")
        return "'{}'".format(value)
    elif isinstance(value, dict):
        return "'{}'".format(json.dumps(value))
    elif isinstance(value, (tuple, list)):
        return "'{%s}'" % ",".join([str(i) for i in value])
    else:
        return "{}".format(value)


def generate_sql(rows, columns, conflict_keys, table_name):
    rows = remove_conflict_rows(columns, rows, conflict_keys)
    values = ["({})".format(",".join([format_value(value) for value in row])) for row in rows]
    update_columns = []
    for column in columns:
        if column in conflict_keys:
            continue
        update_columns.append("{column}=excluded.{column}".format(column=column))
    sql = """INSERT INTO {table}({columns}) VALUES {values}
              on conflict ({conflict_keys}) do UPDATE
              SET {update_columns}, updated_utc=extract(epoch from now())::int
              RETURNING id;
            """.format(
        table=table_name,
        columns=",".join(columns),
        values=",".join(values),
        conflict_keys=",".join(conflict_keys),
        update_columns=",".join(update_columns),
    )
    return sql


@task(klass=InvokeWrapper)
async def gen_compliance_score(ctx, sync, save=False):
    """
    生成 compliance_score 信息保存到redis，对应 summary-statistics 页面
    """
    compliance_score_key = "compliance_score*"
    compliance_score_flag = "{}_{}".format(compliance_score_key[:-1], "flag")
    push_data(compliance_score_flag, True)
    sync = int(sync)
    if sync:
        logging.info("start exec compliance_score_task")
        await compliance_score(compliance_score_key, save)
        logging.info("success")
    else:
        compliance_score_task.delay(compliance_score_key, save)


@task(klass=InvokeWrapper)
async def gen_rule_summary(ctx, sync):
    """
    生成 rule_summary 信息保存到redis
    """
    rules = await RuleReference.find_abcd_rule_references()
    rules = [item.rule for item in rules]
    for rule in rules:
        rule_summary_key = "{}_{}_*".format("rule_summary", rule)
        rule_summary_flag = "{}_{}".format(rule_summary_key[:-2], "flag")
        push_data(rule_summary_flag, True)
        sync = int(sync)
        if sync:
            logging.info("start exec rule_summary_task %s", rule)
            await rule_summary_task(rule_summary_key)
        else:
            rule_summary_task.delay(rule_summary_key)


@task(klass=InvokeWrapper)
async def gen_review_summary(ctx, sync):
    # async def gen_review_summary(sync):
    """
    生成 review_summary 信息保存到redis
    """
    review_summary_key = "review_summary*"
    review_summary_flag = "review_summary_flag"
    push_data(review_summary_flag, True)
    sync = int(sync)
    logging.info("Start exec review_summary_task")
    if sync:
        await review_summary_task(review_summary_key)
        logging.info("Success")
    else:
        review_summary_task.delay(review_summary_key)
        logging.info("Tasks are pushed to the message queue")


@task(
    klass=InvokeWrapper,
    iterable=["skip"],
    help={
        "mid": "schema id, default: 5",
        "skip": "skip test project ids",
        "tree_id": "selected tree_id",
        "report_year": "financial year",
    },
)
async def export_summary_by_rule(ctx, tree_id=None, skip=None, mid=5, report_year=None):
    """
    按规则导出统计数据至summary.csv
        inv hkex.export-summary-by-rule -h 'jefferson.paodingai.com' -m 5
    """
    mold = await NewMold.find_by_id(mid)
    schema = HKEXSchema(mold.data)
    rules = await RuleReference.get_rule_reference(jura_version="1")
    rules = [item.rule for item in rules]
    sql = """
        select hkex_file_meta.stock_code,
               question.preset_answer -> 'userAnswer' -> 'items' as items,
               question.id as qid,
               file.id     as fid,
               file.pid,
               hkex_file_meta.report_year,
               hkex_file_meta.name
        from hkex_file_meta
               left join file on file.qid = hkex_file_meta.qid
               left join question on question.id = hkex_file_meta.qid
        where file.deleted_utc = 0
          and file.tree_id {}
          and file.mold = {}
          and question.crude_answer is not null
    """

    if isinstance(skip, list):
        skip_ids = []
        for skip_id in skip:
            sub_trees = await NewFileTree.get_subtrees([skip_id], include_self=True)
            tree_ids = [str(tree.id) for tree in sub_trees]
            skip_ids.extend(tree_ids)
        if skip_ids:
            sql = sql.format("not in ({})".format(",".join(skip_ids)), mid)
    else:
        sub_trees = await NewFileTree.get_subtrees([tree_id], include_self=True)
        select_ids = [str(tree.id) for tree in sub_trees]
        if select_ids:
            sql = sql.format("in ({})".format(",".join(select_ids)), mid)

    if report_year:
        sql += " and hkex_file_meta.report_year = '{}'".format(report_year)

    sql += " order by stock_code"
    rows = await pw_db.execute(sql)
    header = ["stock_code", "items", "qid", "fid", "pid", "report_year", "name"]
    records = [
        dict(
            zip(
                header,
                row,
            )
        )
        for row in rows
    ]

    writer = csv.writer(open("summary.csv", "w", newline=""))
    writer.writerow(["Rule No.", "Stock Code", "Company Name", "Compliance Assessment"])
    for rule in rules:
        label = schema.rule_name_map[rule]
        for record in records:
            if not record.get("items"):
                continue
            for item in record["items"]:
                if item["schema"]["data"]["label"] == "({})".format(label):
                    cmp_value = item.get("value", "")
                    break
            else:
                cmp_value = ""

            if cmp_value != "compliance":  # 只导出不合规的记录
                writer.writerow(
                    [
                        rule,
                        record["stock_code"],
                        record["name"],
                        # ds_value,
                        # mds_value,
                        "NC",  # potential non-compliance
                        # url.format(host=host, qid=record['qid'], fid=record['fid'], mold=mold, pid=record['pid']),
                    ]
                )
        logging.info("Collecting data for rule: %s", rule)


async def _fix_up_metas(ins: SyncBakery):
    from remarkable.models.hkex_file import HKEXFile
    from remarkable.models.hkex_file_meta import HKEXFileMeta
    from remarkable.models.new_file import NewFile
    from remarkable.models.new_question import NewQuestion

    for fid in ins.fids:
        await fetch_and_sync(
            fid,
            db_only=ins.db_only,
            file_only=ins.file_only,
            dst_mid_list=ins.dst_mid_list,
            load_func=_fix_up_metas,
        )
    if not ins.fid:
        return

    fid = None
    mids = set()
    ins.rows = [row for row in ins.rows if isinstance(row, (NewFile, NewQuestion, HKEXFile, HKEXFileMeta))]
    is_esg = False
    for row in sorted(ins.rows, key=lambda x: 0 if isinstance(x, (NewFile, NewQuestion)) else 1):
        if isinstance(row, NewFile):
            file = await pw_db.first(
                NewFile.select()
                .filter(NewFile.pdf == row.pdf, NewFile.deleted_utc == 0)
                .order_by(NewFile.created_utc.desc())
            )
            assert file, f"File({row.pdf}) not found"

            fid = file.id
            if file.mold_list and [i for i in special_mold.esg_mids_with_policy if i in file.mold_list]:
                is_esg = True
            for mid in file.mold_list:
                if not await NewQuestion.find_by_fid_mid(fid, mid):
                    await _make_question(fid, mid)
                    mids.add(mid)
            continue

        if isinstance(row, NewQuestion):
            mids.add(row.mold)
            continue

        if isinstance(row, HKEXFileMeta):
            if mids:
                # 兼容，hkex_file_meta.qid 已经不再使用
                row.qid = await pw_db.scalar(
                    NewQuestion.select(NewQuestion.id).filter(NewQuestion.fid == fid, NewQuestion.mold == min(mids))
                )
            else:
                row.qid = await pw_db.scalar(
                    NewQuestion.select(NewQuestion.id).filter(NewQuestion.fid == fid).order_by(NewQuestion.mold)
                )
            row.fid = fid
            row.stat_res = {}
            if is_esg and row.doc_type != DocType.ESG:
                continue
            row.doc_type = DocType.ESG

        excludes = []
        if isinstance(row, HKEXFile):
            row.fid = fid
            if await pw_db.exists(HKEXFile.select().where(HKEXFile.id == row.id)):
                excludes.append("id")

        try:
            await row.__class__.insert_or_update(**row.to_dict(exclude=excludes))
        except peewee.IntegrityError as exp:
            logger.exception(str(exp))
        else:
            await ins.update_id_seq(row)
            logger.debug(f"{row.table_name()}: {row} saved.")

    if is_esg:
        from remarkable.services.esg import update_activated_state

        await update_activated_state(fid)


async def deal_with_files(tree_id: int):
    from remarkable.models.file_tree import NewFileTree
    from remarkable.models.hkex_file import HKEXFile
    from remarkable.models.new_file import NewFile

    for sub_tree in await NewFileTree.get_subtrees([tree_id], include_self=False):
        await deal_with_files(sub_tree.id)

    for file in await pw_db.execute(NewFile.select().where((NewFile.tree_id == tree_id) & (NewFile.deleted_utc == 0))):
        hkex_file: HKEXFile = await pw_db.first(HKEXFile.select().where(HKEXFile.fid == file.id))
        if not hkex_file or hkex_file.stock_code not in file.name:
            # 本地也可能对应了错误的记录，需要从远端同步
            logger.info("try to sync file %s", file.id)
            await fetch_and_sync(
                0, query=f"select id as fid from file where hash = '{file.hash}'", db_only=True, load_func=_fix_up_metas
            )
            hkex_file = await pw_db.first(HKEXFile.select().where(HKEXFile.fid == file.id))
            if hkex_file and hkex_file.stock_code not in file.name:
                logger.warning(
                    f"File {file.id}:{file.name} still not match, delete hkex_file {hkex_file.id}:{hkex_file.stock_code}"
                )
                await pw_db.delete(hkex_file)
        pdfinsight_path = file.pdfinsight_path(abs_path=True) if file.pdfinsight else None
        logger.info(f"Update meta for {file.id}")
        await set_meta_info(
            file.name,
            file.id,
            file.qid,
            hkex_file=hkex_file.to_dict() if hkex_file else {"type": DocType.ESG.name},
            pdfinsight_path=pdfinsight_path,
        )


@task(klass=InvokeWrapper)
async def fix_up_hkex_meta(ctx, tree_id=0):
    """修复hkex_file_meta表中的数据"""
    await deal_with_files(tree_id)


@task
def gen_stat_res(ctx, start=0, end=0, year=None, workers=0, del_invalid_data=False, force=False):
    """生成hkex_file_meta表中stat_res的数据"""

    sql = "select fid from hkex_file_meta where 1=1"
    params = {}
    if start:
        sql += " and fid>=%(start)s"
        params.update({"start": start})
    if end:
        sql += " and fid<=%(end)s"
        params.update({"end": end})
    if year:
        sql += " and report_year=%(year)s"
        params.update({"year": year})
    sql += " order by fid desc;"
    tasks = []
    for row in pw_db.sync_execute(sql, params):
        tasks.append((row.fid, force))

    if del_invalid_data:
        logging.info("start del invalid data in stat_res  from %s to %s", start, end)
        for _ in run_by_batch(run_del_invalid_data, tasks, workers=workers):
            pass
    else:
        logging.info("start gen stat_res for hkex_file_meta from %s to %s", start, end)
        for _ in run_by_batch(run_stat_res, tasks, workers=workers):
            pass


async def run_del_invalid_data(fid: int, force: bool = False):
    logging.info(f"del invalid data in stat_res, {force=}")
    from remarkable.models.hkex_file_meta import HKEXFileMeta as HKEXFileMeta_
    from remarkable.models.new_question import NewQuestion

    for question in await NewQuestion.find_by_fid(fid):
        if question.mold not in special_mold.ar_mids:
            continue
        file_meta = await HKEXFileMeta_.find_by_fid(fid)
        if not file_meta or not file_meta.stat_res:
            continue
        if "C1" in file_meta.stat_res:
            file_meta.stat_res.pop("C1")
        if "C2" in file_meta.stat_res:
            file_meta.stat_res.pop("C2")
        if "C4" in file_meta.stat_res:
            file_meta.stat_res.pop("C4")
        # TODO: 清除更多情况的无效数据
        await pw_db.update(file_meta)
        logging.info("del invalid stat_res, qid: %s, fid: %s", question.id, fid)


@task(klass=InvokeWrapper)
async def gen_non_compliance_issuers(ctx, sync):
    key = "non_compliance_issuers*"
    redis_flag = "{}_{}".format(key[:-1], "flag")
    push_data(redis_flag, True)
    sync = int(sync)
    logging.info("start exec gen_non_compliance_issuers")
    if sync:
        await non_compliance_issuers(key)
        logging.info("success")
    else:
        non_compliance_issuers_task.delay(key)
        logging.info("Tasks are pushed to the message queue")


def _convert_year_end_date(date_str):
    """
    :param date_str: 09 Apr 2019 or Apr 09 2019
    :return: 1554739200
    """
    patterns = ["%d %b %Y", "%b %d %Y"]
    for pattern in patterns:
        try:
            date = datetime.datetime.strptime(date_str, pattern)
        except ValueError:
            pass
        else:
            return int(date.timestamp())
    return None


@task(iterable=["task_name"])
def pick_out_tasks(ctx, task_name=None):
    """挑出不必要的任务（不传参则显示所有任务，不做任何操作）
    具体操作：
        不必要的任务：celery -> celery:bak:20190926
        剩余任务：celery -> celery:tmp -> celery
    """
    unnecessary_tasks = set()
    if task_name:
        unnecessary_tasks.update(task_name)
    if not unnecessary_tasks:
        logging.warning("No task_name specified, so I'll just display all tasks.")
    target_key = "celery"
    temp_key = f"{target_key}:tmp"
    backup_key = "celery:bak:{}".format(datetime.datetime.now().strftime("%Y%m%d"))

    rdb = init_rdb()
    backup_pipe = rdb.pipeline()
    update_pipe = rdb.pipeline()
    task_counter = collections.Counter()

    assert rdb.exists(target_key), "key not found: %s" % target_key
    for value in rdb.lrange(target_key, 0, -1):
        _task = json.loads(value)
        whole_task_name = _task["headers"]["task"]
        if unnecessary_tasks:
            task_name = whole_task_name.split(".")[-1]
            if task_name in unnecessary_tasks:
                logging.info("skip and backup this task: %s", task_name)
                backup_pipe.lpush(backup_key, value)
            else:
                logging.info("keep this task: %s", task_name)
                update_pipe.lpush(temp_key, value)
        else:
            task_counter[whole_task_name] += 1

    if not unnecessary_tasks:
        for task_name, count in task_counter.most_common():
            logging.info("%s: %s", task_name, count)
    else:
        backup_pipe.execute()
        update_pipe.delete(target_key)
        update_pipe.rename(temp_key, target_key)
        update_pipe.execute()


@task(klass=InvokeWrapper)
async def gen_ratio_cache(ctx):
    await RuleReport.create_ratio_cache()


@task()
def gen_disclosure_cache(ctx, force=False):
    update_disclosure_result_for_all_files(force=force)


@task(klass=InvokeWrapper, iterable=["report_years"])
def gen_by_rule_data(ctx, sync=True, report_years=None, include_manual=True, regenerate=False, workers=4):
    """
    生成 by rule 导出数据
    """
    if sync:
        if not report_years:
            report_years = HKEXFileMeta.sync_find_available_years(doc_types=["ar", "qr"])
        export_by_rule(report_years, include_manual=include_manual, regenerate=regenerate, workers=workers)
        export_by_rule(report_years, include_manual=include_manual, regenerate=regenerate, delist=True, workers=workers)
    else:
        export_from_rule_task.delay(report_years=report_years, include_manual=include_manual)


@task()
def gen_by_issuer_data(ctx, stock_code, doc_type="ar", delist=False):
    """
    生成 by issuer 导出数据
    """
    logging.info(f"start exec export_for_issuer {doc_type=} {delist=}")
    export_for_issuer(stock_code, doc_type, delist=delist)
    logging.info("success")


@task(klass=InvokeWrapper, iterable=("report_year",))
async def clean_pdf_cache_by_year(ctx, report_year=None):
    """
    根据年度删除PDF分页缓存
    """
    cache_dir = Path(localstorage.root) / "pdf_cache"
    prefix_p = re.compile(r"^[0-9a-z]{2}$")
    v2a_mold = special_mold.v2a_id

    def cache_dir_iter():
        for prefix_dir in cache_dir.iterdir():
            if not prefix_p.search(prefix_dir.name):
                continue
            for ext_dir in prefix_dir.iterdir():
                yield ext_dir

    async def need_delete(path):
        hash_str = path.parent.name + path.name
        file = await NewFile.find_by_hash(hash_str)
        if not file:
            return True
        if file.mold == v2a_mold or v2a_mold in file.mold_list:  # 需标注的外部文档
            return False
        meta = await HKEXFileMeta.find_by_fid(file.id)
        if not meta:
            return True
        if meta.report_year in report_year:
            return True
        return False

    for dir_path in cache_dir_iter():
        if await need_delete(dir_path):
            logging.info("remove: %s", dir_path)
            shutil.rmtree(dir_path)


@task
def compliance_redis_data(ctx, path="compliance_redis_data.json"):
    """
    获取redis中的统计数据
    """
    ret = {}
    for redis_key in ("compliance_score*", "non_compliance_issuers*"):
        redis_res, flag = pull_statistics_data(redis_key, int(time.time()))
        ret[redis_key[:-1]] = {"flag": flag, "data": redis_res}
    with open(path, "w") as sfp:
        json.dump(ret, sfp)
    logging.info("dump data success: %s", path)


@task(klass=InvokeWrapper, iterable=("rule",))
async def modify_manual_compliance_value(
    ctx, rule, mid=15, enum_value="compliance", disclosure_value="", action="modify", ref_path=None
):
    """
    按rule修改(modify)、导出(export)、恢复(restore)人工标注枚举值（只适用于schema 15）
    """

    def is_equal(label, answer):
        """检查披露情况是否符合指定披露值"""
        for item in (answer.get("userAnswer") or {}).get("items", []):
            if (
                item.get("manual")
                and json.loads(item["key"])[1].split(":")[0] == label
                and item.get("value").lower() == disclosure_value.lower()
            ):
                return True
        return False

    async def do_modify():
        query = NewQuestion.select(NewQuestion.id, NewQuestion.answer).where(NewQuestion.mold == mid)
        for question in await pw_db.execute(query):
            if not question.answer:
                continue
            items = (question.answer.get("rule_result") or {}).get("items", [])
            if not items:
                continue
            for item in items:
                if not item.get("special_ui"):
                    continue
                for _rule in rule:
                    if item["schema"]["data"]["label"] == _rule and (
                        not disclosure_value or is_equal(_rule, question.answer)
                    ):
                        item["value"] = enum_value
            await pw_db.update(question)
            await collect_compliance_answer(question.id, answer=question.answer)
            logging.info(f"update question: {question.id}")

    async def do_export(ref_fh):
        query = NewQuestion.select(NewQuestion.id, NewQuestion.answer).where(NewQuestion.mold == mid)
        for question in await pw_db.execute(query):
            if not question.answer:
                continue
            items = (question.answer.get("rule_result") or {}).get("items", [])
            if not items:
                continue
            write_str = f"{question.id}#"
            for item in items:
                if not item.get("special_ui"):
                    continue
                for _rule in rule:
                    if item["schema"]["data"]["label"] == _rule:
                        write_str += f"{_rule}:{item['value']},"
            write_str = write_str.rstrip(",") + "\n"
            if write_str.split("#")[-1] != "\n":
                logging.info("export: %s", write_str.rstrip())
                ref_fh.write(write_str)

    async def do_restore(ref_fh):
        for line in ref_fh:
            qid, ans_str = line.strip().split("#")
            new_answer = dict(item.split(":") for item in ans_str.split(","))
            question = await NewQuestion.find_by_id(int(qid))
            if question and question.answer:
                items = (question.answer.get("rule_result") or {}).get("items", [])
                if not items:
                    continue
                for item in items:
                    for key, value in new_answer.items():
                        if item["schema"]["data"]["label"] == key:
                            item["value"] = value
                await pw_db.update(question)
                await collect_compliance_answer(qid, answer=question.answer)
                logging.info("update question: %s", qid)

    if not ref_path and action == "modify":
        await do_modify()
    if ref_path and action == "export":
        with gzip.open(f"{ref_path}.gz", "wt") as file_obj:
            await do_export(file_obj)
    if ref_path and action == "restore":
        with gzip.open(ref_path, "rt") as file_obj:
            await do_restore(file_obj)


@task(klass=InvokeWrapper)
async def export_non_stock(ctx, year="2018", mold=15, path=project_root, rule_prefix="B"):
    cols = ["stock_code", "report_years", "lrs_violated", "report_year_end", "ar_released_date", "board_type"]

    def export_nc_count(res_by_rule, rule_summarys):
        name = "每个规则不合规预测总数.csv"
        nc_count_by_rule = {k: len(v) for k, v in res_by_rule.items()}
        with open(Path(path) / "non_stock" / name, "w", newline="") as file_obj:
            writer = csv.writer(file_obj)
            writer.writerow(["rule", "year", "total", "compliance", "non-compliance", "non-compliance/total"])
            for rule, rule_ret in rule_summarys.items():
                for year_ret in rule_ret["body"]:
                    if year_ret["year"] != year:
                        continue
                    all_data = year_ret["data"]["Total"]
                    total = all_data["available"]
                    compliance = all_data["compliance"]
                    non_compliance = nc_count_by_rule[rule]
                    non_proportion = round(non_compliance / total, 2)
                    writer.writerow((rule, year, total, compliance, non_compliance, non_proportion))

    def export_xls(res_by_rule, non_stock_path):
        non_stock_path = Path(non_stock_path) / "non_stock"
        if not non_stock_path.exists():
            non_stock_path.mkdir()
        for rule, answer in res_by_rule.items():
            dump_data_to_excel(answer, non_stock_path / f"{rule}.xls")

    def dump_data_to_excel(answer, path):
        workbook = xlwt.Workbook(encoding="ascii")
        worksheet = workbook.add_sheet("Result Analysis By Rule")
        for index, value in enumerate(cols):
            worksheet.write(0, index, value)
        for row_index, datas in enumerate(answer, start=1):
            for index, value in enumerate(datas):
                worksheet.write(row_index, index, value)
        if os.path.exists(path):
            os.remove(path)
        workbook.save(path)

    async def get_stat_res_by_preset(file_meta, rule_references):
        rules = {rule_reference.rule for rule_reference in rule_references}
        stat_res = dict.fromkeys(rules, "")
        question = await NewQuestion.find_by_fid_mid(file_meta.fid, mold_id=mold)
        if question.preset_answer:
            try:
                answer_items = question.preset_answer["rule_result"]["items"]
            except KeyError:
                return stat_res
            for item in answer_items:
                rule = item["schema"]["data"]["label"]
                manual_answer = item["value"]
                if manual_answer == "compliance":
                    stat_res[rule] = "true"
                elif manual_answer == "potential non-compliance":
                    stat_res[rule] = "false"
        return stat_res

    async def get_non_compliance_issuers(rule_references):
        stocks_groups = await HKEXGroup.get_stock_groups()
        file_metas = await HKEXFileMeta.get_file_metas(report_years=[year])
        ret = []
        for file_meta in tqdm(file_metas):
            stat_res = await get_stat_res_by_preset(file_meta, rule_references)
            if not stat_res:
                continue
            lrs_violated = [rule for rule, result in stat_res.items() if result == "false"]
            if not lrs_violated:
                continue
            board_type = "MB"
            stock_code = file_meta.stock_code
            if stock_code in stocks_groups["gem"]:
                board_type = "GEM"
            report_year = file_meta.report_year
            ar_released_date = file_meta.published
            report_year_end = file_meta.year_end
            row = [stock_code, report_year, lrs_violated, report_year_end, ar_released_date, board_type]
            ret.append(row)
        return ret

    NonComplianceData = collections.namedtuple("NonComplianceInfo", cols)
    rule_references = await RuleReference.get_rule_reference(jura_version="2")
    res_by_rule = {rule_reference.rule: [] for rule_reference in rule_references}
    non_compliance_res = await get_non_compliance_issuers(rule_references)
    for row in non_compliance_res:
        non_compliance_data = NonComplianceData._make(row)
        for rule in non_compliance_data.lrs_violated:
            res_by_rule[rule].append(non_compliance_data._replace(lrs_violated=rule))
    export_xls(res_by_rule, path)
    # 生成 每个规则不合规预测总数.csv
    rules = {rule_reference.rule for rule_reference in rule_references}
    rule_summarys = {}
    for rule in tqdm(sorted(rules, key=lambda i: int(i[1:]))):
        if not rule.startswith(rule_prefix):
            continue
        ret = await get_rule_summary(rule)
        rule_summarys[rule] = ret
    export_nc_count(res_by_rule, rule_summarys)


@task(klass=InvokeWrapper, iterable=["report_years"])
async def stat_prec_recall(ctx, report_years=None, output_path=None):
    """生成包含标注信息的rule_answer"""
    root_path = output_path or project_root
    by_rule_data_path = Path(root_path) / "data" / "rule_answers_include_manual"
    logging.info(f"output_path is {by_rule_data_path}")
    await export_by_rule(report_years, include_manual=True, output_dir=by_rule_data_path)


@task(klass=InvokeWrapper)
async def clean_ar_record(ctx):
    """清除无用的年报相关记录"""
    ar_p = re.compile(r"ann?[ua][ua]?l.*?r?re?p?[or]p?[or]?t", re.I)
    query = HKEXFile.select(HKEXFile.id, HKEXFile.meta, HKEXFile.name).where(HKEXFile.type == "ar")
    for record in await pw_db.execute(query):
        if not record.fid or ar_p.search(record.name):
            logging.info(f"skip: {record}")
            continue
        file = NewFile.find_by_id(record.meta.get("v1", 0))
        if not file:
            continue
        logging.info(f"delete: {record}")
        await pw_db.delete(file)


@task(klass=InvokeWrapper, iterable=["labels"])
async def extend_new_rule(ctx, mid, copy_from="", copy_to="", labels=None):
    """
    复制旧的标注答案(包括合规标注)到新增的Rules
    1. 同父级复制: C1 -> C1.1, C1.2, ..., C1.n
    2. 跨父级复制: B64-Beginning amount -> B63-Beginning amount
    https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/420#note_99332
    """

    def transform(user_ans, template_ans):
        answer = deepcopy(template_ans)
        answer.pop("score", None)
        if user_ans.get("data") or user_ans.get("value"):
            for k in ("data", "value", "manual", "special_ui", "marker"):
                if k in user_ans:
                    answer[k] = user_ans[k]
        else:
            return None
        return answer

    def copy_answer(row, answer, copy_from, copy_to, labels):
        # 跨父节点复制子节点答案
        answer = MoldAnswer(answer)
        for label in labels:
            for ans_from, ans_to in zip(
                answer.collect_answer(copy_from, label),
                answer.collect_answer(copy_to, label),
            ):
                for k in ("data", "value", "manual", "special_ui", "marker"):
                    if k in ans_from:
                        ans_to[k] = ans_from[k]
        logging.info(f"fid: {row.fid}, qid: {row.qid}: COPY {copy_from}->{copy_to}: {labels}")
        return {"schema": answer.schema.mold, **answer.dumps_answer()}

    def transform_answer(src: MoldAnswer, dst: MoldAnswer, key: str):
        answers = defaultdict(list)
        old_key = key.split(".")[0]
        try:
            user_answer = src.get_rule_answer(old_key)
        except RuntimeError:
            return answers

        for u_ans, e_ans in zip(
            user_answer["answer"],
            dst.get_rule_answer(key)["answer"],
        ):
            ans = transform(u_ans, e_ans)
            if ans:
                answers["userAnswer"].append(ans)

        for u_ans, e_ans in zip(
            [user_answer["rule_result"]],
            [dst.get_rule_answer(key)["rule_result"]],
        ):
            ans = transform(u_ans, e_ans)
            if ans:
                answers["rule_result"].append(ans)
        return answers

    async def merge_answer(row, answer: dict):
        if not answer:
            return None
        if copy_from and copy_to and labels:
            return copy_answer(row, answer, copy_from, copy_to, labels)
        mold = await NewMold.find_by_id(row.mold_id)
        empty_answer = EmptyAnswer(mold).answer
        empty_mold_answer = MoldAnswer(empty_answer)
        user_answer = MoldAnswer(answer)
        new_keys = [
            key for key in empty_mold_answer.schema.root_rules_map if key not in user_answer.schema.root_rules_map
        ]
        if not new_keys:
            return None
        new_answer = {"schema": empty_answer["schema"], **user_answer.dumps_answer()}
        for key in new_keys:
            answers = transform_answer(user_answer, empty_mold_answer, key)
            if answers:
                for ans_key in "userAnswer", "rule_result":
                    new_answer[ans_key]["items"].extend(answers[ans_key])
                logging.info(f"file: {row.fid}, qid: {row.qid}, aid: {row.aid}, rule: {key} copied.")
            else:
                logging.warning(f"file: {row.fid}, qid: {row.qid}, aid: {row.aid}, rule: {key} failed.")
        return new_answer

    sql = f"""
        select q.fid as fid, q.id as qid, a.id as aid, a.data as user_answer, q.answer as merged_answer,
        q.preset_answer as preset_answer, q.mold as mold_id
        from question q
            left join answer a on q.id = a.qid
        where q.mold = {mid}
        order by q.fid
    """
    for record in await pw_db.execute(sql):
        answer = await Answer.find_by_id(record.aid)
        question = await NewQuestion.find_by_id(record.qid)
        user_answer = await merge_answer(record, record.user_answer)
        if user_answer:
            answer.data = user_answer
            await pw_db.update(answer)
        merged_answer = await merge_answer(record, record.merged_answer)
        if merged_answer:
            question.answer = merged_answer
            await pw_db.update(question)
        preset_answer = await merge_answer(record, record.preset_answer)
        if preset_answer:
            question.preset_answer = merged_answer
            await pw_db.update(question)


async def _fix_pipe(fid: int, qid: int, mid: int, *, force=False):
    from remarkable.models.esg_result import ESGResult
    from remarkable.models.file_esg_xref import FileESGxREF
    from remarkable.models.new_file import NewFile
    from remarkable.models.new_question import NewQuestion

    file = await NewFile.find_by_id(fid)
    if not file.pdfinsight_path() or not localstorage.exists(file.pdfinsight_path()):
        logging.warning(f"file: {fid} has no pdfinsight")
    if localstorage.size(file.pdfinsight_path()) < 1024 * 1024:
        logging.warning(f"file: {fid} interdoc size too small")
    question = await NewQuestion.find_by_id(qid)
    if force or EmptyAnswer.all_null(question.preset_answer):
        if not force:
            logger.warning(f"file: {fid}, qid: {qid} has no preset answer, generating...")
        return await run_predict_question_answer(file, question)

    if question.mold in special_mold.esg_mids_with_policy:
        if not await FileESGxREF.is_activated(fid):
            logger.warning(f"Not a activated ESG file: {fid}, ignored.")
            return None
        count = await pw_db.scalar(
            ESGResult.select(peewee.fn.COUNT(ESGResult.uid.distinct())).where(ESGResult.fid == fid)
        )
        if count < 3:
            logger.warning(f"file: {fid}, qid: {qid} didn't sync esg result, generating...")
            await esg_result.sync_answers(question.id, question=question)


@task(klass=InvokeWrapper, iterable=["molds", "report_years"])
def fix_pipe(ctx, fids_path=None, molds=None, report_years=None, force=False, workers=0):
    """尝试修复年报初步定位/提取/合规检查等环节可能出现的问题"""
    fids = None
    molds = [int(m) for m in molds if m.isdigit()]
    report_years = [y for y in report_years if y.isdigit()]
    if fids_path and Path(fids_path).is_file():
        with open(fids_path, "r") as file_obj:
            fids = [i.strip() for i in file_obj if i.strip().isdigit()]

    select = NewQuestion.select(NewQuestion.fid, NewQuestion.id, NewQuestion.mold).join(
        NewFile, on=(NewQuestion.fid == NewFile.id)
    )
    cond = NewQuestion.mold.in_(molds) if molds else orm.TRUE
    if fids:
        cond &= NewQuestion.fid.in_(fids)
    if report_years:
        select = select.join(HKEXFileMeta, on=(NewQuestion.fid == HKEXFileMeta.fid))
        cond &= HKEXFileMeta.report_year.in_(report_years)

    tasks = []
    with pw_db.allow_sync():
        for fid, qid, mid in select.where(cond).order_by(NewQuestion.fid.desc()).tuples():
            tasks.append((fid, qid, mid))
    for _ in run_by_batch(partial(_fix_pipe, force=force), tasks, workers=workers):
        pass


async def _make_pdf_cache(fid, force):
    from remarkable.models.new_file import NewFile

    file = await NewFile.find_by_id(fid)
    if not file:
        logging.warning(f"No data found for file: {fid}")
        return

    await create_pdf_cache(file, force)


@task
def rebuild_pdf_cache(ctx, sql="", force=False, workers=4):
    if not sql:
        sql = "select id from file where deleted_utc=0 order by id desc"
    tasks = [(row[0], force) for row in pw_db.sync_execute(sql)]
    for _ in run_by_batch(_make_pdf_cache, tasks=tasks, workers=workers):
        pass


@task(klass=InvokeWrapper)
async def parse_invalid_pw_docs(ctx, workers=4):
    """重解析提取失败的 profit warning 文档"""
    from remarkable.models.hkex_file import HKEXFile
    from remarkable.models.hkex_file_meta import HKEXFileMeta
    from remarkable.plugins.hkex.worker import parse_profit_warning

    query = (
        HKEXFile.select()
        .join(HKEXFileMeta, on=(HKEXFile.fid == HKEXFileMeta.fid))
        .filter(HKEXFileMeta.doc_type == DocType.PROFITWARNING)
        .order_by(HKEXFile.id.desc())
    )
    tasks = []
    for file in await pw_db.execute(query):
        meta = file.to_dict()
        meta["path"] = file.hash[:2] + "/" + file.hash[2:]
        tasks.append((None, meta))
    for _ in run_by_batch(parse_profit_warning, tasks=tasks, workers=workers):
        pass


@task
def find_inactive_users(ctx, days=30):
    """列出不活跃用户"""
    from playhouse.postgres_ext import ServerSide

    from remarkable.models.user import AdminUser

    delta = datetime.datetime.now() - datetime.timedelta(days=days)
    lines = [("ID", "NAME", "LAST LOGIN", "COUNT")]
    count = 0
    with pw_db.allow_sync():
        for user in ServerSide(
            AdminUser.select()
            .filter(AdminUser.deleted_utc == 0, AdminUser.status != UserStatus.INACTIVE)
            .order_by(AdminUser.id)
        ):
            last_login_at = datetime.datetime.fromtimestamp(user.login_utc)
            if last_login_at < delta:
                lines.append((user.id, user.name, last_login_at, user.login_count))
                count += 1

    if count:
        print(f"The inactive users({count}) in {days} days are as follows:")
        for line in lines:
            print("%4s\t%12s\t%20s\t%5s" % line)
    else:
        print("No inactive users found")


@task()
def update_disclosure_result(ctx, start=None, end=None):
    update_disclosure_result_for_all_files(start, end)


@task(klass=InvokeWrapper, iterable=["stock_codes"])
async def delist_company(ctx, stock_codes):
    await exit_hkex(stock_codes)


@task(klass=InvokeWrapper)
async def sync_company_info(ctx):
    await sync_delisted_company()
    await sync_active_company()


@task(klass=InvokeWrapper)
async def update_delist_file_meta(ctx):
    """
    使用飞书文档 https://paodingai.feishu.cn/sheets/ILaMszZwmhH9TntyjtlchpOlnvc 中的数据更新
    hkex_file_meta 和 delist_file_meta表中的数据
    """
    await batch_update_file_meta()


@dataclass
class Stock:
    issuer: str
    type: str
    listed: str
    final_trade: str
    delisted: str
    reason: str


@task()
def get_stock_delist_date(ctx, stock_code: str):
    # todo 将退市和上市时间更新到 delisted_company 和 active_company
    #  目前难点是 接口返回的是公司名是全程 数据库中保存是简称
    # 在市数据查询  https://webb-site.com/dbpub/orgdata.asp?code={stock_code}&Submit=current
    url = f"https://webb-site.com/dbpub/code.asp?code={stock_code}"
    response = httpx.get(url)
    soup = BeautifulSoup(response.text, "html.parser")
    table = soup.find("table", class_="txtable")
    for row in table.find_all("tr")[1:]:
        cols = row.find_all("td")
        stock = Stock(
            issuer=cols[0].text.strip(),
            type=cols[1].text.strip(),
            listed=cols[2].text.strip(),
            final_trade=cols[3].text.strip(),
            delisted=cols[4].text.strip(),
            reason=cols[5].text.strip(),
        )
        logger.info(f"{stock_code:}")
        logger.info(f"{stock.issuer:}, {stock.delisted:}")


@task(
    klass=InvokeWrapper,
    iterable=["molds", "report_years", "file_ids"],
    help={
        "molds": "molds to filter question",
        "report_years": "report years to filter question",
        "reset_db": "truncate ar_result table",
        "file_ids": "file ids to filter question",
    },
)
async def update_ar_like_rule_answer(ctx, molds=None, report_years=None, file_ids=None, reset_db=False):
    from remarkable.services.ar import get_target_clz

    mold_ids = [int(m) for m in molds if m.isdigit()]
    doc_types = []
    is_ar = False
    if not mold_ids:
        mold_ids = special_mold.ar_mids
        is_ar = True
        doc_types.append(DocType.AR)
        logger.warning(f"No valid mold found: {molds}, will process all AR molds: {mold_ids}")
    elif mids := list(set(mold_ids) & set(special_mold.ar_mids)):
        doc_types.append(DocType.AR)
        mold_ids = mids
        is_ar = True
    elif special_mold.v6_agm_id in mold_ids:
        doc_types.append(DocType.AGM)
        mold_ids = [special_mold.v6_agm_id]
    elif special_mold.v6_poll_id in mold_ids:
        doc_types.append(DocType.POLL)
        mold_ids = [special_mold.v6_poll_id]
    else:
        raise ValueError(f"Unsupported mold ids: {mold_ids}")

    cond = HKEXFileMeta.doc_type.in_(doc_types)
    clz = get_target_clz(mold_ids[0])

    if reset_db:
        for clz in {get_target_clz(mid) for mid in mold_ids}:
            await pw_db.execute(clz.delete())
            await SyncBakery.update_id_seq(clz.table_name(), value=1)

    file_ids = [int(f) for f in file_ids or () if f.isdigit()]
    if file_ids:
        cond &= HKEXFileMeta.fid.in_(file_ids)

    report_years = [m for m in report_years if m.isdigit()]
    if report_years:
        cond &= HKEXFileMeta.report_year.in_(report_years)
    else:
        logger.warning(f"{clz.__name__}: No report years specified, will process all files.")

    if is_ar:
        # 年报只取同年最新的一份
        cte = (
            HKEXFileMeta.select(
                HKEXFileMeta.fid,
                fn.ROW_NUMBER()
                .over(
                    partition_by=[HKEXFileMeta.stock_code, HKEXFileMeta.report_year],
                    order_by=[HKEXFileMeta.published.cast("timestamp").desc()],
                )
                .alias("rank"),
            )
            .where(cond)
            .cte("RankedFiles")
        )
        query = (
            NewQuestion.select(NewQuestion.id, NewQuestion.fid, NewQuestion.mold)
            .join(cte, on=(NewQuestion.fid == cte.c.fid))
            .where(cte.c.rank == 1, NewQuestion.mold.in_(mold_ids))
            .order_by(NewQuestion.id.desc())
            .with_cte(cte)
        )
    else:
        # AGM/POLL 全取
        query = (
            NewQuestion.select(NewQuestion.id, NewQuestion.fid, NewQuestion.mold)
            .join(HKEXFileMeta, on=(NewQuestion.fid == HKEXFileMeta.fid))
            .where(cond)
            .order_by(NewQuestion.id.desc())
        )

    questions = await pw_db.execute(query.namedtuples())
    total_count = len(questions)
    logging.info(f"{clz.__name__}: there are {total_count} questions to be processed.")

    mold_map = {
        m.id: EmptyAnswer(m).answer for m in await pw_db.execute(NewMold.select().where(NewMold.id.in_(mold_ids)))
    }
    rows = []
    batch_size = 1000
    corrupt_questions = []

    async def insert_rows(items):
        lines = [f"fid={i['file_id']}, mid={i['mold_id']}, cmp_key={i['compliance_answer_key']}" for i in items]
        try:
            async with pw_db.atomic():
                await clz.bulk_insert(items, chunk_size=batch_size)
        except Exception as e:
            corrupt_questions.extend(lines)
            corrupt_questions.append(f"{datetime.datetime.now()}: {e}")

    reference_mapper = await RuleReference.find_all_rule_refer_map()
    for qid, fid, mid in questions:
        question = await NewQuestion.find_by_id(qid)
        if not question.preset_answer:
            logger.warning(f"{fid=}, {qid=}, {mid=}: no preset answer, make a mock one.")
            question.preset_answer = mold_map[mid]
        if not question.answer:
            logger.warning(f"{fid=}, {qid=}, {mid=}: no answer, make a mock one.")
            question.answer = mold_map[mid]
        rows.extend(gen_results_for_question(question, reference_mapper))
        if len(rows) >= batch_size:
            await insert_rows(rows)
            rows = []

    if rows:
        await insert_rows(rows)

    for line in corrupt_questions:
        logging.error(line)


@task(
    klass=InvokeWrapper,
    help={
        "question_id": "The question to sync with.",
    },
)
async def update_ar_result_from_question(ctx, question_id=0):
    await sync_ar_answer_result(question_id)


@task(klass=InvokeWrapper)
async def import_market_eye_data(ctx, fid=0, tree_id=0):
    """
    指定fid或tree_id导入Market Eye数据到addition_data表
    使用示例：
    inv hkex.import-market-eye-data -f 13696   # 根据file_id
    inv hkex.import-market-eye-data -t 45648   # 根据tree_id
    """
    if not (fid or tree_id):
        raise ValueError("must provide either fid or tree_id")
    if fid and tree_id:
        await load_addition_data_by_fid_tid(fid, tree_id, force=True)
    elif fid:
        await load_addition_data_by_fid(fid, force=True)
    else:
        await load_addition_data_by_tree_id(tree_id, force=True)


if __name__ == "__main__":
    update_ar_like_rule_answer(None, molds=["5", "15"], report_years=[], reset_db=True)
