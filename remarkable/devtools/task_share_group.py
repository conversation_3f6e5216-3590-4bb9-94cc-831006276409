import logging
from functools import partial

from invoke import task

from remarkable.common.multiprocess import run_in_multiprocess
from remarkable.db import pw_db
from remarkable.models.file_share_meta import FileShareMeta
from remarkable.models.hkex_file_meta import HKEXFileMeta
from remarkable.models.mold import special_mold
from remarkable.models.new_file import NewFile
from remarkable.models.new_question import NewQuestion
from remarkable.pdfinsight.reader import Pd<PERSON>sight<PERSON>eader
from remarkable.predictor.utils import (
    get_crude_elements,
    get_jura21_helper_and_ai_schemes,
    get_share_info,
    get_subsidiaries_group,
)

logger = logging.getLogger(__name__)


async def _run_share_group(force: bool, file: NewFile):
    need_delete = False
    if await pw_db.exists(FileShareMeta.select().where(FileShareMeta.fid == file.id)):
        if not force:
            return
        need_delete = True

    # 获取相关信息
    file_id = file.id
    file_meta = await pw_db.first(
        HKEXFileMeta.select(HKEXFileMeta.name, HKEXFileMeta.report_year).where(HKEXFileMeta.fid == file_id)
    )
    company, report_year = (file_meta.name, file_meta.report_year) if file_meta else ("", "")
    pdfinsight = PdfinsightReader(file.pdfinsight_path(abs_path=True))
    if not (question := await NewQuestion.find_by_fid_mid(file_id, 15)):
        return None, None
    crude_elements = {
        share_type: get_crude_elements(question, pdfinsight, share_type) for share_type in ("option", "award")
    }
    share_info = get_share_info(pdfinsight, crude_elements)
    helper_crude_elems = await get_jura21_helper_and_ai_schemes(pdfinsight, file)
    from remarkable.predictor.share_group import ShareGroup

    group_info, subsidary_info = {}, {}
    for share_type in ("option", "award"):
        share_group = ShareGroup(pdfinsight, share_type, helper_crude_elems, share_info, report_year, company)
        group = share_group.get_group_info(as_dict=True)
        group_info[share_type] = group
        if subsidiaries := get_subsidiaries_group(
            pdfinsight, share_type, crude_elements, share_info[f"has_share_{share_type}"]
        ):
            subsidary_info[share_type] = subsidiaries
    data = {
        "fid": file_id,
        "share_info": share_info,
        "group_info": group_info,
        "subsidiary_info": subsidary_info,
    }
    async with pw_db.atomic():
        log_str = "insert to"
        if need_delete:
            # force=True时，若记录存在则先删除
            log_str = "overwrite"
            await pw_db.execute(FileShareMeta.delete().where(FileShareMeta.fid == file.id))
        record = await pw_db.create(FileShareMeta, **data)
        logger.info(f"{log_str} file_share_meta: file_id={record.fid}")


@task(
    help={
        "start": "Start of file id range.",
        "end": "End of file id range.",
        "from_file": "Path of file ids file.",
        "force": "If ignore exist record.",
        "workers": "Number of CPU to run.",
    },
    iterable=[
        "report_year",
    ],
)
def run(
    _,
    start=None,
    end=None,
    from_file=None,
    force=True,
    workers=0,
    report_year=None,
):
    """
    生成JURA2.1所需的分组信息并存入file_share_meta表
    """

    file_ids = None
    if from_file:
        with open(from_file, "r", encoding="utf-8") as file_obj:
            file_ids = [int(i.strip()) for i in file_obj if i.strip().isdigit()]

    cond = NewFile.mold_list.contains(special_mold.v1_id) | NewFile.mold_list.contains(special_mold.v2_id)
    if file_ids:
        cond = cond & (NewFile.id.in_(file_ids))
    if start is not None:
        cond = cond & (NewFile.id >= start)
    if end is not None:
        cond = cond & (NewFile.id <= end)
    if report_year:
        cond = cond & (HKEXFileMeta.report_year.in_(report_year))

    with pw_db.allow_sync():
        query = NewFile.select(NewFile.id, NewFile.pdfinsight, NewFile.pdf).where(cond)
        if report_year:
            query = query.join(HKEXFileMeta, on=(NewFile.id == HKEXFileMeta.fid))
        run_in_multiprocess(partial(_run_share_group, force), query.order_by(NewFile.id.desc()), workers=int(workers))


if __name__ == "__main__":
    from invoke import Context

    run(Context(), start=60995, end=60995)
