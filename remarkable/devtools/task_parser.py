import os
from typing import Iterable

import peewee
from invoke import task
from speedy.peewee_plus.orm import TRUE

from remarkable import logger
from remarkable.common.storage import localstorage
from remarkable.db import pw_db
from remarkable.models.hkex_file_meta import HKEXFileMeta
from remarkable.models.new_file import NewFile
from remarkable.models.new_question import NewQuestion
from remarkable.pdfinsight.interdoc_reader import InterdocReader


def dump_interdoc_schema(file: NewFile, force: bool):
    logger.info("rerun interdoc for %s", file.id)
    interdoc_path = os.path.join(file.label_cache_dir, "interdoc.pkl")
    if not force and localstorage.exists(interdoc_path):
        return
    reader = InterdocReader.from_path(file.pdfinsight_path(abs_path=True))
    reader.schema.dump(interdoc_path, with_char=False)


@task(help={"force": "ignore exist interdoc pickle"})
def rerun_interdoc(_, doc_ids: str = "", force: bool = False, mid=0, report_year=0):
    with pw_db.allow_sync():
        cond = NewFile.id.in_([int(i) for i in doc_ids.split(",")]) if doc_ids else TRUE
        query = NewFile.select().order_by(NewFile.id)
        if mid:
            cond &= peewee.fn.EXISTS(
                NewQuestion.select(1).where((NewQuestion.fid == NewFile.id) & (NewQuestion.mold == mid))
            )
        if report_year:
            cond &= peewee.fn.EXISTS(
                HKEXFileMeta.select(1).where(
                    HKEXFileMeta.fid == NewFile.id,
                    HKEXFileMeta.report_year == report_year,
                )
            )
        query = query.where(cond)
        files: Iterable[NewFile] = list(query.execute())
    for file in files:
        dump_interdoc_schema(file, force)


if __name__ == "__main__":
    from invoke import Context

    rerun_interdoc(Context(), doc_ids="66202")
