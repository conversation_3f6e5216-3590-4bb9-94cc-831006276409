import dataclasses
import functools
import gzip
import json
import os
import traceback
from collections import OrderedDict
from copy import deepcopy
from pathlib import Path
from typing import Literal

from aipod.rpc import decode_data, encode_data
from invoke import task
from tornado.ioloop import IOLoop
from utensils.syncer import sync

from remarkable import logger
from remarkable.common.common import Schema
from remarkable.common.constants import MIN_MODIFY_ANSWER_ACTION, PDFParseStatus
from remarkable.common.multiprocess import run_by_batch, run_in_multiprocess
from remarkable.common.util import compact_dumps
from remarkable.config import get_config
from remarkable.data.answer_tools import load_key_path
from remarkable.db import pw_db
from remarkable.devtools import InvokeWrapper
from remarkable.models.answer import Answer
from remarkable.models.ar_result import ARResult
from remarkable.models.hkex_file_meta import HKEXFileMeta
from remarkable.models.new_file import NewFile
from remarkable.models.new_history import NewHistory
from remarkable.models.new_mold import NewMold
from remarkable.models.new_question import NewQuestion
from remarkable.plugins.fileapi.common import make_pdfinsight
from remarkable.predictor.mold_schema import MoldSchema
from remarkable.services.analysis import flatten_line
from remarkable.services.question import UserAnswer, merge_answers
from remarkable.services.rule_manage import rule_to_action
from remarkable.util import md5json

data_dir = get_config("web.data_dir")
mini = get_config("web.data_dir").replace("files", "mini")


@task(help={"since": "timestamp format, for example: 1631775447"})
def make_pdfinsights(
    ctx,
    start=0,
    end=0,
    report_year=0,
    mold=None,
    overwrite=False,
    run_predict=False,
    only_interdoc=False,
    limit=0,
    from_file=None,
    force_ocr=0,
    auto_ocr=1,
    since=0,
    ocr_name=None,
    garbled_file_handle: Literal[0, 2] = 0,
    force_ocr_pages: str = None,  # 强制OCR的页码，多个页码用英文逗号分隔，如"0,1,2"
):
    """
    补充文档的 pdfinsight 信息
    auto_ocr=0，是直接不走ocr的，默认是1，由程序判断是否走ocr
    """

    async def _make_pdfinsights():
        if not from_file:
            query = NewFile.select()
            if report_year:
                query = query.join(HKEXFileMeta, on=(NewFile.id == HKEXFileMeta.fid)).where(
                    HKEXFileMeta.report_year == report_year
                )
            if not overwrite:
                query = query.where(NewFile.pdfinsight.is_null())
            if start:
                query = query.where(NewFile.id >= start)
            if end:
                query = query.where(NewFile.id <= end)
            if mold:
                query = query.where(NewFile.mold_list.contains_any(mold))
            if since:
                query = query.where(NewFile.created_utc >= since)
            query = query.order_by(NewFile.id)
            if limit:
                query = query.limit(limit)
            files = await pw_db.execute(query)
        else:
            with open(from_file, "r") as file_obj:
                fids = [i.strip() for i in file_obj.readlines() if i.strip().isdigit()]

            files = []
            for fid in fids:
                if file := await NewFile.find_by_id(fid):
                    files.append(file)
                else:
                    logger.error("file not exist, fid: %s", fid)
        update_query = NewFile.update(pdf_parse_status=PDFParseStatus.PENDING.value).where(
            NewFile.id.in_([i.id for i in files])
        )
        await pw_db.execute(update_query)
        for file in files:
            logger.info("make pdfinsight: file {}".format(file.id))
            try:
                if only_interdoc and file.pdfinsight_path():
                    make_pdfinsight(
                        file.pdf_path(),
                        file.id,
                        run_predict=False,
                        interdoc=file.pdfinsight_path(),
                        only_interdoc=1,
                        force_ocr=force_ocr,
                        auto_ocr=auto_ocr,
                        ocr_name=ocr_name,
                        mid=mold,
                        garbled_file_handle=garbled_file_handle,
                        force_ocr_pages=force_ocr_pages,
                    )
                else:
                    make_pdfinsight(
                        file.pdf_path(),
                        file.id,
                        run_predict=run_predict,
                        force_ocr=force_ocr,
                        auto_ocr=auto_ocr,
                        ocr_name=ocr_name,
                        garbled_file_handle=garbled_file_handle,
                    )
            except Exception as exp:
                logger.warning(exp)

    IOLoop.current().run_sync(_make_pdfinsights)


@task(klass=InvokeWrapper, iterable=["report_year"])
def inspect_rules(
    ctx,
    start=None,
    end=None,
    mold=None,
    from_file="",
    workers=0,
    answer_type="preset",
    report_year=None,
    # 删除参数use_addition_data https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/5531#note_662338
    # use_addition_data=False,
):
    from remarkable.devtools.task_inspector import run

    run(ctx, start, end, mold, from_file, workers, answer_type, report_year)


@task
def fix_lost_answers(ctx, mold, domain="http://jura.paodingai.com", logpath="/tmp/lost_attrs.txt"):
    async def _run(mold, domain=None):
        cnt = 0

        def build_schema(schema_info):
            data = {
                "type": schema_info.get("type"),
                "label": schema_info.get("name"),
                "words": schema_info.get("words", ""),
                "multi": schema_info.get("multi"),
                "required": schema_info.get("required"),
            }
            return {"data": data}

        def build_col(schema_info, parent_path, index_l):
            schema = build_schema(schema_info)
            path_l = deepcopy(parent_path)
            path_l.append(schema_info["name"])

            col = {
                "schema": schema,
                "data": [],
                "key": compact_dumps([f"{path}:{idx}" for path, idx in zip(path_l, index_l)]),
                "value": "no disclosure",
            }
            return col

        query = (
            NewFile.select(
                Answer.id,
                NewFile.id.alias("fid"),
                NewFile.name,
                NewFile.pid,
                NewFile.tree_id,
                NewFile.qid,
                Answer.data,
            )
            .join(Answer, on=(NewFile.qid == Answer.qid))
            .where(Answer.data.is_null(False), NewFile.mold == mold, NewFile.id >= 200)
            .order_by(NewFile.id)
        )

        while True:
            if not (data := await pw_db.execute(query.tuples())):
                break
            a_id, f_id, f_name, pid, tree_id, qid, answer = data[0]
            print("checking ... file_id: %s" % f_id)

            answer_items = {
                item["schema"]["data"]["label"]: item for item in answer.get("userAnswer", {}).get("items", [])
            }
            if not answer_items:
                continue
            from remarkable.common.common import HKEXSchema as HkSchema

            hkex_schema = HkSchema(answer["schema"])
            lost_attrs = []
            lost_items = []
            scope = [
                "A10.4",
                "A10.6",
                "A11.1",
                "A11.2",
                "A23",
                "A24",
                "A25",
                "A26",
                "A27",
                "A28.1",
                "A28.2",
                "A28.3",
                "A29.1",
                "A29.2",
                "A29.3",
                "A30",
                "A31",
                "A32",
                "A33",
            ]
            schema_dict = {schema["name"]: schema for schema in answer["schema"]["schemas"]}
            root_schema_name = answer["schema"]["schemas"][0]["name"]

            for col_name in answer["schema"]["schemas"][0]["orders"]:
                for attr in hkex_schema.root_rules_map.get(col_name, []):
                    attr_name = hkex_schema.name_rule_map.get(attr, attr)
                    if "(" in attr_name:
                        continue
                    if attr_name not in scope:
                        continue
                    if attr not in answer_items:
                        lost_attrs.append((attr_name, attr))
                        if col_name == attr:  # 一级属性
                            col_attributes = deepcopy(schema_dict[root_schema_name]["schema"][col_name])
                            col_attributes.update({"name": col_name})
                            col = build_col(col_attributes, [root_schema_name], index_l=("0", "0"))
                        else:  # 二级属性
                            col_attributes = deepcopy(schema_dict[col_name]["schema"][attr])
                            col_attributes.update({"name": attr})
                            col = build_col(col_attributes, [root_schema_name, col_name], ("0", "0", "0"))
                        lost_items.append(col)

            if lost_attrs:
                cnt += 1
                file_url = "%s/#/project/remark/%s?treeId=%s&fileId=%s&schemaId=%s&projectId=%s&fileName=%s" % (
                    domain,
                    qid,
                    tree_id,
                    f_id,
                    mold,
                    pid,
                    f_name,
                )
                print("-----------------" * 5, cnt, f_id, file_url)

                # 修改答案
                logger.info("update answer for file: %s", f_id)
                answer.get("userAnswer", {}).get("items", []).extend(lost_items)
                await pw_db.execute(Answer.update(data=answer).where(Answer.id == a_id))
                # 记录日志
                with open(logpath, "a") as log_f:
                    log_f.write("-----------------" * 5 + "count=%s fid=%s" % (cnt, f_id) + "\n")
                    log_f.write(file_url + "\n")
                    for x in lost_attrs:
                        log_f.write("lost_attr：%s_%s" % (x[0], x[1]) + "\n")

    IOLoop.current().run_sync(lambda: _run(mold=mold, domain=domain))


async def _merge_answer(qid, clean, answer_dir):
    if clean:
        await pw_db.execute(NewQuestion.update(answer=None).where(NewQuestion.id == qid))
    question = await NewQuestion.find_by_id(qid)
    logger.info(f"fid: {question.fid}, qid: {qid} Merging...")
    await question.set_answer()
    answer_path = os.path.join(answer_dir, str(question.id) + ".json.gz")
    if os.path.exists(answer_path):
        # Merge answer from file
        answers = []
        with gzip.open(answer_path, "rt") as file_obj:
            answer = json.loads(file_obj.read())
            answers.append(UserAnswer._make([1, "admin", answer]))
        answers.append(UserAnswer._make([1, "admin", question.answer]))
        question.answer = merge_answers(answers)
        await pw_db.update(question)


@task(iterable=["molds", "report_years"])
def set_question_answer(_, molds, start=0, end=0, report_years=None, project=0, workers=0, clean=False, answer_dir=""):
    """批量合并答案到question.answer中"""
    assert molds, "molds is required"
    cond = (NewFile.deleted_utc == 0) & (NewQuestion.mold.in_([int(m) for m in molds]))
    if start > 0:
        cond &= NewFile.id >= start
    if end > 0:
        cond &= NewFile.id <= end
    if project:
        cond &= NewFile.pid == project
    query = NewQuestion.select(NewQuestion.id).join(NewFile, on=(NewQuestion.fid == NewFile.id))
    if report_years:
        cond &= HKEXFileMeta.report_year.in_(report_years)
        query = query.join(HKEXFileMeta, on=(NewQuestion.fid == HKEXFileMeta.fid))
    query = query.where(cond).order_by(NewFile.id.desc())

    tasks = []
    with pw_db.allow_sync():
        for question in query:
            tasks.append((question.id, clean, answer_dir))

    if tasks:
        run_in_multiprocess(_merge_answer, tasks, workers=workers)
    else:
        logger.warning("No question found")


@task
def del_conf_comment(ctx, conf_path):
    """删除配置文件中的注释内容"""
    with open(conf_path, "r") as in_file, open(conf_path + ".out", "w") as out_file:
        for line in in_file:
            if line.startswith("#"):
                continue
            text = line.split("#")[0].rstrip()
            out_file.write(text + "\n")
    logger.info("All comments removed!")


@task
def export_marker_info(ctx, start=0, end=0, mid=0, workers=0, dst_path="xls_export"):
    """批量导出标注人员信息"""
    with pw_db.allow_sync():
        query = NewFile.select()
        if mid:
            query = query.where(NewFile.mold == mid)
        if start:
            query = query.where(NewFile.id >= start)
        if end:
            query = query.where(NewFile.id <= end)
        if any([start, end, mid]):
            files = query
        else:
            files = []
        if len(files) == 0:
            logger.info("没有查找到相关文件")
            return
    run_in_multiprocess(functools.partial(_export_marker_info, dst_path), files, workers=workers, debug=True)


def level1_answer_groups(answer):
    groups = OrderedDict()
    for item in answer.get("userAnswer", {}).get("items", []):
        keypath = [p.split(":")[0] for p in json.loads(item["key"])]
        groups.setdefault(keypath[1], [item["marker"]["name"]] + item["marker"]["others"])
    return groups


async def _export_marker_info(dst_path, file):
    merged_answer = {}
    try:
        if file.qid:
            logger.info("export marker info for file: %s", file.id)
            question = await NewQuestion.find_by_id(file.qid)
            merged_answer = await question.set_answer()
    except Exception as ex:
        traceback.print_exc()
        logger.error("error in export marker info for file %s, %s", file.id, ex)

    if merged_answer.get("userAnswer", {}).get("version", "0") < "2.0":
        logger.error("can't merge answer version below than 2.0")
        return
    if not os.path.exists(dst_path):
        os.mkdir(dst_path)
    filepath = os.path.join(dst_path, "{}_{}.csv".format(file.id, file.name.lower().rstrip(".pdf")).replace("/", "-"))
    if os.path.exists(filepath):
        os.remove(filepath)

    with open(filepath, "w", encoding="gbk") as csv_fh:
        for attr, names in level1_answer_groups(merged_answer).items():
            csv_fh.write("{},".format(attr) + ",".join(names))
            csv_fh.write("\n")


@dataclasses.dataclass
class AnswerData:
    id: int
    fid: int  # 对于 history 表，fid 为 action
    table: str
    col: str
    data: dict
    modified_keys: dict[str, str] = dataclasses.field(default_factory=dict)
    deleted_keys: list[str] = dataclasses.field(default_factory=list)

    async def backup(self, work_dir: str):
        work_dir = Path(work_dir)
        (work_dir / f"{self.table}_{self.col}_{self.id}.json.zst").write_bytes(encode_data(dataclasses.asdict(self)))
        if self.table == "question":
            if crude_answer := await pw_db.scalar(
                NewQuestion.select(NewQuestion.crude_answer).where(NewQuestion.id == self.id)
            ):
                (work_dir / f"{self.table}_crude_answer_{self.id}.json.zst").write_bytes(
                    encode_data({**dataclasses.asdict(self), "data": crude_answer, "col": "crude_answer"})
                )
            for history in await pw_db.execute(
                NewHistory.select(NewHistory.id, NewHistory.action, NewHistory.meta).where(
                    NewHistory.qid == self.id, NewHistory.action >= MIN_MODIFY_ANSWER_ACTION
                )
            ):
                # 保存答案提交相关的历史记录
                (work_dir / f"history_meta_{history.id}.json.zst").write_bytes(
                    encode_data(
                        {
                            "id": history.id,
                            "fid": history.action,
                            "table": "history",
                            "col": "meta",
                            "data": history.meta,
                        }
                    )
                )

    async def update_db(self):
        logger.info(f"update {self.table=} {self.id=} {self.col=}")
        match self.table:
            case "history":
                await pw_db.execute(NewHistory.update(meta=self.data, action=self.fid).where(NewHistory.id == self.id))
            case "answer":
                await pw_db.execute(Answer.update(data=self.data).where(Answer.id == self.id))
            case "question":
                await pw_db.execute(NewQuestion.update(**{self.col: self.data}).where(NewQuestion.id == self.id))
                sqls = []
                rules = []
                if modified_keys := {
                    k.replace("'", "''"): v.replace("'", "''")
                    for k, v in self.modified_keys.items()
                    if k not in self.deleted_keys
                }:
                    logger.info(f"{modified_keys=}")
                    for k, v in modified_keys.items():
                        # B1-xxx 替换为 B2-xxx
                        k = k.split("-", maxsplit=1)[0]
                        rules.append(k)
                        main_rule, sub_rule = v.split("-", maxsplit=1)
                        action = await rule_to_action(main_rule, self.fid)
                        sqls.append(f"""
                            UPDATE history
                            SET meta = jsonb_set(meta, '{{rule}}', '"{main_rule}"', false), action = {action}
                            WHERE qid = :qid AND meta ->> 'rule' = '{k}' AND action >= {MIN_MODIFY_ANSWER_ACTION}
                            RETURNING 1
                        """)
                        sqls.append(f"""
                            UPDATE history
                            SET meta = jsonb_set(meta, '{{rule_subtab}}', '"{sub_rule}"', false)
                            WHERE qid = :qid AND meta ->> 'rule' = '{main_rule}' AND action >= {MIN_MODIFY_ANSWER_ACTION}
                            RETURNING 1
                        """)
                    sqls.append(f"""
                        WITH key_map(old_key, new_key) AS (
                            VALUES
                            {", ".join(f"('{k}', '{v}')" for k, v in modified_keys.items()) or "('dummy', 'dummy')"}
                        )
                        UPDATE question
                        SET crude_answer = (
                            SELECT json_object_agg(COALESCE(km.new_key, j.key), j.value)
                            FROM json_each(crude_answer) AS j
                            LEFT JOIN key_map km ON j.key = km.old_key
                        )
                        WHERE id = :qid
                        RETURNING 1
                    """)
                if self.deleted_keys:
                    rules.extend(k.split("-", maxsplit=1) for k in self.deleted_keys)
                    self.deleted_keys = [k.replace("'", "''") for k in self.deleted_keys]
                    logger.info(f"{self.deleted_keys=}")
                    sqls.append(f"""
                        DELETE FROM history WHERE qid = :qid AND action >= {MIN_MODIFY_ANSWER_ACTION} AND meta ->> 'rule' IN ({", ".join(f"'{k}'" for k in self.deleted_keys)}) returning id
                    """)
                    sqls.append(f"""
                        UPDATE question
                        SET crude_answer = (
                            SELECT json_object_agg(key, value)
                            FROM json_each(crude_answer)
                            WHERE key NOT IN ({", ".join(f"'{k}'" for k in self.deleted_keys)}))
                        WHERE id = :qid
                        RETURNING 1
                    """)

                for sql in sqls:
                    await pw_db.execute(sql, {"qid": self.id})

                if rules:
                    # 暂时只清年报的中间结果
                    # 直接粗暴拍平先，正常情况这里一定是一维数组，不会有嵌套。
                    rules = list(flatten_line(rules))
                    await pw_db.execute(
                        ARResult.delete().where(ARResult.file_id == self.fid, ARResult.compliance_answer_key.in_(rules))
                    )


def _key2aid(key: str) -> str:
    return "-".join(k.name for k in load_key_path(key)[1:])


async def _migrate_answer_schema(mid: int, qid: int, attrs_map=None, safe_mode=False, overwrite=False, work_dir=""):
    attrs_map = attrs_map or {}
    dst_mold = await NewMold.get_by_id(mid)
    aim_mold_schema = MoldSchema(dst_mold.data)
    aim_schema = Schema(dst_mold.data)
    answers = [AnswerData(answer.id, 0, "answer", "data", answer.data) for answer in await Answer.find_by_qid(qid)]
    if question := await NewQuestion.find_by_id(qid):
        if question.preset_answer:
            answers.append(AnswerData(question.id, question.fid, "question", "preset_answer", question.preset_answer))
        if question.answer:
            answers.append(AnswerData(question.id, question.fid, "question", "answer", question.answer))

    async with pw_db.atomic():
        for answer in answers:
            # 先备份
            await answer.backup(work_dir)

            if answer.data["schema"].get("version") == dst_mold.checksum and not overwrite:
                logger.info("%s %s has same schema version", answer.table, answer.id)
                continue

            for is_comp_key, part in [(False, "userAnswer"), (True, "rule_result")]:
                del_items = []
                remained_items = {}
                conflict_items = {}
                answer_map = {item["key"]: item for item in answer.data.get(part, {}).get("items", [])}
                for item in answer_map.values():
                    # 先替换，尽可能保留原字段数据
                    conflict = False
                    key_paths = load_key_path(item["key"])
                    for older, newer in attrs_map.items():
                        if is_comp_key:
                            # 合规 key 只有一级
                            older = older.split("|")[0]
                            newer = newer.split("|")[0]
                            if older == newer:
                                continue

                        old_paths = older.split("|")
                        new_paths = newer.split("|")
                        if any(p.name in old_paths for p in key_paths[1:]):
                            for idx, p in enumerate(new_paths, 1):
                                key_paths[idx].name = p
                            newer_key = compact_dumps([f"{i.name}:{i.index}" for i in key_paths[: len(new_paths) + 1]])
                            conflict = newer_key in answer_map
                            if not is_comp_key and not conflict:
                                answer.modified_keys.setdefault(_key2aid(item["key"]), _key2aid(newer_key))
                            item["key"] = newer_key
                            break

                    if conflict and (item.get("special_ui", False) or item.get("manual", False)):
                        conflict_items[item["key"]] = item

                    # 再检查是否需要删除
                    keep, key_str = aim_schema.contains_path(item["key"], skip_root=True, is_comp_key=is_comp_key)
                    if not keep or conflict:
                        del_items.append(item)
                    else:
                        # 替换后还在新 schema 中
                        item["key"] = key_str
                        # 更新可能的描述/类型等信息
                        item["schema"] = aim_mold_schema.find_schema_by_path(
                            key_str, is_comp_key=is_comp_key
                        ).to_answer_data()
                        remained_items[key_str] = item

                if del_items:
                    answer.deleted_keys.extend(_key2aid(item["key"]) for item in del_items)
                    logger.info(
                        f"[{answer.table}.{answer.col}|{answer.fid}:{answer.id}|{part}] wants to remove "
                        + "\n".join(item["key"] for item in del_items)
                    )
                    logger.info(f"answer type: {part}, {answer.table=}, {len(remained_items)} items remain")

                    for key, item in conflict_items.items():
                        if (
                            key in remained_items
                            and not remained_items[key].get("special_ui", False)
                            and not remained_items[key].get("manual", False)
                        ):
                            # 如果冲突字段答案非人工标注，则保留
                            remained_items[key] = item

                    answer.data[part]["items"] = list(remained_items.values())
                    answer.data[part].setdefault("version", "2.2")

            if safe_mode:
                logger.warning(f"[{answer.table} id: {answer.id}] running in safe mode, pass this migration")
            else:
                answer.data["schema"] = deepcopy(dst_mold.data)
                answer.data["schema"]["version"] = dst_mold.checksum
                if attrs_map:
                    # 强制补充待修改字段映射，避免遗漏（用户删答案的情况）
                    # https://mm.paodingai.com/cheftin/pl/gspmpf184jdyp8eymxo6byk51c
                    answer.modified_keys.update(
                        {k.replace("|", "-"): v.replace("|", "-") for k, v in attrs_map.items()}
                    )
                await answer.update_db()
                logger.info("update %s %s schema to version %s", answer.table, answer.id, dst_mold.checksum)


@task
def migrate_answer_schema(
    ctx,
    mid=0,
    start=-1,
    end=-1,
    aim_mid=0,
    workers=0,
    attrs_map_path="",
    work_dir="data/tmp/migrate_answer_schema",
    overwrite=False,
    safe_mode=False,
):
    """批量迁移答案 schema
    TODO: 更新esg_result表中rule字段
    NOTE: "attrs_map_path"必须是 json 格式, key 为旧字段, value 为新字段
    """
    if not aim_mid:
        aim_mid = mid

    if not os.path.exists(work_dir):
        os.makedirs(work_dir)

    attrs_map = None
    if attrs_map_path and Path(attrs_map_path).exists():
        with open(attrs_map_path, "r") as file_obj:
            attrs_map = json.load(file_obj)

    def update_mold(mold_id):
        mold = NewMold.select().where(NewMold.id == mold_id).first()
        checksum = md5json(mold.data)
        if mold.checksum != checksum:
            logger.warning("Update checksum for mold %s", mold_id)
            NewMold.update(checksum=checksum).where(NewMold.id == mold_id).execute()
            mold.checksum = checksum
        return mold

    cond = NewQuestion.mold == mid
    if start > 0:
        cond &= NewFile.id >= start
    if end > 0:
        cond &= NewFile.id <= end
    stmt = (
        NewFile.select(NewQuestion.id)
        .join(NewQuestion, on=(NewQuestion.fid == NewFile.id))
        .where(cond)
        .order_by(NewFile.id.desc())
    )
    tasks = []
    with pw_db.allow_sync():
        update_mold(aim_mid)
        for file in stmt.execute():
            tasks.append((aim_mid, file.newquestion.id, attrs_map or {}, safe_mode, overwrite, work_dir))
    for _ in run_by_batch(_migrate_answer_schema, tasks=tasks, workers=workers):
        pass


@task
@sync
async def restore_tables(ctx, path):
    """从备份文件夹恢复表数据"""
    for p in Path(path).rglob("*.json.zst"):
        data = decode_data(p.read_bytes())
        await AnswerData(**data).update_db()
