import asyncio
import calendar
import collections
import csv
import gzip
import json
import logging
import os
import tempfile
import zipfile
from copy import copy
from datetime import datetime, timedelta
from pathlib import Path
from typing import Any, AsyncGenerator, Literal

import msgspec
import openpyxl
import pandas as pd
import peewee
from invoke import task
from openpyxl.cell.cell import ILLEGAL_CHARACTERS_RE
from openpyxl.styles import Alignment, Font
from playhouse.postgres_ext import ServerSide
from pydantic import BaseModel, ConfigDict, Field, field_validator
from utensils.syncer import sync
from utensils.util import generate_timestamp
from utensils.zip import ZipFilePlus, read_zip_first_file

from remarkable import db
from remarkable.answer_exporter.cg_stat import CgStat
from remarkable.answer_exporter.common_stat import CommonStat
from remarkable.answer_exporter.esg_stat import EsgStat
from remarkable.answer_exporter.jura21_stat import Jura21Stat
from remarkable.answer_exporter.policy_esg_stat import PolicyEsgStat
from remarkable.common.common import JURA6_C2_RULES
from remarkable.common.constants import DocType, RuleOperation
from remarkable.common.hkex_util import dump_data_to_worksheet
from remarkable.common.multiprocess import run_by_batch
from remarkable.common.storage import tmp_dir_storage
from remarkable.common.util import DateTimeUtil, is_main_stock
from remarkable.config import project_root
from remarkable.db import embedding_pw_db, init_rdb, pw_db
from remarkable.devtools import InvokeWrapper, read_ids_from_file
from remarkable.models.accuracy_record import AccuracyRecord
from remarkable.models.agm_meta import AGMMeta
from remarkable.models.agm_result import AGMResult
from remarkable.models.answer import Answer
from remarkable.models.disclosure_result import DisclosureResult
from remarkable.models.file_esg_xref import FileESGxREF
from remarkable.models.hkex_file import HKEXFile
from remarkable.models.hkex_file_meta import HKEXFileMeta
from remarkable.models.mold import special_mold
from remarkable.models.new_file import NewFile
from remarkable.models.new_mold import NewMold
from remarkable.models.new_question import NewQuestion, NewQuestionWithFile
from remarkable.models.rule_reference import RuleReference
from remarkable.models.special_answer import NewSpecialAnswer
from remarkable.models.user import AdminUser
from remarkable.optools.fm_upload import FMUploader
from remarkable.optools.stat_util import Url
from remarkable.pdfinsight.reader import PdfinsightReader
from remarkable.plugins.hkex.utils import standard_stock
from remarkable.services.director import fetch_and_update_directors, set_agm_meta
from remarkable.services.embedding import create_and_save_embeddings
from remarkable.services.esg import gen_pre_trial_data
from remarkable.services.feishu import FeiShuTableBot
from remarkable.services.mr import set_mr_meta
from remarkable.services.poll import set_poll_meta
from remarkable.services.stock import (
    get_stock_year_end_month_map,
    update_company_info_task,
)

logger = logging.getLogger(__name__)


@task()
def modify_predictor_config(ctx, mid, version="2.0"):
    """
    修改指定mold的 predictor_option
    """

    async def run():
        mold = await NewMold.find_by_id(mid)
        if not mold:
            logger.info(f"mold：{mid} not found")
        mold.predictor_option = {"framework_version": version}
        await pw_db.update(mold)
        logger.info(f"mold：{mid} update framework_version to {version}, success")

    asyncio.get_event_loop().run_until_complete(run())


@task(klass=InvokeWrapper, iterable=["molds"])
async def copy_manual_to_preset(ctx, fid, molds=None):
    file = await NewFile.find_by_id(fid)
    if not file:
        logger.info("file not found")
        return
    for mid in molds:
        question = await NewQuestion.find_by_fid_mid(fid, mid)
        if not question:
            logger.info(f"question not found for mold: {mid}")
            continue
        preset_answer = question.answer
        for answer_type in ("userAnswer", "rule_result"):
            for item in preset_answer[answer_type]["items"]:
                item.pop("special_ui", None)
                item.pop("manual", None)
                item.pop("marker", None)
                item.pop("md5", None)

        question.preset_answer = preset_answer
        await pw_db.update(question)
        logger.info(f"update preset_answer for question: {question.id}")


@task(klass=InvokeWrapper, iterable=["ids"])
async def export_schemas(ctx, ids, path="", comment=""):
    """导出指定id(不指定导出全部)的schema"""
    cond = NewMold.deleted_utc == 0
    if ids:
        cond &= NewMold.id.in_([int(i) for i in ids])
    query = (
        NewMold.select(
            NewMold.id,
            NewMold.checksum,
            NewMold.name,
            NewMold.data,
            NewMold.created_utc,
            NewMold.updated_utc,
        )
        .where(cond)
        .order_by(NewMold.id)
        .dicts()
    )
    molds = list(await pw_db.execute(query))
    path = path or f"schema_{'_'.join(ids)}.zip"
    with ZipFilePlus(path, mode="w") as zip_fp:
        zip_fp.writestr(
            "mold.json", json.dumps(molds, ensure_ascii=False, indent=4), compress_type=zipfile.ZIP_DEFLATED
        )
        zip_fp.comment = (comment or path).encode()
    logger.info(f"export schemas to {path}, success")


@task(klass=InvokeWrapper)
async def import_schemas(ctx, path="molds.zip", mid=None, mold_name=None):
    """导入指定文件中的schema数据，mid指定导入的mold id，mold_name指定导入的mold name"""
    if zipfile.is_zipfile(path):
        molds = json.loads(read_zip_first_file(path).decode())
    else:
        with gzip.open(path, "rt") as file_obj:
            molds = json.load(file_obj)
    for mold in molds:
        if mid and mold["id"] != int(mid):
            continue
        if mold_name and mold["name"] != mold_name:
            continue
        existed_target = await NewMold.find_by_name(mold["name"])
        assert not existed_target or existed_target.id == mold["id"], f"Conflict mold found: {existed_target.to_dict()}"
        new_inst = await NewMold.insert_or_update(**mold)
        logger.info(f"import mold: {new_inst.name}({new_inst.id})")
    logger.info(f"import schemas from {path}, success")


def optimize_xls_style(workbook):
    width_maps = {
        "A": 50,
        "B": 15,
        "C": 15,
        "D": 15,
        "E": 15,
        "F": 15,
        "G": 15,
        "H": 17,
        "I": 17,
        "J": 17,
        "K": 20,
        "L": 17,
        "M": 23,
    }
    worksheet = workbook.active
    for col_index, width in width_maps.items():
        worksheet.column_dimensions[col_index].width = width
        for col in worksheet.iter_cols():
            for cell in col:
                cell.font = Font(size=18)
                cell.alignment = Alignment(horizontal="center", vertical="center", wrap_text=True)


@task(klass=InvokeWrapper)
async def diff_accuracy(ctx, mid):
    """
    比较最近两次模型统计结果
    """
    cols = ["total", "predict", "match", "rate", "precision"]
    records = await pw_db.execute(
        AccuracyRecord.select().where(AccuracyRecord.mold == mid).order_by(AccuracyRecord.created_utc.desc()).limit(2)
    )
    new_record, old_record = records
    logger.info("diff start ...")
    logger.info(f"new_record create_time：{DateTimeUtil.timestamp2str(new_record.created_utc)}")
    logger.info(f"old_record create_time：{DateTimeUtil.timestamp2str(old_record.created_utc)}")
    old_result = {x["name"]: x for x in old_record.data["result"]}
    new_result = {x["name"]: x for x in new_record.data["result"]}
    for name, item in old_result.items():
        new_item = new_result.get(name)
        if not new_item:
            continue
        for col in cols:
            item[f"new_{col}"] = new_item[col]
            item[f"{col}_change"] = item[f"new_{col}"] - item[col]

    results = sorted(old_result.values(), key=lambda x: x["rate_change"])  # 召回率降低的排前面
    excel_cols = ["name"]
    excel_cols.extend(cols)
    excel_cols.extend((f"new_{col}" for col in cols))
    excel_cols.extend(("rate_change", "precision_change"))

    export_data = []
    for result in results:
        data = []
        for col in excel_cols:
            data.append(result.get(col))
        export_data.append(data)

    sub_total_row = [
        "TOTAL",
        old_record.data["total"],
        old_record.data["total_predict"],
        old_record.data["total_fit"],
        old_record.data["total_percent"],
        old_record.data["total_precision"],
        new_record.data["total"],
        new_record.data["total_predict"],
        new_record.data["total_fit"],
        new_record.data["total_percent"],
        new_record.data["total_precision"],
        new_record.data["total_percent"] - old_record.data["total_percent"],
        new_record.data["total_precision"] - old_record.data["total_precision"],
    ]
    export_data.append([])
    export_data.append(sub_total_row)
    export_data.append(
        [
            "left time",
            DateTimeUtil.timestamp2str(old_record.created_utc),
            "right time",
            DateTimeUtil.timestamp2str(new_record.created_utc),
        ]
    )

    workbook = openpyxl.Workbook()
    dump_data_to_worksheet(workbook, excel_cols, export_data)
    excel_path = "AccuracyRecord.xlsx"
    optimize_xls_style(workbook)
    workbook.save(excel_path)

    logger.info("diff end ...")
    logger.info("upload to fm.paodingai.com ...")
    FMUploader().upload(Path(excel_path))

    logger.info(f"remove file {excel_path}...")
    os.remove(excel_path)


async def query_stock_year(stock_year, mid, condition):
    stock, condition_value = stock_year.split()
    cond = [
        (HKEXFileMeta.stock_code == standard_stock(stock)),
        (NewQuestion.mold == mid),
        getattr(HKEXFileMeta, condition) == condition_value,
    ]

    query = HKEXFileMeta.select(HKEXFileMeta.fid).join(NewQuestion, on=(HKEXFileMeta.fid == NewQuestion.fid))
    if int(mid) in special_mold.esg_mids_with_policy:
        query = query.join(FileESGxREF, on=(FileESGxREF.fid == HKEXFileMeta.fid))
        cond.append(FileESGxREF.activated)

    return await HKEXFileMeta.manager().execute(query.where(*cond))


@task(klass=InvokeWrapper)
async def gen_fids(ctx, mid, from_file, condition: Literal["report_year", "published"] = "report_year"):
    """
    根据提供的stock/report_year生成对应的fid，用于下一步处理流程
    from_file 中每一行分别是 stock_code 以及 report_year, 使用tab隔开
    """
    with open(from_file, "r", encoding="utf-8") as file_obj:
        stock_year_list = [i.strip() for i in file_obj if i.strip()]

    tasks = [query_stock_year(stock_year, mid, condition) for stock_year in stock_year_list]
    results = await asyncio.gather(*tasks)

    fids = []
    for result in results:
        for row in result:
            fids.append(f"{str(row.fid)}\n")

    output_path = Path(tmp_dir_storage.mount("")) / "fids"
    with open(output_path, "w") as file_obj:
        file_obj.writelines(fids)

    logger.info(f"fids is generated, {output_path}")


@task
@sync
async def gen_urls(ctx, from_file, mid=None, schema_word=None, custom_ui=True):
    with open(from_file, "r", encoding="utf-8") as file_obj:
        fids = [i.strip() for i in file_obj]
    workbook = openpyxl.Workbook()
    worksheet = workbook.active
    for fid in fids:
        question = await NewQuestionWithFile.find_by_fid_mid(fid, mid)
        if not question:
            continue
        url = Url(fid, mid, question.id, question.file.tree_id, schema_key=schema_word, custom_ui=custom_ui)

        worksheet.append([str(url)])
    with tempfile.NamedTemporaryFile(prefix="urls", suffix=".xlsx") as tmp_fp:
        excel_path = tmp_fp.name
        workbook.save(excel_path)
        FMUploader().upload(Path(excel_path))


@task(klass=InvokeWrapper)
async def stat_accuracy_for_esg(ctx, from_file):
    """
    JURA4-code按需导出集合数据，为计算准确率
    https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/1213
    """
    with open(from_file, "r", encoding="utf-8") as file_obj:
        fids = file_obj.readlines()
    tasks = [int(i.strip()) for i in fids]
    answers = collections.defaultdict(dict)
    fid_stock_map = {}
    for fid in tasks:
        esg_stat = EsgStat(fid)
        stock_code = await esg_stat.stock_code()
        fid_stock_map[fid] = stock_code
        res = await esg_stat.stat_accuracy_info()
        for rule, item in res.items():
            answers[rule].update({stock_code: item})

    index_row = [str(index) for index, fid in enumerate(tasks, start=1)]
    stock_code_row = [fid_stock_map[fid] for index, fid in enumerate(tasks)]
    stock_code_row.insert(0, "Rule")

    workbook = openpyxl.Workbook()
    for sheet_index, sheet_name in enumerate(
        ["AI Classification", "Manual Classification", "AI Tagging", "Manual Tagging", "AI Location Result"]
    ):
        worksheet = workbook.create_sheet(sheet_name, sheet_index)
        first_row = copy(index_row)
        first_row.insert(0, sheet_name)
        worksheet.append(first_row)
        worksheet.append(stock_code_row)
        for rule, rule_answer in answers.items():
            row = [rule_answer.get(stock_code, {}).get(sheet_name, "") for stock_code in stock_code_row[1:]]
            row = [ILLEGAL_CHARACTERS_RE.sub(" ", x) if isinstance(x, str) else x for x in row]
            row.insert(0, rule)
            worksheet.append(row)

    with tempfile.NamedTemporaryFile(prefix="HKEX_ESG_STAT", suffix=".xlsx") as tmp_fp:
        excel_path = tmp_fp.name
        workbook.save(excel_path)
        logger.info(f"run completed, {excel_path}")
        logger.info("upload to fm.paodingai.com ...")
        FMUploader().upload(Path(excel_path))

    gen_calculation_result(answers, index_row, stock_code_row)


def gen_calculation_result(answers, index_row, stock_code_row):
    answer_key_map = {
        "RAW-Classification-B2": {
            3: "AI Classification",
            70: "Manual Classification",
        },
        "RAW - Location -B2": {
            3: "AI Tagging",
            70: "Manual Tagging",
        },
    }
    template_path = Path(project_root) / "data/hkex/UAT_Batch_2_Precision_Recall_Calculation_Template.xlsx"
    workbook = openpyxl.load_workbook(template_path)
    for sheet_name in ("RAW-Classification-B2", "RAW - Location -B2"):
        worksheet = workbook[sheet_name]
        for start, end in ((3, 68), (70, 135)):
            for i in range(2, worksheet.max_column + 1):
                value = None
                if i - 2 < len(index_row):
                    value = index_row[i - 2]
                worksheet.cell(row=start - 2, column=i).value = value
            for i in range(2, worksheet.max_column + 1):
                value = None
                if i - 2 < len(index_row):
                    value = stock_code_row[i - 1]
                worksheet.cell(row=start - 1, column=i).value = value
            for i in range(start, end):
                rule_name = worksheet.cell(row=i, column=1).value
                rule_answer = answers[rule_name]
                for j in range(2, worksheet.max_column + 1):
                    stock_code = standard_stock(str(worksheet.cell(row=2, column=j).value))
                    new_value = rule_answer.get(stock_code, {}).get(answer_key_map[sheet_name][start], "")
                    worksheet.cell(row=i, column=j).value = new_value
        if sheet_name == "RAW - Location -B2":
            for row_idx in range(137, 202):
                for i in range(2, worksheet.max_column + 1):
                    value = None
                    if i - 2 < len(index_row):
                        value = index_row[i - 2]
                    worksheet.cell(row=135, column=i).value = value
                for i in range(2, worksheet.max_column + 1):
                    value = None
                    if i - 2 < len(index_row):
                        value = stock_code_row[i - 1]
                    worksheet.cell(row=136, column=i).value = value
                rule_name = worksheet.cell(row=row_idx, column=1).value
                rule_answer = answers[rule_name]
                for col_idx in range(2, worksheet.max_column + 1):
                    stock_code = standard_stock(str(worksheet.cell(row=2, column=col_idx).value))
                    new_value = rule_answer.get(stock_code, {}).get("AI Location Result", "")
                    worksheet.cell(row=row_idx, column=col_idx).value = new_value

    with tempfile.NamedTemporaryFile(prefix="UAT_Batch_2_Precision_Recall_Calculation", suffix=".xlsx") as tmp_fp:
        excel_path = tmp_fp.name
        workbook.save(excel_path)
        logger.info(f"run completed, {excel_path}")
        logger.info("upload to fm.paodingai.com ...")
        FMUploader().upload(Path(excel_path))


@task(klass=InvokeWrapper)
async def export_pre_trial_data(ctx, report_year="2022", month=0):
    """导出E&ND比例超过15%的数据"""
    workbook = openpyxl.Workbook()
    worksheet = workbook.active
    worksheet.append(["Stock Code", "Report Year", "Nums of E", "E Rules", "Nums of ND", "ND Rules", "Jura URL"])
    cond = HKEXFileMeta.report_year == report_year
    cond &= NewQuestion.mold.in_((1, 2))
    cond &= FileESGxREF.activated
    if month:
        start_stamp, end_stamp = get_month_timestamps(month)
        cond &= NewQuestion.created_utc >= start_stamp
        cond &= NewQuestion.created_utc <= end_stamp
    query = (
        HKEXFileMeta.select()
        .join(NewQuestion, on=(HKEXFileMeta.fid == NewQuestion.fid))
        .join(FileESGxREF, on=(HKEXFileMeta.fid == FileESGxREF.fid))
        .where(cond)
        .order_by(HKEXFileMeta.qid.desc())
    )
    for hkex_file_meta in await HKEXFileMeta.manager().execute(query):
        question = await NewQuestion.find_by_id(hkex_file_meta.qid)
        if not question or not question.preset_answer:
            continue
        count_res = gen_pre_trial_data(question, hkex_file_meta)
        if not count_res:
            continue
        worksheet.append(count_res)

    with tempfile.NamedTemporaryFile(prefix="E&ND num", suffix=".xlsx") as tmp_fp:
        excel_path = tmp_fp.name
        workbook.save(excel_path)
        FMUploader().upload(Path(excel_path))


def get_month_timestamps(month):
    # 获取指定年月的第一天和最后一天
    current_year = datetime.now().year
    first_day = datetime(current_year, month, 1)
    _, last_day = calendar.monthrange(current_year, month)
    last_day = datetime(current_year, month, last_day)

    start_timestamp = int(first_day.timestamp())
    end_timestamp = int((last_day + timedelta(days=1)).timestamp()) - 1

    return start_timestamp, end_timestamp


@task(klass=InvokeWrapper)
async def remove_invalid_rule(ctx, delete=False):
    if delete:
        query = RuleReference.delete().where(RuleReference.operation != RuleOperation.INIT.value)
        ids = await pw_db.execute(query)
        logger.info(f"delete rule {ids:}")
        query = RuleReference.delete().where(
            (RuleReference.operation == RuleOperation.INIT.value) & (RuleReference.deleted_utc > 1)
        )
        ids = await pw_db.execute(query)
        logger.info(f"delete rule {ids:}")
    else:
        logger.info("init != 10")
        query = RuleReference.select(RuleReference.rule).where(RuleReference.operation != RuleOperation.INIT.value)
        for item in await pw_db.execute(query):
            logger.info(item.rule)
        logger.info("init == 10 and deleted_utc > 2")
        query = RuleReference.select(RuleReference.rule).where(
            (RuleReference.operation == RuleOperation.INIT.value) & (RuleReference.deleted_utc > 1)
        )
        for item in await pw_db.execute(query):
            logger.info(item.rule)


@task(iterable=["fids", "mids"])
def export_user_answer(ctx, mids, fids, from_file="", out_path="user_answer.zip"):
    """导出指定fid的user_answer"""
    mids = [int(m) for m in mids]
    assert mids, '"mids" is required'
    cond = NewQuestion.mold.in_(mids)
    fids = {int(fid) for fid in fids}
    if from_file:
        fids.update(read_ids_from_file(from_file))
    if fids:
        cond &= NewQuestion.fid.in_(fids)
    else:
        logger.warning(f"No fid found, will export all user_answer with mold: {mids}")
    query = (
        Answer.select(Answer.id, Answer.data).join(NewQuestion, on=(Answer.qid == NewQuestion.id)).where(cond).dicts()
    )
    answers = []
    idx = 0
    with pw_db.allow_sync():
        for idx, row in enumerate(ServerSide(query), 1):  # noqa
            logger.info(f"export answer: {row['id']}")
            answers.append(row)
    with ZipFilePlus(out_path, mode="w") as zip_fp:
        zip_fp.writestr(
            "user_answer.json", json.dumps(answers, ensure_ascii=False, indent=4), compress_type=zipfile.ZIP_DEFLATED
        )
    logger.info(f"Successfully export user_answer({idx}) to {out_path}")


@task(klass=InvokeWrapper)
async def import_user_answer(ctx, path):
    """导入指定文件中的user_answer数据（仅更新data字段）"""
    assert zipfile.is_zipfile(path)
    answers = json.loads(read_zip_first_file(path).decode())
    count = 0
    for answer in answers:
        cnt = await pw_db.execute(Answer.update(data=answer["data"]).where(Answer.id == answer["id"]))
        if cnt:
            count += cnt
            logger.info(f"import answer: {answer['id']}")
        else:
            logger.warning(f"answer not found: {answer['id']}")
    logger.info(f"Successfully import user_answer({count}) from {path}")


@task(klass=InvokeWrapper, iterable=["fids", "mids", "report_years"])
def export_question(
    ctx,
    mids,
    fids=None,
    from_file="",
    report_years=None,
    out_path=None,
):
    """导出指定question表数据"""
    mids = {int(m) for m in mids}
    assert mids, '"mids" is required'
    cond = NewQuestion.mold.in_(mids) & (NewQuestion.answer.is_null(False))
    fids = {int(fid) for fid in fids if fid.isdigit() and int(fid)}
    if from_file:
        fids.update(read_ids_from_file(from_file))
    if fids:
        cond &= NewQuestion.fid.in_(fids)

    stmt = NewQuestion.select(
        NewQuestion.id,
        NewQuestion.fid,
        NewQuestion.preset_answer,
        NewQuestion.crude_answer,
        NewQuestion.answer,
    )
    if report_years := {r for r in report_years if r.isdigit() and int(r)}:
        stmt = stmt.join(HKEXFileMeta, on=(NewQuestion.fid == HKEXFileMeta.fid))
        cond &= HKEXFileMeta.report_year.in_(report_years)
    stmt = stmt.where(cond).order_by(NewQuestion.id.desc())
    out_path = out_path or f"{project_root}/data/tmp/question_{datetime.now().strftime('%Y%m%d%H%M')}.zip"
    count = 0
    with ZipFilePlus(out_path, mode="w") as zip_fp:
        with pw_db.allow_sync():
            for question in ServerSide(stmt.dicts()):
                qid = question.pop("id")
                logger.info(f"export question: {qid}")
                zip_fp.writestr(
                    f"question_{qid}.json",
                    json.dumps(question, ensure_ascii=False, indent=4),
                    compress_type=zipfile.ZIP_DEFLATED,
                )
                count += 1
    logger.info(f"Successfully export question({count}) to {out_path}")


@task(klass=InvokeWrapper, iterable=["fids", "report_years"])
def export_meta(
    ctx,
    fids=None,
    from_file="",
    report_years=None,
    out_path=None,
):
    """导出指定 hkex_file_meta 表数据"""
    cond = HKEXFileMeta.deleted_utc == 0
    fids = {int(fid) for fid in fids if fid.isdigit() and int(fid)}
    if from_file:
        fids.update(read_ids_from_file(from_file))
    if fids:
        cond &= HKEXFileMeta.fid.in_(fids)

    stmt = HKEXFileMeta.select(
        HKEXFileMeta.id,
        HKEXFileMeta.report_year,
        HKEXFileMeta.stock_code,
        HKEXFileMeta.fid,
    )
    if report_years := {r for r in report_years if r.isdigit() and int(r)}:
        cond &= HKEXFileMeta.report_year.in_(report_years)
    stmt = stmt.where(cond).order_by(HKEXFileMeta.id.desc())
    out_path = out_path or f"{project_root}/data/tmp/hkex_file_meta_{datetime.now().strftime('%Y%m%d%H%M')}.zip"
    count = 0
    with ZipFilePlus(out_path, mode="w") as zip_fp:
        with pw_db.allow_sync():
            for meta in ServerSide(stmt.dicts()):
                pk = meta.pop("id")
                logger.info(f"export hkex_file_meta: {pk}")
                zip_fp.writestr(
                    f"hkex_file_meta_{pk}.json",
                    json.dumps(meta, ensure_ascii=False, indent=4),
                    compress_type=zipfile.ZIP_DEFLATED,
                )
                count += 1
    logger.info(f"Successfully export hkex_file_meta({count}) to {out_path}")


@task(klass=InvokeWrapper)
async def import_table_with_pk(ctx, path):
    """导入指定压缩包中的数据"""
    assert zipfile.is_zipfile(path)
    table_cls = {
        "question": NewQuestion,
        "hkex_file_meta": HKEXFileMeta,
    }
    count = 0
    with ZipFilePlus(path, mode="r") as zip_fp:
        for name in zip_fp.namelist():
            table, pk = os.path.splitext(name)[0].rsplit("_", maxsplit=1)
            data = msgspec.json.decode(zip_fp.read(name))
            cnt = await pw_db.execute(table_cls[table].update(**data).where(table_cls[table].id == int(pk)))
            if cnt:
                count += cnt
                logger.info(f"import {table}: {pk}")
            else:
                logger.warning(f"{table} not found: {pk}")
                # data["id"] = int(pk)
                # for key in ("preset_answer", "crude_answer", "answer"):
                #     data.pop(key, None)
                # await pw_db.create(table_cls[table], **data)
    logger.info(f"Successfully import {table}({count}) from {path}")


@task
@sync
async def stat_accuracy_for_cg(ctx, from_file, mid=28):
    """
    JURA5-code按需导出集合数据，为计算准确率
    https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/3803
    """
    with open(from_file, "r", encoding="utf-8") as file_obj:
        fids = file_obj.readlines()
    rows = [i.strip().split() for i in fids if i.strip()]
    answers = []
    all_need_label_items = [["STOCK", "RULE", "URL"]]
    rule_refer = await RuleReference.find_cg_rule_refer_map()
    for stock_code, report_year in rows:
        cg_stat = CgStat(stock_code, report_year, mid, rule_refer, need_label_items=[])
        answer = await cg_stat.stat_accuracy_info()
        all_need_label_items.extend(cg_stat.need_label_items)
        if not answer:
            continue
        answer["stock"] = stock_code
        answers.append(answer)

    with open("cg_uat_missing_manual_data.csv", "w", newline="") as file:
        writer = csv.writer(file)
        for need_label_item in all_need_label_items:
            writer.writerow(need_label_item)

    CgStat.stuff_data(answers)


@task
@sync
async def stat_accuracy_for_policy(ctx, from_file, fids_file=None):
    """
    https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6429
    """
    if fids_file:
        fids = list(read_ids_from_file(fids_file))
        query = (
            HKEXFileMeta.select(HKEXFileMeta.stock_code, HKEXFileMeta.report_year)
            .join(NewFile, on=(NewFile.id == HKEXFileMeta.fid))
            .where(NewFile.id.in_(fids))
        )
        rows = list(await pw_db.execute(query.tuples()))
    else:
        with open(from_file, "r", encoding="utf-8") as file_obj:
            fids = file_obj.readlines()
        rows = [i.strip().split() for i in fids if i.strip()]

    answers = []
    rule_refer = await RuleReference.find_policy_esg_rule_refer_map()
    for stock_code, report_year in rows:
        ins = PolicyEsgStat(standard_stock(stock_code), report_year, rule_refer)
        answer = await ins.stat_accuracy_info()
        answers.append(answer)

    PolicyEsgStat.stuff_data(answers, rule_refer)


@task
@sync
async def stat_accuracy_for_jura21(ctx, from_file, for_sign=False):
    """
    https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/4173
    --for-sign 只统计重大投资相关的几个rule
    """
    with open(from_file, "r", encoding="utf-8") as file_obj:
        fids = file_obj.readlines()
    rows = [i.strip().split() for i in fids if i.strip()]
    answers, all_answers = [], []
    all_need_label_items = [["STOCK", "ALIAS", "RULE", "SUB RULE", "URL"]]
    rule_refer = await RuleReference.find_abcd_rule_references()
    rule_refer = {item.rule: item for item in rule_refer}
    alias_rule_map = {item.main_alias: item.rule for item in rule_refer.values()}
    if for_sign:
        alias_rule_map = {item.main_alias: item.rule for item in rule_refer.values() if item.rule in JURA6_C2_RULES}

    for stock_code, report_year in rows:
        jura21_stat = Jura21Stat(standard_stock(stock_code), report_year, rule_refer, need_label_items=[])
        accuracy_infos = await jura21_stat.stat_accuracy_info()
        all_need_label_items.extend(jura21_stat.need_label_items)
        for accuracy_info in accuracy_infos:
            if "_" not in accuracy_info["stock"]:
                answers.append(accuracy_info)
            else:
                all_answers.append(accuracy_info)

    with open("jura21_missing_manual_data.csv", "w", newline="") as file:
        writer = csv.writer(file)
        for need_label_item in all_need_label_items:
            writer.writerow(need_label_item)

    Jura21Stat.stuff_data(answers, all_answers, alias_rule_map)


@task
@sync
async def stat_accuracy(ctx, from_file: str, mid: int):
    mid = int(mid)
    with open(from_file, "r", encoding="utf-8") as file_obj:
        rows = file_obj.readlines()
    rows = [i.strip().split() for i in rows if i.strip()]
    answers = []
    all_need_label_items = [["STOCK", "ALIAS", "RULE", "SUB RULE", "URL"]]
    rule_refer = await RuleReference.find_all_rule_refer_map()
    alias_rule_map = {item.main_alias: item.rule for item in rule_refer.values()}
    for stock_code, fid in rows:
        common_stat_ins = CommonStat(standard_stock(stock_code), fid, mid, rule_refer, need_label_items=[])
        accuracy_infos = await common_stat_ins.stat_accuracy_info()
        all_need_label_items.extend(common_stat_ins.need_label_items)
        answers.append(accuracy_infos)

    with open("missing_manual_data.csv", "w", newline="") as file:
        writer = csv.writer(file)
        for need_label_item in all_need_label_items:
            writer.writerow(need_label_item)

    CommonStat.stuff_data(answers, alias_rule_map, mid)


@task
@sync
async def make_question(
    ctx,
    new_mid,
    mid=special_mold.v1_id,
    start=None,
    end=None,
    force=False,
    cache_pdf=False,
    only_new=False,
    from_file=None,
):
    from remarkable.worker.tasks import _make_question

    """ """
    new_mid = int(new_mid)
    fids = None
    if from_file:
        with open(from_file) as f:
            fids = [line.strip() for line in f.readlines() if line.strip().isdigit()]
    files = await NewFile.list_by_range(mold=mid, start=start, end=end, file_ids=fids)
    for file in files:
        if not force and new_mid in file.mold_list:
            logger.info(f"file {file.id} already has mold {new_mid}, skip!")
            continue
        if new_mid not in file.mold_list:
            file.mold_list.append(new_mid)
            await pw_db.update(file, only=["mold_list"])
        if only_new:
            await NewQuestion.create_question(file.id, new_mid)
            logger.info(f"create question for {file.id}, skip run predict answer")
        else:
            await _make_question(file.id, new_mid, cache_pdf=cache_pdf)
            logger.info(f"make question for {file.id}")


@task(klass=InvokeWrapper)
async def re_construct_cg_result(_):
    """重建 CGResult 表数据"""
    from remarkable.models.cg_result import CGResult
    from remarkable.services.cg import sync_cg_answers

    async with pw_db.atomic():
        # Delete all CGResult
        await pw_db.execute(CGResult.delete())
        # Reset the id sequence
        await pw_db.execute("ALTER SEQUENCE cg_result_id_seq RESTART WITH 1")
        logger.info("Reset CGResult successfully")
        for qid in await pw_db.scalars(
            NewQuestion.select(NewQuestion.id).where(NewQuestion.mold == special_mold.v5_id)
        ):
            await sync_cg_answers(qid)


@task(klass=InvokeWrapper, iterable=["stocks", "report_years"])
def re_parse_fy(_, stocks=None, report_years=None, workers=10):
    """重解析FY数据"""

    from remarkable.common.constants import DocType
    from remarkable.common.multiprocess import run_by_batch
    from remarkable.models.mold import special_mold
    from remarkable.worker.tasks import _parse_ar_year_end

    stmt = HKEXFileMeta.doc_type.in_((DocType.AR.value, DocType.ESG.value))
    stocks = [standard_stock(stock) for stock in stocks if stock.isdigit()]
    if stocks:
        stmt &= HKEXFileMeta.stock_code.in_(stocks)
    report_years = [year for year in report_years if year.isdigit()]
    if not stocks and not report_years:
        logger.error("stocks or report_years is required")
        return

    if report_years:
        stmt &= HKEXFileMeta.report_year.in_(report_years)

    with pw_db.allow_sync():
        tasks = [r.fid for r in HKEXFileMeta.select(HKEXFileMeta.fid).where(stmt).order_by(HKEXFileMeta.fid.desc())]
        # 刷新时间戳，使得增量导出时可以重新导出
        NewQuestion.update(updated_utc=generate_timestamp()).where(
            NewQuestion.fid.in_(tasks), NewQuestion.mold.in_(special_mold.ar_mids)
        ).execute()

    for _ in run_by_batch(_parse_ar_year_end, tasks, workers=workers):
        pass


@task(klass=InvokeWrapper)
async def modify_fy(_, fid, report_year):
    """
    https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/5022
    手动修改指定file的report_year
    """
    from remarkable.models.hkex_file import HKEXFile
    from remarkable.worker.tasks import _parse_ar_year_end

    async with pw_db.atomic():
        file = await pw_db.first(HKEXFile.select().where(HKEXFile.fid == int(fid)))
        if not file:
            logger.warning(f"file: {fid} not found, skip!")
            return
        report_year = int(report_year)
        if not file.meta:
            file.meta = {}
        file.meta["financial_year"] = report_year
        await pw_db.update(file, only=["meta"])
        await pw_db.execute(
            NewQuestion.update(updated_utc=generate_timestamp()).where(
                NewQuestion.fid == file.fid, NewQuestion.mold.in_(special_mold.ar_mids)
            )
        )
        await _parse_ar_year_end(file.fid, report_year=report_year)


class _Link(BaseModel):
    report_type: str | Literal["AR", "ESG", "CG", "QR"] = Field(exclude=True)
    fid: int = Field(exclude=True)
    qid: int = Field(exclude=True)
    mid: int = Field(exclude=True)
    link: str = ""
    text: str = "点击查看"

    def model_post_init(self, __context: Any) -> None:
        base_url = "https://jura.paodingai.com/#/hkex"
        match self.report_type:
            case "AR":
                self.link = f"{base_url}/annual-report-checking/report-review/{self.qid}?fileId={self.fid}&schemaId={self.mid}&rule=B1"
            case "ESG":
                self.link = f"{base_url}/esg-report-checking/report-review/{self.qid}?fileId={self.fid}&schemaId={self.mid}&rule=MDR%2013%20i%20-%20board%20oversight"
            case "CG":
                self.link = f"{base_url}/cg-report-checking/report-review/{self.qid}?fileId={self.fid}&schemaId={self.mid}&rule=A%28a%29-Explanation%20of%20application%20of%20code%20principles"
            case "QR":
                self.link = f"{base_url}/result-announcement/report-review/{self.qid}?fileId={self.fid}"
        if self.fid:
            self.link += "&delist=0"
        else:
            self.text = "暂无数据（仅占位）"
            self.link = ""


class _StockReport(BaseModel):
    model_config = ConfigDict(str_strip_whitespace=True)

    stock_code: str = Field(serialization_alias="stock code")
    jura_fy: str = Field(serialization_alias="JURA FY")
    release_at: str = Field(serialization_alias="publish date")
    year_end: str | None = Field(default=None, serialization_alias="year end in webb site")
    actual_year_end: str | None = Field(default=None, serialization_alias="year end in jura")
    deadline: str | None = Field(default=None)
    listing_segment: str | None = Field(default=None, serialization_alias="listing segment")
    report_status: str | None = Field(default=None, serialization_alias="report status")
    report_type: str = Field(serialization_alias="report type")
    link: _Link = Field(serialization_alias="link")
    check_date: str = Field(default="", serialization_alias="check date")
    name: str = Field(default="", serialization_alias="name")
    remark: str = Field(default="", serialization_alias="remarks/issue description")
    res: str = Field(default="", serialization_alias="处理结果")
    esg_type: str = Field(default="-", serialization_alias="esg type")  # Literal["-", "ar-esg", "independent-esg"]

    @field_validator("report_type", mode="before")
    def validate_report_type(cls, value: str | int) -> str:
        if isinstance(value, int):
            return DocType(value).name
        return value

    def model_post_init(self, __context: Any) -> None:
        self.stock_code = self.stock_code.zfill(5)
        # match self.report_type:
        #     case "AR":
        #         self.name = "黄予入"
        #     case "ESG":
        #         self.name = "李苏悦"
        #     case "CG":
        #         self.name = "朱婧文"

    @classmethod
    def exists(
        cls,
        table: pd.DataFrame,
        stock_code: str,
        *,
        release_at: str = None,
        report_type: str = None,
        report_status: str = None,
        esg_type: str = None,
        filter_by_remark: bool = False,
    ) -> bool:
        if not table.empty:
            cond = table[cls.model_fields["stock_code"].serialization_alias] == stock_code
            if release_at:
                cond &= table[cls.model_fields["release_at"].serialization_alias] == release_at
            if report_type:
                cond &= table[cls.model_fields["report_type"].serialization_alias] == report_type
            if report_status:
                cond &= table[cls.model_fields["report_status"].serialization_alias] == report_status
            if esg_type:
                cond &= table[cls.model_fields["esg_type"].serialization_alias] == esg_type
            if filter_by_remark:
                cond &= (
                    (table[cls.model_fields["remark"].serialization_alias].str.len() > 0)
                    | (table[cls.model_fields["check_date"].serialization_alias].str.len() > 0)
                    | (table[cls.model_fields["name"].serialization_alias].str.len() > 0)
                )
            return not table[cond].empty
        return False

    @classmethod
    def exist_feishu_row(
        cls,
        table: pd.DataFrame,
        stock_code: str,
        *,
        release_at: str = None,
        report_type: str = None,
        report_status: str = None,
        esg_type: str = None,
        filter_by_remark: bool = False,
    ) -> bool:
        cond = table[cls.model_fields["stock_code"].serialization_alias] == stock_code
        if release_at:
            cond &= table[cls.model_fields["release_at"].serialization_alias] == release_at
        if report_type:
            cond &= table[cls.model_fields["report_type"].serialization_alias] == report_type
        if report_status:
            cond &= table[cls.model_fields["report_status"].serialization_alias] == report_status
        if esg_type:
            cond &= table[cls.model_fields["esg_type"].serialization_alias] == esg_type
        if filter_by_remark:
            cond &= (
                (table[cls.model_fields["remark"].serialization_alias].str.len() > 0)
                | (table[cls.model_fields["check_date"].serialization_alias].str.len() > 0)
                | (table[cls.model_fields["name"].serialization_alias].str.len() > 0)
            )
        return table[cond]


async def _prepare_reports(table: pd.DataFrame, date_from: str = None, date_to: str = None) -> list[dict]:
    today = datetime.now().strftime("%Y-%m-%d")
    stmt = (
        HKEXFileMeta.select(
            HKEXFileMeta.fid,
            HKEXFileMeta.stock_code,
            HKEXFileMeta.report_year,
            HKEXFileMeta.published,
            peewee.Case(
                None,
                [
                    (HKEXFileMeta.doc_type == DocType.AR, DocType.AR.name),
                    (HKEXFileMeta.doc_type == DocType.ESG, DocType.ESG.name),
                ],
                DocType.AR.name,
            ).alias("doc_type"),
        )
        .where(
            (HKEXFileMeta.doc_type.in_([DocType.AR, DocType.ESG]))
            & (HKEXFileMeta.published > f"{date_from or today}T00:00:00")
            & (HKEXFileMeta.published < f"{date_to or today}T23:59:59")
        )
        .order_by(HKEXFileMeta.stock_code.desc(), HKEXFileMeta.doc_type.desc())
    )
    reports = []
    for meta in await pw_db.execute(stmt):
        if _StockReport.exists(table, meta.stock_code, release_at=meta.published):
            continue
        if meta.doc_type == DocType.ESG.name:
            mid = 2
            qid = await pw_db.scalar(
                NewQuestion.select(NewQuestion.id).filter(NewQuestion.fid == meta.fid, NewQuestion.mold == mid)
            )
            reports.append(
                _StockReport(
                    stock_code=meta.stock_code,
                    release_at=meta.published,
                    report_type=meta.doc_type,
                    jura_fy=meta.report_year,
                    link=_Link(report_type=meta.doc_type, fid=meta.fid, qid=qid, mid=mid),
                ).model_dump(by_alias=True, exclude_none=True)
            )
            continue

        mid = 5
        qid = await pw_db.scalar(
            NewQuestion.select(NewQuestion.id).filter(NewQuestion.fid == meta.fid, NewQuestion.mold == mid)
        )
        reports.append(
            _StockReport(
                stock_code=meta.stock_code,
                release_at=meta.published,
                report_type=meta.doc_type,
                jura_fy=meta.report_year,
                link=_Link(report_type=meta.doc_type, fid=meta.fid, qid=qid, mid=mid),
            ).model_dump(by_alias=True, exclude_none=True)
        )

        if await FileESGxREF.is_activated(meta.fid):
            mid = 1
            qid = await pw_db.scalar(
                NewQuestion.select(NewQuestion.id).filter(NewQuestion.fid == meta.fid, NewQuestion.mold == mid)
            )
            reports.append(
                _StockReport(
                    stock_code=meta.stock_code,
                    release_at=meta.published,
                    report_type=DocType.ESG.name,
                    jura_fy=meta.report_year,
                    link=_Link(report_type=DocType.ESG.name, fid=meta.fid, qid=qid, mid=mid),
                ).model_dump(by_alias=True, exclude_none=True)
            )

        mid = 28
        qid = await pw_db.scalar(
            NewQuestion.select(NewQuestion.id).filter(NewQuestion.fid == meta.fid, NewQuestion.mold == mid)
        )
        reports.append(
            _StockReport(
                stock_code=meta.stock_code,
                release_at=meta.published,
                report_type="CG",
                jura_fy=meta.report_year,
                link=_Link(report_type="CG", fid=meta.fid, qid=qid, mid=mid),
            ).model_dump(by_alias=True, exclude_none=True)
        )
    return reports


@task(klass=InvokeWrapper)
async def export_review_codes_to_feishu(_, date_from=None, date_to=None):
    """导出指定时间区间的报告审核链接到飞书表格，不指定时间区间则默认为当天
    https://paodingai.feishu.cn/base/KKQUbsQECawnE8sqNx4ch5tAn4c?table=tbldZBsuDyt2IiNw
    inv dev.export-review-codes-to-feishu --date-from=2024-07-30 --date-to=2022-07-30
    """
    app_token = "KKQUbsQECawnE8sqNx4ch5tAn4c"
    table_id = "tbldZBsuDyt2IiNw"
    base_token = "pt--N1UCp1x3aiwzQK8n4Zp1a7-tQlfNhXZYlF3o2aQAQAAAgDBVARARST_xwb-"
    bot = FeiShuTableBot(app_token=app_token, base_token=base_token, table_id=table_id)
    now_table = await bot.get_table()
    if reports := await _prepare_reports(now_table, date_from, date_to):
        await bot.batch_create([{"fields": r} for r in reports])


async def _codes_need_to_check(month: int) -> set[str]:
    """需要检查的codes
    NOTE: 全集 https://mm.paodingai.com/cheftin/pl/6wkkj55cu38txjc1fb3qgwon7c
    """
    from remarkable.spiders import fetch_codes_by_year_end_month

    return set(await fetch_codes_by_year_end_month(month))


async def _prepare_qr_reports(check_month: int, date_from: str = None, date_to: str = None):
    bot = FeiShuTableBot()
    table = await bot.get_table()
    today = datetime.now()
    today_str = today.strftime("%Y-%m-%d")
    codes_to_check = await _codes_need_to_check(check_month)
    stmt = (
        HKEXFileMeta.select(
            HKEXFileMeta.fid,
            HKEXFileMeta.stock_code,
            HKEXFileMeta.report_year,
            HKEXFileMeta.published,
            peewee.Case(
                None,
                [
                    (HKEXFileMeta.doc_type == DocType.Q1, DocType.Q1.name),
                    (HKEXFileMeta.doc_type == DocType.INTERIM, DocType.INTERIM.name),
                    (HKEXFileMeta.doc_type == DocType.Q3, DocType.Q3.name),
                    (HKEXFileMeta.doc_type == DocType.FINAL, DocType.FINAL.name),
                ],
                "QR",
            ).alias("doc_type"),
        )
        .where(
            (HKEXFileMeta.doc_type.in_([DocType.Q1, DocType.INTERIM, DocType.Q3, DocType.FINAL]))
            & (HKEXFileMeta.published > f"{date_from or today_str}T00:00:00")
            & (HKEXFileMeta.published < f"{date_to or today_str}T23:59:59")
            & (HKEXFileMeta.stock_code.in_(codes_to_check))
        )
        .order_by(HKEXFileMeta.stock_code.desc(), HKEXFileMeta.doc_type)
    )
    reviewer = "黄予入"
    reports = []
    records_need_to_del = []
    for meta in await pw_db.execute(stmt):
        if meta.stock_code in codes_to_check:
            codes_to_check.remove(meta.stock_code)
        if _StockReport.exists(table, meta.stock_code, release_at=meta.published):
            continue
        qid = await pw_db.scalar(
            NewQuestion.select(NewQuestion.id).filter(
                NewQuestion.fid == meta.fid, NewQuestion.mold == special_mold.v3r_id
            )
        )
        reports.append(
            _StockReport(
                stock_code=meta.stock_code,
                release_at=meta.published,
                report_type=meta.doc_type,
                jura_fy=meta.report_year,
                name=reviewer,
                link=_Link(report_type="QR", fid=meta.fid, qid=qid, mid=special_mold.v3r_id),
            ).model_dump(by_alias=True, exclude_none=True, exclude={"esg_type", "res"})
        )
        records_need_to_del.append(meta.stock_code)

    if records_need_to_del and not table.empty:
        logger.info(f"Delete {len(records_need_to_del)} rows.")
        await bot.batch_delete(
            table[table[_StockReport.model_fields["stock_code"].serialization_alias].isin(records_need_to_del)][
                bot.RECORD_ID
            ].to_list()
        )

    # 补充未在系统内的code记录
    for code in codes_to_check:
        if not _StockReport.exists(table, code):
            reports.append(
                _StockReport(
                    stock_code=code,
                    release_at="",
                    report_type="",
                    jura_fy="",
                    link=_Link(text="仅占位", report_type="QR", fid=0, qid=0, mid=0),
                ).model_dump(by_alias=True, exclude_none=True, exclude={"esg_type", "res"})
            )

    if reports:
        return await bot.batch_create([{"fields": r} for r in reports])
    logger.warning("Nothing to push.")


@task(klass=InvokeWrapper)
async def export_qr_review_codes_to_feishu(_, check_month: int, date_from=None, date_to=None):
    """导出指定时间区间的季度报告审核链接到飞书表格，不指定时间区间则默认为当天
    2024: https://paodingai.feishu.cn/base/GmUubGfNoa83QFseuIncmaxxnKf?table=tblgoTBRK0IJ5hzL
    2025-03: https://paodingai.feishu.cn/base/VT16bQq1Rax0qZs9Hv0cx6JXnnh?table=tblkQab3SyCy8EzN
    2025-06: https://paodingai.feishu.cn/base/FVZNbljf7avW7zsP5alc7LsTnJe?table=tblxnDqUfGJhtXRk&view=vewtmeUXhE
    inv dev.export-qr-review-codes-to-feishu --date-from=2024-07-30 --date-to=2022-07-30
    """
    check_month = int(check_month)
    await _prepare_qr_reports(check_month, date_from, date_to)


@task(klass=InvokeWrapper)
async def update_company_info(ctx):
    await update_company_info_task()


@task(klass=InvokeWrapper)
async def update_directors(ctx):
    await fetch_and_update_directors()


async def get_cached_stock_year_end_month_map() -> dict[str, str]:
    """获取股票年结月份映射，优先从Redis缓存获取，缓存过期时间为当天24:00"""
    rdb = init_rdb()
    cache_key = "stock_year_end_month_map"

    # 获取缓存数据
    cached_data = rdb.get(cache_key)
    if cached_data:
        return json.loads(cached_data)

    # 如果没有缓存，从接口获取数据
    data = await get_stock_year_end_month_map()

    # 计算当天24:00的时间戳
    tomorrow = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0) + timedelta(days=1)
    expire_seconds = int((tomorrow - datetime.now()).total_seconds())

    # 将数据存入Redis并设置过期时间
    rdb.setex(cache_key, expire_seconds, json.dumps(data))

    return data


@task(klass=InvokeWrapper, iterable=["month"])
async def month_check(
    ctx,
    month,
    file_type,
    checked_year,
    dead_line,
    date_from=None,
    date_to=None,
    special_stock=None,
):
    """
    Jura 项目检查指定月份的报告是否已经存在，之后推送至飞书
    https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/5072
    inv dev.month-check --file-type=ar --checked-year=2024 --month=1 --month=2 --month=4 --month=5 --month=6
    inv dev.month-check --file-type=ar --checked-year=2024 --month=7 --month=8 --month=9 --month=10 --month=11 --month=12 --date-from=2025-04-28 --date-to=2025-04-28
    inv dev.month-check --file-type=ar --checked-year=2024 --month=12 --date-from=2025-04-28 --date-to=2025-04-28
    inv dev.month-check --file-type=ar --checked-year=2025 --month=3  --date-from=2025-04-01 --date-to=2025-07-31 --dead-line=2025/07/31
    """

    def get_last_day_of_month(month, checked_year: int | None = None):
        import datetime

        month = int(month)
        if not 1 <= month <= 12:
            raise ValueError("月份必须在1到12之间")

        current_year = checked_year or datetime.datetime.now().year

        if month == 12:
            next_month = datetime.date(current_year + 1, 1, 1)
        else:
            next_month = datetime.date(current_year, month + 1, 1)

        last_day = next_month - timedelta(days=1)

        return last_day.strftime("%Y/%m/%d")

    logger.info(f"Checking {month} month.")
    month = [str(m) for m in month]
    stock_year_end_map = {
        stock: get_last_day_of_month(m, int(checked_year))
        for stock, m in (await get_cached_stock_year_end_month_map()).items()
        if m in month
    }
    assert stock_year_end_map, "No stock year end found"
    logger.info("got stock_year_end_map")
    bot = FeiShuTableBot()
    table = await bot.get_table()

    rows = []
    rows_to_del = []
    if special_stock:
        stock_year_end_map = {special_stock: stock_year_end_map[special_stock]}
    for stock_code, year_end in stock_year_end_map.items():
        board_type = "MB" if is_main_stock(stock_code) else "GEM"
        if file_type == "ar":
            async for row in parse_ar(
                stock_code,
                board_type,
                checked_year,
                year_end,
                date_from,
                date_to,
                dead_line,
            ):
                if _StockReport.exists(table, stock_code, report_type=row.report_type, filter_by_remark=True):
                    # 已经有备注的记录，或者已经检查过的 不删除也不新增
                    # logger.info(f"Found {row.report_type} report that has been marked for {stock_code}")
                    continue
                if _StockReport.exists(table, stock_code, report_type=row.report_type, report_status="Missing"):
                    feishu_row = _StockReport.exist_feishu_row(
                        table, stock_code, report_type=row.report_type, report_status="Missing"
                    )
                    rows_to_del.append(feishu_row[bot.RECORD_ID].values[0])
                if not _StockReport.exists(table, stock_code, report_type=row.report_type, report_status="Found"):
                    rows.append(row.model_dump(by_alias=True, exclude_none=True))
                if (
                    _StockReport.exists(table, stock_code, report_type="ESG", esg_type="ar-esg", report_status="Found")
                    and row.esg_type == "independent-esg"
                ):
                    logger.info(f"Found independent ESG for {stock_code}")
                    rows.append(row.model_dump(by_alias=True, exclude_none=True))

        else:
            async for row in parse_qr(
                stock_code,
                board_type,
                checked_year,
                file_type,
                year_end,
            ):
                if _StockReport.exists(table, stock_code, report_type=row.report_type, report_status="Missing"):
                    feishu_row = _StockReport.exist_feishu_row(
                        table, stock_code, report_type=row.report_type, report_status="Missing"
                    )
                    rows_to_del.append(feishu_row[bot.RECORD_ID].values[0])
                if not _StockReport.exists(table, stock_code, report_type=row.report_type, report_status="Found"):
                    rows.append(row.model_dump(by_alias=True, exclude_none=True))

    if rows_to_del and not table.empty:
        logger.info(f"Delete {len(rows_to_del)} rows.")
        await bot.batch_delete(rows_to_del)

    if rows:
        await bot.batch_create([{"fields": r} for r in rows])
    else:
        logger.warning("Nothing to push.")


async def parse_qr(stock_code, board_type, current_year, file_type, year_end) -> AsyncGenerator[_StockReport, None]:
    doc_type = None
    check_name = ""
    deadline = "2024/11/15" if file_type == "q3" else "2024/11/30"  # todo hard code
    match file_type:
        case "q1":
            doc_type = DocType.Q1
            check_name = "夏华扬"
        case "interim":
            doc_type = DocType.INTERIM
            check_name = "谢汶纹"
        case "q3":
            doc_type = DocType.Q3
            check_name = "任玥"
        case "final":
            doc_type = DocType.FINAL
            check_name = "夏华扬"

    cond = [
        HKEXFileMeta.stock_code == stock_code,
        HKEXFileMeta.doc_type == doc_type,
        HKEXFileMeta.report_year == current_year,
    ]
    query = HKEXFileMeta.select(
        HKEXFileMeta.fid, HKEXFileMeta.doc_type, HKEXFileMeta.published, HKEXFileMeta.report_year
    ).where(*cond)
    items = list(await pw_db.execute(query))
    if items:
        item = items[0]
        qid = await pw_db.scalar(
            NewQuestion.select(NewQuestion.id).filter(
                NewQuestion.fid == item.fid, NewQuestion.mold == special_mold.v3r_id
            )
        )
        yield _StockReport(
            stock_code=stock_code,
            jura_fy=item.report_year,
            release_at=item.published,
            year_end=year_end,
            deadline=deadline,
            listing_segment=board_type,
            report_type=doc_type,
            report_status="Found",
            link=_Link(report_type="QR", fid=item.fid, qid=qid, mid=special_mold.v3r_id),
            name=check_name,
        )
    else:
        yield _StockReport(
            stock_code=stock_code,
            jura_fy="",
            release_at="",
            year_end=year_end,
            deadline=deadline,
            listing_segment=board_type,
            report_type=doc_type,
            report_status="Missing",
            link=_Link(report_type="QR", fid=0, qid=0, mid=0),
            name=check_name,
        )


async def parse_ar(
    stock_code,
    board_type,
    check_year,
    year_end,
    date_from,
    date_to,
    dead_line,
) -> AsyncGenerator[_StockReport, None]:
    today = datetime.now()
    today_str = today.strftime("%Y-%m-%d")
    for file_type in ("AR", "ESG"):
        doc_types = [DocType.AR.value]
        if file_type == "ESG":
            doc_types.append(DocType.ESG.value)
        cond = [
            HKEXFileMeta.stock_code == stock_code,
            HKEXFileMeta.doc_type.in_(doc_types),
            HKEXFileMeta.report_year == check_year,
            (HKEXFileMeta.published > f"{date_from or today_str}T00:00:00"),
            (HKEXFileMeta.published < f"{date_to or today_str}T23:59:59"),
        ]
        query = HKEXFileMeta.select(
            HKEXFileMeta.fid,
            HKEXFileMeta.doc_type,
            HKEXFileMeta.published,
            HKEXFileMeta.report_year,
            HKEXFileMeta.year_end,
        ).where(*cond)
        items = list(await pw_db.execute(query))
        if items:
            item = items[0]
            if file_type == "AR":
                qid = await pw_db.scalar(
                    NewQuestion.select(NewQuestion.id).filter(
                        NewQuestion.fid == item.fid, NewQuestion.mold == special_mold.v1_id
                    )
                )
                yield _StockReport(
                    stock_code=stock_code,
                    release_at=item.published,
                    report_type="AR",
                    report_status="Found",
                    jura_fy=item.report_year,
                    year_end=year_end,
                    actual_year_end=item.year_end,
                    deadline=dead_line,
                    listing_segment=board_type,
                    link=_Link(report_type="AR", fid=item.fid, qid=qid, mid=special_mold.v1_id),
                )
                qid = await pw_db.scalar(
                    NewQuestion.select(NewQuestion.id).filter(
                        NewQuestion.fid == item.fid, NewQuestion.mold == special_mold.v5_id
                    )
                )
                yield _StockReport(
                    stock_code=stock_code,
                    release_at=item.published,
                    report_type="CG",
                    report_status="Found",
                    jura_fy=item.report_year,
                    year_end=year_end,
                    actual_year_end=item.year_end,
                    deadline=dead_line,
                    listing_segment=board_type,
                    link=_Link(report_type="CG", fid=item.fid, qid=qid, mid=special_mold.v5_id),
                )
            elif file_type == "ESG":
                all_fids = [item.fid for item in items]
                esg_doc_type_map = {item.fid: item.doc_type for item in items}
                cond = [FileESGxREF.fid.in_(all_fids), FileESGxREF.activated]
                query = FileESGxREF.select().where(*cond)
                exist_status = "Missing"
                esg_type = ""
                link = _Link(report_type="ESG", fid=0, qid=0, mid=0)
                if esg_xref_item := await pw_db.first(query):
                    exist_status = "Found"
                    esg_mdoc_type = esg_doc_type_map[esg_xref_item.fid]
                    esg_mold = 1 if esg_mdoc_type == DocType.AR.value else 2
                    esg_type = "ar-esg" if esg_mdoc_type == DocType.AR.value else "independent-esg"
                    qid = await pw_db.scalar(
                        NewQuestion.select(NewQuestion.id).filter(
                            NewQuestion.fid == esg_xref_item.fid, NewQuestion.mold == esg_mold
                        )
                    )
                    link = _Link(report_type="ESG", fid=esg_xref_item.fid, qid=qid, mid=esg_mold)
                yield _StockReport(
                    stock_code=stock_code,
                    release_at=item.published,
                    report_type="ESG",
                    report_status=exist_status,
                    jura_fy=item.report_year,
                    year_end=year_end,
                    actual_year_end=item.year_end,
                    deadline=dead_line,
                    listing_segment=board_type,
                    link=link,
                    esg_type=esg_type,
                )
        else:
            yield _StockReport(
                stock_code=stock_code,
                release_at="",
                report_type=file_type,
                report_status="Missing",
                jura_fy=check_year,
                year_end=year_end,
                actual_year_end="",
                deadline=dead_line,
                listing_segment=board_type,
                link=_Link(report_type=file_type, fid=0, qid=0, mid=0),
            )
            if file_type == "AR":
                yield _StockReport(
                    stock_code=stock_code,
                    release_at="",
                    report_type="CG",
                    report_status="Missing",
                    jura_fy=check_year,
                    year_end=year_end,
                    actual_year_end="",
                    deadline=dead_line,
                    listing_segment=board_type,
                    link=_Link(report_type="CG", fid=0, qid=0, mid=0),
                )


@task(help={"convening_date": "datetime, format: yyyy-mm-dd"})
@sync
async def set_convening_date(ctx, agm_fid, convening_date):
    agm_file = await pw_db.first(AGMMeta.select().where(AGMMeta.agm_fid == agm_fid))
    if not agm_file:
        logging.warning(f"AGM file not found for {agm_fid=}")
        return
    try:
        convening_date = datetime.strptime(convening_date, "%Y-%m-%d").date()
    except ValueError:
        logger.error(f"Invalid date format: {convening_date}")
        return
    agm_file.convening_date = convening_date
    await pw_db.update(agm_file, only=["convening_date"])
    logger.info(f"{agm_fid=} {convening_date=} updated!")


@task
@sync
async def gen_agm_meta(ctx, start=None, end=None, from_file=None, workers=0):
    cond = HKEXFile.type == "AGM"
    if start:
        cond &= AGMMeta.agm_fid >= int(start)
    if end:
        cond &= AGMMeta.agm_fid <= int(end)
    if from_file:
        with open(from_file, "r", encoding="utf-8") as file_obj:
            include_ids = [int(i.strip()) for i in file_obj if i.strip().isdigit()]
        cond &= AGMMeta.agm_fid.in_(include_ids)
    query = AGMMeta.select().join(HKEXFile, on=(AGMMeta.agm_fid == HKEXFile.fid))

    with pw_db.allow_sync():
        query = query.where(cond).dicts()
        tasks = []
        for row in query.where(cond).execute():
            tasks.append((row["agm_fid"], row["stock_code"]))
    for _ in run_by_batch(set_agm_meta, tasks, workers=workers, batch_size=10000):
        pass


@task
@sync
async def gen_poll_meta(ctx, start=None, end=None, from_file=None, workers=0):
    cond = HKEXFile.type == "POLL"
    cond &= HKEXFile.fid.is_null(False)
    if start:
        cond &= HKEXFile.fid >= int(start)
    if end:
        cond &= HKEXFile.fid <= int(end)
    if from_file:
        with open(from_file, "r", encoding="utf-8") as file_obj:
            include_ids = [int(i.strip()) for i in file_obj if i.strip().isdigit()]
        cond &= HKEXFile.fid.in_(include_ids)
    query = HKEXFile.select(HKEXFile.fid, HKEXFile.stock_code).where(cond).dicts()

    with pw_db.allow_sync():
        tasks = []
        for row in query.where(cond).execute():
            tasks.append((row["fid"], row["stock_code"]))
    for _ in run_by_batch(set_poll_meta, tasks, workers=workers, batch_size=10000):
        pass


@task
@sync
async def gen_mr_meta(ctx, start=None, end=None, from_file=None, workers=0):
    cond = HKEXFile.type == "MR"
    cond &= HKEXFile.fid.is_null(False)
    if start:
        cond &= HKEXFile.fid >= int(start)
    if end:
        cond &= HKEXFile.fid <= int(end)
    if from_file:
        with open(from_file, "r", encoding="utf-8") as file_obj:
            include_ids = [int(i.strip()) for i in file_obj if i.strip().isdigit()]
        cond &= HKEXFile.fid.in_(include_ids)
    query = HKEXFile.select(HKEXFile.fid, HKEXFile.stock_code).where(cond).dicts()

    with pw_db.allow_sync():
        tasks = []
        for row in query.where(cond).execute():
            tasks.append((row["fid"]))
    for _ in run_by_batch(set_mr_meta, tasks, workers=workers, batch_size=10000):
        pass


@task
def check_embedding(ctx, start=None, end=None, workers=0, from_file=None):
    cond = []
    if start:
        cond.append(NewFile.id >= int(start))
    if end:
        cond.append(NewFile.id <= int(end))
    if from_file:
        with open(from_file, "r", encoding="utf-8") as file_obj:
            include_ids = [int(i.strip()) for i in file_obj if i.strip().isdigit()]
        cond.append(NewFile.id.in_(include_ids))
    query = NewFile.select(NewFile.id).where(*cond).dicts()
    with pw_db.allow_sync():
        tasks = [row["id"] for row in query.execute()]
    for _ in run_by_batch(_check_embedding, tasks, workers=workers, batch_size=10000):
        pass


async def _check_embedding(fid):
    from remarkable.models.embedding import Embedding

    file = await NewFile.find_by_id(fid)
    if not file:
        logger.warning(f"file: {fid} not found")
        return
    pdfinsight_reader = PdfinsightReader.from_path(file.pdfinsight_path(abs_path=True))
    total_elements = pdfinsight_reader.max_index + 1

    query = (
        Embedding.select(Embedding.index, peewee.fn.COUNT(Embedding.id).alias("count"))
        .where(Embedding.file_id == fid)
        .group_by(Embedding.index)
        .order_by(Embedding.index)
        .tuples()
    )
    embedding_counts = await embedding_pw_db.execute(query)
    existing_indices = {index for index, _ in embedding_counts}
    missing_indices = set(range(total_elements)) - existing_indices
    if not missing_indices:
        logger.info(f"All embeddings exist for fid: {fid}, total elements: {total_elements}")
    else:
        need_process_indices = []
        for index in sorted(missing_indices):
            ele_type, element = pdfinsight_reader.find_element_by_index(index)
            if ele_type in ("IMAGE", "PAGE_HEADER", "PAGE_FOOTER"):
                continue
            need_process_indices.append(index)
        logger.warning(f"Missing embeddings for fid: {fid}, missing indices: {need_process_indices}")
        for index in need_process_indices:
            await create_and_save_embeddings(fid, need_sub_element=True, element_index=index)


@task
@sync
async def list_online_users(ctx):
    redis_client = db.init_rdb()
    online_uids = []
    for key in redis_client.scan_iter(match="*:uid:*"):
        online_uids.append(key.split(":")[-1])

    if not online_uids:
        print("当前没有在线用户")
        return
    users = await pw_db.execute(AdminUser.select().where(AdminUser.id.in_(online_uids)))
    max_id_length = max(len(str(user.id)) for user in users) if users else 0

    id_width = max(max_id_length, 6)

    print(f"| {'用户ID'.ljust(id_width)} | 用户名 |")
    print(f"| {'-' * id_width} | ------ |")
    for user in users:
        print(f"| {str(user.id).ljust(id_width)} | {user.name} |")


@task
@sync
async def del_agm_file(ctx, fid):
    """删除错误爬取到的AGM文件"""
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7135
    async with pw_db.atomic():
        update_query = HKEXFileMeta.update(deleted_utc=generate_timestamp()).where(HKEXFileMeta.fid == fid)
        await pw_db.execute(update_query)
        logger.info(f"{fid} HKEXFileMeta deleted_utc updated!")

        result_delete_query = AGMResult.delete().where(AGMResult.file_id == fid).returning()
        agm_result_deleted = await pw_db.execute(result_delete_query)
        logger.info(f"{fid} AGMResult deleted! agm_result_deleted count: {agm_result_deleted}")


@task
@sync
async def del_qr_file(ctx, fid):
    """删除错误爬取到的QR文件"""
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7591#note_753630
    async with pw_db.atomic():
        update_query = HKEXFileMeta.update(deleted_utc=generate_timestamp()).where(HKEXFileMeta.fid == fid)
        await pw_db.execute(update_query)
        logger.info(f"{fid} HKEXFileMeta deleted_utc updated!")

        # special_answer
        ratio_qid = await pw_db.scalar(
            NewQuestion.select(NewQuestion.id).where(NewQuestion.fid == fid, NewQuestion.mold == special_mold.v3r_id)
        )
        result_delete_query = NewSpecialAnswer.delete().where(NewSpecialAnswer.qid == ratio_qid).returning()
        ratio_result_deleted = await pw_db.execute(result_delete_query)
        logger.info(f"{fid} NewSpecialAnswer deleted! ratio_result_deleted count: {ratio_result_deleted}")

        # disclosure_answer
        disclosure_delete_query = DisclosureResult.delete().where(DisclosureResult.fid == fid).returning()
        disclosure_result_deleted = await pw_db.execute(disclosure_delete_query)
        logger.info(f"{fid} DisclosureResult deleted! disclosure_result_deleted count: {disclosure_result_deleted}")
