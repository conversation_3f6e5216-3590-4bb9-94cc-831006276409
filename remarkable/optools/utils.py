import logging
import tempfile
import time
import traceback
from collections import defaultdict
from pathlib import Path

import openpyxl
from openpyxl.styles import Ali<PERSON><PERSON>, Font, PatternFill
from openpyxl.utils import get_column_letter

from remarkable.config import get_config
from remarkable.optools.fm_upload import FMUploader

logger = logging.getLogger(__name__)


REVIEW_URL = (
    get_config("web.scheme")
    + "://"
    + get_config("web.domain").rstrip("/")
    + "/#/hkex/annual-report-checking/report-review/{qid}?fileId={fid}&schemaId={mid}&rule={rule}&delist=0"
)


def save_to_excel(
    file_name_prefix: str | Path, data: list[list | tuple], headers: list[str], link_col_indices: set[int] = None
):
    """
    file_name_prefix: 文件名前缀，不包含.xlsx
    data: 要写入的数据
    headers: 列名， 例如： ["URL", "B8 Answer", "B9 Answer", "B10 Answer"]
    col_widths: 每列的指定列宽，例如{"A": 100, "B": 20}
    link_col_indices: 需要显示为链接的列
    """
    workbook = openpyxl.Workbook()
    worksheet = workbook.active

    # 自动计算列宽（用数值平均值）
    header_widths = {get_column_letter(i): len(h) + 2 for i, h in enumerate(headers, start=1)}
    col_widths = defaultdict(int)
    for row_data in data:
        for i, value in enumerate(row_data, start=1):
            if not value:
                continue
            col, width = get_column_letter(i), max(len(v) for v in str(value).split("\n"))
            col_widths[col] += width
    count_rows = len(data)
    for col, total_width in col_widths.items():
        width = int(total_width / count_rows) + 2
        if width < header_widths[col]:
            width = header_widths[col]
        if width >= 30:
            width = int(width * 0.85)
            if width > 60:
                width = 60
        elif width < 10:
            width = 10
        worksheet.column_dimensions[col].width = width

    # 标题行
    worksheet.append(headers)
    # 设置标题字号、背景色
    bold = Font(bold=True, size=11)
    alignment = Alignment(horizontal="center", vertical="center")
    blue_fill = PatternFill(start_color="FFB7DEE8", end_color="FFB7DEE8", fill_type="solid")
    for col in [get_column_letter(i) for i in range(1, len(headers) + 1)]:
        cell_pos = f"{col}1"
        worksheet[cell_pos].font = bold
        worksheet[cell_pos].fill = blue_fill
        worksheet[cell_pos].alignment = alignment
    for row in data:
        try:
            worksheet.append(row)
        except:  # noqa
            logger.error(f"write row failed: {row}")
            logger.error(traceback.format_exc())

    # 自动换行
    alignment = Alignment(wrap_text=True)
    for row in worksheet.iter_rows(min_row=2):
        for i, cell in enumerate(row):
            cell.alignment = alignment
            value = cell.value
            if not isinstance(value, str) or not value.startswith("http"):
                continue
            if link_col_indices and i not in link_col_indices:
                continue
            cell.font = Font(underline="single", color="0000FF")  # 设置为蓝色且有下划线
            cell.hyperlink = value

    with tempfile.NamedTemporaryFile(prefix=f"{file_name_prefix}_{time.strftime('%Y%m%d')}_", suffix=".xlsx") as tmp_fp:
        excel_path = Path(tmp_fp.name)
        workbook.save(excel_path)
        logger.info(f"run over: {excel_path.absolute()}")
        FMUploader().upload(excel_path)
