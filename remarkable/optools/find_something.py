import asyncio
import csv
import json
import logging
import os
import re
import tempfile
from collections import defaultdict, namedtuple
from datetime import datetime
from multiprocessing import Pool
from pathlib import Path
from typing import Dict, Tuple

import fire
import httpx
import openpyxl
import pandas as pd
from openpyxl import Workbook, load_workbook
from openpyxl.styles import Alignment, Font
from pdfparser.imgtools.gen_page_image import get_page_corrective_bitmap
from peewee import fn
from PIL.Image import Image
from playhouse.postgres_ext import ServerSide

from remarkable.answer.node import AnswerItem
from remarkable.common.common import JURA21_COLS, EmptyAnswer
from remarkable.common.constants import AnnualReportEnum, DocType, PolicyEsgRules
from remarkable.common.exceptions import PdfInsightNotFound
from remarkable.common.multiprocess import run_by_batch
from remarkable.common.pattern import MatchMulti, NeglectPattern, PatternCollection
from remarkable.common.storage import localstorage
from remarkable.common.util import clean_txt, extract_hyperlinks_from_pdf, outline_to_box, standard_stock
from remarkable.db import embedding_pw_db, loop_wrapper, pw_db
from remarkable.models.answer import Answer, NewAnswer
from remarkable.models.embedding import Embedding, SyllabusEmbedding
from remarkable.models.file_esg_xref import FileESGxREF
from remarkable.models.hkex_file import HKEXFile
from remarkable.models.hkex_file_meta import HKEXFileMeta
from remarkable.models.mold import SpecialMold, special_mold
from remarkable.models.new_file import NewFile
from remarkable.models.new_mold import NewMold
from remarkable.models.new_question import NewQuestion
from remarkable.models.poll_meta import POLLMeta
from remarkable.models.rule_reference import RuleReference
from remarkable.models.user import AdminUser
from remarkable.optools.fm_upload import FMUploader
from remarkable.optools.stat_util import AnswerMeasure, PresetAnswerCompare, PresetAnswerPrepare, Url

# 获取Disclosure16 答案是ND的文件id
from remarkable.pdfinsight.reader import PdfinsightReader, PdfinsightReaderBase
from remarkable.pdfinsight.reader_table import PdfinsightTable
from remarkable.plugins.hkex.handlers_util import RuleAnswerHandlerUtil
from remarkable.plugins.hkex.utils import MoldAnswer
from remarkable.predictor.eltype import ElementClassifier
from remarkable.rule.hkex_disclosure_rules import ND
from remarkable.schemas.esg import SpecialESGAnswerSchema
from remarkable.services.ar import sync_ar_answer_result
from remarkable.services.file import get_ratio4_by_file
from remarkable.services.retrieval import retrieval_by_keywords

AnswerRow = namedtuple(
    "AnswerRow", ["fid", "stock_code", "report_year", "doc_type", "disclosure16_answer", "url", "first_page_has_images"]
)
place_pattern = PatternCollection(r"orporated", re.I)

logger = logging.getLogger(__name__)

# with open('./first_page_has_text', 'r') as file_obj:
#     cache_file = [int(i.strip()) for i in file_obj.readlines() if i.strip().isdigit()]


def save_to_csv(results, file_name):
    df = pd.DataFrame(results)
    with tempfile.NamedTemporaryFile(prefix=file_name, suffix=".csv") as tmp_fp:
        df.to_csv(tmp_fp, index=False)
        FMUploader().upload(tmp_fp.name)
        logging.info(f"Results exported to {tmp_fp.name}")


@loop_wrapper()
async def find_disclosure16_is_nd():
    fids = []
    files = await NewFile.list_by_range(mold=26, start=53000)
    tasks = []
    logging.info(f"task count: {len(files)}")

    for file in files:
        tasks.append((file.id))

    for rets in run_by_batch(get_fid, tasks, workers=6):
        fids.extend([i for i in rets if i])

    with open("fids", "w") as file_obj:
        file_obj.writelines(fids)

    logging.info(f"ret count: {len(fids)}")
    logging.info("success!")


@loop_wrapper()
async def get_fid(fid):
    question = await NewQuestion.find_by_fid_mid(fid, 26)
    for item in question.preset_answer.get("userAnswer", {}).get("items", []):
        if "Incorporated Place (Hong Kong)" not in item["key"]:
            continue
        value = item["value"]
        if value == "No Disclosure":
            return f"{fid}\n"
        return []


def get_has_image_fid(pdfinsight_path, fid):
    try:
        reader = PdfinsightReaderBase(localstorage.mount(pdfinsight_path))
    except PdfInsightNotFound:
        return None
    except AttributeError:
        return None
    images = reader.data.get("images", [])
    shapes = reader.data.get("shapes", [])
    images = [image for image in images if image["page"] == 0]
    shapes = [shape for shape in shapes if shape["page"] == 0]
    if images or shapes:
        logging.info(f"file {fid} first page has images")
        return fid
    return None


@loop_wrapper()
async def find_first_page_has_images():
    has_images_fids, all_fids = [], []
    files = await NewFile.list_by_range(mold=26)
    tasks = []
    with open("/opt/scriber/fids", "r") as file_obj:
        include_ids = [int(i.strip()) for i in file_obj.readlines() if i.strip().isdigit()]

    files = [file for file in files if file.id in include_ids]
    logging.info(f"task count: {len(files)}")

    for file in files:
        if not file.pdfinsight_path():
            continue
        tasks.append((file.pdfinsight_path(), file.id))
        all_fids.append(file.id)

    for rets in run_by_batch(get_has_image_fid, tasks, workers=12):
        has_images_fids.extend([i for i in rets if i])
    not_has_images_fids = sorted(set(all_fids).difference(set(has_images_fids)))
    has_images_fids.sort()

    has_images_file_metas = await pw_db.execute(HKEXFileMeta.select().where(HKEXFileMeta.fid.in_(has_images_fids)))
    not_has_images_file_metas = await pw_db.execute(
        HKEXFileMeta.select().where(HKEXFileMeta.fid.in_(not_has_images_fids))
    )
    work_book = Workbook()
    sheet = work_book.active
    sheet.append(
        ("fid", "stock_code", "report_year", "doc_type", "disclosure16 answer", "url", "first_page_has_images")
    )
    for has_images, file_metas in ((True, has_images_file_metas), (False, not_has_images_file_metas)):
        for file_meta in file_metas:
            if file_meta.doc_type not in DocType.qr_values():
                continue
            question = await NewQuestion.find_by_fid_mid(file_meta.fid, 26)
            disclosure16_value = ""
            if question and question.preset_answer:
                for item in question.preset_answer.get("userAnswer", {}).get("items", []):
                    if "Incorporated Place (Hong Kong)" not in item["key"]:
                        continue
                    disclosure16_value = item["value"]
                    break
            sheet.append(
                (
                    file_meta.fid,
                    file_meta.stock_code,
                    file_meta.report_year,
                    DocType.status_anno_map().get(file_meta.doc_type, file_meta.doc_type),
                    disclosure16_value,
                    f"http://hkex.test.paodingai.com/#/search?fileid={file_meta.fid}",
                    has_images,
                )
            )

    work_book.save("first_page_has_images.xlsx")
    logging.info(f"ret count: {len(has_images_fids)}")
    logging.info("write success!")


def stat():
    old_file = "/opt/scriber/first_page_has_images_no_ocr.xlsx"
    new_file = "/opt/scriber/first_page_has_images_with_ocr.xlsx"

    # read answerRow from old_file and new_file
    old_answer_rows = {}
    new_answer_rows = {}
    old_workbook = load_workbook(os.path.join(old_file))
    old_sheet = old_workbook.active
    rows = list(old_sheet.rows)
    for row in rows[1:]:
        answer_row = AnswerRow(*[cell.value for cell in row])
        old_answer_rows[row[0].value] = answer_row

    new_workbook = load_workbook(os.path.join(new_file))
    new_sheet = new_workbook.active
    rows = list(new_sheet.rows)
    for row in rows[1:]:
        answer_row = AnswerRow(*[cell.value for cell in row])
        new_answer_rows[row[0].value] = answer_row

    # compare
    ret = []
    for fid, old_answer in old_answer_rows.items():
        old_answer_value = old_answer.disclosure16_answer
        if old_answer_value != ND:
            continue
        new_answer = new_answer_rows.get(fid)
        if not new_answer:
            continue
        new_answer_value = new_answer.disclosure16_answer
        if new_answer.first_page_has_images is False:
            continue
        first_page_has_special_para = has_special_para(fid)
        if first_page_has_special_para:
            print(f"{fid} has place para")
            continue
        ret.append(
            (
                old_answer.fid,
                old_answer.stock_code,
                old_answer.report_year,
                old_answer.doc_type,
                old_answer.url,
                old_answer_value,
                new_answer_value,
                new_answer.first_page_has_images,
            )
        )
    ret.sort(key=lambda x: x[0], reverse=True)
    print(len(ret))
    workbook = Workbook()
    sheet = workbook.active
    sheet.append(
        (
            "fid",
            "stock_code",
            "report_year",
            "doc_type",
            "url",
            "old_answer",
            "new_answer",
            "maybe_image",
        )
    )
    for item in ret:
        sheet.append(item)
    workbook.save("result.xlsx")


@loop_wrapper()
async def has_special_para(fid):
    file = await NewFile.find_by_id(fid)
    try:
        reader = PdfinsightReader(file.pdfinsight_path(abs_path=True))
    except PdfInsightNotFound:
        return False
    except AttributeError:
        return False
    for index, items in reader.page_element_dict.items():
        if index > 0:
            break
        for item in items:
            if item.data["class"] == "PARAGRAPH":
                try:
                    _, element = reader.find_element_by_index(item.index)
                except IndexError:
                    break
                if not element:
                    continue
                if place_pattern.nexts(clean_txt(element["text"])):
                    return True
    return False


async def find_three_column_files():
    workbook = openpyxl.Workbook()
    worksheet = workbook.active
    worksheet.append(("url", "three_column_pages", "mid"))

    query = NewFile.select().where((NewFile.mold_list.contains_any(1, 2))).order_by(NewFile.id.desc())
    for file in await pw_db.execute(query):
        try:
            reader = PdfinsightReaderBase(file.pdfinsight_path(abs_path=True))
        except PdfInsightNotFound:
            print(f"{file.fid} PdfInsightNotFound")
            continue
        except AttributeError:
            print(f"{file.fid} AttributeError")
            continue

        three_column_pages = []
        for page_idx, page in reader.data["pages"].items():
            column = page.get("column", {}).get("grid", {}).get("columns")
            if column and len(column) == 2:
                print("find three column", file.id, int(page_idx) + 1)
                three_column_pages.append(int(page_idx) + 1)
        if not three_column_pages:
            continue

        url = f"http://100.64.0.105:55647/#/search?fileid={file.id}"
        print(file.mold_list)
        print(three_column_pages)
        worksheet.append(
            (url, ",".join((str(i) for i in three_column_pages)), ",".join((str(i) for i in file.mold_list)))
        )

    with tempfile.NamedTemporaryFile(prefix="invalid_follow_tables", suffix=".xlsx") as tmp_fp:
        excel_path = tmp_fp.name
        workbook.save(excel_path)
        FMUploader().upload(Path(excel_path))


def del_rule_a_dirty_rule_results(fid=None):
    # 清除 schema = 5 的question 的rule_result 中的脏数据
    cond = NewQuestion.mold == special_mold.v1_id
    if fid:
        cond &= NewQuestion.fid == fid

    query = (
        NewQuestion.select(NewQuestion.id, NewQuestion.preset_answer, NewQuestion.fid)
        .where(cond)
        .order_by(NewQuestion.id.desc())
    )
    with pw_db.allow_sync():
        for question in ServerSide(query):
            preset_answer = question.preset_answer
            if not preset_answer:
                logging.info(f"{question.fid} preset_answer is None")
                continue
            if preset_answer.get("rule_result"):
                preset_answer["rule_result"] = {"items": []}
                NewQuestion.update(preset_answer=preset_answer).where(NewQuestion.id == question.id).execute()
                logging.info(f"remove rule_result for {question.fid}")


async def remove_jura21_cols(path):
    # 删除导出表格中的jura21相关rule的行
    all_rules = await RuleReference.find_abcd_rule_references()
    alias_rule_map = {rule.main_alias: rule.rule for rule in all_rules}
    workbook = load_workbook(path)
    sheet = workbook.active
    rows = []
    for row in sheet.rows:
        if row[1].value and alias_rule_map.get(row[1].value, "") in (
            list(JURA21_COLS) + ["B98", "B99", "B63.2", "B73.1"]
        ):
            continue
        rows.append([i.value for i in row])

    # write to a new excel
    workbook = Workbook()
    sheet = workbook.active
    for row in rows:
        sheet.append(row)
    workbook.save(f"{os.path.splitext(path)[0]}_remove_jura21.xlsx")


async def multi_remove():
    for path in []:
        await remove_jura21_cols(path)
        logging.info(f"{path} done")


def diff_B63_compliance(old_file, new_file):
    # 比较前后两次导出数据中B63的合规的预测答案是否一致，将不一致的导出
    old_data = get_b63_data(old_file)
    new_data = get_b63_data(new_file)

    workbook = Workbook()
    sheet = workbook.active
    sheet.append(["stock_code", "report_year", "before_update", "after_updated"])
    for key, old_value in old_data.items():
        new_value = new_data.get(key)
        if not new_value:
            logging.info(f"{key} not found in new file")
            continue
        if old_value != new_value:
            sheet.append([key[0], key[1], old_value, new_value])
    workbook.save("diff_res.xlsx")


def get_b63_data(path):
    B63_alias = "17.07(1)(b)-SOS"
    res = {}
    workbook = load_workbook(path)
    sheet = workbook.active
    for row in sheet.rows:
        if not row[1].value:
            continue
        if row[1].value and row[1].value != B63_alias:
            continue
        stock_code = row[4].value
        report_year = row[2].value
        res[(stock_code, report_year)] = row[7].value
    return res


KEYWORDS = {
    "ISSB": MatchMulti.compile(r"ISSB", r"IFRS", operator=any),
    "Adoption of Independent Assurance": MatchMulti.compile(
        r"independent assurance",
        # r"external assurance",
        # r"third-party verification",
        # r"report verification",
        # r"verified，independent assurance report",
        # r"Hong Kong Quality Assurance Agency",
        # r"International Standard on Assurance Engagements 3000",
        # r"ISAE3000",
        operator=any,
    ),
    "Details of Independent Assurance": MatchMulti.compile(
        r"independent assurance",
        # r"assurance report，selected sustainability information",
        # r"scope of assurance",
        # r"procedures for assurance",
        # r"level of assurance",
        # r"methodology",
        operator=any,
    ),
    "Independent Assurance on Scope 1 & Scope 2 Emission": MatchMulti.compile(
        r"independent assurance",
        # r"selected sustainability information",
        # r"scope of assurance",
        operator=any,
    ),
    "Scenario analysis": MatchMulti.compile(
        r"climate risks and opportunities",
        r"physical risks, transitional risks",
        r"potential financial implications",
        r"mitigation plan",
        r"response measure",
        operator=any,
    ),
    "Source of scenarios": MatchMulti.compile(r"NGFS", r"IPCC", r"SSP", r"IEA", operator=any),
    "Scope 3 emissions": MatchMulti.compile(r"Scope\s?3", operator=any),
    "Categories of scope 3 emissions": MatchMulti.compile(
        r"purchased goods and services",
        r"capital goods",
        r"fuel- and energy-related activities not included in scope 1 greenhouse gas emissions or scope 2 greenhouse gas emissions",
        r"upstream transportation and distribution",
        r"waste generated in operations",
        r"business travel",
        r"employee commuting",
        r"upstream leased assets",
        r"downstream transportation and distribution",
        r"processing of sold products",
        r"use of sold products",
        r"end-of-life treatment of sold products",
        r"downstream leased assets",
        r"franchise",
        r"investments",
        operator=any,
    ),
}


def process_jura21_file(file_meta: Dict) -> Tuple[str, Dict[str, bool]]:
    logging.info(f"Processing {file_meta["file_id"]=}, {file_meta["report_year"]=}")
    result = dict.fromkeys(RSU_KEYWORDS, False)
    relative_path = os.path.join(file_meta["pdfinsight_path"][:2], file_meta["pdfinsight_path"][2:])
    pdfinsight_path = localstorage.mount(relative_path)
    pdfinsight = PdfinsightReader.from_path(pdfinsight_path)

    for element in pdfinsight.elements_iter():
        element_text = ""
        if "text" in element:
            element_text = element["text"]
        elif element["class"] == "TABLE":
            element_text = PdfinsightTable.to_markdown(element)

        for key, pattern in RSU_KEYWORDS.items():
            if pattern.search(element_text):
                result[key] = True

    # result["AR or Independent"] = "AR" if file_meta["doc_type"] == 1 else "Independent"
    #         label_url = str(Url(fid, 18, question["id"], question["tree_id"], schema_key="C2"))

    return json.dumps(file_meta), result


RSU_KEYWORDS = {
    "Share award scheme": MatchMulti.compile("Share award scheme", operator=any),
    "Long Term Incentive Scheme": MatchMulti.compile("Long Term Incentive Scheme", operator=any),
    "Share incentive plan": MatchMulti.compile("Share incentive plan", operator=any),
    "restricted share unit scheme": MatchMulti.compile("restricted share unit scheme", operator=any),
    "Restricted stock unit scheme": MatchMulti.compile("Restricted stock unit scheme", operator=any),
    "RSU": MatchMulti.compile("\bRSU\b", operator=any),
    "RSU Plan": MatchMulti.compile("\bRSU Plan", operator=any),
    "RSU scheme": MatchMulti.compile("\bRSU scheme", operator=any),
    "RSA scheme": MatchMulti.compile("\bRSA scheme", operator=any),
    "Restricted share award scheme": MatchMulti.compile("Restricted share award scheme", operator=any),
    "Performance share unit": MatchMulti.compile("Performance share unit", operator=any),
    "PSU": MatchMulti.compile("\bPSU\b", operator=any),
    "Share stapled units award scheme": MatchMulti.compile("Share stapled units award scheme", operator=any),
    "Free share ownership plan": MatchMulti.compile("Free share ownership plan", operator=any),
    "Free share award plan": MatchMulti.compile("Free share award plan", operator=any),
    "Share purchase scheme ": MatchMulti.compile("Share purchase scheme ", operator=any),
    "Share appreciation right scheme": MatchMulti.compile("Share appreciation right scheme", operator=any),
}


async def find_jura21_rsu_files():
    results = {}

    cond = NewQuestion.mold.in_((special_mold.v2_id,))
    cond &= HKEXFileMeta.report_year.in_(("2024",))

    query = (
        HKEXFileMeta.select(
            HKEXFileMeta.fid.alias("file_id"),
            HKEXFileMeta.report_year,
            HKEXFileMeta.stock_code,
            HKEXFileMeta.doc_type,
            NewQuestion.id.alias("question_id"),
            NewQuestion.mold.alias("mold_id"),
            NewFile.pid.alias("project_id"),
            NewFile.pdfinsight.alias("pdfinsight_path"),
            NewFile.tree_id,
        )
        .join(NewQuestion, on=(NewQuestion.fid == HKEXFileMeta.fid))
        .join(NewFile, on=(NewFile.id == HKEXFileMeta.fid))
        .filter(cond)
        .order_by(HKEXFileMeta.published.desc())
        .dicts()
    )
    list_file_metas = list(await pw_db.execute(query))

    with Pool(processes=4) as pool:
        file_results = pool.map(process_jura21_file, list_file_metas)

    results.update(dict(file_results))
    with open("jura_21_has_rsu_results.csv", "w", newline="", encoding="utf-8") as csvfile:
        fieldnames = (
            ["rule_B_file_url", "rule_C_file_url", "stock_code", "report_year"]
            + list(RSU_KEYWORDS.keys())
            + [
                "include_all",
                "include_any",
            ]
        )
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        for file_meta, result in results.items():
            file_meta = json.loads(file_meta)
            rule_c_qid = await pw_db.scalar(
                NewQuestion.select(NewQuestion.id).where(
                    (NewQuestion.fid == file_meta["file_id"]) & (NewQuestion.mold == special_mold.v3_id)
                )
            )
            data = {
                "rule_B_file_url": str(
                    Url(file_meta["file_id"], 15, file_meta["question_id"], file_meta["tree_id"], schema_key="B93.1")
                ),
                "rule_C_file_url": str(
                    Url(file_meta["file_id"], 18, rule_c_qid, file_meta["tree_id"], schema_key="C5")
                ),
                "include_all": all(result.values()),
                "include_any": any(result.values()),
            }
            data.update(result)
            data["report_year"] = file_meta["report_year"]
            data["stock_code"] = file_meta["stock_code"]
            writer.writerow(data)


async def find_esg_policy_files():
    results = {}

    cond = FileESGxREF.activated
    cond &= NewQuestion.mold.in_(special_mold.esg_mids)
    cond &= HKEXFileMeta.report_year.in_(("2022", "2023", "2024"))

    query = (
        HKEXFileMeta.select(
            HKEXFileMeta.fid.alias("file_id"),
            HKEXFileMeta.report_year,
            HKEXFileMeta.stock_code,
            HKEXFileMeta.doc_type,
            NewQuestion.id.alias("question_id"),
            NewQuestion.mold.alias("mold_id"),
            NewFile.pid.alias("project_id"),
            NewFile.pdfinsight.alias("pdfinsight_path"),
        )
        .join(NewQuestion, on=(NewQuestion.fid == HKEXFileMeta.fid))
        .join(NewFile, on=(NewFile.id == HKEXFileMeta.fid))
        .join(FileESGxREF, on=(FileESGxREF.fid == HKEXFileMeta.fid))
        .filter(cond)
        .order_by(HKEXFileMeta.published.desc())
        .dicts()
    )
    list_file_metas = await pw_db.execute(query)

    with Pool(processes=4) as pool:
        file_results = pool.map(process_jura21_file, list_file_metas)

    results.update(dict(file_results))
    # Flatten the results
    # results = dict(item for sublist in all_results for item in sublist)
    with open("search_results.csv", "w", newline="", encoding="utf-8") as csvfile:
        fieldnames = (
            ["file_url", "stock_code", "report_year"]
            + list(KEYWORDS.keys())
            + [
                "include_all",
                "include_any",
                "AR or Independent",
            ]
        )
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        for fid, result in results.items():
            data = {
                "file_url": f"http://100.64.0.105:55647/#/search?fileid={fid}",
                "include_all": all(result.values()),
                "include_any": any(result.values()),
            }
            data.update(result)
            writer.writerow(data)


async def find_all_charts_for_file(fid, base_path="/tmp"):
    file = await NewFile.find_by_id(fid)
    pdfinsight_reader = PdfinsightReader(file.pdfinsight_path(abs_path=True))
    path = Path(f"{base_path}/{file.id}")
    path.mkdir(exist_ok=True)
    for element in pdfinsight_reader.elements_iter(filter_func=ElementClassifier.is_chart):
        logging.info(f"process chart element: {element['index']=}")
        interdoc_page = pdfinsight_reader.data["pages"][str(element["page"])]
        scale = 2.0  # scale 越大图片会更清楚
        pillow_image: Image = get_page_corrective_bitmap(
            file.pdf_path(abs_path=True),
            element["page"],
            interdoc_page,
            scale=scale,
        )
        box = outline_to_box(element["outline"])
        crop_box = (
            box["box_left"] * scale,
            box["box_top"] * scale,
            box["box_right"] * scale,
            box["box_bottom"] * scale,
        )
        element_image = pillow_image.crop(crop_box)
        element_image.save(path / f"{element['index']}.png")


async def get_user_name(qid):
    query = NewAnswer.select(NewAnswer.user).where(NewAnswer.question == qid)
    user_name = await pw_db.prefetch(query)
    if user_name:
        return user_name[0].user.name
    return ""


async def find_significant_investment():
    """
    1. 从NewQuestion中查询出mold=18的所有fid, 同时join hkex_file_meta表拿到report_year  stock_code
    2. 使用fid 调用 get_ratio4_by_file 方法
    3. 返回结果 根据 flag_cond 判断 重大投资比例是否大于20% , 重大投资比例是否大于5%, 所有flag_cond为5的 是否都大于5
    4. 最终结果导出到csv中 数据列包含 fid, url , report_year, stock_code, sum_is_greater_20, all_is_greater_5
    """
    # Query records with mold=18 and join with HKEXFileMeta
    query = (
        NewQuestion.select(
            NewQuestion.id,
            NewQuestion.fid,
            HKEXFileMeta.report_year,
            HKEXFileMeta.stock_code,
            NewFile.tree_id,
        )
        .join(
            HKEXFileMeta,
            on=(NewQuestion.fid == HKEXFileMeta.fid),
        )
        .join(NewFile, on=(NewFile.id == HKEXFileMeta.fid,))
        .where(
            NewQuestion.mold == 18,
            HKEXFileMeta.report_year.in_(("2023", "2024")),
            # NewQuestion.fid == 70615,
        )
        .dicts()
    )

    questions = await pw_db.execute(query)

    results = []
    for question in questions:
        fid = question["fid"]
        question_ins = await NewQuestion.find_by_id(question["id"])
        answer = await question_ins.get_user_merged_answer()
        if not answer:
            continue
        skip_flag = True

        for item in answer.get("userAnswer", {}).get("items", []):
            key_path = item.get("key")
            if "C2" in key_path:
                skip_flag = False
                break
        if skip_flag:
            continue
        try:
            ratio4_result = await get_ratio4_by_file(fid)

            sum_is_ge_20 = False
            all_is_gt_5 = True
            any_is_gt_5 = False
            all_less_than_5 = True
            has_5_percent_condition = False
            count_5_percent_condition = 0

            for formula in ratio4_result.formulas:
                ratio = formula.get("ratio", 0)
                flag_cond = formula.get("flag_cond", "")

                if flag_cond == "≥ 20%" and ratio >= 0.20:
                    sum_is_ge_20 = True

                if flag_cond == "≥ 5%":
                    count_5_percent_condition += 1
                    has_5_percent_condition = True
                    if ratio >= 0.05:
                        any_is_gt_5 = True
                        all_less_than_5 = False
                    else:
                        all_is_gt_5 = False

            if not has_5_percent_condition:
                all_is_gt_5 = False
                any_is_gt_5 = False

            # if count_5_percent_condition == 0:
            #     all_less_than_5 = 0
            user_name = await get_user_name(question["id"])
            url = f"http://100.64.0.105:55647/#/search?fileid={fid}"
            label_url = str(Url(fid, 18, question["id"], question["tree_id"], schema_key="C2"))
            results.append(
                {
                    "fid": fid,
                    "search_url": url,
                    "label_url": label_url,
                    "report_year": question["report_year"],
                    "stock_code": standard_stock(question["stock_code"]),
                    "label_user": user_name,
                    "大于等于20": sum_is_ge_20,
                    "所有都大于5": all_is_gt_5,
                    "任一大于5": any_is_gt_5,
                    "所有都小于5": all_less_than_5,
                    "百分比是5的公式数量": count_5_percent_condition,
                    "大于等于20_现在都小于5": sum_is_ge_20 and all_less_than_5,
                    "小于20_现在任一大于5": not sum_is_ge_20 and any_is_gt_5,
                }
            )

        except Exception as e:
            logging.exception(f"Error processing fid {fid}: {str(e)}")

    if results:
        import pandas as pd

        df = pd.DataFrame(results)
        with tempfile.NamedTemporaryFile(prefix="significant_investment_results", suffix=".csv") as tmp_fp:
            df.to_csv(tmp_fp, index=False)
            FMUploader().upload(tmp_fp.name)
            logging.info(f"Results exported to {tmp_fp.name}")

        total = len(df)
        greater_20 = df["大于等于20"].sum()
        all_greater_5 = df["所有都大于5"].sum()
        any_greater_5 = df["任一大于5"].sum()
        logging.info(f"Total records processed: {total}")
        logging.info(f"大于等于20: {greater_20} ({greater_20 / total * 100:.2f}%)")
        logging.info(f"所有都大于5: {all_greater_5} ({all_greater_5 / total * 100:.2f}%)")
        logging.info(f"任一大于5: {any_greater_5} ({any_greater_5 / total * 100:.2f}%)")
    else:
        logging.warning("No results found to export")


pattern = NeglectPattern.compile(
    match=MatchMulti.compile("(join|j oin).*?group", operator=any),
    unmatch=MatchMulti.compile("(company|director|appointed)", operator=any),
)


async def find_special_rules(mid=33, special_rules=None):
    """
    1. 查询有标注答案同时枚举是ND的所有rule
    2. 查询M24  M25  为 PS NS  ND 的记录

    导出M24  M25的合规答案
    """
    query = (
        NewQuestion.select(
            # NewAnswer.data,
            # NewAnswer.user,
            NewQuestion.fid,
            HKEXFileMeta.stock_code,
            HKEXFileMeta.report_year,
            NewQuestion.id.alias("qid"),
            NewFile.tree_id.alias("tree_id"),
            NewQuestion.preset_answer.alias("preset_answer"),
        )
        # .join(NewQuestion, on=(NewAnswer.question == NewQuestion.id))
        .join(HKEXFileMeta, on=(NewQuestion.fid == HKEXFileMeta.fid))
        .join(NewFile, on=(NewQuestion.fid == NewFile.id))
        .where(NewQuestion.mold == mid)
        .dicts()
    )

    answers = await pw_db.execute(query)

    # mold = await NewMold.find_by_id(mid)
    # mold_schema = MoldSchema(mold.data)
    # special_rules = special_rules or mold_schema.all_rules()
    special_rules = [
        # "M30-the perspectives, skills and experience of proposed INEDs",
        # "Emoluments under service contract.",
        "M25-13.51(1)",
        "M24-a super-majority vote required to approve changes to constitutional documents",
    ]

    # uid_name_map = dict(await pw_db.execute(AdminUser.select(AdminUser.id, AdminUser.name).tuples()))
    results = []
    for answer in answers:
        fid = answer["fid"]
        preset_answer = answer["preset_answer"]
        if preset_answer:
            for special_rule in special_rules:
                for item in preset_answer.get("rule_result", {}).get("items", []):
                    key_path = item.get("key")
                    if special_rule not in key_path:
                        continue
                    compliance = item.get("value")
                    # if AnnualReportEnum.PS in item.get("value"):
                    # url = f"http://100.64.0.105:55647/#/search?fileid={fid}"
                    label_url = str(
                        Url(
                            fid,
                            mid,
                            answer["qid"],
                            answer["tree_id"],
                            schema_key=special_rule,
                            custom_ui=True,
                            need_split=False,
                        )
                    )
                    results.append(
                        {
                            "rule": special_rule,
                            "fid": fid,
                            # "search_url": url,
                            "report_year": answer["report_year"],
                            "stock_code": standard_stock(answer["stock_code"]),
                            "label_url": label_url,
                            # "sub_rule": json.loads(key_path)[1].split(":")[0],
                            "compliance": compliance,
                            # "user": uid_name_map[answer["user"]],
                            # "enum": item.get("value"),
                        }
                    )

    if results:
        save_to_csv(results, "special_agm_results_")
    else:
        logging.warning("No results found to export")


async def find_c1_intended_use_wrong_ps(mid=18, start=None, end=None):
    """ """
    results = []
    with pw_db.allow_sync():
        for file in await NewFile.list_by_range(mid, start, end):
            question = await NewQuestion.find_by_fid_mid(file.id, mid)
            if not question:
                continue

            for user_answer in await NewAnswer.get_answers_by_qid(question.id):
                five_is_ps, five_has_kw, four_is_nd = False, False, False
                for item in user_answer.data.get("userAnswer", {}).get("items") or []:
                    key_path = json.loads(item.get("key"))
                    if key_path[1].split(":")[0] != "C1":
                        continue
                    if key_path[-1].split(":")[0] == "Intended use" and AnnualReportEnum.PS in item.get("value"):
                        five_is_ps = True
                        if any("treasury share" in d["text"].lower() for d in item.get("data", [])):
                            five_has_kw = True
                if five_is_ps and (four_is_nd or not five_has_kw):
                    url = f"http://100.64.0.105:55647/#/search?fileid={file.id}"
                    label_url = str(Url(file.id, 18, question.id, file.tree_id, schema_key="C1"))
                    results.append(
                        {
                            "fid": file.id,
                            "user": user_answer.user.name,
                            "search_url": url,
                            "label_url": label_url,
                        }
                    )
    if results:
        save_to_csv(results, "c1_intended_use_wrong_ps")


async def gen_url():
    # data_dict = {
    #     "89770": {"code": "00038", "year": 2023},
    #     "86007": {"code": "00002", "year": 2023},
    #     "87459": {"code": "00014", "year": 2023},
    #     "87180": {"code": "00023", "year": 2023},
    #     "87863": {"code": "00101", "year": 2023},
    #     "87866": {"code": "00010", "year": 2023},
    #     "88812": {"code": "00066", "year": 2023},
    #     "88630": {"code": "00051", "year": 2023},
    #     "88641": {"code": "00008", "year": 2023},
    #     "88702": {"code": "00019", "year": 2023},
    #     "88779": {"code": "00004", "year": 2023},
    #     "89247": {"code": "00006", "year": 2023},
    #     "89553": {"code": "00003", "year": 2023},
    #     "89702": {"code": "00012", "year": 2023},
    #     "90084": {"code": "00069", "year": 2023},
    #     "96597": {"code": "00083", "year": 2024},
    #     "96724": {"code": "00016", "year": 2024},
    #     "90969": {"code": "01090", "year": 2023},
    #     "93012": {"code": "00018", "year": 2024},
    #     "93789": {"code": "00040", "year": 2024},
    # }
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/5666#note_652086
    results = []
    query = (
        HKEXFileMeta.select()
        .join(FileESGxREF, on=(HKEXFileMeta.fid == FileESGxREF.fid))
        .where(HKEXFileMeta.report_year == "2024", FileESGxREF.activated)
    )
    for data in await pw_db.execute(query):
        stock_code = data.stock_code
        report_year = data.report_year
        # fid = await pw_db.scalar(
        #     HKEXFileMeta.select(HKEXFileMeta.fid)
        #     .join(FileESGxREF, on=(HKEXFileMeta.fid == FileESGxREF.fid))
        #     .where(
        #         HKEXFileMeta.stock_code == stock_code, HKEXFileMeta.report_year == report_year, FileESGxREF.activated
        #     )
        # )
        fid = data.fid
        file = await NewFile.find_by_id(fid)
        if special_mold.ar_esg_id in file.mold_list:
            question = await NewQuestion.find_by_fid_mid(fid, special_mold.ar_esg_id)
            mid = special_mold.ar_esg_id
        else:
            question = await NewQuestion.find_by_fid_mid(fid, special_mold.esg_id)
            mid = special_mold.esg_id
        label_url = str(
            Url(
                file.id,
                mid,
                question.id,
                file.tree_id,
                schema_key="E1-Reference to ISSB Standards",
                custom_ui=True,
                need_split=False,
            )
        )
        results.append(
            {
                "fid": fid,
                "report_year": report_year,
                "stock_code": stock_code,
                "label_url": label_url,
            }
        )

    if results:
        save_to_csv(results, "new_policy_esg")
    else:
        print("No results found to export")


async def sync_poll_result_data():
    query = (
        NewQuestion.select(NewQuestion.id)
        .join(NewFile, on=(NewQuestion.fid == NewFile.id))
        .join(HKEXFileMeta, on=(NewFile.id == HKEXFileMeta.fid))
        .where((NewFile.deleted_utc == 0) & (HKEXFileMeta.doc_type == DocType.POLL))
        .order_by(NewFile.id.desc())
    )

    for question in await pw_db.execute(query):
        await sync_ar_answer_result(question.id)


async def find_wrong_label_data():
    cond = [
        NewFile.deleted_utc == 0,
        NewQuestion.mold.in_((31, 32)),
        # NewQuestion.fid == 71298,
    ]
    query = (
        NewQuestion.select(NewQuestion.id, NewQuestion.fid, NewQuestion.mold, NewQuestion.answer)
        .join(NewFile, on=(NewQuestion.fid == NewFile.id))
        .join(HKEXFileMeta, on=(NewFile.id == HKEXFileMeta.fid))
        .where(*cond)
        .order_by(NewFile.id.desc())
    )
    questions = await pw_db.execute(query)
    for question in questions:
        if not question.answer:
            continue
        new_answer_items = []
        has_error = False
        for item in question.answer["userAnswer"]["items"]:
            manual = item.get("manual")
            special_ui = item.get("special_ui")
            marker = item.get("marker", {}).get("name")
            if manual and special_ui and marker == "admin":
                print(question.id, question.fid, question.mold, item["key"], item["value"])
                has_error = True
            else:
                new_answer_items.append(item)
        if has_error:
            question.answer["userAnswer"]["items"] = new_answer_items
            await pw_db.update(question, only=["answer"])
            logging.info(f"Restore answer for {question.id} done.")


async def find_wrong_esg_answer():
    #     https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6644#note_689334
    cond = [
        NewQuestion.mold == special_mold.policy_ar_id,
        # NewQuestion.fid.in_((68581, 71288, 66219, 70820)),
    ]
    query = NewQuestion.select(NewQuestion.answer, NewQuestion.preset_answer, NewQuestion.fid).where(*cond)

    # mold = await NewMold.find_by_id(special_mold.policy_ar_id)
    results = []
    with pw_db.allow_sync():
        for question in ServerSide(query):
            if not question.answer or not question.preset_answer:
                results.append(
                    {
                        "fid": question.fid,
                        "has_answer": bool(question.answer),
                        "has_preset_answer": bool(question.preset_answer),
                        "answer_has_error": True,
                        "preset_has_error": True,
                    }
                )
            else:
                preset_has_error = False
                answer_has_error = False
                for item in question.preset_answer["userAnswer"]["items"]:
                    if "Policy-ESG" in item["key"]:
                        preset_has_error = True
                for item in question.answer["userAnswer"]["items"]:
                    if "Policy-ESG" in item["key"]:
                        answer_has_error = True
                results.append(
                    {
                        "fid": question.fid,
                        "has_answer": bool(question.answer),
                        "has_preset_answer": bool(question.preset_answer),
                        "answer_has_error": answer_has_error,
                        "preset_has_error": preset_has_error,
                    }
                )

    if results:
        save_to_csv(results, "find_wrong_esg_answer")


def export_policy_esg_data():
    """
    for Joey
    \\COPY (SELECT qa.fid as file_id, REGEXP_REPLACE(REGEXP_REPLACE(REGEXP_REPLACE(qa.field, ':.*$', ''), '^E(\\d+)', 'T\1'), '-', '') as rule, LOWER(CASE WHEN qa.value LIKE '[\"_%\"]' THEN REPLACE(REPLACE(qa.value, '[\"', ''), '\"]', '') ELSE qa.value END) as predict_value, fm.stock_code as stock_code, fm.report_year as report_year, concat('https://jura6-esg.paodingai.com/#/hkex/esg-report-checking/report-review/', qa.id, '?fileId=', qa.fid, '&schemaId=1', '&rule=', replace(substr(qa.field, 0, length(qa.field) - 1), ' ', '%20')) as url FROM (SELECT id, fid, mold, (item ->> 'key')::jsonb ->> 1 as field, (item ->> 'key')::jsonb ->> 2 as words, item ->> 'value' AS value FROM (SELECT id, fid, mold, item FROM question, json_array_elements(preset_answer -> 'userAnswer' -> 'items') item WHERE mold in (31, 32) AND deleted_utc = 0) q) qa JOIN file ON file.id = qa.fid JOIN file_esg_xref ON file.id = file_esg_xref.fid JOIN hkex_file_meta fm ON fm.fid = qa.fid where file_esg_xref.activated and fm.report_year in ('2023', '2024') ORDER BY fm.fid) TO '~/policy-esg.csv' WITH (FORMAT CSV, HEADER)
    """
    pass


async def check_url_accessibility(url: str) -> Tuple[str, bool, str]:
    """
    检查URL是否可访问
    返回: (url, 是否可访问, 错误信息)
    """
    # 跳过邮箱链接
    if url.startswith("mailto:"):
        return url, True, "Email link skipped"

    try:
        transport = httpx.AsyncHTTPTransport(verify=False, retries=1)
        timeout = httpx.Timeout(30.0)  # 设置10秒超时
        headers = {"user-agent": "Mozilla/5.0 (X11; Linux x86_64) Jura (From P.A.I; Partner)"}

        async with httpx.AsyncClient(transport=transport, headers=headers, timeout=timeout) as client:
            # 先尝试GET请求，因为有些网站不接受HEAD请求
            try:
                response = await client.get(url, follow_redirects=True)
                return url, response.status_code < 400, f"Status code: {response.status_code}"
            except Exception:
                # 如果GET请求失败，尝试HEAD请求
                try:
                    response = await client.head(url, follow_redirects=True)
                    return url, response.status_code < 400, f"Status code: {response.status_code}"
                except Exception as inner_e:
                    return url, False, f"Both GET and HEAD failed: {str(inner_e)}"
    except httpx.RequestError as e:
        # 捕获所有请求相关错误
        return url, False, f"Request error: {str(e)}"
    except Exception as e:
        # 捕获其他所有错误
        return url, False, f"Error: {str(e)}"


async def find_esg_reports_with_hyperlinks():
    """
    遍历系统上 2023 年 2024 年的所有 ESG 报告，找到其中包含超链接的例子
    检查每个超链接是否可访问，只返回访问失败的URL列表
    邮箱链接会被跳过
    """
    results = []

    # 查询2023和2024年的ESG报告
    cond = [
        FileESGxREF.activated,  # 确保是有效的ESG报告
        HKEXFileMeta.report_year.in_(["2023", "2024"]),  # 只查询2023和2024年的报告
        HKEXFileMeta.doc_type.in_([DocType.ESG, DocType.AR]),  # ESG报告或年报
        NewQuestion.mold.in_([31, 32]),  # ESG报告或年报
    ]

    # We need to use a model that combines both NewFile and HKEXFileMeta
    # Create a query that selects from both tables
    query = (
        NewQuestion.select(
            NewQuestion.id,
            NewQuestion.fid,
            NewQuestion.mold,
            NewFile.pdf,
            HKEXFileMeta.stock_code,
            HKEXFileMeta.report_year,
            HKEXFileMeta.doc_type,
        )
        .join(NewFile, on=(NewFile.id == NewQuestion.fid))
        .join(HKEXFileMeta, on=(NewFile.id == HKEXFileMeta.fid))
        .join(FileESGxREF, on=(FileESGxREF.fid == NewFile.id))
        .where(*cond)
        .order_by(HKEXFileMeta.report_year.desc(), HKEXFileMeta.stock_code)
        .dicts()  # Convert to dictionaries to make attribute access easier
    )

    questions = await pw_db.execute(query)
    logging.info(f"Found {len(questions)} ESG reports from 2023-2024")

    for question_dict in questions:
        try:
            # Get the file object to access its methods
            file_obj = await NewFile.find_by_id(question_dict["fid"])
            if not file_obj:
                logging.warning(f"File not found for id={question_dict['fid']}")
                continue

            # Get PDF file path
            pdf_path = file_obj.pdf_path(abs_path=True)
            if not os.path.exists(pdf_path):
                logging.warning(f"PDF file not found for fid={question_dict['fid']}")
                continue

            # Extract hyperlinks
            hyperlinks = extract_hyperlinks_from_pdf(pdf_path)

            # If there are hyperlinks, add to results
            if hyperlinks:
                # Find pages with hyperlinks
                hyperlink_pages = sorted(hyperlinks.keys())

                # 收集所有超链接及其页码
                all_urls = []
                all_pages = []
                url_page_map = {}  # 用于记录URL对应的页码

                for page_num in hyperlink_pages:
                    for link_info in hyperlinks[page_num]:
                        url = link_info["url"]
                        all_urls.append(url)
                        page = str(page_num + 1)  # 页码从0开始，显示时+1
                        all_pages.append(page)
                        url_page_map[url] = page

                # 检查所有URL的可访问性
                url_check_tasks = [check_url_accessibility(url) for url in all_urls]
                url_check_results = await asyncio.gather(*url_check_tasks)

                # 分离可访问和不可访问的URL
                accessible_urls = []
                failed_urls = []
                failed_reasons = {}

                for url, is_accessible, reason in url_check_results:
                    if is_accessible:
                        if not url.startswith("mailto:"):  # 不将邮箱链接添加到可访问列表
                            accessible_urls.append(url)
                    else:
                        failed_urls.append(url)
                        failed_reasons[url] = reason

                # 只添加一行结果，包含所有超链接
                if all_urls:
                    url = Url(
                        question_dict["fid"],
                        question_dict["mold"],
                        question_dict["id"],
                        0,
                        schema_key="E1-Reference to ISSB Standards",
                        custom_ui=True,
                        need_split=False,
                    )

                    results.append(
                        {
                            "stock_code": standard_stock(question_dict["stock_code"]),
                            "report_year": question_dict["report_year"],
                            "report_url": str(url),
                            "doc_type": DocType.status_anno_map().get(
                                question_dict["doc_type"], question_dict["doc_type"]
                            ),
                            "pages": ", ".join(all_pages),  # 将页码合并为逗号分隔的字符串
                            "hyperlinks": all_urls,
                            "accessible_urls": accessible_urls,
                            "failed_urls": failed_urls,
                            "failed_reasons": failed_reasons,
                            "hyperlink_count": len(all_urls),
                            "failed_count": len(failed_urls),
                            "url_page_map": url_page_map,
                        }
                    )

        except Exception as e:
            logging.exception(f"Error processing file {question_dict.get('id')}: {str(e)}")

    # 收集所有失败的URL
    all_failed_urls = []
    for result in results:
        all_failed_urls.extend(result["failed_urls"])

    # 将失败的URL输出到表格中
    if all_failed_urls:
        # 打印统计信息
        total_files = len(results)
        total_links = sum([len(r["hyperlinks"]) for r in results])
        total_failed = len(all_failed_urls)
        logging.info(f"Found {total_files} files with hyperlinks, total {total_links} hyperlinks")
        logging.info(
            f"Failed to access {total_failed} hyperlinks ({total_failed / total_links * 100:.2f}% failure rate)"
        )

        # 创建Excel表格
        with tempfile.NamedTemporaryFile(prefix="failed_urls_", suffix=".xlsx") as tmp_fp:
            workbook = Workbook()
            worksheet = workbook.active
            worksheet.title = "Failed URLs"

            # 设置表头
            headers = [
                "stock_code",
                "report_year",
                "report_url",
                "doc_type",
                "page",
                "failed_hyperlinks",
                "error_reason",
            ]
            worksheet.append(headers)

            # 收集失败URL的相关信息
            for result in results:
                stock_code = result["stock_code"]
                report_year = result["report_year"]
                report_url = result["report_url"]
                doc_type = result["doc_type"]

                for url in result["failed_urls"]:
                    page = result["url_page_map"].get(url, "")
                    error_reason = result["failed_reasons"].get(url, "Unknown error")
                    worksheet.append([stock_code, report_year, report_url, doc_type, page, url, error_reason])

            # 设置超链接列的格式
            alignment = Alignment(wrap_text=True)

            for row in worksheet.iter_rows(min_row=2):
                for i, cell in enumerate(row):
                    cell.alignment = alignment
                    # 如果是URL列，设置格式
                    if i in [2, 5] and cell.value and isinstance(cell.value, str) and cell.value.startswith("http"):
                        cell.font = Font(underline="single", color="0000FF")
                        cell.hyperlink = cell.value

            # 调整列宽
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    if cell.value:
                        cell_length = len(str(cell.value))
                        if cell_length > max_length:
                            max_length = cell_length
                adjusted_width = min(max_length + 2, 50)  # 限制最大宽度
                worksheet.column_dimensions[column_letter].width = adjusted_width

            # 保存并上传文件
            workbook.save(tmp_fp.name)
            FMUploader().upload(tmp_fp.name)
            logging.info(f"Results exported to {tmp_fp.name}")

            # 同时在控制台输出失败的URL
            for url in all_failed_urls:
                print(url)
    else:
        logging.warning("No failed URLs found")


async def find_diff_answer():
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7170
    cond = [
        NewQuestion.mold.in_((special_mold.policy_ar_id, special_mold.policy_esg_id)),
        # NewQuestion.fid.in_((68112,)),
    ]
    query = (
        NewQuestion.select(
            NewQuestion.id,
            NewQuestion.answer,
            NewQuestion.preset_answer,
            NewQuestion.fid,
            NewQuestion.mold,
            HKEXFileMeta.stock_code,
        )
        .join(HKEXFileMeta, on=(NewQuestion.fid == HKEXFileMeta.fid))
        .where(*cond)
        .dicts()
    )

    results = []
    with pw_db.allow_sync():
        for question in ServerSide(query):
            list_values = await get_list_values(question)
            suggestion_values = await get_suggestion_values(question)
            for rule in PolicyEsgRules:
                url = Url(
                    question["fid"],
                    question["mold"],
                    question["id"],
                    0,
                    schema_key=rule,
                    custom_ui=True,
                    need_split=False,
                )
                list_value = list_values[rule]
                suggestion_value = suggestion_values[rule]
                results.append(
                    {
                        "stock_code": question["stock_code"],
                        "fid": question["fid"],
                        "url": str(url),
                        "rule": rule,
                        "list_value": list_value.lower(),
                        "suggestion_value": suggestion_value.lower(),
                        "is_equal": suggestion_value.lower() == list_value.lower(),
                    }
                )
    if results:
        save_to_csv(results, "esg_answer_diff_result")
    else:
        logging.warning("No results found to export")


async def get_suggestion_values(question):
    question = await NewQuestion.get_by_id(question["id"])
    rule_answers = {rule: {} for rule in PolicyEsgRules}
    mold = await NewMold.find_by_id(question.mold)
    empty_answer = EmptyAnswer(mold)
    for rule in PolicyEsgRules:
        question.answer = empty_answer.merge(question.answer, special_ui_only=True)
        question.preset_answer = empty_answer.merge(question.preset_answer)
        answer = RuleAnswerHandlerUtil.find_rule_answer(rule, question)
        answer["schema"] = mold.data
        for key in {"user_answer", "preset_answer"}:
            answer[key]["answer"] = RuleAnswerHandlerUtil.convert_to_answer_groups(rule, answer[key]["answer"])
        label_answer = answer["user_answer"]["answer"][0]["items"][0]["value"]
        ai_answer = answer["preset_answer"]["answer"][0]["items"][0]["value"]
        rule_answers[rule] = label_answer or ai_answer
    return rule_answers


async def get_list_values(question):
    question = await NewQuestion.get_by_id(question["id"])
    rule_answers = {rule: {} for rule in PolicyEsgRules}
    empty_answer = None
    if not question.answer or not question.preset_answer:
        mold = await NewMold.get_by_id(question.mold)
        empty_answer = EmptyAnswer(mold).answer
    answer = MoldAnswer(question.answer or empty_answer)
    preset_answer = MoldAnswer(question.preset_answer or empty_answer)
    for rule in answer.schema.root_rules_map:
        if rule.startswith("丨"):
            continue
        ans = answer.get_rule_answer(rule)
        preset_ans = preset_answer.get_rule_answer(rule)
        if rule in {PolicyEsgRules.E6, PolicyEsgRules.E8, PolicyEsgRules.E9}:
            rule_answers[rule].setdefault("disclosure_value", "")
            if any(i.get("special_ui") for i in ans["answer"]):
                # 标注界面标注的答案会合并到 answer 表中，所以这里需要根据 special_ui 过滤下
                rule_answers[rule]["disclosure_value"] = SpecialESGAnswerSchema.build_from_answer_items(
                    *ans["answer"], rule=rule
                ).value
            if not rule_answers[rule]["disclosure_value"]:
                rule_answers[rule]["disclosure_value"] = SpecialESGAnswerSchema.build_from_answer_items(
                    *preset_ans["answer"], rule=rule
                ).value
        else:
            rule_answers[rule].setdefault(
                "disclosure_value",
                AnswerItem(**ans["answer"][0]).enum if ans["answer"] and ans["answer"][0].get("special_ui") else "",
            )
            if not rule_answers[rule]["disclosure_value"]:
                rule_answers[rule]["disclosure_value"] = (
                    AnswerItem(**preset_ans["answer"][0]).enum if preset_ans["answer"] else ""
                )
        label_compliance_value = ans["rule_result"]["value"] if ans["rule_result"].get("special_ui") else ""
        rule_answers[rule].setdefault("compliance_value", label_compliance_value)
        if not rule_answers[rule]["compliance_value"]:
            rule_answers[rule]["compliance_value"] = preset_ans["rule_result"]["value"]

    return {rule: rule_answers[rule].get("disclosure_value", "") for rule in rule_answers}


async def find_wrong_prompt_esg_answer():
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7176
    cond = [
        NewQuestion.mold.in_((special_mold.policy_ar_id, special_mold.policy_esg_id)),
        HKEXFileMeta.report_year.in_(("2023", "2024")),
    ]
    query = (
        NewQuestion.select(NewQuestion.mold, NewQuestion.crude_answer, NewQuestion.fid)
        .join(HKEXFileMeta, on=(NewQuestion.fid == HKEXFileMeta.fid))
        .where(*cond)
    )

    # mold = await NewMold.find_by_id(special_mold.policy_ar_id)
    results = []
    with pw_db.allow_sync():
        for question in ServerSide(query):
            if not question.crude_answer:
                results.append({"fid": question.fid, "mold": question.mold, "no_crude": True})
            elif any(
                (
                    not question.crude_answer.get("E6-Source of scenarios_IPCC"),
                    not question.crude_answer.get("E8-Categories of scope 3 emissions_Cat 1"),
                    not question.crude_answer.get("E9-Scope 3 emissions data by categories_Cat 1"),
                ),
            ):
                results.append(
                    {
                        "fid": question.fid,
                        "mold": question.mold,
                    }
                )

    if results:
        save_to_csv(results, "find_wrong_prompt_esg_answer")


async def find_ratio4_gt_5():
    ret = []
    with open("/tmp/ratio4.csv") as f:
        reader = csv.DictReader(f)
        max_count = 0
        for row in reader:
            ratio4_result = await get_ratio4_by_file(row["fid"])
            if not ratio4_result.formulas:
                continue
            percents = []
            for formula in ratio4_result.formulas:
                percents.append(formula["ratio_str"])
            max_count = max(max_count, len(percents))
            for idx, percent in enumerate(percents):
                row[f"percent_{idx}"] = percent
            ret.append(row)
    save_to_csv(ret, "重大投资判断逻辑变更-添加重大投资比例")


async def export_same_day_agm_reports_to_excel():
    query = (
        HKEXFile.select()
        .join(NewFile, on=(HKEXFile.fid == NewFile.id))
        .join(NewQuestion, on=(NewQuestion.fid == NewFile.id))
        .where((HKEXFile.type == "AGM"))
    )

    file_metas = await pw_db.execute(query)

    grouped_reports = defaultdict(lambda: defaultdict(list))
    for file_meta in file_metas:
        published_date = datetime.fromtimestamp(file_meta.release_time).strftime("%Y-%m-%d")
        stock_code = file_meta.stock_code
        grouped_reports[published_date][stock_code].append(file_meta)

    same_day_reports = {}
    for date, stock_reports in grouped_reports.items():
        same_stock_reports = {stock: reports for stock, reports in stock_reports.items() if len(reports) > 1}
        if same_stock_reports:
            same_day_reports[date] = same_stock_reports

    if not same_day_reports:
        logging.info("未找到同一个股票代码在同一天发布的AGM报告")
        return ""
    results = []
    for date, stock_reports in same_day_reports.items():
        for reports in stock_reports.values():
            for report in reports:
                data = {"release_date": date}
                data.update(report.to_dict())
                results.append(data)
    save_to_csv(results, "same_day_agm_reports")


async def find_esg_summary_chapter():
    """
    从syllabusembedding表中查找包含以下关键词的文本：
    - Reporting Standards
    - Reporting Guidelines
    - Preparation basis
    - About the report

    使用retrieval_by_keywords函数进行检索，并将结果导出到CSV文件中。
    表格列包含：fid, url, text
    """
    # 定义要搜索的关键词
    keywords = [
        "Reporting Standards",
        "Reporting Guidelines",
        "Preparation basis",
        "About the report",
        "About this report",
        "Reporting framework",
        "Reporting principles",
    ]

    # 正则表达式模式，用于直接查询syllabusembedding表
    regex_pattern = (
        "("
        + "|".join(
            [
                "reporting standards",
                "reporting guidelines",
                "preparation basis",
                "about (the|this) report",
                "reporting framework",
                "reporting principles",
            ]
        )
        + ")"
    )

    # 查询doc_type为1和2的文件（AR和ESG报告）
    cond = [
        HKEXFileMeta.doc_type.in_([DocType.AR, DocType.ESG]),  # AR和ESG报告
        HKEXFileMeta.deleted_utc == 0,  # 未删除的文件
        HKEXFileMeta.report_year.in_(("2023", "2024")),
    ]

    query = (
        HKEXFileMeta.select(
            HKEXFileMeta.fid,
            HKEXFileMeta.stock_code,
            HKEXFileMeta.report_year,
            HKEXFileMeta.doc_type,
        )
        .where(*cond)
        .order_by(HKEXFileMeta.report_year.desc(), HKEXFileMeta.stock_code)
        .limit(100)  # 限制处理的文件数量，可以根据需要调整
    )

    file_metas = await pw_db.execute(query)
    logging.info(f"Found {len(file_metas)} files with doc_type 1 or 2")

    results = []
    for file_meta in file_metas:
        fid = file_meta.fid
        stock_code = file_meta.stock_code
        report_year = file_meta.report_year
        doc_type = DocType.status_anno_map().get(file_meta.doc_type, file_meta.doc_type)

        logging.info(f"Processing file {fid} ({stock_code}, {report_year}, {doc_type})")

        # 方法1：使用retrieval_by_keywords函数
        for keyword in keywords:
            try:
                # 使用retrieval_by_keywords函数搜索关键词
                search_results = await retrieval_by_keywords(
                    fid=fid,
                    rule_description=keyword,
                    limit=10,  # 每个关键词返回的最大结果数
                )

                if search_results:
                    # 创建URL
                    url = f"https://jura6-esg.paodingai.com/#/search?fileid={fid}"

                    # 添加结果
                    for result in search_results:
                        text = result.get("text", "")
                        page = result.get("page", "")
                        results.append(
                            {
                                "fid": fid,
                                "stock_code": standard_stock(stock_code),
                                "report_year": report_year,
                                "doc_type": doc_type,
                                "url": url,
                                "keyword": keyword,
                                "page": page,
                                "text": text,
                                "method": "retrieval_by_keywords",
                            }
                        )
            except Exception as e:
                logging.exception(f"Error processing file {fid} with keyword {keyword}: {str(e)}")

        # 方法2：直接查询syllabusembedding表
        try:
            # 使用正则表达式直接查询syllabusembedding表
            syllabus_results = await SyllabusEmbedding.regex_search(
                file_ids=[fid], regex=regex_pattern, ignore_case=True, limit=10
            )
            if syllabus_results:
                # 创建URL
                url = f"https://jura.paodingai.com/#/search?fileid={fid}"

                # 获取文件对象以便访问pdfinsight_reader
                file = await NewFile.find_by_id(fid)
                if file:
                    try:
                        # 获取pdfinsight_reader
                        pdfinsight_reader = PdfinsightReader(file.pdfinsight_path(abs_path=True))

                        # 处理每个结果
                        for syllabus_result in syllabus_results:
                            # 获取索引列表
                            indexes = syllabus_result.get("indexes", [])
                            syllabus = syllabus_result.get("syllabus", [])

                            # 将syllabus转换为字符串以便在结果中显示
                            syllabus_text = "|".join(syllabus) if syllabus else ""

                            # 对于每个索引，获取对应的元素
                            for index in indexes:
                                try:
                                    # 获取元素
                                    _, element = pdfinsight_reader.find_element_by_index(index)
                                    if element:
                                        # 添加结果
                                        results.append(
                                            {
                                                "fid": fid,
                                                "stock_code": standard_stock(stock_code),
                                                "report_year": report_year,
                                                "doc_type": doc_type,
                                                "url": url,
                                                "keyword": syllabus_text,
                                                "page": element.get("page", ""),
                                                "text": element.get("text", ""),
                                                "method": "syllabusembedding",
                                            }
                                        )
                                except Exception as e:
                                    logging.warning(f"Error getting element for index {index}: {str(e)}")
                    except Exception as e:
                        logging.warning(f"Error creating PdfinsightReader for file {fid}: {str(e)}")
        except Exception as e:
            logging.exception(f"Error querying syllabusembedding for file {fid}: {str(e)}")

    # 导出结果到CSV文件
    if results:
        save_to_csv(results, "esg_summary_chapter_results")
        logging.info(f"Exported {len(results)} results to CSV")
    else:
        logging.warning("No results found to export")


async def find_share_class_count():
    R_SHARE_CLASS = re.compile(r"class of share", re.I)
    R_SHARE_TYPE = re.compile(r"type of share", re.I)
    R_OTHER_TYPE = re.compile(r"other type", re.I)
    R_TYPE_DESCRIPTION = re.compile(r"description", re.I)

    # 查询file type是MR的question
    query = (
        NewQuestion.select(
            NewQuestion.id,
            NewQuestion.fid,
            HKEXFile.stock_code,
            NewFile.tree_id,
        )
        .join(HKEXFile, on=(HKEXFile.fid == NewQuestion.fid,))
        .join(NewFile, on=(NewFile.id == HKEXFile.fid,))
        .where(HKEXFile.type == "MR")
        .dicts()
    )

    questions = await pw_db.execute(query)
    results = []

    for q in questions:
        file = await NewFile.find_by_id(q["fid"])
        # 获取文件中share class
        reader = PdfinsightReader(file.pdfinsight_path(abs_path=True))
        share_class = set()
        for table in reader.table_dict.values():
            if not R_SHARE_CLASS.search(table.cells["0_0"]["text"]):
                continue
            for cell_idx, cell in table.cells.items():
                cell_row, cell_col = int(cell_idx.split("_")[0]), int(cell_idx.split("_")[1])
                if R_SHARE_TYPE.search(cell["text"]):
                    right_cell = table.cells.get(f"{cell_row}_{cell['right']}")
                    bottom_cell = table.cells.get(f"{cell['bottom']}_{cell_col}")
                    if right_cell and not R_OTHER_TYPE.search(right_cell["text"]):
                        share_class.add(right_cell["text"])
                        break
                    elif bottom_cell and R_TYPE_DESCRIPTION.search(bottom_cell["text"]):
                        bottom_right_cell = table.cells.get(f"{cell['bottom']}_{cell['right']}")
                        if bottom_right_cell:
                            share_class.add(bottom_right_cell["text"])
                            break
        cond = [
            POLLMeta.mr_fid == q["fid"],
            POLLMeta.stock_code == q["stock_code"],
        ]
        agm_fid = await pw_db.scalar(POLLMeta.select(POLLMeta.agm_fid).where(*cond))
        agm_url = ""
        if agm_fid:
            query = (
                NewQuestion.select(NewQuestion.id.alias("qid"), NewFile.tree_id)
                .join(NewFile, on=(NewFile.id == NewQuestion.fid))
                .where(NewQuestion.fid == agm_fid)
                .dicts()
            )
            result = await pw_db.first(query)
            agm_url = str(Url(agm_fid, special_mold.v6_1_agm_id, result["qid"], result["tree_id"], schema_key=""))

        # 组装数据
        results.append(
            {
                "stock_code": q["stock_code"],
                "fid": q["fid"],
                "url": str(Url(q["fid"], 36, q["id"], q["tree_id"], schema_key="")),
                "counts": len(share_class),
                "share_class": share_class,
                "agm_url": agm_url,
            }
        )

    if results:
        save_to_csv(results, "find_share_class_count")


async def find_poll_agm_mr():
    query = (
        POLLMeta.select(
            POLLMeta.stock_code,
            POLLMeta.poll_fid,
            POLLMeta.agm_fid,
            POLLMeta.mr_fid,
            NewQuestion.id.alias("qid"),
            NewQuestion.fid,
            NewFile.tree_id,
        )
        .join(NewQuestion, on=(POLLMeta.poll_fid == NewQuestion.fid,))
        .join(NewFile, on=(NewFile.id == POLLMeta.poll_fid))
        .where(NewQuestion.mold == special_mold.v6_1_poll_id)
        .dicts()
    )
    poll_metas = await pw_db.execute(query)
    results = []
    for poll_meta in poll_metas:
        poll_file = await NewFile.find_by_id(poll_meta["poll_fid"])
        agm_file = await NewFile.find_by_id(poll_meta["agm_fid"])
        mr_file = await NewFile.find_by_id(poll_meta["mr_fid"])
        poll_question = await NewQuestion.find_by_fid_mid(
            poll_meta["poll_fid"], special_mold.v6_1_poll_id, fields=("id",)
        )
        agm_question = await NewQuestion.find_by_fid_mid(poll_meta["agm_fid"], special_mold.v6_1_agm_id, fields=("id",))
        mr_question = await NewQuestion.find_by_fid_mid(poll_meta["mr_fid"], special_mold.v6_1_mr_id, fields=("id",))
        results.append(
            {
                "stock_code": poll_meta["stock_code"],
                "poll_fid": poll_meta["poll_fid"],
                "poll_url": str(Url(poll_meta["poll_fid"], 38, poll_question.id, poll_file.tree_id, schema_key=""))
                if poll_question
                else "",
                "agm_fid": poll_meta["agm_fid"],
                "agm_url": str(Url(poll_meta["agm_fid"], 37, agm_question.id, agm_file.tree_id, schema_key=""))
                if poll_meta["agm_fid"] and agm_question
                else "",
                "mr_fid": poll_meta["mr_fid"],
                "mr_url": str(Url(poll_meta["mr_fid"], 36, mr_question.id, mr_file.tree_id, schema_key=""))
                if poll_meta["mr_fid"] and mr_question
                else "",
            }
        )

    if results:
        save_to_csv(results, "poll_agm_mr_relations")
    else:
        logging.warning("No results found to export")


async def export_agm_m16_info():
    query = (
        NewQuestion.select(
            NewQuestion.id,
            NewQuestion.preset_answer,
        )
        .where(NewQuestion.mold == SpecialMold.v6_agm_id)
        .dicts()
    )
    questions = await pw_db.execute(query)
    needed_question_ids = set()
    for q in questions:
        if not q["preset_answer"]:
            continue
        user_answer_items = q["preset_answer"]["userAnswer"]["items"]
        for item in user_answer_items:
            if "M16-issue mandate" in item["key"] and item["value"] != "No Disclosure":
                needed_question_ids.add(q["id"])
                break
    # 找出question关联的file
    query = (
        NewQuestion.select(
            NewQuestion.id,
            NewQuestion.fid,
            NewFile.tree_id,
            HKEXFile.stock_code,
            HKEXFile.release_time,
        )
        .join(HKEXFile, on=(HKEXFile.fid == NewQuestion.fid,))
        .join(NewFile, on=(NewFile.id == HKEXFile.fid,))
        .where(NewQuestion.id.in_(needed_question_ids))
        .dicts()
    )
    questions = await pw_db.execute(query)
    # 数据组装
    day_2024_06 = datetime(year=2024, month=6, day=1)

    results = []
    for q in questions:
        agm_release_time = datetime.fromtimestamp(q["release_time"])
        v6_1_question_id = await pw_db.scalar(
            NewQuestion.select(NewQuestion.id).where(
                (NewQuestion.fid == q["fid"]) & (NewQuestion.mold == special_mold.v6_1_agm_id)
            )
        )
        results.append(
            {
                "stock_code": q["stock_code"],
                "release_time": agm_release_time.strftime("%Y-%m-%d %H:%M:%S"),
                "after_2024_06": "T" if agm_release_time >= day_2024_06 else "F",
                "fid": q["fid"],
                "url": str(
                    Url(
                        q["fid"],
                        SpecialMold.v6_1_agm_id,
                        v6_1_question_id,
                        q["tree_id"],
                        schema_key="",
                    )
                ),
            }
        )
    if results:
        save_to_csv(results, "agm_m16_info")
    else:
        logging.warning("No results found to export")


async def find_missing_embedding_esg():
    """
    查找2024年激活状态的ESG文件中缺少embedding数据的文件
    1. 使用pw_db查询2024年所有的处于激活状态的esg文件的文件id
    2. 使用 embedding_pw_db 查询 Embedding表和SyllabusEmbedding表中是否有数据
    3. 将没有数据的fid输出到表格中
    """
    # 查询2024年激活状态的ESG文件
    cond = [
        FileESGxREF.activated,
        HKEXFileMeta.report_year == "2024",
        HKEXFileMeta.doc_type.in_([DocType.ESG, DocType.AR]),
        HKEXFileMeta.deleted_utc == 0,
    ]

    query = (
        HKEXFileMeta.select(
            HKEXFileMeta.fid,
            HKEXFileMeta.stock_code,
            HKEXFileMeta.report_year,
            HKEXFileMeta.doc_type,
            HKEXFileMeta.name,
        )
        .join(FileESGxREF, on=(HKEXFileMeta.fid == FileESGxREF.fid))
        .where(*cond)
        .order_by(HKEXFileMeta.stock_code)
        .dicts()
    )

    esg_files = await pw_db.execute(query)
    logging.info(f"Found {len(esg_files)} ESG files from 2024")

    # 检查每个文件是否有embedding数据
    results = []
    for file in esg_files:
        fid = file["fid"]

        # 检查Embedding表
        has_embedding = await embedding_pw_db.scalar(
            Embedding.select(fn.COUNT(Embedding.id))
            .where(Embedding.file_id == fid, Embedding.deleted_utc == 0)
            .limit(1)
        )

        # 检查SyllabusEmbedding表
        has_syllabus = await embedding_pw_db.scalar(
            SyllabusEmbedding.select(fn.COUNT(SyllabusEmbedding.id))
            .where(SyllabusEmbedding.file_id == fid, SyllabusEmbedding.deleted_utc == 0)
            .limit(1)
        )

        if not has_embedding or not has_syllabus:
            results.append(
                {
                    "fid": fid,
                    "stock_code": file["stock_code"],
                    "report_year": file["report_year"],
                    "doc_type": DocType.status_anno_map().get(file["doc_type"], file["doc_type"]),
                    "name": file["name"],
                    "has_embedding": bool(has_embedding),
                    "has_syllabus": bool(has_syllabus),
                    "flag": bool(has_embedding) and bool(has_syllabus),
                }
            )

    if results:
        # 创建Excel表格
        workbook = Workbook()
        worksheet = workbook.active
        worksheet.title = "Missing Embeddings"

        # 设置表头
        headers = [
            "fid",
            "stock_code",
            "report_year",
            "doc_type",
            "name",
            "has_embedding",
            "has_syllabus",
            "url",
            "flag",
        ]
        worksheet.append(headers)

        # 添加数据
        for result in results:
            worksheet.append(
                [
                    result["fid"],
                    result["stock_code"],
                    result["report_year"],
                    result["doc_type"],
                    result["name"],
                    result["has_embedding"],
                    result["has_syllabus"],
                    f"http://hkex.test.paodingai.com/#/search?fileid={result['fid']}",
                    result["flag"],
                ]
            )

        # 保存并上传文件
        with tempfile.NamedTemporaryFile(prefix="missing_embeddings_", suffix=".xlsx") as tmp_fp:
            workbook.save(tmp_fp.name)
            FMUploader().upload(tmp_fp.name)
            logging.info(f"Results exported to {tmp_fp.name}")
    else:
        logging.info("No missing embeddings found")


def extract_answer_values(answer_items: list[dict]):
    answer_values = []
    for answer in answer_items:
        value = answer["value"]
        if isinstance(value, str):
            answer_values.append(value.lower())
        elif isinstance(value, list):
            answer_values.extend([item.lower() for item in value])
    return answer_values


async def find_policy_esg_e1_answer():
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7611#note_754177
    p_answer = re.compile(r"\bclimate[\-—－–‐_]related")
    cond = [
        NewQuestion.mold.in_((31, 32)),
    ]
    query = (
        NewQuestion.select(
            NewQuestion.id, NewQuestion.mold, NewQuestion.answer, NewQuestion.preset_answer, NewQuestion.fid
        )
        .join(FileESGxREF, on=((NewQuestion.fid == FileESGxREF.fid) & FileESGxREF.activated))
        .where(*cond)
    )
    results = []
    answer_compare = PresetAnswerCompare()
    answer_prepare = PresetAnswerPrepare()
    measure = AnswerMeasure(answer_compare, answer_prepare)
    with pw_db.allow_sync():
        rule = PolicyEsgRules.E1
        for question in ServerSide(query):
            answer, standard = await measure.answer_prepare(question, None)
            question = question.to_dict()
            manual_items = (standard and standard.get(rule, [])) or []
            predict_items = (answer and answer.get(rule, [])) or []
            manual_texts = [AnswerItem.text_only(AnswerItem(**item)) for item in manual_items]
            predict_texts = [AnswerItem.text_only(AnswerItem(**item)) for item in predict_items]
            manual_values = extract_answer_values(manual_items)
            predict_values = extract_answer_values(predict_items)
            manual_answer = ("Comply" if "comply" in manual_values else "No Disclosure") if manual_values else ""
            predict_answer = "Comply" if "comply" in predict_values else "No Disclosure"
            if any(p_answer.search(text) for text in manual_texts) or any(
                p_answer.search(text) for text in predict_texts
            ):
                results.append(
                    {
                        "fid": question["fid"],
                        "predict answer": predict_answer,
                        "manual answer": manual_answer,
                        "predict text": "\n\n".join(predict_texts),
                        "manual text": "\n\n".join(manual_texts),
                        "is_equal": predict_answer == manual_answer,
                        "url": Url(
                            question["fid"],
                            question["mold"],
                            question["id"],
                            0,
                            schema_key=rule,
                            custom_ui=True,
                        ),
                    }
                )

    if results:
        save_to_csv(results, "find_policy_esg_e1_answer")


async def find_need_review_esg_files():
    rules = [
        "MDR 14 part 1 - materiality application",
        "A1 policies - emissions",
        "KPI A1.1 - emission type and data",
        "KPI A1.2 part 1 - Scope 1",
        "KPI A1.3 - hazardous waste",
        "KPI A1.5 - emission target",
        "KPI A1.6 part 2 - waste reduction target",
        "KPI A2.1 - energy consumption",
        "KPI A2.2 - water consumption",
        "KPI A2.3 - energy efficiency targets",
        "KPI A2.4 part 1 - water sourcing",
        "KPI A2.4 part 2 - water efficiency targets",
        "KPI A2.5 - packaging material",
        "B1 law compliance - employment",
        "KPI B1.1 - workforce by types",
        "KPI B1.2 - employee turnover by types",
        "B2 law compliance - health and safety",
        "KPI B2.1 - work-related fatalities",
        "KPI B2.2 - work injury lost days",
        "KPI B3.1 - percentage of employees trained",
        "KPI B3.2 - training hours completed",
        "B4 law compliance - labour standards",
        "KPI B4.1 - review measures to avoid child & forced labour",
        "KPI B4.2 - steps to avoid child & forced labour",
        "KPI B5.1 - number of suppliers",
        "B6 policies - product responsibility",
        "B6 law compliance - product responsibility",
        "KPI B6.1 - products recall",
        "KPI B6.2 - products related complaints",
        "KPI B6.3 - IP rights protection",
        "KPI B6.4 - quality assurance process",
        "B7 law compliance - anti-corruption",
        "KPI B7.1 - legal cases on corruption",
        "B8 policies - community investment",
        "KPI B8.1 - community investment focus",
        "KPI B8.2 - resources contributed",
    ]
    # 查找出2024年的所有处于激活状态的esg报告
    # 遍历其question.answer 将 上面rules种涉及到标注答案的 挑出来
    # 只要有一个rule有标注答案就认为改esg报告需要导出
    # 导出的数据：code,  rule,  report_year ,url ,ai_enum ,manual_enum, ar_or_independent
    # 查询2024年激活状态的ESG文件
    cond = [
        FileESGxREF.activated,
        HKEXFileMeta.report_year == "2024",
        HKEXFileMeta.doc_type.in_([DocType.ESG, DocType.AR]),
        NewQuestion.mold.in_(special_mold.esg_mids),
    ]

    query = (
        NewQuestion.select(
            NewQuestion.id.alias("qid"),
            NewQuestion.fid,
            NewQuestion.mold,
            NewQuestion.answer,
            NewQuestion.preset_answer,
            HKEXFileMeta.stock_code,
            HKEXFileMeta.report_year,
            HKEXFileMeta.doc_type,
            NewFile.tree_id,
        )
        .join(NewFile, on=(NewFile.id == NewQuestion.fid))
        .join(HKEXFileMeta, on=(NewFile.id == HKEXFileMeta.fid))
        .join(FileESGxREF, on=(FileESGxREF.fid == NewQuestion.fid))
        .where(*cond)
        .dicts()
    )

    results = []
    with pw_db.allow_sync():
        for question in ServerSide(query):
            if not question["answer"] or not question["preset_answer"]:
                continue

            # 检查是否有标注答案
            has_manual_answer = False
            rule_manual_answers = {}
            rule_ai_answers = {}

            # 获取人工标注答案
            if "userAnswer" in question["answer"] and "items" in question["answer"]["userAnswer"]:
                for item in question["answer"]["userAnswer"]["items"]:
                    if item.get("special_ui"):
                        key_path = json.loads(item["key"])
                        rule = key_path[1].split(":")[0]
                        if rule in rules:
                            has_manual_answer = True
                            rule_manual_answers[rule] = item.get("value", "")

            # 如果没有标注答案，跳过
            if not has_manual_answer:
                continue

            # 获取AI预测答案
            if "userAnswer" in question["preset_answer"] and "items" in question["preset_answer"]["userAnswer"]:
                for item in question["preset_answer"]["userAnswer"]["items"]:
                    key_path = json.loads(item["key"])
                    rule = key_path[1].split(":")[0]
                    if rule in rules:
                        rule_ai_answers[rule] = item.get("value", "")

            # 生成结果
            doc_type = "Annual Report" if question["doc_type"] == DocType.AR.value else "Standalone ESG Report"

            for rule in rule_manual_answers:
                results.append(
                    {
                        "code": question["stock_code"],
                        "rule": rule,
                        "report_year": question["report_year"],
                        "url": str(
                            Url(
                                question["fid"],
                                question["mold"],
                                question["qid"],
                                question["tree_id"],
                                schema_key=rule,
                                custom_ui=True,
                                need_split=False,
                            )
                        ),
                        "ai_enum": rule_ai_answers.get(rule, ""),
                        "manual_enum": rule_manual_answers.get(rule, ""),
                        "equal": rule_ai_answers.get(rule, "").lower() == rule_manual_answers.get(rule, "").lower(),
                        "ar_or_independent": doc_type,
                    }
                )

    if results:
        save_to_csv(results, "need_review_esg_files.csv")
        logging.info(f"Found {len(results)} ESG files that need review, exported to need_review_esg_files.csv")
    else:
        logging.info("No ESG files found that need review")


async def export_jura_label_answer(fid_path):
    fids = []
    if os.path.isfile(fid_path):
        with open(fid_path, "r") as f:
            fids.extend(int(fid.strip()) for fid in f.readlines())

    query = (
        NewQuestion.select(
            NewQuestion.fid,
            NewQuestion.mold,
            HKEXFileMeta.stock_code,
            HKEXFileMeta.report_year,
            NewQuestion.id.alias("qid"),
            NewFile.tree_id.alias("tree_id"),
        )
        .join(HKEXFileMeta, on=(NewQuestion.fid == HKEXFileMeta.fid))
        .join(NewFile, on=(NewQuestion.fid == NewFile.id))
        .where(NewQuestion.fid.in_(fids))
        .dicts()
    )

    questions = await pw_db.execute(query)

    results = []
    users = list(await pw_db.execute(AdminUser.select(AdminUser.id, AdminUser.name)))
    users = {user.id: user.name for user in users}
    for question in questions:
        fid = question["fid"]
        answers = list(await pw_db.execute(Answer.select().where(Answer.qid == question["qid"]).dicts()))
        for answer in answers:
            for item in answer["data"]["userAnswer"]["items"]:
                answer_item = AnswerItem(**item)
                results.append(
                    {
                        "fid": fid,
                        "stock_code": question["stock_code"],
                        "report_year": question["report_year"],
                        "rule": answer_item.first_word,
                        "user_name": users.get(answer["uid"], ""),
                        "enum": answer_item.enum,
                        "text": answer_item.text_only(answer_item),
                        "label_url": Url(
                            fid,
                            question["mold"],
                            question["qid"],
                            question["tree_id"],
                            schema_key=answer_item.first_word,
                        ),
                    }
                )
    save_to_csv(results, "export_jura_label_answers.csv")


def main():
    fire.Fire(
        {
            "find_disclosure16_is_nd": find_disclosure16_is_nd,
            "find_first_page_has_images": find_first_page_has_images,
            "stat": stat,
            "find_three_column_files": find_three_column_files,
            "multi_remove": multi_remove,
            "diff_B63_compliance": diff_B63_compliance,
            "find_esg_policy_files": find_esg_policy_files,
            "find_jura21_rsu_files": find_jura21_rsu_files,
            "find_all_charts_for_file": find_all_charts_for_file,
            "find_significant_investment": find_significant_investment,
            "find_special_rules": find_special_rules,
            "find_c1_intended_use_wrong_ps": find_c1_intended_use_wrong_ps,
            "gen_url": gen_url,
            "sync_poll_result_data": sync_poll_result_data,
            "find_wrong_label_data": find_wrong_label_data,
            "find_wrong_esg_answer": find_wrong_esg_answer,
            "find_esg_reports_with_hyperlinks": find_esg_reports_with_hyperlinks,
            "find_diff_answer": find_diff_answer,
            "find_wrong_prompt_esg_answer": find_wrong_prompt_esg_answer,
            "find_ratio4_gt_5": find_ratio4_gt_5,
            "find_esg_summary_chapter": find_esg_summary_chapter,
            "find_same_day_agm_reports": export_same_day_agm_reports_to_excel,
            "find_share_class_count": find_share_class_count,
            "find_poll_agm_mr": find_poll_agm_mr,
            "export_agm_m16_info": export_agm_m16_info,
            "find_missing_embedding_esg": find_missing_embedding_esg,
            "find_policy_esg_e1_answer": find_policy_esg_e1_answer,
            "find_need_review_esg_files": find_need_review_esg_files,
            "export_jura_label_answer": export_jura_label_answer,
        },
        name="command",
    )


if __name__ == "__main__":
    main()
