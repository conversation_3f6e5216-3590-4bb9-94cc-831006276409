import logging
import os

from remarkable.common.exceptions import PdfInsightNotFound
from remarkable.common.multiprocess import run_by_batch
from remarkable.common.storage import localstorage
from remarkable.common.util import stream_download
from remarkable.db import logger, loop_wrapper, pw_db
from remarkable.models.hkex_file import HKEXFile
from remarkable.models.hkex_file_meta import HKEXFileMeta
from remarkable.models.new_file import NewFile
from remarkable.pdfinsight.reader import PdfinsightReaderBase
from remarkable.plugins.fileapi.common import make_pdfinsight


@loop_wrapper()
async def main():
    fids = []
    files = await NewFile.list_by_range(mold=26)
    tasks = []
    logging.info(f"task count: {len(files)}")

    for file in files:
        if not file.pdfinsight_path():
            continue
        tasks.append((file.pdfinsight_path(), file.id))

    for rets in run_by_batch(get_fid, tasks, workers=6):
        fids.extend([i for i in rets if i])

    with open("fids", "w") as file_obj:
        file_obj.writelines(fids)

    logging.info(f"ret count: {len(fids)}")
    logging.info("success!")


def get_fid(pdfinsight_path, fid):
    try:
        pdfinsight = PdfinsightReaderBase(localstorage.mount(pdfinsight_path))
    except PdfInsightNotFound:
        return None
    except AttributeError:
        return None
    first_page = pdfinsight.data["pages"]["0"]
    if first_page["statis"].get("ocr_name") == "pai":
        # logging.info(f'file {fid} use ocr pai')
        return f"{fid}\n"
    return None


async def rerun_pdfinsight(stock_code, report_year):
    cond = (HKEXFileMeta.stock_code == stock_code) & (HKEXFileMeta.report_year == report_year)
    cond &= HKEXFileMeta.doc_type == 1
    query = HKEXFileMeta.select(HKEXFileMeta.fid).where(cond).order_by(HKEXFileMeta.fid.desc()).limit(1)
    fid = await pw_db.scalar(query)
    if not fid:
        return
    file = await NewFile.find_by_id(fid)
    if not file:
        return
    try:
        pdf_path = file.pdf_path(abs_path=True)
        if not os.path.exists(pdf_path):
            # Note: 预发布环境的文件可能会丢失，需要重新下载
            logger.warning(f'pdf file not found: {file.id}:"{file.name}", will try to fetch it.')
            hkex_file = await pw_db.first(HKEXFile.select().where(HKEXFile.fid == fid))
            await stream_download(hkex_file.url, pdf_path)
    except Exception:
        logging.info(f"mount file failed: {file.pdf_path()}")
        return
    try:
        PdfinsightReaderBase(file.pdfinsight_path(abs_path=True))
    except PdfInsightNotFound:
        logging.info(f"make pdfinsight: stock {stock_code} file {fid}")
        make_pdfinsight(
            file.pdf_path(),
            file.id,
            run_predict=True,
            mid=28,
        )


async def demo():
    stocks = [
        "01621",
        "00160",
        "00638",
        "01002",
        "01173",
        "01580",
        "03963",
        "01125",
        "00518",
        "01094",
        "00591",
        "02263",
        "02158",
        "00924",
        "00252",
        "03878",
        "00660",
        "00723",
        "01421",
        "00125",
        "00756",
        "00650",
        "00053",
        "00186",
        "00150",
        "00559",
        "02322",
        "08065",
        "01716",
        "02442",
        "00526",
        "01226",
        "00296",
        "01140",
        "01246",
        "00986",
        "00060",
        "00498",
        "01104",
        "01627",
        "02031",
        "01703",
        "08093",
        "00677",
        "01443",
        "08422",
        "00374",
        "00491",
        "00199",
        "01653",
        "01613",
        "00266",
        "02110",
        "00616",
        "01775",
        "00948",
        "00247",
        "01729",
        "01315",
        "01013",
        "01982",
        "00331",
        "01667",
        "01499",
        "09669",
        "00277",
        "01884",
        "09900",
        "08487",
        "00401",
        "00519",
        "01662",
        "01050",
        "01693",
        "02293",
        "01933",
        "01047",
        "01341",
        "01419",
        "03998",
        "00722",
        "01468",
        "00923",
        "01460",
        "00026",
        "00335",
        "03830",
        "00320",
        "01903",
        "01793",
        "08366",
        "00290",
        "02221",
        "01046",
        "00333",
        "00149",
        "08427",
        "00029",
        "02211",
        "00278",
        "01865",
        "00464",
        "01559",
        "00262",
        "08262",
        "02153",
        "00016",
        "00398",
        "01825",
        "09988",
        "00862",
        "01466",
        "01162",
        "01243",
        "01473",
        "01668",
        "03860",
        "00259",
        "00794",
        "02309",
        "01591",
        "02102",
        "09913",
        "01850",
        "01023",
        "00052",
        "03626",
        "00146",
        "00768",
        "00129",
        "01489",
        "06858",
        "01069",
        "00558",
        "01849",
        "01166",
        "01894",
        "00855",
        "00659",
        "01718",
        "00595",
        "00673",
        "06639",
        "00040",
        "01752",
        "00315",
        "00508",
        "01060",
        "00997",
        "00292",
        "00745",
        "00399",
        "00825",
        "00214",
        "02193",
        "01655",
        "00784",
        "00280",
        "00567",
        "01632",
        "01705",
        "00040",
        "00989",
        "01221",
        "00130",
        "08353",
        "00345",
        "00084",
        "00959",
        "01310",
        "00313",
        "00164",
        "01723",
        "08013",
        "01673",
        "01989",
        "00919",
        "01360",
        "00088",
        "00188",
        "02080",
        "01235",
        "01637",
        "00147",
        "02288",
        "00092",
        "00243",
        "00932",
        "00571",
        "00899",
        "00077",
        "00318",
        "01160",
        "02663",
        "00704",
        "01130",
        "00306",
        "02708",
        "00254",
        "01566",
        "00375",
        "01943",
        "02138",
        "01539",
        "02033",
        "00645",
        "00216",
        "00204",
        "00163",
        "03306",
        "02350",
        "02682",
        "00328",
        "02689",
        "00070",
        "03638",
        "08118",
        "00931",
        "01660",
        "00896",
        "00985",
        "00841",
        "00104",
        "03683",
        "01082",
        "00298",
        "00037",
        "01182",
        "00444",
        "00943",
        "01218",
        "01326",
        "00759",
        "01757",
        "00159",
        "00294",
        "00497",
        "02368",
        "00279",
        "00907",
        "01195",
        "00025",
        "00599",
        "00287",
        "01172",
        "00353",
        "00126",
        "01867",
        "00370",
        "00858",
        "03816",
        "03789",
        "00105",
        "00938",
        "00035",
        "02199",
        "03997",
        "00928",
        "01709",
        "00083",
        "01854",
        "00682",
        "01273",
        "00022",
        "00211",
        "01827",
        "00224",
        "00475",
        "01663",
        "00193",
        "03728",
        "01630",
        "01843",
        "08356",
        "01025",
        "01142",
        "08645",
        "06182",
        "00073",
        "06033",
        "00488",
        "01059",
        "09998",
        "02132",
        "00309",
        "06893",
        "00151",
        "00114",
        "01960",
        "00565",
        "08156",
        "01726",
        "01314",
        "00897",
        "08321",
        "00495",
        "09901",
        "01657",
        "00202",
        "00367",
        "00485",
        "01975",
        "01557",
        "01009",
        "08021",
        "02421",
        "01472",
        "00618",
        "01686",
        "01547",
        "00674",
        "01796",
        "00183",
        "00017",
        "01647",
        "01283",
        "00191",
        "01500",
        "00970",
        "00411",
        "01371",
        "00789",
        "01260",
        "00276",
        "03708",
        "01220",
        "08041",
        "01222",
        "06819",
        "08293",
        "01124",
        "01682",
        "08370",
        "00033",
        "01348",
        "08067",
        "01939",
        "00122",
        "00979",
        "01711",
        "00391",
        "01797",
        "01841",
        "08201",
        "01737",
        "08412",
        "00223",
        "00213",
        "00476",
        "00973",
        "00417",
        "08250",
        "01470",
        "01955",
        "01079",
        "00332",
        "08140",
        "00922",
        "00197",
        "00131",
        "00442",
        "00513",
        "00384",
        "02230",
        "02882",
        "00736",
        "03893",
        "01496",
        "01406",
        "00474",
        "09896",
        "01373",
    ]

    for stock in stocks:
        await rerun_pdfinsight(stock, "2023")


if __name__ == "__main__":
    import asyncio

    asyncio.run(demo())
