import logging

import fire

from remarkable.common.constants import DocType
from remarkable.db import pw_db
from remarkable.models.file_esg_xref import FileESGxREF
from remarkable.models.hkex_file import HKEXFile
from remarkable.models.hkex_file_meta import HKEXFileMeta
from remarkable.models.mold import special_mold
from remarkable.models.new_file import NewFile
from remarkable.models.new_question import NewQuestion
from remarkable.optools.find_something import save_to_csv
from remarkable.optools.stat_util import Url
from remarkable.services.esg import has_esg_info

codes = [
    "09888",
    "09618",
    "09961",
    "09999",
    "09698",
    "09866",
    "09901",
    "01698",
    "01179",
    "09898",
    "89888",
    "89618",
    "00945",
    "03660",
    "06288",
    "02518",
]


async def find_no_esg_file():
    """
    1.查找处于激活状态的2020-2024的年报ESG文件
    2.判断这些ESG文件是否包含esg章节
    3.将没有esg章节的报告，其stock_code, fid, url输出到表格中
    """
    con = [
        HKEXFileMeta.report_year >= "2020",
        HKEXFileMeta.report_year <= "2024",
        # HKEXFileMeta.doc_type == DocType.AR.value,
        NewQuestion.mold.in_(special_mold.esg_mids),
        HKEXFileMeta.stock_code.in_(codes),
        FileESGxREF.activated,
    ]
    query = (
        NewQuestion.select(
            NewQuestion.id,
            NewQuestion.fid,
            NewQuestion.mold,
            HKEXFile.headline,
            HKEXFileMeta.stock_code,
            HKEXFileMeta.report_year,
            HKEXFileMeta.doc_type,
            FileESGxREF.activated,
        )
        .join(HKEXFile, on=(NewQuestion.fid == HKEXFile.fid))
        .join(HKEXFileMeta, on=(HKEXFile.fid == HKEXFileMeta.fid))
        .join(FileESGxREF, on=(HKEXFile.fid == FileESGxREF.fid))
        .where(*con)
        .dicts()
    )
    questions = await pw_db.execute(query)

    results = []
    for q in questions:
        file = await NewFile.find_by_id(q["fid"])
        inter_path = file.pdfinsight_path(abs_path=True)
        pdf_path = file.path(abs_path=True)
        try:
            has_esg_chapter = has_esg_info(q["headline"], inter_path, pdf_path)
            url = str(
                Url(
                    q["fid"],
                    q["mold"],
                    q["id"],
                    file.tree_id,
                    schema_key="MDR 13 i - board oversight",
                    custom_ui=True,
                )
            )
            results.append(
                {
                    "Stock Code": q["stock_code"],
                    "Report Year": q["report_year"],
                    "Hkexnews Headline": q["headline"],
                    "Fid": q["fid"],
                    "Url": url,
                    "Has Esg Chapter": has_esg_chapter,
                    "Type": "AR" if q["doc_type"] == DocType.AR.value else "ESG",
                    "Activated": q["activated"],
                    "on Jura Whitelist": True,
                }
            )
        except Exception as e:
            logging.info(f"{e}, {q["fid"]=}")

    results_map = {data["Stock Code"] for data in results}
    miss_codes = [code for code in codes if code not in results_map]
    for code in miss_codes:
        results.append(
            {
                "Stock Code": code,
                "Report Year": None,
                "Hkexnews Headline": None,
                "Fid": None,
                "Url": None,
                "Has Esg Chapter": None,
                "Type": "AR" if q["doc_type"] == DocType.AR.value else "ESG",
                "Activated": None,
                "on Jura Whitelist": False,
            }
        )

    if results:
        save_to_csv(results, "export_no_esg_file.csv")


def main():
    fire.Fire(
        {
            "find_no_esg_file": find_no_esg_file,
        },
        name="command",
    )


if __name__ == "__main__":
    main()
