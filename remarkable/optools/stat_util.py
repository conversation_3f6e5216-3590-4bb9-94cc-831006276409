import csv
import datetime
import itertools
import json
import logging
import re
import shutil
import urllib
from collections import Counter, defaultdict
from dataclasses import dataclass
from functools import cached_property
from pathlib import Path
from random import randint
from typing import NamedTuple

import pandas as pd

from remarkable.answer.node import AnswerItem
from remarkable.common.pattern import PatternCollection
from remarkable.common.util import clean_txt
from remarkable.config import get_config, project_root
from remarkable.models.mold import special_mold
from remarkable.pdfinsight.reader import <PERSON>d<PERSON><PERSON><PERSON><PERSON><PERSON>
from remarkable.prompter.schema import attribute_id
from remarkable.rule.hkex_disclosure_rules import ND

logger = logging.getLogger(__name__)


ENUM_MAP = {
    "Negative Statement": "NS",
    "Positive Statement": "PS",
    "No Disclosure": "ND",
    "no disclosure": "ND",
    "Disclosure": "D",
    "Comply": "Y",
    "comply": "Y",
    "Explain": "E",
    "explain": "E",
}


@dataclass
class Url:
    fid: int
    mid: int
    qid: int
    tree_id: int
    schema_key: str
    scheme: str = get_config("web.scheme", "http")
    domain: str = get_config("web.domain", "localhost:8000")
    custom_ui: bool = False
    need_split: bool = True

    def __str__(self):
        self.schema_key = urllib.parse.quote(self.schema_key)
        schema_key = self.schema_key
        # 移除子项结尾拼接
        if self.need_split and "-" in schema_key:
            schema_key = "-".join(schema_key.split("-")[:-1])
        if self.custom_ui:
            sub_path = "annual-report-checking"
            if self.mid == special_mold.v5_id:
                sub_path = "cg-report-checking"
            if self.mid in special_mold.esg_mids_with_policy:
                sub_path = "esg-report-checking"
            if self.mid == special_mold.v6_agm_id:
                sub_path = "agm-circular-checking"
            if self.mid == special_mold.v6_poll_id:
                sub_path = "agm-poll-checking"
            return f"{self.scheme}://{self.domain}/#/hkex/{sub_path}/report-review/{self.qid}?fileId={self.fid}&schemaId={self.mid}&rule={schema_key}&delist=0"
        return f"{self.scheme}://{self.domain}/#/project/remark/{self.qid}?treeId={self.tree_id}&fileId={self.fid}&schemaId={self.mid}&projectId={self.tree_id}&schemaKey={schema_key}"


class _Detail(NamedTuple):
    fid: int
    enum: str
    type: bool


class ErrorReport:
    host: str
    recall_errors: dict[str, set[int]] = defaultdict(set)
    precision_errors: dict[str, set[int]] = defaultdict(set)
    fit_stats: dict[str, set[int]] = defaultdict(set)
    details: dict[str, dict[int, list[_Detail]]] = defaultdict(lambda: defaultdict(list))  # 所有文档的详情
    enum_errors = defaultdict(list)

    def set_host(self, host):
        self.host = host

    def file_url(self, fid):
        base_url = f"http://{self.host}/#/search?fileid="
        return f"- [file {fid}]({base_url}{fid})\r\n"

    @cached_property
    def file_info(self):
        return {}

    def get_direct_url(self, fid, mid, schema_key, md: bool = True):
        if not self.file_info.get(fid):
            return ""
        url = Url(fid, mid, self.file_info[fid].qid, self.file_info[fid].tree_id, schema_key)
        if md:
            return f"- [file {fid}]({url})\r\n"
        return str(url)

    async def export_file(self, measure=None, mid=None, prefix: str = ""):
        report_dir = Path(project_root) / "error_reports"
        if report_dir.exists():
            shutil.rmtree(report_dir)
        report_dir.mkdir(parents=True)

        for name, recall_error_fids in self.recall_errors.items():
            if not name.startswith(prefix):
                continue
            recall_error_fids = sorted(recall_error_fids)
            filepath = report_dir / f"{re.sub(r'/', '', name)}.md"
            with open(filepath, "w") as dumpfp:
                dumpfp.write(f"# {name}\r\n\n")
                dumpfp.write("答案错误\n")
                for fid in recall_error_fids:
                    dumpfp.write(self.get_direct_url(fid, mid, schema_key=name))

        for name, prec_error_fids in self.precision_errors.items():
            if not name.startswith(prefix):
                continue
            prec_error_fids = sorted(prec_error_fids)
            filepath = report_dir / f"{re.sub(r'/', '', name)}.md"
            with open(filepath, "a+") as dumpfp:
                dumpfp.write("\n预测答案比标准答案多\n")
                for fid in prec_error_fids:
                    dumpfp.write(self.get_direct_url(fid, mid, schema_key=name))

        for name, fit_fids in self.fit_stats.items():
            if not name.startswith(prefix):
                continue
            fit_fids = sorted(fit_fids)
            filepath = report_dir / f"{re.sub(r'/', '', name)}.md"
            with open(filepath, "a+") as dumpfp:
                dumpfp.write("\n预测正确\n")
                for fid in fit_fids:
                    dumpfp.write(self.get_direct_url(fid, mid, schema_key=name))

        if self.enum_errors:
            # 输出每个字段的badcase
            for name, rule_items in self.enum_errors.items():
                filepath = report_dir / f"{re.sub(r'/', '', name)}.csv"
                with open(filepath, "w", newline="") as file_obj:
                    writer = csv.writer(file_obj)
                    writer.writerow(["url", "p_enum", "l_enum", "ans_from"])
                    for item in sorted(rule_items, key=lambda x: x["fid"]):
                        url = f"http://{self.host}/#/search?fileid={item['fid']}"
                        writer.writerow([url, item["p_enum"], item["l_enum"], item["ans_from"]])
            # 输出汇总
            self.print_enum_report(measure.docs_count, measure.fits, measure.attr_cnt, self.enum_errors)

    async def export_csv(self, field: str = "", mid=None):
        report_dir = Path(project_root) / "history_reports"
        report_dir.mkdir(parents=True, exist_ok=True)

        time = datetime.datetime.now().strftime("%Y%m%d/%H:%M")
        for name, details_by_fid in self.details.items():
            if not name.startswith(field):
                continue
            filepath = report_dir / f"{re.sub(r'/', '', name)}.csv"
            records = [
                {
                    "fid": fid,
                    "enum": ",".join(sorted({detail.enum for detail in details})),
                    "url": self.get_direct_url(fid, mid, name, md=False),
                    time: all(detail.type for detail in details),
                }
                for fid, details in details_by_fid.items()
            ]
            df = pd.DataFrame.from_records(records)
            if filepath.exists():
                prev_df = pd.read_csv(filepath)
                prev_df.drop(columns=["equal"], inplace=True, errors="ignore")
                df = prev_df.merge(df, on="fid", how="outer", suffixes=("", "_r"))
                df.drop(columns=[col for col in df.columns if col.endswith("_r")], inplace=True)
                df["equal"] = df[df.columns[-1]] == df[df.columns[-2]]
            df.to_csv(filepath, index=False)

    def print_enum_report(self, count, fits, attr_cnt, enum_errors):
        def stat_field_fits(field):
            field_fits = field["fits"]
            fit, total = sum(itertools.chain(*field_fits)), len(list(itertools.chain(*field_fits)))
            percent = float(fit) / float(total or 1)
            res = []
            if "idx_fits" in field:
                for topn in [5, 3, 2, 1]:
                    topn_fits = [[1 if 1 in item[:topn] else 0 for item in doc] for doc in field["idx_fits"]]
                    _fit, _total = sum(itertools.chain(*topn_fits)), len(list(itertools.chain(*topn_fits)))
                    _percent = float(_fit) / float(_total or 1)
                    res.append((_fit, _total, round(_percent, 3)))
            return fit, total, round(percent, 3), res

        def stat_field_precision(field):
            correct = 0
            wrong = 0
            if "idx_fits" in field:
                for doc in field["idx_fits"]:
                    if not doc:
                        continue
                    for i in range(len(doc[0])):
                        if sum((idx_fit[i] for idx_fit in doc)):
                            correct += 1
                        else:
                            wrong += 1
            total = correct + wrong
            percent = round(correct / total, 3) if total else 1
            return correct, total, percent

        def format_error_stat(_count):
            if not _count:
                return ""
            return ",".join([f"{ENUM_MAP.get(k, k)} {v}" for k, v in _count.items()])

        _ratio_range = "100,100".split(",")
        ratio_start = int(_ratio_range[0])
        ratio_end = int(_ratio_range[1]) if len(_ratio_range) > 1 else ratio_start
        res = []
        all_fits = []
        for key, item in sorted(fits.items(), key=lambda x: x[0]):
            enum_errors = self.enum_errors[key]
            error = Counter()
            for error_item in enum_errors:
                if error_item["p_enum"] == error_item["l_enum"]:
                    continue
                error.update([error_item["p_enum"]])
            ratio = float(randint(ratio_start, ratio_end)) / 100
            stat_fit = stat_field_fits(item)
            stat_precision = stat_field_precision(item)
            precision_key = attr_cnt[key]
            res.append(
                {
                    "name": item["name"],
                    "rate": stat_fit[2] * ratio,
                    "match": stat_fit[0] * ratio,
                    "total": stat_fit[1],
                    "precision": stat_fit[0] * ratio / precision_key if precision_key else 0,
                    "prec_correct": stat_precision[0] * ratio,
                    "prec_total": stat_precision[1],
                    "prec_rate": stat_precision[2],
                    "detail": [r[2] for r in stat_fit[3]],
                    "errors_stat": format_error_stat(error) if error else "",
                }
            )
            all_fits.append(stat_fit[:2])
        report_dir = Path(project_root) / "error_reports"
        csv_path = report_dir / "recall_enum_detail.csv"
        res.sort(key=lambda x: x["rate"], reverse=True)
        with open(csv_path, "a") as fd:
            writer = csv.writer(fd)
            writer.writerow(["name", "recall", "percent", "detail"])
            for item in res:
                writer.writerow([item["name"], item["rate"], f"{item['match']}/{item['total']}", item["errors_stat"]])
            total_fit, total = sum([r["match"] for r in res]), sum([r["total"] for r in res])
            total_recall = float(total_fit) / float(total)
            total_correct, total_predict = sum([r["prec_correct"] for r in res]), sum([r["prec_total"] for r in res])
            total_precision = total_correct / total_predict if total_predict else 1
            writer.writerow([f"recall: {total_fit}, {total}, {total_recall}"])
            writer.writerow([f"precision: {total_correct}, {total_predict}, {total_precision}"])


error_report = ErrorReport()


def stat_label_correct(attr, label_data, strict, preset_item):
    label_correct = 0
    for l_data in label_data:
        box_correct = 0
        for p_data in preset_item["data"]:
            for l_box in l_data["boxes"]:
                if any(content_same(p_box["text"], l_box["text"], strict) for p_box in p_data["boxes"]):
                    box_correct += 1
                    continue
                if any(box_same(p_box["box"], l_box["box"], strict) for p_box in p_data["boxes"]):
                    box_correct += 1
                    continue
            if box_correct:
                label_correct += 1
                break
    return label_correct


def finanicial_tbline_same(text_a, text_b):
    text_a_vals = set(extract_number_vals(text_a))
    text_b_vals = set(extract_number_vals(text_b))
    return len(text_a_vals & text_b_vals) >= 2


def extract_number_vals(text):
    vals = []
    for _str in re.split(r"[\s]", text):
        val = try_parse_float(_str)
        if val is not None:
            vals.append(val)
    return vals


def try_parse_float(text):
    text = text.replace(",", "")
    multiplier = 1
    if "%" in text:
        text = text.replace("%", "")
        multiplier = 0.01
    try:
        val = float(text)
        return val * multiplier
    except ValueError:
        # print("%s is not a number" % text)
        return None


def box_same(box_a, box_b, strict):
    percent = PdfinsightReader.overlap_percent(box_a, box_b, base="min")
    # print(' box_same', attr, percent, percent >= 0.2)
    if percent >= (0.9 if strict else 0.2):
        return True
    return False


def content_same(text_a, text_b, strict):
    if isinstance(text_a, list):
        text_a = "".join(text_a)
    if isinstance(text_b, list):
        text_b = "".join(text_b)
    text_a = clean_txt(text_a, remove_cn_text=True)
    text_b = clean_txt(text_b, remove_cn_text=True)
    if not text_a or not text_b:
        return False
    text_a, text_b = text_a.lower(), text_b.lower()
    if text_a == text_b:
        return True
    if not strict and (text_a in text_b or text_b in text_a):
        return True
    return False


def compare_preset_data(label_data, attr, predict, strict):
    if not predict:
        return False

    for preset_item in predict:
        if preset_item.get("mark"):
            continue
        label_correct = stat_label_correct(attr, label_data, strict, preset_item)
        if label_correct >= len(label_data):
            preset_item["mark"] = True
            return True

    return False


def valid_answer_item(answer_item):
    label_texts = []
    for data in answer_item["data"]:
        for box in data.get("boxes", []):
            text = box.get("text")
            if not text:
                continue
            label_texts.append(text)
    return bool(label_texts)


class AnswerMeasure:
    def __init__(self, answer_compare, answer_prepare):
        self.answer_compare = answer_compare
        self.answer_prepare = answer_prepare
        self.docs_count = 0
        self.fits = {}
        # 记录答案数量，用来计算 准确率
        self.attr_cnt = Counter()

    def append(self, fid, answer, standard, skip_reg, ignore_enum):
        fits, attr_cnt = self.answer_compare(fid, answer, standard, skip_reg, ignore_enum)
        self.attr_cnt.update(attr_cnt)
        for aid, val in fits.items():
            attr_fits = self.fits.setdefault(aid, {"name": aid, "fits": [], "idx_fits": []})
            attr_fits["fits"].extend(val["fits"])
            if val.get("idx_fits"):
                attr_fits["idx_fits"].extend(val["idx_fits"])

        self.docs_count += 1

    def measure_result(self):
        pass


class AnswerCompare:
    _white_list = []

    def __init__(self, strict=False, white_list_path=None, only_adjudge_enum=False, only_first_group=False):
        self.strict = strict
        self.only_adjudge_enum = only_adjudge_enum
        self.white_list_path = white_list_path
        # 答案有多组时，是否只对比第一组答案
        self.only_first_group = only_first_group

    def __call__(self, fid, answer, standard, skip_reg=None, ignore_enum=False):
        fits = {}
        attr_cnt = self.get_attr_cnt(answer, standard, skip_reg)
        errors_aid = []
        for aid, std_items in standard.items():
            errors_aid.append(aid)
            if self.is_skip_aid(aid, skip_reg):
                continue
            items = answer.get(aid, [])
            if self.only_first_group:
                std_items = std_items[:1]
                items = items[:1]
            elif len(items) > len(std_items):
                error_report.precision_errors[aid].add(fid)
            for count, std in enumerate(std_items, start=1):
                if get_config("ONLY_B1_B10") and count > len(items):
                    # B1-B7的分组个数以预测的分组个数为主，需要配置环境变量： SCRIBER_CONFIG_ONLY_B1_B10=true
                    continue
                res = self.check(fid, aid, std, items)
                ans_enum = std["value"][0] if isinstance(std["value"], list) and std["value"] else std["value"]
                if not ans_enum:  # 标注答案可能没有标注枚举值
                    logger.warning(
                        f"标注答案没有标注枚举值: rule: {aid}, {fid}",
                    )
                    if not ignore_enum:
                        continue
                if res["fits"][0] is False:
                    error_report.recall_errors[aid].add(fid)
                    detail = _Detail(fid, ans_enum, False)
                else:
                    error_report.fit_stats[aid].add(fid)
                    detail = _Detail(fid, ans_enum, True)
                error_report.details[aid][detail.fid].append(detail)
                attr_fits = fits.setdefault(aid, {"name": aid, "fits": [], "idx_fits": []})
                attr_fits["fits"].append(res["fits"])
                if res.get("idx_fits"):
                    attr_fits["idx_fits"].append(res["idx_fits"])

        # 没有标注答案的字段， 预测答案中该字段有值，属于多预测出答案的一种
        for aid in answer:
            if aid not in errors_aid:
                error_report.precision_errors[aid].add(fid)
        return fits, attr_cnt

    def get_attr_cnt(self, answer, standard, skip_reg=None):
        attr_cnt = Counter()
        for aid, items in answer.items():
            if self.is_skip_aid(aid, skip_reg):
                continue
            cnt = len([item for item in items if item])
            attr_cnt.update({aid: cnt})
        return attr_cnt

    def check(self, fid, aid, std, items):
        return {"fits": [], "idx_fits": None}

    def is_std_fit(self, attr, std, items):
        return False

    @staticmethod
    def re_aid_black_list():
        black_list = [re.compile(r"([>（]表格[<）])|(<.*>)")]
        return black_list

    @property
    def aid_black_list(self):
        return []

    def aid_in_black_list(self, aid):
        if aid in self.aid_black_list:
            return True
        re_black_list = self.re_aid_black_list()
        for black in re_black_list:
            if black.search(aid):
                return True

        return False

    @property
    def aid_white_list(self):
        if not self.white_list_path:
            return None

        if not self._white_list:
            with open(self.white_list_path) as fhp:
                self._white_list = json.load(fhp)
        return self._white_list

    def aid_in_white_list(self, aid):
        if self.aid_white_list:
            if not any(aid.startswith(x) for x in self.aid_white_list):
                return False

        return True

    def is_skip_aid(self, aid, skip_reg=None):
        if self.aid_in_black_list(aid):
            return True

        if not self.aid_in_white_list(aid):
            return True

        if skip_reg:
            if PatternCollection([skip_reg]).nexts(aid):
                return True

        return False


class PresetAnswerCompare(AnswerCompare):
    def re_aid_black_list(self):
        re_black_list = super(PresetAnswerCompare, self).re_aid_black_list()
        return re_black_list

    @property
    def aid_black_list(self):
        black = [
            "扉页-发行概况-股东公开发售股份数量",
            "扉页-发行概况-拟发行新股股票数量",
            "业务与技术-专利-权利人",
            "业务与技术-专利-专利授权国家",
            "业务与技术-专利-授权公告日",
            "业务与技术-专利-申请日",
            "业务与技术-专利-专利期限",
            "业务与技术-科研成果-项目名称",
            "业务与技术-科研成果-项目类型",
            "业务与技术-科研成果-角色",
            "本次发行概况-发行概况（三）-发行费用概算",
            "本次发行概况-发行概况（三）-超额配售发行上限",
            "发行人基本情况-间接控股股东-间接控股股东名称",
            "发行人基本情况-间接控股股东-间接控股股东类型",
        ]
        black.extend(super(PresetAnswerCompare, self).aid_black_list)
        return black

    def check(self, fid, aid, std, items):
        attr = aid.split("-")[-1]
        fit = self.is_std_fit(attr, std, items)
        return {"fits": [fit]}

    def is_std_fit(self, attr, std, items):
        """
        std能否与items中某一条匹配
        :param attr:
        :param std:
        :param items:
        :return:
        """
        if not items:
            return False

        for item in items:
            if item.get("mark"):
                continue
            if self.is_fit(attr, std, item):
                item["mark"] = True
                return True

        return False

    def is_fit(self, attr, std, item):
        key = std["schema"]["data"]["label"]
        count = 0
        for std_data in std["data"]:
            for data in item["data"]:
                if self.is_data_fit(attr, std_data, data):
                    count += 1
                    break
        if self.strict and count > 0:
            return count == len(std["data"])
        std_enum = AnswerItem(**std).enum.lower()
        answer_enum = AnswerItem(**item).enum.lower()
        # 枚举值相同
        if self.only_adjudge_enum and std_enum == answer_enum:
            return True
        # 标注答案没有 预测答案有内容
        if len(std["data"]) == 0 and len(item["data"]) != 0:
            # 客户标注时 在定制页面有可能只标注枚举值
            # 跟export_answer api 中一致
            #             if enum_result and not manual_data:
            #                 box_result = True
            if key == "KPI A1.2 part 3 - Scope 3":
                return True
            return std_enum == answer_enum

        return (count > 0 or count == len(std["data"])) and std_enum == answer_enum

    def is_data_fit(self, attr, std_data, data):
        std_boxes, boxes = std_data["boxes"], data["boxes"]
        if {box["page"] for box in std_boxes} == {box["page"] for box in boxes} and content_same(
            "".join(box["text"] for box in std_boxes), "".join(box["text"] for box in boxes), self.strict
        ):
            return True
        for std_box in std_boxes:
            if any(self.is_box_fit(attr, box, std_box, self.strict) for box in boxes):
                return True

        return False

    @staticmethod
    def is_box_fit(attr, std_box, box, strict):
        if box["page"] != std_box["page"]:
            return False
        if content_same(std_box["text"], box["text"], strict):
            return True
        if box_same(std_box["box"], box["box"], strict):
            return True
        return False


class PresetAnswerEnumCompare(AnswerCompare):
    # 仅仅返回位置正确但是枚举值错误的文件id

    def __call__(self, fid, answer, standard, skip_reg=None, ignore_enum=False):
        fits = {}
        attr_cnt = self.get_attr_cnt(answer, standard, skip_reg)
        errors_aid = []
        for aid, std_items in standard.items():
            errors_aid.append(aid)
            if self.is_skip_aid(aid, skip_reg):
                continue
            items = answer.get(aid, [])
            for std in std_items:
                res = self.check(fid, aid, std, items)
                if res["fits"][0] is False:
                    check_info = {
                        "fid": fid,
                        "p_enum": res["preset_enum"],
                        "l_enum": res["std_enum"],
                        "ans_from": "legacy_ui",
                    }
                    error_report.enum_errors[aid].append(check_info)
                else:
                    error_report.fit_stats[aid].add(fid)
                attr_fits = fits.setdefault(aid, {"name": aid, "fits": [], "idx_fits": []})
                attr_fits["fits"].append(res["fits"])
                if res.get("idx_fits"):
                    attr_fits["idx_fits"].append(res["idx_fits"])

        # 没有标注答案的字段， 预测答案中该字段有值，属于多预测出答案的一种
        for aid in answer:
            if aid not in errors_aid:
                error_report.precision_errors[aid].add(fid)
        return fits, attr_cnt

    def re_aid_black_list(self):
        re_black_list = super().re_aid_black_list()
        return re_black_list

    @property
    def aid_black_list(self):
        black = []
        black.extend(super().aid_black_list)
        return black

    def check(self, fid, aid, std, items):
        attr = aid.split("-")[-1]
        fit, std_enum, preset_enum = self.is_std_fit(attr, std, items)
        return {
            "fits": [fit],
            "std_enum": std_enum,
            "preset_enum": preset_enum,
        }

    def is_std_fit(self, attr, std, items):
        """
        std能否与items中某一条匹配
        :param attr:
        :param std:
        :param items:
        :return:
        """
        std_enum, preset_enum = None, None
        if not items:
            return False, std_enum, preset_enum
        match_once = False

        for item in items:
            if item.get("mark"):
                continue
            box_fit, std_enum, preset_enum = self.is_fit(attr, std, item)
            if box_fit:
                item["mark"] = True
                match_once = True
        if match_once:
            return True, std_enum, preset_enum
        return False, std_enum, preset_enum

    def is_fit(self, attr, std, item):
        key = std["schema"]["data"]["label"]
        count = 0
        for std_data in std["data"]:
            for data in item["data"]:
                if self.is_data_fit(attr, std_data, data):
                    count += 1
                    break
        std_enum = std["value"].lower() if std["value"] else ""
        answer_enum = item["value"].lower() if item["value"] else ""
        # 标注答案没有 预测答案有内容
        if len(std["data"]) == 0 and len(item["data"]) != 0:
            # 客户标注时 在定制页面有可能只标注枚举值
            # 跟export_answer api 中一致
            #             if enum_result and not manual_data:
            #                 box_result = True
            if key == "KPI A1.2 part 3 - Scope 3":
                return True, None, None
            return std_enum == answer_enum, std_enum, answer_enum

        return count > 0 or count == len(std["data"]), std_enum, answer_enum

    def is_data_fit(self, attr, std_data, data):
        for std_box in std_data["boxes"]:
            if any(self.is_box_fit(attr, box, std_box, self.strict) for box in data["boxes"]):
                return True

        return False

    @staticmethod
    def is_box_fit(attr, std_box, box, strict):
        if content_same(std_box["text"], box["text"], strict):
            return True
        if box_same(std_box["box"], box["box"], strict):
            return True
        return False


class ComplianceAnswerCompare(AnswerCompare):
    def check(self, fid, aid, std, items):
        attr = aid.split("-")[-1]
        fit = self.is_std_fit(attr, std, items)
        return {"fits": [fit]}

    def is_std_fit(self, attr, std, items):
        return std["value"] == items[0]["value"]


class CrudeAnswerCompare(AnswerCompare):
    overlap_threshold = 0.15

    def __init__(self, strict=False, white_list_path=None, **kwargs):
        super(CrudeAnswerCompare, self).__init__(strict, white_list_path)
        self.headnum = kwargs["headnum"]

    def get_attr_cnt(self, answer, standard, skip_reg=None):
        attr_cnt = Counter()
        for aid, items in answer.items():
            if self.is_skip_aid(aid, skip_reg):
                continue
            std_items = standard.get(aid, [])
            cnt = len([item for item in items if item])
            if std_items:
                cnt = cnt * len(std_items)
            attr_cnt.update({aid: cnt})
        return attr_cnt

    def get_headnum(self, aid):
        if any(x in aid for x in ["任职关系", "上下游关系", "交易对方所属区域"]):
            return 20
        return self.headnum

    def check(self, fid, aid, std, items):
        fits = []
        idx_fits = []
        std_data = std.get("data", [])
        if std["value"] == ND:
            return {"fits": [True], "idx_fits": []}
        if not std_data:  # ND
            return {"fits": [False], "idx_fits": []}
        for data in std_data:
            if not data or not data.get("boxes"):
                continue
            try:
                _idx_fits = self.compare_crude(aid, data, items)
            except ValueError as ex:
                logger.error("fid: %s, attr: %s, %s", fid, aid, ex)
                continue

            if _idx_fits:
                fits.append(True in _idx_fits)
                idx_fits.append(_idx_fits)
        return {"fits": fits, "idx_fits": idx_fits}

    def compare_crude(self, aid, data, crude_items):
        fits = []
        crude_nums = len(crude_items)
        for idx in range(self.get_headnum(aid)):
            fit = False
            if idx + 1 < crude_nums:
                crude = crude_items[idx]
                for box in data["boxes"]:
                    if int(box["page"]) != int(crude["page"]):
                        continue
                    if None in box["box"].values():
                        raise ValueError("box outline is None")
                    overlap = PdfinsightReader.overlap_percent(box["box"], crude["outline"], base="min")
                    if overlap > self.overlap_threshold:
                        fit = True
                        break
            fits.append(fit)
        return fits


class AnswerPrepare:
    @staticmethod
    def group_by_aid(answer):
        ret = defaultdict(list)
        for item in answer.get("items", []):
            aid = attribute_id(json.loads(item["key"]))
            ret[aid].append(item)
        return ret


class PresetAnswerPrepare(AnswerPrepare):
    async def __call__(self, question, answer):
        standard_answer = None
        if not answer:
            answer = question.preset_answer or {}
        preset_answer = self.group_by_aid(answer.get("userAnswer", {}))

        answer_instance = await question.get_user_merged_answer()
        if not answer_instance:
            # 使用客户在定制页面标注的答案
            answer_instance = question.answer
        if answer_instance:
            standard_answer = self.group_by_aid(answer_instance.get("userAnswer", {}))
        return preset_answer, standard_answer


class ComplianceAnswerPrepare(AnswerPrepare):
    async def __call__(self, question, answer):
        standard = question.answer
        if not answer:
            answer = question.preset_answer

        answer = self.group_by_aid(answer.get("rule_result", {}))
        standard = self.group_by_aid(standard.get("rule_result", {}))
        return answer, standard


class CrudeAnswerPrepare(AnswerPrepare):
    def __init__(self, vid, threshold, headnum, **kwargs):
        super(CrudeAnswerPrepare, self).__init__(**kwargs)
        self.vid = vid
        self.threshold = threshold
        self.headnum = headnum

    def get_headnum(self, aid):
        if any(x in aid for x in ["任职关系", "上下游关系", "交易对方所属区域"]):
            return 20
        return self.headnum

    async def __call__(self, question, answer):
        standard = question.answer
        standard = self.group_by_aid(standard.get("userAnswer", {}))

        crude_answer = {}

        if not answer:
            answer = question.crude_answer or {}

        for aid in standard:
            headnum = self.get_headnum(aid)
            crude_answer[aid] = [c for c in answer.get(aid, []) if c["score"] > self.threshold][:headnum]

        return crude_answer, standard
