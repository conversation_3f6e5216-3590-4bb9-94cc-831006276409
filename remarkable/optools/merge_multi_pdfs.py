import logging
import os
import re
import tempfile
from pathlib import Path

import pikepdf

from remarkable.common.util import stream_download

# for 01199 esg report


async def combin_pdf_from_htm(url: str, saved_path: str = None):
    """合并指定HKEX htm页面多页PDF为一个PDF"""
    p_href = re.compile(r'href="(.*?\.pdf)"')

    saved_path = saved_path or f"{Path(url).stem}.pdf"
    with tempfile.TemporaryDirectory(delete=False) as tmp_dir:
        work_dir = Path(tmp_dir)
        h5_path = work_dir / os.path.basename(url)
        await stream_download(url, h5_path)
        for i, m in enumerate(p_href.finditer(h5_path.read_text()), 1):
            logging.info(f"Downloading {m.group(1)}")
            logging.info(f"Downloading {os.path.join(os.path.dirname(url), m.group(1))}")
            await stream_download(
                os.path.join(os.path.dirname(url), m.group(1)),
                work_dir / f"{i:05d}.pdf",
            )
        with pikepdf.Pdf.new() as pdf:
            for path in sorted(work_dir.glob("*.pdf"), key=lambda x: x.name):
                with pikepdf.Pdf.open(path) as src:
                    pdf.pages.extend(src.pages)
            pdf.save(saved_path, linearize=True)


def merge_multi_pdfs(pdf_paths: list[str], output_path: str):
    with pikepdf.Pdf.new() as pdf:
        for path in pdf_paths:
            with pikepdf.Pdf.open(path) as src:
                pdf.pages.extend(src.pages)
        pdf.save(output_path, linearize=True)


if __name__ == "__main__":
    merge_multi_pdfs(
        [
            "/Users/<USER>/Downloads/CLP_Sustainability_Report_2024_en.pdf.coredownload.pdf",
            "/Users/<USER>/Downloads/CLP_CV2050_2024_en.pdf",
        ],
        "/Users/<USER>/Downloads/00002_2024_indent_esg.pdf",
    )
