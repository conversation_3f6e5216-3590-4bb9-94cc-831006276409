import asyncio
import json
import logging
import os
from itertools import chain

from pdfparser.common.zip_util import zip_string
from pdfparser.pdftools.pdf_util import PDFUtil

from remarkable.common.multiprocess import run_by_batch
from remarkable.common.util import read_zip_first_file
from remarkable.db import pw_db
from remarkable.models.new_file import NewFile


def fix_pdfinsight(args):
    fid, path = args
    try:
        logging.info("fix pdfinsight: %s", fid)
        data = json.loads(read_zip_first_file(path))
        for para in chain(data.get("paragraphs", []), data.get("page_footers", []), data.get("page_headers", [])):
            fix_paragraph(para)
        zip_string(json.dumps(data), path, arcname="interdoc.json")
    except Exception as ex:
        logging.error("error in fix pdfinsight: %s", fid)
        logging.exception(ex)


def fix_paragraph(para):
    # para["text"] = PDFUtil.get_text_from_chars_with_white(para["chars"], with_newline=False, fix_chars=True)
    para["text"] = PDFUtil.get_text_from_chars_with_white(para["chars"], with_newline=False)


async def main():
    files = await pw_db.execute(NewFile.select(NewFile.id, NewFile.pdfinsight).where(NewFile.pdfinsight.is_null(False)))
    tasks = []
    for file in files:
        try:
            path = file.pdfinsight_path(abs_path=True)
            if not os.path.exists(path):
                continue
            tasks.append((file.id, path))
        except Exception as ex:
            logging.error("error find pdfinsight: %s", file.id)
            logging.error(ex)
    finished = 0
    for res in run_by_batch(fix_pdfinsight, tasks, batch_size=100, workers=2):
        finished += len(res)
        logging.info("progress: %s/%s", finished, len(tasks))


if __name__ == "__main__":
    asyncio.run(main())
