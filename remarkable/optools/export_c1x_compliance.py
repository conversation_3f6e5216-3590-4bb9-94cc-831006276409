import json
import logging
import shutil
from pathlib import Path

import fire

from remarkable.common.common import get_keys
from remarkable.common.constants import NON_COMPLIANCE, AnswerValueEnum, DocType
from remarkable.config import get_config
from remarkable.db import loop_wrapper, pw_db
from remarkable.models.hkex_file_meta import HKEXFileMeta
from remarkable.models.new_question import NewQuestion
from remarkable.optools.utils import save_to_excel
from remarkable.rule.jura_21_c.c1x import C1XRule

logger = logging.getLogger(__name__)

EXPORT_PATH = Path(get_config("web.tmp_dir")) / "tmp_exports"


def get_event_answer_value(preset_answer, rule, sub_name=None) -> dict:
    result = {}
    for item in get_keys(preset_answer, ["userAnswer", "items"]) or []:
        key_paths = [i.split(":")[0] for i in json.loads(item["key"])]
        if key_paths[1] != rule or (sub_name and sub_name != key_paths[2]):
            continue
        value = item.get("value")
        if not value or not item.get("meta") or "event_id" not in item["meta"]:
            break
        event_id = item["meta"]["event_id"]
        result[event_id] = (value, item["meta"]["group_name"])
    return result


def trans_enum(enum_value):
    if enum_value == AnswerValueEnum.PS.value:
        return "PS"
    if enum_value == AnswerValueEnum.NS.value:
        return "NS"
    return "ND"


def trans_compliance(rule_result):
    result = []
    for rule, res in rule_result.items():
        value = "NC" if res == NON_COMPLIANCE else "C"
        result.append(f"{rule}: {value}")
    return "\n".join(result)


def get_missed(cur_enum, prev_b9_event_ids, prev_c12_event_ids, prev_b9_enum, prev_c12_enum):
    missed = set()
    missed.update(
        f"B9 - {prev_b9_enum[k][1]}: {trans_enum(prev_b9_enum[k][0])}" for k in prev_b9_event_ids - set(cur_enum)
    )
    missed.update(
        f"C1.2.1 - {prev_c12_enum[k][1]}: {trans_enum(prev_c12_enum[k][0])}" for k in prev_c12_event_ids - set(cur_enum)
    )
    return "\n".join(sorted(missed))


def trans_answer(answer, b9_enum, c12_enum, missed=None):
    result = []
    for event_id, values in answer.items():
        value, group_name = values
        rule = event_id
        if event_id in b9_enum:
            rule = f"B9 - {b9_enum[event_id][1]}"
        elif event_id in c12_enum:
            rule = f"C1.2.1 - {c12_enum[event_id][1]}"
        else:
            rule = f"{rule} - {group_name}"
        result.append(f"{rule}: {trans_enum(value)}")
    if missed:
        result.append("\n缺少分组:\n" + missed)
    return "\n".join(result)


@loop_wrapper()
async def export_c1x_answer(report_years=None, start=None, end=None):
    """
    导出C1.1.1~C1.3相关合规数据
    """
    query = NewQuestion.select(
        NewQuestion.id, NewQuestion.preset_answer, NewQuestion.fid, HKEXFileMeta.stock_code
    ).join(HKEXFileMeta, on=(NewQuestion.fid == HKEXFileMeta.fid))
    cond = [NewQuestion.deleted_utc == 0, NewQuestion.mold == 18]
    if start:
        cond.append(NewQuestion.fid >= int(start))
    if end:
        cond.append(NewQuestion.fid <= int(end))
    if report_years:
        if isinstance(report_years, int):
            report_years = [str(report_years)]
        else:
            report_years = [str(r) for r in report_years]
        cond.append(HKEXFileMeta.report_year.in_(report_years))
    data_rows = []
    with pw_db.allow_sync():
        for question in query.where(*cond).execute():
            if not question or not question.hkexfilemeta.stock_code or not question.preset_answer:
                continue
            fid = question.fid
            file_metas = HKEXFileMeta.find_previous_year_metas(fid, DocType.AR.value)
            if not file_metas:
                continue
            prev_fid = file_metas[0].fid
            prev_b9_enum, prev_c1_enum = {}, {}
            prev_b9_res, prev_c1_res = False, False
            if b9_question := NewQuestion.get_or_none(((NewQuestion.fid == prev_fid) & (NewQuestion.mold == 15))):
                prev_b9_enum = get_event_answer_value(b9_question.preset_answer, "B9")
                prev_b9_res = C1XRule.get_prev_compliance_value(b9_question.preset_answer, "B9")
            if c12_question := NewQuestion.get_or_none(((NewQuestion.fid == prev_fid) & (NewQuestion.mold == 18))):
                prev_c1_enum = get_event_answer_value(c12_question.preset_answer, "C1.2.1", "Unutilized proceeds")
                prev_c1_res = C1XRule.get_prev_compliance_value(c12_question.preset_answer, "C1.2.1")
            # 上一年B9/C1.2.1至少一个为NC
            if not (prev_b9_enum or prev_c1_enum) or NON_COMPLIANCE not in {prev_b9_res, prev_c1_res}:
                continue
            preset_answer = question.preset_answer
            c11_enum = get_event_answer_value(preset_answer, "C1.1.1", "Proceeds brought forward")
            c12_enum = get_event_answer_value(preset_answer, "C1.2.1", "Unutilized proceeds")
            c13_enum = get_event_answer_value(preset_answer, "C1.3")
            c11_result = C1XRule.get_prev_compliance_value(preset_answer, "C1.1.1")
            c12_result = C1XRule.get_prev_compliance_value(preset_answer, "C1.2.1")
            c13_result = C1XRule.get_prev_compliance_value(preset_answer, "C1.3")
            prev_b9_event_ids = {
                k
                for k, v in prev_b9_enum.items()
                if k and (v[0] == AnswerValueEnum.PS.value or v[0] == AnswerValueEnum.ND.value and prev_b9_res)
            }
            prev_c12_event_ids = {
                k
                for k, v in prev_c1_enum.items()
                if k and (v[0] == AnswerValueEnum.PS.value or v[0] == AnswerValueEnum.ND.value and prev_c1_res)
            }
            c11_missed = get_missed(c11_enum, prev_b9_event_ids, prev_c12_event_ids, prev_b9_enum, prev_c1_enum)
            c12_missed = get_missed(c12_enum, prev_b9_event_ids, prev_c12_event_ids, prev_b9_enum, prev_c1_enum)
            c13_missed = get_missed(c13_enum, prev_b9_event_ids, prev_c12_event_ids, prev_b9_enum, prev_c1_enum)
            if not (c11_missed or c12_missed or c13_missed or NON_COMPLIANCE in {c11_result, c12_result, c13_result}):
                continue
            if c12_missed:
                rule = "C1.2.1"
            elif c11_missed:
                rule = "C1.1.1"
            elif c13_missed:
                rule = "C1.3"
            elif c11_result == NON_COMPLIANCE:
                rule = "C1.1.1"
            elif c13_result == NON_COMPLIANCE:
                rule = "C1.3"
            else:
                rule = "C1.2.1"
            data_rows.append(
                [
                    fid,
                    trans_answer(prev_b9_enum | prev_c1_enum, prev_b9_enum, prev_c1_enum),
                    trans_compliance({"B9": prev_b9_res, "C1.2.1": prev_c1_res}),
                    trans_answer(c11_enum, prev_b9_enum, prev_c1_enum, missed=c11_missed),
                    trans_answer(c12_enum, prev_b9_enum, prev_c1_enum, missed=c12_missed),
                    trans_answer(c13_enum, prev_b9_enum, prev_c1_enum, missed=c13_missed),
                    trans_compliance({"C1.1.1": c11_result, "C1.2.1": c12_result, "C1.3": c13_result}),
                    f"http://100.64.0.105:55647/#/hkex/annual-report-checking/report-review/{question.id}?fileId={fid}&schemaId=18&rule={rule}",
                ]
            )

    header = [
        "文件ID",
        "B9/C1.2.1枚举",
        "B9/C1.2.1合规",
        "C1.1.1枚举",
        "C1.2.1枚举",
        "C1.3枚举",
        "C1.1.1/C1.2.1/C1.3合规",
        "URL",
    ]
    save_to_excel(
        EXPORT_PATH / "c1.1.1_to_c1.3_compliance",
        sorted(data_rows, key=lambda x: x[0]),
        header,
        {len(header) - 1},
    )

    logger.info(f"Success, total {len(data_rows)} rows.")


if __name__ == "__main__":
    if EXPORT_PATH.exists():
        shutil.rmtree(EXPORT_PATH)
    EXPORT_PATH.mkdir(parents=True, exist_ok=True)
    # 命令：python3 -m remarkable.optools.export_c1x_compliance -r 2024,2025 -s 60499 -e 65000
    fire.Fire(export_c1x_answer)
