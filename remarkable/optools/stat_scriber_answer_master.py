import argparse
import csv
import itertools
import json
import logging
import os
from functools import cached_property
from operator import and_
from pathlib import Path
from random import randint

import pandas as pd
from playhouse.postgres_ext import ServerSide

from remarkable.common.common import FUNDRAISING_COLS, JURA21_COLS
from remarkable.config import get_config, project_root
from remarkable.db import loop_wrapper, pw_db
from remarkable.models.hkex_file_meta import HKEXFileMeta
from remarkable.models.mold import special_mold
from remarkable.models.new_file import NewFile
from remarkable.models.new_question import NewQuestion
from remarkable.optools.stat_util import (
    AnswerMeasure,
    ComplianceAnswerCompare,
    ComplianceAnswerPrepare,
    CrudeAnswerCompare,
    CrudeAnswerPrepare,
    ErrorReport,
    PresetAnswerCompare,
    PresetAnswerEnumCompare,
    PresetAnswerPrepare,
)

args = None
REPORT_DIR = Path(project_root) / "error_reports"

logger = logging.getLogger(__name__)


class StatScriberAnswer:
    html = {}
    report = {}
    attr_tbl_heads = {}

    def __init__(self, headnum=None, threshold=None, **kwargs):
        self.headnum = headnum or 5
        self.threshold = threshold or 0
        self.from_id = kwargs.get("from_id", 0)
        self.to_id = kwargs.get("to_id", 0)
        self.count_label = kwargs.get("count_label")
        self.print_diff = kwargs.get("print_diff")
        self.mold = kwargs.get("mold")
        self.save = kwargs.get("save")
        self.orderby = kwargs.get("orderby")
        self.ratio = kwargs.get("ratio")
        self.strict = kwargs.get("strict")
        self.tree_s = kwargs.get("tree_s")
        self.host = kwargs.get("host")
        self.bydoc = kwargs.get("bydoc")
        self.vid = kwargs.get("vid", 0)
        self.answers = kwargs.get("answers")
        self.white_list = kwargs.get("white_list")
        self.acid = kwargs.get("acid")
        self.skip_reg = kwargs.get("skip_reg")
        self.special_years = kwargs.get("special_years")
        self.only_adjudge_enum = kwargs.get("only_adjudge_enum")
        self.ignore_enum = kwargs.get("ignore_enum")
        self.from_file = kwargs.get("from_file")
        self.jura_21 = kwargs.get("jura_21")
        self.prefix = kwargs.get("prefix", "")
        self.csv = kwargs.get("csv", False)
        self.only_first_group = kwargs.get("only_first_group", False)

    async def get_all_qids(self):
        sql = """
            select distinct(question.id) as qid
                from file
                inner join question on file.id=question.fid
                left join answer on question.id=answer.qid
                where question.deleted_utc=0 {mold_filter} {id_filter}
        """
        if self.mold is not None:
            mold_filter_sql = " and question.mold=%s" % self.mold
        else:
            raise Exception("mold is needed")

        id_filter = ""
        if self.from_id:
            id_filter += " and file.id >= %s" % (self.from_id,)
        if self.to_id:
            id_filter += " and file.id <= %s" % (self.to_id,)
        if self.tree_s:
            sql += " and file.tree_id in (%s)" % ",".join(map(str, self.tree_s))
        if self.from_file:
            with open(self.from_file, "r") as file_obj:
                include_ids = [int(i.strip()) for i in file_obj.readlines() if i.strip().isdigit()]
                id_filter += " and file.id in (%s)" % ",".join(map(str, include_ids))
        sql += " order by question.id;"

        sql = sql.format(mold_filter=mold_filter_sql, id_filter=id_filter)
        qids = []
        for row in await pw_db.execute(sql):
            qids.append(row.qid)
        return qids

    async def save_result(self, stat_result, count, crude):
        if self.save:
            stat_result.update({"from_id": self.from_id, "to_id": self.to_id, "mold": self.mold})
            await self.dump_stat_result(
                stat_result,
                mold=self.mold,
                crude=crude,
                test=self.save,
                file_count=count,
                vid=self.vid,
                tree_s=self.tree_s,
                acid=self.acid,
            )

    @staticmethod
    def get_file_url(host, qid, tree_id, fid, mold, pid, file_name):
        file_url = "http://%s/#/project/remark/%s?treeId=%s&fileId=%s&schemaId=%s&projectId=%s&fileName=%s" % (
            host,
            qid,
            tree_id,
            fid,
            mold,
            pid,
            file_name,
        )
        return file_url

    async def stat_crude_answer(self):
        answer_compare = CrudeAnswerCompare(strict=self.strict, white_list_path=self.white_list, headnum=self.headnum)
        answer_prepare = CrudeAnswerPrepare(vid=self.vid, threshold=self.threshold, headnum=self.headnum)
        measure = AnswerMeasure(answer_compare, answer_prepare)
        stat_result = await self.stat_answer(measure)
        return stat_result

    async def stat_compliance_answer(self):
        answer_compare = ComplianceAnswerCompare(strict=self.strict, white_list_path=self.white_list)
        answer_prepare = ComplianceAnswerPrepare()
        measure = AnswerMeasure(answer_compare, answer_prepare)
        stat_result = await self.stat_answer(measure)
        return stat_result

    @cached_property
    def file_info(self):
        return {}

    async def stat_answer(self, measure):
        error_report = ErrorReport()
        qids = await self.get_all_qids()
        query = NewQuestion.select(
            NewQuestion.id,
            NewQuestion.fid,
            NewQuestion.crude_answer,
            NewQuestion.preset_answer,
            NewQuestion.answer,
        ).where(NewQuestion.id.in_(qids))
        for record in await pw_db.execute(
            NewQuestion.select(NewFile.id, NewFile.tree_id, NewQuestion.id.alias("qid"), HKEXFileMeta.report_year)
            .join(NewFile, on=and_(NewFile.id == NewQuestion.fid, NewQuestion.mold == self.mold))
            .join(HKEXFileMeta, on=(HKEXFileMeta.fid == NewFile.id))
            .where(NewQuestion.id.in_(qids))
            .namedtuples()
        ):
            self.file_info[record.id] = record
        with pw_db.allow_sync():
            for question in ServerSide(query):
                answer = self.answers.get(question.fid, {}) if self.answers else None
                try:
                    if self.special_years and self.file_info[question.fid].report_year != self.special_years:
                        logger.info(
                            f"__SKIP__ special year {self.special_years}, qid:{question.id}, fid:{question.fid}"
                        )
                        continue
                    answer, standard = await measure.answer_prepare(question, answer)
                    if not standard:
                        logger.info(f"__SKIP__: no standard answer, qid:{question.id}, fid:{question.fid}")
                        continue
                    if get_config("ONLY_B1_B10"):
                        # from env export SCRIBER_CONFIG_ONLY_B1_B10=True
                        if not (standard := {k: v for k, v in standard.items() if k.split("-")[0] in FUNDRAISING_COLS}):
                            logger.info(f"__SKIP__: no b1_b10 standard answer, qid:{question.id}, fid:{question.fid}")
                            continue
                    elif self.jura_21:
                        # has_jura21_standard_answer = False
                        standard = {
                            k: v
                            for k, v in standard.items()
                            if k.split("-")[0] in JURA21_COLS + ("丨H83-92", "丨H38-41")
                        }
                        if not standard:
                            logger.info(f"__SKIP__: no jura21 standard answer, qid:{question.id}, fid:{question.fid}")
                            continue

                    measure.append(question.fid, answer, standard, self.skip_reg, self.ignore_enum)
                    # logger.info(f"Stat: qid:{qid}, fid:{question.fid}, count:{measure.docs_count}")
                except Exception as ex:
                    logger.exception(ex)
                    logger.error(f"__Error__: qid:{question.id}, fid:{question.fid}")
        error_report.set_host(self.host)
        error_report.file_info.update(self.file_info)
        await error_report.export_file(measure, self.mold, self.prefix)
        if self.csv:
            await error_report.export_csv(self.prefix, self.mold)
        stat_result = self.print_report(measure.docs_count, measure.fits, measure.attr_cnt)
        await self.save_result(stat_result, measure.docs_count, crude=False)

        return stat_result

    async def stat_preset_answer(self):
        answer_compare = PresetAnswerCompare(
            strict=self.strict,
            white_list_path=self.white_list,
            only_adjudge_enum=self.only_adjudge_enum,
            only_first_group=self.only_first_group,
        )
        answer_prepare = PresetAnswerPrepare()
        measure = AnswerMeasure(answer_compare, answer_prepare)
        stat_result = await self.stat_answer(measure)
        return stat_result

    async def stat_preset_enum_answer(self):
        answer_compare = PresetAnswerEnumCompare(
            strict=self.strict, white_list_path=self.white_list, only_adjudge_enum=self.only_adjudge_enum
        )
        answer_prepare = PresetAnswerPrepare()
        measure = AnswerMeasure(answer_compare, answer_prepare)
        stat_result = await self.stat_answer(measure)
        return stat_result

    def stat_field_fits(self, field):
        field_fits = field["fits"]
        fit, total = sum(itertools.chain(*field_fits)), len(list(itertools.chain(*field_fits)))
        percent = float(fit) / float(total or 1)  # R = TP/TP+FN
        res = []
        if field["idx_fits"]:
            for topn in [5, 3, 2, 1]:
                topn_fits = [[1 if 1 in item[:topn] else 0 for item in doc] for doc in field["idx_fits"]]
                _fit, _total = sum(itertools.chain(*topn_fits)), len(list(itertools.chain(*topn_fits)))
                _percent = float(_fit) / float(_total or 1)
                res.append((_fit, _total, round(_percent, 3)))
        return fit, total, round(percent, 3), res

    @classmethod
    def stat_field_fits_by_document(cls, field):
        field_fits = field["fits"]
        rates = [len([1 for fit in _doc if fit]) / len(_doc or 1) for _doc in field_fits]
        fit, total = sum(rates), len(rates)
        percent = fit / (total or 1)
        res = []
        return fit, total, round(percent, 3), res

    @classmethod
    def collect_report_records(cls, res):
        records = []
        for item in res:
            records.append(
                (
                    item["name"],
                    item["rate"],
                    item["precision"],
                    item["total"],
                    item["predict"],
                    item["match"],
                    # item['detail'],
                )
            )
        records = pd.DataFrame(
            records,
            columns=[
                "name",
                "recall",
                "precision",
                "sample",
                "predict",
                "match",
                # 'detail',
            ],
        )
        return records

    def print_report(self, count, fits, attr_cnt, tagged_counter=None):
        all_fits = []
        idx_fits = {}
        res = []
        _ratio_range = (self.ratio or "100,100").split(",")
        ratio_start = int(_ratio_range[0])
        ratio_end = int(_ratio_range[1]) if len(_ratio_range) > 1 else ratio_start
        for key, item in sorted(fits.items(), key=lambda x: x[0]):
            if not key.startswith(self.prefix):
                continue
            ratio = float(randint(ratio_start, ratio_end)) / 100
            if self.bydoc:
                stat_fit = self.stat_field_fits_by_document(item)
            else:
                stat_fit = self.stat_field_fits(item)
            # hard code: ignore 0
            # if stat_fit[0] == 0:
            #     continue
            precision = (stat_fit[0] * ratio / attr_cnt[key]) if attr_cnt.get(key) else 0  # 精度：P=TP/TP+FP
            res.append(
                {
                    "name": key,
                    "rate": stat_fit[2] * ratio,
                    "match": stat_fit[0] * ratio,
                    "total": stat_fit[1],
                    "precision": round(precision, 3) if precision <= 1 else 1,
                    "tagged": tagged_counter[item["name"]] if tagged_counter else 0,
                    "detail": [r[2] for r in stat_fit[3]],
                    "predict": attr_cnt[key],
                }
            )
            all_fits.append(stat_fit[:2])
            if item["idx_fits"]:
                idx_fits[key] = list(itertools.chain(*item["idx_fits"]))
        if not all_fits:
            logger.debug("没有文件")
            return {}

        def _sortkey(_str):
            try:
                return int(_str.split("-")[0].strip("A"))
            except ValueError:
                return _str

        if self.orderby == "name":
            res.sort(key=lambda x: _sortkey(x["name"]), reverse=False)
        else:
            res.sort(key=lambda x: float(x["rate"]), reverse=True)

        if self.mold == special_mold.v1_id:
            res = [r for r in res if "(" not in r["name"]]

        report_records = self.collect_report_records(res)
        pd.set_option("display.unicode.ambiguous_as_wide", True)
        pd.set_option("display.unicode.east_asian_width", True)
        pd.set_option("display.max_columns", None)
        pd.set_option("display.max_rows", None)
        pd.set_option("max_colwidth", 60)
        pd.set_option("display.width", 1000)

        if self.bydoc:
            total_fit, total = sum([r["rate"] for r in res]), len(res)
        else:
            total_fit, total = sum([r["match"] for r in res]), sum([r["total"] for r in res])
        total_percent = float(total_fit) / float(total)
        total_predict = sum(attr_cnt.values())
        precision = total_fit / total_predict if total_predict else 0
        stat_desc = f"fit: {total_fit}, sample: {total}, predict: {total_predict}, recall: {total_percent:.4f}, precision: {precision:.4f}"
        file_count_desc = f"======文件数量：{count}======"

        # print results in terminal
        print(report_records)
        print(stat_desc)
        print(file_count_desc)

        # print results to csv
        REPORT_DIR.mkdir(exist_ok=True)
        csv_path = REPORT_DIR / "recall_precision.csv"
        report_records.to_csv(csv_path, index=False)
        with open(csv_path, "a") as fd:
            writer = csv.writer(fd)
            writer.writerow([stat_desc])
            writer.writerow([file_count_desc])

        top_n_result = []

        result = {
            "result": res,
            "total": total,  # 标注总数
            "total_predict": total_predict,  # 预测总数
            "total_fit": total_fit,  # 预测且正确（交集）
            "total_percent": total_percent,  # recall
            "total_precision": total_fit / total_predict if total_predict else 0,  # precsion
        }
        if idx_fits:
            result.update({"top_n_result": top_n_result})

        if args and args.export:
            if not os.path.exists(args.export_dir):
                os.mkdir(args.export_dir)
            for attr, html_str_l in self.html.items():
                with open(os.path.join(args.export_dir, "%s.html" % attr), "w") as html_f:
                    html_f.write(
                        "<style>.red{color: red;font-weight: bold;}.blue{color: green;font-weight: bold;}</style>"
                    )
                    html_f.write("<ol>")
                    html_f.write("\n".join(html_str_l))
                    html_f.write("</ol>")
        if args and args.exportjson:
            export_dir = get_config("web.predict_from_memory.data_dir")
            if not os.path.exists(export_dir):
                os.mkdir(export_dir)
            for attr, data in self.attr_tbl_heads.items():
                json.dump(data, open(os.path.join(export_dir, "%s.json" % attr), "w"))
            # ext = '_'.join(map(str, [args.fromid, args.toid]))
            # for attr, data in cls.attr_tbl_heads.items():
            #     json.dump(data, open(os.path.join(export_dir, '%s.json.%s' % (attr, ext)), 'w'))
        return result

    async def dump_stat_result(self, result, mold, crude, test, file_count, vid=0, tree_s=None, acid=None):
        sql_params = {
            "acid": acid,
            "type": 2 if crude else 1,
            "test": test,
            "data": json.dumps(result),
            "mold_id": mold,
            "file_count": file_count,
            "vid": vid,
            "dirs": tree_s if tree_s else [],
        }
        if acid:
            sql = """
            UPDATE accuracy_record SET
            data=%(data)s,file_count=%(file_count)s
            where id=%(acid)s;
            """
        else:
            sql = """
            INSERT INTO accuracy_record
            (type,test,data,mold,file_count)
            VALUES(%(type)s, %(test)s, %(data)s, %(mold_id)s, %(file_count)s);
            """
        next(pw_db.sync_execute(sql, sql_params), None)


@loop_wrapper()
async def main():
    logger.info(f"{args.crude=}, {args.save=}, {args.fromid=}, {args.toid=}, {args.ignore_enum=}")
    stat = StatScriberAnswer(
        headnum=args.headnum,
        threshold=args.threshold,
        from_id=args.fromid,
        to_id=args.toid,
        count_label=args.label,
        print_diff=args.diff,
        mold=args.mold,
        save=args.save,
        orderby=args.orderby,
        ratio=args.ratio,
        strict=args.strict,
        only_adjudge_enum=args.only_adjudge_enum,
        ignore_enum=args.ignore_enum,
        host=args.host,
        bydoc=args.bydoc,
        white_list=args.whitelist,
        special_years=args.special_years,
        from_file=args.from_file,
        jura_21=args.jura_21,
        csv=args.csv,
        prefix=args.prefix,
        only_first_group=args.only_first_group,
    )
    if args.crude:
        await stat.stat_crude_answer()
    elif args.compliance:
        await stat.stat_compliance_answer()
    else:
        await stat.stat_preset_answer()


if __name__ == "__main__":
    host = get_config("web.domain")
    parser = argparse.ArgumentParser(description="Stat scriber answer.")
    parser.add_argument("-c", "--crude", dest="crude", action="store_true", help="stat crude or preset answer")
    parser.add_argument(
        "-s", "--save", type=int, nargs="?", default=0, help="store result as train_set if 0 else test_set"
    )
    parser.add_argument("-l", "--label", dest="label", action="store_true", help="count label answer")
    parser.add_argument("-d", "--diff", dest="diff", nargs="?", const="all", help="print diff of answers")
    parser.add_argument("-e", "--export", dest="export", nargs="?", const="", help="export label answer")
    parser.add_argument("-ej", "--exportjson", dest="exportjson", nargs="?", const="", help="export label json")
    parser.add_argument(
        "-ed", "--exportdir", dest="export_dir", nargs="?", default="html", help="export label answer dir"
    )
    parser.add_argument("-f", "--fromid", type=int, nargs="?", default=0, help="stat from file id")
    parser.add_argument("-t", "--toid", type=int, nargs="?", default=0, help="stat to file id")
    parser.add_argument("-n", "--headnum", type=int, nargs="?", default=5, help="count of field answer options")
    parser.add_argument("-m", "--mold", type=int, nargs="?", default=-1, required=True, help="stat mold id")
    parser.add_argument("-r", "--ratio", type=str, help="scale ratio range")
    parser.add_argument("--bydoc", dest="bydoc", action="store_true", help="stat answer average by doc")
    parser.add_argument("--orderby", dest="orderby", help="order by col: name or rate")
    parser.add_argument("--threshold", type=float, dest="threshold", help="crude answer threshold value")
    parser.add_argument("--strict", dest="strict", action="store_true", help="statistic with strict standard")
    parser.add_argument("-cm", dest="compliance", action="store_true", help="stat rule results")
    parser.add_argument("-u", "--host", type=str, nargs="?", default=host, help="scriber host:port")
    parser.add_argument("--whitelist", type=str, help="white list path")
    parser.add_argument("--special_years", type=str, help="special years")
    parser.add_argument(
        "--only_adjudge_enum",
        dest="only_adjudge_enum",
        action="store_true",
        help="considered correct when enum are same",
    )
    parser.add_argument("--ignore_enum", dest="ignore_enum", action="store_true", help="ignore_enum", default=False)
    parser.add_argument("-ff", "--from_file", type=str, help="stat ids from file")
    parser.add_argument("--jura_21", action="store_true", help="仅仅统计 jura21 的结果")
    parser.add_argument("--csv", dest="csv", action="store_true", help="export result to csv")
    parser.add_argument("-p", "--prefix", type=str, default="", help="filter output by prefix")
    parser.add_argument(
        "-ofg",
        "--only_first_group",
        dest="only_first_group",
        action="store_true",
        help="答案有多组时，仅对比第一组答案",
    )

    parser.set_defaults(crude=False, label=False, strict=False)
    args = parser.parse_args()

    main()
