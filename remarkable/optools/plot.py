import contextvars
import csv
import json
import os
import re
import zipfile
from collections import defaultdict
from dataclasses import dataclass
from datetime import datetime
from io import StringIO
from pathlib import Path
from typing import Any, Hashable, Sequence
from urllib.parse import quote

import matplotlib.pyplot as plt
import msgspec
import numpy as np
import pandas as pd
import polars as pl
from openpyxl.reader.excel import load_workbook
from openpyxl.styles import Alignment
from openpyxl.utils import get_column_letter
from pandas._typing import IndexLabel  # noqa
from peewee import fn
from playhouse.postgres_ext import ServerSide
from utensils.zip import ZipFilePlus

from remarkable import logger
from remarkable.answer.node import AnswerItem
from remarkable.common.common import HKEXSchema
from remarkable.common.constants import DocType
from remarkable.common.util import match_ext
from remarkable.config import project_root
from remarkable.db import pw_db
from remarkable.devtools import read_ids_from_file
from remarkable.models.hkex_file_meta import HKEXFileMeta
from remarkable.models.mold import special_mold
from remarkable.models.new_question import NewQuestion
from remarkable.models.rule_reference import RuleReference
from remarkable.predictor.utils import SafeFileName
from remarkable.prompter.schema import attribute_id


def load_alias_rule_df() -> pd.DataFrame:
    alias_rule_map_str = """MB Rule Name Alias,Tag,Batch
13.35,A1,JURA1
3.13 / AppD2.12B,A2,JURA1
10.06(4)(b),A3,JURA1
AppD2.4(1),A4,JURA1
AppD2.4(2),A5,JURA1
AppD2.4(2)(a),A6,JURA1
AppD2.4(2)(b),A7,JURA1
AppD2.4(3),A8,JURA1
AppD2.5,A9,JURA1
AppD2.9-1,A10.1,JURA1
AppD2.9-2,A10.2,JURA1
AppD2.9-3,A10.3,JURA1
AppD2.9-4,A10.4,JURA1
AppD2.9-5,A10.5,JURA1
AppD2.9-6,A10.6,JURA1
AppD2.10(1),A11.1,JURA1
AppD2.10(2),A11.2,JURA1
AppD2.10(3),A11.3,JURA1
AppD2.12-1,A12.1,JURA1
AppD2.12-2,A12.2,JURA1
AppD2.14,A13,JURA1
AppD2.15,A14,JURA1
AppD2.19,A15,JURA1
AppD2.24(1),A16,JURA1
AppD2.24(2),A17,JURA1
AppD2.24(3),A18,JURA1
AppD2.24B(1),A19.1,JURA1
AppD2.24B(2),A19.2,JURA1
AppD2.32(1),A20,JURA1
AppD2.32(2)(a),A21.1,JURA1
AppD2.32(2)(b),A21.2,JURA1
AppD2.32(2)(c),A21.3,JURA1
AppD2.32(2)(d),A21.4,JURA1
AppD2.32(2)(e),A21.5,JURA1
AppD2.32(5),A22,JURA1
14A.71(6)(a) - 14A.55,A23,JURA1
14A.72-1,A24.1,JURA1
14A.72-2,A24.2,JURA1
AppD2.12A,A25,JURA1
AppD2.17,A26,JURA1
AppD2.30,A27,JURA1
14A.63(3)-1,A28.1,JURA1
14A.63(3)-2,A28.2,JURA1
14A.63(3)-3,A28.3,JURA1
13.17(1),A29.1,JURA1
13.17(2),A29.2,JURA1
13.17(3),A29.3,JURA1
17.09(1)-SOS,A30,JURA1
17.09(2)-SOS,A31,JURA1
17.09(3)-SOS,A32,JURA1
17.09(4)-SOS,A33,JURA1
AppD2.11A - AppD2.11(8)(a),C1.1,JURA2-C
AppD2.11A - AppD2.11(8)(b),C1.2,JURA2-C
AppD2.11A - AppD2.11(8)(c),C1.3,JURA2-C
AppD2.32(4A)(a)-1,C2.1,JURA2-C
AppD2.32(4A)(a)-2,C2.2,JURA2-C
AppD2.32(4A)(a)-3,C2.3,JURA2-C
AppD2.32(4A)(b)-1,C2.4,JURA2-C
AppD2.32(4A)(b)-2,C2.5,JURA2-C
AppD2.32(4A)(c),C2.6,JURA2-C
AppD2.32(4A)(d),C2.7,JURA2-C
17.07,C3,JURA2-C
17.08-1,C4.1,JURA2-C
17.08-2,C4.2,JURA2-C
17.08-3,C4.3,JURA2-C
14A.71(6)(b) - 14A.56,C5,JURA2-C
AppD2.24(4)-(6),C6,JURA2-C
14A.71(1),C7.1,JURA2-C
14A.71(2),C7.2,JURA2-C
14A.71(3),C7.3,JURA2-C
14A.71(4),C7.4,JURA2-C
14A.71(5),C7.5,JURA2-C
AppD2.11(1),B1,JURA2
AppD2.11(2),B2,JURA2
AppD2.11(3),B3,JURA2
AppD2.11(4),B4,JURA2
AppD2.11(5),B5,JURA2
AppD2.11(6),B6,JURA2
AppD2.11(7),B7,JURA2
AppD2.11(8)(a),B8,JURA2
AppD2.11(8)(b),B9,JURA2
AppD2.11(8)(c),B10,JURA2
AppD2.13(1),B11,JURA2
AppD2.13(2),B12,JURA2
AppD2.13(3),B13,JURA2
AppD2.20,B14,JURA2
AppD2.22,B15,JURA2
AppD2.29,B16,JURA2
AppD2.25(1),B17,JURA2
AppD2.25(2),B18,JURA2
AppD2.25(3),B19,JURA2
AppD2.25(4),B20,JURA2
AppD2.25(5),B21,JURA2
AppD2.25(6),B22,JURA2
AppD2.26(1),B23,JURA2
AppD2.26(2),B24,JURA2
AppD2.26(3)(a)-1,B25,JURA2
AppD2.26(3)(a)-2,B26,JURA2
AppD2.26(3)(a)-3,B27,JURA2
AppD2.26(3)(a)-4,B28,JURA2
AppD2.26(3)(b),B29,JURA2
AppD2.26(3)(c),B30,JURA2
AppD2.26(3)(d),B31,JURA2
AppD2.28(1)(a),B32,JURA2
AppD2.28(1)(b)(i),B33,JURA2
AppD2.28(1)(b)(ii),B34,JURA2
AppD2.28(1)(b)(iii),B35,JURA2
AppD2.28(1)(b)(iv),B36,JURA2
AppD2.28(2)(a)-1,B37,JURA2
AppD2.28(2)(a)-2,B38,JURA2
AppD2.28(2)(a)-3,B39,JURA2
AppD2.28(2)(a)-4,B40,JURA2
AppD2.28(2)(b),B41,JURA2
AppD2.28(2)(c)-1,B42,JURA2
AppD2.28(2)(c)-2,B43,JURA2
AppD2.28(2)(d),B44,JURA2
AppD2.31(1),B45,JURA2
AppD2.31(2),B46,JURA2
AppD2.31(3),B47,JURA2
AppD2.31(4),B48,JURA2
AppD2.31(5),B49,JURA2
AppD2.31(6),B50,JURA2
AppD2.31(7),B51,JURA2
AppD2.32(10)-1,B52,JURA2
AppD2.32(10)-2,B53,JURA2
AppD2.32(11),B54,JURA2
AppD2.32(12),B55,JURA2
AppD2.32(3),B56,JURA2
AppD2.32(4),B57,JURA2
AppD2.32(6),B58,JURA2
AppD2.32(7),B59,JURA2
AppD2.32(8),B60,JURA2
AppD2.32(9),B61,JURA2
AppD2.4(1)(a),B62,JURA2
17.07(1)(b)-SOS,B63,JURA2
17.07(1)(a)-SOS,B63.1,JURA2.1
17.07(1)(c)-SOS,B64,JURA2
17.07(1)(d)-SOS,B65,JURA2
17.07(1)(e)-SOS,B66,JURA2
17.07(1)(f)-SOS,B67,JURA2
17.09(5)-SOS,B68,JURA2
17.09(6)-SOS,B69,JURA2
17.09(7)-SOS,B70,JURA2
17.09(8)-SOS,B71,JURA2
17.09(9)-SOS,B72,JURA2
17.09(1)-SAS,A34,JURA2.1
17.09(2)-SAS,A35,JURA2.1
17.09(3)-SAS,A36,JURA2.1
17.09(4)-SAS,A37,JURA2.1
17.12(1)(b) - 17.09(1),A38,JURA2.1
17.12(1)(b) - 17.09(2),A39,JURA2.1
17.12(1)(b) - 17.09(3),A40,JURA2.1
17.12(1)(b) - 17.09(4),A41,JURA2.1
17.12(1)(b) - 17.09(5),A42,JURA2.1
17.07(1)(a)-SAS,B73,JURA2.1
17.07(1)(b)-SAS,B74,JURA2.1
17.07(1)(c)-SAS,B75,JURA2.1
17.07(1)(d)-SAS,B76,JURA2.1
17.07(1)(e)-SAS,B77,JURA2.1
17.07(1)(f)-SAS,B78,JURA2.1
17.09(6)-SAS,B79,JURA2.1
17.09(7)-SAS,B80,JURA2.1
17.09(8)-SAS,B81,JURA2.1
17.09(9)-SAS,B82,JURA2.1
17.12(1)(a) - 17.07(1)(a),B83,JURA2.1
17.12(1)(a) - 17.07(1)(b),B84,JURA2.1
17.12(1)(a) - 17.07(1)(c),B85,JURA2.1
17.12(1)(a) - 17.07(1)(d),B86,JURA2.1
17.12(1)(a) - 17.07(1)(e),B87,JURA2.1
17.12(1)(a) - 17.07(1)(f),B88,JURA2.1
17.12(1)(b) - 17.09(6),B89,JURA2.1
17.12(1)(b) - 17.09(7),B90,JURA2.1
17.12(1)(b) - 17.09(8),B91,JURA2.1
17.12(1)(b) - 17.09(9),B92,JURA2.1
17.07(2)-SOS,B93.1,JURA2.1
17.07(2)-SAS,B93.2,JURA2.1
17.07(3)-SOS,B94.1,JURA2.1
17.07(3)-SAS,B94.2,JURA2.1
17.07A-SOS,B95.1,JURA2.1
17.07A-SAS,B95.2,JURA2.1
AppC1.A.1.2,B96,JURA2.1
AppC1.E.1.5,B97,JURA2.1
3.13,B98,JURA2.1
Note 2 of 3.09D,B99,JURA2.1
17.07(1)(a)-SOS(ARRP),B63.2,JURA2.1
17.07(1)(a)-SAS(ARRP),B73.1,JURA2.1
Audit opinion,D1,JURA3-D
Basis,D2,JURA3-D
KAM,D3,JURA3-D
GC's uncertainty,D4,JURA3-D
FR standards,D5,JURA3-D
Auditing standards,D6,JURA3-D
Auditor's name,D7,JURA3-D
Auditor's location,D8,JURA3-D
AR date,D9,JURA3-D
Modified opinion,Disclosure1,JURA3
Potential fraud/internal control breakdown,Disclosure2,JURA3
Potential legal issue,Disclosure3,JURA3
Reviewed by AC,Disclosure19,JURA3
Statement of profit or loss,Disclosure5,JURA3
Statement of financial position,Disclosure6,JURA3
Note of revenue,Disclosure7,JURA3
Note of taxation,Disclosure8,JURA3
Note of EPS,Disclosure9,JURA3
Note of dividend,Disclosure10,JURA3
"Purchase, Sales or Redemption of Listed Securities",Disclosure11,JURA3
CG Code,Disclosure12,JURA3
Significant changes in accounting policies,Disclosure13,JURA3
Prior period adjustment,Disclosure14,JURA3
Discontinued operation,Disclosure15,JURA3
Section 436(3) of HKCO,Disclosure16,JURA3
"Money lending, indent trading or proprietary securities trading",Disclosure17,JURA3
Comment on segment,Disclosure18,JURA3
Audited or agreed with the auditors,Disclosure4,JURA3
Cash Ratio,ratio1,JURA3
"Impairment - Revenue Ratio
Impairment - Total Assets Ratio",ratio2,JURA3
Profit Fluctuation,ratio3,JURA3
Significant Investment,ratio4,JURA3
Gain/ Loss on Disposal,ratio5,JURA3
Assets Fluctuation,ratio6,JURA3
Revenue/ Gross Profit Level,ratio7,JURA3
MDR 13 i - board oversight,MDR 13 i - board oversight,JURA4
MDR 13 ii - board management approach,MDR 13 ii - board management approach,JURA4
MDR 13 iii - board progress review,MDR 13 iii - board progress review,JURA4
MDR 14 part 1 - materiality application,MDR 14 part 1 - materiality application,JURA4
MDR 14 part 2 - quantitative application,MDR 14 part 2 - quantitative application,JURA4
MDR 14 part 3 - consistency application,MDR 14 part 3 - consistency application,JURA4
MDR 15 - reporting boundary,MDR 15 - reporting boundary,JURA4
A1 policies - emissions,A1 policies - emissions,JURA4
A1 law compliance - emissions,A1 law compliance - emissions,JURA4
KPI A1.1 - emission type and data,KPI A1.1 - emission type and data,JURA4
KPI A1.2 part 1 - Scope 1,KPI A1.2 part 1 - Scope 1,JURA4
KPI A1.2 part 2 - Scope 2,KPI A1.2 part 2 - Scope 2,JURA4
KPI A1.2 part 3 - Scope 3,KPI A1.2 part 3 - Scope 3,JURA4
KPI A1.3 - hazardous waste,KPI A1.3 - hazardous waste,JURA4
KPI A1.4 - non-hazardous waste,KPI A1.4 - non-hazardous waste,JURA4
KPI A1.5 - emission target,KPI A1.5 - emission target,JURA4
KPI A1.6 part 1 - waste handling,KPI A1.6 part 1 - waste handling,JURA4
KPI A1.6 part 2 - waste reduction target,KPI A1.6 part 2 - waste reduction target,JURA4
A2 policies - use of resources,A2 policies - use of resources,JURA4
KPI A2.1 - energy consumption,KPI A2.1 - energy consumption,JURA4
KPI A2.2 - water consumption,KPI A2.2 - water consumption,JURA4
KPI A2.3 - energy efficiency targets,KPI A2.3 - energy efficiency targets,JURA4
KPI A2.4 part 1 - water sourcing,KPI A2.4 part 1 - water sourcing,JURA4
KPI A2.4 part 2 - water efficiency targets,KPI A2.4 part 2 - water efficiency targets,JURA4
KPI A2.5 - packaging material,KPI A2.5 - packaging material,JURA4
A3 policies - environment and natural resources,A3 policies - environment and natural resources,JURA4
KPI A3.1 - impact on environment and natural resources,KPI A3.1 - impact on environment and natural resources,JURA4
A4 policies - climate-related issues,A4 policies - climate-related issues,JURA4
KPI A4.1 - climate-related issues & impact,KPI A4.1 - climate-related issues & impact,JURA4
B1 policies - employment,B1 policies - employment,JURA4
B1 law compliance - employment,B1 law compliance - employment,JURA4
KPI B1.1 - workforce by types,KPI B1.1 - workforce by types,JURA4
KPI B1.2 - employee turnover by types,KPI B1.2 - employee turnover by types,JURA4
B2 policies - health and safety,B2 policies - health and safety,JURA4
B2 law compliance - health and safety,B2 law compliance - health and safety,JURA4
KPI B2.1 - work-related fatalities,KPI B2.1 - work-related fatalities,JURA4
KPI B2.2 - work injury lost days,KPI B2.2 - work injury lost days,JURA4
KPI B2.3 - health and safety measures,KPI B2.3 - health and safety measures,JURA4
B3 policies - development and training,B3 policies - development and training,JURA4
KPI B3.1 - percentage of employees trained,KPI B3.1 - percentage of employees trained,JURA4
KPI B3.2 - training hours completed,KPI B3.2 - training hours completed,JURA4
B4 policies - labour standards,B4 policies - labour standards,JURA4
B4 law compliance - labour standards,B4 law compliance - labour standards,JURA4
KPI B4.1 - review measures to avoid child & forced labour,KPI B4.1 - review measures to avoid child & forced labour,JURA4
KPI B4.2 - steps to avoid child & forced labour,KPI B4.2 - steps to avoid child & forced labour,JURA4
B5 policies - supply chain,B5 policies - supply chain,JURA4
KPI B5.1 - number of suppliers,KPI B5.1 - number of suppliers,JURA4
KPI B5.2 - suppliers engagement,KPI B5.2 - suppliers engagement,JURA4
KPI B5.3 - supply chain ESG risks identification,KPI B5.3 - supply chain ESG risks identification,JURA4
KPI B5.4 - practice to promote environmentally preferable products,KPI B5.4 - practice to promote environmentally preferable products,JURA4
B6 policies - product responsibility,B6 policies - product responsibility,JURA4
B6 law compliance - product responsibility,B6 law compliance - product responsibility,JURA4
KPI B6.1 - products recall,KPI B6.1 - products recall,JURA4
KPI B6.2 - products related complaints,KPI B6.2 - products related complaints,JURA4
KPI B6.3 - IP rights protection,KPI B6.3 - IP rights protection,JURA4
KPI B6.4 - quality assurance process,KPI B6.4 - quality assurance process,JURA4
KPI B6.5 - consumer data protection,KPI B6.5 - consumer data protection,JURA4
B7 policies - anti-corruption,B7 policies - anti-corruption,JURA4
B7 law compliance - anti-corruption,B7 law compliance - anti-corruption,JURA4
KPI B7.1 - legal cases on corruption,KPI B7.1 - legal cases on corruption,JURA4
KPI B7.2 - preventive measures & whistle-blowing procedures,KPI B7.2 - preventive measures & whistle-blowing procedures,JURA4
KPI B7.3 - anti-corruption training,KPI B7.3 - anti-corruption training,JURA4
B8 policies - community investment,B8 policies - community investment,JURA4
KPI B8.1 - community investment focus,KPI B8.1 - community investment focus,JURA4
KPI B8.2 - resources contributed,KPI B8.2 - resources contributed,JURA4
A(a) - Application of Principles,A(a)-Explanation of application of code principles,JURA5
A(b) - Compliance with CPs,A(b)-Compliance with CPs,JURA5
A(c) - Deviation from CPs,A(c)-Deviation from Code Provisions,JURA5
B(a) - Board Composition,B(a)-Board Composition by category of directors,JURA5
B(b) - No. of board meetings,B(b)-No. of board meetings,JURA5
B(c) - Directors attendance,B(c)-Attendance of directors at board and general meetings,JURA5
B(d) - No. of board or committee meetings,B(d)-Number of board or committee meetings attended,JURA5
B(e) - Responsibilities of board and management,B(e)-Statement of responsibilities of board and management,JURA5
B(f) - Non-compliance with INED requirements,B(f)-Details of non-compliance with rules 3.10 and 3.10A and remedial steps,JURA5
B(g) - Independence of INEDs,B(g)-Why INED is independent if Rule 3.13 is not complied,JURA5
B(h) - Relationship,B(h)-Relationship between board members and between chairman and CEO,JURA5
B(i) - Directors training,B(i)-Directors' compliance with C.1.4,JURA5
C - Identity of chairman and CEO,C-Identity of chairman and CEO,JURA5
D - Term of appointment of NEDs,D-Term of appointment of NED,JURA5
E(a) - RC - Role and function,RC-E(a)-Role and function,JURA5
E(b) - RC - Composition,RC-E(b)-Composition,JURA5
E(c) - RC - Number of meetings and attendance,RC-E(c)-Number of meetings and record of attendance,JURA5
E(d)(ii) - Remuneration policy of EDs,E(d)(ii)-1-Policy for remuneration of executive directors,JURA5
E(d)(ii)-1 - Performance assessment of EDs,E(d)(ii)-2-Assessing performance of executive directors,JURA5
E(d)(ii)-2 - Approval of directors' service contracts,E(d)(ii)-3-Approving terms of directors' service contracts,JURA5
E(d)(ii)-3 - Review and/or approval of share scheme,E(d)(ii)-4-Reviewing and or approving share scheme matters,JURA5
E(d)(ii)-4 - Determination models of EDs' remuneration,E(d)(ii)-5-Disclose which of the models in E.1.2(c) adopted,JURA5
E(a) - NC - Role and function,NC-E(a)-Role and function,JURA5
E(b) - NC - Composition,NC-E(b)-Composition,JURA5
E(c)- NC - No. of meetings and attendance,NC-E(c)-Number of meetings and record of attendance,JURA5
E(d)(iii) - Nomination policy,E(d)(iii)-Policy for directors nomination,JURA5
E(a) - AC - Role and function,AC-E(a)-Role and function,JURA5
E(b) - AC - Composition,AC-E(b)-Composition,JURA5
E(c) - AC - No. of meetings and attendance,AC-E(c)-Number of meetings and record of attendance,JURA5
E(d)(i)-1 - Review of financial reports,E(d)(i)-Review of financial reports,JURA5
E(d)(i)-2 - Review of risk management and internal control,E(d)(i)-1-Review of risk management and internal control system,JURA5
E(d)(i)-3 - Effectiveness of internal audit,E(d)(i)-2-Effectiveness of internal audit function,JURA5
E(d)(i)-4 - Non-compliance with AC composition,E(d)(i)-3-Non-compliance with Rule 3.21 and remedial steps,JURA5
E(a) - RiskCom - Role and function,RiC-E(a)-Role and function,JURA5
E(b) - RiskCom - Composition,RiC-E(b)-Composition,JURA5
E(c) - RiskCom - No. of meetings and attendance,RiC-E(c)-Number of meetings and record of attendance,JURA5
E(d)(iv)-1 - Review of risk management and internal control,E(d)(iv)-1-How responsibility met in review of risk management and internal control,JURA5
E(d)(iv)-2 - Effectiveness of internal audit,E(d)(iv)-2-Effectiveness of issuer's internal audit function,JURA5
E(a) - CG - Role and function,CG-E(a)-Role and function,JURA5
E(b) - CG - Composition,CG-E(b)-Composition,JURA5
E(c) - CG - No. of meetings and attendance,CG-E(c)-Number of meetings and record of attendance,JURA5
E(d)(v)-1 - Determination of CG policy,E(d)(v)-1-Determine issuer's CG policy,JURA5
E(d)(v)-2 - CG duties of board or committee,E(d)(v)-2-Duties performed by the board or the committee under CP A.2.1,JURA5
F(a) - Primary contact person,F(a)-Primary contact person if external service provider is engaged,JURA5
F(b) - Non-compliance with Company Secretary Training,F(b)-Details of non-compliance with rule 3.29,JURA5
G(a) - Adoption of model code,G(a)-Adoption of model code,JURA5
G(b) - Compliance with model code,G(b)-Directors' compliance with model code,JURA5
G(c) - Non-compliance with Model Code and remedial steps,G(c)-Details of non-compliance and remedial steps,JURA5
H(a) - Internal audit function,H(a)-Whether issuer has internal audit function,JURA5
H(b) - Frequency of review and period covered,H(b)-Frequency of review and the period covered,JURA5
H(c) - Effectiveness and adequacy,H(c)-Whether issuer considers them effective and adequate,JURA5
I(a) - Remuneration of audit and non-audit services,I(a)Analysis of remuneration of audit and non-audit services,JURA5
I(b) - Nature of non-audit services and fees,I(b)-Details of nature of significant non-audit services and fees paid,JURA5
J(a) - Board diversity policy,J(a)-Board diversity policy,JURA5
J(b) - Numerical targets and timelines,J(b)-Numerical targets and timelines and measures adopted,JURA5
J(c) - Gender ratios and objectives in workforce,J(c)-Gender ratios in the workforce and gender diversity objectives,JURA5
K(a) - How to convene EGM,K(a)-How to convene EGM,JURA5
K(b) - Enquiry Procedure and contact details,K(b)-Enquiry Procedure and contact details,JURA5
K(c) - Shareholders' proposals,K(c)-Procedure to put forward proposal in shareholders' meetings,JURA5
L(a) - Significant changes in constitutional documents,L(a)-Significant changes in constitutional documents,JURA5
L(b) - Shareholders' communication policy,L(b)-Shareholders' communication policy,JURA5
L(c) - Annual review of communication policy,L(c)-Annual review on effectiveness of communication policy,JURA5
"""
    # rule_alias_path = "/home/<USER>/Documents/rule-alias-batch.xlsx"
    # base_df = pd.read_excel(rule_alias_path, dtype=str)
    # base_df = base_df.map(lambda x: x.strip() if isinstance(x, str) else x)
    # base_df.to_csv(index=False)
    # 以上代码用于将 Excel 文件中的所有字符串列去除首尾空格并保存为 CSV 文件，也即下面的 alias_rule_map_str
    return pd.read_csv(StringIO(alias_rule_map_str))


def combine_pics(pics_dir: Path | str):
    """将生成的初步定位频率分布直方图按年份展示在一起，见：
    https://mm.paodingai.com/cheftin/pl/te8yf34h738e5yo8zzf819wixa
    """
    if isinstance(pics_dir, str):
        pics_dir = Path(pics_dir)
    # 找到所有同名图片文件
    image_files = defaultdict(list)
    for sub_dir in sorted(p.name for p in pics_dir.iterdir() if p.is_dir()):
        for file in os.listdir(os.path.join(pics_dir, sub_dir)):
            if file.endswith(".png"):
                image_name = os.path.splitext(file)[0]
                image_files[image_name].append(os.path.join(sub_dir, file))

    h5 = """<!DOCTYPE html>
<html>
<head>
  <title>图片对比</title>
  <style>
    .image-row {
      display: flex;
      justify-content: center;
      margin-bottom: 10px;
      font-size: 14px;
      font-weight: bold;
    }
    .image-row img {
      max-width: 300px;
      margin: 0 10px;
    }
  </style>
</head>
<body>
  <h1>图片对比</h1>
  __rows__
</body>
</html>"""
    rows = []
    for image_name, image_paths in sorted(image_files.items(), key=lambda x: x[0]):
        if image_name.startswith("("):
            continue
        rows.append('  <div class="image-row">')
        rows.append(f"    {SafeFileName.restore(image_name).rsplit('_', maxsplit=1)[0][:12]}...")
        for image_path in image_paths:
            rows.append(
                f'    <img src="{quote(image_path)}" alt="{quote(image_path.split("/")[0])} - {quote(image_name)}">'
            )
        rows.append("  </div>")
    (pics_dir / "index.html").write_text(h5.replace("__rows__", "\n".join(rows)))


def df2hist(df: pd.DataFrame, group_idx: int, score_idx: int, out_dir: str = "score_histograms"):
    os.makedirs(out_dir, exist_ok=True)
    if len(df.columns) > 2:
        # 将 Score 列转换为数值类型，无效值设为 NaN
        df.iloc[:, score_idx] = pd.to_numeric(df.iloc[:, score_idx], errors="coerce")
        # 过滤出 Score 列中的有效值（不为 NaN 且大于 0）
        df = df[df.iloc[:, score_idx].notna() & (df.iloc[:, score_idx] >= 0)]
    # 按指定列分组
    grouped = df.groupby(df.iloc[:, group_idx])
    # 为每个规则绘制直方图并保存
    for name, group in grouped:
        plt.figure(figsize=(10, 6))

        scores = group.iloc[:, score_idx]
        scores.hist(bins=30, edgecolor="black")

        plt.title(f'Distribution of Scores for "{name}"')
        plt.xlabel("Score")
        plt.ylabel("Frequency")

        # 添加一些统计信息到图中
        stats_text = (
            f"Mean: {scores.mean():.4f}\n"
            f"Median: {scores.median():.4f}\n"
            f"Std: {scores.std():.4f}\n"
            f"Min: {scores.min():.4f}\n"
            f"Max: {scores.max():.4f}"
        )
        plt.text(
            0.85,
            0.95,
            stats_text,
            transform=plt.gca().transAxes,
            verticalalignment="top",
            horizontalalignment="right",
            bbox={
                "boxstyle": "round",
                "facecolor": "white",
                "alpha": 0.8,
            },
        )

        # 保存图片
        plt.savefig(os.path.join(out_dir, f"{SafeFileName.escape(name)}_histogram.png"))
        plt.close()  # 关闭图形，释放内存

        # 打印统计信息
        logger.info(f"\nStatistics for {name}:\n{stats_text}")
    logger.info(f"Histograms have been saved in the '{out_dir}' folder.")


def histogram_plot(path: str, out_dir="score_histograms", group_idx=1, score_idx=13):
    """读取by-rule按年导出统计Excel，对`Score`列（初步定位Top1）绘制直方图并保存到指定文件夹中"""
    df2hist(table_from_file(path), group_idx, score_idx, out_dir)


def histogram_plot_from_db(report_years: list[int], mold_ids: list[int], top_n: int = 1, out_dir="score_histograms"):
    """从数据库中读取数据，对`Score`列（初步定位Top1）绘制直方图并保存到指定文件夹中"""
    select = NewQuestion.select(NewQuestion.crude_answer).join(HKEXFileMeta, on=(NewQuestion.fid == HKEXFileMeta.fid))
    cond = NewQuestion.crude_answer.is_null(False)
    cond &= HKEXFileMeta.doc_type == DocType.AR
    cond &= HKEXFileMeta.report_year.cast("int").in_(report_years)
    cond &= NewQuestion.mold.in_(mold_ids)
    data = {"rule": [], "score": []}
    with pw_db.allow_sync():
        for question in ServerSide(select.where(cond)):
            if not question.crude_answer:
                continue
            for rule, elements in question.crude_answer.items():
                for element in elements[:top_n]:
                    data["rule"].append(rule)
                    data["score"].append(element["score"])
    df2hist(pd.DataFrame(data), 0, 1, out_dir)


@dataclass
class RuleCount:
    name: str
    counts: dict[str, int]


@dataclass
class CountPair:
    left: RuleCount
    right: RuleCount


async def compare_rule_counts(*count_pair: CountPair) -> pd.DataFrame:
    # 合并所有规则，直接使用集合操作
    all_rules = {k for c in count_pair for k in c.left.counts.keys() | c.right.counts.keys()}
    rule_ref = {item.main_alias: item for item in await RuleReference.manager().execute(RuleReference.select())}
    # jura12_rules = filter_rules("JURA1", "JURA2")
    # 创建比较数据
    result = []
    for rule in all_rules:
        # if rule not in rule_ref or rule_ref[rule].rule not in jura12_rules:
        if rule not in rule_ref:
            continue

        result.append(
            {
                "Tag": rule_ref[rule].rule,
                "MB Rule Alias": rule,
                "GEM Rule Alias": rule_ref[rule].gem_alias,
            }
        )
        for pair in count_pair:
            left_c = pair.left.counts.get(rule, 0)
            right_c = pair.right.counts.get(rule, 0)
            diff_c = right_c - left_c
            result[-1].update(
                {
                    f"{pair.left.name}": left_c,
                    f"{pair.right.name}": right_c,
                    f'Diff("{pair.right.name}" - "{pair.left.name}")': diff_c,
                }
            )

    # 创建DataFrame并排序
    df = pd.DataFrame(result)
    df = df.sort_values("Tag")

    return df


def rule_counts(df: pd.DataFrame, idx: int = 1, ai_idx: int = 7, manual_idx: int = 8) -> dict[Hashable, Any]:
    """从20xx_all.xlsx中统计各rule的不合规计数"""
    counts = {}
    for name, group in df.groupby(df.iloc[:, idx]):
        counts[name] = group[
            # NOTE：可以根据实际情况调整筛选条件
            # 1. 人工标注不合规
            (group.iloc[:, manual_idx] == "potential non-compliance")
            # 2. 人工未标注且AI标注不合规
            | (group.iloc[:, manual_idx].isna() & (group.iloc[:, ai_idx] == "potential non-compliance"))
            # 3. 人工、AI均未标注
            | ((group.iloc[:, manual_idx].isna()) & group.iloc[:, ai_idx].isna())
        ].shape[0]
    return {k: v for k, v in counts.items() if v}


def get_col_by_pattern(columns: list[str], pattern: str) -> str:
    """从列名中获取符合指定模式的列名（仅返回第一个匹配项）"""
    for idx, col in enumerate(columns, 1):
        if re.match(pattern, col):
            logger.info(f"Column matched => {idx}: {col}")
            return col
    raise ValueError(f"No column found by pattern: {pattern}")


def get_group_col(columns: list[str]) -> str:
    """从列名中获取规则组别列名"""
    return get_col_by_pattern(columns, r"^(MB|Rule)")


def get_count_col(columns: list[str], ai=True) -> str:
    """从列名中获取计数列名"""
    return get_col_by_pattern(columns, r"^AI" if ai else r"^Manual")


def calc_diff_percentage(df: pl.DataFrame, ai=True) -> dict[str, dict[str, float]]:
    """
    计算每个组别中不同count值的占比

    Args:
        df: 输入数据框
        ai: 是否使用AI列作为count列

    Returns:
        嵌套字典：{group: {count_value: percentage}}
    """
    group_col = get_group_col(df.columns)
    count_col = get_count_col(df.columns, ai=ai)
    invalid_rules = {group_col, "14A.55", "14A.71(6)(a) - 14A.55", "17.07(1)(b)-SOS"}

    # 过滤数据
    df_filtered = df.filter((pl.col(group_col).is_not_null()) & (~pl.col(group_col).is_in(invalid_rules)))

    # 自动获取count列中的唯一值
    count_values = df_filtered.select(pl.col(count_col)).drop_nulls().unique().to_series().sort()
    logger.info(f"Detected count values: {list(count_values)}")

    # 创建结果字典
    result_dict = {}

    # 对每个组别计算结果
    groups = df_filtered.group_by(group_col)

    # 计算每个count值的占比
    for count_value in count_values:
        result = groups.agg(
            [(pl.col(count_col) == count_value).sum().alias("count"), pl.len().alias("total")]
        ).with_columns([(pl.col("count") / pl.col("total")).alias("percentage")])

        # 更新结果字典
        for group, percentage in zip(result[group_col], result["percentage"]):
            if group not in result_dict:
                result_dict[group] = {}
            result_dict[group][count_value] = percentage

    return result_dict


def collect_diffs(df: pl.DataFrame, *, diff_col: str = "Stock code", ai: bool = True) -> dict[str, dict[str, set[str]]]:
    # 过滤掉无效的规则
    group_col = get_group_col(df.columns)
    invalid_rules = {group_col, "14A.55", "14A.71(6)(a) - 14A.55", "17.07(1)(b)-SOS"}
    df_filtered = df.filter((pl.col(group_col).is_not_null()) & (~pl.col(group_col).is_in(invalid_rules)))

    # 按count_col的值和规则分组收集差异
    count_col = get_count_col(df.columns, ai=ai)
    result = df_filtered.group_by([count_col, group_col]).agg(pl.col(diff_col).alias("diff_set"))

    # 构建嵌套字典
    rules_by_value = {}
    for row in result.iter_rows(named=True):
        value = row[count_col] or "None"
        rule = row[group_col]
        diff_set = set(row["diff_set"])

        if diff_set:  # 只保留非空集合
            if value not in rules_by_value:
                rules_by_value[value] = {}
            rules_by_value[value][rule] = diff_set

    return rules_by_value


def collect_files(dst: Path) -> dict[str, list[Path]]:
    """
    从目标目录中收集文件，按年份分组，两两比对
    /dst
    ├── 2020_回滚前_20240625.xlsx
    ├── 2021_回滚前_20240625.xlsx
    ├── 2020_回滚后_20240627.xlsx
    ├── 2021_回滚后_20240627.xlsx

    :return: {year: [left, right]}
    """
    year_p = re.compile(r"(20\d{2})_")
    year_paths = defaultdict(list)
    for path in dst.iterdir():
        if path.name.startswith("."):
            continue
        if path.name.endswith(".xlsx") or path.name.endswith(".csv"):
            if year := year_p.search(path.name):
                year_paths[year.group(1)].append(path)
    return {k: sorted(year_paths[k], reverse=True) for k in sorted(year_paths, key=int) if len(year_paths[k]) == 2}


def pick_column(name: str) -> str:
    return name.rsplit("_", maxsplit=1)[0]


async def make_compare_summary(in_dir: str | Path, out_path: str | Path):
    """从指定文件夹中比对同年份 Non-Compliant Issuers & 20xx_all_xxx 导出 Excel 中特定列（按 Rule 即 Tag 分）的计数情况"""
    in_dir = Path(in_dir) if isinstance(in_dir, str) else in_dir
    pairs = []
    for year, (left, right) in collect_files(in_dir).items():
        pairs.append(
            CountPair(
                RuleCount(f"{year}-{pick_column(left.name).replace(year, '')}", rule_counts(table_from_file(left))),
                RuleCount(f"{year}-{pick_column(right.name).replace(year, '')}", rule_counts(table_from_file(right))),
            )
        )
    df = await compare_rule_counts(*pairs)
    df.to_excel(out_path, index=False)


jura1_schema = contextvars.ContextVar("jura1_schema")


def fill_rule_result(answer: dict):
    """Fill rule_result.items for Jura1 schema."""
    if not jura1_schema.get(None):
        jura1_schema.set(HKEXSchema(answer["schema"]))

    if not answer.get("rule_result"):
        answer["rule_result"] = {"items": []}
    for item in answer.get("userAnswer", {}).get("items") or []:
        if not item.get("value"):
            continue
        aid = attribute_id(item["key"])
        if aid.endswith(")"):
            tag = jura1_schema.get().crude_key2rule(aid)[1:-1]
            item["key"] = json.dumps([json.loads(item["key"])[0], f"{tag}:0"])
            answer["rule_result"]["items"].append(item)


async def dump_compliance_values_to_csv(mids: Sequence[int], report_years: Sequence[int], out_path: str | Path) -> None:
    stmt = _make_query(mids, report_years)

    rule_ref = {item.rule: item for item in await RuleReference.manager().execute(RuleReference.select())}
    with open(out_path, "w", newline="") as fp:
        writer = csv.writer(fp)
        writer.writerow(
            [
                "Tag",
                "MB Rule name Alias",
                "Year of annual report",
                "Stock code",
                "Fake Col1",
                "Fake Col2",
                "Fake Col3",
                "Compliance assessment(AI)",
                "Compliance assessment(Manual)",
                "User",
            ]
        )
        with pw_db.allow_sync():
            for row in ServerSide(stmt.namedtuples()):
                # {tag: [AI, Manual, User]}
                tag_values = defaultdict(lambda: ["", "", ""])
                for idx, answer in enumerate((row.preset_answer, row.answer)):
                    if answer and row.mold == special_mold.v1_id:
                        # Jura1 比较特殊，需要做一个转换
                        fill_rule_result(answer)

                    if not answer or not answer.get("rule_result") or not answer["rule_result"].get("items"):
                        logger.warning(
                            f"No valid compliance answer found in question.{idx}:"
                            f" {row.fid=}, {row.mold=}, {row.stock_code=}, {row.report_year=}"
                        )
                        continue

                    for item in answer["rule_result"]["items"]:
                        if not item.get("value"):
                            # Skip empty values
                            continue
                        tag = attribute_id(item["key"])
                        if not item.get("marker") or item["marker"]["name"] == "admin":
                            # 没有marker或者是admin的，算作是AI的结果
                            tag_values[tag][0] = item["value"].lower()
                            # 如果已经有了标注人员信息，说明是人工标注的结果，不再覆盖
                            tag_values[tag][-1] = tag_values[tag][-1] or "AI"
                            continue

                        if item["marker"]["name"].startswith("pai_"):
                            # Skip PAI markers
                            continue

                        tag_values[tag][1] = item["value"].lower()
                        tag_values[tag][-1] = item["marker"]["name"]

                for tag, values in tag_values.items():
                    writer.writerow(
                        [
                            tag,
                            rule_ref[tag].main_alias if tag in rule_ref else "",
                            row.report_year,
                            row.stock_code,
                            *[""] * 3,
                            *values,
                        ]
                    )
        logger.info(f"Dump compliance values to {out_path}")

        # 分析合规性数据并生成摘要
        analyze_compliance_data(out_path, rule_ref)


def analyze_compliance_data(out_path: str | Path, rule_ref) -> None:
    """分析合规性数据并生成统计摘要

    Args:
        out_path: 输入CSV文件路径，可以是字符串或Path对象
        rule_ref: 规则引用字典

    生成的摘要文件包含以下列:
        - Tag: 规则标签
        - Year of annual report: 年报年份
        - count: 总数量
        - c_count: 合规数量
        - nc_count: 不合规数量
        - c_percent: 合规百分比
        - nc_percent: 不合规百分比
    """
    # 确保out_path是Path对象
    out_path = Path(out_path)

    # 读取CSV文件
    df = pd.read_csv(out_path)

    # 按Tag和年份分组
    grouped = df.groupby(["Tag", "Year of annual report"])

    # 创建结果列表
    results = []

    # 对每个分组进行统计
    for (tag, year), group in grouped:
        # 计算合规和不合规的数量
        compliance_counts = group["Compliance assessment(AI)"].value_counts()
        c_count = compliance_counts.get("compliance", 0)
        nc_count = compliance_counts.get("potential non-compliance", 0)
        total_count = c_count + nc_count

        # 计算百分比
        c_percent = (c_count / total_count * 100) if total_count > 0 else 0
        nc_percent = (nc_count / total_count * 100) if total_count > 0 else 0

        # 添加到结果列表
        results.append(
            {
                "Tag": tag,
                "MB Rule name Alias": rule_ref[tag].main_alias if tag in rule_ref else "",
                "Year of annual report": year,
                "count": total_count,
                "c_count": c_count,
                "nc_count": nc_count,
                "c_percent": round(c_percent, 2),
                "nc_percent": round(nc_percent, 2),
            }
        )

    # 创建结果DataFrame
    result_df = pd.DataFrame(results)

    # 创建Excel文件
    output_path = out_path.parent / f"{out_path.stem}_summary.xlsx"

    # 创建ExcelWriter对象
    with pd.ExcelWriter(output_path, engine="openpyxl") as writer:
        result_df.to_excel(writer, sheet_name="summary", index=False)

        # 按年份分组并创建单独的工作表
        year_groups = result_df.groupby("Year of annual report")
        for year, year_data in year_groups:
            # 按Tag排序
            year_data_sorted = year_data.sort_values("Tag")
            # 删除Year of annual report列，因为已经在sheet名称中显示
            year_data_sorted = year_data_sorted.drop("Year of annual report", axis=1)
            # 将数据写入对应的工作表
            year_data_sorted.to_excel(writer, sheet_name=str(year), index=False)

        # 创建年度对比工作表
        comparison_df = result_df.pivot(
            index=["Tag", "MB Rule name Alias"], columns="Year of annual report", values="c_percent"
        )
        comparison_df = comparison_df.reset_index()

        # 计算年度间的差值
        years = sorted(result_df["Year of annual report"].unique())
        for i in range(len(years) - 1):
            year1, year2 = years[i], years[i + 1]
            diff_col = f"{year2}-{year1} Diff"
            comparison_df[diff_col] = comparison_df[year2] - comparison_df[year1]

        # 将对比数据写入新的工作表
        comparison_df.to_excel(writer, sheet_name="Years Comparison", index=False)

    logger.info(f"Generated summary file at {output_path}")


async def dump_disclosure_values_to_csv(mids: Sequence[int], report_years: Sequence[int], out_path: str | Path) -> None:
    # 保证只取同年度最新的报告
    stmt = _make_query(mids, report_years)

    rule_ref = {item.rule: item for item in await RuleReference.manager().execute(RuleReference.select())}
    with open(out_path, "w", newline="") as fp:
        writer = csv.writer(fp)
        writer.writerow(
            [
                "Tag",
                "MB Rule name Alias",
                "Year of annual report",
                "Stock code",
                "Disclosure Location",
                "Enum Value(AI)",
                "Enum Value(Manual)",
                "User",
            ]
        )
        with pw_db.allow_sync():
            for row in ServerSide(stmt.namedtuples()):
                # {tag: [AI, Manual, User]}
                tag_values = defaultdict(lambda: ["", "", "", ""])
                for idx, answer in enumerate((row.preset_answer, row.answer)):
                    if answer and row.mold == special_mold.v1_id:
                        # Jura1 比较特殊，需要做一个转换
                        fill_rule_result(answer)

                    if not answer or not answer.get("userAnswer") or not answer["userAnswer"].get("items"):
                        logger.warning(
                            f"No valid compliance answer found in question.{idx}:"
                            f" {row.fid=}, {row.mold=}, {row.stock_code=}, {row.report_year=}"
                        )
                        continue

                    for item in answer["userAnswer"]["items"]:
                        answer_item = AnswerItem(**item)
                        if not answer_item.enum:
                            # Skip empty values
                            continue
                        tag = attribute_id(item["key"])
                        if not item.get("marker") or item["marker"]["name"] == "admin":
                            # 没有marker或者是admin的，算作是AI的结果
                            tag_values[tag][1] = answer_item.enum.lower()
                            # 如果已经有了标注人员信息，说明是人工标注的结果，不再覆盖
                            tag_values[tag][0] = tag_values[tag][0] or AnswerItem.text_only(answer_item)[:100]
                            tag_values[tag][-1] = tag_values[tag][-1] or "AI"
                            continue

                        if item["marker"]["name"].startswith("pai_"):
                            # Skip PAI markers
                            continue

                        tag_values[tag][0] = AnswerItem.text_only(answer_item)[:100]
                        tag_values[tag][2] = answer_item.enum.lower()
                        tag_values[tag][-1] = item["marker"]["name"]

                for tag, values in tag_values.items():
                    _tag = tag.split("-", maxsplit=1)[0]
                    writer.writerow(
                        [
                            tag,
                            rule_ref[_tag].main_alias if _tag in rule_ref else "",
                            row.report_year,
                            row.stock_code,
                            *values,
                        ]
                    )

        logger.info(f"Dump disclosure values to {out_path}")


def _make_query(mids, report_years):
    latest_records = (
        HKEXFileMeta.select(
            HKEXFileMeta.stock_code,
            HKEXFileMeta.report_year,
            fn.MAX(HKEXFileMeta.fid).alias("max_fid"),
        )
        .where(HKEXFileMeta.deleted_utc == 0, HKEXFileMeta.doc_type == DocType.AR)
        .group_by(HKEXFileMeta.stock_code, HKEXFileMeta.report_year)
        .alias("latest_records")
    )
    stmt = (
        NewQuestion.select(
            NewQuestion.fid,
            NewQuestion.mold,
            NewQuestion.preset_answer,
            NewQuestion.answer,
            HKEXFileMeta.report_year,
            HKEXFileMeta.stock_code,
        )
        .join(HKEXFileMeta, on=(NewQuestion.fid == HKEXFileMeta.fid))
        .join(
            latest_records,
            on=(
                (HKEXFileMeta.stock_code == latest_records.c.stock_code)
                & (HKEXFileMeta.report_year == latest_records.c.report_year)
                & (HKEXFileMeta.fid == latest_records.c.max_fid)
            ),
        )
        .where(
            NewQuestion.mold.in_(mids),
            HKEXFileMeta.report_year.cast("int").in_(report_years),
        )
        .order_by(NewQuestion.id.desc())
    )
    return stmt


def csv2excel(csv_path: str | Path, excel_path: str | Path, sort_by: IndexLabel | None = None) -> None:
    df = pd.read_csv(csv_path)
    if sort_by is not None:
        df.sort_values(by=sort_by, inplace=True)
    df.to_excel(excel_path, index=False)


def add_suffix_to_duplicates(columns):
    count = defaultdict(int)
    new_columns = {}
    for old_col, col in columns.items():
        value = f"{col}_{count[col]}" if count[col] else col
        new_columns[old_col] = str(value)
        count[col] += 1
    return new_columns


def table_from_file(path: str | Path, skip_rows: int | None = None) -> pd.DataFrame:
    # TODO: 全部换成 polars 的读取方式
    return pd.read_csv(path, skiprows=skip_rows) if match_ext(path, ".csv") else pd.read_excel(path, skiprows=skip_rows)


def pl_table_from_file(path: str | Path, skip_rows: int | None = None) -> pl.DataFrame:
    now = datetime.now()
    if match_ext(path, ".csv"):
        df = pl.read_csv(path, skip_rows=skip_rows)
    elif match_ext(path, ".xlsx", ".xls"):
        df = pl.read_excel(path, engine="calamine")
        if skip_rows:
            new_columns = df.head(skip_rows).to_dicts().pop()
            df = df.rename(add_suffix_to_duplicates(new_columns))
            df = df.slice(skip_rows)
    else:
        raise ValueError(f"Unsupported file format: {path}")
    logger.info(f"Read {path} in {datetime.now() - now}")
    return df


def fix_preset_answers(mids: Sequence[int], report_years: Sequence[int]) -> None:
    """将问题答案中的空内容且被判定为NS的字段枚举值替换为`No Disclosure`"""
    assert special_mold.v1_id not in mids, ValueError("Jura1 schema is not supported")
    stmt = (
        NewQuestion.select(
            NewQuestion.id,
            NewQuestion.fid,
            NewQuestion.mold,
            NewQuestion.preset_answer,
            HKEXFileMeta.stock_code,
            HKEXFileMeta.report_year,
        )
        .join(HKEXFileMeta, on=(NewQuestion.fid == HKEXFileMeta.fid))
        .where(
            NewQuestion.mold.in_(mids),
            HKEXFileMeta.report_year.cast("int").in_(report_years),
            HKEXFileMeta.deleted_utc == 0,
        )
        .order_by(NewQuestion.id.desc())
    )

    with pw_db.allow_sync():
        for row in ServerSide(stmt.namedtuples()):
            answer = row.preset_answer
            if not answer or not answer.get("userAnswer") or not answer["userAnswer"].get("items"):
                logger.warning(f"No valid answer found: {row.fid=}, {row.mold=}, {row.stock_code=}, {row.report_year=}")
                continue

            for item in answer["userAnswer"]["items"]:
                if not item.get("data") or not AnswerItem.text_only(AnswerItem(**item)).strip():
                    logger.info(f"Empty content found in question.{row.id} -> {item['key']}")
                    if not item.get("value") or item["value"].lower() == "positive statement":
                        item["value"] = "No Disclosure"
            NewQuestion.update(preset_answer=answer).where(NewQuestion.id == row.id).execute()


def export_question_items(mids, prefixies: list[str], fids=None, from_file="", report_years=None, out_path=None):
    """按照给定前缀导出问题答案"""
    mids = {int(m) for m in mids}
    assert mids, '"mids" is required'
    cond = NewQuestion.mold.in_(mids) & (NewQuestion.answer.is_null(False))
    fids = {int(fid) for fid in fids if fid.isdigit() and int(fid)}
    if from_file:
        fids.update(read_ids_from_file(from_file))
    if fids:
        cond &= NewQuestion.fid.in_(fids)

    stmt = NewQuestion.select(NewQuestion.id, NewQuestion.preset_answer)
    if report_years := {r for r in report_years if r.isdigit() and int(r)}:
        stmt = stmt.join(HKEXFileMeta, on=(NewQuestion.fid == HKEXFileMeta.fid))
        cond &= HKEXFileMeta.report_year.in_(report_years)
    stmt = stmt.where(cond).order_by(NewQuestion.id.desc())
    out_path = out_path or f"{project_root}/data/tmp/question_{datetime.now().strftime('%Y%m%d%H%M')}.zip"
    count = 0
    with ZipFilePlus(out_path, mode="w") as zip_fp:
        with pw_db.allow_sync():
            for question in ServerSide(stmt):
                logger.info(f"export question: {question.id}")
                answer = defaultdict(list)
                for item in question.preset_answer["userAnswer"]["items"]:
                    aid = attribute_id(item["key"])
                    if any(aid.startswith(prefix) for prefix in prefixies):
                        answer[aid].append(item)
                if not answer:
                    continue
                zip_fp.writestr(
                    f"question_{question.id}.json",
                    json.dumps(answer, ensure_ascii=False, indent=4),
                    compress_type=zipfile.ZIP_DEFLATED,
                )
                count += 1
    logger.info(f"exported {count} questions to {out_path}")


async def replace_question_items(path):
    """导入问题答案"""
    assert zipfile.is_zipfile(path)
    count = 0
    with ZipFilePlus(path, mode="r") as zip_fp:
        for name in zip_fp.namelist():
            table, pk = os.path.splitext(name)[0].rsplit("_", maxsplit=1)
            question = await NewQuestion.get_by_id(int(pk), fields=("preset_answer", "id"))
            if not question or not question.preset_answer:
                logger.warning(f"Not a valid question: {pk}")
                continue
            data = msgspec.json.decode(zip_fp.read(name))
            items = []
            for item in question.preset_answer.get("userAnswer", {}).get("items", []):
                aid = attribute_id(item["key"])
                if aid not in data:
                    items.append(item)
                elif data[aid]:
                    items.extend(data[aid])
                    data[aid] = None
            if not items and not data:
                logger.warning(f"No need to replace: {pk}")
                continue
            question.preset_answer["userAnswer"]["items"] = items
            await pw_db.update(question, only=["preset_answer"])
            logger.info(f"Replaced answer for question: {pk}")
            count += 1
    logger.info(f"Successfully import {table}({count}) from {path}")


def diff_rule_counts(l_diff: dict[str, set[str]], r_diff: dict[str, set[str]], suffix: str) -> pd.DataFrame:
    """比较两组规则计数的差异
    l_diff:
    {
        "Rule1": {"code1", "code2"},
        "Rule2": {"code3", "code4"},
        ...
    }
    r_diff:
    {
        "Rule1": {},
        "Rule2": {"code3", "code5"},
        ...
    }
    suffix: "AI|Manual_{enum}"

    找出满足以下条件的 stock codes：

    1. count 不同
    2. 只在一侧出现

    | Rule_{suffix} | Before count "{suffix}" | After count "{suffix}" | Reason "{suffix}" | Diff codes "{suffix}" |
    | ------------- | ----------------------- | ---------------------- | ----------------- | --------------------- |
    | Rule1         | 2                       | 0                      | MISS              | code1, code2          |
    | Rule2         | 2                       | 2                      | DIFF              | code4, code5          |

    """
    all_rules = set(l_diff) | set(r_diff)

    diff_codes = []
    for rule in all_rules:
        l_rules = l_diff.get(rule, set())
        r_rules = r_diff.get(rule, set())
        diff = l_rules ^ r_rules
        if not diff:
            continue
        l_counts = len(l_rules)
        r_counts = len(r_rules)
        if l_rules != r_rules:
            diff_codes.append(
                {
                    f"Rule_{suffix}": rule,
                    f'Before count "{suffix}"': l_counts,
                    f'After count "{suffix}"': r_counts,
                    f'Reason "{suffix}"': "DIFF" if l_counts and r_counts else "MISS",
                    f'Diff codes "{suffix}"': ", ".join(sorted(diff, key=int)),
                }
            )
    if not diff_codes:
        diff_codes.append(
            {
                f"Rule_{suffix}": "No diff",
                f'Before count "{suffix}"': 0,
                f'After count "{suffix}"': 0,
                f'Reason "{suffix}"': "No diff",
                f'Diff codes "{suffix}"': "",
            }
        )
    return pd.DataFrame(diff_codes)


def sort_df_by_another(df_a: pd.DataFrame, df_b: pd.DataFrame, sort_column: str) -> pd.DataFrame:
    """
    根据 df_a 的指定列的顺序对 df_b 进行排序

    :param df_a: 参考 DataFrame
    :param df_b: 需要排序的 DataFrame
    :param sort_column: 用于排序的列名
    :return: 排序后的 df_b
    """
    # 获取 df_a 中指定列的唯一值，保持原顺序
    order = df_a[sort_column].unique()

    # 创建一个分类类型，指定排序顺序
    cat_type = pd.CategoricalDtype(categories=order, ordered=True)

    # 将 df_b 中的排序列转换为分类类型
    df_b[sort_column] = df_b[sort_column].astype(cat_type)

    # 根据分类列排序
    sorted_df_b = df_b.sort_values(sort_column)

    # 将排序列转回原来的数据类型
    original_dtype = df_b[sort_column].dtype
    sorted_df_b[sort_column] = sorted_df_b[sort_column].astype(original_dtype)

    return sorted_df_b


def calc_summary_base_on_exports(left_in, right_in, out_path):
    """从导出文件中按 Rule 分组，统计前后两次导出数据的合规率、AI-C、AI-NC、Manual-C、Manual-NC差异，汇总到一个 Excel 文件中
    https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/4999#note_556226
    """
    l_df = pl_table_from_file(left_in, skip_rows=1)
    r_df = pl_table_from_file(right_in, skip_rows=1)

    # 比较 AI 各枚举的占比
    l_percent = calc_diff_percentage(l_df)
    r_percent = calc_diff_percentage(r_df)
    all_rules = set(l_percent) | set(r_percent)
    diff_percent = []
    for rule in all_rules:
        l_counts = l_percent.get(rule, defaultdict(float))
        r_counts = r_percent.get(rule, defaultdict(float))
        row = {"Rule": rule}
        for enum in sorted(set(l_counts) | set(r_counts)):
            l_v = l_counts.get(enum, 0)
            r_v = r_counts.get(enum, 0)
            row.update(
                {
                    f'Before AI "{enum}"': f"{l_v:.2%}",
                    f'After AI "{enum}"': f"{r_v:.2%}",
                    f'Diff "{enum}"': f"{r_v - l_v:.2%}",
                }
            )
        diff_percent.append(row)
    alias_rule_df = load_alias_rule_df()
    df = pd.DataFrame(diff_percent)
    df = pd.merge(alias_rule_df, df, left_on="MB Rule Name Alias", right_on="Rule", how="right")
    # df.drop(columns=["Rule"], axis=1, inplace=True)
    logger.info("AI rate diff done")

    # 按rule分组找AI-enums差异codes
    l_diffs = collect_diffs(l_df)
    r_diffs = collect_diffs(r_df)
    for enum in sorted(set(l_diffs) & set(r_diffs)):
        l_diff = l_diffs[enum]
        r_diff = r_diffs[enum]
        suffix = f"AI_{enum}"
        diff = diff_rule_counts(l_diff, r_diff, suffix)
        df = pd.merge(df, diff, left_on="Rule", right_on=f"Rule_{suffix}", how="left")
        df.drop(columns=[f"Rule_{suffix}"], axis=1, inplace=True)
        logger.info(f"{suffix} count diff done")

    # 按rule分组找Manual-enums差异codes
    l_diffs = collect_diffs(l_df, ai=False)
    r_diffs = collect_diffs(r_df, ai=False)
    for enum in sorted(set(l_diffs) & set(r_diffs)):
        l_diff = l_diffs[enum]
        r_diff = r_diffs[enum]
        suffix = f"Manual_{enum}"
        diff = diff_rule_counts(l_diff, r_diff, suffix)
        df = pd.merge(df, diff, left_on="Rule", right_on=f"Rule_{suffix}", how="left")
        df.drop(columns=[f"Rule_{suffix}"], axis=1, inplace=True)

    df = sort_df_by_another(alias_rule_df, df, "MB Rule Name Alias")
    df.to_excel(out_path, index=False)
    logger.info(f"Summary saved to {out_path}")
    _format_xlsx(out_path)
    logger.info("Summary formatted")


def _format_xlsx(file_path):
    """格式化 Excel 文件：
    1. 自适应列宽（最大80）
    2. 第一行添加筛选样式
    3. 超长文本自动换行
    """
    wb = load_workbook(file_path)
    ws = wb.active

    # 获取数据范围
    data_rows = ws.max_row
    data_cols = ws.max_column

    # 添加筛选功能到第一行
    ws.auto_filter.ref = f"A1:{get_column_letter(data_cols)}{data_rows}"

    # 设置所有列的格式
    for col in range(1, data_cols + 1):
        col_letter = get_column_letter(col)

        # 计算最佳列宽
        max_length = 0
        for row in range(1, data_rows + 1):
            cell = ws.cell(row=row, column=col)
            if cell.value:
                # 考虑中文字符的宽度
                cell_length = sum(2 if ord(char) > 127 else 1 for char in str(cell.value))
                max_length = max(max_length, cell_length)

        # 设置列宽（最大80）
        # Excel中列宽单位与字符数不是1:1关系，需要适当调整
        adjusted_width = min(max_length * 1.05 + 1.5, 80)  # 1.2是经验系数，4是边距
        ws.column_dimensions[col_letter].width = adjusted_width

        # 设置单元格格式
        for row in range(2, data_rows + 1):  # 从第2行开始（跳过表头）
            cell = ws.cell(row=row, column=col)
            if cell.value and len(str(cell.value)) > 50:
                cell.alignment = Alignment(wrap_text=True)  # 不自动换行

    # 确保表头文本不换行且居中
    for col in range(1, data_cols + 1):
        header_cell = ws.cell(row=1, column=col)
        header_cell.alignment = Alignment(wrap_text=False, horizontal="left", vertical="center")

    wb.save(file_path)


def truncate_label(label, max_length=10):
    """Truncate label if it exceeds max_length."""
    return label[: max_length // 2] + "..." + label[-max_length // 2 :] if len(label) > max_length else label


def plot_ai_c_diff(summary_path: str | Path, batch="JURA5"):
    """绘制AI-C差异图"""
    summary_path = Path(summary_path) if isinstance(summary_path, str) else summary_path
    # Read data
    df = pd.read_excel(summary_path, dtype=str)
    df = df[df["Batch"] == batch]

    # Define function to handle percentage conversion
    def _p_str_to_float(value):
        if isinstance(value, str):
            return float(value.rstrip("%")) / 100
        elif isinstance(value, (int, float)):
            return value / 100 if value > 1 else value
        else:
            return value

    # Apply conversion function
    df["Before percent_AI_C"] = df[get_col_by_pattern(df.columns, "^Before AI")].apply(_p_str_to_float)
    df["After percent_AI_C"] = df[get_col_by_pattern(df.columns, "^After AI")].apply(_p_str_to_float)
    df["Difference"] = df[get_col_by_pattern(df.columns, "^Diff")].apply(_p_str_to_float)

    df["Percent_Change"] = (df["After percent_AI_C"] - df["Before percent_AI_C"]) / df["Before percent_AI_C"]

    # Create pivot table
    pivot = pd.pivot_table(
        df,
        values=["Before percent_AI_C", "After percent_AI_C", "Difference", "Percent_Change"],
        index=["Tag"],
        aggfunc="mean",
    )
    pivot.index = pivot.index.astype(str)

    # Sort by difference
    pivot_sorted = pivot.sort_values("Difference", ascending=False)
    # 创建一个新的 DataFrame，按 "After percent_AI_C" 排序
    pivot_sorted_after = pivot_sorted.sort_values(by="After percent_AI_C", ascending=False)

    # 过滤掉 0 值和空值
    after_values_filtered = pivot_sorted_after["After percent_AI_C"][
        (pivot_sorted_after["After percent_AI_C"] != 0) & (pivot_sorted_after["After percent_AI_C"].notnull())
    ]

    # 计算 "After percent_AI_C" 的平均值和中位数，忽略 0 值和空值
    mean_after = np.mean(after_values_filtered)
    median_after = np.median(after_values_filtered)

    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(24, 24))

    # First subplot: Comparison of Before and After percent_AI_C (sorted by After)
    x1 = range(len(pivot_sorted_after))
    ax1.bar(x1, pivot_sorted_after["Before percent_AI_C"], label="Before", alpha=0.5)
    ax1.bar(x1, pivot_sorted_after["After percent_AI_C"], label="After", alpha=0.5)
    ax1.set_ylabel("Percent AI C")
    ax1.set_title("Comparison of Before and After percent_AI_C")

    # 添加平均值和中位数的虚线
    ax1.axhline(y=mean_after, color="r", linestyle="--")
    ax1.axhline(y=median_after, color="g", linestyle="--")

    # 添加平均值和中位数的标注
    ax1.text(
        len(x1) - 1,
        mean_after,
        f"Mean: {mean_after:.2f}",
        verticalalignment="bottom",
        horizontalalignment="right",
        color="r",
        fontweight="bold",
    )
    ax1.text(
        len(x1) - 1,
        median_after,
        f"Median: {median_after:.2f}",
        verticalalignment="bottom",
        horizontalalignment="right",
        color="g",
        fontweight="bold",
    )

    ax1.legend()
    ax1.set_xticks(x1)
    ax1.set_xticklabels([truncate_label(label) for label in pivot_sorted_after.index], rotation=45, ha="right")

    # Second subplot: Difference in percent_AI_C (original sorting)
    x2 = range(len(pivot_sorted))
    ax2.bar(x2, pivot_sorted["Difference"])
    ax2.set_ylabel("Difference (After - Before)")
    ax2.set_title("Difference in percent_AI_C")
    ax2.set_xticks(x2)
    ax2.set_xticklabels([truncate_label(label) for label in pivot_sorted.index], rotation=45, ha="right")

    # Adjust layout and save
    plt.tight_layout()
    plt.savefig(summary_path.parent / f"{summary_path.stem}.png", dpi=300, bbox_inches="tight")
    plt.close(fig)


if __name__ == "__main__":
    """
    1. PYTHONPATH=. python3 remarkable/optools/plot.py from-db --report_years='[2023]' --mold_ids='[5,15]' --out_dir=data/tmp/pre_$(date +%Y%m%d%H%M) 2>&1 | tee data/tmp/out_$(date +%Y%m%d%H%M).log
    2. PYTHONPATH=. python3 remarkable/optools/plot.py dump-compliance-values-to-csv --mids='[5,15]' --report_years='[2023]' --out_path=/tmp/out.csv 2>&1 | tee /tmp/out.log
    2. PYTHONPATH=. python3 remarkable/optools/plot.py dump-disclosure-values-to-csv --mids='[18]' --report_years='[2019,2020,2021,2022,2023,2024]' --out_path=data/tmp/out.csv 2>&1 | tee data/tmp/out_$(date +%Y%m%d%H%M).log
    3. PYTHONPATH=. python3 remarkable/optools/plot.py csv2excel --csv_path=/tmp/out.csv --excel_path=/tmp/out.xlsx 2>&1 | tee /tmp/out.log
    4. PYTHONPATH=. python3 remarkable/optools/plot.py fix-preset-answers --mids='[15]' --report_years='[2023]' 2>&1 | tee /tmp/out.log
    5. PYTHONPATH=. python3 remarkable/optools/plot.py calc-summary-base-on-exports --left_in=/tmp/left.xlsx --right_in=/tmp/right.xlsx --out_path=/tmp/out.xlsx 2>&1 | tee /tmp/out.log
    """
    import fire

    fire.Fire(
        {
            "from-excel": histogram_plot,
            "from-db": histogram_plot_from_db,
            "make-compare-summary": make_compare_summary,
            "dump-compliance-values-to-csv": dump_compliance_values_to_csv,
            "dump-disclosure-values-to-csv": dump_disclosure_values_to_csv,
            "csv2excel": csv2excel,
            "fix-preset-answers": fix_preset_answers,
            "export-question-items": export_question_items,
            "replace-question-items": replace_question_items,
            "combine-pics": combine_pics,
            "calc-summary-base-on-exports": calc_summary_base_on_exports,
            "plot-ai-c-diff": plot_ai_c_diff,
        }
    )
