"""
该脚本是由王旭提供的用于修正syllabuses的脚本，详情点链接查看
https://mm.paodingai.com/cheftin/pl/1f9o6cx7qprxpqmdqi5kr1a8eo
"""

import argparse
import hashlib
import io
import json
import logging
import os
from typing import Iterator
from zipfile import ZIP_DEFLATED, ZipFile

from remarkable.common.multiprocess import run_by_batch
from remarkable.common.storage import localstorage
from remarkable.common.util import read_zip_first_file
from remarkable.db import pw_db
from remarkable.models.new_file import NewFile
from remarkable.pdfinsight.reader_util import make_syllabuses_range
from remarkable.plugins.fileapi.worker import create_pdf_cache
from remarkable.security.authtoken import generate_timestamp
from remarkable.services.embedding import create_and_save_embeddings

logger = logging.getLogger(__name__)


def get_all_file(file_id: int = 0, file_path: str = None) -> Iterator[NewFile]:
    query = NewFile.select(NewFile.pdfinsight, NewFile.id, NewFile.pdf)
    with pw_db.allow_sync():
        fids = []
        if file_id:
            fids.append(file_id)
        elif file_path:
            with open(file_path, "r") as f:
                fids.extend(int(i) for i in f.read().split())
        if fids:
            query = query.where(NewFile.id.in_(fids))
        files = query.order_by(NewFile.id).execute()
        for file in files:
            yield file


def create_file(interdoc) -> NewFile:
    with io.BytesIO() as bytes_io:
        with ZipFile(bytes_io, "w", ZIP_DEFLATED) as res_fp:
            res_fp.writestr("origin.json", json.dumps(interdoc))
        body = bytes_io.getvalue()
        md5 = hashlib.md5(body).hexdigest()
        localstorage.write_file(os.path.join(md5[:2], md5[2:]), body)
        return NewFile(pdfinsight=md5)


async def recreate_pdf_insight(file: NewFile):
    from remarkable.devtools.task_parser import dump_interdoc_schema

    """
    调整 interdoc syllabuses range 顺序
    """
    if path := os.path.join(file.pdfinsight[:2], file.pdfinsight[2:]) if file.pdfinsight else None:
        abs_path = localstorage.mount(path)

        logger.info(f"start fix syllabuses range: {file.id}")
        interdoc = json.loads(read_zip_first_file(abs_path))
        make_syllabuses_range(interdoc["syllabuses"], interdoc)

        logger.debug(f"start create new pdf insight: {file.id}")
        new_file = create_file(interdoc)

        logger.debug(f"update file pdf insight: {file.id}")
        file.pdfinsight = new_file.pdfinsight
        file.updated_utc = generate_timestamp()
        await pw_db.update(file, only=["pdfinsight", "updated_utc"])

        logger.debug(f"start create pdf cache: {file.id}")
        await create_pdf_cache(file, force=True)

        localstorage.delete_file(path)
        dump_interdoc_schema(file, True)

        await create_and_save_embeddings(file.id, need_sub_element=True, overwrite=True)


def main(workers, file_id, file_path):
    for _ in run_by_batch(recreate_pdf_insight, tasks=get_all_file(file_id, file_path), workers=workers):
        pass


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("-f", "--fid", help="file id", default=0, type=int)
    parser.add_argument("-ff", "--file", help="file path", default=0, type=str)
    parser.add_argument("-w", "--workers", help="number of processes", default=4, type=int)
    args = parser.parse_args()
    main(args.workers, args.fid, args.file)
