import logging
import os
import shutil
import tempfile

from utensils.hash import md5sum_for_file

from remarkable.common.util import stream_download
from remarkable.db import pw_db
from remarkable.models.hkex_file_meta import HKEXFileMeta
from remarkable.models.new_file import NewFile
from remarkable.plugins.fileapi.common import make_pdfinsight
from remarkable.services.file import SyncBakery, fetch_and_sync

logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO)
logger.setLevel(logging.INFO)


async def pull_file_from_remote(hash_str: str):
    async def pull(ins: SyncBakery):
        if ins.fids:
            logger.info(f"pulling remote file: {ins.fids[0]}, {hash_str=} from remote...")
            await fetch_and_sync(
                ins.fids[0],
                db_only=ins.db_only,
                file_only=ins.file_only,
                dst_mid_list=ins.dst_mid_list,
                disable_tqdm=True,
                load_func=pull,
            )
        await ins.save_files()

    return await fetch_and_sync(
        0,
        query=f"SELECT id AS fid FROM file WHERE hash = '{hash_str}'",
        file_only=True,
        disable_tqdm=True,
        load_func=pull,
    )


async def download_and_move(url):
    with tempfile.TemporaryDirectory() as temp_dir:
        save_path = os.path.join(temp_dir, "temp.pdf")
        await stream_download(url, save_path)
        checksum = md5sum_for_file(save_path)
        new_path = NewFile(pdf=checksum).pdf_path(abs_path=True)
        os.makedirs(os.path.dirname(new_path), exist_ok=True)
        logger.info(f"move {save_path} to {new_path}")
        shutil.copy2(save_path, new_path)


async def find_missing_data():
    from remarkable.worker.tasks import _make_question

    query = (
        NewFile.select(NewFile.id, NewFile.name, NewFile.pdf, NewFile.pdfinsight)
        .join(HKEXFileMeta, on=(NewFile.id == HKEXFileMeta.fid))
        .where(HKEXFileMeta.report_year.cast("int") >= 2019, NewFile.mold_list.contains(28))
        .order_by(NewFile.id.desc())
        .tuples()
    )
    for fid, name, checksum, pdfinsight in await pw_db.execute(query):
        try:
            if not os.path.exists(NewFile(pdf=checksum).pdf_path(abs_path=True)) or not os.path.exists(
                NewFile(pdfinsight=pdfinsight).pdfinsight_path(abs_path=True)
            ):
                logger.warning(
                    f"{fid=}, {name=}, not exists and not valid url to download, try to pull data from Jura server"
                )
                await pull_file_from_remote(checksum)
                await _make_question(fid)
                continue

            if not os.path.exists(NewFile(pdfinsight=pdfinsight).pdfinsight_path(abs_path=True)):
                # 有PDF，没PDFinsight，尝试重新生成
                logger.warning(f"{fid=}, {name=}, missing pdfinsight data")
                make_pdfinsight(NewFile(pdf=checksum).pdf_path(abs_path=True), fid)
                logger.info(f"{fid=}, {name=}, regenerated pdfinsight data")
            else:
                logger.info(f"{fid=}, {name=}, all data exists")
        except Exception as e:
            logger.error(f"{fid=}, {name=}, {e}")


if __name__ == "__main__":
    # 补齐缺失数据
    # PYTHONPATH=. python3 remarkable/optools/fill_missing_data.py
    import asyncio

    asyncio.run(find_missing_data())
