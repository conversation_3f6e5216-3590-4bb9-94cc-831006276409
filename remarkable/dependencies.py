import datetime
import http
import logging
from contextvars import ContextVar
from dataclasses import dataclass
from math import ceil
from typing import Iterable, List, Tuple, Type
from urllib.parse import parse_qsl

import jwt
import redis
from fastapi import Body, Depends, HTTPException, Path, Query
from fastapi.security import H<PERSON><PERSON>uthorizationCredentials, HTT<PERSON><PERSON>earer
from fastapi_permissions import Authenticated, Everyone, configure_permissions
from peewee import ManyToManyField, Ordering, Select
from pydantic import Field
from starlette.requests import Request
from starlette.responses import Response
from starlette.status import HTTP_429_TOO_MANY_REQUESTS
from typing_extensions import Annotated

from remarkable import config
from remarkable.common.constants import (
    API_PREFIX_V2,
    CRUD,
    INVALID_REPORT_YEAR_MSG,
    INVALID_STOCK_CODE_MSG,
    PageModule,
    UserRole,
    UserStatus,
)
from remarkable.common.util import is_valid_report_year_for_ar, standard_stock
from remarkable.db import pw_db
from remarkable.models import TModel
from remarkable.models.hkex_company import HKEXCompany
from remarkable.models.new_question import NewQuestion
from remarkable.models.rule_reference import RuleReference
from remarkable.models.user import AdminUser
from remarkable.schemas import CommonParam, TRuleType, TSchema, TypeStripStr
from remarkable.session import SessionManager

logger = logging.getLogger(__name__)


def _validate_exp(exp) -> None:
    try:
        exp = int(exp)
    except ValueError:
        raise HTTPException(
            status_code=http.HTTPStatus.BAD_REQUEST, detail="Expiration Time claim (exp) must be an integer."
        ) from None

    if exp <= datetime.datetime.now(tz=datetime.timezone.utc).timestamp():
        raise HTTPException(status_code=http.HTTPStatus.UNAUTHORIZED, detail="Token has expired")


async def get_current_user(
    request: Request,
    _: HTTPAuthorizationCredentials = Depends(HTTPBearer(auto_error=False)),
) -> AdminUser:
    user = None
    if "session" not in request.scope:
        request.scope["session"] = SessionManager(request)

    if (secret_key := config.get_config("app.jwt_secret_key")) and (
        auth_token := request.headers.get("Authorization") or request.query_params.get("bearer_token", "")
    ):
        # JWT 可能存在于请求头或者请求参数中
        if (parts := auth_token.split()) and len(parts) == 2 and parts[0].lower() == "bearer":  # 验证头部信息
            try:
                payload = jwt.decode(parts[-1], secret_key, algorithms=["HS256"])  # 解码JWT
                if exp := payload.get("exp"):
                    _validate_exp(exp)
            except jwt.DecodeError:
                raise HTTPException(status_code=http.HTTPStatus.UNAUTHORIZED, detail="Invalid token") from None
            except jwt.ExpiredSignatureError as e:
                raise HTTPException(status_code=http.HTTPStatus.UNAUTHORIZED, detail=str(e)) from None
            except ValueError as e:
                raise HTTPException(status_code=http.HTTPStatus.BAD_REQUEST, detail=str(e)) from None

            if email := payload.get("sub"):
                user = await pw_db.first(AdminUser.select().where(AdminUser.email == email))
                if not user or user.status != UserStatus.ACTIVE:
                    raise HTTPException(status_code=http.HTTPStatus.UNAUTHORIZED, detail="User is not active")
                request.scope["session"]._cache_map["uid"] = user.id  # noqa

        if user is None:
            raise HTTPException(status_code=http.HTTPStatus.BAD_REQUEST, detail="Invalid token")

    if simple_token := request.headers.get("access-token"):
        if simple_token == config.get_config("app.simple_token"):
            request.scope["session"]._cache_map["uid"] = 1  # noqa
            logger.warning("Using simple token will be deprecated in the future, please use JWT instead.")
            user = await AdminUser.get_by_id(1)

        if user is None:
            raise HTTPException(status_code=http.HTTPStatus.UNAUTHORIZED, detail="Invalid token")

    if request.session.session_map:  # noqa
        user = await AdminUser.get_by_id(int(request.session.user_id))  # noqa

    if user is None:
        raise HTTPException(status_code=http.HTTPStatus.UNAUTHORIZED, detail="Not authenticated")

    abbr_in_path = request.url.path.replace(f"{API_PREFIX_V2}", "").lstrip("/").split("/", maxsplit=1)[0]
    if abbr_in_path in PageModule.abbreviations() and abbr_in_path not in user.accessible_module_abbrs:
        raise HTTPException(
            status_code=http.HTTPStatus.FORBIDDEN,
            detail=f"You have no access to this page module: {PageModule.abbr_to_enum(abbr_in_path).label}",
        )

    if (query_dict := dict(parse_qsl(request.url.query))) and (
        abbr := query_dict.get("doc_type") or query_dict.get("rule_type")
    ):
        if abbr not in user.accessible_module_abbrs:
            raise HTTPException(
                status_code=http.HTTPStatus.FORBIDDEN,
                detail=f"You have no access to this page module: {PageModule.abbr_to_enum(abbr).label}",
            )

    return user


async def get_active_principals(user: AdminUser = Depends(get_current_user)) -> List[str]:
    principals = [Everyone]
    if user:
        principals.append(Authenticated)
        principals.extend(getattr(user, "principals", []))
        # TODO: 根据旧的权限分组，添加更细致的权限控制
        return principals
    return principals


Permission = configure_permissions(get_active_principals)


def model_with_perm(
    clz: Type[TModel],
    *prefetch: Iterable[Select],
    alias: str | None = None,
    lock=False,
    action: str = CRUD.R,
    schema_clz: Type[TSchema] | None = None,
    fields: Tuple[str, ...] = (),
    include_deleted: bool = False,
):
    async def get_record(model_id: int = Path(..., alias=alias)) -> TModel:
        record = await clz.get_by_id(model_id, *prefetch, lock=lock, fields=fields, include_deleted=include_deleted)
        if record is None:
            raise HTTPException(
                status_code=http.HTTPStatus.NOT_FOUND,
                detail=f"{clz.table_name()}({model_id}) not found, or deleted?",
            )
        return record

    def schema_to_model(schema_ins: schema_clz = Body(..., alias=alias)) -> TModel:
        # TODO: 存在多对多关系的字段时，实例化时触发了不必要的数据库操作，先略过。
        # https://github.com/coleifer/peewee/issues/2765
        db_fields = {i for i in clz._meta.sorted_field_names if not isinstance(getattr(clz, i), ManyToManyField)}
        return clz(**schema_ins.model_dump(exclude={i for i in schema_ins.model_fields_set if i not in db_fields}))

    return Permission(action, get_record if schema_clz is None else schema_to_model)


async def get_business_admin(user: AdminUser = Depends(get_current_user)) -> AdminUser:
    if user.role != UserRole.BUSINESS_ADMIN:
        raise HTTPException(status_code=http.HTTPStatus.FORBIDDEN, detail="Only business admin user can access this")
    return user


async def get_admin_user(user: AdminUser = Depends(get_current_user)) -> AdminUser:
    if user.name != "admin":
        raise HTTPException(status_code=http.HTTPStatus.FORBIDDEN, detail="Only ADMIN user can access this")
    return user


def target_question_with_perm(
    *,
    qid_alias: str = "qid",
    rule_alias: str = "rule",
    rule_regex: str = ".+",
    lock=False,
    action: str = CRUD.R,
    fields: Tuple[str, ...] = (),
) -> TModel:
    """
    根据qid+rule，查询具体规则的真实qid
    """

    async def get_target_question(
        qid: int = Path(..., alias=qid_alias),
        rule: str = Path(..., regex=rule_regex, alias=rule_alias),
    ) -> TModel:
        new_fields = tuple({"id", "fid", "mold"} | set(fields))
        if question := await NewQuestion.get_by_id(qid, lock=lock, fields=new_fields):
            try:
                return await question.get_target_question(rule, fields=new_fields)
            except:  # noqa
                pass
        raise HTTPException(
            status_code=http.HTTPStatus.NOT_FOUND,
            detail=f"{NewQuestion.table_name()}({qid=}, rule={rule}) not found, or deleted?",
        )

    return Permission(action, get_target_question)


def validate_stock_code(*, alias: str = None):
    async def validate(
        stock_code: TypeStripStr = Query(
            ...,
            description="Stock Code, will be padded to 5 digits, e.g. 00001",
            pattern=r"\d{1,5}",
            alias=alias,
            examples=["00001"],
        ),
    ) -> str:
        if await HKEXCompany.is_valid_code(stock_code):
            return standard_stock(stock_code)
        raise HTTPException(status_code=http.HTTPStatus.BAD_REQUEST, detail=INVALID_STOCK_CODE_MSG)

    return Depends(validate)


def validate_optional_year(*, alias: str = None):
    async def validate(
        year: str = Query(
            default=None,
            description="Report Year or Calendar Year, e.g. 2025",
            alias=alias,
            examples=["2024"],
        ),
    ) -> str | None:
        if year and not is_valid_report_year_for_ar(year):
            raise HTTPException(status_code=http.HTTPStatus.BAD_REQUEST, detail=INVALID_REPORT_YEAR_MSG)
        return year

    return Depends(validate)


@dataclass
class StockYear:
    stock_code: str | None = None
    calendar_year: str | None = None

    def __post_init__(self):
        self.stock_code = standard_stock(self.stock_code) if self.stock_code else None


async def validate_stock_code_and_year(
    calendar_year: Annotated[TypeStripStr, validate_optional_year(alias="calendar_year")],
    stock_code: TypeStripStr = Query(
        default=None,
        pattern=r"\d{1,5}",
        description="Stock Code, will be padded to 5 digits, e.g. 00001",
    ),
) -> StockYear:
    # 只对有效值进行校验
    if stock_code and not await HKEXCompany.is_valid_code(stock_code):
        raise HTTPException(status_code=http.HTTPStatus.BAD_REQUEST, detail=INVALID_STOCK_CODE_MSG)
    return StockYear(stock_code=stock_code, calendar_year=calendar_year)


def validate_rule_query(*, doc_type: TRuleType, default_rule: str = None):
    async def get_target(
        rule: str = Query(default=default_rule, description=f"默认是首个{doc_type.upper()}的规则"),
    ) -> RuleReference:
        match doc_type:
            case "cg":
                ref = await RuleReference.find_cg_rule_refer_map()
            case "agm":
                ref = await RuleReference.find_agm_rule_refer_map()
            case "poll":
                ref = await RuleReference.find_poll_rule_refer_map()
            case _:
                raise NotImplementedError(f"Unknown doc_type: {doc_type}")

        if rule is None:
            return list(ref.values())[0]
        if rule_ref := ref.get(rule):
            return rule_ref
        raise HTTPException(
            status_code=http.HTTPStatus.BAD_REQUEST, detail=f"Not a valid {doc_type.upper()} Report rule"
        )

    return Permission(CRUD.R, get_target)


def validate_date(*, alias: str | None = None, fmt: str = "%b %Y") -> datetime.datetime:
    sample_str = datetime.datetime.now().strftime(fmt)

    def validate(date_str: str = Query(alias=alias, description=f"日期格式`{fmt}`，如：{sample_str}")):
        try:
            return datetime.datetime.strptime(date_str, fmt)
        except ValueError:
            raise HTTPException(
                status_code=http.HTTPStatus.BAD_REQUEST,
                detail=f"Invalid date format, should be like: {sample_str}",
            ) from None

    return Depends(validate)


def order_by_query(clz: Type[TModel], *, default: str = "-id") -> list[Ordering]:
    def prepare_ordering(
        order_by: TypeStripStr = Query(
            default=default,
            regex=r"[-\w,]+",
            description=f"排序字段，多个字段用逗号分隔，前面加`-`表示降序，不加表示按`{default.lstrip('-')}`升序",
        ),
    ):
        ordering = []
        for field in (f.strip() for f in order_by.split(",")):
            if field.lstrip("-") not in clz._meta.fields:
                raise HTTPException(
                    status_code=http.HTTPStatus.BAD_REQUEST,
                    detail=f"{clz.table_name()} has no field {field.lstrip('-')}",
                )
            col = clz._meta.fields[field.lstrip("-")]  # noqa
            if field.startswith("-"):
                ordering.append(col.desc())
            else:
                ordering.append(col)
        return ordering

    return Depends(prepare_ordering)


async def pw_transaction() -> None:
    async with pw_db.atomic():
        yield


CommonDep = Annotated[CommonParam, Depends(CommonParam)]


class PAIRateLimiter:
    lua_sha = ContextVar("lua_sha", default=None)
    lua_script = """local key = KEYS[1]
local limit = tonumber(ARGV[1])
local expire_time = ARGV[2]

local current = tonumber(redis.call('get', key) or "0")
if current > 0 then
 if current + 1 > limit then
 return redis.call("PTTL",key)
 else
        redis.call("INCR", key)
 return 0
 end
else
    redis.call("SET", key, 1,"px",expire_time)
 return 0
end"""

    def __init__(
        self,
        times: Annotated[int, Field(ge=0)] = 1,
        milliseconds: Annotated[int, Field(ge=-1)] = 0,
        seconds: Annotated[int, Field(ge=-1)] = 0,
        minutes: Annotated[int, Field(ge=-1)] = 0,
        hours: Annotated[int, Field(ge=-1)] = 0,
    ):
        self.times = times
        self.milliseconds = milliseconds + 1000 * seconds + 60000 * minutes + 3600000 * hours

    async def _check(self, rdb, key):
        if not self.lua_sha.get():
            self.lua_sha.set(await rdb.script_load(self.lua_script))
        return await rdb.evalsha(self.lua_sha.get(), 1, key, str(self.times), str(self.milliseconds))

    @staticmethod
    def _ip_path_key(request: Request):
        forwarded = request.headers.get("X-Forwarded-For")
        if forwarded:
            ip = forwarded.split(",")[0]
        else:
            ip = request.client.host
        return ip + ":" + request.scope["path"]

    async def __call__(self, request: Request, response: Response):
        route_index = 0
        dep_index = 0
        for i, route in enumerate(request.app.routes):
            if route.path == request.scope["path"] and request.method in route.methods:
                route_index = i
                for j, dependency in enumerate(route.dependencies):
                    if self is dependency.dependency:
                        dep_index = j
                        break

        rate_key = self._ip_path_key(request)
        key = f"rate_limit:{rate_key}:{route_index}:{dep_index}"
        try:
            p_expire = await self._check(request.app.state.aio_redis, key)
        except redis.exceptions.ConnectionError:
            p_expire = 0
        except redis.exceptions.NoScriptError:
            self.lua_sha = await request.app.state.aio_redis.script_load(self.lua_script)
            p_expire = await self._check(request.app.state.aio_redis, key)
        if p_expire != 0:
            expire = ceil(p_expire / 1000)
            raise HTTPException(HTTP_429_TOO_MANY_REQUESTS, "Too Many Requests", headers={"Retry-After": str(expire)})
