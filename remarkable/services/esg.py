import calendar
import collections
import logging
import re
from collections import defaultdict
from dataclasses import asdict, dataclass, field
from datetime import datetime
from itertools import groupby
from typing import Dict, List, Tuple, Union

import msgspec
from pdfparser.pdftools.pdf_doc import PDFDoc
from peewee import Case, fn

from remarkable.common.common import Schema
from remarkable.common.constants import SECONDARY_LISTING_CODES, AnswerValueEnum, DocType, PolicyEsgRules
from remarkable.common.hkex_util import parse_qr_year_end
from remarkable.common.pattern import PatternCollection
from remarkable.common.util import (
    clean_txt,
    gen_juraurl,
    get_pub_date_from_string,
    mm_notify,
    read_zip_first_file,
)
from remarkable.config import get_config
from remarkable.db import pw_db
from remarkable.models import FakeUser
from remarkable.models.esg_result import ESGResult
from remarkable.models.file_esg_xref import FileESGxREF
from remarkable.models.hkex_company import DelistedCompany, HKEXCompany
from remarkable.models.hkex_file import HKEXFile
from remarkable.models.hkex_file_meta import DelistedFileMeta, HKEXFileMeta
from remarkable.models.new_file import NewFile
from remarkable.models.new_mold import NewMold
from remarkable.models.new_question import NewQuestion
from remarkable.models.rule_reference import RuleReference
from remarkable.services.file_meta import get_ar_year_end

logger = logging.getLogger(__name__)
P_ESG_TITLE = PatternCollection(
    [
        r"environment.*?social\s*?(and|&)\s*?governance?.*?(report|review)",
        r"Corporate\s*?Social\s*?Responsibility.*?(Report|Review)",
        r"\b(ESG|CSR|Climate|TCFD|sustainability|responsibility|sustainable)\b",
    ],
    re.I,
)
P_CONTENT_PAGE = PatternCollection(
    [
        r"\bContents?",
        r"\bCONTENTS?",
    ]
)


class _Syllabus(msgspec.Struct):
    title: str = ""
    level: int = 0


class _Paragraph(msgspec.Struct):
    text: str = ""
    page: int = 0


class _Cell(msgspec.Struct):
    text: str = ""


class _Table(msgspec.Struct):
    page: int = 0
    cells: Dict[str, _Cell] = []


class _InterDoc(msgspec.Struct):
    MAX_PAGE_INDEX = 5  # 目录页的页码一般在5页以内

    syllabuses: List[_Syllabus] = []
    paragraphs: List[_Paragraph] = []
    tables: List[_Table] = []

    @property
    def possible_content_pages(self) -> List[str]:
        pages = defaultdict(str)
        for para in self.paragraphs:
            if para.page < self.MAX_PAGE_INDEX:
                pages[para.page] += f"{para.text}\n"
        # 可能误识别为表格的目录页，如：08353,2021
        for table in self.tables:
            if table.page < self.MAX_PAGE_INDEX:
                for cell in table.cells.values():
                    pages[table.page] += f"{cell.text}\n"
        return list(pages.values())

    @classmethod
    def from_doc(cls, doc: PDFDoc) -> "_InterDoc":
        inter_doc = cls()
        for syl in doc.outlines:
            if syl["level"] < 2:
                inter_doc.syllabuses.append(_Syllabus(syl["title"], syl["level"]))
        for idx, page in doc.pages.items():
            if idx < cls.MAX_PAGE_INDEX:
                inter_doc.paragraphs.append(_Paragraph(" ".join(t["text"] for t in page["texts"]), idx))
        return inter_doc

    @property
    def esg_matched(self) -> bool:
        # 1. 优先从一级标题中快速判断
        if P_ESG_TITLE.nexts(clean_txt(" ".join(s.title for s in self.syllabuses if s.level < 2))):
            return True
        # 2. 从可能的目录页中判断
        for text in self.possible_content_pages:
            text = clean_txt(text)
            if P_CONTENT_PAGE.nexts(text) and P_ESG_TITLE.nexts(text):
                return True
        return False


def has_esg_info(headline: str, interdoc_path: str | None = None, pdf_path: str | None = None) -> bool:
    # https://mm.paodingai.com/cheftin/pl/t7jm9tdz8tnru8fpnsqye7tfcc
    if "Environmental, Social and Governance Information/Report" in headline:
        return True
    assert interdoc_path or pdf_path, '"interdoc_path" or "pdf_path" must be provided.'
    # 1. 优先从interdoc一级标题中快速判断（比从PDF中解析快10~50倍）
    if interdoc_path and read_zip_first_file(interdoc_path, msgspec_type=_InterDoc).esg_matched:
        return True
    # 2. 从PDF中判断
    return _InterDoc.from_doc(PDFDoc(pdf_path)).esg_matched


def get_last_day(year: int, month: int) -> str:
    last_day = calendar.monthrange(year, month)[1]
    dt = datetime(year, month, last_day)
    return dt.strftime("%d %b %Y")


async def parse_esg_meta(file: Union[HKEXFile, HKEXFileMeta]) -> HKEXFileMeta | None:
    # 解析ESG文件中的元数据
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/723#note_196212
    if isinstance(file, HKEXFileMeta):
        return file

    # 1. FY: 优先从用户输入取，再从文件名中获取
    report_year: int | None = (file.meta or {}).get("financial_year")
    # 2. 尝试用年报解析year_end的逻辑处理
    this_file = await NewFile.get_by_id(file.fid)
    year_end = get_ar_year_end(this_file, report_year=report_year)
    # 3. 最后从文件名/url中取，作为兜底
    if not year_end:
        if year_end := parse_qr_year_end(file.name):
            year_end = year_end.strftime("%d %b %Y")
        else:
            # 暂时从url中获取
            # 如'https://www1.hkexnews.hk/listedco/listconews/gem/2022/0531/2022053101941.pdf'
            logger.warning(f"{file.to_dict()} has no year_end info, use url to get year_end")
            if pub_date := get_pub_date_from_string(file.url):
                year_end = pub_date.strftime("%d %b %Y")
    if not year_end:
        logger.warning(
            f"{file.fid}: no valid year_end info found {file.stock_code} {report_year}, try to use year-end info from 'hkex_companies_info' table"
        )
        if report_year and (
            year_end_month := await pw_db.scalar(
                HKEXCompany.select(HKEXCompany.year_end).where(HKEXCompany.stock_code == file.stock_code)
            )
        ):
            assert year_end_month.isdigit(), f"Invalid year_end_month: {year_end_month}"
            year_end = get_last_day(report_year, int(year_end_month))

    if not year_end:
        return logger.error(f"{file.fid}: no valid year_end info found {file.stock_code} {report_year}")

    year_end = f"{year_end.rsplit(maxsplit=1)[0]} {report_year}" if report_year else year_end

    qid = await pw_db.scalar(
        NewQuestion.select(NewQuestion.id).where(NewQuestion.fid == file.fid).order_by(NewQuestion.mold).limit(1)
    )

    meta = {
        "fid": file.fid,
        "qid": qid,
        "stock_code": file.stock_code,
        "report_year": str(report_year) if report_year else year_end.split()[-1],
        "name": file.company,
        "published": datetime.fromtimestamp(file.release_time).strftime("%Y-%m-%dT%H:%M:%S"),
        "year_end": year_end,
        "doc_type": DocType.ESG,
    }
    return await HKEXFileMeta.insert_or_update(**meta)


async def update_activated_state(fid: int):
    # 确定是否展示该文件（可能是年报，也可能是ESG报告）
    # 是否展示由activated字段控制
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/723#note_198375
    doc_type = None
    logger.info(f"Deal with ESG metadata for file: {fid}")
    mixing_hkex_file: HKEXFile = await pw_db.first(HKEXFile.select().where(HKEXFile.fid == fid))
    headline = ""
    if mixing_hkex_file:
        doc_type = mixing_hkex_file.type
        headline = mixing_hkex_file.headline
    else:
        # 历史年报可能没有这部分数据，换个地方再查
        logger.warning(f'No metadata found for file: {fid} in table "hkex_file"')
        mixing_hkex_file: HKEXFileMeta = await pw_db.first(HKEXFileMeta.select().where(HKEXFileMeta.fid == fid))
        if mixing_hkex_file:
            doc_type = "ar" if mixing_hkex_file.doc_type == DocType.AR else "ESG"
        else:
            logger.error(f"File: {fid} not found")

    if not doc_type:
        logger.error(f"No valid doc type found for file: {fid}")
        return None

    if doc_type == DocType.ESG.name:
        # NOTE: parse_esg_meta方法的参数 mixing_hkex_file 的类型是 HKEXFileMeta｜HKEXFile
        esg_meta = await parse_esg_meta(mixing_hkex_file)

    elif doc_type == DocType.AR.name.lower():
        file_ins: NewFile = await NewFile.find_by_id(fid)
        if has_esg_info(headline, file_ins.pdfinsight_path(abs_path=True), file_ins.path(abs_path=True)):
            esg_meta = await pw_db.first(HKEXFileMeta.select().where(HKEXFileMeta.fid == fid))
            if mixing_hkex_file.stock_code in SECONDARY_LISTING_CODES:
                mm_notify(
                    f"An ESG chapter was found in the Annual Report for secondary listing code {mixing_hkex_file.stock_code} , fid {file_ins.id}, Please verify.",
                    error=True,
                    tags=("secondary_listing",),
                )
        else:
            logger.warning(f"No ESG syllabus found in this annual report: {fid}, {file_ins.name}")
            return await FileESGxREF.deactivate(fid)
    else:
        logger.warning(f'Not a valid doc type: "{doc_type}", {fid}, will delete it')
        return await pw_db.execute(FileESGxREF.delete().where(FileESGxREF.fid == fid))

    if not esg_meta:
        return None

    previous_metas: List[HKEXFileMeta] = []
    for item in await pw_db.execute(
        HKEXFileMeta.select()
        .filter(
            HKEXFileMeta.stock_code == esg_meta.stock_code,
            HKEXFileMeta.report_year == esg_meta.report_year,
            HKEXFileMeta.doc_type.in_([DocType.ESG, DocType.AR]),
            FileESGxREF.activated,
        )
        .join(FileESGxREF, on=(FileESGxREF.fid == HKEXFileMeta.fid))
        .order_by(HKEXFileMeta.published.desc())
    ):
        previous_metas.append(item)
    if not previous_metas:
        await FileESGxREF.activate(esg_meta.fid)
        return None

    latest_row = previous_metas[0]
    previous_fids = [m.fid for m in previous_metas]
    possible_old_fids = [*[m.fid for m in previous_metas[1:]], esg_meta.fid]
    async with pw_db.atomic():
        _ = list(
            await pw_db.execute(
                FileESGxREF.select().where(FileESGxREF.fid.in_([*previous_fids, esg_meta.fid])).for_update()
            )
        )
        if latest_row.doc_type == esg_meta.doc_type:
            # 同类型的文件，只保留、激活最新的
            if latest_row.published > esg_meta.published:
                await FileESGxREF.deactivate(*possible_old_fids)
                return None
            await FileESGxREF.deactivate(*previous_fids)
            await FileESGxREF.activate(esg_meta.fid)
            return None

        if esg_meta.doc_type != DocType.ESG or (
            esg_meta.doc_type == latest_row.doc_type and latest_row.published > esg_meta.published
        ):
            # 非ESG文件，或者ESG文件不是最新的，只保留、激活最新的，即：latest_row
            await FileESGxREF.deactivate(*possible_old_fids)
            return None

        # ESG文件，且是最新的，保留、激活最新的，即：esg_meta
        await FileESGxREF.deactivate(*previous_fids)
        await FileESGxREF.activate(esg_meta.fid)
    return None


async def get_total_counts(delist: bool) -> Dict[str, Dict[str, int]]:
    """返回示例
    {
        "2022": {
            "Total": 9,
            "MB": 8,
            "GEM": 1
        }
    }
    """
    file_meta_class = DelistedFileMeta if delist else HKEXFileMeta
    min_fy, max_fy = await file_meta_class.find_min_max_years(["esg", "ar"])
    cte = (
        file_meta_class.select(
            file_meta_class.fid,
            fn.RANK()
            .over(
                partition_by=[HKEXFileMeta.stock_code, HKEXFileMeta.report_year],
                order_by=[HKEXFileMeta.published.desc(), HKEXFileMeta.fid.desc()],
            )
            .alias("rank"),
        )
        .join(FileESGxREF, on=(file_meta_class.fid == FileESGxREF.fid))
        .where(
            FileESGxREF.activated,
            file_meta_class.report_year.cast("int").between(min_fy, max_fy),
        )
        .cte("tmp_table")
    )
    stmt = (
        file_meta_class.select(
            file_meta_class.report_year,
            fn.COUNT(1).alias("Total"),
            fn.SUM(Case(None, [(~file_meta_class.stock_code.startswith("08"), 1)], 0)).alias("MB"),
            fn.SUM(Case(None, [(file_meta_class.stock_code.startswith("08"), 1)], 0)).alias("GEM"),
        )
        .join(cte, on=(cte.c.fid == file_meta_class.fid))
        .where(cte.c.rank == 1)  # 只取最新的
        .group_by(file_meta_class.report_year)
        .with_cte(cte)
        .dicts()
    )
    ret = {str(y): {"Total": 0, "MB": 0, "GEM": 0} for y in range(min_fy, max_fy + 1)}
    for row in await pw_db.execute(stmt):
        report_year = row.pop("report_year")
        ret[report_year].update(row)
    return ret


async def get_rule_counts(rule: str, delist: bool) -> Dict[str, Dict[str, Dict[str, int]]]:
    """返回示例
    {
        "2022": {
            "Total": {
                "Explain": 1,
                "Comply": 7,
                "No Disclosure": 1,
                "Available": 9
            },
            "MB": {
                "Explain": 1,
                "Comply": 6,
                "No Disclosure": 1,
                "Available": 8
            },
            "GEM": {
                "Explain": 0,
                "Comply": 1,
                "No Disclosure": 0,
                "Available": 1
            }
        }
    }
    """
    file_meta_class = DelistedFileMeta if delist else HKEXFileMeta
    if rule in PolicyEsgRules:
        mid = get_config("esg_molds.Policy-AR.id")
    else:
        mid = get_config("esg_molds.AR.id")
    mold: NewMold = await NewMold.get_by_id(mid)
    assert mold, "No mold found in db"
    enum_values = Schema(mold.data).get_enum_values(rule)
    assert enum_values, f"No enum values for rule: {rule}"
    enum_dict = dict(
        zip(
            enum_values,
            [0 for _ in enum_values],
        )
    )
    min_fy, max_fy = await file_meta_class.find_min_max_years(["esg", "ar"])
    ret = {str(y): [enum_dict.copy(), enum_dict.copy(), enum_dict.copy()] for y in range(min_fy, max_fy + 1)}
    meta_cte = (
        file_meta_class.select(
            file_meta_class.fid,
            fn.RANK()
            .over(
                partition_by=[HKEXFileMeta.stock_code, HKEXFileMeta.report_year],
                order_by=[HKEXFileMeta.published.desc(), HKEXFileMeta.fid.desc()],
            )
            .alias("rank"),
        )
        .join(FileESGxREF, on=(file_meta_class.fid == FileESGxREF.fid))
        .where(
            FileESGxREF.activated,
            file_meta_class.doc_type.in_([DocType.ESG, DocType.AR]),
            file_meta_class.report_year.cast("int").between(min_fy, max_fy),
        )
        .cte("tmp_table")
    )
    cte = (
        ESGResult.select(
            ESGResult.id,
            fn.RANK().over(partition_by=[ESGResult.rule, ESGResult.fid], order_by=[ESGResult.uid.desc()]).alias("rank"),
        )
        .join(meta_cte, on=(ESGResult.fid == meta_cte.c.fid))
        .where(
            ESGResult.uid >= FakeUser.AI,
            ESGResult.rule == rule,
            ~((ESGResult.uid > FakeUser.AI) & (ESGResult.enum_value == "")),
            meta_cte.c.rank == 1,
        )
        .cte("tmp_ans")
    )
    selects = [file_meta_class.report_year, ESGResult.enum_value, fn.COUNT(ESGResult.enum_value)]
    cond = cte.c.rank == 1
    group_by = (file_meta_class.report_year, ESGResult.enum_value)
    query = (
        ESGResult.select(
            *selects,
            fn.SUM(Case(None, [(~file_meta_class.stock_code.startswith("08"), 1)], 0)).alias("MB"),
            fn.SUM(Case(None, [(file_meta_class.stock_code.startswith("08"), 1)], 0)).alias("GEM"),
        )
        .join(file_meta_class, on=(ESGResult.fid == file_meta_class.fid))
        .join(cte, on=(cte.c.id == ESGResult.id))
        .where(cond)
        .group_by(*group_by)
    )
    for year, value, count, mb_count, gem_count in await pw_db.execute(query.with_cte(meta_cte, cte).tuples()):
        if value in enum_values:
            # 有些rule无枚举值，比如：KPI A1.2 part 3 - Scope 3，或者用户未填写枚举值
            # 所以枚举值计数求和不一定等于Available count
            # 同时，存在某些不合法的枚举值，比如：MDR 13 ii - board management approach，但是枚举值不在枚举列表中
            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/1020#note_247307
            if year in ret:
                ret[year][0][value] = count
                ret[year][1][value] = mb_count
                ret[year][2][value] = gem_count
    total_counts = await get_total_counts(delist)
    return {
        k: dict(
            zip(
                ["Total", "MB", "GEM"],
                [{**i, "Available": list(total_counts[k].values())[idx]} for idx, i in enumerate(v)],
            )
        )
        for k, v in ret.items()
    }


@dataclass
class RecordItem:
    report_year: str
    compliance: str | None = None
    guidance_letter: bool = False
    next_ar: bool = False
    supp_annt: bool = False

    def __post_init__(self):
        if not self.compliance:
            self.compliance = "-"


@dataclass
class RuleItem:
    rule: str
    records: Union[Dict[str, RecordItem], List[RecordItem]] = field(default_factory=dict)
    rule_desc: str = ""
    rule_referenced: str = ""
    additional_comments: str = ""  # 仅占位

    def __post_init__(self):
        if not self.rule_referenced:
            self.rule_referenced = self.rule

    def to_dict(self, min_year: int, max_year: int):
        for year in range(min_year, max_year + 1):
            if str(year) not in self.records:
                self.records[str(year)] = RecordItem(str(year))
        self.records = sorted(self.records.values(), key=lambda x: x.report_year, reverse=True)
        return asdict(self)


async def get_issuer_summary(stock_code: str, start_year: int | None = None, end_year: int | None = None):
    year_cond = (HKEXFileMeta.report_year <= end_year) & (HKEXFileMeta.report_year >= start_year)
    cond = (HKEXFileMeta.stock_code == stock_code) & (HKEXCompany.delisted_date.is_null())
    if start_year and end_year:
        cond &= year_cond
    query = (
        HKEXFileMeta.select(
            HKEXFileMeta.stock_code,
            HKEXCompany.company_name_en.alias("company_name"),
            # 2022-03-29T22:48:00 -> 2022-03-29
            fn.TO_DATE(HKEXFileMeta.published, "YYYY MM DD").cast("text").alias("date_of_listing"),
            # 31 Dec 2021 -> 2021-12-31
            fn.TO_DATE(HKEXFileMeta.year_end, "DD Mon YYYY").cast("text").alias("financial_year_end"),
            HKEXFileMeta.report_year.alias("last_ar"),
            include_deleted=True,
        )
        .join(HKEXCompany, on=(HKEXFileMeta.stock_code == HKEXCompany.stock_code))
        .join(FileESGxREF, on=((HKEXFileMeta.fid == FileESGxREF.fid) & FileESGxREF.activated))
        .filter(cond)
        .order_by(HKEXFileMeta.report_year.desc(), HKEXFileMeta.published.desc())
        .dicts()
    )
    header = await pw_db.first(query)
    if not header:
        return {}

    cond = (ESGResult.uid >= FakeUser.AI) & (HKEXFileMeta.stock_code == stock_code)
    if start_year and end_year:
        cond &= year_cond
    cond &= ~((ESGResult.uid > FakeUser.AI) & (ESGResult.enum_value == ""))
    cte = (
        ESGResult.select(
            ESGResult.id,
            fn.ROW_NUMBER()
            .over(partition_by=[ESGResult.rule, ESGResult.fid], order_by=[ESGResult.uid.desc()])
            .alias("rank"),
        )
        .join(FileESGxREF, on=((FileESGxREF.fid == ESGResult.fid) & FileESGxREF.activated))
        .join(HKEXFileMeta, on=(ESGResult.fid == HKEXFileMeta.fid), include_deleted=True)
        .where(cond)
        .cte("tmp_ans")
    )

    cond = (HKEXFileMeta.stock_code == stock_code) & (HKEXFileMeta.report_year.is_null(False)) & (cte.c.rank == 1)
    if start_year and end_year:
        cond &= year_cond
    query = (
        HKEXFileMeta.select(
            HKEXFileMeta.report_year.alias("year"),
            (
                fn.SUM(
                    Case(
                        None,
                        [
                            (ESGResult.enum_value.in_(["Comply", "Explain"]), 1),
                        ],
                        0,
                    )
                )
                / fn.COUNT(1).cast("float")
            ).alias("score"),
            include_deleted=True,
        )
        .join(ESGResult, on=(HKEXFileMeta.fid == ESGResult.fid))
        .join(cte, on=(cte.c.id == ESGResult.id))
        .where(cond)
        .group_by(HKEXFileMeta.report_year)
        .order_by(HKEXFileMeta.report_year.desc())
        .with_cte(cte)
        .dicts()
    )
    header["compliance_score"] = list(await pw_db.execute(query))

    # body.items
    rule_items = {}
    for rule, item in (await RuleReference.find_all_esg_rule_refer_map()).items():
        rule_items[rule] = RuleItem(
            item.main_alias,
            rule_desc=item.rule_description,
            rule_referenced=item.gem_rule if stock_code.startswith("08") else item.main_rule,
        )
    query = (
        ESGResult.select(ESGResult.rule, HKEXFileMeta.report_year, ESGResult.enum_value)
        .join(
            HKEXFileMeta,
            on=(ESGResult.fid == HKEXFileMeta.fid),
            include_deleted=True,
        )
        .join(cte, on=(cte.c.id == ESGResult.id))
        .where(
            HKEXFileMeta.stock_code == stock_code,
            cte.c.rank == 1,
            ESGResult.enum_value.is_null(False),
        )
        .with_cte(cte)
        .order_by(HKEXFileMeta.report_year.cast("int").desc())
        .tuples()
    )
    years: Dict[str, List[str]] = defaultdict(list)
    for rule, year, value in await pw_db.execute(query):
        rule_items[rule].records[year] = RecordItem(year, value)
        years[year].append(value)

    # body.statistics
    statistics = []
    for year, values in years.items():
        statistics.append(
            {
                **{
                    k: f"{(len([x for x in values if x == k]) * 100 / len(values)):0.2f}%"
                    for k in ("Comply", "Explain", "No Disclosure", "Query")
                },
                "year": year,
            }
        )
    include_deleted = await pw_db.scalar(DelistedCompany.select(1).where(DelistedCompany.stock_code == stock_code))
    if not include_deleted:
        stock_code = None
    min_year, max_year = await HKEXFileMeta.find_min_max_years(
        ["esg", "ar"], include_deleted=include_deleted, stock_code=stock_code
    )
    return {
        "header": header or {},
        "body": {
            "items": [i.to_dict(min_year, max_year) for i in rule_items.values()],
            "statistics": statistics,
        },
    }


async def check_e_nd_nums(preset_answer, question):
    if not get_config("notification.esg_need_label_switch"):
        return
    tail = get_config("notification.esg_need_label_tail")
    if not tail:
        return
    e_rules = []
    nd_rules = []
    for item in preset_answer["userAnswer"]["items"]:
        if item["value"] == AnswerValueEnum.ND.value:
            nd_rules.append(item["schema"]["data"]["label"].split("-")[0].strip())
        elif item["value"] == AnswerValueEnum.EXPLAIN.value:
            e_rules.append(item["schema"]["data"]["label"].split("-")[0].strip())
    if (len(e_rules) + len(nd_rules)) / len(preset_answer["userAnswer"]["items"]) > 0.15:
        hkex_file_meta = await HKEXFileMeta.find_by_fid(question.fid)
        esg_url = gen_juraurl(question.id, question.fid, is_esg_report=True, mid=question.mold)
        msg = f"""
Too many E or ND in the ESG preset answer

|stock code|{hkex_file_meta.stock_code}|
|-----|------|
|report year|{hkex_file_meta.report_year}|
|num of E|{len(e_rules)}|
|rules of E|{", ".join(e_rules)}|
|num of ND|{len(nd_rules)}|
|rules of ND|{", ".join(nd_rules)}|
|url|{esg_url}|
"""
        mm_notify(msg, error=True, tags=("ESG", "need_label"), tail=tail, channel="esg")


def gen_pre_trial_data(question, hkex_file_meta):
    e_rules = []
    nd_rules = []
    for item in question.preset_answer["userAnswer"]["items"]:
        if item["value"] == AnswerValueEnum.ND.value:
            nd_rules.append(item["schema"]["data"]["label"].split("-")[0].strip())
        elif item["value"] == AnswerValueEnum.EXPLAIN.value:
            e_rules.append(item["schema"]["data"]["label"].split("-")[0].strip())
    if (len(e_rules) + len(nd_rules)) / len(question.preset_answer["userAnswer"]["items"]) > 0.15:
        esg_url = gen_juraurl(question.id, question.fid, is_esg_report=True, mid=question.mold)
        return (
            hkex_file_meta.stock_code,
            hkex_file_meta.report_year,
            len(e_rules),
            ", ".join(e_rules),
            len(nd_rules),
            ", ".join(nd_rules),
            esg_url,
        )
    return None


async def get_esg_file_meta(stock_code: str) -> Tuple[List[HKEXFileMeta], List[DelistedFileMeta]]:
    def prepare_query(klass: Union[DelistedFileMeta, HKEXFileMeta], _min_year: int):
        return (
            klass.select(
                klass.stock_code,
                klass.report_year,
                klass.name,
            )
            .join(FileESGxREF, on=(klass.fid == FileESGxREF.fid))
            .filter(
                klass.stock_code == stock_code,
                klass.report_year >= _min_year,
                FileESGxREF.activated,
            )
            .order_by(klass.report_year.desc(), klass.published.desc())
        )

    min_year, _ = await HKEXFileMeta.find_min_max_years(["esg", "ar"])
    query = prepare_query(HKEXFileMeta, min_year)
    delist_query = prepare_query(DelistedFileMeta, min_year)
    query = query.union_all(delist_query)
    metas = []
    delist_metas = []
    for item in await pw_db.execute(query):
        if isinstance(item, HKEXFileMeta):
            metas.append(item)
        else:
            delist_metas.append(item)
    return metas, delist_metas


async def get_company_names(stock_code, for_cg=False):
    """
    获取在市和退市的公司名
    """
    if for_cg:
        # cg 跟 ar是同步的
        # ar中可能没有 esg 章节  https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6784
        list_file_metas = await HKEXFileMeta.get_file_metas(annual_report=True, stock_code=stock_code)
        delist_file_metas = await DelistedFileMeta.get_file_metas(annual_report=True, stock_code=stock_code)
    else:
        # default for esg
        list_file_metas, delist_file_metas = await get_esg_file_meta(stock_code)
    delist_file_metas_dict = collections.defaultdict(list)
    for file_meta in delist_file_metas:
        delist_file_metas_dict[int(file_meta.report_year)].append(file_meta)
    grouped_delist_file_metas = {}
    for _, file_metas in groupby(enumerate(reversed(delist_file_metas_dict)), lambda x: x[1] - x[0]):
        items = []
        for _, i in file_metas:
            items.extend(delist_file_metas_dict[i])
        if not items:
            continue
        items = items[::-1]
        start_year = items[-1].report_year
        end_year = items[0].report_year
        grouped_delist_file_metas[items[0].name] = {
            "start_year": start_year,
            "end_year": end_year,
            "delist": True,
        }

    grouped_list_file_metas = {}
    if list_file_metas:
        list_start_year = list_file_metas[-1].report_year
        list_end_year = list_file_metas[0].report_year
        grouped_list_file_metas = {
            list_file_metas[0].name: {
                "start_year": list_start_year,
                "end_year": list_end_year,
                "delist": False,
            }
        }

    return grouped_list_file_metas, grouped_delist_file_metas
