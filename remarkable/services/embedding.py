import base64
import logging
import math
import re
from collections import defaultdict
from dataclasses import dataclass
from io import BytesIO
from itertools import chain
from typing import Iterable, List, Union

from pdfparser.imgtools.gen_page_image import get_page_corrective_bitmap
from peewee import OperationalError
from PIL.Image import Image
from pydantic import BaseModel, Field

from remarkable.common.constants import TableType
from remarkable.common.util import clean_txt, mm_notify, outline_to_box, split_paragraph
from remarkable.db import embedding_pw_db
from remarkable.models.embedding import Embedding, SyllabusEmbedding
from remarkable.models.new_file import NewFile
from remarkable.pdfinsight.parser import parse_table
from remarkable.pdfinsight.reader import PdfinsightReader
from remarkable.pdfinsight.reader_table import PdfinsightTable
from remarkable.predictor.eltype import ElementClassifier
from remarkable.services.chatgpt import OpenAIClient

logger = logging.getLogger(__name__)

P_CONTINUOUS_SENTENCES = re.compile(r"(?<=[a-z\d])\.(?=[A-Z])(?!\d)")
P_SPLIT = re.compile(r"(?<=[.!?])\s+")
P_NUMBER_PATTERN = re.compile(r"\d+\.\d+|\d+\.[A-Z]")  # 版本号和引用编号 rule 等


@dataclass
class SubElement:
    element_index: int
    element_sequence: int  # 元素块为表格则为行号，否则为元素在段落中的位置 element_sequence=0时表示整个元素块
    text: str
    syllabus: List[str] = None
    idx: str | None = None  # 文件内唯一约束

    def __post_init__(self):
        self.idx = f"{self.element_index}_{self.element_sequence}"

    def __hash__(self):
        return hash(self.idx)


def cosine_similarity(vec1, vec2):
    # 计算两个向量的点积
    dot_product = sum(a * b for a, b in zip(vec1, vec2))

    # 计算两个向量的欧几里得范数
    norm_vec1 = math.sqrt(sum(a**2 for a in vec1))
    norm_vec2 = math.sqrt(sum(b**2 for b in vec2))

    # 计算余弦相似度
    if norm_vec1 == 0 or norm_vec2 == 0:
        return 0  # 避免除以零的情况
    similarity = dot_product / (norm_vec1 * norm_vec2)

    return similarity


def split_sentence(sentence):
    text = P_CONTINUOUS_SENTENCES.sub(r". ", sentence)
    text = clean_txt(text)

    # 定义不应该被视为句子结束的模式
    exceptions = [
        r"Mr\.",
        r"Ms\.",
        r"Mrs\.",
        r"Dr\.",
        r"Prof\.",
        r"Inc\.",
        r"Ltd\.",
        r"Co\.",  # 添加公司相关缩写
        r"i\.e\.",
        r"e\.g\.",
        r"[A-Z]\.[A-Z]\.",
        r"\d+\.",
    ]

    potential_sentences = P_SPLIT.split(text)

    sentences = []
    current_sentence = ""
    for part in potential_sentences:
        if not part:
            continue
        current_sentence += part
        last_word = current_sentence.strip().split()[-1].rstrip(".")
        if all(c in "IVXLCDM" for c in last_word):  # is_roman
            continue
        if P_NUMBER_PATTERN.match(last_word):
            continue
        if re.search(r"\s\d{4}\.\s*$", current_sentence):
            sentences.append(current_sentence.strip())
            current_sentence = ""
            continue
        if not any(re.search(exc + r"\s*$", current_sentence) for exc in exceptions):
            sentences.append(current_sentence.strip())
            current_sentence = ""

    if current_sentence:  # 添加最后一个句子（如果有的话）
        sentences.append(current_sentence.strip())

    return sentences


def load_elements(
    file: NewFile,
    need_merged_table: bool = False,
    need_sub_element: bool = False,
    element_index: int | None = None,
    only_shape: bool = False,
) -> Iterable[SubElement]:
    pdfinsight_reader = PdfinsightReader.from_path(file.pdfinsight_path(abs_path=True))
    filter_func = None
    if element_index:
        filter_func = lambda x: x["index"] == element_index  # noqa: E731
    if only_shape:
        filter_func = lambda x: ElementClassifier.is_chart(x)  # noqa: E731
    for element in pdfinsight_reader.elements_iter(filter_func):
        if ElementClassifier.is_chart(element):
            box = outline_to_box(element["outline"])
            if box["box_bottom"] - box["box_top"] < 5 or box["box_right"] - box["box_left"] < 5:
                logger.info(f"skip small chart element: {element['index']=}")
                continue
            # logger.info(f"process chart element: {element['index']=}")
            interdoc_page = pdfinsight_reader.data["pages"][str(element["page"])]
            scale = 2.0  # scale 越大图片会更清楚
            pillow_image: Image = get_page_corrective_bitmap(
                file.pdf_path(abs_path=True),
                element["page"],
                interdoc_page,
                scale=scale,
            )
            crop_box = (
                box["box_left"] * scale,
                box["box_top"] * scale,
                box["box_right"] * scale,
                box["box_bottom"] * scale,
            )
            element_image = pillow_image.crop(crop_box)
            img_byte_arr = BytesIO()
            element_image.save(img_byte_arr, format="PNG")
            base64_image = base64.b64encode(img_byte_arr.getvalue()).decode("utf-8")
            image_title = pdfinsight_reader.find_shape_title(element)
            element_text = summary_chart(base64_image, image_title)
            if not element_text:
                continue
            syllabus = pdfinsight_reader.find_syllabuses_by_index(element["index"])
            syllabus = [i["title"] for i in syllabus]
            yield SubElement(element_index=element["index"], element_sequence=0, text=element_text, syllabus=syllabus)
        elif ElementClassifier.like_paragraph(element):
            # 1.跨页或者跨栏连续段落的第二段不做pattern匹配
            ele_index = element["index"]
            if (
                (page_merged := element.get("page_merged_paragraph"))
                and ele_index in page_merged["paragraph_indices"]
                and ele_index != page_merged["paragraph_indices"][0]
            ):
                continue
            element_text = element.get("text", "")
            if not element_text:
                # logger.warning(f"skip empty text for {element['index']=}")
                continue
            syllabus = pdfinsight_reader.find_syllabuses_by_index(ele_index)
            syllabus = [i["title"] for i in syllabus]
            yield SubElement(element_index=ele_index, element_sequence=0, text=element_text, syllabus=syllabus)
            if need_sub_element:
                for idx, sub_para in enumerate(split_paragraph(element_text, need_clean=False), start=1):
                    yield SubElement(element_index=ele_index, element_sequence=idx, text=sub_para, syllabus=syllabus)
        elif ElementClassifier.is_table(element):
            syllabus = pdfinsight_reader.find_syllabuses_by_index(element["index"])
            syllabus = [i["title"] for i in syllabus]
            table_text = PdfinsightTable.to_markdown(element, need_merged_table=need_merged_table)
            yield SubElement(element_index=element["index"], element_sequence=0, text=table_text, syllabus=syllabus)
            if need_sub_element:
                table = parse_table(element, tabletype=TableType.TUPLE, pdfinsight_reader=pdfinsight_reader)
                if table.pdfinsight_table.is_combo_sub_table():
                    continue
                for idx, row in enumerate(table.rows, start=1):
                    row_text = " ".join(cell.text for cell in row)
                    yield SubElement(
                        element_index=element["index"], element_sequence=idx, text=row_text, syllabus=syllabus
                    )
        # else:
        #     logger.warning(f"unspport type: {element['index']=}, {element['class']=}")


async def create_and_save_embeddings(
    file_id, need_sub_element=False, overwrite=False, element_index=None, only_shape=False
):
    if element_index and overwrite:
        overwrite = False
    logger.info(f"create_and_save_embeddings {file_id=}, {element_index=},{need_sub_element=}, {overwrite=}")

    file = await NewFile.find_by_id(file_id)
    if not file:
        logger.warning(f"file {file_id} not found")
        return
    logger.info(f"create embedding for {file_id=} {element_index=}")
    texts = []
    sub_elements = []
    syllabus_map = defaultdict(set)
    for sub_element in load_elements(
        file, need_sub_element=need_sub_element, element_index=element_index, only_shape=only_shape
    ):
        if sub_element.syllabus:
            syllabus_map[tuple(sub_element.syllabus)].add(sub_element.element_index)
        if sub_element.text and len(clean_txt(sub_element.text)) > 1:
            texts.append(sub_element.text)
            sub_elements.append(sub_element)

    if not texts:
        return
    try:
        embedding_res = OpenAIClient().safe_embeddings(texts)
        embedding_by_sub_elements = dict(zip(sub_elements, embedding_res))
    except ValueError:
        mm_notify(f"An error occurred while creating embedding, {file_id=}", error=True)
        return

    syllabus_texts = []
    for syllabuses in syllabus_map:
        syllabus_texts.append("@@".join(syllabuses))

    syllabus_embedding_res = OpenAIClient().safe_embeddings(syllabus_texts)
    syllabus_embedding = dict(zip(syllabus_map, syllabus_embedding_res))

    async with embedding_pw_db.atomic():
        if overwrite:
            await embedding_pw_db.execute(Embedding.delete().where(Embedding.file_id == file.id))
            await embedding_pw_db.execute(SyllabusEmbedding.delete().where(SyllabusEmbedding.file_id == file.id))
            logger.info(f"delete embedding data for {file_id}")

        await insert_embeddings(file_id, embedding_by_sub_elements)
        await insert_syllabus_embeddings(file_id, syllabus_map, syllabus_embedding)
        logger.info(f"create embedding for {file_id=} success")


async def insert_syllabus_embeddings(file_id, syllabus_map, syllabus_embedding):
    syllabus_rows = []
    for syllabus, element_ids in syllabus_map.items():
        syllabus_rows.append(
            {
                "file_id": file_id,
                "syllabus": syllabus,
                "embedding": syllabus_embedding[syllabus],
                "indexes": list(element_ids),
            }
        )
    try:
        await SyllabusEmbedding.bulk_insert(
            syllabus_rows,
            on_conflict={
                "action": "update",
                "preserve": ("embedding", "metadata", "updated_utc"),
                "conflict_target": ("file_id", "syllabus"),
            },
        )
    except OperationalError as e:
        # prod file id 59305 34980 33713  todo 后面删除该索引
        mm_notify(f"{file_id=}Index creation skipped due to error: {e}", error=True)


async def insert_embeddings(file_id, embedding_by_sub_elements):
    rows = []
    for sub_element, embedding in embedding_by_sub_elements.items():
        data = {
            "file_id": file_id,
            "index": sub_element.element_index,
            "element_sequence": sub_element.element_sequence,
            "text": sub_element.text,
            "embedding": embedding,
            "metadata": {},
        }
        rows.append(data)

    await Embedding.bulk_insert(
        rows,
        on_conflict={
            "action": "update",
            "preserve": ("text", "embedding", "metadata", "updated_utc"),
            "conflict_target": ("file_id", "index", "element_sequence"),
        },
    )


async def semantic_search_by_embedding_and_syllabus(
    *,
    file_ids: Union[int, list[int], tuple[int], None],
    mean_embedding: list[float],
    syllabus_mean_embedding: list[float] | None = None,
    syllabus_query: list[str] | None = None,
    neglect_syllabus_query: list[str] | None = None,
    element_sequence: int | None = None,
    limit=20,
):
    index_candidates = []
    if syllabus_mean_embedding is not None:
        syllabus_res = await SyllabusEmbedding.semantic_search(file_ids, syllabus_mean_embedding, limit=20)
        index_candidates = list(chain.from_iterable(item["indexes"] for item in syllabus_res if item["score"] > 0.7))
    if syllabus_query or neglect_syllabus_query:
        syllabus_res = await SyllabusEmbedding.regex_search(
            file_ids, syllabus_query, neglect_regex=neglect_syllabus_query, limit=20
        )
        index_candidates.extend(chain.from_iterable(item["indexes"] for item in syllabus_res))

    semantic_crude_elements = await Embedding.semantic_search_by_embedding(
        file_ids=file_ids,
        embedding=mean_embedding,
        indexes=index_candidates,
        element_sequence=element_sequence,
        limit=limit,
    )
    return semantic_crude_elements


def encode_image(image_path):
    with open(image_path, "rb") as image_file:
        return base64.b64encode(image_file.read()).decode("utf-8")


def summary_chart(base64_image, image_title):
    prompt = """You are an AI assistant specialized in analyzing charts, graphs, tables, and text from PDF documents. Your first task is to classify the image content and then process it accordingly.

Classification Step:
1. Analyze the image and determine if it requires table conversion:
   - If the image contains structured data (e.g., tables, complex charts with multiple data points, or comparative data) that would lose meaning when presented as plain text → Convert to table
   - If the image contains text that maintains its meaning when read sequentially (e.g., paragraphs, simple labels, single statements) → Use OCR text only

Processing Steps:
If OCR TEXT is sufficient:
1. Extract all text in a logical reading order
2. Preserve all numbers, identifiers, and special formatting
3. Return the text as a well-formatted paragraph

If TABLE CONVERSION is needed:
1. Identify the visualization type (pie chart, bar chart, line graph, table, etc.)
2. Extract ALL information:
   - ALL text labels and identifiers (maintaining exact codes)
   - ALL numerical values in their original format
   - ALL categories and hierarchical relationships
   - ALL titles, subtitles, and footnotes
   - ALL units of measurement
   - ANY contextual information from legends or color coding

3. Convert to a markdown table that:
   - Preserves the data structure and hierarchy
   - Maintains ALL original identifiers
   - Includes ALL numerical values exactly as shown
   - Retains the original organization of information

CRITICAL REQUIREMENTS:
- NEVER simplify or omit identifiers/codes
- NEVER round numbers unless shown rounded in the image
- ALWAYS maintain exact formatting
- ALWAYS include ALL visible text
- ALWAYS preserve relationships between data points
"""
    if image_title:
        prompt += f"Here is the image title: {image_title}."

    prompt += "Here is the PDF image you need to analyze:"

    class Summary(BaseModel):
        text: str = Field(description="markdown table")

    messages = [
        {
            "role": "system",
            "content": prompt,
        },
        {
            "role": "user",
            "content": [{"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{base64_image}"}}],
        },
    ]
    try:
        llm_res = OpenAIClient().send_message(messages, response_format=Summary, options={"model": "gpt-4o-mini"})
    except Exception as e:
        logging.exception(e)
        return ""
    response_model: Summary = llm_res.parsed
    return response_model.text


if __name__ == "__main__":
    import asyncio

    # asyncio.run(create_and_save_embeddings(111952, need_sub_element=True, overwrite=True, element_index=None))
    asyncio.run(create_and_save_embeddings(33713, need_sub_element=True, overwrite=True, element_index=None))
