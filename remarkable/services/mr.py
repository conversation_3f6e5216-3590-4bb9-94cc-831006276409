import asyncio
import logging
import re
from dataclasses import dataclass
from datetime import datetime

from remarkable.common.pattern import <PERSON><PERSON><PERSON><PERSON>, PatternCollection
from remarkable.common.util import mm_notify
from remarkable.db import pw_db
from remarkable.models.hkex_file import HKEXFile
from remarkable.models.mold import special_mold
from remarkable.models.mr_meta import MRMeta
from remarkable.models.new_file import NewFile
from remarkable.models.new_question import NewQuestion
from remarkable.optools.find_something import save_to_csv
from remarkable.optools.stat_util import Url
from remarkable.pdfinsight.reader import PdfinsightReader
from remarkable.predictor.common_pattern import R_EN_MONTH


@dataclass
class MREndDate:
    ended_date: str
    hkex_file: HKEXFile


class MREndDateService:
    END_DATE_PATTERN = PatternCollection(
        [
            rf"ended (?P<date>\d{{1,2}}\s*{R_EN_MONTH}\s*20\d{{2}})",
            rf"ended (?P<date>{R_EN_MONTH}\s*\d{{1,2}}[,，\s]*20\d{{2}})",
            r"ended (?P<date>\d{2}/\d{2}/20\d{2})",
        ],
        flags=re.I,
    )
    DATE_PATTERN = PatternCollection(
        [
            rf"\b(?P<date>\d{{1,2}}\s*{R_EN_MONTH}\s*20\d{{2}})",
            rf"\b(?P<date>{R_EN_MONTH}\s*\d{{1,2}}[,，\s]*20\d{{2}})",
            r"ended (?P<date>\d{2}/\d{2}/20\d{2})",
        ],
        flags=re.I,
    )
    P_END_DATE = MatchMulti.compile(r"For the month ended", operator=any)

    @classmethod
    async def find_end_date_from_file(cls, fid):
        ended_date = ""
        hkex_file = await pw_db.first(HKEXFile.select().where(HKEXFile.fid == int(fid)))
        if not hkex_file:
            return None
        if matched := cls.END_DATE_PATTERN.nexts(hkex_file.name):
            date_str = matched.group("date")
            if ended_date := cls.extract_date_from_date_str(date_str):
                return MREndDate(ended_date=ended_date, hkex_file=hkex_file)
            logging.error(f"Invalid date from file name: {date_str}, fid: {fid}")
        if not ended_date:
            file = await NewFile.find_by_id(fid)
            if file.pdfinsight_path():
                reader = PdfinsightReader(file.pdfinsight_path(abs_path=True))
                first_table = sorted(reader.table_dict.values(), key=lambda x: x.index)[0]
                cells = first_table.cells
                for cell_idx, cell in cells.items():
                    cell_row = int(cell_idx.split("_")[0])
                    if cls.P_END_DATE.search(cell["text"]):
                        date_str = ""
                        if matched := cls.DATE_PATTERN.nexts(cell["text"]):
                            date_str = matched.group("date")
                        elif (right_cell := cells.get(f"{cell_row}_{cell['right']}")) and (
                            matched := cls.DATE_PATTERN.nexts(right_cell["text"])
                        ):
                            date_str = matched.group("date")
                        if date_str and (ended_date := cls.extract_date_from_date_str(date_str)):
                            return MREndDate(ended_date=ended_date, hkex_file=hkex_file)
                        logging.error(f"Invalid date from file first table: {date_str}, fid: {fid}")
        logging.error(f"Can't find ended date from file, fid: {fid}")
        return MREndDate(ended_date=ended_date, hkex_file=hkex_file)

    @staticmethod
    def extract_date_from_date_str(date_str):
        input_formats = [
            "%d %b %Y",  # 31 Mar 2024
            "%d %B %Y",  # 31 March 2024
            "%B %d, %Y",  # April 30, 2025
            "%b %d, %Y",  # Apr 30, 2025
            "%d/%m/%Y",  # 30/04/2025
        ]
        output_format = "%Y-%m-%d"
        for fmt in input_formats:
            try:
                return datetime.strptime(date_str, fmt).strftime(output_format)
            except ValueError:
                continue
        return ""


async def set_mr_meta(file_id):
    if (ended_date_info := await MREndDateService.find_end_date_from_file(file_id)) and ended_date_info.ended_date:
        mr_meta = {
            "file_id": file_id,
            "stock_code": ended_date_info.hkex_file.stock_code,
            "ended_date": ended_date_info.ended_date,
        }
        await MRMeta.insert_or_update(conflict_target=[MRMeta.file_id], **mr_meta)
    else:
        mm_notify(f"Can't find ended date from file, fid: {file_id}", error=True)


async def main():
    """
    测试每份文档是否都可以取得ended_date
    """
    results = []
    for hkex_file in await pw_db.execute(
        HKEXFile.select(HKEXFile.fid, HKEXFile.name).where(HKEXFile.type == "MR").dicts()
    ):
        if ended_date_info := await MREndDateService.find_end_date_from_file(hkex_file["fid"]):
            question = await pw_db.first(
                NewQuestion.select(
                    NewQuestion.id,
                    NewFile.tree_id,
                )
                .join(NewFile, on=(NewFile.id == NewQuestion.fid))
                .where((NewQuestion.fid == hkex_file["fid"]) & (NewQuestion.mold == special_mold.v6_1_mr_id))
                .dicts()
            )
            url = ""
            if question:
                url = str(
                    Url(hkex_file["fid"], special_mold.v6_1_mr_id, question["id"], question["tree_id"], schema_key="")
                )
            results.append(
                {
                    "fid": hkex_file["fid"],
                    "ended_date": ended_date_info.ended_date,
                    "title": hkex_file["name"],
                    "url": url,
                }
            )
    save_to_csv(results, "get_ended_date")


if __name__ == "__main__":
    asyncio.run(main())
