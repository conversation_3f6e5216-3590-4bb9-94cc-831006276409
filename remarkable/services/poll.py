import calendar
import logging
from datetime import datetime

from remarkable.common.util import mm_notify, standard_stock
from remarkable.db import pw_db
from remarkable.models.hkex_file import HKEXFile
from remarkable.models.mr_meta import MRMeta
from remarkable.models.poll_meta import POLLMeta

logger = logging.getLogger(__name__)


async def set_poll_meta(poll_file_id: int, stock_code=None):
    if not stock_code:
        stock_code = await pw_db.scalar(
            HKEXFile.select(HKEXFile.stock_code).where(HKEXFile.fid == poll_file_id, HKEXFile.type == "POLL")
        )
        if not stock_code:
            logger.warning(f"set_poll_meta: No stock_code found for {poll_file_id=}")
            mm_notify(f"No stock_code found for {poll_file_id=}", tags=("set_poll_meta",), error=True)
            return
    stock_code = standard_stock(stock_code)
    poll_file = await pw_db.first(HKEXFile.select().where(HKEXFile.fid == poll_file_id))
    agm_fid = await find_agm_file(poll_file, stock_code)
    mr_fid = await find_mr_file(poll_file, stock_code)

    poll_meta = {
        "poll_fid": poll_file_id,
        "agm_fid": agm_fid,
        "mr_fid": mr_fid,
        "stock_code": standard_stock(stock_code),
    }
    await POLLMeta.insert_or_update(conflict_target=[POLLMeta.poll_fid], **poll_meta)


async def find_agm_file(poll_file: HKEXFile, stock_code: str) -> int | None:
    cond = [
        HKEXFile.type == "AGM",
        HKEXFile.stock_code == stock_code,
        HKEXFile.release_time < poll_file.release_time,
    ]
    agm_file = await pw_db.first(HKEXFile.select().where(*cond).order_by(HKEXFile.release_time.desc()).limit(1))
    if not agm_file:
        mm_notify(f"No AGM file found for {poll_file.id=}, {stock_code=}", tags=("find_agm_file",), error=True)
        return None
    if not agm_file.id:
        mm_notify(f"No AGM fid found for {agm_file.id=}, {stock_code=}", tags=("find_agm_file",), error=True)
        return None
    if (datetime.fromtimestamp(poll_file.release_time) - datetime.fromtimestamp(agm_file.release_time)).days > 180:
        mm_notify(
            f"The release time interval between agm and poll exceeds six months for {poll_file.id=}, {agm_file.id=}, {stock_code=}",
            tags=("find_agm_file",),
        )
    return agm_file.fid


async def find_mr_file(poll_file: HKEXFile, stock_code: str) -> int | None:
    poll_date = datetime.fromtimestamp(poll_file.release_time)

    if poll_date.month == 1:
        prev_month = 12
        prev_year = poll_date.year - 1
    else:
        prev_month = poll_date.month - 1
        prev_year = poll_date.year

    last_day = calendar.monthrange(prev_year, prev_month)[1]
    prev_month_last_day = datetime(prev_year, prev_month, last_day)
    formatted_date = prev_month_last_day.strftime("%Y-%m-%d")

    cond = [
        MRMeta.stock_code == stock_code,
        HKEXFile.release_time < poll_file.release_time,
        MRMeta.ended_date == formatted_date,
    ]

    mr_fid = await pw_db.scalar(
        MRMeta.select(MRMeta.file_id)
        .join(HKEXFile, on=(MRMeta.file_id == HKEXFile.fid))
        .where(*cond)
        .order_by(HKEXFile.release_time.desc())
        .limit(1)
    )

    if not mr_fid:
        mm_notify(
            f"No MR file found for {poll_file.fid=}, {stock_code=}, {formatted_date=}",
            tags=("find_mr_file",),
            error=True,
        )
        return None

    return mr_fid


if __name__ == "__main__":
    import asyncio

    asyncio.run(set_poll_meta(166795))
