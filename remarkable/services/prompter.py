import logging

from remarkable.common.common import B1_B7_COLS, JURA6_B_COLS, JURA6_C_COLS
from remarkable.common.constants import FundRaisingRuleEnum
from remarkable.config import get_config
from remarkable.db import embedding_pw_db, pw_db
from remarkable.models.embedding import Embedding
from remarkable.models.hkex_file_meta import HKEXFileMeta
from remarkable.models.mold import special_mold
from remarkable.models.new_file import NewFile
from remarkable.models.new_mold import NewMold
from remarkable.models.new_question import NewQuestion
from remarkable.prompter.impl.v4_embedding import EmbeddingPrompter
from remarkable.prompter.manager import prompter_manager
from remarkable.services.embedding import create_and_save_embeddings
from remarkable.services.question import ctx_only_jura21, ctx_only_prev_fund_raising
from remarkable.services.retrieval.policy_esg import get_category_candidates

logger = logging.getLogger(__name__)

JURA_21_ADD_RULES = [
    "A34-Content",
    "A35-Content",
    "A36-Content",
    "A37-Content",
    "A38-Content",
    "A39-Content",
    "A40-Content",
    "A41-Content",
    "丨H38-41",
    "B63.1-Name of grantee(Description of grantees)",
    "B64-Accounting standard and policy adopted",
    "B64-Fair value of options at the date of grant",
    "B64-Performance targets (if any)",
    "B73-Content",
    "B74-Beginning amount",
    "B74-Date of grant",
    "B74-Ending amount",
    "B74-Exercise period",
    "B74-Purchase price",
    "B74-The number of unvested awards",
    "B74-Vesting period",
    "B75-Accounting standard and policy adopted",
    "B75-Closing price",
    "B75-Date of grant",
    "B75-Exercise period",
    "B75-Fair value of awards at the date of grant",
    "B75-Number of awards",
    "B75-Performance targets(if any)",
    "B75-Purchase price",
    "B75-Vesting period",
    "B76-Closing price",
    "B76-Number of awards vested",
    "B76-Purchase price",
    "B77-Number of awards cancelled",
    "B77-Purchase price",
    "B78-Content",
    "B79-Content",
    "B80-Amount",
    "B80-Period",
    "B81-Content",
    "B82-Content",
    "B83-Content",
    "B84-Beginning amount",
    "B84-Date of grant",
    "B84-Ending amount",
    "B84-Exercise period",
    "B84-Purchase price",
    "B84-The number of unvested awards",
    "B84-Vesting period",
    "B85-Accounting standard and policy adopted",
    "B85-Closing price",
    "B85-Date of grant",
    "B85-Exercise period",
    "B85-Fair value of awards at the date of grant",
    "B85-Number of awards",
    "B85-Performance targets(if any)",
    "B85-Purchase price",
    "B85-Vesting period",
    "B86-Closing price",
    "B86-Number of awards vested",
    "B86-Purchase price",
    "B87-Number of awards cancelled",
    "B87-Purchase price",
    "B88-Content",
    "B89-Content",
    "B90-Amount",
    "B90-Period",
    "B91-Content",
    "B92-Content",
    "B93.1-Scheme mandate at the beginning of year",
    "B93.1-Scheme mandate at the end of the year",
    "B93.1-Service provider sublimit at the beginning of year",
    "B93.1-Service provider sublimit at the end of the year",
    "B93.2-Scheme mandate at the beginning of year",
    "B93.2-Scheme mandate at the end of the year",
    "B93.2-Service provider sublimit at the beginning of year",
    "B93.2-Service provider sublimit at the end of the year",
    "B94.1-Content",
    "B94.2-Content",
    "B95.1-Content",
    "B95.2-Content",
    "B96-Content",
    "B97-Details of any remuneration payable to members of senior management by band",
    "B97-Directors’ remuneration policy",
    "丨H83-92",
    "C5-CCT Confirmed with Auditors",
]

JURA_6_ADD_RULES = [
    "B98-Content",
    "B99-Date of legal advice",
    "B99-Directors' confirmation",
    "C1-Aggregate price",
    "C1-Intended use",
    "C1-Number of treasury share held",
    "C1-Purchase by issuer/subsidiary",
    "C1-The exchangs/arrangement",
    "C1.1.1-Proceeds brought forward",
    "C1.1.1-Used during the year",
    "C1.2.1-Expected timeline",
    "C1.2.1-Unutilized proceeds",
    "C1.3-Whether the proceeds were used, or are proposed to be used, according to the issuer’s intention and the reason for change or delay",
    "C2.1.1-Name",
    "C2.1.1-Principal business",
    "C2.2.1-Number of shares held",
    "C2.2.1-Percentage of shares held",
    "C2.3-The investment costs",
    "C2.4-The fair value of each investment as at the year end",
    "C2.5-The size relative to the issuer’s total assets",
    "C2.6-The performance of each investment during the year",
    "C2.7-A discussion of the issuer’s investment strategy",
    # "C5-CCT Confirmed with Auditors",
    "C6.1-Figures",
    "C6.2-Inducement Payment",
    "C6.3-Compensation for Loss of Office",
    "C7.1-the transaction date",
    "C7.2-the parties to the transaction and a description of their connected relationship",
    "C7.3-a brief description of the transaction and its purpose",
    "C7.4.1-Consideration",
    "C7.4.1-Terms",
    "C7.5-the nature of the connected person's interest in the transaction",
]

B1_B7_CRUDE_COLS = [
    "B1-Issue reason",
    "B2-Class of equity securities",
    "B3-Number of issued",
    "B4-Issue price",
    "B5-Net price",
    "B6-Names of allottes",
    "B7-Market price",
]


def table_element_content_text(ele):
    def cell_ordering(row_and_col):
        row, col = row_and_col.split("_")
        return int(row) * 1000 + int(col)

    return "|".join([v.get("text") for k, v in sorted(ele.get("cells").items(), key=lambda x: cell_ordering(x[0]))])


async def predict_crude_answer(file, mid=None, god_mode=False):
    if not mid:
        raise Exception("not set mid in predict_crude_answer")
    mid = int(mid)
    if not file.pdfinsight:
        raise Exception("pdfinsight not found")
    question = await NewQuestion.find_by_fid_mid(fid=file.id, mold_id=mid)
    mold = await NewMold.find_by_id(mid)
    if not mold:
        logger.error("can't find mold %s", mid)
        return None

    if meta := await HKEXFileMeta.find_by_fid(file.id):
        report_year = meta.report_year
    else:
        logger.warning(f"can't find hkex meta info: {file.id=}")
        report_year = None

    # 从上下文变量中读取only_prev_fund_raising
    if ctx_only_prev_fund_raising.get():
        # TODO 所有2024年年报全部预测完成之后，可删除该分支逻辑
        return await predict_prev_fund_crude_answer(mold, file, question, report_year, god_mode)

    # 从上下文变量中读取only_jura21
    only_jura21 = ctx_only_jura21.get()
    only_b1_b10 = get_config("ONLY_B1_B10", False)  # from env export SCRIBER_CONFIG_ONLY_B1_B10=True
    if only_jura21 or mid == special_mold.v3_id:
        logger.info("predict_crude_answer for jura21 or mid=18, set answer to: {}")
        answer = {}
    else:
        rules = None
        if only_b1_b10:
            logger.info("predict_crude_answer for the rules: B8, B9, B10")
            rules = {FundRaisingRuleEnum.B8.value, FundRaisingRuleEnum.B9.value, FundRaisingRuleEnum.B10.value}
        else:
            logger.info("predict_crude_answer for jura1.0 jura2.0")
        answer = predict(file, question, mold, report_year, god_mode, rules=rules)

    if not only_b1_b10 and mid in (special_mold.v1_id, special_mold.v2_id, special_mold.v3_id):
        # for jura2.1
        # logger.info("predict_crude_answer for jura2.1")
        if add_answer := predict(file, question, mold, report_year, god_mode, mode_version="v2", vid=0):
            for key, item in add_answer.items():
                if key in JURA_21_ADD_RULES and item:
                    logger.info(f"predict_crude_answer for jura2.1 rule: {key}")
                    answer[key] = item

    if not only_jura21 and mold.id == special_mold.v2_id:
        # for jura2 B1-B7
        from remarkable.prompter.impl.v4_embedding import EmbeddingPrompter

        # 配置了export SCRIBER_CONFIG_ONLY_B1_B10=True, 仅针对B1-B7跑embedding
        # 注意这里不跑B8-B10，因为B8-B10用逻辑回归
        embedding_rules = B1_B7_COLS if only_b1_b10 else B1_B7_COLS + JURA6_B_COLS
        if add_answer := await EmbeddingPrompter(mid).prompt_for_file(file, rules=embedding_rules):
            for key, item in add_answer.items():
                if item:
                    logger.info(f"predict_crude_answer for v2 rule: {key}")
                    answer[key] = item

    if not only_jura21 and mold.id == special_mold.v3_id:
        # for jura6 exclude C5
        from remarkable.prompter.impl.v4_embedding import EmbeddingPrompter

        if add_answer := await EmbeddingPrompter(mid).prompt_for_file(file, rules=JURA6_C_COLS):
            for key, item in add_answer.items():
                if item:
                    logger.info(f"predict_crude_answer for c rule: {key}")
                    answer[key] = item

    if mid in (special_mold.policy_esg_id, special_mold.policy_ar_id):
        logger.info("predict_crude_answer for policy_esg")
        category_element_by_rule = await get_category_candidates(file.id)
        answer.update(category_element_by_rule)
    for elements in answer.values():
        for ele in elements:
            if ele.get("element_type") in ("SHAPE", "IMAGE", "INFOGRAPHIC") and not ele.get("text"):
                query = Embedding.select(Embedding.text).where(
                    Embedding.file_id == file.id,
                    Embedding.index == ele["element_index"],
                    Embedding.element_sequence == 0,
                )
                if results := await embedding_pw_db.execute(query):
                    logger.info(f"use text from embedding: file-id: {file.id}  index: {ele['element_index']}")
                    ele["text"] = results[0].text

    return answer


async def predict_prev_fund_crude_answer(
    mold: NewMold, file: NewFile, question: NewQuestion, report_year: str, god_mode: bool
):
    """
    only_prev_fund_raising为True时，预测B9/C1.2.1的初步定位
    """

    # 预测B9
    mid = mold.id
    # 预测B9
    if mid == special_mold.v2_id:
        logger.info("predict_crude_answer for prev_fund_raising: rule=B9")
        # B9直接取question.crude_answer  https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6309#note_680302
        crude_answer = question.crude_answer or {}
        if answer := {key: item for key, item in crude_answer.items() if item and key.startswith("B9-")}:
            return answer
        # 取不到则重新预测
        return predict(file, question, mold, report_year, god_mode, rules={FundRaisingRuleEnum.B9.value})
    # 预测C1.2.1
    logger.info("predict_crude_answer for prev_fund_raising: rule=C1.2.1")
    if add_answer := await EmbeddingPrompter(mid).prompt_for_file(file, rules=[FundRaisingRuleEnum.C1_2_1.value]):
        return {key: item for key, item in add_answer.items() if item}
    return {}


async def save_crude_answer(crude_answer: dict, fid: int, question: NewQuestion):
    """
    初步定位结果入库
    """
    if not crude_answer:
        logging.warning(f"no prompt answer for {fid=}, mid={question.mold}, qid={question.id}")
        return
    if ctx_only_prev_fund_raising.get():
        # TODO 所有2024年年报全部预测完成之后，可删除该分支逻辑
        from remarkable.models.new_question import QuestionPrevFundRaising

        # 这个条件下生成的数据，只存入question_prev_fund_raising表
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6309
        logger.info(f"generate fund raising crude answer: {fid=}, {question.mold=}, {question.id=}")
        await QuestionPrevFundRaising.save_crude_answer(fid, question.mold, crude_answer)
        return crude_answer
    question.crude_answer = (question.crude_answer or {}) | crude_answer
    await pw_db.update(question, only=["crude_answer"])


def predict(file, question, mold, report_year, god_mode, mode_version=None, vid=None, rules=None):
    prompter = prompter_manager.get_schema_prompter(mold.id, god_mode, mode_version=mode_version, vid=vid)
    if not prompter:
        return {}
    if god_mode:
        res = prompter.prompt_all(file, mold, question.answer, rules=rules)
    else:
        res = prompter.prompt_all(file, mold, report_year=report_year, vid=vid, rules=rules)
    if not res:
        return {}
    answer = {}
    for aid, items in res.items():
        answer[aid] = []
        for proba, ele, keywords, etype in items:
            text = ele.get("text", "")
            if etype == "TABLE":
                text = ele.get("title") or table_element_content_text(ele)
            answer[aid].append(
                {
                    "score": proba,
                    "element_index": ele.get("index"),
                    "element_type": etype,
                    "text": text,
                    "page": ele.get("page"),
                    "outline": ele.get("outline"),
                    "keywords": [(k.text, k.score) for k in keywords[:12]],
                    "element": ele if god_mode else None,
                }
            )
    return answer


async def predict_crude_answer_by_embedding(mid, fid, rules: list | None = None, special_enum=None):
    from remarkable.prompter.impl.v4_embedding import EmbeddingPrompter

    logger.info(f"prompt answer for file by_embedding: {fid}, mold: {mid}")
    file = await NewFile.find_by_id(fid)
    if not file:
        logger.warning(f"file {fid} not found")
        return
    query = Embedding.select(Embedding.id).where(Embedding.file_id == file.id)
    if not await embedding_pw_db.exists(query):
        logger.warning(f"file {fid} has no embedding, create_and_save_embeddings")
        await create_and_save_embeddings(fid, need_sub_element=True, overwrite=False)
    question = await NewQuestion.find_by_fid_mid(fid, mid)
    if not question:
        logger.warning(f"question {fid}-{mid} not found")
        return
    try:
        crude_answer = await EmbeddingPrompter(mid).prompt_for_file(file, rules=rules, special_enum=special_enum)
        question.crude_answer = {**(question.crude_answer or {}), **crude_answer}
        await pw_db.update(question, only=["crude_answer"])
    except Exception as ex:
        logging.exception(f"error in prompt element for file by_embedding: {fid}, {ex}")


if __name__ == "__main__":
    import asyncio

    # asyncio.run(predict_crude_answer_by_embedding(18, 70573, rules=["C1.2.1"]))
    asyncio.run(predict_crude_answer_by_embedding(18, 70461, rules=["C1"]))
