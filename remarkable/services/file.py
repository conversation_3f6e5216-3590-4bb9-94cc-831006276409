import asyncio
import copyreg
import gzip
import logging
import os
import pickle
import re
from collections import defaultdict
from contextvars import ContextV<PERSON>
from copy import copy
from dataclasses import dataclass
from typing import Callable, Dict, List, Literal, Tuple

import attr
import msgspec
import peewee
from more_itertools import chunked
from tqdm.asyncio import tqdm

from remarkable.answer.reader import has_special_answer
from remarkable.common.constants import ConflictTreatmentType
from remarkable.common.storage import LocalStorage, localstorage
from remarkable.common.util import httpx_client, mm_notify, read_zip_first_file
from remarkable.config import get_config
from remarkable.converter.hkex import <PERSON><PERSON><PERSON><PERSON>, ExtInfo, SignificantInvestmentConverter, attr2dict
from remarkable.db import pw_db
from remarkable.models import TModel
from remarkable.models.addition_data import AdditionData
from remarkable.models.agm_meta import AGMMeta
from remarkable.models.answer import Answer
from remarkable.models.embedding import Embedding, SyllabusEmbedding
from remarkable.models.file_esg_xref import FileESGxREF
from remarkable.models.file_project import NewFileProject
from remarkable.models.file_tree import NewFileTree
from remarkable.models.hkex_company import DelistedCompany
from remarkable.models.hkex_file import HKEXFile
from remarkable.models.hkex_file_meta import DelistedFileMeta, HKEXFileMeta
from remarkable.models.hkex_stock import HKEXCompaniesInfo
from remarkable.models.manual_group_answer import ManualGroupAnswer
from remarkable.models.mold import special_mold
from remarkable.models.new_file import NewFile
from remarkable.models.new_history import NewHistory
from remarkable.models.new_mold import NewMold
from remarkable.models.new_question import NewQuestion
from remarkable.models.poll_gml import PollGML
from remarkable.models.rule_group import GroupRuleRef, RuleGroup
from remarkable.models.rule_reference import RuleReference
from remarkable.models.special_answer import NewSpecialAnswer
from remarkable.models.user import AdminUser
from remarkable.services.question import pick_special_answer

logger = logging.getLogger(__name__)
EXPORT_URL = "{}/api/v2/files/sync"
ctx_increment = ContextVar("increment", default=False)
ctx_overwrite_mold = ContextVar("overwrite_mold", default=False)


def seq_iter(seq, step=5):
    return chunked(seq, step)


class _Doc(msgspec.Struct):
    tables: List[dict] = []
    paragraphs: List[dict] = []
    page_headers: List[dict] = []
    page_footers: List[dict] = []
    images: List[dict] = []  # 图片
    shapes: List[dict] = []  # 柱状图、折线图等
    footnotes: List[dict] = []
    infographics: List[dict] = []  # 艺术字sync
    syllabuses: List[dict] = []
    pages: Dict[str, dict] = {}
    _index: dict = {}

    def __getitem__(self, key):
        return getattr(self, key, [] if key.endswith("s") else None)

    def __setitem__(self, key, value):
        setattr(self, key, value)

    def get(self, key, default=None):
        return self[key] or default

    @property
    def non_tables(self) -> List[Tuple[str, List[dict]]]:
        syllabus_indexes = {x["element"] for x in self.syllabuses}
        return [
            ("page_header", self.page_headers),
            ("paragraph", [x for x in self.paragraphs if x["index"] not in syllabus_indexes]),
            ("page_footer", self.page_footers),
            ("footnote", self.footnotes),
            ("image", self.images),
            ("shape", self.shapes),
            ("infographic", self.infographics),
            ("syllabus", [x for x in self.paragraphs if x["index"] in syllabus_indexes]),
        ]

    @property
    def all_elements(self):
        elements = (
            self.tables
            + self.paragraphs
            + self.page_headers
            + self.page_footers
            + self.images
            + self.shapes
            + self.infographics
            + self.footnotes
        )
        elements.sort(key=lambda x: x["index"])
        return elements


class _OutLine(msgspec.Struct):
    index: int
    outline: List[float]
    merged: list = []
    grid: dict = {}
    cells: Dict[str, dict] = {}
    type: (
        Literal[
            "page_header", "paragraph", "page_footer", "table", "footnote", "shape", "image", "infographic", "syllabus"
        ]
        | None
    ) = None

    def to_dict(self):
        res = {"outline": self.outline, "type": self.type, "index": self.index}
        if self.cells:
            res.update({"merged": self.merged, "grid": self.grid, "cells": self.cells})
        return res


def get_or_create_outlines(file: NewFile, fail_count=0) -> Dict[str, List[dict]]:
    work_dir = LocalStorage(file.label_cache_dir)
    # 删除旧缓存
    work_dir.delete_file("label_tables.json.gz")

    outlines_path = work_dir.mount("outlines.json.gz")
    if work_dir.exists(outlines_path):
        try:
            raw_data = work_dir.read_file(outlines_path, open_pack="gzip")
        except Exception:
            logger.exception(f"Load label cache failed for file:{file.id}, path:{outlines_path}")
            work_dir.delete_file(outlines_path)
            if fail_count > 3:
                logger.error(f"Max fail count reached for file:{file.id}, path:{outlines_path}")
                return {}
            return get_or_create_outlines(file, fail_count + 1)
        else:
            return {
                k: [o.to_dict() for o in v]
                for k, v in msgspec.json.decode(raw_data, type=Dict[str, List[_OutLine]]).items()
            }

    data = read_zip_first_file(file.pdfinsight_path(abs_path=True), msgspec_type=_Doc)
    outlines = defaultdict(list)
    for table in data.tables:
        thin_table = {k: v for k, v in table.items() if k in ("outline", "merged", "grid")}
        thin_table["cells"] = {k: {"box": v["box"]} for k, v in table["cells"].items()}
        thin_table["type"] = "table"
        thin_table["index"] = table["index"]
        outlines[str(table["page"])].append(thin_table)
    for typ, elements in data.non_tables:
        for element in elements:
            outlines[str(element["page"])].append(
                {"outline": element["outline"], "type": typ, "index": element["index"]}
            )
    for page in outlines:
        elements = outlines[page]
        if len(elements) > 1:
            # ↓ → 排序保存
            outlines[page].sort(key=lambda e: (e["outline"][1], e["outline"][0]))
    work_dir.write_file(outlines_path, msgspec.json.encode(outlines), open_pack="gzip")
    return outlines


def fill_none_default(row_ins: TModel) -> TModel | dict:
    if isinstance(row_ins, dict):
        # for RuleGroupRuleReferenceThrough
        return row_ins

    """Replace None with the default value of the field"""
    for field in row_ins._meta.fields.values():
        if field.default is not None and getattr(row_ins, field.name) is None:
            default_value = field.default() if callable(field.default) else field.default
            setattr(row_ins, field.name, default_value)
    return row_ins


@attr.define
class SyncBakery:
    RENDER_BLACKLIST = ("file_only", "dst_mid_list")

    fid: int = attr.ib(converter=lambda x: 0 if x is None else int(x))
    mid: int | None = attr.ib(default=None, converter=lambda x: None if x is None else int(x))
    query: str = attr.ib(default="", converter=lambda x: x or "")
    fids: List[int] = attr.ib(default=attr.Factory(list))
    rows: List[TModel] = attr.ib(default=attr.Factory(list), converter=lambda x: [fill_none_default(i) for i in x])
    files: Dict[str, bytes] = attr.ib(default=attr.Factory(dict), repr=lambda x: x.keys())
    db_only: bool = attr.ib(default=False)
    file_only: bool = attr.ib(default=False)  # 仅同步文档，创建新的questions，供新环境标注使用
    dst_mid_list: List[int] = attr.ib(default=attr.Factory(list))  # 匹配目标环境schema ids
    need_embedding: bool = attr.ib(default=False)  # 是否同步embedding数据
    rule_manage: bool = attr.ib(default=False)

    def __attrs_post_init__(self):
        if self.file_only:
            self.db_only = False
        if not self.dst_mid_list:
            self.dst_mid_list = []

    @staticmethod
    def _update_pdfinsight_ctime(file: NewFile | None) -> None:
        if file and file.pdfinsight and localstorage.exists(file.pdfinsight_path()):
            # NOTE: update updated_utc with the existed pdfinsight file create or last modified time
            file.updated_utc = int(os.lstat(file.pdfinsight_path(abs_path=True)).st_ctime)

    @staticmethod
    def is_safe_query(query):
        return not re.search(
            r"\b(DELETE|UPDATE|INSERT|DROP|TRUNCATE|COMMIT|GRANT\s+ALL|CREATE|REPLACE)\b", query, flags=re.I
        )

    async def load_files(self, instance, cols):
        if self.db_only:
            return
        for col in cols:
            path = instance.path(col)
            if path and localstorage.exists(path) and path not in self.files:
                self.files[path] = await asyncio.get_event_loop().run_in_executor(None, localstorage.read_file, path)

    async def dumps(self) -> bytes | None:
        if self.rule_manage:
            # 返回对应环境rule_reference, rule_group, rule_group_rule_reference_through  三张表的所有数据
            self.rows.extend(await pw_db.execute(RuleReference.select()))
            self.rows.extend(await pw_db.execute(RuleGroup.select()))
            group_rule_refs = await pw_db.execute(
                GroupRuleRef.select(GroupRuleRef.id, GroupRuleRef.rulegroup_id, GroupRuleRef.rulereference_id)
            )
            self.rows.extend([row.to_dict() for row in group_rule_refs])
            return gzip.compress(pickle.dumps(self))

        """Load db rows and files, then dump as a pickle object(bytes) with gzip compression algorithm"""
        if self.query and self.is_safe_query(self.query):
            # 仅返回文件ID列表
            for fid, *_ in await NewFile.manager().execute(self.query):
                self.fids.append(fid)
            return gzip.compress(pickle.dumps(self))

        file = await NewFile.get_by_id(self.fid, include_deleted=True)
        if not file:
            return gzip.compress(pickle.dumps(self))

        # file
        self._update_pdfinsight_ctime(file)
        self.rows.append(file)

        # files
        await self.load_files(file, ("hash", "pdf", "docx", "pdfinsight"))

        # trees
        self.rows.extend(await NewFileTree.get_upper_trees(file.tree_id))
        # project
        self.rows.append(await NewFileProject.get_by_id(file.pid, include_deleted=True))
        # file_esg_xref
        esg_ref = await pw_db.first(FileESGxREF.select(include_deleted=True).where(FileESGxREF.fid == file.id))
        if esg_ref:
            self.rows.append(esg_ref)

        # molds
        for mid in file.mold_list:
            mold = await NewMold.get_by_id(mid, include_deleted=True)
            if self.mid and self.mid == mid:
                self.rows.append(mold)
                break
            self.rows.append(mold)

        if self.file_only:
            return gzip.compress(pickle.dumps(self))

        # hkex_files
        for hkex_file in await file.get_related_objects(HKEXFile, HKEXFile.fid, include_deleted=True):
            self.rows.append(hkex_file)
            await self.load_files(hkex_file, ("hash", "pdf_hash"))
        # hkex_file_meta
        for meta in await file.get_related_objects(HKEXFileMeta, HKEXFileMeta.fid, include_deleted=True):
            self.rows.append(meta)
            # hkex_companies_info
            self.rows.extend(
                await meta.get_related_objects(
                    HKEXCompaniesInfo,
                    HKEXCompaniesInfo.stock_code,
                    self_key=meta.stock_code,
                    include_deleted=True,
                )
            )

        # delisted_file_meta
        for meta in await file.get_related_objects(DelistedFileMeta, DelistedFileMeta.fid, include_deleted=True):
            # delisted_company
            self.rows.extend(
                await meta.get_related_objects(
                    DelistedCompany,
                    DelistedCompany.stock_code,
                    self_key=meta.stock_code,
                    include_deleted=True,
                )
            )
            self.rows.extend(
                await meta.get_related_objects(
                    DelistedFileMeta,
                    DelistedFileMeta.stock_code,
                    self_key=meta.stock_code,
                    include_deleted=True,
                )
            )
        # agm_meta
        for meta in await file.get_related_objects(AGMMeta, AGMMeta.agm_fid, include_deleted=True):
            self.rows.append(meta)
        # poll_gml
        for meta in await file.get_related_objects(PollGML, PollGML.fid, include_deleted=True):
            self.rows.append(meta)

        if self.need_embedding:
            # Embedding
            for meta in await file.get_related_objects(Embedding, Embedding.file_id, include_deleted=True):
                self.rows.append(meta)

            # SyllabusEmbedding
            for meta in await file.get_related_objects(
                SyllabusEmbedding, SyllabusEmbedding.file_id, include_deleted=True
            ):
                self.rows.append(meta)

        # addition_data
        for data in await file.get_related_objects(AdditionData, AdditionData.file_id, include_deleted=True):
            self.rows.append(data)

        # questions
        cond = NewQuestion.mold == self.mid if self.mid else None
        question_ids = []
        for question in await file.get_related_objects(NewQuestion, NewQuestion.fid, cond, include_deleted=True):
            question_ids.append(question.id)
            self.rows.append(question)
            for answer in await question.get_related_objects(Answer, Answer.qid, include_deleted=True):
                self.rows.append(answer)
                user = await answer.get_related_object(AdminUser, answer.uid, include_deleted=True)
                if user:
                    self.rows.append(user)
            for special_answer in await question.get_related_objects(
                NewSpecialAnswer,
                NewSpecialAnswer.qid,
                include_deleted=True,
            ):
                self.rows.append(special_answer)

            for history in await question.get_related_objects(
                NewHistory,
                NewHistory.qid,
                peewee.SQL(f"{NewHistory.meta.name} ? 'adjustment'"),
                include_deleted=True,
            ):
                self.rows.append(history)

        # 同步人工创建的Event Type: https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/5464
        for manual_group in await pw_db.execute(
            ManualGroupAnswer.select().where(ManualGroupAnswer.qid.in_(question_ids))
        ):
            self.rows.append(manual_group)

        # NOTE: Do we need to sync cache files or records?(optional)
        return gzip.compress(pickle.dumps(self))

    async def loads(self):
        """Restore all the db rows and files etc."""
        from remarkable.worker.tasks import _make_question

        if self.rule_manage:
            async with pw_db.atomic():
                for query in (RuleReference.delete(), RuleGroup.delete(), GroupRuleRef.delete()):
                    count = await pw_db.execute(query)
                    logger.info(f"delete {query.model} {count} rows")
                for row in self.rows:
                    if isinstance(row, RuleReference):
                        row_data = row.to_dict()
                        row_data.pop("created_utc")
                        await pw_db.execute(RuleReference.insert(**row_data))
                logger.info("Insert RuleReference done")
                for row in self.rows:
                    if isinstance(row, RuleGroup):
                        row_data = row.to_dict()
                        row_data.pop("created_utc")
                        await pw_db.execute(RuleGroup.insert(**row_data))
                logger.info("Insert RuleGroup done")
                for row in self.rows:
                    if isinstance(row, dict):
                        await pw_db.execute(GroupRuleRef.insert(**row))
                logger.info("Insert GroupRuleRef done")
            logger.info("Sync <RuleReference>, <RuleGroup>, <GroupRuleRef> done")
            return None

        for fids in seq_iter(self.fids, 10):
            # Reduce the number of concurrent
            await asyncio.gather(
                *(
                    fetch_and_sync(
                        fid,
                        db_only=self.db_only,
                        file_only=self.file_only,
                        dst_mid_list=self.dst_mid_list,
                        check_exists=ctx_increment.get(False),
                        need_embedding=self.need_embedding,
                    )
                    for fid in fids
                )
            )

        if not self.rows:
            return None

        if self.file_only:
            self.rows = [row for row in self.rows if isinstance(row, (NewFile, NewFileTree, NewFileProject, NewMold))]

        if self.dst_mid_list:
            self.rows = [row for row in self.rows if not isinstance(row, NewMold)]
            for row in self.rows:
                if isinstance(row, NewFile):
                    row.mold = min(self.dst_mid_list)
                    row.mold_list = self.dst_mid_list
                if isinstance(row, NewFileProject):
                    row.default_mold = min(self.dst_mid_list)
                if isinstance(row, NewFileTree):
                    row.default_mold = self.dst_mid_list
        await self.save_files()
        need_make_question = False
        is_esg = False

        qids, special_answers, addition_data_rows, file_names = set(), [], [], []
        for row in sorted(self.rows, key=lambda x: 0 if isinstance(x, (NewFile, HKEXFileMeta, NewMold)) else 1):
            if isinstance(row, NewFile):
                if row.mold_list and [i for i in special_mold.esg_mids_with_policy if i in row.mold_list]:
                    is_esg = True
                file = await NewFile.get_by_id(row.id)
                if file:
                    file_names.append(file.name)
                if file and file.pdfinsight and row.pdfinsight and file.pdfinsight != row.pdfinsight:
                    # 清理旧缓存
                    localstorage.delete_file(file.pdfinsight_path())
                    localstorage.delete_dir(file.label_cache_dir)

                if self.file_only:
                    # 仅在本地创建基本文件数据（文件 ID 与远端不一致）
                    new_file = await pw_db.first(
                        NewFile.select().filter(NewFile.pdf == row.pdf).order_by(NewFile.created_utc.desc())
                    )
                    if not new_file:
                        new_file = await NewFile.create(**row.to_dict(exclude=(NewFile.id,)))
                        logger.warning(f"New file created: {new_file.id}, make question...")
                    elif row.pdfinsight and row.pdfinsight != new_file.pdfinsight:
                        # 清理旧缓存
                        localstorage.delete_file(new_file.pdfinsight_path())
                        localstorage.delete_dir(new_file.label_cache_dir)
                        new_file.pdfinsight = row.pdfinsight
                        await pw_db.update(new_file)
                    if not new_file.pdfinsight:
                        logger.warning(f"File({new_file.id}) has no pdfinsight data")

                    await _make_question(new_file.id)
                    continue
            if isinstance(row, AdminUser) and row.id <= 1:
                logger.warning("Keep admin info")
                continue
            if isinstance(row, NewSpecialAnswer):
                special_answers.append(row.id)
                qids.add(row.qid)
            row_data = row.to_dict()
            # addition_data批量导入
            if isinstance(row, AdditionData):
                addition_data_rows.append(row_data)
                continue
            if isinstance(row, NewHistory):
                row_data.pop("id")

            if (
                isinstance(row, NewMold)
                and (
                    local_ver := await pw_db.scalar(
                        NewMold.select(NewMold.checksum).where((NewMold.id == row.id) | (NewMold.name == row.name))
                    )
                )
                and local_ver != row.checksum
            ):
                msg = f"Local mold({row.id}) version({local_ver}) is different from the remote({row.checksum}), overwrite it"
                if ctx_overwrite_mold.get(False):
                    logger.warning(f"{msg}.")
                else:
                    raise ValueError(f"{msg}?")

            try:
                if isinstance(row, HKEXFile):
                    row_data.pop("id")
                if isinstance(row, AGMMeta):
                    row_data.pop("id")
                    await AGMMeta.insert_or_update(conflict_target=[AGMMeta.agm_fid], **row_data)
                elif isinstance(row, Embedding):
                    row_data.pop("id")
                    await Embedding.insert_or_update(
                        conflict_target=[Embedding.file_id, Embedding.index, Embedding.element_sequence], **row_data
                    )
                elif isinstance(row, SyllabusEmbedding):
                    row_data.pop("id")
                    await SyllabusEmbedding.insert_or_update(
                        conflict_target=[SyllabusEmbedding.file_id, SyllabusEmbedding.syllabus],
                        **row_data,
                    )
                elif isinstance(row, PollGML):
                    row_data.pop("id")
                    await PollGML.insert_or_update(
                        conflict_target=[PollGML.qid],
                        **row_data,
                    )
                else:
                    await row.__class__.insert_or_update(**row_data)
                    await self.update_id_seq(row)
                    logger.debug(f"{row.table_name()}: {row} saved.")
            except Exception as exp:
                logger.exception(exp)

        if addition_data_rows:
            # 写入addition_data之前，清空表格中对应的文件名的数据
            local_fids = await NewFile.manager().scalars(
                NewFile.select(NewFile.id)
                .join(NewFileTree, on=(NewFileTree.id == NewFile.tree_id))
                .join(NewFileProject, on=(NewFileProject.id == NewFileTree.pid))
                .where(
                    NewFileProject.name == get_config("project_list.addition_files.name"), NewFile.name.in_(file_names)
                )
            )
            count = await AdditionData.manager().execute(
                AdditionData.delete().where(AdditionData.file_id.in_(local_fids))
            )
            logger.info(f"remove data from addition_data: file_ids={local_fids}, {file_names=}, {count} rows")
            # 删除new_file表中的数据
            count = await NewFile.manager().execute(NewFile.delete().where(NewFile.id.in_(local_fids)))
            logger.info(f"remove data from file: file_ids={local_fids}, {file_names=}, {count} rows")
            # 批量写入全部
            await AdditionData.bulk_insert(addition_data_rows)

        if special_answers and qids:
            await pw_db.execute(
                NewSpecialAnswer.delete().where(
                    (NewSpecialAnswer.qid.in_(qids)) & (NewSpecialAnswer.id.not_in(special_answers))
                )
            )
        if need_make_question:
            # 补充可能缺失的关联schema
            # HARDCODE: ESG in Annual Report
            await _make_question(self.fid, get_config("esg_molds.AR.id", 1))
        if is_esg:
            from remarkable.worker.esg_tasks import deal_with_esg_metadata

            deal_with_esg_metadata.delay(self.fid)
        logger.info(f"File: {self.fid} synchronized successfully.")
        return None

    async def save_files(self):
        for path, raw_data in self.files.items():
            await asyncio.get_event_loop().run_in_executor(None, localstorage.write_file, path, raw_data)

    @staticmethod
    async def update_id_seq(table: str | peewee.Model, value=None):
        if isinstance(table, peewee.Model):
            table = table._meta.table_name
        for (real_name,) in await pw_db.execute("SELECT c.relname FROM pg_class c WHERE c.relkind = 'S'"):
            if re.search(r"^{}_id.*".format(table), real_name):
                if value is None:
                    await pw_db.execute(f"SELECT setval('{real_name}', (SELECT MAX(id) FROM {table})+1)")
                else:
                    await pw_db.execute(f"SELECT setval('{real_name}', {value})")
                logger.debug(f"{real_name} seq id updated")


def _unpickle_sync_bakery(kwargs):
    # via https://github.com/bslatkin/effectivepython/blob/4ae6f3141291ea137eb29a245bf889dbc8091713/example_code/item_68.py#L171
    valid_fields = {f.name for f in attr.fields(SyncBakery)}  # noqa
    return SyncBakery(**{k: v for k, v in kwargs.items() if k in valid_fields})


def _pickle_sync_bakery(sync_bakery):
    return _unpickle_sync_bakery, (attr2dict(sync_bakery),)


copyreg.pickle(SyncBakery, _pickle_sync_bakery)


async def fetch_and_sync(
    file_id,
    mid=None,
    query=None,
    db_only=False,
    data_path=None,
    bakery: SyncBakery | None = None,
    file_only=False,
    dst_mid_list=None,
    disable_tqdm=False,
    load_func: Callable | None = None,
    check_exists: bool = False,
    need_embedding: bool = False,
    rule_manage: bool = False,
):
    if check_exists and (file := await NewFile.get_by_id(file_id)):
        if (
            (path := file.pdfinsight_path(abs_path=True))
            and os.path.exists(path)
            and (path := file.pdf_path(abs_path=True))
            and os.path.exists(path)
        ):
            logger.warning(f"{file_id=} already exists in local db. Skip fetching.")
            return None

    if data_path:
        with open(data_path, "rb") as fp:
            content = fp.read()
    else:
        if not bakery:
            bakery = SyncBakery(
                file_id,
                mid=mid,
                query=query,
                db_only=db_only,
                file_only=file_only,
                dst_mid_list=dst_mid_list,
                need_embedding=need_embedding,
                rule_manage=rule_manage,
            )
        logger.debug(f"Local bakery info: {bakery}")
        chunk_size = 5242880  # 5MB
        try:
            async with httpx_client(timeout=180) as client:
                content = b""
                async with client.stream(
                    "POST", EXPORT_URL.format(get_config("web.apis.sync_host")), content=pickle.dumps(bakery)
                ) as rsp:
                    file_size = int(rsp.headers.get("X-Content-Length") or rsp.headers.get("Content-Length"))
                    async for chunk in tqdm(
                        rsp.aiter_bytes(chunk_size=chunk_size), total=file_size // chunk_size, disable=disable_tqdm
                    ):
                        content += chunk
        except Exception as exp:
            logger.exception(str(exp))
            return None
    instance: SyncBakery = pickle.loads(gzip.decompress(content))
    instance.file_only = file_only
    instance.dst_mid_list = dst_mid_list
    instance.need_embedding = need_embedding
    return await instance.loads() if not load_func else await load_func(instance)


async def _merge_remote_answer(ins: SyncBakery):
    for fids in seq_iter(ins.fids, 10):
        # Reduce the number of concurrent
        await asyncio.gather(
            *(
                fetch_and_sync(
                    fid,
                    db_only=ins.db_only,
                    file_only=ins.file_only,
                    dst_mid_list=ins.dst_mid_list,
                    load_func=_merge_remote_answer,
                )
                for fid in fids
            )
        )

    questions = {}
    fid = ins.fid
    for row in ins.rows:
        if isinstance(row, NewFile):
            file = await NewFile.get_first_one(NewFile.hash == row.hash)
            if not file:
                logger.warning(f"No same file with hash {row.hash}")
                return
            fid = file.id
            for question in await NewQuestion.find_by_fid(file.id):
                questions[question.mold] = question
        if isinstance(row, NewQuestion) and row.mold in questions:
            # 仅合并定制页面提交的答案
            await questions[row.mold].set_answer(pick_special_answer(row.answer))

    logger.info(f"Answer for file(R:{ins.fid} => L:{fid}) merged successfully.")


async def merge_remote_answer(file_id, mid=None, query=None):
    # 合并远端定制页面答案到本地
    return await fetch_and_sync(file_id, mid=mid, query=query, db_only=True, load_func=_merge_remote_answer)


async def _prepare_remark_files(ins: SyncBakery):
    from remarkable.worker.tasks import _make_question

    for fids in seq_iter(ins.fids, 10):
        # Reduce the number of concurrent
        await asyncio.gather(
            *(
                fetch_and_sync(
                    fid,
                    db_only=ins.db_only,
                    file_only=ins.file_only,
                    dst_mid_list=ins.dst_mid_list,
                    load_func=_prepare_remark_files,
                )
                for fid in fids
            )
        )

    fid = None
    meta_ins: HKEXFileMeta | None = None
    file_exists = False
    mids = []
    ins.rows = [
        row
        for row in ins.rows
        if isinstance(
            row, (NewFile, NewFileTree, NewFileProject, NewQuestion, HKEXFile, HKEXFileMeta, HKEXCompaniesInfo)
        )
    ]
    is_esg = False
    for row in sorted(ins.rows, key=lambda x: 0 if isinstance(x, (NewFile, NewQuestion)) else 1):
        if isinstance(row, NewFile):
            row.qid = None
            new_file = await pw_db.first(
                NewFile.select()
                .filter(NewFile.pdf == row.pdf, NewFile.deleted_utc == 0)
                .order_by(NewFile.created_utc.desc())
            )
            if new_file:
                file_exists = True
                fid = new_file.id
                await _make_question(fid)
                logger.warning(f"File already exists: {fid}")
                continue
            await ins.save_files()
            new_file = await NewFile.create(**row.to_dict(exclude=(NewFile.id,)))
            logger.warning(f"New file created: {new_file.id}, making questions...")
            if not new_file.pdfinsight:
                logger.warning(f"File({new_file.id}) has no pdfinsight data")
            fid = new_file.id
            await _make_question(fid)
            if new_file.mold_list and [i for i in special_mold.esg_mids_with_policy if i in new_file.mold_list]:
                is_esg = True
            continue

        if isinstance(row, NewQuestion):
            row.fid = fid
            row.preset_answer = None
            row.answer = None
            row.data = {"file_id": fid}
            row.checksum = f"{fid}_{row.mold}"
            mids.append(row.mold)

        if isinstance(row, HKEXFileMeta):
            local_qid = await pw_db.scalar(
                NewQuestion.select(NewQuestion.id).filter(NewQuestion.fid == fid, NewQuestion.mold == min(mids))
            )
            row.fid = fid
            row.qid = local_qid
            row.stat_res = {}
            meta_ins = copy(row)
            await pw_db.execute(HKEXFileMeta.delete().where(HKEXFileMeta.fid == fid))

        if isinstance(row, HKEXFile):
            row.fid = fid

        if not file_exists or isinstance(row, (HKEXFileMeta, HKEXFile)):
            row_data = row.to_dict()
            if isinstance(row, HKEXFile):
                row_data.pop("id")
            try:
                await row.__class__.insert_or_update(**row_data)
            except Exception as exp:
                logger.exception(exp)
            else:
                await ins.update_id_seq(row)
                logger.debug(f"{row.table_name()}: {row} saved.")

    if fid and meta_ins:
        if is_esg:
            from remarkable.worker.esg_tasks import deal_with_esg_metadata

            deal_with_esg_metadata.delay(fid)
        print(
            f"{meta_ins.stock_code},{meta_ins.report_year},{meta_ins.published},"
            # f"{get_config('web.scheme', 'https')}://{get_config('web.domain')}:{get_config('web.http_port')}"
            f"http://100.64.0.105:55647"
            f"/#/search?fileid={fid}"
        )


async def prepare_remark_files(file_id, mid=None, query=None):
    print("stock_code,report_year,published_date,url")
    return await fetch_and_sync(file_id, mid=mid, query=query, load_func=_prepare_remark_files)


async def _do_nothing(ins: SyncBakery):
    logger.info(f"Update pdfinsight data for remote file: {ins.fid} complete")


async def push_pdfinsight_data(file_id: int, mid: int | None = None):
    logger.info(f"Pushing pdfinsight data from local file: {file_id}")
    fake_id = -1
    ins = SyncBakery(fake_id, mid=mid)
    file = await NewFile.get_by_id(file_id)
    if not file:
        logger.error(f"File not exists: {file_id}")
        return
    ins.rows.append(file)
    await ins.load_files(file, ("pdfinsight",))
    if not ins.files:
        logger.error(f"No pdfinsight data found for file: {file_id}")
        return
    try:
        await fetch_and_sync(fake_id, bakery=ins, load_func=_do_nothing)
    except Exception as exp:
        logger.error(exp)
    return


async def get_crumbs(tree_id: int) -> List[dict]:
    own = NewFileTree.select().where(NewFileTree.id == tree_id).cte("base", recursive=True)
    parent = NewFileTree.alias("parent")
    recursive = parent.select().join(own, on=(own.c.ptree_id == parent.id))
    cte = own.union_all(recursive)
    subquery = cte.select_from(cte.c.id, cte.c.name, cte.c.ptree_id)
    exist_query = peewee.fn.EXISTS(subquery.where(cte.c.id == NewFileTree.id))
    trees = await pw_db.prefetch(NewFileTree.select().where(exist_query).order_by(NewFileTree.id))
    return [{"id": tree.id, "name": tree.name} for tree in trees]


@dataclass
class Ratio4Result:
    formulas: list
    reason: Literal["no_question", "no_meta", ""]


async def get_ratio4_by_file(fid: int, notify_mm=False) -> Ratio4Result:
    mid = special_mold.jura21_helper_id
    question = await NewQuestion.find_by_fid_mid(fid, mid)
    if not question:
        return Ratio4Result([], "no_question")
    merged_label_answer = await question.get_user_merged_answer(conflict_type=ConflictTreatmentType.LATEST.value)
    answer = (
        merged_label_answer
        if merged_label_answer
        and has_special_answer(
            merged_label_answer, check_schemas=["Significant Investment", "Total Assets"], check_method="any"
        )
        else question.preset_answer
    )
    file_meta = await pw_db.first(HKEXFileMeta.select(include_deleted=True).where(HKEXFileMeta.fid == question.fid))
    if not file_meta:
        return Ratio4Result([], "no_meta")
    ext_info = ExtInfo(file_meta)
    scope_info = await SignificantInvestmentConverter.load_scope_info(ext_info)

    formulas = []
    for items in SignificantInvestmentConverter(answer).convert(ext_info, scope_info, "ai").values():
        for item in items:
            formulas.append(attr2dict(item))

    # "Significant Investment"可能没有答案，但是"Total Assets"必须有答案
    if not has_special_answer(answer, check_schemas=["Total Assets"], check_method="any"):
        if notify_mm:
            error_msg = f"#Total Assets and Significant Investment were not extracted. {fid=}"
            mm_notify(error_msg, error=True)
        return Ratio4Result([], "")

    for formula in formulas:
        ratio = formula.get("ratio", 0)
        if ratio < 0.05 and formula["formula_info"] != "":
            formula["formula_info"] = f"{formula['formula_info'][:-1]}{BaseChecker.desc2latex('< 5%')}$"
        # 重大投资占比超过100%，数据存在问题
        if notify_mm and ratio >= 1:
            error_msg = f"#ratio4_error There's an error in the ratio4 result. {ratio=} {fid=}"
            mm_notify(error_msg, error=True)

    return Ratio4Result(formulas, "")


if __name__ == "__main__":
    # asyncio.run(get_ratio4_by_file(114485))
    # asyncio.run(get_ratio4_by_file(70805))
    asyncio.run(get_ratio4_by_file(112806))
