import re

from remarkable.models.embedding import Embedding
from remarkable.models.new_file import New<PERSON>ile
from remarkable.pdfinsight.reader import PdfinsightReader

SEMANTIC_WEIGHT = 3.0  # 语义搜索权重
FULL_TEXT_WEIGHT = 1.0  # 全文和正则搜索权重
REGEX_WEIGHT = 5.0  # 正则搜索权重，设置为更高的值


def group_embedding_results_by_rank(group_result: dict, result, rank, search_type):
    key = f"{result['index']}_{result['element_sequence']}"
    if data := group_result.get(key):
        scores = data.get("scores") or {}
        if c_rank := scores.get(search_type):
            if c_rank > rank:
                scores[search_type] = rank
        else:
            scores[search_type] = rank
    else:
        group_result[key] = {"data": result, "scores": {search_type: rank}}


async def retrieval_by_keywords(
    fid,
    rule_description,
    sub_rules: list = None,
    limit=20,
    k=60,
    only_sub_element=False,
    indexes: list[int] | None = None,
    element_sequence: int | None = None,
):
    if sub_rules:
        sub_rules.append(rule_description)
    else:
        sub_rules = [rule_description]

    results = {"semantic": [], "full_text": [], "regex": []}

    for rule in sub_rules:
        semantic_results = await Embedding.semantic_search(
            file_ids=[fid],
            query_text=rule,
            limit=limit,
            indexes=indexes,
            element_sequence=element_sequence,
        )
        results["semantic"].extend(semantic_results)

        full_text_results = await Embedding.full_text_search(
            file_ids=[fid],
            query_text=rule,
            limit=limit,
            indexes=indexes,
            element_sequence=element_sequence,
        )
        results["full_text"].extend(full_text_results)

        sanitized_value = re.escape(rule)
        regex_pattern = f"\\y{sanitized_value}\\y"
        regex_results = await Embedding.regex_search_with_score(
            file_ids=[fid],
            regex=regex_pattern,
            query_text=rule,
            limit=limit,
            only_sub_element=only_sub_element,
            indexes=indexes,
            element_sequence=element_sequence,
        )
        results["regex"].extend(regex_results)

    all_results = {}
    for s_type, search_result in results.items():
        for rank, result in enumerate(search_result, 1):
            group_embedding_results_by_rank(all_results, result, rank, s_type)

    # Calculate final RRF scores
    final_results = []
    for result_data in all_results.values():
        rrf_score = 0
        for method, rank in result_data["scores"].items():
            if method == "semantic":
                weight = SEMANTIC_WEIGHT
            elif method == "regex":
                weight = REGEX_WEIGHT
            else:
                weight = FULL_TEXT_WEIGHT
            rrf_score += weight * (1 / (k + rank))

        final_results.append({**result_data["data"], "rrf_score": rrf_score})

    final_results.sort(key=lambda x: x["rrf_score"], reverse=True)
    file = await NewFile.find_by_id(fid)
    pdfinsight_reader = PdfinsightReader.from_path(file.pdfinsight_path(abs_path=True))
    results = final_results[:limit]
    for result in results:
        _, element = pdfinsight_reader.find_element_by_index(result["index"])
        if not element:
            continue
        result["page"] = element["page"]
        result["outline"] = element["outline"]
        result["class"] = element["class"]

    return results
