from remarkable.services.retrieval import retrieval_by_keywords

CATEGORY_RULES = {
    "E6-Source of scenarios": {
        "IPCC": ["IPCC"],
        "IEA": ["IEA"],
        "NGFS": ["NGFS"],
    },
    "E8-Categories of scope 3 emissions": {
        "Cat 1": ["Purchased goods and services"],
        "Cat 2": ["Capital goods"],
        "Cat 3": [
            "Fuel- and energy-related activities not included in scope 1 greenhouse gas emissions or scope 2 greenhouse gas emissions",
            "Fuel- and energy-related activities not included in Scope 1 or 2",
        ],
        "Cat 4": ["Upstream transportation and distribution"],
        "Cat 5": ["Waste generated in operations"],
        "Cat 6": ["Business travel"],
        "Cat 7": ["Employee commuting"],
        "Cat 8": ["Upstream leased assets"],
        "Cat 9": ["Downstream transportation and distribution"],
        "Cat 10": ["Processing of sold products"],
        "Cat 11": ["Use of sold products"],
        "Cat 12": ["End-of-life treatment of sold products"],
        "Cat 13": ["Downstream leased assets"],
        "Cat 14": ["Franchises"],
        "Cat 15": ["Investments"],
    },
}


async def get_category_candidates(fid):
    result = {}
    for rule in CATEGORY_RULES:
        for key, values in CATEGORY_RULES[rule].items():
            # 初步定位只需要完整的元素块
            elements = await retrieval_by_keywords(fid, rule_description=key, sub_rules=values, element_sequence=0)
            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7123#note_721275
            # TODO rrf score 归一化处理可以直接使用 rrf_score 作为展示分数,此处重排序可以删除
            elements.sort(key=lambda x: x["score"], reverse=True)
            result[f"{rule}_{key}"] = elements
            if rule == "E8-Categories of scope 3 emissions":
                result[f"E9-Scope 3 emissions data by categories_{key}"] = elements

    return result


if __name__ == "__main__":
    import asyncio

    asyncio.run(get_category_candidates(71587))
