import logging
import re
import tempfile
from copy import deepcopy
from dataclasses import dataclass
from datetime import datetime
from functools import wraps
from operator import itemgetter
from typing import Callable, List

import dateutil.parser
import peewee
import polars as pl
from dateutil.relativedelta import relativedelta
from pydantic import BaseModel, Field

from remarkable.common.common import get_financial_year
from remarkable.common.constants import TableType
from remarkable.common.exceptions import PdfInsightNotFound
from remarkable.common.pattern import PatternCollection
from remarkable.common.util import clean_txt, mm_notify, standard_stock, stream_download
from remarkable.config import get_config
from remarkable.db import pw_db
from remarkable.models.agm_meta import AGMMeta
from remarkable.models.director import Director
from remarkable.models.embedding import Embedding
from remarkable.models.hkex_file import HKEXFile
from remarkable.models.new_file import NewFile
from remarkable.pdfinsight.parser import parse_table
from remarkable.pdfinsight.reader import PdfinsightReader
from remarkable.predictor.common_pattern import R_DATES
from remarkable.predictor.eltype import ElementClassifier, ElementType
from remarkable.predictor.models.base_model import BaseModel as PredictorBaseModel
from remarkable.services.chatgpt import OpenAIClient
from remarkable.services.retrieval import retrieval_by_keywords


async def fetch_and_update_directors():
    url = get_config("director_list_url")
    with tempfile.NamedTemporaryFile(suffix=".xlsx") as tmp_fp:
        await stream_download(url, tmp_fp.name)
        df = pl.read_excel(tmp_fp.name, has_header=True)

        async with pw_db.atomic():
            await pw_db.execute(Director.delete())

            directors = []
            for row in df.iter_rows(named=True):
                appointment_date = row["Appointment Date (yyyy-mm-dd)"]
                resignation_date = row["Resignation Date (yyyy-mm-dd)"]
                director = {
                    "stock_code": row["Stock Code"],
                    "english_company": row["Listed Company's English Name"],
                    "chinese_company": row["Listed Company's Chinese Name"],
                    "industry": row["Industry"],
                    "listing_status": row["Listing Status"],
                    "english_name": row["Director's English Name"],
                    "chinese_name": row["Director's Chinese Name"],
                    "capacity": row["Capacity"],
                    "position": row["Position"],
                    "appointment_date": datetime.strptime(appointment_date, "%Y-%m-%d").date()
                    if appointment_date
                    else None,
                    "resignation_date": datetime.strptime(resignation_date, "%Y-%m-%d").date()
                    if resignation_date
                    else None,
                }
                directors.append(director)
            await Director.bulk_insert(directors)


R_DATE_PATTERNS = R_DATES + [
    r"\b\d{1,2}(\w|,)? \w{3,9},? \d{4}",
    r"\b\w{3,9} \d{1,2},? \d{4}",
    r"\b\d{1,2}(th)? day of \w{3,9} \d{4}",
]

P_BAD_DATE = PatternCollection([r"\d{1,2}J une,? \d{4}"], re.I)
P_HOLD_AT = re.compile(r"\bh[eo]ld (at|on)\b")


class ConveningTimePatterns:
    R_FIRST_PAGE_PATTERN = [
        *[
            rf"(A|the) notice convening the.*?(Annual General Meeting|AGM).*?\b(?P<dst>{r})\b.*(this circular|was published|set out)"
            for r in R_DATE_PATTERNS
        ],
        *[
            rf"(A|the) supplemental notice of (Annual General Meeting|AGM).*?\b(?P<dst>{r})\b.*this circular"
            for r in R_DATE_PATTERNS
        ],
        *[rf"notice of the (Annual General Meeting|AGM).*?\b(?P<dst>{r})\b.*this circular" for r in R_DATE_PATTERNS],
        *[
            rf"It is proposed that.*?(Annual General Meeting|AGM).*?\b(?P<dst>{r})\b.*be proposed"
            for r in R_DATE_PATTERNS
        ],
        *[rf"(Annual General Meeting|AGM).*will be held.*?\b(?P<dst>{r})\b" for r in R_DATE_PATTERNS],
        *[rf"(Annual General Meeting|AGM).*?to be held.*?(?P<dst>{r})" for r in R_DATE_PATTERNS],
        *[rf"conven(ed?|ing) the (Annual General Meeting|AGM).*?(?P<dst>{r})" for r in R_DATE_PATTERNS],
    ]
    P_FIRST_PAGE = PatternCollection(R_FIRST_PAGE_PATTERN, re.I)

    R_NOTICE_PATTERN = [
        rf"^NOTICE IS HEREBY GIVEN THAT.*annual general meeting.*?(?<!ended\s)\b(?P<dst>{r})\b.*[-—－–‐:.]$"
        for r in R_DATE_PATTERNS
    ]
    P_NOTICE = PatternCollection(R_NOTICE_PATTERN, re.I)

    R_DEFINITION = [
        rf"^[“\"‘\'].*(Annual General Meeting|AGM).*(held|convened).*?\b(?P<dst>{r})\b" for r in R_DATE_PATTERNS
    ]
    P_DEFINITION = PatternCollection(R_DEFINITION, re.I)

    R_FULL = [
        # https://www1.hkexnews.hk/listedco/listconews/sehk/2025/0331/2025033101562.pdf
        *[
            rf"^For.*?year['‘’]s (Annual General Meeting|AGM).*?returning to.*?on.*?(?P<dst>{r})\b"
            for r in R_DATE_PATTERNS
        ],
        # https://www1.hkexnews.hk/listedco/listconews/sehk/2024/0508/2024050800662.pdf
        *[rf"(Annual General Meeting|AGM).*?to be held.*?(?P<dst>{r})" for r in R_DATE_PATTERNS],
    ]
    P_FULL = PatternCollection(R_FULL, re.I)


def log_strategy(func):
    @wraps(func)
    def wrapper(self, *args, **kwargs):
        strategy_name = func.__name__
        try:
            result = func(self, *args, **kwargs)
            status = "succeeded" if result else "found no match"
            logging.info(f"Strategy {strategy_name} {status}")
            return result
        except Exception as e:
            logging.error(f"Strategy {strategy_name} failed with error: {str(e)}")
            raise

    return wrapper


@dataclass
class SearchStrategy:
    name: str
    pattern: PatternCollection
    element_filter: Callable
    get_elements: Callable
    priority: int = 0


@dataclass
class ConveningDate:
    date: datetime
    text: str
    matcher: re.Match
    element_class: ElementType = None
    index: int = None
    row_index: int | None = None
    chars: list | None = None

    def __str__(self):
        return f"{self.date}"


class DateExtractionResult(BaseModel):
    date: str | None = None


class ConveningDateExtractor:
    def __init__(
        self,
        fid: str,
        notice_elements: list,
        definition_elements: list,
        full_elements: list,
        *,
        pdfinsight: PdfinsightReader = None,
        pdfinsight_path: str = None,
        notify: bool = True,
        stock_code: str = None,
    ):
        self.fid = fid
        self.notice_elements = notice_elements
        self.definition_elements = definition_elements
        self.full_elements = full_elements
        self.pdfinsight_path = pdfinsight_path
        self.pdfinsight = pdfinsight
        self.notify = notify
        self.stock_code = stock_code

        self.strategies = [
            SearchStrategy(
                name="first_page",
                pattern=ConveningTimePatterns.P_FIRST_PAGE,
                element_filter=ElementClassifier.like_paragraph,
                get_elements=self.get_first_page_elements,
                priority=1,
            ),
            SearchStrategy(
                name="notice",
                pattern=ConveningTimePatterns.P_NOTICE,
                element_filter=ElementClassifier.like_paragraph,
                get_elements=self.get_notice_elements,
                priority=2,
            ),
            SearchStrategy(
                name="definition",
                pattern=ConveningTimePatterns.P_DEFINITION,
                element_filter=ElementClassifier.is_table,
                get_elements=self.get_definition_elements,
                priority=3,
            ),
            SearchStrategy(
                name="full",
                pattern=ConveningTimePatterns.P_FULL,
                element_filter=ElementClassifier.like_paragraph,
                get_elements=self.get_full_elements,
                priority=4,
            ),
        ]
        self.strategies.sort(key=lambda x: x.priority)

    def initialize(self) -> bool:
        # todo  need  refactor
        if self.pdfinsight:
            return True
        if not self.pdfinsight_path:
            return False
        try:
            self.pdfinsight = PdfinsightReader(self.pdfinsight_path)
            return True
        except PdfInsightNotFound:
            logging.error(f"pdfinsight {self.pdfinsight_path} not found")
            return False

    @staticmethod
    def llm_parse_date(text: str) -> datetime | None:
        messages = [
            {
                "role": "system",
                "content": "You are a helpful assistant that extracts meeting times from text. Return only the date and time in YYYY-MM-DD format, or empty string if not found.",
            },
            {"role": "user", "content": text},
        ]
        openai_client = OpenAIClient()
        try:
            llm_res = openai_client.send_message(messages, response_format=DateExtractionResult)
            return dateutil.parser.parse(llm_res.parsed.date)
        except Exception as e:
            logging.exception(e)
        return None

    def parse_time_from_text(self, text: str, pattern: PatternCollection) -> ConveningDate | None:
        clean_text = clean_txt(text)
        if matcher := pattern.nexts(clean_text):
            convening_date = matcher.group("dst")
            try:
                parsed_date = dateutil.parser.parse(convening_date)
                return ConveningDate(
                    date=parsed_date,
                    text=convening_date,
                    matcher=matcher,
                )
            except (ValueError, TypeError):
                if P_BAD_DATE.nexts(convening_date):
                    try:
                        parsed_date = dateutil.parser.parse(clean_txt(convening_date, remove_blank=True))
                        return ConveningDate(
                            date=parsed_date,
                            text=convening_date,
                            matcher=matcher,
                        )
                    except (ValueError, TypeError) as e:
                        logging.error(f"Failed to parse date '{convening_date}': {str(e)}")
                if res_by_llm := self.llm_parse_date(clean_text):
                    return ConveningDate(
                        date=res_by_llm,
                        text=convening_date,
                        matcher=matcher,
                    )
                return None
        return None

    def get_first_page_elements(self) -> List:
        elements = self.pdfinsight.page_element_dict.get(0, [])[::-1]
        logging.info(f"Found {len(elements)} elements on first page")
        return elements

    def get_notice_elements(self) -> List:
        return self.notice_elements

    def get_definition_elements(self) -> List:
        elements = self.definition_elements
        if not elements:
            logging.info("No definition section found")
            return []

        definition_element = elements[0]
        _, element = self.pdfinsight.find_element_by_index(definition_element["index"])
        if not element:
            logging.info("Definition element not found")
            return []

        page_elements = self.pdfinsight.page_element_dict.get(element["page"], [])
        logging.info(f"Found {len(page_elements)} elements in definition section")
        return page_elements

    def get_full_elements(self) -> List:
        return self.full_elements

    @log_strategy
    def process_table_element(self, element) -> ConveningDate | None:
        try:
            parsed_table = parse_table(element, tabletype=TableType.TUPLE.value, pdfinsight_reader=self.pdfinsight)
            for idx, row in enumerate(parsed_table.rows):
                text = clean_txt(" ".join(i.text for i in row))
                if result := self.parse_time_from_text(text, ConveningTimePatterns.P_DEFINITION):
                    logging.info(f"Found date in table: {result}")
                    result.element_class = ElementType.TABLE_KV
                    result.index = element["index"]
                    result.row_index = idx
                    return result
        except Exception as e:
            logging.error(f"Error processing table element: {str(e)}")
        return None

    @log_strategy
    def search_with_strategy(self, strategy: SearchStrategy) -> ConveningDate | None:
        for element in strategy.get_elements():
            ele_type, element = self.pdfinsight.find_element_by_index(
                element.index if hasattr(element, "index") else element["index"]
            )
            if not element or not strategy.element_filter(element):
                continue

            if strategy.element_filter == ElementClassifier.is_table:
                if result := self.process_table_element(element):
                    return result
            else:
                if result := self.parse_time_from_text(element.get("text", ""), strategy.pattern):
                    logging.info(f"Found date with strategy {strategy.priority=} {strategy.name=}: <{result}>")
                    result.element_class = ElementType.PARAGRAPH
                    result.index = element["index"]
                    return result
        return None

    def extract_convening_date(self) -> ConveningDate | None:
        if not self.initialize():
            return None

        for strategy in self.strategies:
            logging.info(f"Trying strategy: {strategy.name} (priority: {strategy.priority})")
            if result := self.search_with_strategy(strategy):
                return result
        if result := self.get_convening_date_from_merged_para():
            return result
        if self.notify:
            logging.info(f"No convening time found with any strategy {self.fid=}")
            tail = get_config("notification.esg_need_label_tail")
            msg = f"""No convening time found, please check:
```shell
agm_file_id: {self.fid}
stock_code: {self.stock_code}
```
{get_config("web.scheme")}://{get_config("web.domain")}/#/search?fileid={self.fid}
"""
            mm_notify(msg, tags=("parse_convening_time",), tail=tail, error=True)
        return None

    def get_convening_date_from_merged_para(self):
        elements = self.get_first_page_elements()
        merged_paras = []
        for element in elements:
            ele_type, element = self.pdfinsight.find_element_by_index(
                element.index if hasattr(element, "index") else element["index"]
            )
            if not element:
                continue
            if element["class"] == "PARAGRAPH" and P_HOLD_AT.search(element["text"]):
                merged_paras.append(element)
                pre_paras = self.pdfinsight.find_elements_near_by(
                    element["index"], amount=2, step=-1, aim_types=["PARAGRAPH", "FOOTNOTE"]
                )
                next_paras = self.pdfinsight.find_elements_near_by(
                    element["index"], amount=2, step=1, aim_types=["PARAGRAPH", "FOOTNOTE"]
                )
                merged_paras.extend(pre_paras)
                merged_paras.extend(next_paras)
        merged_paras.sort(key=itemgetter("index"))
        text = " ".join(para["text"] for para in merged_paras)
        chars = []
        for para in merged_paras:
            last_char = deepcopy(para["chars"][-1])
            last_char["text"] = " "
            chars.extend(para["chars"])
            chars.append(last_char)
        # chars = list(chain.from_iterable(para["chars"] for para in merged_paras))
        if result := self.parse_time_from_text(text, ConveningTimePatterns.P_FIRST_PAGE):
            matcher = result.matcher
            value = matcher.groupdict().get("dst", None)
            if not value:
                return None
            span = matcher.span()
            result.chars = PredictorBaseModel.get_chars(text, value, chars, span)
            result.element_class = ElementType.PARAGRAPH
            result.index = merged_paras[2]["index"]
            return result
        return None


def parse_convening_date(
    fid: str,
    notice_elements: list,
    definition_elements: list,
    full_elements: list,
    stock_code: str = None,
    pdfinsight_path: str = None,
) -> datetime | None:
    logging.info(f"Starting to parse convening time for file: {fid}")
    extractor = ConveningDateExtractor(
        fid,
        notice_elements,
        definition_elements,
        full_elements,
        stock_code=stock_code,
        pdfinsight_path=pdfinsight_path,
    )
    try:
        result = extractor.extract_convening_date()
        logging.info(f"Finished parsing convening time: {result}")
        if result:
            return result.date
        return None
    except Exception as e:
        logging.error(f"Error parsing convening time: {str(e)}")
        return None


async def get_notice_elements(fid) -> List:
    elements = await Embedding.regex_search([fid], "^NOTICE IS HEREBY GIVEN THAT", 1)
    logging.info(f"Found {len(elements)} notice elements")
    if elements:
        logging.info(f"{elements[0].get('text', '')}")
    return elements


async def get_definition_elements(fid) -> List:
    elements = await Embedding.regex_search([fid], "^DEFINITIONS$", 1, desc=False)
    return elements


async def get_full_text_elements(fid) -> List:
    rule_description = "For this year AGM we will be returning to Convene on Thursday 8 May 2025"
    elements = await retrieval_by_keywords(
        fid,
        rule_description=rule_description,
        sub_rules=[],
        limit=50,
        only_sub_element=False,
    )
    convening_reg = [r"For this year['‘’]s AGM.*?returning to convene"]
    r_convening = "|".join(convening_reg)
    elements_by_regex = await Embedding.regex_search([fid], r_convening, ignore_case=True)
    elements.extend(elements_by_regex)
    return elements


async def find_file_convening_date(fid, stock_code):
    file = await NewFile.find_by_id(fid)
    if not file or not file.pdfinsight_path():
        logging.warning(f"file or pdfinsight not found, when extract convening_date {fid=}")
        return None
    notice_elements = await get_notice_elements(fid)
    definition_elements = await get_definition_elements(fid)
    full_elements = await get_full_text_elements(fid)
    return parse_convening_date(
        file.id,
        stock_code=stock_code,
        pdfinsight_path=file.pdfinsight_path(abs_path=True),
        notice_elements=notice_elements,
        definition_elements=definition_elements,
        full_elements=full_elements,
    )


async def set_agm_meta(file_id: int, stock_code=None):
    if not stock_code:
        stock_code = await pw_db.scalar(
            HKEXFile.select(HKEXFile.stock_code).where(HKEXFile.fid == file_id, HKEXFile.type == "AGM")
        )
        if not stock_code:
            logging.warning(f"set_agm_meta: No stock_code found for {file_id=}")
            mm_notify(f"No stock_code found for {file_id=}", tags=("set_agm_meta",), error=True)
            return
    stock_code = standard_stock(stock_code)
    convening_date = await find_file_convening_date(file_id, stock_code)
    if convening_date:
        report_year = convening_date.date().year
        agm_meta = {
            "agm_fid": file_id,
            "stock_code": standard_stock(stock_code),
            "report_year": report_year,
            "convening_date": convening_date,
        }
        await AGMMeta.insert_or_update(conflict_target=[AGMMeta.agm_fid], **agm_meta)
    else:
        logging.warning(f"No convening_date found for {stock_code=}, {file_id=}")


class B99AdditionData(BaseModel):
    last_updated: datetime
    year_start: datetime
    published_at: datetime
    items: list[dict] = Field(default_factory=list)

    def model_dump(self, **kwargs):
        data = super().model_dump(**kwargs)
        for field in ["last_updated", "year_start", "published_at"]:
            if data.get(field):
                data[field] = int(data[field].timestamp())

        for item in data["items"]:
            for date_field in ["appointment_date", "resignation_date"]:
                if value := item.get(date_field):
                    item[date_field] = int(datetime.combine(value, datetime.min.time()).timestamp())
        return data


async def find_new_director_for_b99(file_meta) -> B99AdditionData:
    year_start, _ = get_financial_year(file_meta.fid, file_meta.year_end, "%d %b %Y")
    year_start = datetime.strptime(year_start, "%Y-%m-%d")
    published_at = file_meta.published_at
    cond = [
        Director.stock_code == file_meta.stock_code,
        Director.appointment_date >= year_start,
        Director.appointment_date <= published_at,
    ]
    new_directors = list(await pw_db.execute(Director.select().where(*cond).dicts()))
    return B99AdditionData(
        last_updated=datetime.now(),
        year_start=year_start,
        published_at=published_at,
        items=new_directors,
    )


async def find_ined_director(fid: int, stock_code: str):
    convening_date = await pw_db.scalar(AGMMeta.select(AGMMeta.convening_date).where(AGMMeta.agm_fid == fid))
    if not convening_date:
        logging.warning(f"not found convening_date {fid=}")
        return {"all_ined_gt_9": False, "ined": [], "ined_companies": [], "director": [], "new_director": []}
    cond = (Director.stock_code == stock_code) & (
        (Director.resignation_date.is_null()) | (Director.resignation_date >= convening_date)
    )
    directors = list(await pw_db.execute(Director.select().where(cond)))
    cond &= Director.capacity.in_(("Independent Non Executive Director - A/F", "Independent Non Executive Director"))
    director_query = cond & (Director.appointment_date <= convening_date)
    ined_data = list(await pw_db.execute(Director.select().where(director_query)))
    cond &= (
        peewee.fn.EXTRACT(
            peewee.SQL(f"YEAR FROM AGE('{convening_date}', appointment_date)"),
        )
        >= 9
    )
    ined_ge9_data = list(await pw_db.execute(Director.select().where(cond)))
    ined_companies = []
    for ined in ined_data:
        ined_companies.append(
            {
                "director": ined,
                "companies": list(
                    await pw_db.execute(
                        Director.select().where(
                            Director.english_name == ined.english_name,
                            (Director.resignation_date.is_null() | (Director.resignation_date >= convening_date)),
                        )
                    )
                ),
            }
        )

    old_ined_data = [director for director in ined_data if director.appointment_date < convening_date]
    return {
        "all_ined_gt_9": bool(old_ined_data) and old_ined_data == ined_ge9_data,
        "ined": ined_ge9_data,  # 任期超过9年的INED
        "ined_companies": ined_companies,
        "directors": directors,
        "new_director": await find_new_director(fid, stock_code),
    }


async def build_query_new_director(current_convening_date, last_convening_date, stock_code, capacity=None):
    cond = Director.stock_code == stock_code
    if capacity:
        if capacity == "Independent Non Executive Director":
            cond &= Director.capacity.in_(
                ("Independent Non Executive Director - A/F", "Independent Non Executive Director")
            )
        else:
            cond &= Director.capacity == capacity
    cond &= Director.resignation_date.is_null() | (Director.resignation_date >= current_convening_date)
    subquery = Director.select(
        Director.stock_code,
        Director.english_name,
        Director.appointment_date,
        Director.resignation_date,
        peewee.fn.LAG(Director.resignation_date)
        .over(partition_by=[Director.stock_code, Director.english_name], order_by=[Director.appointment_date])
        .alias("previous_resignation_date"),
    ).alias("director_with_pre_resignation_date")
    cte = (
        Director.select(
            subquery.c.stock_code,
            subquery.c.english_name,
            peewee.fn.MAX(subquery.c.appointment_date).alias("last_appointment_date"),
        )
        .from_(subquery)
        .where(
            (subquery.c.previous_resignation_date.is_null())
            | (subquery.c.appointment_date - subquery.c.previous_resignation_date > 1)
        )
        .group_by(subquery.c.stock_code, subquery.c.english_name)
        .cte("cte")
    )
    cond &= cte.c.last_appointment_date <= current_convening_date
    cond &= cte.c.last_appointment_date > last_convening_date
    query = (
        Director.select()
        .join(
            cte,
            on=((Director.english_name == cte.c.english_name) & (Director.stock_code == cte.c.stock_code)),
        )
        .where(cond)
        .with_cte(cte)
    )
    return query


async def find_new_director(fid: int, stock_code: str):
    query = AGMMeta.select().where(AGMMeta.agm_fid == fid)
    current_agm_meta = await pw_db.first(query)
    if not current_agm_meta:
        raise ValueError(f"not found agm_meta {fid=}")
    current_convening_date = current_agm_meta.convening_date
    last_convening_date = await pw_db.scalar(
        AGMMeta.select(AGMMeta.convening_date)
        .where(
            AGMMeta.stock_code == current_agm_meta.stock_code,
            AGMMeta.convening_date < current_agm_meta.convening_date,
        )
        .order_by(AGMMeta.convening_date.desc())
        .limit(1)
    )
    if not last_convening_date:
        last_convening_date = current_agm_meta.convening_date - relativedelta(years=1)
        logging.warning(f"not found last_convening_date {fid=}")
    query = await build_query_new_director(current_convening_date, last_convening_date, stock_code)
    new_directors = list(await pw_db.execute(query))
    return new_directors


async def main(agm_fid=None):
    cond = HKEXFile.type == "AGM"
    if agm_fid:
        cond &= HKEXFile.fid == agm_fid
    agm_hkex_files = await pw_db.execute(HKEXFile.select().where(cond))
    for hkex_file in agm_hkex_files:
        await set_agm_meta(hkex_file.fid, hkex_file.stock_code)


if __name__ == "__main__":
    text = "Notice convening the Annual General Meeting of China International Capital Corporation Limited to be held at Meeting Rooms 3004 and 3005, 30/F, China Life Finance Centre, No. 23 Zhenzhi Road, Chaoyang District, Beijing, the PRC on Friday, June 27, 2025 at 2:30 p.m. is set out on pages 10 to 12 of this circular."
    for index, pattern in enumerate(ConveningTimePatterns.R_FIRST_PAGE_PATTERN):
        if matched := re.compile(pattern, re.I).search(text):
            print(index, matched.group("dst"))
            print(pattern)
