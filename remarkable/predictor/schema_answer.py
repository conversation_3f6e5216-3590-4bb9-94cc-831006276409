# -*- coding: utf-8 -*-
import json
import logging
from collections import defaultdict
from copy import deepcopy
from dataclasses import dataclass
from functools import cached_property
from itertools import groupby
from typing import Any, Dict, List, Literal

import numpy as np
from interdoc.element import Cell, ChildCell
from sklearn.cluster import KMeans

from remarkable.common.common import real_all
from remarkable.common.util import box_in_box, clean_txt, compact_dumps, outline_to_box, split_chars
from remarkable.plugins.fileapi.predict import get_bound_box
from remarkable.predictor.mold_schema import MoldSchema, SchemaItem


def cluster_boxes_by_kmeans(
    boxes: list[tuple[int, int, int, int]],
) -> list[list[tuple[int, int, int, int]]]:
    # 元素块在分栏时需要分开画框,这里传入所有行的box,将其进行分类,分为两类

    # 将框数据转换为NumPy数组,并添加索引
    boxes_with_index = [(i, x1, y1, x2, y2) for i, (x1, y1, x2, y2) in enumerate(boxes)]
    boxes_array = np.array(boxes_with_index)

    # 计算每个框的中心点坐标
    box_centers = np.array([[(x1 + x2) / 2, (y1 + y2) / 2] for _, x1, y1, x2, y2 in boxes_array])

    # 使用K-Means算法进行聚类,设置聚类数为2
    kmeans_result = KMeans(n_clusters=2, random_state=0, n_init="auto").fit(box_centers)

    labels = kmeans_result.labels_

    #  NOTE: 这里认为多个框最多只能分成两组是有问题的 https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7167#note_731513
    # 处理标签，确保只有一个变化点，从而只有两组连续的值
    # 例如：[1,0,0,0,0,0,1] 应该变成 [1,0,0,0,0,0,0]
    if len(labels) > 2:
        # 找到第一个变化点
        change_index = -1
        for i in range(1, len(labels)):
            if labels[i] != labels[i - 1]:
                change_index = i
                break

        # 如果找到了变化点
        if change_index > 0:
            # 获取变化点位置的值
            change_value = labels[change_index]

            # 查找第二个变化点
            second_change_index = -1
            for i in range(change_index + 1, len(labels)):
                if labels[i] != labels[i - 1]:
                    second_change_index = i
                    break

            # 如果找到了第二个变化点，将从第二个变化点开始的所有值都设置为第一个变化点的值
            if second_change_index > 0:
                for i in range(second_change_index, len(labels)):
                    labels[i] = change_value

    # 根据索引对每一类中的元素重新排序
    sorted_cluster0 = boxes_array[labels == 0]
    sorted_cluster1 = boxes_array[labels == 1]
    sorted_cluster0_order = sorted_cluster0[0][0]
    sorted_cluster1_order = sorted_cluster1[0][0]
    sorted_cluster0 = [(x1, y1, x2, y2) for i, x1, y1, x2, y2 in sorted_cluster0]
    sorted_cluster1 = [(x1, y1, x2, y2) for i, x1, y1, x2, y2 in sorted_cluster1]
    if sorted_cluster0_order < sorted_cluster1_order:
        return [sorted_cluster0, sorted_cluster1]
    else:
        return [sorted_cluster1, sorted_cluster0]


def split_continued_boxes(
    line_boxes: list[tuple[int, int, int, int]],
) -> list[list[tuple[int, int, int, int]]]:
    """
    https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/2694
    问题文档id=62536，搜索规则：G(a)-Adoption of model code
    若要定位的element为特殊段落： 文档分左右两块，左下角和右上角是一个段落，则对line_boxes做拆分

    补充用例
    http://100.64.0.105:55647/#/project/remark/244579?treeId=8134&fileId=67037&schemaId=28&projectId=17&schemaKey=E%28d%29%28ii%29-1-Policy%20for%20remuneration%20of%20executive%20directors&page=125
    """
    if len(line_boxes) == 2:
        if abs(line_boxes[0][0] - line_boxes[1][0]) < 1 and abs(line_boxes[-1][2] - line_boxes[-1][2]) < 1:
            # 只有两行且第一个字符的left和最后一个字符的right相差小于1, 才可以画成一个框
            return [line_boxes]
        else:
            return [line_boxes[:1], line_boxes[1:]]
    # 计算所有box中<left> <right>的标准差
    left_std = np.std([box[0] for box in line_boxes])
    right_std = np.std([box[2] for box in line_boxes])
    # 遇到标准差大的连续的box, 这里可以修改标准差阈值 跨栏的元素块的标准差大概在100左右
    # 正常的段落的标准差一般小于1
    # 下面设置为20，是为了兼容as_follow中的分论点，前面有<黑点>时，标准差会变大
    # •made recommendations to the Board on the remuneration packages of a non-executive director and an executive director for their new term of appointments;
    if left_std > 20 or right_std > 20:
        return cluster_boxes_by_kmeans(line_boxes)
    # 没有连续段落
    return [line_boxes]


class AnswerResult:
    def __init__(self, text=None, flag=None):
        self._text = text
        # 设置标记，用于辅助判断
        self._flag = flag

    def to_answer(self):
        raise NotImplementedError

    @property
    def text(self):
        return self._text or ""

    @property
    def flag(self):
        return self._flag

    @flag.setter
    def flag(self, value):
        self._flag = value

    @staticmethod
    def create_box(page, line_box, text):
        return {
            "page": page,
            "box": outline_to_box(line_box),
            "text": text,
        }

    def __str__(self):
        return self.text

    @property
    def elements(self):
        raise NotImplementedError


class FinalResult(AnswerResult):
    """
    从preset_answer中获取到的答案 不需要再经过转换
    """

    def __init__(self, data, flag=None):
        text = "".join([i["text"] for i in data["boxes"]])
        super().__init__(text=text, flag=flag)
        self.data = data

    def to_answer(self):
        return self.data

    @property
    def elements(self):
        return []


class PageResult(AnswerResult):
    def __init__(self, page, outline, elements=None, text=None, flag=None):
        super().__init__(text=text, flag=flag)
        self.page = page
        self.outline = outline
        self.all_elements = elements

    def to_answer(self):
        answer = {
            "boxes": [
                {
                    "page": self.page,
                    "box": outline_to_box(self.outline),
                    "text": "",
                }
            ],
            "handleType": "wireframe",
        }
        return answer

    @property
    def elements(self):
        return self.all_elements


class OutlineResult(AnswerResult):
    """
    通过已知的外框生成答案
    """

    def __init__(self, page_box, text=None, element=None, origin_elements=None, display_text=None, flag=None):
        if text is None:
            text = "\n".join(i["text"] for i in page_box if i.get("text"))
        super().__init__(text=text, flag=flag)
        self.page_box = page_box
        if origin_elements:
            self.origin_elements = origin_elements
        else:
            self.origin_elements = []
            for page_item in page_box:
                self.origin_elements.extend(page_item.get("elements", []))
        if element:
            self.element = element
        elif self.origin_elements:
            self.element = self.origin_elements[0]
        self.display_text = display_text

    def __repr__(self):
        return f"OutlineResult({[e['index'] for e in self.elements]})"

    @property
    def text(self):
        return self._text or self.display_text or ""

    @property
    def index(self):
        return self.element["index"]

    def to_answer(self):
        answer = {"boxes": [], "handleType": "wireframe"}
        # http://100.64.0.105:55647/#/project/remark/244305?treeId=6307&fileId=67311&schemaId=28&projectId=43974&schemaKey=H(b)-Frequency%20of%20review%20and%20the%20period%20covered
        # 解决：文件67311的recommended answer只框选了右上，未框选左下

        for page_data in self.page_box:
            page_outline = page_data["outline"]
            if answer["boxes"] and any(
                box["page"] == page_data["page"] and box_in_box(page_outline, box["box"]) for box in answer["boxes"]
            ):
                continue
            answer["boxes"].append(self.create_box(page_data["page"], page_outline, page_data.get("text")))
        for element in self.origin_elements:
            if (merged_paragraph := element.get("page_merged_paragraph")) and (chars := merged_paragraph.get("chars")):
                merged_paragraph_answers = ParagraphResult(element, chars).to_answer()
                is_cross_column = len({item["page"] for item in merged_paragraph_answers["boxes"]}) == 1
                for box in merged_paragraph_answers["boxes"]:
                    # 已经添加过的不再添加
                    if any(box_in_box(box["box"], item["box"]) for item in answer["boxes"]):
                        continue
                    if box["page"] == element["page"]:
                        # 不跨页，有可能是跨栏
                        if is_cross_column:
                            # 跨栏元素块的右边部分 text为空
                            answer["boxes"].append({"box": box["box"], "page": box["page"], "text": ""})
                    else:
                        # 跨页元素块的第二部分 text为空
                        answer["boxes"].append({"box": box["box"], "page": box["page"], "text": ""})
        if self.text:
            answer["text"] = self.text
        if self.display_text:
            answer["text"] = self.display_text
        return answer

    @property
    def elements(self):
        return self.origin_elements or ([self.element] if self.element else [])


class ElementResult(AnswerResult):
    def __init__(self, element, text=None, flag=None):
        super().__init__(text=text, flag=flag)
        self.element = element

    def __hash__(self):
        return hash((self.index, self.text))

    def __eq__(self, other):
        if isinstance(other, ElementResult):
            return self.index == other.index and self.text == other.text
        return False

    def __repr__(self):
        return f"ElementResult({self.index}, {self.text})"

    def to_answer(self):
        raise NotImplementedError

    @property
    def index(self):
        return self.element["index"]

    @property
    def elements(self):
        return [self.element]


class TextResult(ElementResult):
    def __init__(self, element, chars, flag=None):
        super().__init__(element, flag=flag)
        self.chars = chars

    def __repr__(self):
        return f"TextResult({self.index}, {self.text})"

    @property
    def text(self):
        return "".join(i.get("text", "") for i in self.chars)

    def to_answer(self):
        raise NotImplementedError


class NoBoxResult(TextResult):
    def __init__(self, text, flag=None):
        super().__init__(element={}, chars=[], flag=flag)
        self._text = text

    def __repr__(self):
        return f"NoBoxResult({self.index}, {self.text})"

    @property
    def text(self):
        return self._text

    def to_answer(self):
        return {"boxes": [], "handleType": "wireframe"}


class CharResult(TextResult):
    def __init__(self, element, chars, display_text=None, start=None, end=None, flag=None):
        super().__init__(element, chars, flag=flag)
        self.chars = chars
        self.display_text = display_text
        self.start = start
        self.end = end

    def __hash__(self):
        return hash((self.index, self.start, self.end, self.text))

    def __eq__(self, other):
        if isinstance(other, CharResult):
            return (
                self.index == other.index
                and self.start == other.start
                and self.end == other.end
                and self.text == other.text
            )
        return False

    def __repr__(self):
        return f"CharResult({self.index}, {self.start}, {self.end}, {self.text})"

    def get_answer_boxes_by_chars(self, page, chars):
        answer_boxes = []
        lines = split_chars(chars)
        line_boxes = []
        for chars in lines:
            line_box = get_bound_box([char["box"] for char in chars])
            comp_text = "".join([char["text"] for char in chars])
            if not line_boxes:
                line_boxes.append(line_box)
                answer_boxes.append(self.create_box(page, line_box, comp_text))
            else:
                last_box = line_boxes[-1]
                # 比较上下两行 如果左右边界的大小不超过1 则合并两个box
                last_left = last_box[0]
                last_right = last_box[2]
                current_left = line_box[0]
                current_right = line_box[2]
                if abs(current_left - last_left) <= 1 and abs(current_right - last_right) <= 1:
                    line_boxes.pop()
                    last_answer_box = answer_boxes.pop()
                    new_box = (
                        min(last_left, current_left),
                        min(last_box[1], line_box[1]),
                        max(last_right, current_right),
                        max(last_box[3], line_box[3]),
                    )
                    current_text = last_answer_box["text"] + comp_text
                    line_boxes.append(new_box)
                    answer_boxes.append(self.create_box(page, new_box, current_text))
                else:
                    line_boxes.append(line_box)
                    answer_boxes.append(self.create_box(page, line_box, comp_text))
        return answer_boxes

    def to_answer(self):
        answer = {"boxes": [], "handleType": "wireframe"}
        page_chars = {}
        for char in self.chars:
            page_chars.setdefault(int(char["page"]), []).append(char)

        answer_boxes = []
        for page in sorted(page_chars):
            answer_boxes.extend(self.get_answer_boxes_by_chars(page, page_chars[page]))
        answer["boxes"] = answer_boxes
        if self.display_text:
            answer["text"] = self.display_text

        return answer


class TitleCharResult(CharResult):
    def __init__(self, element, chars=None, title_text="", flag=None):
        super().__init__(element, chars, flag=flag)
        self.chars = chars
        self.title_text = title_text

    def __repr__(self):
        return f"TitleCharResult({self.index}, {self.text or self.title_text})"

    def __hash__(self):
        return hash((self.index, self.text or self.title_text))

    def __eq__(self, other):
        if isinstance(other, TitleCharResult):
            return self.index == other.index and self.chars == other.chars and self.title_text == other.title_text
        return False

    @property
    def text(self):
        if self.chars:
            return "".join(i.get("text", "") for i in self.chars)
        return self.title_text


class ParagraphResult(TextResult):
    def __repr__(self):
        return f"ParagraphResult({self.index}, {self.text})"

    def to_answer(self):
        answer = {"boxes": [], "handleType": "wireframe"}
        chars_by_page = defaultdict(list)
        for char in self.chars:
            # 按页分组
            chars_by_page[int(char["page"])].append(char)
        for page, chars in chars_by_page.items():
            lines = split_chars(chars)
            line_boxes = [get_bound_box(char["box"] for char in line) for line in lines]
            for index, boxes in enumerate(split_continued_boxes(line_boxes)):
                box_data = get_bound_box(boxes)
                # 多个段落拼接，中间带上空格，同时text的box必须在box_data内
                text = (" " if index > 0 else "") + "".join(i["text"] for i in chars if box_in_box(i["box"], box_data))
                answer["boxes"].append(self.create_box(page, box_data, text=text))
        # if para_res.confirm:
        #     res['confirm'] = True
        return answer

        # NOTE: http://100.64.0.105:55647/#/project/remark/303205?projectId=17&treeId=6307&fileId=69681&schemaId=31&page=68 index 812
        # 这个元素块每一行最后都有一个空格 但是元素块的框没有包含空格的框， 所以导致不属于 merged_outline的char被分配到 merged_outline中
        # 导致画出来奇怪的框

        # for page, chars in chars_by_page.items():
        #     group_by_merged_boxes = {
        #         "master": [],  # 合并段落第一个
        #         "merged": [],  # 合并段落第二个
        #         "others": [],  # 这个不应该有值 但是却有值
        #     }
        #     if page_merged_paragraph := self.element.get("page_merged_paragraph"):
        #         merged_outline = page_merged_paragraph["outline"]
        #         for char in chars:
        #             if box_in_box(char["box"], merged_outline):
        #                 group_by_merged_boxes["merged"].append(char)
        #             elif box_in_box(char["box"], self.element['oultine']):
        #                 group_by_merged_boxes["master"].append(char)
        #             eles:
        #                 group_by_merged_boxes["others"].append(char)
        #     else:
        #         group_by_merged_boxes["master"] = chars
        #     for independent_chars in group_by_merged_boxes.values():
        #         if not independent_chars:
        #             continue
        #         lines = split_chars(independent_chars)
        #         line_boxes = [get_bound_box(char["box"] for char in line) for line in lines]
        #         for index, boxes in enumerate(split_continued_boxes(line_boxes)):
        #             box_data = get_bound_box(boxes)
        #             # 多个段落拼接，中间带上空格，同时text的box必须在box_data内
        #             text = (" " if index > 0 else "") + "".join(
        #                 i["text"] for i in independent_chars if box_in_box(i["box"], box_data)
        #             )
        #             answer["boxes"].append(self.create_box(page, box_data, text=text))


class TableResult(ElementResult):
    def __init__(self, element, parsed_table=None, display_text=None, flag=None):
        super().__init__(element, text=None, flag=flag)
        self._parsed_table = parsed_table
        self.display_text = display_text

    def __repr__(self):
        return f"TableResult({self.index})"

    @property
    def cells(self):
        return list(self.element["cells"])

    @property
    def parsed_cells(self):
        if not self._parsed_table:
            return []
        return self._parsed_table.body

    @property
    def parsed_table(self):
        return self._parsed_table

    @property
    def text(self) -> str:
        if self.display_text:
            return self.display_text
        if self._parsed_table:
            return self.get_cells_text(self._parsed_table.body)
        answer = self.to_answer()
        return "|".join([box["text"] for box in answer.get("boxes", []) if box.get("text")])

    def to_answer(self):
        answer = {"boxes": [], "handleType": "wireframe"}
        # 按页画框（考虑跨页合并表格）
        page_cells = defaultdict(list)
        for cell_id in self.cells:
            cell = self.element["cells"].get(cell_id, {})
            page_cells[cell["page"]].append((int(cell_id.split("_")[0]), cell))
        if not page_cells:
            logging.warning("TableResult has no valid cells.")
            return answer
        for page, row_idx_cells in page_cells.items():
            # 文本按行分组
            row_texts, all_chars = defaultdict(list), []
            for row_idx, cell in row_idx_cells:
                cell_chars = cell.get("chars") or []
                all_chars.extend(cell_chars)
                row_texts[row_idx].append(clean_txt("".join(c["text"] for c in cell_chars)))
            line_box = get_bound_box([char["box"] for char in all_chars])
            # 每行单元格之间用\t连接；多行之间用\n连接
            comp_text = "\n".join(["\t".join(texts) for _, texts in sorted(row_texts.items(), key=lambda x: x[0])])
            answer["boxes"].append(self.create_box(page, line_box, comp_text))
        if self.display_text:
            answer["text"] = self.display_text
        return answer

    @staticmethod
    def is_empty_cell(cell):
        cell_chars = cell.get("chars", [])
        if cell_chars:
            return False

        cell_box = cell.get("box", [])
        return cell_box and cell.get("page")

    @staticmethod
    def get_cells_text(cells):
        # 先按行分组，每行单元格之间用\t串起来
        # 再把各行用\n连接
        row_cells = defaultdict(list)
        for cell in cells:
            if cell.dummy:
                continue
            row_cells[cell.rowidx].append(cell)
        return "\n".join(
            "\t".join(cell.clean_text for cell in row_cells)
            for _, row_cells in sorted(row_cells.items(), key=lambda x: x[0])
        )


class TableCellsResult(TableResult):
    def __init__(
        self,
        element,
        cells: List["ParsedTableCell"],  # noqa
        merge_cells=False,
        group_type: Literal["row", "col"] | None = None,
        display_text: str | None = None,
        flag: str | None = None,
    ):
        super().__init__(element, display_text=display_text, flag=flag)
        self._parsed_cells = cells
        self.merge_cells = merge_cells
        self.group_type = group_type

    def __repr__(self):
        return f"TableCellsResult({self.index}, {self.cells})"

    def __hash__(self):
        return hash((self.index, tuple(self.cells)))

    def __eq__(self, other):
        if isinstance(other, TableCellsResult):
            return self.index == other.index and self.cells == other.cells
        return False

    @property
    def text(self):
        if self.display_text:
            return self.display_text
        # 先按行分组，每行单元格之间用\t串起来
        # 再把各行用\n连接
        return self.get_cells_text(self.parsed_cells)

    @property
    def cells(self) -> List[str]:
        if not self.parsed_cells:
            return []
        return [cell.raw_cell["index"] for cell in self.parsed_cells]

    @property
    def parsed_cells(self):
        return self._parsed_cells

    @parsed_cells.setter
    def parsed_cells(self, cells):
        self._parsed_cells = cells

    @property
    def parsed_table(self):
        if not self.parsed_cells:
            return None
        return self.parsed_cells[0].table

    def to_answer(self):
        answer = {"boxes": [], "handleType": "wireframe"}
        groups = self.group_cells_by_page()
        for page, page_cells in groups.items():
            if self.is_contiguous_cells(page_cells):
                contiguous_cells = [page_cells]
                rest_cells = None
            else:
                # cells按照连续性分组
                # http://100.64.0.105:55647/#/project/remark/269115?treeId=6438&fileId=70636&schemaId=18&projectId=6438&schemaKey=C1.3
                contiguous_cells, rest_cells = self.group_cells_by_contiguous(page_cells)
            if rest_cells:
                row_group, col_group = defaultdict(list), defaultdict(list)
                for cell in rest_cells:
                    row_group[cell.rowidx].append(cell)
                    col_group[cell.colidx].append(cell)

                if real_all([c.is_col_header and not c.is_row_header for c in self.parsed_cells if c.is_header]) or len(
                    col_group
                ) == len(list(groupby(enumerate(col_group), lambda x: x[1] - x[0]))):
                    # 按列分组条件：1. 没有行头，只有列头 2. col_group分组后长度等于本身长度，说明列中没有任何连续单元格
                    idx_attr, group_dict = "rowidx", col_group
                else:
                    idx_attr, group_dict = "colidx", row_group
                min_width, min_height = min(cell.width for cell in rest_cells), min(cell.height for cell in rest_cells)
                for _, sub_indices in groupby(enumerate(group_dict), lambda x: x[1] - x[0]):
                    # 按行/列分组后单元格可能不连续，要对每组中的单元格再分一次组
                    for _, by_idx in sub_indices:
                        for _, cell_indices in groupby(
                            enumerate([getattr(cell, idx_attr) for cell in group_dict[by_idx]]), lambda x: x[1] - x[0]
                        ):
                            neighbor_cells = []
                            cur_indices = [c[1] for c in list(cell_indices)]
                            for cell in group_dict[by_idx]:
                                if getattr(cell, idx_attr) not in cur_indices:
                                    continue
                                if cell.width != min_width or cell.height != min_height:
                                    # 有合并单元格时，不能与普通单元格合并到一起
                                    # http://100.64.0.105:55647/#/project/remark/?treeId=38114&fileId=80135&schemaId=15&projectId=17&schemaKey=B74&page=114 index=1107
                                    if neighbor_cells:
                                        contiguous_cells.append(neighbor_cells)
                                        neighbor_cells = []
                                    contiguous_cells.append([cell])
                                else:
                                    neighbor_cells.append(cell)
                            if neighbor_cells:
                                contiguous_cells.append(neighbor_cells)

            for num, cells_ in enumerate(contiguous_cells):
                if self.display_text:
                    text = self.display_text
                else:
                    cell_texts = ["".join(i["text"] for i in cell.raw_cell["chars"]) for cell in cells_]
                    text = ("" if num == 0 else "\t") + "\t".join(cell_texts)
                box_info = {
                    "page": page,
                    "box": outline_to_box(self.cells_outline(cells_)),
                    "text": text,
                }
                answer["boxes"].append(box_info)
        return answer

    def group_cells_by_contiguous(self, page_cells: list["ParsedTableCell"]) -> tuple[list[list], list]:  # noqa
        def group_cells(row_or_col_indices, idx_attr):
            groups_, un_conti_cells = [], []
            for _, sub_indices_ in groupby(enumerate(row_or_col_indices), lambda x: x[1] - x[0]):
                sub_indices = [c[1] for c in sub_indices_]
                sub_cells = [c for c in page_cells if getattr(c, idx_attr) in sub_indices]
                if len(sub_cells) > 1 and self.is_contiguous_cells(sub_cells):
                    groups_.append(sub_cells)
                else:
                    un_conti_cells.extend(sub_cells)
            return groups_, un_conti_cells

        row_indices, col_indices = [], []
        for cell in page_cells:
            if cell.rowidx not in row_indices:
                row_indices.append(cell.rowidx)
            if cell.colidx not in col_indices:
                col_indices.append(cell.colidx)
        table = page_cells[0].table
        count_rows = len(table.rows) - len(table.row_indices_of_header)
        count_cols = len(table.cols)
        # 存在连续行，尝试按列分组
        groups, un_grouped_cells = [], []
        if self.group_type == "row":
            groups, un_grouped_cells = group_cells(row_indices, "rowidx")
        elif self.group_type == "col":
            groups, un_grouped_cells = group_cells(col_indices, "colidx")
        else:
            # 判断行是否连续，仅用非标题行
            # http://100.64.0.105:55647/#/project/remark/296370?treeId=42602&fileId=71022&schemaId=18&projectId=42602&schemaKey=C1.3
            if count_rows == len(set(row_indices) - table.row_indices_of_header):
                groups, un_grouped_cells = group_cells(col_indices, "colidx")
            if not groups and count_cols == len(col_indices):
                row_groups, ungrouped_row_cells = group_cells(row_indices, "rowidx")
                if row_groups:
                    return row_groups, ungrouped_row_cells
        return groups, un_grouped_cells if groups else page_cells

    @staticmethod
    def is_contiguous_cells(cells: list["ParsedTableCell"]) -> bool:  # noqa
        """
        判断是否为连续且工整对齐的单元格（可以画在一个矩形框中）
        """

        def find_real_min_max(min_, max_, merged_headers):
            for headers in merged_headers:
                if min_ in headers:
                    min_ = min(headers)
                if max_ in headers:
                    max_ = max(headers)
            return min_, max_

        if len(cells) == 1:
            return True

        cell_indices, row_indices, col_indices = [], set(), set()
        for cell in cells:
            cell_indices.append((cell.rowidx, cell.colidx))
            row_indices.add(cell.rowidx)
            col_indices.add(cell.colidx)

        # 存在部分合并单元格，则认为不连续
        merged_cells = cells[0].table.merged_cells
        for cell in cells:
            if cell.is_header:
                continue
            pair = (cell.rowidx, cell.colidx)
            for m_cells in merged_cells:
                if pair in m_cells and any(cpair not in cell_indices for cpair in m_cells if cpair != pair):
                    return False

        merged_rows, merged_cols = cells[0].table.merged_headers
        col_min, col_max = min(col_indices), max(col_indices)
        row_min, row_max = min(row_indices), max(row_indices)
        row_min, row_max = find_real_min_max(row_min, row_max, merged_rows)
        col_min, col_max = find_real_min_max(col_min, col_max, merged_cols)
        # 根据上方的col_min, col_max, row_min, row_max生成一个完整的子表格，与当前单元格做对比如果缺的单元格都是标题，说明结果连续
        # http://100.64.0.105:55647/#/project/remark/293766?treeId=7074&fileId=70677&schemaId=18&projectId=7074&schemaKey=C1.3
        for row in cells[0].table.rows:
            # 跳过结果完全不涉及的行
            if not row_min <= row[0].rowidx <= row_max:
                continue
            for cell in row:
                # 跳过列头、结果完全不涉及的列及结果中已经存在的单元格
                if (
                    cell.is_col_header
                    or not col_min <= cell.colidx <= col_max
                    or (cell.rowidx, cell.colidx) in cell_indices
                ):
                    continue
                return False
        return True

    def group_cells_by_page(self):
        ret = defaultdict(list)
        for cell in self.parsed_cells:
            ret[cell.page].append(cell)

        return ret

    @staticmethod
    def cells_outline(cells):
        # TODO: 不相邻的多个cell，应该分别画框
        left = min(parsed_cell.outline[0] for parsed_cell in cells)
        top = min(parsed_cell.outline[1] for parsed_cell in cells)
        right = max(parsed_cell.outline[2] for parsed_cell in cells)
        bottom = max(parsed_cell.outline[3] for parsed_cell in cells)

        return [left, top, right, bottom]


class CellCharResult(CharResult):
    def __init__(
        self,
        element,
        chars,
        cells: list["ParsedTableCell"],  # noqa
        display_text=None,
        start=None,
        end=None,
        flag=None,
    ):
        super().__init__(element, chars, display_text=display_text, start=start, end=end, flag=flag)
        self.parsed_cells = cells

    def __hash__(self):
        return hash((self.index, tuple(self.cells), self.start, self.end, self.text))

    def __eq__(self, other):
        if isinstance(other, CellCharResult):
            return (
                self.index == other.index
                and self.cells == other.cells
                and self.start == other.start
                and self.end == other.end
                and self.text == other.text
            )
        return False

    def __repr__(self):
        return f"CellCharResult({self.index}, {self.cells}, {self.start}, {self.end}, {self.text})"

    @property
    def text(self):
        return self.display_text or "".join(c["text"] for c in self.chars)

    @property
    def cells(self) -> List[str]:
        if not self.parsed_cells:
            return []
        return [cell.raw_cell["index"] for cell in self.parsed_cells]

    @property
    def parsed_table(self):
        if not self.parsed_cells:
            return None
        return self.parsed_cells[0].table

    def to_answer(self):
        answer = {"boxes": [], "handleType": "wireframe"}
        page = self.parsed_cells[0].page if self.parsed_cells else self.element["page"]
        answer["boxes"] = self.get_answer_boxes_by_chars(page, self.chars)
        if self.display_text:
            answer["text"] = self.display_text
        return answer

    def cells_outline(self):
        # TODO: 不相邻的多个cell，应该分别画框
        left = min(char["box"][0] for char in self.chars)
        top = min(char["box"][1] for char in self.chars)
        right = max(char["box"][2] for char in self.chars)
        bottom = max(char["box"][3] for char in self.chars)

        return [left, top, right, bottom]

    @staticmethod
    def chars_outline(chars):
        left = min(char["box"][0] for char in chars)
        top = min(char["box"][1] for char in chars)
        right = max(char["box"][2] for char in chars)
        bottom = max(char["box"][3] for char in chars)

        return [left, top, right, bottom]


@dataclass
class InterdocTableCellResult:
    element: Any
    cells: List[Cell]

    def __post_init__(self):
        self.cells.sort(key=lambda x: (x.row, x.col))

    @cached_property
    def text(self):
        return "\t".join([cell.text for cell in self.cells])

    @staticmethod
    def cells_outline(cells: list[ChildCell]):
        left = min(cell.box.left for cell in cells)
        top = min(cell.box.top for cell in cells)
        right = max(cell.box.right for cell in cells)
        bottom = max(cell.box.bottom for cell in cells)

        return [left, top, right, bottom]

    def to_answer(self):
        answer = {"boxes": [], "handleType": "wireframe"}
        cells_by_page_index: dict[int, dict[int, list[ChildCell]]] = defaultdict(lambda: defaultdict(list))
        for cell in self.cells:
            for child_cell in cell.children:
                cells_by_page_index[child_cell.page][child_cell.index].append(child_cell)
        for page, page_cells in cells_by_page_index.items():
            for cells in page_cells.values():
                box_info = {
                    "page": page,
                    "box": outline_to_box(self.cells_outline(cells)),
                    "text": "".join(cell.text for cell in sorted(cells, key=lambda x: (x.row, x.col))),
                }
                answer["boxes"].append(box_info)
        return answer


def build_element_result(element):
    if element["class"] in ["PARAGRAPH", "PAGE_FOOTER", "PAGE_HEADER", "FOOTNOTE"]:
        return ParagraphResult(element, element["chars"])
    if element["class"] == "TABLE":
        return TableResult(element)
    logging.error(f"Not supported element class: {element['class']}")
    return None


class PredictorResult:
    def __init__(
        self,
        element_results: List[ElementResult],
        value: str = None,
        text: str = None,
        primary_key: List[str] = None,
        schema: SchemaItem | None = None,
        score: int = None,
        meta: Dict = None,
        answer_model: str = None,
        ignore_missing_crude_answer: bool = False,
    ):
        if primary_key and not isinstance(primary_key, list):
            raise ValueError(f"primary_key must be a list: {primary_key}")
        self.element_results = element_results
        self.answer_value = value
        self.primary_key = primary_key
        self.schema = schema
        self._text = text
        self.score = score
        self.group_indexes = []
        self.meta = meta or {}
        self.answer_model = answer_model
        self.ignore_missing_crude_answer = ignore_missing_crude_answer

    @property
    def key_path(self):
        return self.schema.path

    @property
    def text(self):
        separator = "\n"
        if all(isinstance(result, (CellCharResult,)) for result in self.element_results):
            separator = "\t"
        return self._text or separator.join(i.text for i in self.element_results)

    @property
    def key_path_str(self):
        return json.dumps(self.key_path, ensure_ascii=False)

    @property
    def relative_elements(self):
        return [res.element for res in self.element_results]

    @property
    def answer(self):
        return self.build_answer_data()

    def push_group_index(self, idx):
        self.group_indexes.append(idx)

    def build_answer_path_key(self):
        """
        schema path:    0 1 2 3
        group index: -1 0 1 2
        注：group index 的第一个实际是 root 的所属分组，并且最后一个叶子节点尚未添加，所以是错位的
        """
        if len(self.schema.path) != len(self.group_indexes):
            raise ValueError("length of indexes is not match with schema path")
        path_with_index = [f"{name}:{idx}" for name, idx in zip(self.schema.path, self.group_indexes[1:] + [0])]
        return compact_dumps(path_with_index)

    def build_answer_data(self):
        return {
            "key": self.build_answer_path_key(),
            "schema": self.schema.to_answer_data(),
            "score": self.confidence_score(),
            "data": [i.to_answer() for i in self.element_results],
            "value": self.answer_value,
            "meta": self.meta,
        }

    def confidence_score(self):
        score = self.score
        if score is None:
            element_result = next((i for i in self.element_results), None)
            element = getattr(element_result, "element", None) or {}
            score = element.get("score", -1)
        return "%.2f" % float(score)

    def update_answer_value(self, value):
        self.answer_value = value

    def merge(self, other):
        self.element_results.extend(other.element_results)
        if self.answer_value and other.answer_value:
            answer = []
            if isinstance(self.answer_value, str):
                answer.append(self.answer_value)
            elif isinstance(self.answer_value, list):
                answer.extend(self.answer_value)
            if isinstance(other.answer_value, str):
                answer.append(other.answer_value)
            elif isinstance(other.answer_value, list):
                answer.extend(other.answer_value)
            deduplicate_answers = list(set(answer))
            if len(deduplicate_answers) == 1:
                self.answer_value = answer[0]
            else:
                self.answer_value = deduplicate_answers
        else:
            self.answer_value = self.answer_value or other.answer_value

    def clone(self):
        return PredictorResult(
            self.element_results, value=self.answer_value, primary_key=self.primary_key, schema=self.schema
        )

    def __str__(self):
        return self.text or self.answer_value

    @cached_property
    def repr_with_page_idx(self):
        # just for debug use
        pages, indices = set(), set()
        for element_result in self.element_results:
            if not hasattr(element_result, "element"):
                continue
            if index := element_result.element.get("index"):
                indices.add(str(index))
            if page_idx := element_result.element.get("page"):
                pages.add(str(page_idx + 1))
        if pages or indices:
            return f"review pages: {','.join(pages)}, indices: {','.join(indices)} => {self.text or ''}"
        return ""


class PredictorResultGroup(PredictorResult):
    """封装 predictor.predict_groups() 输出的多组答案，作为父级节点的一个答案，以参与父级的分组
    NOTE:
    - 子节点 sub_predictor.predict_groups() 输出的是 {"sub_primary_key_str": sub_group, ...}
    - 父节点 predictor.predict_answer_from_models() 会将其封装为 [{"parent_column": [PredictorResultGroup([sub_group])]}, ...]
    - 父节点 通过 primary_model 和 guess_primary_key 为每一组添加父级 primary key，并进行父级分组
    - 最终输出时，revise_group_answer 会把同组同path的答案合并，变为
        {"primary_key_str": [PredictorResultGroup([sub_group1, sub_group2, ...]), PredictorResult(其他字段), ...]}
    - 注：group 结构为 List[PredictorResult]
    """

    def __init__(
        self,
        groups: List[List[PredictorResult]],
        primary_key: List[str] = None,
        schema: MoldSchema = None,
        element_results: List[ElementResult] = None,
    ):
        PredictorResult.__init__(
            self,
            element_results=element_results or [],
            text=self.gen_group_text(groups),
            primary_key=primary_key,
            schema=schema,
        )
        self.groups = groups

    @classmethod
    def gen_group_text(cls, groups):
        return "|".join([item.text or "" for group in groups for item in group])

    @property
    def relative_elements(self):
        return [ele for group in self.groups for item in group for ele in item.relative_elements]

    def push_group_index(self, idx):
        PredictorResult.push_group_index(self, idx)
        for group in self.groups:
            for item in group:
                item.push_group_index(idx)

    def build_answer_data(self):
        # NOTE: non-leaf node won't have an answer item in final output
        return None

    def update_answer_value(self, value):
        raise TypeError("unsupported this method")

    def merge(self, other: "PredictorResultGroup"):
        if not isinstance(other, PredictorResultGroup):
            raise ValueError("can't merge PredictorResultGroup with %s" % type(other).__name__)
        self.groups.extend(other.groups)

    def clone(self):
        return deepcopy(self)
