import logging
from typing import Any, Dict

from remarkable.common.common import FUNDRAISING_COLS, JURA6_C1_RULES
from remarkable.common.constants import AnnualReportEnum, FundRaisingRuleEnum
from remarkable.models.mold import special_mold
from remarkable.pdfinsight.reader import Pdfin<PERSON><PERSON>eader
from remarkable.predictor.predictor_metadata.metadata_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, PredictionContext
from remarkable.prompter.impl.v4_embedding import EmbeddingPrompter
from remarkable.services.addition_data import get_addition_data
from remarkable.services.question import ctx_only_prev_fund_raising

logger = logging.getLogger(__name__)


class C1RuleHandler(MetadataHandler):
    def is_necessary(self, **kwargs) -> bool:
        """
        针对C1/C1.1.1/C1.2.1/C1.3
        """
        special_rules = kwargs.get("special_rules")
        if not special_rules:
            return True
        return set(special_rules) & JURA6_C1_RULES

    async def handle_metadata(self, context: PredictionContext, **kwargs) -> Dict[str, Any]:
        metadata = context.base_metadata.copy()
        special_rules = set(kwargs.get("special_rules") or ())
        # C1.1.1/C1.2.1/C1.3才需要addition_data
        if not special_rules or special_rules & set(FUNDRAISING_COLS):
            if context.file_meta and context.file_meta.stock_code:
                # for the rule C1.1.1, C1.2.1, C1.3
                metadata["addition_data"] = await get_addition_data(context.file_meta.stock_code, metadata["year_end"])

        pdfinsight = PdfinsightReader.from_path(context.file.pdfinsight_path(abs_path=True), pdf_hash=context.file.pdf)
        # C1/C1.1.1/C1.2.1/C1.3需要extend_candidate_by_enum
        embedding_prompter_client = EmbeddingPrompter(special_mold.v3_id)
        embedding_rules = (
            [FundRaisingRuleEnum.C1_2_1.value] if ctx_only_prev_fund_raising.get() else kwargs.get("special_rules")
        )
        logger.info("gen ps_crude_answer...")
        metadata["ps_crude_answer"] = await embedding_prompter_client.prompt_for_file(
            context.file, rules=embedding_rules, special_enum=AnnualReportEnum.PS, pdfinsight=pdfinsight
        )
        logger.info("gen ns_crude_answer...")
        metadata["ns_crude_answer"] = await embedding_prompter_client.prompt_for_file(
            context.file, rules=embedding_rules, special_enum=AnnualReportEnum.NS, pdfinsight=pdfinsight
        )
        return metadata
