import logging
from typing import Any, Dict

from remarkable.common.common import get_financial_year
from remarkable.models.hkex_file_meta import HKEXFileMeta
from remarkable.models.mold import special_mold
from remarkable.models.new_file import NewFile
from remarkable.predictor.predictor_metadata.agm_poll import <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from remarkable.predictor.predictor_metadata.b1_b7 import <PERSON>1<PERSON><PERSON><PERSON><PERSON><PERSON>
from remarkable.predictor.predictor_metadata.esg import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from remarkable.predictor.predictor_metadata.jura_21 import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from remarkable.predictor.predictor_metadata.metadata_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Metada<PERSON><PERSON><PERSON><PERSON>
from remarkable.predictor.predictor_metadata.mr import <PERSON><PERSON><PERSON><PERSON>
from remarkable.predictor.predictor_metadata.policy_esg import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, PolicyESGtHandler
from remarkable.predictor.predictor_metadata.rule_c1 import <PERSON>1<PERSON><PERSON><PERSON><PERSON><PERSON>
from remarkable.predictor.predictor_metadata.rule_c2 import C2RuleHandler

logger = logging.getLogger(__name__)


async def prepare_base_metadata(file: NewFile, question: Any, file_meta: HKEXFileMeta | None) -> Dict[str, Any]:
    doc_type = file_meta.doc_type if file_meta else ""
    metadata = {"report_year": file_meta.report_year if file_meta else ""}

    if file_meta:
        if file_meta.year_end:
            metadata["year_start"], metadata["year_end"] = get_financial_year(
                file_meta.fid, file_meta.year_end, year_end_format="%d %b %Y"
            )
        if file_meta.stock_code:
            metadata["stock_code"] = file_meta.stock_code
        if file_meta.published:
            metadata["published_at"] = file_meta.published_at
        if file_meta.name:
            metadata["company_name"] = file_meta.name
    metadata["doc_type"] = doc_type
    metadata["pdf_path"] = file.pdf_path(abs_path=True)
    metadata["file"] = file
    metadata["question_id"] = question.id

    return metadata


class MetadataFactory:
    """Mold元数据处理器的工厂类"""

    def __init__(self):
        self._handlers = {}
        self._register_handlers()

    def _register_handlers(self):
        for mold_id in special_mold.esg_mids:
            self._handlers[mold_id] = ESGHandler()

        self._handlers[special_mold.v1_id] = Jura21Handler()
        self._handlers[special_mold.v2_id] = CompositeHandler([Jura21Handler(), B1B7Handler()])
        self._handlers[special_mold.v3_id] = CompositeHandler([C1RuleHandler(), C2RuleHandler()])
        self._handlers[special_mold.policy_esg_id] = PolicyESGtHandler()
        self._handlers[special_mold.policy_ar_id] = PolicyArESGtHandler()
        self._handlers[special_mold.v6_poll_id] = PollResultHandler()
        self._handlers[special_mold.v6_agm_id] = AGMHandler()
        self._handlers[special_mold.v6_1_mr_id] = MRHandler()

    def get_handler(self, mold_id: str) -> MetadataHandler | None:
        """获取对应mold_id的处理器"""
        return self._handlers.get(mold_id)
