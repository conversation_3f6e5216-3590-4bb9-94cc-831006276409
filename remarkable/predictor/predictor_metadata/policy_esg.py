import logging
from itertools import chain
from operator import itemgetter
from typing import Any, Dict

from pdfparser.imgtools.ocraug.faded_h_stroke import defaultdict

from remarkable.answer.reader import AnswerReader
from remarkable.common.common import get_keys
from remarkable.common.constants import PDFInsightClassEnum, PolicyEsgRules, TableType
from remarkable.common.pattern import <PERSON><PERSON>ulti, NeglectPattern, PatternCollection
from remarkable.common.protocol import SearchPatternLike
from remarkable.common.util import clean_txt, extract_hyperlinks_from_pdf
from remarkable.db import pw_db
from remarkable.models.embedding import Embedding, SyllabusEmbedding
from remarkable.models.mold import special_mold
from remarkable.models.new_question import NewQuestion
from remarkable.pdfinsight.parser import parse_table
from remarkable.pdfinsight.reader import PdfinsightReader
from remarkable.predictor.predictor_metadata.metadata_handler import <PERSON>adataH<PERSON><PERSON>, PredictionContext
from remarkable.prompter.impl.v4_embedding import EmbeddingPrompter
from remarkable.services.retrieval import retrieval_by_keywords

logger = logging.getLogger(__name__)


P_E1_VALID_SYLLABUS = MatchMulti.compile(
    r"^Report(?:ing)? Standards$",
    r"^Report(?:ing)? Guidelines$",
    r"about this report",
    operator=any,
)

R_E1_VALID_SYLLABUS = [
    "Reporting Standards",
    "Reporting Guidelines",
    "Preparation basis",
    "About the report",
    "About this report",
    "Reporting framework",
    "Reporting principles",
]
P_E2_RULES = NeglectPattern.compile(
    match=MatchMulti.compile(
        r"\bindependent(?:ly)?\b",
        r"\bthird[-\s]+part(y|ies)\b",
        r"Hong\s*Kong\s*Quality\s*Assurance\s*Agency|HKQAA",
        r"(International\s*Standard\s*on\s*Assurance\s*Engagements|ISAE)\s*3000",
        r"engaged\s*to\s*perform",  # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6342#note_700785
        r"assurance\s*(?:report|engagement)",  # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6342#note_709385
        operator=any,
    ),
    unmatch=MatchMulti.compile(
        r"independent\s*of\s*the\s*(?:Group|Company)",
        r"External assurance External Assurance Report$",  # 08072 2024
        operator=any,
    ),
)

P_E2_NEGLECT_CHAPTER = MatchMulti.compile(
    r"^Environmental[,，] Social\s*(?:(&|And)\s*Governance)?(?:\s*Report)?(?:\s*\(CONTINUED\))?$",
    r"^ESG REPORT環境、社會及管治報告$",
    r"^Environmental[,，] Social and Governance Report環境、社會及管治報告$",
    r"CORPORATE GOVERNANCE REPORT",
    operator=any,
)

P_E8_NEGLECT_CHAPTER = MatchMulti.compile(
    r"SASB CONTENT INDEX SASB",  # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6348#note_690138
    r"Scope 3 Emissions Data Reporting Methodology",  # http://************:55647/#/hkex/esg-report-checking/report-review/339100?fileId=103407&schemaId=2&rule=E7-Scope%203%20emissions&delist=0
    operator=any,
)

# todo 添加对年报 esg 的章节过滤
# https://jura6-esg.paodingai.com/#/project/remark/301864?projectId=17&treeId=2476&fileId=68316&schemaId=31  page119 index 1963 E9


class PolicyESGtHandler(MetadataHandler):
    P_VALID_SYLLABUS = MatchMulti.compile(
        r"assurance (?:report|statement)",
        r"verification statement",
        r"Assurance relating to ESG data",
        r"^Verification$",
        r"^ABOUT THE REPORT$",
        operator=any,
    )

    P_CONTENT_PAGE = PatternCollection(
        [
            r"^([^\d])+(?P<page>[\d]+)$",
            r"^(?P<page>[\d]+)([^\d])+$",
        ]
    )

    async def handle_metadata(self, context: PredictionContext, **kwargs) -> Dict[str, Any]:
        metadata = context.base_metadata.copy()
        pdfinsight = PdfinsightReader.from_path(context.file.pdfinsight_path(abs_path=True), pdf_hash=context.file.pdf)

        special_rules = kwargs.get("special_rules", [])

        # 定义规则处理映射
        rule_handlers = {
            PolicyEsgRules.E1.value: lambda: self.get_e1_elements(context, pdfinsight),
            PolicyEsgRules.E2.value: lambda: self.get_e2_elements(context, pdfinsight),
            PolicyEsgRules.E4.value: lambda: self.get_e4_elements(context, pdfinsight),
            PolicyEsgRules.E5.value: lambda: self.get_e5_elements(context),
            PolicyEsgRules.E6.value: lambda: self.get_e6_elements(context),
            PolicyEsgRules.E7.value: lambda: self.get_e7_elements(context, pdfinsight),
        }

        # 处理E1, E2, E4, E5, E6规则
        for rule, handler in rule_handlers.items():
            if not special_rules or (special_rules and rule in special_rules):
                metadata[rule] = await handler()

        # 处理E7, E8, E9规则
        scope3_rules = [PolicyEsgRules.E8.value, PolicyEsgRules.E9.value]
        if not special_rules or any(rule in special_rules for rule in scope3_rules):
            scope3_elements = await self.gen_scope3_elements(context, pdfinsight)
            for rule in scope3_rules:
                metadata[rule] = scope3_elements

        metadata["hyperlinks"] = extract_hyperlinks_from_pdf(context.file.pdf_path(abs_path=True))
        return metadata

    @classmethod
    async def get_e7_elements(cls, context: PredictionContext, pdfinsight):
        # elements = await EmbeddingPrompter(special_mold.policy_esg_id).prompt_for_file(
        #     context.file, rules=[PolicyEsgRules.E7.value], limit=10, pdfinsight=pdfinsight
        # )
        scope3_elements = await cls.gen_scope3_elements(context, pdfinsight)
        return scope3_elements

    @classmethod
    async def gen_scope3_elements(cls, context: PredictionContext, pdfinsight):
        scope3_regex = cls.gen_scope3_regex()
        scope3_elements = await Embedding.regex_search([context.file.id], scope3_regex, ignore_case=True, limit=50)
        issb_regex = [
            r"Scope 3.*include",
            r"Scope 3[：:]",
            # r"[（(]Scope 3[)）][,.]",
            r"category.*percent",
            r"Other indirect (GHG )?emissions.*scope 3",
            r"(Scope 3|GHG) emissions.*\d tonnes",
            r"(Air|Business).*travel",
        ]
        r_scope3 = "|".join(issb_regex)
        elements_by_regex = await Embedding.regex_search([context.file.id], r_scope3, ignore_case=True)
        scope3_elements += elements_by_regex
        for i in scope3_elements:
            _, element = pdfinsight.find_element_by_index(i["index"])
            if not element:
                continue
            i["page"] = element["page"]
            i["outline"] = element["outline"]
            i["class"] = element["class"]
            i["score"] = 0.8
        result = []
        for ele in cls.filter_invalid_elements(pdfinsight, scope3_elements):
            if P_E8_NEGLECT_CHAPTER.search(ele.get("text") or "") or any(
                P_E8_NEGLECT_CHAPTER.search(clean_txt(p_ele.data.get("text") or ""))
                for p_ele in (pdfinsight.page_element_dict.get(ele["page"]) or [])[:2]
            ):
                continue
            syllabuses = pdfinsight.find_syllabuses_by_index(ele["index"])
            if any(P_E8_NEGLECT_CHAPTER.search(clean_txt(i.get("title") or "")) for i in syllabuses):
                continue
            result.append(ele)

        return result

    @staticmethod
    def gen_scope3_regex():
        e8_regex = r"(categor(y|ies) \d|cat\. \d)|("
        for i in (
            r"Purchased\s+goods\s+and\s+service",
            r"Capital\s+good",
            r"Fuel\s+and\s+energy\s+related",
            r"Upstream\s+transportation",
            r"Waste\s+generated\s+in\s+operations",
            r"(Business|Air|Employee).*travel",
            r"Employee\s+commut",
            r"Upstream\s+leased\s+asset",
            r"Downstream\s+transportation",
            r"Processing\s+of\s+sold\s+product",
            r"Use\s+of\s+sold\s+product",
            r"End.*treatment\s+of\s+sold\s+product",
            r"Downstream\s+leased\s+asset",
            r"Franchise",
            r"Investment",
        ):
            e8_regex += rf"((C\d\d?|Scope 3).*?{i})|({i}.*emission)|"
        e8_regex = e8_regex[:-1]
        e8_regex = rf"{e8_regex})"
        return e8_regex

    @classmethod
    async def get_scope1_2_answer(cls, context: PredictionContext):
        result = []
        cond = [
            NewQuestion.fid == context.file.id,
            NewQuestion.mold.in_((special_mold.ar_esg_id, special_mold.esg_id)),
        ]
        query = NewQuestion.select(NewQuestion.preset_answer).where(*cond)
        preset_answer = await pw_db.scalar(query)
        if not preset_answer:
            logger.warning(f"no normal esg preset_answer for file {context.file.id=}")
            return result
        answer_reader = AnswerReader(preset_answer)

        for node in answer_reader.find_nodes(["KPI A1.2 part 1 - Scope 1"]):
            result.append(node.data)
        for node in answer_reader.find_nodes(["KPI A1.2 part 2 - Scope 2"]):
            result.append(node.data)

        return result

    @classmethod
    async def get_e1_elements(cls, context, pdfinsight):
        # embedding_crude_elements = await EmbeddingPrompter(special_mold.policy_esg_id).prompt_for_file(
        #     context.file, rules=[PolicyEsgRules.E1.value], limit=10, pdfinsight=pdfinsight
        # )
        sub_rules = [r"Reference to IFRS", "recommended by GRI and ISSB"]
        rule_description = "Reference to ISSB"
        elements = await retrieval_by_keywords(
            context.file.id,
            rule_description=rule_description,
            sub_rules=sub_rules,
            limit=50,
            only_sub_element=False,
        )
        # 上一步有可能因为权重问题而找不到某些元素块，下面是根据正则明确的一些初步定位的元素块
        issb_regex = [
            r"validate our alignment to.*IFRS S2",
            r"(referenced?|\yas\sper\y).*(ISSB|IFRS|International Sustainability Standards Board)",
            r"align with (ISSB|IFRS|International Sustainability Standards Board)",
            r"prepared in accordance with.*(ISSB|IFRS|International Sustainability Standards Board)",
        ]
        r_issb = "|".join(issb_regex)
        elements_by_regex = await Embedding.regex_search([context.file.id], r_issb, ignore_case=True)
        element_mapping = {i["index"]: [i] for i in elements}
        for ele in elements_by_regex:
            if not (org_eles := element_mapping.get(ele["index"])) or all(
                org_ele["element_sequence"] != ele["element_sequence"] for org_ele in org_eles
            ):
                _, element = pdfinsight.find_element_by_index(ele["index"])
                if not element:
                    continue
                ele["page"] = element["page"]
                ele["outline"] = element["outline"]
                ele["class"] = element["class"]
                ele["score"] = 0.8
                element_mapping.setdefault(ele["index"], []).append(ele)
        elements = cls.filter_invalid_elements(pdfinsight, chain.from_iterable(element_mapping.values()), min_len=10)
        results = cls.filter_duplicate_elements_by_position(elements)
        # 如果预测答案属于Reporting Standards/Reporting Guidelines标题下，优先采纳
        valid_syllabus_elements = await cls.get_special_syllabus_elemens(context, results, pdfinsight)
        return {"summary_elements": valid_syllabus_elements, "all_elements": results}

    @classmethod
    async def get_special_syllabus_elemens(cls, context, results, pdfinsight):
        elements = await retrieval_by_keywords(
            context.file.id,
            rule_description=R_E1_VALID_SYLLABUS[0],
            sub_rules=R_E1_VALID_SYLLABUS[1:],
            limit=10,
            element_sequence=0,
        )
        regex_pattern = f"({'|'.join(R_E1_VALID_SYLLABUS)})"
        syllabus_results = await SyllabusEmbedding.regex_search(
            file_ids=[context.file.id], regex=regex_pattern, ignore_case=True, limit=10
        )
        possible_indexes = set()
        for syllabus_result in syllabus_results:
            possible_indexes = possible_indexes.union(syllabus_result["indexes"])
        for element in elements:
            if syllabus := pdfinsight.syllabus_reader.elt_syllabus_dict.get(element["index"]):
                start, end = syllabus["range"]
                for i in range(start, end + 1):
                    possible_indexes.add(i)
        # 前两页的元素块，优先采纳 文档的第二页是 about this report ,但是因为是红色字体，所以没有被识别出来
        # http://************:55647/#/hkex/esg-report-checking/report-review/355587?fileId=112816&schemaId=32&rule=E1-Reference%20to%20ISSB%20Standards&delist=0
        valid_syllabus_elements = [i for i in results if i["index"] in possible_indexes or i["page"] <= 2]
        return valid_syllabus_elements

    @classmethod
    def filter_duplicate_elements_by_position(cls, elements):
        group_elements = defaultdict(list)
        for ele in elements:
            group_elements[ele["index"]].append(ele)
        results = []
        for eles in group_elements.values():
            if len(eles) == 1:
                results.extend(eles)
            else:
                results.extend([ele for ele in eles if ele["element_sequence"] != 0])
        return results

    @classmethod
    async def get_e2_elements(cls, context, pdfinsight):
        embedding_crude_elements = await EmbeddingPrompter(special_mold.policy_esg_id).prompt_for_file(
            context.file, rules=[PolicyEsgRules.E2.value], limit=10, pdfinsight=pdfinsight
        )
        rule_description = "The ESG report is verified by an independent third party"
        sub_rules = [
            "对ESG报告的全部或者部分取得了独立验证或第三方验证",
            r"obtained independent verification of all or part of the ESG report",
            r"已由第三方根据标准进行核实",
            r"has been verified by a third-party",
            r"working with a third party to validate",  # fid: 103416
            r"Hong Kong Quality Assurance Agency",  # fid: 103415
            r"engaged independent assurance",  # fid 112991
            r"perform a limited assurance engagement",  # fid: 112774、112886、104174  执行有限鉴证业务
        ]
        elements = await retrieval_by_keywords(
            context.file.id,
            rule_description=rule_description,
            sub_rules=sub_rules,
            limit=100,
            only_sub_element=False,
        )
        if content_elements := cls.find_elements_from_content(pdfinsight):
            for ele in await retrieval_by_keywords(
                context.file.id,
                rule_description=rule_description,
                sub_rules=sub_rules,
                limit=100,
                only_sub_element=False,
                indexes=[ele["index"] for ele in content_elements],
            ):
                if ele not in elements:
                    elements.insert(0, ele)
        element_idx = [i["index"] for i in elements]
        for items in embedding_crude_elements.values():
            for item in items:
                if item["element_index"] not in element_idx:
                    elements.append({"index": item.pop("element_index"), "score": item.pop("score") * 0.1, **item})
        result = []
        for ele in cls.filter_invalid_elements(pdfinsight, elements, p_valid=P_E2_RULES, min_len=10):
            if (
                P_E2_NEGLECT_CHAPTER.search(ele.get("text") or "")
                or any(
                    P_E2_NEGLECT_CHAPTER.search(clean_txt(p_ele.data.get("text") or ""))
                    for p_ele in (pdfinsight.page_element_dict.get(ele["page"]) or [])[:2]
                )
                or pdfinsight.syllabus_reader.is_syllabus_elt(ele)
            ):
                continue
            result.append(ele)
        return result

    @classmethod
    def group_page_content(cls, pdfinsight):
        contexts = []
        for page, page_eles in pdfinsight.page_element_dict.items():
            if not pdfinsight.is_catalog_page(page):
                continue
            contexts = []
            for item in page_eles:
                ele_type, element = pdfinsight.find_element_by_index(item.data["index"])
                if ele_type == "TABLE":
                    table = parse_table(element, tabletype=TableType.ROW, pdfinsight_reader=pdfinsight)
                    for row in table.rows:
                        contexts.append(" ".join(i.text for i in row))
                elif ele_type == "PARAGRAPH":
                    contexts.append(element["text"])
            break
        return contexts

    @classmethod
    def find_elements_from_content(cls, pdfinsight):
        elements = []
        for text in cls.group_page_content(pdfinsight):
            if cls.P_VALID_SYLLABUS.search(text) and (res := cls.P_CONTENT_PAGE.nexts(text)):
                elements.extend(pdfinsight.find_elements_by_page(pdfinsight.content_pages.get(int(res.group("page")))))
        elements = cls.find_elements_from_syllabus(cls.P_VALID_SYLLABUS, pdfinsight, elements)
        return sorted(elements, key=itemgetter("index"))

    @classmethod
    def find_elements_from_syllabus(cls, p_syll, pdfinsight, elements=None):
        elements = elements or []
        for syll in pdfinsight.find_sylls_by_pattern([p_syll]):
            for index in range(*syll["range"]):
                elt_type, ele = pdfinsight.find_element_by_index(index)
                if not ele or ele in elements:
                    continue
                elements.append(ele)
        return sorted(elements, key=itemgetter("index"))

    @classmethod
    def filter_invalid_elements(cls, pdfinsight, elements, p_valid: SearchPatternLike = None, min_len=4) -> list[dict]:
        elements = cls.filter_duplicate_elements(pdfinsight, elements)
        filtered_elements = []
        for element in elements:
            ele_type, org_ele = pdfinsight.find_element_by_index(element["index"])
            if pdfinsight.is_chinese_elt(element):
                continue
            if element["class"] in ("PAGE_HEADER", "PAGE_FOOTER"):
                continue
            if len((org_ele.get("text") or element.get("text") or "").split()) <= min_len:
                continue
            if pdfinsight.is_catalog_page(element["page"]):
                continue
            if p_valid and not p_valid.search(element.get("text") or ""):
                continue
            filtered_elements.append(element)
        return filtered_elements

    @staticmethod
    def filter_duplicate_elements(pdfinsight, elements) -> list[dict]:
        filter_elements = []
        element_idxes = []
        para_group = defaultdict(list)
        for item in sorted(elements, key=itemgetter("index")):
            ele_type, element = pdfinsight.find_element_by_index(item["index"])
            if not element:
                continue
            merged_idx = element["index"]
            if ele_type == PDFInsightClassEnum.TABLE:
                merged_idx = get_keys(element, ["page_merged_table", "index"]) or element["index"]
            if element_sequence := item.get("element_sequence"):
                merged_idx = f"{merged_idx}_{element_sequence}"

            if merged_idx in element_idxes:
                continue
            item["index"] = element["index"]
            item["page"] = element["page"]
            item["outline"] = element["outline"]
            item["class"] = element["class"]
            if ele_type == PDFInsightClassEnum.TABLE:
                filter_elements.append(item)
                element_idxes.append(merged_idx)
            else:
                para_group[element["index"]] = list(
                    {ele.get("element_sequence") or 0: ele for ele in [item, *para_group[element["index"]]]}.values()
                )
                element_idxes.extend(
                    get_keys(element, ["page_merged_paragraph", "paragraph_indices"], default=[element["index"]]) or []
                )
        for items in para_group.values():
            items = sorted(items, key=itemgetter("element_sequence"))
            if items[0].get("element_sequence") == 0:
                filter_elements.append(items[0])
                continue
            filter_elements.extend(items)
        return sorted(filter_elements, key=itemgetter("score"), reverse=True)

    @classmethod
    async def get_e4_elements(cls, context, pdfinsight):
        embedding_crude_elements = await EmbeddingPrompter(special_mold.policy_esg_id).prompt_for_file(
            context.file, rules=[PolicyEsgRules.E4.value], pdfinsight=pdfinsight
        )
        scope_e4_elements = await Embedding.regex_search(
            [context.file.id],
            r"Scope [12]",
            ignore_case=False,
            limit=50,
            # element_sequence=0,  # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7699#note_760179
        )
        scope1_2_answer = await cls.get_scope1_2_answer(context)
        e4_elements = {
            "scope": scope_e4_elements,
            "embedding_crude_elements": embedding_crude_elements.get(PolicyEsgRules.E4.value, []),
            "scope1_2_answer": scope1_2_answer,
        }
        return e4_elements

    @staticmethod
    async def _get_e5_elements(file_id):
        r_e5 = r"\y[\(（]?(SSP((1-| +)?2\.6|(?:5-| +)?8\.5)?|RCP( *\d\.\d+)?|NZE|[Cc]limate [Rr]isks?|Physical [Rr]isks?|IEA|NGFS|Material [Rr]isks?)(\d(?=\y))?[）\)]?\y"
        e5_elements = await Embedding.regex_search(
            [file_id],
            r_e5,
            ignore_case=False,
            element_sequence=0,
            desc=True,
            limit=50,
        )
        return e5_elements

    @classmethod
    async def get_e5_elements(cls, context):
        elements = await cls._get_e5_elements(context.file.id)
        return cls.filter_duplicate_elements_by_position(elements)

    @staticmethod
    async def _get_e6_elements(file_id):
        r_e6 = r"\y[\(（]?(IPCC|IEA|NGFS|NZE|SSP((1-| )?2.6|(?:5-| )?8.5)|RCP( *\d\.\d+)?)[）\)]?\y"
        e6_elements = await Embedding.regex_search(
            [file_id],
            r_e6,
            ignore_case=False,
            # element_sequence=0,
            desc=True,
            limit=50,
        )
        return e6_elements

    @classmethod
    async def get_e6_elements(cls, context):
        elements = await cls._get_e6_elements(context.file.id)
        return cls.filter_duplicate_elements_by_position(elements)


class PolicyArESGtHandler(PolicyESGtHandler):
    FILTER_RULES = [
        PolicyEsgRules.E1,
        PolicyEsgRules.E2,
    ]

    P_VALID_CONTENT = MatchMulti.compile(
        r"(?:ESG|environment.*?social\s*?(and|&)\s*?governance?).*?(report|review)$",
        r"Corporate\s*?Social\s*?Responsibility.*?(Report|Review)",
        operator=any,
    )

    P_INVALID_CONTENT = MatchMulti.compile(
        r"Director.{0,2} Report",
        r"Independent Auditor.{0,2} Report",
        r"Corporate Sustainability",
        r"^Appendix",
        operator=any,
    )

    async def handle_metadata(self, context: PredictionContext, **kwargs) -> Dict[str, Any]:
        pdfinsight = PdfinsightReader.from_path(context.file.pdfinsight_path(abs_path=True), pdf_hash=context.file.pdf)
        metadata = await super().handle_metadata(context, **kwargs)
        for rule in self.FILTER_RULES:
            if elements := metadata.get(rule):
                if rule == PolicyEsgRules.E1:
                    for key, items in elements.items():
                        elements[key] = self.filter_elements(pdfinsight, items)
                    metadata[rule] = elements
                else:
                    metadata[rule] = self.filter_elements(pdfinsight, elements)
        return metadata

    @classmethod
    def filter_elements(cls, pdfinsight, elements) -> list[dict]:
        # 通过content目录页，获取指定章节所在页
        valid_pages = cls.find_pages_by_pattern(pdfinsight, cls.P_VALID_CONTENT)
        invalid_pages = cls.find_pages_by_pattern(pdfinsight, cls.P_VALID_CONTENT)
        return [ele for ele in elements if ele["page"] in valid_pages or ele["page"] not in invalid_pages]

    @classmethod
    def find_pages_by_pattern(cls, pdfinsight, p_content: SearchPatternLike) -> set[int]:
        pages = cls.find_page_from_content(pdfinsight, p_content)
        pages.extend([ele["page"] for ele in cls.find_elements_from_syllabus(p_content, pdfinsight)])
        pages.extend(
            [page for page, title in pdfinsight.page_chapter_from_first_para.items() if p_content.search(title)]
        )
        return set(pages)

    @classmethod
    def find_page_from_content(cls, pdfinsight, p_content: SearchPatternLike) -> list[int]:
        content_pages = []
        start, end = 0, 0
        for text in cls.group_page_content(pdfinsight):
            if p_content.search(text):
                if start == 0 and (res := cls.P_CONTENT_PAGE.nexts(text)):
                    start = int(res.group("page"))
            elif start != 0 and (res := cls.P_CONTENT_PAGE.nexts(text)):
                end = int(res.group("page"))
                if end < start:
                    continue
                content_pages.extend(list(range(start, end)))
                start = 0
        if start != 0:
            content_pages.extend(range(start, pdfinsight.max_page + 1))
        return content_pages
