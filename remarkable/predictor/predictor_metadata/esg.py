from typing import Any, Dict

from remarkable.pdfinsight.reader import Pd<PERSON>sight<PERSON><PERSON>er
from remarkable.predictor.hkex_predictor.esg_util import get_esg_rule_info
from remarkable.predictor.predictor_metadata.metadata_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, PredictionContext


class ESGHandler(MetadataHandler):
    """处理ESG相关的Mold"""

    async def handle_metadata(self, context: PredictionContext, **kwargs) -> Dict[str, Any]:
        metadata = context.base_metadata.copy()

        pdfinsight = PdfinsightReader.from_path(context.file.pdfinsight_path(abs_path=True), pdf_hash=context.file.pdf)
        metadata["esg_rules"] = get_esg_rule_info(pdfinsight)
        return metadata
