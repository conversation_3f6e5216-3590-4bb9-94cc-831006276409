import logging
import re
from operator import itemgetter
from typing import Any, Dict

from remarkable.common.common import JURA6_C2_RULES, is_para_elt, is_table_elt
from remarkable.common.common_pattern import (
    P_NOTES,
    R_CHAPTER_PREFIX,
    R_CHAPTER_SUFFIX,
    R_CN_SPACE,
    R_MIDDLE_DASH,
    R_MIDDLE_DASHES,
)
from remarkable.common.constants import PDFInsight<PERSON>lassEnum, TableType
from remarkable.common.pattern import MatchMulti, NeglectPattern, PositionPattern
from remarkable.common.protocol import SearchPatternLike
from remarkable.common.util import clean_txt, is_in_range, normalize_outline
from remarkable.pdfinsight import clear_syl_title
from remarkable.pdfinsight.chapter_fixer import UnifiedChapter
from remarkable.pdfinsight.parser import parse_table
from remarkable.pdfinsight.reader import Pdfinsight<PERSON>eader
from remarkable.pdfinsight.reader_util import P_CHINESE, UnifiedParaElement, find_table_title
from remarkable.predictor.common_pattern import R_PERCENT_STRICT
from remarkable.predictor.hkex_predictor.model_util import (
    find_children_of_notes_chapter,
    find_syllabuses_by_note_nums,
    get_foot_note_by_cells,
    split_note_num,
)
from remarkable.predictor.hkex_predictor.models.c2 import (
    P_OTHER_NS_CHAPTER,
    P_SIGNIFICANT_INVESTMENT_CHAPTER,
    R_TOTAL_ASSETS,
)
from remarkable.predictor.hkex_predictor.models.total_assets import R_SIGNI_INVEST_NEG_SUBJECTS
from remarkable.predictor.hkex_predictor.schemas.pattern import P_NOTE_CHAPTER, R_NOT
from remarkable.predictor.predictor_metadata.metadata_handler import MetadataHandler, PredictionContext
from remarkable.services.file import Ratio4Result, get_ratio4_by_file

logger = logging.getLogger(__name__)

# 注意有先后顺序，匹配到任意一个就终止
R_C2_KEY_OBJECTS = [
    r"other\s*financial\s*asset",
    # http://************:55647/#/project/remark/408896?treeId=4844&fileId=113521&schemaId=18&projectId=17&schemaKey=C2.1.1
    rf"{r'\s*'.join('financial')}\s*{r'\s*'.join('asset')}",
    r"financial\s*investment",
    r"financial\s*instrument",
    r"other\s*equity\s*instruments?\s*investment",
    r"equity\s*instruments?\s*investment",
    r"other\s*equity\s*securities|securities\s*in\s*other\s*equity",
    r"other\s*equity\s*instrument|instruments?\s*in\s*other\s*equity",
    r"other\s*equity\s*investment|investments?\s*in\s*other\s*equity",
    r"equity\s*securities|securities\s*in\s*equity",
    r"equity\s*instrument|instruments?\s*in\s*equity",
    r"equity\s*investment|investments?\s*in\s*equity",
    r"other\s*investments",
    # http://************:55647/#/project/remark/410876?treeId=3468&fileId=113744&schemaId=18&projectId=17&schemaKey=C2.1.1
    rf"{R_CHAPTER_PREFIX}{R_CN_SPACE}designated",
    r"investments",
]

R_FVTPL = r"\b(FVT?PL|fair\s*value\s*through\s*profit)"
R_FVTOCI = r"\b(FVT?OCI|fair\s*value\s*through\s*other)"
R_HELD_FOR_TRADING = rf"held[{R_MIDDLE_DASHES}\s]*for[{R_MIDDLE_DASHES}\s]*(sale|trading)"
R_HELD_AMORTISED_COST = r"amortised\s*cost"
P_C2_POSSIBLE_CHAPTERS = [
    *[MatchMulti.compile(kw, R_FVTPL, operator=all) for kw in R_C2_KEY_OBJECTS],
    *[MatchMulti.compile(kw, R_FVTOCI, operator=all) for kw in R_C2_KEY_OBJECTS],
    *[MatchMulti.compile(kw, R_HELD_FOR_TRADING, operator=all) for kw in R_C2_KEY_OBJECTS],
    *[MatchMulti.compile(kw, R_HELD_AMORTISED_COST, operator=all) for kw in R_C2_KEY_OBJECTS],
    re.compile(r"Trading\s*portfolio\s*investment", re.I),
    re.compile(r"trading\s*securities", re.I),
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7326#note_732857
    MatchMulti.compile(rf"long[{R_MIDDLE_DASHES}\s]*term", r"equity", r"investment", operator=all),
    MatchMulti.compile(rf"short[{R_MIDDLE_DASHES}\s]*term", r"equity", r"investment", operator=all),
    MatchMulti.compile(r"\bother\s*equity", r"\binstrument", r"\binvestment", operator=all),
    MatchMulti.compile(r"\bother\s*equity", r"\bsecurities", r"\binvestment", operator=all),
    MatchMulti.compile(r"\bequity", r"\binstrument", r"\binvestment", operator=all),
    MatchMulti.compile(r"\bequity", r"\bsecurities", r"\binvestment", operator=all),
    MatchMulti.compile(r"\bsecurities", r"\binvestment", operator=all),
    MatchMulti.compile(r"\bequity", r"\binvestment", operator=all),
    MatchMulti.compile(r"\bequity", r"\binstrument", operator=all),
    MatchMulti.compile(r"other\s*investment", r"fund", operator=all),
    MatchMulti.compile(r"investment", r"fund", operator=all),
    MatchMulti.compile(r"instrument", r"fund", operator=all),
    re.compile(rf"{R_CHAPTER_PREFIX}other\s*((non[{R_MIDDLE_DASHES}\s]?)?current\s*)?financial\s*assets?$", re.I),
    re.compile(rf"{R_CHAPTER_PREFIX}other\s*((non[{R_MIDDLE_DASHES}\s]?)?current\s*)?financial\s*investments?$", re.I),
    re.compile(rf"{R_CHAPTER_PREFIX}financial\s*assets?$", re.I),
    re.compile(rf"{R_CHAPTER_PREFIX}financial\s*investments?$", re.I),
    MatchMulti.compile(rf"long[{R_MIDDLE_DASHES}\s]*term", r"investment", operator=all),
    MatchMulti.compile(rf"short[{R_MIDDLE_DASHES}\s]*term", r"investment", operator=all),
    MatchMulti.compile(r"other\s*investments?$", operator=all),
    re.compile(rf"{R_CHAPTER_PREFIX}(Investments)$", re.I),
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7350
    re.compile(r"crypto\s*currenc(y|ies)", re.I),
]
P_FVTPL = re.compile(rf"(?P<pre>.+){R_FVTPL}", re.I)
P_FVTOCI = re.compile(rf"(?P<pre>.+){R_FVTOCI}", re.I)
# 超过5%的资产投资 http://************:55647/#/project/remark/265539?treeId=44695&fileId=70322&schemaId=18&projectId=17&schemaKey=C2.1.1&page=18 index=303
P_MORE_THAN_5_PERCENT = PositionPattern.compile(r"\s([5-9]|[1-9]\d)(\.\d+)?[%％]", r"\bof\b", R_TOTAL_ASSETS)
# https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7334
P_FOLLOW_SIG_INVESTMENT = MatchMulti.compile(
    rf"\s([5-9]|[1-9]\d)(\.\d+)?{R_PERCENT_STRICT}.*\sof\s.*{R_TOTAL_ASSETS}|(material|significant|major)\s*(securities|investment|financial)",
    r"follow|below",
    r"[:：]$",
    operator=all,
)
# 这些章节不需要校验科目，直接作为候选元素块
P_C2_MUST_CHAPTERS = MatchMulti.compile(
    P_SIGNIFICANT_INVESTMENT_CHAPTER,
    # http://************:55647/#/project/remark/407383?treeId=15095&fileId=113351&schemaId=18&projectId=17&schemaKey=C2.6
    r"^(un)?listed\s*investment\s*review$",
    # http://************:55647/#/project/remark/416338?treeId=7221&fileId=114354&schemaId=18&projectId=17&schemaKey=C2.6
    r"^INVESTMENT PORTFOLIO REVIEW$",
    # http://************:55647/#/project/remark/410403?treeId=5097&fileId=113690&schemaId=18&projectId=17&schemaKey=C2.6
    r"^Major\s*Events$",
    # http://************:55647/#/project/remark/413974?treeId=10475&fileId=114090&schemaId=18&projectId=17&schemaKey=C2.6
    r"^China\s*Everbright\s*Bank\b",
    operator=any,
)
# 其他可能提及了重大投资的章节，这些章节中的内容需要用关键词做过滤
P_C2_OTHER_CHAPTERS = MatchMulti.compile(
    # code:133 2024 http://************:55647/#/project/remark/379680?treeId=4473&fileId=113004&schemaId=18&projectId=4473&schemaKey=C2.5
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7334
    rf"{R_CHAPTER_PREFIX}Investments?\s*(Review|h[eo]ld|Holding|portfolio)s?{R_CHAPTER_SUFFIX}",
    rf"{R_CHAPTER_PREFIX}(the\s*)?(\S+\s+)?investments?{R_CHAPTER_SUFFIX}",
    # http://************:55647/#/project/remark/269023?treeId=7151&fileId=70623&schemaId=18&projectId=17&schemaKey=C2.4
    # http://************:55647/#/project/remark/407383?treeId=15095&fileId=113351&schemaId=18&projectId=17&schemaKey=C2.6
    rf"{R_CHAPTER_PREFIX}(u?n?listed\s*)?(equity\s*)?(investments?|securities)(\s*review)?{R_CHAPTER_SUFFIX}",
    rf"{R_CHAPTER_PREFIX}financial\s*assets?{R_CHAPTER_SUFFIX}",
    # http://************:55647/#/hkex/annual-report-checking/report-review/340986?fileId=104148&schemaId=5&rule=B1&delist=0
    rf"{R_CHAPTER_PREFIX}(proprietary|portfolio|trade|trading|financial|security|securities|other|review\s*of)\s*investments?(\s*business)?{R_CHAPTER_SUFFIX}",
    # http://************:55647/#/project/remark/404989?treeId=15932&fileId=113077&schemaId=18&projectId=17&schemaKey=C2.1.1
    rf"{R_CHAPTER_PREFIX}Strategic\s*investments?(\s*business)?{R_CHAPTER_SUFFIX}",
    # http://************:55647/#/project/remark/405813?treeId=6649&fileId=113174&schemaId=18&projectId=17&schemaKey=C2.1.1
    rf"{R_CHAPTER_PREFIX}(the\s*)?investments?\s*(in|of|for)\s",
    # http://************:55647/#/project/remark/412459?treeId=14072&fileId=113921&schemaId=18&projectId=17&schemaKey=C2.1.1
    # http://************:55647/#/project/remark/414249?treeId=4625&fileId=114121&schemaId=18&projectId=17&schemaKey=C2.4
    # http://************:55647/#/project/remark/413974?treeId=10475&fileId=114090&schemaId=18&projectId=17&schemaKey=C2.4&page=34
    # http://************:55647/#/project/remark/420225?treeId=9010&fileId=114791&schemaId=18&projectId=17&schemaKey=C2.4
    # http://************:55647/#/project/remark/417818?treeId=4533&fileId=114519&schemaId=18&projectId=17&schemaKey=C2.4
    rf"({R_CHAPTER_PREFIX}|^{R_MIDDLE_DASH}\s*)(securit(y|ies)|(financial\s*)?assets?|Cornerstone|private)\s*investment",
    # code:8033 2024
    rf"{R_CHAPTER_PREFIX}other\s*investments?\s*(h[eo]ld|holdings?)",
    # http://************:55647/#/project/remark/404827?treeId=44720&fileId=113059&schemaId=18&projectId=17&schemaKey=C2.4
    rf"{R_CHAPTER_PREFIX}ACQUISITION[（(]?S?[)）]?\s*OF\s*TREASURY\s*SECURITIES",
    # http://************:55647/#/project/remark/417818?treeId=4533&fileId=114519&schemaId=18&projectId=17&schemaKey=C2.4 章节识别错误，只能取上一个章节
    rf"{R_CHAPTER_PREFIX}Provision\s*of\s*Financial\s*Business",
    # http://************:55647/#/project/remark/416995?treeId=28543&fileId=113721&schemaId=18&projectId=17&schemaKey=C2.1.1
    P_OTHER_NS_CHAPTER,
    operator=any,
)
# https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7236#note_725936
R_C2_NEG_KW = r"\b((?<!and )(?<![,，])(?<![,，] )debt|associate|joint\s*(venture|operation)|derivative|subsidiar(y|ies)|liabilities)"
# treasury note: http://************:55647/#/project/remark/413918?treeId=38036&fileId=114084&schemaId=18&projectId=17&schemaKey=C2.4&page=26
R_FIXED_RATE = r"\bfixed\s*([a-z]+\s*)?rate|treasury\s*note"
P_C2_NEG_ACCOUNTS = MatchMulti.compile(*R_SIGNI_INVEST_NEG_SUBJECTS, r"\bcall\s*option", R_FIXED_RATE, operator=any)
P_C2_NEG_CHAPTERS = MatchMulti.compile(
    *R_SIGNI_INVEST_NEG_SUBJECTS,
    rf"{R_C2_NEG_KW}|\bfuture|increase|decrease",
    # http://************:55647/#/project/remark/416347?treeId=38056&fileId=114355&schemaId=18&projectId=17&schemaKey=C2.1.1
    r"form\s*20-F",
    operator=any,
)
# https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7236#note_725936
P_C2_NEG_ELEMENTS = MatchMulti.compile(
    # 收购某个公司的所有股份，相当收购子公司：https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7195#note_725218
    # http://************:55647/#/project/remark/409003?treeId=7191&fileId=113533&schemaId=18&projectId=17&schemaKey=C2.3
    PositionPattern.compile(
        r"\b(acquir(ed?|ing)|acquisition)",
        rf"\b(entire|whole|100{R_PERCENT_STRICT})\s*([a-z]+\s+)?(equity|share|capital)",
    ),
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7330
    PositionPattern.compile(
        r"\b(agreed\s*to\s*acquire|acquisition)", rf"100{R_PERCENT_STRICT}\s*of", r"\b(share|capital|equity)"
    ),
    # 标的公司是子公司： https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7330
    # http://************:55647/#/project/remark/405514?treeId=5915&fileId=113139&schemaId=18&projectId=17&schemaKey=C2.1.1&page=91 index=878
    PositionPattern.compile(
        r"target\s*compan(y|ies)",
        r"\b(remain|become|will\s*be|was|is|were|are)",
        r"subsidiar(y|ies)\s*of\s*the\s*(company|group)",
    ),
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7236#note_726407
    r"deconsolidated",
    NeglectPattern.compile(
        match=rf"{R_C2_NEG_KW}\s*(investment|instrument|securities)",
        unmatch=rf"{R_NOT}|\bmight\b|\bmay\b|\binclud(e|ing)|limited\s*to\b|\bwide\s*range|listed\s*equity",
    ),
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7195#note_725220
    rf"\binvestments?\s*in\s+{R_C2_NEG_KW}",
    # 对于投资一个公司研发的产品，不属于重大投资内容 code=0836 2023
    r"(?<!responsible)\sfor\s*(the\s*)?development",
    # 低于5%的重大投资不提取
    # rf"\s((?<!more than )(?<!not less than )[0-4](\.\d+)?|(?<!not )less\s*than\s*5(\.0)?)[%％][^%％]*?{R_TOTAL_ASSETS}",
    R_FIXED_RATE,
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7436#note_760163
    r"\san\s*associate|\bcall\s*option|\sloan",
    operator=any,
)
R_COMPANY_NAMES = [
    r"\s(?i:Ltd|Co|Inc|Corp|Cie).?\b",
    r"(?<!Hong Kong )(?<!Exchange )\bL(?i:imited)(?!\s*partnership)",
    r"\s[A-Z][a-z]+\s+(?<!The )(Group|GROUP)",
    r"\bHSBC\b|Berhad",
    r"Bank\s*of\s*[A-Z]|Sports",
    r"[A-Z][a-zA-Z]+\s*Global",
    r"有限([責责]任)?公司|集团|集團|[創创]投|保[险險]公司",
    r"[\"“”](?!FV|AC)[A-Z]+[\"“”]",
    r"[,，]\s*[A-Z]\.[A-Z]\.",
    # http://************:55647/#/project/remark/416797?treeId=6255&fileId=114405&schemaId=18&projectId=17&schemaKey=C2.4 中国育儿网络
    r"\bCPN\b",
    # http://************:55647/#/project/remark/266194?treeId=45553&fileId=70453&schemaId=18&projectId=17&schemaKey=C2.4
    r"\bEntity\s*[A-Z]",
]
P_INVALID_SUBTITLE = MatchMulti.compile(*R_COMPANY_NAMES, operator=any, flag=0)
# P_VALID_TBL_COL_HEADERS = MatchMulti.compile(
#     r"(fair|book|market|carrying)\s*value",
#     r"book\s*balance",
#     r"(^|\t)investment\s*cost$",
#     r"(^|\t)carrying\s*amount",
#     operator=any,
# )
P_C2_TOTAL_ROW_HEADER = MatchMulti.compile(
    rf"\b((sub{R_MIDDLE_DASH}?)?total|less)\b",
    rf"^{R_MIDDLE_DASH}?\s*(non{R_MIDDLE_DASH})?current",
    rf"^{R_CN_SPACE}analys",
    operator=any,
)


class C2RuleHandler(MetadataHandler):
    def is_necessary(self, **kwargs) -> bool:
        """
        C2.1-C2.7才需要ratio4相关结果
        """
        special_rules = kwargs.get("special_rules")
        if not special_rules:
            return True
        return set(special_rules) & JURA6_C2_RULES

    async def handle_metadata(self, context: PredictionContext, **kwargs) -> Dict[str, Any]:
        metadata = context.base_metadata.copy()
        company_name = metadata.get("company_name")
        ratio4_result: Ratio4Result = await get_ratio4_by_file(context.file.id, notify_mm=True)
        if ratio4_result.reason != "":
            logger.warning(f"ratio4 not found for file {context.file.id}, {ratio4_result.reason=}")
        metadata["ratio4"] = ratio4_result
        pdfinsight = PdfinsightReader.from_path(context.file.pdfinsight_path(abs_path=True), pdf_hash=context.file.pdf)
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7195
        metadata["c2_candidate_elements"] = self.find_c2_significant_elements(
            pdfinsight, ratio4_result.formulas, company_name
        )
        return metadata

    @classmethod
    def find_c2_significant_elements(cls, pdfinsight, formulas, company_name):
        """
        TODO 多个科目在同一个汇总章节下，例如：financial investments时，段落需要用科目关键词过滤（遇到特例再实现）
        """
        count_accounts = 0
        ge5_accounts, ge5_note_nums = set(), set()
        for formula in formulas:
            count_accounts += 1
            if formula["ratio"] < 0.05:
                continue
            for section in formula["sections"]:
                if section["label"] != "Significant Investment":
                    continue
                for item in section["items"]:
                    account = item.get("account") or {}
                    if text := account.get("text"):
                        ge5_accounts.add(text)
                        # 添加重大投资所在的note子章节
                        page, box = account.get("page"), account.get("box")
                        if page and box and (note_num := cls.find_c2_note_num(pdfinsight, page, box)):
                            ge5_note_nums.add(note_num)
        # 返回重大投资比例>=5%的章节下可用元素块
        account_patterns = cls.get_c2_account_patterns(ge5_accounts)
        # 当有且仅有1个重大投资科目时，other章节内容直接提取
        # http://************:55647/#/project/remark/404989?treeId=15932&fileId=113077&schemaId=18&projectId=17&schemaKey=C2.1.1
        check_account = not count_accounts == len(ge5_accounts) == 1
        return cls.find_c2_candidate_elements(
            pdfinsight, account_patterns, ge5_note_nums, company_name, len(ge5_accounts), check_account
        )

    @classmethod
    def find_c2_candidate_elements(
        cls,
        pdfinsight: PdfinsightReader,
        account_patterns: list[SearchPatternLike],
        note_nums: set[str],
        company_name: str,
        count_accounts: int,
        check_account: bool,
    ):
        """
        根据投资比例超过5%的科目名称+科目所属的note子章节提取候选元素块
        """
        count_notes, syllabuses, other_syllabuses, black_patterns = cls.find_c2_syllabuses(
            pdfinsight, account_patterns, note_nums
        )
        # 找到的notes子章节的个数与重大科目个数不一致，需要再进一步提取notes子章节
        if count_notes < count_accounts and (notes_children := find_children_of_notes_chapter(pdfinsight)):
            notes_chapters = find_syllabuses_by_note_nums(pdfinsight, notes_children, note_nums)
            for chapter in notes_chapters:
                if patterns := cls.get_black_account_by_syllabus(pdfinsight, chapter):
                    black_patterns.update(patterns)
                    continue
                # 章节标题中包含过多空格，则不匹配account
                # http://************:55647/#/project/remark/408896?treeId=4844&fileId=113521&schemaId=18&projectId=17&schemaKey=C2.1.1
                is_more_blank_title = len([t for t in chapter["title"].split() if len(t) == 1]) > 5
                if (
                    check_account
                    and not is_more_blank_title
                    and not any(p.search(chapter["title"]) for p in account_patterns)
                ):
                    # note 章节无法匹配科目，则添加到其他章节，用科目做过滤
                    # http://************:55647/#/project/remark/410324?treeId=12154&fileId=113681&schemaId=18&projectId=17&schemaKey=C2.1.1
                    other_syllabuses.append(chapter)
                else:
                    syllabuses.append(chapter)
        # 如果黑名单科目刚好等于所有>5%的科目，说明没有候选元素块
        if black_patterns and black_patterns == set(account_patterns):
            return []
        result_elements = []
        checked_ranges, existed_indices = [], set()
        if not check_account:
            syllabuses.extend(other_syllabuses)
        for syllabus in syllabuses:
            start, end = syllabus["range"]
            if cls.is_contained_syllabus(start, end, checked_ranges):
                continue
            # 跳过黑名单科目
            if any(p.search(syllabus["title"]) for p in black_patterns):
                continue
            result_elements.extend(
                cls.get_c2_elements_by_syllabus(
                    pdfinsight, syllabus, company_name, existed_indices, black_patterns, is_account_syllabus=True
                )
            )
            checked_ranges.append([start, end])
        # 追加其他章节元素块
        if check_account:
            other_elements = cls.find_elements_from_other_syllabus(
                pdfinsight, other_syllabuses, company_name, account_patterns, black_patterns, existed_indices
            )
            result_elements.extend(other_elements)
        return sorted(result_elements, key=itemgetter("index"))

    @classmethod
    def find_elements_from_other_syllabus(
        cls, pdfinsight, other_syllabuses, company_name, account_patterns, black_patterns, existed_indices
    ):
        """
        一些模糊章节，例如investments章节下，需要根据科目关键词过滤元素块
        """
        result_elements = []
        for syllabus in other_syllabuses:
            elements = cls.get_c2_elements_by_syllabus(
                pdfinsight, syllabus, company_name, existed_indices, black_patterns
            )
            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7334
            _, first_para = pdfinsight.find_element_by_index(syllabus["range"][0] + 1)
            if is_para_elt(first_para) and P_FOLLOW_SIG_INVESTMENT.search(first_para["text"]):
                return elements
            if P_MORE_THAN_5_PERCENT.search(syllabus["title"]):
                return elements
            for element in elements:
                if element["index"] in existed_indices:
                    continue
                if is_para_elt(element):
                    text = clean_txt(element["text"])
                    # TODO 第一句上面是小章节，则取整个小章节
                    if P_MORE_THAN_5_PERCENT.search(text) or any(p.search(text) for p in account_patterns):
                        result_elements.append(element)
                        existed_indices.add(element["index"])
                elif is_table_elt(element):
                    if table_elements := cls.get_c2_table_elements(
                        pdfinsight, element, [P_MORE_THAN_5_PERCENT, *account_patterns]
                    ):
                        result_elements.extend(table_elements)
                        existed_indices.update({element["index"]} | {e["index"] for e in table_elements})
        return result_elements

    @classmethod
    def get_c2_elements_by_syllabus(
        cls, pdfinsight, syllabus, company_name, exclude_indices, black_patterns, is_account_syllabus=False
    ):
        result_elements = []
        existed_indices = set()
        for index in range(*syllabus["range"]):
            if index in exclude_indices | existed_indices:
                continue
            existed_indices.add(index)
            _, element = pdfinsight.find_element_by_index(index)
            if company_name and pdfinsight.is_page_first_para(element):
                text = clear_syl_title(element["text"], remove_cn_text=True, remove_bound_num=True).upper()
                # 页眉被识别成段落，需要排除： https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/3168#note_725774
                if text.startswith(company_name) or text.endswith(company_name):
                    continue
            if pdfinsight.is_skip_element(
                element, skip_tbl_unit=True, aim_types={PDFInsightClassEnum.PARAGRAPH, PDFInsightClassEnum.TABLE}
            ):
                continue
            if not cls.is_c2_valid_element(pdfinsight, syllabus, element, black_patterns, is_account_syllabus):
                continue
            if is_table_elt(element):
                # todo 章节名称是三个科目，下面刚好3个小章节，按序判断到底哪个科目是重大投资科目
                # todo http://************:55647/#/project/remark/412187?treeId=5295&fileId=113891&schemaId=18&projectId=17&schemaKey=C2.3&page=19 tbl_index=279
                # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7195#note_725422
                # 表名或所有subtitle中都包含debt investments等关键词，则跳过表格及下方所有notes元素块
                table = parse_table(element, tabletype=TableType.TUPLE.value, pdfinsight_reader=pdfinsight)
                title = find_table_title(pdfinsight, element["index"]) or ""
                # 带符号的标题，如：• - 等，不是表格的表名，此时取章节名
                # http://************:55647/#/project/remark/409241?treeId=5193&fileId=113559&schemaId=18&projectId=17&schemaKey=C2.4&page=168 章节：note 27
                if is_account_syllabus and title.lstrip().startswith(tuple(R_MIDDLE_DASHES) + ("•", "*", "#")):
                    title = syllabus["title"]
                # http://************:55647/#/project/remark/406553?treeId=5815&fileId=113257&schemaId=18&projectId=17&schemaKey=C2.1.1 tbl=1698+1705 换行的公司名被误识别为subtitle
                if P_C2_NEG_ELEMENTS.search(title) or cls.is_black_table(table, strict=True):
                    existed_indices.update({index} | {e["index"] for e in table.footnotes})
                    continue
                # 识别问题：表格第一行是章节，被识别为单元格：http://************:55647/#/project/remark/410341?treeId=7162&fileId=113683&schemaId=18&projectId=17&schemaKey=C2.4&page=284
                if len({c.clean_text for c in table.rows[0] if c.clean_text}) < 3 and any(
                    P_C2_NEG_CHAPTERS.search(c.clean_text) for c in table.rows[0] if c.clean_text
                ):
                    existed_indices.update({index} | {e["index"] for e in table.footnotes})
                    continue
                # 行名为黑名单，则跳过对应的脚注
                # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7431#note_740087
                # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7431#note_740198
                # http://************:55647/#/project/remark/416677?treeId=12228&fileId=114392&schemaId=18&projectId=17&schemaKey=C2.1.1&page=162 tbl_index=2013
                # http://************:55647/#/project/remark/416471?treeId=7054&fileId=114369&schemaId=18&projectId=17&schemaKey=C2.1.1&page=151 tbl_index=1678
                for cells in table.row_header:
                    # subtitle 是黑名单
                    # http://************:55647/#/project/remark/409821?treeId=25010&fileId=113625&schemaId=18&projectId=17&schemaKey=C2.1.1&page=212 index=2397
                    if any(
                        cell.clean_text
                        and P_C2_NEG_ACCOUNTS.search(cell.clean_text)
                        or any(P_C2_NEG_ACCOUNTS.search(c.text) for c in cell.subtitle_cells)
                        for cell in cells
                    ):
                        row_cells = table.rows[cells[0].rowidx]
                        existed_indices.update({e["index"] for e in get_foot_note_by_cells(table, row_cells)})
            result_elements.append(element)
        return result_elements

    @staticmethod
    def get_c2_table_elements(pdfinsight, element, patterns: list):
        table = parse_table(element, tabletype=TableType.TUPLE.value, pdfinsight_reader=pdfinsight)
        # # http://************:55647/#/project/remark/407508?treeId=7885&fileId=113365&schemaId=18&projectId=17&schemaKey=C2.4&page=184 排除只有黑名单+大类的表格
        # if all(
        #     any(p.search("\t".join(c.clean_text for c in cells)) for p in [P_C2_NEG_ACCOUNTS, *P_C2_POSSIBLE_CHAPTERS])
        #     for cells in table.data_row_header
        # ):
        #     return []
        title = find_table_title(pdfinsight, element["index"]) or ""
        if any(p.search(title) for p in patterns) or any(
            any(p.search(t) for t in table.row_header_texts.values()) for p in patterns
        ):
            table = parse_table(element, tabletype=TableType.TUPLE.value, pdfinsight_reader=pdfinsight)
            return [element, *table.footnotes]
        # 列名中包含investment cost/fair value/book value/carrying value等，则该表格也需要
        # http://************:55647/#/project/remark/414249?treeId=4625&fileId=114121&schemaId=18&projectId=17&schemaKey=C2.4
        # if any(P_VALID_TBL_COL_HEADERS.search(c.clean_text) for cells in table.col_header for c in cells):
        #     return [element]
        return []

    @staticmethod
    def is_c2_valid_element(pdfinsight, syllabus, element, black_patterns, is_account_syllabus):
        """
        跳过黑名单章节及黑名单分类的元素块
        """
        # 明确的重大投资章节不判断neg章节：http://************:55647/#/project/remark/408138?treeId=5253&fileId=113436&schemaId=18&projectId=17&schemaKey=C2.4
        if not P_C2_MUST_CHAPTERS.search(syllabus["title"]):
            # notes结果中可能没有index，只有范围
            for elt_syllabus in pdfinsight.get_real_parent_syllabuses(syllabus) + [
                s for s in pdfinsight.find_syllabuses_by_index(element["index"]) if s["index"] > syllabus["index"]
            ]:
                if is_account_syllabus and elt_syllabus["index"] == syllabus.get("index"):
                    continue
                # 以句号结尾的标题，可能属于错误识别： http://************:55647/#/project/remark/413974?treeId=10475&fileId=114090&schemaId=18&projectId=17&schemaKey=C2.6&page=24 index=212
                if syllabus["title"].endswith("."):
                    continue
                if P_C2_NEG_CHAPTERS.search(elt_syllabus["title"]):
                    return False
        if not is_para_elt(element):
            return True
        return not any(p.search(clean_txt(element["text"])) for p in [*black_patterns, P_C2_NEG_ELEMENTS])

    @staticmethod
    def get_c2_account_patterns(accounts):
        """
        根据重大投资科目生成章节正则
        """
        account_patterns = []
        for account in accounts:
            text = P_CHINESE.sub(" ", account)
            text = clean_txt(text, remove_cn_text=True)
            # account_patterns.append(MatchMulti.compile(re.escape(text), operator=any))
            for pattern in P_C2_POSSIBLE_CHAPTERS:
                if pattern not in account_patterns and pattern.search(text):
                    account_patterns.append(pattern)
                    break
        return account_patterns

    @staticmethod
    def is_contained_syllabus(start, end, ranges):
        return any(is_in_range(start, rng) and end <= rng[-1] for rng in ranges)

    @staticmethod
    def get_syllabus_prefix(pdfinsight, syllabus):
        _, syll_element = pdfinsight.find_element_by_index(syllabus["element"])
        para_elt = UnifiedParaElement(syll_element, pdfinsight)
        return para_elt.prefix_words.strip().rstrip(".")

    @classmethod
    def is_valid_note_syllabus(cls, pdfinsight, syllabus, note_nums):
        # http://************:55647/#/project/remark/381286?treeId=13293&fileId=113124&schemaId=18&projectId=17&schemaKey=C2.4
        prefix_pattern = re.compile(rf"\b{re.escape(syllabus['title'].split('.', maxsplit=1)[0])}$")
        if any(prefix_pattern.search(n) for n in note_nums):
            return True
        # note下的子章节根据note_nums做过滤
        para_prefix = cls.get_syllabus_prefix(pdfinsight, syllabus)
        for note_num in note_nums:
            _, num, sub_header = split_note_num(note_num)
            if not num:
                continue
            # http://************:55647/#/project/remark/411879?treeId=6352&fileId=113857&schemaId=18&projectId=17&schemaKey=C2.1.1
            num = num.rstrip(".")
            if not sub_header and para_prefix == num:
                return True
            if not sub_header or sub_header != para_prefix:
                continue
            parent_syllabus = pdfinsight.get_real_parent_syllabuses(syllabus)
            if len(parent_syllabus) < 2:
                continue
            parent_prefix = cls.get_syllabus_prefix(pdfinsight, parent_syllabus[1])
            if parent_prefix == num:
                return True
        return False

    @classmethod
    def is_black_table(cls, table, strict=False):
        """
        判断表格是否为黑名单表格，满足如下条件之一：
        1. 所有的subtitle都是黑名单
        2. 所有数据行都是黑名单（排除表名及total行）
        """
        data_rows = []
        subtitles = {t.title for t in table.all_subtitles.values()}
        # P_INVALID_SUBTITLE 换行的公司名被误识别为subtitle
        # http://************:55647/#/project/remark/406553?treeId=5815&fileId=113257&schemaId=18&projectId=17&schemaKey=C2.1.1 tbl=1698+1705
        if subtitles and all(P_C2_NEG_ACCOUNTS.search(t) or P_INVALID_SUBTITLE.search(t) for t in subtitles):
            return True
        for cells in table.data_row_header:
            row_header = "\t".join(c.clean_text for c in cells)
            if not row_header or P_C2_TOTAL_ROW_HEADER.search(row_header):
                continue
            row_index = cells[0].rowidx
            if row_index in table.all_subtitles:
                continue
            data_rows.extend([r for r in table.rows if r[0].rowidx == row_index])
        if strict and not data_rows:
            return True
        # http://************:55647/#/project/remark/406398?treeId=13124&fileId=113240&schemaId=18&projectId=17&schemaKey=C2.4&page=212
        # http://************:55647/#/project/remark/416632?treeId=19309&fileId=114387&schemaId=18&projectId=17&schemaKey=C2.4&page=21 index=281
        # http://************:55647/#/project/remark/413918?treeId=38036&fileId=114084&schemaId=18&projectId=17&schemaKey=C2.4&page=26 index=272
        return data_rows and all(any(P_C2_NEG_ACCOUNTS.search(c.clean_text) for c in cells) for cells in data_rows)

    @classmethod
    def get_black_account_by_syllabus(cls, pdfinsight, syllabus):
        """
        根据note下的第一章表格，判断当前科目是否为黑名单科目，如果是，则根据章节标题生成对应科目的正则
        https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7326#note_732857
        """
        for index in range(*syllabus["range"]):
            _, element = pdfinsight.find_element_by_index(index)
            if not is_table_elt(element):
                continue
            table = parse_table(element, tabletype=TableType.TUPLE.value, pdfinsight_reader=pdfinsight)
            if cls.is_black_table(table):
                return cls.get_c2_account_patterns([syllabus["title"]])
            break
        return []

    @classmethod
    def find_real_syllabuses(cls, pdfinsight, syllabus):
        # 章节范围为1，则找下一个章节及其兄弟章节作为章节所有范围（为避免找错，仅找3个）
        # http://************:55647/#/project/remark/414392?projectId=17&treeId=11113&fileId=114137&schemaId=18&page=13  stock=00116， year=2024， index=248
        start, end = syllabus["range"]
        if end - start != 1 or not (next_syllabus := pdfinsight.syllabus_reader.elt_syllabus_dict.get(end)):
            return [syllabus]
        base_font = UnifiedChapter(next_syllabus, pdfinsight).font_style
        syllabuses = []
        for child_idx in (
            pdfinsight.syllabus_reader.syllabus_dict.get(next_syllabus["parent"], {}).get("children") or []
        ):
            if child_idx <= syllabus["index"]:
                continue
            child_syllabus = pdfinsight.syllabus_reader.syllabus_dict.get(child_idx)
            if not child_syllabus:
                continue
            child_chapter = UnifiedChapter(child_syllabus, pdfinsight)
            if base_font and not base_font.similarity(child_chapter.font_style):
                break
            if P_C2_NEG_CHAPTERS.search(child_syllabus["title"]):
                continue
            syllabuses.append(child_syllabus)
            # 只找最近的4个章节
            if len(syllabuses) > 3:
                break
        return syllabuses

    @classmethod
    def find_c2_syllabuses(
        cls, pdfinsight: PdfinsightReader, account_patterns: list, note_nums: set[str]
    ) -> tuple[int, list[dict], list[dict], set]:
        """
        通过公式中提到的重大投资科目，扩充可能的重大投资章节名称
        """
        ranges, other_ranges = [], []
        syllabuses, other_syllabuses = [], []
        black_patterns = set()
        count_notes = 0
        for syllabus in pdfinsight.syllabus_reader.syllabuses:
            # 跳过名称过长的章节或root章节
            if syllabus["title"].endswith((".", ":", "：")):
                continue
            start, end = syllabus["range"]
            # 重大投资章节直接取： http://************:55647/#/project/remark/294553?treeId=2883&fileId=70792&schemaId=18&projectId=2883&schemaKey=C2.6
            if P_C2_MUST_CHAPTERS.search(syllabus["title"]):
                # 范围去重
                if cls.is_contained_syllabus(start, end, ranges):
                    continue
                ranges.append([start, end])
                syllabuses.append(syllabus)
                continue
            # 明确的重大投资科目章节
            is_account_syllabus = any(p.search(syllabus["title"]) for p in account_patterns)
            if not is_account_syllabus and syllabus["parent"] == -1:
                continue
            # 可能的章节
            is_other_syllabus = P_C2_OTHER_CHAPTERS.search(syllabus["title"])
            if not (is_other_syllabus or is_account_syllabus):
                continue
            is_notes_syllabus = P_NOTE_CHAPTER.search(
                pdfinsight.get_root_syllabus_title(syllabus, is_syllabus=True) or ""
            )
            # note下的子章节根据note_nums做过滤
            if (
                not is_other_syllabus
                and is_notes_syllabus
                and note_nums
                and not cls.is_valid_note_syllabus(pdfinsight, syllabus, note_nums)
            ):
                continue
            if is_account_syllabus:
                if is_notes_syllabus:
                    # 找下方的第一张表格，判断是否为黑名单
                    count_notes += 1
                    # 根据note表格判断章节所属科目是否为黑名单科目
                    if patterns := cls.get_black_account_by_syllabus(pdfinsight, syllabus):
                        black_patterns.update(patterns)
                        continue
                for real_syllabus in cls.find_real_syllabuses(pdfinsight, syllabus):
                    if cls.is_contained_syllabus(*real_syllabus["range"], ranges):
                        continue
                    ranges.append(real_syllabus["range"])
                    syllabuses.append(real_syllabus)
            # notes章节下要排除fair value measurement章节
            elif is_other_syllabus and not is_notes_syllabus:
                if P_C2_NEG_CHAPTERS.search(syllabus["title"]):
                    continue
                for real_syllabus in cls.find_real_syllabuses(pdfinsight, syllabus):
                    if cls.is_contained_syllabus(*real_syllabus["range"], other_ranges + ranges):
                        continue
                    other_ranges.append(real_syllabus["range"])
                    other_syllabuses.append(real_syllabus)
        return count_notes, syllabuses, other_syllabuses, black_patterns

    @staticmethod
    def find_c2_note_num(pdfinsight, page, box) -> str | None:
        """
        根据公式所在文本的box，定位到对应表格的行，再取出这行中，列名为Note xxx对应的数据
        """
        _, tbl_element = pdfinsight.find_element_by_outline(page, box)
        if not is_table_elt(tbl_element):
            return None
        outline = normalize_outline(box)
        raw_cell_index = pdfinsight.find_cell_idx_by_outline(tbl_element, outline, page)
        if not raw_cell_index:
            return None
        table = parse_table(tbl_element, tabletype=TableType.TUPLE, pdfinsight_reader=pdfinsight)
        cells = [table.raw_cell_map.get(raw_cell_index)]
        # note没在行上，在行所属的subtitle上
        # http://************:55647/#/project/remark/265279?treeId=14095&fileId=70270&schemaId=18&projectId=17&schemaKey=C2.1.1&page=198 index=2133
        if len(cells[0].subtitle_cells) > 1:
            cells.append(cells[0].subtitle_cells[0])
        if not cells:
            return None
        note_num, col_text = None, None
        for cell in cells:
            col_header_texts = [(c, t) for c, t in table.col_header_texts.items() if P_NOTES.search(t)]
            if not col_header_texts:
                continue
            col_idx, col_text = col_header_texts[0]
            if note_num := clean_txt(table.rows[cell.rowidx][col_idx].text, remove_cn_text=True):
                break
        if not note_num:
            return None
        col_text = clean_txt(col_text, remove_cn_text=True).lower().strip().replace("notes", "note")
        if " " in col_text:
            # 列名可能是NOTE I，行中的小序号是NOTE I下面的子章节
            parent_num = col_text.split()[1]
            return f"note {parent_num}@{note_num}"
        return f"note {note_num}"
