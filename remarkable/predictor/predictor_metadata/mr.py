from typing import Any, Dict

from remarkable.db import pw_db
from remarkable.models.mr_meta import MR<PERSON><PERSON>
from remarkable.predictor.predictor_metadata import <PERSON>adata<PERSON><PERSON><PERSON>
from remarkable.predictor.predictor_metadata.metadata_handler import PredictionContext


class MRHandler(MetadataHandler):
    async def handle_metadata(self, context: PredictionContext, **kwargs) -> Dict[str, Any]:
        """
        get hkex_file by fid, extract the ended date from the file
        """
        metadata = context.base_metadata.copy()
        if mr_meta := await pw_db.first(MRMeta.select().where(MRMeta.file_id == context.file.id)):
            metadata["ended_date"] = mr_meta.ended_date
        return metadata
