from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import Any, Dict, List

from remarkable.models.hkex_file_meta import HKEXFileMeta
from remarkable.models.new_file import NewFile
from remarkable.models.new_question import NewQuestion


@dataclass
class PredictionContext:
    file: NewFile
    question: NewQuestion
    file_meta: HKEXFileMeta | None
    doc_type: str
    base_metadata: Dict[str, Any]


class MetadataHandler(ABC):
    def is_necessary(self, **kwargs) -> bool:
        """是否需要执行handle_metadata"""
        return True

    @abstractmethod
    async def handle_metadata(self, context: PredictionContext, **kwargs) -> Dict[str, Any]:
        pass


class CompositeHandler(MetadataHandler):
    """组合处理器，可以包含多个处理器"""

    def __init__(self, handlers: List[MetadataHandler]):
        self.handlers = handlers

    async def handle_metadata(self, context: PredictionContext, **kwargs) -> Dict[str, Any]:
        metadata = context.base_metadata.copy()

        for handler in self.handlers:
            if not handler.is_necessary(**kwargs):
                continue
            handler_metadata = await handler.handle_metadata(
                PredictionContext(
                    file=context.file,
                    question=context.question,
                    file_meta=context.file_meta,
                    doc_type=context.doc_type,
                    base_metadata=metadata,
                ),
                **kwargs,
            )
            metadata.update(handler_metadata)

        return metadata
