import logging
from typing import Any, Dict

from remarkable.common.constants import AnnualReportEnum
from remarkable.models.mold import special_mold
from remarkable.pdfinsight.reader import PdfinsightReader
from remarkable.predictor.predictor_metadata.metadata_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, PredictionContext
from remarkable.prompter.impl.v4_embedding import Embedding<PERSON>rompter
from remarkable.services.director import (
    find_ined_director,
    get_definition_elements,
    get_full_text_elements,
    get_notice_elements,
)

logger = logging.getLogger(__name__)


class AGMHandler(MetadataHandler):
    async def handle_metadata(self, context: PredictionContext, **kwargs) -> Dict[str, Any]:
        metadata = context.base_metadata.copy()
        metadata["notice_elements"] = await get_notice_elements(context.file.id)
        metadata["definition_elements"] = await get_definition_elements(context.file.id)
        metadata["full_elements"] = await get_full_text_elements(context.file.id)

        logger.info("gen ps_crude_answer...")
        pdfinsight = PdfinsightReader.from_path(context.file.pdfinsight_path(abs_path=True), pdf_hash=context.file.pdf)
        embedding_prompter_client = EmbeddingPrompter(special_mold.v6_agm_id)
        metadata["ps_crude_answer"] = await embedding_prompter_client.prompt_for_file(
            context.file, rules=kwargs.get("special_rules"), special_enum=AnnualReportEnum.PS, pdfinsight=pdfinsight
        )
        logger.info("gen ns_crude_answer...")
        metadata["ns_crude_answer"] = await embedding_prompter_client.prompt_for_file(
            context.file, rules=kwargs.get("special_rules"), special_enum=AnnualReportEnum.NS, pdfinsight=pdfinsight
        )

        directors = await find_ined_director(context.file.id, stock_code=context.file_meta.stock_code)
        metadata["director"] = directors
        metadata["nine_year_director"] = directors["ined"]
        metadata["new_director"] = directors["new_director"]

        return metadata


class PollResultHandler(MetadataHandler):
    async def handle_metadata(self, context: PredictionContext, **kwargs) -> Dict[str, Any]:
        metadata = context.base_metadata.copy()
        embedding_prompter_client = EmbeddingPrompter(special_mold.v6_poll_id)
        logger.info("gen ps_crude_answer...")
        pdfinsight = PdfinsightReader.from_path(context.file.pdfinsight_path(abs_path=True), pdf_hash=context.file.pdf)
        metadata["ps_crude_answer"] = await embedding_prompter_client.prompt_for_file(
            context.file, rules=kwargs.get("special_rules"), special_enum=AnnualReportEnum.PS, pdfinsight=pdfinsight
        )
        logger.info("gen ns_crude_answer...")
        metadata["ns_crude_answer"] = await embedding_prompter_client.prompt_for_file(
            context.file, rules=kwargs.get("special_rules"), special_enum=AnnualReportEnum.NS, pdfinsight=pdfinsight
        )
        return metadata
