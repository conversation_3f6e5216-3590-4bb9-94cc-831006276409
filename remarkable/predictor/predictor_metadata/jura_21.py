import logging
from typing import Any, Dict

from remarkable.common.common import JURA21_GROUP_BASED_COLS
from remarkable.config import get_config
from remarkable.predictor.predictor_metadata.metadata_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, PredictionContext
from remarkable.predictor.share_group import get_or_create_share_meta
from remarkable.predictor.utils import get_helper_predict_answers
from remarkable.services.question import ctx_only_jura21

logger = logging.getLogger(__name__)


class Jura21Handler(MetadataHandler):
    """处理Jura21相关的Mold"""

    def is_necessary(self, **kwargs) -> bool:
        """
        1. B1-B10不执行handle_metadata
        2. 指定了special_rules且其中没有任何一个rule需要用到GroupBased模型，则不执行handle_metadata
        """
        # 由于only_b1_b10是环境变量，故only_jura21的优先级高于only_b1_b10
        if ctx_only_jura21.get():
            return True
        if get_config("ONLY_B1_B10"):
            return False
        special_rules = kwargs.get("special_rules")
        return not special_rules or bool(set(special_rules) & JURA21_GROUP_BASED_COLS)

    async def handle_metadata(self, context: PredictionContext, **kwargs) -> Dict[str, Any]:
        metadata = context.base_metadata.copy()

        company = "" if not context.file_meta else context.file_meta.name
        helper_answers = await get_helper_predict_answers(context.file)
        share_meta = await get_or_create_share_meta(
            context.file,
            context.question,
            metadata["report_year"],
            company,
            helper_answers=helper_answers,
        )
        metadata.update(share_meta)
        return metadata
