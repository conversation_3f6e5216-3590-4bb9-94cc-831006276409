import re
from collections import defaultdict
from dataclasses import dataclass
from typing import Any, Dict, List, Pattern, Set


@dataclass
class FilterConfig:
    regex_patterns: List[Pattern] = None
    page_range: tuple[int, int] = None
    section_names: List[str] = None
    regex_exclude: bool = False
    element_types: Set[str] = None
    exclude_types: bool = False
    page_positions: List[int] = None
    exclude_positions: List[int] = None


class ElementFilter:
    def __init__(self, pdfinsight):
        self.pdfinsight = pdfinsight
        self._filters = []

    def add_regex_filter(self, patterns: List[str], exclude: bool = False) -> "ElementFilter":
        """添加正则过滤器"""
        compiled_patterns = [re.compile(pattern, re.IGNORECASE) for pattern in patterns]
        self._filters.append(FilterConfig(regex_patterns=compiled_patterns, regex_exclude=exclude))
        return self

    def add_page_filter(self, start_page: int, end_page: int) -> "ElementFilter":
        """添加页面范围过滤器"""
        self._filters.append(FilterConfig(page_range=(start_page, end_page)))
        return self

    def add_section_filter(self, section_names: List[str]) -> "ElementFilter":
        """添加章节过滤器"""
        self._filters.append(FilterConfig(section_names=section_names))
        return self

    def add_element_type_filter(self, element_types: List[str], exclude: bool = False) -> "ElementFilter":
        """
        添加元素类型过滤器

        Args:
            element_types: 要过滤的元素类型列表，如 ['header', 'footer', 'table']
            exclude: 如果为True，则排除这些类型；如果为False，则只保留这些类型
        """
        self._filters.append(FilterConfig(element_types=set(element_types), exclude_types=exclude))
        return self

    def add_page_position_filter(
        self,
        positions: List[int] = None,
        exclude_positions: List[int] = None,
    ) -> "ElementFilter":
        self._filters.append(FilterConfig(page_positions=positions, exclude_positions=exclude_positions))
        return self

    def filter_elements(self, elements: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """应用所有过滤器"""
        filtered_elements = elements

        for filter_config in self._filters:
            if filter_config.regex_patterns:
                filtered_elements = self._apply_regex_filter(
                    filtered_elements, filter_config.regex_patterns, filter_config.regex_exclude
                )

            if filter_config.page_range:
                filtered_elements = self._apply_page_filter(
                    filtered_elements, filter_config.page_range[0], filter_config.page_range[1]
                )

            if filter_config.section_names:
                filtered_elements = self._apply_section_filter(filtered_elements, filter_config.section_names)

            if filter_config.element_types:
                filtered_elements = self._apply_element_type_filter(
                    filtered_elements, filter_config.element_types, filter_config.exclude_types
                )

            if filter_config.page_positions or filter_config.exclude_positions:
                filtered_elements = self._apply_page_position_filter(
                    filtered_elements,
                    filter_config.exclude_positions,
                )

        return filtered_elements

    @staticmethod
    def _apply_regex_filter(
        elements: List[Dict[str, Any]], patterns: List[Pattern], exclude: bool
    ) -> List[Dict[str, Any]]:
        """应用正则过滤"""

        def match_any_pattern(text: str) -> bool:
            return any(pattern.search(text) for pattern in patterns)

        return [element for element in elements if match_any_pattern(element["text"]) != exclude]

    @staticmethod
    def _apply_page_filter(elements: List[Dict[str, Any]], start_page: int, end_page: int) -> List[Dict[str, Any]]:
        """应用页面范围过滤"""
        return [element for element in elements if start_page <= element.get("page", 0) <= end_page]

    @staticmethod
    def _apply_section_filter(elements: List[Dict[str, Any]], section_names: List[str]) -> List[Dict[str, Any]]:
        """应用章节过滤"""
        return [
            element for element in elements if any(section in element.get("section", "") for section in section_names)
        ]

    @staticmethod
    def _apply_element_type_filter(
        elements: List[Dict[str, Any]], element_types: Set[str], exclude: bool
    ) -> List[Dict[str, Any]]:
        """应用元素类型过滤"""
        return [element for element in elements if (element.get("type", "") in element_types) != exclude]

    def _apply_page_position_filter(
        self,
        elements: List[Dict[str, Any]],
        exclude_positions: List[int],
    ) -> List[Dict[str, Any]]:
        page_elements = defaultdict(list)
        for element in elements:
            page_elements[element["page"]].append(element)

        result = []
        for page, page_elems in page_elements.items():
            elements_by_page = self.pdfinsight.find_elements_by_page(page)
            # all_idx = [i['index'] for i in elements_by_page]
            # if page_positions:
            #     special_position_element_idx = [
            #         ele["index"]
            #         for idx, ele in enumerate(all_idx)
            #         if idx in page_positions
            #     ]
            special_position_element_idx = [
                ele["index"] for idx, ele in enumerate(elements_by_page) if idx not in exclude_positions
            ]
            result.extend(e for e in page_elems if e["index"] in special_position_element_idx)

        return result
