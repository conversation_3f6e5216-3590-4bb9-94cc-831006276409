import logging
from typing import Any, Dict

from remarkable.common.common import B1_B7_COLS, B1_B10_COLS
from remarkable.common.constants import AnnualReportEnum
from remarkable.pdfinsight.reader import PdfinsightReader
from remarkable.predictor.predictor_metadata.metadata_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, PredictionContext
from remarkable.prompter.impl.v4_embedding import EmbeddingPrompter
from remarkable.services.addition_data import get_addition_data
from remarkable.services.question import ctx_only_jura21

logger = logging.getLogger(__name__)


class B1B7Handler(MetadataHandler):
    """处理B1-B7相关的Mold"""

    def is_necessary(self, **kwargs) -> bool:
        """
        指定了special_rules且其中没有B1-B10时，不执行handle_metadata
        """
        # 由于only_b1_b10是环境变量，故only_jura21的优先级高于only_b1_b10
        if ctx_only_jura21.get():
            return True
        special_rules = kwargs.get("special_rules")
        return not special_rules or bool(set(B1_B10_COLS) & set(special_rules))

    async def handle_metadata(self, context: PredictionContext, **kwargs) -> Dict[str, Any]:
        metadata = context.base_metadata.copy()
        #   https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/5320
        # helper_answers = await get_helper_predict_answers(context.file)
        # metadata["no_proceeds_elements"] = helper_answers.get("no proceeds") or []

        if context.file_meta and context.file_meta.stock_code:
            metadata["addition_data"] = await get_addition_data(context.file_meta.stock_code, metadata["year_end"])
        special_rules = kwargs.get("special_rules")
        if special_rules and not set(B1_B7_COLS) & set(special_rules):
            return metadata
        # 以下仅针对B1-B7
        prompter = EmbeddingPrompter(context.question.mold)
        pdfinsight = PdfinsightReader.from_path(context.file.pdfinsight_path(abs_path=True), pdf_hash=context.file.pdf)

        if special_rules := kwargs.get("special_rules"):
            embedding_rules = [c for c in B1_B7_COLS if c in special_rules]
        else:
            embedding_rules = B1_B7_COLS
        metadata["ps_crude_elements"] = await prompter.prompt_for_file(
            context.file, rules=embedding_rules, special_enum=AnnualReportEnum.PS, pdfinsight=pdfinsight
        )
        metadata["ns_crude_elements"] = await prompter.prompt_for_file(
            context.file, rules=embedding_rules, special_enum=AnnualReportEnum.NS, pdfinsight=pdfinsight
        )
        return metadata
