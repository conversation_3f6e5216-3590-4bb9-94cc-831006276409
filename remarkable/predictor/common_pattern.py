import re
from itertools import product

from remarkable.common.common_pattern import R_MIDDLE_DASHES
from remarkable.common.pattern import PatternCollection

ZH_NUMBER_CHAR_PATTERN = r"[零一二三四五六七八九十百千万]"

UNIT_LIST = [
    "吨",
    "升",
    "方",
    "立方",
    "立方米",
    "家",
    "公吨",
    "期",
    "头",
    "片",
    "股",
    "套",
    "克",
    "千克",
    "间",
    "桶",
    "单位注册资本",
    "人",
    "户",
    "辆",
    "车次",
    "台",
    "人次",
    "公斤",
    "斤",
    "平米",
    "平方米",
    "亩",
    "公顷",
    "度",
    "瓦",
    "瓦时",
    "千瓦",
    "千瓦时",
    "千千瓦时",
    "兆瓦时",
    "兆瓦",
    "kwh",
    "KWH",
    "分钟",
    "小时",
    "时",
    "年",
    "月",
    "日",
    "天",
    "元",
    "次",
    "倍",
    "％",
    "个百分点",
    "笔",
]
slash_unit_prefix = [
    "元",
    "元次",
    "平米",
    "平方米",
    "次",
]

slash_unit_suffix = [
    "天",
    "月",
    "年",
    "半年",
    "吨",
    "期",
    "股",
]

slash_unit = list(product(slash_unit_prefix, slash_unit_suffix))
UNIT_LIST += [f"{pre}[/⁄]{suffix}" for (pre, suffix) in slash_unit]

# UNIT_PATTERN = "|".join([r"[十百千万亿]*" + u for u in sorted(UNIT_LIST, key=len, reverse=True)])
UNIT_PATTERN = rf"[十百千万亿]*({'|'.join(sorted(UNIT_LIST, key=len, reverse=True))})"
CURRENCY = r"(人民币|美元|日元|欧元|英镑|德国马克|瑞士法郎|法国法郎|加拿大元|菲律宾比索|俄罗斯卢布|新加坡元|韩国元|泰国铢|爱尔兰镑|意大利里拉|卢森堡法郎|荷兰盾|葡萄牙埃斯库多|西班牙比塞塔|印尼盾|马来西亚林吉特|澳大利亚元|港币|奥地利先令|芬兰马克|比利时法郎|新西兰元)"
DIGITAL = r"[-+]?[\d]+[\d,×.百千万亿]*[多余]?[百千万亿]*"
CHINESE_DIGITAL = "(?:零|(?:[一二三四五六七八九十零壹贰叁肆伍陆柒捌玖拾]+[佰仟百千万亿])+[多余]?[佰仟百千万亿]*)"
DATE_PATTERN = (
    r"(?P<dst>[\d一二三四五六七八九〇○OＯ零]{4}\s?(?:半?年[度初中末]?(（?/?年?末）?)?/?|\.|-|/)\s?([\d正元一二三四五六七八九十零〇○Ｏ—~-]{1,4}\s?"
    r"(?:月份?（?/?末?）?|\.|-|/|季度)?(?:([\d]{1,2}|[一二三四五六七八九十零〇○Ｏ]{1,3})[日号]?)?)?)"
)
R_PERCENT_STRICT = r"([%％]|\s+per\s*cent(um)?\.?)"
R_PERCENT = "[%％‰‱]"
DATE_EN_PATTERN = r"""
        \b
        (?:
        (?:
         \d+
         (?:\.|st|nd|rd|th)*    # number followed by a dot, st, nd, rd, or th (optional)
         |                      # or a month name or FY
         (?:(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec|FY)[a-z]*)
        )
        [\s./-]*                # possible date separator or whitespace (optional)
        ){3}
        \b
    """


P_ESG_RULE_DESC = PatternCollection(
    [
        r"Description",
        r"描述",
    ],
    re.I,
)

P_ESG_RULE_SYLLABUS = PatternCollection(
    [
        r"Statement",
        r"Section",
        r"References",
        r"chapter",
        r"聲明",
        r"部分",
        # r'Remarks',
    ],
    re.I,
)

P_ESG_RULE_PAGE = PatternCollection(
    [
        r"Page",
        r"No",
        r"頁數",
        r"disclosure location",
        r"Report location",
    ],
    re.I,
)

P_PAGE = PatternCollection(
    [
        r"P(?P<dst>\d+)",
        r"p\.(?P<dst>\d+)[\)]",
        r"(?P<dst>^\d+$)",
        r"^(?P<dst>\d+)\s?[-–]\s?(?P<dst1>\d+)$",
        r"^p\.\s?(?P<dst>\d+)[-–,]\s?(?P<dst1>\d+)$",
        r"p\.\s?(?P<dst>\d+)$",
    ],
    re.I,
)

P_ESG_DIRECT_ANSWER = PatternCollection(
    [
        r"Not Applicable",
        r"not disclosed",
        r"not involve",
        r"not directly involved",
        r"Explain",
        r"N/A",
        r"will be disclosed",
        r"plans to disclose.*?in the future",
        r"not a relevant and material",
        r"aspect to be irrelevant",
        r"not encounter any issues",
        r"issue is (\w+ )?not material",  # issue is considered not material
        r"Not materially related to.*?business",
        r"no material non-compliance",
        r"insignificant to the Group.*?relevant figures are not disclosed",
        r"not generate significant.*?data were not disclosed",
        r"not material.*?was not recorded",
        r"Not material to",
    ],
    re.I,
)


P_ESG_RULE = PatternCollection(
    [
        r"(?P<dst>A1\.[1-6])",
        r"(?P<dst>A2\.[1-5])",
        r"(?P<dst>A3\.1)",
        r"(?P<dst>A4\.1)",
        r"(?P<dst>B1\.[12])",
        r"(?P<dst>B2\.[123])",
        r"(?P<dst>B3\.[12])",
        r"(?P<dst>B4\.[12])",
        r"(?P<dst>B5\.[1-4]|^[B8]5\.?[1-4]$)",
        r"(?P<dst>B6\.[1-5]|^[B8]6\.?[1-5]$)",
        r"(?P<dst>B7\.[123]|^[B8]7\.?[123]$)",
        r"(?P<dst>B8\.[12]|^[B8]8\.?[12]$)",
    ],
    re.I,
)


P_NOTE = PatternCollection(
    [
        r"note \d",
    ],
    re.I,
)

R_AWARD = r"award(ed|s)?"
R_CANCELLED = r"cancell?ed"

# 标题为incentive plan，可能是option也可能是award，要根据内容判断
R_INCENTIVE_ABBREV = r"\b(MSOP|ESOP|ESPP|LTIP?)\b"
# LTI PLAN = Long Term Incentive Plan
# https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/4559#note_517936
R_SHARE_INCENTIVE = rf"(incentive|ownership|purchased?)\s*(scheme|plan)|stock\s*incentive|(?<!award )(?<!awarded )(?<![AH] )share\s*(scheme|plan)|{R_INCENTIVE_ABBREV}"
P_SHARE_INCENTIVE = re.compile(R_SHARE_INCENTIVE, re.I)
# INCENTIVE章节下，不用标题带number of shares的子章节判断是option还是award
# http://************:55647/#/project/remark/233994?treeId=37676&fileId=66621&schemaId=15&projectId=37676&schemaKey=B94.1&page=104
P_INCENTIVE_SKIP_TITLE = PatternCollection(r"number\s*of\s*shares", flags=re.I)

R_OPTIONS_AWARDS = r"options?\s*(and(/or)?|or)\s*(award|rsu)"
R_SHARE_OPTION = [
    # including的场景：https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/3573#note_457222
    r"(?<![(（]including the )Share\s*option(?!\s*for\b)",
    # http://************:55647/#/project/remark/264318?treeId=37706&fileId=70078&schemaId=15&projectId=17&schemaKey=H83
    r"restricted\s*(share|stock)\s*units?\s*option",
    r"\b(number|no\.)\s*of\s*options",
    r"value\s*of\s*(the\s*)options",
    R_OPTIONS_AWARDS,
]
P_SHARE_OPTION = PatternCollection(R_SHARE_OPTION, flags=re.I)

R_SHARE_AWARD = [
    # ?!(s)的场景见：http://************:55647/#/project/remark/250333?treeId=10931&fileId=68511&schemaId=15&projectId=43974&schemaKey=B93.2 P318
    r"\bRS[UA]s?\b",
    r"shares?\s*award",
    r"award\s*(plan|scheme)",
    r"share\s*grant\s*scheme",
    r"award(s|ed)?\s*share",
    r"restricted\s*([AH]|units?)\s*(share|stock|award)",
    # http://************:55647/#/project/remark/264318?treeId=37706&fileId=70078&schemaId=15&projectId=17&schemaKey=H83
    r"restricted\s*(share|stock|award)(?!\s*(units?\s*)?option)",
    r"Performance\s*share\s*unit",
    r"Share\s*stapled\s*units\s*a\s*ward\s*(scheme|plan)",
    r"share\s*subscription\s*(scheme|plan)",  # TODO 确认这个是否为incentive
]
P_SHARE_AWARD = PatternCollection(R_SHARE_AWARD, flags=re.IGNORECASE)

# incentive章节下句子中的share type匹配
# https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/3573#note_457271
# https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/4574#note_545570
R_INCENTIVE_AWARD = [r"performance-based\s*shares", r"free\s*shares", R_OPTIONS_AWARDS]
P_INCENTIVE_AWARD = PatternCollection([*R_INCENTIVE_AWARD, *R_SHARE_AWARD], flags=re.I)

# incentive章节下表格中的share type匹配
P_TBL_INCEN_OPTION = PatternCollection(r"options", flags=re.I)
P_TBL_INCEN_AWARD = PatternCollection([r"\bawards\b", *R_SHARE_AWARD, r"(number|no\.)\s*of\s*shares"], flags=re.I)

R_PARTITIVES = "Hundred|Thousand|Million|Billion|Trillion|Quadrillion|Quintillion|Sextillion|Septillion|Octillion|Nonillion|Decillion"
R_EN_NUM = rf"(zero|one|two|three|four|five|six|seven|eight|nine|ten|eleven|twelve|thirteen|fourteen|fifteen|sixteen|seventeen|eighteen|nineteen|twenty|thirty|forty|fifty|sixty|seventy|eighty|ninety|{R_PARTITIVES})"
R_EN_MONTH = r"\b(Jan(uary)?|Feb(ruary)?|Mar(ch)?|Apr(il)?|May|June?|July?|Aug(ust)?|S[ae]p(tember)?|[O0]ct(ober)?|Nov(ember)?|Dec(ember)?)\b"
P_EN_MONTH = re.compile(R_EN_MONTH, re.I)
R_ANY_EN_DATE = rf"(\d{{1,2}}(st|nd|rd|th)?\s*{R_EN_MONTH}|{R_EN_MONTH}\s*\d{{1,2}}(st|nd|rd|th)?)[,，]?\s*\d{{4}}"
R_EN_MONTH_DATE = rf"{R_EN_MONTH}\s*\d{{4}}"
R_DATES = [
    # 英式日期
    rf"\b\d{{1,2}}(st|nd|rd|th)?[{R_MIDDLE_DASHES}/\s]*{R_EN_MONTH}[,，]?[{R_MIDDLE_DASHES}/\s]*\d{{4}}\b",
    # 美式日期
    rf"\b{R_EN_MONTH}\.?[{R_MIDDLE_DASHES}/\s]*\d{{1,2}}(st|nd|rd|th)?[,，]?[{R_MIDDLE_DASHES}/\s]*\d{{4}}\b",
    # ISO 8601格式
    rf"\b\d{{4}}[{R_MIDDLE_DASHES}/.]\d{{1,2}}[{R_MIDDLE_DASHES}/.]\d{{1,2}}\b",
    rf"\b\d{{1,2}}[{R_MIDDLE_DASHES}/.]\d{{1,2}}[{R_MIDDLE_DASHES}/.](\d{{2}}|\d{{4}})\b",
    # 日期年份都是2位  http://************:55647/#/project/remark/266309?treeId=4016&fileId=70476&schemaId=18&projectId=17&schemaKey=C7.1 index=1078
    rf"\b(\d{{2}}|\d{{4}})[{R_MIDDLE_DASHES}/]{R_EN_MONTH}[,，]?[{R_MIDDLE_DASHES}/]\d{{1,2}}\b",
    rf"\b\d{{1,2}}[{R_MIDDLE_DASHES}/]{R_EN_MONTH}[,，]?[{R_MIDDLE_DASHES}/](\d{{2}}|\d{{4}})\b",
]
P_YEAR = re.compile(r"(^|[^\d]\s*)(?P<year>\d{4})($|\s*[^\d])")
PC_ONLY_DATE = PatternCollection(
    [rf"^(for\s*the\s*year\s*ended\s*)?{p}$" for p in R_DATES] + [r"^\d{4}$", rf"^{R_EN_MONTH}\d{{4}}$"], flags=re.I
)

R_WEEKDAY = r"(?:\b(?:Mon(?:day)?|Tue(?:sday)?|Wed(?:nesday)?|Thu(?:rsday)?|Fri(?:day)?|Sat(?:urday)?|Sun(?:day)?)\b\s*[,，]?\s*)"
