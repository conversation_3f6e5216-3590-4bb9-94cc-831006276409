import json
import logging
import os
import pickle
from collections import defaultdict, namedtuple
from functools import cached_property
from pathlib import Path

from remarkable.common.constants import ADMIN
from remarkable.common.storage import TRAINING_CACHE_DIR, localstorage
from remarkable.config import get_config
from remarkable.models.new_file import NewFile
from remarkable.pdfinsight.reader import P<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from remarkable.plugins.predict.answer import AnswerReader
from remarkable.predictor.dataset import DatasetItem
from remarkable.predictor.mold_schema import MoldSchema
from remarkable.services.question import UserAnswer, merge_answers

PredictorContext = namedtuple("PredictorContext", ["crude_answer", "reader", "metadata"])
logger = logging.getLogger(__name__)


def find_item_elements(_pdfinsight, item):
    _elements = {}
    for data in item["data"]:
        data.setdefault("elements", [])
        for box in data["boxes"]:
            outline = (box["box"]["box_left"], box["box"]["box_top"], box["box"]["box_right"], box["box"]["box_bottom"])
            for _, ele in _pdfinsight.find_elements_by_outline(box["page"], outline):
                _elements[ele["index"]] = ele
                data["elements"].append(ele["index"])
    return _elements


class BaseProphet:
    debug = True
    depends_root_predictor = True

    def __init__(self, prophet_config, mold, version_id=0):
        self._mold = mold
        self._version_id = str(version_id) if version_id is not None else None
        self.prophet_config = prophet_config
        self.mold_schema = MoldSchema(mold.data)
        self.predictor_config = self.load_config(prophet_config["predictor_options"])
        self.depends = prophet_config.get("depends", {})
        self.answer = defaultdict(list)
        self.predictor_context = None
        # TODO: 需要可以指定 Predictor 实现
        if self.depends_root_predictor:
            from remarkable.predictor.predictor import SchemaPredictor

            self.root_predictor = SchemaPredictor(self.root_schema, {"path": self.root_schema.path}, prophet=self)
        self.answer_version = str(get_config("prompter.answer_version", "1.0"))
        self.enum_predictor = self.get_enum_predictor()

    @property
    def predictor_options(self):
        return self.prophet_config["predictor_options"]

    @property
    def root_schema(self):
        return self.mold_schema.root_schema

    @property
    def predictors(self):
        predictors = self.root_predictor.sub_predictors[:]
        high_priority_names = []
        for names in self.depends.values():
            high_priority_names.extend(names)
        predictors.sort(key=lambda x: 0 if x.schema_name in high_priority_names else 100)
        return predictors

    @property
    def dataset_dir(self):
        return TRAINING_CACHE_DIR.joinpath(f"{self._mold.id}", self._version_id or "", "dataset")

    @property
    def model_data_dir(self):
        return TRAINING_CACHE_DIR.joinpath(f"{self._mold.id}", self._version_id or "", "predictors")

    @property
    def crude_answer(self):
        return self.predictor_context.crude_answer

    @property
    def reader(self):
        return self.predictor_context.reader

    @cached_property
    def interdoc(self):
        from interdoc.collection import Interdoc

        from remarkable.pdfinsight.interdoc_reader import InterdocReader

        cache_dir = localstorage.mount(
            os.path.join(
                localstorage.LABEL_CACHE_ROOT,
                os.path.relpath(self.reader.path, os.path.dirname(os.path.dirname(self.reader.path))),
            )
        )
        localstorage.create_dir(cache_dir)
        interdoc_path = os.path.join(cache_dir, "interdoc.pkl")
        try:
            schema = Interdoc.load(interdoc_path)
        except (OSError, pickle.PickleError) as e:
            logger.error("try to read interdoc from pickle failed, error: %s", e)
            reader = InterdocReader.from_path(self.reader.path)
            reader.schema.dump(interdoc_path, with_char=False)
            schema = reader.schema

        return InterdocReader(schema=schema)

    @property
    def metadata(self):
        return self.predictor_context.metadata

    @property
    def merge_schema_answers(self):
        return self.prophet_config.get("merge_schema_answers", False)

    def parse_value(self, predictor_result):
        schema = predictor_result.schema
        if self.mold_schema.is_enum_schema(schema):
            return self.parse_enum_value(predictor_result, schema, self.metadata)

        return None

    def parse_enum_value(self, predictor_result, schema, metadata):
        raise NotImplementedError

    def get_enum_predictor(self):
        raise NotImplementedError

    async def run_dump_dataset(self, start, end, file_full_label=False):
        from remarkable.predictor.helpers import prepare_prophet_dataset

        files = await prepare_prophet_dataset(self._mold, start, end, prophet=self, file_full_label=file_full_label)
        return files

    def run_train(self):
        self.prepare_dir(self.model_data_dir)
        self.root_predictor.train()

    def run_predict(self, **kwargs):
        self.predictor_context = self._bind_context(
            kwargs["crude_answer"], kwargs.get("file"), metadata=kwargs["metadata"]
        )

        self.predict_answer()
        answer_items = self.collect_answer_items()

        return self.build_question_answer(answer_items)

    # def predict_schema_answer(self, crude_answer, file, schema_name=None):
    #     if schema_name is None:
    #         return self.run_predict(crude_answer=crude_answer, file=file)
    #
    #     self.predictor_context = self._bind_context(crude_answer, file=file)
    #
    #     self.predict_answer()
    #     answer_items = self.collect_answer_items()
    #     return self.build_question_answer(answer_items)

    def add_answer_result(self, path_key, answer_result):
        self.answer[path_key].append(answer_result)

    def get_answer_result(self, path_key):
        return self.answer[path_key]

    def build_question_answer(self, answer_items):
        """Build final answer with current structure."""
        if self.answer_version < "2.2":
            raise NotImplementedError("not implement for answer version under 2.2")

        return self.build_v_2_2_question_answer(answer_items)

    def build_v_2_2_question_answer(self, answer_items):
        schema = {
            "schema_types": self._mold.data.get("schema_types", []),
            "schemas": self._mold.data.get("schemas", []),
            "version": self._mold.checksum,
        }

        answer = {"schema": schema, "userAnswer": {"version": "2.2", "items": answer_items}}
        return answer

    def collect_answer_items(self):
        items = []
        for path_key in self.answer:
            answer_results = self.answer[path_key]
            for result in answer_results:
                items.append(result.answer)
        return items

    @staticmethod
    def gen_answer_key(answer, index):
        if isinstance(answer["key"], str):
            key_path = json.loads(answer["key"])
        else:
            key_path = answer["key"]
        result = [f"{i}:0" for i in key_path[:-1]] + [f"{key_path[-1]}:{index}"]
        return json.dumps(result, ensure_ascii=False)

    def predict_answer(self):
        tree_answer = self.root_predictor.predict()
        for path_key, answer_items in tree_answer.items():
            for item in answer_items:
                self.add_answer_result(path_key, item)

    def load_config(self, predictor_options):
        root_path = [self.root_schema.name]
        return {json.dumps(root_path + item["path"], ensure_ascii=False): item for item in predictor_options}

    @classmethod
    def prepare_dir(cls, directory):
        directory.mkdir(parents=True, exist_ok=True)

    @staticmethod
    def _bind_context(crude_answer, file: NewFile, pdfinsight_data: dict = None, metadata: dict = None):
        if not pdfinsight_data:
            pdfinsight = PdfinsightReader.from_path(
                file.pdfinsight_path(abs_path=True),
                pdf_hash=file.pdf,
            )
        else:
            pdfinsight = PdfinsightReader(None, data=pdfinsight_data, pdf_contents=file.pdf_contents)
        logger.info("--------interdoc model_version--------")
        for key, value in pdfinsight.data["model_version"].items():
            logger.info(f"{key}: {value}")

        return PredictorContext(crude_answer, pdfinsight, metadata)

    @staticmethod
    def post_process(preset_answer, **kwargs):
        return preset_answer


def prepare_dataset(dataset_dir: Path, meta):
    logging.info(f"loading file: {meta['fid']}, qid: {meta['qid']}")
    answer_reader = AnswerReader(meta["answer"])
    reader = PdfinsightReader(meta["pdfinsight_path"], pdf_contents=meta["pdf_contents"])
    root_node = answer_reader._tree
    if not root_node:
        logging.warning(f"can't find answer root node for file:{meta['fid']}")
        return
    elements = {}
    for leaf_node in root_node.descendants(only_leaf=True):
        elements.update(find_item_elements(reader, leaf_node.data))
    data_item = DatasetItem(
        answer_reader.main_schema["name"],
        {"elements": elements, "syllabuses": reader.syllabuses},
        answer_reader,
        meta["fid"],
    )
    dataset_dir.mkdir(parents=True, exist_ok=True)
    with open(dataset_dir.joinpath(f"{meta['qid']}.pkl"), "wb") as file_obj:
        pickle.dump(data_item, file_obj)
    logging.info(f"dataset saved: {meta['fid']}, qid: {meta['qid']}")


class PredictorV1ProphetAdapter(BaseProphet):
    depends_root_predictor = False

    def __init__(self, mold, version_id=0, *, new_prophet=None, **kwargs):
        super().__init__({"predictor_options": {}}, mold, version_id=version_id)
        predictor = self._create_v1_predictor(vid=self._version_id, **kwargs)
        self.predictor_config = getattr(predictor, "predictor_config", {})
        self.new_prophet = new_prophet

    @property
    def predictor_options(self):
        return self.predictor_config

    def _create_v1_predictor(self, *args, **kwargs):
        from remarkable.plugins.fileapi.predict import AnswerPredictorFactory

        predictor = AnswerPredictorFactory.create(self._mold, *args, **kwargs)
        return predictor

    def run_predict(self, crude_answer, **kwargs):
        file = kwargs.get("file")
        metadata = kwargs.get("metadata")
        predictor = self._create_v1_predictor(
            file,
            crude_answer,
            metadata,
            vid=self._version_id,
            anno_mold=kwargs.get("anno_mold"),
            anno_crude_answer=kwargs.get("anno_crude_answer"),
            enum_predictor=kwargs.get("enum_predictor"),
            special_rules=kwargs.get("special_rules"),
        )

        answer = None
        only_b1_b10 = get_config("ONLY_B1_B10")
        logger.info(f"{kwargs.get("only_jura21")=}, {only_b1_b10=}")
        if (not kwargs.get("only_jura21") and not only_b1_b10) and predictor:
            # jura2 的预测
            answer = predictor.predict_answer()
        if not self.new_prophet or not self.new_prophet.predictor_options:
            return answer
        # jura21 & b1_b7 的预测
        new_answer = self.new_prophet.run_predict(
            crude_answer=crude_answer,
            file=file,
            metadata=metadata,
            enum_predictor=kwargs.get("enum_predictor"),
        )
        new_answer = self.new_prophet.post_process(new_answer, doc_type=metadata.get("doc_type"))
        if answer and new_answer:
            return merge_answers([UserAnswer._make([ADMIN.id, ADMIN.name, ans]) for ans in (answer, new_answer)])
        return answer or new_answer

    def run_train(self):
        predictor = self._create_v1_predictor(vid=self._version_id)
        predictor.train()

    async def run_dump_dataset(self, start, end, tree_l=None, file_full_label=False):
        from remarkable.plugins.predict.predictor import dump_dataset

        predictor = self._create_v1_predictor(vid=self._version_id)
        files = await dump_dataset(self._mold.id, start, end, predictor.predictor, file_full_label)
        return files

    def get_enum_predictor(self):
        pass

    def parse_enum_value(self, predictor_result, schema, metadata):
        pass
