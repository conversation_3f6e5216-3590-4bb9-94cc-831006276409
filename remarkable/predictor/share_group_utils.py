import logging
import re
from collections import defaultdict
from datetime import datetime
from itertools import chain, groupby

from remarkable.common.common import (
    get_first_key,
    is_paragraph_elt,
    is_table_elt,
    real_all,
    roman_to_int,
    to_datetime,
)
from remarkable.common.common_pattern import R_MIDDLE_DASH
from remarkable.common.constants import TableType
from remarkable.common.element_util import <PERSON>ont<PERSON>tyle
from remarkable.common.pattern import <PERSON><PERSON><PERSON><PERSON>, NeglectPattern
from remarkable.common.util import clean_txt, is_in_range, split_paragraph
from remarkable.models.file_share_meta import GroupItem
from remarkable.pdfinsight.parser import INVALID_TABLE_TITLE, ParsedTable, parse_table
from remarkable.pdfinsight.reader import P_FOLLOW_CHAPTER_PREFIX, PdfinsightReader
from remarkable.predictor.common_pattern import P_SHARE_AWARD, P_SHARE_INCENTIVE, R_INCENTIVE_ABBREV
from remarkable.predictor.hkex_predictor.schemas.pattern import P_DATE, P_ONLY_NUM, R_SAVE_AS
from remarkable.predictor.share_group_gpt import (
    DEFAULT_GROUP_KEY_MAP,
    P_RIGHT_QUOTES,
    P_SCHEME_ALIAS,
    P_SCHEME_NAME,
    R_ALIAS,
    R_ALIAS_BEFORE,
    R_ALIAS_START,
    R_PLAN,
    R_PRE_POST_IPO,
    R_QUOTES,
    R_SCHEME_SUFFIX,
    R_TYPE_PREFIX,
    R_YEAR,
    SPECIAL_GROUP_KEYS,
    lower_keyword,
)
from remarkable.predictor.utils import match_incentive_table, match_incentive_text

logger = logging.getLogger(__name__)

R_CHAPTER_PREFIX = r"^([■•]|[(（]?([a-z]|\d(\.?\d)*|[ivx]+)[）).]|([a-z]|[ivx]{1,4}|\d{1,2})\s)?\s*"
# R_SCHEME_PREFIX = r"(\b[Aa]\s+|The\s+(Scheme\s+)?|Plan\s+)"
R_CAPITAL_SCHEME = r"\b(([B-OPQVWXYZ]|(?<!^)A(?!mended )|R(?!(estated|evised|(?i:s[ua]s?)) )|S(?!cheme)|T(?!(he|his|hese) )|U(?!nder )|\d{4})[-\w]*\s+){1,8}(?i:Scheme|Plan)(?![Ss])(\s*[IV]{1,4})?(?i:\s*plus)?"
R_UNDER = r"\b((under|adopted)\s*(the|\ba\b)?|of\s+the)(?!\s*the\s*rule)\s+"
# 句子前面的介词
R_SENTENCE_PRE = r".*?(^(.{1,2}[ivx]{0,3}[.)） ])?|\t|\b(a(\s*new)?|the|its|and(\s*a)?|under|of(\s*a)?)\s+)"
# 特殊别名：Scheme, 直接提取别名括号之前的内容
P_SPECIAL_ALIAS = re.compile(rf"{R_ALIAS_BEFORE}(scheme|plan|awards){R_QUOTES}[)）]", re.I)

COMMON_REGS = {
    # 利用首字母大写提取关键词, 考虑前后年份
    re.compile(
        rf"(?<!-)(?P<scheme>(\d{{4}}\s+)?{R_CAPITAL_SCHEME})\s*(?P<year>(for\s*)?\d{{4}})?(?!\s*[Mm]andate)"
    ): r"\g<scheme>\g<year>",
    # 利用别名提取关键词
    re.compile(R_ALIAS, re.I): r"\g<scheme>",
    # 特殊别名：全大写的缩写 http://************:55647/#/project/remark/233215?treeId=37654&fileId=66491&schemaId=15&projectId=17&schemaKey=B82
    re.compile(
        rf"(scheme|plan|incentive|rsu)\s*{R_ALIAS_START}(?P<scheme>[A-Z]{{2,6}}){R_QUOTES}[)）]", re.I
    ): r"\g<scheme>",
    # 提取under等介词后面的xxxx scheme或scheme yyyy
    re.compile(
        rf"{R_SENTENCE_PRE}(?P<scheme>{R_TYPE_PREFIX}{{1,2}}(Scheme|Plan)(\s*Mandate\s*Limit)?|(Scheme|Plan)\s*\d{{4}})",
        re.I,
    ): r"\g<scheme>",
}
INCENTIVE_KEY_REGS = {
    # 1. http://************:55647/#/project/remark/250811?treeId=42901&fileId=68591&schemaId=15&projectId=17&schemaKey=B93.1
    re.compile(
        r"(?P<scheme>(Restricted\s*Shares|\srsus)\s*under\s*the\s*\d{4}\s*share\s*incentive\s*plan)", re.I
    ): r"\g<scheme>",
    # 2.
    re.compile(rf".*(the|under\s*(the\s*)?)\s+(?P<scheme>([-\w]+\s+){{,4}}{R_INCENTIVE_ABBREV})", re.I): r"\g<scheme>",
    # 3.
    re.compile(
        rf"{R_SENTENCE_PRE}(?P<scheme>{R_TYPE_PREFIX}{{0,2}}(Restricted\s*|share\s*|[AH]\s+|Equity|incentive\s*|long[-\s]*term|ownership\s*|purchase\s*|stock\s*){{2,}}{R_SCHEME_SUFFIX})",
        re.I,
    ): r"\g<scheme>",
    # 4.
    re.compile(
        rf"{R_SENTENCE_PRE}(?P<scheme>{R_TYPE_PREFIX}{{0,2}}{R_INCENTIVE_ABBREV}\s*{R_SCHEME_SUFFIX}?)",
        re.I,
    ): r"\g<scheme>",
}

# share option的前缀
R_OPTION_TYPE = rf"\b(\d{{4}}\s*)?((New|Old)\s*SUNeVision\b|{R_PRE_POST_IPO}|SmarTone|Old|(?<! a )New)\b"
SHARE_OPTION_GROUP_KEY_REGS = {
    # 1-4
    **INCENTIVE_KEY_REGS,
    # 5. 表头识别到了一起： Share Option Scheme 2012 Share Option Scheme 2022 Share Award Scheme
    re.compile(r"^(?P<scheme>share\s*option\s*scheme\s*\d{4})", re.I): r"\g<scheme>",
    # 6.
    re.compile(r"scheme\s*\d{4}(?P<scheme>Share\s*option\s*scheme\s*\d{4})", re.I): r"\g<scheme>",
    # http://************:55647/#/project/remark/250333?treeId=10931&fileId=68511&schemaId=15&projectId=17&schemaKey=B93.1 group_key=2012 MAGHL
    # http://************:55647/#/project/remark/?treeId=24500&fileId=66458&schemaId=15&projectId=17&schemaKey=B93.1
    # 7-10
    **COMMON_REGS,
    # 11.
    re.compile(
        rf"{R_SENTENCE_PRE}(?P<scheme>{R_TYPE_PREFIX}{{,2}}(stock|share)\s*option\s*(scheme|plan)(?!s)\s*(\d{{4}})?)",
        re.I,
    ): r"\g<scheme>",
}

# 忽略senior management: http://************:55647/#/project/remark/233197?treeId=13539&fileId=66488&schemaId=15&projectId=17&schemaKey=B76&page=186
R_SHARE_AWARD = r"(shares?\s*a\s*ward|a\s*ward\s*shares?|shares?\s*grant|shares?(?!option)|award)"
# PB、SBC：http://************:55647/#/project/remark/250167?treeId=37595&fileId=68485&schemaId=15&projectId=17&schemaKey=B77&page=28
R_AWARD_TYPE = (rf"\b(\d{{4}}\s*)?\b({R_TYPE_PREFIX})?\s*\b(PCCW|SAP|RSA?|PB|SBC)\b",)
SHARE_AWARD_GROUP_KEY_REGS = {
    # 1-4
    **INCENTIVE_KEY_REGS,
    # 5. http://************:55647/#/project/remark/250811?treeId=42901&fileId=68591&schemaId=15&projectId=17&schemaKey=B93.1
    # http://************:55647/#/project/remark/264823?treeId=42555&fileId=70179&schemaId=15&projectId=17&schemaKey=B76&page=75
    re.compile(
        r"(?P<rsu>(Restricted\s*Shares|\sRSUs))\s*(granted\s*)?(?P<scheme>(?i:under\s*the\s*\d{4}\s*share\s*incentive\s*plan))"
    ): r"\g<rsu>\g<scheme>",
    # 6. http://************:55647/#/project/remark/234893?treeId=9336&fileId=66771&schemaId=15&projectId=9336&schemaKey=B78
    re.compile(
        rf"{R_CHAPTER_PREFIX}(?P<award>share\s*award\s*scheme)\s*by\s*.+{R_ALIAS}$", re.I
    ): r"\g<scheme>\g<award>",
    # 7. http://************:55647/#/project/remark/238299?treeId=3997&fileId=67339&schemaId=15&projectId=17&schemaKey=B76&page=94
    re.compile(rf"{R_CHAPTER_PREFIX}share\s*award\s*scheme\s*(?P<num>[IV]+)$", re.I): r"share award scheme\g<num>",
    # http://************:55647/#/project/remark/232965?treeId=4451&fileId=66449&schemaId=15&projectId=17&schemaKey=B76
    # http://************:55647/#/project/remark/234432?treeId=37922&fileId=66694&schemaId=15&projectId=17&schemaKey=B76
    # 8. http://************:55647/#/project/remark/250547?treeId=18899&fileId=68547&schemaId=15&projectId=17&schemaKey=B93.2
    re.compile(
        rf"(?P<scheme>RS[UA])\s*{R_PLAN}\s*for\s*(?P<prefix>managements?|employees?|Core\s*Connected\s*Persons)", re.I
    ): r"\g<prefix>\g<scheme>",
    # http://************:55647/#/project/remark/235194?treeId=37994&fileId=66821&schemaId=15&projectId=17&schemaKey=B74
    # 9. TODO url
    re.compile(
        rf"{R_CHAPTER_PREFIX}(?P<scheme>restricted\s*(share\s*|award\s*|incentive\s*){{1,2}}(?!s)\s*{R_PLAN})\s*adopted\s*by\s*(the\s*)?(?P<comp>.+$)",
        re.I,
    ): r"\g<scheme>of\g<comp>",
    # 带tranche的场景：http://************:55647/#/project/remark/233808?treeId=37712&fileId=66590&schemaId=15&projectId=37712&schemaKey=B73&page=173
    # http://************:55647/#/project/remark/236162?treeId=37716&fileId=66982&schemaId=15&projectId=17&schemaKey=B76
    # http://************:55647/#/project/remark/250363?treeId=6141&fileId=68516&schemaId=15&projectId=17&schemaKey=B76&page=47
    # 10.
    re.compile(rf"(?P<scheme>global\s*partner\s*program\s*{R_SHARE_AWARD}?)(\s*{R_PLAN})?", re.I): r"\g<scheme>",
    # 11. http://************:55647/#/project/remark/233808?treeId=37712&fileId=66590&schemaId=15&projectId=17&schemaKey=B74
    re.compile(
        rf"PHASE\s*2\s*(?P<scheme>Employee\s*Stock\s*ownership\s*{R_PLAN})|(?P<scheme1>ESOP)\s*II\b", re.I
    ): r"\g<scheme>\g<scheme1>II",
    # 12.
    re.compile(
        rf"PHASE\s*1\s*(?P<scheme>Employee\s*Stock\s*ownership\s*{R_PLAN})|(?P<scheme1>ESOP)\s*I\b", re.I
    ): r"\g<scheme>\g<scheme1>I",
    # http://************:55647/#/project/remark/234000?treeId=42548&fileId=66622&schemaId=15&projectId=42548&schemaKey=B73
    # http://************:55647/#/project/remark/250860?treeId=10041&fileId=68599&schemaId=15&projectId=17&schemaKey=B93.2f  68646相同
    # 13.
    re.compile(
        rf"(?P<scheme>{R_SHARE_AWARD})?\s*(?P<plan>{R_PLAN})\s*of\s*(?P<prefix>{R_AWARD_TYPE})", re.I
    ): r"\g<prefix>\g<scheme>\g<plan>",
    # 14.
    re.compile(
        rf"(\W|^)\s*(?P<scheme>{R_AWARD_TYPE}\s*{R_SHARE_AWARD}?{R_PLAN})(\s*(for|[-—–\s]+)\s*{R_YEAR})?$", re.I
    ): r"\g<year>\g<scheme>",
    # 15.
    re.compile(rf"(?P<scheme>{R_AWARD_TYPE}\s*(share\s+|A\s?ward\s+){{0,2}}{R_PLAN})", re.I): r"\g<scheme>",
    # 16.
    re.compile(
        rf"(?P<scheme>\b({R_TYPE_PREFIX}|\d{{4}})?\s*\b[HA]\s*Share\s*A\s?ward\s*and\s*Trust\s*{R_PLAN})", re.I
    ): r"\g<scheme>",
    # http://************:55647/#/project/remark/238023?treeId=8837&fileId=67293&schemaId=15&projectId=17&schemaKey=B76
    # 17. http://************:55647/#/project/remark/?treeId=6372&fileId=66430&schemaId=15&projectId=17&schemaKey=B93.2
    re.compile(
        rf"{R_YEAR}?(?P<prefix>{R_TYPE_PREFIX})?\s*(?P<scheme>restricted\s*(share\s*|award\s*){{1,2}}(?!s)(\s*{R_PLAN}(?!s)))(\s*(for|of)\s*(?P<year1>\d{{4}}))?",
        re.I,
    ): r"\g<year>\g<prefix>\g<scheme>\g<year1>",
    # http://************:55647/#/project/remark/231582?treeId=6025&fileId=66219&schemaId=15&projectId=17&schemaKey=B80&page=208
    # http://************:55647/#/project/remark/234000?treeId=42548&fileId=66622&schemaId=15&projectId=42548&schemaKey=B73
    # 18. http://************:55647/#/project/remark/231840?treeId=13680&fileId=66262&schemaId=15&projectId=17&schemaKey=B76
    re.compile(rf"(?P<scheme>{R_SHARE_AWARD}\s*{R_PLAN}\s*of\s*JBM)", re.I): r"\g<scheme>",
    # 19. http://************:55647/#/project/remark/233197?treeId=13539&fileId=66488&schemaId=15&projectId=17&schemaKey=B74
    re.compile(r"(senior\s*management\s*)?(?P<scheme>share\s*grant\s*scheme)", re.I): r"\g<scheme>",
    # http://************:55647/#/project/remark/233922?treeId=4887&fileId=66609&schemaId=15&projectId=17&schemaKey=B76&page=29 share scheme award
    # http://************:55647/#/project/remark/232881?treeId=38070&fileId=66435&schemaId=15&projectId=17&schemaKey=B93.1
    # http://************:55647/#/project/remark/239670?treeId=20582&fileId=67568&schemaId=15&projectId=17&schemaKey=B76&page=91
    # http://************:55647/#/project/remark/235650?treeId=4907&fileId=66897&schemaId=15&projectId=17&schemaKey=B78
    # 20-23
    **COMMON_REGS,
    # 24.
    re.compile(
        rf"(?P<scheme>{R_TYPE_PREFIX}{{0,2}}\b(Restricted\s*share\s*unite?s?|(?<!options and )rs[ua]s?|ras|RSA?)\b\s*{R_SCHEME_SUFFIX}?)",
        re.I,
    ): r"\g<scheme>",
    # 25.
    re.compile(
        rf"{R_SENTENCE_PRE}(?P<scheme>{R_TYPE_PREFIX}{{0,2}}(share\s*|award\s*|[AH]\s+){{2,}}{R_SCHEME_SUFFIX})", re.I
    ): r"\g<scheme>",
    # 26. http://************:55647/#/project/remark/250811?treeId=42901&fileId=68591&schemaId=15&projectId=17&schemaKey=B80&page=194
    re.compile(
        r"the\s*(grant\s*of\s*)?(?P<scheme>restricted\s*shares)(?!\s*(unit|under|award|and))", re.I
    ): r"\g<scheme>",
}

GROUP_KEY_REGS_MAP = {"option": SHARE_OPTION_GROUP_KEY_REGS, "award": SHARE_AWARD_GROUP_KEY_REGS}

# 若父章节有这些关键词，则整个章节都不参与分组  https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/3383#note_428438
P_NEG_PARENT_CHAPTERS = {
    "option": MatchMulti.compile(
        r"\b(KPL|XDC)\b",
        NeglectPattern.compile(match=re.compile(r"\baward(s|ed)?\b", re.I), unmatch=re.compile(r"option", re.I)),
        operator=any,
    ),
    "award": MatchMulti.compile(
        r"\b(KPL|XDC)\b",
        NeglectPattern.compile(
            match=re.compile(r"\boptions?\b", re.I),
            unmatch=re.compile(r"award|\bRS\b|restricted\s*(share|award)|schemes|plans", re.I),
        ),
        operator=any,
    ),
}

# 章节标题或者元素所在章节满足以下正则，则跳过
# 依据下方替换规则，标准化分组名称
P_STD_REPLACE = {
    re.compile(R_MIDDLE_DASH): "-",
    re.compile(R_PLAN): "scheme",
    re.compile(r"restricted(share|stock)unite?(scheme|plan)?[(（]?(?P<tail>s)?[)）]?", re.I): r"rsu\g<tail>",
    re.compile(r"(long\s*term\s*incentive|lti)\s*(scheme|plan)", re.I): "ltip",
    re.compile(rf"Employee\s*(Share|Stock)\s*ownership\s*{R_PLAN}", re.I): "esop",
    re.compile(rf"Employee\s*(Share|Stock)\s*Purchase\s*{R_PLAN}", re.I): "espp",
    re.compile(rf"Employee\s*(Share|Stock)\s*Award\s*{R_PLAN}", re.I): "esap",
    re.compile(r"rsu(plan|scheme)"): "rsus",
    # http://************:55647/#/project/remark/233581?treeId=7494&fileId=66552&schemaId=15&projectId=17&schemaKey=%E4%B8%A8H83-92
    re.compile(r"(?P<year>\d{4})rsus"): r"\g<year>rsu",
    re.compile(r"Share(?P<type>Award|Option)Scheme"): r"share\g<type>scheme",
}
P_IGNORE_ELEMENTS = MatchMulti.compile(
    # http://************:55647/#/project/remark/233958?treeId=5370&fileId=66615&schemaId=15&projectId=17
    r"^The \d+ Share Award Scheme has been terminated",
    # http://************:55647/#/project/remark/232965?treeId=4451&fileId=66449&schemaId=15&projectId=17&schemaKey=B82&page=106
    rf"{R_SAVE_AS}under\s*{R_QUOTES}",
    # # http://************:55647/#/project/remark/233167?treeId=4194&fileId=66483&schemaId=15&projectId=17&schemaKey=B82&page=109
    # r"(\brsus|Restricted\s*Shares)\s*(shall\s*be\s*)?(available|granted)",
    # r"[\d,]+\s+(\brsus|Restricted\s*Shares)",
    operator=any,
)

# 判断分组名称是否相同，尝试替换这些内容
P_SCHEME_NAME_REPLACER = {
    "option": {
        re.compile(r"in[cv]entive", re.I): "option",
        re.compile(rf"(?P<year>\d{{4}})(share)?(incentive|option)(?P<plan>{R_PLAN})", re.I): r"\g<year>\g<plan>",
        re.compile(rf"(?P<year>\d{{4}})(share)?(option|incentive)(?P<plan>{R_PLAN})", re.I): r"\g<plan>\g<year>",
        re.compile(rf"(?P<year>\d{{4}})(?P<scheme>(share)?(incentive|option){R_PLAN})", re.I): r"\g<year>\g<scheme>",
        re.compile(rf"(?P<year>\d{{4}})(?P<scheme>(share)?(option|incentive){R_PLAN})", re.I): r"\g<scheme>\g<year>",
        re.compile(rf"^(?P<scheme>option{R_PLAN}.*)", re.I): r"share\g<scheme>",
        # http://************:55647/#/project/remark/235650?treeId=4907&fileId=66897&schemaId=15&projectId=4907&schemaKey=B64
        re.compile(r"(?P<pre>pre|post)-iposhareoption(?P<plan>scheme|plan)", re.I): r"\g<pre>-iposhare\g<plan>",
    },
    "award": {
        re.compile(r"in[cv]entive"): "award",
        re.compile(rf"(?P<year>\d{{4}})(share)?(incentive|award)(?P<plan>{R_PLAN})", re.I): r"\g<year>\g<plan>",
        re.compile(rf"(?P<year>\d{{4}})(share)?(award|incentive)(?P<plan>{R_PLAN})", re.I): r"\g<plan>\g<year>",
        re.compile(rf"(?P<year>\d{{4}})(?P<scheme>(share)?(incentive|award){R_PLAN})", re.I): r"\g<year>\g<scheme>",
        re.compile(rf"(?P<year>\d{{4}})(?P<scheme>(share)?(award|incentive){R_PLAN})", re.I): r"\g<scheme>\g<year>",
        # 去掉`a share incentive` 中的a
        re.compile(r"^restrictedashareincentive", re.I): r"restrictedshareincentive",
        # http://************:55647/#/project/remark/258043?treeId=3543&fileId=68823&schemaId=15&projectId=17&schemaKey=B76
        re.compile(r"shareaward(s|ed)(?P<plan>scheme|plan)", re.I): r"shareaward\g<plan>",
        # http://************:55647/#/project/remark/235650?treeId=4907&fileId=66897&schemaId=15&projectId=4907&schemaKey=B64
        re.compile(r"(?P<pre>pre|post)-iposhareaward(?P<plan>scheme|plan)", re.I): r"\g<pre>-iposhare\g<plan>",
    },
}

TITLE_STYLE_MAP = {
    # 数字序号
    re.compile(r"^\s*(?P<start>[■(（]?)\d+(?P<end>[）).]).+$"): r"\g<start>1\g<end>",
    # 大写罗马序号
    re.compile(r"^\s*(?P<start>[■(（]?)[IVX]{1,4}(?P<end>[）).]).+$"): r"\g<start>I\g<end>",
    # 小写罗马序号
    re.compile(r"^\s*(?P<start>[■(（]?)[ivx]{1,4}(?P<end>[）).]).+$"): r"\g<start>i\g<end>",
    # 大写英文序号
    re.compile(r"^\s*(?P<start>[■(（]?)[A-Z](?P<end>[）).]).+$"): r"\g<start>A\g<end>",
    # 小写英文序号
    re.compile(r"^\s*(?P<start>[■(（]?)[a-z](?P<end>[）).]).+$"): r"\g<start>a\g<end>",
}


def is_valid_keyword(keyword, sentence, share_type, all_keywords=(), strict=True):
    keyword = keyword.lower()
    if keyword.replace(" ", "") in {
        *all_keywords,
        "scheme",
        "plan",
        "incentivescheme",
        "awardedshares",
        "schemelimit",
        "suchscheme",
    }:
        return False
    # len(sentence.split()) < 4 一般是表格中的scheme名称，没有太多上下文
    strict = strict and not (P_SHARE_INCENTIVE.search(sentence) or len(sentence.split()) < 4)
    if share_type == "award":
        if "option" in keyword:
            return False
        if strict and not P_SHARE_AWARD.nexts(sentence):
            return False
    if share_type == "option":
        if P_SHARE_AWARD.nexts(keyword):
            return False
        if strict and "option" not in sentence.lower():
            return False
    return True


def extract_group_key(text, share_type, multi=False, ignore_case=False, strict=True) -> set[str]:
    """
    根据替换正则从文本中提取关键词，任意一个正则匹上则返回
    注意：替换正则尽可能从长到短，从特殊到通用
    """
    if not text:
        return set()
    if not share_type:
        raise ValueError("`share_type` must have value!")

    if not (
        sentences := [
            sen
            for sen in split_paragraph(text, remove_cn_text=True)
            if P_SCHEME_NAME.search(sen) and not P_IGNORE_ELEMENTS.search(sen)
        ]
    ):
        return set()

    group_key_regs = GROUP_KEY_REGS_MAP[share_type]
    keywords = set()
    # for idx, (regex, replace_regex) in enumerate(group_key_regs.items(), start=1):
    for regex, replace_regex in group_key_regs.items():
        for sentence in sentences:
            for mat in regex.finditer(sentence):
                keyword = regex.sub(replace_regex, mat.group())
                if not is_valid_keyword(keyword, sentence, share_type, keywords, strict):
                    continue
                keyword = lower_keyword(keyword.replace(" ", ""), ignore_case)
                # print(f"{sentence=}")
                # print(f"{idx=}, {keyword=}, {regex.pattern}")
                keywords.add(keyword)
        if keywords and not multi:
            break

    if len(keywords) > 1:
        # 排除被包含的key
        keywords = {key for key in keywords if not any(key in kw for kw in keywords if kw != key)}

    alias_keywords = extract_alias_keys(sentences, share_type, keywords)

    # 同时存在默认key和其它key，丢弃默认key
    if alias_keywords or len(keywords) > 1:
        filter_keywords(keywords, SPECIAL_GROUP_KEYS, need_one=not alias_keywords)
    if alias_keywords or len(keywords) > 1:
        filter_keywords(keywords, DEFAULT_GROUP_KEY_MAP[share_type], need_one=not alias_keywords)
    return keywords | alias_keywords


def extract_alias_keys(sentences, share_type, existed_keywords):
    """
    提取(“”)中的key，这种key一般比较确定，不做过滤
    """
    alias_keywords = set()
    for sentence in sentences:
        for sub_sentence in split_paragraph(sentence, separator=P_RIGHT_QUOTES):
            if not (match_alias := P_SCHEME_ALIAS.search(sub_sentence)) and not (
                match_special := P_SPECIAL_ALIAS.search(sub_sentence)
            ):
                continue
            keyword = (
                (match_alias.group("scheme") if match_alias else match_special.group("before")).replace(" ", "").lower()
            )
            if is_valid_keyword(keyword, sub_sentence, share_type, existed_keywords | alias_keywords):
                alias_keywords.add(keyword)
    return alias_keywords


def filter_keywords(keywords, exclude_keys, need_one):
    if need_one and keywords.issubset(exclude_keys):
        # 没有别名且都是默认key，则不过滤
        return
    for key in exclude_keys:
        if need_one and len(keywords) == 1:
            break
        if key in keywords:
            keywords.remove(key)
    return


def is_sub_range(range1, range2):
    return range2[0] <= range1[0] <= range2[1] and range2[0] <= range1[1] <= range2[1]


def table_headers(table: ParsedTable):
    return [
        clean_txt(cell.text, remove_cn_text=True)
        for row in table.rows
        for cell in row
        if cell.is_header and cell.indexstr != "0_0" and not cell.dummy
    ]


def get_elem_text(pdfinsight: PdfinsightReader, element: dict) -> str:
    if not element:
        return ""
    if is_table_elt(element):
        elements_above = pdfinsight.find_elements_near_by(
            element["index"], amount=1, step=-1, aim_types=["PARAGRAPH"], neg_patterns=INVALID_TABLE_TITLE
        )
        if elements_above and ParsedTable.is_title(elements_above[0], [r"[：:]$"]):
            # 先找最近元素块
            text = elements_above[0]["text"]
        elif (title := element.get("title") or "") and ParsedTable.is_title(
            {"class": "PARAGRAPH", "text": title}, [r"[：:]$"]
        ):
            # 找不到用表格title
            text = element.get("title") or ""
        else:
            return ""
    else:
        text = element.get("text") or element.get("title") or ""
    return clean_txt(text or "", remove_cn_text=True)


def str_to_date(day_str) -> datetime | None:
    if not day_str:
        return None
    if len(day_str) == 4:
        return to_datetime(day_str, "%Y")
    if len(day_str) == 7:
        return to_datetime(day_str, "%Y-%m")
    elif len(day_str) == 10:
        return to_datetime(day_str, "%Y-%m-%d")
    return None


def same_adopted_days(days: set[str], next_days: set[str], diff_days: int = 1) -> set[str]:
    """
    判断两组日期集合中，是否有日期相差diff_days天的日期， 有则返回True
    """
    if not days or not next_days:
        return set()
    strict_same_days, same_days = set(), set()

    for day in days:
        if len(day) != 10:
            continue
        for next_day in next_days:
            if len(next_day) != 10:
                continue
            if day == next_day:
                strict_same_days.add(next_day)
            elif abs((str_to_date(next_day) - str_to_date(day)).days) <= diff_days:
                same_days.add(next_day)
    return strict_same_days or same_days


def standardize_group_key(group_key):
    """
    按照P_STD_REPLACE中的正则，依次替换，做规范化
    """
    for reg, rpl in P_STD_REPLACE.items():
        group_key = reg.sub(rpl, group_key)
    return group_key


def is_same_key(share_type, group_key1, group_key2, company_words=None):
    """
    判断两个group_key是否为同一个key
    """
    if group_key1.lower() == group_key2.lower():
        return True
    std_key1, std_key2 = standardize_group_key(group_key1), standardize_group_key(group_key2)
    if std_key1 == std_key2:
        return True
    for reg, rpl in P_SCHEME_NAME_REPLACER[share_type].items():
        if reg.sub(rpl, std_key1) == reg.sub(rpl, std_key2):
            return True
    if company_words and len(group_key1) != len(group_key2):
        # 替换关键词中的公司名称，再判断是否相等
        long_key, short_key = (
            (group_key1, group_key2) if len(group_key1) > len(group_key2) else (group_key2, group_key1)
        )
        for last_index in range(len(company_words) - 1, 0, -1):
            words = company_words[0:last_index]
            if is_same_key(share_type, long_key.replace("".join(words), ""), short_key):
                return True
        # http://************:55647/#/project/remark/264723?treeId=37994&fileId=70159&schemaId=15&projectId=17&schemaKey=B76
        for word in {"group"}:
            if is_same_key(share_type, long_key.replace(f"{company_words[0]}{word}", ""), short_key):
                return True
    return False


def guess_share_type(text):
    """
    判断文本属于option还是award
    all - 既属于option又属于award
    option/award - 属于option或award
    None - 无法判断
    """
    types = set()
    for share_type in ("option", "award"):
        if match_incentive_text(share_type, text):
            types.add(share_type)
    if not types:
        return None
    return "all" if len(types) == 2 else get_first_key(types)


def guess_tbl_share_type(table: ParsedTable):
    """
    判断表格属于option还是award
    all - 既属于option又属于award
    option/award - 属于option或award
    None - 无法判断
    """
    types = set()
    for share_type in ("option", "award"):
        if match_incentive_table(share_type, table):
            types.add(share_type)
    if not types:
        return None
    return "all" if len(types) == 2 else get_first_key(types)


def prefix_to_int(title):
    """
    将章节前面的序号转成整型
    """
    if not (matched := P_FOLLOW_CHAPTER_PREFIX.search(title)):
        return 0
    num, word, roman = matched.group("num"), matched.group("word"), matched.group("roman")
    if num:
        # 超过两位数的序号不是序号
        return int(num) if len(num) < 3 else 0
    if word:
        return ord(word.upper()) - 64
    if roman:
        return roman_to_int(roman)
    return 0


def trans_indices_to_tuple(elem_indices: list[int]):
    """
    尽可能把连续的index合并，用于debug和单元测试，示例：
    输入：[1,2,5,6,7,8,10]，
    输出：([1,2], [5,8], 10)
    """
    return tuple(
        tup[0] if len(tup) == 1 else [min(tup), max(tup)]
        for tup in [
            [idx for _, idx in list(indices)]
            for _, indices in groupby(enumerate(sorted(elem_indices)), lambda x: x[1] - x[0])
        ]
    )


def group_children_by_style(pdfinsight: PdfinsightReader, children: list[dict]) -> dict[str, list[dict]]:
    """
    把子章节按照标题样式重新分组
    TODO: 无法识别的样式考虑用行高
    """
    style_dict = defaultdict(list)
    for child in children:
        style = chapter_title_style(pdfinsight, child["element"])
        style_dict[style].append(child)
    if len(style_dict) == 1:
        return style_dict
    # 考虑I被识别成罗马序号的场景
    roman_lower, roman_upper = None, None
    invalid_styles = set()
    for key, titles in style_dict.items():
        if len(titles) != 1 or not isinstance(key, str):
            continue
        if "i" in key:
            roman_lower = key
        elif "I" in key:
            roman_upper = key
        else:
            # 仅有1个标题的样式可能有问题
            invalid_styles.add(key)
    for from_key, roman_word, word in {(roman_lower, "i", "a"), (roman_upper, "I", "A")}:
        if not from_key:
            continue
        to_key = from_key.replace(roman_word, word)
        if to_key not in style_dict or len(style_dict[to_key]) < 8:
            continue
        style_dict[to_key].extend(style_dict[from_key])
        style_dict.pop(from_key)
    for key in invalid_styles:
        style_dict[""].extend(style_dict[key])
        style_dict.pop(key)
    if "" in style_dict and len(style_dict) == 2:
        style_dict.pop("")
        return dict.fromkeys(style_dict, children)
    return style_dict


def chapter_title_style(
    pdfinsight: PdfinsightReader, index: int, element: dict = None, use_char_style=True
) -> FontStyle | str:
    """
    提取章节标题样式，提取逻辑：
    1. 优先提取序号样式，例如(a)
    2. 提不到则从chars中提取style信息（可能不准）
    """
    if not element:
        try:
            _, element = pdfinsight.find_element_by_index(index)
        except IndexError:
            return ""
    title = clean_txt(element.get("text") or "", remove_cn_text=True)
    for regex, repl in TITLE_STYLE_MAP.items():
        if not regex.search(title):
            continue
        return regex.sub(repl, title).replace("（", "(").replace("）", ")")
    if not use_char_style:
        return ""
    return pdfinsight.get_font_style(element.get("chars") or []) or ""


def is_contiguous_titles(titles: list[str], style: str):
    """
    判断标题序号是否连续，注意标题必须有序号，且已经排好顺序
    """
    last_num, last_word = 0, 0
    if not style:
        # 无法识别的样式当作连续来处理
        return True
    for title in titles:
        if not (matched := P_FOLLOW_CHAPTER_PREFIX.search(title)):
            # 无法识别的样式当作连续来处理
            return True
        num, word, roman = matched.group("num"), matched.group("word"), matched.group("roman")
        if word and word.lower() in {"i", "v", "x"} and "a" in style.lower():
            # 满足以下条件表示i v x不是字母，而是罗马数字
            if ord(word.lower()) != last_word + 1:
                style = style.replace("a", "i").replace("A", "I")
                roman = word
                word = None
        if "1" in style:
            if not num:
                return False
            num = int(num)
        elif "a" in style.lower():
            if not word:
                return False
            num = ord(word.lower())
            last_word = num
        elif "i" in style.lower():
            if not roman:
                return False
            num = roman_to_int(roman.upper())
        else:
            return True
        if last_num > 0 and not num:
            return False
        if num != last_num + 1:
            return False
        last_num += 1


def is_elem_in_group(
    element: dict,
    pdfinsight: PdfinsightReader,
    share_type: str,
    share_group: dict[str, list[GroupItem]],
    correct_group_keys: set[str],
    incorrect_group_keys: set[str],
) -> bool:
    """
    判断一个element是否属于某个group_key
    返回False表示绝对不属于
    """
    if not element:
        return False

    # 1. 如果在当前分组的单个元素块中，说明已经提取过关键词，可以直接返回True
    if any(
        any(is_in_range(element["index"], item.range) for item in items if not item.is_chapter)
        for key, items in share_group.items()
        if key in correct_group_keys
    ):
        return True

    # 2.如果在其他分组的单个元素块中，返回False
    if any(
        any(is_in_range(element["index"], item.range) for item in items if not item.is_chapter)
        for key, items in share_group.items()
        if key in incorrect_group_keys
    ):
        return False

    # 3. 提取元素块的关键词，判断是否有效
    if is_table_elt(element):
        table = parse_table(element, tabletype=TableType.TUPLE, pdfinsight_reader=pdfinsight)
        # 表格中有number of shares=award, 有number of options=option
        if (tbl_share_type := guess_tbl_share_type(table)) and tbl_share_type not in {"all", share_type}:
            return False
        header_texts = table_headers(table)
        header_keys = set(chain.from_iterable(extract_group_key(ht, share_type, multi=True) for ht in header_texts))
        if (valid := is_valid_keys(header_keys, correct_group_keys, incorrect_group_keys)) is not None:
            return valid
        # 遍历单元格严格匹配关键词
        has_incorrect = False
        for cell in chain.from_iterable(row for row in table.rows):
            text = clean_txt(cell.text_no_superscript, remove_cn_text=True)
            if len(text.split()) > 8 or P_ONLY_NUM.search(text) or P_DATE.search(text):
                continue
            text = clean_txt(text, remove_blank=True).lower()
            if any(text == key.lower() for key in correct_group_keys):
                return True
            if any(text == key.lower() for key in incorrect_group_keys):
                has_incorrect = True
        if has_incorrect:
            return False
        group_keys = set(
            chain.from_iterable(extract_group_key(t, share_type, multi=True) for t in table.possible_titles)
        )
    elif is_paragraph_elt(element, strict=True):
        text = get_elem_text(pdfinsight, element)
        if not text:
            return False
        group_keys = extract_group_key(text, share_type, multi=True)
    elif title := clean_txt(element.get("title") or "", remove_cn_text=True):
        group_keys = extract_group_key(title, share_type, multi=True)
    else:
        return False
    if (valid := is_valid_keys(group_keys, correct_group_keys, incorrect_group_keys)) is not None:
        return valid

    # 4. 所有提到的key都属于其他类型，返回False
    if group_keys:
        if share_type == "award" and real_all(["option" in key.lower() for key in group_keys]):
            return False
        if share_type == "option" and real_all([P_SHARE_AWARD.nexts(key.lower()) for key in group_keys]):
            return False

    # 5. 根据元素章节判断
    if (chapter := pdfinsight.syllabus_dict.get(element.get("syllabus"))) and (
        chapter_keys := extract_group_key(chapter.get("title", ""), share_type, multi=True)
    ):
        if (valid := is_valid_keys(chapter_keys, correct_group_keys, incorrect_group_keys)) is not None:
            return valid

    # 6. 是否在当前分组章节中
    if any(
        any(is_in_range(element["index"], item.range) for item in items if item.is_chapter)
        for key, items in share_group.items()
        if key in correct_group_keys
    ):
        return True

    # 7. 是否在其他分组的章节中
    if any(
        any(is_in_range(element["index"], item.range) for item in items if item.is_chapter)
        for key, items in share_group.items()
        if key in incorrect_group_keys
    ):
        return False

    # 8. 其余情况返回True
    return True


def is_valid_keys(keys: set[str], correct_group_keys: set[str], incorrect_group_keys: set[str]) -> bool | None:
    if not keys:
        return None
    if keys & correct_group_keys:
        return True
    if keys & incorrect_group_keys:
        return False
    return None


if __name__ == "__main__":
    text = """Scheme, respectively, and (b) as no grants had been made under the 51 Award Scheme during the year ended 31 December 2022, the number of options and """
    print(text)
    print(
        extract_group_key(
            text,
            "option",
            multi=True,
        )
    )
    # print(is_same_key("award", "2020shareincentiveplan", "restrictedsharesunderthe2020shareincentiveplan"))
