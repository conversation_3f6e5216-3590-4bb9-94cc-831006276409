import re

from remarkable.common.constants import AnswerValueEnum
from remarkable.common.pattern import PatternCollection
from remarkable.common.util import clean_txt, split_paragraph
from remarkable.predictor.models.base_model import BaseModel
from remarkable.predictor.schema_answer import ParagraphResult

# 1. 匹配总发行股份数
shares_pattern = r"total number of (?:issued|issued and fully paid up) (?:shares|Shares|ordinary shares).*?was ([\d,]+)(?:\s+[Ss]hares)?"

# 2. 匹配有权参加和投票的股份数
voting_shares_pattern = r"total number of [Ss]hares entitling the holders? to attend and vote.*?was ([\d,]+)"

# 3. 匹配日期
date_pattern = r"As at (?:the date of )?(?:the )?\d{4}? ?AGM|As at.*?(?:20\d{2})"

# 额外的模式
# 4. 匹配需要回避投票的股东
abstain_pattern = r"([^.]+) (?:were|was) required to abstain.*?from voting"

# 5. 匹配上市规则引用
rules_pattern = (
    r"Rule (?:13\.40|17\.47A) of (?:the )?(?:Rules Governing the Listing of Securities|GEM Listing Rules|Listing Rules)"
)

# 6. 匹配股东出席比例
attendance_pattern = r"representing approximately ([\d.]+)% of the (?:total number of |issued )?[Ss]hares"


class PollResultCommon(BaseModel):
    def train(self, dataset, **kwargs):
        pass

    @property
    def pattern(self):
        return PatternCollection(self.get_config("pattern", []), re.I)

    @property
    def neglect_pattern(self):
        return PatternCollection(self.get_config("neglect_pattern", []), re.I)

    @property
    def neglect_element_pattern(self):
        return PatternCollection(self.get_config("neglect_element_pattern", []), re.I)

    def predict_schema_answer(self, elements):
        results = []
        if not elements:
            return results
        first_embedding_crude_answer, first_crude_element = None, None
        crude_key = f"{self.schema.parent.name}-{self.columns[0]}"
        embedding_key = "ps_crude_answer" if self.enum == AnswerValueEnum.PS else "ns_crude_answer"
        embedding_crude_answer = self.predictor.prophet.metadata[embedding_key].get(crude_key)
        if not embedding_crude_answer:
            return results
        if embedding_crude_answer:
            first_embedding_crude_answer = embedding_crude_answer[0]
        if elements:
            first_crude_element = elements[0]

        if (
            first_crude_element
            and first_embedding_crude_answer
            and first_embedding_crude_answer["element_index"] == first_crude_element["index"]
        ):
            if self.neglect_pattern.nexts(clean_txt(first_embedding_crude_answer["text"])):
                results.extend(self.get_answer_from_sub_elements(first_crude_element))
                return results
            if self.neglect_element_pattern.nexts(clean_txt(first_crude_element["text"])):
                return results
            if first_embedding_crude_answer["element_sequence"] == 0:
                answer_result = ParagraphResult(first_crude_element, first_crude_element["chars"])
            else:
                answer_text = first_embedding_crude_answer["text"]
                element_text = first_crude_element.get("text", "")
                if answer_text in element_text:
                    start = element_text.index(answer_text)
                    end = start + len(answer_text)
                else:
                    start, end = 0, len(element_text)
                answer_result = ParagraphResult(first_crude_element, first_crude_element["chars"][start:end])
            results.append(self.create_result([answer_result], schema=self.schema, value=self.enum))

        if not results:
            results.extend(self.get_answer_from_sub_elements(first_crude_element))
        return results

    def get_answer_from_sub_elements(self, first_crude_element):
        results = []
        element_text = first_crude_element.get("text", "")
        for sub_text in split_paragraph(element_text):
            if self.pattern.nexts(clean_txt(sub_text)):
                start = element_text.index(sub_text)
                end = start + len(sub_text)
                answer_result = ParagraphResult(first_crude_element, first_crude_element["chars"][start:end])
                results.append(self.create_result([answer_result], schema=self.schema, value=self.enum))
        return results
