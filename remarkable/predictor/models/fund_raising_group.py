import logging
import re
from collections import Counter, defaultdict
from copy import copy, deepcopy
from dataclasses import dataclass, fields
from datetime import datetime
from functools import cached_property, lru_cache
from itertools import chain
from operator import itemgetter
from typing import Callable

from remarkable.common.common import (
    FUNDRAISING_COLS,
    diff_days,
    get_first_key,
    is_para_elt,
    is_table_elt,
    real_all,
    to_datetime,
)
from remarkable.common.common_pattern import (
    P_CHAPTER_PREFIX,
    R_AS_AT,
    R_CHAPTER_PREFIX,
    R_MIDDLE_DASH,
    R_MIDDLE_DASHES,
)
from remarkable.common.constants import (
    DEFAULT_FUNDRAISING_EVENT_TYPE,
    AnswerValueEnum,
    FundRaisingRuleEnum,
    PDFInsightClassEnum,
)
from remarkable.common.pattern import (
    MATCH_NEVER,
    MatchMulti,
    NeglectPattern,
    PatternCollection,
    PositionPattern,
)
from remarkable.common.protocol import SearchPatternLike
from remarkable.common.util import clean_txt, is_in_range, split_paragraph
from remarkable.models.addition_data import AdditionData
from remarkable.models.hkex_company import HKEXCompany
from remarkable.models.manual_group_answer import ManualGroupAnswer
from remarkable.pdfinsight.reader_util import find_real_syllabus, find_table_title
from remarkable.plugins.predict.common import get_element_candidates
from remarkable.predictor.common_pattern import R_DATES, R_EN_MONTH
from remarkable.predictor.hkex_predictor.pattern import R_DATES_FOR_EXTRACT, R_SIM_MONTH
from remarkable.predictor.hkex_predictor.schemas.pattern import (
    P_DATE,
    P_DR_CHAPTER,
    P_MDA_CHAPTER,
    P_NOTE_CHAPTER,
    R_DURING_THE_YEAR_NO_END,
    R_MONEY_UNIT,
    gen_regex_by_date,
    gen_regex_by_num,
    gen_regex_by_price,
)
from remarkable.predictor.models.base_model import BaseModel
from remarkable.predictor.mold_schema import SchemaItem
from remarkable.predictor.schema_answer import CharResult, PredictorResult, PredictorResultGroup
from remarkable.predictor.utils import extract_date

logger = logging.getLogger(__name__)


R_MON_YEAR = rf"\b(\d{{1,2}}\s+)?(\b{R_SIM_MONTH}[a-z]{{0,6}}\s+)?\d{{4}}\b"
P_NON_SHARES = MatchMulti.compile(r"convertible|preference|warrant", operator=any)
R_SUBSCRIPTION = r"subscription"
P_CONVERTIBLE_BONDS = NeglectPattern.compile(
    match=r"convertible|bonds?\s*issue", unmatch=r"outstanding\s*convertible|bonds\s*outstanding"
)
R_SHARE = r"(ordinary|\bnew|rights?|preference|placing)\s*share|rights?\s*issue"
P_AS_AT_DATES = PatternCollection(
    [rf"({R_AS_AT}|\bon)\s*((\d{{1,2}}\s+|{R_EN_MONTH}\s+){{2}}\d{{4}}\s*and\s*)?{dt}" for dt in R_DATES_FOR_EXTRACT],
    flags=re.I,
)
DYNAMIC_MODEL_ID = "need_row_pattern"
P_IPO_KW_CHAPTERS = MatchMulti.compile(
    r"proceeds|\bfund|raising",
    # http://************:55647/#/project/remark/266108?treeId=7506&fileId=70436&schemaId=15&projectId=7506&schemaKey=B9 元素块index=388
    r"\blisted\s*securities",
    rf"{R_CHAPTER_PREFIX}share\s*capital\s*$",
    operator=any,
)
# 使用以下关键词匹配章节，匹配上的元素块直接提取
P_KW_CHAPTER_TITLES = MatchMulti.compile(
    P_IPO_KW_CHAPTERS,
    r"rights?\s*(issue|share)|placing|placement",
    r"subscription.*shares|shares.*subscription",
    r"\b(open\s*offer|convertible|warrants?)\b",
    # r"(specific|general)\s*mandate|subscription",
    # http://************:55647/#/project/remark/269142?treeId=2573&fileId=70640&schemaId=18&projectId=2573&schemaKey=C1.3
    r"capital\s*structure",
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6240#note_661918
    r"\b(issuance|issue\s*of\s*share).*(specific|general)\s*mandate",
    operator=any,
)
# https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6240#note_664266
# debt: https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6238#note_661986
# https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6840#note_705513 B1中一开始就写了no net proceeds
R_IGNORE_B1_REASON = (
    r"(^|\s)(not\s*generate\s*any\s*(net\s*)?proceeds\s*of\s*cash|Debt\s*Capitalisation|no\s*net\s*proceeds)"
)
# 忽略B1的reason符合以下正则的addition_data记录
P_IGNORE_B1_REASONS = MatchMulti.compile(
    # # 收购公司
    # r"acquisition|\bto\s*acquire",
    # # 行使购股权
    # r"share\s*(option|award)\s*(scheme|plan)",
    # # 贷款资本化
    # r"(?i)capitali.*?loan|loan\s*capitali",
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6240#note_664266
    R_IGNORE_B1_REASON,
    operator=any,
)

# 文中若有以下描述（例如没有募到钱），认为B1-B10都为ND
P_B1_B10_MUST_ND = MatchMulti.compile(
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6236#note_662687
    rf"{R_DURING_THE_YEAR_NO_END}.*\sdid\s*n.t\s*issue(\s*or\s*allot)?\s*new\s*shares",
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6463#note_676930
    r"\b(were|was)\s*extended\s*and\s*to\s*be\s*matured",
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6239#note_662934
    r"\b(for|to)\s.*(capitali[sz]ation|capitali[sz]e).*\bdebts",
    r"\bfor\s.*?\bdebts?\s*capitali[sz]ation",
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6239#note_662934
    r"\bfor\s*capitali[sz]ation\s*of\s*\S+\s*debts",
    operator=any,
)
P_DAY_MONTH_YEAR = PatternCollection(R_DATES_FOR_EXTRACT, flags=re.I)
P_MONTH_YEAR = PatternCollection(
    [
        rf"(\s|^)(?P<mon>{R_SIM_MONTH})[a-z]{{0,6}}[,，]?\s*(?P<year>\d{{4}})(\s|$)",
        rf"(\s|^)(?P<year>\d{{4}})(?P<mon>{R_SIM_MONTH})[a-z]{{0,6}}(\s|$)",
    ],
    flags=re.I,
)


@dataclass
class MatchedTableRowPattern:
    """
    针对表格的每一行，生成唯一的正则表达式
    它们可能是b3/fund_raised/first_report_date/last_report_date中一个或多个
    """

    p_issue_type: SearchPatternLike = None
    p_b3: SearchPatternLike = None
    p_fund_raised: SearchPatternLike = None
    p_first_date: SearchPatternLike = None
    p_last_date: SearchPatternLike = None

    @property
    def useful_pattern(self):
        useful_patterns = []
        for field in fields(self):
            pattern = getattr(self, field.name)
            if pattern is not None:
                useful_patterns.append(pattern)
        if not useful_patterns:
            return None
        if len(useful_patterns) == 1:
            return useful_patterns[0]
        return MatchMulti.compile(*useful_patterns, operator=all)


class FundRaisingGroup(BaseModel):
    C_RULES = {FundRaisingRuleEnum.C1_1_1, FundRaisingRuleEnum.C1_2_1, FundRaisingRuleEnum.C1_3}
    P_LISTING_DATE = PatternCollection(
        [rf"\b(on|since|from|(?<!as )at)\s*{d}" for d in R_DATES_FOR_EXTRACT], flags=re.I
    )
    DEFAULT_IPO_EVENT_TYPE = "IPO 1"
    DEFAULT_EVENT_ID_MAP = {
        # 保证IPO的排序在最后
        DEFAULT_IPO_EVENT_TYPE: "zzz-ipo-1",
        # 保证no-event的排序在最前
        DEFAULT_FUNDRAISING_EVENT_TYPE: "",
    }
    P_AND = re.compile(r"(/|\band\b|&)(?!\s*use\s*of\s)", re.I)
    P_YEAR = MatchMulti.compile(r"\b2[01]\d{2}\b", operator=any)
    # 提取IPO募集情况，这里根据章节名称判断
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/5331#note_618255
    P_IPO_CHAPTER_TITLES = MatchMulti.compile(
        r"(?i)\b(from|through)\s*(the\s*)?listing",
        MatchMulti.compile(r"\blisted|\blisting", r"proceeds", operator=all),
        r"(?i)initial\s*[a-z]+\s*offer",
        rf"(^|[^{R_MIDDLE_DASHES}\w])IPO([^{R_MIDDLE_DASHES}\w]|$)",
        operator=any,
        flag=0,
    )
    # IPO不能单纯根据share/public/global offer判断，还需有listed/listing/initial关键词
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/5611#note_633196
    P_IPO_LIKE_TITLES = MatchMulti.compile(
        MatchMulti.compile(r"proceeds", r"(global|share|public)\s*offer|(\s|^)[AH]\s*share", operator=all),
        r"\b(from|through)\s*(the\s*)?((global|share|public)\s*offer|\s[AH]\s*share)",
        operator=any,
    )
    P_IPO_ELEMENTS = MatchMulti.compile(
        r"(?i)initial\s*[a-z]+\s*offer",
        rf"(^|[^{R_MIDDLE_DASHES}\w])IPO([^{R_MIDDLE_DASHES}\w]|$)",
        r"\blisted\s*on\b|\b[Ll]isting\s*date|\b(from|through)\s*(the\s*|its\s*)?[Ll]isting\b(?!\s*rule|of)",
        # http://************:5564/#/hkex/annual-report-checking/report-review/266223?fileId=70459&schemaId=15&rule=B4&delist=0
        r"(?i)\slisted.*?(main\s*board|Exchange)",
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6601
        rf"(?i)\bover{R_MIDDLE_DASH}allotment\b",
        flag=0,
        operator=any,
    )
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/5276#note_581263
    P_SKIP_SYLLABUS_TITLE = MatchMulti.compile(
        r"SUBSIDIARIES",
        r"events\s*after\s*the\s*(year|report|financial|FY\b)",
        r"share\s*(award|option|plan|incentive|scheme)",
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/5324#note_623098 排除重组章节
        r"restruct|reorgani",
        # http://************:55647/#/hkex/annual-report-checking/report-review/294417?fileId=70773&schemaId=5&rule=B2&delist=0 page=127
        r"accounting\s*polic(y|ies)",
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6414#note_673642
        r"long\s*position",
        # http://************:55647/#/project/remark/265268?treeId=15844&fileId=70268&schemaId=15&projectId=17&schemaKey=B6 index=1092
        r"reserve\s*fund",
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6238#note_661551
        r"proposed issuance",
        # http://************:55647/#/hkex/annual-report-checking/report-review/340835?fileId=104129&schemaId=5&rule=C1.3&delist=0
        # index=1325 误判为IPO
        r"\bs\s*a\s*l\s*e\b|p\s*u\s*r\s*c\s*h\s*a\s*s\s*e\b|\br\s*e\s*d\s*e\s*e?\s*m",
        # http://************:55647/#/hkex/annual-report-checking/report-review/266223?fileId=70459&schemaId=15&rule=B1&delist=0&page=144
        r"Interest",
        operator=any,
    )
    P_EVENT_TYPE_MAPPING = {
        "issue of preference shares": re.compile(r"preference\s*shares", re.I),
        "issue of convertible securities": P_CONVERTIBLE_BONDS,
        "issue of warrants": re.compile(r"\bwarrant", re.I),
        "issue of shares": re.compile(r"\bshares|subscription|placing|placement|rights?\s*issue", re.I),
    }
    # 组合excel的event_type（addition_data.b2) 和method of issue(addition_data.issue_method)，给出对应正则
    P_ISSUE_TYPE_MAPPING = {
        ("issue of shares", "subscription under transaction"): NeglectPattern.compile(
            match=r"subscription", unmatch=P_NON_SHARES
        ),
        ("issue of shares", "subscription"): NeglectPattern.compile(
            match=rf"{R_SUBSCRIPTION}|(new|ordinary)\s*shares",
            unmatch=r"convertible|preference|warrant|placing|placement|rights?\s*(issue|share)",
        ),
        # http://************:55647/#/project/remark/230375?treeId=37994&fileId=61209&schemaId=15&projectId=37994&schemaKey=B
        # http://************:55647/#/project/remark/269069?treeId=7358&fileId=70629&schemaId=18&projectId=7358&schemaKey=C1.2.1
        # placing排除commission： http://************:55647/#/project/remark/233221?treeId=13168&fileId=66492&schemaId=15&projectId=13168&schemaKey=B9 元素块115
        ("issue of shares", "top-up placing"): NeglectPattern.compile(
            match=r"\b(placing|placement)\b(?!\s*commission)", unmatch=P_NON_SHARES
        ),
        ("issue of shares", "placing"): NeglectPattern.compile(
            match=rf"(?<!top{R_MIDDLE_DASH}up )(placing|placement)\b(?!\s*commission)", unmatch=P_NON_SHARES
        ),
        ("issue of shares", "rights issue"): NeglectPattern.compile(
            match=r"rights?\s*(issue|shares?)", unmatch=P_NON_SHARES
        ),
        ("issue of shares", "open offer"): NeglectPattern.compile(match=r"\bopen\s*offer", unmatch=P_NON_SHARES),
        # ("issue of debt securities", "subscription"): MatchMulti.compile(r"\bdebt\b", R_SUBSCRIPTION, operator=all),
        # ("issue of debt securities", "placing"): MatchMulti.compile(r"\bdebt\b", "placing", operator=all),
        ("issue of convertible securities", "subscription under transaction"): P_CONVERTIBLE_BONDS,
        ("issue of convertible securities", "subscription"): P_CONVERTIBLE_BONDS,
        ("issue of convertible securities", "placing"): P_CONVERTIBLE_BONDS,
        # ("issue of convertible securities", "placing"): MatchMulti.compile(r"convertible", "placing", operator=all),
        # http://************:55647/#/hkex/annual-report-checking/report-review/265878?fileId=70390&schemaId=15&rule=B1&delist=0
        # http://************:55647/#/project/remark/265863?treeId=4887&fileId=70387&schemaId=15&projectId=17&schemaKey=
        ("issue of preference shares", "subscription"): re.compile(r"preference\s*shares", re.I),
        ("issue of warrants", "subscription"): re.compile(r"warrant", re.I),
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/5423#note_590441
        ("", ""): re.compile(r"ordinary\s*shares", re.I),
    }

    ENTERED_KEY_WORDS = {"entered into", "raise", "allot", "issued", "issuance", "issuing", "announce", "dated"}
    COMPLETED_KEY_WORDS = {"completed", "completion", "issued", "raised", "issuing", "allot", "dated", "sold"}
    # 找权益发行日
    P_ENTERED_INTO = PatternCollection(
        [
            *[
                rf"({R_CHAPTER_PREFIX}|[,，.]\s*)on\s*{r_date}.+?entered\s*into.+?(placing|subscription)\s*agreement"
                for r_date in R_DATES_FOR_EXTRACT
            ],
            *[
                rf"\bentered\s*into.+?(placing|subscription)\s*agreement.*?\s(on|at|dated)\s*{r_date}\b"
                for r_date in R_DATES_FOR_EXTRACT
            ],
            *[
                rf"({R_CHAPTER_PREFIX}|[,，.]\s*)on\s*{r_date}.+?entered\s*into.+?agreement.+?((net|gross)\s*proceeds|{R_SHARE})"
                for r_date in R_DATES_FOR_EXTRACT
            ],
            *[
                rf"({R_CHAPTER_PREFIX}|[,，.]\s*)on\s*{r_date}.+?announced\s.*?((net|gross)\s*proceeds|{R_SHARE})"
                for r_date in R_DATES_FOR_EXTRACT
            ],
        ],
        flags=re.I,
    )
    # 找完成日
    P_COMPLETION_DATE = PatternCollection(
        [
            *[rf"\b(on|at)\s*{r_date}\b.+?\s*(completed|completion)" for r_date in R_DATES_FOR_EXTRACT],
            *[rf"(completed|completion)[^,，]+?\b(on|at|dated)\s*{r_date}\b" for r_date in R_DATES_FOR_EXTRACT],
        ],
        flags=re.I,
    )
    P_SHARE_CAPITAL = MatchMulti.compile(r"(?i)share\s*capital", operator=any)
    # activity: http://************:55647/#/project/remark/266278?treeId=14511&fileId=70470&schemaId=15&projectId=17&schemaKey=B1 index=668
    # share\s*consolidation: https://jura6-lir.paodingai.com/#/hkex/annual-report-checking/report-review/340606?fileId=104100&schemaId=5&rule=B7&delist=0
    P_EVENT_ALIAS = [
        re.compile(regex, re.I)
        for regex in [
            rf"(?P<num>{R_MON_YEAR}|first|second|third|fou?rth|fifth|sixth|\b1st|\b2nd|\b3rd|\b[4-6]th\s*|one|two|three|four|five|six|\b[1-9]\b)\s+(?P<name>placing|subscription|warrants?|rights?\s*issue|bonds?\s*issue|convertible|activity|share\s*consolidation)",
            rf"(?P<name>(placing|subscription|subscriber|warrants?|rights?\s*issue|bonds?\s*issue|activity|share\s*consolidation)\s+(price|of\s*([a-z]+\s*)?shares(\s+[io]n)?|\d{{4}}{R_MIDDLE_DASH})?)\s*(?P<num>one|two|three|four|five|six|\b[1-9A-Z]\b|{R_MON_YEAR}|[IV]{{1,4}}\b)",
        ]
    ]
    ISSUE_OF_SHARES = "issue of shares"
    R_AS_AND_DURING = [rf"({R_AS_AT}|(for|during)\s*the\s*year)\s*{dt}" for dt in R_DATES]
    # 句式： 截至当年末，已经募集到xxx million等
    # http://************:55647/#/project/remark/266498?treeId=8859&fileId=70514&schemaId=15&projectId=8859&schemaKey=B9 元素块index=1634
    P_RECEIVED_MONEY = PositionPattern.compile(
        "|".join(R_AS_AND_DURING), r"(ha[sd]|have)\s*received", rf"{R_MONEY_UNIT}\d"
    )
    R_DATE_PREP = r"\b(^|on|(?<!as )at|and|[,，]|\t|dated)\s*"
    R_MONTH_PREP = r"\b(^|on|(?<!as )at|in|from|[,，]|\t|dated)(\s*the)?\s*"
    P_ENTERED_DATES = PatternCollection(
        [
            rf"(\s|^)(?P<day>[1-2]?\d|3[01])?\s*(?P<mon>{R_SIM_MONTH})[a-z]{{,6}}[，,]?\s*and\s*\d{{,2}}\s*{R_SIM_MONTH}[a-z]{{,6}}[，,]?\s*(?P<year>\d{{4}})",
            rf"(?P<mon>{R_SIM_MONTH})[a-z]{{,6}}\s*(?P<day>[1-2]?\d|3[01])?[，,]?\s*and\s*{R_SIM_MONTH}[a-z]{{,6}}\s*\d{{,2}}[，,]?\s*(?P<year>\d{{4}})",
            *R_DATES_FOR_EXTRACT,
            # http://************:55647/#/hkex/annual-report-checking/report-review/258057?fileId=68826&schemaId=5&rule=C1.1.1&delist=0
            rf"\scomplet(ed|ion)\s*in\s*(?P<mon>{R_SIM_MONTH})[a-z]{{,6}}\s*(?P<year>\d{{4}})",
        ],
        flags=re.I,
    )
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6840#note_707259
    P_SKIP_ELEMENT = MatchMulti.compile(r"\bdisposal", operator=any)
    P_SHARE_OFFER = MatchMulti.compile(r"\b(global|share|public)\s*offer", operator=any)

    def __init__(self, options: dict, schema: SchemaItem, predictor=None):
        super().__init__(options, schema, predictor)
        self.addition_rows = []
        # 存储章节index和title映射关系
        self.syllabus_titles = {}
        # 存储表格index和title关系
        self.table_titles = {}
        # 存储所有表格元素块
        self.table_elements = {}
        # 如果标题中有placing one/1/II等描述，则把title名称和对应序号关系存起来
        self.aliases_of_chapter = defaultdict(set)
        # 如果element或element所属标题中有placing one/1/II/2022等描述，则把element和对应别名关系存起来
        self.aliases_of_element = defaultdict(set)
        self.alias_of_event = defaultdict(set)
        # 存储每个元素块提取到的发行日
        self.entered_dates_of_element = defaultdict(set)
        # 有多组发行的募集资金表格中，每行数据可以对应一个外部数据，这里存储每个表格中，每种外部数据对应的唯一正则
        self.table_row_patterns = defaultdict(dict)
        # 存储所有找到的候选元素块
        self.all_candidate_elements = []

    def train(self, dataset, **kwargs):
        pass

    @property
    def column_configs(self):
        return self.get_config("column_configs", {})

    @property
    def group_default_enum(self):
        return self.config.get("group_default_enum", AnswerValueEnum.ND.value)

    @property
    def post_process_answers(self) -> Callable:
        """
        后处理函数，配置了该函数会据此对结果进行后处理
        """
        return self.config.get("post_process_answers")

    @property
    def question_id(self) -> int:
        return self.predictor.prophet.metadata.get("question_id") or 0

    @property
    def file_id(self) -> int:
        if file := self.predictor.prophet.metadata.get("file"):
            return file.id
        return 0

    @property
    def stock_code(self):
        """
        stock code
        """
        return self.predictor.prophet.metadata.get("stock_code") or ""

    @cached_property
    def schema_key(self):
        """
        提取路径，返回示例： B1
        """
        return self.schema.path[1]

    @cached_property
    def addition_data(self):
        """
        当前文件的addition_data记录
        """
        addition_data = [
            r
            for r in self.predictor.prophet.metadata.get("addition_data") or []
            if r.b2.lower() not in AdditionData.IGNORE_EVENT_TYPES
        ]
        return sorted(addition_data, key=lambda x: x.first_report_date)

    def get_element_indices_from_crude(self, crude_elements) -> set[int]:
        if not crude_elements:
            return set()
        for rule, elements in crude_elements.items():
            if rule.split("-")[0] == self.schema.name:
                return {e["element_index"] for e in elements}
        return set()

    @cached_property
    def ps_crude_indices(self) -> set[int]:
        return self.get_element_indices_from_crude(self.predictor.prophet.metadata.get("ps_crude_elements"))

    @cached_property
    def ns_crude_indices(self) -> set[int]:
        return self.get_element_indices_from_crude(self.predictor.prophet.metadata.get("ns_crude_elements"))

    @cached_property
    def children_models(self):
        models = {}
        for column, model_configs in self.column_configs.items():
            schema = self.predictor.schema.children_schema(column)
            # 因为在配置文件中指定了fake_leaf 这里需要显式修改predictor的column
            self.predictor.columns = [column]
            models[column] = [self.predictor.create_model(model_config, schema) for model_config in model_configs]
        return models

    @classmethod
    def regex_issue_type(cls, addition_row: AdditionData) -> SearchPatternLike | None:
        """
        用b2+issue type列组合生成对应正则
        """
        if not addition_row.b2:
            return MATCH_NEVER
        return cls.P_ISSUE_TYPE_MAPPING.get(addition_row.type_key) or MatchMulti.compile(
            addition_row.event_type.replace("issue of", ""), addition_row.issue_type, operator=all
        )

    @classmethod
    def regex_b2(cls, addition_row: AdditionData) -> SearchPatternLike | None:
        """
        仅用b2列生成正则
        """
        if not addition_row.b2:
            return MATCH_NEVER
        return cls.P_EVENT_TYPE_MAPPING.get(addition_row.b2.lower())

    @staticmethod
    def regex_b3(addition_row: AdditionData):
        if not addition_row.b3:
            return MATCH_NEVER
        return gen_regex_by_num(addition_row.b3)

    @staticmethod
    def regex_fund_raised(addition_row: AdditionData):
        if not addition_row.fund_raised:
            return MATCH_NEVER
        return gen_regex_by_price(addition_row.fund_raised, min_digit=1)

    def _find_alias_for_addition(self, row: AdditionData, aliases: set[str]) -> set[str] | None:
        def can_match_addition_by_alias(alias_: str):
            if not alias_:
                return False
            if not self.regex_issue_type(row).search(alias_):
                return False
            # 别名中带了确切日期，直接对比日期
            if matched := P_DAY_MONTH_YEAR.nexts(alias_):
                year, month, day = matched.group("year"), matched.group("mon"), matched.group("day")
                if date_ := to_datetime(f"{year}-{month.capitalize()}-{day}", r"%Y-%b-%d"):
                    # todo 判断标题如果有关键词completion，则用last_report_date
                    return date_ in {
                        to_datetime(row.first_report_date, "%Y-%m-%d"),
                        to_datetime(row.last_report_date, "%Y-%m-%d"),
                    }
            # 别名中带了确切月份，直接对比月份
            if matched := P_MONTH_YEAR.nexts(alias_):
                year, month = matched.group("year"), matched.group("mon")
                if date_ := to_datetime(f"{year}-{month.capitalize()}", r"%Y-%b"):
                    return date_.strftime("%Y-%m") == to_datetime(row.first_report_date, "%Y-%m-%d").strftime("%Y-%m")
            year = alias_.split()[-1] if alias_ else ""
            if len(year) != 4:
                return False
            # 找月份最小的年份
            return year == min(
                (datetime.strptime(d, "%Y-%m-%d") for d in row.report_dates), key=lambda x: x.strftime("%m-%d")
            ).strftime("%Y")

        aliases = aliases or set()
        aliases_ = self.alias_of_event[row.event_id]
        if aliases_ and aliases_ & aliases:
            return aliases_ & aliases
        if not aliases:
            return None
        matched_aliases = [a for a in aliases if can_match_addition_by_alias(a)]
        # 仅匹配到1个alias，则绑定event_id和alias关系
        if len(matched_aliases) != 1:
            return None
        aliases_ = matched_aliases[0]
        # 仅匹配到1个alias，则绑定event_id和alias关系
        self.alias_of_event[row.event_id].add(aliases_)
        return {aliases_}

    def _extract_entered_dates_from_para(self, text: str) -> list[str]:
        """
        从段落中提取发行日期/完成日期
        """
        dates = []
        for matched in self.P_ENTERED_DATES.finditer(text):
            group_dict = matched.groupdict()
            day, mon, year = group_dict.get("day"), group_dict.get("mon"), group_dict.get("year")
            # http://************:55647/#/project/remark/242457?treeId=30959&fileId=68033&schemaId=15&projectId=17&schemaKey=B1 index=94
            if day and day != "0":
                fmt = "%Y-%m-%d"
                date_ = to_datetime(f"{year}-{mon}-{day}", "%Y-%b-%d")
            else:
                fmt = "%Y-%m"
                date_ = to_datetime(f"{year}-{mon}", "%Y-%b")
            if date_:
                dates.append(date_.strftime(fmt))
            else:
                logger.warning(rf"[fid={self.file_id}] invalid date in paragraph: {text}")
        return dates

    def _get_entered_dates_by_addition(
        self, text: str, syllabus_title: str, tbl_idx: int = None, tbl_title: str = None, aliases: set[str] = ()
    ) -> set[str]:
        """
        注意： 这一步必须用self.addition_data, 因为self.addition_rows还未生成
        http://************:55647/#/project/remark/296381?treeId=24090&fileId=71023&schemaId=18&projectId=24090&schemaKey=C1.1.1
        """

        def match_first_or_last(has_kw, date_str):
            if not has_kw:
                return None
            # 匹配日期  TODO 日期偏移后期视情况调整
            for offset in range(0, 4):
                for text_date in all_dates:
                    if len(text_date) != 10:
                        continue
                    if diff_days(text_date, date_str) == offset:
                        return gen_regex_by_date(text_date, prep_regex=prep_regex)
            # 匹配月份
            for text_date in all_dates:
                if len(text_date) == 10:
                    continue
                if date_str.startswith(text_date):
                    return gen_regex_by_date(text_date, ignore_day=True, prep_regex=self.R_MONTH_PREP)
            return None

        def set_tbl_pattern(addition_row_):
            """
            给table row绑定正则，同时为每个event_id绑定一个唯一别名
            """
            if tbl_idx and (pattern := pattern_dict[addition_row_.event_id].useful_pattern):
                self._add_tbl_row_pattern(tbl_idx, addition_row_.event_id, pattern)

        tbl_title = "" if tbl_title is None else tbl_title
        prep_regex = "" if text == syllabus_title else self.R_DATE_PREP
        all_dates = self._extract_entered_dates_from_para(text)
        matched_rows = []
        first_count, last_count = defaultdict(int), defaultdict(int)
        b3_count, fund_raised_count = defaultdict(int), defaultdict(int)
        entered_dates = set()
        pattern_dict = {}
        count_addition = {
            r.type_key: len([d for d in self.addition_data if d.type_key == r.type_key]) for r in self.addition_data
        }
        for row in self.addition_data:
            # 提取日期时，仅用b2匹配event_type，不考虑issue_method，因为部分文档不体现具体issue method
            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/5331#note_616541
            p_b2 = self.regex_b2(row)
            if not (p_b2.search(tbl_title) or p_b2.search(syllabus_title) or p_b2.search(text)):
                continue
            if cur_aliases := self._find_alias_for_addition(row, aliases or set()):
                entered_dates.add(row.first_report_date)
                if tbl_idx:
                    self._add_tbl_row_pattern(tbl_idx, row.event_id, self.gen_pattern_by_alias(cur_aliases))
            p_issue_type, p_b3, p_raised = self.regex_issue_type(row), self.regex_b3(row), self.regex_fund_raised(row)
            p_first_date = gen_regex_by_date(row.first_report_date, prep_regex=prep_regex)
            p_last_date = gen_regex_by_date(row.last_report_date, prep_regex=prep_regex)
            # 尝试匹配类型/b3/fund_raised/first_date/last_date
            matched_type = (
                p_issue_type.search(text) or p_issue_type.search(tbl_title) or p_issue_type.search(syllabus_title)
            )
            if not matched_type:
                continue
            matched_b3, matched_raised = p_b3.search(text), p_raised.search(text)
            matched_first, matched_last = p_first_date.search(text), p_last_date.search(text)
            if tbl_title or text == syllabus_title:
                has_entered_kw, has_completed_kw = True, True
            else:
                has_entered_kw = any(kw in text for kw in self.ENTERED_KEY_WORDS)
                has_completed_kw = any(kw in text for kw in self.COMPLETED_KEY_WORDS)
            # 发行日偏移尝试匹配
            if not matched_first and (p_first_date := match_first_or_last(has_entered_kw, row.first_report_date)):
                matched_first = True
            # 完成日偏移尝试匹配
            if not matched_last and (p_last_date := match_first_or_last(has_completed_kw, row.last_report_date)):
                matched_last = True
            # 类型能作为决定性条件的因素： 1.类型不为issue of shares 2.或者只有一条外部数据 3.或者issue_type不为subscription/placing
            if count_addition[row.type_key] == 1 or row.event_type != "issue of shares":
                matched_type = True
            else:
                matched_type = "subscription" in row.issue_type or "placing" in row.issue_type
            if not tbl_idx:
                # 精准匹配到类型+日期，则直接取
                for sentence in split_paragraph(text, remove_cn_text=True):
                    # 根据正则提取发行日
                    if not any(kw in sentence for kw in self.ENTERED_KEY_WORDS | self.COMPLETED_KEY_WORDS):
                        continue
                    if entered_date := extract_date(self.P_ENTERED_INTO, sentence, str_format="%Y-%m-%d"):
                        if entered_date == row.first_report_date:
                            entered_dates.add(row.first_report_date)
                            continue
                    # 提取完成日
                    if completion_date := extract_date(self.P_COMPLETION_DATE, sentence, str_format="%Y-%m-%d"):
                        if completion_date == row.last_report_date:
                            entered_dates.add(row.first_report_date)
            pattern_attrs = {}
            if matched_type:
                pattern_attrs["p_issue_type"] = p_issue_type
            if matched_b3:
                b3_count[row.b3] += 1
                pattern_attrs["p_b3"] = p_b3
            if matched_raised:
                fund_raised_count[row.fund_raised] += 1
                pattern_attrs["p_fund_raised"] = p_raised
            if matched_first:
                first_count[row.first_report_date] += 1
                pattern_attrs["p_first_date"] = p_first_date
            if matched_last:
                last_count[row.last_report_date] += 1
                pattern_attrs["p_last_date"] = p_last_date
            if pattern_attrs:
                if pattern_attrs:
                    pattern_dict[row.event_id] = MatchedTableRowPattern(**pattern_attrs)
                matched_rows.append((matched_type, matched_first, matched_b3, matched_last, matched_raised, row))
        if len(matched_rows) == 1:
            addition_row = matched_rows[0][-1]
            set_tbl_pattern(addition_row)
            entered_dates.add(addition_row.first_report_date)
            return entered_dates
        # addition_row中first_date/last_date/b3都可能重复，因此需要对重复情况做处理
        # http://************:55647/#/project/remark/266443?treeId=10192&fileId=70503&schemaId=15&projectId=10192&schemaKey=B9
        for matched_type, matched_first, matched_b3, matched_last, matched_raised, row in matched_rows:
            if not (matched_type or matched_b3):
                continue
            enter_date = row.first_report_date
            count_first, count_last = first_count[enter_date], last_count[row.last_report_date]
            count_b3, count_raised = b3_count[row.b3], fund_raised_count[row.fund_raised]
            # 1. 匹配到了first_date/b3/last_date/fund_raised中的一个或多个，但匹配上的addition个数都大于1，则可能取错，这里不提取
            if real_all(
                {
                    c > 1
                    for c in {
                        count_first if matched_first else 0,
                        count_b3 if matched_b3 else 0,
                        count_last if matched_last else 0,
                        count_raised if matched_raised else 0,
                    }
                }
            ):
                continue
            # 2. 同时匹配上了first_date/last_date/b3/fund_raised中的任2个，则认为可取
            # 3. type/first_date/b3/last_date/month_of_last唯一且匹配，则认为可取
            if sum([matched_first, matched_b3, matched_last, matched_raised]) > 1 or any(
                count == 1 for count in {count_first, count_b3, count_last, count_raised}
            ):
                set_tbl_pattern(row)
                entered_dates.add(enter_date)
        if entered_dates:
            return entered_dates
        # 同时匹配到2个日期，且对应日期的addition_row也有2个，则认为该段落同时属于这两个分组
        # http://************:55647/#/project/remark/264498?treeId=23794&fileId=70114&schemaId=15&projectId=17&schemaKey=B1 index=243
        if matched_rows and len(matched_rows) == len(all_dates):
            for _, matched_first, _, matched_last, _, addition_row in matched_rows:
                if not (matched_first or matched_last):
                    continue
                entered_dates.add(addition_row.first_report_date)
        return entered_dates

    def _add_tbl_row_pattern(self, tbl_idx: int, event_id: str, pattern: SearchPatternLike):
        if event_id in self.table_row_patterns.get(tbl_idx, []):
            self.table_row_patterns[tbl_idx][event_id].append(pattern)
        else:
            self.table_row_patterns[tbl_idx][event_id] = [pattern]

    @staticmethod
    def gen_pattern_by_alias(aliases: set[str]):
        regs = []
        for alias in aliases:
            *name_words, num_str = alias.split()
            regs.extend([rf"\b{alias.replace(' ', r'\s*')}\b", rf"\b{num_str} {r'\s*'.join(name_words)}\b"])
        return MatchMulti.compile(*regs, operator=any)

    def extract_entered_dates(
        self, text: str, syllabus_title: str, *, tbl_idx: int = None, tbl_title: str = None, aliases: set[str] = ()
    ) -> set[str]:
        """
        带有entered into句式的句子，提取日期
        """
        # 根据addition_data提取发行日
        if enter_dates := self._get_entered_dates_by_addition(
            text, syllabus_title, tbl_idx=tbl_idx, tbl_title=tbl_title, aliases=aliases
        ):
            return enter_dates
        type_pattern_map = {row.first_report_date: self.regex_issue_type(row) for row in self.addition_data}
        entered_dates = set()
        for para in split_paragraph(text, remove_cn_text=True):
            # 根据正则提取发行日
            if tbl_title or not any(kw in para for kw in self.ENTERED_KEY_WORDS):
                continue
            if entered_date := extract_date(self.P_ENTERED_INTO, para, str_format="%Y-%m-%d"):
                if (pattern := type_pattern_map.get(entered_date)) and not pattern.search(text):
                    continue
                entered_dates.add(entered_date)
        return entered_dates

    def get_valid_para_text(self, index):
        type_, element = self.pdfinsight.find_element_by_index(index)
        if type_ != PDFInsightClassEnum.PARAGRAPH.value:
            return ""
        text = clean_txt(element["text"])
        if self.pdfinsight.is_skip_element(element) or {text.upper(), element["text"].upper()} & {
            s["title"] for s in self.pdfinsight.root_syllabuses
        }:
            return ""
        return text

    def get_ipo_elements_from_syllabus(
        self, title: str, score_map: dict[int, float], start: int, end: int
    ) -> tuple[int, dict[int, dict]]:
        """
        在章节中找IPO元素块
        1. 如果章节标题包含IPO关键词，则取整章元素块:
        http://************:55647/#/project/remark/265598?treeId=38065&fileId=70334&schemaId=15&projectId=38065&schemaKey=B9
        2. 如果仅第一段包含IPO关键词，且段落没有小标题，取整章的元素块:
        http://************:55647/#/project/remark/265598?treeId=38065&fileId=70334&schemaId=15&projectId=38065&schemaKey=B9
        3. 仅第一段包含IPO关键词且段落有小标题，则取到第二个小段落之前:
        http://************:55647/#/project/remark/266358?treeId=5331&fileId=70486&schemaId=18&projectId=5331&schemaKey=C1.2.1
        4. 有多张表格，则用表格拆分
        http://************:55647/#/project/remark/200540?treeId=12299&fileId=65182&schemaId=15&projectId=12299&schemaKey=B9
        """
        table_indices = [
            e["index"]
            for e in self.get_candidate_elements_by_range([start, end], aim_types={PDFInsightClassEnum.TABLE.value})
        ]
        title = clean_txt(title, remove_cn_text=True)
        if not self.P_IPO_CHAPTER_TITLES.search(title) or self.has_and_in_text(title):
            title_like_ipo = self.P_IPO_LIKE_TITLES.search(title)
            if not ("proceeds" in title.lower() or title_like_ipo):
                return start, {}
            type_, first_element = self.pdfinsight.find_element_by_index(start + 1)
            if type_ != PDFInsightClassEnum.PARAGRAPH.value:
                return start, {}
            first_element_text = clean_txt(first_element["text"], remove_cn_text=True)
            # 有share/global/public offering关键词，同时有募集资金表格，暂时认为是IPO章节
            if not self.P_IPO_ELEMENTS.search(first_element_text) and not (
                (title_like_ipo or self.P_IPO_ELEMENTS.search(first_element_text)) and table_indices
            ):
                return start, {}
            if P_CHAPTER_PREFIX.search(first_element_text):
                # IPO章节下有子章节或带序号的段落，或者有序号，则按照序号切分，第一组为IPO，其余的不是
                for idx in range(start + 2, end):
                    if not (text := self.get_valid_para_text(idx)):
                        continue
                    if P_CHAPTER_PREFIX.search(text):
                        end = idx
                        break
            elif len(table_indices) > 1:
                # IPO章节下有多张表，如果表格不是连续表格，只取第一个表格 TODO 考虑notes
                for idx in range(table_indices[0] + 1, end):
                    if self.get_valid_para_text(idx):
                        continue
                    # 走到这里表示表格之间有段落描述
                    if any(idx < i for i in table_indices):
                        end = idx
                        break
            elif self.has_and_in_text(title):
                # 标题有and时，仅提取满足正则的元素块
                # http://************:55647/#/hkex/annual-report-checking/report-review/265627?fileId=70340&schemaId=5&rule=C1.3
                for idx in range(start + 1, end):
                    if not (text := self.get_valid_para_text(idx)):
                        continue
                    if not self.P_IPO_ELEMENTS.search(text):
                        end = idx - 1
                        break
        ipo_elements = {}
        for element in self.get_candidate_elements_by_range([start, end]):
            index = element["index"]
            element["score"] = score_map.get(index, 0)
            ipo_elements[index] = element
        return end, ipo_elements

    @classmethod
    def is_skip_title(cls, title: str):
        return cls.P_SKIP_SYLLABUS_TITLE.search(clean_txt(title, remove_cn_text=True))

    @staticmethod
    def merge_syllabus_result(base_result, result):
        for key, elements in result.items():
            base_result[key].extend(elements)

    def get_elements_by_syllabus(
        self,
        syllabus: dict,
        score_map: dict,
        exclude_elt_indices: set[int],
        checked_syll_indices: set[int],
        ipo_elements,
        root_title=None,
    ) -> dict[(str, int), list[dict]]:
        syllabus_index, syllabus_title = syllabus["index"], syllabus["title"]
        self.syllabus_titles[syllabus_index] = syllabus_title

        result = defaultdict(list)
        parent_titles = self.pdfinsight.get_parent_titles(syllabus_index)
        # 过滤期后数据及重组章节
        # http://************:55647/#/project/remark/265984?treeId=3706&fileId=70411&schemaId=18&projectId=3706&schemaKey=C1.2.1
        if not parent_titles or any(self.is_skip_title(t) for t in parent_titles):
            checked_syll_indices.add(syllabus_index)
            return result

        start, end = syllabus["range"]
        # 提取IPO章节中，描述IPO的段落
        ipo_end, cur_ipo_elements = self.get_ipo_elements_from_syllabus(syllabus_title, score_map, start, end)
        if cur_ipo_elements:
            ipo_elements.update(cur_ipo_elements)
            exclude_elt_indices.update(cur_ipo_elements)
            if ipo_end == end:
                return result
        page = syllabus["dest"]["page_index"]
        # 优先取page_chapter_from_first_para中的root章节
        # http://************:55647/#/project/remark/242823?treeId=17250&fileId=68094&schemaId=15&projectId=17&schemaKey=B6 index=288
        root_title = root_title or self.pdfinsight.page_chapter_from_first_para.get(page) or parent_titles[0]
        # 取所属章节的其他元素块（不在初步定位中）
        syll_elements = self.get_candidate_elements_by_range(
            [ipo_end, end], exclude_indices=exclude_elt_indices, include_start=True
        )
        # 如果当前章节不是最小章节，还有子章节，则需要拆到最小章节
        for s_element in syll_elements:
            index = s_element["index"]
            if self.pdfinsight.is_syllabus_title(s_element) or index in exclude_elt_indices:
                continue
            if syllabus["children"]:
                child_syllabus = self.pdfinsight.find_syllabuses_by_index(index)[-1]
                if child_syllabus["index"] != syllabus_index:
                    self.merge_syllabus_result(
                        result,
                        self.get_elements_by_syllabus(
                            child_syllabus, score_map, exclude_elt_indices, checked_syll_indices, ipo_elements
                        ),
                    )
                    continue
            exclude_elt_indices.add(index)
            result[(root_title, syllabus_index)].append(s_element)
        return result

    def get_kw_chapter_elements(
        self,
        score_map: dict[int, float],
        exclude_elt_indices: set[int],
        checked_syll_indices: set[int],
        ipo_elements: dict[int, dict],
    ) -> dict[(str, int), list[dict]]:
        """
        根据P_KW_CHAPTER_TITLES配置的正则，增加指定章节的元素块
        """
        result = defaultdict(list)
        for syllabus in self.pdfinsight_syllabus.syllabuses:
            if syllabus["children"] or syllabus["index"] in checked_syll_indices:
                continue
            # http://************:55647/#/project/remark/266163?treeId=13884&fileId=70447&schemaId=15&projectId=17&schemaKey=B1
            real_syllabus = find_real_syllabus(self.pdfinsight, syllabus)
            title = real_syllabus["title"]
            if self.P_SKIP_SYLLABUS_TITLE.search(title) or not P_KW_CHAPTER_TITLES.search(title):
                continue
            self.merge_syllabus_result(
                result,
                self.get_elements_by_syllabus(
                    real_syllabus, score_map, exclude_elt_indices, checked_syll_indices, ipo_elements
                ),
            )
        return result

    def group_elements_by_syllabus(
        self, elements: list[dict]
    ) -> tuple[dict[int, dict], dict[tuple[str, int], list[dict]]]:
        group_by_syllabus, ipo_elements = defaultdict(list), {}
        all_indices, score_map = set(), {}
        for element in elements:
            score_map[element["index"]] = element["score"]
        checked_chapters = set()
        for element in sorted(elements, key=itemgetter("index")):
            if self.pdfinsight.is_syllabus_title(element) or element["index"] in ipo_elements:
                continue
            if not (elt_syllabuses := self.pdfinsight.find_syllabuses_by_index(element["index"])):
                continue
            # 根据直系父章节给元素块分组
            root_title = self.pdfinsight.page_chapter_from_first_para.get(element["page"]) or elt_syllabuses[0]["title"]
            # 过滤非法章节
            if any(self.P_SKIP_SYLLABUS_TITLE.search(s["title"]) for s in elt_syllabuses):
                continue
            # 过滤notes章节下非share capital章节的元素块
            if P_NOTE_CHAPTER.search(root_title) and not any(
                self.P_SHARE_CAPITAL.search(c["title"]) for c in elt_syllabuses
            ):
                continue
            all_indices.add(element["index"])
            nearest_syllabus = elt_syllabuses[-1]
            self.merge_syllabus_result(
                group_by_syllabus,
                self.get_elements_by_syllabus(
                    nearest_syllabus, score_map, all_indices, checked_chapters, ipo_elements, root_title
                ),
            )
            # listing只在指定章节下找
            # http://************:55647/#/project/remark/341442?treeId=14845&fileId=104206&schemaId=15&projectId=17&schemaKey=B8 index=428 不是ipo
            if (
                is_para_elt(element)
                and self.is_ipo_element(element)
                and any(P_KW_CHAPTER_TITLES.search(c["title"]) for c in elt_syllabuses)
            ):
                ipo_elements[element["index"]] = element
                continue
            group_by_syllabus[(root_title, nearest_syllabus["index"])].append(element)
        kw_elements = self.get_kw_chapter_elements(score_map, all_indices, checked_chapters, ipo_elements)
        self.merge_syllabus_result(group_by_syllabus, kw_elements)
        return ipo_elements, group_by_syllabus

    def is_ipo_element(self, element):
        pre_element_is_ipo = False
        _, pre_element = self.pdfinsight.find_element_by_index(element["index"] - 1)
        if pre_element and self.P_IPO_ELEMENTS.search(clean_txt(pre_element.get("text", ""))):
            pre_element_is_ipo = True
        return (
            self.P_IPO_ELEMENTS.search(clean_txt(element["text"]))
            or self.P_SHARE_OFFER.search(clean_txt(element["text"]))
            and pre_element_is_ipo
        )

    def sort_root_groups(self, groups_item):
        title, chapter_elements = groups_item
        entered_order, syll_order = 10, 10
        # 1. 提取到的当前FY范围内的entered date越多，排序越靠前
        entered_dates, syll_indices = [k[0] for k in chapter_elements], [k[1] for k in chapter_elements]
        entered_order = -len({ed for ed in entered_dates if ed and self.year_start <= ed <= self.year_end})
        # 2. 子章节连续的分组，排序靠前
        if (count_syllabus := len(syll_indices)) > 1:
            syll_order = 1 if max(syll_indices) - min(syll_indices) + 1 == count_syllabus else 2
        # 3. root章节优先级：MDA>DR>OTHER>NOTE
        if P_MDA_CHAPTER.search(title):
            return 1, entered_order, syll_order, 1
        if P_DR_CHAPTER.search(title):
            return 1, entered_order, syll_order, 2
        if not P_NOTE_CHAPTER.search(title):
            return 1, entered_order, syll_order, 3
        # 4. note章节必须在最后
        return 2, entered_order, syll_order, 4

    def get_entered_dates_of_table(
        self, element: dict, chapter_is_a_group: bool, syllabus_title: str, title_aliases: set[str]
    ) -> set[int]:
        """ """
        all_indices = set()
        continuous_tables = self.get_texts_of_continuous_tables(element)
        tbl_title_aliases, title_dates = [], []
        # 提前计算表名中的日期及order信息
        min_index = min(continuous_tables)
        if (table_title := find_table_title(self.pdfinsight, min_index)) and not chapter_is_a_group:
            # 表名优先取最后一句，最后一句匹配不到内容，再取整段
            # http://************:55647/#/project/remark/294922?treeId=14268&fileId=70841&schemaId=18&projectId=14268&schemaKey=C1.1.1 元素块229
            for title in (list(split_paragraph(table_title))[-1], table_title):
                tbl_title_aliases = self.extract_event_aliases(title)
                title_dates = self.extract_entered_dates(
                    title, "" if title else syllabus_title, aliases=tbl_title_aliases or title_aliases
                )
                if tbl_title_aliases or title_dates:
                    table_title = title
                    break
        for tbl_idx, tbl_row_texts in continuous_tables.items():
            if not table_title:
                # 如果没取到表名，一定要用连续表格的第一个表格的表名
                table_title = self.table_elements[min_index].get("title")
            # 全局存储表名
            self.table_titles[tbl_idx] = table_title
            all_indices.add(tbl_idx)
            if chapter_is_a_group:
                continue
            for text in tbl_row_texts:
                if aliases := self.extract_event_aliases(text):
                    self.aliases_of_element[tbl_idx].update(aliases)
                for tbl_date in self.extract_entered_dates(
                    text, syllabus_title, tbl_idx=tbl_idx, tbl_title=table_title, aliases=aliases
                ):
                    self.entered_dates_of_element[tbl_idx].add(tbl_date)
            if not self.aliases_of_element[tbl_idx] and title_aliases:
                self.aliases_of_element[tbl_idx].update(title_aliases)
            if not self.entered_dates_of_element[tbl_idx]:
                if title_dates:
                    for tbl_date in title_dates:
                        self.entered_dates_of_element[tbl_idx].add(tbl_date)
                else:
                    # 任一续表提取到日期，其他续表都不能再使用标题日期
                    title_dates = []
        return all_indices

    def _group_paragraphs_by_entered_date(
        self,
        para_entered: list[tuple[str, int]],
        syllabus: dict,
        existed_indices: set[int],
        dates_of_para_element: dict[int, set[str]],
        grouped_elements: list[dict],
    ):
        """
        根据提取到的entered date，对elements进行分组，entered相同的元素块归类到一组
        """
        entered_elements = defaultdict(list)
        para_indices = {idx for _, idx in para_entered}
        count_indices = len(para_indices)
        element_dict = {e["index"]: e for e in grouped_elements}
        # 1. 多个发行日在同一个段落，或结果中包含了多事件表格，或结果中包含了所有段落，或者所有段落都包含序号，说明只对段落生效
        # http://************:55647/#/project/remark/230867?treeId=4807&fileId=60976&schemaId=15&projectId=4807&schemaKey=B 元素块3410
        # http://************:55647/#/project/remark/266088?treeId=5595&fileId=70432&schemaId=15&projectId=5595&schemaKey=B9 元素块133
        # http://************:55647/#/project/remark/268596?treeId=8317&fileId=70562&schemaId=18&projectId=8317&schemaKey=C1.3
        if (
            count_indices == 1
            or {e["index"] for e in grouped_elements} == para_indices
            or real_all([P_CHAPTER_PREFIX.search(element_dict[idx]["text"]) for idx in para_indices])
            or any(self.table_row_patterns[e["index"]] for e in grouped_elements)
        ):
            for enter_date, idx in para_entered:
                self.entered_dates_of_element.update(dates_of_para_element)
                entered_elements[enter_date].extend([e for e in grouped_elements if e["index"] == idx])
            return entered_elements
        # 2. 每个段落都有相同的发行日，说明整章为一个分组
        # http://************:55647/#/project/remark/242823?treeId=17250&fileId=68094&schemaId=15&projectId=17250&schemaKey=B3 章节元素块=269
        if count_indices > 1:
            count_dates = Counter([d for d, _ in para_entered])
            if enter_dates := {dt for dt, cnt in count_dates.items() if cnt == count_indices}:
                # 到这里，说明dates_of_para_element可能提取的是错误的，丢弃
                for idx in existed_indices:
                    self.entered_dates_of_element[idx].update(enter_dates)
                for enter_date in enter_dates:
                    entered_elements[enter_date].extend(grouped_elements)
                return entered_elements
        # 更新entered_dates_of_element
        self.entered_dates_of_element.update(dates_of_para_element)
        # 3. 章节下多个发行日，需要根据段落位置给整章分组，同时考虑：前面N个段落描述发行日，最后一个大表格描述多个发行事件
        len_para_entered = len(para_entered)
        for i, (entered_date, elt_idx) in enumerate(para_entered):
            # 期后记录不需要提取
            if entered_date and entered_date > self.year_end:
                continue
            end_idx = syllabus["range"][-1] if i == len_para_entered - 1 else para_entered[i + 1][1]
            if end_idx == elt_idx:
                end_idx += 1
            elements = [e for e in grouped_elements if is_in_range(e["index"], [elt_idx, end_idx])]
            if not elements:
                continue
            entered_elements[entered_date].extend(sorted(elements, key=itemgetter("index")))
        return entered_elements

    def _group_tables_by_entered_date(self, tbl_entered: dict[str, set[int]], element_dict: dict[int, dict]):
        entered_elements = defaultdict(list)
        # 给表格按照entered date做归属分组
        for entered_date, tbl_indices in tbl_entered.items():
            # entered_dates.add(entered_date)
            tbl_elements = []
            score = 0
            for idx in sorted(tbl_indices):
                if elt := element_dict.get(idx):
                    tbl_elements.append(elt)
                    score = elt["score"]
                else:
                    _, elt = self.pdfinsight.find_element_by_index(idx)
                    new_elt = copy(elt)
                    new_elt["score"] = score
                    tbl_elements.append(new_elt)
            entered_elements[entered_date].extend(tbl_elements)
        return entered_elements

    def set_pattern_for_table(self):
        """
        根据self.alias_of_event，给对应表格设置别名对应的pattern
        http://************:55647/#/project/remark/269107?treeId=12401&fileId=70635&schemaId=18&projectId=12401&schemaKey=C1.1.1
        # TODO 同一个event可以有多个别名，但同一个别名，不能属于多个event
        """
        event_of_alias, event_patterns = {}, {}
        for event_id, aliases in self.alias_of_event.items():
            event_patterns[event_id] = self.gen_pattern_by_alias(aliases)
            for alias in aliases:
                event_of_alias[alias] = event_id
        for idx, aliases in self.aliases_of_element.items():
            if idx not in self.table_titles or idx in self.table_row_patterns:
                continue
            for alias in aliases:
                if alias not in event_of_alias or alias not in event_patterns:
                    continue
                event_id = event_of_alias[alias]
                self._add_tbl_row_pattern(idx, event_id, event_patterns[event_id])

    def group_candidates(self, elements: list[dict]) -> tuple[list[dict[tuple, list]], list[dict], set[str]]:
        """
        根据章节名称、entered into等信息，给候选元素块分组
        TODO 单元测试
        """

        def set_alias_of_event(enter_dates_, aliases_) -> set[str] | None:
            if len(aliases_) != 1:
                return enter_dates_
            the_alias = get_first_key(aliases_)
            for the_date in enter_dates_:
                # 同一个别名不能分给不同的event
                if the_date not in event_id_map:
                    continue
                event_id = event_id_map[the_date]
                if the_alias in self.alias_of_event[event_id]:
                    return {the_date}
                if any(the_alias in value for value in self.alias_of_event.values()):
                    continue
                self.alias_of_event[event_id].add(the_alias)
                return {the_date}
            return None

        ipo_elements, group_by_syllabus = self.group_elements_by_syllabus(elements)
        groups = defaultdict(dict)
        element_dict = {e["index"]: e for e in elements}
        event_id_map = {a.first_report_date: a.event_id for a in self.addition_data}
        for (root_title, syll_idx), grouped_elements in sorted(group_by_syllabus.items(), key=lambda x: x[0]):
            syllabus = find_real_syllabus(self.pdfinsight, self.pdfinsight_syllabus.syllabus_dict[syll_idx])
            syllabus_title = syllabus["title"]
            # 目录中带有placing 1等信息
            if title_aliases := self.extract_event_aliases(syllabus_title, is_title=True):
                self.aliases_of_chapter[syll_idx] = title_aliases
            # http://************:55647/#/project/remark/266499?treeId=8859&fileId=70514&schemaId=18&projectId=8859&schemaKey=C1.2.1
            title_dates = self.extract_entered_dates(syllabus_title, syllabus_title)
            chapter_is_a_group = self.is_one_group_chapter(syllabus_title, title_aliases, title_dates)
            set_alias_of_event(title_dates, title_aliases)
            # 记录所有带了entered into或者placing 1/2的元素块
            para_entered = []
            existed_indices, all_tbl_indices = set(ipo_elements), set()
            first_entered_date = get_first_key(title_dates)
            dates_of_para_element = defaultdict(set)
            for element in sorted(grouped_elements, key=itemgetter("index")):
                elt_index = element["index"]
                if elt_index in existed_indices or self.pdfinsight.is_skip_element(element):
                    continue
                existed_indices.add(elt_index)
                if chapter_is_a_group:
                    # 这种场景，一个章节就是一个分组
                    self.aliases_of_element[elt_index].update(title_aliases)
                    if first_entered_date:
                        continue
                if is_table_elt(element):
                    tbl_indices = self.get_entered_dates_of_table(
                        element, chapter_is_a_group, syllabus_title, title_aliases
                    )
                    all_tbl_indices.update(tbl_indices)
                    existed_indices.update(tbl_indices)
                    continue
                if is_para_elt(element):
                    text = clean_txt(element["text"], remove_cn_text=True)
                    aliases = self.extract_event_aliases(text)
                    if not chapter_is_a_group and aliases:
                        self.aliases_of_element[elt_index].update(aliases)
                    if dates_ := self.extract_entered_dates(text, syllabus_title, aliases=aliases or title_aliases):
                        if chapter_is_a_group:
                            # 从章节标题能判读出是一个分组时，排除已经有对应的分组，再取最小日期 TODO 根据日期+年份更合理
                            if not first_entered_date:
                                first_entered_date = min(set_alias_of_event(dates_, aliases) or dates_)
                            continue
                        dates_of_para_element[elt_index].update(dates_)
                        para_entered.extend((dt, elt_index) for dt in dates_)
                        set_alias_of_event(dates_, aliases)
            # 章节为一个分组，且提取到发行日，给章节下每个元素块都标记发行日
            if chapter_is_a_group and first_entered_date:
                for idx in existed_indices:
                    self.entered_dates_of_element[idx].add(first_entered_date)
            # 针对所有表格，组合tbl_entered
            tbl_entered = defaultdict(set)
            for index, dates in self.entered_dates_of_element.items():
                if not (dates and index in all_tbl_indices):
                    continue
                for dt in dates:
                    tbl_entered[dt].add(index)
            # 1. 章节标题上已经带了序号，直接取整章
            if chapter_is_a_group:
                groups[root_title][(first_entered_date, syll_idx)] = grouped_elements
                continue
            # 2. 没提取到发行日
            if not para_entered and not tbl_entered:
                groups[root_title][(None, syll_idx)] = grouped_elements
                continue
            # 3. 整个章节只有一个发行日，说明整章为一个分组
            if len(para_entered) == 1 or (not para_entered and len(tbl_entered) == 1):
                enter_date = para_entered[0][0] if para_entered else get_first_key(tbl_entered)
                groups[root_title][(enter_date, syll_idx)] = grouped_elements
                continue
            # 4. 只有一个段落有发行日，则章节下其他元素块都按此发行日
            # http://************:55647/#/project/remark/251308?treeId=16467&fileId=68674&schemaId=15&projectId=16467&schemaKey=B9 元素块161
            grouped_tbl_indices = set(tbl_entered)
            if len({idx for _, idx in para_entered}) == 1:
                for date_, _ in para_entered:
                    for idx_ in existed_indices - grouped_tbl_indices:
                        self.entered_dates_of_element[idx_].add(date_)
                    groups[root_title][(date_, syll_idx)] = grouped_elements
                continue
            # 5-7: 按段落计算
            entered_elements = self._group_paragraphs_by_entered_date(
                para_entered, syllabus, existed_indices, dates_of_para_element, grouped_elements
            )
            existed_indices.update(e["index"] for elems in entered_elements.values() for e in elems)
            # 8. 表格分组
            for dt, tbl_elements in self._group_tables_by_entered_date(tbl_entered, element_dict).items():
                entered_elements[dt].extend(tbl_elements)
            unknown_tbl_elements = [e for e in grouped_elements if e["index"] in all_tbl_indices - grouped_tbl_indices]
            p_type_dict = {r.first_report_date: self.regex_issue_type(r) for r in self.addition_data}
            for ent_date, ent_elements in entered_elements.items():
                if unknown_tbl_elements and ent_date and (p_type := p_type_dict.get(ent_date)):
                    # 没有分类的表格按类型匹配所属分组
                    ent_elements.extend(
                        e
                        for e in unknown_tbl_elements
                        if p_type.search(
                            self.table_titles.get(e["index"]) or find_table_title(self.pdfinsight, e["index"])
                        )
                    )
                groups[root_title][(ent_date, syll_idx)] = ent_elements

        # 根据提取到的发行日+别名，设置event_id和别名的关系
        self.set_pattern_for_table()
        # 如果只有notes章节能提取到日期，则优先notes章节，否则优先其他章节
        only_has_date_titles = [title for title, item_dict in groups.items() if any(i[0] for i in item_dict)]
        if len(only_has_date_titles) == 1 and P_NOTE_CHAPTER.search(only_has_date_titles[0]):
            grouped_elements = [groups.pop(only_has_date_titles[0])]
        else:
            grouped_elements = []
        fy_entered_dates = set()
        # 按照MDA、DR、其他章节的顺序排序
        for _, chapter_elements in sorted(groups.items(), key=self.sort_root_groups):
            grouped_elements.append(chapter_elements)
            tmp_entered_dates = set()
            for (enter_date, _), elements_ in sorted(chapter_elements.items(), key=lambda x: not bool(x[0][0])):
                # 这里只需要当年以前的日期： https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6412#note_675854
                if enter_date and not fy_entered_dates and enter_date < self.year_start:
                    tmp_entered_dates.add(enter_date)
                self.all_candidate_elements.extend(elements_)
            # 取当年发行日期最多，最具连续性的一组entered dates最为最终发行日
            if not fy_entered_dates and tmp_entered_dates:
                fy_entered_dates = tmp_entered_dates
        return grouped_elements, list(ipo_elements.values()), fy_entered_dates

    def get_texts_of_continuous_tables(self, element, ignore_header=True) -> dict[int, list[str]]:
        """
        TODO 考虑脚注
        """
        texts_dict = {}
        for elt in self.pdfinsight.continuous_tables(element):
            # 记录所有的table元素
            self.table_elements[elt["index"]] = elt
            group_by_row = defaultdict(list)
            for key, cell in sorted(elt["cells"].items(), key=lambda x: [int(i) for i in x[0].split("_")]):
                if cell_text := clean_txt(cell["text"], remove_cn_text=True):
                    row, _ = key.split("_")
                    if ignore_header and row == "0":
                        continue
                    group_by_row[row].append(cell_text)
            texts_dict[elt["index"]] = ["\t".join(row_texts) for row_texts in group_by_row.values()]
        return texts_dict

    def gen_tbl_candidates(self, tbl_indices: set[int], score: float, tmp_score: float = None) -> list[dict]:
        tbl_elements = []
        for idx in tbl_indices:
            _, elt = self.pdfinsight.find_element_by_index(idx)
            new_elt = copy(elt)
            new_elt["score"] = score
            if tmp_score:
                new_elt["tmp_score"] = tmp_score
            tbl_elements.append(new_elt)
        return tbl_elements

    def filter_elements_by_addition_data(
        self,
        grouped_candidates: list[dict[tuple, list]],
        addition_row: AdditionData,
        order_or_alias: int | str,
        total_count: int,
    ) -> tuple[list[dict], set[int]]:
        """
        根据B2+issue_method列过滤初步定位元素块
        """
        p_issue_type = self.regex_issue_type(addition_row)
        result_elements = []
        known_syll_indices = set()
        count_title_type = 0
        for chapter_elements in grouped_candidates:
            group_elements, continued_tbl_indices = [], set()
            count, last_entered = 0, None
            # 这里的排序规则： 有entered date的章节优先
            for (entered_date, syll_idx), elements in sorted(chapter_elements.items(), key=lambda x: not bool(x[0][0])):
                if (
                    count > 0
                    and (last_entered is None and entered_date is not None)
                    or (last_entered is not None and entered_date is None)
                ):
                    # 重置count
                    count = 0
                syll_title = self.syllabus_titles[syll_idx]
                # 只有一个分组时，确保该组不是特殊分组
                matched = p_issue_type.search(syll_title) or self.can_ignore_event_type
                last_entered = entered_date
                if entered_date:
                    if entered_date == addition_row.first_report_date:
                        if not matched:
                            elements = [e for e in elements if is_table_elt(e) or p_issue_type.search(e["text"])]
                        if elements:
                            count += 1
                            # 记录匹中元素块所在章节
                            known_syll_indices.add(syll_idx)
                            group_elements.extend(elements)
                    continue
                if matched:
                    if total_count == 1:
                        # 记录匹中元素块所在章节
                        known_syll_indices.add(syll_idx)
                        group_elements.extend(elements)
                        count += 1
                        continue
                    if P_DATE.search(syll_title) or self.P_YEAR.search(syll_title):
                        # title中包含日期，章节序号+1
                        count_title_type += 1
                        if count_title_type == order_or_alias:
                            # 记录匹中元素块所在章节
                            known_syll_indices.add(syll_idx)
                            group_elements.extend(elements)
                            count += 1
                            continue
                    if title_orders := self.aliases_of_chapter.get(syll_idx):
                        if order_or_alias in title_orders:
                            # 记录匹中元素块所在章节
                            known_syll_indices.add(syll_idx)
                            group_elements.extend(elements)
                        count += 1
                        continue
                elif paras := [e for e in elements if is_para_elt(e)]:
                    matched = bool(p_issue_type.search(clean_txt(paras[0]["text"], remove_cn_text=True)))
                if matched:
                    count += 1
                    elt_aliases = self.aliases_of_element[elements[0]["index"]]
                    if (
                        total_count == 1
                        or order_or_alias in elt_aliases
                        or addition_row.first_report_date == entered_date
                        or (not elt_aliases and count == order_or_alias)
                    ):
                        # 记录匹中元素块所在章节
                        known_syll_indices.add(syll_idx)
                        group_elements.extend(elements)
                        continue
                for element in elements:
                    elt_aliases = self.aliases_of_element[element["index"]]
                    if order_or_alias in elt_aliases or self.alias_of_event[addition_row.event_id] & elt_aliases:
                        if element["index"] not in continued_tbl_indices:
                            # 记录匹中元素块所在章节
                            known_syll_indices.add(syll_idx)
                            group_elements.append(element)
                        continue
                    tbl_indices = set()
                    matched_title = False
                    if is_table_elt(element):
                        if element["index"] in continued_tbl_indices:
                            continue
                        elt_index = element["index"]
                        table_title = self.table_titles.get(elt_index) or find_table_title(self.pdfinsight, elt_index)
                        if table_title and elt_index not in self.table_titles:
                            self.table_titles[elt_index] = table_title
                        texts = {table_title}
                        matched_title = p_issue_type.search(table_title)
                        for idx, row_texts in self.get_texts_of_continuous_tables(element).items():
                            if idx in continued_tbl_indices:
                                continue
                            continued_tbl_indices.add(idx)
                            if matched_title or any(p_issue_type.search(rt) for rt in row_texts):
                                tbl_indices.add(idx)
                                texts.update(row_texts)
                        if not texts:
                            continue
                    else:
                        texts = [clean_txt(element.get("text") or "", remove_cn_text=True)]
                    if not (matched_title or any(p_issue_type.search(text) for text in texts)):
                        continue
                    if total_count == 1:
                        # 记录匹中元素块所在章节
                        known_syll_indices.add(syll_idx)
                        group_elements.append(element)
                        group_elements.extend(self.gen_tbl_candidates(tbl_indices, element["score"]))
                        continue
                    found = False
                    p_first_date, p_b3 = gen_regex_by_date(addition_row.first_report_date), self.regex_b3(addition_row)
                    for text in texts:
                        if elt_aliases := self.extract_event_aliases(text):
                            if order_or_alias in elt_aliases:
                                found = True
                                break
                            continue
                        if any(p_first_date.search(text) or p_b3.search(text) for text in texts):
                            found = True
                            break
                    if found:
                        # 记录匹中元素块所在章节
                        known_syll_indices.add(syll_idx)
                        group_elements.append(element)
                        group_elements.extend(self.gen_tbl_candidates(tbl_indices, element["score"]))
            # 按照分数降序排序
            result_elements.extend(sorted(group_elements, key=itemgetter("score"), reverse=True))
        return result_elements, known_syll_indices

    @classmethod
    @lru_cache
    def has_and_in_text(cls, text):
        return cls.P_AND.search(text)

    def is_one_group_chapter(self, syllabus_title: str, title_aliases: set[str], title_dates: set[str]) -> bool:
        if len(title_aliases) == 1:
            return True
        if title_aliases or self.has_and_in_text(syllabus_title):
            return False
        # 通过章节名称可判断出唯一分组
        # http://************:55647/#/project/remark/242823?treeId=17250&fileId=68094&schemaId=15&projectId=17&schemaKey=B6 index=288
        if len(title_dates) == 1:
            return True
        return P_DATE.search(syllabus_title) or self.P_YEAR.search(syllabus_title)

    @classmethod
    @lru_cache
    def extract_event_aliases(cls, text, is_title=False) -> set[str]:
        aliases = set()
        for p_alias in cls.P_EVENT_ALIAS:
            for matched in p_alias.finditer(text):
                aliases.add(f"{matched.group('name')} {matched.group('num')}".lower())
        # 标题中有and却只提取到一个序号，表示不是单一章节，order必须从元素块获取
        # http://************:55647/#/project/remark/251272?treeId=5487&fileId=68668&schemaId=15&projectId=5487&schemaKey=B9 元素块639
        if is_title and len(aliases) == 1 and cls.has_and_in_text(text):
            return set()
        return aliases

    @staticmethod
    def set_model_conf(model, row_pattern: SearchPatternLike = None):
        """
        动态配置B1B10ND的属性`group_pattern`或SpecialCells的属性 `row_pattern`/`title_patterns`
        注意：使用完后，必须调用该函数，将配置复位为None
        """
        if model.name == "special_cells":
            if not (model_id := model.config.get("model_id")):
                return
            if model_id == DYNAMIC_MODEL_ID:
                if row_pattern:
                    model.config["row_pattern"] = row_pattern
                else:
                    model.config["row_pattern"] = None
        if model.name == "multi_models":
            for model_config in model.config["models"]:
                if model_config["name"] != "special_cells" or not (model_id := model_config.get("model_id")):
                    continue
                if model_id == DYNAMIC_MODEL_ID:
                    if row_pattern:
                        model_config["row_pattern"] = row_pattern
                    else:
                        model_config.pop("row_pattern", None)

    def _nd_answer(self, schema, meta, elt_result: list = None):
        return self.create_result(elt_result or [], schema=schema, value=self.group_default_enum, meta=meta)

    def _all_are_nd_answer(self):
        meta = {
            "event_id": self.DEFAULT_EVENT_ID_MAP[DEFAULT_FUNDRAISING_EVENT_TYPE],
            "group_name": DEFAULT_FUNDRAISING_EVENT_TYPE,
        }
        answer_results = []

        for column in self.column_configs:
            schema = self.predictor.schema.children_schema(column)
            # 因为在配置文件中指定了fake_leaf 这里需要显式修改predictor的column
            self.predictor.columns = [column]
            answer_results.append(self._nd_answer(schema, meta))
        answer_result = PredictorResultGroup([answer_results], schema=self.predictor.schema)
        return [{self.schema.name: [answer_result]}]

    @cached_property
    def ref_rules(self):
        if self.is_c_rule:
            return self.C_RULES
        return [r for r in FUNDRAISING_COLS if r not in self.C_RULES]

    @property
    def is_c_rule(self):
        return self.schema_key in self.C_RULES

    @staticmethod
    def is_disclosure_date(first_report_date: str, entered_dates: set[str]) -> bool:
        """
        判断market eye中的日期是否在文档中被披露
        """
        return first_report_date in entered_dates

    def _find_exact_orders(self) -> set[str]:
        """
        根据提取到的文章中的placing 1等序号，及所在元素块/章节对应的发行日，来匹配对应的addition_row
        """
        found_types = set()
        for addition_row in self.addition_rows:
            for idx, dates in self.entered_dates_of_element.items():
                # 跳过表格元素块提取的数据，可能会有多个
                if (
                    idx in self.table_elements
                    or idx not in self.aliases_of_element
                    or addition_row.event_id in self.alias_of_event
                    or addition_row.first_report_date not in dates
                    or idx not in self.aliases_of_element
                ):
                    continue
                if len(aliases := self.aliases_of_element[idx]) > 1:
                    continue
                self.alias_of_event[addition_row.event_id].add(get_first_key(aliases))
                found_types.add(addition_row.type_key)
        return found_types

    def get_alias_or_order_of_events(self, count_types: dict) -> dict[str, tuple[str | int, int]]:
        """
        提取每个事件的别名或顺序，规则：
        1. 若文章中有 placing 2022/first placing等关键词，提取该关键词
        2. 若未提取到任意关键词，按照事件的顺序，从1开始给定序号
        """
        result_dict = {}
        order_dict = defaultdict(int)
        found_types = self._find_exact_orders()
        for row in sorted(self.addition_rows, key=lambda x: (x.first_report_date, x.event_id)):
            total = count_types[row.type_key]
            # 在文章中找到对应的order，则先给准确order，否则再自动排序
            if row.type_key in found_types:
                # 注意这里可能为None
                aliases = self.alias_of_event[row.event_id]
                if not aliases:
                    alias = 1 if total == 1 else None
                else:
                    alias = get_first_key(aliases)
            else:
                order_dict[row.type_key] += 1
                alias = order_dict[row.type_key]
            result_dict[row.event_id] = (alias, total)
        return result_dict

    def calc_event_type_orders(self, fy_entered_dates: set[str] = ()) -> dict[str, tuple[str | int, int]]:
        """
        统计每种event type在当年内的排序及当年内的总个数
        优先用元素块对应的发行日匹配
        """
        count_types_dict = defaultdict(int)
        for addition_row in self.addition_data:
            if addition_row.first_report_date > self.year_end:
                continue
            # C1.1.1, C1.2.1, C1.3 完成日期必须在当前财年期初之前
            is_skip = self.is_c_rule and addition_row.last_report_date >= self.year_start
            if self.is_disclosure_date(addition_row.first_report_date, fy_entered_dates):
                # 文章中出现的发行日对应的数据都要
                count_types_dict[addition_row.type_key] += 1
                if is_skip:
                    continue
                if addition_row not in self.addition_rows:
                    self.addition_rows.append(addition_row)
                continue
            if addition_row.is_completed_in_the_year(self.year_start, self.year_end):
                count_types_dict[addition_row.type_key] += 1
                if is_skip:
                    continue
                if addition_row not in self.addition_rows:
                    self.addition_rows.append(addition_row)
        return self.get_alias_or_order_of_events(count_types_dict)

    def ipo_groups(self, elements: list[dict], crude_elements: list[dict]):
        """
        proceeds from the listing/share offer/global offer章节下，如果有募集资金相关表格，直接提取
        https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/5331#note_618255
        http://************:55647/#/project/remark/230375?treeId=37994&fileId=61209&schemaId=15&projectId=37994&schemaKey=B8
        http://************:55647/#/project/remark/231019?treeId=15315&fileId=60902&schemaId=15&projectId=15315&schemaKey=B8
        """
        if not elements:
            return []
        candidate_elements = []
        checked_date = False
        ipo_date = HKEXCompany.get_listing_date_by_stock(self.stock_code)
        for element in elements:
            if self.pdfinsight.is_skip_element(
                element, aim_types={"PARAGRAPH", "TABLE"}
            ) or self.pdfinsight.is_syllabus_title(element):
                continue
            if not checked_date and not ipo_date and is_para_elt(element):
                # 仅用第一个段落里的日期来判断上市日期，后续日期都不准
                checked_date = True
                if ipo_date := extract_date(self.P_LISTING_DATE, clean_txt(element["text"], remove_cn_text=True)):
                    ipo_date = ipo_date.timestamp()
            candidate_elements.append(element)
        if self.year_end:
            year_start = datetime.strptime(self.year_start, "%Y-%m-%d").timestamp()
            if ipo_date:
                if ipo_date > datetime.strptime(self.year_end, "%Y-%m-%d").timestamp():
                    # 若IPO发行日期在当年期末之后，不提取IPO分组
                    return []
                if self.is_c_rule:
                    # C1.1.1/C1.2.1/C1.3需要往年完成的IPO
                    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/5609#note_626422
                    if ipo_date > year_start:
                        return []
                elif ipo_date < year_start:
                    # B1-B10需要当年完成的IPO
                    return []
        # B1-B10没有外部数据，只有IPO时，所有候选元素块都归类为IPO
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6414#note_674292
        # http://************:55647/#/project/remark/263863?treeId=45534&fileId=69987&schemaId=15&projectId=17&schemaKey=B3
        if crude_elements:
            candidate_elements.extend(crude_elements)
        return [(self.DEFAULT_IPO_EVENT_TYPE, candidate_elements)]

    # def sort_group_func(self, row_and_candidates: tuple[AdditionData, list[dict]]):
    #     """
    #     B8-B10 预测结果排序规则：
    #     1. 本年发行完成的要按照升序排（为了和B1-B7对应）
    #     2. 非本年发行完成的按照发行日期 + event_id降序排
    #     event_id常见格式： frs000015212, des000005714
    #     """
    #     addition_row, elements = row_and_candidates
    #     if addition_row.is_issued_in_the_year(self.year_start, self.year_end):
    #         return (
    #             0,
    #             min({e["index"] for e in elements}, default=to_timestamp(addition_row.first_report_date)),
    #             addition_row.event_id,
    #         )
    #     return 1, -to_timestamp(addition_row.first_report_date), addition_row.event_id

    def get_candidate_elements(self, schema_name: str, column: str) -> list[dict]:
        """
        根据子项重新获取初步定位元素块
        """
        crude_answer_path = [schema_name, column]
        candidates = get_element_candidates(
            self.predictor.prophet.crude_answer,
            crude_answer_path,
            priors=self.config.get("element_candidate_priors", []),
            limit=self.config.get("element_candidate_count", 20),
        )
        candidate_elements = []
        location_threshold = self.config.get("location_threshold") or 0
        for item in candidates:
            index, score = item["element_index"], item["score"]
            _, element = self.pdfinsight.find_element_by_index(index)
            if not element or item["score"] < location_threshold:
                continue
            new_elem = copy(element)
            new_elem["score"] = score
            candidate_elements.append(new_elem)
        return candidate_elements

    @cached_property
    def can_ignore_event_type(self):
        # 多组外部数据时，不能忽略类型
        if len(self.addition_data) != 1:
            return False
        # 一组外部数据时，类型为preference share/convertible/warrants不能忽略类型
        if P_NON_SHARES.search(self.addition_data[0].event_type):
            return False
        return True

    def only_one_group(self, fy_entered_dates: set[str]):
        """
        B1-B10有且仅有一个分组的条件： 仅有1个发行事件，且在当年内完成发行
        C1.1.1/C1.2.1/C1.3有且仅有一个分组的条件： 仅有1个发行事件, 且在当年期初之前完成发行
        """
        if not self.can_ignore_event_type:
            return False
        if self.is_c_rule:
            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6411#note_676058
            return self.addition_data[0].last_report_date < self.year_start and self.is_disclosure_date(
                self.addition_data[0].first_report_date, fy_entered_dates
            )
        return self.year_start <= self.addition_data[0].last_report_date <= self.year_end

    @classmethod
    def filter_group_candidates(cls, elements):
        return [e for e in elements if not (e.get("text") and cls.P_SKIP_ELEMENT.search(clean_txt(e["text"])))]

    def predict_schema_answer(self, elements: list[dict]):
        """
        注意： B8B10Group也复用
        """
        existed_indices = {e["index"] for e in elements}
        # 为确保分组统一： B1-B10合并所有候选元素块；C1.1.1/C1.2.1/C1.3合并所有候选元素块
        for schema in self.schema.parent.children:
            schema_name = schema.name
            # 跳过当前schema的候选元素块
            if schema_name not in FUNDRAISING_COLS or (schema_name == self.schema.name and len(schema.children) == 1):
                continue
            for column in schema.children:
                for element in self.get_candidate_elements(schema_name, column.name):
                    if element["index"] in existed_indices:
                        continue
                    existed_indices.add(element["index"])
                    elements.append(element)

        grouped_candidates, ipo_candidates, fy_entered_dates = self.group_candidates(elements)
        event_order_dict = self.calc_event_type_orders(fy_entered_dates)
        candidate_groups, existed_group_nums = [], {}
        known_indices, chapter_events = set(), defaultdict(set)
        for addition_row in self.addition_rows:
            if not self.is_c_rule and not addition_row.is_completed_in_the_year(self.year_start, self.year_end):
                # B1-B10 仅需要当年完成的分组
                continue
            # 过滤以下分组数据(https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6240#note_664282)：
            # 1. Event Type='Issue of Debt Securities'
            # 2. Method of Issue= "subscription under transaction"或Reasons = "satisfy the consideration"
            # 3. Reasons满足P_IGNORE_B1_REASONS正则
            if (
                addition_row.issue_type == "subscription under transaction"
                or addition_row.b1.strip().lower() in AdditionData.IGNORE_REASONS
                or any(P_IGNORE_B1_REASONS.search(sen) for sen in split_paragraph(addition_row.b1))
            ):
                continue
            # 查询各个event type下已经存在的人工event type分组序号
            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/5598
            if addition_row.b2 not in existed_group_nums:
                existed_group_nums[addition_row.b2] = ManualGroupAnswer.get_group_nums(
                    self.question_id, addition_row.b2, self.ref_rules
                )
            # 根据addition_row找初步定位元素块
            candidate_elements, known_chapters = self.filter_elements_by_addition_data(
                grouped_candidates, addition_row, *event_order_dict[addition_row.event_id]
            )
            known_indices.update(e["index"] for e in candidate_elements)
            # 存章节和event的对应关系
            for syll_idx in known_chapters:
                chapter_events[syll_idx].add(addition_row.event_id)
            candidate_groups.append((addition_row, candidate_elements))
        # 如果构造的表格行正则，仅匹配到一条addition记录，则丢弃
        # http://************:55647/#/hkex/annual-report-checking/report-review/265008?fileId=70216&schemaId=15&rule=B1&delist=0 page=14
        if invalid_indices := {idx for idx, items in self.table_row_patterns.items() if len(items) <= 1}:
            for idx in invalid_indices:
                self.table_row_patterns.pop(idx)
        ipo_indices = {e["index"] for e in ipo_candidates}
        date_indices = {k for k, v in self.entered_dates_of_element.items() if v}
        exclude_ipo_elements = [e for e in self.all_candidate_elements if e["index"] not in ipo_indices | known_indices]
        more_event_elements = defaultdict(list)
        # 如果只有一个event, 直接给所有未识别到日期的元素块（识别到日期的元素块可能是当年的数据）
        # http://************:55647/#/project/remark/293717?treeId=7109&fileId=70670&schemaId=18&projectId=7109&schemaKey=C1.2.1
        if not candidate_groups and self.only_one_group(fy_entered_dates):
            addition_row = self.addition_data[0]
            event_order_dict[addition_row.event_id] = (1, 1)
            existed_group_nums[addition_row.b2] = ManualGroupAnswer.get_group_nums(
                self.question_id, addition_row.b2, self.ref_rules
            )
            candidate_groups.append((addition_row, exclude_ipo_elements))
        elif len(candidate_groups) == 1 and self.can_ignore_event_type:
            # 只有一个分组，添加所有候选元素块
            # http://************:55647/#/hkex/annual-report-checking/report-review/265268?fileId=70268&schemaId=15&rule=B6&delist=0
            candidate_groups[0][1].extend(exclude_ipo_elements)
        elif chapter_events:
            # 补充唯一event章节下的其他元素块
            for chapter_elements in grouped_candidates:
                for _, syll_idx in chapter_elements:
                    cur_syllabus = self.pdfinsight_syllabus.syllabus_dict[syll_idx]
                    syllabuses = self.pdfinsight.get_real_parent_syllabuses(cur_syllabus)
                    if not syllabuses:
                        continue
                    syllabus_range = syllabuses[0]["range"]
                    # 章节下识别到多个日期，则不是唯一分组章节
                    if len({idx for idx in self.entered_dates_of_element if is_in_range(idx, syllabus_range)}) > 1:
                        continue
                    event_ids = chapter_events[syll_idx]
                    # 如果已识别到的元素块所属章节仅属于同一个event，且章节不为share capital，则补充未识别到的元素块到已知分组
                    if (
                        len(event_ids) != 1
                        or self.has_and_in_text(self.syllabus_titles[syll_idx])
                        or any(self.P_SHARE_CAPITAL.search(s["title"]) for s in syllabuses)
                    ):
                        continue
                    more_event_elements[get_first_key(event_ids)] = [
                        e for e in exclude_ipo_elements if is_in_range(e["index"], syllabus_range)
                    ]

        # 只披露了IPO时，未知候选元素块都归类为IPO
        ipo_crude_elements = []
        if not candidate_groups and (
            self.is_c_rule and not fy_entered_dates or not self.is_c_rule and not date_indices
        ):
            ipo_crude_elements = [
                e
                for e in exclude_ipo_elements
                if e["index"] not in ipo_indices | date_indices
                and any(
                    P_IPO_KW_CHAPTERS.search(s["title"])
                    for s in self.pdfinsight.find_syllabuses_by_index(e["index"])[1:]
                )
            ]
        # 按照event_id排序，同时追加IPO分组
        sorted_groups = (
            candidate_groups if len(candidate_groups) <= 1 else sorted(candidate_groups, key=lambda x: x[0].event_id)
        ) + self.ipo_groups(ipo_candidates, ipo_crude_elements)
        if not sorted_groups:
            logger.warning("No addition candidates found!")
            return self._all_are_nd_answer()

        # 根据外部数据生成答案
        result_groups = []
        group_num_dict = defaultdict(int)
        for addition_row, candidate_elements in sorted_groups:
            if isinstance(addition_row, AdditionData):
                b2 = addition_row.b2
                group_num_dict[b2] += 1
                # 如果序号已经被人工创建的序号占用，则+1
                # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/5598 AI预测分组名称不能与人工分组名称重复
                if existed_nums := existed_group_nums.get(b2):
                    while group_num_dict[b2] in existed_nums:
                        group_num_dict[b2] += 1
                meta = {"event_id": addition_row.event_id, "group_name": f"{b2} {group_num_dict[b2]}"}
                # 补充候选元素块
                if more_event_elements:
                    candidate_elements.extend(more_event_elements[addition_row.event_id])
                group_answer = self.predict_group_answer(
                    self.filter_group_candidates(candidate_elements),
                    meta,
                    addition_row,
                    *event_order_dict[addition_row.event_id],
                )
            else:
                # 年报中有Proceeds from listing章节且披露了募集资金使用情况，则增加一个IPO分组
                # event_id构造为zzz-ipo是为了保证WEB分组排序在最后（正常event_id见addition_data表的event_id字段）
                meta = {"event_id": self.DEFAULT_EVENT_ID_MAP[addition_row], "group_name": addition_row}
                group_answer = self.predict_group_answer(self.filter_group_candidates(candidate_elements), meta)
            if group_answer:
                result_groups.append(group_answer)
        if not result_groups:
            return self._all_are_nd_answer()
        answer_result = PredictorResultGroup(result_groups, schema=self.predictor.schema)
        return [{self.schema.name: [answer_result]}]

    def special_nd_answer(self, elements, schema, meta):
        """
        如果存在特殊描述，则框选内容并判定为ND
        """
        for elt in elements:
            if not is_para_elt(elt):
                continue
            chars = elt["chars"]
            for sen, (start, end) in split_paragraph(clean_txt(elt["text"]), need_pos=True):
                if not P_B1_B10_MUST_ND.search(sen):
                    continue
                # 这种场景不应该影响合规
                meta["nd_c"] = True
                return self._nd_answer(schema, meta, [CharResult(elt, chars[start:end])])
        return None

    def predict_group_answer(
        self,
        elements: list[dict],
        meta: dict,
        addition_row: AdditionData = None,
        order_or_alias: int = None,
        total_count: int = None,
    ):
        answer_results = []
        for column, col_models in self.children_models.items():
            schema = self.predictor.schema.children_schema(column)
            if not elements:
                answer_results.append(self._nd_answer(schema, meta))
                continue
            if addition_row and not self.is_c_rule:
                # B1-B10仅限当年
                if not addition_row.is_completed_in_the_year(self.year_start, self.year_end):
                    continue
                # 存在特殊描述则当前分组下B1-B10全部ND
                if answer := self.special_nd_answer(elements, schema, meta):
                    answer_results.append(answer)
                    continue
            column_has_answer = False
            for index, model in enumerate(col_models):
                if self.is_c_rule:
                    final_elements = elements
                elif model.enum == AnswerValueEnum.NS.value:
                    final_elements = [e for e in elements if e["index"] in self.ns_crude_indices] or elements
                else:
                    # TODO 后续考虑ND
                    final_elements = [
                        e
                        for e in elements
                        if e["index"] in self.ps_crude_indices or e["index"] not in self.ns_crude_indices
                    ] or elements
                special_answers, predicted_indices = [], set()
                tbl_elements = {e["index"]: e for e in final_elements if is_table_elt(e)}
                # multi_models中包含model_id='need_row_pattern'的模型
                is_special_multi_model = model.name == "multi_models" and any(
                    m.get("model_id") == DYNAMIC_MODEL_ID for m in model.config.get("models")
                )
                # model_id='need_row_pattern'的special_cells模型
                is_special_tbl_model = model.config.get("model_id") == DYNAMIC_MODEL_ID
                if addition_row and self.table_row_patterns and (is_special_multi_model or is_special_tbl_model):
                    # 表格中日期太多，单纯用单个正则会匹配到多行，需要限制更严格的正则
                    # http://************:55647/#/project/remark/268484?projectId=17&treeId=6340&fileId=70546&schemaId=18
                    for tbl_idx in tbl_elements:
                        if tbl_idx not in tbl_elements:
                            continue
                        if not (pattern := self.table_row_patterns[tbl_idx].get(addition_row.event_id)):
                            continue
                        predicted_indices.add(tbl_idx)
                        self.set_model_conf(model, pattern)
                        special_answers = model.predict([tbl_elements[tbl_idx]], sort_by_score=False)
                        # 预测完成，恢复默认配置
                        self.set_model_conf(model)
                        if special_answers:
                            break
                # 没有外部数据（例如IPO）或没有table_row_patterns时，跳过`model_id=need_row_pattern`的模型
                if is_special_tbl_model:
                    if not special_answers:
                        # 模型全部包含model_id=need_row_pattern且没有答案，则跳过该模型
                        continue
                    # 已有答案不再预测，但要做后处理
                    model_answers = special_answers
                else:
                    origin_multi_models = None
                    if is_special_multi_model:
                        # 包含动态模型的multi_models删除动态模型进行预测
                        origin_multi_models = deepcopy(model.config["models"])
                        model.config["models"] = [
                            m for m in model.config["models"] if m.get("model_id") != DYNAMIC_MODEL_ID
                        ]
                    # multi_models中已经预测出了表格答案，则排除所有的表格元素块
                    exclude_indices = tbl_elements if special_answers else predicted_indices
                    # 排除已预测表格及包含need_row_pattern的模型，再预测一次
                    model_answers = special_answers + model.predict(
                        [e for e in final_elements if e["index"] not in exclude_indices], sort_by_score=False
                    )
                    if origin_multi_models:
                        model.config["models"] = origin_multi_models
                if not model_answers:
                    continue

                model_answers = self.get_common_predictor_results(model_answers)
                if callable(self.post_process_answers):
                    # 若配置了后处理函数，依据后处理函数处理answers
                    model_answers = self.post_process_answers(
                        model_answers,
                        model=model,
                        schema=schema,
                        pdfinsight=self.pdfinsight,
                        year_start=self.year_start,
                        year_end=self.year_end,
                        report_year=self.report_year,
                    )
                    if not model_answers:
                        continue
                # 如果model开启了multi_elements，可能会有多组答案，合并，并指定枚举值
                model_answer = self.create_result(
                    list(chain.from_iterable(ans.element_results for ans in model_answers)),
                    value=model_answers[0].answer_value,
                    schema=schema,
                    meta=meta,
                )
                # 过滤答案后可能结果为None
                if addition_row:
                    model_answer = self.filter_result_answer(model_answer, addition_row, order_or_alias, total_count)
                    if not model_answer:
                        continue
                    logger.debug(
                        f"------schema: {column}, effected model: ==={model.name}===, order: ==={index + 1}===, "
                        f"addition_data={addition_row}"
                    )
                else:
                    logger.debug(
                        f"------schema: {column}, effected model: ==={model.name}===, order: ==={index + 1}==="
                    )
                answer_results.append(model_answer)
                column_has_answer = True
                break
            if not column_has_answer:
                # 确保每个column有默认值
                answer_results.append(self._nd_answer(schema, meta))
        return answer_results

    # def _must_be_nd(self, addition_row: AdditionData, elements: list[dict]):
    #     """
    #     满足以下任一条件，答案必须为ND
    #     1. 规则为B1-B7，当年之前发行完成（addition_row.last_report_date<self.year_start)
    #     2. 规则为B1-B10，当年发行且当年未完成（即跨年发行）：当年没有募集到钱，则B1-B10都为ND
    #     https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/5331#note_618281
    #     """
    #     if not self.is_c_rule and addition_row.last_report_date < self.year_start:
    #         return True
    #     if self.year_start <= addition_row.first_report_date <= self.year_end < addition_row.last_report_date:
    #         # 包含当年募集到钱的描述，则不能ND
    #         # http://************:55647/#/project/remark/266498?treeId=8859&fileId=70514&schemaId=15&projectId=8859&schemaKey=B9
    #         if any(
    #             self.P_RECEIVED_MONEY.search(clean_txt(e["text"])) for e in elements if is_para_elt(e)
    #         ):
    #             return False
    #         if not any(dt <= self.year_end for dt in sorted(addition_row.report_dates)[1:]):
    #             return True
    #     return False

    def filter_result_answer(
        self, model_answer: PredictorResult, addition_row: AdditionData, order_or_alias: int, total_count: int
    ) -> PredictorResult | None:
        """
        提取到的NS句式里，如果描述的不是当前分组，丢弃答案
        """
        element_results = []
        for elem_res in model_answer.element_results:
            elt_index = elem_res.element["index"]
            # 答案中有表格，说明答案正确
            if is_table_elt(elem_res.element):
                element_results.append(elem_res)
                continue
            # syllabus_title = ""
            # if syllabus_titles := self.pdfinsight.find_syllabuses_by_index(elt_index):
            #     syllabus_title = syllabus_titles[-1]["title"]
            # entered date对应不上，答案错误
            answer_aliases = self.extract_event_aliases(elem_res.text) or self.aliases_of_element[elt_index]
            answer_dates = self.entered_dates_of_element[elt_index]
            # if len(answer_dates) != 1:
            #     answer_dates = self.extract_entered_dates(elem_res.text, syllabus_title, aliases=answer_aliases)
            if answer_dates:
                if addition_row.first_report_date in answer_dates:
                    element_results.append(elem_res)
                continue
            # 序号对应不上，答案错误
            if total_count > 1:
                if answer_aliases and order_or_alias and order_or_alias not in answer_aliases:
                    continue
            element_results.append(elem_res)
        if not element_results:
            return None
        model_answer.element_results = element_results
        return model_answer
