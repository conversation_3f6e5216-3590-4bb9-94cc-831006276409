# -*- coding: utf-8 -*-
import logging
import re
from collections import Counter, defaultdict
from copy import copy, deepcopy
from enum import Enum
from functools import reduce
from itertools import groupby
from operator import itemgetter
from typing import Any, Callable, Dict, Iterable, List

from remarkable.common.common import get_keys, is_para_elt, is_paragraph_elt, is_table_elt
from remarkable.common.common_pattern import (
    P_CHAPTER_PREFIX,
    P_NON_WORDS,
    R_MIDDLE_DASHES,
    esg_chapter_p,
    esg_outline_p,
)
from remarkable.common.constants import (
    ALL_SCHEME_ENUM,
    AnswerValueEnum,
    Language,
    PDFInsightClassEnum,
    SchemaName,
    TableType,
)
from remarkable.common.element_util import is_multi_line
from remarkable.common.pattern import PatternCollection
from remarkable.common.rectangle import Rectangle
from remarkable.common.util import (
    clean_txt,
    group_cells,
    has_english_chars,
    index_in_space_string,
    is_in_range,
    split_paragraph,
)
from remarkable.config import get_config
from remarkable.pdfinsight import clear_syl_title
from remarkable.pdfinsight.parser import ParsedTable, header_cell_unit_patterns, parse_table
from remarkable.pdfinsight.reader import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, PdfinsightSyllabus
from remarkable.pdfinsight.reader_util import find_real_syllabus, find_table_title
from remarkable.plugins.predict.models.model_base import DIMENSION_PATTERNS, SPECIAL_ATTR_PATTERNS
from remarkable.predictor.common_pattern import R_PERCENT, UNIT_PATTERN
from remarkable.predictor.dataset import DatasetItem
from remarkable.predictor.default_predictor.utils import is_table_result
from remarkable.predictor.eltype import ElementClassifier, ElementType
from remarkable.predictor.mold_schema import SchemaItem
from remarkable.predictor.schema_answer import (
    AnswerResult,
    CellCharResult,
    CharResult,
    ElementResult,
    OutlineResult,
    ParagraphResult,
    PredictorResult,
    PredictorResultGroup,
    TableResult,
)

SPECIAL_WORDS = PatternCollection([r"[:：（）()、\.%．，的╱]", r"^/"])
SERIAL_WORDS = PatternCollection([r"^[一二三四五六七八九\d](?!\d)"])
SUPPLEMENTARY_UNIT = PatternCollection(
    [
        r"[（\(【](次[/∕／]年([，、]次[/∕／]半年)?)[】\)）]",
        r"[（\(【](倍数?)[】\)）]",
        r"[（\(【](注\d?)[】\)）]",
        r"[（\(【](次[/∕／]?[期年])[】\)）]",
        r"[（\(【](合并，元)[】\)）]",
        r"[（\(【](元[/∕／]股、元[/∕／]注册资本)[】\)）]",
        r"[（\(【]?(元股)[】\)）]?",
    ]
)
CN_PATTERN = re.compile(r"([\u4e00-\u9fa5][\u4e00-\u9fa5\d]*[\u4e00-\u9fa5])")

AS_FOLLOW_PATTERN = PatternCollection(
    [
        r"following:$",
        r"including:$",
        r"include:$",
        r"following.*?measures.*?:$",
        r"measures.*?handle wastes:$",
        r"following practices to.*?:$",
        r"such as:$",
        r"as follows?:$",
        r"following initiatives are adopted:$",
        r"follows.*?sources:$",
        r"unless otherwise stated:$",
        r"scope of the Report covers:$",
        r"with regards to:$",
        r"the following matters:$",
        r"following.*?measures.*?:$",
        r"following.*?actions:$",
    ],
    re.I,
)

AS_FOLLOW_START_PATTERN = PatternCollection(
    [
        r"^(\d{1,2}|[ivx]{1,4})\s",
        r"^[(（]?(\d{1,2}|[a-h]|[ivx]{1,4})([)）‧•]|\.\s)",
        r"^[•‧①②③④⑤⑥⑦⑧⑨⑩⑪⑫⑬⑭⑮⑯⑰⑱⑲⑳]",
        r"^[-—－–]",
    ],
    re.I,
)

R_PRIMARY_ROLE = r"(primary(ly)?|principal|main(ly)?|primarily|major)\s*(responsib(le|ility|ilities)|functions|duties|roles?|objectives?)|responsible\s*(for|to)|The\s*duties\s*of"

P_PRIMARY_ROLE = PatternCollection(
    [
        R_PRIMARY_ROLE,
        # r"terms of reference", # http://************:55647/#/project/remark/245402?treeId=13627&fileId=66214&schemaId=28&projectId=13627&schemaKey=E%28d%29%28i%29-2-Effectiveness%20of%20internal%20audit%20function
        # r"primary work", # http://************:55647/#/project/remark/244883?treeId=7850&fileId=66733&schemaId=28&projectId=7850&schemaKey=E%28d%29%28i%29-2-Effectiveness%20of%20internal%20audit%20function
        r"主要职责|主要職責",
    ]
)

P_PRIMARY_ROLE_CHAPTER = PatternCollection([r"Management Structure"], re.I)

# 接续上一页的章节标题
P_CONTINUED_TITLES = PatternCollection(
    [
        r"^Corporate\s*Governance\s*Report$",
        r"[-—(（]\s*continued?\s*[)）]?\s*$",
    ],
    re.I,
)
REPORT_YEAR_REG = re.compile(r"(?:\D|^)(20\d{2})(?:\D|$)")
P_NOT_SKIP_TABLE = PatternCollection([r"set out in the following table:$"], re.I)
# 详情见xxx章节
P_AND = re.compile(r"\s+[，,]\s+|\s+and\s+")
P_QUOTE = re.compile(r"[\"“”]")
P_BRACKET = re.compile(r"[（(【]")
R_DISCLOSED = r"\b(disclosed|set\s*out\b|detail(ed|s)?|included?|contain(ed|s)?)"
P_DISCLOSED_IN_CHAPTER = [
    re.compile(
        rf"{R_DISCLOSED}.*?((under|headed)\s*(the\s*)?[\"“”](?P<headers>[^\"“”]+)[\"“”])?\s*(in|under)\s*(?P<notes>notes?\s+\S+(\s+and(\s+note)?\s+\S+)?)\s+(to|of)\s*(the\s*)?(consolidated|financial)",
        re.I,
    ),
    re.compile(
        rf"{R_DISCLOSED}.*?((under|headed)\s*(the\s*)?(?P<headers>[\"“”][^\"“”]+[\"“”]))?\s*(in|under)\s*(?P<notes>notes?\s+\S+)\s*\.$",
        re.I,
    ),
    re.compile(
        rf"{R_DISCLOSED}.*?(under|headed)\s*(the\s*)?(?P<headers>[\"“”][^\"“”]+[\"“”](\s+(and|or)\s+[\"“”][^\"“”]+[\"“”])?)\s+(in|under)\s+(the\s+)?[\"“”](?P<root>[^\"“”]+)[\"“”]",
        re.I,
    ),
    re.compile(
        rf"{R_DISCLOSED}.*?(under|headed)\s*(the\s*)?(?P<headers>[\"“”][^\"“”]+[\"“”](\s+(and|or)\s+[\"“”][^\"“”]+[\"“”])?)",
        re.I,
    ),
]
P_ANNUAL_REPORT = re.compile(rf"^[{R_MIDDLE_DASHES}\d\s]*ANNUAL\s*REPORT|ANNUAL\s*REPORT[\d\s]*$", re.I)

logger = logging.getLogger(__name__)


class BaseModel:
    target_element = None
    base_all_elements = False
    filter_elements_by_target = False

    # predictor: SchemaPredictor = None
    def __init__(self, options: Dict, schema: SchemaItem, predictor=None):
        self._options = options
        self.name = options.get("name")
        self.schema = schema
        self.predictor = predictor
        self.columns = predictor.columns
        self.model_data = {}
        self.primary_key = predictor.primary_key
        self.ignore_missing_crude_answer = options.get("name")
        self.note_chapter_children = []

    @property
    def predictors(self) -> Dict[str, Any]:
        return {predictor.schema_name: predictor for predictor in self.predictor.prophet.predictors}

    @property
    def is_english(self):
        return self.get_config("client.content_language", "zh_CN") != Language.ZH_CN.value

    @property
    def leaf(self):
        return self.schema.is_leaf

    @property
    def config(self):
        return self._options

    @property
    def file_id(self):
        return self.predictor.prophet.metadata["file"].id

    @property
    def report_year(self):
        """财年"""
        return self.predictor.prophet.metadata.get("report_year") or ""

    @property
    def year_start(self):
        """
        财报期初
        """
        return self.predictor.prophet.metadata.get("year_start") or ""

    @property
    def year_end(self):
        """
        财报期末
        """
        return self.predictor.prophet.metadata.get("year_end") or ""

    @property
    def company_name(self):
        """
        年报所属企业名称
        """
        return self.predictor.prophet.metadata["company_name"]

    @property
    def group_answer(self):
        return self.predictor.config.get("group_answer")

    @property
    def value_required(self):
        return self.predictor.schema.is_enum

    @property
    def group_by(self):
        return self.predictor.config.get("group_by")

    @property
    def model_id(self) -> str:
        """
        为某个规则下的指定model设置一个唯一的id
        目前应用场景：应用模型Reference时，配置了属性model_ids，那么要在from_path对应的模型中，配置model_id
        """
        return self.get_config("model_id")

    @property
    def multi(self):
        return self.get_config("multi", False)

    @property
    def multi_content(self):
        return self.get_config("multi_content", False)

    @property
    def enum(self) -> Enum | None:
        """
        直接设置结果枚举值
        """
        return self.get_config("enum")

    @property
    def with_element_box(self):
        """
        是否需要框选元素块，默认框选
        如果不框选的时候，必须有枚举值
        """
        return self.get_config("with_element_box", True)

    @property
    def aim_types(self) -> list[str] | set[str]:
        """
        指定元素块类型
        """
        aim_types = self.get_config("aim_types")
        if aim_types and isinstance(aim_types, str):
            aim_types = [aim_types]
        return aim_types

    @property
    def skip_syllabus_title(self):
        # 是否跳过章节标题
        return self.get_config("skip_syllabus_title", False)

    @property
    def skip_page_header(self):
        """
        是否跳过被误识别为paragraph的页眉
        """
        return self.get_config("skip_page_header", False)

    @property
    def skip_notes(self):
        """
        是否忽略表格附注
        """
        return self.get_config("skip_notes")

    @property
    def skip_child_syllabuses(self):
        # 匹配到子章节时，是否需要忽略子章节，仅保留父章节
        return self.get_config("skip_child_syllabuses", True)

    @property
    def multi_elements(self):
        return self.get_config("multi_elements", False)

    @property
    def enum_every_element(self):
        """是否每个元素块都对应一个枚举值，例如规则B93.1"""
        return self.get_config("enum_every_element", False)

    @property
    def filter_content_answer(self):
        return self.get_config("filter_content_answer", False)

    @property
    def syllabus_regs(self):
        syllabus_regs = self.get_config("syllabus_regs", [])
        if (
            not self.get_config("strict_limit") and self.predictor.schema.parent.name == "Jura4 Annual Report ESG"
        ):  # 年报esg
            syllabus_regs.extend(esg_outline_p.patterns)
            syllabus_regs.extend(esg_chapter_p.patterns)
        return PatternCollection(syllabus_regs, self.flags)

    @property
    def extend_candidates_syllabus_regs(self):
        extend_candidates_syllabus_regs = self.get_config("extend_candidates_syllabus_regs", [])
        return PatternCollection(extend_candidates_syllabus_regs, self.flags)

    @property
    def extend_candidates_by_enum(self):
        value = self.get_config("extend_candidates_by_enum", False)
        if value and not self.enum:
            raise ValueError("'enum' must be configured")
        return value

    @property
    def extend_by_disclosed_kw_regs(self) -> PatternCollection:
        """
        如果候选元素块中提及“详见xxx章节”，则将对应章节元素块也作为候选元素块
        这里的正则指，段落中包含了正则匹配的关键词，才根据`P_DISCLOSED_IN_CHAPTER`提取章节。
        """
        return PatternCollection(self.get_config("extend_by_disclosed_kw_regs", default=[]), self.flags)

    @property
    def extend_by_disclosed_neg_regs(self) -> PatternCollection:
        """
        如果候选元素块中提及“详见xxx章节”，则将对应章节元素块也作为候选元素块
        这里的正则指，段落中任意一个句子匹配了该关键词，则不提取详情章节
        """
        return PatternCollection(self.get_config("extend_by_disclosed_neg_regs", default=[]), self.flags)

    @property
    def neglect_syllabus_regs(self):
        return PatternCollection(self.get_config("neglect_syllabus_regs"), self.flags)

    @property
    def neglect_parent_features(self):
        """
        忽略父级章节syllabus的标题正则
        """
        return PatternCollection(self.get_config("neglect_parent_features", default=[]), self.flags)

    @property
    def parent_features(self):
        """
        父级章节中必须至少有一个能匹配这个正则
        """
        return PatternCollection(self.get_config("parent_features", default=[]), self.flags)

    @property
    def parent_must_be_root(self):
        """
        parent_features和neglect_parent_features只校验一级章节
        """
        value = self.get_config("parent_must_be_root", False)
        if value and not self.parent_features and not self.neglect_parent_features:
            raise ValueError("`parent_features` or `neglect_parent_features` must be configured")
        return value

    @property
    def page_first_as_parent_syllabus(self):
        """
        将页面第一个识别到的root标题作为root章节
        """
        return self.get_config("page_first_as_parent_syllabus", True)

    @property
    def page_range(self) -> list[int, int]:
        """
        过滤元素块的页码范围
        """
        return self.get_config("page_range", [])

    @property
    def filter_primary_role_elements(self):
        return self.get_config("filter_primary_role_elements")

    @property
    def remove_blank(self):
        return self.get_config("remove_blank", False)

    @property
    def flags(self):
        """
        使用para_pattern及neglect_pattern匹配时，用到的模式修正符
        默认忽略大小写，不想忽略大小写可以配置0，也可以配置多个例如：re.I | re.X
        """
        pattern_flag = self.get_config("flags", re.IGNORECASE)
        return pattern_flag | re.X if self.remove_blank else pattern_flag

    @property
    def as_follow_start_pattern(self):
        as_follow_start_pattern = deepcopy(AS_FOLLOW_START_PATTERN.patterns)
        as_follow_start_pattern.extend(self.config.get("special_as_follow_start", []))
        return PatternCollection(as_follow_start_pattern, self.flags)

    @property
    def max_paragraphs_for_every_follow(self):
        """
        as following 下每条记录的段落数，默认为1，即连续段落
        """
        return self.get_config("max_paragraphs_for_every_follow", 1)

    @property
    def neglect_below_pattern(self):
        """
        as following下的条目中，匹配则跳过
        """
        pattern = None
        if self.schema.root_name == SchemaName.CG.value:
            pattern = self.get_config("neglect_below_pattern") or self.config.get("para_config", {}).get(
                "neglect_below_pattern", []
            )
        return PatternCollection(pattern, self.flags)

    @property
    def below_pattern(self):
        """
        as following下的条目中，匹配才取，否则跳过
        """
        pattern = self.get_config("below_pattern") or self.config.get("para_config", {}).get("below_pattern", [])
        return PatternCollection(pattern, self.flags)

    @property
    def below_need_continus(self):
        """
        as following下提取到的序号是否需要连续
        """
        return self.get_config("below_need_continuous", False) or self.config.get("para_config", {}).get(
            "below_need_continuous", False
        )

    @property
    def below_skip_chinese(self):
        """
        as following下面内容是否需要跳过中文
        """
        return (
            self.config.get("para_config", {}).get("below_skip_chinese", True)
            if self.get_config("below_skip_chinese") is None
            else self.get_config("below_skip_chinese", True)
        )

    @property
    def skip_answer_value(self) -> AnswerValueEnum | None:
        """
        丢弃当前模型结果中answer_value == skip_answer_value的结果
        """
        answer_value = self.get_config("skip_answer_value")
        return AnswerValueEnum(answer_value) if answer_value else None

    @property
    def ignore_pattern(self):
        # 排除章节中不需要的元素块 比如 没有被识别成页眉的esg相关的描述
        return PatternCollection(self.get_config("ignore_pattern", []), self.flags)

    @property
    def ignore_syllabus_pattern(self):
        # 判断as follow时，能匹配该正则的章节当作段落来处理
        return PatternCollection(self.config.get("ignore_syllabus_pattern", []), self.flags)

    @property
    def post_process_model_answers(self) -> Callable:
        """
        后处理函数，配置了该函数会据此对结果进行后处理
        """
        return self.config.get("post_process_model_answers")

    @property
    def dr_chapter_first(self):
        """
        是否优先DR章节，share option相关规则，需要优先DR章节
        """
        return self.config.get("dr_chapter_first", False)

    def get_model_data(self, column=None):
        if not self.model_data:
            self.model_data = self.predictor.model_data.get(self.name, {})
        return self.model_data.get(column or self.schema.name)

    @property
    def pdfinsight(self) -> PdfinsightReader:
        return self.predictor.pdfinsight

    @property
    def doc_type(self):
        return self.predictor.doc_type

    @property
    def pdfinsight_syllabus(self) -> PdfinsightSyllabus:
        return self.pdfinsight.syllabus_reader

    @property
    def inject_elements_func(self):
        """
        配置该参数，表示候选元素块来自`inject_elements_func`函数
        """
        return self.get_config("inject_elements_func")

    def get_config(self, key, default=None, column=None):
        """获取字段模型配置
        兼容统一配置和各自配置：
        1. 优先字段配置：option[column][key]，若缺省则：
        2. 再取公共配置：option[key]，若缺省则：
        3. 默认配置 default
        """
        if column and column in self.config and key in self.config[column]:
            return self.config[column][key]
        return self.config.get(key, default)

    def columns_with_fullpath(self) -> List[str]:
        for column in self.columns:
            yield column, self.schema.sibling_path(column)

    def train(self, dataset, **kwargs):
        raise NotImplementedError

    def find_answer_nodes(self, item, path):
        schema = self.schema.mold_schema.find_schema_by_path(path[1:])
        if schema.is_amount:
            return item.answer.find_nodes(path[1:] + schema.orders[:1])
        return item.answer.find_nodes(path[1:])

    def print_model(self):
        pass

    def get_models(self, predictor, from_paths):
        """
        针对reference配置的path，获取对应的models配置，考虑models为GroupBased的场景
        """
        if predictor.models and predictor.models[0].name == "group_based":
            models = predictor.models[0].children_models.get(from_paths[1], [])
        else:
            models = predictor.models
        return models

    def filter_elements(self, elements, check_syllabus=True):
        return self.base_filter_elements(elements)

    def base_filter_elements(self, elements, check_syllabus=True):
        if self.filter_elements_by_target:
            elements = self.filter_target_elements(elements)
        if self.skip_syllabus_title:
            elements = self.filter_syllabuses_titles(elements)
        if self.skip_notes:
            elements = self.filter_notes(elements)
        if self.parent_features or self.neglect_parent_features:
            elements = self.filter_elements_by_parent_syllabuses(elements)
        if self.page_range:
            elements = self.filter_elements_by_page(elements)
        if check_syllabus and (not hasattr(self, "filter_by_syllabus_regs") or self.filter_by_syllabus_regs):
            elements = self.filter_elements_by_syllabus_regs(elements)
        if self.config.get("current_report_period") and self.report_year:
            elements = self.filter_elements_by_current_report_year(elements)
        if self.aim_types:
            elements = [e for e in elements if e["class"] in self.aim_types]
        # 过滤错误识别的页眉元素块
        if self.skip_page_header:
            elements = [e for e in elements if not self.is_page_header(e)]
        return elements

    def is_page_header(self, element):
        """
        页眉被误识别为段落，这里做过滤
        判断元素块为页眉，必须同时满足以下2个条件：
        1. 页面第一个段落元素块
        2. 以XXXannual report开头或以annual report结尾；或以公司名称开头或结尾
        http://************:55647/#/project/remark/268552?treeId=31014&fileId=70556&schemaId=18&projectId=17&schemaKey=C2.1.1&page=162
        https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/3168#note_725774
        """
        if not self.pdfinsight.is_page_first_para(element):
            return False
        if is_multi_line(element):
            return False
        if P_ANNUAL_REPORT.search(element["text"]):
            return True
        text = clear_syl_title(element["text"].upper(), remove_cn_text=True, remove_bound_num=True).upper()
        return text.startswith(self.company_name) or text.endswith(self.company_name)

    def filter_elements_by_page(self, elements):
        return [element for element in elements if is_in_range(element["page"], self.page_range)]

    def filter_elements_by_syllabus_regs(self, elements):
        filtered_elements = []
        strict_limit = self.get_config("strict_limit", False)
        for element in elements:
            if self.syllabus_regs and not self.match_syllabus(
                element, self.syllabus_regs, match_above=True, strict_limit=strict_limit
            ):
                continue
            if self.neglect_syllabus_regs and self.match_syllabus(
                element, self.neglect_syllabus_regs, strict_limit=strict_limit
            ):
                continue
            filtered_elements.append(element)

        return filtered_elements

    def filter_elements_by_current_report_year(self, elements):
        res_elements = []
        for element in elements:
            if "text" in element:
                if not self.check_content_by_report_year(clean_txt(element["text"]), self.report_year):
                    continue
            res_elements.append(element)
        return res_elements

    @staticmethod
    def check_content_by_report_year(content, report_year):
        next_report_year = None
        year_matched = []
        if not report_year:
            return True
        if report_year.isdigit():
            next_report_year = str(int(report_year) + 1)
        for matched in REPORT_YEAR_REG.finditer(content):
            year_matched.append(matched.group(1))
        if (
            year_matched
            and report_year.isdigit()
            and report_year not in year_matched
            and next_report_year not in year_matched
        ):
            return False
        return True

    def match_syllabus(self, element, pattern, match_above=False, strict_limit=False):
        syllabuses = self.pdfinsight.syllabus_reader.find_by_elt_index(element["index"])
        for syllabus_ in syllabuses[::-1]:
            syllabus = find_real_syllabus(self.pdfinsight, syllabus_)
            if self.parent_must_be_root and syllabus["parent"] == -1:
                continue
            matcher = pattern.nexts(clean_txt(syllabus.get("title", ""), remove_blank=self.remove_blank))
            if matcher:
                return True
            if not strict_limit and match_above and self.predictor.schema.root_name == get_config("esg_molds.AR.name"):
                page = syllabus["dest"]["page_index"]
                page_elements = self.pdfinsight.find_elements_by_page(page)
                for page_element in page_elements[:5]:
                    if not page_element.get("text"):
                        continue
                    if esg_outline_p.nexts(clean_txt(page_element["text"], remove_blank=self.remove_blank)):
                        return True
        return False

    @classmethod
    def extract_disclosed_headers(cls, sentence: str) -> set[str]:
        titles = set()
        start = 0
        for pattern in P_DISCLOSED_IN_CHAPTER:
            # 每次从上次正则匹配结束的位置开始匹配，避免重复
            for matched in pattern.finditer(sentence[start:]):
                group_dict, start = matched.groupdict(), matched.end()
                notes, headers, root_header = group_dict.get("notes"), group_dict.get("headers"), group_dict.get("root")
                if notes:
                    # 注意这里note的序号要保证大小写
                    for note_title in P_AND.split(notes):
                        if note_title.lower().startswith(("note ", "notes ")):
                            note_title = f"note {note_title.split(maxsplit=1)[1]}"
                        else:
                            note_title = f"note {note_title}"
                        if headers:
                            titles.add(f"{note_title} - {headers}")
                        else:
                            titles.add(note_title)
                else:
                    if P_QUOTE.search(headers):
                        titles_ = [h.lower() for i, h in enumerate(P_QUOTE.split(headers)) if i % 2 != 0]
                    else:
                        titles_ = [h.lower() for h in P_AND.split(headers)]
                    if root_header:
                        titles_ = [f"{root_header.lower()} - {t}" for t in titles_]
                    titles.update(titles_)
        return titles

    def get_exclude_indices_by_neg_syllabus(self, syllabus: dict) -> set[int]:
        if not self.neglect_syllabus_regs or not syllabus["children"]:
            return set()
        exclude_indices = set()
        for child_idx in syllabus["children"]:
            if not (child := self.pdfinsight_syllabus.syllabus_dict.get(child_idx)):
                continue
            child_title = clean_txt(child["title"], remove_cn_text=True)
            # 超过60个单词句号结尾的段落，被错误识别为章节
            # https://jura6-lir.paodingai.com/#/hkex/annual-report-checking/report-review/261202?fileId=69455&schemaId=5&rule=C7.2&delist=0 index=774
            if P_CHAPTER_PREFIX.search(child_title) and len(child_title.split()) > 60 and child_title.endswith("."):
                continue
            if self.neglect_syllabus_regs and self.neglect_syllabus_regs.nexts(child_title):
                exclude_indices.update(range(*child["range"]))
            else:
                exclude_indices.update(self.get_exclude_indices_by_neg_syllabus(child))
        return exclude_indices

    def find_candidates_by_disclosed_titles(self, element, check_kw=True):
        def guess_syllabus_title(syll_title):
            # 去掉子章节标题中的特殊符号再对比
            # http://************:55647/#/project/remark/294796?treeId=6414&fileId=70825&schemaId=18&projectId=17&schemaKey=C7.1
            syll_title = syll_title.strip().lower().strip()
            syll_title1 = syll_title.split("(", maxsplit=1)[0].split("（", maxsplit=1)[0].strip()
            return {syll_title, syll_title1, P_NON_WORDS.sub("", syll_title), P_NON_WORDS.sub("", syll_title1)}

        if not is_para_elt(element):
            return []
        titles = set()
        for sentence in split_paragraph(element["text"]):
            if self.extend_by_disclosed_neg_regs.nexts(sentence):
                return []
            if check_kw and self.extend_by_disclosed_kw_regs and not self.extend_by_disclosed_kw_regs.nexts(sentence):
                continue
            titles.update(self.extract_disclosed_headers(sentence))
        if not titles:
            return []
        element_chapters = self.pdfinsight.find_syllabuses_by_index(element["index"])
        if not element_chapters:
            return []
        elt_chapter_index = element_chapters[-1]["index"]
        # 1. 先找notes章节下的note {N}，仅根据N匹配所属章节
        from remarkable.predictor.hkex_predictor.model_util import find_syllabuses_by_note_nums

        note_titles = {t for t in titles if t.startswith("note ")}
        syllabuses = find_syllabuses_by_note_nums(
            self.pdfinsight, self.note_chapter_children, note_titles, self.aim_types
        )
        # 2. 除了note {N}，以外的章节，必须严格匹配标题
        for title in titles - note_titles:
            if " - " in title:
                # http://************:55647/#/project/remark/265134?treeId=10062&fileId=70241&schemaId=18&projectId=17&schemaKey=C7.2 index=958
                root_header, header = title.split(" - ", maxsplit=1)
            else:
                root_header, header = None, title
            for syllabus_ in self.pdfinsight_syllabus.syllabuses:
                syllabus = find_real_syllabus(self.pdfinsight, syllabus_)
                if syllabus["level"] == 1 or syllabus["index"] == elt_chapter_index:
                    continue
                if not guess_syllabus_title(syllabus["title"]) & guess_syllabus_title(header):
                    continue
                if self.extend_candidates_syllabus_regs.nexts(syllabus["title"]):
                    continue
                if not root_header:
                    syllabuses.append(syllabus)
                    continue
                root_title = self.pdfinsight.page_chapter_from_first_para.get(syllabus["dest"]["page_index"])
                if not root_title and (root_syll := self.pdfinsight_syllabus.get_root_syllabus(syllabus)):
                    root_title = root_syll["title"]
                if not (root_title and guess_syllabus_title(root_title) & guess_syllabus_title(root_header)):
                    continue
                syllabuses.append(syllabus)
        result_elements = []
        for syllabus in syllabuses:
            # 排除neg_syllabus_regs章节的元素块
            exclude_indices = self.get_exclude_indices_by_neg_syllabus(syllabus)
            syllabus_elements = self.get_candidate_elements_by_range(syllabus["range"], exclude_indices=exclude_indices)
            if syllabus:
                logger.debug(f"extend candidates from disclosed in the chapter: {syllabus['title']}")
                result_elements.extend(syllabus_elements)
        return result_elements

    def get_syllabus_candidates(self):
        exclude_indices = set()
        new_candidate_elements, chapter_elements = [], []
        if self.extend_candidates_syllabus_regs:
            for syllabus in self.pdfinsight.syllabus_reader.syllabuses:
                # 需要一级一级向上找父章节: https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/3168#note_702757
                real_syllabuses = self.pdfinsight.get_real_parent_syllabuses(syllabus) or [syllabus]
                real_syllabus = real_syllabuses[0]
                start, end = real_syllabus["range"]
                if end - start < 2:
                    continue
                # 必须能够匹配正则
                if not any(self.extend_candidates_syllabus_regs.nexts(s["title"]) for s in real_syllabuses):
                    continue
                # 排除上级neg_syllabus_regs章节的元素块
                if self.neglect_syllabus_regs and any(
                    self.neglect_syllabus_regs.nexts(s["title"]) for s in real_syllabuses
                ):
                    continue
                # 排除下级neg_syllabus_regs章节的元素块
                exclude_indices.update(self.get_exclude_indices_by_neg_syllabus(real_syllabus))
                syllabus_elements = self.get_candidate_elements_by_range(
                    real_syllabus["range"], exclude_indices=exclude_indices, aim_types=self.aim_types
                )
                chapter_elements.extend(syllabus_elements)
                exclude_indices.update({element["index"] for element in syllabus_elements})
            # 目标章节是一级目录，则根据page_chapter_from_first_para做扩展
            for page, root_title in self.pdfinsight.page_chapter_from_first_para.items():
                if not self.extend_candidates_syllabus_regs.nexts(root_title):
                    continue
                if self.neglect_syllabus_regs.nexts(root_title):
                    continue
                if page_info := self.pdfinsight.page_dict.get(page):
                    cur_elements = self.get_candidate_elements_by_range(
                        page_info.element_index_range,
                        exclude_indices=exclude_indices,
                        include_start=True,
                    )
                    # 排除否定章节的元素块
                    if self.neglect_syllabus_regs:
                        for element in cur_elements:
                            if self.match_syllabus(element, self.neglect_syllabus_regs):
                                exclude_indices.add(element["index"])
                    chapter_elements.extend(e for e in cur_elements if e["index"] not in exclude_indices)
            new_candidate_elements.extend(chapter_elements)
        new_candidate_elements = self.base_filter_elements(new_candidate_elements, check_syllabus=False)
        return sorted(new_candidate_elements, key=itemgetter("index"))

    def get_specific_candidates(self, elements):
        exclude_indices = set()
        new_candidate_elements = []
        if self.extend_by_disclosed_kw_regs or self.extend_by_disclosed_neg_regs:
            from remarkable.predictor.hkex_predictor.model_util import find_children_of_notes_chapter

            # 提前找到所有note子章节
            self.note_chapter_children = find_children_of_notes_chapter(self.pdfinsight)
            for element in elements:
                if disclosed_elements := self.find_candidates_by_disclosed_titles(element):
                    new_candidate_elements.extend(disclosed_elements)
                    if len(disclosed_elements) > 2:
                        # 元素块太多，就不再向下找一层
                        continue
                    for next_elt in disclosed_elements:
                        # 跳转到详情章节后，再次指定了一个详情章节，这次不限制关键词正则
                        # http://************:55647/#/project/remark/293789?treeId=5154&fileId=70680&schemaId=18&projectId=17&schemaKey=C7.1
                        # http://************:55647/#/project/remark/294387?treeId=8904&fileId=70768&schemaId=18&projectId=17&schemaKey=C7.1
                        if next_disclose_elements := self.find_candidates_by_disclosed_titles(next_elt, check_kw=False):
                            new_candidate_elements.extend(next_disclose_elements)
                    exclude_indices.update({element["index"] for element in disclosed_elements})
        new_candidate_elements = self.base_filter_elements(new_candidate_elements, check_syllabus=False)
        return sorted(new_candidate_elements, key=itemgetter("index"))

    def get_enum_candidates(self, elements):
        existed_indices = {e["index"] for e in elements}
        new_candidate_elements = []
        if self.extend_candidates_by_enum:
            embedding_key = None
            if self.enum == AnswerValueEnum.PS:
                embedding_key = "ps_crude_answer"
            elif self.enum == AnswerValueEnum.NS:
                embedding_key = "ns_crude_answer"
            if embedding_key:
                crud_key = f"{self.schema.parent.name}-{self.columns[0]}"
                for crude_element in get_keys(self.predictor.prophet.metadata, [embedding_key, crud_key], []):
                    elt_index = crude_element["element_index"]
                    if elt_index in existed_indices:
                        continue
                    _, element = self.pdfinsight.find_element_by_index(elt_index)
                    new_element = copy(element)
                    new_element["score"] = crude_element["score"]
                    new_candidate_elements.append(new_element)
        return new_candidate_elements

    def predict(self, elements, sort_by_score=True):
        # 配置了扩展函数，则使用扩展函数处理后的元素块
        if callable(self.inject_elements_func):
            elements = self.inject_elements_func(elements, self.predictor, model=self)
            elements = self.filter_elements(elements)
        else:
            # 扩展embedding初步定位元素块
            elements.extend(self.get_enum_candidates(elements))
            # 依赖初步定位元素块，指定章节/条件的元素块直接取
            syllabus_elements = self.get_syllabus_candidates()
            # TODO syllabus_based模型会进来两次，filter是否可以放到外层？
            elements = self.filter_elements(elements)
            # 根据详见xx元素块配置添加候选元素块
            other_elements = syllabus_elements + self.get_specific_candidates(elements)
            # 表格模型: 扩展元素块必须通过表名过滤
            if other_elements and isinstance(self, TableModel):
                other_elements = self.filter_table_elements(other_elements)
            # 追加章节+详情元素块，确保元素块唯一
            existed_indices = {e["index"] for e in elements}
            for element in other_elements:
                elt_index = element["index"]
                if elt_index in existed_indices:
                    continue
                elements.append(element)
                existed_indices.add(elt_index)

        # element maybe not have score, because that maybe come from syllabus_based.
        # elements of syllabus_based come from pdfinsight query, rather than from the crude answer
        if sort_by_score:
            elements.sort(key=lambda x: x.get("score", 0), reverse=True)
        answer_results = self.predict_schema_answer(elements)
        return self.process_after_predict(answer_results)

    def process_after_predict(self, answer_results):
        if not answer_results:
            return answer_results

        if self.filter_content_answer:
            answer_results = self.exclude_content_answer(answer_results)
        if self.filter_primary_role_elements:
            answer_results = self.exclude_primary_answer(answer_results)
        if self.skip_answer_value:
            answer_results = self.exclude_skip_answer_value(answer_results)
        # 给模型添加后处理
        if callable(self.post_process_model_answers):
            # 若配置了后处理函数，且不是分组结果，则依据后处理函数处理answers
            answer_results = self.post_process_model_answers(
                self.get_common_predictor_results(answer_results),
                model=self,
                schema=self.schema,
                pdfinsight=self.pdfinsight,
                year_start=self.year_start,
                year_end=self.year_end,
                report_year=self.report_year,
            )
        if logger.level > logging.DEBUG:
            return answer_results

        # Only print debug info when debug level is enabled
        if answer_results:
            logger.debug(
                f"{self.schema.name}: answer_results predicted from: ==={self.__class__.__name__}"
                f"{'.' + self.rule.__class__.__name__ if self.__class__.__name__ == 'YodaLayer' else ''}==="
            )
        if isinstance(answer_results, PredictorResult):
            logger.debug(answer_results.repr_with_page_idx)
        if isinstance(answer_results, list):
            for answer_result in answer_results:
                if isinstance(answer_result, PredictorResult):
                    if self.with_element_box and answer_result.repr_with_page_idx:
                        logger.debug(answer_result.repr_with_page_idx)
                else:
                    for key, ans in answer_result.items():
                        for item in ans or []:
                            if isinstance(item, PredictorResult) and item.repr_with_page_idx:
                                logger.debug(item.repr_with_page_idx)
                                continue
                            if hasattr(item, "text") and item.text:
                                logger.debug(f"{key}: {item.text}")

        return answer_results

    def predict_schema_answer(self, elements) -> List[Dict[str, List[PredictorResult]]]:
        raise NotImplementedError

    def create_result(
        self,
        element_results,
        value: ALL_SCHEME_ENUM = None,
        schema=None,
        column=None,
        text=None,
        score=None,
        meta=None,
        primary_key: list[str] = None,
    ) -> PredictorResult:
        if column is None and schema is None:
            raise Exception("column or schema must be specified")
        if schema is None:
            if column and column != self.schema.name:
                schema = self.predictor.parent.find_child_schema(column)
            else:
                schema = self.schema
        if not schema:
            raise Exception(f"can't find column {column} according {self.schema.path}")
        value = value or self.enum
        if not self.with_element_box and len(element_results) >= 1:
            logger.debug(
                f"page={element_results[0].element['page']}, index={element_results[0].element['index']}: {element_results[0].element.get('text', '')}"
            )
        result = PredictorResult(
            # with_element_box=False且有枚举值表示只设置枚举值，不框选元素块
            [] if schema.is_enum and value and not self.with_element_box else element_results,
            value,
            schema=schema,
            text=text,
            score=score,
            meta=meta or {},
            answer_model=self.name,
            ignore_missing_crude_answer=self.ignore_missing_crude_answer,
            primary_key=primary_key,
        )
        if schema.is_enum and self.skip_answer_value and self.skip_answer_value == result.answer_value:
            result.element_results = []
        if schema.is_enum and value is None:
            # TODO: 在这里调用无法引用其他字段
            value = self.predictor.predict_value(result)
            result.update_answer_value(value)
        return result

    def create_multi_outline_results(self, elements):
        """
        针对给定元素块，尽可能合并连续元素块的外框线
        """
        # 拆分表格和段落，且尽量保证原有顺序
        all_elements_list, para_elements = [], []
        for element in elements:
            if not is_table_elt(element):
                para_elements.append(element)
                continue
            if para_elements:
                all_elements_list.append(para_elements)
                para_elements = []
            all_elements_list.append([element])
        if para_elements:
            all_elements_list.append(para_elements)
        outline_results = []
        for elements_ in all_elements_list:
            element = elements_[0]
            if is_table_elt(element):
                # 表格使用TableResult
                table = parse_table(element, tabletype=TableType.TUPLE, pdfinsight_reader=self.pdfinsight)
                outline_results.append(TableResult(element, parsed_table=table))
                continue
            for page_box in PdfinsightSyllabus.elements_outline(elements_):
                box_elements = page_box["elements"]
                if not box_elements:
                    continue
                outline_results.append(
                    OutlineResult(page_box=[page_box], element=box_elements[0], origin_elements=box_elements)
                )
        return outline_results

    @staticmethod
    def do_not_merge(para_results: Iterable[ParagraphResult | ElementResult]) -> bool:
        if any(is_table_result(res) for res in para_results):
            return True
        # 存在同一个元素块下多个不同start值，在不能合并
        sen_starts = {f"{ans.element['index']}-{ans.start if hasattr(ans, 'start') else 0}" for ans in para_results}
        return len({s.split("-")[0] for s in sen_starts}) != len(sen_starts)

    def merge_multi_answers(self, para_results: Iterable[ParagraphResult | ElementResult]):
        """
        若多个段落结果连续，则合并外框线
        """
        # 有特殊类型则不合并
        if self.do_not_merge(para_results):
            return para_results
        paras_dict = {ans.element["index"]: ans for ans in para_results}
        idx_groups = []
        cur_group = []
        # 按元素块索引是否连续分组
        for idx in sorted(paras_dict):
            if not cur_group or idx == cur_group[-1] + 1:
                cur_group.append(idx)
            else:
                # 否则，将当前组添加到结果列表中，并重新开始一个新的组
                idx_groups.append(cur_group)
                cur_group = [idx]

        merged_answers = []
        for indices in idx_groups + [cur_group] if cur_group else []:
            if len(indices) == 1:
                merged_answers.append(paras_dict[indices[0]])
            else:
                merged_answers.extend(self.merge_paragraphs([ans for i, ans in paras_dict.items() if i in indices]))
        return merged_answers

    def merge_paragraphs(
        self, para_result: list[ParagraphResult | CharResult]
    ) -> list[ParagraphResult | CharResult | OutlineResult]:
        # 合并多个连续段落为整个框
        para_result = sorted(para_result, key=lambda x: x.element["index"])
        if any(
            is_paragraph_elt(r.element)
            and (
                (hasattr(r, "start") and r.start and r.start > 0)
                or (hasattr(r, "end") and r.end and r.end < len(r.element["text"]) - 1)
            )
            for r in para_result
        ):
            # 存在非完整段落，则不合并  TODO 使用merge_c7_results()合并句子
            return para_result
        elements = []
        start = para_result[0].element["index"]
        end = para_result[-1].element["index"] + 1
        element_indexes = set()
        for idx in range(start, end):
            try:
                elt_type, elt = self.pdfinsight.find_element_by_index(idx)
            except IndexError:
                break
            if (
                elt
                and elt_type not in ["PAGE_HEADER", "PAGE_FOOTER"]
                and elt["index"] not in element_indexes
                and not self.ignore_pattern.nexts(clean_txt(elt.get("text", "")))
            ):
                elements.append(elt)
                if elt.get("page_merged_paragraph"):
                    for elt_index in elt["page_merged_paragraph"]["paragraph_indices"]:
                        element_indexes.add(elt_index)
                else:
                    element_indexes.add(elt["index"])
        return [OutlineResult(PdfinsightSyllabus.elements_outline(elements))]

    def is_target_element(self, element: Dict) -> bool:
        if not self.target_element:
            self.target_element = self.get_config("target_element")
        if not self.target_element or not self.filter_elements_by_target:
            return True
        if self.target_element == ElementType.PARAGRAPH:
            return ElementClassifier.like_paragraph(element)
        return ElementClassifier.get_type(element) == self.target_element

    def filter_syllabuses_titles(self, elements):
        """
        跳过目录
        """
        return [
            e
            for e in elements
            if not self.pdfinsight.syllabus_reader.is_syllabus_elt(e) and not self.pdfinsight.is_syllabus_title(e)
        ]

    def filter_notes(self, elements):
        from remarkable.predictor.hkex_predictor.model_util import is_note_element

        return [e for e in elements if not is_note_element(self.pdfinsight, e)]

    def filter_elements_by_parent_syllabuses(self, elements):
        """
        根据参数parent_features、neglect_parent_features过滤元素块
        """
        result = []
        for element in elements:
            syllabuses = self.pdfinsight.syllabus_reader.find_by_elt_index(element["index"])
            if self.page_first_as_parent_syllabus:
                if fake_title := self.pdfinsight.page_chapter_from_first_para.get(element["page"]):
                    fake_syllabus = {"title": fake_title, "parent": -1, "fake": True}
                    if self.parent_must_be_root:
                        syllabuses = [*[syll for syll in syllabuses if syll["parent"] != -1], fake_syllabus]
                    else:
                        syllabuses.append(fake_syllabus)

            if not syllabuses:
                continue
            if self.filter_parent_syllabus(syllabuses, match_full_path=False):
                result.append(element)
        return result

    def filter_target_elements(self, elements):
        return [e for e in elements if self.is_target_element(e)]

    def get_title_element(self, element):
        if not element.get("syllabus"):
            return None
        syllabus = self.pdfinsight_syllabus.syllabus_dict[element["syllabus"]]
        if syllabus["element"] == element["index"]:  # syllabus指向自己,说明该element本来就是标题,返回其父标题
            parent_syllabus = self.pdfinsight_syllabus.syllabus_dict.get(syllabus["parent"])
            if not parent_syllabus:
                return None
            title_element_index = parent_syllabus["element"]
        else:
            title_element_index = syllabus["element"]
        _, para = self.pdfinsight.find_element_by_index(title_element_index)
        if not para:
            return None
        return para

    @staticmethod
    def feature_key_str(texts, ignore_cn_char=True):
        """取内容特征字符串
        在此做一些泛化替换
        去掉 表头中的序号相关的描述：  五现金及现金等价物净增加额 ==> 现金及现金等价物净增加额
        去掉 虚词  ：  r':：（）()、.%．， 的' ===> ''
        去掉 表头中包含括号的单位：  r'(倍) （次）（次/年）'===> ''
        """
        texts = [header_cell_unit_patterns.sub("", text) for text in texts]
        texts = [SUPPLEMENTARY_UNIT.sub("", text) for text in texts]
        texts = [SPECIAL_WORDS.sub("", text) for text in texts]
        texts = [SERIAL_WORDS.sub("", text) for text in texts]
        if ignore_cn_char:
            texts = [CN_PATTERN.sub("", text) for text in texts]
        texts = sorted([clean_txt(x) for x in texts])
        return "|".join([t for t in texts if t])

    @staticmethod
    def get_paragraphs_from_table(element):
        """
        公司代码/公司简称/公告编号/户名/开户行 等误识别为table的情况,把element组装成paragraphs
        """
        paragraphs = []
        cells_by_row, _ = group_cells(element["cells"])
        for _, cells in cells_by_row.items():
            line_content = ""
            line_chars = []
            for _, cell in cells.items():
                line_content += clean_txt(cell.get("text", ""))
                line_chars.extend([i for i in cell["chars"] if not re.search(r"^\s+$", i["text"])])
            paragraph = {
                "index": element["index"],
                "syllabus": element["syllabus"],
                "cells": element["cells"],
                "text": line_content,
                "chars": line_chars,
                "class": "PARAGRAPH",
                "origin_class": "TABLE",
            }
            paragraphs.append(paragraph)
        return paragraphs

    def find_special_attr(self, col, elt, **kwargs):
        """
        从当前或上一个element中获取特定属性信息
        :param col: 币种, <金额单位>, <每股收益单位>, <百分比单位>, <**单位>其中之一
        :param elt:
        :return: ResultOfPredictor object or None
        """

        def _filter_chars(item):
            patterns = []
            # 正则优先级: 特殊属性 > 配置文件 > 词频统计
            if col in SPECIAL_ATTR_PATTERNS:
                patterns.extend(SPECIAL_ATTR_PATTERNS[col])
            patterns.extend(self.config.get("regs", []))

            for reg_p in kwargs.get("patterns", []):
                if reg_p is None:
                    print("error!")
                if reg_p.startswith("D_"):
                    patterns.extend(SPECIAL_ATTR_PATTERNS.get(reg_p.split("_")[-1]))
                else:
                    patterns.append(reg_p)

            for pattern in patterns:
                if isinstance(pattern, str):
                    pattern = re.compile(r"{}".format(pattern))

                if isinstance(pattern, re.Pattern):
                    match = pattern.search(clean_txt(item["text"], remove_blank=self.remove_blank))
                    if match:
                        try:
                            m_s, m_e = match.span("dst")
                        except IndexError:
                            # 正则未指定dst参数, 直接返回当前element所有chars
                            return item["chars"]
                        sp_start, sp_end = index_in_space_string(
                            clean_txt(item["text"], remove_blank=self.remove_blank),
                            (m_s, m_e),
                            remove_blank=self.remove_blank,
                        )
                        # 去掉空的文本框, 可能是space/\n\r\t其中一种
                        chars = [i for i in item["chars"] if not re.search(r"^\s+$", i["text"])]
                        return chars[sp_start:sp_end]
            return None

        def _find_special_attr(idx):
            chars = None
            try:
                ele_typ, elt = self.pdfinsight.find_element_by_index(idx)
            except IndexError:
                return None
            if not elt:
                return None
            if ele_typ == "PARAGRAPH":
                chars = _filter_chars(elt)
            elif ele_typ == "TABLE":
                for _idx, cell in elt["cells"].items():
                    chars = _filter_chars(cell)
                    if chars:
                        return CharResult(elt, chars)
            else:
                logger.debug(f"不支持的元素类型: {ele_typ}")
            return CharResult(elt, chars) if chars else None

        element_results = []
        if col in SPECIAL_ATTR_PATTERNS:
            start, end = 0, -3
        else:
            start, end = self.config.get("pos", (0, 1))
        for idx in range(start, end, 1 if start < end else -1):
            _idx = elt["index"] + idx
            unit = _find_special_attr(_idx if _idx > 0 else 0)
            if unit:
                element_results.append(unit)
                break
        else:
            logger.debug(f"未找到: {col}")
        return self.create_result(element_results) if element_results else None

    @staticmethod
    def same_text(cell, text):
        cell_text = cell if isinstance(cell, str) else cell["text"]
        if text.startswith("D_") and text[2:] in DIMENSION_PATTERNS:
            return DIMENSION_PATTERNS[text[2:]].match(cell_text)
        pattern = re.compile(r"\s{2,}")
        cell_text = re.sub(pattern, " ", cell_text)
        text = re.sub(pattern, " ", text)
        return clean_txt(cell_text) == clean_txt(text)

    @staticmethod
    def select_elements(elements, box):
        selected = []
        for ele in elements:
            if not ele["page"] == box["page"]:
                continue
            if PdfinsightReader.overlap_percent(ele["outline"], box["box"], base="box") > 0.2:
                selected.append(ele)
            elif PdfinsightReader.overlap_percent(ele["outline"], box["box"], base="element") > 0.2:
                selected.append(ele)
        return selected

    def get_dst_chars_from_text(self, text, element, span=None):
        cell_text = element["text"]
        return self.get_chars(cell_text, text, element.get("chars"), span)

    def get_dst_chars_from_matcher(self, matcher, element):
        element_text = element["text"]
        if not isinstance(matcher, re.Match):
            return element["chars"]
        value = matcher.groupdict().get("dst", None)
        span = matcher.span()
        if not value:
            return None
        return self.get_chars(element_text, value, element["chars"], span, self.remove_blank)

    @staticmethod
    def get_chars(origin_text, aim_text, chars, span=None, remove_blank=False):
        if span:
            origin_text = origin_text[span[0] :]
            chars = chars[span[0] :]
        start = clean_txt(origin_text, remove_blank=remove_blank).index(clean_txt(aim_text, remove_blank=remove_blank))
        end = start + len(clean_txt(aim_text, remove_blank=remove_blank))
        sp_start, sp_end = index_in_space_string(origin_text, (start, end), remove_blank=remove_blank)
        dst_chars = chars[sp_start:sp_end]
        return dst_chars

    def get_nearby_elements(self, element, ele_type="PARAGRAPH", step=-10, special_pattern=None):
        element_index = element["index"]
        if step > 0:
            above_element_indexes = list(range(element_index + 1, element_index + step + 1))[::-1]
        elif step < 0:
            above_element_indexes = list(range(element_index + step, element_index))[::-1]
        else:
            return []
        element_syllabus_index_start = self.pdfinsight_syllabus.syllabus_dict[element["syllabus"]]["range"][0]
        ret = []
        for idx in above_element_indexes:
            if idx < 0:
                break
            if ele_type == "PARAGRAPH" and idx in self.pdfinsight.table_dict:
                break
            try:
                elt_type, element = self.pdfinsight.find_element_by_index(idx)
            except IndexError:
                break
            if elt_type != ele_type or not element:
                continue
            ret.append(element)
            if idx == element_syllabus_index_start:  # 匹配到章节标题 则停止
                break
            if special_pattern and elt_type == "PARAGRAPH" and special_pattern.search(element["text"]):
                break
        return ret

    @staticmethod
    def is_match(pattern, text, origin_texts=None):
        # 允许feature传入正则表达式, 以"__regex__"为前缀作为区分
        prefix = "__regex__"
        if pattern.startswith(prefix):
            return BaseModel.match_regex(pattern, text, prefix)
        # 从原始文本匹配，保留停用词，分隔符信息。正则以origin开头
        if pattern.startswith("origin__regex__") and origin_texts:
            pattern = pattern[6:]
            return BaseModel.match_regex(pattern, "|".join(origin_texts), prefix)
        return BaseModel.match_text(pattern, text)

    @staticmethod
    def match_regex(pattern, text, prefix):
        texts = text.split("|")
        patterns = [re.compile(p, re.I) for p in pattern.split(prefix) if p]
        texts_match = [[p for p in patterns if p.search(t)] for t in texts]
        if any(m == [] for m in texts_match):
            # some texts mismatch
            return False
        if set(reduce(lambda x, y: x + y, texts_match)) != set(patterns):
            # some patterns mismatch
            return False
        return True

    @staticmethod
    def match_text(pattern, text):
        return pattern == text

    @staticmethod
    def regroup(answers):
        ret = defaultdict(list)
        for answer in answers:
            for col, value in answer.items():
                ret[col].extend(value)
        return ret

    def exclude_content_answer(self, answer_results):
        ret = []
        for answer_result in answer_results:
            belong = False
            elements = self.get_elements_from_answer_result([answer_result])
            for element in elements:
                page = element["page"]
                if self.pdfinsight.is_catalog_page(page):
                    belong = True
                    break
            if not belong:
                ret.append(answer_result)
        return ret

    def exclude_primary_answer(self, answer_results):
        ret = []
        for answer_result in answer_results:
            elements = self.get_elements_from_answer_result([answer_result])
            answer_text = self.get_text_from_answer_result([answer_result])
            if any(self.has_primary_role_title(element, answer_text=answer_text) for element in elements):
                continue
            ret.append(answer_result)
        return ret

    def exclude_skip_answer_value(self, answer_results):
        ret = []
        if isinstance(answer_results[0], dict):
            for answer_result in answer_results:
                new_results = {}
                for key, answers in answer_result.items():
                    if filtered_results := [ans for ans in answers if ans.answer_value != self.skip_answer_value]:
                        new_results[key] = filtered_results
                if not new_results:
                    continue
                ret.append(new_results)
            return ret
        return [ans for ans in answer_results if ans.answer_value != self.skip_answer_value]

    @classmethod
    def get_elements_from_answer_result(cls, answer_results):
        elements = []
        for answer_result in answer_results:
            if isinstance(answer_result, dict):
                for answer in answer_result.values():
                    elements.extend(cls.get_elements_from_answer_result(answer))
            elif isinstance(answer_result, PredictorResult):
                for element_result in answer_result.element_results:
                    if hasattr(element_result, "origin_elements"):
                        elements.extend(element_result.origin_elements)
                    else:
                        elements.append(element_result.element)
        # NoBoxResult has no element
        elements = [i for i in elements if i]
        return elements

    @staticmethod
    def get_common_predictor_results(
        answer_results: list[dict | PredictorResult | PredictorResultGroup],
    ) -> list[PredictorResult]:
        predictor_results = []
        for answer_result in answer_results:
            if isinstance(answer_result, dict):
                for answers in answer_result.values():
                    predictor_results.extend(answers)
            elif isinstance(answer_result, PredictorResultGroup):
                for group in answer_result.groups:
                    predictor_results.extend(group)
            elif isinstance(answer_result, PredictorResult):
                predictor_results.append(answer_result)
        return predictor_results

    @staticmethod
    def get_common_element_results(answer_results) -> list[AnswerResult | ElementResult]:
        element_results = []
        for answer_result in answer_results:
            if isinstance(answer_result, dict):
                for answers in answer_result.values():
                    for answer in answers:
                        element_results.extend(answer.element_results)
            elif isinstance(answer_result, PredictorResult):
                element_results.extend(answer_result.element_results)
        return element_results

    @staticmethod
    def get_text_from_answer_result(answer_results):
        texts = []
        for answer_result in answer_results:
            if isinstance(answer_result, dict):
                for answers in answer_result.values():
                    for answer in answers:
                        texts.extend(answer.text)
            elif isinstance(answer_result, PredictorResult):
                for element_result in answer_result.element_results:
                    texts.append(element_result.text)
        return "\n".join(texts)

    @staticmethod
    def get_enums_from_answer_result(answer_results):
        enums = set()
        for answer_result in answer_results:
            if isinstance(answer_result, dict):
                for answers in answer_result.values():
                    for answer in answers:
                        enums.add(answer.answer_value)
            elif isinstance(answer_result, PredictorResult):
                enums.add(answer_result.answer_value)
        return list(enums)

    def gen_outline_answer(self, answer_elements: List[Dict]) -> List[PredictorResult]:
        answer_results = []
        elements_dict = {i["index"]: i for i in answer_elements}
        answer_groups = []
        for _, value in groupby(enumerate(sorted(elements_dict.keys())), lambda x: x[1] - x[0]):
            answer_groups.append([v for i, v in value])
        answer_groups.sort(key=lambda x: x[0])
        for answer_group in answer_groups:
            answer_elements = []
            for i in answer_group:
                answer_elements.append(elements_dict[i])
            page_box = PdfinsightSyllabus.elements_outline(answer_elements)
            element_results = [
                OutlineResult(page_box=page_box, element=answer_elements[0], origin_elements=answer_elements)
            ]
            answer_result = self.create_result(element_results, column=self.schema.name)
            answer_results.append(answer_result)
        return answer_results

    def get_special_elements(self, element_class):
        return list(self.pdfinsight.elements_iter(lambda x: x["class"] == element_class))

    def get_candidate_elements_by_range(
        self,
        index_range: list[int],
        *,
        exclude_indices: set[int] = None,
        aim_types: set[str] = (PDFInsightClassEnum.PARAGRAPH.value, PDFInsightClassEnum.TABLE.value),
        filter_func: Callable = None,
        need_score: bool = True,
        include_start: bool = False,
        limit: int = 0,
    ) -> list[dict]:
        start, end = index_range
        if not include_start:
            start += 1
        elements, existed_merged_indices = [], set()
        exclude_indices = exclude_indices or set()
        for index in range(start, end):
            if exclude_indices and index in exclude_indices:
                continue
            elem_type, element = self.pdfinsight.find_element_by_index(index)
            if self.pdfinsight.is_skip_element(
                element,
                aim_types=aim_types,
                skip_tbl_unit=True,
            ):
                continue
            if callable(filter_func) and not filter_func(element):
                continue
            if merged_indices := get_keys(element, ["page_merged_paragraph", "paragraph_indices"]):
                # 合并段落只取第1个，否则容易误匹
                first_index = merged_indices[0]
                if index != first_index:
                    if first_index in exclude_indices | existed_merged_indices:
                        continue
                    _, element = self.pdfinsight.find_element_by_index(first_index)
                    existed_merged_indices.add(index)
            if need_score:
                new_element = copy(element)
                new_element["score"] = 0
                elements.append(new_element)
            else:
                elements.append(element)
            if len(elements) >= limit > 0:
                break
        return elements

    def filter_parent_syllabus(self, syllabuses: Iterable[dict], match_full_path: bool = True) -> List[dict]:
        ret = []
        for syllabus in syllabuses:
            if match_full_path:
                syllabus_path = self.pdfinsight_syllabus.full_syll_path(syllabus)
                if self.page_first_as_parent_syllabus:
                    _, syllabus_element = self.pdfinsight.find_element_by_index(syllabus["element"])
                    if syllabus_element and (
                        fake_title := self.pdfinsight.page_chapter_from_first_para.get(syllabus_element["page"])
                    ):
                        fake_syllabus = {"title": fake_title, "parent": -1, "fake": True}
                        if self.parent_must_be_root:
                            syllabus_path = [*[syll for syll in syllabuses if syll["parent"] != -1], fake_syllabus]
                        else:
                            syllabus_path.append(fake_syllabus)

                if not syllabus_path:
                    syllabus_path = [syllabus]
            else:
                syllabus_path = [syllabus]
            title_texts = []
            for item in syllabus_path[::-1]:
                if self.parent_must_be_root and item["parent"] != -1:
                    continue
                title = clear_syl_title(item["title"], remove_bound_num=True)
                # 章节序号和章节之间的空格可能没识别到，要用title[1:]再匹配一次
                # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/3168#note_494834
                title_texts.extend([title, title[1:]])
            if any(self.neglect_parent_features.nexts(item) for item in title_texts):
                # 父章节任意一个匹配上就退出
                if not match_full_path:
                    return []
                continue
            if self.parent_features and not any(self.parent_features.nexts(item) for item in title_texts):
                continue
            ret.append(syllabus)
        return ret

    def get_follow_table_text(self, table, element, follow_element_text):
        if len(table.rows) == 1:
            # 处理段落被误识别成一行N列的表格
            return clean_txt(" ".join(cell.text for cell in table.rows[0]), remove_blank=self.remove_blank)
        if len(table.cols) == 1 and all(self.as_follow_start_pattern.nexts(row[0].text.strip()) for row in table.rows):
            # 处理段落被误识别成1列N行的表格
            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/3168#note_415113
            return clean_txt("\n".join(row[0].text for row in table.rows), remove_blank=self.remove_blank)
        if not any(has_english_chars(cell) for cell in element["cells"].values()):
            # 表格全部是中文，说明是另一半中文页面，跳过
            # http://************:55647/#/project/remark/245409?treeId=8904&fileId=66207&schemaId=28&projectId=17&schemaKey=E(d)(iv)-2&page=37
            return ""
        if P_NOT_SKIP_TABLE.nexts(follow_element_text):
            return ""
        return None

    def find_blow_paras(self, follow_element):
        from remarkable.predictor.hkex_predictor.model_util import find_as_follow_paras

        return find_as_follow_paras(
            self.pdfinsight,
            follow_element,
            self.as_follow_start_pattern,
            ignore_pattern=self.ignore_pattern,
            ignore_syllabus_pattern=self.ignore_syllabus_pattern,
            below_pattern=self.below_pattern,
            neglect_below_pattern=self.neglect_below_pattern,
            max_paragraphs_for_every_follow=self.max_paragraphs_for_every_follow,
            include_start=True,
            need_continuous=self.below_need_continus,
            skip_chinese=self.below_skip_chinese,
        )

    def has_primary_role_title(self, current_element, answer_text=None):
        as_follow_start_regs = AS_FOLLOW_START_PATTERN.patterns
        as_follow_start_regs.extend(self.config.get("special_as_follow_start", []))
        as_follow_start_pattern = PatternCollection(as_follow_start_regs, self.flags)
        clean_text = clean_txt(current_element.get("text", ""), remove_blank=self.remove_blank)
        if matcher := P_PRIMARY_ROLE.nexts(answer_text or clean_text):
            return True
        if not as_follow_start_pattern.nexts(clean_text):
            return matcher
        element_index = current_element["index"]
        current_page = current_element["page"]
        current_syllabus = self.pdfinsight_syllabus.find_by_index(current_element["syllabus"])
        start = 0
        if current_syllabus:
            start = current_syllabus[-1]["range"][0]
        has_skip_start_para = False
        current_page_elements = self.pdfinsight.find_elements_by_page(current_page)
        first_element_in_page = current_page_elements[0] if current_page_elements else None
        for index in range(element_index, start, -1):
            ele_type, element = self.pdfinsight.find_element_by_index(index)
            if not element:
                continue
            if ele_type != "PARAGRAPH":
                continue
            if element["page"] < current_page - 1:
                break
            clean_text = clean_txt(element["text"], remove_blank=self.remove_blank)
            if as_follow_start_pattern.nexts(clean_text):
                has_skip_start_para = True
                continue
            if P_PRIMARY_ROLE.nexts(clean_text):
                return True
            if has_skip_start_para and not as_follow_start_pattern.nexts(clean_text):
                if first_element_in_page and element["index"] == first_element_in_page["index"]:
                    continue
                # 已经跳出as follow 的范围
                return False

        # 最后利用章节判断
        return self.match_syllabus(current_element, P_PRIMARY_ROLE_CHAPTER)


class TableModel(BaseModel):
    target_element = None
    filter_elements_by_target = False

    @property
    def force_use_all_elements(self):
        """
        是否使用所有表格
        """
        return self.get_config("force_use_all_elements", False)

    @property
    def skip_continued_table(self):
        """
        是否跳过续表
        """
        return self.get_config("skip_continued_table", True)

    @property
    def use_all_elements(self):
        """
        初步模型结果过滤后无数据时，是否使用所有表格
        """
        return self.get_config("use_all_elements", False)

    @property
    def num_above_as_title(self) -> int:
        """
        table_above_pattern不为空时生效，取表格标题上方指定个数的段落匹配`table_above_pattern`做补充判断
        注：目前仅TableModel和ParamMatch及其子类适用
        """
        return self.get_config("num_above_as_title", 1)

    @property
    def merge_first_two_element(self) -> int:
        """
        配置num_above_as_title时是否合并当前页面的前两个元素块
        场景： 页面最前面的表格标题被识别成两个元素块   Consolidated Statement of + Financial Position
        http://************:55647/#/project/remark/268060?projectId=17&treeId=44929&fileId=70262&schemaId=29&page=54
        """
        return self.get_config("merge_first_two_element", False)

    @property
    def title_above_pattern(self):
        """
        `num_above_as_title`>0时，对表格标题上方`num_above_as_title`个段落进行匹配
        注：目前仅TableModel和ParamMatch及其子类适用
        """
        return PatternCollection(self.get_config("title_above_pattern"))

    @property
    def title_patterns(self):
        return PatternCollection(self.config.get("title_patterns"), flags=self.flags)

    @property
    def neglect_title_patterns(self):
        return PatternCollection(self.config.get("neglect_title_patterns"), flags=self.flags)

    def train(self, dataset: List[DatasetItem], **kwargs):
        model_data = {}
        for col, col_path in self.columns_with_fullpath():
            for item in dataset:
                for node in self.find_answer_nodes(item, col_path):
                    if node.data is None:
                        continue
                    features = self.extract_feature(item.data["elements"], node.data)
                    model_data.setdefault(col, Counter()).update(features)
        self.model_data = model_data

    def is_target_element(self, element: Dict) -> bool:
        if self.filter_elements_by_target and self.target_element:
            return ElementClassifier.get_type(element) == self.target_element
        # base filter
        return element["class"] == "TABLE"

    def predict_schema_answer(self, elements) -> List[Dict[str, List[PredictorResult]]]:
        raise NotImplementedError

    def extract_feature(self, elements, answer):
        raise NotImplementedError

    def filter_continued_tbl_elements(self, elements):
        continued_indices = set()
        for element in elements:
            if element["index"] in self.pdfinsight.merged_tables:
                continued_indices.update(self.pdfinsight.merged_tables[element["index"]])
        return [e for e in elements if e["index"] not in continued_indices]

    def filter_table_elements(self, elements):
        # 根据表头过滤 需要配置 neglect_title_patterns 或者 title_patterns
        if self.neglect_title_patterns or self.title_patterns:
            elements = self.filter_element_by_title(elements)
        for element in elements:
            element.setdefault("score", 0)
        if self.skip_continued_table:
            elements = self.filter_continued_tbl_elements(elements)
        return elements

    def filter_elements(self, elements, check_syllabus=True):
        table_elements = [element for element in elements if is_table_elt(element)]

        if self.force_use_all_elements or (not elements and self.use_all_elements):
            elements = self.get_all_tables()
            elements_map = {item["index"]: item for item in elements}
            for item in table_elements:
                if item["index"] in elements_map:
                    temp_element = copy(elements_map[item["index"]])
                    temp_element["score"] = item.get("score", 0)
                    elements_map[item["index"]] = temp_element
            elements = elements_map.values()
        else:
            elements = table_elements
        elements = super().base_filter_elements(elements, check_syllabus=check_syllabus)
        # 根据表头过滤 需要配置 neglect_title_patterns 或者 title_patterns
        return self.filter_table_elements(elements)

    @classmethod
    def group_by_pattern(cls, records, pattern, pattern_col_name=None):
        groups = {}
        for record in records:
            headers = record[0] + record[1]
            group_cell = None
            for header_cell in headers:
                header_cell_text = clean_txt(header_cell["text"])
                if pattern.match(header_cell_text):
                    group_cell = header_cell
                    break
            if group_cell:
                groups.setdefault(clean_txt(group_cell["text"]), [([pattern_col_name], [], group_cell)]).append(record)
        return groups

    @staticmethod
    def same_box(cell, box):
        if cell.get("fake"):
            return False
        if cell["page"] != box["page"]:
            return False
        box_outline = (box["box"]["box_left"], box["box"]["box_top"], box["box"]["box_right"], box["box"]["box_bottom"])
        if PdfinsightReader.overlap_percent(cell["box"], box_outline, base="box") < 0.5:
            return False
        return True

    @staticmethod
    def aim_cell(cell, box):
        if cell.get("fake"):
            return False
        if cell["page"] != box["page"]:
            return False
        box_outline = (box["box"]["box_left"], box["box"]["box_top"], box["box"]["box_right"], box["box"]["box_bottom"])
        if PdfinsightReader.overlap_percent(cell["box"], box_outline, base="box") > 0.5:
            return True
        if PdfinsightReader.overlap_percent(cell["box"], box_outline, base="element") > 0.5:
            return True
        return False

    def is_title_above_matched(self, table: ParsedTable) -> bool:
        """
        从表名起向上找指定个数段落（不含表名），判断是否匹配`title_above_pattern`
        """
        above_texts = []
        for elm in table.elements_above[1:]:
            text = clean_txt(elm["text"], remove_blank=self.remove_blank)
            if P_CONTINUED_TITLES.nexts(text):
                continue
            # 遇到章节标题则终止
            if len(above_texts) == self.num_above_as_title or self.pdfinsight.syllabus_reader.is_syllabus_elt(elm):
                break
            above_texts.append(text)
        above_texts.reverse()
        return bool(self.title_above_pattern.nexts("\n".join(above_texts)))

    def filter_element_by_title(self, elements):
        ret = []
        for element in elements:
            table = parse_table(
                element,
                tabletype=TableType.TUPLE.value,
                pdfinsight_reader=self.pdfinsight,
                extra_title_patterns=self.title_patterns.patterns,
            )
            # TODO 在pdfinsgiht中修正title
            if title := find_table_title(self.pdfinsight, element["index"]):
                table_titles = [title]
            else:
                table_titles = [clean_txt(title, remove_blank=self.remove_blank) for title in table.possible_titles]
            if self.neglect_title_patterns and any(self.neglect_title_patterns.nexts(title) for title in table_titles):
                continue
            if not self.title_patterns:
                ret.append(element)
                continue
            if any(self.title_patterns.nexts(title) for title in table_titles) and (
                not self.title_above_pattern or self.is_title_above_matched(table)
            ):
                ret.append(element)
        return ret

    def get_all_tables(self):
        return self.get_special_elements("TABLE")

    def create_content_result(self, element, chars, cells, split_pattern):
        result = CellCharResult(element, chars, cells)
        if not split_pattern:
            return [result]
        return self.create_split_results(split_pattern, result)

    @staticmethod
    def create_split_results(split_pattern, result):
        results = []
        start = 0
        for item in re.split(split_pattern, result.text):
            new_result = deepcopy(result)
            end = start + len(item)
            new_result.chars = result.chars[start:end]
            results.append(new_result)
            start = end + 1  # 增加分隔符的长度,目前约定分隔符的长度为1
        return results


def find_currency_element_result(pdfinsight, result: ElementResult):
    if result.element is None:
        return None
    # 处理"币种"在表格上方段落中的情况
    for elt in pdfinsight.find_elements_near_by(result.element["index"], step=-1, aim_types=("PARAGRAPH",)):
        match = re.search(r"币\s*种\s*[:：]\s*(?P<dst>\w{2,4})\s*$", elt["text"])
        if match:
            return CharResult(elt, elt["chars"][slice(*match.span("dst"))])
    # 处理"币种"在当前段落的情况
    currency_pattern = re.compile(r"(?P<dst>人民币|美元)")
    match = currency_pattern.search(result.element.get("text", ""))
    if match:
        return CharResult(result.element, result.element["chars"][slice(*match.span("dst"))])
    return None


def find_unit_by_element_result(result: ElementResult):
    if result.element is None:
        return None
    if result.element["class"] == "TABLE":
        if not is_table_result(result) or not result.parsed_cells:
            return None
        if isinstance(result, CellCharResult):
            return find_unit_from_text(result, cell=result.parsed_cells[0])
        return result.parsed_cells[0].unit
    if result.element["class"] in ["PARAGRAPH", "PAGE_HEADER", "PAGE_FOOTER"]:
        if not isinstance(result, CharResult):
            return None
        paragraph = result.element
        return find_unit_from_text(result, paragraph=paragraph)
    return None


def get_char_position(char, chars):
    for index, origin_char in enumerate(chars):
        if Rectangle(*char["box"]).overlap_rate(Rectangle(*origin_char["box"])) > 0.9:
            return index
    return None


def select_unit(result, chars, matched):
    matched = list(matched)
    if not matched:
        return None

    if len(matched) == 1:
        return matched[0]

    position = 0
    for char in result.chars:
        _position = get_char_position(char, chars)
        if _position is not None:
            position = _position
            break

    for match in sorted(matched, key=lambda x: x.start()):
        if match.end() >= position:
            return match
    return matched[0]


def find_unit_from_text(result, paragraph=None, cell=None):
    unit_patterns = PatternCollection(
        [
            rf"{result.text}\s*(?P<dst>{UNIT_PATTERN})",
            rf"{result.text}\s*[（（\(]?(?P<dst>{R_PERCENT})[\)）]?",
        ]
    )
    match = None
    if paragraph:
        match = select_unit(result, paragraph["chars"], unit_patterns.finditer(paragraph["text"]))
    if cell:
        match = select_unit(result, cell.raw_cell["chars"], unit_patterns.finditer(cell.text))
    if match:
        unit_slice = slice(*match.span("dst"))
        if paragraph:
            return CharResult(paragraph, paragraph["chars"][unit_slice])
        if cell:
            return CellCharResult(result.element, cell.raw_cell["chars"][unit_slice], [cell])
    return None
