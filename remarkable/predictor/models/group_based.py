from __future__ import annotations

import logging
import re
from collections import defaultdict
from copy import copy
from datetime import datetime
from functools import cached_property
from itertools import chain
from typing import Callable

from remarkable.common.common import get_first_key, get_first_value, get_keys, is_paragraph_elt
from remarkable.common.constants import AnswerV<PERSON><PERSON><PERSON>num
from remarkable.common.pattern import <PERSON><PERSON><PERSON><PERSON>, PatternCollection
from remarkable.common.util import clean_txt, is_in_range
from remarkable.models.file_share_meta import GroupInfo, GroupItem
from remarkable.plugins.predict.common import get_element_candidates
from remarkable.predictor.common_pattern import P_INCENTIVE_AWARD, P_YEAR
from remarkable.predictor.hkex_predictor.pattern import R_DATES_FOR_EXTRACT
from remarkable.predictor.hkex_predictor.schemas.pattern import (
    P_DR_CHAPTER,
    P_NS_DATE,
    P_REPORT_YEAR,
)
from remarkable.predictor.models.base_model import BaseModel
from remarkable.predictor.schema_answer import (
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    Predictor<PERSON><PERSON>ult,
    PredictorR<PERSON>ult<PERSON>roup,
    TableCellsResult,
)
from remarkable.predictor.share_group_gpt import DEFAULT_GROUP_KEY_MAP, SPECIAL_GROUP_KEYS
from remarkable.predictor.share_group_utils import extract_group_key, is_elem_in_group
from remarkable.predictor.utils import get_element_text, subtract_keys

logger = logging.getLogger(__name__)


P_KEYWORD_MAP = {
    "option": re.compile(r"\boptions|期權", re.I),
    "award": MatchMulti.compile(
        r"\brestricted|\baward", r"\bRS[UA]", r"\b[AH]\s*shares", *P_INCENTIVE_AWARD.patterns, operator=any
    ),
}

# https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/4571#note_521168
P_SINCE_DATE = PatternCollection(
    [
        rf"\b(nor?)\s*([a-z]+\s*|options?\s*(and|or|and/or)\s*)?(awards|rsus|shares)[^.:：;；,，]+(since|from|after)\s*{dt}"
        for dt in R_DATES_FOR_EXTRACT
    ],
    flags=re.I,
)

P_FROM_DATE = PatternCollection([rf"(since|from|after)\s*{dt}" for dt in R_DATES_FOR_EXTRACT], flags=re.I)
# 排除and: https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/4697#note_527675
# P_END_DATE = PatternCollection([rf"(?<!and )(as\s*at|as\s*of|end(ed)?|\bup\s*to)\s*{dt}" for dt in R_DATES], flags=re.I)
P_END_DATE = PatternCollection([rf"years?\s*end(ed)?\s*{dt}" for dt in R_DATES_FOR_EXTRACT], flags=re.I)
P_AND_YEAR = re.compile(r"during.+\band\s*(?P<year>\d{4}\b)", re.I)


class GroupBased(BaseModel):
    def train(self, dataset, **kwargs):
        pass

    @property
    def column_configs(self):
        return self.get_config("column_configs", [] if self.no_column else {})

    @property
    def no_column(self):
        """
        no_column=Tre，代表没有子项，column_configs直接配置多个model的列表
        场景参考规则丨H83-92
        """
        return self.get_config("no_column")

    @property
    def share_type(self):
        share_type = self.get_config("share_type", "")
        if share_type not in ["option", "award"]:
            raise ValueError(f"share_type should be option or award, but got {share_type}")
        return share_type

    @property
    def pick_answer_strategy(self):
        """Strategy to pick predicted answers by models

        :return: pick strategy: single, all
        :rtype: str
        """
        strategy = self.config.get("pick_answer_strategy", "single")
        if strategy not in ["single", "all"]:
            raise ValueError(f"pick_answer_strategy should be single or all', but got {strategy}")
        return strategy

    @property
    def group_default_enum(self):
        return self.config.get("group_default_enum", AnswerValueEnum.ND.value)

    @property
    def filter_group_answer(self):
        """
        需要检查答案是否在分组信息的范围中
        """
        return self.config.get("filter_group_answer", False)

    @property
    def check_consumed(self):
        """
        同一个元素块是否只能出现在一个分组中
        """
        return self.config.get("check_consumed", False)

    @property
    def follow_first_enum(self) -> AnswerValueEnum:
        """
        假设配置了值为`NS`，表示当第一个schema为NS，其余schema全为NS
        """
        enum = self.config.get("follow_first_enum")
        if enum and not any(enum == member.value for member in AnswerValueEnum):
            raise ValueError("`follow_first_enum` must be a value from `AnswerValueEnum`")
        return enum

    @property
    def filter_candidates(self) -> Callable:
        """
        指定函数过滤元素块，注意：模型配置了YodaLayer且threshold=1时不生效
        """
        return self.config.get("filter_candidates")

    @property
    def post_process_answers(self) -> Callable:
        """
        后处理函数，配置了该函数会据此对每个schema的结果进行后处理
        """
        return self.config.get("post_process_answers")

    @property
    def year_start(self):
        """
        财报期初
        """
        return self.predictor.prophet.metadata.get("year_start") or ""

    @property
    def year_end(self):
        """
        财报期末
        """
        return self.predictor.prophet.metadata.get("year_end") or ""

    @property
    def share_group_info(self) -> dict[str, GroupInfo]:
        return self.predictor.prophet.metadata.get("share_group", {}).get(self.share_type) or {}

    @property
    def share_group(self) -> dict[str, list[GroupItem]]:
        return {key: info.items for key, info in self.share_group_info.items()}

    # @cached_property
    # def grouped_elem_ranges(self):
    #     """
    #     所有已被分组的元素块索引范围，用于排除初步定位元素块
    #     """
    #     return list(chain.from_iterable([it.range for it in items] for items in self.share_group.values())) + list(
    #         chain.from_iterable([it.range for it in items] for items in self.another_share_group.values())
    #     )

    @staticmethod
    def sort_group_func(sort_item: tuple[str, GroupInfo]):
        group_key, info = sort_item
        if not info.adopt_year and (matched := P_YEAR.search(group_key)):
            info.adopt_year = matched.group("year") or ""
        adoption = ""
        if info.adopt_year or info.adopt_days:
            adoption = info.adopt_year or (max(info.adopt_days) if info.adopt_days else "")
        return not info.is_subsidiary, adoption, -min(item.index for item in info.items)

    @cached_property
    def sorted_share_group(self):
        """
        给group的分组排序，优先总公司，子公司次之
        总公司内按照日期排序，排序规则：
        1. 先提取group key中的年份，按降序排
        2. 再取adopted day中最大日期，降序排序
        https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/4300
        """
        return [
            (item[0], item[1].items)
            for item in sorted(self.share_group_info.items(), key=self.sort_group_func, reverse=True)
        ]

    @property
    def another_share_type(self):
        return "option" if self.share_type == "award" else "award"

    @property
    def another_share_group_info(self) -> dict[str, GroupInfo]:
        return get_keys(self.predictor.prophet.metadata, ["share_group", self.another_share_type], default={})

    @property
    def another_share_group(self) -> dict[str, list[GroupItem]]:
        share_type = "option" if self.share_type == "award" else "award"
        return {
            key: info.items
            for key, info in get_keys(self.predictor.prophet.metadata, ["share_group", share_type], default={}).items()
        }

    @property
    def share_alias(self) -> dict[str, set[str]]:
        return {key: info.aliases for key, info in self.share_group_info.items()}

    @property
    def share_subsidiary(self) -> dict | None:
        return self.predictor.prophet.metadata.get("share_subsidiary", {}).get(self.share_type)

    @cached_property
    def children_models(self):
        models = {}
        if self.no_column:
            models[self.schema.name] = [
                self.predictor.create_model(model, self.schema) for model in self.column_configs
            ]
            return models
        for column, model_configs in self.column_configs.items():
            schema = self.predictor.schema.children_schema(column)
            # 因为在配置文件中指定了fake_leaf 这里需要显式修改predictor的column
            self.predictor.columns = [column]
            models[column] = [self.predictor.create_model(model_config, schema) for model_config in model_configs]
        return models

    def predict_schema_answer(self, elements: list[dict]):
        consumed_element_indexes = defaultdict(set)
        if not self.share_group:
            group_answer = self.predict_group_answer([], consumed_element_indexes)
            if self.no_column:
                return group_answer
            answer_result = PredictorResultGroup([group_answer], schema=self.predictor.schema)
            return [{self.schema.name: [answer_result]}]
        result_groups = []
        for group_key, group_elements in self.sorted_share_group:
            #  根据 group 和 score_elements 重新生成 备选元素块
            candidate_elements = self.get_elements_from_group(group_elements)
            # 生成一组答案
            group_answer = self.predict_group_answer(
                candidate_elements, consumed_element_indexes, group_key if len(self.share_group) > 1 else None
            )
            result_groups.append(group_answer)
            if self.no_column:
                # TODO 这种场景目前只针对丨H83-92和丨H38-41，只返回第一组答案，后续若有需要再改造
                break
        if self.share_subsidiary and not self.no_column:
            result_groups.extend(self.generate_subsidiaries_results())
        if self.no_column:
            # TODO 这种场景目前只针对丨H83-92和丨H38-41，只返回第一组答案，后续若有需要再改造
            return result_groups[0] if result_groups else []
        answer_result = PredictorResultGroup(result_groups, schema=self.predictor.schema)
        return [{self.schema.name: [answer_result]}]

    @staticmethod
    def reset_group_elements_score(elements, scores_dict):
        """
        从分组信息获取的元素块，如果在初步定位结果中，则设置对应分值
        """
        result = []
        for element in elements:
            if element["index"] in scores_dict:
                new_elem = copy(element)
                new_elem["score"] = scores_dict[element["index"]]
                result.append(new_elem)
            else:
                result.append(element)
        result.sort(key=lambda x: x.get("score", 0), reverse=True)
        return result

    def correct_incorrect_group_keys(self, group_key) -> tuple[set, set]:
        incorrect_group_keys = set(DEFAULT_GROUP_KEY_MAP[self.another_share_type])
        if not group_key:
            return set(DEFAULT_GROUP_KEY_MAP[self.share_type]), incorrect_group_keys
        incorrect_group_keys |= set(
            chain.from_iterable({key} | info.aliases for key, info in self.another_share_group_info.items())
        )
        if group_key:
            incorrect_group_keys |= subtract_keys(
                {key for key in self.share_group if key != group_key}
                | set(chain.from_iterable(aliases for key, aliases in self.share_alias.items() if key != group_key)),
                SPECIAL_GROUP_KEYS,
            )
        return {group_key} | self.share_alias.get(group_key, set()), incorrect_group_keys

    def predict_group_answer(
        self,
        candidate_elements: list[dict],
        consumed_element_indexes: dict[str, set[int]],
        group_key: str | None = None,
    ):
        answer_results = []
        first_col_answers = None
        correct_group_keys, incorrect_group_keys = self.correct_incorrect_group_keys(group_key)
        for col_idx, (column, col_models) in enumerate(self.children_models.items()):
            schema = self.schema if self.no_column else self.predictor.schema.children_schema(column)
            if first_col_answers:
                # 配置了follow_first_enum，直接取结果
                for answer in first_col_answers:
                    answer_results.append(
                        self.create_result(
                            element_results=answer.element_results, schema=schema, value=self.follow_first_enum
                        )
                    )
                if self.share_group:
                    logger.debug(
                        f"------schema: {column}, group_key: {group_key}, followed first enum: {self.follow_first_enum}"
                    )
                else:
                    logger.debug(f"------schema: {column}, followed first enum: {self.follow_first_enum}")
                continue
            for index, model in enumerate(col_models):
                self.set_reference_conf(model, correct_group_keys, incorrect_group_keys)
                score_elements, score_map = self.get_candidate_elements(
                    column, model, correct_group_keys, incorrect_group_keys
                )
                crude_indices = {elem["index"] for elem in score_elements}
                if group_key:
                    # 有分组时，优先取candidate_elements与初步定位有交集的元素块
                    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/3573#note_459443
                    sorted_cand_elements = self.reset_group_elements_score(candidate_elements, score_map)
                    cand_indices = {elem["index"] for elem in sorted_cand_elements}
                    # TODO 模型中的threshold只对初步定位生效，在这里从分组中获取的元素块还在
                    elements = sorted_cand_elements + [
                        elm for elm in score_elements if elm["index"] not in cand_indices
                    ]
                    sort_by_score = False
                else:
                    # 在初步预测结果中的答案设置分值
                    sorted_cand_elements = self.reset_group_elements_score(candidate_elements, score_map)
                    elements = score_elements + [
                        elem for elem in sorted_cand_elements if elem["index"] not in crude_indices
                    ]
                    sort_by_score = True
                if callable(self.filter_candidates):
                    elements = self.filter_candidates(self.pdfinsight, elements)
                if self.check_consumed and (indexes := consumed_element_indexes.get(column, [])):
                    elements = [item for item in elements if item["index"] not in indexes]
                if self.dr_chapter_first or model.dr_chapter_first:
                    # share option要求优先DR章节
                    elements = self.sort_candidates_by_chapter(elements)
                model_answers = model.predict(elements, sort_by_score=sort_by_score)
                # 恢复配置
                self.set_reference_conf(model, None)
                if self.pick_answer_strategy == "single" and model_answers:
                    if self.filter_group_answer:
                        model_answers = self.filter_answer_by_group(
                            model_answers, group_key, correct_group_keys, incorrect_group_keys
                        )
                        if not model_answers:
                            continue
                    indexes = {item["index"] for item in self.get_elements_from_answer_result(model_answers)}
                    if self.check_consumed and indexes.intersection(consumed_element_indexes[column]):
                        continue
                    consumed_element_indexes[column].update(indexes)

                    model_answers = self.get_common_predictor_results(model_answers)
                    group_info = None
                    if group_key:
                        group_info = self.share_group_info[group_key]
                    elif self.share_group_info:
                        group_info = get_first_value(self.share_group_info)
                    if not (model_answers := self.filter_no_grant_ns_answers(model, model_answers, group_info)):
                        continue
                    if callable(self.post_process_answers):
                        # 若配置了后处理函数，依据后处理函数处理answers
                        model_answers = self.post_process_answers(model_answers, model, schema, self.pdfinsight)
                    # 如果model开启了multi_elements，可能会有多组答案，合并
                    if len(model_answers) > 1:
                        # 如果多个答案的枚举值相同，指定枚举值
                        # http://100.64.0.105:55647/#/project/remark/231557?treeId=4700&fileId=66215&schemaId=15&projectId=17&schemaKey=B76
                        answer_value = (
                            model_answers[0].answer_value
                            if len({ans.answer_value for ans in model_answers}) == 1
                            else None
                        )
                        model_answers = [
                            self.create_result(
                                list(chain.from_iterable(ans.element_results for ans in model_answers)),
                                value=answer_value,
                                schema=schema,
                            )
                        ]
                    # 后处理后可能结果为[]
                    if not model_answers:
                        continue
                    if col_idx == 0 and self.follow_first_enum:
                        first_col_answers = [ans for ans in model_answers if ans.answer_value == self.follow_first_enum]
                    answer_results.extend(model_answers)
                    if self.share_group:
                        logger.debug(
                            "------schema: %s, group_key: %s, effected model: ===%s===, order: ===%s===",
                            column,
                            group_key or get_first_key(self.share_group),
                            model.name,
                            index + 1,
                        )
                    else:
                        logger.debug(
                            "------schema: %s, effected model: ===%s===, order: ===%s===", column, model.name, index + 1
                        )
                    break
            else:
                # 确保每个column有默认值
                answer_results.append(self.create_result([], schema=schema, value=self.group_default_enum))
        return answer_results

    def sort_candidates_by_chapter(self, elements):
        """
        dr_chapter_first=True时，依据元素块是否属于DR章节来排序所有元素块
        """
        dr_elements, other_elements = [], []
        for elem in elements:
            root_chapters = set()
            if parents := self.pdfinsight.find_syllabuses_by_index(elem["index"]):
                root_chapters.add(parents[0]["title"])
            if page_chapter := self.pdfinsight.page_chapter_from_first_para.get(elem["page"]):
                root_chapters.add(page_chapter)
            if any(P_DR_CHAPTER.search(title) for title in root_chapters):
                dr_elements.append(elem)
            else:
                other_elements.append(elem)
        return dr_elements + other_elements

    def set_reference_conf(self, model, correct_group_keys=None, incorrect_group_keys=None):
        """
        动态配置Reference的属性`group_key`, `share_type`
        注意：使用完后，必须调用该函数，将配置复位为None
        """
        if model.name != "reference":
            return
        if correct_group_keys:
            model.config["share_type"] = self.share_type
            model.config["share_group"] = self.share_group
            model.config["correct_group_keys"] = correct_group_keys
            model.config["incorrect_group_keys"] = incorrect_group_keys
        else:
            for key in ("group_key", "share_type", "correct_group_keys", "incorrect_group_keys"):
                model.config[key] = None

    def generate_subsidiaries_results(self):
        """
        特殊场景：仅提到不重要的子公司也采用了share option，需要圈选这段文本，并标注为NS
        https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/3659#note_436991
        """
        if not self.share_subsidiary:
            return []

        group_dict = self.share_subsidiary
        ret = []
        element, start, end, count = group_dict["element"], group_dict["start"], group_dict["end"], group_dict["count"]
        for _ in range(count):
            group_answer = []
            for column in self.column_configs:
                # page_box = self.pdfinsight_syllabus.elements_outline([element])
                group_answer.append(
                    self.create_result(
                        [CharResult(element, chars=element["chars"][start:end])],
                        value=AnswerValueEnum.NS.value,
                        schema=self.predictor.schema.children_schema(column),
                    )
                )
            ret.append(group_answer)
        return ret

    def filter_no_grant_ns_answers(
        self, model: BaseModel, answers: list[PredictorResult], group_info: GroupInfo = None
    ) -> list[PredictorResult]:
        """
        B74~B78的NS场景，除了满足no shares granted之外，采纳日必须包含于句子中提到的年份近1年内
        https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/4017
        http://100.64.0.105:55647/#/project/remark/238215?treeId=42600&fileId=67325&schemaId=15&projectId=17&schemaKey=B74
        """
        new_results = []
        has_date = False
        # 需要判断此逻辑的model，都配置固定model_id="ns_no_grant"
        if not (model_id := model.config.get("model_id")) or model_id not in {
            "ns_since_adoption",
            "ns_during_the_year",
        }:
            return answers
        # if self.predictor.schema.name not in {"B63.1", "B74", "B75", "B76", "B77", "B78"}:
        #     return answers
        check_since = model_id == "ns_since_adoption"
        if not answers or (check_since and not group_info or not (group_info.adopt_days or group_info.adopt_year)):
            return answers
        for predict_result in answers:
            if predict_result.answer_value != AnswerValueEnum.NS.value:
                new_results.append(predict_result)
                continue
            text = clean_txt(predict_result.text)
            if P_NS_DATE.search(text):
                new_results.append(predict_result)
                continue
            if matched := P_FROM_DATE.nexts(text):
                has_date = True
                # 场景1: 提到from/since xxxx年x月x日，则采纳日必须>=这个日期
                start_date = self.extract_date(matched)
                if self.is_adoption_in_the_year(group_info, start_date) or (
                    not check_since and start_date == self.year_start
                ):
                    new_results.append(predict_result)
                    continue
            elif matched := P_END_DATE.nexts(text):
                has_date = True
                # 场景2：提到during the year ended xxx，则采纳日必须在以这个日期为结束的1年期间
                end_date = self.extract_date(matched)
                year, month, _ = map(int, end_date.split("-"))
                start_date = f"{year}-01-01" if month == 12 else f"{year - 1}-{str(month + 1).zfill(2)}-01"
                if self.is_adoption_in_the_year(group_info, start_date, end_date) or (
                    not check_since and end_date == self.year_end
                ):
                    new_results.append(predict_result)
                    continue
            elif check_since and P_REPORT_YEAR.search(text):
                has_date = True
                # 场景3: 提到 during the reporting/annual period，必须满足采纳日在财报期初与期末之间
                # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/4571#note_521508
                if self.year_end and self.is_adoption_in_the_year(group_info, self.year_start, self.year_end):
                    new_results.append(predict_result)
                    continue
            if check_since and (matched := P_AND_YEAR.search(text)):
                has_date = True
                # 场景4: 提到and xxxx年，则采纳日必须在这年期间
                # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/4697#note_527700
                start_date = f"{matched.group('year')}-01-01"
                end_date = f"{matched.group('year')}-12-31"
                if self.is_adoption_in_the_year(group_info, start_date, end_date):
                    new_results.append(predict_result)
                    continue
            # TODO 暂未遇到这种场景，若遇到，要求expired date<end date
            # if matched := P_END_DATE.nexts(text):
            #     has_date = True
            #     # 场景3: 提到as at/ended/up to xxxx年x月x日，则采纳日必须在这个日期-1年~日期期间
            #     # http://100.64.0.105:55647/#/project/remark/250763?treeId=9450&fileId=68583&schemaId=15&projectId=9450&schemaKey=B74 P243 index=4112
            #     end_date = self.extract_date(matched)
            #     start_date = (
            #         f"{end_date[:4]}-01-01"
            #         if end_date[5:7] == "12"
            #         else f"{int(end_date[:4]) - 1}-{str(int(end_date[5:7]) + 1).zfill(2)}-01"
            #     )
            #     if self.is_valid_ns_answer(group_info, start_date, end_date):
            #         new_results.append(predict_result)
            #         continue
            if not has_date:
                new_results.append(predict_result)
        return new_results

    @staticmethod
    def extract_date(matched):
        return datetime.strptime(
            f"{matched.group('day')} {matched.group('mon')} {matched.group('year')}", "%d %b %Y"
        ).strftime("%Y-%m-%d")

    @staticmethod
    def is_adoption_in_the_year(group_info: GroupInfo, start_date: str, end_date: str = None) -> bool:
        if group_info.adopt_days:
            if any(day >= start_date and (not end_date or day <= end_date) for day in group_info.adopt_days):
                return True
        if group_info.adopt_year:
            years = {start_date[:4]} | ({end_date[:4]} if end_date else set())
            if {group_info.adopt_year, f"{int(group_info.adopt_year) - 1}"} & years:
                return True
        return False

    def get_elements_from_group(self, groups: list[GroupItem]):
        res = []
        existed_indices = set()
        for group in groups:
            elements = self.get_candidate_elements_by_range(
                group.range, exclude_indices=existed_indices, filter_func=self.is_valid_candidate
            )
            existed_indices.update(e["index"] for e in elements)
            res.extend(elements)
        return res

    def is_valid_candidate(self, element):
        """
        过滤超出报告期末的段落
        TODO 可能误删
        """
        if is_paragraph_elt(element):
            text = clean_txt(element["text"])
            if matched := P_SINCE_DATE.nexts(text):
                if f"{matched.group('year')}-{matched.group('mon')}-{matched.group('day')}" >= self.year_end:
                    return False
        return True

    def is_keyword_element(self, element):
        """
        https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/3620#note_482159
        元素块在another_group的分组中，但元素块中有当前share_type的关键词，例如option，则不过滤元素块
        """
        text = clean_txt(element.get("text") or "")
        return bool(P_KEYWORD_MAP[self.share_type].search(text))

    @staticmethod
    def is_elem_in_group_ranges(share_group: dict[str, list[GroupItem]], element: dict):
        if not share_group:
            return False
        group_ranges = [item.range for item in chain(*share_group.values())]
        return any(is_in_range(element["index"], rng) for rng in group_ranges)

    def get_candidate_elements(
        self, column: str, model: BaseModel, correct_group_keys: set[str], incorrect_group_keys: set[str]
    ) -> (list, dict):
        crude_answer_path = [self.schema.name] if self.no_column else [self.schema.name, column]
        candidates = get_element_candidates(
            self.predictor.prophet.crude_answer,
            crude_answer_path,
            priors=self.config.get("element_candidate_priors", []),
            limit=self.config.get("element_candidate_count", 10),
        )
        score_map = {}
        candidate_elements = []
        location_threshold = self.config.get("location_threshold") or 0
        for item in candidates:
            index, score = item["element_index"], item["score"]
            score_map[index] = score
            _, ele = self.pdfinsight.find_element_by_index(index)
            if not ele or item["score"] < location_threshold or self.is_valid_candidate(ele):
                continue
            new_elem = copy(ele)
            new_elem["score"] = score
            candidate_elements.append(new_elem)
        # 根据模型配置过滤候选元素块
        result_elements = []
        for elem in model.filter_elements(candidate_elements):
            if not is_elem_in_group(
                elem, self.pdfinsight, self.share_type, self.share_group, correct_group_keys, incorrect_group_keys
            ):
                continue
            result_elements.append(elem)
        return result_elements, score_map

    def filter_answer_by_group(
        self, model_answers, group_key: str, correct_group_keys: set[str], incorrect_group_keys: set[str]
    ):
        if len(self.share_group) <= 1:
            return model_answers
        default_keys = set(DEFAULT_GROUP_KEY_MAP[self.share_type]) | SPECIAL_GROUP_KEYS

        group_items = self.share_group[group_key] if group_key else []
        filtered_answers = []
        for model_answer in self.get_common_predictor_results(model_answers):
            filtered_results = []
            for elem_res in model_answer.element_results:
                elements = elem_res.origin_elements if hasattr(elem_res, "origin_elements") else [elem_res.element]
                # 先从结果句子中找关键词，再从单元格对应的行名+列名中找，最后在结果所在元素块找关键词
                answer_keys = extract_group_key(elem_res.text, self.share_type, multi=True) or set()
                if not answer_keys and isinstance(elem_res, TableCellsResult):
                    for header in chain.from_iterable(cell.headers for cell in elem_res.parsed_cells):
                        answer_keys.update(
                            extract_group_key(clean_txt(header.text, remove_cn_text=True), self.share_type, multi=True)
                        )
                if not answer_keys:
                    answer_keys = set(
                        chain.from_iterable(
                            extract_group_key(get_element_text(elem, self.pdfinsight), self.share_type, multi=True)
                            for elem in elements
                        )
                    )
                if answer_keys:
                    if not (correct_group_keys & answer_keys) and (answer_keys & incorrect_group_keys):
                        # 提取结果句子中的关键词，提到是group_key以外的key则丢弃
                        continue
                    # 提取到的group_key是默认组且默认组不在group_info中
                    if answer_keys.issubset(correct_group_keys) or answer_keys.issubset(
                        subtract_keys(SPECIAL_GROUP_KEYS | default_keys, incorrect_group_keys)
                    ):
                        filtered_results.append(elem_res)
                        continue
                # 通过index检查是否在分组中
                if any(any(is_in_range(element["index"], item.range) for element in elements) for item in group_items):
                    filtered_results.append(elem_res)
                    continue
                filtered_results.append(elem_res)
            if filtered_results:
                model_answer.element_results = filtered_results
                filtered_answers.append(model_answer)
        return filtered_answers
