import logging
import re
from collections import defaultdict
from enum import IntEnum
from typing import Match

from remarkable.common.common import get_keys, is_paragraph_elt
from remarkable.common.common_pattern import P_CONTINUED
from remarkable.common.constants import Schema<PERSON><PERSON>, TableType
from remarkable.common.pattern import PatternCollection
from remarkable.common.util import P_CHNS, clean_txt, index_in_space_string, split_paragraph
from remarkable.pdfinsight import clear_syl_title
from remarkable.pdfinsight.parser import parse_table
from remarkable.pdfinsight.reader import PdfinsightSyllabus
from remarkable.predictor.eltype import ElementType
from remarkable.predictor.hkex_predictor.model_util import find_as_follow_tables
from remarkable.predictor.hkex_predictor.schemas.pattern import P_ONLY_DATE
from remarkable.predictor.models.base_model import AS_FOLLOW_PATTERN, BaseModel
from remarkable.predictor.schema_answer import (
    AnswerResult,
    CharResult,
    ParagraphResult,
)
from remarkable.predictor.utils import (
    get_chars_text_from_outline,
    is_correct_spelling_sentence,
    make_pattern,
)

logger = logging.getLogger(__name__)


class AsFollowType(IntEnum):
    PARA = 0  # 只要paragraph，默认值
    ANY = 1  # table和paragraph任意一个都可以（会带上as follow段落）
    TABLE = 2  # 只要table
    TABLE_NO_TITLE = 3  # 要table且不带表名


class ParaMatch(BaseModel):
    target_element = ElementType.PARAGRAPH
    base_all_elements = True
    filter_elements_by_target = True

    @property
    def combine_paragraphs(self):
        """
        拼接多个段落为一个答案, 不分组(适用于PDFinsight把一个段落拆成多个元素块的情况)
        """
        return self.get_config("combine_paragraphs", False)

    @property
    def para_separator(self):
        """
        按照该正则拆分段落进行匹配（一般用来匹配句子）
        """
        value = self.get_config("para_separator", None)
        if value and not isinstance(value, re.Pattern):
            raise ValueError(f"{value} is not a valid regex")
        return value

    @property
    def split_pattern(self):
        """
        目前分隔符的长度必须为1
        """
        return self.get_config("split_pattern", None)

    @property
    def syllabus_neglect_pattern(self):
        """
        父章节黑名单：如果父章节的标题匹配到黑名单中的任意一个，则忽略该元素块
        """
        return PatternCollection(self.get_config("syllabus_neglect_pattern", None), flags=self.flags)

    def paragraph_pattern(self, col=None):
        regs = self.get_config("paragraph_pattern", column=col)
        return make_pattern(regs, flags=self.flags)

    def sentence_pattern(self, col=None):
        regs = self.get_config("sentence_pattern", column=col)
        if regs and not self.para_separator:
            raise ValueError(f"rule={self.schema.name}: 'para_separator' must be configured!")
        return make_pattern(regs, flags=self.flags)

    def neglect_pattern(self, col=None):
        regs = self.get_config("neglect_pattern", column=col)
        return make_pattern(regs, flags=self.flags)

    def neglect_sentence_pattern(self, col=None):
        """
        配置了para_separator时生效，针对切开的子句排除关键词

        """
        regs = self.get_config("neglect_sentence_pattern", column=col)
        if regs and not self.para_separator:
            raise ValueError(f"rule={self.schema.name}: 'para_separator' must be configured!")
        return make_pattern(regs, flags=self.flags)

    def include_anchor(self, col=None):
        return self.get_config("include_anchor", default=False, column=col)

    def anchor_regs(self, col=None):
        regs = self.get_config("anchor_regs", column=col)
        return make_pattern(regs, flags=self.flags)

    def current_regs(self, col=None):
        regs = self.get_config("current_regs", column=col)
        return make_pattern(regs, flags=self.flags)

    @property
    def use_all_elements(self):
        return self.get_config("use_all_elements", False)

    @property
    def force_use_all_elements(self):
        return self.get_config("force_use_all_elements", False)

    @property
    def default_follows(self):
        if self.get_config("need_default_follows", False):
            return PatternCollection(AS_FOLLOW_PATTERN.patterns, flags=self.flags)
        return PatternCollection(None)

    @property
    def as_follow_pattern(self):
        as_follow_pattern = self.get_config("as_follow_pattern", [])
        return PatternCollection(as_follow_pattern, flags=self.flags)

    @property
    def as_follow_type(self) -> AsFollowType:
        """
        当配置了as_follow_pattern，取表格还是句子
        """
        return self.get_config("as_follow_type", AsFollowType.PARA)

    @property
    def as_follow_near_offset(self) -> int:
        """
        配置了as_follow_pattern时，在初步定位元素块周围尝试匹配下
        http://100.64.0.105:55647/#/project/remark/244798?treeId=5614&fileId=66818&schemaId=28&projectId=17&schemaKey=B(a)
        """
        return self.get_config("as_follow_near_offset", 0)

    @property
    def any_below_pattern(self):
        """
        as following下的条目中，至少有一条满足这个正则才取，否则整个都不取
        """
        pattern = self.get_config("any_below_pattern")
        return PatternCollection(pattern, self.flags)

    @property
    def min_below_count(self):
        """
        配置了as_follow时，不算标题在内的as follow的最小个数
        """
        return self.get_config("min_below_count", 1)

    @property
    def threshold(self):
        return self.get_config("threshold", 0)

    @property
    def enum_from_multi_element(self):
        """是否把所有结果合并一起判断枚举"""
        return self.get_config("enum_from_multi_element", False)

    def is_valid_chapter(self, element, check_parent=False):
        syllabus_titles = [s["title"] for s in self.pdfinsight_syllabus.find_by_elt_index(element["index"])]
        if any(
            self.syllabus_neglect_pattern.nexts(clear_syl_title(title, remove_bound_num=True, remove_prefix=True))
            for title in syllabus_titles
        ):
            logger.warning(f"neglect syllabus title matched: {syllabus_titles}")
            return False
        if check_parent:
            if not self.filter_elements_by_syllabus_regs([element]):
                return False
            if not self.filter_elements_by_parent_syllabuses([element]):
                return False
        return True

    def train(self, dataset, **kwargs):
        pass

    def print_model(self):
        pass

    def filter_target_elements(self, elements):
        if not elements and not self.get_config("use_crude_answer", True):
            index_range = self.get_config("index_range", (0, 20))
            elements = self.pdfinsight.find_elements_near_by(index_range[0], amount=(index_range[1] - index_range[0]))
        return [e for e in elements if self.is_target_element(e)]

    def create_para_results(self, element):
        return [ParagraphResult(element, element.get("chars") or [])]

    def _create_content_result(self, element, matched: Match | bool, offset: int = 0) -> list[AnswerResult]:
        chars = element.get("chars") or []
        if isinstance(matched, Match) and "content" in matched.groupdict():
            start, end = matched.span("content")
            sp_start, sp_end = index_in_space_string(
                element["text"], (start + offset, end + offset), remove_blank=self.remove_blank
            )
            char_result = CharResult(element, chars[sp_start:sp_end], start=sp_start, end=sp_end)
            if self.split_pattern:
                return self.create_split_results(char_result)
            return [char_result]
        return [ParagraphResult(element, chars)]

    def create_content_result(self, element, regs: PatternCollection, col=None) -> list[AnswerResult]:
        all_chars = element.get("chars") or []
        text = element["text"]
        if self.para_separator:
            results = []
            neglect_sen_patten = self.neglect_sentence_pattern(col)
            for sentence, pos in split_paragraph(text, separator=self.para_separator, need_pos=True):
                sentence = clean_txt(sentence, remove_blank=self.remove_blank)
                if sentence_matched := regs.nexts(sentence):
                    start, end = pos
                    if neglect_sen_patten and neglect_sen_patten.nexts(sentence):
                        continue
                    if isinstance(sentence_matched, re.Match) and "content" in sentence_matched.groupdict():
                        results.extend(self._create_content_result(element, sentence_matched, offset=start))
                    else:
                        if start == 0 and end == len(text):
                            # 如果匹配了整段，直接取段落，以确保框线
                            return [ParagraphResult(element, all_chars)]
                        results.append(CharResult(element, all_chars[start:end], start=start, end=end))
                    if results and not self.multi:
                        return results
            return results
        else:
            matched = regs.nexts(clean_txt(text, remove_blank=self.remove_blank))
            if not matched:
                return []
            return self._create_content_result(element, matched)

    def create_split_results(self, char_result):
        results = []
        start = 0
        for item in re.compile(self.split_pattern).split(char_result.text):
            end = start + len(item)
            chars = char_result.chars[start:end]
            results.append(CharResult(char_result.element, chars, start=start, end=end))
            start = end + 1  # 增加分隔符的长度
        return results

    def match_result_by_prev_anchor(self, current_regs, anchor_regs, element, col=None) -> list[AnswerResult] | None:
        if not current_regs or not anchor_regs:
            return None

        prev_elements = self.pdfinsight.find_elements_near_by(
            element["index"], step=-1, amount=1, aim_types=["PARAGRAPH"]
        )
        if not prev_elements:
            return None
        prev_elt = prev_elements[0]
        if not anchor_regs.nexts(prev_elt.get("text", "")):
            return None

        next_paragraph_matched = current_regs.nexts(element["text"])
        if not next_paragraph_matched:
            return None

        results = self.create_content_result(element, current_regs, col=col)
        return results or None

    def filter_elements(self, elements, check_syllabus=True):
        if self.force_use_all_elements or (not elements and self.use_all_elements):
            elements = self.get_special_elements(element_class="PARAGRAPH")
        elements = self.base_filter_elements(elements, check_syllabus=check_syllabus)
        if self.threshold:
            elements = [e for e in elements if e.get("score", 0) >= self.threshold]
        return elements

    def predict_schema_answer(self, elements):
        answers = []
        answer_elements = defaultdict(list)
        existed_indices = defaultdict(set)
        all_elements = defaultdict(list)
        all_element_indices = {e["index"] for e in elements}
        for col in self.columns:
            include_anchor = self.include_anchor(col)
            anchor_regs, current_regs = self.anchor_regs(col), self.current_regs(col)
            paragraph_pattern, neglect_patterns = self.paragraph_pattern(col), self.neglect_pattern(col)
            sentence_pattern, neg_sen_pattern = self.sentence_pattern(col), self.neglect_sentence_pattern(col)
            for element in elements:
                if not self.is_valid_chapter(element):
                    continue
                # 被合并的段落，只用最全的文本做判断 TODO 放到base_model中
                if (
                    merged_indices := get_keys(element, ["page_merged_paragraph", "paragraph_indices"], [])
                ) and element["index"] != merged_indices[0]:
                    if merged_indices[0] in all_element_indices:
                        continue
                    _, element = self.pdfinsight.find_element_by_index(merged_indices[0])
                if element["index"] in existed_indices[col]:
                    continue

                _result = self.match_result_by_prev_anchor(current_regs, anchor_regs, element, col=col)
                if _result:
                    answer_elements[col].extend(_result)
                    continue

                clean_element_text = clean_txt(element.get("text", ""), remove_blank=self.remove_blank)
                if not clean_element_text:
                    continue

                near_matched = self.as_follow_near_offset != 0 and self.as_follow_start_pattern.nexts(
                    clean_element_text
                )
                if not near_matched and neglect_patterns and neglect_patterns.nexts(clean_element_text):
                    continue
                paragraph_matched = paragraph_pattern.nexts(clean_element_text) if paragraph_pattern else False
                if self.para_separator:
                    # 同时有sentence_pattern和paragraph_pattern时，必须匹配paragraph_pattern
                    if sentence_pattern and paragraph_pattern and not paragraph_matched:
                        continue
                    pattern = sentence_pattern or paragraph_pattern
                    paragraph_matched = any(
                        pattern.nexts(s) and not neg_sen_pattern.nexts(s)
                        for s in split_paragraph(clean_element_text, separator=self.para_separator)
                    )
                follow_matched = False
                if self.as_follow_pattern:
                    follow_matched = self.as_follow_pattern.nexts(clean_element_text) or self.default_follows.nexts(
                        clean_element_text
                    )
                if (
                    self.predictor.schema.name in (SchemaName.AR_ESG.value, SchemaName.ESG.value)
                    and not paragraph_matched
                    and not follow_matched
                ):
                    paragraph_matched, follow_matched = self.match_again(element, paragraph_pattern, neglect_patterns)

                follow_start_element = None
                if follow_matched:
                    follow_start_element = element
                elif near_matched:
                    follow_start_element = self.get_as_follow_start_by_near(element["index"], neglect_patterns)

                if self.filter_primary_role_elements:
                    ans_text = (
                        paragraph_matched.group()
                        if paragraph_matched and paragraph_matched is not True
                        else clean_element_text
                    )
                    if self.has_primary_role_title(element, answer_text=ans_text):
                        continue

                # multi_elements模式下，连续段落只需要一个，否则结果会出现重复
                if (
                    self.multi_elements
                    and answer_elements.get(col)
                    and element.get("page_merged_paragraph")
                    and any(
                        PdfinsightSyllabus.indices_of_merged_para(element)
                        == PdfinsightSyllabus.indices_of_merged_para(answer_element.element)
                        for answer_element in answer_elements[col]
                    )
                ):
                    continue

                element_results = []
                if anchor_regs and not current_regs:
                    prev_elts = self.pdfinsight.find_elements_near_by(
                        element["index"], step=-1, amount=1, aim_types=["PARAGRAPH"]
                    )
                    if not prev_elts:
                        continue
                    prev_elt = prev_elts[0]
                    if (
                        include_anchor
                        and anchor_regs.nexts(prev_elt.get("text", ""))
                        and self.is_valid_chapter(prev_elt, check_parent=True)
                    ):
                        element_results.extend(self.create_para_results(prev_elt))
                        existed_indices[col].add(prev_elt["index"])

                if not paragraph_matched and not follow_start_element:
                    continue

                check_para = True
                if follow_start_element and (follow_elements := self.get_as_follow_elements(follow_start_element)):
                    if self.as_follow_type != AsFollowType.TABLE_NO_TITLE:
                        # 这种场景说明当前的element已经在结果中，不再走paragraph_pattern的逻辑
                        check_para = False
                    # 未配置neglect_below_pattern时，不过滤元素块，确保as follow结果连续
                    if self.neglect_below_pattern:
                        follow_elements = [
                            elem for elem in follow_elements if elem["index"] not in existed_indices[col]
                        ]
                    existed_indices[col].update({e["index"] for e in follow_elements})
                    if self.enum_from_multi_element:
                        all_elements[col].extend(follow_elements)
                    else:
                        element_results.extend(self.create_multi_outline_results(follow_elements))
                # 如果as_follow_pattern和paragraph_pattern都能匹配到，需要排除as_follow语句已经被匹配的情况
                if paragraph_matched and check_para:
                    existed_indices[col].add(element["index"])
                    if self.enum_from_multi_element:
                        all_elements[col].append(element)
                    else:
                        pattern = sentence_pattern or paragraph_pattern if self.para_separator else paragraph_pattern
                        element_results.extend(self.create_content_result(element, pattern, col=col))

                if not element_results:
                    continue
                answer_elements[col].extend(element_results)

                if not self.multi_elements and answer_elements[col]:  # 多元素块
                    break
        for col in self.columns:
            answer_results = answer_elements[col]
            if col_elements := all_elements[col]:
                answer_results.extend(self.create_multi_outline_results(col_elements))
            if not answer_results:
                continue
            answers.extend(self.create_answer_result(col, answer_results))
        return answers

    def create_answer_result(self, col, answer_elements):
        answers = []
        answer_elements.sort(key=lambda x: (x.element["index"], (x.start or 0) if hasattr(x, "start") else 0))
        if self.enum_from_multi_element:
            answers.append({col: [self.create_result(answer_elements, column=col)]})
            return answers
        if self.combine_paragraphs:
            # 拼接多个段落为一个答案, 不分组(适用于PDFinsight把一个段落拆成多个元素块的情况)
            answers.append({col: [self.create_result(self.merge_paragraphs(answer_elements), column=col)]})
            return answers
        for i in answer_elements:
            answer = {col: [self.create_result([i], column=col)]}
            answers.append(answer)
        return answers

    def get_as_follow_start_by_near(self, start_index, neglect_patterns):
        step = -1 if self.as_follow_near_offset < 0 else 1
        for near_element in self.pdfinsight.find_elements_near_by(
            start_index,
            step=step,
            amount=abs(self.as_follow_near_offset),
            aim_types=["PARAGRAPH"],
            neg_patterns=neglect_patterns,
        ):
            if not self.is_valid_chapter(near_element) or not is_paragraph_elt(near_element):
                break
            text = clean_txt(near_element.get("text", ""), remove_blank=self.remove_blank)
            if self.as_follow_pattern.nexts(text):
                return None if neglect_patterns.nexts(text) else near_element
            is_follow = self.ignore_pattern.nexts(text) or self.as_follow_start_pattern.nexts(text)
            if (
                is_follow
                or P_CONTINUED.search(text)
                or P_CHNS.search(text)
                or not is_paragraph_elt(near_element, strict=True)
            ):
                continue
            if not is_follow or (
                not self.ignore_pattern.search(near_element) and self.pdfinsight.is_syllabus_title(near_element)
            ):
                break
        return None

    def get_as_follow_elements(self, element: dict) -> list[dict]:
        follow_elements = []
        element_text = clean_txt(element.get("text"), self.remove_blank)
        if not element_text:
            return []
        if self.as_follow_type == AsFollowType.ANY:
            if elements := self.find_blow_table(element):
                follow_elements.append(element)
                follow_elements.extend(elements)
            else:
                follow_elements.extend(self.find_blow_para_elements(element))
        elif self.as_follow_type in (AsFollowType.TABLE, AsFollowType.TABLE_NO_TITLE):
            if self.as_follow_type == AsFollowType.TABLE:
                follow_elements.append(element)
            follow_elements.extend(self.find_blow_table(element))
        else:
            follow_elements.extend(self.find_blow_para_elements(element))
        return follow_elements

    def find_blow_table(self, follow_element: dict) -> list[dict]:
        """
        提取as_follow_pattern下相邻的第一个表格
        """
        if self.as_follow_type == AsFollowType.PARA:
            return []

        follow_elements = []
        has_any_below = not self.any_below_pattern
        for element in find_as_follow_tables(self.pdfinsight, follow_element, ignore_pattern=P_ONLY_DATE):
            # 取到的follow元素块中至少应有一个满足any_below_pattern，否则丢弃
            if self.any_below_pattern and any(
                self.any_below_pattern.nexts(clean_txt(cell["text"], remove_blank=self.remove_blank))
                for cell in element["cells"].values()
            ):
                has_any_below = True

            # 依据表格内容做一些过滤
            table = parse_table(element, tabletype=TableType.TUPLE, pdfinsight_reader=self.pdfinsight)
            if not self.below_pattern:
                follow_elements.extend([element, *table.footnotes])
                continue
            table_rows = [
                "\t".join(
                    [
                        clean_txt(cell.text, remove_blank=self.remove_blank)
                        for cell in row
                        if cell.page == element["page"]
                    ]
                )
                for row in table.rows
            ]
            table_cols = [
                "\t".join(
                    [
                        clean_txt(cell.text, remove_blank=self.remove_blank)
                        for cell in col
                        if cell.page == element["page"]
                    ]
                )
                for col in table.cols
            ]
            if self.below_pattern.nexts("\n".join(table_rows)) or self.below_pattern.nexts("\n".join(table_cols)):
                follow_elements.extend([element, *table.footnotes])
        if not has_any_below:
            return []
        return follow_elements

    def find_blow_para_elements(self, follow_element: dict) -> list[dict]:
        elements = self.find_blow_paras(follow_element)
        if len(elements) < self.min_below_count + 1:
            # 匹配的元素块太少，可能是匹错了，丢弃这种答案
            return []
        # 取到的follow元素块中至少应有一个满足any_below_pattern，否则丢弃
        if not self.any_below_pattern:
            return elements
        for elem in elements[1:]:
            if is_paragraph_elt(elem):
                text = elem["text"]
            else:
                # 取到的可能是一个单行或单列的表格 https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/3168#note_505241
                text = " ".join(cell["text"] for cell in elem["cells"].values())
            if self.any_below_pattern.nexts(clean_txt(text, remove_blank=self.remove_blank)):
                return elements
        return []

    def match_again(self, element, paragraph_pattern, neglect_patterns):
        if "ESG" in self.schema.root_name:
            return False, False
        if is_correct_spelling_sentence(clean_txt(element["text"])):
            return False, False
        cache_text = clean_txt(self.get_text_from_cache(element))
        if not cache_text:
            return False, False
        if neglect_patterns and neglect_patterns.nexts(cache_text):
            return False, False
        para_matched = paragraph_pattern and paragraph_pattern.nexts(cache_text)
        follow_matched = False
        if self.as_follow_pattern:
            if (
                self.as_follow_pattern.nexts(cache_text)
                or self.default_follows
                and self.default_follows.nexts(cache_text)
            ):
                follow_matched = True
        return bool(para_matched), follow_matched

    def get_text_from_cache(self, element):
        if not element.get("outline"):
            return ""
        box = {"box": element["outline"], "page": element["page"]}
        res = get_chars_text_from_outline(self.predictor.pdf_path, box)
        return res["text"] if res["text"] else ""
