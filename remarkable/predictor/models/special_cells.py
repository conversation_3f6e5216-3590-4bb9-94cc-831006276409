from collections import defaultdict
from copy import copy
from enum import IntEnum
from inspect import signature
from itertools import chain
from typing import List, Set, Union

from remarkable.common.common import get_keys, real_all
from remarkable.common.common_pattern import P_FOLLOW_PREFIX, P_ONLY_MIDDLE_DASH
from remarkable.common.constants import PDFInsightClassEnum, TableType
from remarkable.common.pattern import PatternCollection
from remarkable.common.util import box_in_box, clean_txt
from remarkable.pdfinsight.parser import ParsedTable, ParsedTableCell, parse_table
from remarkable.pdfinsight.reader_table import PdfinsightTable
from remarkable.predictor.dataset import DatasetItem
from remarkable.predictor.default_predictor.utils import is_table_result
from remarkable.predictor.hkex_predictor.esg_util import is_index_table
from remarkable.predictor.hkex_predictor.model_util import find_all_section_rows, get_foot_note_by_cells
from remarkable.predictor.models.base_model import TableModel
from remarkable.predictor.schema_answer import (
    CellCharResult,
    ParagraphResult,
    TableCellsResult,
    TableResult,
)


class LastMode(IntEnum):
    """
    last_mode可选值
    """

    FIRST_ROW = 1  # 取首行
    FIRST_COL = 2  # 取首列
    LAST_ROW = 3  # 取末行
    LAST_COL = 4  # 取末列


class SpecialCells(TableModel):
    CONTINUOUS_FIRST_IDX = "__continuous_first_idx__"

    def train(self, dataset: List[DatasetItem], **kwargs):
        pass

    def extract_feature(self, elements, answer):
        pass

    @property
    def threshold(self):
        return self.get_config("threshold", 0.0)

    @property
    def need_headers_meta(self):
        """
        是否将结果单元格对应的列名/行名信息写入meta
        """
        return self.get_config("need_headers_meta", False)

    @property
    def need_continuous(self):
        """
        是否需要连续表格，默认需要
        """
        return self.get_config("need_continuous", True)

    @property
    def neglect_header_pattern(self):
        patterns = self.get_config("neglect_header_pattern", [])
        return PatternCollection(patterns, flags=self.flags)

    @property
    def row_pattern(self):
        patterns = self.get_config("row_pattern", [])
        return PatternCollection(patterns, flags=self.flags)

    @property
    def row_pattern_as_section(self):
        """
        行正则是否按照分段来匹配，如下表，配置`分段标题1`的正则，将会一直匹配到分段标题2之前
        |   列A  | 列B | 列C | 列D|
        | ------ | -- | -- | -- |
        |分段标题1|    |    |    |
        |内容1   |... |... |... |
        |内容2   |... |... |... |
        |分段标题2|    |    |    |
        |内容3   |... |... |... |
        |内容4   |... |... |... |
        例如如下文件中P24 index=220的表格，配置`2022 Placing`的正则将匹配到`2023 Rights Issue`之前为止：
        http://************:55647/#/project/remark/268596?treeId=8317&fileId=70562&schemaId=18&projectId=8317&schemaKey=C1.1.1
        """
        if value := self.get_config("row_pattern_as_section", False):
            if not self.row_pattern and not self.row_header_pattern:
                raise ValueError("'row_pattern' or 'row_header_pattern' must be configured!")
            if self.just_first_row:
                raise ValueError("'just_first_row' can not be configured to True!")
        return value

    @property
    def aim_types(self) -> list[str] | set[str]:
        """
        指定元素块类型
        """
        return {PDFInsightClassEnum.TABLE.value}

    @property
    def col_pattern(self):
        patterns = self.get_config("col_pattern", [])
        return PatternCollection(patterns, flags=self.flags)

    @property
    def row_header_pattern(self):
        """指定行名正则，用于和col_header_pattern一起定位单元格，结果中带header"""
        patterns = self.get_config("row_header_pattern", [])
        return PatternCollection(patterns, flags=self.flags)

    @property
    def col_header_pattern(self):
        """指定列名正则，用于和row_header_pattern一起定位单元格，结果中带header"""
        patterns = self.get_config("col_header_pattern", [])
        return PatternCollection(patterns, flags=self.flags)

    @property
    def row_col_relation(self):
        """
        若同时配置了列正则和行正则，要同时满足还是满足其一
        """
        if value := self.get_config("row_col_relation"):
            if value not in ("and", "or"):
                raise ValueError("value of 'row_col_relation' must be 'and' or 'or'!")
            if not ((self.row_pattern or self.row_header_pattern) and (self.col_pattern or self.col_header_pattern)):
                raise ValueError("regex for row and column must be configured simultaneously!")
        return value or "and"

    @property
    def footnote_pattern(self):
        """如果配置了footnote_pattern，会尝试匹配脚注文本"""
        patterns = self.get_config("footnote_pattern", [])
        return PatternCollection(patterns, flags=self.flags)

    @property
    def allow_only_footnotes(self):
        """
        当表格无结果，是否允许只取footnotes
        注意：配置为True时，必须同时配置footnote_pattern
        """
        can_only = self.get_config("allow_only_footnotes") or False
        if can_only and not self.footnote_pattern:
            raise ValueError("'footnote_pattern' must be configured!")
        return can_only

    @property
    def last_mode(self) -> Set[LastMode] | None:
        """
        multi为True时，选择首行/列，或者末行/列，可配置多种场景组合
        注意：不能同时配置FIRST和LAST
        """
        if last_mode := self.get_config("last_mode"):
            if LastMode.FIRST_ROW in last_mode and LastMode.LAST_ROW in last_mode:
                raise ValueError("Cannot configure 'FIRST_ROW' and 'LAST_ROW' simultaneously.")
            if LastMode.FIRST_COL in last_mode and LastMode.LAST_COL in last_mode:
                raise ValueError("Cannot configure 'FIRST_COL' and 'LAST_COL' simultaneously.")
        return last_mode

    @property
    def include_footnotes(self) -> bool:
        """
        当结果单元格中有脚注时，是否需要提取对应脚注内容
        """
        return self.get_config("include_footnotes", False)

    @property
    def cell_pattern(self):
        """
        结果的行/列中必须有一个单元格能匹配该正则
        """
        patterns = self.get_config("cell_pattern", [])
        return PatternCollection(patterns, flags=self.flags)

    @property
    def neglect_cell_pattern(self):
        """
        结果的行/列中必须有一个单元格能匹配该正则
        """
        patterns = self.get_config("neglect_cell_pattern", [])
        return PatternCollection(patterns, flags=self.flags)

    @property
    def any_cell_pattern(self):
        """
        表格中任意一个单元格匹配该正则，最终结果中不需要匹配
        """
        patterns = self.get_config("any_cell_pattern", [])
        return PatternCollection(patterns, flags=self.flags)

    @property
    def any_neglect_cell_pattern(self):
        """
        表格中任意一个单元格匹配该正则，则跳过该表格
        """
        patterns = self.get_config("any_neglect_cell_pattern", [])
        return PatternCollection(patterns, flags=self.flags)

    @property
    def special_col_indices(self):
        return self.get_config("special_col_indices", [])

    @property
    def whole_table(self):
        return self.get_config("whole_table", False)

    @property
    def just_first_row(self):
        return self.get_config("just_first_row", False)

    @property
    def without_col_header(self):
        """
        当配置了`row_pattern`或`col_pattern`时，是否忽略列名做匹配
        """
        return self.get_config("without_col_header", False)

    @property
    def without_row_header(self):
        """
        当配置了`col_pattern`时，是否忽略列名做匹配
        """
        return self.get_config("without_row_header", False)

    @property
    def enum_pattern(self):
        """
        取到表格结果后，用enum_pattern过滤结果
        """
        patterns = self.get_config("enum_pattern", [])
        return PatternCollection(patterns, flags=self.flags)

    @property
    def enum_function(self) -> callable:
        """
        取到表格结果后，依据enum_function中的配置计算枚举值
        """
        enum_func = self.get_config("enum_function")
        if not enum_func:
            return None
        if not callable(enum_func):
            raise ValueError("'enum_function' must be a callable function")
        sig = signature(enum_func)
        required_params_count = len(sig.parameters) - sum(p.default is not p.empty for p in sig.parameters.values())
        if required_params_count != 2:
            raise ValueError(f"'enum_function' must have 2 required parameters, but {required_params_count} found")

        return enum_func

    @property
    def enum(self) -> str | None:
        value = self.get_config("enum")
        if value and self.enum_function:
            raise ValueError("'enum' won't work when 'enum_function' is configured!")
        return value

    @property
    def remove_cn_text(self):
        return self.get_config("remove_cn_text", False)

    @property
    def ignore_col_header(self):
        return self.get_config("ignore_col_header", False)

    def clean_text(self, text: str):
        if not text:
            return ""
        return clean_txt(text.strip(), remove_blank=self.remove_blank, remove_cn_text=self.remove_cn_text)

    def add_continuous_tables(self, elements: list[dict]) -> tuple[list[dict], dict[int, list]]:
        """
        找每个表格可能的跨页连续表格，尽量保证原有elements顺序
        https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/4574#note_545337
        http://************:55647/#/project/remark/232965?projectId=17&treeId=4451&fileId=66449&schemaId=15&schemaKey=B81 page103
        """
        table_elements_dict = defaultdict(list)
        score_map = {e["index"]: e.get("score", 0) for e in elements}
        existed_indices, drop_indices = set(), set()
        for index, element in enumerate(elements):
            if element["index"] in existed_indices:
                drop_indices.add(element["index"])
                continue
            first_idx = None
            conti_elements = self.pdfinsight.continuous_tables(element)
            if len(conti_elements) == 1 and element["index"] not in self.pdfinsight.page_continued_tables:
                continue
            for conti_elem in conti_elements:
                # 注意：连续表格内包含了element
                elem_index = conti_elem["index"]
                if not first_idx:
                    first_idx = elem_index
                    # 已经被合并的表格，后面不再追加，避免重复: http://************:55647/#/project/remark/450550?fileid=73278&projectId=37&treeId=46485&fileId=73278&schemaId=38
                    existed_indices.update(self.pdfinsight.merged_tables.get(first_idx) or [])
                existed_indices.add(elem_index)
                if elem_index in score_map:
                    score = score_map[elem_index]
                    drop_indices.add(elem_index)
                else:
                    score = element.get("score") or 0
                new_elem = copy(conti_elem)
                new_elem["score"] = score
                new_elem[self.CONTINUOUS_FIRST_IDX] = first_idx
                table_elements_dict[index].append(new_elem)
        if not table_elements_dict:
            return elements, {}
        # 存入连续表格第一个和最后一个的映射关系，用于保证脚注只提一次
        continuous_last_map = {}
        for index, conti_elements in sorted(table_elements_dict.items(), key=lambda x: x[0], reverse=True):
            first_index = conti_elements[0]["index"]
            continuous_last_map[first_index] = self.pdfinsight.page_continued_tables[first_index][-1]
            # 这里组结果的时候，不要elements[index]，因为包含在了conti_elements中
            elements = elements[:index] + conti_elements + elements[index + 1 :]
        # 跨页连续表格可能被重复添加，丢弃已经存在的表格
        return [
            e for e in elements if e.get(self.CONTINUOUS_FIRST_IDX) or e["index"] not in drop_indices
        ], continuous_last_map

    def predict_schema_answer(self, elements):
        """
        row_pattern、col_pattern若同时配置，任取一个
        row_pattern或col_pattern和cell_pattern同时配置，先全表判断是否能匹配cell_pattern，不匹配则跳过这张表
        """
        pred_results, footnote_results = [], []

        # 找表格元素块的跨页连续表格
        continuous_last_map = {}
        if self.need_continuous or self.include_footnotes or self.footnote_pattern:
            continuous_elements, continuous_last_map = self.add_continuous_tables(elements)
            if self.need_continuous:
                elements = continuous_elements
        last_table_first_idx = None
        for element in elements:
            if element.get("score", 0) < self.threshold:
                continue
            cur_first_idx = element.get(self.CONTINUOUS_FIRST_IDX)
            # multi_elements=False 且已经有了结果，且不是连续表格，则终止
            if (
                not self.multi_elements
                and (pred_results or footnote_results)
                and (not last_table_first_idx or last_table_first_idx != cur_first_idx)
            ):
                break
            last_table_first_idx = cur_first_idx
            # 跳过正确识别的跨页合并表格： http://************:55647/#/project/remark/233958?treeId=5370&fileId=66615&schemaId=15&projectId=17&schemaKey=B77&page=88
            if not cur_first_idx:
                merge_idx = get_keys(element, ["page_merged_table", "index"], 0)
                if merge_idx > 0 and merge_idx != element["index"]:
                    continue
            table = parse_table(element, tabletype=TableType.TUPLE, pdfinsight_reader=self.pdfinsight)
            if "ESG" in self.predictor.schema.parent.name and is_index_table(table):
                continue
            if self.neglect_header_pattern and any(
                self.neglect_header_pattern.nexts(self.clean_text(cell.text))
                for cell in chain(*table.col_header, *table.row_header)
                if not cell.dummy
            ):
                continue
            if self.any_neglect_cell_pattern and self.matched_any_cell_pattern(table, self.any_neglect_cell_pattern):
                continue
            answers = []
            # TODO add unittests
            if not self.matched_any_cell_pattern(table, self.any_cell_pattern):
                continue
            if self.row_col_relation == "and" and self.row_header_pattern and self.col_header_pattern:
                answers = self.get_answers_by_row_col(element, table)
            elif self.row_pattern or self.col_pattern or self.col_header_pattern or self.row_header_pattern:
                if self.row_pattern or (not self.col_pattern and self.row_header_pattern):
                    answers = self.get_answers_by_row(element, table)
                if not answers and (self.col_pattern or (not self.row_pattern and self.col_header_pattern)):
                    answers = self.get_answers_by_col(element, table)
            elif self.cell_pattern:
                answers = self.get_answers_by_cell(element, table)
            elif self.special_col_indices:
                if cells := [cell for col in table.cols for cell in col if cell.colidx in self.special_col_indices]:
                    answers = self.make_answer_results(table, element, cells, need_merge=True, group_type="col")
                else:
                    answers = []
            elif self.title_patterns or self.title_above_pattern:
                # 走到这里说明分值+标题匹配成功
                answers = self.make_answer_results(table, element, [cell for row in table.rows for cell in row])
            if answers:
                for answer in answers:
                    if answer not in pred_results:
                        pred_results.append(answer)
            # 允许footnotes且为连续表格的最后一个时，提取脚注
            elif (
                self.allow_only_footnotes
                and (not cur_first_idx or element["index"] == continuous_last_map.get(cur_first_idx))
                and (
                    notes := [
                        fn
                        for fn in table.footnotes
                        if self.footnote_pattern.nexts(self.clean_text(fn.get("text") or ""))
                    ]
                )
            ):
                footnote_results.extend(ParagraphResult(note, note["chars"]) for note in notes)
        for element in elements:
            if self.CONTINUOUS_FIRST_IDX in element:
                element.pop(self.CONTINUOUS_FIRST_IDX)
        if footnote_results:
            footnote_results.sort(key=lambda x: x.element["index"])
            pred_results.append(self.create_result(footnote_results, schema=self.schema, value=self.enum))
        # 答案为ND时，可能存在element_results为空
        if (
            pred_results
            and all(r.element_results for r in pred_results)
            and (callable(self.enum_function) or not self.enum)
        ):
            return self.enum_multi_elements(pred_results)
        return pred_results

    def enum_multi_elements(self, pred_results):
        """
        连续表格要合并结果做判断
        """
        pred_results.sort(key=lambda x: min(er.element["index"] for er in x.element_results))
        table_results, foot_results = [], []
        meta = None
        for res in pred_results:
            if not meta:
                meta = res.meta
            for elt_result in res.element_results:
                if is_table_result(elt_result):
                    table_results.append(elt_result)
                else:
                    foot_results.append(elt_result)
        table_results.sort(key=lambda x: x.element["index"])
        enum_value = None
        if callable(self.enum_function):
            enum_value = self.enum_function(table_results, foot_results)
        return [self.create_result(table_results + foot_results, schema=self.schema, meta=meta, value=enum_value)]

    def is_other_page_cell(self, element, cell):
        # http://************:55647/#/project/remark/244323?treeId=8837&fileId=67293&schemaId=28&projectId=43974&schemaKey=E(d)(i)-2-Effectiveness%20of%20internal%20audit%20function
        # 解决：跨页表格框选某个单元格，结果重复，这里判断单元格的page不等于当前element的page，跳过
        return not self.skip_continued_table and (
            not self.whole_table
            and cell.page != element.get("page")
            or not box_in_box(cell.outline, element["outline"])
        )

    def matched_any_cell_pattern(self, table, pattern):
        return not pattern or any(
            pattern.nexts(self.clean_text(cell.text))
            for row in table.rows
            for cell in row
            if not cell.dummy and cell.text
        )

    def make_answer_results(
        self,
        table: ParsedTable,
        element: Union[PdfinsightTable, dict],
        all_cells: List[ParsedTableCell],
        need_merge: bool = True,
        group_type: str = None,
    ):
        # 提取到的所有单元格都没有内容，则丢弃答案
        if not all_cells or not any(c.text for c in all_cells):
            return []
        # 如果结果中只有列头，说明结果不合理
        if not self.ignore_col_header and len(table.cols) > 2 and all(cell.is_col_header for cell in all_cells):
            return []
        # enum_pattern 过滤单元格
        if self.enum_pattern:
            if self.whole_table:
                if not self.enum_pattern.nexts(
                    "\t".join(self.clean_text(cell.text_no_superscript) for row in table.rows for cell in row)
                ):
                    return []
            else:
                all_cells = [
                    cell for cell in all_cells if self.enum_pattern.nexts(self.clean_text(cell.text_no_superscript))
                ]
                if not all_cells:
                    return []

        foot_notes = []
        if self.include_footnotes or self.footnote_pattern:
            notes = get_foot_note_by_cells(table, all_cells, self.footnote_pattern)
            if not notes:
                # 脚注在合并单元格上
                # http://************:55647/#/project/remark/234378?treeId=5980&fileId=66685&schemaId=15&projectId=17&schemaKey=B76
                col_indices = {cell.colidx for cell in all_cells}
                notes = get_foot_note_by_cells(
                    table,
                    chain.from_iterable(
                        cells for cells in table.col_header if cells and cells[0].colidx in col_indices
                    ),
                    self.footnote_pattern,
                )
            # 配置了footnote_pattern时，也可以直接过滤所有脚注
            if not notes and self.footnote_pattern:
                notes = [e for e in table.footnotes if self.footnote_pattern.nexts(self.clean_text(e.get("text", "")))]
            if self.enum_pattern:
                foot_notes = [note for note in notes if self.enum_pattern.nexts(self.clean_text(note.get("text")))]
            else:
                foot_notes = notes
            foot_notes.sort(key=lambda x: x["index"])

        foot_results = [ParagraphResult(foot, foot["chars"]) for foot in foot_notes] if foot_notes else []

        if callable(self.enum_function):
            enum_value = self.enum_function([TableCellsResult(element, all_cells)], foot_results)
            # 配置了enum_function，却没计算出枚举值，丢弃当前答案
            if not enum_value:
                return []
        else:
            enum_value = self.enum
        if self.need_headers_meta:
            col_headers, row_headers = table.col_header_texts, table.row_header_texts
            if not self.whole_table:
                all_cols, all_rows = {c.colidx for c in all_cells}, {c.rowidx for c in all_cells}
                col_headers = {c: self.clean_text(text) for c, text in col_headers.items() if c in all_cols}
                row_headers = {r: self.clean_text(text) for r, text in row_headers.items() if r in all_rows}
            meta = {
                # 这里要存数据库，不能为set
                "header_rows": list(table.row_indices_of_header),
                "header_cols": list(table.col_indices_of_header),
                "col_headers": col_headers,
                "row_headers": row_headers,
            }
        else:
            meta = {}
        if self.whole_table:
            return [
                self.create_result(
                    [TableResult(element, parsed_table=table)], schema=self.schema, value=enum_value, meta=meta
                ),
                *foot_results,
            ]

        if not need_merge and not self.enum_pattern:
            # 这种场景是多个不相邻的单元格
            return [
                self.create_result(
                    [TableCellsResult(element, [cell], group_type=group_type)],
                    schema=self.schema,
                    meta={"cell": [int(i) for i in cell.raw_cell["index"].split("_")]} | meta,
                    value=enum_value,
                )
                for cell in all_cells
            ] + ([self.create_result(foot_results, schema=self.schema, value=enum_value)] if foot_results else [])
        answer_results = []
        if self.enum_pattern:
            for ans_cell in all_cells:
                # 配置了enum_pattern，则针对单元格中的条目，仅框选满足该正则的条目
                # 注意，配置的正则包含content，才会触发CellCharResult，且此时不提取footnotes
                answer_results.extend(self.get_cell_char_result(element, ans_cell))
        if not answer_results:
            answer_results.append(TableCellsResult(element=element, cells=all_cells, group_type=group_type))
            answer_results.extend(foot_results)
        return [self.create_result(answer_results, schema=self.schema, meta=meta, value=enum_value)]

    def get_cell_char_result(self, element: dict, cell: ParsedTableCell) -> list[CellCharResult]:
        cell_text, cell_chars = cell.text, cell.raw_cell["chars"]
        start = 0
        cell_results = []
        for matched in chain(P_FOLLOW_PREFIX.finditer(cell_text), (None,)):
            end = matched.start() if matched else len(cell_text)
            if end == 0:
                continue
            for matched in self.enum_pattern.finditer(cell_text[start:end]):
                # if matched := self.enum_pattern.nexts(cell_text[start:end]):
                if matched is True:
                    if start == 0 and end == len(cell_text):
                        # http://************:55647/#/project/remark/249786?fileid=68421&projectId=17&treeId=4807&fileId=68421&schemaId=15&schemaKey=B6
                        cell_results.append(TableCellsResult(element, cells=[cell]))
                    else:
                        cell_results.append(
                            CellCharResult(element, cell_chars[start:end], cells=[cell], start=start, end=end)
                        )
                elif "content" in matched.groupdict():
                    new_start, new_end = [i + start for i in matched.span("content")]
                    cell_results.append(
                        CellCharResult(
                            element, cell_chars[new_start:new_end], cells=[cell], start=new_start, end=new_end
                        )
                    )
                if not self.multi_content:
                    break
            start = end
        return cell_results

    def filter_row_or_col(self, table: ParsedTable, row_or_col: list[ParsedTableCell], index: int, is_row: bool = True):
        all_cells = []
        if is_row:
            pattern, header_pattern, cell_header_pattern, count, header_attr = (
                self.row_pattern,
                self.row_header_pattern,
                self.col_header_pattern if self.row_col_relation == "and" else None,
                len(table.rows),
                "is_row_header",
            )
            len_attr, header_texts, cell_header_texts = "height", table.row_header_texts, table.col_header_texts
        else:
            pattern, header_pattern, cell_header_pattern, count, header_attr = (
                self.col_pattern,
                self.col_header_pattern,
                self.row_header_pattern if self.row_col_relation == "and" else None,
                len(table.cols),
                "is_col_header",
            )
            len_attr, header_texts, cell_header_texts = "width", table.col_header_texts, table.row_header_texts
        # 根据行名或列名过滤
        if header_pattern and not (header_pattern.nexts(header_texts[index])):
            return []
        cells_texts = []
        for cell in row_or_col:
            if self.without_col_header and cell.is_col_header or self.without_row_header and cell.is_row_header:
                continue
            text = self.clean_text(cell.text)
            # 合并单元格去重
            if text not in cells_texts:
                cells_texts.append(text)
        if not cells_texts or pattern and not pattern.nexts("\t".join(cells_texts)):
            return []
        cell_matched = not self.cell_pattern
        for cell in row_or_col:
            cell_text = self.clean_text(cell.text)
            if (
                self.cell_pattern
                and not (header_pattern and getattr(cell, header_attr))
                and self.cell_pattern.nexts(cell_text)
                and not self.neglect_cell_pattern.nexts(cell_text)
            ):
                cell_matched = True
            # 有合并header时，row_pattern仅对单元格生效
            if self.is_skip_header(cell, len_attr, count, pattern):
                continue
            header_index = cell.colidx if is_row else cell.rowidx
            if cell_header_pattern and not cell_header_pattern.nexts(cell_header_texts[header_index]):
                continue
            # 第一个列名或行名为空，可以丢弃
            if not all_cells and not cell_text and cell.is_header:
                continue
            if self.special_col_indices and cell.colidx not in self.special_col_indices:
                continue
            all_cells.append(cell)
        if not cell_matched:
            return []
        # 配置了行正则只取到行头，或配置了列正则，只取到列头，则整行/列丢弃
        if not self.row_pattern_as_section:
            if pattern and is_row and all(cell.is_row_header for cell in all_cells):
                return []
            if pattern and not is_row and all(cell.is_col_header for cell in all_cells):
                return []
        return all_cells

    def add_header_cells(
        self,
        all_cells: list[ParsedTableCell],
        rows_or_cols: list[list[ParsedTableCell]],
        header_pattern: PatternCollection,
        is_row: bool = True,
    ):
        """
        若配置了col_header_pattern或row_header_pattern，在最后把列名/行名所在单元格追加到结果中
        """
        if not all_cells or not header_pattern:
            return all_cells
        idx_attr, len_attr, head_attr = (
            ("colidx", "height", "is_col_header") if is_row else ("rowidx", "width", "is_row_header")
        )
        indices = {getattr(cell, idx_attr) for cell in all_cells}
        header_cells = []
        for row_or_col in rows_or_cols:
            if not real_all([getattr(cell, head_attr) for cell in row_or_col]):
                break
            header_cells = []
            for cell in row_or_col:
                if (
                    cell in all_cells
                    or getattr(cell, idx_attr) not in indices
                    or self.is_skip_header(cell, len_attr)
                    or not header_pattern.nexts(self.clean_text(cell.text))
                ):
                    continue
                header_cells.append(cell)
            if len(header_cells) == len(row_or_col):
                header_cells = []
        return header_cells + all_cells

    def is_skip_header(self, cell, len_attr, count=0, pattern=None):
        merged_count = getattr(cell, len_attr)
        if merged_count == count:
            return True
        if cell.is_header and cell.dummy:
            return True
        if merged_count == 1:
            return False
        text = self.clean_text(cell.text)
        if not cell.is_header:
            # 单元格内容只有-或者空格时，容易被错误识别为合并单元格
            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/3168#note_533628
            if not text.strip() or P_ONLY_MIDDLE_DASH.search(text):
                return True
            return False
        if pattern:
            return not pattern.nexts(text)
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6247#note_663348
        return False

    @classmethod
    def get_section_rows(cls, rows, section_row_indices: set[int], row_indices: set[int], col_indices: set[int]):
        cells = []
        sorted_row_indices = sorted(row_indices)
        # 先添加多行之间的行（可能一个正则匹配上了多个分段）
        for row_idx, next_row_idx in zip(sorted_row_indices, sorted_row_indices[1:]):
            if row_idx + 1 == next_row_idx:
                continue
            for idx in range(row_idx + 1, next_row_idx):
                cells.extend([cell for cell in rows[idx] if cell.colidx in col_indices])
        # 再找到最后一个行之后到下一个section之间的行
        for row in rows[(max(row_indices) + 1) :]:
            if row[0].rowidx in section_row_indices:
                break
            cells.extend([cell for cell in row if cell.colidx in col_indices])
        return cells

    def get_answers_by_row(self, element: Union[PdfinsightTable, dict], table: ParsedTable):
        all_cells = []
        rows = table.rows[:1] if self.just_first_row else table.rows
        for row in rows:
            if not (filtered_cells := self.filter_row_or_col(table, row, row[0].rowidx, is_row=True)):
                continue

            all_cells.extend(filtered_cells)
            if all_cells and not self.multi:
                break
        if not all_cells:
            return []
        all_cells = self.filter_by_last_mode(all_cells)
        if not self.just_first_row and self.row_pattern_as_section:
            # 先找出所有row_header的字体样式
            section_rows = find_all_section_rows(rows)
            # 如果匹配到的每行，都是分段行，则认为符合分段场景
            row_indices = {cell.rowidx for cell in all_cells if not cell.is_col_header}
            if section_rows and row_indices and row_indices.issubset(section_rows):
                # 追加分段行
                all_cells.extend(
                    self.get_section_rows(
                        rows,
                        section_rows,
                        row_indices,
                        self.special_col_indices or {cell.colidx for cell in all_cells if not cell.is_col_header},
                    )
                )
        if self.row_col_relation == "and":
            all_cells = self.add_header_cells(all_cells, rows, self.col_header_pattern)
        return self.make_answer_results(table, element, all_cells, group_type="row")

    def get_answers_by_col(self, element: Union[PdfinsightTable, dict], table: ParsedTable):
        all_cells = []
        for col in table.cols:
            if not (filtered_cells := self.filter_row_or_col(table, col, col[0].colidx, is_row=False)):
                continue
            all_cells.extend(filtered_cells)
            if all_cells and not self.multi:
                break
        all_cells = self.filter_by_last_mode(all_cells)
        if self.row_col_relation == "and":
            all_cells = self.add_header_cells(all_cells, table.cols, self.row_header_pattern, is_row=False)
        return self.make_answer_results(table, element, all_cells, group_type="col")

    def get_answers_by_cell(self, element: Union[PdfinsightTable, dict], table: ParsedTable):
        cell_texts = set()
        all_cells = []
        rows = table.rows[:1] if self.just_first_row else table.rows
        for row in rows:
            for cell in row:
                cleaned_cell_text = self.clean_text(cell.text)
                if not cleaned_cell_text:
                    continue
                if self.neglect_cell_pattern and self.neglect_cell_pattern.nexts(cleaned_cell_text):
                    continue
                if not self.cell_pattern or self.cell_pattern.nexts(cleaned_cell_text):
                    if cleaned_cell_text in cell_texts:
                        continue

                    # 解决：跨页表格框选某个单元格，结果重复，这里判断单元格的page不等于当前element的page，跳过
                    if self.is_other_page_cell(element, cell):
                        continue

                    if self.last_mode and {LastMode.LAST_COL, LastMode.LAST_ROW}.issubset(self.last_mode):
                        all_cells = [cell]
                        continue
                    all_cells.append(cell)
                    cell_texts.add(cleaned_cell_text)
                    if not self.multi:
                        break
            if all_cells and not self.multi:
                break
        return self.make_answer_results(table, element, all_cells, need_merge=False)

    def filter_by_last_mode(self, cells):
        if not self.last_mode or not cells:
            return cells
        row_indices = {c.rowidx for c in cells if not c.is_header}
        header_rows = {c.rowidx for c in cells if c.is_col_header}
        if LastMode.FIRST_ROW in self.last_mode:
            # 取列标题所在行 + 第一个数据行
            indices = {min(row_indices)} | header_rows
            cells = [cell for cell in cells if cell.rowidx in indices]
        elif LastMode.LAST_ROW in self.last_mode:
            # 取列标题所在行 + 最后一个数据行
            indices = {max(row_indices)} | header_rows
            cells = [cell for cell in cells if cell.rowidx in indices]
        col_indices = {c.colidx for c in cells if not c.is_header}
        header_cols = {c.colidx for c in cells if c.is_row_header}
        if LastMode.FIRST_COL in self.last_mode:
            # 取行标题所在列 + 第一个数据列
            indices = {min(col_indices)} | header_cols
            cells = [cell for cell in cells if cell.colidx in indices]
        elif LastMode.LAST_COL in self.last_mode:
            # 取行标题所在列 + 最后一个数据列
            indices = {max(col_indices)} | header_cols
            cells = [cell for cell in cells if cell.colidx in indices]
        return cells

    def get_answers_by_row_col(self, element: Union[PdfinsightTable, dict], table: ParsedTable):
        """
        根据row_header_pattern + col_header_pattern定位单元格
        """
        row_indices = [
            idx for idx, text in table.row_header_texts.items() if self.row_header_pattern.nexts(self.clean_text(text))
        ]
        col_indices = [
            idx for idx, text in table.col_header_texts.items() if self.col_header_pattern.nexts(self.clean_text(text))
        ]
        if not row_indices or not col_indices:
            return []

        if self.last_mode:
            if LastMode.FIRST_ROW in self.last_mode:
                row_indices = [row_indices[0]]
            elif LastMode.LAST_ROW in self.last_mode:
                row_indices = [row_indices[-1]]
            if LastMode.FIRST_COL in self.last_mode:
                col_indices = [col_indices[0]]
            elif LastMode.LAST_COL in self.last_mode:
                col_indices = [col_indices[-1]]

        all_cells = []
        for row in table.rows:
            for cell in row:
                row_idx, col_idx = cell.rowidx, cell.colidx
                if row_idx not in row_indices or col_idx not in col_indices:
                    continue

                # 解决：跨页表格框选某个单元格，结果重复，这里判断单元格的page不等于当前element的page，跳过
                if self.is_other_page_cell(element, cell):
                    continue
                # 取单元格的行名 + 列名，尽量排除合并单元格
                for attr, idx in {("row_header", row_idx), ("col_header", col_idx)}:
                    headers = getattr(table, attr)[idx]
                    if len(headers) == 1 and headers[0] not in all_cells:
                        all_cells.append(headers[0])
                        continue
                    for header in headers:
                        if header in all_cells or header.dummy or not header.width == header.height == 1:
                            continue
                        all_cells.append(header)
                        # 多个时可能误判，只取一个标题
                        # http://http://************:55647/#/project/remark/264603?treeId=37545&fileId=70135&schemaId=15&projectId=17&schemaKey=B74 P191 index=2178
                        break
                all_cells.append(cell)

                if all_cells and not self.multi:
                    break
            if all_cells and not self.multi:
                break
        return self.make_answer_results(table, element, all_cells)
