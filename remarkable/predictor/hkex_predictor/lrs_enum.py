from remarkable.common.util import clean_txt
from remarkable.predictor.hkex_predictor.schemas.pattern import (
    R_ND_SHARE_AWARD_SCHEME,
    UNDER_INCENTIVE_SCHEME,
)
from remarkable.predictor.predictor import JudgeByRegex
from remarkable.predictor.utils import GPTDictator


class LRsEnumChecker(JudgeByRegex):
    col_patterns = {
        "A34": {
            "negative statement": R_ND_SHARE_AWARD_SCHEME,
            "disclosure": [r".*"],
        },
        "A35": {
            "negative statement": R_ND_SHARE_AWARD_SCHEME,
            "disclosure": [r".*"],
        },
        "A36": {
            "negative statement": R_ND_SHARE_AWARD_SCHEME,
            "disclosure": [r".*"],
        },
        "A37": {
            "negative statement": UNDER_INCENTIVE_SCHEME,
            "disclosure": [r".*"],
        },
        # models中指定了enum值，这里不需要再配置
        # "丨H38-41": H38_41_ENUM,
    }

    def predict(self, predictor_result, schema, metadata=None):
        schema_name = schema.parent.name if schema.name == "Content" else schema.name
        if (pattern := self.col_patterns.get(schema_name)) and isinstance(pattern, GPTDictator):
            return pattern.judge(clean_txt(predictor_result.text, remove_blank=self.remove_blank))
        return super().predict(predictor_result, schema, metadata)
