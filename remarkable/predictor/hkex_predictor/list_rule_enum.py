import datetime
import re
from collections import namedtuple
from typing import Dict

from remarkable.common.common_pattern import P_SEN_SEPARATOR, R_MIDDLE_DASH
from remarkable.common.constants import AnswerValueEnum
from remarkable.common.pattern import (
    MatchMulti,
    NeglectPattern,
    <PERSON>ternCollection,
    SplitBeforeMatch,
)
from remarkable.common.util import clean_txt
from remarkable.predictor.common_pattern import R_EN_MONTH
from remarkable.predictor.hkex_predictor.pattern import (
    R_BE,
    R_DATES_FOR_EXTRACT,
    R_DAY,
    R_NUM_DAYS,
)
from remarkable.predictor.hkex_predictor.schemas.list_rule_schema.list_rule_b74 import (
    R_B74_NS,
    R_NO_VESTING,
    R_VESTING_CELL,
)
from remarkable.predictor.hkex_predictor.schemas.list_rule_schema.list_rule_b75 import P_NS_TARGET
from remarkable.predictor.hkex_predictor.schemas.list_rule_schema.list_rule_b76 import (
    P_B76_PS_SENTENCE,
)
from remarkable.predictor.hkex_predictor.schemas.list_rule_schema.list_rule_b81 import (
    P_NOT_AWARD,
    P_PRICE_DESCRIPTION,
    R_ND_TEXT,
    R_NIL_PRICE_WORD,
    R_NOT_ADOPTED_WORD,
)
from remarkable.predictor.hkex_predictor.schemas.list_rule_schema.list_rule_b82 import P_B82_NS
from remarkable.predictor.hkex_predictor.schemas.list_rule_schema.list_rule_b93 import (
    P_B93_1_HEADER,
    P_B93_PARTI_NS,
    R_B93_1_AVAILABLE,
    R_B93_2_AVAILABLE,
    R_NO_SERVICE,
)
from remarkable.predictor.hkex_predictor.schemas.list_rule_schema.list_rule_schema import (
    R_NO_SHARE_AWARDS,
    R_NO_SHARE_OPTIONS,
)
from remarkable.predictor.hkex_predictor.schemas.pattern import (
    P_PRICE_PS,
    R_AWARD_KEYWORD,
    R_B95_NS_REGS,
    R_NIL,
    R_NO_AWARD_GRANTED,
    R_NO_AWARD_NS,
    R_NO_OPTION_GRANTED,
    R_NO_OPTION_NS,
    R_NUMBER,
    R_NUMBER_PS,
    R_OPTION_KEYWORD,
    R_PRICE,
    R_ZERO,
    reg_words,
)
from remarkable.predictor.predictor import JudgeByRegex
from remarkable.predictor.schema_answer import PredictorResult, TableCellsResult
from remarkable.predictor.utils import GPTDictator, extract_date

R_DATES_NO_GROUP = [
    rf"\d{{1,2}}\s*{R_EN_MONTH}[,，]?\s*\d{{4}}[,，]?",
    rf"{R_EN_MONTH}\s*\d{{1,2}}[,，]?\s*\d{{4}}[,，]?",
]
P_EXPIRED_DATE = PatternCollection(
    [
        *[rf"expir(ed?|y)\s*({reg_words(0, 5)}scheme\s*)?(on|at|of)\s*{r_date}" for r_date in R_DATES_FOR_EXTRACT],
        *[
            rf"termination\s*(of\s*)?[^.]+?(?<!adopted )(?<!commencing )((as\s*)?at|on|of)\s*{r_date}"
            for r_date in R_DATES_FOR_EXTRACT
        ],
        *[rf"meeting[^.]*?held\s*on\s*{r_date}[,，]\s*[^.]+?termination\s*of" for r_date in R_DATES_FOR_EXTRACT],
        *[rf"{R_BE}\s*terminated\s*[^.]*?(on|at)\s*{r_date}" for r_date in R_DATES_FOR_EXTRACT],
        *[rf"during\s*the\s*year\s*ended\s*{r_date}.+?(ha[sd]|have)\s*terminated" for r_date in R_DATES_FOR_EXTRACT],
        *[rf"on\s*{r_date}.+?(ha[sd]|have)\s*resolved\s*to\s*terminate" for r_date in R_DATES_FOR_EXTRACT],
        *[rf"in\s*force\s*during\s*the\s*period\s*from\s*{R_DAY}\s*to\s*{r_date}" for r_date in R_DATES_FOR_EXTRACT],
        # http://100.64.0.105:55647/#/project/remark/?treeId=9450&fileId=68583&schemaId=15&projectId=17&schemaKey=B93.2
        *[rf"to\s*terminate\s*[\w\s]+?with\s*effect\s*from\s*{r_date}" for r_date in R_DATES_FOR_EXTRACT],
    ],
    flags=re.I,
)
P_ADOPTED_DATE = PatternCollection(
    [
        *[rf"adopted\s*{reg_words(0, 6)}([(（].+?[)）]\s*)?(at|on|of)\s*{r_date}" for r_date in R_DATES_FOR_EXTRACT],
        *[rf"on\s*{r_date}[^.]+?the\s*company\s*adopted\s*(of\s*)?a\s*new" for r_date in R_DATES_FOR_EXTRACT],
        *[
            rf"(have|ha[sd])\s*adopted\s*the{reg_words(0, 6)}scheme\s*(at|on|of)\s*{r_date}"
            for r_date in R_DATES_FOR_EXTRACT
        ],
        *[rf"in\s*force\s*during\s*the\s*period\s*from\s*{r_date}" for r_date in R_DATES_FOR_EXTRACT],
        *[rf"bec[oa]me\s*effective\s*(as\s*)?(at|on|of)\s*{r_date}" for r_date in R_DATES_FOR_EXTRACT],
    ],
    flags=re.I,
)
P_RENEWED_DATE = PatternCollection(
    [rf"renewed\s*on\s*{r_date}" for r_date in R_DATES_FOR_EXTRACT],
    flags=re.I,
)
# P_NO_FURTHER = re.compile(r"no\s*further\s*(shares?\s*)?option", re.I)

# remained at见：http://100.64.0.105:55647/#/project/remark/250363?treeId=6141&fileId=68516&schemaId=15&projectId=17&schemaKey=B93.2 P49
R_B93_1_AVAIL_NUMBER = (
    rf"({R_BE}|remain(ed)?\s*at)\s+{R_NUMBER}|{R_NUMBER}\s*{R_OPTION_KEYWORD}\s*[^,，.]*?{R_BE}\s*available"
)
R_B93_2_AVAIL_NUMBER = rf"({R_BE}|remain(ed)?\s*at)\s*{R_NUMBER}|{R_NUMBER}\s*{R_AWARD_KEYWORD}\s*[^,，.]*?{R_BE}\s*available|total\s*of\s*{R_NUMBER}\s*underlying"
P_B93_1_NO_AVAIL = re.compile(rf"\b{R_ZERO}\s*{R_OPTION_KEYWORD}\s*{R_BE}\s*available\s*for\s*grant", re.I)
P_B93_2_NO_AVAIL = re.compile(rf"\b{R_ZERO}\s*{R_AWARD_KEYWORD}\s*{R_BE}\s*available\s*for\s*grant", re.I)
P_B93_SERVICE_NS = MatchMulti.compile(P_B93_PARTI_NS, *R_NO_SERVICE, operator=any)


def multi_pattern(patterns, neglect=None):
    if neglect:
        return SplitBeforeMatch(
            NeglectPattern.compile(match=MatchMulti.compile(*patterns, operator=all), unmatch=neglect),
            separator=P_SEN_SEPARATOR,
            operator=any,
        )
    return SplitBeforeMatch(
        MatchMulti.compile(*patterns, operator=all),
        separator=P_SEN_SEPARATOR,
        operator=any,
    )


DatesPatterns = namedtuple(
    "DatesPatterns",
    [
        "year_start",
        "year_end",
        "year_start_patterns",
        "year_end_patterns",
        "start_pattern",
        "end_pattern",
        "last_year",
        "year",
    ],
)


def gene_date_patterns(date_time):
    return [date_time.strftime(fmt) for fmt in ("%-d %b[a-z]{0,6}[,，]? %Y", "%b[a-z]{0,6} %-d[,，]? %Y")]


def get_year_start_end(metadata):
    report_year = int((metadata or {}).get("report_year") or datetime.datetime.now().strftime("%Y"))
    year_end = datetime.datetime.strptime(metadata.get("year_end") or f"{report_year}-12-31", "%Y-%m-%d")
    year_start = datetime.datetime.strptime(metadata.get("year_start") or f"{report_year}-01-01", "%Y-%m-%d")
    return year_start, year_end


def get_dates_and_patterns(metadata):
    year_start, year_end = get_year_start_end(metadata)
    year_start_patterns, year_end_patterns = gene_date_patterns(year_start), gene_date_patterns(year_end)
    start_pattern = MatchMulti.compile(r"beginning", *year_start_patterns, operator=any)
    end_pattern = MatchMulti.compile(
        r"\b(the|and)\s*end\s*of\s*(the\s*)?(FY\d{4}|financial\s*year|[\d/]+\s*annual\s*period)\b",
        *year_end_patterns,
        operator=any,
    )

    last_year = year_start.year - 1 if year_start.month == year_start.day == 1 else None
    year = year_end.year if year_end.month == 12 and year_end.day == 31 else None
    return DatesPatterns(
        year_start, year_end, year_start_patterns, year_end_patterns, start_pattern, end_pattern, last_year, year
    )


def get_result_text(predictor_result, year_end_patterns):
    if predictor_result.element_results and isinstance(predictor_result.element_results[0], TableCellsResult):
        parsed_cells = predictor_result.element_results[0].parsed_cells
        if all(cell.is_header for cell in parsed_cells):
            return ""
        if not predictor_result.meta:
            return predictor_result.text
        for headers in predictor_result.meta.values():
            if len(parsed_cells) != len(headers):
                continue
            cells = []
            if indexes := [
                index
                for index, header in enumerate(headers)
                if any(re.compile(pat, re.I).search(header) for pat in year_end_patterns)
            ]:
                if is_beginning(predictor_result):
                    cells = [cell for i, cell in enumerate(parsed_cells) if i not in indexes]
                else:
                    cells = [cell for i, cell in enumerate(parsed_cells) if i in indexes]
            elif indexes := [index for index, header in enumerate(headers) if P_B93_1_HEADER.search(header)]:
                cells = [cell for i, cell in enumerate(parsed_cells) if i in indexes]
            separator = "\n" if parsed_cells[0].is_col_header else "\t"
            if cells:
                return separator.join([cell.text for cell in cells])
    return predictor_result.text


def gene_b93_begin_ps_pattern(key_pattern, avail_pattern, start_pattern, end_pattern):
    return MatchMulti.compile(
        rf"\b{R_NUMBER}\s*and\s*({R_NUMBER}|{R_ZERO})\s+{key_pattern}\s*{R_BE}\s*available",
        rf"{R_BE}\s*{R_NUMBER}\s*{key_pattern}\s*available.*{R_BE}\s+({R_NUMBER}|{R_ZERO})\s+{key_pattern}\s*available",
        rf"{R_BE}\s*{R_NUMBER}\s*{reg_words(1, 5)}\d{{4}}[,，]?\s*and\s+({R_NUMBER}|{R_ZERO})\s",
        MatchMulti.compile(
            avail_pattern,
            start_pattern,
            MatchMulti.compile(*end_pattern.patterns, R_DAY, operator=any),
            # + r"|".join([rf"{r_date}\s*(and|[,，])\s*{r_date}\s*" for r_date in R_DATES_NO_GROUP]),
            r"|".join(
                [
                    rf"{R_BE}\s*{R_NUMBER}\s*and\s*({R_NUMBER}|{R_ZERO})",
                    rf"{R_BE}\s*(both\s*|all\s*)?{R_NUMBER}",
                    rf"(grant|underlying)\s+{R_NUMBER}\s.+?(grant|underlying)\s+({R_NUMBER}|{R_ZERO})",
                    rf"{R_NUMBER}\s*(share|option)s.+?({R_NUMBER}|{R_ZERO})\s*(share|option)s.*?respectively",
                    # http://100.64.0.105:55647/#/project/remark/251248?treeId=13914&fileId=68664&schemaId=15&projectId=17&schemaKey=B93.2
                    *[
                        rf"as\s*at\s*{r_date}\s*and\s*{r_date}\s*[,，]\s*{R_NUMBER}\s*(share|option)s.+?respectively"
                        for r_date in R_DATES_NO_GROUP
                    ],
                ]
            ),
            operator=all,
        ),
        rf"(\t|^){R_NUMBER}(\t|$)",
        operator=any,
    )


def gene_b93_end_ps_pattern(key_pattern, avail_pattern, start_pattern, end_pattern):
    return MatchMulti.compile(
        rf"\s+({R_NUMBER}|{R_ZERO})\s*and\s*{R_NUMBER}\s*{key_pattern}\s*{R_BE}\s*available",
        rf"{R_BE}\s+({R_NUMBER}|{R_ZERO})\s+{key_pattern}\s*available.*?{R_BE}\s*{R_NUMBER}\s*{key_pattern}\s*available",
        rf"{R_BE}\s+({R_NUMBER}|{R_ZERO})\s+{reg_words(1, 5)}\d{{4}}[,，]?\s*and\s*{R_NUMBER}",
        MatchMulti.compile(
            avail_pattern,
            MatchMulti.compile(*start_pattern.patterns, R_DAY, operator=any),
            end_pattern,
            # r"beginning.*\bend\b|" + r"|".join([rf"{r_date}\s*and\s*{r_date}\s*" for r_date in R_DATES_NO_GROUP]),
            r"|".join(
                [
                    rf"{R_BE}\s+({R_NUMBER}|{R_ZERO})\s+and\s*{R_NUMBER}",
                    rf"{R_BE}\s*(both\s*|all\s*){R_NUMBER}",
                    rf"(grant|underlying)\s+({R_NUMBER}|{R_ZERO})\s.+?(grant|underlying)\s*{R_NUMBER}",
                    rf"({R_NUMBER}|{R_ZERO})\s*(share|option)s.+?{R_NUMBER}\s*(share|option)s.*?respectively",
                    # http://100.64.0.105:55647/#/project/remark/251248?treeId=13914&fileId=68664&schemaId=15&projectId=17&schemaKey=B93.2
                    *[
                        rf"as\s*at\s*{r_date}\s*and\s*{r_date}\s*[,，]\s*{R_NUMBER}\s*(share|option)s.+?respectively"
                        for r_date in R_DATES_NO_GROUP
                    ],
                ]
            ),
            operator=all,
        ),
        rf"(\t|^){R_NUMBER}(\t|$)",
        operator=any,
    )


def is_mandate(predictor_result: PredictorResult):
    return "mandate" in predictor_result.schema.name


def is_beginning(predictor_result: PredictorResult):
    return "beginning" in predictor_result.schema.name


def is_b93_1(predictor_result: PredictorResult):
    return "B93.1" in predictor_result.key_path


B93Patterns = namedtuple(
    "B93Patterns",
    ["ps_pattern", "ps_pattern_1", "ps_pattern_2", "nil_pattern", "nil_pattern_1", "no_available"],
)


def get_enum_pattern(predictor_result, dates_pattern):
    if is_b93_1(predictor_result):
        key_pattern = R_OPTION_KEYWORD
        avail_pattern = R_B93_1_AVAILABLE
        avail_num_pattern = R_B93_1_AVAIL_NUMBER
        # no_ns_patterns = R_NO_OPTION_NS + R_NO_OPTION_GRANTED
        no_ns_patterns = R_NO_OPTION_NS
        no_available = P_B93_1_NO_AVAIL
    else:
        key_pattern = R_AWARD_KEYWORD
        avail_pattern = R_B93_2_AVAILABLE
        avail_num_pattern = R_B93_2_AVAIL_NUMBER
        # no_ns_patterns = R_NO_AWARD_NS + R_NO_AWARD_GRANTED
        no_ns_patterns = R_NO_AWARD_NS
        no_available = P_B93_2_NO_AVAIL

    # 期初和期末在一起
    if is_beginning(predictor_result):
        ps_pattern = gene_b93_begin_ps_pattern(
            key_pattern, avail_pattern, dates_pattern.start_pattern, dates_pattern.end_pattern
        )
        # 期初和期末各自是单独的句子，用日期确定是期初还是期末
        ps_pattern_1 = multi_pattern(
            patterns=[avail_num_pattern, dates_pattern.start_pattern], neglect=dates_pattern.end_pattern
        )
        # 仅有描述期末的句子，但最后带了`期初日期：10,000,000`描述
        begin_date_patterns = [dates_pattern.year_start_patterns] + (
            [str(dates_pattern.last_year)] if dates_pattern.last_year else []
        )
        ps_pattern_2 = MatchMulti.compile(
            *[rf"{r_pat}\s*[:：]\s*{R_NUMBER}" for r_pat in begin_date_patterns], operator=any
        )
        nil_pattern = SplitBeforeMatch(
            MatchMulti.compile(
                # 注意：zero在前时，考虑0.618这种情况
                rf"{R_ZERO}\s*and\s*{R_NUMBER}",
                rf"{R_ZERO}\s.*?\s{R_NUMBER}",
                R_NIL,
                *no_ns_patterns,
                operator=any,
            ),
            separator=P_SEN_SEPARATOR,
            operator=any,
        )
        nil_pattern_1 = multi_pattern(
            patterns=[avail_pattern, rf"\b({R_BE}|representing)\s+{R_ZERO}", dates_pattern.start_pattern]
        )
    else:
        ps_pattern = gene_b93_end_ps_pattern(
            key_pattern, avail_pattern, dates_pattern.start_pattern, dates_pattern.end_pattern
        )
        # ps_pattern_1 = multi_pattern(patterns=[avail_pattern, avail_num_pattern, dates_pattern.end_pattern])
        ps_pattern_1 = multi_pattern(
            patterns=[avail_num_pattern, dates_pattern.end_pattern], neglect=dates_pattern.start_pattern
        )
        # 仅有描述期初的句子，但最后带了`期末日期：10,000,000`描述
        end_date_patterns = [dates_pattern.year_end_patterns] + (
            [str(dates_pattern.year)] if dates_pattern.year else []
        )
        ps_pattern_2 = MatchMulti.compile(
            *[rf"{r_pat}\s*[:：]\s*{R_NUMBER}" for r_pat in end_date_patterns], operator=any
        )
        nil_pattern = SplitBeforeMatch(
            MatchMulti.compile(
                rf"{R_NUMBER}\s*and\s+{R_ZERO}", rf"{R_NUMBER}\s.*?\s{R_ZERO}", R_NIL, *no_ns_patterns, operator=any
            ),
            separator=P_SEN_SEPARATOR,
            operator=any,
        )
        nil_pattern_1 = multi_pattern(
            patterns=[avail_pattern, rf"\b({R_BE}|representing)\s+{R_ZERO}", dates_pattern.end_pattern]
        )

    return B93Patterns(ps_pattern, ps_pattern_1, ps_pattern_2, nil_pattern, nil_pattern_1, no_available)


def enum_b93(predictor_result: PredictorResult, metadata: Dict):
    """
    规则：B93.1、B93.2
    NS条件： ①在以前年度expired或termination
            ②beginning时，当前年度内adopted，不管有没有提到available for grant的数量
            ③end时：当年年度内expired或termination，不管有没有提到available for grant的数量，但要排除重新续订的情况
            ④0/nil options available或者表格中为-或N/A或声明没有share award scheme
            ⑤规则mandate：has not adopted any share option scheme或者no options for grant
            ⑥规则service provider sublimit: no service provider sublimit
            ⑦仅针对B93.2：句式save for share option scheme, no equity-linked agreements （统一在no_share中处理）
    PS条件：①xxxx options available for grant
           ②表格中不为 -或N/A
    ND条件：no share option was granted及未披露任何数据
    """
    dates_pattern = get_dates_and_patterns(metadata)
    res_text = get_result_text(predictor_result, dates_pattern.year_end_patterns)
    b93_pat = get_enum_pattern(predictor_result, dates_pattern)
    if not res_text:
        predictor_result.element_results = []
        return AnswerValueEnum.ND.value
    if expired_date := extract_date(P_EXPIRED_DATE, res_text):
        # 排除续订的情况 http://100.64.0.105:55647/#/project/remark/?treeId=4103&fileId=68590&schemaId=15&projectId=17&schemaKey=B93.1 P135
        renewed_date = extract_date(
            P_RENEWED_DATE, "\n".join(elem.get("text") or "" for elem in predictor_result.relative_elements)
        )
        # NS情况①：以前年度expired或termination
        if not (renewed_date and renewed_date <= dates_pattern.year_start) and expired_date < dates_pattern.year_start:
            return AnswerValueEnum.NS.value
        # NS情况②：end时：当年年度内expired或termination
        if not is_beginning(predictor_result) and (
            dates_pattern.year_start <= expired_date < dates_pattern.year_end or "during" in res_text
        ):
            return AnswerValueEnum.NS.value
    # NS情况③：beginning时，当前年度内adopted
    if is_beginning(predictor_result) and (adopted_date := extract_date(P_ADOPTED_DATE, res_text)):
        if adopted_date > dates_pattern.year_start:
            return AnswerValueEnum.NS.value
    # PS情况：明确提到非0 options available for grant
    if (
        b93_pat.ps_pattern.search(res_text)
        or b93_pat.ps_pattern_1.search(res_text)
        or b93_pat.ps_pattern_2.search(res_text)
    ):
        return AnswerValueEnum.PS.value
    # NS情况④：可授权股份为0或声明没有share option scheme
    if b93_pat.nil_pattern.search(res_text) or b93_pat.nil_pattern_1.search(res_text):
        return AnswerValueEnum.NS.value
    # NS情况⑤：no share options for grant
    if is_mandate(predictor_result) and b93_pat.no_available.search(res_text):
        return AnswerValueEnum.NS.value
    # NS情况⑥：no service provider sublimit
    if not is_mandate(predictor_result) and P_B93_SERVICE_NS.search(res_text):
        return AnswerValueEnum.NS.value
    return AnswerValueEnum.ND.value


class ListRuleEnumChecker(JudgeByRegex):
    col_patterns = {
        "B63.1": {
            "Name of grantee(Description of grantees)": {
                AnswerValueEnum.NS.value: MatchMulti.compile(
                    *R_NO_SHARE_OPTIONS, *R_NO_OPTION_NS, *R_NO_OPTION_GRANTED, operator=any
                ),
                AnswerValueEnum.PS.value: [r".+"],
            },
        },
        "B64": {
            "Performance targets (if any)": {
                AnswerValueEnum.NS.value: MatchMulti.compile(
                    *R_NO_SHARE_OPTIONS, *R_NO_OPTION_NS, *R_NO_OPTION_GRANTED, operator=any
                ),
                AnswerValueEnum.PS.value: [r".+"],
            },
            "Fair value of options at the date of grant": {
                AnswerValueEnum.NS.value: MatchMulti.compile(
                    *R_NO_SHARE_OPTIONS, *R_NO_OPTION_NS, *R_NO_OPTION_GRANTED, operator=any
                ),
                AnswerValueEnum.PS.value: [r".+"],
            },
            "Accounting standard and policy adopted": {
                AnswerValueEnum.NS.value: MatchMulti.compile(
                    *R_NO_SHARE_OPTIONS, *R_NO_OPTION_NS, *R_NO_OPTION_GRANTED, operator=any
                ),
                AnswerValueEnum.PS.value: [r".+"],
            },
        },
        "B73": {
            "Content": {
                AnswerValueEnum.NS.value: MatchMulti.compile(*R_NO_AWARD_NS, *R_NO_AWARD_GRANTED, operator=any),
                AnswerValueEnum.PS.value: [r".+"],
            },
        },
        "B74": {
            "Beginning amount": {
                AnswerValueEnum.PS.value: R_NUMBER_PS,
                AnswerValueEnum.NS.value: [MatchMulti.compile(*R_B74_NS, operator=any), r".+"],
            },
            "Ending amount": {
                AnswerValueEnum.PS.value: R_NUMBER_PS,
                AnswerValueEnum.NS.value: [MatchMulti.compile(*R_B74_NS, operator=any), r".+"],
            },
            "The number of unvested awards": {
                AnswerValueEnum.PS.value: R_NUMBER_PS,
                AnswerValueEnum.NS.value: [MatchMulti.compile(*R_B74_NS, operator=any), r".+"],
            },
            "Date of grant": {
                AnswerValueEnum.PS.value: MatchMulti.compile(
                    R_DAY,
                    *R_NUM_DAYS,
                    operator=any,
                ),
                AnswerValueEnum.NS.value: [MatchMulti.compile(*R_B74_NS, operator=any), r".+"],
            },
            "Vesting period": {
                AnswerValueEnum.PS.value: NeglectPattern.compile(
                    match=MatchMulti.compile(
                        *R_VESTING_CELL,
                        operator=any,
                    ),
                    unmatch=MatchMulti.compile(*R_NO_VESTING, operator=any),
                ),
                AnswerValueEnum.NS.value: [
                    MatchMulti.compile(*R_B74_NS, *R_NO_VESTING, operator=any),
                    NeglectPattern.compile(match=R_NIL, unmatch=MatchMulti.compile(*R_VESTING_CELL, operator=any)),
                ],
            },
            "Exercise period": {
                AnswerValueEnum.NS.value: MatchMulti.compile(*R_B74_NS, r"no\s*purchase\s*price", operator=any),
                AnswerValueEnum.PS.value: [r".+"],
            },
            "Purchase price": {
                AnswerValueEnum.PS.value: P_PRICE_PS,
                AnswerValueEnum.NS.value: [r".+"],
            },
        },
        "B75": {
            "Number of awards": {
                AnswerValueEnum.PS.value: [R_NUMBER_PS],
                AnswerValueEnum.NS.value: [MatchMulti.compile(*R_B74_NS, operator=any), r".+"],
            },
            "Purchase price": {
                AnswerValueEnum.PS.value: P_PRICE_PS,
                AnswerValueEnum.NS.value: [r".+"],
            },
            "Date of grant": {
                AnswerValueEnum.PS.value: MatchMulti.compile(
                    R_DAY,
                    *R_NUM_DAYS,
                    operator=any,
                ),
                AnswerValueEnum.NS.value: [MatchMulti.compile(*R_B74_NS, operator=any), r".+"],
            },
            "Vesting period": {
                AnswerValueEnum.PS.value: NeglectPattern.compile(
                    match=MatchMulti.compile(
                        *R_VESTING_CELL,
                        operator=any,
                        flag=re.S | re.I,
                    ),
                    unmatch=MatchMulti.compile(*R_NO_VESTING, operator=any),
                ),
                AnswerValueEnum.NS.value: [MatchMulti.compile(*R_B74_NS, *R_NO_VESTING, operator=any)],
            },
            "Exercise period": {
                AnswerValueEnum.NS.value: MatchMulti.compile(*R_B74_NS, r"no\s*purchase\s*price", operator=any),
                AnswerValueEnum.PS.value: [r".+"],
            },
            "Performance targets(if any)": {
                AnswerValueEnum.NS.value: MatchMulti.compile(
                    *R_NO_SHARE_AWARDS,
                    *R_NO_AWARD_NS,
                    *R_NO_AWARD_GRANTED,
                    P_NS_TARGET,
                    rf"(\t|^)(N/A|not\s*applicable|{R_MIDDLE_DASH}+)(\t|$)",
                    operator=any,
                ),
                AnswerValueEnum.PS.value: [r".+"],
            },
            "Closing price": {
                AnswerValueEnum.PS.value: P_PRICE_PS,
                AnswerValueEnum.NS.value: [r".+"],
            },
            "Fair value of awards at the date of grant": {
                AnswerValueEnum.PS.value: P_PRICE_PS,
                AnswerValueEnum.NS.value: [r".+"],
            },
            "Accounting standard and policy adopted": {
                AnswerValueEnum.NS.value: MatchMulti.compile(
                    *R_NO_SHARE_AWARDS, *R_NO_AWARD_NS, *R_NO_AWARD_GRANTED, operator=any
                ),
                AnswerValueEnum.PS.value: [r".+"],
            },
        },
        "B76": {
            "Number of awards vested": {
                AnswerValueEnum.PS.value: MatchMulti.compile(
                    *R_NUMBER_PS,
                    P_B76_PS_SENTENCE,
                    operator=any,
                ),
                AnswerValueEnum.NS.value: [r".+"],
            },
            "Purchase price": {
                AnswerValueEnum.PS.value: P_PRICE_PS,
                AnswerValueEnum.NS.value: [r".+"],
            },
            "Closing price": {
                # AnswerValueEnum.PS.value: MatchMulti.compile(R_CLOSING_PRICE_COLS, r"|".join(R_PRICE_PS), operator=all),
                AnswerValueEnum.PS.value: P_PRICE_PS,
                AnswerValueEnum.NS.value: [r".+"],
            },
        },
        "B77": {
            # "Number of awards cancelled": {
            #     AnswerValueEnum.PS.value: MatchMulti.compile(
            #         *R_B74_NUMBER_PS,
            #         operator=any,
            #     ),
            #     # AnswerValueEnum.NS.value: MatchMulti.compile(*R_NO_AWARD_GRANTED, P_NS_COL_VALUE, operator=any),
            #     AnswerValueEnum.NS.value: [r".+"],
            # },
            "Purchase price": {
                AnswerValueEnum.PS.value: P_PRICE_PS,
                AnswerValueEnum.NS.value: [r".+"],
            },
        },
        "B80": {
            "Amount": {
                AnswerValueEnum.PS.value: [re.compile(R_PRICE, re.I)],
                AnswerValueEnum.NS.value: [r".*"],
            }
        },
        "B81": {
            "Content": {
                AnswerValueEnum.NS.value: [
                    NeglectPattern.compile(
                        match=MatchMulti.compile(
                            R_NIL_PRICE_WORD,
                            R_NOT_ADOPTED_WORD,
                            r"\bnil|N/A|None|Not applicable",
                            r"not provide.*?purchase price",
                            r"not required .*? to (pay|make)",
                            r"not applicable herein",
                            r"no cash consideration",
                            r"No subscription",
                            operator=any,
                        ),
                        unmatch=P_PRICE_DESCRIPTION,
                    ),
                    P_NOT_AWARD,
                    r"aggregate.*?Selected Participants at nil consideration Scheme",
                    r"not required to make any payment.*?Awarded Shares",
                ],
                AnswerValueEnum.PS.value: [
                    NeglectPattern.compile(
                        match=MatchMulti.compile(
                            r"price|market value",
                            r"be paid an amount to the trustee",
                            rf"{R_AWARD_KEYWORD} Scheme shall be determined",
                            operator=any,
                        ),
                        unmatch=R_ND_TEXT,
                    ),
                ],
                AnswerValueEnum.ND.value: [r".*"],
            }
        },
        "B82": {
            "Content": {
                AnswerValueEnum.NS.value: [P_B82_NS],
                AnswerValueEnum.PS.value: [r".*"],
            }
        },
        "B93.1": {
            "Scheme mandate at the beginning of year": enum_b93,
            "Scheme mandate at the end of the year": enum_b93,
            "Service provider sublimit at the beginning of year": enum_b93,
            "Service provider sublimit at the end of the year": enum_b93,
        },
        "B93.2": {
            "Scheme mandate at the beginning of year": enum_b93,
            "Scheme mandate at the end of the year": enum_b93,
            "Service provider sublimit at the beginning of year": enum_b93,
            "Service provider sublimit at the end of the year": enum_b93,
        },
        # "B94.1": {
        #     "Content": {
        #         AnswerValueEnum.NS.value: R_NO_SHARE_OPTIONS,
        #         AnswerValueEnum.PS.value: [r",*"],
        #     }
        # },
        "B95.1": {
            "Content": {
                AnswerValueEnum.NS.value: MatchMulti.compile(*R_B95_NS_REGS, operator=any),
                AnswerValueEnum.PS.value: [r".*"],
            }
        },
        "B95.2": {
            "Content": {
                AnswerValueEnum.NS.value: MatchMulti.compile(*R_B95_NS_REGS, operator=any),
                AnswerValueEnum.PS.value: [r".*"],
            }
        },
        "B96": {
            "Content": {
                AnswerValueEnum.PS.value: [r".*"],
            }
        },
        "B97": {
            "Directors’ remuneration policy": {
                AnswerValueEnum.PS.value: MatchMulti.compile("emolument|remuneration", operator=all),
                AnswerValueEnum.NS.value: [r".*"],
            },
            "Details of any remuneration payable to members of senior management by band": {
                AnswerValueEnum.PS.value: MatchMulti.compile(
                    MatchMulti.compile(
                        "salar",
                        "benefit",
                        "bonus",
                        "emolument",
                        "remuneration",
                        operator=any,
                    ),
                    MatchMulti.compile("senior|key", "management", operator=all),
                    operator=all,
                ),
                AnswerValueEnum.NS.value: [r".*"],
            },
        },
        # "丨H83-92": H38_41_ENUM,
    }

    def predict(self, predictor_result, schema, metadata=None) -> str:
        clean_text = (
            clean_txt(predictor_result.text, remove_blank=self.remove_blank)
            if self.remove_blank
            else predictor_result.text
        )
        if col_patterns_or_func := (
            self.col_patterns.get(schema.parent.name, {}).get(schema.name) or self.col_patterns.get(schema.name)
        ):
            # GPT预测答案
            if isinstance(col_patterns_or_func, GPTDictator):
                return col_patterns_or_func.judge(clean_text)
            # 函数计算答案
            if callable(col_patterns_or_func):
                return col_patterns_or_func(predictor_result, metadata)
            # 正则计算答案
            for col, patterns in col_patterns_or_func.items():
                if PatternCollection(patterns).nexts(clean_text):
                    return col
        return AnswerValueEnum.ND.value  # 默认返回ND
