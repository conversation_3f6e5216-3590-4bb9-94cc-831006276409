"""
Jura 3.0 Ratio Checking
"""

# （PL利润表/BS资产负债表/notes)
from remarkable.pdfinsight.parser import TOTAL_ASSETS_PATTERN

PL_TITLE_PATTERN = [
    r"CONSOLIDATED.*?STATEMENT.*?OF.*?PROFIT(.*?(OR|AND).*?LOSS)?",
    r"CONSOLIDATED INCOME STATEMENT",
    r"CONSOLIDATED PROFIT OR LOSS STATEMENT",
    r"CONSOLIDATED STATEMENTS? OF\s?(COMPREHENSIVE )?INCOME",
    r"Profit before tax(ation \(continued\))?",
    r"CONSO LIDATESTAEMN.*?THERCOMPREHNSIVE INCOME",
    r"C ondes conslidate staemn ofprofit",  # 42782
    r"OR LOSS AND OTHER COMPREHENSIVE INCOME",  # 表头有三行 id 39697
    r"CONSOLIDATEDSTATEMENT OFPROFITORLOSSANDOTHERCOMPREHENSIVE INCOME",
    r"CONDENSED CONSOLIDATED INCOME 簡明綜合收益表",
    r"(CONDENSED )?CONSOLIDATED PROFIT AND LOSS ACCOUNT",
    r"UNAUDITED CONSOLIDATED RESULTS",
    r"Conslidate S taemnof Comprehnsive Income",
    r"profit or loss? and other\s?comprehe?nsive income",
    r"Other revenue and other net \(loss\)/income",  # 49110
    r"Consolidated and the Bank’s Income Statements",  # 58865
    r"^\d\.\s?REVENUE$",
    r"^•Revenue of the Group",
]

IMPAIRMENT_BLACK_LIST = [
    r"__regex__disposal",
    r"__regex__depreciation",
    r"__regex__Change in expected credit losses",  # 50635
    r"__regex__Write-back of other payable",  # 42905
    r"__regex__Trade and other receivables",  # 42905
]

IMPAIRMENT_WHITE_LIST = [
    r"__regex__allowances?\s*?(for|on|of)|(written|write)[-\s](down|off)|impairment loss(es)?|credit loss",
    r"__regex__impairment",
    r"__regex__(?<!over\s)provision",
    r"__regex__w r ite-of f of",  # 特殊情况 识别问题
    r"__regex__impairment|receivabl",  # 53397
]


BS_TITLE_PATTERN = [
    r"CONSOLIDATED.*?FINANCIAL.*?POSITION",
    r"CONSOLIDATED.*?BALANCE.*?SHEET",
    r"CONSOLIDATEDSTATEMENT.*?綜合財務狀況表",
    r"Consolidated Financial Statement",
    r"^CONSOLIDATEDSTATEMENTOF$",
] + [
    "^RMB$",  # 51613
]


NEGLECT_BS_TITLE_PATTERN = [
    r"^[（(][ivx]{1,4}[）)]",
    r"^note",
]
NEGLECT_BS_TITLE_ABOVE_PATTERN = [
    r"^IFRS",
    r"^financial summary|review",
]

NEGLECT_BS_SYLLABUS_PATTERN = [
    r"AUDITED CONSOLIDATED RESULT",
]

# PL-Impairment
PL_IMPAIRMENT_BLACK_PATTERN = [
    r"CONSOLIDATED STATEMENT OF FINANCIAL POSITION",
    r"INTERIM CONDENSED CONSOLIDATED STATEMENT OF FINANCIAL POSITION",
    r"CONDENSED CONSOLIDATED STATEMENT OF FINANCIAL POSITION",
    r"CONSOLIDATED STATEMENT OF CHANGES IN EQUITY",
    r"CONSOLIDATED STATEMENT OF CASH FLOWS",
    r"BALANCE SHEET",
    r"Non-recurring profit or loss items and amount",  # 58871
]


NOTE_IMPAIRMENT_BLACK_PATTERN = [
    r"DIVIDENDS?",
    r"EARNINGS PER SHARE",
    r"LOSS PER SHARE",
    r"SHARE CAPITAL",
    r"FINANCIAL RISK MANAGEMENT",
    r"TRADE.*?(RECEIVABLES|PAYABLES)",
    r"LEASES|INVENTORIES",
    r"(?<!before )(TAXATION|INCOME TAX)",
    r"PENSION SCHEMES|RIGHTS ISSUE|FINANCE COSTS|INVESTMENT PROPERTY|CONTRACT ASSETS",
    r"PREPAYMENTS, OTHER RECEIVABLES AND OTHER ASSETS",
    r"PREPAYMENTS AND OTHER RECEIVABLES",
    r"ACCRUALS AND OTHER PAYABLES",
    r"PROMISSORY NOTES PAYABLE",
    r"BANK BORROWINGS",
    r"DISPOSAL GROUP CLASSIFIED AS HELD FOR SALE",
    r"EVENTS AFTER THE END OF THE REPORTING PERIOD",
    r"PASSENGER SERVICE LICENCES",
    r"PLEDGED TIME DEPOSITS",
    r"PROPERTY, PLANT AND EQUIPMENT",
    r"CASH AND CASH EQUIVALENTS",
    r"EMPLOYEE BENEFIT EXPENSES",
    r"DIRECTORS’ AND SENIOR EXECUTIVES’ EMOLUMENTS",
    r"RELATED PARTY TRANSACTIONS",
    r"BANK BALANCES AND CASH",
    r"AMOUNT DUE TO A SHAREHOLDER",
    r"CONVERTIBLE PREFERENCE SHARES",
    r"INTERESTS IN SUBSIDIARIES",
    r"IMPAIRMENT LOSSES ON",
    r"recognised in the profit or loss for the year",
    r"^\(?Reversal of",
]

# Notes-Impairment
# Notes-Gain or Loss on disposal
NOTE_TITLE_PATTERN = [
    r"(\(?PROFIT\)?|\(?LOSS\)?) BEFORE (TAX|INCOME)",
    r"LOSS (FROM|before)",
    r"(LOSS|PROFIT).*?FOR.*?THE.*?(YEAR|PERIOD)",
    r"O\s?THER\s*?[(（]?(GAINS|INCOME|LOSS(es)?|NET|REVENUE|(OPERATING )?EXPENSES?|items?)",
    r"EXPENSES? BY NATURE",
    r"PROFIT FROM OPERATING ACTIVITIES",
    r"OTHER OPERATING INCOME",
    # 概率低
    # r'^REVENUE$', # 42882
    r"OPERATING.*?(PROFIT|LOSS)",
    r"DISCONTINUED OPERATION",
    r"REVERSAL/(PROVISION) OF ALLOWANCE FOR EXPECTED CREDIT LOSS",
    r"GAIN ON DISPOSAL OF SUBSIDIARIES",
    r"Disposal of Engineering Services Division",
    r"ASSETS IMPAIRMENT LOSS",
]
NEGLECT_NOTE_TITLE_ABOVE_PATTERN = [
    r"NOTE TO THE CONSOLIDATED STATEMENT OF CASH FLOWS$",
    r"現金及現金等值物",
]

NEGLECT_NOTE_IMPAIRMENT_BLACK_PATTERN = [
    r"CASH AND CASH EQUIVALENTS",
]

# Segment Notes-Gain or Loss on Disposal
# Segment Notes-Impairment
SEGMENT_TITLE_PATTERN = [
    r"SEGMENT\s*?(INFORMATION|REVENUE)",
    r"following tables present revenue",
]

LOSS_PROFIT = r"(profit|loss|profit/\s?loss|loss/\s?profit)"


GAIN_LOSS_ON_DISPOSAL_PATTERN = [
    r"__regex__(gain|loss|profit).*?((upon|on|from|deemed|relating to)\s?(partial)?\s?disposals?|disposals? of)",
    r"__regex__(gain|loss|profit).*?(disposal|disposed)",
    r"__regex__note to consolidated statements of cash flows",
    r"__regex__upon disposals of subsidiaries",  # 51526
    r"__regex__Gain on surrender of investment properties under land resumption",  # 53401
    r"__regex__income from disposal of",  # 53401
]

GAIN_LOSS_BLACK_PATTERN = [
    r"__regex__Finance costs",
    r"__regex__(gain|loss|profit).*?on derivative financial instruments",
    r"__regex__(gain|loss|profit).*?on investments at fair value through profit or loss",
    r"__regex__Sales of scrap materials",
    r"__regex__Fixed assets",
]

predictor_options = [
    {
        "path": ["Each Individual Asset"],
        "sub_primary_key": ["Time Period", "Currency", "Unit", "Account", "Value"],
        "primary_key_include_subtotal": "Value",
        "strict_group": True,
        "models": [
            {
                "name": "add_subtotal",
                "feature_from": "row_headers",
                "table_title_pattern": BS_TITLE_PATTERN,
                "neglect_title_pattern": NEGLECT_BS_TITLE_PATTERN,
                "neglect_title_above_pattern": NEGLECT_BS_TITLE_ABOVE_PATTERN,
                "neglect_syllabus_pattern": NEGLECT_BS_SYLLABUS_PATTERN,
                "multi_elements": True,
                "filter_later_elements": True,
                "sub_total_headers_features": [
                    TOTAL_ASSETS_PATTERN,
                ],
                "only_inject_features": True,
                "feature_white_list": [r"__regex__.*"],
            },
        ],
    },
    {
        "path": ["Short Term Investments"],
        "sub_primary_key": ["Time Period", "Account", "Value"],
        "strict_group": True,
        "models": [
            {
                "name": "add_subtotal",
                "feature_from": "row_headers",
                "multi_elements": True,
                "filter_later_elements": True,
                "sub_total_headers_features": [
                    TOTAL_ASSETS_PATTERN,
                    "properties for sale",
                    r"^(non-)?Current assets and liabilities$",  # 42827
                ],
                "feature_black_list": [
                    r"__regex__cash and (bank balances|cash equivalents)",
                    r"__regex__(intangible|right-of-use) assets",
                    r"__regex__property, plant and equipment",
                    r"__regex__^inventories$",
                    r"__regex__investment properties",
                    r"__regex__Prepayments?",
                    r"__regex__receivables",
                    r"__regex__deposits and other",
                ],
                "table_title_pattern": BS_TITLE_PATTERN,
                "neglect_title_pattern": NEGLECT_BS_TITLE_PATTERN,
                "neglect_title_above_pattern": NEGLECT_BS_TITLE_ABOVE_PATTERN,
                "neglect_syllabus_pattern": NEGLECT_BS_SYLLABUS_PATTERN,
                "feature_white_list": [
                    #### a long regex start
                    r"__regex__(Investments?|Financial instruments?|Financial assets?|Other financial assets?|"
                    r"Equity investments?|Equity instruments?|Equity securities?|Debt instruments?|Debt investments?|"
                    r"Debt securities?|Other investments?|Financial investments?).*?"
                    r"(FVTPL|at fairs? value through profit (or|and) loss|held for trading)",
                    #### a long regex end
                    r"__regex__Investments? in securities",
                    r"__regex__Trading (portfolio investments|securities)",
                    r"__regex__(FVTOCI|FVPL|FVOCI|fairs? value through other comprehensive income|amortised costs?)$",
                    r"__regex__Other\s?financial assets$",
                    r"__regex__(derivative)?\s?financial instruments?",
                    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/620#note_167345 去掉了 Other receivables
                    r"__regex__(Debt|Other|Equity)?\s?(investment|securitie)s?$",
                    r"__regex__impairment loss recognised in respect of",
                    r"__regex__Short-term bank deposits and restricted dep",
                    r"__regex__stock",
                    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/620#note_167345 去掉了 Other receivables
                    # r'__regex__Other receivables',
                    r"__regex__Investments in debt instruments",  # 53428
                ],
            },
        ],
    },
    {
        "path": ["Total Assets"],
        "sub_primary_key": ["Time Period", "Account", "Currency"],
        "strict_group": True,
        "models": [
            {
                "name": "cash_bank_balances",
                "multi_elements": True,
                "filter_later_elements": True,
                "feature_from": "row_headers",
                "table_title_pattern": BS_TITLE_PATTERN,
                "neglect_title_pattern": NEGLECT_BS_TITLE_PATTERN,
                "neglect_title_above_pattern": NEGLECT_BS_TITLE_ABOVE_PATTERN,
                "neglect_syllabus_pattern": NEGLECT_BS_SYLLABUS_PATTERN,
                "only_inject_features": True,
                "feature_white_list": [
                    rf"__regex__^{x}\s*((非?流[動动]|[总總])?(資產|资产)(總額|总额)?)?$" for x in TOTAL_ASSETS_PATTERN
                ]
                + [
                    r"__regex__^(non-)?(current )?assets and liabilities$",  # 特殊表格 42827
                    # r'__regex__assets? (classified (as )?)?held.for.sales?',  # 42320 先去除
                ],
            },
        ],
    },
    {
        "path": ["Significant Investment"],
        "sub_primary_key": ["Time Period", "Account", "Value"],
        "strict_group": True,
        "models": [
            {
                "name": "add_subtotal",
                "feature_from": "row_headers",
                "sub_total_headers_features": [
                    TOTAL_ASSETS_PATTERN,
                ],
                "feature_black_list": [
                    r"__regex__debt|investment properties|derivative financial instruments",
                    r"__regex__investments in (associate|joint venture|subsidiary)",
                    r"__regex__Interests in associates?",
                    r"__regex__derivative",  # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/621#note_170140
                    r"__regex__using the equity method",  # docs_scriber/-/issues/621#note_170141
                ],
                "table_title_pattern": BS_TITLE_PATTERN,
                "neglect_title_pattern": NEGLECT_BS_TITLE_PATTERN,
                "neglect_title_above_pattern": NEGLECT_BS_TITLE_ABOVE_PATTERN,
                "neglect_syllabus_pattern": NEGLECT_BS_SYLLABUS_PATTERN,
                "multi_elements": True,
                "filter_later_elements": True,
                "feature_white_list": [
                    r"__regex__Other financial assets",
                    r"__regex__Financial assets? at fair value through profit (and|or) loss",
                    r"__regex__Financial assets? measured at fair value",
                    r"__regex__Equity Investments",
                    r"__regex__Equity financial assets at fair value through other comprehensive income",
                    r"__regex__(Investments in )?financial assets at amortised cost",
                    r"__regex__Investments in equity instruments designated at fair value through other comprehensive income",
                    r"__regex__short-term investments?",
                    r"__regex__Investments? in Equity (Investees?|Instruments?|Securities|Security)",
                    r"__regex__Investment in Fund (Entities|Entity)",
                    r"__regex__Held for trading (financial assets|investments)",
                ],
            },
        ],
    },
    {
        "path": ["Cash and Bank Balances"],
        "sub_primary_key": ["Time Period", "Account", "Value"],
        "strict_group": True,
        "location_threshold": 0.01,
        "models": [
            {
                "name": "add_subtotal",
                "feature_from": "row_headers",
                "sub_total_headers_features": [
                    TOTAL_ASSETS_PATTERN,
                ],
                "feature_white_list": [
                    r"__regex__(Bank|Cash and|Cash|Fixed|Regulatory|Restricted|Structured|term|time|pledged|statutory) deposits?|(bank|cash) balances?|Cash equivalents?|Deposits held by/with|Restricted funds",
                    r"__regex__term deposits with initial term over three months",
                    r"__regex__Deposits in (a )?Financial Institution",
                    r"__regex__Liquid funds",
                    r"__regex__stock",
                    r"__regex__Cash and amounts due from banks",
                    r"__regex__other financial institutions",
                    r"__regex__Balances with the Central Bank",
                    r"__regex__Deposit placed with non-bank financial institutions",  # 53397
                    r"__regex__Restrict(ed)? cash",  # 2022|00700
                ],
                "feature_black_list": [
                    r"__regex__current\s*?assets$",
                    r"__regex__profit or loss",
                    r"__regex__bank balances held on behalf of (clients?|customers?)",
                    r"__regex__Bank balances-client account",
                    r"__regex__^deposits$",
                    r"__regex__deposits, prepayments and other receivables",
                    r"__regex__on behalf of customers|deposit paid|security deposits?|restricted fund|client account",
                    r"__regex__cash surrender value of life insurance",
                    r"__regex__trust accounts of shares dealing clients",
                    r"__regex__Equity instruments at FVTOCI",
                ],
                "table_title_pattern": BS_TITLE_PATTERN,
                "neglect_title_pattern": NEGLECT_BS_TITLE_PATTERN,
                "neglect_title_above_pattern": NEGLECT_BS_TITLE_ABOVE_PATTERN,
                "neglect_syllabus_pattern": NEGLECT_BS_SYLLABUS_PATTERN,
                "multi_elements": True,
                "filter_later_elements": True,
            },
            # # 部分文档 sub_total_headers + table tile 解析有问题 下面的配置会尝试不匹配sub_total_headers+table tile 验证后recall prec均提供
            # {
            #     'name': 'add_subtotal',
            #     'feature_from': 'row_headers',
            #     'feature_white_list': [
            #         r'__regex__(Bank|Cash and|Cash|Fixed|Regulatory|Restricted|Structured|term|time|pledged|statutory) deposits?|(bank|cash) balances?|Cash equivalents?|Deposits held by/with|Restricted funds',
            #         r'__regex__term deposits with initial term over three months',
            #         r'__regex__Deposits in (a )?Financial Institution',
            #         r'__regex__Liquid funds',
            #         r'__regex__stock',
            #         r'__regex__Cash and amounts due from banks',
            #         r'__regex__other financial institutions',
            #         r'__regex__Balances with the Central Bank',
            #     ],
            #     'feature_black_list': [
            #         r'__regex__current\s*?assets$',
            #         r'__regex__profit or loss',
            #         r'__regex__bank balances held on behalf of (clients?|customers?)',
            #         r'__regex__Bank balances-client account',
            #         r'__regex__^deposits$',
            #         r'__regex__deposits, prepayments and other receivables',
            #         r'__regex__on behalf of customers|deposit paid|security deposits?|restricted fund|client account',
            #         r'__regex__cash surrender value of life insurance',
            #         r'__regex__trust accounts of shares dealing clients',
            #         r'__regex__Equity instruments at FVTOCI',
            #     ],
            # },
        ],
    },
    {
        "path": ["Revenue"],
        "sub_primary_key": ["Time Period", "Account", "Value"],
        "strict_group": True,
        "models": [
            {
                "name": "revenue",
                "multi_features": False,
                # 'sub_total_as_feature': True,
                # 'multi_rows': True,
                "feature_from": "row_headers",
                "feature_white_list": [
                    r"__regex__^revenue$",
                    r"__regex__total\s*?(net)?\s*?revenue$",
                    r"__regex__revenue from (customers|principal)",
                    r"__regex__Lease\s*?rental\s*?income",
                    r"__regex__Interest\s*?and\s*?fee\s*?income",
                    r"__regex__Net revenues",
                    r"__regex__Total operating revenue",  # 58871
                    r"__regex__(?<!Non-)Operating income",  # 58865
                ],
                "feature_black_list": [r"__regex__Other Income"],
                "table_title_pattern": PL_TITLE_PATTERN
                + [
                    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/1043#note_252296
                    r"unaudited comparative figure",
                ],
                "neglect_title_pattern": PL_IMPAIRMENT_BLACK_PATTERN,
                "col_black_feature": [
                    r"biological assets",
                    r"note",
                ],
            },
            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/1043#note_252291
            {
                "name": "revenue",
                "multi_features": False,
                "sub_total_as_feature": True,
                "multi_rows": True,
                "max_row_num": 6,  # 三行 两列
                "feature_from": "row_headers",
                "feature_white_list": [
                    r"__regex__^revenue$",
                    r"__regex__total\s*?(net)?\s*?revenue$",
                    r"__regex__revenue from (customers|principal)",
                    r"__regex__Lease\s*?rental\s*?income",
                    r"__regex__Interest\s*?and\s*?fee\s*?income",
                    r"__regex__Net revenues",
                    r"__regex__Total operating revenue",  # 58871
                    r"__regex__(?<!Non-)Operating income",  # 58865
                ],
                "feature_black_list": [r"__regex__Other Income"],
                "table_title_pattern": PL_TITLE_PATTERN
                + [
                    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/1043#note_252296
                    r"unaudited comparative figure",
                ],
                "neglect_title_pattern": PL_IMPAIRMENT_BLACK_PATTERN,
                "col_black_feature": [
                    r"biological assets",
                    r"note",
                ],
            },
        ],
    },
    {
        "path": ["Net Profit or Loss"],
        "sub_primary_key": ["Time Period", "Account", "Value"],
        "strict_group": True,
        "models": [
            {
                "name": "net_profit_or_loss",
                "multi_features": False,
                "feature_from": "row_headers",
                "full_match": False,
                "feature_white_list": [
                    # a long regex start
                    rf"__regex__^{LOSS_PROFIT}"
                    r"( and total comprehensive (loss|income/expenses?|(loss|expenses?)/income|expenses?|income))?"
                    " for the (period|year)( attributable to (the )?(owners|equity (share)?holders) of the company)?$",
                    # a long regex end
                    # a long regex start
                    rf"__regex__^{LOSS_PROFIT}"
                    r"( and total comprehensive (loss|income/expenses?|(loss|expenses?)/income|expenses?|income))?"
                    "( attributable to (the )?(owners|equity (share)?holders) of the company)?"
                    " for the (period|year)$",
                    # a long regex end
                    rf"__regex__^{LOSS_PROFIT} for the (period|year)( (carried|brought) forward)",
                    r"__regex__Loss for the period.*?(Company|interests|total)",  # 45798 45701 46436
                    r"__regex__loss for the year before allocation",  # 41086
                    # a long regex start
                    rf"__regex__^{LOSS_PROFIT} for the (period|year)"
                    r" and total comprehensive income for the (period|year)",
                    # a long regex end
                    r"__regex__net income / loss for the (period|year)",  # 43240
                    rf"__regex__^{LOSS_PROFIT} after tax",
                    rf"__regex__^{LOSS_PROFIT}\s?for the year.*?(Company|interests|total)",
                    r"__regex__^net income$",  # 51510
                    r"__regex__net profit represented by a sign of.*?–.*?for net loss",  # 51510
                ],
                "feature_black_list": [
                    r"__regex__^total comprehensive|^owners of the company",
                    r"__regex__gross|before tax|operations",
                ],
                "table_title_pattern": PL_TITLE_PATTERN,
            },
        ],
    },
    {
        "path": ["Gross Profit or Loss"],
        "sub_primary_key": ["Time Period", "Account", "Value"],
        "strict_group": True,
        "models": [
            {
                "name": "revenue",
                "multi_features": False,
                "feature_from": "row_headers",
                "feature_black_list": [
                    r"__regex__^revenue",
                    r"__regex__^cost",
                    r"__regex__^(loss/profit|profit/loss)",
                    r"__regex__operating costs",
                    r"__regex__^gross proceeds from",
                    r"cost of sales gross profit|e s y s t e m s",
                ],
            },
        ],
    },
    {
        "path": ["Cost of Sales"],
        "sub_primary_key": ["Time Period", "Account", "Value"],
        "strict_group": True,
        "models": [
            {
                "name": "revenue",
                "multi_features": False,
                "feature_from": "row_headers",
                "table_title_pattern": PL_TITLE_PATTERN,
                "feature_black_list": [
                    r"__regex__^(staff costs?|revenue|other expenses)$",
                ],
            },
        ],
    },
    {
        "path": ["PL-Gain or Loss on Disposal"],
        "sub_primary_key": ["Time Period", "Account", "Value"],
        "strict_group": True,
        "models": [
            {
                "name": "revenue",
                "feature_from": "row_headers",
                "multi_elements": True,
                "multi_rows": True,
                "table_title_pattern": PL_TITLE_PATTERN,
                "feature_white_list": GAIN_LOSS_ON_DISPOSAL_PATTERN,
                "feature_black_list": GAIN_LOSS_BLACK_PATTERN,
            },
        ],
    },
    {
        "path": ["Notes-Gain or Loss on Disposal"],
        "sub_primary_key": ["Time Period", "Account", "Value"],
        "strict_group": True,
        "location_threshold": 0.01,
        "models": [
            {
                "name": "cash_bank_balances",
                "feature_from": "row_headers",
                "multi_elements": True,
                "sub_total_as_feature": True,
                "table_title_pattern": NOTE_TITLE_PATTERN,
                "neglect_title_above_pattern": NEGLECT_NOTE_TITLE_ABOVE_PATTERN,
                "feature_white_list": GAIN_LOSS_ON_DISPOSAL_PATTERN,
                "feature_black_list": GAIN_LOSS_BLACK_PATTERN
                + [
                    r"__regex__Disposal of packaging materials",
                    r"__regex__Sale of properties",
                    r"__regex__Non-controlling interests",
                ],
            },
        ],
    },
    {
        "path": ["Segment Notes-Gain or Loss on Disposal"],
        "sub_primary_key": ["Time Period", "Account", "Value"],
        "strict_group": True,
        "location_threshold": 0.01,
        "models": [
            {
                "name": "cash_bank_balances",
                "feature_from": "row_headers",
                "multi_elements": True,
                "table_title_pattern": SEGMENT_TITLE_PATTERN,
                "feature_white_list": GAIN_LOSS_ON_DISPOSAL_PATTERN,
                "feature_black_list": GAIN_LOSS_BLACK_PATTERN,
            },
        ],
    },
    {
        "path": ["PL-Impairment"],
        "sub_primary_key": ["Time Period", "Account", "Value"],
        "strict_group": True,
        "models": [
            {
                "name": "impairment",
                "feature_from": "row_headers",
                "multi_elements": True,
                "filter_later_elements": True,
                "sub_total_as_feature": True,
                "feature_black_list": [
                    r"__regex__before impairment",
                ]
                + IMPAIRMENT_BLACK_LIST,
                "feature_white_list": IMPAIRMENT_WHITE_LIST
                + [
                    r"__regex__impairment loss under ecl model on trade receivables",
                    r"__regex__loss allowance for expected credit loss",
                    r"__regex__^provision (for|of)",
                ],
                "table_title_pattern": PL_TITLE_PATTERN,
                "neglect_title_pattern": NOTE_IMPAIRMENT_BLACK_PATTERN + PL_IMPAIRMENT_BLACK_PATTERN,
            },
        ],
    },
    {
        "path": ["Notes-Impairment"],
        "sub_primary_key": ["Time Period", "Account", "Value"],
        "strict_group": True,
        "models": [
            {
                "name": "impairment",
                "feature_from": "row_headers",
                "sub_total_as_feature": True,
                "multi_elements": True,
                "feature_black_list": [
                    r"__regex__continuing operations",
                    r"__regex__Foreign exchange differences",
                ]
                + IMPAIRMENT_BLACK_LIST,
                "feature_white_list": IMPAIRMENT_WHITE_LIST
                + [
                    r"__regex__impairment loss under ecl model on trade receivables",
                    r"__regex__Impairment of",
                    r"__regex__Loss allowance recognised",
                    r"__regex__^[-—–] property, plant and equipment$",
                    r"__regex__^[-—–] inventories$",
                    r"__regex__^[-—–] advances to customers in margin financing$",
                ],
                "table_title_pattern": NOTE_TITLE_PATTERN,
                "neglect_title_pattern": NOTE_IMPAIRMENT_BLACK_PATTERN + PL_IMPAIRMENT_BLACK_PATTERN,
                "neglect_title_above_pattern": NEGLECT_NOTE_TITLE_ABOVE_PATTERN,
                "neglect_syllabus_pattern": NEGLECT_NOTE_IMPAIRMENT_BLACK_PATTERN,
            },
        ],
    },
    {
        "path": ["Segment Notes-Impairment"],
        "sub_primary_key": ["Time Period", "Account", "Value"],
        "strict_group": True,
        "models": [
            {
                "name": "impairment",
                "feature_from": "row_headers",
                "multi_elements": True,
                "feature_black_list": IMPAIRMENT_BLACK_LIST,
                "feature_white_list": IMPAIRMENT_WHITE_LIST,
                "neglect_title_pattern": NOTE_IMPAIRMENT_BLACK_PATTERN + PL_IMPAIRMENT_BLACK_PATTERN,
                "table_title_pattern": SEGMENT_TITLE_PATTERN,
            },
        ],
    },
    {
        "path": ["Material Impairment"],
        "models": [
            {
                "name": "score_filter",
                "aim_types": ["PARAGRAPH"],
                "threshold": 0.1,
            },
        ],
    },
]


prophet_config = {"depends": {}, "predictor_options": predictor_options}
