import logging
import re
from collections import defaultdict
from copy import copy
from functools import lru_cache
from typing import List

from pydantic import BaseModel as PydanticBaseModel

from remarkable.common.common_pattern import R_MIDDLE_DASHES
from remarkable.common.constants import AnswerValueEnum
from remarkable.common.pattern import PatternCollection
from remarkable.common.util import clean_txt
from remarkable.pdfinsight.parser import ParsedTableCell
from remarkable.pdfinsight.reader_table import PdfinsightTable
from remarkable.predictor.models.base_model import BaseModel
from remarkable.predictor.schema_answer import CellCharResult, PredictorResult, TableCellsResult
from remarkable.services.chatgpt import OpenAIClient

logger = logging.getLogger(__name__)

P_INVALID_E7 = PatternCollection(
    [
        rf"^305[{R_MIDDLE_DASHES}]3\s*[A-Za-z\(\)（）\d\s]+(Scope\s*3|GHG\s*emissions)\s+(?:P\d+|[\dA-Za-z\s]*Data\s*Summary)$",
        r"started\s*to\s*disclose[^.]*?emissions",
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7378#note_737977
        r"Reduce[^.]*?carbon intensity from[^.]*?per square metre",
    ],
    re.I,
)

P_CATEGORY_TITLE = PatternCollection(
    r"Category\s*No",
    re.I,
)


class E7AnswerModel(PydanticBaseModel):
    result: bool = False


def is_invalid_e7_answer(cells: List[ParsedTableCell], report_year=None):
    if not cells:
        return False
    row_cells = defaultdict(list)
    for cell in sorted(cells, key=lambda x: (x.rowidx, x.colidx)):
        row_cells[cell.rowidx].append(cell)
    if any(P_INVALID_E7.nexts(clean_txt(" ".join(cell.text for cell in r_cells))) for r_cells in row_cells.values()):
        return True
    row_text = " ".join(cell.text for r_cells in row_cells.values() for cell in r_cells)
    # 如只有一行，且为表格的头部，存在Category No， 则不符合
    if set(row_cells.keys()) == {
        0,
    } and any(P_CATEGORY_TITLE.nexts(cell.clean_text) for cell in row_cells[0]):
        return True
    # 如果存在report year, 检查是否相同
    if report_year and all(
        not BaseModel.check_content_by_report_year(" ".join(cell.clean_text for cell in cells), report_year)
        for cells in row_cells.values()
    ):
        return True
    if check_e7_text_by_llm(row_text):
        # 有Scope 3 数据
        return False
    return True


# E7的后处理函数
def check_e7_answer(answers: list[PredictorResult], **kwargs):
    flat_answers = BaseModel.get_common_predictor_results(answers)
    new_answers = []
    for answer in flat_answers:
        for result in answer.element_results:
            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6347#note_713003
            # 暂仅针对单元格内同一行的内容，
            if not isinstance(result, CellCharResult):
                continue
            if is_invalid_e7_answer(result.parsed_cells):
                break
        else:
            new_answers.append(answer)
    if not new_answers:
        return []
    results = []
    if new_answers:
        for answer in new_answers:
            if check_e7_text_by_llm(answer.text):
                results.append(answer)
    if results:
        # 当枚举值是C时， 看起是否是0/insignificant/NA/ immaterial
        e7_normal_data = check_e7_has_normal_data(results)
        kwargs["predictor"].prophet.metadata["e7_normal_data"] = e7_normal_data
    return results


def check_e7_has_normal_data(answers):
    flat_answers = BaseModel.get_common_predictor_results(answers)
    enum_value = {answer.answer_value for answer in flat_answers}
    if len(enum_value) and list(enum_value)[0] == AnswerValueEnum.COMPLY.value:
        for answer in flat_answers:
            for element_result in answer.element_results:
                if isinstance(element_result, (CellCharResult, TableCellsResult)):
                    element = element_result.element
                    fake_element = copy(element)
                    fake_element["cells"] = {cell.indexstr: cell.raw_cell for cell in element_result.parsed_cells}
                    fake_element["origin_cells"] = {
                        cell.indexstr: cell.raw_cell for cell in element_result.parsed_cells
                    }
                    answer_text = PdfinsightTable(fake_element).markdown
                else:
                    answer_text = element_result.text
                if e7_has_normal_data(answer_text):
                    return True
    return False


@lru_cache()
def check_e7_text_by_llm(text):
    """是否包含正确的Scope3的数据"""
    messages = [
        {
            "role": "system",
            "content": """你是一位专业的ESG数据分析助手， 请帮我分析提供的文本中是否有具体的Scope 3的数据。如果有具体的数据则返回True, 没有则返回False

`– 或 / 或 not disclosed` 类似的描述 返回False

`0/insignificant/NA/ immaterial` 类似的描述返回True
            """,
        },
        {"role": "user", "content": text},
    ]
    openai_client = OpenAIClient()
    try:
        llm_res = openai_client.send_message(messages, response_format=E7AnswerModel)
    except Exception as e:
        logger.exception(e)
        return []
    response_model: E7AnswerModel = llm_res.parsed
    return response_model.result


@lru_cache()
def e7_has_normal_data(text):
    """Scope3的数据 是否是"""
    messages = [
        {
            "role": "system",
            "content": """你是一位专业的ESG数据分析助手， 请帮我分析提供的文本中是否有具体的Scope 3的数据。如果有具体的数据则返回True, 没有则返回False

`0/insignificant/NA/ immaterial` 类似的描述返回False
            """,
        },
        {"role": "user", "content": text},
    ]
    openai_client = OpenAIClient()
    try:
        llm_res = openai_client.send_message(messages, response_format=E7AnswerModel)
    except Exception as e:
        logger.exception(e)
        return []
    response_model: E7AnswerModel = llm_res.parsed
    return response_model.result
