from __future__ import annotations

import logging
import re
from datetime import datetime
from functools import lru_cache

from remarkable.common.common_pattern import (
    P_CONTINUED,
    P_PERIOD_SEPARATOR,
    P_SEN_SEPARATOR,
    R_CG_CHAPTER_TITLES,
    R_CHAPTER_PREFIX,
    R_CHAPTER_SUFFIX,
    R_CI_CHAPTER_TITLES,
    R_CURRENCY,
    R_DR_CHAPTER_TITLES,
    R_INED_REPORT_TITLES,
    R_MANAGEMENT_TITLES,
    R_MDA_CHAPTER_TITLES,
    R_MIDDLE_DASHES,
    R_NOTES_CHAPTER_TITLES,
)
from remarkable.common.pattern import (
    MATCH_NEVER,
    MatchMulti,
    NeglectPattern,
    <PERSON>ternCollection,
    PositionPattern,
    SplitBeforeMatch,
)
from remarkable.common.protocol import SearchPatternLike
from remarkable.predictor.common_pattern import R_DATES, R_SHARE_AWARD
from remarkable.predictor.hkex_predictor.models.yoda_layer.filter import ParaTextFilter
from remarkable.predictor.hkex_predictor.pattern import (
    <PERSON>_<PERSON><PERSON>,
    <PERSON>_<PERSON><PERSON><PERSON>,
    <PERSON>_<PERSON>IM_MONTH,
)
from remarkable.predictor.models.base_model import R_PRIMARY_ROLE

# ------一些常见的一级章节目录------
P_NOTE_CHAPTER = MatchMulti.compile(*R_NOTES_CHAPTER_TITLES, operator=any)
P_DR_CHAPTER = MatchMulti.compile(*R_DR_CHAPTER_TITLES, operator=any)
P_CG_CHAPTER = MatchMulti.compile(*R_CG_CHAPTER_TITLES, operator=any)
P_MDA_CHAPTER = MatchMulti.compile(*R_MDA_CHAPTER_TITLES, operator=any)
P_CI_CHAPTER = MatchMulti.compile(*R_CI_CHAPTER_TITLES, operator=any)

# ------JURA5 规则CG------
R_HAVE = r"(have|ha[sd])"
R_ALL = r"(all|whole|entire)"
R_FULLY = r"(fully|completely|entirely)"
R_NOT = r"((^|\s)(no|not(\s*yet)?|nor|neither|none)|n['‘’]t)\s+"
R_ENG_ORDER = r"\b(\d+(st|nd|rd|th)|first|second|third|[a-z]{3,5}th)\b"
R_ENG_NUMBER = r"\b(an?|one|two|three|four|five|six|seven|eight|nine|ten|eleven|twelve|(thir|fou?r|fif|six|seven|eigh|nin)teen|twenty)\b"
R_ONLY_ENG_NUMBER = r"\b(one|two|three|four|five|six|seven|eight|nine|ten|eleven|twelve|(thir|fou?r|fif|six|seven|eigh|nin)teen|twenty)\b"
R_CHAIR_CEO = r"(Chairlady|Chairwoman|Chairman|chairmen|President|Chief Executive(\sOfficer)?|G?CEO|Managing Director|General manager)"
R_BRACKET_CHAIR_CEO = rf"[(（]?({R_CHAIR_CEO})[)）]?"
R_APPELLATION = r"\b(Mr|Mrs|Ms|Mdm|Dr|Prof(essor)?|Madam|Miss)(\.|\s)"
P_APPELLATION = re.compile(rf"{R_APPELLATION}")
R_ENG_TIMES = rf"\b(\d{{1,2}}|once|twice|{R_ENG_NUMBER})\b"
R_THE_BOARD = r"(its|our|the)\s*board"
P_THE_BOARD = MatchMulti.compile(R_THE_BOARD, operator=any)
R_OVERALL = r"over(all|see)|whole"
R_AND = r"(and|&)\s*"
R_EC = r"\bExecutive\s*Committ?ee?"
P_EC = MatchMulti.compile(R_EC, operator=any)
R_CC = r"Compliance\s*Committ?ee?"
P_CC = MatchMulti.compile(R_CC, operator=any)
R_AC = rf"(the\s*)?(\bAudit\s*Committ?ee?|\b(AC|BARMC)|\bAudit\s*({R_AND})?([)（）a-z(]+\s+){{1,5}}Committ?ee?|審[計核]委員會)\b(?!\s*member)"
P_AC = MatchMulti.compile(R_AC, operator=any)
R_NC = rf"(Nominati(on|ng)\s*Committ?ee?|\bNR?C\b|Nominati(on|ng)\s*{R_AND}([a-z]+\s*){{1,2}}Committ?ee?)"
R_CG = r"(Corporate\s*governance|\bI?C?CG\b|企業管治及法規委員會)"
R_CG_COMMITTEE = r"(Corporate\s*Governance\s*Committee|\bI?C?CG\s*Committee|企業管治及法規委員會)"
R_RC = rf"((remuneration|compensation)\s*(management\s*)?committ?ee?|(the\s*)?[BN]?RC\b|薪酬委員會|(remuneration|compensation)\s*{R_AND}([)（）a-z(]+\s+){{1,5}}committ?ee?)"

R_RMC = rf"risk\s*management\s*committee|\bRMC\b|風險管理委員會|\brisk\s*(management\s*|Prevention\s*)?{R_AND}([)（）a-z(]+\s+){{1,5}}committ?ee?"
R_RIC = rf"(the\s*)?(\brisk\s*committ?ee?|\bRIC\b|風險委員會|\brisk\s*{R_AND}([)（）a-z(]+\s+){{1,5}}committ?ee?)"

R_ALL_COMMITTEES = [
    R_AC,
    R_NC,
    R_RC,
    R_EC,
    R_CC,
    R_RIC,
    R_RMC,
    R_CG_COMMITTEE,
    r"(strateg(ic|y)|invest(ment)?|DEVELOPMENT|supervisory)\s*committ?e",
]
R_EC_BOARD = r"board|member|director|committ?ee?|董事|成員|委員會"
R_DURING_THE_YEAR = r"(((for|in)\s*the\s*year(?!\s*end)|during\s*(\S+\s*){0,3}\s*(period|year|(\bfy)?\d{4})|in\s*\d{4}\b)|financial\s*year)"
P_DURING_THE_YEAR = MatchMulti.compile(R_DURING_THE_YEAR, operator=any)
R_DURING_THE_YEAR_NO_END = (
    r"(((for|in)\s*the\s*year|during\s*(\S+\s*){0,3}\s*(period|year|(\bfy)?\d{4})|in\s*\d{4}\b)|financial\s*year)"
)
R_ATTEND = rf"attend|出席|meeting|會議|\bmet\s*{R_ENG_TIMES}"
P_ATTEND = MatchMulti.compile(R_ATTEND, operator=any)
# AC-E(c)、RC-E(c)、Ric-E(c)匹配表格时单元格正则
R_EC_CELL = r"attendance|Eligible\s*to\s*attend|(\bno\.|number)\s*of\s*meetings?|meetings?\s*attended"
P_EC_CELL = MatchMulti.compile(R_EC_CELL, operator=any)
R_EC_NO_MEET_NEG = r"Note\s*\d+[:：]|\b(but|however)\b"
R_EC_NEG = r"allowance"
R_RC_MEETING = [
    rf"The {R_RC} met \w+ times?",
    rf"The {R_RC} (normally )?meets at least once",
    r"held.*?meetings?",
    r"meetings were held",
    r"reviewed matters relating to remuneration",
    r"discussed the remuneration policy",
    rf"The {R_RC} (held|convened).*?meeting",
    rf"The {R_RC}.*?reference to the Company’s remuneration policy",
    rf"the {R_RC} (\s?ha[sd]).*?reviewed",
    r"held.*?meetings?",
    r"meetings were held",
    r"met (once|one)",
    r"reviewed matters relating to remuneration",
    r"discussed the remuneration policy",
    r"Remuneration Committee performed the following duties",
    rf"{R_RC}.*?held.*?(review|consider).*?policy",
    # rf'{R_RC}.*?(review|consider)',  # 太过宽泛
]

# http://************:55647/#/project/remark/245364?treeId=8568&fileId=66252&schemaId=28&projectId=17&schemaKey=E(d)(iv)-2
P_MEETING_SKIP = MatchMulti.compile(r"meeting", r"\bnot?\s*less\s*than", r"\bto\s*review", operator=all)
P_SKIP_FORMED = MatchMulti.compile(r"was.*formed in \d{4}", operator=any)
R_AS_FOLLOW_END = rf"([{R_MIDDLE_DASHES}:：]|(below|follow(ing)?)|({R_DURING_THE_YEAR})|\bended\s*{R_DAY})$"
P_AS_FOLLOW_END = MatchMulti.compile(R_AS_FOLLOW_END, operator=any)
# 召开、举行
R_HOLD = r"(h[eo]ld|convened?|call(ed)?|organized?|conduct(ed)?|舉行)"
# http://************:55647/#/project/remark/245239?treeId=37579&fileId=66377&schemaId=28&projectId=17&schemaKey=E(d)(i)-2
# http://************:55647/#/project/remark/245387?treeId=5364&fileId=66229&schemaId=28&projectId=17&schemaKey=E(d)(i)-2
R_INTERNAL_AUDIT = r"(?<!its )\b((internal\s*audit)(ing)?\b|\b(IA)\b)|conduct\s*an\s*(internal\s?audit)(ing)?\b"
R_RISK_CONTROL = r"\brisks?\b(?!\s*(management\s*)?committee)|internal\s*control(?!\s*department)|\bERM\b"
P_CHAPTER_RISK = NeglectPattern.compile(match=r"\brisks?\b|internal\s*(audit|control)", unmatch=r"(?<!internal )audit")
R_SPACE = r"\s*"
R_EXIST_SPACE = r"\s+"

R_CHAIRMAN = r"[(（]?(chairlady|chairwoman|chairman|managing director|general manager|chief executive)".replace(
    " ", R_SPACE
)
R_CEO = r"chief\s*executive(\s*officer)?|\bg?ceo\b|the\s*managing\s*director"
R_SEGREGATED_CEO_CHAIRMAN = [
    rf"segregated the roles of {R_CHAIRMAN}.*and {R_CEO}",
]

R_NOT_SEGREGATED_CEO_CHAIRMAN = [rf"{R_CHAIRMAN}.*and {R_CEO}.*?not been segregated"]

R_NULL_VALUE = r"(nil|[–—]|N/A)"

# 独立非执行董事
R_INED = r"(\bINEDs?\b|independent\s*non-executive\s*directors?)"
P_INED = PatternCollection(R_INED, re.I)

R_DIRECTOR = rf"({R_INED}|non-executive\s*directors?|executive\s*directors?|directors?)"

P_RETIRING_DIRECTOR = PatternCollection([r"(Re-electing|retiring|re-election\s*(as)?)\s*Directors"], re.I)

P_NEED_IGNORE = MatchMulti.compile(
    P_CONTINUED,
    rf"{R_CHAPTER_PREFIX}BOARD\s*COMMITTEES{R_CHAPTER_SUFFIX}",
    # http://************:55647/#/project/remark/247858?treeId=6649&fileId=61587&schemaId=28
    rf"{R_CHAPTER_PREFIX}(corporate\s*|governance\s*|report\s*){{1,2}}{R_CHAPTER_SUFFIX}",
    *R_CG_CHAPTER_TITLES,
    *R_INED_REPORT_TITLES,
    operator=any,
)

IGNORE_CG_CHAPTER_TITLE_FILTER = ParaTextFilter(pattern=P_NEED_IGNORE)

R_COMPOSE = rf"\b((?<!among )members?|compris|consist|includ|made\s*up|compos|Construct|Assembl|serve[ds]?\b|ha(ve|[sd])\s*{R_ENG_NUMBER})"

# jur5 需要忽略的章节
R_JURA_CG_IGNORE_CHAPTER = [
    *R_DR_CHAPTER_TITLES,
    *R_NOTES_CHAPTER_TITLES,
    *R_MANAGEMENT_TITLES,
    *R_CI_CHAPTER_TITLES,
    *R_INED_REPORT_TITLES,
    r"^of Directors$",
    r"REPORT OF THE BOARD OF SUPERVISORS",
    r"Work of Professional Committees of the Board",
    r"^CORPORATE\s*INFORMATION$",
    r"^EXECUTIVE\s*DIRECTORS$",
    r"MANAGEMENT\s*DISCUSSION\s*AND\s*ANALYSIS",
]


def reg_words(min_times, max_times):
    """
    匹配单词出现频次，max_times尽量在15个以内，否则性能很差
    @param min_times 最少单词个数
    @param max_times 最大单词个数
    """
    if max_times > 15:
        logging.warning("max_times should lower than 15!")
    if min_times == max_times:
        return r"(\S+\s*){%s}" % min_times
    return r"(\S+\s*){%s,%s}" % (min_times, max_times)


# ------JURA2.1 规则LRS------
TERMINATE_TEXT = r"terminat(ed?|tion)"
R_EXPIRED_OR_ADOPTED = (
    rf"\bexpir(ed?|y)|termination|({R_BE}|ha[sd]|have)\s*terminated|to\s*terminate|\sadopted\b|bec[ao]me\s*effective"
)

number_share_str_list = [
    "(total|maximum) number of (share|securities issuable)",
    "award shares? available",
    "shares? in issue",
    "issued Shares?",
]
R_NUMBER_SHARE = f"""({"|".join(number_share_str_list)})"""

UNDER_INCENTIVE_SCHEME = [
    MatchMulti.compile(
        r"each participant under the employee incentive scheme",
        r"no maximum entitlement",
        # r"no maximum entitlement for each rsu eligible person",
        r"not adopted any share option scheme or other share scheme",
        r"shall be no limit",
        r"no limit.*?each participant",
        r"not provide.*?each rsu award scheme participant",
        r"no specific limit.*?eligible participant",
        r"no more shares.*?eligible participant",
        r"neither specified the maximum number of",
        operator=any,
    )
]

R_PARTICIPANTS_REG = r"eligible.*?participa"

R_BASE_NEGLECT_SYLLABUS_REGS = [
    NeglectPattern(
        match=re.compile(r"(share options? schemes?$|^share options?$)", re.I),
        unmatch=re.compile(r"Restricted Share Unit Scheme", re.I),
    ),
    r"^Vesting$",
    r"SHARE OPTION INCENTIVE SCHEME",
    r"retirement benefit schemes$",
    r"good mpf employer award",
    r"employees and remuneration policy",
    r"Pensions? scheme",
    r"Staff costs",
    r"Employees and Remuneration Policies",
    r"Recruiting High Caliber Talents",
    r"SHAREHOLDERS’ COMMUNICATION POLICY",
    r"RETIREMENT SCHEME",
    r"PAYMENT TRANSACTIONS",
    r"RETIREMENT BENEFIT SCHEME",
    r"EQUITY-LINKED (ARRANGEMENTS|AGREEMENTS)",
    r"ARRANGEMENTS TO PURCHASE SHARES OR DEBENTURES",
    r"^SHARE OPTION SCHEME$",
]
R_34_37_NEGLECT_SYLLABUS_REGS = R_BASE_NEGLECT_SYLLABUS_REGS + [r"^DIRECTORS?$"]

H38_NEGLECT_PATTERN_REGS = ("did not repurchase any shares",)

# https://jura-uat2.paodingai.com/#/hkex/annual-report-checking/report-review/244003?fileId=80395&schemaId=5&rule=A36&delist=0
# Free Shares
SHARE_AWARD_SCHEME_TEXT = (
    rf"({'|'.join(R_SHARE_AWARD)}|Free Shares?)"  # "(the )?((share award|RSU) (scheme|plan)|Award Share)"
)

# ------JURA2.1 规则LIST_RULE B63-B97------
R_PRICE_NUM = r"([1-9][\d.]*|0\.([1-9]|0+[1-9])\d*|[1-9][0-9]{0,2}(?:,[0-9]{3}){0,5})"
P_PRICE_NUM = re.compile(R_PRICE_NUM)

R_MONEY_UNIT = rf"\b({R_CURRENCY}|[$¥€￡])"
R_PRICE = rf"\b{R_MONEY_UNIT}?\s*{R_PRICE_NUM}"
P_PRICE_PS = MatchMulti.compile(
    rf"(^|\t)\s*{R_PRICE}(\t|$|\s*[(（])",
    R_PRICE,
    rf"(is|was|are|were|be|equals?\s*to)\s+{R_PRICE_NUM}\b",
    rf"{R_PRICE}\s+per\s",
    operator=any,
)

R_OPTION_KEYWORD = r"(\b(shares?\s*)?options?\b|期權|購股權)"
P_OPTION_KEYWORD = PatternCollection(R_OPTION_KEYWORD, flags=re.I)
R_RSU_KW = r"\bRS([UA]s?)?\b|\brestricted\s*(unite?s?\s*)?(share|a\s*ward|stock)"
R_AWARD_KEYWORD_LIST = [
    # http://************:55647/#/project/remark/200598?treeId=37735&fileId=65189&schemaId=5&projectId=17&schemaKey=A34&page=133
    r"\brestricted\s*([AH]|unite?s?)\s*(share|a\s*ward|stock)",
    # http://************:55647/#/project/remark/264318?treeId=37706&fileId=70078&schemaId=15&projectId=17&schemaKey=H83
    r"restricted\s*(share|stock|award)(?!\s*(units?\s*)?option)",
    r"\bRS([UA]s?)?\b",
    r"\ba\s*ward(s|ed)?\s*shares?",
    r"\bshares?\s*a\s*ward",
    r"\b[AH]\s*shares",
    r"options?\s*(and/or|or|and)\s*award",
    r"Incentive share",
]
R_AWARD_KEYWORD = "(" + r"|".join(R_AWARD_KEYWORD_LIST) + ")"
P_AWARD_KEYWORD = PatternCollection(R_AWARD_KEYWORD_LIST, flags=re.I)
R_AWARD_SHARES = r"(matching\s*|match(ed)?\s*)?(\brestricted\s*([AH]\s*)?(shares?|awards?|stocks?)(\s*units?)?|award(s|ed)?\s*shares?|(shares?\s*)?award(s|ed)?|shares?(?!\s*option)|\brsu?s?\b|\brsa\b(\s*schemes?)?)"
# meta_data["share_info"]["no_option_element_idx"]：公司没有share option scheme，NS

# http://************:55647/#/project/remark/264763?treeId=8310&fileId=70167&schemaId=15&projectId=8310&schemaKey=B73
R_NO_SHARE_SCHEME = r"\bnot\s*(have|ha[sd]|adopt(ed)?)\s*any\s*share\s*scheme\s*(and|or)\s*(no\s*)?(share\s*)?(option|award)\s*(or|and)\s*(share\s*)?(option|award)"

R_NO_OPTION_NS = [
    rf"\bnot\s*adopted\s*any\s*{R_OPTION_KEYWORD}\s*scheme",
    r"\b(did|do|does)\s*not\s*(establish|have|ha[sd]|set\s*up|adopt(ed)?)\s*(any\s*|a\s*)?share\s*option\s*scheme(?!\s*other\s*than)",
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/4830#note_540770
    r"\b(did|do|does)\s*not\s*adopt(ed)?\s*[^.;；:：]*share\s*option\s*scheme",
    R_NO_SHARE_SCHEME,
]
# 没有share option时，award章节提到这种描述，option为NS
P_NOT_CONSTITUTE_OPTION = PatternCollection(
    r"\b(did|do|does)\s*not\s*constitute\s*(any\s*|a\s*)?share\s*option\s*scheme", flags=re.I
)
P_NO_OPTION_NS = PatternCollection(R_NO_OPTION_NS, flags=re.I)
# meta_data["share_info"]["no_award_element_idx"]：公司没有share award scheme，NS
R_NO_AWARD_NS = [
    rf"\bnot\s*adopted\s*any\s*({R_OPTION_KEYWORD}\s*scheme\s*(and|or)\s*(other\s*|any\s*)?)?(share\s*|award\s*){{1,2}}scheme",
    r"(did|do|does)\s*not\s*(establish|have|ha[sd]|set\s*up)\s*(any\s*)?share\s*a\s*ward\s*scheme(?!\s*other\s*than)",
    R_NO_SHARE_SCHEME,
    # http://************:55647/#/project/remark/264678?treeId=45541&fileId=70150&schemaId=15&projectId=45541&schemaKey=B73
    r"\bnot\s*adopt(ed)?\s*any(.*?schemes?){1,2}\s*(and|or)\s*(restrict(ed)?\s*share\s*unit\s*|(share\s*|award\s*){1,2})scheme",
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/4570#note_541593 这个应该在`R_NO_AWARD_GRANTED`中
    # rf"\bno\s*share\s*{R_BE}granted",
]

P_NO_AWARD_NS = PatternCollection(R_NO_AWARD_NS, flags=re.I)
# meta_data["share_info"]["no_award_element_idx"]：全文中没有AWARD章节并满足以下正则，NS
R_SAVE_AS = r"\b(saved?\s*(and\s*except\s*)?(for|as|from|(the\s*)?above)|except\s*(for|as|otherwise|from)?|besides|other\s*than|(apart|aside)\s*from|in\s*addition)[^.,，]*?"
# http://************:55647/#/project/remark/231467?treeId=3590&fileId=66200&schemaId=15&projectId=17&schemaKey=B77&page=97
P_SAVE_AS_ABOVE = PositionPattern.compile(R_SAVE_AS, r"\b(above|disclosed|aforesaid|(afore)?mentioned)")
P_SAVE_AS = re.compile(rf"{R_SAVE_AS}|^Also\s*[,，]", re.I)
P_SAVE_AS_IN_REPORT = re.compile(r"^save\s*as\s*disclosed\s*in\s*(this|the)\s*(annual\s*)?report", re.I)
R_NO_ARRANGES = [
    r"at\s*no\s*time\s*during\s*[^.]+?any\s*arrangement",
    # ttp://100.64.0.11:55647/#/project/remark/232333?treeId=11958&fileId=66344&schemaId=15&projectId=17&schemaKey=B77&page=60
    r"ha(s|d|ve)\s*no\s*other\s*share\s*(scheme|plan)",
]
# http://************:55647/#/project/remark/250709?treeId=5880&fileId=68574&schemaId=15&projectId=17&schemaKey=B93.2&page=28
P_NO_ARRANGE = MatchMulti.compile(P_SAVE_AS_ABOVE, r"at\s*no\s*time\s*during\s*[^.]+?any\s*arrangement", operator=all)

R_NO_EQUITY = [
    r"\bno\s*equity[-\s]linked",
    r"\bnot\s*enter(ed)?\s*into\s*(any\s*)?equity[-\s]linked",
    r"\bno[rt]\s*(ha[sd]|have)\s*(any\s*)?equity[-\s]linked",
    r"At\s*no\s*time\s*during[^,，.。]*?(any\s*)?equity[-\s]linked",
]

P_NO_EQUITY = MatchMulti.compile(
    *R_NO_EQUITY,
    operator=any,
)
P_AWARD_NO_EQUITY = MatchMulti.compile(
    P_NO_EQUITY,
    NeglectPattern.compile(
        match=MatchMulti.compile(
            rf"{R_SAVE_AS}(share\s*option|the\s*scheme|option)",
            r"|".join(R_NO_ARRANGES),
            operator=all,
        ),
        unmatch=r"shares?\s*award|\bRSU\b|Restricted\s*(share|award|stock)",
    ),
    operator=any,
)
P_OPTION_NO_EQUITY = MatchMulti.compile(
    P_NO_EQUITY,
    NeglectPattern.compile(
        match=MatchMulti.compile(
            rf"{R_SAVE_AS}(restricted\s*(shares?|award(s|ed)?)|RSUs?|shares?\s*award|award(s|ed)\s*shares?)",
            r"|".join(R_NO_ARRANGES),
            operator=all,
        ),
        unmatch=R_OPTION_KEYWORD,
    ),
    operator=any,
)

P_OPTION_CHAPTER = NeglectPattern.compile(match=r"share\s*option", unmatch=R_AWARD_KEYWORD)
P_AWARD_CHAPTER = NeglectPattern.compile(
    match=r"share\s*award|rsu|restricted\s*(share|award|stock)", unmatch=R_OPTION_KEYWORD
)

R_REPORT_YEAR = [
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/4830#note_541063
    r"\bthe\s*date\s*of\s*issuance",
    r"\bthe\s*date\s*of\s*(the|this)\s*(year|report(ing)?)(?!\s*(end|as|\d{4}))",
    r"\b(the\s*date\s*of\s*)?\s*(the|this)\s*(financial\s*|annual\s*|report(ing)?\s*|year\s*|period\s*){2,4}(?!\s*(end|as|\d{4}))",
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/4873#note_549557
    r"\bthe\s*beginning\s*(and\s+|at\s+|the\s+){1,3}end\s*of\s*the\s*year",
]
P_REPORT_YEAR = MatchMulti.compile(
    R_DURING_THE_YEAR,
    *[rf"\b(during|in)\s*{report_year}" for report_year in R_REPORT_YEAR],
    operator=any,
)
# 满足以下条件+R_NO_AWARD_GRANTED，则B74~B78为确定NS
P_NS_DATE = MatchMulti.compile(
    r"\b(adoption|listing)\s*date",
    r"\bsince\s*(it\s*was|the|its)?\s*(adopted|adoption)",
    *[rf"\b(for|as\s*at|as\s*of|up\s*to){report_year}" for report_year in R_REPORT_YEAR],
    operator=any,
)

# 描述财年内的NS需求：B64-当年内无options granted, B74-当年内无unvested，B75-当年内无awards granted, B76-当年内没有vested, B77-当年内没有cancelled, B78-当年内没有lapsed
P_DURING_REPORT_YEAR = MatchMulti.compile(
    R_DURING_THE_YEAR_NO_END,
    *R_REPORT_YEAR,
    P_NS_DATE,
    operator=any,
)

P_OPTION_NO_OUTSTANDING = MatchMulti.compile(
    # 场景一：同时包含beginning和end
    MatchMulti.compile(rf"\bno\s*{R_OPTION_KEYWORD}\s*(outstanding|balance)", r"beginning", r"\bend\b", operator=all),
    MatchMulti.compile(r"\bno\s*(outstanding|balance)", r"beginning", r"\bend\b", operator=all),
    # 场景二：既不包含beginning也不包含end（即不能包含其中任意一个）
    NeglectPattern.compile(
        match=MatchMulti.compile(r"\bno\s*(outstanding|balance)", R_OPTION_KEYWORD, operator=all),
        unmatch=r"beginning|\bend\b",
    ),
    NeglectPattern.compile(match=rf"\bno\s*{R_OPTION_KEYWORD}\s*(outstanding|balance)", unmatch=r"beginning|\bend\b"),
    operator=any,
)
P_AWARD_NO_OUTSTANDING = MatchMulti.compile(
    # 场景一：同时包含beginning和end
    MatchMulti.compile(rf"\bno\s*{R_AWARD_SHARES}\s*outstanding", r"beginning", r"\bend\b", operator=all),
    MatchMulti.compile(r"\bno\s*outstanding", r"beginning", r"\bend\b", R_AWARD_SHARES, operator=all),
    # 场景二：既不包含beginning也不包含end（即不能包含其中任意一个）
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/4570#note_521332
    NeglectPattern.compile(
        match=MatchMulti.compile(r"\bno\s*outstanding", R_AWARD_SHARES, operator=all), unmatch=r"beginning|\bend\b"
    ),
    # TODO 待定描述：as at year end https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/4568#note_521470
    NeglectPattern.compile(match=rf"\bno\s*{R_AWARD_SHARES}\s*outstanding", unmatch=r"beginning|\bend\b"),
    operator=any,
)

# B63~B64、B93.1、B94.1没有grant过share option
R_NO_OPTION_GRANTED = [
    # http://************:55647/#/project/remark/233886?treeId=42631&fileId=66603&schemaId=15&projectId=42631&schemaKey=B64
    r"(\bno[tr]|didn.t|ha[sd]n.t|haven.t|neither)\s*grant(ed)?\s*(any|further)?\s*(share\s*)?options?\b",
    rf"\bno\s*{R_OPTION_KEYWORD}\s*granted",
    rf"\bno\s*grant\s*of\s*{R_OPTION_KEYWORD}",
    PositionPattern.compile(
        r"\b(no|none\s*of\b|nor\s*any)\b",
        r"\boptions?\b",
        rf"(have\s*|ha[sd]|(?<!will ){R_BE})\s*(issued\s*or\s*)?granted(?!\s*when)",
    ),
    MatchMulti.compile(r"\bno\s*movement|remain(ed)?\s*unchanged?", R_OPTION_KEYWORD, operator=all),
    # http://************:55647/#/project/remark/238299?treeId=3997&fileId=67339&schemaId=15&projectId=17&schemaKey=B63.1
    r"All\s*(of\s*)?the\s*outstanding\s*share\s*options\s*were\s*cancelled",
]
P_NO_OPTION_GRANTED = MatchMulti.compile(*R_NO_OPTION_GRANTED, operator=any)
# B74~B78、B93.2 没有grant过share award
# todo https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/3821#note_448341
R_NO_AWARD_GRANTED = [
    # http://************:55647/#/project/remark/234911?treeId=37726&fileId=66774&schemaId=15&projectId=17&schemaKey=B74
    # http://************:55647/#/project/remark/251254?treeId=37879&fileId=68665&schemaId=15&projectId=17&schemaKey=B73
    rf"(\bno[tr]|didn.t|ha[sd]n.t|haven.t|neither)\s+(grant|award)(ed)?[^.;；]*?any\s*{R_AWARD_SHARES}",
    # http://************:55647/#/project/remark/250363?treeId=6141&fileId=68516&schemaId=15&projectId=17&schemaKey=B76&page=49
    # http://************:55647/#/project/remark/233215?treeId=37654&fileId=66491&schemaId=15&projectId=17&schemaKey=B76
    rf"\bno\s*grant\s*of\s*{R_AWARD_SHARES}",
    PositionPattern.compile(
        rf"\b(no|none\s*of(\s*the)?|nor\s*any|no\s*{reg_words(0, 3)}options?\s*(and/or|and|or)\s*{reg_words(0, 3)})\s*{R_AWARD_SHARES}",
        r"(be(en)?|was|were)\s*(issued\s*or\s*)?(grant|award)ed(?!\s*when)",
    ),
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/4697#note_527731
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/4571#note_521508
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/4570#note_541593 从R_NO_AWARD_NS中删除的
    rf"\bno\s*(shares?\s*|awards?\s*|rsus?\s*|\bof\s*){{1,3}}\s*(ha([sd]|ve)\s*)?((?<!will ){R_BE}\s*)?(award|grant)ed",
    # 没有变动过
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/4568#note_521632
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/4571#note_521529
    # http://************:55647/#/project/remark/251195?projectId=17&treeId=37638&fileId=68655&schemaId=15&schemaKey=B75
    MatchMulti.compile(r"\bno\s*movement|remain(ed)?\s*unchanged?", R_AWARD_SHARES, operator=all),
    rf"\bno\s*free\s*shares?\s*{R_BE}\s*granted",
]
P_NO_AWARD_GRANTED = MatchMulti.compile(*R_NO_AWARD_GRANTED, operator=any)
R_REVIEW = r"\b(review|assess|discuss|approve?|consider|engage?|consult|conduct)(ing|ed)?"
R_REVIEWED = r"\b(review|assess|approve?|discuss|consider|engage?|consult|conduct|not|consolidat)ed\b(?!\s*[a-z]+ing)|\bby\s*(review|assess|discuss|approv|consider|engag)ing\b"
# 有效
R_EFFECTIVE = r"effective|sound|sufficient|adequacy|adequate|(satisfied|satisfying)\s*(with\s*)?the\s*(result|review)"
# 履行了xx
R_DISCHARGED = r"\b(discharg(e|ing)|performed|undertaken|completed|accomplished|carried\s*out|work\s*done)"
R_DUTIES = r"((major|main(ly)|primary(ly)|principal|primarily))?((?<!audit)(?<!audit )functions?|roles?|dut(y|ies)|responsibilit(y|ies)|responsible\s*for|objectives?|under\s*(the|its)\s*terms\s*of\s*reference)"
reviewing_approving = rf"{R_REVIEW} and/or approv(ing|ed)"
R_B95_NS_REGS = (
    r"no grants were made under the share option scheme and share award",
    r"no option has been granted, agreed to be granted",
    r"the company does not have share option scheme",
    r"not adopted any share option scheme or other share scheme",
    rf"no grant was made under the RSU Scheme which requires {R_REVIEW} by the Remuneration Committee",
    r"No option under the Share Option Scheme had been recommended",
    r"no share option granted under the Share Option",
    r"no material.*?Share Option Scheme",
    rf"has not {reviewing_approving}",
    SplitBeforeMatch(
        MatchMulti.compile(
            rf"not (discussed|{R_REVIEW})",
            r"share (option|schemes)",
            operator=all,
        ),
        separator=P_SEN_SEPARATOR,
        operator=any,
    ),
)
# 适用于LIST RULE 2.1 判断数量
R_ZERO = r"\b(0|(?<!at)(?<!at )nil)\b"
# R_NIL = rf"((\t|^)([0{R_MIDDLE_DASHES}]|nil|\s*)(\t|$)|N/A|不適用)"  # TODO 里面的 \s* 需要再讨论下
R_NIL = rf"((\t|^)([0{R_MIDDLE_DASHES}]|nil)(\t|$)|N/A|不適用)"

P_NUMBER = re.compile(r"\d+")
R_NUMBER = r"\b([1-9][0-9]{0,2}(?:,[0-9]{3}){1,6})\b"
R_NUMBER_PS = [rf"(^|\t)\s*[\[(（]?\s*{R_NUMBER}\s*([)）\]]\d?)?(\t|$|\s*[(（]|\s*note)"]
P_NUMBER_PS = MatchMulti.compile(*R_NUMBER_PS, operator=any)

# 替换掉日期中的0
P_0_IN_DATE_REGEX = re.compile(r"(?<=\])(0)|(?<=\\b)0")
P_DATE = MatchMulti.compile(*R_DATES, operator=any)
P_ONLY_DATE = MatchMulti.compile(*[rf"^{p}$" for p in R_DATES], operator=any)
P_ONLY_NUM = re.compile(r"^[,.\d\s]+$")

R_ND_SHARE_AWARD_SCHEME = (
    MatchMulti.compile(
        r"(do|did|does) not have share award",
        R_EXPIRED_OR_ADOPTED,
        operator=all,
    ),
    MatchMulti.compile(
        "no other share scheme",
        "not have (any )?share scheme",
        operator=any,
    ),
    MatchMulti.compile(
        rf"({'|'.join(R_NO_EQUITY)}) agreements? (have been|was) entered",
        operator=all,
    ),
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/4561#note_518134
    MatchMulti.compile(
        rf"terminate the {SHARE_AWARD_SCHEME_TEXT}",
        operator=all,
    ),
    # https://jura-uat2.paodingai.com/#/hkex/annual-report-checking/report-review/242585?fileId=79830&schemaId=5&rule=A36&delist=0
    MatchMulti.compile(
        rf"no new Shares shall {R_BE} issued thereunder",
        operator=all,
    ),
)

R_MOVEMENT = r"movements?|information|changes?|details?"

R_HOLD_MEETING = rf"(held|convened)[\w\s]+?meeting|meetings?[\w\s]+?(held|convened)|\bmet\s+{R_ENG_TIMES}"

R_SHARE_CLASS = r"(HK|Hong\sKong|H|A|B|RMB|[Dd]omestic\s(Un|Non-)listed|[Dd]omestics?|(Un|Non-)listed)"


def gene_p_reviewed_sth(r_keyword: str):
    """
    正则： 年内，reviewed xx
    """
    return [
        # 情况一：召开了会议
        PositionPattern.compile(R_HOLD_MEETING, r_keyword),
        SplitBeforeMatch(
            MatchMulti.compile(
                # 情况二：履行了职责或检查了xx，句号切分
                MatchMulti.compile(
                    R_DURING_THE_YEAR, PositionPattern.compile(rf"{R_DISCHARGED}|{R_REVIEW}", r_keyword), operator=all
                ),
                # 情况三：reviewed xx且非主要职责
                NeglectPattern.compile(match=PositionPattern.compile(R_REVIEWED, r_keyword), unmatch=R_PRIMARY_ROLE),
                operator=any,
            ),
            separator=P_PERIOD_SEPARATOR,
        ),
    ]


def gene_p_reviewed_sth1(r_keyword: str):
    # 情况三：reviewed xx且非主要职责
    return SplitBeforeMatch(PositionPattern.compile(R_REVIEWED, r_keyword), separator=P_PERIOD_SEPARATOR)


def gene_p_committee_reviewed_sth(r_committee: str, r_keyword: str):
    """
    正则： 年内，xx委员会，reviewed xx
    """
    return [
        # 情况一：召开了会议
        PositionPattern.compile(
            r_committee,
            R_HOLD_MEETING,
            r_keyword,
        ),
        SplitBeforeMatch(
            MatchMulti.compile(
                # 情况二：履行了职责或检查了xx，句号切分
                MatchMulti.compile(
                    R_DURING_THE_YEAR,
                    r_committee,
                    PositionPattern.compile(rf"{R_DISCHARGED}|{R_REVIEW}", r_keyword),
                    operator=all,
                ),
                # 情况三：reviewed xx且非主要职责
                NeglectPattern.compile(
                    match=PositionPattern.compile(r_committee, R_REVIEWED, r_keyword),
                    unmatch=R_DUTIES,
                ),
                operator=any,
            ),
            separator=P_PERIOD_SEPARATOR,
        ),
    ]


def gene_p_committee_reviewed_sth1(r_committee: str, r_keyword: str):
    # 情况三：reviewed xx且非主要职责
    return SplitBeforeMatch(PositionPattern.compile(r_committee, R_REVIEWED, r_keyword), separator=P_PERIOD_SEPARATOR)


def get_regex_by_float(float_num, min_digit=None) -> set[str]:
    if int(float_num) == float_num:
        return {rf"{float_num:,.0f}(\.0*)?"}
    # 计算有效小数个数
    number, origin_decimal = str(float_num).split(".")
    decimal = f".{origin_decimal}"[::-1]
    count_decimal = len(decimal) - 1
    for i, num in enumerate(decimal):
        if num != "0":
            count_decimal = count_decimal - i
            break
    result = set()
    # 注意这里min_digit=0和=None处理逻辑相同
    if not min_digit:
        result.add(rf"{float_num:,.0f}(\.0*)?")
    if count_decimal == 0:
        return result
    min_digit = min_digit or 1
    for digit in range(min_digit, count_decimal + 1):
        for regex in [rf"{float_num:,.{digit}f}0*", rf"{int(number):,}.{origin_decimal[:digit]}0*"]:
            result.update({regex, regex.replace(",", "")})
    return result


@lru_cache
def gen_regex_by_price(price_str, unit=None, min_digit=0):
    """
    将价格转为正则
    input: HK$0.21
    output: [r"\b(HKD|HK[DS$]*)\\s*0.21\b", r"\b(HKD|HK[DS$]*)\\s*0.210+\b"]
    input: HK$0.211
    output: [r"\b(HKD|HK[DS$]*)\\s*0.21\b", r"\b(HKD|HK[DS$]*)\\s*0.2110*\b"]
    即：转为小数点后最多3位
    """
    if not (matched := P_PRICE_NUM.search(price_str)):
        return MATCH_NEVER
    price_num = matched.group()
    prices = [rf"{price_num}\b"]
    if unit is None:
        unit = price_str.replace(price_num, "").strip()
    price_num = float(price_num)
    if price_num > 1000000:
        for regex in get_regex_by_float(price_num / 1000000, min_digit=min_digit):
            prices.append(rf"{regex}\s*million")
            # 表格中的million单位一般在表头
            prices.append(rf"(million|000.000)\s+.+\t{regex}\s*$")
    for regex in [rf"{r}\b" for r in get_regex_by_float(price_num, min_digit=min_digit or 1)]:
        if regex not in prices:
            prices.append(regex)
    if price_num > 1000:
        # 表格中的k单位一般在表头
        prices.extend(
            [rf"(?<!000)['‘’]000\s+.+\t{r}\s*$" for r in get_regex_by_float(price_num / 1000, min_digit=min_digit)]
        )
    return MatchMulti.compile(
        *[rf"\b({unit}|HK[DS$]*)\s*{price}" for price in prices],
        operator=any,
    )


@lru_cache
def gen_regex_by_num(number: int | str):
    """
    将数字转为正则，数字可能带其他字符，提取其中的数字再转换
    """
    if isinstance(number, str):
        if number.isdigit():
            number = int(number)
        elif matched := P_NUMBER.search(number):
            number = int(matched.group())
        else:
            return MATCH_NEVER
    return MatchMulti.compile(
        rf"\b{number}\b",
        rf"\b{number:,}\b",
        *[rf"\b{r}\s*million" for r in get_regex_by_float(float(number) / 1000000)],
        operator=any,
    )


@lru_cache
def gen_regex_by_date(
    date_str: str, *, date_fmt="%Y-%m-%d", ignore_day=False, prep_regex: str = ""
) -> SearchPatternLike:
    """
    ignore_day: 是否不匹配具体日期
    prep_regex: 表示日期前的介词，例如 r"(on|at|in) +"等
    """
    if not date_str:
        return MATCH_NEVER
    try:
        date_ = datetime.strptime(date_str, date_fmt)
    except ValueError:
        return MATCH_NEVER
    year_str, day_str = date_.strftime("%Y"), date_.strftime("%d")
    dates_regex = []
    for mon_str in [date_.strftime("%b"), date_.strftime("%m")]:
        if is_num_month := mon_str.startswith("0"):
            mon_str = rf"0?{mon_str[1]}"
        if ignore_day:
            dates_regex = dates_regex.extend(
                [
                    rf"{prep_regex}\b{mon_str}[a-z]*[,，]?[-\s/]?{year_str}\b",
                    rf"{prep_regex}\b{year_str}[-\s/]+{mon_str}[a-z]*\b",
                    rf"{prep_regex}\b{mon_str}[a-z]*[-\s/]?\d{{,2}}[-\s/]?{year_str}\b",
                ]
            )
            if not is_num_month:
                # 这里考虑句子中带and的日期，例如： On April and May 2023
                dates_regex.append(
                    rf"{prep_regex}\b{mon_str}[a-z]*[,，]?\s*and\s*{R_SIM_MONTH}[a-z]*[,，]?\s*{year_str}"
                )
        else:
            if day_str.startswith("0"):
                day_str = rf"0?{day_str[1]}"
            dates_regex.extend(
                [
                    rf"{prep_regex}\b{day_str}[-\s/]{mon_str}[a-z]*[,，]?[-\s/]?{year_str}\b",
                    rf"{prep_regex}\b{mon_str}[a-z]*[-\s/]{day_str}[,，]?[-\s/]?{year_str}\b",
                    rf"{prep_regex}\b{year_str}[-\s/]{mon_str}[-\s/]{day_str}\b",
                ]
            )
            if not is_num_month:
                # 这里考虑句子中带and的日期，例如： On 17 April and 27 April 2023
                dates_regex.extend(
                    [
                        rf"{prep_regex}\b{day_str}\s*{mon_str}[a-z]*[,，]?\s*and\s*\d{{1,2}}\s*{R_SIM_MONTH}[a-z]*[,，]?\s*{year_str}",
                        rf"{prep_regex}\b{mon_str}[a-z]*\s*{day_str}[,，]?\s*and\s*{R_SIM_MONTH}[a-z]*\s*\d{{1,2}}[,，]?\s*{year_str}",
                    ]
                )
    return MatchMulti.compile(*dates_regex, operator=any)


if __name__ == "__main__":
    # print(gen_regex_by_price("HKD 0.2110"))
    # print(gen_regex_by_date("2022-09-05"))
    print(gen_regex_by_price("HKD 1551066030"))
