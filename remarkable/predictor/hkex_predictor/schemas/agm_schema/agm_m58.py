import re
from itertools import chain

from remarkable.common.common_pattern import P_PERIOD_SEPARATOR, R_MIDDLE_DASH, R_MIDDLE_DASHES
from remarkable.common.constants import AnswerValueEnum, PDFInsightClassEnum
from remarkable.common.pattern import MatchMult<PERSON>, PositionPattern
from remarkable.predictor.hkex_predictor.schemas.pattern import R_NOT
from remarkable.predictor.models.base_model import BaseModel
from remarkable.predictor.schema_answer import PredictorResult

# https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6168
R_RULE_1351_1750 = (
    r"\b(rule|paragraph)s?\s*(13\.51|17\.50)|(13\.51|17\.50)[（）\(\)\da-zA-Z\s]+of\s*the\s*listing\s*rules?"
)
P_NS = PositionPattern.compile(R_NOT, R_RULE_1351_1750)
P_DIRECTOR_ALIAS = re.compile(
    rf"^.+?[(（]\s*([“”]|[‘‘’’]{{2}})\s*(?P<name>[a-zA-Z.{R_MIDDLE_DASHES}\s]+)\s*([“”]|[‘‘’’]{{2}})\s*[)）]\s*([,，]\s*)?(aged?\s*\d{{2}}|\d{{2}}\s*years\s*old)\b"
)
P_DIRECTOR_AGED = re.compile(
    r"^([(（]?(\d+|[a-z]|[ivx]{1,4})[）).]\s*)?(?P<name>.+?)(\s+[(（].+?)?[,，]\s*(aged?\s*\d{2}|\d{2}\s*years\s*old)\b",
    re.I,
)
P_CHAPTER_RE_ELECTED = MatchMulti.compile(rf"\bre{R_MIDDLE_DASH}elect(ed|ion)", operator=any)
P_EACH_DIRECTORS = MatchMulti.compile(
    r"(?<!\bany )directors\b(?!\sare\s*aware)|each\s*(of\s*the\s*)?([a-z]+\s*)?director\b", operator=any
)


def check_nd_answer(answers: list[PredictorResult], **kwargs):
    """
    M58后处理
    若提取了多段描述，先统计附录re-election章节下所有的董事个数，若董事个数>NS描述个数，则修改枚举值为ND
    http://************:55647/#/project/remark/316827?treeId=49138&fileId=81132&schemaId=33&projectId=36&schemaKey=M58
    若提取了1段描述，有关键词directors，直接NS，否则该段落中必须提及所有董事名字，未全部提及则修改枚举值为ND
    http://************:55647/#/project/remark/305935?treeId=45780&fileId=71173&schemaId=33&projectId=36&schemaKey=M58
    特例：
      董事被分为两部分，前一部分统一描述，后面的分开描述 https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6063#note_650353
      http://http://************:55647/#/project/remark/325039?treeId=51133&fileId=89344&schemaId=33&projectId=36&schemaKey=M58
    """

    # def ns_or_ps_answers():
    #     if ps_answers and any(ans.answer_value == AnswerValueEnum.PS for ans in flat_answers):
    #         new_answers = []
    #         for ans in flat_answers:
    #             ans.update_answer_value(AnswerValueEnum.PS)
    #             new_answers.append(ans)
    #         return new_answers
    #     return flat_answers

    def nd_answers():
        new_answers = []
        for ans in flat_answers:
            ans.update_answer_value(AnswerValueEnum.ND)
            new_answers.append(ans)
        return new_answers

    if not answers:
        return answers
    flat_answers = BaseModel.get_common_predictor_results(answers)
    pdfinsight = kwargs.get("predictor").pdfinsight
    element_indices, max_page = set(), 0
    # ps_answers = {}
    for answer in flat_answers:
        if answer.answer_value != AnswerValueEnum.NS:
            return answers
        for element in BaseModel.get_elements_from_answer_result([answer]):
            # if answer.answer_value == AnswerValueEnum.PS:
            #     ps_answers[element["index"]] = answer
            # else:
            element_indices.add(element["index"])
            max_page = max(max_page, element["page"])
    found_chapter = False
    all_directors, directors, elt_idx_of_directors = [], [], []
    # skip_answers = []
    index = 10
    # valid_ps_index = set()
    while True:
        index += 1
        type_, element = pdfinsight.find_element_by_index(index)
        if element["page"] >= pdfinsight.max_page - 3:
            break
        if type_ != PDFInsightClassEnum.PARAGRAPH.value:
            continue
        elt_text = element["text"]
        if not found_chapter:
            if index in pdfinsight.syllabus_reader.elt_syllabus_dict and P_CHAPTER_RE_ELECTED.search(elt_text):
                found_chapter = True
            continue
        # if index in ps_answers:
        #     ps_answer = ps_answers[index]
        #     if directors and all(n in ps_answer.text for n in directors[-1].split()):
        #         # 匹配到了PS句子，且句子中提及董事名字，则对应的董事不用做后面的判断
        #         directors = directors[:-1]
        #         valid_ps_index.add(index)
        #     else:
        #         skip_answers.append(ps_answer)
        #     continue
        if index in element_indices:
            if not all_directors or all_directors[-1]:
                all_directors.append(directors)
            directors = []
            continue
        director_name = ""
        if matched := P_DIRECTOR_ALIAS.search(elt_text):
            director_name = matched.group("name")
        elif matched := P_DIRECTOR_AGED.search(element["text"]):
            # 人名只取前两位，例如：Mr. Li
            director_name = " ".join(n.capitalize() for n in matched.group("name").split()[:2])
        if director_name:
            if directors and " " in director_name and " " in directors[-1] and index - elt_idx_of_directors[-1] < 4:
                # 人名取重复： http://************:55647/#/project/remark/318581?treeId=49550&fileId=82886&schemaId=33&projectId=36&schemaKey=M58
                first_name, *_, last_name = director_name.split()
                prev_first_name, *_, prev_last_name = directors[-1].split()
                if prev_last_name == first_name:
                    continue
                if prev_first_name == last_name:
                    directors[-1] = director_name
                    continue
            directors.append(director_name)
            elt_idx_of_directors.append(index)
    # skip_answers.extend(v for k, v in ps_answers.items() if k not in valid_ps_index)
    if directors:
        all_directors.append(directors)
    # if skip_answers:
    #     flat_answers = [ans for ans in flat_answers if ans not in skip_answers]
    if not any(all_directors):
        return flat_answers
    max_director_idx, min_ans_idx, max_ans_idx = max(elt_idx_of_directors), min(element_indices), max(element_indices)
    # 答案在所有董事之前或所有董事之后 + directors关键词，认为统一描述了所有董事的情况
    # 答案在最前： http://************:55647/#/project/remark/316288?treeId=49013&fileId=80593&schemaId=33&projectId=36&schemaKey=M58
    # 答案在最后： http://************:55647/#/project/remark/315308?treeId=48755&fileId=79613&schemaId=33&projectId=36&schemaKey=M58
    if max_ans_idx < min(elt_idx_of_directors) or max_director_idx < min_ans_idx:
        for answer in flat_answers:
            text = answer.text
            if (
                P_EACH_DIRECTORS.search(text)
                or all(d in text for d in all_directors[0])
                or not any(d in text for d in all_directors[0])
            ):
                return flat_answers
        return nd_answers()
    # 总结语之后还有董事描述
    if max_ans_idx < max_director_idx or len(all_directors) > len(flat_answers):
        return nd_answers()
    # 答案个数==提及的董事个数
    if len(flat_answers) == len(list(chain(*all_directors))):
        return flat_answers
    # 在所有董事后面有一个否定描述，这个段落中不包含任何董事信息, 可以认为是对所有董事统一的否定描述
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7063#note_717956
    if max_ans_idx > max_director_idx:
        _, answer_element = pdfinsight.find_element_by_index(max_ans_idx)
        if (
            answer_element
            and not any(director in answer_element["text"] for directors in all_directors for director in directors)
            and not P_EACH_DIRECTORS.search(answer_element["text"])
        ):
            return flat_answers
    # 分组对比每位董事有无被提及
    for directors, answer in zip(all_directors, flat_answers):
        # http://************:55647/#/project/remark/320996?treeId=50142&fileId=85301&schemaId=33&projectId=36&schemaKey=M58
        if len(directors) <= 1:
            continue
        if P_EACH_DIRECTORS.search(answer.text):
            continue
        if any(d not in answer.text for d in directors):
            return nd_answers()
    return flat_answers


predictor_options = [
    {
        "path": [
            "M58-information to be disclosed pursuant to any of the requirements of this rule 13.51(2)",
            "Content",
        ],
        "post_process": check_nd_answer,
        "models": [
            # # 1. 任一董事描述了同时兼任其他公司的情况，都为PS，同时其他所有董事必须说明不涉及13.51/17.50
            # # stock_code=08195: http://************:55647/#/project/remark/316054?treeId=48952&fileId=80359&schemaId=33&projectId=36&schemaKey=M58
            # # stock_code=01653: http://************:55647/#/project/remark/319146?treeId=49682&fileId=83451&schemaId=33&projectId=36&schemaKey=M58
            # # stock_code=00526: http://************:55647/#/project/remark/324335?treeId=50965&fileId=88640&schemaId=33&projectId=36&schemaKey=M58
            # {
            #     "name": "multi_models",
            #     "operator": "join",
            #     "require_unique_element": True,
            #     "enum_every_element": True,
            #     "enum": AnswerValueEnum.PS.value,
            #     "models": [
            #         {
            #             "name": "para_match",
            #             "enum": AnswerValueEnum.PS.value,
            #             "multi_elements": True,
            #             # 制裁/谴责/仲裁/纠纷
            #             "paragraph_pattern": r"sanction\s*against|reprimanded|arbitration|dispute",
            #         },
            #         {
            #             "name": "para_match",
            #             "enum": AnswerValueEnum.NS.value,
            #             "para_separator": P_PERIOD_SEPARATOR,
            #             "multi_elements": True,
            #             "paragraph_pattern": P_NS,
            #         },
            #     ],
            # },
            # 2. 所有董事都没有需要披露的13.51/17.50相关信息
            # stock=08111: http://************:55647/#/project/remark/316827?treeId=49138&fileId=81132&schemaId=33&projectId=36&schemaKey=M58
            {
                "name": "para_match",
                "enum": AnswerValueEnum.NS,
                "para_separator": P_PERIOD_SEPARATOR,
                "multi_elements": True,
                "paragraph_pattern": P_NS,
            },
            {
                "name": "score_filter",
                "multi_elements": True,
                "enum_every_element": True,
                "enum": AnswerValueEnum.NS,
                "threshold": 0.618,
                "pattern": R_NOT,
            },
        ],
    }
]
