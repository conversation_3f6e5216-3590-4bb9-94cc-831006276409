import re

from remarkable.common.common_pattern import P_SEN_SEPARATOR
from remarkable.common.pattern import <PERSON><PERSON><PERSON><PERSON>, PositionPattern
from remarkable.predictor.common_pattern import R_PERCENT
from remarkable.predictor.hkex_predictor.pattern import R_BE
from remarkable.predictor.hkex_predictor.schemas.cg_schema.cg_b import R_RELATIONSHIP
from remarkable.predictor.hkex_predictor.schemas.pattern import (
    R_NOT,
    R_NUMBER,
    R_SAVE_AS,
    reg_words,
)

R_ITEM_SEPARATOR = re.compile(r"[(（][ivx]+[)）]")

# M38
R_PART_XV = r"part\s*XV\s*(of|to)\s*(the\s*)?(SFO|Securities\s*and\s*Futures?\s*Ordinance|SF\s*Ordinance)"


predictor_options = [
    {
        "path": [
            "M37-relationships with any directors, senior management or substantial or controlling shareholders of the issuer",
            "Content",
        ],
        "models": [
            {
                "name": "agm_m38",
                "multi_elements": True,
                "multi": True,
                "para_separator": P_SEN_SEPARATOR,
                "item_separator": R_ITEM_SEPARATOR,
                "as_follow_pattern": [
                    r"save\s*as.*re-election.*[:：]\s*$",
                    r"^Save as disclosed herein",
                ],
                "neglect_sentence_pattern": MatchMulti.compile(
                    r"sole director of.*?Group Limited, which is a substantial shareholder of the Company",
                    r"knowledge (made|make).*?\binvestor",  # 经验使...成为投资者
                    r"h[eo]ld under.*?\bperson",  # 被某人控制
                    r"administrative measures on remuneration",  # 绩效管理办法
                    r"\bserved as (a|the) director",  # 担任董事
                    rf"{R_NOT}.*?\bfinancial.*?\binterest in the business",  # 无任何利益
                    operator=any,
                ),
                "all_ns_pattern": [
                    PositionPattern.compile(
                        r"saved?\s*as",
                        r"\b(each|all|following)\b",
                        R_NOT,
                        r"(related|connected|relationships?)\s*(with|to)",
                    ),
                    PositionPattern.compile(
                        r"saved?\s*as",
                        r"\bnone of the retiring",
                        r"(related|connected|relationships?)\s*(with|to)",
                    ),
                ],
                "ns_pattern": [
                    rf"{R_NOT}{reg_words(0, 3)}\s*ha(ve|s|d)\s*any\s*{reg_words(0, 2)}relationships?\s*(with|to)",
                    rf"{R_NOT}{reg_words(0, 3)}\s*(related|connected|relationship)\s*(to|with)\s*any\s*{reg_words(0, 2)}director",
                    rf"ha(ve|s|d)\s*{R_NOT}\s*{reg_words(0, 2)}relationships?\s*(with|to)\s*any",
                ],
                "ps_pattern": [
                    MatchMulti.compile(
                        rf"\b{R_BE}\b\s*{reg_words(0, 3)}(the|a)\s*{R_RELATIONSHIP}\b\s*of",
                        # http://************:55647/#/project/remark/324997?treeId=51128&fileId=89302&schemaId=33&projectId=51128&schemaKey=M37-relationships%20with%20any%20directors,%20senior%20management%20or%20substantial%20or%20controlling%20shareholders%20of%20the%20issuer
                        rf"(the|an?) {R_RELATIONSHIP} of",
                        rf"and\b.*?(\b{R_BE}\b)?\s*{R_RELATIONSHIP}",
                        operator=any,
                    ),
                ],
            },
        ],
    },
    {
        "path": [
            "M38-interests in shares of the issuer",
            "Content",
        ],
        "models": [
            {
                "name": "agm_m38",
                "multi": True,
                "multi_elements": True,
                "extend_candidates_syllabus_regs": [r"^(INDEPENDENT )?(NON-)?EXECUTIVE DIRECTOR$"],
                "filter_elements_by_target": False,
                "title_patterns": [PositionPattern.compile(r"\binterests (in|of)", R_PART_XV)],
                "neglect_sentence_pattern": MatchMulti.compile(
                    r"deemed connected person", rf"shares? of which {R_BE} listed", operator=any
                ),
                "para_separator": P_SEN_SEPARATOR,
                "item_separator": R_ITEM_SEPARATOR,
                "neglect_item_pattern": [r"major appointments? and professional qualifications?"],
                "as_follow_pattern": [
                    r"save\s*as.*re-election.*[:：]\s*$",
                    r"^Save as disclosed herein",
                ],
                "below_need_continuous": True,
                "below_skip_chinese": False,
                "all_ns_pattern": [
                    PositionPattern.compile(
                        R_SAVE_AS,
                        r"\b(each|all|directors\s*proposed\s*for\s*re-election)\b",
                        R_NOT,
                        r"interest.*?shares",
                        R_PART_XV,
                    ),
                    PositionPattern.compile(
                        r"none\s*of", r"(following|directors?.*?re-elect)", r"interest.*?shares", R_PART_XV
                    ),
                    PositionPattern.compile(
                        r"\bdirectors?\b", r"\bnot (have|has) any\b", r"interest.*?shares", R_PART_XV
                    ),
                ],
                "ns_pattern": [
                    MatchMulti.compile(
                        rf"({R_NOT}|\snot[,，]?\s+).*interest(s|ed)?\s*{reg_words(0, 6)}in\s*(the\s*|any\s*)?(issued\s*)?share",
                        R_PART_XV,
                        operator=all,
                    ),
                    MatchMulti.compile(rf"{R_NOT}(hold|held|ha(ve|s|d)).*?interest", R_PART_XV, operator=all),
                    PositionPattern.compile(R_SAVE_AS, r"\bno\s*other\s*interest", R_PART_XV),
                ],
                "ps_pattern": [
                    rf"(interest(ed|s)?|(hold|held)).*?{reg_words(0, 5)}{R_NUMBER}\s*{reg_words(0, 2)}\s*(shares|share\s*options)",
                    # http://************:55647/#/project/remark/305923?treeId=45769&fileId=71162&schemaId=33&projectId=45769&schemaKey=M38-interests%20in%20shares%20of%20the%20issuer
                    rf"interest(ed|s)?\s*in\s*share\s*options\s*{reg_words(0, 3)}{R_NUMBER}",
                    rf"share\s*options?\s*.*?{R_NUMBER}\s*{reg_words(0, 3)}share",
                    MatchMulti.compile(
                        r"practicable\s*date",
                        "(hold|held|ha(ve|s|d))",
                        rf"{R_NUMBER}\s*{reg_words(0, 2)}\s*share",
                        operator=all,
                    ),
                    MatchMulti.compile(rf"{R_NUMBER}\s*{reg_words(0, 2)}\s*shares", R_PART_XV, operator=all),
                    MatchMulti.compile("benefi(t|cial)", rf"{R_NUMBER}\s*{reg_words(0, 2)}\s*shares", operator=all),
                    # http://************:55647/#/project/remark/325021?treeId=51132&fileId=89326&schemaId=33&projectId=36&schemaKey=M38-interests%20in%20shares%20of%20the%20issuer
                    PositionPattern.compile(
                        "through the trustee", rf"\d+(\.\d*?)?{R_PERCENT}\s*interest(ed|s)?"
                    ),  # 间接持股也属于
                ],
            },
        ],
    },
]
