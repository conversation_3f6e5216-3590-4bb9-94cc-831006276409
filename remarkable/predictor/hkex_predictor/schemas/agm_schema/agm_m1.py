from remarkable.common.constants import AnswerValueEnum
from remarkable.common.pattern import PositionPattern

predictor_options = [
    {
        "path": ["M1-disclaimer on the front page", "Content"],
        "models": [
            {
                "name": "fixed_position",
                "positions": list(range(6)),
                "pages": [0],
                "regs": [
                    PositionPattern.compile(
                        r"Hong Kong Exchanges and Clearing Limited",
                        r"take no responsibility for",
                        r"disclaim any liability",
                        r"the contents of this (supplemental\s)?circular",
                    ),
                ],
                "enum": AnswerValueEnum.PS.value,
            },
            {
                "name": "score_filter",
                "skip_syllabus_title": True,
                "threshold": 0.5,
                "enum": AnswerValueEnum.PS.value,
            },
        ],
    }
]
