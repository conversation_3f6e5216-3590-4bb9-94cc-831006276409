from remarkable.common.constants import AnswerValueEnum

predictor_options = [
    {
        "path": ["M26-length of tenure of each INED when all INED serving more than nine years", "Content"],
        "models": [
            {
                "name": "agm_m26",
            },
            {
                "name": "kmeans_classification",
                "enum": AnswerValueEnum.PS.value,
                "threshold": 0.618,
            },
        ],
    },
    {
        "path": ["M27-appoint a new INED when all INED serving more than nine years", "Content"],
        "models": [
            {
                "name": "agm_m27",
            },
            {
                "name": "kmeans_classification",
                "enum": AnswerValueEnum.PS.value,
                "threshold": 0.618,
            },
        ],
    },
]
