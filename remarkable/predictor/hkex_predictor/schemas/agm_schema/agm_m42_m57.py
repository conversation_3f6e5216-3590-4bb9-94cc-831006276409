from remarkable.common.constants import AnswerValueEnum

M58_NS_MODEL = {
    "name": "reference",
    "from_path": [
        "M58-information to be disclosed pursuant to any of the requirements of this rule 13.51(2)",
        "Content",
    ],
    "from_answer_value": [
        AnswerValueEnum.NS,
        AnswerValueEnum.ND,
    ],
    "only_reference_crude_elements": True,
}


predictor_options = [
    {
        "path": [
            "M42-public sanctions made against directors",
            "Content",
        ],
        "models": [
            M58_NS_MODEL,
        ],
    },
    {
        "path": [
            "M43-directors adjudged bankrupt or insolvent",
            "Content",
        ],
        "models": [
            M58_NS_MODEL,
        ],
    },
    {
        "path": [
            "M44-deed of arrangement or the arrangement or composition with creditors",
            "Content",
        ],
        "models": [
            M58_NS_MODEL,
        ],
    },
    {
        "path": [
            "M45-unsatisfied judgments or court orders against directors",
            "Content",
        ],
        "models": [
            M58_NS_MODEL,
        ],
    },
    {
        "path": [
            "M46-dissolved or put into liquidation",
            "Content",
        ],
        "models": [
            M58_NS_MODEL,
        ],
    },
    {
        "path": [
            "M47-any conviction for any offence",
            "Content",
        ],
        "models": [
            M58_NS_MODEL,
        ],
    },
    {
        "path": [
            "M48-insider dealing",
            "Content",
        ],
        "models": [
            M58_NS_MODEL,
        ],
    },
    {
        "path": [
            "M49-adjudged by a Court or arbitral body civilly liable for any fraud, breach of duty or other misconduct",
            "Content",
        ],
        "models": [
            M58_NS_MODEL,
        ],
    },
    {
        "path": [
            "M50-business registration or licence revoked",
            "Content",
        ],
        "models": [
            M58_NS_MODEL,
        ],
    },
    {
        "path": [
            "M51-disqualified from holding or deemed unfit to hold the position",
            "Content",
        ],
        "models": [
            M58_NS_MODEL,
        ],
    },
    {
        "path": [
            "M52-prohibited by law, full particulars of any investigation by any judicial, regulatory or governmental authority",
            "Content",
        ],
        "models": [
            M58_NS_MODEL,
        ],
    },
    {
        "path": [
            "M53-be refused admission to membership of any professional body or been censured or disciplined by any such body",
            "Content",
        ],
        "models": [
            M58_NS_MODEL,
        ],
    },
    {
        "path": [
            "M54-a member of a triad or other illegal society",
            "Content",
        ],
        "models": [
            M58_NS_MODEL,
        ],
    },
    {
        "path": [
            "M55-any investigation, hearing or proceeding brought or instituted by any securities regulatory authority",
            "Content",
        ],
        "models": [
            M58_NS_MODEL,
        ],
    },
    {
        "path": [
            "M56-be a defendant in any current criminal proceeding",
            "Content",
        ],
        "models": [
            M58_NS_MODEL,
        ],
    },
    {
        "path": [
            "M57-any other matters that need to be brought to the attention of holders of securities of the issuer",
            "Content",
        ],
        "models": [
            M58_NS_MODEL,
        ],
    },
]
