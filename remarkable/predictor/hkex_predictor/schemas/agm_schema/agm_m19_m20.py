from remarkable.common.constants import AnswerValueEnum
from remarkable.common.pattern import MatchMulti
from remarkable.predictor.hkex_predictor.models.yoda_layer.rule.by_pos import ParaFromPos
from remarkable.predictor.hkex_predictor.models.yoda_layer.rule.root import ScoreStartDestFilter

R_APPOINT_AUDITOR = r"to.*?appoint.*?(auditor|account(ing)?\s*firm)"
P_ORDINARY = MatchMulti.compile(
    r"^(AS\s*)?ORDINARY\s*RESOLUTIONS?$",
    r"^(AS\s*)?ORDINARY\s*BUSINESS$",
    operator=any,
)

auditor_models = [
    {
        "name": "kmeans_classification",
        "threshold": 0.8,
        "high_score_elements_count": 2,
        "enum": AnswerValueEnum.PS.value,
    },
    {
        "name": "yoda_layer",
        "threshold": 0.01,
        "multi_elements": True,
        "enum": AnswerValueEnum.PS.value,
        "rule": ScoreStartDestFilter(
            include_start=True,
            pattern=MatchMulti.compile(R_APPOINT_AUDITOR, operator=any),
            dest=ParaFromPos(
                start=0,
                limit=40,
                step=-1,
                pattern=MatchMulti.compile(
                    r"^(AS\s*)?ORDINARY\s*RESOLUTIONS?$",
                    operator=any,
                ),
            ),
        ),
    },
    {
        "name": "middle_paras",
        "enum": AnswerValueEnum.PS.value,
        "include_top_anchor": True,
        "include_bottom_anchor": True,
        "crude_top_offset": 10,
        "crude_bottom_offset": 20,
        "top_anchor_regs": [
            MatchMulti.compile(
                r"^(AS\s*)?ORDINARY\s*RESOLUTIONS?$",
                r"pass\s*(the\s*)?following\s*ordinary\s*resolutions?[:：]?$",
                operator=any,
            )
        ],
        "top_anchor_content_regs": [r"(?P<content>(as\s*)?ordinary\s*resolutions?)"],
        "middle_content_regs": ["(?P<content>^$)"],
        "bottom_anchor_regs": [R_APPOINT_AUDITOR],
    },
    {
        "name": "para_match",
        "enum": AnswerValueEnum.PS.value,
        "paragraph_pattern": [
            R_APPOINT_AUDITOR,
        ],
    },
]
predictor_options = [
    {"path": ["M19-appoint an auditor to hold office", "Content"], "models": auditor_models},
    {
        "path": ["M20-the appointment, removal and remuneration of auditors", "Content"],
        "models": [
            {"name": "reference", "from_path": ["M19-appoint an auditor to hold office", "Content"]},
        ],
    },
]
