import re

from remarkable.common.constants import Answer<PERSON><PERSON><PERSON><PERSON>num
from remarkable.common.pattern import <PERSON><PERSON>ult<PERSON>, NeglectPattern, PatternCollection
from remarkable.common.util import clean_txt, split_paragraph
from remarkable.predictor.hkex_predictor.schemas.pattern import (
    P_APPELLATION,
    P_INED,
    P_RETIRING_DIRECTOR,
    R_DIRECTOR,
    R_INED,
)
from remarkable.predictor.models.base_model import BaseModel
from remarkable.predictor.schema_answer import Predictor<PERSON><PERSON>ult
from remarkable.services.agm import DirectorCVMatcher

P_INDEPENDENCE = PatternCollection(r"independence|independent", re.I)


P_ALL_DIRECTORS = MatchMulti.compile(
    rf"(?:each|all)\s*(of\s*the\s*)?([a-z]+\s*)?{R_DIRECTOR}\b",
    operator=any,
)

P_ALL_INED = MatchMulti.compile(
    rf"(?:each|all)\s*(of\s*the\s*)?([a-z]+\s*)?{R_INED}\b",
    operator=any,
)

P_ALL_RETURING_DIRECTORS = MatchMulti.compile(
    rf"(?:each|all|above)\s*(of\s*the\s*)?([a-z]+\s*)?{R_DIRECTOR}\b",
    rf"Details\s*of\s*the\s*retiring\s*{R_DIRECTOR}\b",
    operator=any,
)


def nd_answers(flat_answers):
    new_answers = []
    for ans in flat_answers:
        ans.update_answer_value(AnswerValueEnum.ND)
        new_answers.append(ans)
    return new_answers


def extract_director_data(predictor) -> DirectorCVMatcher:
    director_matcher = DirectorCVMatcher(predictor.pdfinsight)
    director_matcher.extract()
    directors = [
        director["director"] for director in (predictor.prophet.metadata.get("director") or {}).get("ined_companies")
    ]
    director_matcher.filter_elements_data_by_director(directors)
    return director_matcher


def check_m28_nd_answer(answers: list[PredictorResult], **kwargs):
    flat_answers = BaseModel.get_common_predictor_results(answers)
    if not (predictor := kwargs.get("predictor")):
        return flat_answers
    director_matcher = extract_director_data(predictor)

    if not director_matcher.filter_director_data:
        if any(P_ALL_INED.search(clean_txt(answer.text)) for answer in flat_answers):
            return flat_answers
        return nd_answers(flat_answers)

    director_name_patterns = [
        director_matcher.build_director_name_pattern(director) for director in director_matcher.filter_director_data
    ]
    for answer in flat_answers:
        for sentence in split_paragraph(clean_txt(answer.text)):
            if P_INDEPENDENCE.nexts(sentence):
                if P_ALL_DIRECTORS.search(sentence):
                    return flat_answers
                for p_name in reversed(director_name_patterns):
                    if p_name.nexts(sentence):
                        director_name_patterns.remove(p_name)

    return (
        nd_answers(flat_answers)
        if director_name_patterns and len(director_name_patterns) != len(director_matcher.filter_director_data)
        else flat_answers
    )


def check_nd_answer_by_director(answers: list[PredictorResult], **kwargs):
    flat_answers = BaseModel.get_common_predictor_results(answers)
    if not (predictor := kwargs.get("predictor")):
        return flat_answers
    director_matcher = extract_director_data(predictor)

    if not director_matcher.filter_director_data:
        if any(P_ALL_INED.search(clean_txt(answer.text)) for answer in flat_answers):
            return flat_answers
        return nd_answers(flat_answers)

    director_name_patterns = [
        director_matcher.build_director_name_pattern(director) for director in director_matcher.filter_director_data
    ]

    for answer in flat_answers:
        for sentence in split_paragraph(clean_txt(answer.text)):
            if P_ALL_DIRECTORS.search(sentence):
                return flat_answers
            for p_name in reversed(director_name_patterns):
                if p_name.nexts(sentence):
                    director_name_patterns.remove(p_name)

    if not director_name_patterns:
        return flat_answers

    # 如果未在答案中定位到董事
    # 答案中仅说明对重新选举为董事的退休董事详情进行披露，需检查当前被选举的董事是否存在新委任，存在则ND
    new_directors = predictor.prophet.metadata.get("new_director") or []
    if director_name_patterns:
        if all(director not in new_directors for director in director_matcher.filter_director_data):
            for answer in flat_answers:
                for sentence in split_paragraph(clean_txt(answer.text)):
                    if P_ALL_RETURING_DIRECTORS.search(sentence):
                        return flat_answers
    return (
        nd_answers(flat_answers)
        if len(director_name_patterns) != len(director_matcher.filter_director_data)
        else flat_answers
    )


def check_m30_nd_answer(answers: list[PredictorResult], **kwargs):
    """
    如无ined信息，直接nd，否则保留原有答案
    """
    flat_answers = BaseModel.get_common_predictor_results(answers)
    if not (predictor := kwargs.get("predictor")):
        return flat_answers
    director_matcher = extract_director_data(predictor)
    new_directors = predictor.prophet.metadata.get("new_director") or []
    directors = [
        director["director"] for director in (predictor.prophet.metadata.get("director") or {}).get("ined_companies")
    ]
    # 全部为新董事，则剔除答案中仅有“退休董事”的答案
    if len(new_directors) == len(directors):
        flat_answers = [
            answer
            for answer in flat_answers
            if not (
                P_RETIRING_DIRECTOR.nexts(clean_txt(answer.text))
                and not P_APPELLATION.search(clean_txt(answer.text))
                and not P_INED.nexts(clean_txt(answer.text))
            )
        ]
    return [] if not director_matcher.filter_director_data else flat_answers


predictor_options = [
    {
        "path": ["M28-AC1.B.3.4(a)", "Process for identifying & Reason for election"],
        "post_process": check_nd_answer_by_director,
        "models": [
            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6154#note_667411
            {
                "name": "syllabus_elt_v2",
                "enum": AnswerValueEnum.PS.value,
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__(?:PROCEDURE|PROCESS)\s*FOR\s*NOMINATION\s*OF\s*DIRECTORS?",
                    r"__regex__NOMINATION\S*PROCEDURE",
                ],
            },
            {
                "name": "agm_m28_1",
            },
            {
                "name": "score_filter",
                "threshold": 0.7,
                "enum": AnswerValueEnum.PS.value,
            },
        ],
    },
    {
        "path": ["M28-AC1.B.3.4(a)", "Independence confirmation"],
        "post_process": check_m28_nd_answer,
        "models": [
            {
                "name": "para_match",
                "multi_elements": True,
                "enum": AnswerValueEnum.PS.value,
                "extend_candidates_syllabus_regs": NeglectPattern.compile(
                    match=MatchMulti.compile(
                        r"(?:RE-ELECTION|ELECT)\s*OF\s*(THE\s*RETIRING\s*)?DIRECTORS",
                        r"DETAILS\s*OF\s*RETIRING\s*DIRECTORS\s*PROPOSED\s*FOR\s*(?:RE-ELECTION|ELECT)",
                        r"LETTER\s*FROM\s*THE\s*BOARD",
                        operator=any,
                    ),
                    unmatch=MatchMulti.compile(
                        r"(?:PROCEDURE|PROCESS)\s*FOR\s*NOMINATION",
                        operator=any,
                    ),
                ),
                "paragraph_pattern": (
                    MatchMulti.compile(
                        r"meet|fulfill?|confirmation|assessed|evaluated|reviewed|according[^.]*?independence\s*guidelines",
                        r"independence|independent",
                        r"3[\.．]13|5[\.．]09",
                        # R_INED,
                        operator=all,
                    ),
                    NeglectPattern.compile(
                        match=MatchMulti.compile(
                            rf"maintains\s*an\s*independent\s*view.*?carry\s*out.*?{R_INED}",
                            rf"(?:continue\s*fulfilling[a-zA-Z\s]*|independence)\s*of\s*an\s*{R_INED}\s*.*?still\s*independent",
                            r"confirmed\s*in\s*writing[^.]*?independence",
                            r"(?:confirmed|satisfied|confirming).*?(?:independence|independent).*?rules?\s(?:3[\.．]13|5[\.．]09)",
                            r"(Mr|Ms|Dr|Prof|Mdm|Mass|Mrs|nomination\s*committee).*?(?:maintain|mee?ts?|considered|confirm(?:ation|ed))\s*[a-zA-Z\s]*?([^.]*?includ(e|ing)\s*the\s*)?independence",
                            rf"nomination\s*committee\s*has\s*reviewed[^.]*?(confirmations|disclosures)[^.]*?independence\s*of\s*all\s*{R_INED}",
                            rf"(?:consider|believe)s?[^.]*?retiring\s*{R_INED}[^.]*?independen(?:t|ce)",
                            rf"nothing[^.]*?affect[^.]*?independen(?:t|ce)[^.]*?retiring\s*{R_INED}",
                            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6571#note_685190
                            r"Independence\s*of.*?of\s*the\s*listing\s*rules\s*.?\s*was\s*also\s*assessed",
                            r"(?:Nomination\s*Committee|board)[^.]*?satisfied[^.]*?(?:independence|independent).*?of\s*the\s*Listing\s*Rules",
                            operator=any,
                        ),
                        unmatch=MatchMulti.compile(
                            r"company[^.]*?not\s*aware\s*[^.]*?affect[^.]*?independence", operator=any
                        ),
                    ),
                ),
            },
            {
                "name": "agm_m28_2",
            },
        ],
    },
    {
        "path": ["M29-AC1.B.3.4(b)"],
        "models": [
            {
                "name": "agm_m29",
            },
        ],
    },
    {
        "path": ["M30-the perspectives, skills and experience of proposed INEDs", "Content"],
        "post_process": check_m30_nd_answer,
        "models": [
            {
                "name": "agm_m30",
            },
            {
                "name": "score_filter",
                "threshold": 0.8,
                "enum": AnswerValueEnum.PS.value,
            },
        ],
    },
    {
        "path": ["M31-contribution to diversity from proposed INEDs", "Content"],
        "post_process": check_nd_answer_by_director,
        "models": [
            {
                "name": "agm_m31",
                "extend_candidates_syllabus_regs": [r"LETTER\s*FROM\s*THE\s*(?:BOARD|CHAIRMAN)"],
            },
            {
                "name": "score_filter",
                "threshold": 0.8,
                "enum": AnswerValueEnum.PS.value,
            },
        ],
    },
]
