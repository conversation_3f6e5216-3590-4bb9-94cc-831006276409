from remarkable.common.common_pattern import P_SEN_SEPARATOR
from remarkable.common.constants import AnswerValueEnum
from remarkable.common.pattern import Match<PERSON><PERSON><PERSON>, PositionPattern

predictor_options = [
    {
        "path": [
            "M40-any person appointed by the directors to fill a casual vacancy on or as an addition to the board",
            "Content",
        ],
        "models": [
            {
                "name": "agm_m40",
            },
        ],
    },
    {
        "path": ["M41-AC1.B.2.3", "Separate resolution"],
        "models": [
            {
                "name": "agm_m41_separate_resolution",
                "neglect_syllabus_regs": [r"RE-ELECTION AND ELECTION OF THE DIRECTORS$"],
            },
        ],
    },
    {
        "path": ["M41-AC1.B.2.3", "Factor consider"],
        "models": [
            {
                "name": "agm_m41_nd",
            },
            {
                "name": "para_match",
                "para_separator": P_SEN_SEPARATOR,
                "multi_elements": True,
                "multi": True,
                "paragraph_pattern": [
                    MatchMulti.compile(
                        r"\b(view|review|confirm)",
                        r"\bindependen(ce|t) (criteria|guidelines|judgement)",
                        r"\b(independent non-executive Directors?|INEDs?)",
                        r"rule (3\.13|5\.09)",
                        operator=all,
                    ),
                    MatchMulti.compile(
                        r"impact on the independence", r"\bbelieve?(s|d|ing)?\b", r"still independent", operator=all
                    ),
                    PositionPattern.compile(
                        r"\b(view|review|confirm|provide|receive)",
                        r"\bconfirmation of independence",
                        r"rule (3\.13|5\.09)",
                    ),
                    PositionPattern.compile(r"\bconsider", r"\bmeets? the independence", r"rule (3\.13|5\.09)"),
                    MatchMulti.compile(r"\b(affirm|consider)", r"\bremain independent", operator=all),
                ],
                "enum": AnswerValueEnum.PS.value,
            },
            {
                "name": "agm_m41_director_profile",
                "paragraph_pattern": MatchMulti.compile(
                    r"\b(view|review|confirm)",
                    r"\bindependen(ce|t)",
                    r"with the listing rules?",
                    operator=all,
                ),
            },
            {
                "name": "score_filter",
                "threshold": 0.1,
                "enum": AnswerValueEnum.PS.value,
            },
        ],
    },
    {
        "path": ["M41-AC1.B.2.3", "Process"],
        "models": [
            {
                "name": "agm_m41_nd",
            },
            {
                "name": "multi_models",
                "operator": "union",
                "enum": AnswerValueEnum.PS.value,
                "require_unique_element": True,
                "models": [
                    {
                        "name": "para_match",
                        "syllabus_regs": ["RE-ELECTION OF.*?DIRECTORS", "DIRECTORS? PROPOSED FOR RE-ELECTION"],
                        "paragraph_pattern": MatchMulti.compile(
                            r"\b(assess|review|conduct|consider|recommend)(s|e?d|ing)?\b", operator=any
                        ),
                        "enum": AnswerValueEnum.PS.value,
                    },
                    {
                        "name": "para_match",
                        "syllabus_regs": [
                            r"RE-ELECTION OF.*?DIRECTORS",
                            r"DIRECTORS? PROPOSED FOR RE-ELECTION",
                            r"RECOMMENDATION OF THE NOMINATION COMMITTEE",
                        ],
                        "paragraph_pattern": [
                            PositionPattern.compile(
                                r"In view of the above",
                                r"the Board (consider|believe)(s|e?d|ing)",
                            ),
                            PositionPattern.compile(
                                r"\bnomination",
                                r"\bmade in accordance with\b",
                                r"\bnomination policy\b",
                                r"\bobjective criteria\b",
                            ),
                        ],
                        "enum": AnswerValueEnum.PS.value,
                    },
                    {
                        "name": "para_match",
                        "paragraph_pattern": MatchMulti.compile(r"\bevaluate", operator=any),
                        "enum": AnswerValueEnum.PS.value,
                    },
                    # {
                    #     "name": "score_filter",
                    #     "threshold": 0.06,
                    #     "enum": AnswerValueEnum.PS.value,
                    # },
                ],
            },
            {
                "name": "agm_m41_director_profile",
                "paragraph_pattern": MatchMulti.compile(
                    r"\b(assess|review|conduct|consider|recommend|evaluate)(s|e?d|ing)?\b", operator=any
                ),
            },
        ],
    },
]
