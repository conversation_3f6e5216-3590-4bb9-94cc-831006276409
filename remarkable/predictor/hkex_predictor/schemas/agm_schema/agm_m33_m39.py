import re

from remarkable.common.common_pattern import (
    P_PERIOD_SEPARATOR,
    P_SEN_SEPARATOR,
    R_CHAPTER_PREFIX,
    R_EXCHANGE,
    R_NOTE_PREFIX,
    R_PERIOD,
)
from remarkable.common.pattern import MatchMulti, NeglectPattern, PositionPattern
from remarkable.predictor.common_pattern import P_YEAR, R_DATES
from remarkable.predictor.hkex_predictor.models.agm_m34 import P_AGE, R_APPOINT, R_POSITION
from remarkable.predictor.hkex_predictor.models.agm_m35 import R_EN_DATE
from remarkable.predictor.hkex_predictor.pattern import R_BE
from remarkable.predictor.hkex_predictor.schemas.pattern import (
    R_APPELLATION,
    R_ENG_NUMBER,
    R_NOT,
    reg_words,
)

R_ITEM_SEPARATOR = re.compile(r"[(（][ivx]+[)）]")

R_PREFIX = r"(?:independent )?(?:(non-)?executive)?"


# M34 本公司内部任职
R_M34_FOLLOW = r"(appointed|joined|invited|serv(ed?|ing)|act(ed)?).*following\s*positions?\s*in\s*(the|our)\s*group:"

# M35 其他上市公司任职 及 专业经验
R_M35_FOLLOW = r" following\s*positions\s*outside\s*the\s*group:"
R_LISTED = rf"(stock\s*code|listed.*?(exchange|market|{R_EXCHANGE}|overseas)|listed\s*(public\s*)?compan(y|ies))"
R_COMP = r"\b(?:Inc|Ltd|GmbH|LLC|Corp|公司|Limited)\b"

P_M35_PS = MatchMulti.compile(
    MatchMulti.compile(
        rf"(?<!not\s)\b(be|been|is|are|was|were|serve(s|d))\b\s{reg_words(0, 3)}(a|an|the)?\s*{R_POSITION}",
        # 没有动词以职位开头
        rf"^{R_NOTE_PREFIX}?\s*(a|an|the)?\s*{R_POSITION}",
        operator=any,
    ),
    R_LISTED,
    operator=all,
)

# M36
R_PREFIX_NAME = rf"{R_CHAPTER_PREFIX}\s*{R_APPELLATION}"
P_M36_TIME = MatchMulti.compile(*R_DATES, P_YEAR, operator=any)

P_M36_POSITION = MatchMulti.compile(
    # 1 系动词 + 职位
    rf"(?<!remuneration for ){R_BE}\s*{reg_words(0, 2)}(a|an|the)\s*{R_POSITION}",
    # 2 被委任为 + 职位
    rf"{R_APPOINT}( (to )?(the )?board )?\s*as\s*(a|an|the)\s*{R_POSITION}",
    operator=any,
)
# M36 任职 + 时间 相关描述
M36_UNMATCH = MatchMulti.compile(R_LISTED, R_COMP, re.compile(r"\b(TTG|UG Management)\b"), operator=any)
P_M36_APPOINT = [
    # 1. 有年龄， 一般是描述本公司的任职
    NeglectPattern.compile(
        match=MatchMulti.compile(
            MatchMulti.compile(R_PREFIX_NAME, rf"{R_CHAPTER_PREFIX}\s*{reg_words(2, 5)}aged?", operator=any),
            P_M36_POSITION,
            MatchMulti.compile(P_AGE, r"joined\s*the\s*Board", operator=any),
            P_M36_TIME,
            operator=all,
        ),
        unmatch=M36_UNMATCH,
    ),
    # 2. 有明确的描本公司
    NeglectPattern.compile(
        match=MatchMulti.compile(P_M36_POSITION, P_M36_TIME, r"(the|our)\s*company", operator=all),
        unmatch=M36_UNMATCH,
    ),
    # 3. 最简单的描述， 只有名称 + 职位 + 时间 不包含任何公司信息
    rf"{R_PREFIX_NAME}\s?{reg_words(1, 5)}({R_BE}|{R_APPOINT})\s(as\s)?(a|an|the)\s{R_POSITION}\s(and\s(a|an|the)\smember\sof\s{reg_words(1, 3)})?(from|since|in|on)\s{R_EN_DATE}[.]?\s*$",
    # 4. join company , join group + 职位 + 时间
    MatchMulti.compile(r"(join|j oin)(ed)?\s*(the|our)\s*company", r"\b(from|in|on)\b", P_M36_TIME, operator=all),
    NeglectPattern.compile(
        match=MatchMulti.compile(
            r"((join|j oin)(ed)?|work\sin)\s*(the|our)\s*group",
            R_POSITION,
            r"\b(from|in|on)\b",
            P_M36_TIME,
            operator=all,
        ),
        unmatch=M36_UNMATCH,
    ),
]
# M36描述合同任期相关正则
P_M36_TERM = [
    # 1. 委任为**职位 + 年限
    MatchMulti.compile("appoint", rf"term\s*(of|for)\s*{reg_words(0, 3)}({R_ENG_NUMBER}|\d+)\s*year", operator=all),
    MatchMulti.compile("term", "appoint", "renewed|continue", rf"({R_ENG_NUMBER}|\d+)\s*year", operator=all),
    # 2.与公司签订了合同 + 年限
    MatchMulti.compile(
        MatchMulti.compile(
            r"service\s*contract\s*for",
            r"term\s*of\s*service\s*",
            r"(contract|service\sagreement).*?term",
            r"(letter of appointment|appointment letter).*term",
            operator=any,
        ),
        MatchMulti.compile(
            rf"({R_ENG_NUMBER}|\d+)\s*year",
            rf"({R_NOT}|without)\s*{reg_words(0, 2)}(fixed|specified)\s*term",
            operator=any,
        ),
        operator=all,
    ),
    MatchMulti.compile(r"service\s*contract", "renewable", operator=all),
    # 3. 合同/委任书 + 时间点
    MatchMulti.compile(
        "entered into", "(appointment letter|service agreement)", "the company", P_M36_TIME, operator=all
    ),
    # 4. 轮值相关描述
    # 没有固定期限，轮值
    MatchMulti.compile(
        rf"(({R_NOT}|without)\s*{reg_words(0, 2)}(fixed|specified)\s*term|any\s*service\s*contract)",
        "rotation",
        operator=all,
    ),
    # 轮值 + 时间段
    rf"retirement.*rotation\s.*({R_ENG_NUMBER}|\d+)\s*years",
]

# M38
R_PART_XV = r"part\s*XV\s*(of|to)\s*(the\s*)?(SFO|Securities\s*and\s*Futures\s*Ordinance)"

predictor_options = [
    {
        "path": ["M33-the full name and age of proposed directors", "Content"],
        "models": [
            {
                "name": "agm_m33",
                "anchor_regs": [
                    rf"{R_CHAPTER_PREFIX}(?P<content>{R_APPELLATION}\s*{reg_words(1, 8)})\s*$",
                ],
                "multi_elements": True,
                "paragraph_pattern": [
                    rf"{R_CHAPTER_PREFIX}(?P<content>{R_APPELLATION}\s*.*?aged?[,，:：]?\s*\d+[）)]?)\s*[,，]",
                    rf"{R_CHAPTER_PREFIX}(?P<content>{R_APPELLATION}\s*{reg_words(1, 5)}[,,]\s*\d+\s*(years\s*old)?)[,，]",
                    rf"{R_CHAPTER_PREFIX}(?P<content>{reg_words(2, 5)}aged?[,，:：]?\s*\d+[）)]?)\s*[,，]\s*{R_BE}\s*appointed",
                    # http://************:55647/#/project/remark/324544?treeId=51020&fileId=88849&schemaId=33&projectId=51020&schemaKey=M37-relationships%20with%20any%20directors,%20senior%20management%20or%20substantial%20or%20controlling%20shareholders%20of%20the%20issuer
                    rf"(?P<content>aged\s*\d+[.。]\s*{R_APPELLATION}\s*{reg_words(2, 5)})joined",
                    rf"{R_CHAPTER_PREFIX}(?P<content>{reg_words(2, 5)}[(“]*{R_APPELLATION}\s*{reg_words(1, 3)}[”)]*[,，]?aged?\s*\d+)",
                    rf"{R_CHAPTER_PREFIX}(?P<content>(?:{R_APPELLATION})?\s*.*?aged?[,，:：]?\s*\d+[）)]?)",  # fid:81870
                ],
            },
            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7049
            {
                "name": "agm_m33",
                "anchor_regs": [
                    rf"{R_CHAPTER_PREFIX}(?P<content>{R_APPELLATION}\s*{reg_words(1, 8)})\s*$",
                ],
                "syllabus_regs": [
                    rf"{R_CHAPTER_PREFIX}(?P<content>{R_APPELLATION}\s*{reg_words(1, 8)})\s*$",
                ],
                "multi_elements": True,
                "paragraph_pattern": [
                    rf"{R_CHAPTER_PREFIX}(?P<content>{R_APPELLATION}\s*{reg_words(1, 5)}[,，]\s*born\s*in\s*{R_EN_DATE})",
                ],
                "extend_candidates_syllabus_regs": [rf"{R_CHAPTER_PREFIX}CANDIDATES FOR.*DIRECTORS?"],
            },
            {
                "name": "agm_m33",
                "para_separator": P_SEN_SEPARATOR,
                "multi_elements": True,
                "paragraph_pattern": [
                    rf"{R_CHAPTER_PREFIX}(?P<content>{R_APPELLATION}\s*{reg_words(1, 5)}aged?[,，]?\s*\d+)$",
                ],
            },
        ],
    },
    {
        "path": ["M34-positions of proposed directors", "Content"],
        "models": [
            {
                "name": "agm_m34",
                "multi_elements": True,
                "para_separator": P_PERIOD_SEPARATOR,
                "as_follow_pattern": R_M34_FOLLOW,
                "extend_candidates_syllabus_regs": [r"^ORDINARY BUSINESS$", "^NOTICE OF ANNUAL GENERAL MEETING$"],
                "below_need_continuous": True,
                "below_skip_chinese": False,
            },
        ],
    },
    {
        "path": [
            "M35-experience including other directorships held in the last three years in public companies and other major appointments and professional qualifications",
            "Content",
        ],
        "models": [
            {
                "name": "agm_m35",
                # "para_separator": P_SEN_SEPARATOR,
                "para_separator": re.compile(R_PERIOD),
                "multi": True,
                "as_follow_pattern": [r"save\s*as.*(re-election|directors?\s*confirm).*[:：]\s*$"],
                "below_need_continuous": True,
                "below_skip_chinese": False,
                "item_separator": R_ITEM_SEPARATOR,
                "ps_pattern": [P_M35_PS],
                "ns_pattern": [
                    PositionPattern.compile(
                        R_NOT, rf"(hold|held|ha(ve|s|d))\s*{reg_words(0, 5)}any\s*directorship", R_LISTED
                    ),
                    PositionPattern.compile(
                        R_NOT, rf"(hold|held|ha(ve|s|d))\s*{reg_words(0, 5)}any\s*directorship", r"past\s*three\s*years"
                    ),
                    PositionPattern.compile(
                        r"save\s*as", R_NOT, rf"position|directorship|{R_BE}\s(a|an|the)?\sdirector", R_LISTED
                    ),
                    NeglectPattern.compile(
                        match=MatchMulti.compile(R_NOT, "position|directorship", operator=all),
                        unmatch=r"the\s*(group|company)|remuneration",
                    ),
                ],
                "all_ns_pattern": [
                    PositionPattern.compile(
                        r"save\s*as",
                        r"\b(each|all|directors\s*proposed\s*for\s*re-election|the\s*retiring\s*directors?)\b",
                        rf"{R_NOT}.*any\s*{reg_words(0, 5)}directorship",
                        r"three\s*years",
                    ),
                    PositionPattern.compile(
                        r"save\s*as",
                        r"none\s*of",
                        rf"any\s*{reg_words(0, 5)}directorship",
                        r"(three|3)\s*years",
                    ),
                    PositionPattern.compile(
                        r"save\s*as",
                        r"none\s*of",
                        r"(three|3)\s*years",
                        rf"any\s*{reg_words(0, 5)}directorship",
                    ),
                ],
            },
        ],
    },
    {
        "path": [
            "M36-length or proposed length of service of proposed directors",
            "Content",
        ],
        "models": [
            {
                "name": "agm_m36",
                "multi_elements": True,
                "para_separator": P_SEN_SEPARATOR,
                "ps_pattern": [*P_M36_APPOINT, *P_M36_TERM],
            },
        ],
    },
    {
        "path": ["M39-13.74 - 13.51(2)(g)"],
        "models": [
            {
                "name": "agm_m39",
                "para_separator": re.compile(R_PERIOD),
                "multi": True,
            },
        ],
    },
]
