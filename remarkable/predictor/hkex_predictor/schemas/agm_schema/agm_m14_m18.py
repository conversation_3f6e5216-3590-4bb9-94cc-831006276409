import re

from remarkable.common.constants import AnswerV<PERSON>ueEnum
from remarkable.common.pattern import <PERSON><PERSON><PERSON><PERSON>, PositionPattern
from remarkable.pdfinsight.reader import PdfinsightReader
from remarkable.predictor.hkex_predictor.schemas.agm_schema.agm_m2_m13 import R_BUY
from remarkable.predictor.hkex_predictor.schemas.pattern import R_NOT, reg_words
from remarkable.predictor.models.base_model import BaseModel
from remarkable.predictor.schema_answer import PredictorResult

R_SHARE_NUM = rf"(numbers?|amount|nominal\s*value)\s*of\s*(the\s*)?{reg_words(0, 2)}(share|securities)"

R_NOT_EXCEED = rf"(\bnot?\s*{reg_words(0, 2)}(exceed(ing)?|more\s*than)|\bup\sto\b)"

R_M16_PRECENT = r"([12]0[%％]|[12]0\s*per\s?cent)"

R_M16_SYLLABUS = rf"general\s*mandate\s*{reg_words(0, 3)}issuance (of )?([a-z] )?shares?$"

R_M14_SYLLABUS = rf"general\s*mandate\s*{reg_words(0, 3)}{R_BUY} (of )?([a-z] )?shares?$"

P_RELEVANT_PERIOD = MatchMulti.compile(
    r"conclusion.*?(meeting|\bagm\b)",
    r"(expiration|expiry)\s*of\s*(the\s*)?period",
    r"revocation|variation|revoke",
    operator=any,
)

P_M15_M18_IGNORE = MatchMulti.compile(
    r"^NOTICE\s*OF\s*(THE)?\s*(AGM|ANNUAL\s*GENERAL\s*MEETING)$", r"^(THE)?\s*AGM\s*NOTICE$", operator=any
)
R_MANDATE_PURCHASE = r"mandate\s*(for|to|of)\s*(the\s)?(re)?purchase"

R_ITEM_START = r"^[(（]([a-z])\1[）)]"

P_SPECIAL_M15_SYLLABUS = MatchMulti.compile(
    r"^SPECIAL RESOLUTIONS$",
    operator=any,
)

P_M15_NEGLECT_PATTERN = MatchMulti.compile(
    r"the proposed grant of general mandate to the Board to issue additional Shares", operator=any
)

P_SPECIAL_M18_SYLLABUS = MatchMulti.compile(
    r"That:$",
    operator=any,
)
P_M18_NEGLECT_PATTERN = MatchMulti.compile(
    r"Relevant Period.*the Company to repurchase the Shares",
    operator=any,
)


def filter_issue_answer_for_m15(answers: list[PredictorResult], **kwargs):
    # 找到答案的章节，再看章节下的 RESOLUTIONS 是否是M18相关的 `issue mandate`， 如果是则排除
    flat_answers = BaseModel.get_common_predictor_results(answers)
    if not flat_answers:
        return answers
    element_result = flat_answers[0].element_results[0]
    para_element = element_result.element
    pdfinsight: PdfinsightReader = kwargs.get("predictor").pdfinsight
    syllabuses = pdfinsight.find_syllabuses_by_index(para_element["index"])
    if not syllabuses:
        return answers
    syllabus = syllabuses[-1]
    if not P_SPECIAL_M15_SYLLABUS.search(syllabus["title"]):
        return answers
    ele_type, next_para = pdfinsight.find_element_by_index(syllabus["range"][0] + 1)
    if ele_type != "PARAGRAPH":
        return answers
    if P_M15_NEGLECT_PATTERN.search(next_para["text"]):
        #  issue mandate 相关的属于M18
        return []
    return answers


def filter_purchase_elements(elements, predictor, **kwargs):
    rets = []
    pdfinsight: PdfinsightReader = predictor.pdfinsight
    for element in elements:
        syllabuses = pdfinsight.find_syllabuses_by_index(element["index"])
        if syllabuses:
            # 文档同时出现M15  M18 相关的描述， 需要根据 purchase相关的特征排除 M15的元素块
            syllabus = syllabuses[-1]
            if P_SPECIAL_M18_SYLLABUS.search(syllabus["title"]):
                ele_type, next_para = pdfinsight.find_element_by_index(syllabus["range"][0] + 1)
                if ele_type == "PARAGRAPH" and P_M18_NEGLECT_PATTERN.search(next_para["text"]):
                    continue
        rets.append(element)
    return rets


predictor_options = [
    {
        "path": ["M14-purchase mandate", "Content"],
        "models": [
            {
                "name": "para_match",
                "enum": AnswerValueEnum.PS.value,
                "paragraph_pattern": MatchMulti.compile(
                    R_SHARE_NUM,
                    rf"{R_BUY}.*{R_NOT_EXCEED}.*(10|ten)\s*([%％]|per\s?cent)",
                    operator=all,
                ),
            },
            {
                "name": "syllabus_based",
                "enum": AnswerValueEnum.PS.value,
                "inject_syllabus_features": [
                    rf"__regex__{R_M14_SYLLABUS}",
                ],
                "multi": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": MatchMulti.compile(
                        R_SHARE_NUM,
                        rf"{R_BUY}.*{R_NOT_EXCEED}.*(10|ten)\s*([%％]|per\s?cent)",
                        operator=all,
                    ),
                },
                "table_model": "empty",
            },
        ],
    },
    {
        "path": ["M15-relevant period for purchase mandate", "Content"],
        "post_process": filter_issue_answer_for_m15,
        "models": [
            {
                "name": "middle_paras",
                "enum": AnswerValueEnum.PS.value,
                "include_top_anchor": False,
                "include_bottom_anchor": True,
                "crude_top_offset": 20,
                "use_top_crude_neighbor": True,
                "top_anchor_regs": [
                    MatchMulti.compile(
                        rf"subject\s*to.*?the\s*powers?\s*of.*?{R_BUY}",
                        # http://100.64.0.105:55647/#/project/remark/319818?treeId=49838&fileId=84123&schemaId=33&projectId=49838&schemaKey=M15-relevant%20period%20for%20purchase%20mandate
                        rf"exercise.*?powers?\s*of\s*the\s*company\s*to\s*{R_BUY}",
                        operator=any,
                    )
                ],
                "middle_content_regs": ["(?P<content>^$)"],
                "bottom_anchor_regs": [
                    r"relevant\s*period.*?means.*?",
                    r"aforesaid\s*mandate\s*shall\s*remain\s*in\s*effect\s*until",
                ],
                "as_follow_pattern": [r"Relevant Period.*?[:：]?"],
                "below_need_continuous": True,
                "special_as_follow_start": [R_ITEM_START],
                "ignore_pattern": P_M15_M18_IGNORE,
            },
            {
                "name": "para_match",
                "enum": AnswerValueEnum.PS.value,
                "neglect_syllabus_regs": [r"MANDATE TO ISSUE SHARES?$"],
                "paragraph_pattern": MatchMulti.compile(
                    r"relevant\s*period.*?means",
                    operator=all,
                ),
                "as_follow_pattern": [r"Relevant Period.*?[:：]"],
                "below_need_continuous": True,
                "special_as_follow_start": [R_ITEM_START],
                "ignore_pattern": P_M15_M18_IGNORE,
            },
            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7090
            {
                "name": "para_match",
                "enum": AnswerValueEnum.PS.value,
                "extend_candidates_syllabus_regs": [
                    rf"{R_MANDATE_PURCHASE}.*Shares$",
                ],
                "as_follow_pattern": [
                    rf"{R_MANDATE_PURCHASE}.*effective.*earlier of[:：]$",
                    rf"validity\s*period.*{R_MANDATE_PURCHASE}.*earlier of[:：]$",
                ],
                "below_need_continuous": True,
                "multi_elements": True,
                "special_as_follow_start": [R_ITEM_START],
                "ignore_pattern": P_M15_M18_IGNORE,
            },
        ],
    },
    {
        "path": ["M16-issue mandate", "Content"],
        "models": [
            {
                "name": "middle_paras",
                "enum": AnswerValueEnum.PS.value,
                "include_bottom_anchor": True,
                "use_top_crude_neighbor": True,
                "top_anchor_regs": [
                    rf"{R_SHARE_NUM}.*otherwise\s*than\s*pursuant\s*to[:：]$",
                ],
                "bottom_anchor_regs": [
                    rf"{R_NOT_EXCEED}\s*{R_M16_PRECENT}",
                ],
                "ignore_pattern": P_M15_M18_IGNORE,
            },
            {
                "name": "para_match",
                "enum": AnswerValueEnum.PS.value,
                "extend_candidates_syllabus_regs": [R_M16_SYLLABUS],
                "syllabus_regs": [
                    R_M16_SYLLABUS,
                ],
                "paragraph_pattern": MatchMulti.compile(
                    r"issued?|deal\s*with|\ballot",
                    R_SHARE_NUM,
                    rf"{R_NOT_EXCEED}.*{R_M16_PRECENT}",
                    operator=all,
                ),
                "multi_elements": True,
                "ignore_pattern": P_M15_M18_IGNORE,
            },
            {
                "name": "para_match",
                "enum": AnswerValueEnum.PS.value,
                "paragraph_pattern": MatchMulti.compile(
                    r"issued?|deal\s*with|\ballot",
                    R_SHARE_NUM,
                    rf"{R_NOT_EXCEED}.*{R_M16_PRECENT}",
                    operator=all,
                ),
                "as_follow_pattern": [
                    MatchMulti.compile(R_NOT_EXCEED, R_SHARE_NUM, "[：:]$", operator=all),
                    rf"issue of shares?.*?{R_NOT_EXCEED}.*?aggregate\s*of\s*[：:]$",
                ],
                "any_below_pattern": [rf"{R_M16_PRECENT}"],
                "special_as_follow_start": [R_ITEM_START],
            },
        ],
    },
    {
        "path": ["M17-extension to general mandate", "Content"],
        "models": [
            # PS 在通过issue mandate + purchase mandate两项决议的前提下，可以增加的可发行股份的数量
            {
                "name": "para_match",
                "enum": AnswerValueEnum.PS.value,
                "paragraph_pattern": PositionPattern.compile(
                    r"(condition(al)?|subject)\s*(to|upon|on)|under",
                    r"resolution",
                    "extend|extension|add",
                    R_SHARE_NUM,
                ),
            },
            # NS不扩展issue mandate
            {
                "name": "para_match",
                "enum": AnswerValueEnum.NS.value,
                "paragraph_pattern": PositionPattern.compile(
                    rf"{R_NOT}.*(extend|extension|add)",
                    r"issue\s*mandate",
                ),
            },
        ],
    },
    {
        "path": ["M18-relevant period for issue mandate", "Content"],
        "models": [
            # 1
            {
                "name": "middle_paras",
                "enum": AnswerValueEnum.PS.value,
                "include_top_anchor": False,
                "include_bottom_anchor": True,
                "crude_top_offset": 20,
                "top_anchor_regs": [
                    MatchMulti.compile(
                        r"subject\s*to.*?the\s*powers?\s*of.*?(allot|issue|deal\s*with)",
                        r"general\s*mandate.*given.*(allot|issue|deal\s*with)",
                        r"exercise.*?powers?\s*of\s*the\s*company\s*to\s*(allot|issue|deal\s*with)",
                        operator=any,
                    )
                ],
                "max_paragraphs_for_every_follow": 3,
                "middle_content_regs": ["(?P<content>^$)"],
                "bottom_anchor_regs": [
                    r"relevant\s*period.*?means.*?[:：]?",
                    r"aforesaid\s*mandate\s*shall\s*remain\s*in\s*effect\s*until",
                ],
                "as_follow_pattern": [r"Relevant Period.*?[:：]?"],
                "below_need_continuous": True,
                "special_as_follow_start": [R_ITEM_START],
                "ignore_pattern": P_M15_M18_IGNORE,
            },
            # 2
            {
                "name": "para_match",
                "inject_elements_func": filter_purchase_elements,
                "enum": AnswerValueEnum.PS.value,
                "paragraph_pattern": [
                    r"relevant\s*period.*?means",
                    r"validity\s*period.*General Mandate for the Issuance of Shares.*[:：]$",  # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7093#note_717979
                ],
                "as_follow_pattern": [
                    r"Relevant Period.*?[:：]",
                    r"validity Period.*?[:：]",
                ],
                "below_need_continuous": True,
                "special_as_follow_start": [R_ITEM_START],
                "ignore_pattern": P_M15_M18_IGNORE,
            },
            # 3
            # Issue mandate有3种股票，分别为H、D、A shares , 特殊的period的描述<effective period> multi_elements=True
            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7093#note_717983
            {
                "name": "para_match",
                "enum": AnswerValueEnum.PS.value,
                "paragraph_pattern": [
                    r"effective period.*General Mandate.*[:：]$",
                ],
                "as_follow_pattern": [
                    r"effective Period.*?[:：]",
                ],
                "below_need_continuous": True,
                "multi_elements": True,
                "special_as_follow_start": [R_ITEM_START],
                "ignore_pattern": P_M15_M18_IGNORE,
            },
        ],
    },
]

if __name__ == "__main__":
    p = re.compile(rf"general\s*mandate\s*{reg_words(0, 3)}issuance (of )?([a-z] )?shares?$", re.I)
    para = "General Mandate for the Issuance of Shares"
    print(p.search(para))
