import re

from remarkable.common.common_pattern import P_PERIOD_SEPARATOR, R_CHAPTER_PREFIX
from remarkable.common.constants import AnswerV<PERSON>ueEnum, TableType
from remarkable.common.pattern import MatchMulti, NeglectPattern, PositionPattern
from remarkable.common.util import clean_txt, is_aim_element
from remarkable.pdfinsight.parser import parse_table
from remarkable.pdfinsight.reader import Index
from remarkable.predictor.hkex_predictor.models.yoda_layer.rule.by_pos import SentenceFromSplitPara
from remarkable.predictor.hkex_predictor.models.yoda_layer.rule.root import ScoreDestFilter
from remarkable.predictor.hkex_predictor.schemas.pattern import R_NOT, R_NUMBER, R_SAVE_AS, reg_words
from remarkable.predictor.models.base_model import BaseModel
from remarkable.predictor.schema_answer import ParagraphResult, PredictorResult, TableResult

R_BUY = r"((re)?purchas(e|ing|ed)|(buy|bought)(-|\s)?backs?)"
P_BUY = MatchMulti.compile(r"((re)?purchas(e|ing|ed)|(buy|bought)(-|\s)?backs?)", operator=any)
R_MANDATE = rf"(propos(ed?|al)?\s*(share )?{R_BUY}|{R_BUY}\s*(mandate|propos(ed?|al)))"
R_SHARE_PRICE = r"((ordinary|market)?(\s*share)?\s*prices?|(market)?\s*prices?\s*of(\s*[AHB])?\s*shares)"
R_MATERIAL_ADVERSE_IMPACT = r"material\s*(adverse)?\s*(impact|effect|disadvantages)"
P_M10_NS = PositionPattern.compile(
    r"(\bno\b|none\s*of|not been notified by any)",
    r"(core\s*)?connected person",
    rf"intention\s*{reg_words(0, 2)}sell",
)

P_M9_PS = MatchMulti.compile(
    rf"{R_BUY}.*{R_NUMBER}\s*([a-z]\s)?(share|securit)", rf"(six|6)\s*{reg_words(0, 2)}months", operator=all
)

P_APPENDIX_TITLES = re.compile(r"^APPENDIX\s*((?P<dst>[A-Z]|[IVX]{1,4}|\d+)\s.+$)")


def extract_appendix_num(element: Index):
    if element.data.get("class") not in ("PARAGRAPH", "FOOTNOTE"):
        return None
    clean_text = clean_txt(element.data["text"])
    matcher = P_APPENDIX_TITLES.search(clean_text)
    value = matcher.groupdict().get("dst", None)
    return value


def add_detail_table(answers: list[PredictorResult], **kwargs):
    # 答案是段落且枚举值是ps时，往下找回购相关的表格
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7081
    flat_answers = BaseModel.get_common_predictor_results(answers)
    if not flat_answers:
        return answers
    element_result = flat_answers[0].element_results[0]
    if not (isinstance(element_result, ParagraphResult) and flat_answers[0].answer_value == AnswerValueEnum.PS.value):
        return answers
    para_element = element_result.element
    pdfinsight = kwargs.get("predictor").pdfinsight

    page_first_element = pdfinsight.page_element_dict.get(para_element["page"])[0]
    answer_appendix_num = extract_appendix_num(page_first_element)
    if not answer_appendix_num:
        return answers
    appendix_num_map = {para_element["page"]: answer_appendix_num}
    element_results = [element_result]
    for index in range(para_element["index"], para_element["index"] + 20):
        try:
            _, ele = pdfinsight.find_element_by_index(index)
        except IndexError:
            break
        if not ele or not is_aim_element(ele, ["TABLE"]) or pdfinsight.is_skip_element(ele):
            continue
        current_appendix_num = appendix_num_map.get(ele["page"])
        if not current_appendix_num:
            current_page_first_element = pdfinsight.page_element_dict.get(ele["page"])[0]
            current_appendix_num = extract_appendix_num(current_page_first_element)
            appendix_num_map[ele["page"]] = current_appendix_num
        if answer_appendix_num != current_appendix_num:
            break
        table = parse_table(ele, tabletype=TableType.TUPLE, pdfinsight_reader=pdfinsight)
        if any(P_BUY.search(clean_txt(cell.text)) for cell in table.header):
            table_element_result = TableResult(ele, parsed_table=table)
            element_results.append(table_element_result)

    answer = PredictorResult(
        element_results,
        schema=kwargs["predictor"].schema,
        value=AnswerValueEnum.PS.value,
    )

    return [answer]


predictor_options = [
    {
        "path": ["M2-total number and description of the shares which the issuer proposes to purchase", "Content"],
        "models": [
            # {
            #     "name": "poll_result_common",
            #     "enum": AnswerValueEnum.PS.value,
            #     "pattern": [
            #         MatchMulti.compile(
            #             r"subject\s*to\s*(the\s*)?pass",
            #             MatchMulti.compile("repurchase", "buy-back", operator=any),
            #             operator=all,
            #         ),
            #     ],
            # },
            {
                "name": "para_match",
                "enum": AnswerValueEnum.PS.value,
                "paragraph_pattern": MatchMulti.compile(
                    R_BUY,
                    rf"{R_NUMBER}\s*{reg_words(0, 2)}shares",
                    operator=all,
                ),
            },
        ],
    },
    {
        "path": ["M3-reasons for the proposed purchase of shares", "Content"],
        "models": [
            {
                "name": "syllabus_based",
                "enum": AnswerValueEnum.PS.value,
                "include_title": True,
                "inject_syllabus_features": [rf"__regex__reasons?\s*{reg_words(0, 3)}(for|of).*{R_BUY}"],
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": [
                        NeglectPattern.compile(
                            match=MatchMulti.compile(
                                r"The\s*(Directors|Board)\s*(believe|consider)",
                                r"Although\s*the\s*Directors.*they\s*believe",
                                r"\b(intend\s*to|in\s*order\s*to)",
                                operator=any,
                            ),
                            unmatch=MatchMulti.compile(
                                PositionPattern.compile(
                                    r"The\s*(Directors|Board)\s*(believe|consider)",
                                    r"Repurchase Mandate was to be exercised in full",
                                    r"material adverse impact on",
                                ),
                                operator=any,
                            ),
                        )
                    ],
                },
            },
        ],
    },
    {
        "path": ["M4-source of funds for making the proposed purchase", "Content"],
        "models": [
            {
                "name": "syllabus_based",
                "enum": AnswerValueEnum.PS.value,
                "include_title": True,
                "inject_syllabus_features": [
                    rf"__regex__funding\s*{reg_words(0, 2)}(of|for)\s*(shares?)?\s*{R_BUY}",
                    r"__regex__source\s*of\s*fund",
                ],
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": [
                        r"\blegal",
                        MatchMulti.compile(R_BUY, "financed", operator=all),
                    ],
                },
            },
        ],
    },
    {
        "path": ["M5-material adverse impact on the working capital or gearing position", "Content"],
        "models": [
            # NS 回购不会产生重大不利影响
            {
                "name": "para_match",
                "enum": AnswerValueEnum.NS.value,
                "paragraph_pattern": MatchMulti.compile(
                    MatchMulti.compile(
                        rf"not\s*(expected\s*to)?\s*(have|be|expect|cause)\s*(a|any)?\s*(potential)?\s*{R_MATERIAL_ADVERSE_IMPACT}",
                        rf"have\s*no\s*{R_MATERIAL_ADVERSE_IMPACT}",
                        operator=any,
                    ),
                    R_BUY,
                    operator=all,
                ),
            },
            # PS 回购可能产生重大不利影响
            {
                "name": "para_match",
                "enum": AnswerValueEnum.PS.value,
                "paragraph_pattern": MatchMulti.compile(R_MATERIAL_ADVERSE_IMPACT, R_BUY, operator=all),
            },
        ],
    },
    {
        "path": [
            "M6-close associates of the directors with a present intention to sell shares to the issuer",
            "Content",
        ],
        "models": [
            {
                "name": "para_match",
                "enum": AnswerValueEnum.NS.value,
                "paragraph_pattern": MatchMulti.compile(
                    rf"{R_NOT}\s*{reg_words(0, 2)}director",
                    r"associate|core\s*connected\s*person",
                    r"\bsell(s|ing)?\b",
                    operator=all,
                ),
            },
        ],
    },
    {
        "path": ["M7-directors will exercise the power to make purchases in accordance with rules and laws", "Content"],
        "models": [
            {
                "name": "para_match",
                "enum": AnswerValueEnum.PS.value,
                "paragraph_pattern": [
                    MatchMulti.compile(
                        MatchMulti.compile(r"\b(laws?|act(s|\.))\b", r"exercise.*?power", operator=any),
                        R_BUY,
                        operator=all,
                    )
                ],
            },
        ],
    },
    {
        "path": ["M8-consequences of any purchases under the Takeovers Code", "Content"],
        "models": [
            {
                "name": "syllabus_elt_v2",
                "enum": AnswerValueEnum.PS.value,
                "ignore_pattern": [
                    r"^APPENDIX",
                ],
                "include_title": True,
                "include_shape": True,
                "inject_syllabus_features": [r"__regex__(takeovers?\s*code|codes?\s*on\s*takeovers?)"],
            },
            {
                "name": "multi_models",
                "operator": "union",
                "enum": AnswerValueEnum.PS.value,
                "require_unique_element": True,
                "models": [
                    {
                        "name": "para_match",
                        # "enum": AnswerValueEnum.PS.value,
                        "multi_elements": True,
                        "paragraph_pattern": [
                            MatchMulti.compile(
                                r"takeovers?\s*code",
                                operator=all,
                            ),
                        ],
                    },
                    {
                        "name": "para_match",
                        # "enum": AnswerValueEnum.PS.value,
                        "syllabus_regs": [r"GENERAL", r"DISCLOSURE OF INTERESTS AND MINIMUM PUBLIC HOLDING"],
                        "multi_elements": True,
                        "paragraph_pattern": [
                            MatchMulti.compile(
                                r"Directors.*Shareholder.*\d+%.*issued share capital.*Section 336\(1\).*Securities and Futures Ordinance",
                                r"Directors.*Repurchase Mandate.*public.*\d+%",
                                operator=any,
                            ),
                        ],
                    },
                ],
            },
        ],
    },
    {
        "path": ["M9-purchases made in the previous six months", "Content"],
        "post_process": add_detail_table,
        "models": [
            # 1 NS 前六个月内没有购买股份
            {
                "name": "para_match",
                "enum": AnswerValueEnum.NS.value,
                "paragraph_pattern": NeglectPattern.compile(
                    unmatch=MatchMulti.compile(R_SAVE_AS, P_M9_PS, operator=any),
                    match=MatchMulti.compile(
                        R_NOT,
                        R_BUY,
                        r"share|securit",
                        rf"((six|6|twelve|12)\s*{reg_words(0, 2)}months|listing\s*date)",
                        operator=all,
                    ),
                ),
            },
            # 2
            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7080#note_717774
            {
                "name": "para_match",
                "enum": AnswerValueEnum.NS.value,
                "syllabus_regs": [rf"SHARES?\s*{R_BUY}.*BY\s*THE\s*COMPANY$"],
                "paragraph_pattern": NeglectPattern.compile(
                    unmatch=MatchMulti.compile(R_SAVE_AS, P_M9_PS, operator=any),
                    match=MatchMulti.compile(
                        R_NOT,
                        rf"{R_BUY}|\b(buy|bought)\b",
                        r"share|securit",
                        rf"((six|6|twelve|12)\s*{reg_words(0, 2)}months|listing\s*date)",
                        operator=all,
                    ),
                ),
            },
            # 3
            {
                "name": "agm_m9",
                "enum": AnswerValueEnum.NS.value,
                "paragraph_pattern": MatchMulti.compile(
                    R_NOT,
                    R_BUY,
                    r"share|securit",
                    r"(since|during).*?\bto.*Latest\s*Practicable\s*Date",
                    operator=all,
                ),
            },
            # 4 PS 前六个月内购买股份的详情
            {
                "name": "para_match",
                "enum": AnswerValueEnum.PS.value,
                "syllabus_regs": [rf"SHARES?\s*{R_BUY}.*BY\s*THE\s*COMPANY$"],
                "paragraph_pattern": P_M9_PS,
            },
            # 5
            {
                "name": "special_cells",
                "enum": AnswerValueEnum.PS.value,
                "threshold": 0.2,
                "whole_table": True,
                "title_patterns": P_M9_PS,
                "col_header_pattern": [R_BUY],
            },
        ],
    },
    {
        "path": [
            "M10-whether core connected persons of the issuer have notified their intention to sell shares to the issuer",
            "Content",
        ],
        "models": [
            # 1 NS 没有核心关联人士计划售出股份
            # not been notified by any of its core connected | No core connected person of the Company has notified the Company
            {
                "name": "yoda_layer",
                "threshold": 0.1,
                "enum": AnswerValueEnum.NS.value,
                "rule": ScoreDestFilter(
                    multi=True,
                    dest=SentenceFromSplitPara(
                        multi=True,
                        separator=P_PERIOD_SEPARATOR,
                        ordered_patterns=[
                            P_M10_NS,
                        ],
                        # skip_pattern=P_NS_SKIP,
                    ),
                ),
            },
            {
                "name": "para_match",
                "enum": AnswerValueEnum.NS.value,
                "paragraph_pattern": P_M10_NS,
            },
        ],
    },
    {
        "path": ["M11-highest and lowest prices of shares during the previous twelve months", "Content"],
        "models": [
            {
                "name": "syllabus_elt_v2",
                "enum": AnswerValueEnum.PS.value,
                "include_title": True,
                "include_shape": True,
                "inject_syllabus_features": [
                    rf"__regex__{R_CHAPTER_PREFIX}{R_SHARE_PRICE}",
                ],
            },
        ],
    },
    {
        "path": ["M12-unusual features", "Content"],
        "models": [
            # 1. NS 解释性声明和回购授权没有异常
            {
                "name": "yoda_layer",
                "threshold": 0.001,
                "enum": AnswerValueEnum.NS.value,
                "rule": ScoreDestFilter(
                    multi=True,
                    dest=SentenceFromSplitPara(
                        multi=True,
                        separator=P_PERIOD_SEPARATOR,
                        ordered_patterns=[
                            MatchMulti.compile(
                                MatchMulti.compile(
                                    r"neither.*nor",
                                    r"explanatory\s*statement",
                                    R_MANDATE,
                                    r"(unusual|unusal)\s*feature|unusualf\s*eature",
                                    operator=all,
                                ),
                                MatchMulti.compile(
                                    r"explanatory\s*statement",
                                    R_MANDATE,
                                    r"nothing (unusual|unusal)\b",
                                    operator=all,
                                ),
                                operator=any,
                            )
                        ],
                    ),
                ),
            },
        ],
    },
    {
        "path": [
            "M13-whether issuer intends to cancel the repurchased shares or hold them as treasury shares",
            "Content",
        ],
        "models": [
            {
                "name": "yoda_layer",
                "threshold": 0.1,
                "enum": AnswerValueEnum.NS.value,
                "rule": ScoreDestFilter(
                    multi=True,
                    dest=SentenceFromSplitPara(
                        multi=True,
                        separator=P_PERIOD_SEPARATOR,
                        ordered_patterns=[
                            PositionPattern.compile(
                                rf"{R_NOT}.*intention",
                                rf"{R_BUY}",
                                r"cancel|treasury\s*shares",
                            )
                        ],
                    ),
                ),
            },
            {
                "name": "yoda_layer",
                "threshold": 0.1,
                "enum": AnswerValueEnum.PS.value,
                "rule": ScoreDestFilter(
                    multi=True,
                    dest=SentenceFromSplitPara(
                        multi=True,
                        separator=P_PERIOD_SEPARATOR,
                        ordered_patterns=[
                            PositionPattern.compile(
                                rf"{R_BUY}",
                                r"cancel|treasury\s*shares",
                            )
                        ],
                    ),
                ),
            },
            {
                "name": "para_match",
                "enum": AnswerValueEnum.PS.value,
                "paragraph_pattern": [
                    NeglectPattern.compile(
                        match=MatchMulti.compile(
                            rf"{R_BUY}",
                            r"cancel|treasury\s*shares",
                            operator=all,
                        ),
                        unmatch=MatchMulti.compile(
                            # NOTE: 下面两个都是 描述假设没有发行、回购等行为下，允许回购的股份数目，只是提到了一个cancel  后面再有例子 可以总结下类似的正则
                            # https://jura6-lir.paodingai.com/#/hkex/agm-circular-checking/report-review/317557?fileId=81862&schemaId=33&rule=M13-whether%20issuer%20intends%20to%20cancel%20the%20repurchased%20shares%20or%20hold%20them%20as%20treasury%20shares&delist=0
                            MatchMulti.compile(
                                r"Subject to the passing of the resolution granting the Shares Buy-back Mandate",
                                r"no further Shares are issued or bought back or cancelled",
                                r"the Company will be allowed under the Shares Buy-back Mandate",
                                operator=all,
                            ),
                            # https://jura6-lir.paodingai.com/#/hkex/agm-circular-checking/report-review/324241?fileId=88546&schemaId=33&rule=M13-whether%20issuer%20intends%20to%20cancel%20the%20repurchased%20shares%20or%20hold%20them%20as%20treasury%20shares
                            MatchMulti.compile(
                                r"Subject to the passing of the proposed ordinary resolution approving the grant of the Repurchase Mandate",
                                r"no further Shares are issued or repurchased and canceled",
                                r"the Company would be allowed to repurchase",
                                operator=all,
                            ),
                            r"corporate\s*bond",
                            operator=any,
                        ),
                    ),
                ],
            },
        ],
    },
]
