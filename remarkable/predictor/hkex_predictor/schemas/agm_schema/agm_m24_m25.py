from remarkable.common.common_pattern import R_CHAPTER_PREFIX
from remarkable.common.constants import Answer<PERSON><PERSON>ue<PERSON><PERSON>
from remarkable.common.pattern import <PERSON><PERSON>ult<PERSON>, NeglectPattern, PositionPattern
from remarkable.predictor.hkex_predictor.schemas.cg_schema.cg_rc import R_APPROVE

R_AMEND = r"\bamend"
R_ARTICLE = r"(bye?.?law|articles?\s*of\s*association|constitutional\s*document)"
R_CHAPTER_THAT = rf"{R_CHAPTER_PREFIX}\s*[‘‘“]*THAT\s*[:：]$"


# def add_special_elements(elements, predictor, **kwargs):
#     import re
#
#     from remarkable.common.util import clean_txt
#     from remarkable.pdfinsight.parser import parse_table
#     from remarkable.pdfinsight.reader import PdfinsightReader
#
#     rets = []
#     p_special_syllabus = MatchMulti.compile(r"MATTERS TO BE CONSIDERED", operator=any)
#     p_resolution = re.compile(r"Resolution\s(?P<dst>\d+).*Resolution on the Amendments to the Articles of Association")
#     p_amendments = re.compile(r"Resolution\s(?P<dst>\d+).*Resolution on the Amendments to the Articles of Association")
#     pdfinsight: PdfinsightReader = predictor.pdfinsight
#     syllabuses = pdfinsight.find_sylls_by_pattern(p_special_syllabus)
#     for syllabus in syllabuses:
#         start, end = syllabus["range"]
#         elements_map = {}
#         for idx in range(start, end + 1):
#             _, element = pdfinsight.find_element_by_index(idx)
#             if not element:
#                 continue
#             elements_map[idx] = element
#             if not is_para_elt(element) or pdfinsight.is_skip_element(element):
#                 continue
#             matcher = p_resolution.search(element["text"])
#             if not matcher:
#                 continue
#             resolution_num = matcher.group("dst")
#             above_table = elements_map.get(idx - 1)
#             if not above_table:
#                 return rets
#             parsed_table = parse_table(element, tabletype=TableType.TUPLE, pdfinsight_reader=pdfinsight)
#             for row in parsed_table.rows:
#                 row_text = clean_txt("".join(cell.text for cell in row))
#                 if p_resolution.search(row_text):
#                     rets.append(element)
#
#     return rets


predictor_options = [
    {
        "path": ["M24-a super-majority vote required to approve changes to constitutional documents", "Content"],
        "models": [
            # 1
            {
                # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6569#note_685638
                "name": "para_match",
                "enum": AnswerValueEnum.PS.value,
                "syllabus_regs": [R_CHAPTER_THAT],
                "as_follow_near_offset": -4,
                "as_follow_pattern": [R_CHAPTER_THAT],
                "any_below_pattern": MatchMulti.compile(R_APPROVE, R_AMEND, R_ARTICLE, operator=all),
            },
            # 2
            {
                "name": "para_match",
                "enum": AnswerValueEnum.PS.value,
                "syllabus_regs": [r"^SPECIAL\s*RESOLUTION", r"^NOTICE\s*OF\s*.*(ANNUAL\s*GENERAL\s*MEETING|\bAGM\b)$"],
                "paragraph_pattern": MatchMulti.compile(R_APPROVE, R_AMEND, R_ARTICLE, operator=all),
                "as_follow_pattern": MatchMulti.compile(
                    R_APPROVE, R_AMEND, R_ARTICLE, r"resolution.*?[:：]", operator=any
                ),
                "any_below_pattern": MatchMulti.compile(R_AMEND, R_ARTICLE, operator=all),
            },
            # 3
            {
                "name": "para_match",
                "enum": AnswerValueEnum.PS.value,
                "syllabus_regs": [r"^SPECIAL\s*RESOLUTION", R_CHAPTER_THAT],
                "paragraph_pattern": MatchMulti.compile(R_APPROVE, R_AMEND, R_ARTICLE, operator=all),
            },
            # 4 https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7046#note_717262
            {
                "name": "syllabus_based",
                "enum": AnswerValueEnum.PS.value,
                "multi": True,
                "inject_syllabus_features": [
                    r"__regex__^resolution\s\d+.*Proposed Amendments to the Articles of Association",
                    r"__regex__^MATTERS TO BE CONSIDERED$",
                ],
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": MatchMulti.compile(
                        "special resolution", R_APPROVE, R_AMEND, R_ARTICLE, operator=all
                    ),
                },
                "table_model": "row_match",
                "table_config": {
                    "row_pattern": [
                        r"Resolution\s(?P<dst>\d+).*Resolution on the Amendments to the Articles of Association"
                    ],
                },
            },
            # 5 https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7046#note_717198
            #  https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7046#note_717210
            {
                "name": "syllabus_based",
                "enum": AnswerValueEnum.PS.value,
                "inject_syllabus_features": [
                    r"__regex__Amendment to the Articles of Association and Filing and Registration of Change",
                    r"__regex__Proposed Amendments to the Articles of Association",
                ],
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": MatchMulti.compile(
                        "(special|this) resolution", R_APPROVE, R_AMEND, R_ARTICLE, operator=all
                    ),
                },
                "table_model": "empty",
            },
        ],
    },
    {
        "path": ["M25-13.51(1)", "Explanation of the effect"],
        "models": [
            {
                "name": "syllabus_based",
                "enum": AnswerValueEnum.PS.value,
                "inject_syllabus_features": [
                    r"__regex__^ADOPTION\s*OF\s*(THE\s*)?NEW\s*BYE?.?LAWS\s*$",
                    rf"__regex__PROPOSED.*AMEND(MENTS?|ED).*{R_ARTICLE}",
                ],
                "paragraph_model": "para_match",
                "para_config": {
                    "multi_elements": True,
                    "paragraph_pattern": MatchMulti.compile(
                        r"(consider|proposes?)\b", R_AMEND, R_ARTICLE, operator=all
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "para_match",
                "enum": AnswerValueEnum.PS.value,
                "syllabus_regs": MatchMulti.compile(
                    r"LETTER\s*FROM\s*THE\s*BOARD",
                    r"ADOPTION\s*OF\s*NEW\s*BYE?.?LAWS\s*$",
                    MatchMulti.compile(R_ARTICLE, R_AMEND, operator=all),
                    r"PROPOSED ADOPTION OF AMENDED AND RESTATED M&A",
                    operator=any,
                ),
                "paragraph_pattern": NeglectPattern.compile(
                    match=MatchMulti.compile(r"consider|propose", R_AMEND, R_ARTICLE, operator=all),
                    unmatch=MatchMulti.compile(
                        PositionPattern.compile(
                            r"authorise the Board and approve.*increase in registered capital",
                            r"without holding board meetings to (consider|propose) and approve",
                        ),
                        MatchMulti.compile(
                            r"consider|propose",
                            R_AMEND,
                            R_ARTICLE,
                            "full text of which is set out in Appendix.*to this circular",
                            operator=all,
                        ),
                        operator=any,
                    ),
                ),
            },
        ],
    },
    {
        "path": ["M25-13.51(1)", "Full terms of the proposed amendments"],
        "models": [
            {
                "name": "syllabus_elt_v2",
                "enum": AnswerValueEnum.PS.value,
                "multi": True,
                "include_shape": True,
                "break_para_pattern": [r"^NOTICE\s*OF\s*ANNUAL\s*GENERAL\s*MEETING$"],
                "inject_syllabus_features": [
                    r"__regex__APPENDIX.*?PROPOSED\s*AMENDMENTS?\s*TO$",
                    rf"__regex__APPENDIX.*?PROPOSED\s*AMENDMENTS?\s*TO.*{R_ARTICLE}",
                    rf"__regex__APPENDIX.*(PARTICULAR|DETAIL|RESOLUTION).*AMENDMENTS?\s*TO.*{R_ARTICLE}",
                    r"__regex__APPENDIX.*?NEW\s*BYE-LAWS",
                ],
            },
            {
                "name": "special_cells",
                "enum": AnswerValueEnum.PS.value,
                "multi_elements": True,
                "whole_table": True,
                "title_patterns": MatchMulti.compile(R_APPROVE, R_AMEND, R_ARTICLE, operator=any),
                "syllabus_regs": [
                    r"LETTER\s*FROM\s*THE\s*BOARD",
                ],
                "col_header_pattern": [r"\bamend(ed)?\s*to", rf"(before|after).*{R_AMEND}"],
            },
            {
                "name": "agm_m25",
                "enum": AnswerValueEnum.PS.value,
                "include_shape": True,
                "include_title": True,
                "break_para_pattern": [r"^NOTICE\s*OF\s*ANNUAL\s*GENERAL\s*MEETING$"],
            },
        ],
    },
]
