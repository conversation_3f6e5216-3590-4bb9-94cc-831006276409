from copy import deepcopy

from remarkable.common.common_pattern import P_PERIOD_SEPARATOR, P_SEN_SEPARATOR
from remarkable.common.constants import AnswerValueEnum, TableType
from remarkable.common.pattern import MatchMulti, NeglectPattern, PositionPattern
from remarkable.common.util import split_paragraph
from remarkable.pdfinsight.parser import parse_table
from remarkable.predictor.default_predictor.utils import is_table_result
from remarkable.predictor.hkex_predictor.models.yoda_layer.rule.root import ScoreLensFilter
from remarkable.predictor.hkex_predictor.schemas.pattern import R_NOT
from remarkable.predictor.models.base_model import BaseModel
from remarkable.predictor.schema_answer import (
    CharR<PERSON>ult,
    OutlineResult,
    ParagraphResult,
    PredictorResult,
    TableCellsResult,
)

P_TOTAL_SHARES = MatchMulti.compile(
    r"total number of.*?shares?", r"entitling.*?holders?", r"\bto attend and vote", operator=all
)
P_NUM_PERCENT = MatchMulti.compile(r"\d+(\.\d+)?[%％‰‱]", operator=any)
P6_SUB2_PATTERN = MatchMulti.compile(
    rf"{R_NOT}",
    MatchMulti.compile(
        r"\b(intends|intention|intended)\b", r"\bvote against\b", r"\babstain from voting", operator=all
    ),
    operator=all,
)


def p4_post_process(answers, **kwargs):
    answers = BaseModel.get_common_predictor_results(answers)
    pdfinsight = kwargs["predictor"].pdfinsight

    for answer in answers:
        new_element_results = []
        if answer.answer_value == AnswerValueEnum.PS.value:
            for element_result in answer.element_results:
                if is_table_result(element_result):
                    target_col = {cell.colidx for cell in element_result.parsed_cells}.pop()
                    element = element_result.element
                    table = element_result.parsed_table
                    next_elements = pdfinsight.continuous_tables(element, only_after=True)[1:]
                    for table_element in next_elements:
                        next_table = parse_table(table_element, tabletype=TableType.TUPLE, pdfinsight_reader=pdfinsight)
                        if table.element["page"] + 1 == next_table.element["page"] and len(table.cols) == len(
                            next_table.cols
                        ):
                            cells = [cell for row in next_table.rows for cell in row if cell.colidx == target_col]
                            if all(P_NUM_PERCENT.search(cell.text) for cell in cells):
                                new_element_results.append(TableCellsResult(next_elements[0], cells))
            answer.element_results.extend(new_element_results)
    return answers


def split_p6_answers(answers: list[PredictorResult], **kwargs):
    predictor = kwargs.get("predictor")
    if not answers or not predictor:
        return answers
    common_answers = BaseModel.get_common_predictor_results(answers)

    for answer in common_answers:
        new_element_results = []
        for ele_res in answer.element_results:
            if isinstance(ele_res, (ParagraphResult, CharResult)):
                new_ele_res = deepcopy(ele_res)
                short_sentence = get_short_sentence(P6_SUB2_PATTERN, ele_res.text)
                if short_sentence:
                    new_ele_res.chars = new_ele_res.chars[short_sentence[0] : short_sentence[1]]
                new_element_results.append(new_ele_res)
            if isinstance(ele_res, OutlineResult):
                for box in ele_res.page_box:
                    for element in box["elements"]:
                        short_sentence = get_short_sentence(P6_SUB2_PATTERN, element["text"])
                        if short_sentence:
                            new_element_results.append(
                                CharResult(element, element["chars"][short_sentence[0] : short_sentence[1]])
                            )
                        else:
                            new_element_results.append(CharResult(element, element["chars"]))

        answer.element_results = new_element_results
    return answers


def get_short_sentence(sen_pattern, text):
    for sentence, (sen_start, _sen_end_pos) in split_paragraph(text, need_pos=True, separator=P_SEN_SEPARATOR):
        if sen_pattern.search(sentence):
            return sen_start, _sen_end_pos
    return None, None


predictor_options = [
    {
        "path": ["P1-shares to attend and vote"],
        "element_candidate_count": 5,
        "models": [
            {
                "name": "poll_result_common",
                "enum": AnswerValueEnum.NS.value,
            },
            {
                "name": "poll_result_common",
                "neglect_pattern": [
                    PositionPattern.compile(
                        r"\bno Share", r"\bentitling (the|any) (share)?holders? to attend", r"\babstain from voting"
                    ),
                ],
                "enum": AnswerValueEnum.PS.value,
            },
            {
                "name": "para_match",
                "multi_elements": True,
                "para_separator": P_PERIOD_SEPARATOR,
                "paragraph_pattern": [P_TOTAL_SHARES],
                "enum": AnswerValueEnum.PS.value,
            },
            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6915#note_709003
            {
                "name": "near_paras",
                "force_use_all_elements": True,
                "multi_elements": True,
                "start_regs": [r"attendance of the shareholder"],
                "near_amount": 5,
                "para_separator": P_PERIOD_SEPARATOR,
                "paragraph_pattern": MatchMulti.compile(
                    r"voted at (the )?meeting|voted online", r"total share", operator=all
                ),
                "enum": AnswerValueEnum.PS.value,
            },
            {
                # 有且只有一个分数大于0.7
                "name": "yoda_layer",
                "threshold": 0.7,
                "rule": ScoreLensFilter(length=1),
                "enum": AnswerValueEnum.PS.value,
            },
        ],
    },
    {
        "path": ["P4-shares voted for"],
        "element_candidate_count": 5,
        "post_process": p4_post_process,
        "models": [
            {
                "name": "special_cells",
                "multi": False,
                "force_use_all_elements": True,
                "need_continuous": False,
                "multi_elements": True,
                "col_header_pattern": [
                    NeglectPattern.compile(
                        match=MatchMulti.compile(
                            r"^For$",
                            r"vote.*For",
                            r"For.*vote",
                            r"For\s(Number|No\.)",
                            r"(Number|No\.)\sFor",
                            MatchMulti.compile(r"Number of shares?", r"\bFor\b", operator=all),
                            operator=any,
                        ),
                        unmatch=MatchMulti.compile(r"total number.*votes for and against", operator=any),
                    ),
                ],
                "enum": AnswerValueEnum.PS.value,
            },
            {
                "name": "special_cells",
                "multi": False,
                "need_continuous": False,
                "multi_elements": True,
                "col_header_pattern": [
                    NeglectPattern.compile(
                        match=MatchMulti.compile(r"FOR", operator=any, flag=0),
                        unmatch=MatchMulti.compile(r"total number.*votes for and against", operator=any),
                    ),
                ],
                "enum": AnswerValueEnum.PS.value,
            },
        ],
    },
    {
        "path": ["P5-shares voted against"],
        "element_candidate_count": 5,
        "post_process": p4_post_process,
        "models": [
            {
                "name": "special_cells",
                "multi": False,
                "force_use_all_elements": True,
                "need_continuous": False,
                "multi_elements": True,
                "col_header_pattern": [
                    NeglectPattern.compile(
                        match=MatchMulti.compile(
                            r"^Against$",
                            r"vote.*Against",
                            r"Against.*vote",
                            r"Against\s(Number|No\.)",
                            r"(Number|No\.)\sAgainst",
                            PositionPattern.compile(r"Number of shares?", r"\bAgainst\b"),
                            operator=any,
                        ),
                        unmatch=MatchMulti.compile(r"total number.*votes for and against", operator=any),
                    ),
                ],
                "enum": AnswerValueEnum.PS.value,
            },
        ],
    },
    {
        "path": ["P6-13.39(5)", "Scrutineer"],
        "element_candidate_count": 5,
        "models": [
            {
                "name": "poll_result_common",
                "enum": AnswerValueEnum.NS.value,
            },
            {
                "name": "para_match",
                "para_separator": P_PERIOD_SEPARATOR,
                "paragraph_pattern": [
                    # r"\b(appoint|act).*?as the scrutineers?\b",
                    r"\bas.*?scrutineers?\b",
                ],
                "enum": AnswerValueEnum.PS.value,
            },
            {
                # 有且只有一个分数大于0.7
                "name": "yoda_layer",
                "threshold": 0.7,
                "rule": ScoreLensFilter(length=1),
                "enum": AnswerValueEnum.PS.value,
            },
        ],
    },
    {
        "path": ["P6-13.39(5)", "Voting intention"],
        "element_candidate_count": 5,
        "post_process": split_p6_answers,
        "models": [
            {
                "name": "poll_result_common",
                "enum": AnswerValueEnum.NS.value,
                "pattern": [
                    MatchMulti.compile(
                        PositionPattern.compile(rf"{R_NOT}", r"part(y|ies)|shareholder"),
                        r"\b(intends|intention|intended)\b",
                        MatchMulti.compile(
                            r"vote against.*?or (to )?abstain from",
                            r"vote (against|abstain) from",
                            operator=any,
                        ),
                        operator=all,
                    ),
                ],
                "neglect_pattern": [
                    NeglectPattern.compile(
                        match=MatchMulti.compile(".*", operator=any),
                        unmatch=MatchMulti.compile(r"\b(intends|intention|intended)\b", operator=any),
                    ),
                ],
            },
            {
                "name": "para_match",
                "para_separator": P_PERIOD_SEPARATOR,
                "paragraph_pattern": [
                    NeglectPattern.compile(
                        match=MatchMulti.compile(
                            r"\bintention\b", r"\bvote against\b", r"\babstain from voting", operator=all
                        ),
                        unmatch=rf"{R_NOT}",
                    ),
                ],
                "enum": AnswerValueEnum.PS.value,
            },
            {
                # 有且只有一个分数大于0.7
                "name": "yoda_layer",
                "threshold": 0.7,
                "rule": ScoreLensFilter(length=1),
                "enum": AnswerValueEnum.NS.value,
            },
        ],
    },
]
