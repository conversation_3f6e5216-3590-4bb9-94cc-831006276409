from remarkable.common.common_pattern import P_SEN_SEPARATOR, R_MIDDLE_DASH
from remarkable.common.constants import AnswerValueEnum
from remarkable.common.pattern import <PERSON><PERSON><PERSON><PERSON>, NeglectPattern
from remarkable.predictor.hkex_predictor.models.yoda_layer.rule.root import ScoreLensFilter

R_AGM = r"(\bAGM\b|Annual\sGeneral\sMeeting)"

predictor_options = [
    {
        "path": ["P7-directors' attendance at the general meeting"],
        "element_candidate_count": 5,
        "models": [
            {
                "name": "poll_result_common",
                "enum": AnswerValueEnum.NS.value,
            },
            {
                "name": "middle_paras",
                "include_top_anchor": True,
                "use_syllabus_model": False,
                "use_top_crude_neighbor": False,
                "top_anchor_regs": [
                    rf"attendance\s+record.*directors.*{R_AGM}.*follows?:$",
                ],
                "bottom_anchor_regs": [
                    r"as\s+follows:$",
                    r"By\s+Order\s+of\s+the\s+Board",
                    rf"{R_MIDDLE_DASH}\d{R_MIDDLE_DASH}",
                ],
                "allow_element_class": ["PARAGRAPH", "TABLE", "FOOTNOTE", "PAGE_FOOTER", "PAGE_HEADER"],
                "enum": AnswerValueEnum.PS.value,
            },
            {
                "name": "para_match",
                "enum": AnswerValueEnum.PS.value,
                "as_follow_pattern": [
                    rf"attendance\s*record.*directors.*{R_AGM}.*[:：]$",
                ],
                "any_below_pattern": [rf"attend.*{R_AGM}"],
                "need_default_follows": True,
                "as_follow_near_offset": -5,
            },
            {
                "name": "poll_result_common",
                "pattern": NeglectPattern.compile(
                    match=MatchMulti.compile(r"\battend", r"present(s|ing|ed)? at", operator=any),
                    unmatch=rf"^ATTENDANCE\s*AT\s*(THE\s*)?{R_AGM}$",
                ),
                "enum": AnswerValueEnum.PS.value,
            },
            {
                "name": "para_match",
                "enum": AnswerValueEnum.PS.value,
                "as_follow_pattern": [r"attendance record.*directors.*(AGM|Annual General Meeting ).*follows:$"],
                "below_need_continuous": True,
                "need_default_follows": True,
                "as_follow_near_offset": -5,
            },
            {
                # 有且只有一个分数大于0.7
                "name": "yoda_layer",
                "threshold": 0.7,
                "rule": ScoreLensFilter(length=1),
                "enum": AnswerValueEnum.PS.value,
            },
            {
                "name": "para_match",
                "para_separator": P_SEN_SEPARATOR,
                "paragraph_pattern": [
                    r"all.*?directors.*?(attended|present at) the (AGM|Meetings?)",
                    r"(?P<content>Directors.*attended the AGM by.*?\.)",
                    r"(?P<content>The following directors attended the (annual general meeting|AGM).*?:.*\.)",
                    r"(?P<content>The Directors that attended the AGM (has|had|have|did|was|were|are).*\.)",
                ],
                "enum": AnswerValueEnum.PS.value,
            },
        ],
    },
]
