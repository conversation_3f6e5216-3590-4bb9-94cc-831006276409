from remarkable.common.common_pattern import P_PERIOD_SEPARATOR, P_SEN_SEPARATOR
from remarkable.common.constants import AnswerValueEnum
from remarkable.common.pattern import MatchMulti, NeglectPattern, PatternCollection, PositionPattern
from remarkable.common.util import split_paragraph
from remarkable.predictor.hkex_predictor.models.yoda_layer.rule.root import ScoreLensFilter
from remarkable.predictor.hkex_predictor.schemas.pattern import R_NOT
from remarkable.predictor.models.base_model import BaseModel
from remarkable.predictor.schema_answer import Char<PERSON><PERSON><PERSON>, OutlineResult, ParagraphResult, PredictorResult

P_P3_SEN = MatchMulti.compile(
    PositionPattern.compile(
        r"\bno (other )?((Share)?holders?|Share the holders?)", r"\brequired\b", r"\bto abstain from voting"
    ),
    PositionPattern.compile(r"\bno Shares requiring", r"to abstain from voting"),
    PositionPattern.compile(
        r"\bno restrictions?",
        r"\bshareholders?\b",
        r"\bcast(s|ed|ing)? votes?",
        r"\bresolutions.*?at the (Annual General Meeting|AGM)",
    ),
    operator=any,
)
P_P2_SEN = MatchMulti.compile(
    PositionPattern.compile(
        r"\bno Share", r"\bentitling (the|any) (share)?holders? to attend", r"\babstain from voting"
    ),
    PositionPattern.compile(
        r"\bno (other )?shareholders?", r"\b(entitled|eligible) to", r"\battend", r"\babstain from voting"
    ),
    PositionPattern.compile(
        r"\bno restrictions?",
        r"\bshareholders?\b",
        r"\bcast(s|ed|ing)? votes?",
        r"\bresolutions.*?at the (Annual General Meeting|AGM)",
    ),
    operator=any,
)
P_NOT_MATCHED = MatchMulti.compile(r"\bno Shareholders? who had any material interests in the matters?", operator=any)
R_AND_OR = PatternCollection(patterns=[r"\band|or\b", r"(and|[;；])\s*[(（]\d+[）)]\s*"])
R_BE = r"\b(was|were|is|are)\b"
R_NONE = r"\b(No|none of)\b"
P_ABSTAIN = NeglectPattern.compile(
    match=PositionPattern.compile(r"\brequired? to\b", r"abstain from voting"), unmatch=rf"{R_NOT}"
)


def split_p2_p3_answers(answers: list[PredictorResult], **kwargs):
    """
    1.使用and|or将句子切开
    2.使用正则匹配p2的句式特征，精准提取p2
    3.使用正则匹配p3的句式特征，精准提取p3
    4.判断当前schema是p2还是p3
    5.p2 schema使用步骤2的答案
    6.p3 schema使用步骤3的答案
    """
    predictor = kwargs.get("predictor")
    shema_name = p2_or_p3(predictor.schema)
    if not shema_name:
        return answers
    P_SEN = P_P3_SEN if shema_name == "P3" else P_P2_SEN

    if not answers or not predictor:
        return answers
    common_answers = BaseModel.get_common_predictor_results(answers)
    for answer in [answer for answer in common_answers if answer.answer_value == AnswerValueEnum.NS.value]:
        found_above = False
        for element_result in answer.element_results:
            if isinstance(element_result, (ParagraphResult, CharResult)) and element_result.text.startswith(
                "Save as disclosed"
            ):
                above_paras = kwargs["predictor"].pdfinsight.find_elements_near_by(
                    element_result.element["index"],
                    amount=5,
                    step=-1,
                    aim_types="PARAGRAPH",
                )
                for para in above_paras:
                    for sentence, (start, end) in split_paragraph(para["text"], need_pos=True):
                        if P_ABSTAIN.search(sentence):
                            answer.element_results.append(ParagraphResult(para, para["chars"][start:end]))
                            found_above = True
                            break
                    if found_above:
                        break
        if found_above:
            answer.answer_value = AnswerValueEnum.PS.value
            break

    for answer in common_answers:
        new_element_results = []
        for ele_res in answer.element_results:
            if isinstance(ele_res, (ParagraphResult, CharResult)):
                # new_ele_res = deepcopy(ele_res)
                short_sentence = get_short_sentence(P_SEN, ele_res.text)
                if short_sentence:
                    new_element_results.append(
                        CharResult(ele_res.element, ele_res.chars[short_sentence[0] : short_sentence[1]])
                    )
                    # new_ele_res.chars = new_ele_res.chars[short_sentence[0] : short_sentence[1]]
                # new_element_results.append(new_ele_res)
            if isinstance(ele_res, OutlineResult):
                for box in ele_res.page_box:
                    for element in box["elements"]:
                        short_sentence = get_short_sentence(P_SEN, element["text"])
                        if short_sentence:
                            new_element_results.append(
                                CharResult(element, element["chars"][short_sentence[0] : short_sentence[1]])
                            )
                        else:
                            new_element_results.append(CharResult(element, element["chars"]))
        answer.element_results = new_element_results

    # 排除相同内容的答案元素块
    for answer in common_answers:
        exists_answer_texts = []
        for element_result in answer.element_results[:]:
            if isinstance(element_result, (ParagraphResult, CharResult)):
                if element_result.text in exists_answer_texts:
                    answer.element_results.remove(element_result)
                else:
                    exists_answer_texts.append(element_result.text)

    return answers


def get_short_sentence(senten_pattern, text):
    results = []
    sentence_results = []
    for sentence, (sen_start, _sen_end_pos) in split_paragraph(text, need_pos=True, separator=P_SEN_SEPARATOR):
        if P_NOT_MATCHED.search(sentence):
            continue
        sub_spans = []
        for matched in R_AND_OR.finditer(sentence):
            sub_spans.append(matched.span())
        for sub_span in sub_spans:
            sub_text = sentence[: sub_span[0]]
            sub_text_2 = sentence[sub_span[1] :]
            # 如果在一个句子中匹配到了所有需要的子句，那么不切分
            if senten_pattern.search(sub_text) and senten_pattern.search(sub_text_2):
                continue
            if senten_pattern.search(sub_text):
                results.append((sen_start, sen_start + sub_span[0]))
            elif senten_pattern.search(sub_text_2):
                results.append((sen_start + sub_span[1] + 1, sen_start + len(sentence)))
        if not results and senten_pattern.search(sentence):
            sentence_results.append((sen_start, sen_start + len(sentence)))
    if results:
        # 选择结果中最短的句子
        result = min(results, key=lambda x: x[1] - x[0])
        return result
    if sentence_results:
        result = min(sentence_results, key=lambda x: x[1] - x[0])
        return result


def p2_or_p3(schema):
    if "P2-shares to attend and abstain from voting" in schema.path:
        return "P2"
    elif "P3-shares of holders required to abstain from voting" in schema.path:
        return "P3"
    return None


P_ABSTAIN_FROM_VOTING = MatchMulti.compile(
    r"attend.*abstain from voting",
    r"\bno Shareholder.*?required to abstain from voting",
    r"\bno shares? (of the company )?entitling the Shareholders? to attend and vote",
    operator=any,
)

P_NO_RESTRICTION_MODEL = {
    "name": "para_match",
    "force_use_all_elements": True,
    "para_separator": P_PERIOD_SEPARATOR,
    "paragraph_pattern": PositionPattern.compile(
        r"\bno restrictions?",
        r"\bshareholders?\b",
        r"\bcast(s|ed|ing)? votes?",
        r"\bresolutions?\b",
        r"\bat the (AGM|Annual General Meeting)\b",
    ),
}


predictor_options = [
    {
        "path": ["P2-shares to attend and abstain from voting"],
        "post_process": split_p2_p3_answers,
        "element_candidate_count": 10,
        "models": [
            {
                "name": "multi_models",
                "operator": "union",
                "enum": AnswerValueEnum.NS.value,
                "deduplicate_elements": False,
                "models": [
                    {
                        "name": "para_match",
                        "para_separator": P_PERIOD_SEPARATOR,
                        "paragraph_pattern": PositionPattern.compile(
                            r"\bno (other )?shareholders?",
                            r"entitle?(s|ing|ed) to|entitling",
                            r"\battend\b",
                            r"abstain from voting",
                        ),
                    },
                    {
                        "name": "poll_result_common",
                        "pattern": [P_ABSTAIN_FROM_VOTING],
                        "neglect_pattern": [
                            NeglectPattern.compile(
                                match=MatchMulti.compile(".*", operator=any),
                                unmatch=P_ABSTAIN_FROM_VOTING,
                            ),
                        ],
                    },
                    P_NO_RESTRICTION_MODEL,
                ],
            },
            # 暂时没有ps的例子
            # {
            #     "name": "poll_result_common",
            #     "enum": AnswerValueEnum.PS.value,
            # },
            {
                # 有且只有一个分数大于0.75
                "name": "yoda_layer",
                "threshold": 0.75,
                "rule": ScoreLensFilter(length=1),
                "enum": AnswerValueEnum.NS.value,
            },
        ],
    },
    {
        "path": ["P3-shares of holders required to abstain from voting"],
        "post_process": split_p2_p3_answers,
        # todo save sa类型的ps的答案需要扩充 fileid  71273
        "location_threshold": 0.1,
        "element_candidate_count": 5,
        "models": [
            {
                "name": "multi_models",
                "operator": "union",
                "enum": AnswerValueEnum.NS.value,
                "deduplicate_elements": False,
                "models": [
                    {
                        "name": "poll_result_common",
                        "pattern": [
                            PositionPattern.compile(
                                rf"{R_NONE}",
                                r"\b(shareholder|person)s?\b",
                                r"\brequired\b",
                                r"\babstain\b",
                                r"\b(AGM|Annual General Meeting|above resolutions?)\b",
                            ),
                            # rf"{R_NONE}.*?(shareholders|person)?.*?(required|entitled).*?abstain.*(AGM|Annual General Meeting|above resolutions?)",
                            r"required the holder to abstain from voting pursuant to The Rules Governing the Listing of Securities on The Stock Exchange of Hong Kong",
                        ],
                        "neglect_pattern": [
                            # # for nd
                            NeglectPattern.compile(
                                match=MatchMulti.compile(
                                    rf"(?<!Save as disclosed above, )There {R_BE} no restriction.*Shareholder.*at the AGM\.",
                                    rf"(?<!Save as disclosed above, )There {R_BE} no restriction.*Shareholder.*at the AGM\.",
                                    operator=any,
                                ),
                                unmatch=MatchMulti.compile(
                                    r"abstain from voting",
                                    operator=any,
                                ),
                            ),
                            # for ps
                            MatchMulti.compile(
                                r"The total number of shares.*to abstain from voting at the AGM.*\d+",
                                operator=any,
                            ),
                            # for ns
                            PositionPattern.compile(
                                r"\bno (other )?shareholders?",
                                r"entitle?(s|ing|ed) to|entitling",
                                r"\battend\b",
                                r"abstain from voting",
                            ),
                        ],
                        "neglect_element_pattern": [
                            # for ps
                            MatchMulti.compile(
                                r"Except for the.*\d+.*Shares.*which is required to abstain from voting on matters",
                                operator=any,
                            ),
                        ],
                    },
                    {
                        "name": "para_match",
                        "para_separator": P_SEN_SEPARATOR,
                        "anchor_regs": MatchMulti.compile(
                            r"^As at the date of the (AGM|Annual General Meeting)[:：]$", operator=any
                        ),
                        "paragraph_pattern": PositionPattern.compile(
                            rf"{R_NONE}.*?(shareholder|person)",
                            r"\b(required|entitled)\b",
                            r"abstain from voting on",
                        ),
                    },
                    P_NO_RESTRICTION_MODEL,
                ],
            },
            # 明确的期权投票相关的描述  用于修改badcase特例
            {
                "name": "para_match",
                # "multi_elements": True,
                "paragraph_pattern": [
                    r"As the date of the AGM.*\bMr.*has abstained from voting.*at the AGM.",
                ],
                "enum": AnswerValueEnum.PS.value,
            },
            {
                "name": "poll_result_common",
                "enum": AnswerValueEnum.PS.value,
                "neglect_pattern": [
                    # for nd
                    rf"(?<!Save as disclosed above, )There {R_BE} no restriction.*Shareholder.*at the AGM",
                    rf"The total number of.*?shares.*entitling the Shareholders to attend and vote for or against the Resolutions was (\d{1, 3}(,\d{3})*|\d+)(\.\d+)? Shares",
                    rf"{R_NONE} shareholders?.*?{R_BE} subject to any restrictions?",
                ],
            },
            {
                # 有且只有一个分数大于0.7
                "name": "yoda_layer",
                "threshold": 0.7,
                "rule": ScoreLensFilter(length=1),
                # "enum": AnswerValueEnum.PS.value,
            },
        ],
    },
]
