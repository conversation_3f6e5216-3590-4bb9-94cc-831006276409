from __future__ import annotations

import re

from remarkable.common.common_pattern import P_SEN_SEPARATOR, R_FOLLOW_PREFIX, R_MIDDLE_DASH
from remarkable.common.constants import AnswerValueEnum, LRSEnum
from remarkable.common.pattern import <PERSON><PERSON><PERSON><PERSON>, Neglect<PERSON><PERSON>ern, <PERSON>BeforeMatch
from remarkable.predictor.common_pattern import R_INCENTIVE_ABBREV, R_PERCENT, R_SHARE_INCENTIVE
from remarkable.predictor.hkex_predictor.models.yoda_layer.rule.root import ScoreLensFilter
from remarkable.predictor.hkex_predictor.pattern import R_BE
from remarkable.predictor.hkex_predictor.schemas.list_rule_schema.list_rule_h83 import (
    H38_41_MODELS,
    h83_post_process_answers,
)
from remarkable.predictor.hkex_predictor.schemas.pattern import (
    P_NO_EQUITY,
    R_34_37_NEGLECT_SYLLABUS_REGS,
    R_AWARD_KEYWORD,
    R_AWARD_KEYWORD_LIST,
    R_EXPIRED_OR_ADOPTED,
    R_ND_SHARE_AWARD_SCHEME,
    R_NUMBER_SHARE,
    R_OPTION_KEYWORD,
    SHARE_AWARD_SCHEME_TEXT,
    UNDER_INCENTIVE_SCHEME,
    reg_words,
)

P_NO_SHARE = SplitBeforeMatch(
    MatchMulti.compile(
        "No Shares was granted",
        R_AWARD_KEYWORD,
        operator=all,
    ),
    separator=P_SEN_SEPARATOR,
    operator=any,
)
R_A34_KEY_WORDS = r"(objectives?|purposes?|as a means to|(to|for) recogni)"
R_PARTICIPANT = r"Participa(nt|tion|te|ting)"

R_ISSUED_SHARE = rf"issued {reg_words(0, 2)}shares?"

predictor_options = [
    {
        "path": ["A34"],
        "fake_leaf": True,
        "models": [
            {
                "name": "group_based",
                "share_type": "award",
                "group_default_enum": LRSEnum.ND.value,
                "column_configs": {
                    "Content": [
                        {
                            "name": "no_share",
                            "with_element_box": True,
                            "enum": LRSEnum.NS.value,
                        },
                        {
                            # 有且只有一个分数大于0.9
                            "name": "yoda_layer",
                            "threshold": 0.9,
                            "indices_filter": set.issuperset,
                            "rule": ScoreLensFilter(length=1),
                        },
                        {
                            "name": "syllabus_elt_v2",
                            "multi": True,
                            "inject_syllabus_features": [
                                r"__regex__purpose.*share.*(plan|scheme)",
                                r"__regex__^purpose$",
                            ],
                            "neglect_features": [
                                NeglectPattern.compile(match=R_OPTION_KEYWORD, unmatch=R_AWARD_KEYWORD),
                            ],
                        },
                        {
                            "name": "para_match",
                            "multi_elements": True,
                            # "neglect_syllabus_regs": R_BASE_NEGLECT_SYLLABUS_REGS,
                            "paragraph_pattern": ("share the success of the company.*?stimulate",),
                        },
                        {
                            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/4559#note_518143
                            "name": "follow_paras",
                            # "multi_elements": False,
                            # "neglect_syllabus_regs": R_BASE_NEGLECT_SYLLABUS_REGS,
                            "neglect_pattern": [
                                r"may contribute funds to the trust constituted by the Trust Deed",
                                r"^REPORT\s*OF\s*THE\s*DIRECTORS$",
                            ],
                            "paragraph_pattern": (
                                SplitBeforeMatch(
                                    MatchMulti.compile(
                                        R_A34_KEY_WORDS,
                                        #  Environment Share Award Scheme = ESAS
                                        rf"{R_AWARD_KEYWORD}|{R_SHARE_INCENTIVE}|Grant\s*Scheme|\bESAS\b",
                                        operator=all,
                                    ),
                                    separator=P_SEN_SEPARATOR,
                                    operator=any,
                                ),
                                SplitBeforeMatch(
                                    MatchMulti.compile(
                                        rf"({R_EXPIRED_OR_ADOPTED})\sto\senable\b",
                                        MatchMulti.compile(
                                            *R_AWARD_KEYWORD_LIST,
                                            operator=any,
                                        ),
                                        operator=all,
                                    ),
                                    separator=P_SEN_SEPARATOR,
                                    operator=any,
                                ),
                                # "the specific objectives of the share",
                                # "purpose.*?is to( provide)? incentiv",
                                # "purpose.*?scheme are to recognise",
                                # "improve the long-term incentive",
                                # "provides eligible employees with the opportunity to acquire proprietary",
                                SplitBeforeMatch(
                                    MatchMulti.compile(
                                        rf"({R_INCENTIVE_ABBREV}|long{R_MIDDLE_DASH}?term incentive|Co-Ownership plans)",
                                        "(compensation|attract|retain|motivate)",
                                        operator=all,
                                    ),
                                    separator=P_SEN_SEPARATOR,
                                    operator=any,
                                ),
                            ),
                            "syllabus_regs": [
                                MatchMulti.compile(rf"{R_SHARE_INCENTIVE}|{R_AWARD_KEYWORD}", operator=any),
                            ],
                            "lead_paras_patterns": [
                                r"[:：]$",
                            ],
                            "follow_paras_patterns": [
                                R_FOLLOW_PREFIX,
                            ],
                        },
                        {
                            "name": "table_kv",
                            "feature_white_list": [
                                "__regex__Purpose",
                            ],
                        },
                        {
                            "name": "score_filter",
                            "threshold": 0.618,
                        },
                        # 匹配 negative statement
                        # http://************:55647/#/project/remark/232284?treeId=5589&fileId=66336&schemaId=5&projectId=17&schemaKey=A34
                        {
                            "name": "para_match",
                            "multi_elements": True,
                            # "neglect_syllabus_regs": R_BASE_NEGLECT_SYLLABUS_REGS,
                            "paragraph_pattern": R_ND_SHARE_AWARD_SCHEME,
                            "neglect_pattern": P_NO_EQUITY,
                        },
                    ]
                },
            },
        ],
    },
    {
        "path": ["A35"],
        "fake_leaf": True,
        "models": [
            {
                "name": "group_based",
                "share_type": "award",
                "group_default_enum": LRSEnum.ND.value,
                "column_configs": {
                    "Content": [
                        {
                            "name": "no_share",
                            "with_element_box": True,
                            "enum": LRSEnum.NS.value,
                        },
                        # 根据分数提取
                        {
                            "name": "score_filter",
                            "threshold": 0.7,
                        },
                        # 根据关键字章节提取
                        {
                            "name": "syllabus_elt_v2",
                            "only_inject_features": True,
                            "neglect_parent_features": [
                                NeglectPattern.compile(match=R_OPTION_KEYWORD, unmatch=R_AWARD_KEYWORD),
                            ],
                            "neglect_features": [
                                NeglectPattern.compile(match=R_OPTION_KEYWORD, unmatch=R_AWARD_KEYWORD),
                            ],
                            "inject_syllabus_features": [
                                rf"__regex__^{R_PARTICIPANT}$",
                                rf"__regex__(Eligibility|Eligible).*{R_PARTICIPANT}",
                                rf"__regex__{R_PARTICIPANT}.*(Eligibility|Eligible)",
                                rf"__regex__{R_PARTICIPANT}.*({SHARE_AWARD_SCHEME_TEXT})",
                                rf"__regex__({SHARE_AWARD_SCHEME_TEXT}).*{R_PARTICIPANT}",
                            ],
                            "ignore_pattern": [r"^Report of Directors$", r"^Directors’ Report$"],
                        },
                        {
                            "name": "table_kv",
                            "feature_white_list": [
                                rf"__regex__Eligible\s*{R_PARTICIPANT}",
                                rf"__regex__{R_PARTICIPANT}s?\s*of\s*the\s*{R_AWARD_KEYWORD}",
                            ],
                        },
                        # 根据关键字提取
                        {
                            "name": "follow_paras",
                            "neglect_syllabus_regs": [
                                # *R_34_37_NEGLECT_SYLLABUS_REGS,
                                # 退休福利计划
                                # r"RETIREMENT BENEFITS SCHEME",
                                # r"Pre-IPO",
                                # r"Award of the Awarded Shares",
                                # r"DIRECTORS’ RIGHTS TO ACQUIRE SHARES OR DEBT SECURITIES",
                                # r"COMPETING BUSINESS",
                                r"Objective",
                                r"Vesting",
                            ],
                            "syllabus_regs": [
                                r"(Share (Award|Incentive|Purchase|Ownership)?|RSU|RAS)\s*(Scheme|plan)",
                                r"Restricted (Share|STOCK) (Award|Unit) (Scheme|plan)",
                                r"(Equity[\-\s*]settled )?Share-based (transaction|PAYMENTS)",
                                r"Eligibility",
                                r"GLOBAL PARTNER PROGRAM SHARE SCHEME",
                                r"INCENTIVE SCHEME",
                            ],
                            "lead_paras_patterns": [
                                r"[:：]$",
                            ],
                            "follow_paras_patterns": [
                                R_FOLLOW_PREFIX,
                            ],
                            "paragraph_pattern": [
                                SplitBeforeMatch(
                                    NeglectPattern.compile(
                                        match=MatchMulti.compile(
                                            MatchMulti.compile(
                                                MatchMulti.compile(
                                                    r"recognis(e|ing) (the )?contributions?",
                                                    # http://************:55647/#/project/remark/257727?projectId=17&treeId=21876&fileId=68760&schemaId=5
                                                    # 为员工提供一个机会
                                                    r"provide an opportunity for the Group full[\-\s*]time",
                                                    r"(eligible|certain|group's)",  # {R_EN_NUM}
                                                    # 员工 获得一项奖励
                                                    r"receive an award",
                                                    operator=any,
                                                ),
                                                r"employee",
                                                operator=all,
                                            ),
                                            #  符合条件的参与者
                                            MatchMulti.compile(
                                                MatchMulti.compile(
                                                    r"includ(e|ing)[^\.]*(directors|employee|personnel)",
                                                    r"entitled",
                                                    r"(classes|contributions?) of (the )?eligible",
                                                    r"full[\-\s*]time",
                                                    r"\bcover\b",
                                                    r"are existing",
                                                    r"following criteria",
                                                    r"^Eligible",
                                                    operator=any,
                                                ),
                                                rf"{R_PARTICIPANT}",
                                                operator=all,
                                            ),
                                            # 合格人士 http://************:55647/#/project/remark/231098?projectId=17&treeId=24713&fileId=60863&schemaId=5
                                            MatchMulti.compile(
                                                rf"[（(](the )?[\"“]?(RSU )?((Eligible|Selected) (Persons?|Employee|{R_PARTICIPANT}))[(（]?s?[）)]?[\"”]?[）)]",
                                                operator=any,
                                            ),
                                            # 奖励 授予 员工
                                            MatchMulti.compile(
                                                # # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/4560#note_518820
                                                r"shares? (award|Incentive)|restricted stock unit award",
                                                r"be granted",
                                                r"(employee|person|director)",
                                                operator=all,
                                            ),
                                            operator=any,
                                        ),
                                        unmatch=MatchMulti.compile(
                                            rf"{R_OPTION_KEYWORD}",
                                            r"Pre-IPO",
                                            r"No Share",
                                            r"payable",
                                            r"eligible grantees",
                                            rf"vested to eligible {R_PARTICIPANT}",
                                            r"^Eligible Participants$",
                                            r"no eligible",
                                            operator=any,
                                        ),
                                    ),
                                    separator=P_SEN_SEPARATOR,
                                    operator=any,
                                ),
                            ],
                        },
                        # 根据 留住、激励、认可、吸引 相关人员的描述提取
                        {
                            "name": "para_match",
                            "neglect_syllabus_regs": R_34_37_NEGLECT_SYLLABUS_REGS,
                            "paragraph_pattern": (
                                SplitBeforeMatch(
                                    NeglectPattern.compile(
                                        match=MatchMulti.compile(
                                            MatchMulti.compile(
                                                SHARE_AWARD_SCHEME_TEXT,
                                                MatchMulti.compile(
                                                    r"employee|(selected|such) individuals|eligible persons",
                                                    # r"(retain|provide) incentives to the selected individuals",
                                                    # r"encouraging and retaining such individuals",
                                                    r"recogni[sz]e the contributions (by|of) (certain|the) participants",
                                                    operator=any,
                                                ),
                                                operator=all,
                                            ),
                                            # 吸引、留住 员工
                                            # http://************:55647/#/project/remark/264442?projectId=17&treeId=13124&fileId=70103&schemaId=5
                                            MatchMulti.compile(
                                                r"attract|retain",
                                                r"(management|technical|key|officer)s? ([(（]?business[）)]? )?personnel",
                                                operator=all,
                                            ),
                                            operator=any,
                                        ),
                                        unmatch=MatchMulti.compile(
                                            rf"{R_OPTION_KEYWORD}",
                                            r"No Share",
                                            r"no eligible",
                                            operator=any,
                                        ),
                                    ),
                                    separator=P_SEN_SEPARATOR,
                                    operator=any,
                                ),
                            ),
                        },
                        {
                            # 无participant相关的明确标题时，多位于purpose相关段落或者scheme下方首段中
                            "name": "syllabus_elt_v2",
                            "only_inject_features": True,
                            "inject_syllabus_features": [
                                r"__regex__^Purpose$",
                            ],
                            "neglect_parent_features": [
                                NeglectPattern.compile(match=R_OPTION_KEYWORD, unmatch=R_AWARD_KEYWORD),
                                r"Pre-IPO",
                            ],
                        },
                        # 匹配 NS
                        {
                            "name": "para_match",
                            "multi_elements": True,
                            "paragraph_pattern": R_ND_SHARE_AWARD_SCHEME,
                            "neglect_pattern": P_NO_EQUITY,
                        },
                    ]
                },
            },
        ],
    },
    {
        "path": ["A36"],
        # "default_enum_value": LRSEnum.ND.value,
        "fake_leaf": True,
        "models": [
            {
                "name": "group_based",
                "share_type": "award",
                "group_default_enum": LRSEnum.ND.value,
                "column_configs": {
                    "Content": [
                        # 1.NS
                        {
                            "name": "no_share",
                            "with_element_box": True,
                            "enum": LRSEnum.NS.value,
                        },
                        {
                            "name": "para_match",
                            "multi_elements": True,
                            "paragraph_pattern": R_ND_SHARE_AWARD_SCHEME,
                            "neglect_pattern": P_NO_EQUITY,
                        },
                        # 2. PS
                        {
                            "name": "para_match",
                            "neglect_syllabus_regs": R_34_37_NEGLECT_SYLLABUS_REGS,
                            "paragraph_pattern": (
                                SplitBeforeMatch(
                                    MatchMulti.compile(
                                        r"as of the latest practicable date.*?restricted shares",
                                        # r"of the total issued shares of.*?the date of",
                                        rf"The maximum number of Shares.*?under {SHARE_AWARD_SCHEME_TEXT} in any financial year",
                                        r"the date of.*?total number of securities issuable",
                                        r"the maximum number of underlying shares that may be granted under.*?at the Latest Practicable Date",
                                        rf"at the end of the reporting period.*?awarded shares outstanding under {SHARE_AWARD_SCHEME_TEXT}",
                                        r"The number of awarded shares remain available",
                                        operator=any,
                                    ),
                                    separator=P_SEN_SEPARATOR,
                                    operator=any,
                                ),
                                SplitBeforeMatch(
                                    NeglectPattern.compile(
                                        match=MatchMulti.compile(
                                            r"represent(ing|ed)",
                                            NeglectPattern.compile(
                                                match=R_ISSUED_SHARE,
                                                unmatch=rf"{R_ISSUED_SHARE} and the {R_ISSUED_SHARE}",
                                            ),
                                            MatchMulti.compile(
                                                rf"the (Company’s (issued )?shares? capital|{R_ISSUED_SHARE} (capital)? of the Company)",
                                                MatchMulti.compile(
                                                    r"the (relevant )?(time|date)",
                                                    r"this (annual )?report",
                                                    operator=all,
                                                ),
                                                operator=any,
                                            ),
                                            operator=all,
                                        ),
                                        unmatch=MatchMulti.compile(
                                            rf"\b(since|after|from|upon|as\s*(at|of)|up\s*to|end(ed)?)\s.*\b20\d{{2}}\b {R_BE} [\d,]+ (and [\d,]+ )?Shares",
                                            r"\b(during|in)\s*(the\s*date\s*of\s*)?\s*(the|this)\s*(financial\s*|annual\s*|report(ing)?\s*|year\s*|period\s*){2,4}",
                                            r"\b(during|in)\s*the\s*date\s*of\s*(the|this)\s*(year|report(ing)?)(?!\s*(end|as|\d{4}))",
                                            operator=any,
                                        ),
                                    ),
                                    separator=re.compile(rf"{P_SEN_SEPARATOR.pattern}"),
                                    operator=any,
                                ),
                            ),
                        },
                        {
                            "name": "special_cells",
                            "cell_pattern": (f"the date of(?! the Board).*?{R_NUMBER_SHARE}",),
                        },
                        {
                            "name": "para_match",
                            "neglect_parent_features": [
                                NeglectPattern(
                                    match=MatchMulti.compile(
                                        r"share options? schemes?$",
                                        r"^share options?$",
                                        operator=any,
                                    ),
                                    unmatch=MatchMulti.compile(r"Restricted Share Unit Scheme", operator=any),
                                ),
                            ],
                            "neglect_syllabus_regs": R_34_37_NEGLECT_SYLLABUS_REGS
                            + [
                                r"maximum entitlement",
                                r"sufficiency of public float",
                                r"executive director",
                                r"share capital",
                                r"Maximum number of Shares underlying the RSUs and Restricted Shares",
                                r"Accounting Treatment on Restrictive Shares",
                                r"SALE OR REDEMPTION OF THE COMPANY’S LISTED SECURITIES",
                                r"Limit for each Participant",
                                r"capital structure",
                                r"SHARES ISSUED IN THE YEAR",
                                r"INTERESTS AND SHORT POSITIONS",
                            ],
                            "neglect_pattern": [
                                r"share incentive scheme is limited",
                                r"the maximum number of shares may be renewed",
                                rf"(first|second) restricted {SHARE_AWARD_SCHEME_TEXT}",
                                r"the date of which the 20\d{2} a share",
                                r"issued Shares on the adoption date without prior approval from the Shareholders",
                                r"no shares shall be purchased or subscribed pursuant to the scheme",
                                rf"the company will not grant any award shares under {SHARE_AWARD_SCHEME_TEXT}",
                                rf"not issue or grant any awarded shares under {SHARE_AWARD_SCHEME_TEXT}",
                                r"the board shall not make any further award of awarded shares",
                                rf"{SHARE_AWARD_SCHEME_TEXT} and % of {R_ISSUED_SHARE}",
                            ],
                            "paragraph_pattern": (
                                MatchMulti.compile(
                                    r"this annual report|at the latest practicable date",
                                    SHARE_AWARD_SCHEME_TEXT,
                                    rf"of the total number of shares|representing.*?of the {R_ISSUED_SHARE}",
                                    operator=all,
                                ),
                                # https://jura-uat2.paodingai.com/#/hkex/annual-report-checking/report-review/248382?fileId=83243&schemaId=5&rule=A36&delist=0
                                MatchMulti.compile(
                                    rf"{SHARE_AWARD_SCHEME_TEXT} was.*?shares of the company|restricted a shares was|total number of awarded shares|new Shares",
                                    rf"representing.*?of (the|our) total {R_ISSUED_SHARE}|represents.*?per cent of the issued",
                                    operator=all,
                                ),
                                # https://jura-uat2.paodingai.com/#/hkex/annual-report-checking/report-review/243425?fileId=80151&schemaId=5&rule=A36&delist=0
                                MatchMulti.compile(
                                    SHARE_AWARD_SCHEME_TEXT,
                                    r"exceeding.*?% of the Shares in issue|excess of [\d+.]+% of the issued share",
                                    operator=all,
                                ),
                                MatchMulti.compile(
                                    r"Latest Practicable Date",
                                    r"[\d,]+\bShares",
                                    rf"representing.*?of the {R_ISSUED_SHARE}",
                                    operator=all,
                                ),
                                MatchMulti.compile(
                                    r"reporting period",
                                    r"had.*?share",
                                    r"represented.*?of",
                                    operator=all,
                                ),
                                rf"the restricted {SHARE_AWARD_SCHEME_TEXT} is limited",
                                rf"total number of {R_ISSUED_SHARE} at the (relevant )?(time|date)",
                                SplitBeforeMatch(
                                    NeglectPattern.compile(
                                        match=MatchMulti.compile(
                                            rf"{R_NUMBER_SHARE}",
                                            # https://jura-uat2.paodingai.com/#/hkex/annual-report-checking/report-review/243206?fileId=80076&schemaId=5&rule=A36&delist=0
                                            r"the (date|time) of(?! the Board)(?! such grant)",
                                            rf"\d+{R_PERCENT}",
                                            operator=all,
                                        ),
                                        # https://mm.paodingai.com/cheftin/pl/icmcspeaqfr4tgkcf13xqaa15r
                                        unmatch=MatchMulti.compile(
                                            rf"not issue or grant any {SHARE_AWARD_SCHEME_TEXT}|Participant",
                                            rf"\b(since|after|from|upon|as\s*(at|of)|up\s*to|end(ed)?)\s.*\b20\d{{2}}\b {R_BE} [\d,]+ (and [\d,]+ )?Shares",
                                            r"\b(during|in)\s*(the\s*date\s*of\s*)?\s*(the|this)\s*(financial\s*|annual\s*|report(ing)?\s*|year\s*|period\s*){2,4}",
                                            r"\b(during|in)\s*the\s*date\s*of\s*(the|this)\s*(year|report(ing)?)(?!\s*(end|as|\d{4}))",
                                            operator=any,
                                        ),
                                    ),
                                    separator=P_SEN_SEPARATOR,
                                    operator=any,
                                ),
                            ),
                        },
                        {
                            "name": "score_filter",
                            "threshold": 0.618,
                        },
                    ]
                },
            },
        ],
    },
    {
        "path": ["A37"],
        "fake_leaf": True,
        "models": [
            {
                "name": "group_based",
                "share_type": "award",
                "group_default_enum": LRSEnum.ND.value,
                "column_configs": {
                    "Content": [
                        {
                            "name": "no_share",
                            "with_element_box": True,
                            "enum": LRSEnum.NS.value,
                        },
                        {
                            "name": "score_filter",
                            "neglect_syllabus_regs": R_34_37_NEGLECT_SYLLABUS_REGS,
                            "threshold": 0.618,
                        },
                        {
                            "name": "syllabus_based",
                            "include_title": False,
                            "inject_syllabus_features": [
                                r"__regex__maximum entitlement",
                            ],
                            "paragraph_model": "para_match",
                            "para_config": {
                                "paragraph_pattern": [
                                    r"maximum entitlement",
                                ],
                            },
                            "table_model": "empty",
                        },
                        {
                            "name": "para_match",
                            # "multi_elements": False,
                            "neglect_syllabus_regs": R_34_37_NEGLECT_SYLLABUS_REGS,
                            "paragraph_pattern": (*UNDER_INCENTIVE_SCHEME,),
                        },
                        {
                            "name": "para_match",
                            "multi_elements": True,
                            "neglect_syllabus_regs": R_34_37_NEGLECT_SYLLABUS_REGS
                            + [
                                r"Scheme Limit",
                                # 过滤总的限制章节  http://************:55647/#/project/remark/231881?treeId=37756&fileId=66269&schemaId=5&projectId=37756&schemaKey=A37
                            ],
                            "paragraph_pattern": (
                                SplitBeforeMatch(
                                    MatchMulti.compile(
                                        r"not exceed|\b(individual|no) limit",
                                        r"participa|eligible person|(each eligible|elected) employee|selected person",
                                        operator=all,
                                    ),
                                    separator=P_SEN_SEPARATOR,
                                    operator=any,
                                ),
                                SplitBeforeMatch(
                                    MatchMulti.compile(
                                        r"exceed.*?of the Shares in issue",
                                        r"each individual grantee",
                                        operator=all,
                                    ),
                                    separator=P_SEN_SEPARATOR,
                                    operator=any,
                                ),
                                r"participant shall not in aggregate exceed",
                                r"each (above )?participant.*?exceed",
                                r"\b(individual|no) limit",
                                r"selected participant are limited to",
                                r"aggregate over.*?of the Shares in issue",
                                r"not make any share award to a Participant",
                            ),
                        },
                        {
                            "name": "para_match",
                            # "multi_elements": False,
                            # "neglect_syllabus_regs": R_34_37_NEGLECT_SYLLABUS_REGS,
                            "neglect_syllabus_regs": [
                                r"Objective",
                                r"Vesting",
                            ],
                            "syllabus_regs": [
                                r"(Share (Award|Incentive|Purchase|Ownership)?|RSU|RAS)\s*(Scheme|plan)",
                                r"Restricted (Share|STOCK) (Award|Unit) (Scheme|plan)",
                                r"(Equity[\-\s*]settled )?Share-based (transaction|PAYMENTS)",
                                r"Eligibility",
                                r"GLOBAL PARTNER PROGRAM SHARE SCHEME",
                                r"INCENTIVE SCHEME",
                            ],
                            "paragraph_pattern": (
                                r"number of Shares entitled by a participant is subject to such terms",
                                r"each participant.*?compliance with",
                                r"person.*?period exceeding .*?per cent",
                                r"the total number of shares.*?not exceed",
                                r"award exceed",
                                # rf"{SHARE_AWARD_SCHEME_TEXT} shall not exceed",
                                SplitBeforeMatch(
                                    MatchMulti.compile(
                                        MatchMulti.compile(
                                            rf"(Eligible|Selected) (Grantee|Persons?|Employee|{R_PARTICIPANT})",
                                            operator=any,
                                        ),
                                        MatchMulti.compile(
                                            rf"{SHARE_AWARD_SCHEME_TEXT} shall not exceed",
                                            r"maximum number of (award )?shares? (which|that) may be (awarded|granted)",
                                            operator=any,
                                        ),
                                        operator=all,
                                    ),
                                    separator=P_SEN_SEPARATOR,
                                    operator=any,
                                ),
                                r"neither.*?maximum entitlement of each eligible participant",
                            ),
                        },
                    ]
                },
            },
        ],
    },
    {
        "path": ["丨H38-41"],
        "fake_leaf": True,
        "models": [
            {
                "name": "group_based",
                "share_type": "award",
                "no_column": True,
                "group_default_enum": AnswerValueEnum.ND_CAP.value,
                "filter_group_answer": True,
                "post_process_answers": h83_post_process_answers,
                "column_configs": H38_41_MODELS,
            }
        ],
    },
    # A38-A41 根据丨H38-41的答案分别从 A34-A37获得
]
