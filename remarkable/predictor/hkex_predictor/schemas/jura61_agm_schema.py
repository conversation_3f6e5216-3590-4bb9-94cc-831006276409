from remarkable.common.common_pattern import R_MIDDLE_DASHES
from remarkable.common.pattern import <PERSON><PERSON><PERSON><PERSON>, NeglectPattern
from remarkable.predictor.common_pattern import R_PERCENT_STRICT
from remarkable.predictor.hkex_predictor.pattern import R_BE

predictor_options = [
    {
        "path": ["Percentage"],
        "sub_primary_key": ["Relevant share class"],
        "strict_group": True,
        "models": [
            {
                "name": "agm_shares_issued",
                "paragraph_pattern": [
                    rf"new.*shares.*?of the company allotted.*?shall not exceed (?P<value>\d{{1,2}}{R_PERCENT_STRICT})",
                    rf"\b(?<!\.)(?P<value>\d{{1,2}}{R_PERCENT_STRICT})\s*of\s*(?P<class>(the|each).*shares?)\b",
                    rf"\b(?<!\.)(?P<value>\d{{1,2}}{R_PERCENT_STRICT})\s*of\s*(?P<class>the shares?.*?\bin issue)\b",
                    r"\b(?P<value>(twenty|ten) per cent(um)? [(（]\d{1,2}[%％][）)])\s*of\s*(?P<class>the.*shares?)\b",
                ],
                "neglect_pattern": [
                    MatchMulti.compile(
                        NeglectPattern.compile(
                            match=rf"\b(repurchas(ed?|ing)|(buy|bought)[\s{R_MIDDLE_DASHES}]back)",
                            unmatch=rf"excluding.+?share.+?{R_BE} repurchased",
                        ),
                        r"\bexten(ds?|tion).*?\smandate",
                        r"\bcash consideration",
                        r"\bdiscount\b",
                        r"\btotal amount of proceed",
                        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7724#note_764582
                        r"\bsubsequent (consolidation|sub-?division)\b.{,20}\bof shares",
                        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7724#note_764646
                        NeglectPattern.compile(
                            match=r"\b(as defined in|under)\b.{,20}\bshare option scheme", unmatch=r"otherwise than"
                        ),
                        operator=any,
                    ),
                ],
            }
        ],
    },
]


prophet_config = {"depends": {}, "predictor_options": predictor_options}
