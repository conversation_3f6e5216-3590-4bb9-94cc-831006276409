"""
Jura4 Annual Report ESG
"""

from remarkable.predictor.hkex_predictor.schemas.ar_esg_schema.common_pattern import (
    DEFAULT_MODEL,
    NEGLECT_PAGE_HEADER_PATTERN,
)

predictor_options = [
    {
        # 一般位于ESG report 前面几页，常在board statement或governance  structure小标题下面，
        # 提取涉及“board对整个公司ESG相关的措施或report负责的描述-包括制定，实施，检查，复核，监督，批准，风险评估”等，
        # 主要突出有responsibility或role
        # 常包含关键词
        # oversee/oversight/overall/full/ultimate/solely +
        # responsibility/accountability/leadership/direction/charge of或responsible等等
        # 有时会在多个段落出现相同的描述，可同时提取。
        "path": ["MDR 13 i - board oversight"],
        "models": [
            {
                "name": "kmeans_classification",
                "para_pattern": [
                    r"(oversee|oversight|overall|full|ultimate|solely).*?(responsibility|accountability|leadership|direction|responsible|governance|committee|charge of)",
                    r"(responsibility|accountability|leadership|direction|responsible|governance|committee).*?(integrity)",
                ],
            },
            {
                "name": "para_match",
                "syllabus_regs": [
                    r"board statement",
                    r"governance structure",
                    r"approach|strategy",
                ],
                "paragraph_pattern": (
                    r"(oversee|oversight|overall|full|ultimate|solely).*?(responsibility|accountability|leadership|direction|responsible|governance|committee|charge of)",
                    r"(responsibility|accountability|leadership|direction|responsible|governance|committee).*?(integrity)",
                ),
                "multi_elements": True,
            },
            {
                "name": "para_match",
                "syllabus_regs": [
                    r"Social.*?Governance.*?Report",
                    r"esg",
                ],
                "paragraph_pattern": (
                    r"(oversee|oversight|overall|full|ultimate|solely).*?(responsibility|accountability|leadership|direction|responsible|governance|committee)",
                    r"(responsibility|accountability|leadership|direction|responsible|governance|committee).*?(integrity)",
                ),
                "multi_elements": True,
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__Social.*?Governance.*?Report",
                    r"__regex__esg",
                ],
                "multi_elements": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"(?P<content>(oversee|oversight|overall|full|ultimate|solely).*?("
                        r"responsibility|accountability|leadership|direction|responsible|governance|committee))",
                        r"(?P<content>(responsibility|accountability|leadership|direction|responsible|governance|committee).*?(integrity))",
                    ),
                },
                "table_model": "empty",
            },
            DEFAULT_MODEL,
        ],
    },
    {
        "path": ["MDR 13 ii - board management approach"],
        "models": [
            {
                "name": "kmeans_classification",
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "multi": True,
                "inject_syllabus_features": [
                    r"__regex__ESG Working Group",
                    r"__regex__ESG.*?(approach|strategy)",
                ],
            },
            {
                "name": "para_match",
                "syllabus_regs": [
                    r"board statement",
                    r"governance structure",
                    r"approach|strategy",
                ],
                "paragraph_pattern": (
                    r"(?P<content>ESG Committee|ESG|committee|orking team|working group|establish|set up|delegate|authorise|formulated|strategy|identify|evaluate|priority|approach|strategy)",
                ),
                "multi_elements": True,
            },
            {
                "name": "para_match",
                "syllabus_regs": [
                    r"Social.*?Governance.*?Report",
                    r"esg",
                ],
                "paragraph_pattern": (
                    r"(?P<content>ESG Committee|ESG|committee|orking team|working group|establish|set up|delegate|authorise|formulated|strategy|identify|evaluate|priority|approach|strategy)",
                ),
                "multi_elements": True,
            },
            DEFAULT_MODEL,
        ],
    },
    {
        # 一般位于ESG report 前面几页，常在board statement或governance structure小标题下面，提取涉及到review ESG问题的描述。
        # 优先提取涉及到“ESG相关的target或goal的review”或”progress review”的描述。
        "path": ["MDR 13 iii - board progress review"],
        "models": [
            {
                "name": "kmeans_classification",
                "para_pattern": [
                    r"briefing|meet|frequency|monitor|oversight|review|assess",
                    r"goals",
                    r"overseeing.*?esg",
                ],
            },
            {
                "name": "para_match",
                "multi_elements": True,
                "strict_limit": True,
                "syllabus_regs": [
                    r"board statement",
                    r"governance structure",
                    r"target|goal",
                ],
                "paragraph_pattern": (
                    r"briefing|meet|frequency|monitor|oversight|review|assess",
                    r"overseeing.*?esg",
                ),
            },
            # {
            #     "name": "para_match",
            #     'syllabus_regs': [
            #         r'Social.*?Governance.*?Report',
            #         r'esg',
            #     ],
            #     'paragraph_pattern': (r'(?P<content>report|briefing|meet|frequency|monitor|oversight|review|assess)'),
            #     'multi_elements': True,
            # },
            DEFAULT_MODEL,
        ],
    },
    {
        "path": ["MDR 14 part 1 - materiality application"],
        "pick_answer_strategy": "all",
        "models": [  # 添加参数 NEGLECT_PAGE_HEADER_PATTERN 准确率变低 下一步需要查看badcase
            {
                "name": "row_match",
                "row_pattern": [
                    r"materiality",
                    r"validates the list of material ESG issues",
                ],
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"Materiality.*?Principle.*?esg",
                    r"Materiality:.*?esg",
                    r"Materiality:.*?confirmed by the management",
                ),
            },
            {
                "name": "esg_materiality",
                "paragraph_pattern": (
                    r"Materiality.*?Principle.*?esg",
                    r"Materiality[–:：].*?(esg|materiality matrix)",
                    r"Materiality[–:：].*?confirmed by the management",
                    r"Materiality.*?[-:: ]",
                    r"^•\s?Materiality\s?[–:：]",
                    r"^\d.*?Materiality[–:：].*?(esg|social)",
                ),
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "multi": True,
                "only_inject_features": True,
                "include_title": True,
                "inject_syllabus_features": [
                    r"__regex__STAKEHOLDER ENGAGEMENT AND MATERIALITY ASSESSMENT",
                    r"__regex__MATERIALITY ASSESSMENT",
                    r"__regex__STAKEHOLDER ENGAGEMENT",
                    r"__regex__MATERIAL ANALYSIS",
                    r"__regex__MATERIALITY",
                    r"__regex__MATERIALITY MATRIX",
                    r"__regex__STAKEHOLDERS ENGAGEMENT",
                ],
            },
            {
                "name": "table_title",
                "only_inject_features": True,
                "feature_white_list": [
                    r"materiality assessment",
                ],
            },
            {
                "name": "special_cells",
                "whole_table": True,
                "cell_pattern": (r"Relevant ESG issues to the Group",),
            },
        ],
    },
    {
        # 1. 文档中是表格的时候，是一些特殊情况，将表格模型放在前面
        # 2. 优先匹配 report.*?(principle|standard|framework)---quantitative章节
        # 3. 其次匹配 quantitative: 开头的段落 其实也跟第二点类似，只不过没有quantitative的小标题
        # 4. 在 environment 和 report.*?(principle|standard|framework) 章节下根据关键词匹配
        #    其中 environment下面通常是表格的附注
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/839
        "path": ["MDR 14 part 2 - quantitative application"],
        "models": [
            {
                "name": "row_match",
                "row_pattern": [r"quantitative"],
            },
            {
                "name": "special_cells",
                "cell_pattern": [
                    r"quantitative.*?methods",
                    r"practice the principle of quantitative",
                ],
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__report.*?(principle|standard|framework)__regex__quantitative",
                ],
            },
            {
                "name": "para_match",
                "strict_limit": True,
                "syllabus_regs": [
                    r"report.*?(principle|standard|framework)",
                ],
                "paragraph_pattern": (r"quantitative”?:",),
            },
            {
                "name": "para_match",
                "strict_limit": True,
                "multi_elements": True,
                "syllabus_regs": [
                    r"environment",
                    r"report.*?(principle|standard|framework)",
                ],
                "paragraph_pattern": (
                    r"quantitative|methodology(ies)?|assumptions?|conversion factors?",
                    r"re-calculates|calculated|recalculated|calculation|calculating",
                    r"GHG emissions data.*?based on.*?but not limited to",
                ),
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__MATERIALITY AND REPORTING BOUNDARY",
                ],
                "only_inject_features": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"quantitative|methodology(ies)?|assumptions?|conversion factors?",
                        r"re-calculates|calculated|recalculated|calculation|calculating",
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "esg_quantitative",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__Community investment",  # 获取年报中的汇总表格
                    r"__regex__emission|consumption|Energy",
                ],
                # 'multi_elements': True, # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/997#note_243309
                "multi": True,
                "paragraph_model": "empty",
                "table_model": "special_cells",
                "table_config": {
                    "whole_table": True,
                    "cell_pattern": (
                        r"Energy Consumption",
                        r"Emissions",
                    ),
                },
            },
        ],
    },
    {
        # MDR14-consistency:发行人应在 ESG 报告中披露所用方法或 KPI 的变化，或影响对比的相关因素。
        # 位置：
        #
        # i.一般位置在ESG报告的前几页，通常在reporting principle/standard/framework标题下方，有明确的标题 consistency,
        # 直接框选标题及内容，枚举选择C
        #
        # iii.若有reporting principle/standard/framework时，但无consistency明确标题可通过关键词定位到段落中对应内容时，
        # 另需同时框选情况ii (找各个指标两年的数据列并框选两年数据作为对比) 的内容，枚举选择C
        # ii.若无reporting principle/standard/framework时，可先定位ESG报告中,找各个指标两年的数据列并框选两年数据作为对比,枚举选择C
        # iv.若ESG 报告中无相关描述则枚举选择ND
        "path": ["MDR 14 part 3 - consistency application"],
        "models": [
            {
                "name": "para_match",
                "paragraph_pattern": (r"^.?consistency\s?[:-•]",),
                "syllabus_regs": [
                    r"reporting principle",
                    r"standard",
                ],
            },
            {
                "name": "row_match",
                "row_pattern": [r"consistency"],
            },
            {
                "name": "special_cells",
                "cell_pattern": [r"consistency.*?methods"],
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__report.*?(principle|standard|framework)__regex__consistency",
                ],
            },
            {
                "name": "consistency",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                # "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__reporting principle",
                    r"__regex__About this Report",
                    r"__regex__MATERIALITY",  # MATERIALITY AND REPORTING BOUNDARY
                ],
                "multi_elements": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"consistent methodology",
                        r"used consistent reporting",
                        r"adopt consistent reporting",
                        r"(?P<content>consistency|methodology(ies)?|assumptions?|conversion factors?)",
                        r"(?P<content>aligns with)",
                        r"adopt consistent report",
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"consistent methodology",
                    r"used consistent reporting",
                    r"adopt consistent reporting",
                    r"(?P<content>consistency|methodology(ies)?|assumptions?|conversion factors?)",
                    r"(?P<content>aligns with)",
                ),
                "syllabus_regs": [
                    r"reporting principle",
                    r"standard",
                ],
            },
            {
                "name": "score_filter",
                "multi_elements": True,
                "aim_types": ["TABLE"],
                "threshold": 0.1,
            },
        ],
    },
    {
        # MDR15 reporting boundary：说明ESG 报告的报告界限,并描述用于识别 ESG 报告中包含哪些实体或业务的过程。
        # 范围发生变更的，发行人应当说明差异及变更原因
        # 位置：
        #
        # i.一般位置在ESG报告的前几页，通常在
        # scope and reporting period/reporting scope/reporting boundary/reporting scope and boundaries标题下，
        # 主要描述report包含的xxx operation/business，或在xxx地方的operation,或xxx entities或其包含的xxx子公司，直接框选段落，枚举C;
        #
        # ii.当ESG报告中有包含scope的标题，下方段落除boundary内容外还披露相关的准则或者是ESG报告的报告期则只需要框选boundary相关的内容，
        # 即report包含的xxx operation/business，xxx地方，xxxentitites
        #
        # iii.当无明确标题时或关键词也不明显但是披露了公司及其包含的子公司的主营相关内容也属于此规则，枚举应判断为C
        # 常见关键词：boundary,scope,cover,focus,engaged in
        "path": ["MDR 15 - reporting boundary"],
        "models": [
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"We regularly review the scope of the ESG Report",
                    r"ESG Report includes",
                    r"has two major operating segments",
                ),
            },
            # 优先提取指定章节的整个段落
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__ESG Report Scope and Boundary",
                    r"__regex__Reporting Scope and (Boundary|Boundaries)",
                    r"__regex__SCOPE OF REPORTING",
                    r"__regex__REPORT(ING)? SCOPE$",
                    r"__regex__REPORT(ING)? BOUNDARY$",
                    r"__regex__SCOPE OF THE REPORT",
                    r"__regex__Scope of this ESG Report",
                ],
            },
            {
                "name": "syllabus_based",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__AND REPORTING BOUNDARY",
                    r"__regex__REPORTING BOUNDARIES AND PRINCIPLES",
                    r"__regex__REPORTING SCOPE AND PERIOD",
                    r"__regex__REPORTING PERIOD AND BOUNDARY",
                    r"__regex__ESG Report Scope and Boundary",
                    r"__regex__SCOPE AND PERIOD OF REPORTING",
                    r"__regex__About this Report",
                ],
                "multi_elements": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"the scope of",
                        r"the ESG report covers",
                        r"report contains details.*?social responsibilities",
                        r"report.*?(focus|operations|businesses)",
                        r"focus|engaged in",
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"(?P<content>report.*?(boundary|scope|cover|focus|operations|businesses))",
                    r"(?P<content>report contains details.*?social responsibilities)",
                    r"the scope of",
                    r"the ESG report covers",
                    r"report contains details.*?social responsibilities",
                    r"focus|engaged in",
                ),
                "neglect_pattern": (r"a lasting standstill for the whole of the reporting period",),
            },
            {
                "name": "special_cells",
                "cell_pattern": [
                    # r'report contains details.*?social responsibilities',
                    r"Report.*?encompasses.*?subsidiaries",
                ],
            },
            DEFAULT_MODEL,
        ],
    },
]
