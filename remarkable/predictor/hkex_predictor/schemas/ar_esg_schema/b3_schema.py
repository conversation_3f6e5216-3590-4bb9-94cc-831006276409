"""
Jura4 Annual Report ESG
"""

from remarkable.predictor.hkex_predictor.schemas.ar_esg_schema.common_pattern import (
    DEFAULT_MODEL,
    NEGLECT_PAGE_HEADER_PATTERN,
)

predictor_options = [
    {
        "path": ["B3 policies - development and training"],
        "models": [
            # 特例 GRI 相关 https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/997#note_243521
            {
                "name": "para_match",
                "multi_elements": True,
                "syllabus_regs": [
                    r"Benefits and Employee Retention",
                ],
                "paragraph_pattern": (
                    r"create a culture of continuous learning",
                    r"designs its training.*?improve their job performance",
                ),
            },
            {
                "name": "after_row_match",
                "row_pattern": [r"B3"],
                "para_pattern": [
                    r"(?P<content>B3|development|training)",
                ],
            },
            {
                "name": "policy_row_match",
                "multi": True,
                "multi_elements": True,
                "row_pattern": ["^General Disclosure"],
                "anchor_pattern": ["Aspect B3"],
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                # "only_before_first_chapter": True,
                "inject_syllabus_features": [
                    r"__regex__III.3 Training and Development",
                    r"__regex__Development and Training",
                    r"__regex__staff development",
                ],
            },
            DEFAULT_MODEL,
        ],
    },
    {
        # B3.1 此条规则是披露员工中受培训的情况。具体是按性别和员工类别（例如高级管理人员、中层管理人员）培训的员工百分比（重点是百分比数据）
        # 常见关键词有B3.1，train, gender, employee category。
        #  所在位置：1、优先在index或者data summary的地方找对应的图表、表格及段落；
        #           2、根据index给出KPI的位置去定位，一般在标题Development and Training/staff development下
        #
        #  判断依据：1、报告中没有相关数据但有解释，涉及no training的描述（常见描述如“本年因为疫情原因，集团未给员工提供培训”），为E；
        #           2、报告中披露了参与培训的各类员工人数对应的百分比数据，都是Y；
        #           3、报告没有披露相关内容，也没有进行解释，为ND
        #
        #  判断特殊情况：1、仅有total training hours，没有披露按照gender和category分类下的average training hours ，也没有
        #               percentage of employee trained，B3.1属于Y；
        #               2、有时B3.1描述100%完成了相关的培训项目，出现completion xxx也属于B3.1
        "path": ["KPI B3.1 - percentage of employees trained"],
        "models": [
            {
                "name": "split_first_table",
                # "second_table": "True",
                "regs": [
                    r"B3\.1",
                    r"Percentage of employees trained",
                    r"Number.*?employees.*?train",
                ],
                "start_regs": [
                    r"B3\.1",
                    r"Training Indicators",
                    r"Number.*?employees.*?train",
                    r"(Percentage|%|proportion).*?(traine\s?d|training)",
                    r"(trained|training).*?(Percentage|%|proportion)",
                ],
                "end_regs": [
                    r"Average hours",
                    r"B3\.2",
                ],
            },
            {
                "name": "first_table",
                "title_regs": [
                    r"性別及僱員類別劃分之百分比及平均培訓時數",
                ],
                "regs": [
                    r"B3.1",
                    r"Percentage of employees trained",
                    r"Total number of employees received",
                ],
            },
            {
                "name": "after_row_match",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "row_pattern": [r"B3.1"],
                "para_pattern": [
                    r"(?P<content>During the reporting period.*?training)",
                    r"(?P<content>no training)",
                ],
                "middle_rows": True,
                "multi_elements": True,
                "table_title": [
                    r"trained by.*?(gender|category)",
                    r"受訓僱員百分比",
                    r"trained by",
                    r"(Percentage|%|proportion).*?(traine\s?d|training)",
                    r"(trained|training).*?(Percentage|%|proportion)",
                    r"Below are the details of employee training",
                    r"train.*?percentage by",
                ],
                "start_regs": [r"Percentage of employees trained", r"By employee category"],
                "end_regs": [r"Average training hours"],
            },
            {
                "name": "shape_title",
                "regs": (
                    r"受訓僱員百分比",
                    r"trained by",
                    r"(Percentage|%|proportion).*?(traine\s?d|training)",
                    r"(trained|training).*?(Percentage|%|proportion)",
                ),
            },
            {
                "name": "table_title",
                "only_inject_features": True,
                "multi_elements": True,
                "feature_white_list": (
                    r"受訓僱員百分比",
                    r"trained by",
                    r"(Percentage|%|proportion).*?(traine\s?d|training)",
                    r"(trained|training).*?(Percentage|%|proportion)",
                    r"Employees.*?provided.*?training sessions",
                ),
            },
            {
                "name": "special_cells",
                "whole_table": True,
                "cell_pattern": (
                    r"受訓僱員百分比",
                    r"發展與培訓 \(%\)",
                    r"(Percentage|%|proportion).*?(traine\s?d|training)",
                    r"(trained|training).*?(Percentage|%|proportion)",
                    r"number.*?trained",
                ),
            },
            {
                "name": "middle_rows",
                "ignore_index_table": True,
                "regs": [
                    r"B3\.1",
                    r"Percentage of employees trained",
                ],
                "start_regs": [
                    r"B3\.1",
                    r"Training Indicators",
                    r"(Percentage|%|proportion).*?(traine\s?d|training)",
                    r"(trained|training).*?(Percentage|%|proportion)",
                ],
                "end_regs": [
                    r"Average hours",
                    r"B3\.2",
                ],
            },
            {
                "name": "para_match",
                "syllabus_regs": [
                    r"train|development",
                ],
                "paragraph_pattern": (
                    # r'(?P<content>B3.1|train|gender|employee category)',
                    r"(?P<content>no training)",
                    r"\d+% of our employees are trained",
                    r"percentage of trained employees.*?\d+%",
                    r"the male and female composition ratio of the employees trained is approximately",
                ),
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__Summary of Development and Training",
                    r"__regex__Summary of Development and Training",
                    r"__regex__Provision of Training Opportunities",
                    r"__regex__Training and Development Management",
                    r"__regex__Development and training",
                    r"__regex__training",
                ],
                "multi_elements": True,
                "only_inject_features": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"(?P<content>no training)",
                        r"\d%.*?attended training courses",
                        r"did not arrange any training courses",
                        r"proportions of training",
                    ),
                },
                "table_model": "table_title",
                "table_config": {
                    "feature_white_list": [
                        r"Summary of Development and Training",
                    ],
                },
            },
            # DEFAULT_MODEL,
        ],
    },
    {
        #  规则是针对employment下面的Development and Training，披露按性别和员工类别划分的每位员工完成的平均培训时数。
        #  关键词是average training hours、employee category
        #  所在位置：1、优先去index/summary/key performance indicators中找寻；
        #           2、根据index给出的KPI位置去定位；
        #           3、在Development and Training/staff development标题下。
        #
        #  判断标准：1、如果报告中说没有培训并且解释原因，那么选E，比如`本年因为疫情原因，集团未给员工提供培训`，关键词：no training；
        #           2、找到对应图表、段落或者句子，就算Y；
        #           3、如果报告中没有对此有任何描述，那么就是ND。
        "path": ["KPI B3.2 - training hours completed"],
        "models": [
            {
                "name": "split_first_table",
                "second_table": "True",
                "regs": [
                    r"B3.2",
                    r"train.*?hours",
                    r"hours.*?train",
                ],
                "start_regs": [
                    r"B3.2",
                    r"hours.*?train",
                    r"train.*?hours",
                ],
                "end_regs": [
                    r"B5\.1",
                    r"Occupational safety",
                    r"operational indicator",
                    r"ESG Indicator",
                ],
            },
            {
                "name": "special_cells",
                "multi_elements": True,
                "whole_table": True,
                "cell_pattern": [
                    r"training hours by",
                    r"Average training hours",
                    r"Hours of employees trained",
                ],
            },
            {
                "name": "first_table",
                "regs": [
                    r"B3.2",
                    r"train.*?hours",
                    r"hours.*?train",
                ],
            },
            {
                "name": "shape_title",
                "regs": (r"The average training hours completed",),
            },
            {
                "name": "table_title",
                "multi_elements": True,
                "feature_white_list": [
                    r"training hours.*?by",
                    r"The average training hours completed",
                ],
            },
            {
                "name": "elements_collector_based",
                "elements_collect_model": "after_row_match",
                "elements_collect_config": {
                    "row_pattern": [r"B3.2"],
                    "para_pattern": [
                        r"(?P<content>no training)",
                    ],
                },
                "paragraph_model": "para_match",
                "para_config": {
                    # 'content_pattern': True,
                    "paragraph_pattern": (
                        r"(?P<content>During the reporting period.*?training)",
                        # r'(?P<content>B3.2|train|gender|employee category)',
                        r"(?P<content>no training)",
                    ),
                },
            },
            {
                "name": "after_row_match",
                "row_pattern": [r"B3.2"],
                "para_pattern": [
                    r"(?P<content>average training hours)",
                    r"(?P<content>no training)",
                    r"(?P<content>average.*?hours.*?training)",
                ],
            },
            {
                "name": "elements_collector_based",
                "elements_collect_model": "syllabus_elt_v2",
                "elements_collect_config": {
                    "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                    "include_shape": True,
                    "only_inject_features": True,
                    "inject_syllabus_features": [
                        r"__regex__^KPI B3.[12]",  # 60838
                        r"__regex__DEVELOPMENT AND TRAINING",  # 60879
                    ],
                },
                "shape_model": "shape_title",
                "shape_config": {
                    "regs": (
                        r"average training hours",
                        r"training hours completed",
                    ),
                },
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "inject_syllabus_features": [r"__regex__^KPI B3.2"],
            },
            {
                "name": "para_match",
                "syllabus_regs": [
                    r"Development and Training",
                    r"staff development",
                ],
                "paragraph_pattern": (
                    r"the Group provided.*?trainings hours to our employees",
                    r"arranged.*?hours training",
                    r"(?P<content>no training)",
                    r"(?P<content>average.*?hours.*?training)",
                ),
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [r"__regex__Development and Training"],
                "only_inject_features": True,
                "multi_elements": True,
                "paragraph_model": "empty",
                "table_model": "table_title",
                "table_config": {
                    "multi_elements": True,
                    "feature_white_list": [
                        r"Average training hours per employee by (Gender|Category)",
                    ],
                },
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [],
                "multi_elements": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        # r'(?P<content>B3.2|average training hours|employee category|gender)',
                        r"(?P<content>no training)",
                        r"(?P<content>average.*?hours.*?training)",
                    ),
                },
                "table_model": "empty",
            },
            DEFAULT_MODEL,
        ],
    },
]
