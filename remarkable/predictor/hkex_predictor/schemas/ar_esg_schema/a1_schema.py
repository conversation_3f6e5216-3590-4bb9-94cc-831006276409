"""
Jura4 Annual Report ESG
"""

from remarkable.predictor.hkex_predictor.schemas.ar_esg_schema.common_pattern import (
    COMPLY_LAW_PATTERN,
    DEFAULT_MODEL,
    EXPLAIN_PATTERN,
    LAW_COMMON_PATTERN,
    NEGLECT_PAGE_HEADER_PATTERN,
)

DIRECTORY_START = r"^\d+\.\d+.(\d+)?"  # 目录中的段落


predictor_options = [
    {
        # todo A1+A2:如果只提第一段或前两段可以符合大部分的情况
        # A3+A4 提取标题下的所有内容。这两个规则不涉及表格数据
        "path": ["A1 policies - emissions"],
        "models": [
            # 特例 待客户确认 https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/997#note_243518
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__^A\.\sENVIRONMENTAL$",
                ],
                "only_inject_features": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"the Group has policies for the reduction.*?use of resources",
                        r"The Group has established environmental policies",
                    ),
                },
                "table_model": "special_cells",
                "table_config": {
                    "whole_table": True,
                    "cell_pattern": (r"Environmental Policies",),
                },
            },
            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/997#note_243524
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__A\.\sENVIRONMENTAL__regex__A1:\sEmissions",
                    # 特例 GRI 相关 https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/997#note_243521
                    r"__regex__Emissions Management",
                ],
            },
            {
                "name": "para_match",
                "paragraph_pattern": (r"KPI A1\.1 to KPI A1\.6 are not applicable",),
            },
            {
                "name": "policy_row_match",
                "multi": True,
                "multi_elements": True,
                "row_pattern": ["^General Disclosure"],
                "anchor_pattern": ["Aspect A1"],
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN
                + [
                    # TODO: "During xxx"的描述到底要不要？
                    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/840#note_217387
                    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/840#note_217613
                    # 'during.*?(time|period|years|days|months|weeks)',
                ],
                "multi": True,
                "skip_child_syllabuses": False,
                "only_inject_features": True,
                "only_before_first_chapter": True,
                "only_first": True,
                "break_para_pattern": [
                    r"Overview of key performance indicators",
                ],
                "inject_syllabus_features": [
                    r"__regex__ENVIRONMENT__regex__Emissions__regex__\b(Air|gas|ghg|green\s?house)\b",
                    r"__regex__ENVIRONMENT__regex__OPERATION__regex__Emission",
                    r"__regex__ENVIRONMENT__regex__Emission",
                    r"__regex__ENVIRONMENT__regex__(Waste Management|Energy Management|Paper and Water Management)",
                ],
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_before_first_chapter": True,
                # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/803#note_212766
                "inject_syllabus_features": [
                    r"__regex__ENVIRONMENTAL.*?REPORT__regex__ENVIRONMENTAL__regex__Environmental Policies",
                    r"__regex__General Disclosure and KPIs",
                    r"__regex__A1.*?Emissions",
                    r"__regex__Air.*?Emissions",
                    r"__regex__gas.*?Emission",
                    r"__regex__ghg.*?Emission",
                    r"Emissions",
                ],
            },
            {
                "name": "para_match",
                # "multi_elements": True,
                # "enum_from_multi_element": True,
                "paragraph_pattern": (
                    r"(?P<content>not involve|not engage|not disclosed|not generate|no generation|not produce|not quantify)",
                    r"(?P<content>(?<!not )limit)",
                    r"(?P<content>not own.*?emissions)",
                    r"(?P<content>environment|policy|management|GHG|carbon dioxide|CO2|methane|CH4|nitrous oxide|N2O|ozone|O3)",
                    r"(?P<content>more environment-friendly fuel)",
                    r"carbon footprint|waste handling|air pollutant|air quality",
                ),
            },
            {
                "name": "policy_row_match",
                "multi": True,
                "multi_elements": True,
                "row_pattern": ["^General Disclosure"],
                "anchor_pattern": ["Aspect A1"],
            },
            {
                "name": "kmeans_classification",
            },
            DEFAULT_MODEL,
        ],
    },
    {
        #    1.**优先根据index去定位相关内容的位置去定位****是否遵守**的关键词框选其段落
        #    2.当不存在index的时候，先定位其**是否遵守**的关键词，在根据周围的描述判定属于哪一条规则的law
        #    （如：周围关键词为emission, pollutions, greenhouse gases, GHG等，则是A1law）
        #  涉及“明确表明没有相关政策和法律法规适用于公司”的描述。 比如no laws and regulations。E的情况一般较少出现
        "path": ["A1 law compliance - emissions"],
        # "location_threshold": 0.02,
        "models": [
            {
                "name": "row_match",
                "ignore_index_table": True,
                "row_pattern": [r"^Emissions"],
            },
            {
                "name": "row_match",
                "syllabus_regs": [
                    r"HKEx ESG Guide Content Index",
                ],
                "row_pattern": [
                    r"not aware of any.*?laws and regulations that may have.*?impact on.*?air and GHG",
                ],
            },
            {
                "name": "para_match",
                "neglect_syllabus_regs": [
                    r"Product Health and Safety",
                    r"Labou?r Standards?",
                    r"B[1-8]|A2|A3",
                ],
                "paragraph_pattern": (
                    r"KPI A1\.1 to KPI A1\.6 are not applicable",
                    r"no relevant law",
                ),
            },
            {
                "name": "para_match",
                "neglect_syllabus_regs": [
                    r"Product Health and Safety",
                    r"Labou?r Standards?",
                    r"B[1-8]|A2|A3",
                ],
                "paragraph_pattern": LAW_COMMON_PATTERN,
            },
            {
                "name": "twice_para_match",
                "multi_elements": True,
                "ignore_index_table": True,
                "syllabus_regs": [],
                "neglect_syllabus_regs": [
                    r"Product Health and Safety",
                    r"Labou?r Standards?",
                    r"B[1-8]|A2|A3",
                ],
                "paragraph_pattern": COMPLY_LAW_PATTERN,
                "second_pattern": (
                    r"relevant laws and regulations",
                    r"emission",
                    r"pollution",
                    r"greenhouse gas(es)?",
                    r"GHG",
                    r"Air Pollution Control Ordinance, ",
                ),
            },
            {
                "name": "syllabus_based",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__ENVIRONMENTAL__regex__A1.*?Emissions",
                    r"__regex__Exhaust Management",
                    r"__regex__^Emissions Management",
                ],
                "multi_elements": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"(?P<content>law|ordinance|regulation|legislation)",
                        r"strictly abides by all relevant laws and regulations",
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "para_match",
                "neglect_syllabus_regs": [
                    r"Product Health and Safety",
                    r"Labou?r Standards?",
                    r"B[1-8]|A2|A3",
                ],
                "paragraph_pattern": (
                    # r'(?P<content>law|ordinance|regulation|legislation)',
                    r"the Group did not identify any material non-compliance related to emissions",
                ),
            },
            {
                "name": "policy_row_match",
                "multi": True,
                "multi_elements": True,
                "row_pattern": ["^General Disclosure"],
                "anchor_pattern": ["Aspect A1"],
            },
            DEFAULT_MODEL,
        ],
    },
    {
        #  所在位置：1、优先index或者data summary表格中；
        #           2、其次文档其他位置，根据index给出对的KPI位置去定位框选；
        #           3、有关键词的段落、表格：`emission category`、`A1.1`
        #
        #  判断依据：1、报告中没有相关披露但是有解释，比如`因为没有从事XXX活动导致大气污染物的排放较少`，或者出现关键词`not involve`、
        #           `not engage`、`not disclosed`、`not generate`、`no generation`、`not produce`，上述情况为E；
        #           2、有对应emission类型和数量的算Y；
        #           3、没有披露相应内容也没有解释，算ND
        #
        #  特殊情况：若遇到没有直接明确的表示出有emission类型时，Scope 1, Scope 2及 Scope 3 都算是 “types of emissions” 及
        #           “emissions data”，并且判断为Y
        "path": ["KPI A1.1 - emission type and data"],
        "neglect_table_cell_missing_crude_regs": [r"Energy Consumption"],
        "models": [
            {
                "name": "after_row_match",
                "row_pattern": [r"A1.1"],
                "middle_rows": True,
                "start_regs": [
                    r"Greenhouse Gas Emis",
                    r"GHG emissions",
                ],
                "end_regs": [r"Energy consumption"],
                "just_a_para": True,  # for special cases 61008
                "para_pattern": [
                    r"^溫室氣體排放概覽$",
                ],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__^Energy Conservation$",
                ],
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"Scope 1 carbon emissions refers to all fuels used directly by our companies",
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "syllabus_elt_v2",
                "multi": True,
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "inject_syllabus_features": [
                    # https://hkex.test.paodingai.com/#/hkex/esg-report-checking/report-review/203363?fileId=64701&schemaId=1&rule=KPI%20A1.1%20-%20emission%20type%20and%20data
                    r"__regex__^Pollutants Emissions$",
                    r"__regex__^Greenhouse Gas Emissions$",
                ],
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"KPI A1\.1 to KPI A1\.6 are not applicable",
                    r"not directly produce",
                ),
            },
            {
                "name": "shape_title",
                "regs": (r"Greenhouse gas emissions",),
            },
            {
                "name": "after_row_match",
                "first_cell": False,
                "middle_rows": True,
                "row_pattern": [r"A1.1"],
                "table_title": [
                    r"Gas Emission",
                    r"quantitative information on GHG emissions",
                ],
                "start_regs": [r"Greenhouse Gas Emis"],
                "end_regs": [],
                "direct_answer_pattern": [r"not applicable"],
                "just_a_para": True,  # for special cases 61008
                "para_pattern": [
                    r"^溫室氣體排放概覽$",
                ],
            },
            {
                "name": "row_match",
                "multi": True,
                "ignore_index_table": True,
                "row_pattern": [
                    r"NOx|SOx|Particles|Nitrogen Oxide|Sulphur Oxide",
                    r"Particular|Particulate",  # r'Particular matter', r'PM',
                    r"Air pollutants",
                    r"Air emissions",
                    r"oxides|particles",
                    r"^PM$",  # r'Particular matter', r'PM',
                    r"PM.*?物",  # r'Particular matter', r'PM',
                    r"Sulphur dioxide",  # r'Particular matter', r'PM',
                    r"–\s?(office|projects)",  # r'Particular matter', r'PM',
                ],
            },
            {
                # 汇总表中的某几行 本篇同样没有说emission有什么分类，所以框GHG所在表格即可，并且应该选Y，
                # 而在data summary表格中，只要Greenhouse gas emission这部分表格 60943 60939
                "name": "row_match",
                "ignore_index_table": True,
                "multi": True,
                "row_pattern": [
                    r"GHG|Scope\s?[123]",
                    r"A1.1",
                ],
            },
            {
                "name": "table_title",
                "feature_white_list": [
                    r"GHG Emissions",
                    r"Waste Gas Generation and Management",
                    r"air emission",
                ],
            },
            {
                "name": "para_match",
                # "multi_elements": True,
                "paragraph_pattern": (
                    r"not? (directly )?(involve|engage|disclosed|generate|generation|produce)",
                    r"limited|not quantify",
                    r"A1.1|emission|category|emission",
                ),
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "inject_syllabus_features": [
                    r"A1 Emissions & A2 Use of Resources",
                ],
            },
            DEFAULT_MODEL,
        ],
    },
    {
        "path": ["KPI A1.2 part 1 - Scope 1"],
        "neglect_table_cell_missing_crude_regs": [
            r"Energy Consumption",
            # https://hkex.test.paodingai.com/#/hkex/esg-report-checking/report-review/203403?fileId=64579&schemaId=1&rule=KPI%20A1.2%20part%201%20-%20Scope%201
            r"For the year ended 30 June",
        ],
        "models": [
            {
                "name": "ar_esg_multi_table",
                "title_regs": [
                    r"SUMMARY\s?OF ENVIRONMENTAL PERFORMANCE",
                    r"^7. Significant increase.*?dded cinemas in the reporting scope",
                ],
                "row_regs": [r"Scope 1"],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__^Energy Conservation$",
                ],
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"Scope 1 carbon emissions refers to all fuels used directly by our companies",
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"KPI A1\.1 to KPI A1\.6 are not applicable",
                    r"did not consume resources.*?Scope 1 emission",
                    r"relating to air emissions and scope 1 ",
                ),
            },
            {
                "name": "split_first_table",
                "regs": [
                    r"Scope 1",
                    r"GHG emissions",
                    r"Direct emission",
                    r"Greenhouse gases",
                ],
                "title_regs": [r"TABLE OF KEY PERFORMANCE"],
                "start_regs": [
                    r"Scope 1",
                    r"Direct GHG emission",
                    r"Direct emission",
                    r"Total emission of greenhouse gas",
                ],
                "end_regs": [
                    r"Scope 2",
                    r"A1\.3",
                    r"Indirect GHG emissions",
                    r"hazardous waste",
                    r"indirect emission",
                ],
                "neglect_regs": [
                    r"NOx|SOx|Particles|Nitrogen Oxide|Sulphur Oxide",
                    r"PM direct emission",
                ],
            },
            {
                "name": "row_match",
                "multi": True,
                "ignore_index_table": True,
                "first_cell": False,
                "row_pattern": [
                    r"Scope 1",
                    r"Scope I(?!I)",
                    r"(?<!in)Direct.*?emissions",
                    # r'^GHG emission',
                    # r'Aspects 1.1|Nitrogen Oxides|Respiratory Suspended Particles' # #todo 60877 待修复
                ],
            },
            {
                "name": "first_table",
                "regs": [
                    r"Scope 1",
                ],
            },
            {
                "name": "scope",
                "regs": [r"Scope 1"],
                "start_regs": [
                    r"^Scope 1",
                ],
                "end_regs": [
                    r"^Scope 2",
                ],
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"not involve|not engage|not disclosed|not generate|no generation|not produce|not quantify",
                    r"carbon dioxide|methane|nitrous oxide|hydrofluorocarbons|perfluorocarbons and sulphur hexafluoride",
                    r"A1.2|direct emissions",
                    r"not to compile.*?scope\s?1",
                    # r'(?P<content>direct|reenhouse gas emission)',
                ),
            },
        ],
    },
    {
        "path": ["KPI A1.2 part 2 - Scope 2"],
        "location_threshold": 0.01,
        "neglect_table_cell_missing_crude_regs": [r"Energy Consumption"],
        "models": [
            {
                "name": "ar_esg_multi_table",
                "title_regs": [
                    r"SUMMARY\s?OF ENVIRONMENTAL PERFORMANCE",
                    r"^7. Significant increase.*?dded cinemas in the reporting scope",
                ],
                "row_regs": [r"Scope 2"],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__^Greenhouse Gas Emissions$",
                    r"__regex__^Energy Conservation$",
                ],
                "multi_elements": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"scope (1|2|3)",
                        r"contributed to the emissions of 23.1",
                        r"Scope 1 carbon emissions refers to all fuels used directly by our companies",
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "para_match",
                "paragraph_pattern": (r"KPI A1\.1 to KPI A1\.6 are not applicable"),
            },
            {
                "name": "split_first_table",
                "regs": [
                    r"Scope 2",
                    r"GHG emissions",
                    r"Indirect emissions",
                ],
                "title_regs": [r"TABLE OF KEY PERFORMANCE"],
                "start_regs": [
                    r"Scope 2",
                    r"Indirect GHG emissions",
                    r"Indirect emissions",
                ],
                "end_regs": [
                    r"Scope 3",
                    r"^Total",
                    r"A1\.3",
                ],
            },
            {
                "name": "row_match",
                "ignore_index_table": True,
                "first_cell": False,
                "multi": True,
                "row_pattern": [
                    r"Scope 2",
                    r"Scope II",
                    r"Indirect.*?emissions?",
                    r"^–\s(Hepu Plantation|prc office|hong kong office)",
                ],
            },
            {
                "name": "first_table",
                "regs": [r"Scope 2"],
            },
            {
                "name": "shape_title",
                "regs": (r"The (GHG|greenhouse gas) emission data is set out in the table below",),
            },
            {
                "name": "scope",
                "regs": [r"Scope 2"],
                "start_regs": [
                    r"^Scope 2",
                ],
                "end_regs": [
                    r"^Scope 3",
                ],
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"not involve|not engage|not disclosed|not generate|no generation|not produce|limited|not quantify",
                    r"carbon dioxide|methane|nitrous oxide|hydrofluorocarbons|perfluorocarbons and sulphur hexafluoride",
                    r"scope2",
                    # r'A1.2|scope2|greenhouse gas emission|energy',
                ),
            },
            # DEFAULT_MODEL,
        ],
    },
    {
        "path": ["KPI A1.2 part 3 - Scope 3"],
        "neglect_table_cell_missing_crude_regs": [r"Energy Consumption"],
        "models": [
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"KPI A1\.1 to KPI A1\.6 are not applicable",
                    r"The only source of GHG emission is Scope 2 GHG emissions",  # only Scope 2, 说明没有 Scope 3
                ),
            },
            {
                "name": "scope",
                "regs": [
                    r"Scope 3",
                ],
                "start_regs": [
                    r"^Scope 3",
                ],
                "end_regs": [
                    r"^Scope 3",
                    r"A1\.3",
                ],
            },
            {
                "name": "split_first_table",
                "regs": [
                    r"Scope 3",
                    # r'GHG emissions',
                ],
                "title_regs": [r"TABLE OF KEY PERFORMANCE"],
                "start_regs": [
                    r"Scope 3",
                ],
                "end_regs": [
                    r"total",
                    r"Scope 3",
                    r"A1\.3",
                ],
            },
            {
                "name": "row_match",
                "ignore_index_table": True,
                "first_cell": False,
                "multi": True,
                "row_pattern": [
                    r"Scope 3",
                    # r'Aspects 1.1|Nitrogen Oxides|Respiratory Suspended Particles' # #todo 60877 待修复
                ],
            },
            {
                "name": "first_table",
                "regs": [r"Scope 3"],
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"(?P<content>not involve|not engage|not disclosed|no generation|not produce|not quantify)",
                    r"(?P<content>carbon dioxide|methane|nitrous oxide|hydrofluorocarbons|perfluorocarbons and sulphur hexafluoride)",
                    r"scope3",
                    r"other indirect emissions",
                ),
                "neglect_pattern": (
                    r"scope [12]",
                    DIRECTORY_START,
                ),
            },
            # DEFAULT_MODEL,
        ],
    },
    {
        "path": ["KPI A1.3 - hazardous waste"],
        "models": [
            {
                "name": "ar_esg_multi_table",
                "title_regs": [
                    r"SUMMARY\s?OF ENVIRONMENTAL PERFORMANCE",
                    r"^7. Significant increase.*?dded cinemas in the reporting scope",
                ],
                "start_regs": [r"A1.3"],
                "end_regs": [
                    r"A1.4",
                ],
            },
            {
                "name": "after_row_match",
                "middle_rows": True,
                "row_pattern": [r"A1.3"],
                "start_regs": [r"hazardous wastes?"],
                "end_regs": [
                    # r'hazardous wastes?',
                    r"non-hazardous wastes?",
                ],
                "para_pattern": [
                    r"A1.3|(?<!non-)hazardous wastes?",
                    r"not generate.*?hazardous wastes?",
                ],
                "direct_answer_pattern": [
                    r"not applicable",
                    r"No material.*?hazardous waste",
                ],
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"KPI A1\.1 to KPI A1\.6 are not applicable",
                    r"KPI A1.3 are not applicable",
                    r"A1\.3.*?(not been|is not)\s?disclosed",
                    r"(?<!Non-)Hazardous waste.*?not include",
                    r"not generate.*?hazardous waste",
                    r"^(?!In ).*no (significant)?hazardous wastes? (is|was|are|were|or .* (is|was|are|were)) (generated|produced|recorded)",
                    r"hazardous waste generation was insignificant",
                    r"no significant hazardous wastes? (generated|produced)",
                    r"not produce (significant amounts of|any|any \w+) hazardous (wastes?)?",
                    r"Regarding hazardous wastes?.*not (produce|generated)",
                ),
            },
            {
                "name": "split_first_table",  # 定位第一个表格的
                "regs": [
                    r"A1.3",
                    r"(?<!non-)hazardous waste",
                ],
                "start_regs": [r"(?<!non-)hazardous wastes?"],
                "end_regs": [r"non-hazardous wastes?"],
            },
            {
                "name": "row_match",
                "ignore_index_table": True,
                "row_pattern": [
                    r"(?<!non-)hazardous wastes?",
                ],
            },
            {
                "name": "table_title",
                "feature_black_list": [
                    r"__regex__^.$",
                    r"__regex__^(Environment[al,]*)$",
                    r"__regex__Emissions$",
                    r"__regex__環境",
                ],
                "feature_white_list": [
                    r"(?<!non-)hazardous wastes?",
                    r"有害廢棄物排放",
                ],
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"not involve|not engage|not disclosed|not generate|no generation|not produce|not quantify",
                    r"no hazardous waste is generated",
                    r"not produce any hazardous waste",
                    r"(?<!non-)hazardous waste",
                ),
            },
            {
                "name": "score_filter",
                "threshold": 0.618,
            },
        ],
    },
    {
        # A1.4 产生的无害废物总量（以吨为单位）以及在适当情况下的强度（例如，每单位产量、每项设施）。
        # 整个rule的关键词是non-hazardous waste
        #  所在位置：1、优先index或者data summary表格中；
        #           2、其次文档其他位置，根据index给出对的KPI位置去定位框选；
        #           3、有关键词的段落、表格：`non-hazardous waste`、`A1.4`
        #
        #  判断依据：1、报告中没有相关披露但是有解释，解释内容为“产生的无害废弃物对环境的影响小”或“not available 没有产生无害废弃
        #           物”或“由第三方公司处理相关waste，本公司无数据”，判断为E；
        #           2、有non-hazardous waste对应的数据披露判断为Y；
        #           3、没有披露相关内容也没有解释，算ND
        #
        #  特殊情况：1、当报告中non-hazardous waste明确表述出paper或者其他材料，但是报告中没有non-hazardous相关数据表格或者未E的描述
        #           时，在 emission的相关表格中有关于paper consumption的数据，可以提取再A1.4中，并判断为Y；
        #           2、当没有明确指出hazardous waste和non-hazardous waste的类型，仅讲了waste时，视为non-hazardous waste
        "path": ["KPI A1.4 - non-hazardous waste"],
        "models": [
            {
                "name": "ar_esg_multi_table",
                "title_regs": [
                    r"SUMMARY\s?OF ENVIRONMENTAL PERFORMANCE",
                    r"^7. Significant increase.*?dded cinemas in the reporting scope",
                ],
                "start_regs": [r"A1.4"],
                "end_regs": [
                    r"A2.1",
                ],
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"KPI A1\.1 to KPI A1\.6 are not applicable",
                    r"A1\.4.*?not been disclosed",
                    r"non-hazardous waste generated by our Group ",
                    r"Group generates no hazardous waste",
                    r"the Group will expand its data .*?non-hazardous waste.*?future",
                    r"amount of waste are trivial and no KPI are identified and disclosed",
                    r"non-hazardous.*?wastes.*?negligible",
                    r"expand its data.*?non-hazardous waste.*?future",
                    r"no KPI are identified and disclosed",
                    r"waste are trivial",
                    r"non-hazardous waste produced was [\d\.]+ tonnes",
                ),
            },
            {
                "name": "table_title",
                "only_inject_features": True,
                "feature_white_list": [
                    r"non-hazardous waste (was )?generate",
                    r"generate non-hazardous waste",
                    r"non-hazardous waste production performance",
                ],
            },
            {
                "name": "after_row_match",
                "row_pattern": [r"A\s?1\s?.4"],
                "para_pattern": [
                    r"For non-hazardous.*?handling by qualified agencies",
                ],
                "table_title": [
                    r"non-hazardous",
                ],
                "middle_rows": True,
                "start_regs": [
                    r"non-hazardous",
                ],
                "end_regs": [
                    # r'non-hazardous',
                    r"energy consumption",
                ],
            },
            {
                "name": "middle_rows",
                "regs": [
                    r"A1.4",
                    r"hazardous waste",
                    r"C&D.*?Waste",
                ],
                "start_regs": [
                    r"non-\s?hazardous waste",
                    r"C&D.*?Waste",
                ],
                "end_regs": [
                    r"Use of Resources",
                    r"energy consumption",
                    r"Exhaust Gas Emissions",
                    r"total hazardous waste",
                ],
            },
            {
                "name": "table_title",
                "only_inject_features": True,
                "feature_white_list": [
                    r"non-hazardous waste",
                    r"無害廢棄物表現如下",
                    r"Food Waste Reduction",
                ],
                "feature_black_list": [
                    r"__regex__^.$",
                    r"__regex__^(Environment[al,]*)$",
                ],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__A1.4",
                    r"__regex__non-hazardous waste",
                    r"__regex__^Wastes$",
                    r"__regex__Wastes? management",
                ],
                "only_inject_features": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        EXPLAIN_PATTERN,
                        r"insignificant",
                        r"non-hazardous wastes generated",
                        r"waste collector approved by the Environmental Protection Department",
                        r"no plan to set a goal to reduce non-hazardous waste",
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "para_match",
                "multi_elements": True,
                "enum_from_multi_element": True,
                "paragraph_pattern": (
                    r"(?P<content>non-hazardous waste)",
                    r"(?P<content>A1.4)",
                    r"(?P<content>During.*?the total construction waste disposed.*?respectively)",
                    r"amount of waste are trivial",
                ),
            },
            DEFAULT_MODEL,
        ],
    },
    {
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/811#note_214005
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/808#note_213626
        # add a special model for 1.5
        "path": ["KPI A1.5 - emission target"],
        "models": [
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"KPI A1\.1 to KPI A1\.6 are not applicable",
                    r"A1\.5.*?not been disclosed",
                ),
            },
            {
                "name": "table_title",
                "feature_white_list": [r"steps taken to achieve the target"],
                "only_inject_features": True,
                "first_row_as_title": True,
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                # "multi": True,
                "inject_syllabus_features": [
                    r"__regex__environmental protection__regex__target",
                    r"__regex__environmental policy__regex__Our Environmental Targets",
                    # r'__regex__environmental__regex__target',
                ],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__^A\.\s?environmental",
                    r"__regex__A1.*?Emissions",
                    r"Greenhouse Gas Emission and Energy Efficiency",
                ],
                # 'multi_elements': True,
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "as_follow_pattern": [
                    r"following.*?emission.*?measures.*?:$",
                    r"following.*?measures.*?emission.*?:$",
                ],
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"not set any environmental targets",
                        "(target|aim) is",  # uat 58245
                        "GHG emission and energy use targets",
                        r"following.*?emission.*?measures.*?:$",
                        r"following.*?measures.*?emission.*?:$",
                        r"in addition to the above measures",
                    ),
                    "neglect_pattern": (),
                },
                "table_model": "first_table",
                "table_config": {"regs": [r"base year performance"]},  # uat file 64382
            },
            {
                "name": "row_match",
                "first_cell": False,
                "ignore_index_table": True,
                "force_use_all_elements": True,
                "row_pattern": [
                    r"KPI\s?A1\.5",
                ],
            },
            {
                "name": "kmeans_classification",
                "remedy_low_score_element": True,
                "neglect_remedy_pattern": [
                    r"The table below highlights",
                    r"下表列示",
                ],
            },
            {
                "name": "para_match",
                "multi_elements": True,
                "enum_from_multi_element": True,
                "paragraph_pattern": (
                    r"(?P<content>not involve|not engage|not disclosed|not generate|no generation|not produce|limited|not quantify)",
                    r"(?P<content>immaterial|insignificant|plan|continue)",
                    r"(?P<content>target|aim|future plan|reduce|minimize)",
                    r"(?P<content>not undertake)",
                ),
            },
            DEFAULT_MODEL,
        ],
    },
    {
        "path": ["KPI A1.6 part 1 - waste handling"],
        "models": [
            {
                # for explain
                "name": "para_match",
                "paragraph_pattern": (
                    r"KPI A1\.1 to KPI A1\.6 are not applicable",
                    r"Due to the business nature of our Group, certain construction waste is generated in our construction process",
                    r"A1\.6.*?not been disclosed",
                ),
            },
            {
                # for comply
                "name": "para_match",
                "paragraph_pattern": (r"striving to classify the recyclable waste",),
            },
            {
                "name": "after_row_match",
                "row_pattern": [r"A1.6"],
                "multi_elements": True,
                "para_pattern": [
                    r"(?P<content>not involve|not engage|not disclosed|not generate|no generation|not produce|limited|not quantify)",
                    r"(?P<content>immaterial|insignificant|plan|continue)",
                    r"(?P<content>dispose|recycle|reuse|recover|recycling)",
                    r"(?P<content>managed centrally by the office property management company)",
                    r"(?P<content>not undertake)",
                ],
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__Non-hazardous Waste Management",
                    r"__regex__Waste.*?Management",
                    r"__regex__Non-hazardous wastes",
                    r"__regex__Non-hazardous waste emissions",
                ],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"waste managemen",
                ],
                "multi_elements": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"(?P<content>not involve|not engage|not disclosed|not generate|no generation|not produce|limited|not quantify)",
                        r"(?P<content>immaterial|insignificant|plan|continue)",
                        r"(?P<content>dispose|recycle|reuse|recover|recycling)",
                        r"(?P<content>managed centrally by the office property management company)",
                        r"(?P<content>not undertake)",
                        EXPLAIN_PATTERN,
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"(?P<content>not involve|not engage|not disclosed|not generate|no generation|not produce|limited|not quantify)",
                    r"(?P<content>immaterial|insignificant|plan|continue)",
                    r"(?P<content>dispose|recycle|reuse|recover|recycling)",
                    r"(?P<content>managed centrally by the office property management company)",
                    r"(?P<content>not undertake)",
                ),
            },
            {
                "name": "kmeans_classification",
            },
            DEFAULT_MODEL,
        ],
    },
    {
        # 该规则是框选出公司为排放固体废物而设置的排放目标，或者为了减少产生废物而采取的措施；
        # 如果都没有也可以框选已经实现的减排目标，可以不是对未来目标的设置
        #  所在位置：1. 涉及到waste management的标题下关于waste reduction target的文字段落
        #           2. 在waste management的标题下有关键词waste reduction的小标题下的内容  优先级靠前
        #           3. 如果按照non-hazardous waste和hazardous waste披露的，一般是两处都会涉及到 reduction waste的内容
        #           4. 在ESG报告中单独有个environmental target的标题下，关键词waste的相关内容
        #
        #  提取内容：1. 提取涉及到“waste reduction target”的数据
        #           2. 没数据但有“为了reduce waste 而有一些措施”的描述
        #           3. 当年或目前为止已经实现的reduction，可以不是未来的target
        #           4. 如果上面几点都有，则可以按照上面序号的优先级来全部提取
        #
        #  关键词：（需要在waste相关位置范围内查找）target, aim, future plan, minimize, reduction, reduce, reuse, recycle, prevent
        #
        #  判定方式：Y-披露并描述以上的提取内容（满足提取内容的任何一条或多条即可）
        #           E-明确披露了 not material/not involve/not engage/ not disclosed/ not produce的
        #           E-写明了`没有waste target`的，即使有相关措施，也判定为E
        #           ND-没有任何描述
        "path": ["KPI A1.6 part 2 - waste reduction target"],
        "models": [
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"KPI A1\.1 to KPI A1\.6 are not applicable",
                    r"A1\.6.*?not been disclosed",
                ),
            },
            {
                "name": "para_match",
                "anchor_regs": (r"waste management",),
                "current_regs": (r"to strengthen education and propaganda related to waste reduction",),
            },
            {
                "name": "row_match",
                "row_pattern": [r"^waste (reduction|management)"],
            },
            {
                "name": "para_match",
                "multi_elements": True,
                "paragraph_pattern": (
                    r"the Company does not have a reduction policy that may apply to the disposal of soil",
                    r"—Maintaining.?[(]or reducing[)].*?waste",
                    r"—Recycling.*?waste",
                    r"dedicated to proper management of the non-hazardous waste",
                    r"targets to reduce.*?waste",
                ),
            },
            # only for explain answer
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    # r'__regex__^A\.\s?environmental',
                    r"__regex__green office",
                ],
                # 'multi_elements': True,
                "only_inject_features": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"not set any environmental targets",
                        r"A1.6.*?not applicable",
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__A1.6: Wastes Reduction and Initiatives",
                    r"__regex__A1.6 Handling of Hazardous and Non-hazardous Waste",
                    r"__regex__waste reduction measures",
                    # r'__regex__Non-hazardous Waste Management',
                    r"__regex__Waste Generation and Management",
                    r"__regex__Generation of Waste",
                ],
            },
            {
                "name": "after_row_match",
                "row_pattern": [r"A1.6"],
                "para_pattern": [
                    r"aim.*?(to|at).*?reduc.*?waste",
                    r"be recycled",
                    r"be reused",
                    r"measures.*?handle wastes:",
                    r"measures.*?following:$",
                    r"following measures for reducing waste:$",
                    r"has set a target",
                    r"set.*?reduction target",
                    r"set target to reduce waste",
                    r"minimisation of waste generation",
                    r"reuse of materials",
                    r"recovery and recycling",
                    r"printed when needed",
                    r"reduction initiatives",
                    r"Use recyclable products",
                    r"Recycle office paper",
                    r"Print.*?when necessary|print on both",
                    r"Provide reusable",
                    r"waste reduction measures",
                    r"waste disposed",
                    r"no specific reduction target",
                    r"targets? (is )?to reduce",
                    r"targeted a reduction",
                    r"initiatives to reduce",
                    r"measures.*?reduce.*?waste disposal",
                    r"managed centrally by the office property management company",
                    r"dedicated to proper management of the non-hazardous waste",
                    r"(?P<content>not undertake)",
                ],
                "multi_elements": True,
                "direct_answer_pattern": [
                    r"No.*?target is set",
                    r"not set.*?targets",
                ],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__A1\.6",
                    r"__regex__Waste Reduction",
                    r"__regex__Waste Handling",
                    r"__regex__wastes? managemen",
                    r"__regex__reduction Initiatives",
                    r"__regex__Handling Initiatives",
                    r"__regex__Hazardous waste emissions",
                    r"__regex__EMISSION REDUCTION",
                    r"__regex__^\s.*?wastes$",
                ],
                "multi_elements": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"aim.*?(to|at).*?reduc.*?waste",
                        r"be recycled",
                        r"be reused",
                        r"measures.*?handle wastes:",
                        r"measures.*?following:$",
                        r"following measures for reducing waste:$",
                        r"has set a target",
                        r"set.*?reduction target",
                        r"set target to reduce waste",
                        r"minimisation of waste generation",
                        r"reuse of materials",
                        r"recovery and recycling",
                        r"printed when needed",
                        r"reduction initiatives",
                        r"Use recyclable products",
                        r"Recycle office paper",
                        r"Print.*?when necessary|print on both",
                        r"Provide reusable",
                        r"waste reduction measures",
                        r"waste disposed",
                        r"no specific reduction target",
                        r"targets? (is )?to reduce",
                        r"targeted a reduction",
                        r"initiatives to reduce",
                        r"measures.*?reduce.*?waste disposal",
                        r"managed centrally by the office property management company",
                        r"dedicated to proper management of the non-hazardous waste",
                        r"adopted the following practices to reduce the consumption",
                        r"implementation of the measures.*?reduc",
                        r"implementation of the measures.*?reduc",
                        r"not undertake",
                        r"objective is to.*?reduction",
                        r"adopted.*?measures.*?to reduce",
                        r"to further reduce overall waste generation",
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"aim.*?(to|at).*?reduc.*?waste",
                    r"be recycled",
                    r"be reused",
                    r"measures.*?handle wastes:",
                    r"measures.*?following:$",
                    r"following measures for reducing waste:$",
                    r"has set a target",
                    r"set.*?reduction target",
                    r"set target to reduce waste",
                    r"minimisation of waste generation",
                    r"reuse of materials",
                    r"recovery and recycling",
                    r"printed when needed",
                    r"reduction initiatives",
                    r"Use recyclable products",
                    r"Recycle office paper",
                    r"Print.*?when necessary|print on both",
                    r"Provide reusable",
                    r"waste reduction measures",
                    r"waste disposed",
                    r"no specific reduction target",
                    r"targets? (is )?to reduce",
                    r"targeted a reduction",
                    r"initiatives to reduce",
                    r"measures.*?reduce.*?waste disposal",
                    r"managed centrally by the office property management company",
                    r"dedicated to proper management of the non-hazardous waste",
                    r"control.*?generation.*?non-hazardous waste at source",
                    r"recycle station for further recycle",
                    r"^•.*?reduce usage",
                    r"(?P<content>not involve|not engage|not disclosed|not generate|no generation|not produce|limited|not quantify)",
                    r"(?P<content>immaterial|insignificant|plan|continue)",
                    r"in order to|waste reduction",
                    r"(?P<content>not undertake)",
                ),
            },
            {
                "name": "kmeans_classification",
            },
            DEFAULT_MODEL,
        ],
    },
]
