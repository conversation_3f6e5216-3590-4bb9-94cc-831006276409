"""
Jura4 Annual Report ESG
"""

from remarkable.predictor.hkex_predictor.schemas.ar_esg_schema.common_pattern import (
    COMPLY_LAW_PATTERN,
    DEFAULT_MODEL,
    EXPLAIN_PATTERN,
    LAW_COMMON_PATTERN,
    NEGLECT_PAGE_HEADER_PATTERN,
)

B6_CHAPTER_PATTERN = [
    r"__regex__Product\s?Responsibility",
    r"__regex__B6",
    r"__regex__Responsible\s?Services",
]


predictor_options = [
    {
        #  一般企业产品责任很多无非都是保证服务质量，保证产品质量，所以如果不存在明显的有小标题的概括性文字，
        #  很多时候B6会包含B6.4
        #
        #  框选内容：**优先从index定位的位置去找以下内容**
        #  <有明显概括性文字的>，一般在product/service responsibly相关的大标题与小标题之间的文字段落。
        #  <不存在明显概括性文字的>，当不存在任何小标题时候，可以直接框选大标题段落第一段关于质量控制的文字段落，
        #  如果第一段文字也和质量要求无关，那么可以直接框B6.4的quality assurance
        #
        #  关键词：ordinance, Product Quality Law, Ordinance
        # （在这些关键词附近可同时关注Product Responsibility, responsible services, high quality services,
        # meet the expectation, satisfaction。product responsibility,
        # product quality, safety and health，responsible, service等）
        # 大标题 与下一个小标题之间 Product  Responsibility，
        "path": ["B6 policies - product responsibility"],
        "models": [
            # 特殊的badcase uat
            # https://hkex.test.paodingai.com/#/hkex/esg-report-checking/report-review/196200?fileId=58245&schemaId=1&rule=B4%20policies%20-%20labour%20standards
            {
                "name": "syllabus_based",
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__B3.*?Development and Training",
                    r"__regex__Responsible Service",
                ],
                "multi_elements": True,
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"as are not addressed in this ESG",
                        r"The Group took precautionary measures",
                    ),
                    "neglect_pattern": (),
                },
                "table_model": "special_cells",
                "table_config": {
                    "whole_table": True,
                    "cell_pattern": (r"Data Privacy"),
                },
            },
            {
                "name": "policy_row_match",
                "multi": True,
                "multi_elements": True,
                "row_pattern": ["^General Disclosure"],
                "anchor_pattern": ["Aspect B6"],
            },
            {
                # 只有第一段
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "only_first": True,
                "inject_syllabus_features": [
                    r"__regex__Product Responsibility__regex__General Disclosure",
                ],
            },
            {  # 特例
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "multi": True,
                "inject_syllabus_features": [
                    r"__regex__Product Quality Control",
                    r"__regex__service excellen",
                    r"__regex__Customer Health and Safety",
                    r"__regex__Data Protection and Privacy",
                ],
            },
            {
                # 章节标题和第一个小标题之间
                "name": "policy",
                # "only_before_first_chapter": True,
                # "only_first": True,
                "para_pattern": [
                    r"quality",
                    # r'Quality manage',
                    # r'service quality',
                    # r'Quality responsibility',
                ],
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "neglect_features": [
                    r"Labour standards",
                    r"community",
                    r"STAKEHOLDER ENGAGEMENT",
                    r"ESG Reporting Guide Content Index",
                    r"Supply Chain Management",
                    r"Protection of Intellectual Property Rights",
                ],
                "inject_syllabus_features": [
                    r"__regex__Quality Assurance",
                ]
                + B6_CHAPTER_PATTERN
                + [
                    r"__regex__PRODUCT RESPONSIBILITIES",
                    r"__regex__Aspect B6: Product Responsibility",
                    r"__regex__Product Quality Management",
                    r"__regex__Service pledge to our customer",
                ],
            },
            {
                # 不存在明显概括性文字的，可以框质量保证程序B6.4的段落，
                # 也可通过关键词定位质量保证内容段落，此篇如product liabilities，rectification， to ensure the  quality
                # 下面是B6.4的章节标题
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__Quality Control",
                    r"__regex__service excellen",
                    r"__regex__Product and Service Quality Management",
                ],
            },
            DEFAULT_MODEL,
        ],
    },
    {
        "path": ["B6 law compliance - product responsibility"],
        "models": [
            {
                "name": "para_match",
                "paragraph_pattern": LAW_COMMON_PATTERN,
                "neglect_syllabus_regs": [
                    r"Environmental Policy",
                    r"health and safety",
                    r"Supply chain management",
                    r"Anti-Corruption",
                    r"B[1-5]|B7|B8|A[1-3]",
                ],
            },
            {
                "name": "twice_para_match",
                "multi_elements": True,
                # "strict_limit": True,
                "syllabus_regs": B6_CHAPTER_PATTERN
                + [
                    r"HIGH QUALITY PRODUCTS",
                    r"Protection of Personal Data",
                ],
                "neglect_syllabus_regs": [
                    r"Environmental Policy",
                    r"health and safety",
                    r"Supply chain management",
                    r"Anti-Corruption",
                    r"B[1-5]|B7|B8|A[1-3]",
                ],
                "paragraph_pattern": COMPLY_LAW_PATTERN,
                "neglect_pattern": (r"complies with the corresponding national standards",),
                "second_pattern": (
                    r"relevant laws and regulations",
                    r"Product Responsibility",
                    r"responsible services",
                    r"high quality services",
                    r"meet the expectation",
                    r"satisfaction",
                    r"product responsibility",
                    r"product quality",
                    r"safety and health",
                    r"responsible",
                    r"data.*?(Ordinance|law)",
                    r"Ordinance",
                    r"intellectual property rights",
                    r"with the aforementioned laws and regulations",
                    r"sales.*?properties",
                    r"responsibility or privacy",
                ),
            },
            {
                "name": "para_match",
                "multi_elements": True,
                "paragraph_pattern": (
                    r"responsible to comply with the requirements of the rules and regulations",
                    r"no material non-compliance.*data protection and privacy",
                    r"not involve in any confirmed violations of laws and regulations",
                    r"Product Eco-responsibility Ordinance",
                    r"Consumer Goods Safety Ordinance",
                    r"B6.*?are not the key material matters.*?are not addressed in this ESG report",
                ),
            },
            {
                "name": "kmeans_classification",
                "threshold": 0.1,
            },
        ],
    },
    {
        # 关于出于安全和健康原因而进行召回的已售或运输产品的数据的披露，可提取涉product recall的数据所在的图表或段落或句子，优先提取index，
        # 其次data summary，最后正文。
        # （关键词：recall，recovery，return）0或nil或no或not等否定描述是Y
        "path": ["KPI B6.1 - products recall"],
        "models": [
            {
                "name": "para_match",
                "syllabus_regs": [
                    r"product.*recall",
                    r"Healthy Gaming",
                    r"Product.*Respons",
                    r"Safe Meals",
                ],
                "paragraph_pattern": (
                    r"B6.*?are not the key material matters.*?are not addressed in this ESG report",
                    r"recall.*?reason",
                    r"recall.*product.*health and safety",
                    r"product recalls during the.*?Year",
                    r"In.*?period.*?product recall",
                    r"product.*?recall.*(?:concerns|reasons)",
                    r"no recall case",
                    r"not experience any material quality issues",
                    r"product labelling.*?not applicable",
                    r"\d.*?product returned",
                ),
            },
            {
                "name": "after_row_match",
                "first_cell": False,
                "row_pattern": [r"B6\.1"],
                "just_a_para": True,
                "multi_elements": True,
                "para_pattern": [r"recall|B6\.1|return"],
                "direct_answer_pattern": [
                    r"No product.*?recalls",
                    r"not applicable",
                    r"There were no recalls.*?products",
                ],
            },
            {
                "name": "row_match",
                "ignore_index_table": True,
                "first_cell": False,
                "syllabus_regs": [r"Governance Report"],
                "row_pattern": [
                    r"B6.1.*\d",
                    r"product.*?recall.*\d",
                    r"recall.*?product.*\d",
                ],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__Quality Assurance",
                ]
                + B6_CHAPTER_PATTERN,
                "multi_elements": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"B6.1",
                        r"not have( any)?.*?products.*?to recall",
                        r"no.*?products.*?to recall",
                        r"no recalled products",
                        r"no (goods|orders).*?to recalls for product quality",
                        # EXPLAIN_PATTERN,
                    ),
                    "neglect_pattern": (
                        r"so as to protect.*?any potential health and safety issue",
                        r"creating higher return",
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "twice_para_match",
                "strict_limit": True,
                "multi_elements": True,
                "syllabus_regs": [
                    r"product responsibility",
                ],
                "paragraph_pattern": (
                    r"recall|B6.1|return",
                    r"not have( any)?.*?products.*?to recall",
                    r"no.*?products.*?to recall",
                    r"no recalled products",
                    r"product returned",
                    # EXPLAIN_PATTERN,
                ),
                "neglect_pattern": (
                    r"so as to protect.*?any potential health and safety issue",
                    r"creating higher return",
                ),
                "second_pattern": (
                    EXPLAIN_PATTERN,
                    r"(had|was|were|is) (zero|\d+|no)",
                    r"not have (any)?",
                    r"not have( any)?.*?products.*?to recall",
                    r"no.*?products.*?to recall",
                    r"no recalled products",
                    r"product quality",
                ),
            },
            {
                "name": "score_filter",
                "threshold": 0.618,
            },
        ],
    },
    {
        # 收到的与产品和服务相关的投诉数量以及处理方式，优先提取index，其次data summary，最后正文。
        # （关键词：complaint, service-related complaints）0或nil或no或not等否定描述是Y。
        # 不仅要提取投诉数量，还要提取文中描述的处理投诉的方式措施。
        "path": ["KPI B6.2 - products related complaints"],
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/852#note_217847
        # "pick_answer_strategy": "all",
        "models": [
            {
                "name": "after_row_match",
                "first_cell": False,
                "row_pattern": [r"B6.2"],
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "multi_elements": True,
                "para_pattern": [
                    r"complaint handling |respond to complaint|Compliance Department",
                    r"no.*?complaints received",
                    r"not receive any material compliant.*?product quality",
                    r"not receive any compliant.*?product",
                    r"opinion.*?products",
                    r"\d+ complaint",
                ],
            },
            # 60943 https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/852#note_217847
            {
                "name": "row_match",
                "ignore_index_table": True,
                "row_pattern": [
                    r"B6.2",
                    r"Number of complaints",
                ],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__product\s?responsibility",
                    r"__regex__Service Quality",
                    r"__regex__(Product\s?Responsibility|Responsible\s?Services|Quality\s?Assurance)",
                ],
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "multi_elements": True,
                "multi": True,
                "multi_level": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"(?P<content>complaint|B6.2)",
                        r"complaint.*?handling |respond to complaint|Compliance Department",
                        r"no complaints received",
                        r"not receive any material compliant.*?product quality",
                        r"not receive any compliant.*?product",
                        r"opinion.*?products",
                        r"\d+ complaint",
                        EXPLAIN_PATTERN,
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__Handling of Customer Complaints",
                ],
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"(?P<content>complaint|B6.2)",
                    r"no complaints received",
                    r"not receive any material compliant.*?product quality",
                    r"not receive any compliant.*?product",
                    r"opinion.*?products",
                    r"\d+ complaint",
                    r"B6.*?are not the key material matters.*?are not addressed in this ESG report",
                    EXPLAIN_PATTERN,
                ),
            },
            # 兜底para_match没有匹配到的段落 阈值需要设置一个比较高的值
            {
                "name": "score_filter",
                "threshold": 0.618,
            },
        ],
    },
    {
        # B6.3：描述与遵守和保护知识产权有关的做法。
        # （关键词：intellectual property rights, intellectual property protection）
        "path": ["KPI B6.3 - IP rights protection"],
        "models": [
            {
                "name": "after_row_match",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "row_pattern": [r"B6.3"],
                "first_cell": False,
                "para_pattern": [r"intellectual property rights|intellectual property protection"],
                "direct_answer_pattern": [
                    r"not applicable",
                    r"The Group has established a Trademark Register which records the registration number",
                ],
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__(\d\.)*?\d\sIntellectual Property Rights$",
                    r"__regex__Intellectual Property.*?Rights$",
                ],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__Protection of Intellectual Property( Rights)?",
                    r"__regex__(Product\s?Responsibility|Responsible\s?Services)__regex__(Intellectual\s?Property\s?"
                    r"Rights|Intellectual\s?Property\s?Protection)",
                    r"__regex__intellectual\s?Property\s?Protection",
                    r"__regex__product\s?responsibility",
                    r"__regex__intellectual Property Rights",
                ],
                "multi_elements": True,
                "include_title": False,
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        # r'Copyright Ordinance', # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/852#note_218211
                        r"IP rights",
                        r"IP infringement",
                        r"intellectual property right",
                        r"intellectual property protection",
                        r"protect intellectual property and data and privacy",
                        r"protect.*?data and privacy",
                        r"protect.*?our data assets",
                        r"secures its intellectual property",
                        r"B6.3",
                        r"respects intellectual property",
                        r"Company Trademark|Intellectual Property",
                        # r'personal data.*?ordinance',  # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/852#note_218211
                        r"maintained our trademark portfolio",
                        r"prohibited to use the IP of the Group",
                        r"apply for the patents",
                        r"Patent Licensing Contract",
                        r"exercise the priority right to apply for the invalidity",
                        EXPLAIN_PATTERN,
                    ),
                    "neglect_pattern": (
                        r"not have physical products for sale",
                        r"laws.*?labelling",
                        r"ensure.*?complies.*?laws.*?intellectual property right",
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    # r'(?P<content>Copyright Ordinance)',# https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/852#note_218211
                    r"(?P<content>IP rights)",
                    r"(?P<content>intellectual property right)",
                    r"(?P<content>intellectual property protection)",
                    r"(?P<content>protect intellectual property and data and privacy)",
                    r"(?P<content>protect.*?data and privacy)",
                    r"(?P<content>protect.*?our data assets)",
                    r"(?P<content>secures its intellectual property)",
                    r"(?P<content>B6.3)",
                    r"(?P<content>respects intellectual property)",
                    r"(?P<content>Company Trademark|Intellectual Property)",
                    # r'(?P<content>personal data.*?ordinance)', # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/852#note_218211
                    r"(?P<content>maintained our trademark portfolio)",
                    r"(?P<content>prohibited to use the IP of the Group)",
                    r"B6.*?are not the key material matters.*?are not addressed in this ESG report",
                    EXPLAIN_PATTERN,
                ),
                "neglect_pattern": (
                    r"not have physical products for sale",
                    r"laws.*?labelling",
                    r"ensure.*?complies.*?laws.*?intellectual property right",
                ),
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__Intellectual Property Rights",
                ],
            },
            {
                "name": "row_match",
                "ignore_index_table": True,
                "row_pattern": [
                    r"B6.3",
                    r"Protection of\s?Intellectual Property Rights",
                ],
            },
            # DEFAULT_MODEL,
        ],
    },
    {
        # B6.4： 侧重于产品及服务质量保证的具体流程及方法措施。
        #  有的可能会具体到产品从原材料采购到产出的质量检测及要求的流程图。相对于B6policy的概括性文字会描述得具体很多，
        #  有的文章可能直接整段讲具体质量保证措施，无法明确区分**B6policy的概括性文字**和B6.4，那么B6和B6.4的内容也会重合
        #
        #  框选内容：**优先从index定位的位置去找以下内容**
        #  <当存在明显小标题quality assurance/control或者B6.4>时，直接框选该标题下所有内容，
        #  <当不存在明显小标题>或内容描述质控过程措施，优先框关键词关于质量控制的段落，如果没有则可以框选客户投诉complaint处理措施，
        #  产品召回recall处理措施
        #
        #  关键词quality, performance check, rectification, recall，compliant， B6.4, quality assurance
        "path": ["KPI B6.4 - quality assurance process"],
        "models": [
            # 章节全部 适用于有明显的小标题
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "break_para_pattern": [
                    r"Anti-corruption$",
                ],
                "inject_syllabus_features": [
                    r"__regex__B6.4",
                    r"__regex__Complaints Handling Procedures",
                    r"__regex__Quality Management",
                    r"__regex__PRODUCT AND SERVICE RESPONSIBILITY",
                    r"__regex__PRODUCT AND SERVICE QUALITY",
                    r"__regex__Product Quality Management and Control",
                    r"__regex__Quality Control and Product Warranty",
                    r"__regex__Quality Control",
                    r"__regex__Quality Assurance",
                    # r'__regex__PRODUCT RESPONSIBILIT',  # B6 大章节标题
                ],
            },
            {
                "name": "after_row_match",
                "row_pattern": [r"B6.4"],
                "para_pattern": [
                    r"quality|performance check|rectification|recall|compliant|B6.4",
                ],
            },
            # 仅仅第一段
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "only_first": True,
                "inject_syllabus_features": [
                    r"__regex__Product Responsibility__regex__KPI B6.4",
                ],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__Quality\s?and Safety\s?of\s?Services",
                    r"__regex__Quality Management",
                    r"__regex__PRODUCT RESPONSIBILIT",
                ],
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "multi_elements": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"quality|performance check|rectification|recall|compliant|B6.4",
                        r"advertisements are mainly based on word-of-mouth",
                        EXPLAIN_PATTERN,
                        r"Examples of|includes the following",
                        r"^\d+\.",
                        r"^•",
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"(?P<content>quality|performance check|rectification|recall|compliant|B6.4)",
                    r"B6.*?are not the key material matters.*?are not addressed in this ESG report",
                    EXPLAIN_PATTERN,
                ),
                "neglect_pattern": (r"(supplier|subcontractors).*?quality",),
            },
            DEFAULT_MODEL,
        ],
    },
    {
        # B6.5：描述消费者数据保护和隐私政策。
        # （关键词：privacy, personal data, confidentiality）；
        "path": ["KPI B6.5 - consumer data protection"],
        "models": [
            {
                "name": "after_row_match",
                "row_pattern": [r"B6.5"],
                "multi_elements": True,
                "para_pattern": [r"personal data|confidentiality|B6.5"],
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN
                + [
                    r"OPERATING PRACTICES – continued",
                ],
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__Customer Data Privacy",
                    r"__regex__CUSTOMER PRIVACY",
                    r"__regex__Customer Privacy Protection",
                    r"__regex__(Customer|Personal) Data protection",
                    r"__regex__Data Privacy protection",
                    r"__regex__Data Protection and Privacy",
                    r"__regex__information Security Management",
                    r"__regex__data protection and privacy policies",
                    r"__regex__Information Security and Privacy Protection",
                    r"__regex__Privacy Matters",
                    r"__regex__Protecting information security",
                    r"__regex__Privacy and Information Security",
                    r"__regex__Product Responsibility__regex__privacy",
                    r"__regex__Confidential Information",
                ],
                "break_para_pattern": [
                    r"Aspect B7: Anti-corruption",
                ],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__(Product\s?Responsibility|Responsible\s?Services)__regex__Privacy\s?Protection",
                    r"__regex__Data\s?Privacy\s?Protection",
                    r"__regex__B6.*?SERVICES RESPONSIBILITY",
                ],
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "multi_elements": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"data privacy|personal data|confidentiality|B6.5",
                        r"(?P<content>safeguard the information of the Group)",
                        r"(?P<content>collected only when it is necessary)",
                        EXPLAIN_PATTERN,
                    ),
                    "neglect_pattern": (r"had no non-compliance cases.*?on.*?data privacy",),
                },
                "table_model": "empty",
            },
            {
                "name": "para_match",
                "multi_elements": True,
                "paragraph_pattern": (
                    r"protect customers.*?privacy",
                    r"(?P<content>safeguard the information of the Group)",
                    r"(?P<content>collected only when it is necessary)",
                    r"contractual obligation to protect the information of clients",
                    r"B6.*?are not the key material matters.*?are not addressed in this ESG report",
                    r"ensure confidential information are properly kept",
                    EXPLAIN_PATTERN,
                ),
            },
            {
                "name": "row_match",
                "ignore_index_table": True,
                "row_pattern": [
                    r"Data Privacy",
                ],
            },
            {
                "name": "kmeans_classification",
                "threshold": 0.618,
            },
        ],
    },
]
