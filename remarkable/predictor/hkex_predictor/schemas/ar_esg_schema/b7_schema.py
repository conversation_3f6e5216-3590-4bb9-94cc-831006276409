"""
Jura4 Annual Report ESG
"""

from remarkable.predictor.hkex_predictor.schemas.ar_esg_schema.common_pattern import (
    COMPLY_LAW_PATTERN,
    DEFAULT_MODEL,
    LAW_COMMON_PATTERN,
    NEGLECT_PAGE_HEADER_PATTERN,
)

predictor_options = [
    {
        # B7-policy：关于anti-corruption政策的披露，可提取涉及到anti-corruption政策下的所有内容，一般在anti-corruption相关的标题下。
        # （关键词：anti-corruption，anti-money laundering）
        #       1. 情况一：在anti-corruption大标题及下一个小标题之间有内容，则建议框选两个标题中间这段内容；
        #
        #       2. 情况二：在anti-corruption大标题下没有任何一个小标题，则建议框选标题下第一段内容；
        #
        #       3. 情况三：在anti-corruption大标题及下一个小标题之间没有内容，则建议框选大标题下全部内容。
        "path": ["B7 policies - anti-corruption"],
        "models": [
            # 特例 GRI 相关 https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/997#note_243521
            {
                "name": "syllabus_elt_v2",
                "only_inject_features": True,
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "inject_syllabus_features": [
                    r"__regex__FOCUS 1: GOVERNANCE AND ETHICS__regex__Anti-Corruption",
                ],
            },
            {
                # 章节标题和第一个小标题之间
                "name": "policy",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "neglect_features": [
                    r"Supply Chain Management",
                ],
                "ignore_pattern_match": True,
                "inject_syllabus_features": [
                    r"__regex__ANTI-CORRUPTION POLICY AND WHISTLEBLOWER PROCEDURE",
                    r"__regex__Business Integrity, Anti-Corruption and Anti-Money Laundering",
                ],
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "only_first": True,
                "only_before_first_chapter": True,
                "inject_syllabus_features": [
                    r"__regex__Anti-Corruption__regex__KPI B7",
                ],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__Anti-Corruption",
                ],
                "multi_elements": True,
                "only_before_first_chapter": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"(?P<content>regards.*?laws)",
                        r"(?P<content>comply.*?laws)",
                        r"(?P<content>no corruption-related violations)",
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "neglect_features": [
                    r"Supply Chain Management",
                ],
                "inject_syllabus_features": [
                    r"__regex__Ethical Business",
                ],
                # 'only_before_first_chapter': True,
                # 'only_first': True,
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"anti-corruption policy",
                    r"(?P<content>regards.*?laws)",
                    r"avoidance of bribery and corruption",
                    r"Group has established policies on anti-corruption",
                    r"policy.*?(money[\-\s]laundering|anti[\-\s]corruption)",
                    r"comply.*?(money[\-\s]laundering|anti[\-\s]corruption)",
                ),
            },
            DEFAULT_MODEL,
        ],
    },
    {
        "path": ["B7 law compliance - anti-corruption"],
        "models": [
            {
                "name": "para_match",
                "paragraph_pattern": LAW_COMMON_PATTERN,
                "neglect_syllabus_regs": [
                    r"IMPROvE OuR vALuE CHAIN",
                    r"Product Health and Safety",
                    r"Labou?r Standards?",
                    r"Data Privacy",
                    r"B[1-6]|B8|A[1-3]",
                ],
            },
            {
                "name": "twice_para_match",
                "multi_elements": True,
                # "strict_limit": True,
                "syllabus_regs": [],
                "neglect_syllabus_regs": [
                    r"IMPROvE OuR vALuE CHAIN",
                    r"Product Health and Safety",
                    r"Labou?r Standards?",
                    r"Data Privacy",
                    r"B[1-6]|B8|A[1-3]",
                ],
                "paragraph_pattern": COMPLY_LAW_PATTERN,
                # 'neglect_pattern': (),
                "second_pattern": (
                    r"Anti-corruption",
                    r"Anti-money laundering",
                    r"Prohibition of Commercial Bribery",
                    r"bribery",
                ),
            },
            {
                "name": "para_match",
                "multi_elements": True,  # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/793#note_210675
                "strict_limit": True,  # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/793#note_210675
                "syllabus_regs": [r"ANTI-CORRUPTION"],
                "neglect_syllabus_regs": [
                    r"corporate governance report",
                    r"Product Health and Safety",
                    r"Labou?r Standards?",
                    r"Data Privacy",
                    r"B[1-6]|B8|A[1-3]",
                ],
                "paragraph_pattern": (
                    r"There was no legal cases regarding corrupt practices",
                    r"During the Period, there are no legal cases",
                    r"(?P<content>not aware.*?money laundering)",
                    r"(?P<content>strictly comply with.*?law)",
                    r"complied with relevant laws and regulations",
                    r"(law|regulation|Ordinance).*?(money|anti[-\s]|Bribery)",
                    r"(money|anti[-\s]|Bribery).*?(law|regulation|Ordinance)",
                    r"no.*?violation.*?corruption",
                    r"no legal case.*?corruption",
                ),
                "neglect_pattern": (r"^—.*?Ordinance",),
            },
            {
                "name": "kmeans_classification",
                "threshold": 0.1,
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__Integrity and Discipline",
                    r"__regex__Anti-Corruption",
                ],
                "multi_elements": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"(?P<content>not aware.*?money laundering)",
                        r"(?P<content>no corruption-related violations)",
                        r"no.*?in violation of.*?(laws|regulations).*?corruption lawsuits",
                        r"implemented the Whistleblowing Policy",
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "score_filter",
                "threshold": 0.618,
            },
        ],
    },
    {
        # 报告期内对发行人或其雇员提起的以身结的腐败行为法律案件数量，优先index，其次data summary，最后正文。
        # （关键词：bribery，legal，case，lawsuit，corruption charges）
        #       1. 0或nil或no或not等否定描述是Y；
        #       2. 当描述中没有涉及到case或lawsuit，仅有没有违反法规的相关描述（e.g. no non-compliance)，应判断为Y。
        "path": ["KPI B7.1 - legal cases on corruption"],
        "neglect_table_cell_missing_crude_regs": [r"Health and Safety"],
        "models": [
            {
                "name": "after_row_match",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "just_a_para": True,
                "include_row": True,
                "row_pattern": [r"B7.1"],
                "direct_answer_pattern": [
                    r"no (concluded )?legal case",
                ],
                "para_pattern": [
                    r"not involved.*?corruption",
                    r"not aware of any non-compliance with the relevant laws and regulations",
                    r"no (concluded )?legal case",
                    r"not aware of.*?cases",
                ],
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"no serious breach or non-compliance with relevant laws",
                    r"No reports or related complaints",
                    r"material impact on the Company in terms of anti-corruption",
                    r"not notify any material non-compliance with the relevant laws",
                    r"concluded legal case",
                    r"no legal case",
                    r"not identified.*?anti-bribery",
                    r"not have any legal.*?corruption",
                    r"no confirmed.*?legal case",
                    r"not aware of.*?cases",
                    r"have not involved.*?legal ",
                    r"No corruption charges",
                    r"no legal.*?corruption",
                    r"no reported case of corruption",
                    r"no.*?activities occurr",
                    r"not identified any.*?cases of",
                    r"During the Period, there are no legal cases",
                    r"not identify any material non-compliance cases",
                    r"had no non-compliance cases regarding violations",
                    r"subsidiaries experienced no litigation brought against",
                    r"involved in anti-corruption litigation cases",
                    r"no material non-compliance with the relevant laws",
                    r"nor violation of regulations related to corruption",
                    r"no corruption lawsuits",
                ),
            },
            {
                "name": "row_match",
                "ignore_index_table": True,
                "first_cell": True,
                "row_pattern": [
                    r"legal cases",
                    r"Number.*?corruption",
                ],
            },
            {
                "name": "ar_esg_71",
                "threshold": 0.1,
            },
        ],
    },
    {
        # 描述预防措施和举报程序，以及如何实施和监管。
        # （关键词：whistle-blowing, whistleblowing, misconduct, preventive, prevention, prevent）
        # reporting channels
        "path": ["KPI B7.2 - preventive measures & whistle-blowing procedures"],
        "models": [
            {
                "name": "para_as_index",
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__Complaint\s*?handling",
                    r"__regex__satisfaction\s*?and\s*?feedback",
                ],
                "index_patterns": [
                    r"grievance procedures?.*refer\s*?to.*section.*?[\"“\(\[\‘](?P<dst>.+?)[\"”’\)\]]",  # 01752|2022
                ],
                "para_patterns": [
                    r"grievance\s*?polic(y|ies)",
                ],
            },
            {
                "name": "para_match",
                "multi_elements": True,
                # "combine_paragraphs": True,
                "paragraph_pattern": (
                    r"To prevent corrupt practices ",
                    r"Whistle-?blowing Mechanism",
                    r"reporting channels",
                    r"whistle-blowing.*?In the event.*?investigation",
                ),
            },
            {
                "name": "syllabus_elt_v2",
                "only_inject_features": True,
                "multi": True,
                "inject_syllabus_features": [
                    r"__regex__Anti-Corruption__regex__KPI B7.2",
                    r"__regex__Customer Due Diligence",
                    r"__regex__Suspicious Transactions Reporting",
                    r"__regex__Gifts and benefits",
                    r"__regex__GOVERNANCE AND ETHICS__regex__Whistleblowing",
                ],
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
            },
            {
                "name": "after_row_match",
                "row_pattern": [r"B7.2"],
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "para_pattern": [
                    r"whistle.*?blowing|misconduct|B7.2",
                    r"(?P<content>not tolerate any.*?business activities)",
                    r"reporting channels",
                ],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__Customer Due Diligence",
                    r"__regex__ANTI-CORRUPTION POLICY AND WHISTLEBLOWER PROCEDURE",
                ],
                "multi_elements": True,
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"(?P<content>whistle.*?blowing|misconduct|B7.2)",
                        r"(?P<content>not tolerate any.*?business activities)",
                        r"encouraged to report on suspected business irregularities",
                        r"misconduct|prevention|preventive|prevent",
                        r"Whistle-?blowing",
                        r"reporting mechanism",
                        r"reporting channels",
                    ),
                },
                "table_model": "special_cells",
                "table_config": {
                    "cell_pattern": [
                        r"whistle-blowing|misconduct",
                        r"reporting channels",
                    ]
                },
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"(?P<content>whistle.*?blowing|misconduct|B7.2)",
                    r"(?P<content>not tolerate any.*?business activities)",
                    r"(?P<content>violate the company’s rules, we will directly dismiss the employee)",
                    r"reporting channels",
                ),
            },
            {
                "name": "kmeans_classification",
                "para_pattern": [
                    r"whistle.*?blowing|misconduct|B7.2",
                    r"(?P<content>not tolerate any.*?business activities)",
                    r"reporting channels",
                ],
            },
            DEFAULT_MODEL,
        ],
    },
    {
        # 描述向董事和员工提供的反腐败培训，提取涉及到anti-corruption training相关的段落，
        # 有时候会出现在B3的相关段落下，也可提取（关键词：anti-corruption，training，seminar
        # 没有明确表明提供相关training, 只是说会把相关的政策告知employee，不属于training相关，应判断为ND
        "path": ["KPI B7.3 - anti-corruption training"],
        "models": [
            {
                "name": "para_match",
                "syllabus_regs": [
                    r"anti.*?corruption$",  # 01752|2022
                ],
                "paragraph_pattern": (
                    r"anti.*?corruption.*?training",  # 01752|2022
                    r"training.*?anti.*?corruption",
                ),
            },
            {
                "name": "after_row_match",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "just_a_para": True,
                "row_pattern": [r"B7.3"],
                "para_pattern": [
                    r"anti.*?corruption.*?training|B7.3",
                    r"training",
                    r"(?<!terrorism )seminar",
                ],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__Anti-corruption",
                    r"__regex__Anti-corruption Training",
                    r"__regex__Anti-corruption and Integrity",
                    r"__regex__Prevention of bribery",
                    r"__regex__money laundering",
                    r"__regex__other misconduct",
                    r"__regex__Bribery",
                ],
                "multi_elements": True,
                "include_title": False,
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"anti.*?corruption.*?training|B7.3",
                        r"anti.*?corruption.*?(training|seminar)",
                        r"(training|seminar).*?anti.*?corruption",
                    ),
                },
                "table_model": "special_cells",
                "table_config": {
                    "cell_pattern": [
                        r"whistle-blowing|misconduct",
                    ]
                },
            },
            {
                "name": "para_match",
                "strict_limit": True,
                "syllabus_regs": [
                    r"ANTI-CORRUPTION",
                    r"Integrity",
                    r"Business Ethics",
                    r"Enhance Compliance Awareness",
                    r"__regex__money laundering",
                    r"__regex__other misconduct",
                    r"__regex__Bribery",
                ],
                "multi_elements": True,  # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/803#note_212983
                "paragraph_pattern": (
                    r"anti.*?corruption.*?training|B7.3",
                    r"training.*?anti.*?corruption",
                    r"training|seminar",
                    r"study.*?anti-corruption",
                    r"sign.*?Compliance.*?Commitment",
                    r"sent.*?anti-corruption.*?articles",
                ),
            },
            {
                "name": "row_match",
                "first_cell": True,
                "row_pattern": [
                    r"anti-corruption.*?training",
                    r"training.*?anti-corruption",
                ],
            },
        ],
    },
]
