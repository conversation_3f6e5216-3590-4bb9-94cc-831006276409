"""
Jura4 Annual Report ESG
"""

from remarkable.predictor.hkex_predictor.schemas.ar_esg_schema.common_pattern import (
    DEFAULT_MODEL,
    NEGLECT_PAGE_HEADER_PATTERN,
)

predictor_options = [
    {
        # A3&A3.1 这两个规则的内容接近较难区分，A3侧重于总结性的政策，A3.1侧重于实际行动。
        # 在实际提取中描述涉及的关键词类似较难分开，可以在这两处提取相同的内容，这两规则的提取和判断规则是一致的。
        # 提取内容：
        # 【涉及natural resources，environment 政策的段落】
        # 在有包含“natural resources+environment”标题时，
        #       可提取该标题下的全部内容及environment相关标题下reduce+impact或impact+environment的段落。
        # 在有index的情况下，去index指明的位置提取会更准确
        # 当没有“natural resources+environment”相关的标题时，
        #        提取A1POLICY+A2POLICY的内容,及environment相关标题下reduce+impact或impact+environment的段落
        # E的判断：
        # 【简单情况】：not disclosed,not available, NA,
        # 【复杂情况】：not+impact，little significance+impact， not involve, not produce等 ，且没有任何政策措施相关的描述
        # Y的判断：
        # 有：natural resources，environment等相关的policy或management或measure或reduce或control等措施的描述。
        # 【要注意】：当有“not+impact，little significance+impact， not involve, not produce”等的描述，
        # 但是同时又有政策措施相关的描述，仍判断Y而非E
        "path": ["A3 policies - environment and natural resources"],
        "models": [
            # 优先提取
            {
                "name": "a3_policy",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__natur(e|al) resources?.*?environment",
                    r"__regex__environment.*?natur(e|al) resources?",
                ],
                "include_title": True,
            },
            {
                "name": "policy_row_match",
                "multi": True,
                "multi_elements": True,
                "row_pattern": ["^General Disclosure"],
                "anchor_pattern": ["Aspect A3"],
            },
            {
                "name": "after_row_match",
                "row_pattern": [r"A3"],
                "multi_elements": True,
                "para_pattern": [
                    r"(minimise|minimize).*?impacts",
                    r"environmental protection",
                    r"environmental sustainability",
                    r"minimise|(minimize|lower|minimising).*?(impacts|effect)",
                    r"(?P<content>not own.*?emissions)",
                    r"(?P<content>not cause any significant impact on)",
                ],
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "multi": True,
                "only_before_first_chapter": True,
                "inject_syllabus_features": [
                    r"__regex__Sustainable Construction",
                    r"__regex__natural resources+environment",
                    r"__regex__ENVIRONMENTAL.*?REPORT__regex__ENVIRONMENTAL__regex__Environmental Policies",
                    r"__regex__A[.]?3",
                    r"__regex__Environment.*?Resources",
                    r"__regex__use of resources",
                    r"__regex__Ecological Protection in Operating Regions",
                    r"__regex__environment management",
                    r"__regex__Energy Conservation and Consumption Reduction",
                ],
            },
            {
                "name": "para_match",
                # NOTE: need_missing_crude_answer=False, 非常特例的情况，应该不需要补充初步定位答案
                "syllabus_regs": [
                    r"Environment|Resources",
                ],
                "anchor_regs": (r"^Sustainable\s*?Construction$",),  # 09998|2022
                "include_anchor": True,
                "paragraph_pattern": (r"(?P<content>implemented\s*?environment(al)?\s*?polic(ies|y))",),  # 09998|2022
            },
            {
                "name": "para_match",
                "syllabus_regs": [
                    r"Environment|Resources",
                ],
                "paragraph_pattern": (
                    r"(?P<content>not direct impact|not disclosed|of little significance|not involve|not produce|not applicable|non-material|not disclosed)",
                    r"(?P<content>not own.*?emissions)",
                    r"(?P<content>not cause any significant impact on)",
                    r"(?P<content>minimise|(minimize|lower|minimising).*?(impacts|effect)|environmental protection|environmental sustainability)",
                ),
            },
            DEFAULT_MODEL,
        ],
    },
    {
        "path": ["KPI A3.1 - impact on environment and natural resources"],
        "models": [
            # 优先提取
            {
                "name": "a3_policy",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__natural resources.*?environment",
                    r"__regex__environment.*?natural resources",
                ],
            },
            {
                "name": "policy_row_match",
                "multi": True,
                "multi_elements": True,
                "row_pattern": ["^General Disclosure"],
                "anchor_pattern": ["Aspect A3"],
            },
            {
                "name": "after_row_match",
                "row_pattern": [r"A3"],
                "multi_elements": True,
                "para_pattern": [
                    r"(minimise|minimize).*?impacts",
                    r"environmental protection",
                    r"environmental sustainability",
                    r"minimise|(minimize|lower|minimising).*?(impacts|effect)",
                    r"(?P<content>not own.*?emissions)",
                    r"(?P<content>not cause any significant impact on)",
                ],
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "multi": True,
                "only_before_first_chapter": True,
                "inject_syllabus_features": [
                    r"__regex__natural resources+environment",
                    r"__regex__ENVIRONMENTAL.*?REPORT__regex__ENVIRONMENTAL__regex__Environmental Policies",
                    r"__regex__A[.]?3",
                    r"__regex__Environment.*?Resources",
                    r"__regex__use of resources",
                    r"__regex__Ecological Protection in Operating Regions",
                    r"__regex__environment management",
                    r"__regex__Energy Conservation and Consumption Reduction",
                    r"__regex__KPI A3.1",
                ],
            },
            {
                "name": "para_match",
                "syllabus_regs": [
                    r"Environment|Resources",
                ],
                "paragraph_pattern": (
                    r"(?P<content>not direct impact|not disclosed|of little significance|not involve|not produce|not applicable|non-material|not disclosed)",
                    r"(?P<content>not own.*?emissions)",
                    r"(?P<content>not cause any significant impact on)",
                    r"(?P<content>minimise|(minimize|lower|minimising).*?(impacts|effect)|environmental protection|environmental sustainability)",
                ),
            },
            DEFAULT_MODEL,
        ],
    },
]
