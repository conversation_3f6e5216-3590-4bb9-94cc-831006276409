from remarkable.common.common_pattern import EXPLAIN_PATTERN as COMMON_EXPLAIN_PATTERN

EXPLAIN_PATTERN = tuple(COMMON_EXPLAIN_PATTERN)

DEFAULT_MODEL = {
    "name": "score_filter",
    "threshold": 0.1,
}


NEGLECT_PAGE_HEADER_PATTERN = [
    r"^ENVIRONMENT(AL)?.*?SOCIAL.*?AND.*?GOVERNANCE",
    r"^環境.*?社會及管治報告",
    r"^ENVIRONMENT(AL)?.*?SOCIAL.*?AND$",
    r"^GOVERNANCE.*?REPORT$",
    r"^SOCIAL\s*?AND\s*?GOVERNANCE\s*?REPORT$",  # esg换行
    r"^environment(al)?.*?social\s*?and$",  # esg换行
    r"^Report \d+$",
    r"^ENVIRONMENTAL,$",
    r"\(?CONTINUED\)?$",
    r"\(?CONT’D\)?$",
]


COMPLY_LAW_PATTERN = (
    r"(no|any|neither) non-compliance",
    r"(no|any|neither) (material|confirmed|recordable) non-compliance",
    r"(no|any|neither) (\w+\s){0,3}non-compliance",
    r"not aware of any .*? non-compliance",
    r"abides",
    r"strictly (comply|complied|complies|complying)",
    # r'ordinance',
    # r'regulation',
    r"has (comply|complied|complies|complying)",
    r"strictly abide",
    r"strictly adheres",
    r"(strict|in) compliance with",
    r"no violation",
    r"not record violation",
    r"not violated",
    r"strictly follows",
    r"(comply|complied|complies|complying) with",
    r"not discover any material violation",
    r"ensuring compliance",
)

LAW_COMMON_PATTERN = (
    r"^(during|there was|the group).*?(no|any|neither) (\w+\s){0,4}non-compliance",
    r"\.\s(during|there was|the group).*?(no|any|neither) (\w+\s){0,4}non-compliance",
)
