"""
Jura4 Annual Report ESG
"""

from remarkable.predictor.hkex_predictor.schemas.ar_esg_schema.common_pattern import (
    COMPLY_LAW_PATTERN,
    DEFAULT_MODEL,
    LAW_COMMON_PATTERN,
    NEGLECT_PAGE_HEADER_PATTERN,
)

predictor_options = [
    {
        "path": ["B1 policies - employment"],
        # todo B1-3 policies 是标题下除了表格之外的内容全要 意思是不管表格出现在什么位置 表格之后 表格之前的段落都要 对吧
        # 如果能把“表格之后 表格之前的段落”中涉及到law，也就是B1-LAW，B2-LAW相关的内容剔除掉就更好了
        # B4,B5,B6,B7，B8如果只提第一段或前两段可以符合大部分的情况吧
        # 16.B1 polices 可以框选employment 标题下除数据、图表、child的所有段落
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/811#note_214095
        "models": [
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN
                + [
                    r"^Our Workforce by",
                    r"^Number of training hours by Gender",
                    r"^Human Capital Targets$",
                ],
                "only_inject_features": True,
                "break_para_pattern": [
                    r"Overview of key performance indicators",
                ],
                "inject_syllabus_features": [
                    r"__regex__OUR PEOPLE",
                ],
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_before_first_chapter": True,  # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/803#note_212861
                "inject_syllabus_features": [
                    # r'__regex__B1:', # todo id uat 58767
                    r"__regex__B[.]1",
                    r"__regex__Employment__regex__General Disclosure and KPIs",
                    r"__regex__\d.*?employment$",
                    r"__regex__Employment and Labor Practices",
                    r"__regex__Protection of Employees.*?Rights",
                ],
            },
            {
                "name": "policy_row_match",
                "multi": True,
                "multi_elements": True,
                "row_pattern": ["^General Disclosure"],
                "anchor_pattern": ["Aspect B1"],
            },
            {"name": "kmeans_classification"},
        ],
    },
    {
        "path": ["B1 law compliance - employment"],
        "location_threshold": 0.01,
        "models": [
            {
                "name": "para_match",
                "paragraph_pattern": LAW_COMMON_PATTERN,
                "neglect_syllabus_regs": [
                    r"Labou?r Standards?",
                    r"B[2-8]|A[1-3]",
                ],
            },
            {
                "name": "syllabus_based",
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__B1\.\semployment",
                    r"__regex__^employment$",
                ],
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"^(during|there was|the group).*?(no|any|neither) (\w+\s){0,4}non-compliance",
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "twice_para_match",
                "multi_elements": True,
                "syllabus_regs": [],
                "neglect_syllabus_regs": [
                    r"Labou?r Standards?",
                    r"B[2-8]|A[1-3]",
                ],
                "paragraph_pattern": COMPLY_LAW_PATTERN,
                "neglect_pattern": (
                    r"^[a-zA-Z]\.",
                    r"^•",
                ),
                "second_pattern": (
                    r"relevant laws and regulations",
                    r"employment",
                    r"labour",
                    r"workforce",
                    r"compensation",
                    r"dismissal",
                    r"recruitment",
                    r"promotion",
                    r"working hours",
                    r"rest periods",
                    r"equal opportunity",
                    r"diversity",
                    r"anti-discrimination",
                    r"other benefits",
                    r"other welfare",
                ),
            },
            {
                "name": "para_match",
                "neglect_syllabus_regs": [
                    r"Product Responsibility",
                    r"Labou?r Standards?",
                    r"B[2-8]|A[1-3]",
                ],
                "paragraph_pattern": (
                    r"no material non-compliance with|are compliant with",
                    r"complie[ds].*?(law|regulation|ordinance)",
                    r"abides.*?(law|regulation|ordinance)",
                    r"compliance.*?(law|regulation|ordinance)",
                    r"conforms.*?(law|regulation|ordinance)",
                    r"follow.*?(law|regulation|ordinance)",
                    r"comply(ing)? with.*?(law|regulation|ordinance)",
                    r"not violate.*?(law|regulation|ordinance)",
                    r"not contravened any relevant legislations or regulations",
                    r"no material non-compliance.*?employment",
                    r"(complie[ds]|abide|compliance).*?(law|regulation|ordinance)",
                ),
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__Compliance Management",
                    r"__regex__Human Capital",
                    r"__regex__Employment Practices",
                    r"__regex__employment",
                ],
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"no material non-compliance with",
                        r"are compliant with",
                        r"no known non-compliance with",
                    ),
                },
                "table_model": "row_match",
                "table_config": {
                    "row_pattern": [r"Employment"],
                },
            },
            {
                "name": "kmeans_classification",
                "threshold": 0.1,
                "para_pattern": [
                    r"(comply|not violate|abide).*?employment.*?(law|regulation)"
                    r"no material non-compliance with|are compliant with",
                    r"complied with all relevant laws",
                    r"complies with the relevant laws and regulations.*?employment",
                    r"(?P<content>non-compliance|employment|ordinance|legal|law|regulation)",
                ],
            },
            # DEFAULT_MODEL,
        ],
    },
    {
        #  B1.1 规则是披露员工人数情况，按性别、雇佣类型（例如，全职或兼职）、年龄组和地理区域划分的劳动力总数。
        #  一般都是按照各类员工的数量及比例披露的。
        #  关键词是by gender, employment type、number of、age 、geographical、resign、resigned、resignation
        #  所在位置：1、优先去index/summary/key performance indicators中找寻；
        #           2、根据index给出的KPI位置去定位；
        #           3、在Employment/ Employment Practices/Human Capital标题下。
        #
        #  判断标准：1、如果报告中说没有按员工类型总数来披露，并且解释了原因，那么选E，但是不常见；
        #           2、找到对应图表、段落或者句子，就算Y；
        #           3、如果报告中没有对此有任何描述，那么就是ND。
        "path": ["KPI B1.1 - workforce by types"],
        "models": [
            {
                "name": "after_row_match",
                "row_pattern": [
                    r"B1.1",
                    r"Workforce",
                ],
                "para_pattern": [
                    r"B1.1|by gender|employment type|number of|age|geographical|resign|resigned|resignation"
                ],
            },
            {
                "name": "shape_title",
                "multi_elements": True,
                "regs": (
                    r"Employee.*?distribution",
                    r"Employees by.*",
                    r"Age distribution",
                    r"Age group",
                    r"^gender$",
                    r"(Employment|Employee) Category",
                    r"本集團員工團隊人數統計資料概述如下",
                    r"僱員明細載列如下",
                    r"僱員分佈詳情如下",
                    r"僱員總數",
                    r"Employees’ breakdown by employment type",
                ),
                "neglect_regs": (r"turnover",),
            },
            {
                "name": "middle_rows",
                "second_table": True,
                "regs": [
                    r"workforce distribution",
                    r"workforce as",
                    r"by type",
                    r"male",
                    r"by.*?(gender|age|location)",
                    r"B1.1",
                ],
                "start_regs": [
                    r"Employee breakdown",
                    r"number of employees",
                    r"workforce",
                ],
                "end_regs": [
                    r"Employee turnover rate",
                    r"Development and training",
                ],
            },
            {
                "name": "table_title",
                "multi_elements": True,
                "feature_white_list": [
                    r"^Diversity$",
                    r"Summary of Employment Performance Indicators",
                    r"Employee.*?distribution",
                    r"Age distribution",
                    r"Age group",
                    r"gender",
                    r"Employment Category",
                    r"本集團員工團隊人數統計資料概述如下",
                    r"僱員分佈詳情如下",
                    r"僱員總數",
                ],
            },
            {
                "name": "row_match",
                "ignore_index_table": True,
                "multi_elements": True,
                "row_pattern": [
                    r"distribution of employees",
                ],
            },
            {
                "name": "elements_collector_based",
                "elements_collect_model": "syllabus_elt_v2",
                "elements_collect_config": {
                    "inject_syllabus_features": [r"__regex__B[.]1"],
                    "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                    "only_inject_features": True,
                },
                "table_model": "shape_title",
                "table_config": {
                    "regs": [
                        r"The total workforce and breakdown are listed as follows:",
                    ],
                    "ignore_regs_before_shape": [r"total|总数|總數"],
                },
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [],
                "multi_elements": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"B1.1|by gender|employment type|number of|geographical|resign|resigned|resignation",
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"(?P<content>B1.1|by gender|employment type|number of|age|geographical|resign|resigned|resignation)",
                ),
            },
            DEFAULT_MODEL,
        ],
    },
    {
        # B1.2 此条规则是用于披露员工流动率，一般是按照性别、年龄和地理区域划分的员工来描述流动率。
        # 常见关键词为B1.2,turnover, by gender, age , geographical, employment type，leave，leaver
        #  所在位置：1、优先在index或者data summary的地方找对应的图表、表格及段落；
        #           2、根据index给出KPI的位置去定位，一般在标题Employment/ Employment Practices/Human Capital下
        #
        #  位置特殊情况：有的报告会把B1.1和B1.2内容位置相近，如果能分开标注最好，如果不能就合并标注。
        #
        #  判断依据：1、报告中披露涉及发行人不披露流动率的原因（比如数据涉及商业敏感commercially sensitive）等描述，为E；
        #           2、报告中披露了员工turnover，并且是按照各类别披露的数据，为Y；
        #           3、报告没有披露相关内容，也没有进行解释，为ND
        #
        #  判断特殊情况：1、若报告中仅有overall employees的turnover rate，没有披露按照gender, age group and geographical region
        #               分类下的turnover rate，B1.2属于Y；
        #               2、B1.2 只披露的离职人数，没有turnover，属于Y
        "path": ["KPI B1.2 - employee turnover by types"],
        "models": [
            {
                "name": "after_row_match",
                "row_pattern": [r"B1.2"],
                "para_pattern": [r"B1.2|turnover|by gender|by age|geographical|employment type|leave|leaver"],
                "middle_rows": True,
                "start_regs": [r"Turnover Rate"],
                "end_regs": [r"Training"],
                "table_title": [
                    r"本集團僱員流失率概述如下",
                    r"員工流失率",
                    r"turnover rate",
                    r"employee Structure and Turnover",
                ],
            },
            {
                "name": "table_title",
                "multi_elements": True,
                "only_inject_features": True,
                "feature_white_list": [
                    r"本集團僱員流失率概述如下",
                    r"員工流失率",
                    r"turnover rate",
                    r"employee Structure and Turnover",
                ],
            },
            {
                "name": "shape_title",
                "multi_elements": True,
                "regs": (
                    r"Employee Turnover",
                    r"Geographical Region",
                    r"turnover rate",
                ),
            },
            {
                "name": "middle_rows",
                "ignore_index_table": False,
                "regs": [r"turnover"],
                "title_regs": [r"turnover"],
                "start_regs": [r"turnover rate", "Employee turnover"],
                "end_regs": [
                    r"Percentage of employees trained",
                    r"training",
                    r"B2",
                ],
            },
            {
                "name": "table_title",
                "only_inject_features": True,
                "feature_white_list": [
                    r"turnover",
                ],
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"(?P<content>not disclosed|sensitive)",
                    r"full-time employee turnover rate",
                    r"employee turnover rate for male and female",
                    r"average turnover rate by",
                    r"commercially sensitive",
                    r"low employee turnover rate",
                ),
            },
            # DEFAULT_MODEL,
        ],
    },
]
