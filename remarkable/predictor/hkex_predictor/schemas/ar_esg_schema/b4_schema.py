"""
Jura4 Annual Report ESG
"""

from remarkable.predictor.hkex_predictor.schemas.ar_esg_schema.common_pattern import (
    COMPLY_LAW_PATTERN,
    LAW_COMMON_PATTERN,
    NEGLECT_PAGE_HEADER_PATTERN,
)

predictor_options = [
    {
        # 枚举判定为：E
        # ESG报告中没有相关劳工标准政策披露但是有解释
        # 常见表述“由于公司的业务性质是XXX，涉及到的劳动力较少，所以not applicable”
        # 或“由于公司的业务性质,雇佣的员工多为有一定资历或者工作经验的员工，童工或强制劳动的风险小 "
        # 或“no相关的问题” 或“not material" 的描述
        # 常见关键词：not material，not applicable, non-labour intensive
        #
        # 枚举判定为：Y
        # ESG报告中有关于劳工标准labour standards政策的披露
        # 提取涉及到labour standards政策的所有段落。常见的描述是“公司的劳工标准是……，遵守当地劳工法律法规，禁止雇佣童工、强迫劳动和非法用工等”。
        # i. 一般有单独的labour standard相关标题，能区分出后面B4.1和B4.2的就不勾进去
        # ii.无单独的labour standard相关标题，可能会跟B1 employment标题下方的内容混在一起，可通过关键词child,forced labor进行定位
        # 常见关键词： labour, labor，B4, workforce, forced labour, engaging child, Labour Standards，prevent
        #
        # ND: 没有披露相关内容,也没有进行解释
        "path": ["B4 policies - labour standards"],
        "models": [
            {
                # 章节标题和第一个小标题之间
                "name": "policy",
                "only_inject_features": True,
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "ignore_pattern_match": True,
                "inject_syllabus_features": [
                    r"__regex__Labour Standards",
                    r"__regex__Aspect B4: Labour Standards",
                ],
            },
            {
                "name": "policy_row_match",
                "multi": True,
                "multi_elements": True,
                "row_pattern": ["^General Disclosure"],
                "anchor_pattern": ["Aspect B4"],
            },
            {
                "name": "syllabus_based",
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__B3.*?Development and Training",
                    r"__regex__talent management",
                    r"__regex__Labou?r Standard",
                    r"__regex__Labou?r Practices",
                    r"__regex__Prevention of Child and Forced Labour",
                ],
                "multi_elements": True,
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"as are not addressed in this ESG",
                        r"any child labour",
                        r"forbids all types",
                        r"prevent.*?child labour",
                        r"(child|forced|illegal).*?(labour|labor)",
                        r"ensure compliance with relevant labour laws",
                        r"To prevent hiring child labour by mistake",
                        r"rights of labours",
                        r"Free chosen employment",
                        r"Remuneration and benefits",
                        r"Equal opportunity and no discrimination policy",
                        r"not force any employees to work",
                        r"Harassment and abuse",
                    ),
                    "neglect_pattern": (
                        r"have been no cases of ",
                        r"During the Reporting Period",
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_before_first_chapter": True,
                "only_first": True,
                "inject_syllabus_features": [
                    r"__regex__Labou?r Standards",
                    r"__regex__Employment and Labour Standards",
                    r"__regex__Human Capital",
                    r"__regex__Employment Policy",
                ],
            },
            {
                "name": "para_match",
                "strict_limit": True,
                "syllabus_regs": [
                    r"Labour Standards",
                    r"Employment and Labour Standards",
                    r"Human Capital",
                    r"Employment Policy",
                ],
                "paragraph_pattern": (
                    r"(?P<content>not material|not applicable|non-labour intensive)",
                    r"(?P<content>labour|labor|B4|workforce|forced labour|engaging child|Labour Standards)",
                    r"(?P<content>not have any violation relating)",
                ),
                "neglect_pattern": (r"to prevent any employment of child labour",),
            },
            {
                "name": "kmeans_classification",
            },
        ],
    },
    {
        "path": ["B4 law compliance - labour standards"],
        "models": [
            {
                "name": "para_match",
                "paragraph_pattern": LAW_COMMON_PATTERN,
                "neglect_syllabus_regs": [
                    r"Supply chain management",
                    r"Anti.*?Corruption",
                    r"The Environment and Natural Resources",
                    r"Data Privacy",
                    r"B[1-3]|B[5-8]|A[1-3]",
                ],
            },
            {
                "name": "twice_para_match",
                "multi_elements": True,
                # "strict_limit": True,
                "syllabus_regs": [
                    r"labour standard",
                ],
                "neglect_syllabus_regs": [
                    r"Supply chain management",
                    r"Anti.*?Corruption",
                    r"The Environment and Natural Resources",
                    r"Data Privacy",
                    r"B[1-3]|B[5-8]|A[1-3]",
                ],
                "paragraph_pattern": COMPLY_LAW_PATTERN,
                # 'neglect_pattern': (
                #     r'^[a-zA-Z]\.',
                #     r'^•',
                # ),
                "second_pattern": (
                    r"workforce",
                    r"laws and regulations",
                    r"engaging child",
                    r"labour law",
                    r"(child|forced|illegal).*?(labour|labor)",
                    r"labour related (law|regulation)",
                ),
            },
            {
                "name": "para_match",
                "syllabus_regs": [
                    r"Labour Standards",
                    r"Employment and Labour Standards",
                    r"Human Capital",
                    r"Employment Policy",
                ],
                "neglect_syllabus_regs": [
                    r"Supply chain management",
                    r"Anti.*?Corruption",
                    r"The Environment and Natural Resources",
                    r"Data Privacy",
                    r"B[1-3]|B[5-8]|A[1-3]",
                ],
                "paragraph_pattern": (
                    r"complied.*?laws and regulations.*?labour standards",
                    r"(?P<content>not applicable|no (law|ordinance|regulation))",
                    r"(?P<content>(law|regulations).*?(workforce|forced labor|child|engaging child))",
                    r"(?P<content>not have any violation relating)",
                ),
                "neglect_pattern": (r"Information on:",),
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__Labour Standards",
                    r"__regex__Employment and Labour Standards",
                    r"__regex__Human Capital",
                    r"__regex__Employment Policy",
                ],
                "multi_elements": True,
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"(?P<content>not applicable|no (law|ordinance|regulation))",
                        r"(?P<content>(law|regulations).*?(workforce|forced labor|child|engaging child))",
                        r"(?P<content>notl have any violation relating)",
                        r"did not identify any material breaches",
                    ),
                    "neglect_pattern": (r"Information on:",),
                },
                "table_model": "empty",
            },
            {
                "name": "kmeans_classification",
            },
            # DEFAULT_MODEL,
        ],
    },
    {
        # 枚举判定为：E
        # ESG报告中没有相关披露但是有解释
        # 常见表述“这条规则不适用于本集团”或者 “N/A”或“not disclosure”
        # 关键词：not material，not applicable, N/A, not disclosure
        #
        # 枚举判定为：Y
        # ESG报告中有关于公司如何审查以避免child and labour force的措施的披露
        # 提取涉及child和forced labour措施的所有段落，侧重于labour force和用工年龄方面。
        # 常见的描述是“公司已制定招聘政策，符合年龄的申请人才可被聘用”或者“本集团要求求职者提供有效的身份证明文件，确保年龄符合规定”等。
        # i.一般在labour standard相关标题的内容中披露相关措施；
        # ii.无单独的labour standard相关标题，可能会跟B1 employment标题下方的内容混在一起，可通过关键词child,forced labor进行定位`
        # 关键词：workforce, forced labour, engaging child，child labour，establish，overtime，prohibition，prevent，
        # identity/identification  document,identity card,recruiting,above 18 years old,working visa
        #
        # ND: 没有披露相关内容,也没有进行解释
        "path": ["KPI B4.1 - review measures to avoid child & forced labour"],
        "models": [
            {
                "name": "after_row_match",
                "row_pattern": [r"B4.1"],
                "ignore_syllabus": [r"^Our Employees$"],
                "just_a_para": True,
                # 'multi_elements': True,
                "para_pattern": (
                    r"(?P<content>not material|not applicable|N/A|not disclosure)",
                    r"(?P<content>workforce|forced labour|engaging child|child labour|establish|overtime)",
                    # r'forced labour|child labour|engaging child',
                    # r'prevent|recruiting|prohibition|overtime|establish|workforce',
                    r"(identity|identification document|identity card)",
                    r"not material|not applicable|N/A|not disclosure",
                    r"workforce|engaging child|child labour",
                    # r'establish|overtime',
                    r"complete pre-employment application",
                    r"provide.*?identification",
                    r"(identity|identification document)",
                    r"identity card",
                    r"above 18 years old",
                    r"working visa",
                    r"ensure no child labor",
                    r"checks the documents provided",
                    r"inspect applicant’s documents",
                    r"checks.*?identity documents",
                    r"Free chosen employment",
                    r"Remuneration and benefits",
                    r"Equal opportunity and no discrimination policy",
                    r"not force any employees to work",
                    r"Harassment and abuse",
                ),
                "direct_answer_pattern": [
                    r"apply the same standard.*?prevent child and force",
                ],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__KPI B4.1",
                    r"__regex__Employment Guidelines",
                    r"__regex__Employment and Labour Standards",
                    r"__regex__Labor Rights and Interests Protection",
                    r"__regex__Prevent.*?Child and Forced Labour",
                    # r'__regex__Preventative measures against child and forced labour',
                    r"__regex__Labour Standard",
                    r"__regex__Human Capital",
                    r"__regex__Employment Policy",
                ],
                "multi_elements": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"not material|not applicable|N/A|not disclosure",
                        r"workforce|engaging child|child labour",
                        # r'establish|overtime',
                        r"complete pre-employment application",
                        r"provide.*?identification",
                        r"(identity|identification document)",
                        r"identity card",
                        r"recruiting",
                        r"above 18 years old",
                        r"working visa",
                        r"ensure no child labor",
                        r"checks the documents provided",
                        r"inspect applicant’s documents",
                        r"checks.*?identity documents",
                        r"Free chosen employment",
                        r"Remuneration and benefits",
                        r"Equal opportunity and no discrimination policy",
                        r"not force any employees to work",
                        r"Harassment and abuse",
                    ),
                    "neglect_pattern": (
                        r"During the Year",
                        r"engage suppliers and contractors",
                    ),
                },
                "table_model": "row_match",
                "ignore_index_table": True,
                "table_config": {
                    "row_pattern": [
                        r"(child|forced) labour",
                    ],
                },
            },
            {
                "name": "syllabus_elt_v2",
                "only_inject_features": True,
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "inject_syllabus_features": [
                    r"__regex__B4.1",
                    # r'__regex__Labour Standard',
                    r"__regex__Employment and Labour Standards",
                    r"__regex__Human Capital",
                    r"__regex__Employment Policy",
                ],
            },
            {
                "name": "para_match",
                "syllabus_regs": [
                    r"Labour Standards",
                    r"Employment and Labour Standards",
                    r"Human Capital",
                ],
                # 'multi_elements': True,
                "paragraph_pattern": (
                    # r'(?P<content>not material|not applicable|N/A|not disclosure)',
                    # r'(?P<content>workforce|forced labour|engaging child|establish|overtime)',
                    r"complete pre-employment application",
                    r"provide.*?identification",
                    r"(identity|identification document)",
                    r"identity card",
                    r"recruiting",
                    r"above 18 years old",
                    r"working visa",
                    r"ensure no child labor",
                    r"checks the documents provided",
                    r"inspect applicant’s documents",
                    r"checks.*?identity documents",
                    r"B4.*?are not the key material matters.*?are not addressed in this ESG report",
                ),
            },
            # {
            #     "name": 'kmeans_classification',
            # },
            # DEFAULT_MODEL,
        ],
    },
    {
        # 框选内容：<优先在index指引的正文内容去找>
        #
        # 如果存在明显的细分小标题**kpi B4.2**，则直接框该小标题下内容
        #
        #  一般描述中会有**比较明确的关键词**： in case of，once found, in the event这种表示
        #  如果发现forced labour，child labour的词。
        # todo 修改twice_para_match之后还需要再次重跑根据badcase 补充规则
        "path": ["KPI B4.2 - steps to avoid child & forced labour"],
        "models": [
            {
                "name": "para_match",
                "paragraph_pattern": (r"B4.*?are not the key material matters.*?are not addressed in this ESG report",),
            },
            {
                "name": "twice_para_match",
                "syllabus_regs": [
                    r"Labou?r Standards",
                    r"_Labou?r right",
                    r"Employment and Labour Standards",
                    r"Human Capital",
                    r"Employment Policy",
                ],
                "paragraph_pattern": (
                    r"(child|forced|illegal).*?(labour|labor)",
                    r"engaging child",
                    r"background check",
                    r"done voluntarily",
                    r"overtime work",
                ),
                "second_pattern": (
                    r"(?<!no )cases? of",
                    r"in case of|once found|in the event",
                    r" (once|facing|discovered)",
                    r"if.*?occurrence",
                    r"if(?:(?!\.).)*?needed",
                    r"background check|working age|disqualified|refuse|handle",
                    r"comprehensive identity check",
                    r"screening process",
                    r"review employment practices",
                    r"preventive procedures",
                    # 用于确定年龄的描述
                    r"verify(?:(?!\.).)*?age",
                    r"identity card and vocational qualification certificate",
                    r"social security card",
                    r"medical or health certificate",
                    r"recent photos",
                    r"other relevant information and documents",
                    r"travel documents",
                ),
            },
            {
                "name": "after_row_match",
                "row_pattern": [r"B4.2"],
                "para_pattern": (
                    r"(child|forced|illegal).*?(labour|labor)",
                    r"engaging child",
                    r"background check",
                    r"done voluntarily",
                ),
                "second_pattern": (
                    r"(?<!no )cases? of",
                    r"in case of|once found|in the event",
                    r"once|facing|discovered",
                    r"if.*?occurrence",
                    r"background check|working age|disqualified|refuse|handle|overtime work",
                    r"comprehensive identity check",
                    r"screening process",
                    r"review employment practices",
                    r"preventive procedures",
                    # 用于确定年龄的描述
                    r"verify.*?age",
                    r"identity card and vocational qualification certificate",
                    r"social security card",
                    r"medical or health certificate",
                    r"recent photos",
                    r"other relevant information and documents",
                    r"identification|travel documents",
                ),
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__B4.[12]",
                    r"Handling of non-compliance",
                ],
                "multi_elements": True,
                "paragraph_model": "twice_para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"(child|forced|illegal).*?(labour|labor)",
                        r"engaging child",
                        r"background check",
                        r"done voluntarily",
                    ),
                    "second_pattern": (
                        r"(?<!no )cases? of",
                        r"in case of|once found|in the event",
                        r"once|facing|discovered",
                        r"if.*?occurrence",
                        r"background check|disqualified|refuse|handle|overtime work",
                        r"comprehensive identity check",
                        r"screening process",
                        r"review employment practices",
                        r"preventive procedures",
                        # 用于确定年龄的描述
                        r"verify\s?age",
                        r"identity card and vocational qualification certificate",
                        r"social security card",
                        r"medical or health certificate",
                        r"recent photos",
                        r"other relevant information and documents",
                        r"identification|travel documents",
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "para_match",
                "paragraph_pattern": (r"employees work voluntarily",),
            },
        ],
    },
]
