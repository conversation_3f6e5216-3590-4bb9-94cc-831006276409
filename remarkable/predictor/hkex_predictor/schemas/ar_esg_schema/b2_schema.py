"""
Jura4 Annual Report ESG
"""

from remarkable.predictor.hkex_predictor.schemas.ar_esg_schema.common_pattern import (
    COMPLY_LAW_PATTERN,
    DEFAULT_MODEL,
    LAW_COMMON_PATTERN,
    NEGLECT_PAGE_HEADER_PATTERN,
)

predictor_options = [
    {
        # B2涉及到的规则都是关于health and safety相关的内容，policy一般都为公司制定了安全管理政策的相关概括性描述；
        #  所在位置：1. 涉及到health and safety的大标题下（一般位置比较固定，都在这个章节下定位）
        #           2. 如果大标题分了小标题的情况，policy一般可以提取大标题和小标题中间的文段
        #           3. 小标题有policy/policies的关键词的，可以直接提取对应小标题下的内容
        #
        #  提取内容：1. 常见描述“集团重视员工健康和安全，以保护员工免受职业危害，按照适用的法律法规制定了安全管理政策。”等
        #           2. 如果大标题下有小标题，可以提取大标题和小标题中间的文段
        #           3. 对于篇幅较小的或没有按照小标题披露的，可以提取标题下全部内容
        #
        #  关键词：health, healthy and safe, working environment, established, safety management strategy,
        #  reduce potential workplace hazards, hazard-free working environment
        #
        #  判定方式：Y-披露并描述以上的提取内容（满足提取内容的任何一条或多条即可）
        #           E- 不常见，但可能会有些金融行业或者与员工劳动伤害很远的行业，在此标题下会有 not material之类的描述
        #           ND-没有任何相关描述
        "path": ["B2 policies - health and safety"],
        "models": [
            {
                # GRI 特例 https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/997#note_243522
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__Occupational Health and Safety",
                ],
            },
            {
                # 章节标题和第一个小标题之间
                "name": "policy",
                "only_inject_features": True,
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "ignore_pattern_match": True,
                "inject_syllabus_features": [
                    r"__regex__(?<!student )Health and Safety",
                ],
                "neglect_features": [
                    r"Product and Service Health and Safety",
                ],
            },
            {
                "name": "policy_row_match",
                "multi": True,
                "multi_elements": True,
                "row_pattern": ["^General Disclosure"],
                "anchor_pattern": ["Aspect B2"],
            },
            {
                "name": "after_row_match",
                "row_pattern": [r"B2"],
                "just_a_para": True,
                "para_pattern": [
                    r"protect.*?health",
                    r"(?P<content>health|Safety|workplace|fire)",
                ],
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_before_first_chapter": True,
                "inject_syllabus_features": [
                    r"__regex__Health and Safety__regex__General Disclosure and KPI",
                ],
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_before_first_chapter": True,
                "inject_syllabus_features": [
                    r"__regex__Health and Safety",
                    r"__regex__Health and Safety__regex__General Disclosure and KPI",
                ],
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"(?P<content>health|Safety|workplace|fire)",
                    r"(?P<content>protect.*?health)",
                ),
            },
            DEFAULT_MODEL,
        ],
    },
    {
        "path": ["B2 law compliance - health and safety"],
        "models": [
            {
                "name": "para_match",
                "paragraph_pattern": LAW_COMMON_PATTERN,
                "neglect_syllabus_regs": [
                    r"Product Health and Safety",
                    r"Labou?r Standards?",
                    r"B1|B[3-8]|A[1-3]",
                ],
            },
            {
                "name": "twice_para_match",
                "multi_elements": True,
                "strict_limit": True,
                "syllabus_regs": [
                    r"Health and Safety",
                    r"Employee Health and Care",
                    r"COVID-19",
                    r"CARING FOR EMPLOYEES",  # 特殊文档 文档解析错误
                ],
                "neglect_syllabus_regs": [
                    r"Product Health and Safety",
                    r"Labou?r Standards?",
                    r"B1|B[3-8]|A[1-3]",
                ],
                "paragraph_pattern": COMPLY_LAW_PATTERN,
                "neglect_pattern": (
                    r"^[a-zA-Z]\.",
                    r"^•",
                ),
                "second_pattern": (
                    r"relevant laws and regulations",
                    r"health",
                    r"Safety",
                    r"workplace",
                    r"safe working environment",
                    r"protecting employees",
                ),
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__Compliance Management",
                ],
                "paragraph_model": "empty",
                "table_model": "row_match",
                "table_config": {
                    "row_pattern": [r"health and safety"],
                },
            },
            {
                "name": "para_match",
                "multi_elements": True,
                "enum_from_multi_element": True,
                "neglect_syllabus_regs": [
                    r"Product Health and Safety",
                    r"Labou?r Standards?",
                    r"B1|B[3-8]|A[1-3]",
                ],
                "paragraph_pattern": (r"(?P<content>non-compliance|ordinance|law|regulation)",),
                "neglect_pattern": (r"Information on:",),
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_before_first_chapter": True,
                "only_first": True,
                "inject_syllabus_features": [
                    r"Health and Safety",
                ],
            },
            {
                "name": "kmeans_classification",
            },
            DEFAULT_MODEL,
        ],
    },
    {
        # 此条规则是披露报告年度在内的过去三年中每年发生的与工作有关的死亡人数和发生率。
        # 常见关键词为work related, fatalities, fatal accidents, occupational，death，B2.1
        #  所在位置：1、优先在index或者data summary的地方找对应的图表、表格及段落；
        #           2、根据index给出KPI的位置去定位，一般在标题Health and Safety下；
        #           3、如果没有index和data summary，在报告中既有段落的，又有表格的，优先表格部分
        #
        #  判断依据：1、报告中没有相关数据但是有解释，为E（此情况非常不常见）；
        #           2、报告中披露了三年内每年发生的fatality，无论数据是nil或no或not，都是Y；
        #           3、报告没有披露相关内容，也没有进行解释，为ND
        #
        #  判断特殊情况：1、若无上述关键词，仅出现了no work safety-related incident或no work safety-related accident或无work
        #               injury等描述，B2.1判断为Y；
        #               2、如果报告中只披露了两年的死亡人数及发生率也是可以的；
        #               3、没有work safety-related accident可视为无work injury lost day ，判断为Y
        "path": ["KPI B2.1 - work-related fatalities"],
        "models": [
            {
                "name": "after_row_match",
                "row_pattern": [r"B2.1|B-2.1"],
                "just_a_para": True,
                "include_row": True,
                "filter_content_answer": True,
                "direct_answer_pattern": [
                    r"No work-related fatality was recorded",
                ],
                "para_pattern": [
                    r"work[\-\s]related fatalit",
                    r"did not have any work-related fatalities",
                    r"not have any violation.*?work-related",
                    r"fatalities|fatal accidents|death|B2.1",
                    r"(?P<content>no work safety-related incident|no work safety-related accident|no work injury)",
                ],
                "middle_rows": True,
                "table_title": [
                    r"職業健康",
                    r"安全表現",
                ],
                "start_regs": [r"B2\.1"],
                "end_regs": [r"B2\.2"],
            },
            {
                "name": "row_match",
                "ignore_index_table": True,
                "multi": True,
                "row_pattern": [
                    r"work-related",
                    r"B2\.1",
                ],
            },
            {
                "name": "first_table",
                "regs": [
                    r"work.*?related",
                    r"Number.*?(injuries|fatalit)",
                    r"Fatalities rate",
                ],
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"no fatality cases related to our employees occurred",
                    r"did not have any work-related fatalities",
                    r"protect.*?health",
                    r"not.*?violation",
                    r"incidents of workplace accidents",
                    r"(?P<content>work related|fatalities|fatal accidents|occupational|death|B2.1)",
                    r"(?P<content>no work safety-related incident|no work safety-related accident|no work injury)",
                    r"(?P<content>work-related fatalit)",
                ),
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [],
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "multi_elements": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"(?P<content>work related|fatalities|fatal accidents|occupational|death|B2.1)",
                        r"(?P<content>no work safety-related incident|no work safety-related accident|no work injury)",
                    ),
                },
                "table_model": "empty",
            },
            # DEFAULT_MODEL,
        ],
    },
    {
        # B2.2 此条规则是披露员工因工伤损失的天数。
        # 常见的关键词有work related，lost days, sickness, work injury, accidents，occupational，B2.2
        #  所在位置：1、优先在index或者data summary的地方找对应的图表、表格及段落；
        #           2、根据index给出KPI的位置去定位，一般在标题Health and Safety下
        #           3、如果没有index和data summary，在报告中既有段落的，又有表格的，优先表格部分
        #
        #  判断依据：1、报告中没有相关数据但是有解释，为E（此情况非常不常见）；
        #           2、报告中有work injury天数或占比的披露，无论数据是nil或no或not，都是Y；
        #           3、报告没有披露相关内容，也没有进行解释，为ND
        #
        #  判断特殊情况：1、若没有披露injury，仅说没有规范heathy和safety相关的法律规定，B2.2为ND。
        "path": ["KPI B2.2 - work injury lost days"],
        "neglect_table_title_missing_crude_regs": [
            r"Employee work-related fatalities over the past three years",
        ],
        "models": [
            {
                "name": "table_title",
                "only_inject_features": True,
                "feature_black_list": [
                    r"__regex__^.$",
                    r"__regex__^(Environment[al,]*)$",
                    r"__regex__ENVIRONMENTAL, SOCIAL AND GOVERNANCE REPORT",
                ],
                "feature_white_list": [
                    r"工傷及損失天數統計",
                    r"職業健康",
                    r"安全表現",
                ],
            },
            {
                "name": "after_row_match",
                "row_pattern": [r"B2.2"],
                "just_a_para": True,
                "include_row": True,
                "para_pattern": [
                    r"injuries",
                    r"(?P<content>work[\-\s]related|sickness|work injury|accidents|occupational|B2.2)",
                    r"(?P<content>no work safety-related incident|no work safety-related accident|no work injury)",
                ],
                "second_pattern": [
                    r"lost days",
                    r"was (zero|\d+)",
                    r"no (work-related fatalities|lost day)",
                    r"no incident of work-related fatality",
                    r"no working day lost",
                ],
                "middle_rows": True,
                "table_title": [],
                "start_regs": [r"B2\.2"],
                "end_regs": [r"B2\.3"],
                "direct_answer_pattern": [
                    r"No lost day due to work injury was record",
                ],
            },
            {
                "name": "row_match",
                "ignore_index_table": True,
                "multi": True,
                "row_pattern": [
                    r"Lost Days.*?Work-related Injury",
                    r"Workdays lost.*?(work-related|fatalities)",
                    r"Number of lost days",
                    r"B2\.2",
                ],
            },
            {
                "name": "first_table",
                "regs": [
                    r"(?P<content>work related|lost days|sickness|work injury|occupational|B2.2)",
                    # r'accidents',
                ],
            },
            {
                "name": "twice_para_match",
                "multi_elements": True,
                # "strict_limit": True,
                "syllabus_regs": [],
                "neglect_syllabus_regs": [],
                "paragraph_pattern": (
                    r"(?P<content>work-?related|lost days|sickness|work injury|accidents|occupational|B2.2)",
                    r"(?P<content>no work safety-related incident|no work safety-related accident|no work injury)",
                ),
                # 'neglect_pattern': (
                # ),
                "second_pattern": (
                    r"lost (labor )days",
                    r"(had|was|were|is) (zero|\d+|no)",
                    r"nor? (any )?(work-related fatalities|l[ao]st day)",
                ),
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"incidents of work injuries arose",
                    # r'(?P<content>work-related)',
                ),
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__Accident Rate Analysis",
                    r"__regex__Employee Development",
                ],
                "multi_elements": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"(?P<content>work related|lost days|sickness|work injury|accidents|B2.2)",
                        r"(?P<content>no work safety-related incident|no work safety-related accident|no work injury)",
                        r"LTIFR",
                    ),
                },
                "table_model": "empty",
            },
        ],
    },
    {
        # B2涉及到的规则都是关于health and safety相关的内容，
        # B2.3提取的是公司采用的职业健康和安全措施，以及如何实施和监测这些措施；
        #  所在位置：1. health and safety的大标题下（一般位置比较固定，都在这个章节下定位）
        #
        #  提取内容：1. 常见描述形式为一段话末尾加冒号，然后按条描述明细措施，需要提取这段话加下面的所有明细
        #           2. 有小标题的，提取小标题下面的所有文段；或者除了policy，law以及B2.1、B2.2数据描述以外的其他文段
        #           3. 单独存在关于新冠的措施的小标题下，包含COVID关键词的，提取小标题下的全部内容
        #           3. 对于篇幅较小的或没有按照小标题披露的，可以提取包含各类动词的文段，如 check，daily, provide, organize,
        #           required, regularly等
        #
        #  关键词：health, healthy and safe, occupational, healthy, safety, measures, training, check，daily, provide,
        #  organize, required, regularly, first-aid
        #
        #  判定方式：Y-披露并描述以上的提取内容（满足提取内容的任何一条或多条即可）
        #           E- 不常见，但可能会有些金融行业或者与员工劳动伤害很远的行业，在此标题下会有 not material之类的描述
        #           ND-没有任何相关描述
        # todo 取 health and safety children syllabus
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/803#note_212892  todo
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/805#note_213474  todo
        "path": ["KPI B2.3 - health and safety measures"],
        "models": [
            {
                "name": "after_row_match",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "row_pattern": [r"B2.3"],
                "multi_elements": True,
                "ignore_syllabus": [
                    r"^Health and Safety$",  # 忽略范围太大的标题
                ],
                "para_pattern": [
                    r"(?P<content>COVID-?19|preventive measures|facilities|safety performance)",
                    r"adopted the following measures:$",
                ],
                "neglect_para_pattern": [
                    r"during the reporting period",
                    r"During the past.*?years",
                    r"safety performance:$",
                    r"lifelong learning",
                    r"Development and Training",
                ]
                + NEGLECT_PAGE_HEADER_PATTERN,
                "sub_syllabus": [
                    r"Normalized Management of the Pandemic",
                    r"Staff Health Protection",
                    r"Fire Safety Management",
                    r"Staff Communication and Care",
                ],
            },
            {
                "name": "ar_esg_b23",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "multi": True,
                "inject_syllabus_features": [
                    r"__regex__Health and Safety__regex__Monitoring System",
                    r"__regex__Health and Safety__regex__Safety Organisation",
                    r"__regex__Health and Safety (train|Measures)",
                    r"__regex__Precautions Against Covid-19",
                    r"__regex__(Prevent|Response).*?(of|for).*?Covid[-\s]?19",
                    r"__regex__Employee Health and Care",
                    r"__regex__Occupational Hea\s?lth and Safety",
                    r"__regex__Work Health",
                ],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__Health and Safety",
                    r"__regex__Health & Safety",
                    r"__regex__(Prevent|Response).*?(of|for|to).*?Covid[-\s]?19",
                ],
                "neglect_parent_features": [
                    r"Product Responsibility",
                    r"Contacting Customers",
                ],
                "multi": True,
                "multi_elements": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"COVID-?19|preventive measures|facilities|safety performance",
                        r"safety training",
                        r"safety monitoring mechanism",
                        r"healthy and safe|occupational|safety|measures|training|check|daily|provide|organize|required|first-aid",
                        r"strictly abided to relevant preventive",
                        r"conduct fire drills",
                        r"take various measures to minimise",
                        r"health and safety measures",
                    ),
                    "neglect_pattern": (
                        r"^Aspect B2:\s?Health and Safety$",
                        r"comply with relevant laws and regulations.*?Health Ordinance",
                        r"^during the year",
                        r"^During the Reporting Period",
                        r"number of work injuries",
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "para_match",
                "multi_elements": True,
                "paragraph_pattern": (
                    r"(?P<content>COVID 19|preventive measures|facilities|safety performance)",
                    r"(?P<content>safety training)",
                    r"(?P<content>safety monitoring mechanism)",
                ),
            },
            {
                "name": "syllabus_elt_v2",
                "only_inject_features": True,
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "inject_syllabus_features": [
                    # r'__regex__Improvement of HSE System',
                    r"__regex__^B2.*?Health and Safety",
                    r"__regex__II.2.*?Health and Safety",
                    r"__regex__B\(II.*?HEALTH AND SAFETY",
                    # r'__regex__Occupational Health and Safety',
                ],
            },
            DEFAULT_MODEL,
        ],
    },
]
