"""
Jura4 Annual Report ESG
"""

from remarkable.predictor.hkex_predictor.schemas.ar_esg_schema.common_pattern import (
    DEFAULT_MODEL,
    NEGLECT_PAGE_HEADER_PATTERN,
)

predictor_options = [
    {
        # 提取内容：
        # 涉及resources政策的段落，段落中常包含 resource，energy，usage，water consumption等词语。
        # 在有包含use/usage+resources标题时，可提取该标题下的全部内容。在有index的情况下，去index指明的位置提取会更准确
        # 当没有use/usage+resources相关的标题时，可提取energy，water，paper等相关标题下的措施
        # E的判断：
        # 【简单情况】：not disclosed,not available, NA,
        # 该规则E的情况在标注过程中还没有遇到过
        # Y的判断：
        # 有：resources，energy，water，paper等相关的policy或management或measure或reduce或control等措施的描述。
        "path": ["A2 policies - use of resources"],
        "models": [
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                # "only_before_first_chapter": True,
                "only_inject_features": True,
                # "only_first": True,
                "inject_syllabus_features": [
                    r"__regex__Use of Resources",
                    r"__regex__(use|usage).*?resources",
                    r"__regex__Emissions and Use of resources",
                    r"__regex__Use of Resources and Management",
                    r"__regex__utilisation.*?resources",
                    r"__regex__Resources.*?Utilisation",
                ],
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_before_first_chapter": True,
                "multi": True,
                "break_para_pattern": [
                    r"Overview of key performance indicators",
                ],
                "inject_syllabus_features": [
                    r"__regex__(Energy|Water) (Conservation|management|Consumption|resource)",
                    r"__regex__Packaging Materials",
                    r"__regex__(energy usage|consumption|paper|ELECTRICITY)",
                    # r'__regex__resource',
                ],
            },
            {
                "name": "kmeans_classification",
                "filter_content_answer": True,
            },
            {
                "name": "para_match",
                "multi_elements": True,
                "enum_from_multi_element": True,
                "paragraph_pattern": (
                    r"(?P<content>measures|policy|resource|energy|usage|consumption)",
                    r"turn off (lights|air-conditioners)|energy efficiency|energy conservation|switch off|avoid printing",
                    r"LED lighting|effectiveness|reduction|recycle|reuse|save|waste|garbage|electricity|power|solar|digitalise|wastepaper|renewable|new energy",
                ),
            },
            DEFAULT_MODEL,
        ],
    },
    {
        # 直接或间接能源消耗按类型（例如电力、天然气或石油）的总量('000 千瓦时）和强度（例如每单位产量、每个设施）。
        # 一般搜索关键词energy consumption、electricity、gas、oil、indirect、direct
        #  所在位置：1、优先index或者data summary表格中；
        #           2、其次文档其他位置，根据index给出对的KPI位置去定位框选；
        #           3、有关键词的段落、表格：`energy consumption`、`A2.1`
        #
        #  判断依据：1、若报告披露`公司执行了环保政策，能源的消耗对公司来说not a concern”或“not available”或
        #  “公司正在建相关的信息收集系统，下一年会有相关信息披露`，算E；
        #           2、披露了energy的类型及数据的算Y；
        #           3、没有披露相关内容，也没有解释，算ND
        "path": ["KPI A2.1 - energy consumption"],
        "models": [
            {
                "name": "ar_esg_multi_table",
                "title_regs": [
                    r"SUMMARY\s?OF ENVIRONMENTAL PERFORMANCE",
                    r"^7. Significant increase.*?dded cinemas in the reporting scope",
                ],
                "start_regs": [r"A2.1"],
                "end_regs": [
                    r"A2.2",
                ],
            },
            {
                "name": "after_row_match",
                "row_pattern": [r"A\s?2\s?.1"],
                # 'para_pattern': [r'A\s?1\s?.4|hazardous waste'],
                # 'table_title': [
                #     r'non-hazardous',
                # ],
                "middle_rows": True,
                "start_regs": [r"Energy"],
                "end_regs": [r"non-hazardous"],
            },
            {
                "name": "first_table",
                "title_regs": [
                    "本集團的能源消耗表現概要",
                ],
            },
            {
                "name": "split_first_table",
                "regs": [
                    r"Energy|Electricity",
                ],
                "start_regs": [
                    r"energy consumption",
                    r"Energy",
                    r"electricity consumption",
                    r"electricity.*?processing",
                ],
                "end_regs": [
                    r"Water Consumption",
                    r"hazardous waste",
                    r"non-hazardous",
                ],
            },
            {
                "name": "row_match",
                "multi": True,
                "ignore_index_table": True,
                "neglect_row_pattern": [
                    r"Energy indirect emissions",
                ],
                "row_pattern": [
                    r"A2.1",
                    r"Energy|Electricity",
                    r"LPG",
                    r"Petrol",
                ],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    "__regex__KPI A2.1",
                    "__regex__nergy Consumption",
                    "__regex__Use of Energy and Resources",
                ],
                "only_inject_features": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"energy consumption.*?not material",
                        r"electricity consumption amounted",
                        r"total energy consumption.*?was.*?\d",
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "para_match",
                "multi_elements": True,
                "enum_from_multi_element": True,
                "paragraph_pattern": (
                    r"(?P<content>not available|not a concern)",
                    r"(?P<content>oil|petrol|gasoline)",
                    # r'(?P<content>energy|electricity|gas)',
                    r"(?P<content>A2.1|consumption|indirect|direct)",
                    r"(?P<content>During the Year.*?consumed.*?electricity)",
                    r"(?P<content>During the Year.*?consumed.*?gas oil)",
                ),
            },
            DEFAULT_MODEL,
        ],
    },
    {
        # A2.2 总耗水量和强度（例如，每单位产量、每项设施）。
        # 一般搜索关键词A2.2、Water consumption
        #  所在位置：1、优先index或者data summary表格中；
        #           2、其次文档其他位置，根据index给出对的KPI位置去定位框选；
        #           3、有关键词的段落、表格
        #
        #  判断依据：1、若报告披露“水资源的消耗不重大”或者“由物业等第三方公司处理相关water或not applicable”，算E；
        #           2、披露了water的类型及数据的算Y；
        #           3、没有披露相关内容，也没有解释，算ND
        "path": ["KPI A2.2 - water consumption"],
        "models": [
            {
                "name": "ar_esg_multi_table",
                "title_regs": [
                    r"SUMMARY\s?OF ENVIRONMENTAL PERFORMANCE",
                    r"^7. Significant increase.*?dded cinemas in the reporting scope",
                ],
                "start_regs": [r"A2.2"],
                "end_regs": [],
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"water usage data.*?not feasible",
                    r"no individual water consumption data",
                    r"use of water is not significant",
                    # r'Water consumption.*?insignificant',
                    r"not possess information.*?water consumption for disclosure",
                    r"unable to provide.*?water consumption",
                    r"does not have details of the exact amount used",
                    r"due to our business nature.*?not consume.*?water",
                ),
            },
            {
                "name": "after_row_match",
                "row_pattern": [
                    r"A2.2",
                ],
                "para_pattern": [
                    r"no individual water consumption data",
                ],
            },
            {
                "name": "first_table",
                "title_regs": [
                    "本集團的用水表現概要",
                    "water consumption",
                ],
            },
            {
                "name": "split_first_table",
                "regs": [
                    r"A2.2",
                    r"water consumption",
                ],
                "start_regs": [
                    r"A2.2",
                ],
                "end_regs": [
                    r"A2.[345]",
                    r"packaging material",
                ],
            },
            {
                "name": "row_match",
                "ignore_index_table": True,
                "row_pattern": [
                    r"water\s",
                    r"Water Consumption",
                    r"A2.2",
                    r"^water$",
                ],
                "multi": True,
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    "__regex__KPI A2.2",
                    "__regex__Wat.*?er Consumption",
                ],
                "only_inject_features": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"water consumption.*?not available",
                        r"water consumption.*?approximately",
                        r"Water usage in the Group.*?is minimal",
                        r"Water consumption.*?tonnes",
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "para_match",
                "multi_elements": True,
                "enum_from_multi_element": True,
                "paragraph_pattern": (
                    # r'not applicable|no significant',
                    # r'no material',
                    r"(?P<content>A2.2|Water consumption|water consumed)",
                ),
                "neglect_pattern": (r"To reduce the water consumption",),
            },
        ],
    },
    {
        # A2涉及到的规则都是关于资源使用效率相关的内容；A2.3需要框选出公司为实现节能而设置的节能目标，或者为了节能而采取的措施；
        # 如果都没有也可以框选已经实现的节能目标，可以不是对未来目标的设置。
        #  所在位置：1. 涉及到use of resources的标题下关于energy efficiency的文字段落
        #           2. 在use of resources的标题下有关键词energy management的小标题下的内容
        #           3. 如果按照energy的种类披露的，一般会涉及到electricity，fuel，natural gas的reduction/saving/conservation内容
        #           4. 在ESG报告中单独有个environmental target的标题下，关键词energy的相关内容
        #
        #  提取内容：1. 提取涉及到“energy efficiency target”的数据
        #           2. 没数据但有“为了energy conservation 而有一些measures”的描述
        #           3. 当年或目前为止已经实现的energy efficiency target，可以不是未来的target
        #           4. 如果上面几点都有，则可以按照上面序号的优先级来全部提取
        #
        #  关键词：（需要在energy相关位置范围内查找）target, efficiency, establish, goal, aim, future plan,
        #  energy conservation, reduction, prevent
        #
        #  判定方式：Y-披露并描述以上的提取内容（满足提取内容的任何一条或多条即可）
        #           E-明确披露了 not material/not involve/not engage/ not disclosed/ not produce的
        #           E-写明了`没有establish energy efficiency target`的，即使有相关措施，也判定为E
        #           ND-没有任何描述
        "path": ["KPI A2.3 - energy efficiency targets"],
        "models": [
            {
                "name": "row_match",
                "ignore_index_table": True,
                "row_pattern": [
                    r"Energy Conservation",
                    r"A2.3",
                ],
                # 'multi': True,
            },
            {
                "name": "after_row_match",
                "row_pattern": [r"A2.3"],
                # 'para_pattern': [r'water source'],
            },
            {
                "name": "syllabus_elt_v2",
                "only_inject_features": True,
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "inject_syllabus_features": [
                    r"__regex__energy conservation",
                    r"__regex__Energy Use Efficiency Initiatives",
                    r"__regex__sustainability target",
                    r"__regex__Energy Conservation.*?KPI A2\.3",
                    r"__regex__Electricity and Water Management",
                ],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__Energy Consumption",  # uat 58433
                    r"__regex__Energy usage",
                    r"__regex__use of resources?",  # uat 58426
                    r"__regex__Use of Natural Resources?",
                    r"__regex__Electricity",  # 60875
                    r"__regex__Fuel",  # 60875
                    r"__regex__A2\.3.*?energy",
                ],
                # 'multi': True,
                "only_inject_features": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"group targets? to reduce",
                        r"Group closely monitors its energy usage to consumption",
                        r"our aim is",
                        r"energy conservation",
                        r"set targets to",
                        r"targets? (is )?to reduce",
                        r"targeted a reduction",
                        r"has set a target",
                        r"set.*?reduction target",
                        r"set target to reduce waste",
                        r"no specific reduction target",
                        r"target",
                        r"employing various initiatives and measures",
                        r"implemented.*?as follows:",
                        r"to improve our energy efficiency",
                        r"plan is to reduce energy",
                        r"in order to.*?energy efficiency",
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "syllabus_elt_v2",
                # "only_first": True,  # id  60856 取的是整个章节
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "inject_syllabus_features": [
                    r"__regex__A2\.3.*?energy",
                    r"__regex__energy conservation",
                    r"Energy",  # 63571
                    r"__regex__KPI A2\.3.*?energy",
                    "__regex__nergy Consumption",
                    r"__regex__USE OF RESOURCES",  # 60879
                    r"__regex__Energy Management",
                    r"__regex__Electricity and Energy Usage",
                ],
            },
            {
                "name": "para_match",
                "multi_elements": True,
                "enum_from_multi_element": True,
                "paragraph_pattern": (
                    r"(?P<content>not set|not establish)",
                    r"(?P<content>target|goal|reduce|measure|establish|set|energy|efficiency)",
                ),
            },
            # DEFAULT_MODEL,
        ],
    },
    {
        # 该条规则需要框选公司遇到的取水问题，取水的水源。
        # Y的表述
        #  報告中有“明确表示没有遇到取水方面的问题” 或者水源取自“municipal water，ground water，或recycled water”或者
        # 有關“取水遇到的問題”的披露，
        #
        #  Y的常见表述：not encounter, not deal with, not face, no issue, source,municipal water,
        #  ground water, water sourcing, recycle，water supply by xxx
        # E的表述：
        #  报告中有解释水源與公司、業務不相關，沒有重大影響，
        #
        #  E的常见表述： irrelevant, non-material, no significant impact, not applicable, N/A
        "path": ["KPI A2.4 part 1 - water sourcing"],
        "models": [
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"water conservation target",
                    r"sources water from.*?no problem",
                    r"water consumption.*?insignificant",
                    r"no sourcing issue",
                    r"sources water from the municipal supplies",
                ),
            },
            {
                "name": "after_row_match",
                "row_pattern": [r"A2.4"],
                "para_pattern": [r"water source"],
                "direct_answer_pattern": [
                    r"No.*?target is set",
                    r"not set.*?targets",
                ],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": ["__regex__KPI A2.4"],
                "only_inject_features": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (r"water consumption.*?not available",),
                },
                "table_model": "empty",
            },
            {
                "name": "para_match",
                # "multi_elements": True,
                # "enum_from_multi_element": True,
                "paragraph_pattern": (
                    r"(?P<content>not encounter|not deal with|not face|no issue|sourcing water)",
                    r"(?P<content>municipal water|ground water|sourcing water|water sourcing)",
                    r"(?P<content>not have problems.*?water resources)",
                ),
            },
            {
                "name": "kmeans_classification",
                "para_pattern": [
                    r"water source|sources water|sourced water|sourcing suitable water",
                    r"Only water suppl",
                ],
            },
            # DEFAULT_MODEL,
        ],
    },
    {
        # A2涉及到的规则都是关于资源使用效率相关的内容；A2.4-part2需要框选出公司为节水而设置的节水目标，或者为了节水而采取的措施；
        # 如果都没有也可以框选已经实现的节水目标，可以不是对未来目标的设置。
        #  所在位置：1. 涉及到use of resources的标题下关于water efficiency的文字段落
        #           2. 在use of resources的标题下有关键词water management/ water usage的小标题下的内容
        #           3. 在ESG报告中单独有个environmental target的标题下，关键词water的相关内容
        #
        #  提取内容：1. 提取涉及到“water efficiency/saving target”的数据
        #           2. 没数据但有“为了water conservation/saving 而有一些measures”的描述
        #           3. 当年或目前为止已经实现的water efficiency target，可以不是未来的target
        #           4. 如果上面几点都有，则可以按照上面序号的优先级来全部提取
        #
        #  关键词：（需要在water相关位置范围内查找）target ,conserve water, water-efficient, water scarcity issue, establish,
        #  goal, aim, future plan,water conservation, reduction, prevent, decreased, avoid
        #
        #  判定方式：Y-披露并描述以上的提取内容（满足提取内容的任何一条或多条即可）
        #           E-明确披露了 not material/not involve/not engage/ not disclosed/ not produce的
        #           E-写明了`没有establish water efficiency target`的，即使有相关措施，也判定为E
        #           ND-没有任何描述
        "path": ["KPI A2.4 part 2 - water efficiency targets"],
        "models": [
            {
                "name": "row_match",
                "ignore_index_table": True,
                "row_pattern": [
                    r"water Conservation",
                    r"conserving water resource",
                ],
            },
            {
                "name": "after_row_match",
                "row_pattern": [r"A2.4"],
                # 'para_pattern': [r'water source'],
            },
            {
                "name": "para_match",
                "anchor_regs": (r"water.*?target",),
                "paragraph_pattern": (r"To reduce the water",),
            },
            {
                "name": "syllabus_elt_v2",
                "only_inject_features": True,
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "inject_syllabus_features": [
                    r"__regex__sustainability target",
                    r"__regex__Water Use Efficiency Initiatives",
                    r"__regex__Environmental Targets",
                    r"__regex__use of resource__regex__(water management|water usage)",
                ],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__Water Consumption$",
                    r"__regex__SAFEGUARDING THE ENVIRONMENT",
                    r"__regex__water.*?targets",
                    r"__regex__KPI A2.4",
                    r"__regex__Water management$",
                    r"__regex__sustainability target",
                    r"__regex__use of resource__regex__(^water|water$)",
                    r"__regex__environmental__regex__(^water|water$)",
                    r"__regex__Use of Resources",
                    r"__regex__EMISSION",
                ],
                "only_inject_features": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"water consumption.*?not available",
                        r"Waste management.*?not been disclosed",
                        r"set a reduction target to reduce the water",
                        r"in order to reduce.*?water",
                        r"(reuse|recycling).*?water",
                        r"(saving|save|conserving) water",
                        r"reducing.*?waste.*?water",
                        r"water efficiency",
                        r"water Conservation",
                        r"target is.*?water",
                        r"plan is to reduce.*?water",
                        r"^•.*?water",
                        r"cannot control what water saving measures",
                        r"return water is discharge",
                        r"have established a water-saving supervision",
                        r"reduce unnecessary water consumption",
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "para_match",
                "multi_elements": True,
                "enum_from_multi_element": True,
                "paragraph_pattern": (
                    r"(?P<content>not encounter|not deal with|not face|no issue|sourcing water)",
                    r"(?P<content>municipal water|ground water|sourcing water|water sourcing)",
                    r"(?P<content>water conservation)",
                    r"(?P<content>conserving clean water)",
                    r"(?P<content>saving water|save water)",
                    r"(?P<content>data on water consumption is not available)",
                    # r'(?P<content>target|source)',
                    r"(?P<content>saving.*?methods)",
                    r"(?P<content>saving.*?instruments)",
                    r"reducing.*?waste.*?water",
                    r"reduce unnecessary water consumption",
                ),
            },
            {
                "name": "kmeans_classification",
                "threshold": 0.618,
            },
            # DEFAULT_MODEL,
        ],
    },
    {
        "path": ["KPI A2.5 - packaging material"],
        "models": [
            {
                "name": "middle_paras",
                "use_syllabus_model": True,
                "include_top_anchor": False,
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "inject_syllabus_features": [
                    r"__regex__pack(?:ag)?ing material",
                ],
                "top_anchor_regs": [
                    r"pack(?:ag)?ing material.*as (below|follow(ing)?)",
                ],
                "bottom_anchor_regs": [
                    r"Notes?.*?pack(?:ag)?ing material",
                ],
            },
            {
                "name": "after_row_match",
                "row_pattern": [r"A2.5"],
                "index_header_patterns": [
                    r"\bKPI\b",
                ],
                "dst_row_patterns": [r"packaging material", r"packing material"],
                # 拆分索引单元格内容
                "split_cell_pattern": r"－|—|:|：|\d+\.(?:\d+)?",
            },
            {
                "name": "first_table",
                "regs": [
                    r"packaging material",
                    r"packing material",
                ],
                "title_regs": [
                    r"packaging material",
                    r"packing material",
                ],
            },
            {
                "name": "split_first_table",
                "second_table": True,
                "regs": [
                    r"packaging material",
                    r"packing material",
                ],
                "start_regs": [
                    r"packaging material",
                    r"packing material",
                ],
                "end_regs": [],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    "__regex__KPI A2.5",
                    "__regex__Packaging",
                ],
                "only_inject_features": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"pack(?:ag)?.*?not material",
                        r"total pack(?:ag)?ing material used",
                        r"The Group.*?setting up comprehensive pack(?:ag)?ing materials.*?system",
                        r"not involve using packaging materials",
                        r"KPI A2.5 are not applicable",
                        r"no.*?significant",
                        r"Due the Group’s business",
                    ),
                },
                "table_model": "first_table",
                "table_config": {"regs": [r"pack(?:ag)?ing material"], "title_regs": [r"pack(?:ag)?ing material"]},
            },
            {
                "name": "para_match",
                "multi_elements": True,
                "enum_from_multi_element": True,
                "paragraph_pattern": (
                    # 'not involve|not produce|not applicable|non-material|not disclosed',
                    r"pack(?:ag)?ing material",
                    r"none?-material",
                ),
            },
            {
                "name": "row_match",
                "row_pattern": [
                    r"KPI\sA2\.5",
                    r"packaging material",
                ],
            },
            # DEFAULT_MODEL,
        ],
    },
]
