"""
Jura4 Annual Report ESG
"""

from remarkable.predictor.hkex_predictor.schemas.ar_esg_schema.common_pattern import (
    NEGLECT_PAGE_HEADER_PATTERN,
)

predictor_options = [
    {
        "path": ["A4 policies - climate-related issues"],
        "models": [
            {
                "name": "para_match",
                "paragraph_pattern": (r"formulated working mechanisms.*?climate change issues",),
            },
            {
                "name": "shape_title",
                "regs": (r"Transition Risks",),
            },
            {
                "name": "policy_row_match",
                "multi": True,
                "multi_elements": True,
                "row_pattern": ["^General Disclosure"],
                "anchor_pattern": ["Aspect A4"],
            },
            {
                "name": "syllabus_elt_v2",
                "multi": True,
                "include_title": True,
                "include_shape": True,
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN
                + [
                    r"B.*?SOCIAL$",
                ],
                "inject_syllabus_features": [
                    r"__regex__KPI A4(.1)?",
                    r"__regex__climate change",
                    r"__regex__The Group.s response",
                    r"__regex__Climate-related Risks, Opportunities, and Financial impact",
                    r"__regex__Physical risks",
                    r"__regex__Transition(?:al)? risks",
                    r"__regex__respond to climate change",
                    r"__regex__Tackling Climate Change",
                ],
                "neglect_patterns": [
                    r"Employment",
                ],
                "break_para_pattern": [r"^[\u4e00-\u9fa5]{1,}"],
            },
            {
                "name": "para_match",
                "multi_elements": True,
                "enum_from_multi_element": True,
                "paragraph_pattern": (
                    r"(?P<content>not direct impact|not disclosed|of little significance|not involve|not produce|not applicable|non-material|not disclosed)",
                    r"(?P<content>identified|recognising|climate change|respond|climate|measure)",
                    r"there are policies",
                ),
            },
            # DEFAULT_MODEL,
        ],
    },
    {
        "path": ["KPI A4.1 - climate-related issues & impact"],
        "models": [
            {
                "name": "para_match",
                "paragraph_pattern": (r"formulated working mechanisms.*?climate change issues",),
            },
            {
                "name": "shape_title",
                "regs": (r"Transition Risks",),
            },
            {
                "name": "policy_row_match",
                "multi": True,
                "multi_elements": True,
                "row_pattern": ["^General Disclosure"],
                "anchor_pattern": ["Aspect A4"],
            },
            {
                "name": "syllabus_elt_v2",
                "multi": True,
                "include_title": True,
                "include_shape": True,
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN
                + [
                    r"B.*?SOCIAL$",
                ],
                "inject_syllabus_features": [
                    r"__regex__KPI A4.1",
                    r"__regex__climate change",
                    r"__regex__Climate-related Risks, Opportunities, and Financial impact",
                    r"__regex__Action on Climate Change",
                    r"__regex__The Group.s response",
                    r"__regex__Physical risks",
                    r"__regex__Transition(?:al)? risks",
                    r"__regex__respond to climate change",
                    r"__regex__Tackling Climate Change",
                ],
                "neglect_patterns": [
                    r"Employment",
                ],
                "break_para_pattern": [r"^[\u4e00-\u9fa5]{1,}"],
            },
            {
                "name": "para_match",
                "multi_elements": True,
                "enum_from_multi_element": True,
                "paragraph_pattern": (
                    r"(?P<content>not direct impact|not disclosed|of little significance|not involve|not produce|not applicable|non-material|not disclosed)",
                    r"(?P<content>identified|recognising|climate change|respond|climate|measure)",
                    r"no significant climate-related issues",
                ),
            },
            # DEFAULT_MODEL,
        ],
    },
]
