"""
Jura4 Annual Report ESG
"""

from remarkable.predictor.hkex_predictor.schemas.ar_esg_schema.common_pattern import (
    DEFAULT_MODEL,
    NEGLECT_PAGE_HEADER_PATTERN,
)

predictor_options = [
    {
        # 社区参与相关的政策，以了解发行人经营所在社区的需求，并确保其活动考虑到社区的利益。
        # （关键词：community）
        # 一般在community investment/public welfare/Giving back to society/the community相关标题下的第一个段落描述，
        # 或第一个小标题下内容。E描述不常见。
        "path": ["B8 policies - community investment"],
        "models": [
            # 特例 GRI 相关 https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/997#note_243521
            {
                "name": "syllabus_based",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "inject_syllabus_features": [
                    r"__regex__FOCUS 5: OUR COMMUNITY",
                ],
                "multi_elements": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"engagement|focus|key|dedicate|B8.1|community",
                        r"giving back to society",
                        r"inculcate greater environmental awareness",
                        r"GRI 413-1",
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "policy",
                "only_inject_features": True,
                "ignore_pattern_match": True,
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "inject_syllabus_features": [
                    r"__regex__Community Investment__regex__General Disclosure and KPIs",
                    r"__regex__Aspect B8",
                    r"__regex__COMMUNITY INVESTMENT",
                ],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__Aspect B8: Community Investment",
                    r"__regex__COMMUNITY INVESTMENT",
                ],
                "multi_elements": True,
                "only_before_first_chapter": True,
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"(?P<content>engagement|focus|key|dedicate|commit|B8.1|community)",
                        r"(?P<content>encouraging.*?communities.)",
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"(?P<content>engagement|focus|key|dedicate|commit|B8.1|community)",
                    r"(?P<content>encourag.*?(communit|society))",
                ),
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
            },
            DEFAULT_MODEL,
        ],
    },
    {
        # 公司重点在哪些领域做贡献，例如：education, environmental concerns, labour needs, health, culture, sport。
        # （关键词：engagement, focus, key, dedicate，commit）如果没有描述重点领域，可框选B8.2的内容
        "path": ["KPI B8.1 - community investment focus"],
        "models": [
            {
                "name": "after_row_match",
                "row_pattern": [r"B8.1"],
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "para_pattern": [
                    r"engagement|focus|key|dedicate|B8.1|community",
                    r"community development",
                    r"community investment",
                    r"(?P<content>encouraging.*?communities)",
                ],
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "multi": True,
                "inject_syllabus_features": [
                    r"__regex__Community Investment__regex__KPI B8\.1",
                    r"__regex__Community Engagement",
                ],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__kpi B8.1",
                    r"__regex__COMMUNITY INVESTMENT",
                ],
                "multi_elements": True,
                "break_when_table": True,
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"engagement|focus|key|dedicate|commit|B8.1",
                        r"(?P<content>encouraging.*?communities)",
                        r"(?P<content>made donations)",
                        r"(?P<content>contribute to the society)",
                        r"for social responsibilities purposes",
                        r"distribute gifts and coupons",
                        r"donat.*?to",
                        r"made great efforts in education",
                        r"education|environmental concerns|labour needs|health|culture|sport",
                        r"contribute to our community",
                        r"support community programmes",
                    ),
                },
                "table_model": "table_title",
                "table_config": {
                    "only_inject_features": True,
                    "feature_white_list": [
                        r"made donations",
                        r"捐贈",
                    ],
                },
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"(?P<content>engagement|focus|key|dedicate|commit|B8.1|community)",
                    r"(?P<content>encourag.*?(communit|society))",
                ),
            },
            DEFAULT_MODEL,
        ],
    },
    {
        # 为重点领域贡献的资源（例如金钱或时间），优先提取index，再到data summary，最后正文。
        # （关键词：community，donation，prepare，provide, distribute）。
        # 表示未来计划会关注或投入community的描述，属于E（in the future, plan等），
        # 其他属于E的关键词还有stop, suspended, did not engage等
        "path": ["KPI B8.2 - resources contributed"],
        "models": [
            {
                "name": "after_row_match",
                "row_pattern": [r"B8.2"],
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "para_pattern": [
                    r"(?P<content>donation|prepare|provide|distribute|dedicate|B8.2)",
                    r"(?P<content>prepare|provide|distribute)",
                ],
                "middle_rows": True,
                "start_regs": [r"B8.2"],
                "end_regs": [],
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "break_when_table": True,
                "multi": True,
                "inject_syllabus_features": [
                    r"__regex__Community Investment__regex__KPI B8\.2",
                ],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__kpi B8.2",
                    r"__regex__COMMUNITY INVESTMENT",
                ],
                "multi_elements": True,
                "break_when_table": True,
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"(?P<content>donation|provide|distribute|dedicate|B8.2)",
                        r"(?P<content>made donations)",
                        r"(?P<content>cash donations)",
                        r"donat.*?to",
                        r"for social responsibilities purposes",
                        r"distribute gifts and coupons",
                        r"participated in",
                        r"delivered to the needy",
                        r"\$[\d,]+",
                        r"contribute to our community",
                        r"support community programmes",
                        # r'(?P<content>contribution)',  # 遇到badcase 添加更详细的正则
                        # r'(?P<content>prepare)',  # 遇到badcase 添加更详细的正则
                    ),
                },
                "table_model": "table_title",
                "table_config": {
                    "only_inject_features": True,
                    "feature_white_list": [
                        r"made donations",
                        r"捐贈",
                    ],
                },
            },
            {
                "name": "para_match",
                # 'multi': True,
                "paragraph_pattern": (
                    r"\$[\d,]+",
                    r"(?P<content>community|donation|prepare|provide|distribute|B8.2)",
                    r"(?P<content>contribution)",
                    r"(?P<content>prepare|provide|distribute)",
                    r"(?P<content>cash donations)",
                ),
            },
            DEFAULT_MODEL,
        ],
    },
]
