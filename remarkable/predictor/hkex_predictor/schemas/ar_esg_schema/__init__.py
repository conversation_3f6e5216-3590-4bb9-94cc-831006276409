# Jura4 ESG
import importlib
import pkgutil

__all__ = ["predictor_options", "prophet_config"]


predictor_options = []

for _, name, _ in pkgutil.walk_packages([__path__[0]], prefix=__name__ + "."):  # type: ignore
    module = importlib.import_module(name)
    if not name.split(".")[-1].endswith("schema"):
        continue
    predictor_options.extend(module.predictor_options)

prophet_config = {"depends": {}, "predictor_options": predictor_options}
