from remarkable.common.constants import AnswerValueEnum, CategoryTypeEnum
from remarkable.common.pattern import PositionPattern
from remarkable.predictor.hkex_predictor.schemas.policy_esg_prompt import (
    policy_esg_e5_prompt,
    policy_esg_e6_prompt,
    policy_esg_e8_prompt,
)
from remarkable.predictor.hkex_predictor.schemas.policy_esg_util import check_e7_answer, is_invalid_e7_answer

DIRECTORY_START = r"^\d+\.\d+.(\d+)?"  # 目录中的段落
R_T7_SKIP_SYLLABUS = [
    "Appendix.*Glossary",
    "AppendiApnix.*Glpedxossary",
]

E1_SYLLABUS = [
    r"^Guidance referenced$",
    r"^Preparation (standards?|Basis)$",
]

R_E1_STANDARD = (
    r"(\bIFRS\b|\bIFRS\b|International Sustainability Standards Board|International Financial Reporting Standards)"
)

R_E7_SCOPE_CAT = "|".join(category.value for category in CategoryTypeEnum)

predictor_options = [
    {
        "path": ["E1-Reference to ISSB Standards"],
        "models": [
            # 1. 编制标准、指南(Guidance referenced/Preparation standards) 等章节下的 ISSB/IFRS
            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7377#note_738737
            {
                "name": "para_match",
                "enum": AnswerValueEnum.COMPLY.value,
                "extend_candidates_syllabus_regs": E1_SYLLABUS,
                "syllabus_regs": E1_SYLLABUS,
                "paragraph_pattern": [R_E1_STANDARD],
                "parent_features": [r"^about\s*(our|the|this)\s*report$"],
                "parent_must_be_root": True,
            },
            {
                "name": "policy_esg_e1",
            },
        ],
    },
    {
        "path": ["E2-Adoption of Independent Assurance"],
        "element_candidate_count": 5,
        "models": [
            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7491#note_744261
            {
                "name": "row_match",
                "extend_candidates_syllabus_regs": [
                    r"^GRI CONTENT INDEX$",
                ],
                "row_pattern": [
                    PositionPattern.compile(
                        r"External assurance",
                        r"Scope 1 emissions, Scope 2 emissions",
                        r"\bhas verified",
                        r"the GHG statement",
                    ),
                ],
                "enum": AnswerValueEnum.COMPLY.value,
            },
            {
                "name": "policy_esg_e2",
            },
        ],
    },
    {
        "path": ["E3-Details of Independent Assurance"],
        "models": [
            {
                # http://************:55647/#/hkex/esg-report-checking/report-review/306967?fileId=71290&schemaId=2&rule=E3-Details%20of%20Independent%20Assurance&delist=0
                "name": "syllabus_elt_v2",
                "enum": AnswerValueEnum.COMPLY.value,
                "only_inject_features": True,
                "include_shape": True,
                "multi": True,
                "inject_syllabus_features": [
                    r"__regex__Independent Assurance Opinion Statement__regex__Scope",
                    r"__regex__Independent Assurance Opinion Statement__regex__Opinion Statement",
                    r"__regex__Independent Assurance Opinion Statement__regex__Methodology",
                    # r"__regex__Independent Assurance Opinion Statement__regex__Limited Sustainability Report__regex__scope",
                    r"__regex__Independent Assurance Opinion Statement__regex__Limited Sustainability Report",
                ],
                "break_para_pattern": [
                    r"^ASSURANCE OPINION$",
                    r"Conclusions?$",
                    r"^ESG review.*Environmental$",
                ],
            },
            {
                "name": "para_match",
                "extend_candidates_syllabus_regs": [
                    r"^Performance data$",
                    r"^Our approach$",
                ],
                "multi_elements": True,
                "paragraph_pattern": (
                    r"The limited assurance report can be found on our website",
                    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6979#note_714950
                    r"We seek third party assurance[^.]*?performance data",
                    r"(data points[^.]*?limited assurance|sustainability data that has been reported).*?(see|refer to) the Independent (limited\s*)?Assurance Report",
                ),
                "enum": AnswerValueEnum.COMPLY.value,
            },
            {
                "name": "policy_esg_e3",
                "from_answer_value": AnswerValueEnum.COMPLY.value,
            },
        ],
    },
    {
        "path": ["E4-Independent Assurance on scope 1 and scope 2 GHG emissions"],
        "models": [
            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7491#note_744243
            {
                "name": "para_match",
                "extend_candidates_syllabus_regs": [
                    r"^Climate-Related Risks and Opportunities$",
                ],
                "paragraph_pattern": (
                    r"we conducted carbon accounting across the Company and engaged a third-party certification body to verify our GHG emissions",
                ),
                "enum": AnswerValueEnum.COMPLY.value,
            },
            {
                "name": "policy_esg_e4",
            },
        ],
    },
    {
        "path": ["E5-Scenario analysis"],
        "models": [
            {
                "name": "policy_esg_e56",
                "prompt": policy_esg_e5_prompt,
            },
            {
                "name": "syllabus_elt_v2",
                "enum": AnswerValueEnum.COMPLY.value,
                "only_inject_features": True,
                "include_shape": True,
                "inject_syllabus_features": [
                    r"__regex__scenario analysis",
                ],
                # "break_para_pattern": [r"^ASSURANCE OPINION$"],
            },
        ],
    },
    {
        "path": ["E6-Source of scenarios"],
        # IPCC IEA NGFS
        "models": [
            {
                "name": "policy_esg_e56",
                "prompt": policy_esg_e6_prompt,
            }
        ],
    },
    {
        "path": ["E7-Scope 3 emissions"],
        "post_process": check_e7_answer,
        "models": [
            {
                "name": "scope",
                "neglect_syllabus_regs": R_T7_SKIP_SYLLABUS,
                "regs": [
                    r"Scope 3",
                ],
                "start_regs": [
                    r"^Scope 3",
                ],
                "end_regs": [
                    r"^Scope 3",
                    r"A1\.3",
                ],
                "enum": AnswerValueEnum.COMPLY.value,
            },
            {
                "name": "row_match",
                "neglect_syllabus_regs": R_T7_SKIP_SYLLABUS,
                "first_cell": False,
                "multi": True,
                "row_pattern": [
                    r"^(Scope 3|Scope III).*\d[,\.]\d",
                    r"^3.*Categor(y|ies)\s*\d[,\.]\d",
                ],
                "enum": AnswerValueEnum.COMPLY.value,
            },
            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6347#note_690134
            {
                "name": "middle_rows",
                "multi_elements": True,
                "neglect_syllabus_regs": R_T7_SKIP_SYLLABUS,
                "filter_func": is_invalid_e7_answer,
                "regs": [
                    r"Scope 3",
                ],
                "title_regs": [r"TABLE OF KEY PERFORMANCE"],
                "start_regs": [
                    r"Scope\s*3.*no data has been collected",
                    r"Purchased goods & services \(Cat 1\)",
                ],
                "end_regs": [
                    r"(Scope\s*3|305-4).*emissions intensity",
                    r"Energy Consumed and Generated",  # http://************:55647/#/hkex/esg-report-checking/report-review/339100?fileId=103407&schemaId=2&rule=E7-Scope%203%20emissions&delist=0
                ],
                "enum": AnswerValueEnum.COMPLY.value,
            },
            {
                "name": "middle_rows",
                "multi_elements": True,
                "neglect_syllabus_regs": R_T7_SKIP_SYLLABUS,
                "filter_func": is_invalid_e7_answer,
                "regs": [
                    r"Scope 3",
                    # r'GHG emissions',
                ],
                "title_regs": [r"TABLE OF KEY PERFORMANCE"],
                "start_regs": [
                    r"(?<!Scope 2 and )(Scope\s*3|Scope III).*\d",
                    r"Other indirect (GHG|greenhouse gas)?\s*emissions?",
                    r"^(((?:GHG|greenhouse gas)\s*(emissions?\s*)?|\(?Scope 3\)?)[:：\s]*){2}$",
                    # r"GRI 305-[123]",
                ],
                "end_regs": [
                    r"total",
                    r"A\s?1\.3",
                    r"GHG emission intensity \(scope 1&2\)",
                    r"(Scope\s*3|305-4).*emissions intensity",
                    r"^Scope\s*[12]",
                    # r"employee",
                    # r"GRI 305-4",
                ],
                "enum": AnswerValueEnum.COMPLY.value,
            },
            {
                "name": "special_cells",
                "multi_elements": True,
                "multi": True,
                "whole_table": True,
                "table_title": [
                    r"Scope 3 Emissions",
                ],
                "col_header_pattern": ["Scope 3 category", "Tonnes? of CO2"],
                "row_pattern": [
                    "Scope 3 category.*Tonnes? of CO2",
                    R_E7_SCOPE_CAT,
                ],
                "enum": AnswerValueEnum.COMPLY.value,
            },
            {
                "name": "row_match",
                "neglect_syllabus_regs": R_T7_SKIP_SYLLABUS,
                "first_cell": False,
                "multi": True,
                "row_pattern": [
                    r"^(Scope 3|Scope III).*\d",
                    r"^3.*Categor(y|ies) \d",
                ],
                "enum": AnswerValueEnum.COMPLY.value,
            },
            {
                "name": "para_match",
                "neglect_syllabus_regs": R_T7_SKIP_SYLLABUS,
                "paragraph_pattern": (
                    r"(?P<content>not involve|not engage|not disclosed|no generation|not produce|not quantify)",
                    # r"(?P<content>carbon dioxide|methane|nitrous oxide|hydrofluorocarbons|perfluorocarbons and sulphur hexafluoride)",
                    r"scope3",
                    r"other indirect emissions",
                ),
                "neglect_pattern": (
                    r"scope [12]",
                    DIRECTORY_START,
                ),
                "enum": AnswerValueEnum.COMPLY.value,
            },
            {
                "name": "policy_esg_e7",
                "regs": [
                    r"^Scope 3 Emissions$",
                ],
                "remove_blank": False,
            },
            # DEFAULT_MODEL,
        ],
    },
    {
        "path": ["E8-Categories of scope 3 emissions"],
        "models": [
            # 明确包含cat 1-15
            {
                "name": "policy_esg_e89",
                "prompt": policy_esg_e8_prompt,
            }
        ],
    },
]
