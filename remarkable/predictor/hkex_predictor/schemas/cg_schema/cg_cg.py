import re

from interdoc.element import Para

from remarkable.common.common import is_paragraph_elt, is_table_elt
from remarkable.common.common_pattern import (
    P_PERIOD_SEPARATOR,
    P_SEN_SEPARATOR,
    R_CHAPTER_PREFIX,
    R_FOLLOW_PREFIX,
    R_MIDDLE_DASHES,
)
from remarkable.common.constants import AnswerValueEnum, CGEnum, PDFInsightClassEnum
from remarkable.common.pattern import (
    MatchFirst,
    MatchMulti,
    NeglectPattern,
    PositionPattern,
    SplitBeforeMatch,
)
from remarkable.common.protocol import CompleteSearchPattern, SearchPatternLike
from remarkable.common.util import clean_txt
from remarkable.pdfinsight.reader import <PERSON><PERSON><PERSON><PERSON><PERSON>ead<PERSON>, PdfinsightSyllabus
from remarkable.predictor.hkex_predictor.models.yoda_layer.base import ElementLocator
from remarkable.predictor.hkex_predictor.models.yoda_layer.filter import (
    AdjacentElemFilter,
    And<PERSON><PERSON><PERSON>,
    Chapter<PERSON><PERSON>er,
    ElemChapter<PERSON>ilter,
    InvalidPara<PERSON>ilter,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    ParaText<PERSON>ilter,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    TableTextFilter,
)
from remarkable.predictor.hkex_predictor.models.yoda_layer.form import Answer, ParaAsPos
from remarkable.predictor.hkex_predictor.models.yoda_layer.generics import PT
from remarkable.predictor.hkex_predictor.models.yoda_layer.operator import Operator
from remarkable.predictor.hkex_predictor.models.yoda_layer.rule.by_pos import (
    ContinuedElem,
    ParaFromPos,
    SentenceFromPos,
    SentenceFromSplitPara,
    TableFromPos,
)
from remarkable.predictor.hkex_predictor.models.yoda_layer.rule.root import (
    CGCascadeChapterLocator,
    Reference,
    ScoreDestFilter,
)
from remarkable.predictor.hkex_predictor.schemas.cg_schema.cg_b import (
    P_ALL_KIND_MEETING,
    P_B_A_PERSON,
    R_B_E_RESP,
)
from remarkable.predictor.hkex_predictor.schemas.cg_schema.cg_nc import R_ROLE_AND_FUNCTION
from remarkable.predictor.hkex_predictor.schemas.cg_schema.common_models import (
    R_CHAIR_MAN,
    R_EXECUTIVE,
    R_EXECUTIVE_END,
    get_eb_models,
    get_root_chapter_config,
)
from remarkable.predictor.hkex_predictor.schemas.pattern import (
    P_AC,
    P_CC,
    P_EC,
    P_NEED_IGNORE,
    P_THE_BOARD,
    R_AC,
    R_ALL_COMMITTEES,
    R_AND,
    R_ATTEND,
    R_CC,
    R_CG,
    R_CG_COMMITTEE,
    R_DUTIES,
    R_EC,
    R_EC_BOARD,
    R_EC_CELL,
    R_EC_NEG,
    R_HOLD,
    R_OVERALL,
)
from remarkable.predictor.models.base_model import BaseModel
from remarkable.predictor.models.para_match import AsFollowType
from remarkable.predictor.schema_answer import OutlineResult, ParagraphResult, PredictorResult, TableResult

P_COMMITTEE = (
    re.compile(r"corporate\s*governance\s*duties have been delegated.*?(Audit|Remuneration)\s*committee", re.I),
    re.compile(r"(Audit|Remuneration)\s*Committee performed the duties relating to corporate governance", re.I),
    # The Board did not establish a corporate governance committee but has delegated its responsibility for performing corporate governance duties to the Audit Committee
    re.compile(
        r"delegated\s*its\s*responsibility\s*for\s*performing\s*corporate\s*governance\s*dut(ies|y)\s*to\s*the\s*("
        r"Audit|Remuneration)\s*Committee",
        re.I,
    ),
)

# 可能出现CG相关委托描述的chapter
P_CHAPTER = MatchMulti.compile(
    r"CORPORATE\s*GOVERNANCE\s*(FUNCTIONS?|DUT(Y|IES)|PRACTICES?)?",
    r"Audit\s*Committee",
    r"Board\s*and\s*Management",
    operator=any,
)

P_DELEGATED_CHAPTER = NeglectPattern.compile(
    match=P_CHAPTER,
    unmatch=R_CG_COMMITTEE,
)


P_MEETING_CHAPTER = MatchMulti.compile(
    r"BOARD\s*MEETINGS\s*AND\s*GENERAL\s*MEETING",
    # DIRECTORS’ ATTENDANCE RECORDS AT MEETINGS
    r"DIRECTORS’\s*ATTENDANCE\s*RECORDS\s*AT\s*MEETINGS" r"BOARD\s*AND\s*BOARD\s*COMMITTEE\s*MEETINGS",
    r"CHAIRMAN AND THE CHIEF EXECUTIVE OFFICER",
    r"THE\s*BOARD|AUDIT\s*COMMITTEE",
    # 1. Attendance Record of Directors and Committee Members
    # 2. Attendance of Board and Committee Meetings and General Meeting
    # 3. Attendance Records at the Board, Board Committees and General Meetings
    # r"Attendance Record of Directors and Committee Members",
    # r"Attendance of Board and Committee Meetings and General Meeting",
    # r"Attendance\s*(Records?)?(of|at)\s*(The)?Board\s*(and|,)\s*Committees?.*?Meetings?)?",
    MatchMulti.compile(
        r"Attend",
        r"Directors?|Board",
        r"Committee",
        r"Members?|Meetings?",
        operator=all,
    ),
    operator=any,
)


P_POLICY_CHAPTER = MatchMulti.compile(r"^THE\s*BOARD|^AUDIT\s*COMMITTEE", operator=any)

# 会议出席记录表格相关正则
P_MEETING_ROW = MatchMulti.compile(
    P_ALL_KIND_MEETING,
    MatchMulti.compile(
        r"Board",
        r"Audit\s*Committee",
        r"Nomination\s*Committee",
        r"Remuneration\s*Committee",
        r"AGM|Annual\s*General\s*Meeting",
        # r"EGM|Investment\s*Committee\s*Meeting",
        operator=all,
    ),
    operator=any,
)

P_CG_DELEGATED = NeglectPattern.compile(
    match=MatchMulti.compile(
        r"(The\s*Board|Audit\s*Committee).*?corporate\s*governance\s*(dut(y|ies)|functions?)",
        r"(The\s*Board|Audit\s*Committee)\s*performed.*?(dut(y|ies)|functions?).*?corporate\s*governance",
        r"The\s*Board\s.*?responsible.*?leadership\s*and\s*control.*?overseeing",
        r"The Board is responsible for performing the functions set out in the code provision A.2.1 of the CG Code",
        operator=any,
    ),
    unmatch=MatchMulti.compile(r":$", operator=any),
)


P_CG_POLICY_START = NeglectPattern.compile(
    # r"The Board is responsible for performing the following corporate governance functions as required under the CG
    # Code:",
    match=MatchMulti.compile(
        r"CG\s*Code|Corporate\s*Governance\s*Functions?",
        r"The\s*Board|the\s*Executive\s*Committee",
        r":$",
        operator=all,
    ),
    unmatch=re.compile(r"Risk\s*Management\s*Committee"),
)

P_CG_POLICY_END = MatchMulti.compile(
    # During the Year, the above corporate governance functions have been performed and executed by the Board and
    # the Board has reviewed the Company’s compliance with the CG Code",
    MatchMulti.compile(
        r"During\s*the\s*Year|annual",
        r"above|aforesaid",
        operator=all,
    ),
    MatchMulti.compile(
        r"During\s*the\s*Year|annual",
        r"carried",
        r"corporate\s*governance\s*functions\s*of\s*the\s*Company\s*in\s*accordance\s*with\s*its\s*terms\s*of\s*reference",
        operator=all,
    ),
    re.compile(r"As at the date of this annual report, the Board has reviewed and monitored"),
    operator=any,
    flag=re.I,
)

P_CG_POLICY_ITEM = MatchMulti.compile(
    r"^\d+\.(?!\d)",
    r"^[•‧]",
    r"^\([iv]",
    r"^[-—－–]",
    r"^\([abcdefg]\)",
    r"\(i\).*?\(i+\)",
    r"\(\d+\)",
    operator=any,
)


R_REVIEW = r"(review|assess|discuss|approve?|consider|engage)(ing|ed)?"
R_CG_SUFFIX = r"(policies|practice|function|duties|report|code)s?"
R_CG_WITH_SUFFIX = rf"{R_CG} {R_CG_SUFFIX}"
R_ED_V2_AS_FOLLOW = [
    rf"{R_CG_WITH_SUFFIX}.*?set out below:-?$",
    rf"{R_CG_WITH_SUFFIX} as follows:$",
    rf"{R_CG_WITH_SUFFIX}.*?including:$",
    rf"{R_CG_WITH_SUFFIX} of the Company:$",
    rf"{R_CG_WITH_SUFFIX} among others:$",
    rf"{R_CG_WITH_SUFFIX} as required under the Code:$",
]

R_PERIOD = r"[^.。:：;；]*?"

R_CG_FUNCTION = r"((?<!good )(?<!quality of )(?<!standard of )(?<!practices on )Corporate\s*governance(?!\s*(standard|practice|principle|structure))|governed\s*by|A\.2\.1|functions\s*set\s*out\s*in\s*the\s*CG\s*code)"
P_CG_FUNCTION = MatchMulti.compile(R_CG_FUNCTION, operator=any)
P_CG_DUTIES = MatchMulti.compile(
    r"(GOVERNANCE|I?C?CG)\s*(committ?e|function|responsib|dut(y|ies)|policies)",
    r"(duty|duties|functions?|responsibilit(y|ies))\s*of\s*(the\s*)?(corporate|governance|\bI?C?CG\b)",
    operator=any,
)
# http://************:55647/#/project/remark/246908?treeId=14300&fileId=62536&schemaId=28&projectId=14300&schemaKey=CG-E%28b%29
R_E_A_NEGLECT = [
    r"C\.2\.1",
    r"are\s*in\s*compliance\s*with\s*the Corporate\s*Governance\s*Code",
]
P_E_A_BOARD_NEGLECT = MatchMulti.compile(rf"delegat|{R_OVERALL}", R_CG_FUNCTION, operator=all)

R_PERFORM = r"\b(perform(?!ance)|responsib|under(tak|took)|carr|recogni|in\s*respect\s*of)"
P_PERFORM_CG = SplitBeforeMatch(
    MatchMulti.compile(
        rf"{R_PERFORM}|delegat|function|develop|\b(the|primary(ly)?|principal|main(ly)?|primar[a-z]+|major|follow(ing)?)\s*dut(y|ies)",
        R_CG_FUNCTION,
        operator=all,
    ),
    separator=P_SEN_SEPARATOR,
    operator=any,
)
P_PERFORM_FOLLOW = MatchMulti.compile(
    r"following|below", rf"responsib|perform|under(take|took)|{R_ROLE_AND_FUNCTION}", r"[:：]$", operator=all
)
# 由董事会承担CG职责
P_CG_DELEGATED_BOARD = MatchFirst.compile(
    rf"The\s*Board\s*play.*?supervisory\s*role.*?{R_CG_FUNCTION}\s*{R_ROLE_AND_FUNCTION}",
    rf"The\s*Board.*?({R_PERFORM}|determining){R_PERIOD}{R_ROLE_AND_FUNCTION}{R_PERIOD}([A-Z].\d.\d)?{R_PERIOD}{R_CG_FUNCTION}{R_PERIOD}",
    rf"The\s*Board.*?({R_PERFORM}|determining){R_PERIOD}{R_CG_FUNCTION}{R_PERIOD}{R_ROLE_AND_FUNCTION}{R_PERIOD}",
    rf"The\s*Board.*?{R_PERFORM}{R_PERIOD}{R_ROLE_AND_FUNCTION}{R_PERIOD}(under\s*rule\s*A\.2\.1){R_PERIOD}",
    rf"The\s*Board.*?{R_PERFORM}{R_PERIOD}(under\s*rule\s*A\.2\.1){R_PERIOD}{R_ROLE_AND_FUNCTION}{R_PERIOD}",
    # rf"The\s*Board\s*recognizes.*?{R_CG}\s*{reg_words(0,5)}(collective\s*)?{R_ROLE_AND_FUNCTION}\s*of\s*the\s*Directors?",
    rf"In\s*respect\s*of\s*the{R_CG_FUNCTION}\s*{R_ROLE_AND_FUNCTION}{R_PERIOD}reference\s*of\s*the\s*Board",
    # rf"The\s*Board\s*recogni([zs]e[sd])\s*its\s*{R_CG}\s*dut(y|ies)",
    # rf"the Board of the Company have performed.*{R_CG} duties which include",
    rf"{R_ROLE_AND_FUNCTION}\s*of\s*the\s*Board\s*to\s*perform{R_PERIOD}{R_CG_FUNCTION}",
    r"The Board is responsible for leadership of the Group as well as promoting the success.*affairs",
    # http://************:55647/#/project/remark/244316?treeId=3943&fileId=67300&schemaId=28&projectId=17&schemaKey=CG-E(c)
    r"the Board made great efforts in improving the corporate governance system",
    # http://************:55647/#/project/remark/244290?treeId=13124&fileId=67326&schemaId=28&projectId=17&schemaKey=CG-E(b)
    r"governed\s*by\s*the\s*Board",
)

# 由审核委员会承担CG职责
P_CG_DELEGATED_AC = MatchFirst.compile(
    # rf"{R_AC}{R_PERIOD}monitor\s*the\s*{R_CG_FUNCTION}\s*of\s*the\s*Group{R_PERIOD}",
    rf"{R_AC}{R_PERIOD}{R_PERFORM}{R_PERIOD}{R_CG_FUNCTION}{R_PERIOD}{R_ROLE_AND_FUNCTION}{R_PERIOD}",
    rf"{R_AC}{R_PERIOD}{R_PERFORM}{R_PERIOD}{R_ROLE_AND_FUNCTION}{R_PERIOD}{R_CG_FUNCTION}{R_PERIOD}",
    rf"delegated\s*(the\s*)?{R_CG_FUNCTION}\s*{R_ROLE_AND_FUNCTION}{R_PERIOD}{R_AC}",
    # http://************:55647/#/project/remark/245406?treeId=2614&fileId=66210&schemaId=28&projectId=17&schemaKey=CG-E(c)
    # rf"{R_AC}\s*perform{R_PERIOD}{R_ROLE_AND_FUNCTION}{R_PERIOD}{R_CG}"
)

# 由合规委员会(Compliance Committee)承担CG职责
P_CG_DELEGATED_CC = MatchFirst.compile(
    # http://************:55647/#/project/remark/244319?treeId=10734&fileId=67297&schemaId=28&projectId=10734&schemaKey=CG-E%28c%29-Number%20of%20meetings%20and%20record%20of%20attendance
    rf"{R_CC}{R_PERIOD}make\s*recommend{R_PERIOD}improvement\s*(of\s*)?{R_CG_FUNCTION}",
)

# 由执行委员会(Executive Committee)承担CG职责
P_CG_DELEGATED_EC = MatchFirst.compile(
    # http://************:55647/#/project/remark/244319?treeId=10734&fileId=67297&schemaId=28&projectId=10734&schemaKey=CG-E%28c%29-Number%20of%20meetings%20and%20record%20of%20attendance
    rf"{R_EC}{R_PERIOD}make\s*recommend{R_PERIOD}improvement\s*(of\s*)?{R_CG_FUNCTION}",
)

R_E_A_NEG_CHAPTERS = [r"corporate\s*governance\s*practice", r"risk", r"internal\s*audit", r"SECRETAR|assistant|Clerk"]
R_CG_COMMITTEE_CHAPTER_TITLES = [
    r"__regex__CORPORATE\s*GOVERNANCE\s*(FUNCTION|STRUCTURE|COMMITTEE)",
    r"__regex__Responsibilit(y|ies)\s*for\s*Corporate\s*Governance",
    r"__regex__CORPORATE\s*GOVERNANCE\s*AND\s*COMPLIANCE\s*COMMITTEE",
    r"__regex__ICCG Committee",
]
P_CG_COMMITTEE_CHAPTERS = MatchMulti.compile(
    rf"CORPORATE\s*GOVERNANCE\s*{R_AND}([a-z]+\s*){{1,2}}(STRUCTURE|COMMITTEE)", r"ICCG Committee", operator=any
)


E_D_V_12 = [
    {
        "name": "syllabus_elt_v2",
        "only_inject_features": True,
        "inject_syllabus_features": [
            r"__regex__Corporate Governance Function",
            # r"__regex__^Duties and Responsibilities$",  # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/3609#note_445993
        ],
        "ignore_pattern": [P_NEED_IGNORE],
    },
    {
        "name": "syllabus_based",
        "multi": True,
        "only_inject_features": True,
        "ignore_pattern": [P_NEED_IGNORE],
        "filter_content_answer": True,
        "inject_syllabus_features": R_CG_COMMITTEE_CHAPTER_TITLES,
        "neglect_features": [
            r"MINIMUM\s*NUMBERS\s*OF\s*CORPORATE\s*GOVERNANCE\s*COMMITTEE",
        ],
        "table_model": "row_match",
        "table_config": {
            "row_pattern": [
                NeglectPattern.compile(
                    match=MatchMulti.compile(r"Report on work performed", r"工作摘要", operator=any),
                    unmatch=MatchMulti.compile(r"repurchase", operator=any),
                ),
            ],
        },
    },
    {
        "name": "reference",
        "only_reference_crude_elements": True,
        "from_path": "CG-E(a)-Role and function",
    },
]


def link_next_filter(interdoc, answer_head, answer_follow):
    return answer_follow if answer_follow else Answer()


def get_e_a_with_performed(r_committee: str, p_sentence: CompleteSearchPattern, is_board=False, syllabus_regs=()):
    other_patterns = (
        [] if is_board else [MatchMulti.compile(r_committee, r"delegate", R_CG_FUNCTION, R_DUTIES, operator=all)]
    )
    r_skip = [*R_E_A_NEGLECT, P_E_A_BOARD_NEGLECT] if is_board else R_E_A_NEGLECT
    p_skip = MatchMulti.compile(*r_skip, operator=any)
    syllabus_regs = syllabus_regs or [r_committee]
    neg_syllabus_regs = [*R_E_A_NEG_CHAPTERS, *R_ALL_COMMITTEES] if is_board else R_E_A_NEG_CHAPTERS
    return {
        "name": "multi_models",
        "operator": "all",
        "models": [
            {
                "name": "yoda_layer",
                "threshold": 0,
                "multi_elements": True,
                "skip_syllabus_title": True,
                **get_root_chapter_config(neg_syllabus_regs=neg_syllabus_regs),
                "rule": ScoreDestFilter(
                    pattern=MatchMulti.compile(R_CG_FUNCTION, operator=any),
                    skip_pattern=p_skip,
                    dest=Operator.any(
                        # 限制委员会+关键词
                        SentenceFromSplitPara(
                            separator=P_PERIOD_SEPARATOR,
                            ordered_patterns=[
                                MatchMulti.compile(r_committee, P_PERFORM_CG.pattern, operator=all),
                                p_sentence,
                                *other_patterns,
                            ],
                        ),
                        # 限制委员会章节+关键词
                        ContinuedElem[Para](
                            start=0,
                            skip=ParaTextFilter(p_skip),
                            filter=AndFilter.from_filters(
                                ParaTextFilter(P_PERFORM_CG.pattern),
                                ElemChapterFilter(MatchMulti.compile(*syllabus_regs, operator=any)),
                            ),
                            size=1,
                        ),
                    ),
                ),
            },
            {
                "name": "para_match",
                "skip_syllabus_title": True,
                **get_root_chapter_config(syllabus_regs=syllabus_regs, neg_syllabus_regs=neg_syllabus_regs),
                "paragraph_pattern": [MatchMulti.compile(r_committee, R_DUTIES, operator=all), p_sentence],
                "neglect_pattern": p_skip,
                "as_follow_pattern": [MatchMulti.compile(r_committee, R_DUTIES, r"[:：]$", operator=all)],
                "as_follow_type": AsFollowType.ANY,
                "below_pattern": [R_CG, R_CG_FUNCTION],
                "neglect_below_pattern": p_skip,
                "ignore_pattern": P_NEED_IGNORE,
                "as_follow_near_offset": -1,
            },
        ],
    }


def get_cg_performed(key_pattern: str, p_sentence: CompleteSearchPattern, is_board=False):
    """
    CG function的职责由key_pattern对应的部门承担
    """
    p_follow_sentence = MatchMulti.compile(rf"{R_FOLLOW_PREFIX}|^[(（][iv]+[)）]", rf"{R_CG}|A\.2\.1", operator=all)
    other_patterns = (
        [] if is_board else [MatchMulti.compile(key_pattern, r"delegate", R_CG_FUNCTION, R_DUTIES, operator=all)]
    )
    return Operator.any(
        # 从初步定位结果找
        ScoreDestFilter(
            dest=SentenceFromSplitPara(
                separator=P_SEN_SEPARATOR,
                ordered_patterns=[
                    MatchMulti.compile(key_pattern, P_PERFORM_CG.pattern, operator=all),
                    p_sentence,
                    *other_patterns,
                ],
            ),
        ),
        # 从CG FUNCTIONS章节找
        CGCascadeChapterLocator(
            chapter_patterns=[re.compile(rf"{R_CG}\s*function", re.I)],
            dest=ElementLocator[Para](
                stop=ChapterFilter(skip_pattern=P_NEED_IGNORE),
                skip=ParaTextFilter(pattern=P_NEED_IGNORE) | ParaLensFilter(0, 30),
                limit=100,
                size=30,
                adapter=ParaAsPos(),
            ).link_next(
                Operator.any(
                    SentenceFromSplitPara(
                        separator=P_SEN_SEPARATOR,
                        ordered_patterns=[
                            MatchMulti.compile(key_pattern, P_PERFORM_CG.pattern, operator=all),
                            p_sentence,
                            *other_patterns,
                        ],
                    ),
                    SentenceFromPos(pattern=p_sentence),
                ),
                filter=link_next_filter,
            ),
        ),
        # 从key_pattern对应的章节找
        CGCascadeChapterLocator(
            chapter_patterns=[re.compile(key_pattern, re.I)],
            dest=ElementLocator[Para](
                filter=ParaTextFilter(
                    pattern=MatchMulti.compile(P_PERFORM_CG, p_follow_sentence, p_sentence, operator=any)
                ),
                stop=ChapterFilter(skip_pattern=P_NEED_IGNORE),
                skip=ParaTextFilter(
                    pattern=MatchMulti.compile(
                        P_NEED_IGNORE, rf"{R_CHAPTER_PREFIX}(corporate|governance)", operator=any
                    )
                )
                | ParaLensFilter(0, 50),
                limit=100,
                size=30,
                adapter=ParaAsPos(),
            ).link_next(
                Operator.any(
                    SentenceFromSplitPara(
                        separator=P_SEN_SEPARATOR,
                        ordered_patterns=[P_PERFORM_CG.pattern, p_sentence, p_follow_sentence],
                    ),
                    SentenceFromPos(pattern=p_sentence),
                ),
                filter=link_next_filter,
            ),
        ),
    )


def get_cg_performed_by_reference(p_committee: SearchPatternLike, p_chapter: SearchPatternLike = None):
    def filter_reference_results(pdfinsight: PdfinsightReader, results: list[PredictorResult]) -> list[PredictorResult]:
        """
        对关联结果进行过滤
        """
        p_follow_pattern = MatchMulti.compile(R_FOLLOW_PREFIX, rf"[{R_MIDDLE_DASHES}：:]$", operator=all)
        p_chapter_ = p_chapter or p_committee
        elem_indices = []
        for predictor_result in results:
            for element in BaseModel.get_elements_from_answer_result([predictor_result]):
                is_table = is_table_elt(element)
                # TODO TABLE元素块的场景，识别有问题，暂不兼容 https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/3168#note_510198
                # if not is_paragraph_elt(element, strict=True) and not is_table:
                if not is_paragraph_elt(element, strict=True):
                    continue
                if is_table:
                    elem_text = clean_txt("\t".join(cell["text"] for cell in element["cells"].values()))
                else:
                    elem_text = clean_txt(element["text"])
                is_comm_chapter = any(
                    p_chapter_.search(syll["title"])
                    for syll in pdfinsight.syllabus_reader.find_by_elt_index(element["index"])[1:]
                )
                # is_comm_chapter = p_chapter_.search(get_keys(pdfinsight.syllabus_dict, [element["syllabus"], "title"])) if element.get("syllabus") else False
                if (p_committee.search(elem_text) and P_CG_FUNCTION.search(elem_text)) or (
                    is_comm_chapter and P_CG_FUNCTION.search(elem_text)
                ):
                    predictor_result.element_results = (
                        [TableResult(element)] if is_table else [ParagraphResult(element, element.get("chars", []))]
                    )
                    return [predictor_result]
                if p_committee.search(elem_text) or (is_comm_chapter and p_follow_pattern.search(elem_text)):
                    elem_indices.append(element["index"])
            if not elem_indices:
                continue
        if not elem_indices:
            return []
        orig_elements = [e for e in BaseModel.get_elements_from_answer_result(results) if e["index"] in elem_indices]
        page_box = PdfinsightSyllabus.elements_outline(orig_elements)
        # 重组
        predictor_result = results[0]
        predictor_result.element_results = [
            OutlineResult(page_box=page_box, element=orig_elements[0], origin_elements=orig_elements)
        ]
        return [predictor_result]

    return {
        "name": "reference",
        "from_path": "CG-E(a)-Role and function",
        "from_answer_value": CGEnum.C.value,
        "filter_results_function": filter_reference_results,
    }


def table_filter(p_title_pattern, r_header_pattern):
    return TableFromPos(
        limit=100,
        size=1,
        start=0,
        filter=AndFilter.from_filters(
            TableRowColFilter(
                pattern=re.compile(r_header_pattern, re.I),
                limit=2,
            ),
            AdjacentElemFilter(
                -1,
                ParaTextFilter(pattern=NeglectPattern.compile(match=p_title_pattern, unmatch=R_EC_NEG)),
                skip=InvalidParaFilter(),
            ),
        ),
    )


def get_eb_composition(p_committee):
    return CGCascadeChapterLocator(
        chapter_patterns=[p_committee],
        strict=True,
        dest=Operator.any(
            ParaFromPos(pattern=MatchMulti.compile(R_CHAIR_MAN, R_EXECUTIVE, P_B_A_PERSON, operator=all)),
            ContinuedElem[PT](
                stop=ChapterFilter(skip_pattern=MatchMulti.compile(P_NEED_IGNORE, R_EXECUTIVE_END, operator=any)),
                skip=ParaTextFilter(pattern=P_NEED_IGNORE) | ParaLensFilter(0, 5),
                filter=AndFilter.from_filters(
                    ElemChapterFilter(p_committee),
                    ParaTextFilter(P_B_A_PERSON)
                    | TableTextFilter(pattern=MatchMulti.compile(R_CHAIR_MAN, R_EXECUTIVE, operator=any)),
                ),
                limit=10,
                size=2,
            ),
        ),
    )


def get_ec_cg_table(p_title_pattern, r_header_pattern, syllabus_regs=None):
    """
    https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/4152
    规则E(c)： 优先找标题或者列名中包含CG+attendance关键词的表格，同时要带上由xx承担CG职责语句（如果有）
    """
    return {
        "name": "yoda_layer",
        "threshold": 0,
        "syllabus_regs": syllabus_regs,
        "multi_elements": True,
        "rule": Operator.join(
            # 找表格
            ScoreDestFilter(
                dest=table_filter(p_title_pattern, r_header_pattern),
                aim_type="TABLE",
            ),
            # 找职责语句
            ScoreDestFilter(
                dest=SentenceFromSplitPara(
                    separator=P_SEN_SEPARATOR,
                    ordered_patterns=[
                        MatchMulti.compile(R_EC, P_PERFORM_CG.pattern, operator=all),
                        MatchMulti.compile(R_CC, P_PERFORM_CG.pattern, operator=all),
                        MatchMulti.compile(R_AC, P_PERFORM_CG.pattern, operator=all),
                        MatchMulti.compile(r"\bboard", P_PERFORM_CG.pattern, operator=all),
                    ],
                ),
            ),
        ),
    }


def post_process_ec(answers, **kwargs):
    """
    规则CG-E(c)的后处理函数，处理原则： 若提取到的答案中没有TABLE且answer_value不为NA则不框选元素块并返回NA
    """
    # 答案中必须有表格
    elements = BaseModel.get_elements_from_answer_result(answers)
    if not elements or any(elem["class"] == PDFInsightClassEnum.TABLE.value for elem in elements):
        return answers
    answer = BaseModel.get_common_predictor_results(answers)[0]
    if answer.answer_value != AnswerValueEnum.NA.value:
        answer.element_results = []
        answer.answer_value = AnswerValueEnum.NA.value
    return [answer]


predictor_options = [
    {
        "path": ["CG-E(a)-Role and function"],
        "default_enum_value": CGEnum.NA.value,
        "models": [
            # 1. 仅有一个高分段落直接提取
            {
                "name": "score_filter",
                "threshold": 0.4,
                "over_threshold_count": 1,
                "pattern": P_CG_DUTIES,
            },
            # 2. CG 章节下满足条件直接提
            {
                "name": "para_match",
                "multi_elements": True,
                "skip_syllabus_title": True,
                "enum_from_multi_element": True,
                "syllabus_regs": MatchMulti.compile(
                    r"(?<!social and )GOVERNANCE|\bI?C?CG\b", r"committ?e|function|responsib|dut(y|ies)", operator=all
                ),
                "paragraph_pattern": NeglectPattern.compile(
                    match=MatchMulti.compile(R_DUTIES, R_CG, R_CG_FUNCTION, operator=any), unmatch=R_FOLLOW_PREFIX
                ),
                "neglect_pattern": R_E_A_NEGLECT,
                "as_follow_pattern": [
                    MatchMulti.compile(rf"{R_DUTIES}|^following", r"[:：]$", operator=all),
                    r"functions$",
                ],
                "as_follow_type": AsFollowType.ANY,
                "ignore_pattern": P_NEED_IGNORE,
                "as_follow_near_offset": -1,
            },
            # 3. 提取as follow，包含cg function/duty/committee/responsb关键词，follow内容全部提取
            {
                "name": "para_match",
                "skip_syllabus_title": True,
                **get_root_chapter_config(neg_syllabus_regs=R_E_A_NEG_CHAPTERS),
                "paragraph_pattern": NeglectPattern.compile(match=P_CG_DUTIES, unmatch=R_FOLLOW_PREFIX),
                "as_follow_pattern": MatchMulti.compile(
                    P_CG_DUTIES,
                    rf"([{R_MIDDLE_DASHES}:：]|include|including|follow(ing)?|below)$",
                    operator=all,
                ),
                "as_follow_type": AsFollowType.ANY,
                "as_follow_near_offset": -30,
                "ignore_pattern": P_NEED_IGNORE,
            },
            # 4. EC承担CG职责 62192,62434,65257,67364,66231
            # http://************:55647/#/project/remark/245385?treeId=3882&fileId=66231&schemaId=28&projectId=17&schemaKey=CG-E(a)
            get_e_a_with_performed(R_EC, P_CG_DELEGATED_EC),
            # 5. CC承担CG职责  66818
            # http://************:55647/#/project/remark/244319?treeId=10734&fileId=67297&schemaId=28&projectId=10734&schemaKey=CG-E%28a%29
            get_e_a_with_performed(R_CC, P_CG_DELEGATED_CC),
            # 6. The Board承担CG职责，严格的语句条件
            {
                "name": "para_match",
                "multi_elements": True,
                "skip_syllabus_title": True,
                **get_root_chapter_config(
                    syllabus_regs=[MatchMulti.compile(R_B_E_RESP, r"board|management|director", operator=all)],
                    neg_syllabus_regs=R_E_A_NEG_CHAPTERS,
                ),
                "pattern": MatchMulti.compile(r"the\s*board", R_PERFORM, R_DUTIES, R_CG_FUNCTION, operator=all),
                "neglect_pattern": [r"includ(es?|ing)[:：,，]", r"[:：]$", *R_E_A_NEGLECT, P_E_A_BOARD_NEGLECT],
            },
            # 7. AC承担CG职责
            get_e_a_with_performed(R_AC, P_CG_DELEGATED_AC),
            # 8. The board承担CG职责
            get_e_a_with_performed(r"the\s*board", P_CG_DELEGATED_BOARD, is_board=True, syllabus_regs=[r"board"]),
            # 9. 提取as follow，用CG过滤as follow内容
            {
                "name": "para_match",
                "enum_from_multi_element": True,
                "skip_syllabus_title": True,
                **get_root_chapter_config(neg_syllabus_regs=R_E_A_NEG_CHAPTERS),
                "as_follow_pattern": [
                    MatchMulti.compile(R_DUTIES, r"[:：]$", operator=all),
                    NeglectPattern.compile(match=rf"{R_CG_FUNCTION}.*[:$]$", unmatch=r"work"),
                ],
                "as_follow_type": AsFollowType.ANY,
                "below_pattern": [R_CG, R_CG_FUNCTION],
                "neglect_below_pattern": [R_OVERALL],
                "as_follow_near_offset": -30,
                "ignore_pattern": P_NEED_IGNORE,
            },
            # 10. kmeans
            {
                "name": "kmeans_classification",
                "skip_syllabus_title": True,
                **get_root_chapter_config(neg_syllabus_regs=[r"SECRETAR|assistant|Clerk"]),
                "keep_high_score_threshold": 0.618,
                "filter_low_score_threshold": 0.1,
                "para_pattern": [R_CG_FUNCTION],
                "neglect_pattern": [R_FOLLOW_PREFIX, *R_E_A_NEGLECT],
            },
        ],
    },
    {
        "path": ["CG-E(b)-Composition"],
        "default_enum_value": CGEnum.NA.value,
        "element_candidate_count": 20,
        "models": [
            # 1~11. 找CG章节下的组成
            *get_eb_models(
                R_CG_COMMITTEE, skip_answer_value=CGEnum.NA.value, add_score_model=False, add_yoda_model=True
            ),
            # 12. 前面没提到，且存在CG COMMITTEE章节则直接返回NA
            {
                "name": "yoda_layer",
                "use_model_answer": False,
                "enum": CGEnum.NA.value,
                "with_element_box": False,
                "rule": Operator.any(
                    CGCascadeChapterLocator(
                        chapter_patterns=[P_CG_COMMITTEE_CHAPTERS], strict=True, dest=ParaFromPos()
                    ),
                ),
            },
            # 13. Executive Committee承担职责
            # http://************:55647/#/project/remark/245385?treeId=3882&fileId=66231&schemaId=28&projectId=17&schemaKey=CG-E(b)
            {
                "name": "multi_models",
                "operator": "join",
                "skip_answer_value": CGEnum.ND.value,
                "models": [
                    get_cg_performed_by_reference(P_EC),
                    {
                        "name": "yoda_layer",
                        "use_model_answer": False,
                        "rule": get_eb_composition(P_EC),
                    },
                ],
            },
            # 14. Compliance Committee承担职责
            # http://************:55647/#/project/remark/244319?treeId=10734&fileId=67297&schemaId=28&projectId=10734&schemaKey=CG-E%28a%29-Role%20and%20function
            {
                "name": "multi_models",
                "operator": "join",
                "skip_answer_value": CGEnum.ND.value,
                "models": [
                    get_cg_performed_by_reference(P_CC),
                    {
                        "name": "yoda_layer",
                        "use_model_answer": False,
                        "rule": get_eb_composition(P_CC),
                    },
                ],
            },
            # 15. Audit Committee承担职责
            {
                "name": "multi_models",
                "operator": "join",
                "skip_answer_value": CGEnum.ND.value,
                "models": [
                    get_cg_performed_by_reference(P_AC),
                    {
                        "name": "reference",
                        "from_path": "AC-E(b)-Composition",
                    },
                ],
            },
            # 16. board承担职责
            {
                "name": "multi_models",
                "operator": "join",
                "skip_answer_value": CGEnum.ND.value,
                "models": [
                    get_cg_performed_by_reference(
                        P_THE_BOARD,
                        NeglectPattern.compile(
                            match=r"board", unmatch=MatchMulti.compile(*R_ALL_COMMITTEES, operator=any)
                        ),
                    ),
                    {
                        "name": "reference",
                        "from_path": "B(a)-Board Composition by category of directors",
                    },
                ],
            },
        ],
    },
    {
        "path": ["CG-E(c)-Number of meetings and record of attendance"],
        "default_enum_value": CGEnum.NA.value,
        "post_process": post_process_ec,
        "models": [
            # 1. 遍历初步预测中的表格，列名中包含CG，表名中包含attendance
            get_ec_cg_table(MatchMulti.compile(R_EC_BOARD, R_ATTEND, operator=all), r"Corporate\s*governance"),
            # 2. 遍历初步预测中的表格，列名中包含attendance，表名中包含CG
            get_ec_cg_table(
                MatchMulti.compile(r"Corporate\s*governance", R_ATTEND, operator=all),
                R_EC_CELL,
                syllabus_regs=r"Corporate\s*governance\s*(function|committ?ee?)",
            ),
            # 3. no meetings
            # http://************:55647/#/project/remark/244902?treeId=44923&fileId=66714&schemaId=28&projectId=44923&schemaKey=CG-E%28c%29
            {
                "name": "para_match",
                "force_use_all_elements": True,
                "enum": AnswerValueEnum.NA.value,
                "syllabus_regs": rf"{R_CG}\s*(function|committee)",
                "paragraph_pattern": SplitBeforeMatch(
                    MatchMulti.compile(rf"\bnot?\s*(mee?t|{R_HOLD})", R_CG, operator=all),
                    separator=P_SEN_SEPARATOR,
                    operator=any,
                ),
            },
            # 4. The Board承担CG职责，严格的语句条件
            {
                "name": "yoda_layer",
                "threshold": 0.1,
                "multi_elements": True,
                "rule": Operator.join(
                    ScoreDestFilter(
                        skip_pattern=MatchMulti.compile(
                            r"includ(es?|ing)[:：,，]", *R_E_A_NEGLECT, P_E_A_BOARD_NEGLECT, operator=any
                        ),
                        dest=SentenceFromSplitPara(
                            ordered_patterns=[
                                PositionPattern.compile(
                                    r"the\s*board", r"responsible\s*for\s*perform(?!ance)|responsibility", R_CG_FUNCTION
                                ),
                                PositionPattern.compile(
                                    r"the\s*board",
                                    r"responsib|perform(?!ance)|dut(y|ies)",
                                    r"Corporate\s*governance\s*function",
                                ),
                            ],
                        ),
                    ),
                    Reference(path="B(b)-No. of board meetings", from_answer_value=AnswerValueEnum.COMPLY.value),
                ),
            },
            # 5. The Board承担CG职责，限制章节为CG FUNCTION
            # http://************:55647/#/project/remark/245104?treeId=23132&fileId=66512&schemaId=28&projectId=23132&schemaKey=CG-E%28c%29
            {
                "name": "yoda_layer",
                "threshold": 0.1,
                "multi_elements": True,
                "syllabus_regs": rf"{R_CG}\s*function",
                "rule": Operator.join(
                    ScoreDestFilter(
                        skip_pattern=re.compile(r"delegat", re.I),
                        dest=SentenceFromSplitPara(
                            ordered_patterns=[
                                PositionPattern.compile(
                                    r"the\s*board", rf"developed|reviewed|confirmed|{R_PERFORM}", R_CG_FUNCTION
                                ),
                                MatchMulti.compile(
                                    r"the\s*board", r"following|below", R_PERFORM, r"[:：]$", operator=all
                                ),
                            ],
                        ),
                    ),
                    Reference(path="B(b)-No. of board meetings", from_answer_value=AnswerValueEnum.COMPLY.value),
                ),
            },
            # 6. Executive Committee 承担CG职责
            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/4152#note_486610
            {
                "name": "yoda_layer",
                "threshold": 0,
                "multi_elements": True,
                "rule": Operator.join(
                    get_cg_performed(R_EC, P_CG_DELEGATED_EC),
                    CGCascadeChapterLocator(
                        chapter_patterns=[re.compile(R_EC, re.I)],
                        dest=Operator.any(
                            table_filter(MatchMulti.compile(R_ATTEND, operator=any), R_EC),
                            table_filter(MatchMulti.compile(R_EC, R_ATTEND, operator=all), R_EC_CELL),
                        ),
                    ),
                ),
            },
            # 7. Compliance Committee 承担CG职责
            {
                "name": "yoda_layer",
                "threshold": 0,
                "multi_elements": True,
                "rule": Operator.join(
                    get_cg_performed(R_CC, P_CG_DELEGATED_CC),
                    CGCascadeChapterLocator(
                        chapter_patterns=[re.compile(R_CC, re.I)],
                        dest=Operator.any(
                            table_filter(MatchMulti.compile(R_ATTEND, operator=any), R_CC),
                            table_filter(MatchMulti.compile(R_CC, R_ATTEND, operator=all), R_EC_CELL),
                        ),
                    ),
                ),
            },
            # 8. Audit Committee 承担CG职责
            {
                "name": "yoda_layer",
                "threshold": 0,
                "multi_elements": True,
                "rule": Operator.join(
                    get_cg_performed(R_AC, P_CG_DELEGATED_AC),
                    Reference(
                        path="AC-E(c)-Number of meetings and record of attendance",
                        from_answer_value=AnswerValueEnum.COMPLY.value,
                    ),
                ),
            },
            # 9. The Board承担CG职责，语句宽松
            {
                "name": "yoda_layer",
                "threshold": 0,
                "multi_elements": True,
                "rule": Operator.join(
                    get_cg_performed(r"(?<!^composition of )the\s*board|board\s*of\s*directors", P_CG_DELEGATED_BOARD),
                    Reference(path="B(b)-No. of board meetings", from_answer_value=AnswerValueEnum.COMPLY.value),
                ),
            },
        ],
    },
    {
        "path": ["E(d)(v)-1-Determine issuer's CG policy"],
        "default_enum_value": CGEnum.NA.value,
        "models": [
            # {
            #     "name": "twice_para_match",
            #     "filter_primary_role_elements": True,
            #     "syllabus_regs": [],
            #     "paragraph_pattern": (
            #         # rf"(?P<content>During the year.*?code provision A.2.1 of the {R_CG} Code were performed by.*)",
            #         rf"the CG Committee had reviewed the {R_CG} compliance status",
            #         rf"reviewed the.*?compliance with the {R_CG} (Code|report)",
            #         rf"the Board had reviewed the Company’s {R_CG_WITH_SUFFIX}",
            #         rf"performed the Company’s {R_CG_WITH_SUFFIX}",
            #         rf"The work performed by the Board on {R_CG_WITH_SUFFIX}",
            #         rf"the Company have performed the {R_CG_WITH_SUFFIX}",
            #         rf"{R_REVIEW} the Company’s {R_CG_WITH_SUFFIX}",
            #         rf"the Board reviewed the Group’s compliance with the code of {R_CG}",
            #         rf"the Board committees had developed and reviewed the Company’s policies and practices on {R_CG}",
            #         rf"the\s*Board\s*performed\s*{reg_words(0,3)}{R_CG_SUFFIX}\s*regarding\s*{R_CG}",
            #     ),
            #     "neglect_pattern": (),
            #     "second_pattern": (
            #         r"(^|\.\s?)During\s*the\s*(financial|report(ing)?)?\s?(year|period)",
            #         r"\.\s?During the (year|period)",
            #         r"For the year ended",
            #     ),
            # },
            # {
            #     "name": "syllabus_based",
            #     "inject_syllabus_features": [
            #         r"__regex__Board\s*and\s*committee\s*attendance",
            #         r"__regex__CORPORATE\s*GOVERNANCE\s*COMMITTEE",
            #         r"__regex__Corporate\s*Governance\s*Function",
            #         r"__regex__CORPORATE\s*GOVERNANCE\s*PRACTICES",
            #         r"__regex__^\s*Audit\s*Committee",
            #     ],
            #     "multi": True,
            #     "neglect_features": [
            #         r"CORPORATE GOVERNANCE REPORT",
            #     ],
            #     "only_inject_features": True,
            #     "paragraph_model": "twice_para_match",
            #     "para_config": {
            #         "paragraph_pattern": (
            #             r"consider\s*and\s*approve.*?corporate\s*governance\s*related\s*polic(y|ies)",
            #             rf"{R_CG_COMMITTEE}.*?review(ed|ing)?\s*the\s*corporate\s*governance\s*(report|compliance)",
            #             rf"performed the corporate functions by reviewing the Company’s {R_CG_WITH_SUFFIX}",
            #             rf"(have|has)\s*performed\s*the\s*{R_CG_WITH_SUFFIX}",
            #             rf"reviewed\s*and\s*discussed.*?{R_CG_WITH_SUFFIX}",  # fileId: 66346
            #         ),
            #         "second_pattern": (
            #             r"During\s*the\s*(financial)?\s*year",
            #             r"For\s*the\s*year\s*ended",
            #             r"During\s*the\s*year\s*ended",
            #         ),
            #         "neglect_pattern": (),
            #     },
            # },
            # {
            #     "name": "yoda_layer",
            #     "threshold": 1,
            #     "rule": CGChapterLocator(
            #         pattern=MatchMulti.compile(
            #             r"Meeting of the Board of Directors$",
            #             r"^Audit\s*Committee",
            #             r"^CORPORATE\s*GOVERNANCE\s*FUNCTIONS",
            #             operator=any,
            #         ),
            #         dest=MiddleElement[PT](
            #             unmatch_count=3,
            #             start=ParaTextFilter(
            #                 pattern=MatchMulti.compile(
            #                     r"During the Reporting Period.*?Board meetings were convened by the Company. Matters considered at the Board meetings include, among others:",
            #                     r"During the Financial Year, the Audit Committee had undertaken the follow duties:",
            #                     r"The Board is responsible for performing the corporate governance duties as set out in the Corporate Governance Functions of the Board adopted by the Company including:",
            #                     operator=any,
            #                 )
            #             ),
            #             stop=ParaTextFilter(
            #                 pattern=MatchMulti.compile(
            #                     rf"{R_CG}\s*related\s*systems",
            #                     rf"Reviewed the Company’s compliance with the Code Provisions and disclosure in the {R_CG_WITH_SUFFIX}",
            #                     r"year ended.*?has covered the aforesaid matters",
            #                     operator=any,
            #                 )
            #             ),
            #             skip=ParaTextFilter(
            #                 pattern=MatchMulti.compile(
            #                     r"^CORPORATE\s*GOVERNANCE\s*REPORT$",
            #                     r"^CORPORATE\s*GOVERNANCE\s*(FUNCTIONS?|DUT(Y|IES)|PRACTICES?)?\s*(\(continued\))?$",
            #                     operator=any,
            #                     flag=re.I,
            #                 )
            #             ),
            #             # re.compile(rf"Corporate governance related systems; and", re.I),
            #             filter=ParaTextFilter(pattern=P_CG_POLICY_ITEM),
            #             include_between=False,
            #         ),
            #     ),
            # },
            # {
            #     "name": "yoda_layer",
            #     "threshold": 1,
            #     "rule": Operator.all(
            #         CGChapterLocator(
            #             min_page=10,
            #             pattern=P_DELEGATED_CHAPTER,
            #             dest=ParaFromPos(
            #                 pattern=P_CG_DELEGATED,
            #             ),
            #         ),
            #         CGChapterLocator(
            #             min_page=10,
            #             pattern=P_POLICY_CHAPTER,
            #             dest=ParaFromPos(
            #                 pattern=MatchMulti.compile(
            #                     # r"(?<!to\s)review(ed)?.*?(CG\s*Code|Corporate\s*Governance)",
            #                     # r"reviewed.*?compliance.*?The\s*Code",
            #                     # reviewed the Company’s compliance with the CG Code and relevant disclosures
            #                     # rf"reviewed the.*?compliance with the {R_CG}?\s*(Code|report)",
            #                     # r"reviewed the Company’s compliance with the Code and relevant disclosures",
            #                     operator=any,
            #                 ),
            #             ),
            #         ),
            #     ),
            # },
            # {
            #     "name": "kmeans_classification",
            #     "filter_low_score_threshold": 0.7,
            # },
            # 因需求更改 先注释掉上面的配置 具体见 https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/3609
            *E_D_V_12
        ],
    },
    {
        "path": ["E(d)(v)-2-Duties performed by the board or the committee under CP A.2.1"],
        "default_enum_value": CGEnum.NA.value,
        "models": [
            # {
            #     "name": "para_match",
            #     "syllabus_regs": [],
            #     "paragraph_pattern": (
            #         rf"^•{R_CG} practices and policies, compliance with CG Code",
            #         rf"adopted Compliance Policy for {R_CG}",
            #         rf"The Board reviewed the Company’s {R_CG_WITH_SUFFIX}",
            #         rf"(?P<content>During the year.*?the Board had reviewed the Company’s {R_CG_WITH_SUFFIX}.*)",
            #         rf"{R_CG_WITH_SUFFIX} has been {R_REVIEW} by the Board",
            #     ),
            # },
            # {
            #     "name": "para_match",
            #     "syllabus_regs": [],
            #     "as_follow_include_title": True,
            #     "as_follow_pattern": (r"The following.*corporate governance.*during.*:$",),
            # },
            # {
            #     "name": "twice_para_match",
            #     "filter_primary_role_elements": True,
            #     "syllabus_regs": [],
            #     "paragraph_pattern": (
            #         rf"(?P<content>During the year.*?code provision A.2.1 of the {R_CG} Code were performed by.*)",
            #         rf"the CG Committee had reviewed the {R_CG} compliance status",
            #         rf"reviewed the.*?compliance with the {R_CG} (Code|report)",
            #         rf"the Board had reviewed the Company’s {R_CG_WITH_SUFFIX}",
            #         rf"performed the Company’s {R_CG_WITH_SUFFIX}",
            #         rf"The work performed by the Board on {R_CG_WITH_SUFFIX}",
            #         rf"the Company have performed the {R_CG_WITH_SUFFIX}",
            #         rf"{R_REVIEW} the Company’s {R_CG_WITH_SUFFIX}",
            #         rf"the Board reviewed the Group’s compliance with the code of {R_CG}",
            #     ),
            #     "neglect_pattern": (),
            #     "second_pattern": (
            #         r"^During the\s?(\s?financial)?\s?(year|period)",
            #         r"\.\s?During the (year|period)",
            #         r"For the year ended",
            #     ),
            # },
            # {
            #     "name": "syllabus_based",
            #     "multi": True,
            #     "only_inject_features": True,
            #     "filter_primary_role_elements": True,
            #     "inject_syllabus_features": [
            #         r"__regex__CORPORATE\s*GOVERNANCE\s*FUNCTIONS?",
            #         r"__regex__Executive Committee",
            #         r"__regex__Board and committee attendance",
            #         r"__regex__Corporate Governance Committee",
            #         r"__regex__^Compliance Committee$",
            #     ],
            #     "as_follow_pattern": R_ED_V2_AS_FOLLOW
            #     + [
            #         r"At the meeting, the committee:$",
            #     ],
            #     "special_as_follow_start": [],
            #     "paragraph_model": "para_match",
            #     "para_config": {
            #         "paragraph_pattern": tuple(
            #             # R_ED_V2_AS_FOLLOW
            #             []
            #             + [
            #                 rf"reviewed the Group’s policies and practices on {R_CG_WITH_SUFFIX} and compliance",
            #                 rf"carried out the duties on the {R_CG_WITH_SUFFIX}",
            #                 rf"to consider and approve.*?{R_CG} related policies of the Group",
            #                 rf"satisfied with the effectiveness of its {R_CG}",
            #                 rf"{R_CG_WITH_SUFFIX} is carried out.*?\(a\).*?\(b\).*?\(c\).*?\(d\).*?\(e\)",
            #                 rf"the Board had reviewed the policies and practices.*?{R_CG_WITH_SUFFIX}",
            #                 rf"{R_CG_WITH_SUFFIX} with its written terms of reference as set out below:$",
            #                 rf"the Board has reviewed the policy of the {R_CG}",
            #                 r"The Board had performed the above duties",
            #                 rf"reviewing and considering the policy and {R_CG_SUFFIX} of {R_CG} ",
            #                 r"At the meeting, the committee:$",
            #             ]
            #         ),
            #         "neglect_pattern": (r"terms of reference",),
            #         "neglect_below_pattern": (r"^Corporate Governance Report$"),
            #     },
            #     "table_model": "empty",
            #     "table_config": {},
            # },
            # {
            #     "name": "kmeans_classification",
            #     "filter_low_score_threshold": 0.8,
            #     "filter_primary_role_elements": True,
            #     "para_pattern": [
            #         # r'A.2.1'
            #     ],
            #     "neglect_pattern": [
            #         r"D.3.1",
            #         r"the Board has performed the corporate governance duties in accordance with its terms of reference",  # 66287
            #     ],
            # },
            # 因需求更改 先注释掉上面的配置 具体见 https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/3609
            *E_D_V_12
        ],
    },
]
