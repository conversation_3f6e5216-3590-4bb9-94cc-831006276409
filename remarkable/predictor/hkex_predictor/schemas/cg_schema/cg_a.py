import re
from itertools import chain

from interdoc.element import Para

from remarkable.common.common_pattern import (
    P_CONTINUED,
    R_CG_CHAPTER_TITLES,
    R_CHAPTER_PREFIX,
    R_CHAPTER_SUFFIX,
    R_DR_CHAPTER_TITLES,
)
from remarkable.common.constants import AnswerValueEnum
from remarkable.common.pattern import <PERSON><PERSON>ulti
from remarkable.common.protocol import SearchPatternLike
from remarkable.common.util import clean_txt
from remarkable.predictor.hkex_predictor.models.yoda_layer.filter import <PERSON><PERSON><PERSON>er, Or<PERSON><PERSON><PERSON>, ParaTextFilter
from remarkable.predictor.hkex_predictor.models.yoda_layer.operator import Operator
from remarkable.predictor.hkex_predictor.models.yoda_layer.rule.by_pos import (
    ContinuedElem,
)
from remarkable.predictor.hkex_predictor.models.yoda_layer.rule.root import (
    CGCascadeChapterLocator,
    ScoreParaFilter,
)
from remarkable.predictor.hkex_predictor.schemas.pattern import (
    P_DR_CHAPTER,
    P_NEED_IGNORE,
    R_APPELLATION,
    R_JURA_CG_IGNORE_CHAPTER,
    R_SEGREGATED_CEO_CHAIRMAN,
)
from remarkable.predictor.models.base_model import BaseModel

R_MANY_WORDS = r"[^.\n]*?"
R_MR_MS = r"\bM[rs]\."
R_PROVISION_NAME = r"[A-Z]\.\d.\d"
R_CG_CODE = r"((the|applicable)\s*(HKEX|CG|Corporate\s*Governance)\s*(of\s*list)?|the\s*code\s*(on|of)\s*Corporate\s*Governance)"

R_SPECIAL_CG_CODE = r"(the|applicable)\s*(HKEX|CG|Corporate\s*Governance)\s*code.*Appendix 14"
# 规则A(c)： 遵守了全部条文的正则，满足这些正则不一定代表N/A
R_LIST_A_C_COMPLIED = [
    r"fully\s*complied\s*with",
    rf"compli(ed|ance)\s*with[,，]?\s*{R_MANY_WORDS}{R_CG_CODE}",
    rf"compli(ed|ance)\s*with[,，]?\s*{R_MANY_WORDS}{R_SPECIAL_CG_CODE}",
    rf"ha(s|ve|d)\s*(adopted|applied)\s*{R_MANY_WORDS}{R_CG_CODE}",
    rf"ha(s|ve|d)\s*(adopted|applied)\s*{R_MANY_WORDS}{R_SPECIAL_CG_CODE}",
    r"ha(s|ve|d)\s*(adopted|applied)\s*the principles and code provisions of the CG Code",
    rf"{R_CG_CODE}{R_MANY_WORDS}(have|ha[sd])\s*been\s*(adopted|applied)",
    rf"compli(ed|ance)\s*with\s*{R_MANY_WORDS}code\s*provisions?",
    rf"not\s*comply\s*with\s*Code\s*Provision\s*{R_PROVISION_NAME}\s*",
    r"\bnot\s*deviate|\bno(\s?material\s?)\s*deviations?",
    r"has\s*set\s*up\s*procedures\s*on\s*corporate\s*governance\s*that\s*comply\s*with\s*the\s*principles\s*in\s*the\s*Corporate\s*Governance\s*Code",
    r"Code Provision A\.2\.1.*?does not have any offices.*?(Chief Executive Officer|CEO)",
    r"the (Company|group) (was|has) in compli(ed|ance)\s*with all the CG Code.$",
    r"(adopted|applied) all the applicable code provisions.*compli(ed|ance)\s*with the Code",
]
# 规则A(c)：这些正则匹配所有描述偏离如下的段落，有这种段落必然是Comply
R_FOLLOWING = r"(the\s*following|below)"
R_SAVE_AS = r"((save|except)\s*(for|in|as)|except)"
R_CHAIRMAN = r"(the\s*|our\s*)?(co-)?chair(man|woman|lady|person)\s*(of\s*the\s*(Board|company)\s*(\(.+\))?)?"
R_CEO = r"(the\s*)?(Chief\s*Executive(\s*Officer)?|CEO)"
R_SAVE_AS_CEO = rf"{R_SAVE_AS}\s*disclose[^.]+?[\"“”][a-z\s]*?{R_CHAIRMAN}[a-z\s]*?{R_CEO}[a-z\s]*?[\"“”]"
R_LIST_A_C_AS_FOLLOWS = [
    rf"{R_SAVE_AS}\s*{R_FOLLOWING}\s*(deviation|(code\s*)?provision)s?[.:：-]",
    rf"{R_SAVE_AS}\s*(the\s*)?(deviation|provision)s?\s*discussed\s*{R_FOLLOWING}[.:：-]",
    rf"{R_SAVE_AS}\s*respect\s*of\s*{R_FOLLOWING}\s*matters\s*[.:：-]",
    rf"{R_SAVE_AS}\s*(disclosed\s*|discussed\s*)?{R_FOLLOWING}\s*[.:：-]",
    rf"{R_SAVE_AS}\s*the\s*issues\s*mentioned\s*in\s*the\s*following\s*paragraphs[.:：-]",
    rf"({R_CG_CODE}|deviation|provision).+(save|except)\s*(for|in|as)?\s*the\s*following\S*[.:：-]",
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/3735#note_446302
    R_SAVE_AS_CEO,
]
# 规则A(c)：董事长和CEO是同一人
R_TUPLE_A_C_CHAIRMAN_IS_CEO = (
    rf"(is|was|being|been|assumes)(\s*the roles of)?\s*(currently\s*)?(both\s*)?{R_CHAIRMAN}\s*and\s*(the\s*)?{R_CEO}",
    r"currently\s*holds\s*both\s*position",
    rf"currently\s*performs\s*the\s*roles\s*of\s*{R_CHAIRMAN}\s*and{R_CEO}",
    rf"is\s*also\s*the\s*{R_CEO}",
    r"not\s*separated\s*the\s*roles",
    rf"the\s*role\s*of\s*{R_CEO}\s*keeps\s*vacant\s*and\s*the\s*duties\s*will\s*be\s*borne\s*by\s*both",
    rf"{R_CHAIRMAN}\s*and\s*{R_CEO}\s*are\s*vested\s*in\s*one\s*person",
    rf"(is|are|was|were)\s*no\s*separation\s*between\s*the\s*roles\s*of\s*{R_CHAIRMAN}\s*and\s*{R_CEO}",
    rf"roles\s*of\s*(both\s*)?{R_CHAIRMAN}\s*and\s*{R_CEO}\s*(performed|in)\s*the\s*same\s*(individual|person)",
    rf"is\s*{R_CHAIRMAN}\s*and\s*{R_CEO}",
    rf"However[,，]?\s*(the\s*functions\s*of\s*){R_CEO}\s*(is|are)\s*now\s*performed\s*by\s*([^\s]+\s*){{0,9}}{R_CHAIRMAN}",
    rf"the\s*positions\s*of\s*{R_CHAIRMAN}\s*and\s*{R_CEO}\s*(were|are|have)\s*not\s*separated",
    rf"has\s*been\s*appointed\s*as\s*{R_CHAIRMAN}\s*of\s*the\s*Board\s*and\s*has\s*been\s*re-designated\s*as\s*{R_CEO}",
    rf"^M(r|r?s)\.\s*[^.,，\n]*?[,，]\s*the\s*chair(man|woman|lady)\s*and\s*{R_CEO}",
    MatchMulti.compile(rf"\b{R_APPELLATION}", R_CEO, R_CHAIRMAN, "also perform|(\b(both|and)\b)", operator=all),
    # rf"{R_APPELLATION}.*{R_CEO}.*also performs as the {R_CHAIRMAN}",
    # http://100.64.0.105:55647/#/project/remark/244292?treeId=20673&fileId=67324&schemaId=28&projectId=17
    # 如果上面的 MatchMulti 匹配到内容过多 可以删除 然后用下面的 {R_APPELLATION}.*{R_CEO}.*also performs as the {R_CHAIRMAN}
)
# 规则A(c)：偏离正则，满足这些正则不一定代表Comply
R_LIST_A_C_DEVIATE = [
    r"(?<!\bno)(?<!\bno\s)(?<!\bnot\s)(?<!\bnot)(?<!potential\s)(?<!potential)(deviate|deviation)",
    rf"except(ion)?\s*(for|as|of)\s*{R_MANY_WORDS}({R_CG_CODE}|provision|{R_PROVISION_NAME})",
    rf"save\s*(for|as)(?!\s*disclosed)\s*{R_MANY_WORDS}({R_CG_CODE}|provision|{R_PROVISION_NAME})",
    # https://jura-uat.paodingai.com/#/hkex/cg-report-checking/report-review/251316?fileId=80220&schemaId=28&rule=A%28c%29-Deviation%20from%20Code%20Provisions
    rf"save\s*(for|as)\s*disclosed below.*compli(ed|ance)\s*with[,，]?\s*{R_MANY_WORDS}{R_CG_CODE}",
    rf"({R_CG_CODE}|provision|{R_PROVISION_NAME}).*(save|except)\s*(for|in|as)(?!\s*disclosed)",
    r"except\s*as\s*stated\s*and\s*explained\s*below",
    rf"except\s*(for\s*|as\s*)?(code\s*provision\s*)?{R_PROVISION_NAME}",
    r"Subject to the deviations as disclosed.*set out on page",
    # rf"code\s*provision\s*{R_PROVISION_NAME}",
    # rf"(?<!in compliance with code provision ){R_PROVISION_NAME}(?!\s*of\s*{R_CG_CODE}\s*require)",
] + list(R_TUPLE_A_C_CHAIRMAN_IS_CEO)
# 规则A(c)： 结果中必须排除这些句子
R_LIST_EXCLUDE = [
    rf"in\s*compliance\s*with\s*code\s*provision\s*{R_PROVISION_NAME}",
    rf"{R_PROVISION_NAME}\s*of\s*{R_CG_CODE}\s*(require)",
    rf"(responsible|Responsibility)\s*{R_MANY_WORDS}{R_PROVISION_NAME}",
    r"stipulate[sd]?\s*that\s*every\s*director",
    rf"for\s*(the\s*)?purpose\s*of\s*code\s*provision\s*{R_PROVISION_NAME}",
    rf"stipulated\s*in\s*(the\s*)?code\s*provision\s*{R_PROVISION_NAME}",
]
# 规则A(c)：这个章节的内容必须单独匹配
R_LIST_A_C_SPECIAL_CHAPTER_TITLE = [
    rf"Chair(man|woman|lady)\s*and\s*{R_CEO}",
]

P_STOP_CHAPTERS = MatchMulti.compile(
    rf"{R_CHAPTER_PREFIX}independent\s*non-executive\s*directors{R_CHAPTER_SUFFIX}", operator=any
)

R_A_C_IGNORE = [
    r"CORPORATE\s*GOVERNANCE\s*FUNCTION",
    r"model\s*code",
    r"^BY DIRECTORS AND RELEVANT EMPLOYEES$",
    r"MODEL CODE FOR SECURITIES TRANSACTIONS",
    r"M O D E L C O D E F O R S E C U R I T I E S TRANSACTIONS",
]


def filter_answers(answers, **kwargs):
    """
    若多组答案中同时有Comply和N/A，丢弃N/A答案
    """
    answers = BaseModel.get_common_predictor_results(answers)
    for answer in answers:
        # DR章节识别错误 元素块所在页码的前两个元素块若是 DR ，则排除
        element_results = []
        for element_result in answer.element_results:
            if hasattr(element_result, "element"):
                if page_elements := kwargs["predictor"].pdfinsight.find_elements_by_page(
                    element_result.element["page"]
                ):
                    first_two_elements = page_elements[:2]
                    if any(P_DR_CHAPTER.search(clean_txt(element.get("text", ""))) for element in first_two_elements):
                        continue
            element_results.append(element_result)
        answer.element_results = element_results
    if not (filter_answers := [ans for ans in answers if ans.answer_value == AnswerValueEnum.COMPLY.value]):
        return answers
    new_answer = filter_answers[0]
    new_answer.element_results = sorted(
        chain.from_iterable(r.element_results for r in filter_answers), key=lambda x: x.element["index"]
    )

    return [new_answer]


def cg_a_a_para_match_models(*para_patterns: SearchPatternLike | str):
    return [
        {
            "name": "para_match",
            "threshold": 0.1,
            "paragraph_pattern": MatchMulti.compile(para_pattern, r"corporate\s*governance|\bCG\b", operator=all),
        }
        for para_pattern in para_patterns
    ]


predictor_options = [
    {
        "path": ["A(a)-Explanation of application of code principles"],
        "models": [
            {
                "name": "cg_a_a",
            },
            {
                "name": "score_filter",
                "skip_syllabus_title": True,
                "threshold": 0.2,
            },
            *cg_a_a_para_match_models(
                r"applied|adopted|adopting|based|adapting|adapted",
                r"commit(ted)?\s*to",
                r"maintain|endeavours",
                r"complied|compliance",
            ),
        ],
    },
    {
        "path": ["A(b)-Compliance with CPs"],
        "models": [
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"the (Company|Directors|group).*(complied|compliance).*with.*code",
                    r"Company.*met.*provisions.*Corporate Governance Code",
                ),
            },
            {
                "name": "score_filter",
                "threshold": 0.5,
            },
            {
                "name": "score_filter",
                "threshold": 0.1,
                "pattern": [
                    r"the (Company|Directors|group).*(complied|compliance).*with.*code",
                    r"we.*complied with.*code",
                ],
            },
        ],
    },
    {
        "path": ["A(c)-Deviation from Code Provisions"],
        # 注意这里打开了多模型开关，不同模型枚举值可能不同，会以最后一个为准
        "pick_answer_strategy": "all",
        "post_process": filter_answers,
        "location_threshold": 0.09,
        "models": [
            # 模型1：如果提取到deviation from code provision C2.1或save as "chairman and ceo"，则提取整个CEO章节
            {
                "name": "yoda_layer",
                "threshold": 0,
                "multi_elements": True,
                "skip_syllabus_title": True,
                "neglect_syllabus_regs": [*R_A_C_IGNORE, *R_JURA_CG_IGNORE_CHAPTER],
                "rule": Operator.join(
                    ScoreParaFilter(
                        pattern=MatchMulti.compile(
                            R_SAVE_AS_CEO,
                            MatchMulti.compile(
                                r"(save|except)\s*(for|as)",
                                r"deviation",
                                r"\bC\.2\.1\b",
                                r"|".join(R_LIST_A_C_SPECIAL_CHAPTER_TITLE),
                                operator=all,
                            ),
                            operator=any,
                        )
                    ),
                    CGCascadeChapterLocator(
                        only_chapter=True,
                        chapter_patterns=[re.compile(rf"Chairman\s*and\s*(the\s*)?{R_CEO}", re.I)],
                        dest=ContinuedElem[Para](
                            stop=OrFilter.from_filters(
                                ChapterFilter(skip_pattern=P_CONTINUED), ParaTextFilter(pattern=P_STOP_CHAPTERS)
                            ),
                            skip=OrFilter.from_filters(
                                ChapterFilter(),
                                ParaTextFilter(pattern=MatchMulti.compile(*R_CG_CHAPTER_TITLES, operator=any)),
                            ),
                        ),
                    ),
                ),
            },
            # 模型2：分值过滤
            {
                "name": "score_filter",
                "skip_syllabus_title": True,
                "multi_elements": True,
                "threshold": 0.618,
                "neglect_pattern": R_SEGREGATED_CEO_CHAIRMAN,
            },
            # 模型3： 提取偏离段落及as following
            {
                "name": "para_match",
                "multi_elements": True,
                "enum_from_multi_element": True,
                "skip_syllabus_title": True,
                "neglect_syllabus_regs": [
                    R_LIST_A_C_SPECIAL_CHAPTER_TITLE,
                    *R_A_C_IGNORE,
                    *R_JURA_CG_IGNORE_CHAPTER,
                    *R_DR_CHAPTER_TITLES,
                ],
                "ignore_pattern": [P_NEED_IGNORE],
                "as_follow_pattern": R_LIST_A_C_AS_FOLLOWS,
                "need_default_follows": False,
                "special_as_follow_start": [
                    r"^\s*deviations?[:：]?\s*$",
                    rf"Code\s*Provision\s*{R_PROVISION_NAME}",
                    rf"{R_PROVISION_NAME}\s*of\s*the\s*{R_CG_CODE}Code",
                    R_MR_MS,
                ],
                "neglect_pattern": R_LIST_EXCLUDE,
                "max_paragraphs_for_every_follow": 2,
                "paragraph_pattern": R_LIST_A_C_COMPLIED
                + R_LIST_A_C_DEVIATE
                + [
                    # 特例 https://jura-uat.paodingai.com/#/hkex/cg-report-checking/report-review/251389?fileId=80075&schemaId=28&rule=A%28c%29-Deviation%20from%20Code%20Provisions&delist=0
                    r"ensure that the Company will fully comply with the requirements as set out in the CG Codes",
                ],
            },
            # 模型4：如果描述了董事长和CEO是同一人，提取整个章节
            {
                "name": "para_match",
                "multi_elements": True,
                "skip_syllabus_title": True,
                "enum_from_multi_element": True,
                "syllabus_regs": [
                    rf"Chairman\s*and\s*(the\s*)?{R_CEO}",
                ],
                "paragraph_pattern": R_TUPLE_A_C_CHAIRMAN_IS_CEO,
            },
            # 模型5： kmeans
            {
                "name": "kmeans_classification",
                "multi_elements": True,
                "filter_low_score_threshold": 0.1,
                "skip_syllabus_title": True,
                "keep_high_score_threshold": 0.3,
                "neglect_syllabus_regs": [
                    *R_LIST_A_C_SPECIAL_CHAPTER_TITLE,
                    *R_A_C_IGNORE,
                    *R_JURA_CG_IGNORE_CHAPTER,
                    *R_DR_CHAPTER_TITLES,
                ],
                "para_pattern": R_LIST_A_C_COMPLIED + R_LIST_A_C_DEVIATE,
                "neglect_pattern": R_LIST_EXCLUDE,
            },
            # 模型6： 特例表格
            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/4150#note_486508
            {
                "name": "syllabus_based",
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__Participation of Shareholders__regex__General Meeting",
                ],
                "table_model": "row_match",
                "table_config": {
                    "anchor_pattern": ["Code ProvisionsComplianceProcedures of Corporate Governance"],
                    "row_pattern": [
                        r"\.NoThe Chairman",
                    ],
                },
            },
        ],
    },
]
