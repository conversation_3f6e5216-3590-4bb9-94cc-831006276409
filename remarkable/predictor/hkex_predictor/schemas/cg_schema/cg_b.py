"""
Board of Directors
包含B(a)-B(i)的字段提取规则
"""

from __future__ import annotations

import re
from typing import List

from remarkable.common.common import is_paragraph_elt, is_table_elt
from remarkable.common.common_pattern import (
    P_SEN_SEPARATOR,
    R_CG_CHAPTER_TITLES,
    R_CHAPTER_PREFIX,
    R_DR_CHAPTER_TITLES,
    R_FOLLOW_PREFIX,
    R_MIDDLE_DASH,
    R_MIDDLE_DASHES,
)
from remarkable.common.constants import CGEnum, PDFInsightClassEnum, TableCellsResultType
from remarkable.common.pattern import (
    MATCH_ALWAYS,
    MatchMulti,
    NeglectPattern,
    PositionPattern,
    SplitBeforeMatch,
)
from remarkable.common.util import clean_txt
from remarkable.pdfinsight.interdoc_reader import Para, Table
from remarkable.pdfinsight.parser import FOOTNOTE_START_PATTERN
from remarkable.pdfinsight.reader import PdfinsightSyllabus
from remarkable.predictor.hkex_predictor.models.yoda_layer.base import (
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    Follow<PERSON>ustExist,
    KeepFollowAnswerFilter,
)
from remarkable.predictor.hkex_predictor.models.yoda_layer.filter import (
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ilter,
    And<PERSON>ilter,
    ChapterFilter,
    ElemChapterFilter,
    OrFilter,
    ParaLensFilter,
    ParaTextFilter,
    TableFilter,
    TableRowColFilter,
    TableTextFilter,
)
from remarkable.predictor.hkex_predictor.models.yoda_layer.form import FirstParaAsPos, ParaAsPos
from remarkable.predictor.hkex_predictor.models.yoda_layer.generics import PT
from remarkable.predictor.hkex_predictor.models.yoda_layer.operator import Operator, OptionOperator
from remarkable.predictor.hkex_predictor.models.yoda_layer.rule.by_pos import (
    ContinuedElem,
    ParaFromPos,
    SentenceFromSplitPara,
    TableCellsFromPos,
    TableFromPos,
)
from remarkable.predictor.hkex_predictor.models.yoda_layer.rule.root import (
    CGCascadeChapterLocator,
    CGChapterLocator,
    ScoreContinuedElem,
    ScoreDestFilter,
    ScoreLocator,
    ScoreParaFilter,
    ScoreStartDestFilter,
    ScoreStartParaLocator,
    StrictMode,
)
from remarkable.predictor.hkex_predictor.pattern import R_FOLLOW
from remarkable.predictor.hkex_predictor.schemas.cg_schema.common_models import (
    R_CHAIR,
    R_CHAIR_MAN,
    R_EB_AS_FOLLOW_END,
    R_EXECUTIVE,
    R_EXECUTIVE_END,
    RP_EB_AS_FOLLOW_START,
    get_eb_as_follow_config,
    get_root_chapter_config,
)
from remarkable.predictor.hkex_predictor.schemas.cg_schema.utils import add_follow_table
from remarkable.predictor.hkex_predictor.schemas.pattern import (
    P_ATTEND,
    P_EC_CELL,
    P_NEED_IGNORE,
    R_ALL_COMMITTEES,
    R_APPELLATION,
    R_ATTEND,
    R_CG,
    R_COMPOSE,
    R_DURING_THE_YEAR,
    R_DUTIES,
    R_EC_CELL,
    R_EC_NEG,
    R_INED,
    R_OVERALL,
    reg_words,
)
from remarkable.predictor.models.base_model import R_PRIMARY_ROLE, BaseModel
from remarkable.predictor.models.para_match import AsFollowType
from remarkable.predictor.schema_answer import OutlineResult, PredictorResult, TableResult

R_RELATIONSHIP = rf"((elde(r|st)|older|younger|great|step|half|foster|biological|adoptive){R_MIDDLE_DASH}?)?\s*(:?father|mother|(grand)?son|daughter|spouse|husband|wife|(half{R_MIDDLE_DASH})?brother|sister|sibling|uncle|aunt|nephew|niece|cousin|widow|(controlling|substantial) shareholder)s?(-in-law)?"
R_B_E_RESP = r"(roles?|functions?|(authorit|responsibilit|dut)(y|ies)|delegat(e|ion))"

# b系列（例如AC-E(b)）规则中，用到的board章节
P_B_BOARD_CHAPTER = MatchMulti.compile(
    r"^board\s*composition",
    r"composition of the board",
    r"board\s*(composition|members)$",
    r"^(the)?\s*board\s*of\s*director",
    r"^board\s*diversity\s*policy",
    r"^\s*the\s*board$",
    r"^directors\s*and\s*composition\s*of\s*the\s*board",
    r"^\d\.\sBoard of Directors$",
    r"Composition and Nomination of the Board$",
    operator=any,
)

CHAPTER_LOCATOR = CGChapterLocator(
    min_page=5,
    pattern=P_B_BOARD_CHAPTER,
)
DIRECT_ATTENDANCE_LOCATOR = CGChapterLocator(
    min_page=5,
    pattern=MatchMulti.compile(
        r"^Directors’ Attendance Records",
        r"^Directors$",
        operator=any,
    ),
)

P_ATTENDANCE_MEETING = MatchMulti.compile(
    MatchMulti.compile(
        r"attend|held",
        r"director",
        r"board|general|董事",
        operator=all,
    ),
    r"ATTENDANCE RECORD OF MEETINGS HELD DURING THE REPORTING PERIOD",
    operator=any,
)

P_ALL_KIND_MEETING = MatchMulti.compile(
    r"board|(audit|remuneration|nomination|cg|Development\s*and\s*Strategy)\s+committee",
    r"attend",
    operator=all,
)

P_B_A_CONTINUE_PARA = MatchMulti.compile(
    MatchMulti.compile(
        rf"^{R_APPELLATION}",
        "^ined",
        r"^(independent\s*)?(non.\s*)?executive\s?directors?.?$",
        r"chairman",
        operator=any,
    ),
    SplitBeforeMatch.compile(
        NeglectPattern.compile(match=r"^[A-Z][a-z]+$", unmatch="^(Board|Corporate)", flag=0),
        r",?\s+",
        operator=all,
    ),
    operator=any,
)

# 董事会会议出席记录包含各个委员会 https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/3124
P_ALL_COMMITTEE_MEETING = MatchMulti.compile(
    r"board",
    r"audit",
    r"remuneration",
    r"nomination",
    r"attend|number of meeting",
    r"meeting",
    operator=all,
)

# 书面确认
R_WRITTEN_CONFIRMATION = r"(?:written\s*annual\s*confirmation|annual\s*written\s*confirmation)"

# 确认独立非执行董事的独立性
P_INED_INDEPENDENCE = MatchMulti.compile(
    rf"(considers?|confirms?|of\s*the\s*view).*?({R_INED}|them|they)\s*(to\s*be|are|be)\s*independent",
    MatchMulti.compile(r"confirm", rf"{R_INED}|their", "independence", operator=all),
    operator=any,
)

B_C_TABLE_FILTER = OrFilter.from_filters(
    TableRowColFilter(
        pattern=P_ATTENDANCE_MEETING,
        limit=2,
        type="row",
    ),
    AndFilter.from_filters(
        AdjacentElemFilter(
            -1,
            ParaTextFilter(pattern=MatchMulti.compile(R_FOLLOW, "board|general", operator=all)),
        ),
        TableRowColFilter(
            pattern=MatchMulti.compile("attend|held", operator=all),
            limit=2,
            type="row",
        ),
    ),
)
# 独立非执行董事独立性规则 MB 3.13 GEM 5.09
P_INED_INDEPENDENCE_RULE = MatchMulti.compile(
    MatchMulti.compile(r"\brule\b", r"\b3\.13\b", operator=all),
    MatchMulti.compile(r"\bgem\b", r"5\.09", operator=all),
    operator=any,
)

B_C_CONTINUED_ELEM = ContinuedElem[Para | Table](
    limit=50,
    size=2,
    skip=ParaTextFilter(pattern=MatchMulti.compile(r"^note", operator=all))
    | ParaTextFilter(pattern=MatchMulti.compile(*R_CG_CHAPTER_TITLES, operator=any)),
    filter=OrFilter.from_filters(
        AdjacentElemFilter(
            1,
            TableRowColFilter(
                pattern=MatchMulti.compile("attend|held", operator=all),
                limit=2,
                type="row",
            ),
        ),
        TableFilter(),
    ),
)

P_COMPOSITION_TYPE = MatchMulti.compile(r"chairman|Executive Director", operator=any)

P_SUPPLY_PARA = MatchMulti.compile(
    r"(Chair((wo)?man|lady)|Directors?)$",
    r"^INEDs$",
    rf"^{R_APPELLATION}",
    r"^(Executive|and) chairman",
    r"^Executive Director",
    r"^Non-executive Directors",
    r"^Independent Non-executive Directors",
    r"^\((Chairman|Executive Director)",
    operator=any,
)


R_PROFESSIONAL = r"|".join(
    [
        r"(appropriate|sufficient).*expertise",
        r"business\s*experience",
        r"\bprofessional(ism)?\b",
        r"related financial management",
        r"(skills|expertise) and experience",
        r"(Certified Public|tax) Accountant",
        r"\blawyer\b",
        r"\bsound knowledge\b",
        r"consolidated financial expert",
    ]
)


# def must_contain_composition_type(answers: list[dict], **kwargs):
#     # 排除没有完整披露board composition的类型的答案
#     # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/3691#note_437663
#     elements = BaseModel.get_elements_from_answer_result(answers)
#     for element in elements:
#         if is_table_elt(element):
#             paras = BaseModel.get_paragraphs_from_table(element)
#         else:
#             paras = [element]
#         for para in paras:
#             if P_COMPOSITION_TYPE.search(clean_txt(para.get("text"))):
#                 return answers
#     return []


def supply_table(answers: list[dict | PredictorResult], **kwargs) -> List[dict]:
    # 补充表格下方的段落
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/4002
    result = []
    for answer in answers:
        elements = BaseModel.get_elements_from_answer_result([answer])
        if not elements:
            result.append(answer)
            continue
        last_element = elements[-1]
        if not is_table_elt(last_element):
            result.append(answer)
            continue
        # 判断表格是否包含 ED 和 INED
        paras = BaseModel.get_paragraphs_from_table(last_element)
        table_text = "\n".join([clean_txt(para.get("text")) for para in paras])
        if MatchMulti.compile(r"executive director", R_INED, operator=all).search(table_text):
            result.append(answer)
            continue
        next_element_index = last_element["index"]
        # 补充表格下方的段落
        predictor = kwargs["predictor"]
        answer_elements = [last_element]
        while True:
            next_element_index += 1
            try:
                ele_type, element = predictor.pdfinsight.find_element_by_index(next_element_index)
            except IndexError:
                break
            if not element:
                continue
            if element["page"] > last_element["page"]:
                break
            if P_SUPPLY_PARA.search(clean_txt(element.get("text", ""))):
                answer_elements.append(element)
        page_box = PdfinsightSyllabus.elements_outline(answer_elements)
        supplement_answer = PredictorResult(
            [OutlineResult(page_box=page_box, element=answer_elements[0], origin_elements=answer_elements)],
            schema=predictor.schema,
            value=answer.answer_value,
        )
        result.append(supplement_answer)
    return result


def must_contain_professional_text(answers: list[dict], **kwargs):
    # 排除没有 3.10(2)专业知识 技能和经验 独立非执行董事具备适当的专业资格 的描述
    elements = BaseModel.get_elements_from_answer_result(answers)
    for element in elements:
        if MatchMulti.compile(R_PROFESSIONAL, operator=any).search(clean_txt(element.get("text"))):
            return answers
    return []


R_CODE_CHAPTER = r"compliance.+?(listing\s*rule|the\s*code)|corporate\s*governance\s*practice"
R_B_A_NEG_CHAPTERS = [
    *R_ALL_COMMITTEES,
    r"Appointment|re-election|candidate|SELECT",
    "diversity",
    r"(corporate\s*governance|CG)\s*function",
    R_CHAIR,
]
R_HOLD_POSITION = r"h[eo]lds?\s*(both|the)\s*position"
R_B_A_CHAPTER = [r"board", r"director", r"composition"]
R_BOARD_KW = r"board|directors|each\s*director"
P_B_A_PERSON = MatchMulti.compile(
    r"chaired\s*by\b",
    rf"{R_CHAIR_MAN}\s*of\s*the\s*(board|company|group)",
    rf"[(（][^)）.。;；]*?({R_CHAIR_MAN}|director)[^（(.。;；]*?[）)]",
    R_APPELLATION,
    r"namely",
    r"\bas\s*(an?\s*)?((independent\s*)?(non-)?executive|director|\bI?N?ED\b|chair)",
    operator=any,
)
P_B_A_SEN_COMPOSE = MatchMulti.compile(R_BOARD_KW, R_COMPOSE, P_B_A_PERSON, R_EXECUTIVE, operator=all)

P_IF_HAS_CHAIRMAN = SplitBeforeMatch.compile(
    MatchMulti.compile(
        MatchMulti.compile(r"\bnot\s*have|ha(s|ve)\s*not\b|left\s*vacant", R_CHAIR_MAN, operator=all),
        MatchMulti.compile(
            rf"{R_APPELLATION}|namely|as\s*(a\s*|the\s*)?Chair|{R_HOLD_POSITION}", R_CHAIR, operator=all
        ),
        operator=any,
    ),
    separator=P_SEN_SEPARATOR,
)

P_B_A_AS_FOLLOW = [MatchMulti.compile(r"directors|persons|board\s*members", r"[:：]$", operator=all)]
P_B_A_PARA_UNMATCH = MatchMulti.compile(
    rf"({R_EXECUTIVE}|directors?|officer|{R_CHAIR_MAN})$",
    r"names\s*are\s*shown",
    # re.compile("Rule"),
    r"meeting",
    R_FOLLOW_PREFIX,
    *FOOTNOTE_START_PATTERN.patterns,
    operator=any,
)
P_B_A_NEGLECT = NeglectPattern.compile(
    # http://************:55647/#/project/remark/244313?treeId=16833&fileId=67303&schemaId=28&projectId=16833&schemaKey=B%28a%29
    match=MatchMulti.compile(
        *R_ALL_COMMITTEES,
        r"\bof\s*(the\s*)?directors?$",
        rf"{R_CHAPTER_PREFIX}{R_CHAIR_MAN}\s*and\s*(chief|ceo)",
        # R_PRIMARY_ROLE,
        operator=any,
    ),
    unmatch=rf"(memeber|{R_CHAIR_MAN})\s*of\s*[a-z]+\s*committ?e|meeting|attend",
)
P_B_A_COL_TITLE = NeglectPattern.compile(
    match=r"name|title|description|board\s*member|director(?!s?['‘’])", unmatch=rf"{R_ATTEND}|train|committ?e"
)


def b_a_table_filter(r_title_kw):
    return AndFilter.from_filters(
        AdjacentElemFilter(
            -2,
            ParaTextFilter(pattern=MatchMulti.compile(R_BOARD_KW, r_title_kw, operator=all)),
            stop=ChapterFilter(
                skip_pattern=MatchMulti.compile(P_NEED_IGNORE, r"^(No\.|number)\s*of\s*meetings", operator=any)
            ),
        ),
        TableRowColFilter(
            pattern=MatchMulti.compile(R_EXECUTIVE, operator=any),
            limit=4,
            type="row",
        ),
    )


def b_a_table_from_pos(r_title_kw, start=None, limit=None):
    return TableCellsFromPos(
        cell_pattern=MatchMulti.compile(R_EXECUTIVE, P_B_A_COL_TITLE, operator=any),
        result_type=TableCellsResultType.COLS.value,
        start=start or 1,
        limit=limit or 10,
        multi=True,
        filter=AndFilter.from_filters(
            AdjacentElemFilter(
                -2,
                ParaTextFilter(pattern=MatchMulti.compile(R_BOARD_KW, r_title_kw, operator=all)),
                stop=ChapterFilter(
                    skip_pattern=MatchMulti.compile(P_NEED_IGNORE, r"^(No\.|number)\s*of\s*meetings", operator=any)
                ),
            ),
            TableRowColFilter(pattern=re.compile(R_EXECUTIVE, re.I)),
        ),
    )


def b_a_chairman_locator(p_chapter, p_keywords):
    return CGCascadeChapterLocator(
        chapter_patterns=[p_chapter],
        dest=ElementLocator[Para](
            stop=ChapterFilter(skip_pattern=MatchMulti.compile(P_NEED_IGNORE, p_chapter, operator=any)),
            skip=ParaTextFilter(pattern=MatchMulti.compile(P_NEED_IGNORE, *R_ALL_COMMITTEES, operator=any))
            | ParaLensFilter(0, 30),
            filter=ParaTextFilter(MatchMulti.compile(*p_keywords, operator=any)) & ElemChapterFilter(p_chapter),
            limit=500,
            size=1,
            adapter=ParaAsPos(),
        ).link_next(
            SentenceFromSplitPara(separator=P_SEN_SEPARATOR, ordered_patterns=p_keywords),
            filter=KeepFollowAnswerFilter(),
        ),
    )


def add_meeting_table(answers, **kwargs):
    """
    规则B(d)的后处理函数，处理原则： 若元素块的下一个元素块是表格，且包含关键词attend或meeting，则添加表格
    """
    pdfinsight = None
    if (predictor := kwargs.get("predictor")) and hasattr(predictor, "pdfinsight"):
        pdfinsight = predictor.pdfinsight
    for answer in BaseModel.get_common_predictor_results(answers):
        table_results = []
        table_indices = set()
        for elem_result in answer.element_results:
            elements = elem_result.origin_elements if hasattr(elem_result, "origin_elements") else [elem_result.element]
            table_indices.update(e["index"] for e in elements if is_table_elt(e))
            if len(elements) != 1 or not is_paragraph_elt(elements[0], strict=True):
                continue
            element = elements[0]
            next_index = element["index"] + 1
            if next_index in table_indices or not element.get("text", "").endswith((":", "：", "-")):
                continue
            elem_type, table_element = pdfinsight.find_element_by_index(next_index)
            if elem_type != PDFInsightClassEnum.TABLE.value:
                continue
            # 标题包含attend/meeting关键词或表格内包含attend/meeting关键词
            if P_ATTEND.search(element.get("text", "")) or any(
                P_EC_CELL.search(clean_txt(cell["text"])) for cell in table_element["cells"].values()
            ):
                table_results.append(TableResult(table_element))
        if table_results:
            answer.element_results.extend(table_results)
            answer.element_results.sort(key=lambda x: x.element["index"])
    return answers


predictor_options = [
    {
        "path": ["B(a)-Board Composition by category of directors"],
        "default_enum_value": CGEnum.ND.value,
        "element_candidate_count": 20,
        "models": [
            # 1. 满足条件的高分段落
            {
                "name": "score_filter",
                "enum": CGEnum.C.value,
                "threshold": 0.618,
                "over_threshold_count": 1,
                **get_root_chapter_config(),
                "pattern": MatchMulti.compile(
                    r"the\s*board", rf"{R_CHAIR}|{R_HOLD_POSITION}", R_EXECUTIVE, P_B_A_PERSON, operator=all
                ),
            },
            # 2. 找as follow
            {
                "name": "para_match",
                "threshold": 0.1,
                "skip_answer_value": CGEnum.ND.value,
                # "multi_elements": True,
                "enum_from_multi_element": True,
                **get_root_chapter_config(),
                "syllabus_regs": R_B_A_CHAPTER,
                "neglect_syllabus_regs": R_B_A_NEG_CHAPTERS,
                **get_eb_as_follow_config(extra_as_follows=P_B_A_AS_FOLLOW, extra_conf={"min_below_count": 2}),
                "neglect_pattern": R_ALL_COMMITTEES,
            },
            # 3. 找段落+as follow
            {
                "name": "para_match",
                "skip_answer_value": CGEnum.ND.value,
                "multi_elements": True,
                "enum_from_multi_element": True,
                **get_root_chapter_config(),
                "syllabus_regs": R_B_A_CHAPTER,
                "neglect_syllabus_regs": R_B_A_NEG_CHAPTERS,
                **get_eb_as_follow_config(
                    extra_as_follows=P_B_A_AS_FOLLOW, extra_conf={"as_follow_type": AsFollowType.PARA}
                ),
                "paragraph_pattern": NeglectPattern.compile(
                    match=MatchMulti.compile(P_B_A_SEN_COMPOSE, P_IF_HAS_CHAIRMAN, operator=any),
                    unmatch=P_B_A_PARA_UNMATCH,
                ),
                "neglect_pattern": [P_B_A_NEGLECT, r"train"],
            },
            # 4. 找关于chairman的描述+para match
            {
                "name": "multi_models",
                "operator": "all",
                "skip_answer_value": CGEnum.ND.value,
                "enum": CGEnum.C.value,
                "models": [
                    # 1. 找成员句子，不包含chairman
                    {
                        "name": "para_match",
                        "multi_elements": True,
                        "enum_from_multi_element": True,
                        **get_root_chapter_config(),
                        "syllabus_regs": R_B_A_CHAPTER,
                        "neglect_syllabus_regs": R_B_A_NEG_CHAPTERS,
                        **get_eb_as_follow_config(extra_as_follows=P_B_A_AS_FOLLOW, any_below_pattern=R_EXECUTIVE),
                        "paragraph_pattern": NeglectPattern(
                            match=MatchMulti.compile(R_COMPOSE, R_EXECUTIVE, P_B_A_PERSON, operator=all),
                            unmatch=P_B_A_PARA_UNMATCH,
                        ),
                        "neglect_pattern": [P_B_A_NEGLECT, R_CHAIR_MAN],
                    },
                    # 2. 再找chairman描述的句子，或者提到没有主席
                    {
                        "name": "multi_models",
                        "operator": "any",
                        "models": [
                            # ①在chairman章节找
                            {
                                "name": "para_match",
                                **get_root_chapter_config(),
                                "syllabus_regs": R_CHAIR_MAN,
                                "paragraph_pattern": [
                                    MatchMulti.compile(R_APPELLATION, R_HOLD_POSITION, operator=any),
                                    P_IF_HAS_CHAIRMAN,
                                ],
                            },
                            # ②在board和code章节找
                            {
                                "name": "para_match",
                                **get_root_chapter_config(),
                                "syllabus_regs": [*R_B_A_CHAPTER, R_CODE_CHAPTER],
                                "paragraph_pattern": P_IF_HAS_CHAIRMAN,
                                "neglect_pattern": P_B_A_NEGLECT,
                            },
                        ],
                    },
                ],
            },
            # 5. 找composition或开会或training表格
            {
                "name": "special_cells",
                "skip_answer_value": CGEnum.ND.value,
                **get_root_chapter_config(),
                "neglect_syllabus_regs": R_B_A_NEG_CHAPTERS,
                "title_patterns": MatchMulti.compile(R_BOARD_KW, rf"{R_COMPOSE}|{R_ATTEND}|train", operator=all),
                "col_pattern": [MatchMulti.compile(R_EXECUTIVE, R_CHAIR_MAN, operator=all), P_B_A_COL_TITLE],
                "multi": True,
            },
            # 6. 找关于chairman的描述+表格
            {
                "name": "yoda_layer",
                "threshold": 0,
                "multi_elements": True,
                "skip_answer_value": CGEnum.ND.value,
                **get_root_chapter_config(),
                "rule": Operator.all(
                    # 1) 找成员表格或者句子，不包含chairman
                    Operator.any(
                        # ①基于初步定位找as follow
                        ScoreStartDestFilter(
                            multi=True,
                            end_offset=20,
                            include_start=True,
                            pattern=MatchMulti.compile(
                                MatchMulti.compile(R_COMPOSE, R_EB_AS_FOLLOW_END, operator=all),
                                R_EXECUTIVE_END,
                                r"members\s*of\s*the\s*board",
                                # http://************:55647/#/project/remark/244973?treeId=19427&fileId=66643&schemaId=28&projectId=17&schemaKey=B(a)
                                r"commencement",
                                operator=any,
                            ),
                            dest=ContinuedElem[PT](
                                stop=ChapterFilter(
                                    skip_pattern=MatchMulti.compile(P_NEED_IGNORE, R_EXECUTIVE_END, operator=any)
                                )
                                | ParaTextFilter(MatchMulti.compile(r"\.$", operator=any)),
                                skip=ParaTextFilter(pattern=P_NEED_IGNORE) | ParaLensFilter(0, 5),
                                filter=AndFilter.from_filters(
                                    TableFilter()
                                    | ParaTextFilter(MatchMulti.compile(*RP_EB_AS_FOLLOW_START[:-1], operator=any)),
                                    ElemChapterFilter(
                                        MatchMulti.compile(*R_B_A_CHAPTER, operator=any),
                                        skip_pattern=MatchMulti.compile(
                                            *R_B_A_NEG_CHAPTERS, *R_B_A_NEG_CHAPTERS, operator=any
                                        ),
                                    ),
                                ),
                                limit=30,
                                size=2,
                            ),
                        ),
                        # ②基于初步定位找句子 + 表格
                        ScoreStartDestFilter(
                            dest=Operator.any(
                                # 找组成的句子
                                ContinuedElem[Para](
                                    start=0,
                                    limit=1,
                                    filter=AndFilter.from_filters(
                                        ParaTextFilter(
                                            NeglectPattern(
                                                match=MatchMulti.compile(
                                                    R_BOARD_KW, R_COMPOSE, R_EXECUTIVE, P_B_A_PERSON, operator=all
                                                ),
                                                unmatch=P_B_A_PARA_UNMATCH,
                                            )
                                        ),
                                        ElemChapterFilter(
                                            MatchMulti.compile(*R_B_A_CHAPTER, operator=any),
                                            skip_pattern=MatchMulti.compile(
                                                *R_B_A_NEG_CHAPTERS, *R_B_A_NEG_CHAPTERS, operator=any
                                            ),
                                        ),
                                    ),
                                    skip=ParaTextFilter(P_B_A_NEGLECT),
                                ),
                                # 找组成表格
                                b_a_table_from_pos(R_COMPOSE, start=0, limit=1),
                                # 找会议表格
                                b_a_table_from_pos(R_ATTEND, start=0, limit=1),
                                # 找training表格
                                b_a_table_from_pos(r"train|commencement", start=0, limit=1),
                                # TableFromPos(start=0, limit=1, filter=b_a_table_filter(R_COMPOSE)),
                                # TableFromPos(start=0, limit=1, filter=b_a_table_filter(R_ATTEND)),
                                # TableFromPos(start=0, limit=1, filter=b_a_table_filter(r"train")),
                            ),
                        ),
                        # ③初步定位找不到，再全局找表格
                        CGCascadeChapterLocator(
                            chapter_patterns=[MatchMulti.compile(r"board", r"director", operator=any)],
                            dest=Operator.any(
                                # 组成表格
                                b_a_table_from_pos(R_COMPOSE),
                                # TableFromPos(filter=b_a_table_filter(R_COMPOSE)),
                                # 会议表格
                                b_a_table_from_pos(R_ATTEND),
                                # TableFromPos(filter=b_a_table_filter(R_ATTEND)),
                                # training表格
                                b_a_table_from_pos(r"train|commencement"),
                                # TableFromPos(filter=b_a_table_filter(r"train")),
                            ),
                        ),
                    ),
                    # 2）找chairman描述
                    Operator.any(
                        # ①在chairman and ceo章节找
                        b_a_chairman_locator(
                            re.compile(R_CHAIR_MAN, re.I),
                            [
                                MatchMulti.compile(R_APPELLATION, R_HOLD_POSITION, operator=any),
                                P_IF_HAS_CHAIRMAN,
                            ],
                        ),
                        # ②在初步定位答案附近找
                        # http://************:55647/#/project/remark/245331?treeId=20476&fileId=66285&schemaId=28&projectId=17&schemaKey=B(a)
                        ScoreStartDestFilter(
                            end_offset=5,
                            dest=ContinuedElem[Para](
                                start=0,
                                size=1,
                                filter=OrFilter.from_filters(
                                    AndFilter.from_filters(
                                        ElemChapterFilter(
                                            MatchMulti.compile(*R_B_A_CHAPTER, R_CODE_CHAPTER, operator=any),
                                            skip_pattern=MatchMulti.compile(
                                                *R_B_A_NEG_CHAPTERS, *R_B_A_NEG_CHAPTERS, operator=any
                                            ),
                                        ),
                                        ParaTextFilter(P_IF_HAS_CHAIRMAN),
                                    ),
                                    AndFilter.from_filters(
                                        ElemChapterFilter(MatchMulti.compile(R_CHAIR_MAN, operator=any)),
                                        ParaTextFilter(MatchMulti.compile(R_APPELLATION, r"position", operator=any)),
                                    ),
                                ),
                                skip=ParaTextFilter(MatchMulti.compile(P_B_A_NEGLECT, R_PRIMARY_ROLE, operator=any)),
                                stop=ChapterFilter(skip_pattern=P_NEED_IGNORE),
                            ),
                        ),
                        # ③在cg code章节找
                        # http://************:55647/#/project/remark/245392?treeId=11692&fileId=66224&schemaId=28&projectId=17&schemaKey=B(a)
                        b_a_chairman_locator(re.compile(R_CODE_CHAPTER, re.I), [P_IF_HAS_CHAIRMAN]),
                    ),
                ),
            },
            # 7. DIRECTORS' REPORT章节找
            {
                "name": "para_match",
                "skip_answer_value": CGEnum.ND.value,
                "multi_elements": True,
                "enum_from_multi_element": True,
                "parent_features": [*R_DR_CHAPTER_TITLES, r"board\s*of\s*directors"],
                "parent_must_be_root": True,
                "page_first_as_parent_syllabus": True,
                "syllabus_regs": R_B_A_CHAPTER,
                "neglect_syllabus_regs": R_CHAIR_MAN,
                **get_eb_as_follow_config(extra_as_follows=P_B_A_AS_FOLLOW, extra_conf={"min_below_count": 2}),
                "paragraph_pattern": [P_B_A_SEN_COMPOSE, P_IF_HAS_CHAIRMAN],
                "neglect_pattern": P_B_A_NEGLECT,
            },
            # 8. 分值过滤
            {
                "name": "score_filter",
                "skip_answer_value": CGEnum.ND.value,
                "neglect_pattern": P_B_A_NEGLECT,
                "multi_elements": True,
                "aim_types": ["PARAGRAPH"],
                "threshold": 0.3,
            },
        ],
    },
    {
        "path": ["B(b)-No. of board meetings"],
        "post_process": add_follow_table,
        "models": [
            {
                "name": "score_filter",
                "threshold": 0.618,
                "multi_elements": True,
                "aim_types": ["TABLE"],
                "neglect_pattern": R_FOLLOW,
            },
            # 高分的as follow表格直接提取
            # http://************:55647/#/project/remark/245411?treeId=16214&fileId=66205&schemaId=28&projectId=17&schemaKey=CG-E(c)
            {
                "name": "para_match",
                "threshold": 0.618,
                "as_follow_pattern": [MatchMulti.compile(r"attend|出席", r"meeting|\bmet\b|會議", operator=all)],
                "as_follow_type": AsFollowType.TABLE,
                "below_pattern": R_ATTEND,
            },
            {
                "name": "yoda_layer",
                "threshold": 0.1,
                "rule": ScoreDestFilter(
                    aim_type="TABLE",
                    dest=TableFromPos(
                        limit=1,
                        filter=AndFilter.from_filters(
                            AdjacentElemFilter(
                                -1,
                                ParaTextFilter(
                                    pattern=MatchMulti.compile(
                                        r"\bdetails\b",
                                        r"Directors",
                                        "attendance records",
                                        "the board",
                                        ":$",
                                        operator=all,
                                    )
                                ),
                            ),
                        ),
                    ),
                ),
            },
            {
                "name": "special_cells",
                "syllabus_regs": [r"the\s*board", r"Board of Directors"],
                "title_patterns": MatchMulti.compile(r"attend|出席", r"meeting|\bmet\b|會議", operator=all),
                "neglect_title_patterns": R_EC_NEG,
                "whole_table": True,
                "cell_pattern": R_EC_CELL,
            },
            {
                "name": "score_filter",
                "threshold": 0.1,
                "aim_types": "TABLE",
            },
        ],
    },
    {
        "path": ["B(c)-Attendance of directors at board and general meetings"],
        "models": [
            {
                "name": "yoda_layer",
                "threshold": 1,
                "rule": CGChapterLocator(
                    pattern=MATCH_ALWAYS,
                    dest=TableFromPos(
                        limit=1,
                        filter=AndFilter.from_filters(
                            AdjacentElemFilter(
                                -1,
                                ParaTextFilter(
                                    pattern=MatchMulti.compile(
                                        MatchMulti.compile(
                                            R_FOLLOW, "The number of Board meetings attended", operator=all
                                        ),
                                        r"ATTENDANCE RECORD OF MEETINGS HELD DURING THE REPORTING PERIOD",
                                        operator=any,
                                    )
                                ),
                            ),
                            OrFilter.from_filters(
                                TableRowColFilter(
                                    pattern=MatchMulti.compile("attend|held", operator=all),
                                    limit=2,
                                    type="row",
                                ),
                                TableRowColFilter(
                                    pattern=MatchMulti.compile("attend|held", operator=all),
                                    limit=1,
                                    type="col",
                                ),
                            ),
                        ),
                    ),
                ),
            },
            {
                "name": "yoda_layer",
                "threshold": 1,
                "rule": OptionOperator.handoff(
                    CGChapterLocator(
                        pattern=MatchMulti.compile(
                            PositionPattern.compile(r"^attendance", "record", "directors"),
                            PositionPattern.compile(r"^board", "general", "meeting"),
                            operator=any,
                        ),
                        dest=TableFromPos(
                            limit=1,
                            filter=TableRowColFilter(
                                pattern=P_ATTENDANCE_MEETING,
                                limit=2,
                                type="row",
                            ),
                        ),
                    ),
                    ParaFromPos(start=-1, pattern=re.compile(R_FOLLOW)),
                ),
            },
            {
                "name": "yoda_layer",
                "threshold": 1,
                "rule": CGChapterLocator(
                    all=True,
                    pattern=MatchMulti.compile(
                        PositionPattern.compile(r"^board", "meeting"),
                        PositionPattern.compile(r"^general", "meeting"),
                        PositionPattern.compile(r"^code", "provision"),
                        PositionPattern.compile(r"^corporate", "governance", "committee"),
                        operator=any,
                    ),
                    dest=B_C_CONTINUED_ELEM,
                ),
            },
            {
                "name": "yoda_layer",
                "threshold": 1,
                "rule": CGChapterLocator(
                    pattern=MatchMulti.compile(
                        PositionPattern.compile(r"^board", "of", "directors?$"),
                        operator=any,
                    ),
                    dest=B_C_CONTINUED_ELEM,
                ),
            },
            {
                "name": "yoda_layer",
                "threshold": 1,
                "rule": CGChapterLocator(
                    pattern=PositionPattern.compile(r"attend", "director", "board", "general", "meeting"),
                    dest=TableFromPos(
                        limit=1,
                        filter=B_C_TABLE_FILTER,
                    ),
                ),
            },
            {
                "name": "yoda_layer",
                "threshold": 0.618,
                "indices_filter": set.issuperset,
                "multi_elements": True,
                "rule": OptionOperator.handoff(
                    ScoreLocator(
                        dest=Operator.union(
                            OptionOperator.handoff(
                                TableFromPos(limit=2, filter=B_C_TABLE_FILTER),
                                ParaFromPos(pattern=re.compile(R_FOLLOW), start=-1, limit=1),
                            ),
                            ParaFromPos(
                                start=-2,
                                limit=10,
                                pattern=P_ATTENDANCE_MEETING,
                            ),
                        )
                    ),
                    ParaFromPos(
                        pattern=MatchMulti.compile("general|board", "meeting", operator=all, flag=0),
                        limit=10,
                        size=1,
                    ),
                ),
            },
            {
                "name": "score_filter",
                "threshold": 0.618,
                "multi_elements": True,
            },
        ],
    },
    {
        "path": ["B(d)-Number of board or committee meetings attended"],
        "post_process": add_meeting_table,
        "models": [
            # 董事会会议表格已经包含各个委员会
            {
                "name": "yoda_layer",
                "threshold": 1,
                "rule": CHAPTER_LOCATOR.with_dest(
                    Operator.all(
                        ParaFromPos(
                            pattern=P_ATTENDANCE_MEETING,
                            limit=200,
                        ),
                        TableFromPos(
                            filter=TableTextFilter(
                                pattern=P_ALL_COMMITTEE_MEETING,
                            ),
                            limit=200,
                        ),
                    )
                ),
            },
            {
                "name": "yoda_layer",
                "threshold": 1,
                "rule": CHAPTER_LOCATOR.with_dest(
                    Operator.union(
                        ParaFromPos(
                            pattern=P_ALL_KIND_MEETING.union(R_FOLLOW),
                            limit=500,
                            size=10,
                            dest=TableFromPos(limit=1, size=1),
                        ),
                        TableFromPos(
                            filter=TableRowColFilter(
                                pattern=P_ALL_KIND_MEETING,
                                limit=2,
                            ),
                            limit=200,
                            size=10,
                        ),
                    )
                ),
            },
            {"name": "cg_board_meeting"},
            {
                "name": "score_filter",
                "threshold": 0.618,
                "multi_elements": True,
            },
        ],
    },
    {
        "path": ["B(e)-Statement of responsibilities of board and management"],
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/3179
        "element_candidate_count": 20,
        "models": [
            # 1. 指定章节数据直接取
            {
                "name": "multi_models",
                "operator": "union",
                "strict_merge": False,
                "models": [
                    {
                        "name": "syllabus_elt_v2",
                        "multi": True,
                        **get_root_chapter_config(neg_syllabus_regs=R_ALL_COMMITTEES),
                        "include_title": False,
                        "only_leaf_chapter": True,
                        "only_inject_features": True,
                        "inject_syllabus_features": [
                            rf"__regex__{R_B_E_RESP}.*(the\s*board|management|directors)",
                            rf"__regex__{R_CHAPTER_PREFIX}(the\s*board|board\s*of\s*directors)$__regex__{R_CHAPTER_PREFIX}{R_B_E_RESP}$",
                        ],
                        "ignore_pattern": [P_NEED_IGNORE],
                    },
                    # 再取所有0.5分以上
                    {
                        "name": "para_match",
                        "threshold": 0.5,
                        "multi_elements": True,
                        **get_root_chapter_config(neg_syllabus_regs=R_ALL_COMMITTEES),
                        "paragraph_pattern": MATCH_ALWAYS,
                        "as_follow_pattern": MatchMulti.compile(
                            "the board", R_DUTIES, rf"{R_MIDDLE_DASHES}[：:]$", operator=all
                        ),
                        "neglect_pattern": [R_FOLLOW_PREFIX],
                    },
                ],
            },
            # 2. ParaMatch过滤
            {
                "name": "multi_models",
                "operator": "union",
                "strict_merge": False,
                "models": [
                    # 先取所有0.5分以上
                    {
                        "name": "para_match",
                        "threshold": 0.5,
                        "multi_elements": True,
                        **get_root_chapter_config(neg_syllabus_regs=R_ALL_COMMITTEES),
                        "paragraph_pattern": MATCH_ALWAYS,
                        "as_follow_pattern": MatchMulti.compile(
                            "the board", R_DUTIES, rf"{R_MIDDLE_DASHES}[：:]$", operator=all
                        ),
                        "neglect_pattern": [R_FOLLOW_PREFIX],
                    },
                    # 再用关键词过滤
                    {
                        "name": "para_match",
                        "threshold": 0.1,
                        "multi_elements": True,
                        **get_root_chapter_config(neg_syllabus_regs=R_ALL_COMMITTEES),
                        "as_follow_pattern": MatchMulti.compile(
                            "the board", R_DUTIES, rf"[{R_MIDDLE_DASHES}：:]$", operator=all
                        ),
                        "paragraph_pattern": [
                            R_DUTIES,
                            R_OVERALL,
                            r"delegat|authorit|management|day-to-day|formulates?\b",
                        ],
                        "neglect_pattern": [
                            MatchMulti.compile(R_DURING_THE_YEAR, rf"[{R_MIDDLE_DASHES}:：]", operator=all),
                            R_FOLLOW_PREFIX,
                            # 企业管治
                            rf"{R_CG}|\bA\.2\.1|governed",
                            NeglectPattern.compile(
                                match=r"secretary|assistant|\bhad\b|(directors|company|board)\s*ha([sd]|ve)\s*[a-z]+ed",
                                unmatch=r"delegat|authorit|management|day-to-day|formulates?\b",
                            ),
                        ],
                    },
                ],
            },
            # 3. kmeans
            {
                "name": "kmeans_classification",
                "filter_low_score_threshold": 0.1,
                **get_root_chapter_config(neg_syllabus_regs=R_ALL_COMMITTEES),
            },
        ],
    },
    {
        "path": ["B(f)-Details of non-compliance with rules 3.10 and 3.10A and remedial steps"],
        # "post_process": must_contain_professional_text,
        "models": [
            # 先披露了规则，然后描述遵守了上面提到规则 https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/3108#note_396453
            # 先匹配 3.10(1)
            #           超过三名的独立非执行董事 独立非执行董事必须占董事会成员人数至少三分之一
            #  再匹配 3.10(2)
            #           专业知识 技能和经验
            #            独立非执行董事具备适当的专业资格
            # 1. 先提取 kmeans 分组之后仅有一个高分的答案
            {
                "name": "kmeans_classification",
                "para_pattern": [
                    MatchMulti.compile(R_PROFESSIONAL, "(three|four|five|six)|(at least three).*director", operator=all)
                ],
                "filter_low_score_threshold": 0.1,
                "high_score_elements_count": 1,
            },
            # 2. 先提取 CG 的 INED 描述了 3.10 和 3.10A 的高分答案
            {
                "name": "yoda_layer",
                "threshold": 0.8,
                "multi_elements": True,
                "parent_features": R_CG_CHAPTER_TITLES,
                "parent_must_be_root": True,
                "page_first_as_parent_syllabus": True,
                "rule": ScoreContinuedElem[Para](
                    filter=ParaTextFilter(
                        MatchMulti.compile(
                            r"\bmee?t\b.*?requirement|\bcompli.*?Rule", r"3\.10[（(](1|2)|3\.10A", operator=all
                        )
                    ),
                ),
            },
            # 3. 提取一些明确表示遵守了3.10\(1\) and \(2\)的答案
            # 以及明确说了没有遵守 也就是Comply的
            {
                "name": "para_match",
                "paragraph_pattern": [
                    SplitBeforeMatch.compile(
                        MatchMulti.compile(
                            r"\bmet\b.*?requirement|pursuant|compl(y|ied|iance)",
                            r"\brule",
                            r"3\.10[（(]1",
                            r"3\.10[（(]2",
                            operator=all,
                        ),
                        separator=P_SEN_SEPARATOR,
                        operator=any,
                    ),
                    MatchMulti.compile(
                        r"Pursuant to Rules? 3.10\(1\), 3.10\(2\)",
                        r"(compliance|complied) with Rules? 3.10 and Rule 3.10A",
                        r"(compliance|complied) with.*Rules? 3.10\(1\) and (3.10)?\(2\)",
                        r"(compliance|complied) with Rule 5.05\(1\) and (5.05)?\(2\)",
                        r"(met|complied with|compliance) the requirements of Rule 3.10",
                        r"the Board.*(met|complied with|compliance) the requirements.*rules 3.10\(1\) and \(2\)",
                        r"fully compliant with Rule 3.10\(1\), Rule 3.10A",
                        operator=any,
                    ),
                    # 没有遵守
                    MatchMulti.compile(
                        r"in non-compliance with the requirements under Rule 5.05\(1\)",
                        r"details of non-compliance of the Listing Rules",
                        operator=any,
                    ),
                ],
                "as_follow_pattern": [r"the Board Diversity Policy has complied with the following.*?:$"],
            },
            # 4. 分两步提取 先提取 3.10(1) 再提取 3.10(2)
            {
                "name": "yoda_layer",
                "threshold": 1,
                "enum": CGEnum.NA,
                "rule": CHAPTER_LOCATOR.with_dest(
                    dest=ParaFromPos(
                        pattern=NeglectPattern.compile(
                            match=MatchMulti.compile(
                                rf"{R_INED} comprise approximately.*\(3 out of \d\)",
                                rf"at least one-third of its members being {R_INED}",
                                r"no less than one-third of the Board members",
                                rf"The Board.*composition with over half of the board members are.*{R_INED}",
                                MatchMulti.compile(
                                    "(the|our) board|the.*session of Board",
                                    R_COMPOSE,
                                    R_INED,
                                    r"(three|four|five|six)|(at least three)|one-?third of|\b3\b",
                                    operator=all,
                                ),
                                operator=any,
                            ),
                            unmatch=MatchMulti.compile("principal duties of the Nomination Committee", operator=any),
                        ),
                        limit=50,
                        adapter=FirstParaAsPos(),
                    ).link_next(
                        filter=FollowMustExist(),
                        locator=Operator.union(
                            TableFromPos(
                                limit=1,
                                filter=OrFilter.from_filters(
                                    AdjacentElemFilter(
                                        offset=-1,
                                        filter=ParaTextFilter(
                                            pattern=MatchMulti.compile(
                                                MatchMulti.compile(
                                                    "Board committees of the Company are as follows:", operator=all
                                                ),
                                                operator=any,
                                            )
                                        ),
                                    ),
                                ),
                            ),
                            ParaFromPos(
                                pattern=MatchMulti.compile(
                                    MatchMulti.compile(
                                        r"met the requirement",
                                        r"the Listing Rules relating to the appointment of Independent Non-executive",
                                        operator=all,
                                    ),
                                    r"each director.*expertise relevant to the business operations",
                                    r"possesses the required experience and management expertise",
                                    r"as an.*?consolidated financial expert",
                                    NeglectPattern.compile(
                                        match=MatchMulti.compile(
                                            f"{R_INED}|The Directors|Members of the Board", R_PROFESSIONAL, operator=all
                                        ),
                                        unmatch=MatchMulti.compile(
                                            r"Nomination Policy",
                                            r"^PROFESSIONAL DEVELOPMENT OF THE DIRECTORS$",
                                            r"seek independent professional advice",
                                            r"complied with the code provision.*training\.",
                                            MatchMulti.compile(
                                                r"\b(attend|opportunit|monitor|participat)", r"training", operator=all
                                            ),  # 参加培训相关的不需要
                                            r"refresh their knowledge and skills",  # 参加培训相关的不需要
                                            r"approach to diversity|diversity policy",
                                            r":$",
                                            operator=any,
                                        ),
                                    ),
                                    operator=any,
                                ),
                                limit=30,
                            ),
                        ),
                    )
                ),
            },
            # 5. 明确表示没有遵守相关规则 https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/3108#note_396643
            {
                "name": "yoda_layer",
                "threshold": 1,
                "enum": CGEnum.C,
                "rule": CGChapterLocator(
                    min_page=5,
                    pattern=P_B_BOARD_CHAPTER.union(r"PERFORMANCE OF DUTIES BY INDEPENDENT NON-EXECUTIVE DIRECTORS"),
                ).with_dest(
                    dest=ParaFromPos(
                        pattern=MatchMulti.compile(
                            r"the\s*Company\s*fail(ed)?\s*to\s*meet\s*the\s*(following)\s*requirements:$",
                            r"Failure to comply with the requirements of Rules? 3\.10",
                            operator=any,
                        ),
                        limit=20,
                        dest=ContinuedElem[Para](
                            filter=ParaTextFilter(
                                pattern=MatchMulti.compile(
                                    # at least three independent non-executive directors on the Board under Rule 3\.10(\(1\))? of the Listing Rules
                                    rf"at\s*least\s*three{R_INED}",
                                    rf"at\s*least\s*(one-third|1/3)\s*of\s*its members\s*\w+\s*{R_INED}",
                                    # r"Rule\s*3\.10(\(1\)|A|\.1)? of the Listing Rules",
                                    r"rules?\s*3\.10.*?of the Listing Rules",
                                    operator=any,
                                )
                            ),
                            limit=10,
                        ),
                    ),
                ),
            },
            # 6. 特例 https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/3536#note_430226
            {
                "name": "para_match",
                "multi_elements": True,
                "paragraph_pattern": MatchMulti.compile(
                    MatchMulti.compile(
                        r"Independent Non-executive Directors",
                        r"guidelines for assessment of independence under Rule 3.13",
                        r"terms of office for no more than three years",
                        operator=all,
                    ),
                    MatchMulti.compile(
                        "the board|the.*session of Board",
                        R_COMPOSE,
                        rf"(three|four|five|six)|(at least three)|one-?third of.*{R_INED}",
                        r"No Director has any personal relationship",
                        operator=all,
                    ),
                    operator=any,
                ),
            },
            # 7. https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/3536#note_430226
            {
                "name": "para_match",
                "paragraph_pattern": MatchMulti.compile(
                    r"All Directors have appropriate professional qualification or substantive experience and industry knowledge.",
                    MatchMulti.compile(
                        r"Independent Non-executive Directors",
                        r"guidelines for assessment of independence under Rule 3.13",
                        r"terms of office for no more than three years",
                        operator=all,
                    ),
                    MatchMulti.compile(
                        "the board|the.*session of Board",
                        R_COMPOSE,
                        R_INED,
                        "(three|four|five|six)|(at least three)|one-?third of",
                        R_PROFESSIONAL,
                        operator=all,
                    ),
                    operator=any,
                ),
                "as_follow_pattern": [r"the Board Diversity Policy has complied with the following.*?:$"],
            },
            # 8.
            {
                "name": "score_filter",
                "threshold": 0.9,
                "multi_elements": True,
            },
        ],
    },
    {
        "path": ["B(g)-Why INED is independent if Rule 3.13 is not complied"],
        "models": [
            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/4153#note_499277
            {
                "name": "para_match",
                "parent_features": R_CG_CHAPTER_TITLES,
                "parent_must_be_root": True,
                "page_first_as_parent_syllabus": True,
                "threshold": 0.005,
                "syllabus_regs": r"Independent\s*Views\s*and\s*Input",
                "paragraph_pattern": SplitBeforeMatch(
                    MatchMulti.compile(
                        rf"assess\s*{reg_words(0, 4)}independence\s*{reg_words(0, 4)}{R_INED}",
                        r"ensure.*?independent\s*judgement",
                        operator=all,
                    ),
                    separator=P_SEN_SEPARATOR,
                ),
            },
            {
                "name": "para_match",
                "parent_features": R_CG_CHAPTER_TITLES,
                "parent_must_be_root": True,
                "page_first_as_parent_syllabus": True,
                "paragraph_pattern": MatchMulti.compile(
                    # 收到书面确认独立董事独立性
                    MatchMulti.compile(
                        rf"received\s*{R_WRITTEN_CONFIRMATION}\s*from\s*{reg_words(0, 3)}{R_INED}\s*{reg_words(0, 5)}independence",
                        P_INED_INDEPENDENCE,
                        operator=all,
                    ),
                    SplitBeforeMatch(
                        MatchMulti.compile(
                            rf"\bcompli{reg_words(0, 4)}independence\s*requirement",
                            R_INED,
                            operator=all,
                        ),
                        separator=P_SEN_SEPARATOR,
                    ),
                    MatchMulti.compile(
                        r"Rule\s*3\.13",
                        rf"{R_INED}",
                        operator=all,
                    ),
                    operator=any,
                ),
            },
            {
                "name": "yoda_layer",
                "threshold": 0.5,
                "indices_filter": set.issuperset,
                "enum": CGEnum.NA,
                "rule": ScoreParaFilter(
                    pattern=SplitBeforeMatch.compile(
                        pattern=PositionPattern.compile(
                            r"mee?t",
                            r"independen",
                            r"criteria",
                            r"rule\s*3\.13",
                        ),
                        separator="\band\b",
                        operator=any,
                    )
                ),
            },
            {
                "name": "score_filter",
                "parent_features": R_CG_CHAPTER_TITLES,
                "parent_must_be_root": True,
                "page_first_as_parent_syllabus": True,
                "threshold": 0.618,
            },
            {
                "name": "yoda_layer",
                "threshold": 1,
                "enum": CGEnum.NA,
                "rule": CGChapterLocator(
                    pattern=MatchMulti.compile(
                        r"^responsibilities\s*and\s*delegation",
                        r"^board\s*(independence|effective)",
                        r"^independent\s*views\s*of\s*the\s*board",
                        rf"{R_INED}",
                        operator=any,
                    ),
                    dest=ParaFromPos(
                        pattern=SplitBeforeMatch.compile(
                            PositionPattern.compile(
                                r"all|each",
                                R_INED,
                                "|".join(
                                    [
                                        r"are\s*independent",
                                        r"mee?ts?\s*?independen",
                                        r"compliance\s*independence",
                                        r"in\s*accordance\s*rule\s*3\.13",
                                        r"compliance.*?rule\s*3\.13",
                                    ]
                                ),
                                ignore=r"\b(with|of|the)\b",
                            ),
                            separator=r"\.(?=\s)",
                            operator=any,
                        ),
                        size=2,
                        limit=50,
                    ),
                ),
            },
        ],
    },
    {
        "path": ["B(h)-Relationship between board members and between chairman and CEO"],
        "models": [
            {
                "name": "score_filter",
                "threshold": 0.6,
            },
            {
                "name": "yoda_layer",
                "threshold": 0.5,
                "indices_filter": set.issuperset,
                "rule": ScoreStartParaLocator(
                    pattern=re.compile(R_FOLLOW),
                    dest=ContinuedElem[Para](
                        filter=ParaTextFilter(pattern=re.compile(R_APPELLATION)),
                        limit=1,
                    ),
                ),
            },
            # 在CG章节描述董事会成员关系 (financial,business,family,other material)
            {
                "name": "para_match",
                "multi_elements": True,
                "threshold": 0.01,
                "enum": CGEnum.C,
                "parent_features": R_CG_CHAPTER_TITLES,
                "parent_must_be_root": True,
                "page_first_as_parent_syllabus": True,
                "paragraph_pattern": SplitBeforeMatch.compile(
                    MatchMulti.compile(
                        r"Director|Supervisors|member",
                        r"financial|business|family|relevant|material",
                        rf"\brelat|{R_RELATIONSHIP}",
                        operator=all,
                    ),
                    separator=P_SEN_SEPARATOR,
                    operator=any,
                ),
            },
            {
                "name": "kmeans_classification",
                "para_pattern": rf"\brelat|{R_RELATIONSHIP}|famaliy\s*member",
                "filter_low_score_threshold": 0.1,
            },
        ],
    },
    {
        "path": ["B(i)-Directors' compliance with C.1.4"],
        "post_process": supply_table,
        "models": [
            {
                "name": "yoda_layer",
                "threshold": 0.618,
                "indices_filter": set.issuperset,
                "rule": ScoreStartParaLocator(
                    pattern=re.compile(R_FOLLOW),
                    dest=TableFromPos(limit=1),
                ),
            },
            {
                "name": "score_filter",
                "threshold": 0.618,
                "multi_elements": True,
            },
            {
                "name": "yoda_layer",
                "threshold": 1,
                "rule": CGChapterLocator(
                    pattern=MatchMulti.compile(r"director", "train", operator=all),
                    strict=StrictMode.CHAPTER,
                    dest=ContinuedElem[PT](
                        skip=OrFilter(
                            (
                                ParaLensFilter(le=10),
                                ParaTextFilter(pattern=MatchMulti.compile(*R_CG_CHAPTER_TITLES, operator=any)),
                                ParaTextFilter(
                                    pattern=SplitBeforeMatch.compile(
                                        r"^(\w|\d+)$",
                                        separator=r"\s",
                                        operator=all,
                                    )
                                ),
                            )
                        ),
                        start=0,
                    ),
                ),
            },
            {
                "name": "yoda_layer",
                "threshold": 1,
                "rule": CHAPTER_LOCATOR.with_dest(
                    dest=ParaFromPos(
                        pattern=PositionPattern.compile(
                            "each|all",
                            "director",
                            "train",
                        ),
                        limit=100,
                    ),
                ),
            },
        ],
    },
]
