import re
from typing import List

from remarkable.common.common_pattern import P_SEN_SEPARATOR, R_CHAPTER_PREFIX, R_CHAPTER_SUFFIX, R_DR_CHAPTER_TITLES
from remarkable.common.pattern import (
    MATCH_ALWAYS,
    MatchMulti,
    NeglectPattern,
    PositionPattern,
    SplitBeforeMatch,
)
from remarkable.common.util import clean_txt
from remarkable.pdfinsight.reader import PdfinsightSyllabus
from remarkable.predictor.hkex_predictor.models.yoda_layer.filter import (
    Adjacent<PERSON>lem<PERSON>ilter,
    And<PERSON><PERSON>er,
    Or<PERSON>ilter,
    ParaTextFilter,
    TableRowColFilter,
)
from remarkable.predictor.hkex_predictor.models.yoda_layer.operator import Operator
from remarkable.predictor.hkex_predictor.models.yoda_layer.rule.by_pos import ParaFromPos, TableFromPos
from remarkable.predictor.hkex_predictor.models.yoda_layer.rule.root import (
    CGCascadeChapterLocator,
    ESGCascadeChapterLocator,
    <PERSON><PERSON><PERSON><PERSON><PERSON>er,
    ScoreParaFilter,
)
from remarkable.predictor.hkex_predictor.pattern import R_BE
from remarkable.predictor.hkex_predictor.schemas.pattern import (
    reg_words,
)
from remarkable.predictor.models.base_model import AS_FOLLOW_START_PATTERN, R_PRIMARY_ROLE, BaseModel
from remarkable.predictor.models.para_match import AsFollowType
from remarkable.predictor.schema_answer import OutlineResult, PredictorResult

R_FEMALE = r"(females?|wom[ae]n|different\s*gender(?!\s*[,，]))"
R_DIRECTOR = r"(director|board\s*member)s?"
R_BOARD = r"(Board|nomination\s*committee)"
R_STAFF = r"(workforce|employ(ee|ment)|staff|the\s*group|colleague|people|all\s*levels)s?"

P_J_A_KEYWORDS = MatchMulti.compile(
    MatchMulti.compile(r"adopted", r"Board\s*diversity", r"policy", operator=all),
    MatchMulti.compile(
        r"not\s*limited\s*to|without\s*limitation",
        r"\bgender\b",
        r"\bage\b",
        r"\b(cultur(al|e)|education|professional|skill)",
        operator=all,
    ),
    operator=any,
)

R_J_B_NEGLECT = MatchMulti.compile(
    R_PRIMARY_ROLE,
    NeglectPattern(
        match=re.compile(R_STAFF, re.I),
        unmatch=re.compile(r"director|(in|to|of)\s*the\s*board|at\s*all\s*levels", flags=re.I),
    ),
    r"Board members (have|are).*(acquired accounting|working experience|independent non-executive Directors|achieved.*target)",
    r"Nomination Committee held one meeting",
    operator=any,
)

R_J_B_KEYWORDS = [
    R_BOARD,
    rf"gender|{R_FEMALE}|candidate|succession|potential",
]

# 规则J(b)-NA：这组正则表示当前董事会已满足了多元化的情况
R_ACHIEVED = [
    r"(?<!be)(?<!be )(achieved|satisfi(y|e[sd])|satisfactory)(?!iver)",
    r"(is|was|are|were|ha[sd]|have)\s*diversified",
    r"to\s*be\s*sufficient",
    r"no\s*change\s*was\s*required",
    r"should\s*continue\s*unchanged",
    rf"{R_BE}\s*sufficiently\s*diverse",
]
P_ACHIEVED = NeglectPattern.compile(
    match=MatchMulti.compile(
        r"|".join(R_ACHIEVED),
        r"target|objective|diversi",
        rf"gender|{R_FEMALE}|director|board",
        operator=all,
    ),
    unmatch=r"except[^\.]+a\s*single\s*gender",
)

# 规则J(b)-Comply：匹配上这组正则，表示明确实现目标的措施，Comply
P_J_B_MEASURE = MatchMulti.compile(
    r"recruits\s*employees.*regardless\s*of\s*gender.*?male\s*and\s*female",
    r"take initiatives\s*to\s*identify\s*female\s*candidate(\(s\)|s)?\s*to\s*enhance\s*the\s*gender\s*diversity",
    operator=any,
)
# 规则J(b)-Comply：匹配上这组正则，表示明确的董事会性别多元化计划，必然为Comply
# NeglectPattern.compile(
P_J_B_GENDER_PLANS = MatchMulti.compile(
    MatchMulti.compile(
        rf"Ms\.|{R_FEMALE}", r"has\s*been\s*appointed", R_DIRECTOR, r"maintain|continue|to\s*achieve", operator=all
    ),
    MatchMulti.compile(
        rf"Ms\.|{R_FEMALE}", r"appointment", R_DIRECTOR, r"maintain|continue|to\s*achieve", operator=all
    ),
    MatchMulti.compile(r"will|shall", rf"{R_FEMALE}\s*candidate", R_DIRECTOR, operator=all),
    MatchMulti.compile(r"candidate", r"board", r"to\s*achieve\s*gender\s*diversity", operator=all),
    MatchMulti.compile(
        rf"at\s*least\s*(a|one|1)\s*({R_FEMALE}|{R_DIRECTOR})|(will|shall|to)\s*(appoint|review)",
        R_DIRECTOR,
        R_FEMALE,
        operator=all,
    ),
    MatchMulti.compile(r"has\s*set\s*a\s*goal\s*of\s*increasing\s*the\s*(percentage|ratio)", R_FEMALE, operator=all),
    MatchMulti.compile(
        rf"(gender\s*diversity|Mr?s\.|{R_FEMALE}\s*{R_DIRECTOR}|(fe)?male-to-(fe)?male)",
        rf"maintain(s|ed|ing)?|((will|would|may|shall|aim(s|ed)?\s*(at|to)|ha(s|d|ve)\s*always|{R_BE})\s*"
        + r"((continued?|striv(ed?|ing)|commit(ted|ing)|contemplat(ed?|ing)|consider(ed|ing))\s*to\s*)?"
        + r"((improv|enhanc|increas|includ)(e[sd]?|ing)|adjust(s|ed|ing)?"
        + r"|(take|took|taken)\s*(steps)?|pay\s*due\s*regard\s*to))",
        operator=all,
    ),
    MatchMulti.compile(
        rf"{R_FEMALE}|gender",
        r"|".join(
            [
                r"(target|object(ive)?)s?\s*to\s*maintain",
                r"(target|object(ive)?)s?\s*have\s*been\s*set\s*to\s*maintain",
                r"to\s*(implement|achieve)\s*the\s*Board\s*Diversity",
            ]
        ),
        operator=all,
    ),
    rf"target\s*number\s*of\s*{R_FEMALE}\s*(members|{R_DIRECTOR})",
    rf"proportion\s*of\s*{R_FEMALE}\s*(members|{R_DIRECTOR})\s*of\s*the\s*Board",
    rf"(bring\s*the\s*({R_FEMALE}|gender)\s*(representation|ratio|percent(age)?)\s*to)",
    r"achieves?\s*gender\s*diversity\s*through",
    r"achieve\s*an\s*appropriate\s*balance\s*of\s*gender\s*diversity",
    # r"increase\s*the\s*female\s*proportion",
    r"continues?\s*the\s*(existing\s*)?gender\s*diversity\s*(in|of|to)\s*the\s*Board",
    r"to\s*enhance\s*female\s*representation",
    r"\s*to\s*develop\s*a\s*pipeline\s*of\s*female\s*potential\s*successors\s*to\s*the\s*Board",
    r"will\s*continue\s*to\s*take\s*gender\s*diversity\s*into\s*consideration",
    rf"targeted\s*to\s*maintain\s*the\s*level\s*of\s*{R_FEMALE}\s*representation",
    operator=any,
)

# 规则J(b)-Comply：这组正则表示 明确的董事会性别多元化目标，但如果提到已满足多元化，则为NA
R_J_B_OBJECTIVES = (
    rf"gender\s*target\s*{R_BE}\s*as\s*follows",
    r"the\s*following\s*(measurable|numerical)\s*objectives.*:$",
    r"board\s*diversity\s*policy\s*sets\s*out\s*a\s*clear\s*objective",
    r"to\s*avoid\s*a\s*single\s*gender",
    r"goal\s*of\s*achieving\s*gender\s*diversity",
    rf"goal\s*of\s*(increasing|achieving)\s*{reg_words(0, 3)}(percentage|ratio)\s*of\s*{R_FEMALE}\s*on\s*(its|our|the)\s*board",
    r"will\s*consider.*(board|gender)\s*diversity",
    rf"consider.*(setting|implementing)\s*{reg_words(0, 3)}(measurable|numerical)\s*(objective|target)s?",
)
P_J_B_OBJECTIVES = MatchMulti.compile(*R_J_B_KEYWORDS, r"|".join(R_J_B_OBJECTIVES), operator=all)

# 规则J(b)-NA：这组正则表示 没有性别多元化计划
P_J_B_NO_PLANS = MatchMulti.compile(
    r"no\s*specific\s*targets\s*being\s*set",
    rf"{R_BOARD}.*ha(d|s|ve)\s*not\s*set\s*{reg_words(0, 3)}(target|objective)s?",
    rf"neither\s*the\s*{R_BOARD}\s*nor\s*the\s*{R_BOARD}\s*ha(d|s|ve)\s*set\s*any\s*(measurable|numerical)\s*(objective|target)",
    operator=any,
)

R_BOARD_COMPOSITION = r"(Board composition|composition of the Board)"
# 规则J(b)-NA：这组正则表示 董事会组成多元化，符合董事会多元化政策的标准
# 跟 R_J_B_OBJECTIVES 是类似的规则， 这个规则限制比较严格
P_J_B_ALREADY_COMPLIANCE = MatchMulti.compile(
    rf"{R_BOARD_COMPOSITION} is diverse and meets the criteria of the board diversity policy",
    rf"considers.*?{R_BOARD_COMPOSITION} is characterised by diversity.*gender ratio \(male to female",
    r"achieved gender diversity in respect of the Board\.$",
    r"The\s*current\s*Board\s*is\s*considered\s*well-balanced\s*and\s*of\s*a\s*diverse",
    operator=any,
)

R_GENDER = r"\b((fe)?males?|(wo)?m[ae]n)\b"
R_ESG = r"\bESG\s*Report|ENVIRONMENTAL[,，]\s*SOCIAL\s*AND\s*GOVERNANCE"

R_J_C_NEG_CHAPTERS = [
    rf"Environmental[,，]?\s*Social\s*and\s*Governance\s*Report{R_CHAPTER_SUFFIX}",
    rf"環境、社會及管治報告{R_CHAPTER_SUFFIX}",
    *R_DR_CHAPTER_TITLES,
]
# 规则J(c)条件1：披露员工的性别数据
R_NUMBER = r"[(（]?\d+(\.\d{1,4}%|%)?[\)）]?"
R_J_C_GENDER_DATA = [
    # r"gender\s*ratio",
    rf"{R_GENDER}\|\d+",
    rf"{R_GENDER}\s*[:：|]\s*{R_GENDER}",
    rf"number\s*of\s*{R_GENDER}",
    r"\b(wo)?m[ae]n[-\s]*to[-\s]*(wo)?m[ae]n\b",
    r"\b(fe)?males?[-\s]*to[-\s]*(fe)?males?\b",
    rf"(percentages?|ratio)\s*of\s*{R_GENDER}",
    rf"{R_NUMBER}\s*(of\s*)?{R_GENDER}",
    rf"{R_NUMBER}\s*of\s*(the\s*|our\s*|total\s*){{0,2}}{R_STAFF}.*{R_BE}\s*{R_GENDER}",
    rf"{R_NUMBER}\s*{R_BE}\s*{R_GENDER}",
    rf"{R_GENDER}\s*{R_STAFF}\s*({R_BE}|accounted\s*for)\s*{R_NUMBER}",
    rf"about\s*\d+\.\d+%.*{R_BE}\s*(female|wom[ae]n)",
    rf"{R_GENDER}\s*comprises\s*approximately\s*{R_NUMBER}\s*of.+{R_STAFF}",
    rf"(fe)?male\s*and\s*(fe)?male\s*{R_STAFF}",
    rf"a\s*total\s*of\s*\d+\s*{R_STAFF}",
    rf"{R_GENDER}\s*and\s*{R_GENDER}\s*{R_STAFF}\s*of\s*around\s*\d+:\s+\s*ratio",
    rf"Detail\s*of\s*the\s*gender\s*ratio|gender\s*ratio.*{R_ESG}",
]

# 规则J(c)排除 仅提到了董事会的性别多元化 ,J(c)需要的是全体员工的性别多元化
P_J_C_NEGLECT = MatchMulti.compile(
    r"bringing the Board to gender parity",
    r"achieved gender diversity in respect of the Board\.$",
    r"^With regard to gender diversity on the Board",
    r"satisfies our board diversity policy\.$",
    operator=any,
)

# 规则J(c)条件2：提到了多元化目标或者满足了目标
P_J_C_TARGETS = SplitBeforeMatch.compile(
    NeglectPattern.compile(
        match=MatchMulti.compile(
            rf"{R_STAFF}",
            r"\b(gender|females?|wom[ae]n)\b|divers[ei]|recruitment",
            rf"achiev.*gender.*divers|sufficient|diversified|satisfi(y|e[sd])|\b{R_BE}\s*satisfactory"
            + rf"|\b{R_BE}\s*effective(?!ness)|\b{R_BE}\s*(disclosed|maintained)"
            + r"|reasonable\s*(range|level)|set\s*specific\s*target"
            + rf"|\b(to|will)\s*(promote|enhance|encourage|maintain|increase)\s*{reg_words(0, 5)}(gender|diversity|female|wom[ae]n)"
            + r"|\b(aim|committ?)(s|ed)?\s*(at|to)|measurable\s*objectives|free\s*of\s*gender",
            operator=all,
        ),
        unmatch=MatchMulti.compile(
            NeglectPattern(
                match=re.compile(r"directors|candidate|candate|succession|potential", re.I),
                unmatch=re.compile(R_STAFF, re.I),
            ),
            P_J_C_NEGLECT,
            operator=any,
        ),
    ),
    separator=r"(\r)?\n",
    operator=any,
)

# 规则J(c)：过滤表格的条件
R_J_C_NEGLECT_TABLE = r"causal"
J_C_TABLE_FILTER = AndFilter.from_filters(
    TableRowColFilter(
        pattern=re.compile(
            rf"{R_GENDER}(\s*{R_STAFF})?\s*\t|gender\s*ratio\t|number\s*of\s*({R_GENDER}|persons)", re.I
        ),
        limit=10,
        type="row",
    ),
    # 表名或者表头必须匹配关键词
    OrFilter.from_filters(
        TableRowColFilter(
            pattern=NeglectPattern.compile(
                match=re.compile(R_STAFF, re.I), unmatch=re.compile(rf"train|{R_J_C_NEGLECT_TABLE}", re.I)
            ),
            limit=10,
            type="row",
        ),
        AdjacentElemFilter(
            -1,
            ParaTextFilter(
                pattern=NeglectPattern.compile(
                    match=re.compile(R_STAFF, re.I), unmatch=re.compile(rf"train|turnover|{R_J_C_NEGLECT_TABLE}", re.I)
                )
            ),
        ),
    ),
)


def add_follow_para(answers: List[dict | PredictorResult], **kwargs) -> List[dict]:
    # 如果答案中不包含表格 且最后一段以冒号结尾 获取下方的表格
    if not answers:
        return []
    elements = BaseModel.get_elements_from_answer_result(answers)
    if not elements:
        return []
    last_element = elements[-1]
    if not AS_FOLLOW_START_PATTERN.nexts(clean_txt(last_element.get("text", ""))):
        return answers
    predictor = kwargs["predictor"]
    next_paras = predictor.pdfinsight.find_elements_near_by(
        last_element["index"],
        step=1,
        aim_types=["PARAGRAPH"],
        amount=10,
    )
    for next_para in next_paras:
        if AS_FOLLOW_START_PATTERN.nexts(clean_txt(next_para.get("text", ""))):
            elements.append(next_para)
        else:
            break
    page_box = PdfinsightSyllabus.elements_outline(elements)
    if not page_box:
        return []
    origin_enums = BaseModel.get_enums_from_answer_result(answers)
    answer = PredictorResult(
        [OutlineResult(page_box=page_box, element=elements[0], origin_elements=elements)],
        schema=predictor.schema,
        value=origin_enums[0] if origin_enums else None,
    )
    return [answer]


predictor_options = [
    {
        "path": ["J(a)-Board diversity policy"],
        "post_process": add_follow_para,
        "models": [
            {
                "name": "score_filter",
                "multi_elements": True,
                "threshold": 0.618,
            },
            {
                "name": "kmeans_classification",
                "multi_elements": True,
                "neglect_syllabus_regs": [r"board\s*nomination\s*policy"],
                "filter_low_score_threshold": 0.1,
            },
            {
                "name": "yoda_layer",
                "indices_filter": set.issuperset,
                "rule": CGCascadeChapterLocator(
                    only_chapter=False,
                    chapter_patterns=[
                        MatchMulti.compile(
                            rf"{R_CHAPTER_PREFIX}(board|gender)\s*diversity(\s*policy)?{R_CHAPTER_SUFFIX}",
                            rf"{R_CHAPTER_PREFIX}Nomination\s*Committee(\s*and\s*diversity)?{R_CHAPTER_SUFFIX}",
                            operator=any,
                        )
                    ],
                    dest=ParaFromPos(
                        limit=5,
                        size=2,
                        pattern=P_J_A_KEYWORDS,
                    ),
                ),
            },
            {
                "name": "para_match",
                # "multi_elements": True,
                "paragraph_pattern": P_J_A_KEYWORDS,
            },
        ],
    },
    {
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/2703
        # Comply场景：①有明确的董事会性别多元化计划②提及董事会女性任职情况并表示会保持或改进③近期有女性董事任职计划
        # NA场景：①明确提到没有性别多元化计划②提及董事会女性任职计划并表示已满足性别多元化
        "path": ["J(b)-Numerical targets and timelines and measures adopted"],
        "models": [
            # 模型1：取分值0.7以上的所有段落
            {
                "name": "score_filter",
                "multi_elements": True,
                "threshold": 0.7,
                "neglect_pattern": R_J_B_NEGLECT,
            },
            # 模型2: 第一个模型有可能会过滤一些较长的段落其中包好主要职责
            {
                # 有且只有一个分数大于0.7
                "name": "yoda_layer",
                "threshold": 0.7,
                "rule": ScoreLensFilter(length=1),
            },
            # 模型3：匹配必然Comply的段落
            {
                "name": "para_match",
                "multi_elements": True,
                "remove_blank": True,
                # "threshold": 0.02,
                "as_follow_type": AsFollowType.ANY,
                "enum_from_multi_element": True,
                # "filter_primary_role_elements": True,
                "as_follow_pattern": MatchMulti.compile(
                    r"gender|measurable\s*objectives",
                    r"board|director",
                    r"(follow|below).*[:：]\s*$",
                    operator=all,
                ),
                "paragraph_pattern": MatchMulti.compile(
                    P_J_B_GENDER_PLANS,
                    P_J_B_MEASURE,
                    operator=any,
                ),
                "neglect_pattern": R_J_B_NEGLECT,
            },
            # 模型4：取可能为C也可能为NA的段落
            {
                "name": "para_match",
                "multi_elements": True,
                "remove_blank": True,
                # "threshold": 0.02,
                "as_follow_type": AsFollowType.ANY,
                "enum_from_multi_element": True,
                "as_follow_pattern": MatchMulti.compile(
                    r"gender|measurable\s*objectives",
                    r"board|director",
                    r"(follow|below).*[:：]\s*$",
                    operator=all,
                ),
                "paragraph_pattern": MatchMulti.compile(
                    P_J_B_OBJECTIVES,
                    P_ACHIEVED,
                    P_J_B_NO_PLANS,
                    P_J_B_ALREADY_COMPLIANCE,
                    operator=any,
                ),
                "neglect_pattern": R_J_B_NEGLECT,
            },
            # 模型5：特例
            {
                "name": "para_match",
                "paragraph_pattern": [
                    SplitBeforeMatch.compile(
                        PositionPattern.compile(
                            r"enhancing",
                            r"gender\s*diversity",
                        ),
                        separator=P_SEN_SEPARATOR,
                        operator=any,
                    ),  # # 在披露的目标不够具体时（比如未披露具体的女性员工比例），如果同时披露了实现目标的措施，属于Comply
                ],
            },
            # 模型6：保底用kmeans
            {
                "name": "kmeans_classification",
                "multi_elements": True,
                "filter_low_score_threshold": 0.02,
                "para_pattern": MatchMulti.compile(
                    P_J_B_GENDER_PLANS,
                    P_J_B_OBJECTIVES,
                    P_ACHIEVED,
                    P_J_B_NO_PLANS,
                    P_J_B_ALREADY_COMPLIANCE,
                    operator=any,
                ),
                "neglect_pattern": R_J_B_NEGLECT,
            },
        ],
    },
    {
        # 披露员工性别多元化情况或性别多元化目标及措施
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/2704
        "path": ["J(c)-Gender ratios in the workforce and gender diversity objectives"],
        "models": [
            # 模型1：若提到ESG report，则尝试到ESG report中定位表格及关键语句
            {
                "name": "yoda_layer",
                "threshold": 0.1,
                "multi_elements": True,
                "rule": Operator.all(
                    ScoreParaFilter(
                        pattern=MatchMulti.compile(
                            R_STAFF,
                            r"detail|set\s*out|be\s*found|disclos(ed|ure)|\bmentioned\b",
                            rf"{R_ESG}|(in|on|at)\s*page\s*\d{{1,4}}|(of|in)\s*this",
                            operator=all,
                        )
                    ),
                    ScoreParaFilter(
                        pattern=P_J_C_TARGETS,
                    ),
                    ESGCascadeChapterLocator(
                        chapter_patterns=[MATCH_ALWAYS],
                        dest=TableFromPos(
                            limit=200,
                            size=1,
                            filter=J_C_TABLE_FILTER,
                        ),
                    ),
                ),
            },
            # 模型2：同时满足以下条件①多元化目标或已实现多元化②男性/女性员工个数或占比或性别数据表格
            {
                "name": "yoda_layer",
                "threshold": 0.01,
                # "multi_elements": True,
                "neglect_syllabus_regs": R_J_C_NEG_CHAPTERS,
                "rule": Operator.all(
                    ScoreParaFilter(
                        pattern=P_J_C_TARGETS, skip_pattern=MatchMulti.compile(R_J_C_NEGLECT_TABLE, operator=any)
                    ),
                    Operator.union(
                        ScoreParaFilter(
                            pattern=MatchMulti.compile(
                                r"|".join(R_J_C_GENDER_DATA),
                                R_STAFF,
                                operator=all,
                            ),
                            skip_pattern=MatchMulti.compile(R_J_C_NEGLECT_TABLE, operator=any),
                        ),
                        CGCascadeChapterLocator(
                            only_chapter=False,
                            chapter_patterns=[
                                MatchMulti.compile(
                                    rf"{R_CHAPTER_PREFIX}(board|gender|workforce|employ(ment|ee)s?|peoples?)\s*diversity(\s*policy)?{R_CHAPTER_SUFFIX}",
                                    operator=any,
                                )
                            ],
                            dest=TableFromPos(
                                limit=10,
                                size=1,
                                filter=J_C_TABLE_FILTER,
                            ),
                        ),
                    ),
                ),
            },
            # 模型3：直接取高分结果
            {
                "name": "score_filter",
                "multi_elements": True,
                "threshold": 0.618,
                "neglect_syllabus_regs": R_J_C_NEG_CHAPTERS,
                "pattern": [R_STAFF],
                "neglect_pattern": [R_J_C_NEGLECT_TABLE],
            },
            # 模型4：保底用kmeans
            {
                "name": "kmeans_classification",
                "multi_elements": True,
                "filter_low_score_threshold": 0.1,
                "neglect_syllabus_regs": R_J_C_NEG_CHAPTERS,
                "para_pattern": [R_STAFF],
                "neglect_pattern": [R_J_C_NEGLECT_TABLE, P_J_C_NEGLECT],
            },
        ],
    },
]
