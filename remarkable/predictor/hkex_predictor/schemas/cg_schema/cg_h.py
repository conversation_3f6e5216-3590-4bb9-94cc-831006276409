import re

from remarkable.common.common_pattern import P_SEN_SEPARATOR, R_CHAPTER_SUFFIX
from remarkable.common.pattern import MatchMulti, NeglectPattern, SplitBeforeMatch
from remarkable.predictor.hkex_predictor.pattern import R_BE
from remarkable.predictor.hkex_predictor.schemas.pattern import R_EFFECTIVE, reg_words

# 规则H(a)：必须包含这些关键词，才代表Comply
R_TUPLE_IA = (
    r"\s*((?i:internal\s*audit)|((?i:audit)|IA)\s*(?i:department|function))",
    r"\s*(\bIA|IA\b)",
)
R_IA_DEPARTMENT = r"((\bIA\s*)|(?i:(internal\s*)?audit))\s*(?i:department|function)"
R_NO_INTERNAL_AUDIT = (
    re.compile(rf"(?P<content>[^;；.]+?\b(?i:nor?)\s+{R_IA_DEPARTMENT}[^;；.]*?([.;；]|$))"),
    re.compile(
        rf"(?P<content>[^;；.]+?\b(?i:(not|neitheer|nor)\s+(yet\s*)?(have|had|has|set\s*up|establish(ed)?)\s*(the\s*|an?\s*)?){R_IA_DEPARTMENT}[^;；.]*?([.;；]|$))"
    ),
    re.compile(
        rf"(?P<content>[^;；.]+?\b(?i:no\s+(immediate\s*)?need\s*to\s*(set\s*up|establish)\s*an?)\s*{R_IA_DEPARTMENT}[^;；.]*?([.;；]|$))"
    ),
)
# 规则H(a)：满足以下正则优先匹配
R_TUPLE_EXACT_IA = (
    rf"(?i:need\s*for\s*an){R_TUPLE_IA[0]}",
    rf"(?i:no){R_TUPLE_IA[0]}",
    rf"(?i:(not\s*(have\s*|set\s*(up\s*)?)an)){R_TUPLE_IA[0]}",
    rf"(?i:(set(\s*up)?|has|have|had|establish(ed)?))\s*(an\s*|its\s*)?{reg_words(0, 2)}{R_TUPLE_IA[0]}",
)
R_TUPLE_H_B_KEYWORDS = (
    r"ha([sd]|ve)\s*annually\s*reviewed",
    r"annually(?!\s*\))",
    r"ha([sd]|ve)\s*conducted\s*(its|an)\s*annual\s*review",
    r"annual\s*review(?!\s*on\s*whether\s*there\s*is\s*a\s*need)",
    r"on\s*an?\s*(yearly|annual)\s*basis",
    # r"(during|in|for)\s*(the|this)\s*year",
    # r"ha([sd]|ve)\s*assessed",
    # r"(\d+|one|two|three|four|five|six)\s*times",
    # r"every\s*\w{1,6}\s*(months|days)",
)

# CHAPTER_LOCATOR = CGChapterLocator(
#     min_page=5,
#     pattern=re.compile(r"(risk|internal)\s*(management|control)|Annual\s*Confirmation", re.I),
# )
R_H_C_RISK_MGT = r"risk\s*(management|assessment)"
R_H_C_INTER_CTRL = r"internal\s*control"
R_H_C_CONFIRM = r"consider|think|confirm|acknowledge|conducted|satisfied|conclude|ensure|believe|is\s*of\s*the\s*(opinion|view)|no\s*significant\s*weakness\s*was\s*found|conclusion"
# 规则H(c)的否定关键词
P_H_C_UNMATCH = MatchMulti.compile(
    r"monitor|establish|evaluate|e v a l u a t e|provision|to\s*improve",
    r"responsib|r e s p o n s i b|duty|duties|roles?|objectives?|require",
    operator=any,
)

predictor_options = [
    {
        "path": ["H(a)-Whether issuer has internal audit function"],
        "models": [
            # 1. no internal audit department
            {
                "name": "para_match",
                "multi_elements": True,
                # 该规则枚举值不存在NA, https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/4503#note_511259
                # "enum": AnswerValueEnum.NA.value,
                "paragraph_pattern": R_NO_INTERNAL_AUDIT,
            },
            {
                "name": "shape_title",
                "regs": [
                    r"risk\s*management\s*organizational\s*structure",
                ],
                "include_title": False,
                "remove_blank": True,
            },
            {
                "name": "para_match",
                "paragraph_pattern": R_TUPLE_EXACT_IA,
                "flags": 0,
            },
            {
                "name": "para_match",
                "syllabus_regs": [
                    r"risk\s*(management|control)",
                ],
                "paragraph_pattern": R_TUPLE_IA,
                "flags": 0,
            },
            {
                "name": "score_filter",
                "threshold": 0,
                "pattern": R_TUPLE_IA,
                "flags": 0,
            },
            {
                "name": "kmeans_classification",
                "para_pattern": R_TUPLE_IA,
                "flags": 0,
            },
        ],
    },
    {
        "path": ["H(b)-Frequency of review and the period covered"],
        "models": [
            {
                "name": "score_filter",
                "threshold": 0.5,
            },
            {
                "name": "twice_para_match",
                "multi_elements": True,
                "syllabus_regs": [
                    r"(risk|internal)\s*(management|control)",
                    r"Annual\s*Confirmation",
                ],
                "paragraph_pattern": R_TUPLE_H_B_KEYWORDS,
                "second_pattern": (r"confirmed|conduct(ed|ing)",),
            },
            {
                "name": "kmeans_classification",
                "filter_low_score_threshold": 0.1,
            },
        ],
    },
    {
        "path": ["H(c)-Whether issuer considers them effective and adequate"],
        "models": [
            {
                # 百分之百正确的句式：发行人确认风险管理和内部监控有效且充分
                "name": "para_match",
                "skip_syllabus_title": True,
                "paragraph_pattern": SplitBeforeMatch.compile(
                    NeglectPattern.compile(
                        match=MatchMulti.compile(
                            # r"the\s*(board|company|group)",
                            R_H_C_CONFIRM,
                            rf"{R_H_C_RISK_MGT}\s*{reg_words(0, 6)}and\s*{R_H_C_INTER_CTRL}|{R_H_C_INTER_CTRL}\s*{reg_words(0, 2)}and\s*{R_H_C_RISK_MGT}",
                            MatchMulti.compile(
                                r"effective(ly)?\s*and\s*(adequate|sufficient|sound)(ly)?",
                                r"(adequate|sufficient|sound)(ly)?\s*and\s*effective(ly)?",
                                rf"{R_BE}\s*in\s*place\s*and\s*effective(ly|ness)?",
                                r"the\s*effectiveness\s*and\s*(adequacy|adequateness)",
                                r"the\s*(adequacy|adequateness)\s*and\s*effectiveness",
                                # r"(has|have|had)\s*set\s*up\s*a\s*sound\s*control\s*environment",
                                # rf"{R_BE}\s*satisfied\s*that\s*there\s*are\s*({R_EFFECTIVE})\s*(risk\s*management|{R_H_C_INTER_CTRL})",
                                operator=any,
                            ),
                            operator=all,
                        ),
                        unmatch=r"responsib|r e s p o n s i b|duty|duties|roles?|objectives?|require",
                    ),
                    separator=P_SEN_SEPARATOR,
                    operator=any,
                ),
            },
            {
                "name": "score_filter",
                "skip_syllabus_title": True,
                "threshold": 0.8,
            },
            {
                # 风控相关章节下可能会用：(these|such|the|the existing) systems代表风险管理和内部监控
                "name": "para_match",
                "skip_syllabus_title": True,
                "syllabus_regs": [
                    R_H_C_INTER_CTRL,
                    rf"REVIEW\s*OF\s*ADEQUACY\s*AND\s*EFFECTIVENESS{R_CHAPTER_SUFFIX}",
                    rf"Review\s*of\s*the\s*Systems{R_CHAPTER_SUFFIX}",
                ],
                "paragraph_pattern": SplitBeforeMatch.compile(
                    NeglectPattern.compile(
                        match=MatchMulti.compile(
                            R_H_C_CONFIRM,
                            MatchMulti.compile(
                                MatchMulti.compile(R_H_C_RISK_MGT, R_H_C_INTER_CTRL, operator=all),
                                r"(these|such|the(\s*existing)?|internal\s*control)\s*systems",
                                r"control\s*environment",
                                r"and\s*their\s*training\s*programmes\s*and\s*budget",
                                operator=any,
                            ),
                            R_EFFECTIVE,
                            operator=all,
                        ),
                        unmatch=P_H_C_UNMATCH,
                    ),
                    separator=P_SEN_SEPARATOR,
                    operator=any,
                ),
            },
            {
                "name": "score_filter",
                "skip_syllabus_title": True,
                "threshold": 0.618,
            },
            {
                "name": "kmeans_classification",
                "skip_syllabus_title": True,
                "filter_low_score_threshold": 0.1,
                "para_pattern": SplitBeforeMatch.compile(
                    NeglectPattern.compile(
                        match=MatchMulti.compile(
                            R_H_C_CONFIRM,
                            R_H_C_RISK_MGT,
                            R_H_C_INTER_CTRL,
                            R_EFFECTIVE,
                            operator=all,
                        ),
                        unmatch=P_H_C_UNMATCH,
                    ),
                    separator=P_SEN_SEPARATOR,
                    operator=any,
                ),
            },
        ],
    },
]
