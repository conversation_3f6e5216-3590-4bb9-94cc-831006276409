import re

from interdoc.element import Para

from remarkable.common.common_pattern import (
    P_CONTINUED,
    P_SEN_SEPARATOR,
    R_CG_CHAPTER_TITLES,
    R_CHAPTER_PREFIX,
    R_FOLLOW_PREFIX,
)
from remarkable.common.constants import CGEnum
from remarkable.common.pattern import (
    MatchMulti,
    NeglectPattern,
    SplitBeforeMatch,
)
from remarkable.pdfinsight.interdoc_reader import <PERSON>doc<PERSON><PERSON>er
from remarkable.predictor.hkex_predictor.models.yoda_layer.base import AnswerFilter, Option
from remarkable.predictor.hkex_predictor.models.yoda_layer.filter import (
    ChapterFilter,
    ParaLensFilter,
    ParaTextFilter,
)
from remarkable.predictor.hkex_predictor.models.yoda_layer.form import Answer
from remarkable.predictor.hkex_predictor.models.yoda_layer.operator import Operator
from remarkable.predictor.hkex_predictor.models.yoda_layer.rule.by_pos import (
    ContinuedElem,
    SentenceFromSplitPara,
)
from remarkable.predictor.hkex_predictor.models.yoda_layer.rule.root import (
    CGCascadeChapterLocator,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    ScoreParaFilter,
)
from remarkable.predictor.hkex_predictor.schemas.cg_schema.common_models import get_eb_models, get_ec_models
from remarkable.predictor.hkex_predictor.schemas.pattern import (
    P_NEED_IGNORE,
    R_NC,
)
from remarkable.predictor.models.base_model import P_PRIMARY_ROLE

R_ROLE_AND_FUNCTION = r"(dut(y|ies)(\s*and\s*authorit(y|ies))?|(roles?\s*and\s*)?functions?|responsibilit(y|ies))"

R_ED_III_KEYWORDS = r"nomination\s*Policy|PROCESS|Procedure|criteria"
P_ED_III_KEYWORDS = MatchMulti.compile(R_ED_III_KEYWORDS, operator=any)
# 按照优先级取对应章节
R_ED_III_COMPLY_CHAPTERS = [
    r"nomination\s*Policy",
    r"nomination\s*(criteria\s*and\s*)?(Procedure|PROCESS)",
    r"(PROCESS|Procedure|criteria).*?\s*of\s*directors",
    r"Selection\s*Criteria",
]
R_ED_III_OTHER_CHAPTERS = [
    r"(Appointment|re-election|candidate).*?\s*of\s*directors",
    rf"{R_CHAPTER_PREFIX}appointment\s*(and|[,，])\s*re-election\s*(AND\s*REMOVAL\s*)?of\s*directors",
]

P_ED_III_COMPLY = MatchMulti.compile(
    MatchMulti.compile(
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/3805#note_448338
        r"\bidentif|consider|^In\s*deciding|^In\s*respect\s*of",
        rf"{R_ED_III_KEYWORDS}|composition\s*of\s*the\s*Board|board[s'‘’]*\s*composition",
        r"integrity|experience|skill|education|profession|expertise|meritocracy",
        operator=all,
    ),
    rf"{R_NC}\s*(consider|discuss|review).*in selecting.*(candidate|director)",
    r"take into account factors.*(business model|specific needs)",
    r"ultimate decision.*based on (merit|contribution)",
    operator=any,
)

P_DIVERSITY = NeglectPattern.compile(
    match=r"(?<![,，])(?<![,，] )diversity(?!\s*[,，])", unmatch=r"nomination\s*Policy|PROCESS|Procedure|criteria"
)
R_ED_III_NEGS = [
    R_FOLLOW_PREFIX,
    # R_ROLE_AND_FUNCTION,
    rf"^duties\s*of\s*(the\s*)?{R_NC}",
    *P_PRIMARY_ROLE.patterns,
    NeglectPattern.compile(
        match=r"(proper|Objective|Appropriate|Suitable)\s*(procedure|criteria)",
        unmatch=MatchMulti.compile(
            "objective criteria and with due regards to the benefits of diversity", operator=any
        ),
        #     https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/3805#note_470029
    ),
    r"research\s*(the\s*)?(standard(\s*[,，]|\s*and)\s*)?(procedure|criteria)",
]
P_ED_III_NEG_CHAPTER = NeglectPattern.compile(match=r"diversity", unmatch=R_NC)
P_EXECUTIVE_DIRECTORS = re.compile(r"(^|non-)Executive\s*Directors", re.I)


class EDIIIAnswerFilter(AnswerFilter):
    """
    E(d)(iii)连续段落答案中包含了董事章节，则答案丢弃
    http://100.64.0.105:55647/#/project/remark/245342?treeId=6218&fileId=66274&schemaId=28&projectId=17&schemaKey=E(d)(iii)
    """

    def __call__(self, interdoc: InterdocReader, head: Answer, follow: Answer) -> Answer:
        for index in head.para_indices:
            if (chapter := interdoc.chapter_by_elem.get(index)) and P_EXECUTIVE_DIRECTORS.search(chapter.title):
                return Answer()
        return head


class EDIIIAnswerFilter1(AnswerFilter):
    """
    E(d)(iii) Appointment and re-election章节中，包含关键词才取
    """

    def __call__(self, interdoc: InterdocReader, head: Answer, follow: Answer) -> Answer:
        for index in head.para_indices:
            para = interdoc.find_by_index(index)
            if not para or not para.text:
                continue
            if P_ED_III_KEYWORDS.search(para.text):
                return head
        return Answer()


predictor_options = [
    {
        "path": ["NC-E(a)-Role and function"],
        "models": [
            {
                "name": "syllabus_based",
                "only_inject_features": True,
                "inject_syllabus_features": [
                    # r"__regex__(?i)Bank\s*?borrowing|__regex__largest_year_minus_0|__regex__(?i)^Current",
                    r"__regex__Nomination\s*Committee",
                ],
                "multi": True,
                "multi_elements": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "as_follow_pattern": [
                        r"include but are not limited to:$",
                        r"among others:$",
                        r"include, without limitation:$",
                        r"include, amongst other things:$",
                        rf"{R_ROLE_AND_FUNCTION}\s*(of|by)\s*the\s*{R_NC}:$",
                    ],
                    "neglect_below_pattern": (
                        r"^(CORPORATE\s*GOVERNANCE\s*REPORT\s*)?企業管治報告書?$",
                        r"^Corporate\s*Governance\s*Report$",
                    ),
                    "paragraph_pattern": (
                        rf"(?<!Performance\sof\s){R_ROLE_AND_FUNCTION}\s*(of|by)\s*the\s*{R_NC}",
                        rf"{R_NC}\s*is\s*to\s*deal\s*with",
                    ),
                },
                "table_model": "row_match",
                "table_config": {
                    "row_pattern": [
                        r"Role & Function",
                        r"角色與職能",
                    ]
                },
            },
            {
                "name": "para_match",
                "need_default_follows": True,
                "paragraph_pattern": (
                    rf"(?<!Performance\sof\s){R_ROLE_AND_FUNCTION}\s*(of|by)\s*the\s*{R_NC}\s*include:",
                ),
                "para_config": {
                    "neglect_below_pattern": (
                        r"^(CORPORATE GOVERNANCE REPORT )?企業管治報告書?$",
                        r"^Corporate Governance Report$",
                    ),
                },
            },
            {
                "name": "score_filter",
                "threshold": 0.25,
                "multi_elements": True,
                "syllabus_regs": [
                    R_NC,
                ],
            },
            {
                "name": "kmeans_classification",
                "filter_low_score_threshold": 0.1,
                "syllabus_regs": [
                    R_NC,
                ],
            },
        ],
    },
    {
        # 最优先位置，各个committee标题下披露的委员会成员及其类型
        # 次优位置，在1没有披露的情况下，提取各个committee标题下披露的委员会成员开会参与情况，即规则E(c)-Number of meetings and record 内容
        # 再次优位置，在1和2均没有披露的情况下，提取board meeting下汇总披露的各个委员会开会成员参与情况
        # 最后，在1，2，3均没有披露的情况下，提取board composition或board training中的董事list及其类型
        "path": ["NC-E(b)-Composition"],
        "element_candidate_count": 20,
        "models": [
            # 模型1-12：通用查找句子及as follow
            *get_eb_models(R_NC, add_yoda_model=True),
            # 模型13：特例 http://100.64.0.105:55647/#/project/remark/245397?treeId=6025&fileId=66219&schemaId=28&projectId=17&schemaKey=NC-E(b)
            {
                "name": "syllabus_based",
                "enum": CGEnum.C.value,
                "only_inject_features": True,
                "ignore_pattern": [P_NEED_IGNORE],
                "include_shape": True,
                "shape_as_table": True,
                "inject_syllabus_features": [
                    r"__regex__^Composition of th1e Board and Board Committees$",
                ],
                "table_model": "shape_title",
                "table_config": {
                    "only_inject_features": True,
                    "regs": [
                        r"Composition of the Board and Board Committees",
                    ],
                },
            },
        ],
    },
    {
        "path": ["NC-E(c)-Number of meetings and record of attendance"],
        "models": get_ec_models(R_NC, r"Nominati(on|ng)"),
    },
    {
        # alias: E(d)(iii) - Nomination policy
        "path": ["E(d)(iii)-Policy for directors nomination"],
        "models": [
            # 模型1：取NOMINATION policy,APPOINTMENT等整个章节+0.5分以上段落
            {
                "name": "yoda_layer",
                "multi_elements": True,
                "threshold": 0.5,
                # "syllabus_regs": [R_NC],
                "neglect_syllabus_regs": P_ED_III_NEG_CHAPTER,
                "rule": Operator.union(
                    Operator.any(
                        *[
                            CGCascadeChapterLocator(
                                chapter_patterns=[MatchMulti.compile(chapter_reg, operator=any)],
                                remove_blank=True,
                                dest=ContinuedElem[Para](
                                    stop=ChapterFilter(
                                        skip_pattern=MatchMulti.compile(P_CONTINUED, R_ED_III_KEYWORDS, operator=any)
                                    ),
                                    skip=ParaLensFilter(0, 10)
                                    | ParaTextFilter(pattern=MatchMulti.compile(*R_CG_CHAPTER_TITLES, operator=any)),
                                    limit=100,
                                    size=2,
                                    start=1,
                                ),
                            )
                            for chapter_reg in R_ED_III_COMPLY_CHAPTERS
                        ],
                        option=Option(filter=EDIIIAnswerFilter()),
                    ),
                    ScoreParaFilter(),
                ),
            },
            # 模型2：不限制职责和章节，提取as following
            {
                "name": "para_match",
                # "syllabus_regs": [R_NC],
                # "neglect_syllabus_regs": P_ED_III_NEG_CHAPTER,
                "ignore_pattern": [P_NEED_IGNORE],
                "as_follow_pattern": SplitBeforeMatch(
                    MatchMulti.compile(
                        r"nomination|appointment", r"PROCESS|Procedure|nomination\s*policy", r"[:：]", operator=all
                    ),
                    separator=P_SEN_SEPARATOR,
                    operator=any,
                ),
            },
            # 模型3：限制句式
            {
                "name": "para_match",
                # "syllabus_regs": [R_NC],
                "neglect_syllabus_regs": P_ED_III_NEG_CHAPTER,
                "paragraph_pattern": [
                    P_ED_III_COMPLY,
                    SplitBeforeMatch(
                        MatchMulti.compile(
                            r"nomination|appointment",
                            r"PROCESS|Procedure|nomination\s*policy",
                            r"include|follow",
                            operator=all,
                        ),
                        separator=P_SEN_SEPARATOR,
                        operator=any,
                    ),
                ],
                "neglect_pattern": [*R_ED_III_NEGS, P_DIVERSITY, R_ROLE_AND_FUNCTION, r"during"],
            },
            # 模型4： kmeans，用关键词过滤
            {
                "name": "kmeans_classification",
                "multi_elements": True,
                "skip_syllabus_title": True,
                "remedy_low_score_element": True,
                "filter_low_score_threshold": 0.1,
                "filter_primary_role_elements": True,
                # "syllabus_regs": [R_NC],
                "neglect_syllabus_regs": P_ED_III_NEG_CHAPTER,
                "para_pattern": [R_ED_III_KEYWORDS],
                "neglect_pattern": [*R_ED_III_NEGS, P_DIVERSITY, R_ROLE_AND_FUNCTION, r"during"],
            },
            # 模型5：限制章节，不限制职责，根据句子找关键词
            {
                "name": "yoda_layer",
                "threshold": 0.01,
                "multi_elements": True,
                "syllabus_regs": [R_NC],
                "neglect_syllabus_regs": P_ED_III_NEG_CHAPTER,
                "rule": ScoreDestFilter(
                    multi=True,
                    # pattern=MatchMulti.compile(R_ED_III_KEYWORDS, operator=any),
                    dest=SentenceFromSplitPara(
                        ordered_patterns=[
                            NeglectPattern.compile(
                                match=P_ED_III_COMPLY,
                                unmatch=MatchMulti.compile(rf"duties\s*of\s*{R_NC}\s*include", operator=any),
                            )
                        ],
                        skip_pattern=MatchMulti.compile(*R_ED_III_NEGS, operator=any),
                        multi=True,
                    ),
                ),
            },
            # 模型6：re-election章节需要包含关键词
            {
                "name": "yoda_layer",
                "multi_elements": True,
                "threshold": 0.1,
                # "syllabus_regs": [R_NC],
                "neglect_syllabus_regs": P_ED_III_NEG_CHAPTER,
                "rule": Operator.intersection(
                    Operator.any(
                        CGCascadeChapterLocator(
                            chapter_patterns=[MatchMulti.compile(*R_ED_III_OTHER_CHAPTERS, operator=any)],
                            remove_blank=True,
                            dest=ContinuedElem[Para](
                                stop=ChapterFilter(
                                    skip_pattern=MatchMulti.compile(P_CONTINUED, R_ED_III_KEYWORDS, operator=any)
                                ),
                                skip=ParaLensFilter(0, 10)
                                | ParaTextFilter(pattern=MatchMulti.compile(*R_CG_CHAPTER_TITLES, operator=any)),
                                limit=100,
                                size=2,
                                start=1,
                            ),
                        ),
                        option=Option(filter=EDIIIAnswerFilter1()),
                    ),
                    ScoreParaFilter(),
                ),
            },
        ],
    },
]
