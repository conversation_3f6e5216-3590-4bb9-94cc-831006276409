import re

from remarkable.common.constants import CGEnum
from remarkable.common.pattern import <PERSON><PERSON>ult<PERSON>, PositionPattern
from remarkable.predictor.hkex_predictor.models.yoda_layer.rule.by_pos import ParaFromPos
from remarkable.predictor.hkex_predictor.models.yoda_layer.rule.root import <PERSON>GChapterLocator, StrictMode
from remarkable.predictor.hkex_predictor.schemas.pattern import R_APPELLATION

R_SECRETARY = r"company\s*secretar(y|ies)"
P_SECRETARY_CHAPTER = re.compile(rf"^{R_SECRETARY}$", re.I)
P_RULE_3_29 = MatchMulti.compile(
    r"rule\s*3\.29",
    r"15\s*hour",
    r"train\s*requirement",
    operator=any,
)

P_IGNORE_SYLLABUS = [r"^\d+ Directors and Company Secretary"]

R_FA_COMPLY = [
    r"(principal|primary|main) (corporate )?(contact person|associate)",
    r"external service provider",
    r"not an employee of (our|the) (Company|group)",
    r"(principal|primary|main).*?(contact|person) (within|in|at|of).* (our|the) (Company|group|Director)",
]

R_FA_NA = [MatchMulti.compile(rf"\b{R_APPELLATION}", R_SECRETARY, r"no[tr]? less than", r"train", operator=all)]
R_FA_NA_WITHOUT_NAME = [MatchMulti.compile(R_SECRETARY, r"compl.*with", r"training requirement", operator=all)]
R_FA_NA_APPOINT = [
    PositionPattern.compile(
        R_APPELLATION,
        "appointed",
        R_SECRETARY,
    )
]

# https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/3681#note_441193
R_FA_OTHERS = [MatchMulti.compile(rf"\b{R_APPELLATION}", R_SECRETARY, r"responsible for.*Board process", operator=all)]
predictor_options = [
    {
        # 外聘秘书+披露联系方式--> C
        # 外聘秘书+无披露联系方式/没有披露秘书信息--> ND
        # 内聘秘书/无法判断是外聘秘书--> NA
        # 没有明确的披露涉及秘书是外聘时，秘书视为内部员工，此时不需要明确提及秘书名字和职位，即上述文档中AI预测是正确的，应判断为NA
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/3681#note_443311
        "path": ["F(a)-Primary contact person if external service provider is engaged"],
        "default_enum_value": CGEnum.ND.value,
        "models": [
            {
                "name": "score_filter",
                "threshold": 0.01,
                "multi_elements": True,
                "neglect_syllabus_regs": [
                    r"Management Discussion and Analysis",
                    r"Biographical Details of Directors and Senior Management",
                ],
                "pattern": [
                    *R_FA_COMPLY,
                    *R_FA_NA,
                    *R_FA_NA_APPOINT,
                    *R_FA_NA_WITHOUT_NAME,
                    *R_FA_OTHERS,
                    rf"joint {R_SECRETARY}",
                    rf"appointed as the (Company’s )?{R_SECRETARY}",
                    r"primary\s*contact",
                ],
            },
            {
                "name": "kmeans_classification",
                "syllabus_regs": [P_SECRETARY_CHAPTER],
                "syllabus_regs_for_answer": [R_SECRETARY, R_APPELLATION],
                "filter_low_score_threshold": 0.1,
            },
            {
                "name": "score_filter",
                "threshold": 0.7,
            },
        ],
    },
    {
        "path": ["F(b)-Details of non-compliance with rule 3.29"],
        "default_enum_value": CGEnum.ND.value,
        "location_threshold": 0.001,
        "models": [
            {
                "name": "score_filter",
                "threshold": 0.618,
            },
            {
                "name": "kmeans_classification",
                "para_pattern": P_RULE_3_29,
                "syllabus_regs": [P_SECRETARY_CHAPTER],
                "filter_low_score_threshold": 0.1,
            },
            {
                "name": "yoda_layer",
                "threshold": 1,
                "rule": CGChapterLocator(
                    min_page=5,
                    strict=StrictMode.CHAPTER,
                    pattern=P_SECRETARY_CHAPTER,
                    dest=ParaFromPos(
                        limit=5,
                        size=1,
                        pattern=P_RULE_3_29,
                    ),
                ),
            },
            {
                "name": "pick_one_element_by_gpt",
                "prompt": """你是一位精通港交所上市规则的金融专家,需要从上市公司企业管治报告中查找与规则3.29相关的信息:"Rule 3.29:在每个财政年度,发行人的公司秘书须参加不少于15小时的相关专业培训。"

具体需求:
1. 仅关注描述公司秘书本人参加培训的内容,不考虑其他人员。
2. 可能情况包括:
   a) Not Applicable - 明确提及公司秘书参加了不少于15小时培训或者遵守了3.29相关规定。
      常见关键词:company secretary, 15 hours, professional training, undertook, participated, Rule 3.29 , no less than,
                attended sufficient professional training, complied with the qualification and training requirements
   b) No Disclosure - 未披露公司秘书参加培训的相关内容。
3. 报告包含文本段落(class=PARAGRAPHS)和表格(class=TABLE为markdown形式,需转换成表格再解析)。
4. 如果报告中仅包含秘书的名称, 那么忽略这个段落。

返回格式要求:
1. JSON格式,包含字段:"index"(记录位置),"enum_value"("Not Applicable"或"No Disclosure"),"description"(答案解释)
2. 若无相关记录,返回{"index": null, "enum_value": "No Disclosure", "description": "未发现相关披露内容"}
3. 若index有值则enum_value应该是"Not Applicable"
4. 只返回一个最匹配的答案,格式为PythonDict
5. 不自行添加、总结或发挥,只提取原文相关内容

实例:
{"index": 123, "enum_value": "Not Applicable", "description": "公司秘书XXX先生於本年度参加了超过15小时的专业培训。"}

我将提供报告数据,请按要求返回符合格式的JSON答案,确保可通过json.loads解析,且不带markdown格式。

我会先发一个示例让你参考。
                """,
                "assistant": [
                    {
                        "role": "user",
                        "content": """[{"index": 242, "text": "The Company Secretary is responsible for assisting the Board and respective Board Committees in their proceedings and advising the Board on corporate governance matters. During the year ended 31 March 2023, the Company Secretary has complied with the professional training requirements under the Code Provisions.", "class": "PARAGRAPH", "syllabus": "Company Secretary"}, {"index": 21, "text": "Ms. Wong Ching CPA, ACG, ACS", "class": "PARAGRAPH", "syllabus": "Company Secretary"}]""",
                    },
                    {
                        "role": "assistant",
                        "content": '{"index": 242, "enum_value": "Not Applicable", "description": "The Company Secretary has complied with the professional training requirements under the Code Provisions during the year, which indicates that at least 15 hours of professional training were completed. Therefore, the Company Secretary\'s compliance with the training requirements makes this case Not Applicable to Rule 3.29."}',
                    },
                    {
                        "role": "user",
                        "content": """'[{"index": 469, "text": "Each of the three INEDs has made a written confirmation to the Company of his independence with reference to the criteria and guidelines as set out in Rule 3.13 of the Listing Rules. Each Director has declared to the Company of his/her interests in any material contracts or other interest in the business of the Group or in any competing business with the Group. During the year ended 31 December 2022, the Company complied with Rules 3.10(1), 3.10(2) and 3.10A of the Listing Rules which require the minimum number of INEDs and at least one of the INEDs has appropriate professional qualifications or accounting or related financial management expertise.", "class": "PARAGRAPH"}, {"index": 462, "text": "The Company has complied with all the code provisions as stipulated in the Corporate Governance Code (the \u201cCode\u201d) under Appendix 14 in the Listing Rules throughout the year ended 31 December 2022.", "class": "PARAGRAPH"}, {"index": 501, "text": "The Directors acknowledged the importance of updating their professional development and refreshing their knowledge and skills. The Company encouraged the Directors to participate in any seminar or forum organised by professional bodies, independent auditors, solicitors, chambers and business organisations as well as reading relevant articles. Below is a table in accordance with the records maintained by the Company indicating the Directors had received the following training in compliance with code provision A.6.5 of the Code during the year:", "class": "PARAGRAPH"}, {"index": 639, "text": "The Company considers that the current composition of the Board, one out of its nine members being female, is characterised by diversity, whether considered in terms of gender, cultural, professional background and skills. The current Directors have extensive experience and skills relevant to the business of the Company.", "class": "PARAGRAPH"}]'""",
                    },
                    {
                        "role": "assistant",
                        "content": '{"index": null, "enum_value": "No Disclosure", "description": "文中没有任何信息披露公司秘书参与培训的记录"}',
                    },
                ],
            },
        ],
    },
]
