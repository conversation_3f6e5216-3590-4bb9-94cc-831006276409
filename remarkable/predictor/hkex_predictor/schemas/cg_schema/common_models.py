import re
from typing import Iterable

from remarkable.common.common_pattern import (
    P_SEN_SEPARATOR,
    R_CG_CHAPTER_TITLES,
    R_CHAPTER_PREFIX,
    R_FOLLOW_PREFIX,
    R_MIDDLE_DASH,
    R_MIDDLE_DASHES,
)
from remarkable.common.constants import CGEnum
from remarkable.common.pattern import (
    MatchMulti,
    NeglectPattern,
    SplitBeforeMatch,
)
from remarkable.common.protocol import Search<PERSON>attern<PERSON>ike
from remarkable.common.util import P_CHNS
from remarkable.predictor.hkex_predictor.models.yoda_layer.rule.by_pos import Continued<PERSON>araFromPos, ParaFromPos
from remarkable.predictor.hkex_predictor.models.yoda_layer.rule.root import CGCascadeChapterLocator
from remarkable.predictor.hkex_predictor.schemas.pattern import (
    P_MEETING_SKIP,
    P_NEED_IGNORE,
    P_SKIP_FORMED,
    R_<PERSON>,
    R_ALL_COMMITTEES,
    R_AND,
    R_APPELLATION,
    R_AS_FOLLOW_END,
    R_AT<PERSON><PERSON>,
    <PERSON>_<PERSON>,
    <PERSON>_COMPOSE,
    R_<PERSON>ISCHARGED,
    R_DURING_THE_YEAR,
    R_<PERSON>,
    R_EC_BOARD,
    R_EC_CELL,
    R_EC_NEG,
    R_EC_NO_MEET_NEG,
    R_ENG_TIMES,
    R_HOLD,
    R_JURA_CG_IGNORE_CHAPTER,
    R_NC,
    R_RC,
    gene_p_committee_reviewed_sth,
    gene_p_reviewed_sth,
)
from remarkable.predictor.models.base_model import R_PRIMARY_ROLE
from remarkable.predictor.models.para_match import AsFollowType

P_EB_BOARD_CHAPTER = MatchMulti.compile(
    MatchMulti.compile(r"board", rf"{R_COMPOSE}|director|committ?e", operator=all),
    rf"{R_CHAPTER_PREFIX}the\s*board$",
    r"^DIRECTORS?$",
    # http://************:55647/#/project/remark/231463?treeId=43974&fileId=66199&schemaId=28&projectId=28&schemaKey=AC-E(b)
    MatchMulti.compile(r"chair", r"executive", operator=all),
    operator=any,
)
R_CHAIR_MAN = r"((co-)?cha\s*ir((wo)?man|lady|person|ed)|lead\s*director)"
R_CHAIR = rf"\b(co-)?cha\s*ir|{R_CHAIR_MAN}"
R_EXECUTIVE = (
    r"(independent\s*)?(non-)?executive\s*d\s*irector|independent\s*d\s*irector|\bI?N?EDs?\b|G?CEO\b|chief\s*executive"
)
R_EXECUTIVE_END = r"(\bG?CEO|Directors?|executives?|\bI?N?EDs?\b|executive\s*Officer|chair(woman|man|lady|person)|members?)[)）]?($|[：:])"
R_EB_AS_FOLLOW_END = rf"([{R_MIDDLE_DASHES}:：,，]|(directors|below|following)\.)$"
# 人名
P_NAME = MatchMulti.compile(
    re.compile(R_APPELLATION),
    rf"namely|\bas\s*(an?\s*|the\s*)?({R_EXECUTIVE}|chair)",
    re.compile(r"[A-Z][-a-zA-Z.]*(\s+[A-Z][*^-a-zA-Z,.\d]*)+(?<!Committee)(\s*[（(][^)）]+[)）])?(\t|$)"),
    # re.compile(r"((?<!\.)\s[A-Z][-a-zA-Z.]+(?<!\.)\s+[A-Z][-a-zA-Z,.]+)(?<!\.)(?<!Committee)$"),
    re.compile(
        r"(?<!\.)\s[A-Z][-a-zA-Z.]+(?<!\.)\s+(?!(Committ?ee?|Rules?\b|List|Code|Director|(Non-)?Executive|Officer|Provision|Chair))[A-Z][-a-zA-Z,.]+(?<!\.)\s"
    ),
    operator=any,
)
P_NAME_ONE_LINE = re.compile(r"^([A-Z][A-Za-z.,，-]+\s*)+([(（][^)(（）]+[)）])?$|^[(（][^)(（）]+[)）]$")
P_EB_COMPLY = MatchMulti.compile(R_CHAIR, R_EXECUTIVE, P_NAME, operator=all)
P_EB_PARA = MatchMulti.compile(R_COMPOSE, R_CHAIR, R_EXECUTIVE, P_NAME, operator=all)
R_NEG_BELOW = [
    *P_NEED_IGNORE.patterns,
    rf"{R_CHAPTER_PREFIX}{R_AC}$",
    rf"{R_CHAPTER_PREFIX}{R_NC}$",
    rf"{R_CHAPTER_PREFIX}{R_CC}$",
    rf"{R_CHAPTER_PREFIX}{R_RC}$",
    rf"{R_CHAPTER_PREFIX}{R_EC}$",
    rf"{R_CHAPTER_PREFIX}strategy\s*committee$",
    r"Notes[:：]$",
    r"\bof\s*(the\s*)?director",
    r"^composition$",
    r"\.$",
    P_CHNS.pattern,
]
R_EB_FOLLOW_IGNORE_CHAPTER = (
    r"((executive|independent|^)?\s*directors?|\bI?N?EDs?\b|\bchair((wo)?man|lady|person))[:：]?$"
)
RP_EB_AS_FOLLOW_START = [
    r"(Chair((wo)?man|lady|person)|\bG?CEO|Directors?|executives?|\bI?N?EDs?\b)[)）：:]?($|\t)",
    rf"^{R_FOLLOW_PREFIX}?{R_APPELLATION}",
    r"^[(（](appoint|resign|reappoint|cease|retire)",
    MatchMulti.compile(
        rf"^{R_FOLLOW_PREFIX}?(Executive|Non-executive|Chair|Member|Independent|\bI?N?EDs?\b|G?CEO)",
        operator=any,
        flag=0,
    ),
    SplitBeforeMatch(
        re.compile(rf"^[A-Z][a-zA-Z.,，]+|{R_MIDDLE_DASH}|[)(（）A-Z]|•"),
        separator=re.compile(r"\s+"),
        operator=all,
    ),
    # # http://************:55647/#/project/remark/245327?treeId=6020&fileId=66289&schemaId=28&projectId=17&schemaKey=AC-E(b)
    # rf"{R_BE}\s*(INEDs|(Independent\s*)?(Non-)?executive\s*director)",
    # http://************:55647/#/project/remark/244806?treeId=38046&fileId=66810&schemaId=28&projectId=17&schemaKey=B(a)
    r"[^(（]+[)）]$",
    # http://************:55647/#/project/remark/244497?treeId=13590&fileId=67119&schemaId=28&projectId=17&schemaKey=B(a)
    r"((member|director)\s*of|^the)\s*[a-zA-Z,，\s]+?Committ?ee?[,，]?$",
]


def get_root_chapter_config(
    r_extra_chapter: Iterable[str | SearchPatternLike] = (), syllabus_regs=None, neg_syllabus_regs=None
):
    configs = {
        "parent_features": [*R_CG_CHAPTER_TITLES, *r_extra_chapter],
        "neglect_parent_features": R_JURA_CG_IGNORE_CHAPTER,
        "parent_must_be_root": True,
        "page_first_as_parent_syllabus": True,
    }
    if syllabus_regs:
        configs["syllabus_regs"] = syllabus_regs
    if neg_syllabus_regs:
        configs["neglect_syllabus_regs"] = neg_syllabus_regs
    return configs


def get_eb_as_follow_config(
    r_committee=None, any_below_pattern=None, extra_as_follows=(), extra_neg_belows=(), extra_conf=None
):
    if r_committee:
        p_as_follow = MatchMulti.compile(r_committee, R_COMPOSE, R_EB_AS_FOLLOW_END, operator=all)
    else:
        p_as_follow = MatchMulti.compile(
            MatchMulti.compile(R_COMPOSE, R_EB_AS_FOLLOW_END, operator=all),
            R_EXECUTIVE_END,
            *extra_as_follows,
            operator=any,
        )

    configs = {
        "as_follow_pattern": p_as_follow,
        "as_follow_type": AsFollowType.ANY,
        "ignore_syllabus_pattern": R_EB_FOLLOW_IGNORE_CHAPTER,
        "special_as_follow_start": RP_EB_AS_FOLLOW_START,
        "below_pattern": RP_EB_AS_FOLLOW_START,
        "neglect_below_pattern": [R_NEG_BELOW, *extra_neg_belows],
        "as_follow_near_offset": -1,
    }
    if any_below_pattern:
        configs["any_below_pattern"] = any_below_pattern
    if extra_conf:
        configs.update(extra_conf)
    return configs


def get_eb_models(r_committee, skip_answer_value=None, add_score_model=True, add_yoda_model=False):
    """
    r_committee: 各委员会的正则
    1. 最优先位置，各个committee章节下下披露的委员会成员及其类型
    2. 次优位置，在1没有披露的情况下，提取各个committee标题下披露的委员会成员开会参与情况，但表格中必须列出chairman和个人员的董事类型
    3. 再次优位置，在1和2均没有披露的情况下，在board章节下，按照1、2的优先级提取，但必须列出是对应的committee
    4. 以上若提取不到，则先在committee章节下找chairman相关语句，再在board章节找描述其他人员类型的表格或者段落
    5. 最后，先在committee章节下找executive director相关语句，再在board章节找描述对应committee的chairman的表格
    结果要求： 必须列出所有成员名称、及xx委员会主席、及每位成员所属的董事类型，否则不取
    """
    p_other_committees = NeglectPattern.compile(
        match=MatchMulti.compile(*R_ALL_COMMITTEES, operator=any), unmatch=r_committee
    )
    skip_answer_value = skip_answer_value or CGEnum.ND.value
    models = [
        # 1. 找as follow内容，限制章节
        {
            "name": "para_match",
            "skip_answer_value": skip_answer_value,
            "multi_elements": True,
            "enum_from_multi_element": True,
            **get_root_chapter_config([r_committee]),
            "syllabus_regs": r_committee,
            # 句号结尾： http://************:55647/#/project/remark/244956?treeId=12776&fileId=66660&schemaId=28&projectId=17&schemaKey=AC-E(b)
            **get_eb_as_follow_config(),
            "paragraph_pattern": P_EB_PARA,
            "neglect_pattern": p_other_committees,
        },
        # 2. 找as follow内容，限制committee关键词，限制CG章节
        {
            "name": "para_match",
            "skip_answer_value": skip_answer_value,
            "enum_from_multi_element": True,
            **get_root_chapter_config([r_committee]),
            **get_eb_as_follow_config(r_committee),
            # http://************:55647/#/project/remark/245336?treeId=13386&fileId=66280&schemaId=28&projectId=17&schemaKey=AC-E(b)
            "paragraph_pattern": MatchMulti.compile(r_committee, P_EB_PARA, operator=all),
            "neglect_pattern": p_other_committees,
        },
        # http://************:55647/#/project/remark/244431?treeId=12780&fileId=67185&schemaId=28&projectId=17&schemaKey=AC-E(b)
        # http://************:55647/#/project/remark/245156?treeId=10192&fileId=66460&schemaId=28&projectId=17&schemaKey=AC-E(b)
        # 3. 找committee下的表格，包含了chairman，ineds及人名
        {
            "name": "special_cells",
            "skip_answer_value": skip_answer_value,
            "syllabus_regs": r_committee,
            "title_patterns": r_committee,
            "whole_table": True,
            "col_header_pattern": r"member|director|name|description",
            "enum_pattern": P_EB_COMPLY,
        },
        # 4. 在committee章节下，先找描述：INEDs，再找包含chairman的表格
        # http://************:55647/#/project/remark/245212?treeId=4927&fileId=66404&schemaId=28&projectId=17&schemaKey=AC-E(b)
        # http://************:55647/#/project/remark/244849?treeId=4572&fileId=66767&schemaId=28&projectId=17&schemaKey=AC-E(b)
        # http://************:55647/#/project/remark/245126?treeId=22486&fileId=66490&schemaId=28&projectId=17&schemaKey=AC-E(b)
        {
            "name": "multi_models",
            "operator": "all",
            "skip_answer_value": skip_answer_value,
            "models": [
                # 先找描述：由xx组成
                {
                    "name": "para_match",
                    **get_root_chapter_config([r_committee]),
                    "syllabus_regs": r_committee,
                    "paragraph_pattern": MatchMulti.compile(R_COMPOSE, R_EXECUTIVE, operator=all),
                },
                # 再找表格，表格中有chairman
                {
                    "name": "special_cells",
                    **get_root_chapter_config(),
                    "syllabus_regs": r_committee,
                    "title_patterns": r_committee,
                    "whole_table": True,
                    "cell_pattern": R_CHAIR,
                    "enum_pattern": MatchMulti.compile(P_NAME, R_CHAIR, operator=all),
                },
            ],
        },
        # 5. 找board下的表格
        # http://************:55647/#/project/remark/245384?treeId=4429&fileId=66232&schemaId=28&projectId=17&schemaKey=AC-E(b)
        {
            "name": "special_cells",
            "skip_answer_value": skip_answer_value,
            **get_root_chapter_config(),
            "syllabus_regs": P_EB_BOARD_CHAPTER,
            "neglect_syllabus_regs": [R_AC, R_NC, R_RC, R_EC, R_CC],
            "whole_table": True,
            "title_patterns": R_COMPOSE,
            "neglect_title_patterns": r"\bmet\b|attend|allow",
            "col_header_pattern": r_committee,
            "enum_pattern": MatchMulti.compile(r_committee, R_CHAIR, R_EXECUTIVE, P_NAME, operator=all),
        },
        # 6. 找出board的出席记录表格，同时包含committee + committee的chairman信息，但chairman对应的行必须有数字
        # http://************:55647/#/project/remark/244571?treeId=16619&fileId=67045&schemaId=28&projectId=17&schemaKey=AC-E(b)
        {
            "name": "special_cells",
            "skip_answer_value": skip_answer_value,
            "syllabus_regs": P_EB_BOARD_CHAPTER,
            "neglect_syllabus_regs": [R_AC, R_NC, R_RC, R_EC, R_CC],
            "whole_table": True,
            "title_patterns": r"meeting|\bmet\b|attend",
            "col_header_pattern": r_committee,
            "row_pattern": rf"{R_CHAIR}|[1-9]\d*/[1-9]\d*\s*[（(]C[)）]",
            "cell_pattern": r"[1-9]\d*/[1-9]\d*",
        },
        # 7. 先找描述：chairman是xxx, 再找board下的语句或as follow
        # http://************:55647/#/project/remark/245383?treeId=38129&fileId=66233&schemaId=28&projectId=17&schemaKey=AC-E%28b%29
        # http://************:55647/#/project/remark/245380?treeId=4265&fileId=66236&schemaId=28&projectId=17&schemaKey=AC-E(b)
        {
            "name": "multi_models",
            "operator": "all",
            "skip_answer_value": skip_answer_value,
            "models": [
                # 先找描述：chairman是xxx
                {
                    "name": "para_match",
                    **get_root_chapter_config([r_committee]),
                    "syllabus_regs": r_committee,
                    "paragraph_pattern": MatchMulti.compile(r_committee, R_CHAIR, P_NAME, operator=all),
                    **get_eb_as_follow_config(),
                },
                # 再找board章节下的句子或as follow
                {
                    "name": "para_match",
                    "multi_elements": True,
                    "enum_from_multi_element": True,
                    **get_root_chapter_config(),
                    "syllabus_regs": P_EB_BOARD_CHAPTER,
                    "neglect_syllabus_regs": [R_AC, R_NC, R_RC, R_EC, R_CC],
                    "paragraph_pattern": MatchMulti.compile(R_COMPOSE, R_EXECUTIVE, P_NAME, operator=all),
                    **get_eb_as_follow_config(
                        extra_neg_belows=[r"attend|record"],
                    ),
                },
            ],
        },
        # 8. 先找描述：chairman是xxx, 再找committee下的表格
        # http://************:55647/#/project/remark/245129?treeId=10935&fileId=66487&schemaId=28&projectId=17&schemaKey=AC-E(b)
        {
            "name": "multi_models",
            "operator": "all",
            "skip_answer_value": skip_answer_value,
            "models": [
                # 先找描述：chairman是xxx
                {
                    "name": "para_match",
                    **get_root_chapter_config(),
                    "syllabus_regs": r_committee,
                    "paragraph_pattern": MatchMulti.compile(R_CHAIR, P_NAME, operator=all),
                },
                # 再找表格，表格中有executive
                {
                    "name": "special_cells",
                    **get_root_chapter_config(),
                    "syllabus_regs": P_EB_BOARD_CHAPTER,
                    "neglect_syllabus_regs": [R_AC, R_NC, R_RC, R_EC, R_CC],
                    "whole_table": True,
                    "cell_pattern": R_EXECUTIVE,
                    "enum_pattern": MatchMulti.compile(P_NAME, R_EXECUTIVE, operator=all),
                },
            ],
        },
        # 9. 先找committee包含chairman的表格，再找board信息
        # http://************:55647/#/project/remark/245386?treeId=37798&fileId=66230&schemaId=28&projectId=17&schemaKey=AC-E(b)
        {
            "name": "multi_models",
            "operator": "all",
            "skip_answer_value": skip_answer_value,
            "models": [
                # 先找committee表格：chairman
                {
                    "name": "special_cells",
                    **get_root_chapter_config([r_committee]),
                    "syllabus_regs": r_committee,
                    # "title_patterns": r_committee,
                    "col_header_pattern": r"member|director|name",
                    "enum_pattern": MatchMulti.compile(P_NAME, R_CHAIR, operator=all),
                },
                # 再找board章节as follow
                {
                    "name": "para_match",
                    "multi_elements": True,
                    "enum_from_multi_element": True,
                    **get_root_chapter_config(),
                    "syllabus_regs": P_EB_BOARD_CHAPTER,
                    "neglect_syllabus_regs": [R_AC, R_NC, R_RC, R_EC, R_CC],
                    "paragraph_pattern": MatchMulti.compile(R_COMPOSE, R_EXECUTIVE, P_NAME, operator=all),
                    **get_eb_as_follow_config(
                        extra_neg_belows=[r"attend|record"],
                    ),
                },
            ],
        },
        # 10. 找board章节下的as follow，至少有一条包含committee关键词
        # http://************:55647/#/project/remark/244910?treeId=6044&fileId=66706&schemaId=28&projectId=6044&schemaKey=AC-E(b)
        {
            "name": "para_match",
            "skip_answer_value": skip_answer_value,
            "multi_elements": True,
            "enum_from_multi_element": True,
            **get_root_chapter_config(),
            "syllabus_regs": P_EB_BOARD_CHAPTER,
            "neglect_syllabus_regs": [R_AC, R_NC, R_RC, R_EC, R_CC],
            **get_eb_as_follow_config(any_below_pattern=r_committee),
        },
    ]
    if add_yoda_model:
        # 11. yoda_layer 定位章节找
        models.append(get_eb_yoda_layer(r_committee, skip_answer_value))
    if add_score_model:
        # 12. 分值过滤， 0.4分以上都取
        models.append(
            {
                "name": "score_filter",
                "skip_answer_value": skip_answer_value,
                "multi_elements": True,
                "threshold": 0.4,
            }
        )
    return models


def get_eb_yoda_layer(r_committee, skip_answer_value=None):
    return {
        "name": "yoda_layer",
        "skip_answer_value": skip_answer_value or CGEnum.ND.value,
        "use_model_answer": False,
        "multi_elements": True,
        "rule": CGCascadeChapterLocator(
            chapter_patterns=[re.compile(r_committee, re.I)],
            strict=True,
            dest=ParaFromPos(
                limit=20,
                size=3,
                skip_pattern=MatchMulti.compile(r"^[^\s]+$", R_PRIMARY_ROLE, operator=any),
                pattern=MatchMulti.compile(
                    MatchMulti.compile(R_COMPOSE, rf"{R_CHAIR}|director", operator=all), R_EXECUTIVE_END, operator=any
                ),
                only_dest_answers=False,
                dest=ContinuedParaFromPos(
                    skip=MatchMulti.compile(*R_NEG_BELOW, operator=any),
                    pattern=NeglectPattern.compile(match=P_NAME_ONE_LINE, unmatch=R_EXECUTIVE_END),
                    limit=10,
                ),
            ),
        ),
    }


def get_p_no_meetings(r_committee):
    return SplitBeforeMatch(
        MatchMulti.compile(
            r_committee,
            MatchMulti.compile(
                r"\bno\s*meeting",
                rf"\bnot\s+{R_HOLD}\s*any\s*meeting",
                rf"\bno\s+{r_committee}(['‘’]s)?\s*meeting",
                operator=any,
            ),
            operator=all,
        ),
        separator=P_SEN_SEPARATOR,
    )


def get_r_cell_committee(r_committee, r_keyword):
    # 结尾不带committee的特例：http://************:55647/#/project/remark/244570?treeId=10104&fileId=67046&schemaId=28&projectId=17&schemaKey=NC-E(c)
    return [
        r_committee,
        rf"^{r_keyword}\s*({R_AND}([a-z]+\s*){{1,2}})?$",
        rf"^{r_keyword}\s*([a-z]+\s*){{1,2}}committ?ee?",
    ]


def get_all_attend_model(r_committee):
    """
    模型：委员会召开了N次会议，且全员或xx与会的句子
    """
    return {
        "name": "para_match",
        "paragraph_pattern": MatchMulti.compile(
            r_committee,
            R_ATTEND,
            rf"\b(held|convened).*?meeting|meeting.*?(held|convened)|bmet\s*{R_ENG_TIMES}",
            r"\ball\b",
            operator=all,
        ),
    }


def get_ec_models(r_committee, r_keyword):
    """
    针对以下规则，统一抽出一套模型
    AC-E(c)-Number of meetings and record of attendance
    RC-E(c)-Number of meetings and record of attendance
    NC-E(c)-Number of meetings and record of attendance
    提取规则：
    1. 优先提取表格：委员会的会议出席记录，枚举值为Comply
    2. 提取句子描述：委员会由xx组成，当年举行了n次会议，全员参加，枚举值为Comply
    3. 提取句子描述：没有举行过会议，枚举值为N/A
    """
    r_header_pattern = get_r_cell_committee(r_committee, r_keyword)
    return [
        # 模型1：遍历初步预测中的表格，表名中必须包含委员会
        {
            "name": "special_cells",
            "model_id": "table_comm_1",  # xx委员会独立的表格，get_eb_models()中引用
            "title_patterns": MatchMulti.compile(r_committee, R_ATTEND, operator=all),
            "neglect_title_patterns": R_EC_NEG,
            "whole_table": True,
            "cell_pattern": R_EC_CELL,
        },
        # 模型2：遍历初步预测中提及董事出席的表格，单元格包含委员会，但次数都是0/0或者NA/NA
        {
            "name": "special_cells",
            "enum": CGEnum.NA.value,
            "title_patterns": MatchMulti.compile(R_EC_BOARD, R_ATTEND, operator=all),
            "neglect_title_patterns": R_EC_NEG,
            "whole_table": True,
            "without_col_header": True,
            "col_header_pattern": r_header_pattern,
            "col_pattern": rf"^[{R_MIDDLE_DASHES}0N/A\t不適用无無]+$",
        },
        # 模型3：遍历初步预测中提及董事出席的表格，单元格必须包含委员会
        {
            "name": "special_cells",
            "title_patterns": MatchMulti.compile(R_EC_BOARD, R_ATTEND, operator=all),
            "neglect_title_patterns": R_EC_NEG,
            "whole_table": True,
            "cell_pattern": r_header_pattern,
        },
        # 模型4：初步预测答案中的表格，内容满足条件，章节为委员会的直接获取
        {
            "name": "special_cells",
            "model_id": "table_comm_2",  # xx委员会独立的表格，get_eb_models()中引用
            "syllabus_regs": r_committee,
            "whole_table": True,
            "cell_pattern": R_EC_CELL,
        },
        # 模型5：分值+表名关键词过滤
        {
            "name": "special_cells",
            "model_id": "table_comm_3",  # xx委员会独立的表格，get_eb_models()中引用
            "threshold": 0.618,
            "whole_table": True,
            "title_patterns": SplitBeforeMatch.compile(
                MatchMulti.compile(
                    r_committee, rf"meeting|會議|\bmet\s*{R_ENG_TIMES}", r"attend|出席", r"[:：]$", operator=all
                ),
                operator=any,
                separator=P_SEN_SEPARATOR,
            ),
            "neglect_title_patterns": R_EC_NEG,
        },
        # 模型6：依据初步预测中的句子，找出句子下方的表格，表名必须包含委员会关键词
        {
            "name": "para_match",
            "model_id": "table_comm_4",  # xx委员会独立的表格，get_eb_models()中引用
            "as_follow_type": AsFollowType.TABLE_NO_TITLE,
            "multi_elements": True,
            "as_follow_pattern": MatchMulti.compile(r_committee, R_ATTEND, operator=all),
            "below_pattern": R_EC_CELL,
            "neglect_pattern": R_EC_NEG,
        },
        # 模型7：依据初步预测中的句子，找出句子下方的表格，表格中必须包含委员会关键词
        {
            "name": "para_match",
            "as_follow_type": AsFollowType.TABLE_NO_TITLE,
            "multi_elements": True,
            "as_follow_pattern": R_ATTEND,
            "below_pattern": r_header_pattern,
            "neglect_pattern": R_EC_NEG,
        },
        # 模型8：分值+表格关键词过滤
        # http://************:55647/#/project/remark/244309?treeId=8265&fileId=67307&schemaId=28&projectId=17&schemaKey=CG-E(c)
        {
            "name": "score_filter",
            "threshold": 0.618,
            "aim_types": ["TABLE"],
            "pattern": [R_EC_CELL, r_header_pattern],
        },
        # 模型9：依据初步预测中的句子，找出句子下方的表格，表名必须包含委员会关键词，不限制单元格，限制分值
        {
            "name": "para_match",
            "model_id": "table_comm_5",  # xx委员会独立的表格，get_eb_models()中引用
            "threshold": 0.618,
            "as_follow_type": AsFollowType.TABLE_NO_TITLE,
            "ignore_pattern": [P_NEED_IGNORE],
            "as_follow_pattern": MatchMulti.compile(r_committee, R_ATTEND, operator=all),
            "neglect_pattern": R_EC_NEG,
        },
        # 模型10：遍历所有表格，表名的前一句必须包含委员会关键词 https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/3537#note_429844
        {
            "name": "special_cells",
            "force_use_all_elements": True,
            "whole_table": True,
            "title_patterns": MatchMulti.compile(
                rf"meeting|會議|\bmet\s*{R_ENG_TIMES}", r"attend|出席", r"[:：]$", operator=all
            ),
            "neglect_title_patterns": R_EC_NEG,
            "title_above_pattern": r_committee,
            "cell_pattern": R_EC_CELL,
        },
        # 模型11：遍历所有表格，表名的前一句包含attend，表格里包含委员会关键词
        # http://************:55647/#/project/remark/245099?treeId=8711&fileId=66517&schemaId=28&projectId=17&schemaKey=NC-E(c)
        {
            "name": "special_cells",
            "threshold": 0.1,
            "force_use_all_elements": True,
            "whole_table": True,
            "neglect_title_patterns": R_EC_NEG,
            "title_above_pattern": R_ATTEND,
            "cell_pattern": r_header_pattern,
        },
        # 模型12：no meetings
        {
            "name": "para_match",
            "enum": CGEnum.NA.value,
            "paragraph_pattern": get_p_no_meetings(r_committee),
            "neglect_pattern": R_EC_NO_MEET_NEG,
        },
        # 模型13：描述委员会召开了N次会议，且全员参与的句子
        get_all_attend_model(r_committee),
    ]


def get_work_done(r_committee, r_work_keyword, neg_syllabus_regs=()):
    """
    r_committee: 主语，例如董事会、薪酬委员会、审计委员会等
    r_work_keyword: 必须匹配的关键词，表明规则规定的当年的工作内容
    neg_syllabus_regs: 排除章节
    committee_is_root: 是否考虑committee为一级目录的场景
    """
    neglect_patterns = [
        P_MEETING_SKIP,
        P_SKIP_FORMED,
        rf"(below|following)\s*(is|are)\s*the\s*({R_PRIMARY_ROLE})",
        # [FILE 66567](http://************:55647/#/project/remark/245049?treeId=8733&fileId=66567&schemaId=28&projectId=17&schemaKey=E(d)(iv)-2)
        rf"({R_PRIMARY_ROLE})[a-z\s]+of\s*[a-z\s]+committ?ee?\s*are[:：]",
        rf"^the\s*({R_PRIMARY_ROLE})(of\s*[a-z\s]+committ?ee?)?\s*(are|include)",
    ]
    neg_syllabus_regs = [
        *neg_syllabus_regs,
        rf"{R_CHAPTER_PREFIX}(functions?\s*|roles?\s*|dut(y|ies)\s*|responsibilit(y|ies)\s*|and\s*)+$",
    ]
    return [
        # 1. 找包含review+internal audit的表格，限制行名
        # http://************:55647/#/project/remark/?treeId=6658&fileId=67364&schemaId=28&projectId=17&schemaKey=E(d)(i)-2
        {
            "name": "special_cells",
            **get_root_chapter_config([r_committee], neg_syllabus_regs=neg_syllabus_regs),
            "multi": True,
            "row_col_relation": "or",
            "row_header_pattern": rf"work|{R_DURING_THE_YEAR}|during\s*the\s*meeting",
            "col_header_pattern": r"meetings?\s*content",
            "cell_pattern": r_committee,
            "enum_pattern": r_work_keyword,
        },
        # 2. 找包含review+internal audit的表格，限制列名
        # http://************:55647/#/project/remark/244328?treeId=4269&fileId=67288&schemaId=28&projectId=17&schemaKey=E(d)(i)-2
        # http://************:55647/#/project/remark/244323?treeId=8837&fileId=67293&schemaId=28&projectId=17&schemaKey=E(d)(i)-2
        {
            "name": "special_cells",
            "multi": True,
            "neglect_parent_features": R_JURA_CG_IGNORE_CHAPTER,
            "page_first_as_parent_syllabus": True,
            "neglect_syllabus_regs": neg_syllabus_regs or [],
            "title_patterns": r_committee,
            "row_col_relation": "or",
            "row_header_pattern": rf"work|{R_DURING_THE_YEAR}|during\s*the\s*meeting",
            "col_header_pattern": r"meetings?\s*content",
            "enum_pattern": r_work_keyword,
        },
        # 3. 找committee章节下，表格中包含当年工作内容的单元格
        # [FILE 66837](http://************:55647/#/project/remark/244779?treeId=7424&fileId=66837&schemaId=28&projectId=17&schemaKey=E(d)(i)-1)
        {
            "name": "special_cells",
            "multi": True,
            "neglect_parent_features": R_JURA_CG_IGNORE_CHAPTER,
            "page_first_as_parent_syllabus": True,
            "syllabus_regs": r_committee,
            "neglect_syllabus_regs": neg_syllabus_regs,
            "cell_pattern": MatchMulti.compile(rf"work|{R_DISCHARGED}", R_DURING_THE_YEAR, operator=all),
            "enum_pattern": r_work_keyword,
        },
        # 4. 限制一级章节CG+非一级章节committee: 找段落
        {
            "name": "para_match",
            "threshold": 0.01,
            "multi_elements": True,
            "neglect_parent_features": R_JURA_CG_IGNORE_CHAPTER,
            "page_first_as_parent_syllabus": True,
            "syllabus_regs": r_committee,
            "neglect_syllabus_regs": neg_syllabus_regs,
            "paragraph_pattern": gene_p_reviewed_sth(r_work_keyword),
            # 小序号开头的，包含在了as_follow_pattern的结果里，这里需要排除
            "neglect_pattern": [*neglect_patterns, R_FOLLOW_PREFIX],
        },
        # 5. 限制章节committee: 找as follow
        {
            "name": "para_match",
            "threshold": 0.01,
            "multi_elements": True,
            "neglect_parent_features": R_JURA_CG_IGNORE_CHAPTER,
            "page_first_as_parent_syllabus": True,
            "syllabus_regs": r_committee,
            "neglect_syllabus_regs": neg_syllabus_regs,
            "as_follow_pattern": MatchMulti.compile(
                rf"(has|have|had)[:：]|meeting|\bmet\b|{R_DISCHARGED}|{R_DURING_THE_YEAR}|during\s*the\s*meeting",
                # # https://jura-uat.paodingai.com/#/hkex/cg-report-checking/report-review/251205?fileId=83553&schemaId=28&rule=E%28d%29%28i%29-2-Effectiveness%20of%20internal%20audit%20function&delist=0
                R_AS_FOLLOW_END,
                operator=all,
            ),
            "below_pattern": r_work_keyword,
            # 小序号开头的，包含在了as_follow_pattern的结果里，这里需要排除
            "neglect_pattern": neglect_patterns,
            "as_follow_near_offset": -3,
        },
        # 6. 限制一级章节为CG，找r_committee + 段落
        {
            "name": "para_match",
            "threshold": 0.01,
            "multi_elements": True,
            **get_root_chapter_config(neg_syllabus_regs=neg_syllabus_regs),
            "paragraph_pattern": gene_p_committee_reviewed_sth(r_committee, r_work_keyword),
            # 小序号开头的，包含在了as_follow_pattern的结果里，这里需要排除
            "neglect_pattern": [*neglect_patterns, R_FOLLOW_PREFIX],
        },
        # 7. 限制一级章节为CG，找r_committee + as follow
        {
            "name": "para_match",
            "threshold": 0.01,
            "multi_elements": True,
            **get_root_chapter_config(neg_syllabus_regs=neg_syllabus_regs),
            "as_follow_pattern": MatchMulti.compile(
                r_committee,
                rf"(has|have|had)[:：]|meeting|\bmet\b|{R_DISCHARGED}|{R_DURING_THE_YEAR}|during\s*the\s*meeting",
                R_AS_FOLLOW_END,
                operator=all,
            ),
            "below_pattern": r_work_keyword,
            "neglect_pattern": neglect_patterns,
            "as_follow_near_offset": -3,
        },
    ]
