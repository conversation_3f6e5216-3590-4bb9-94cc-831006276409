import re

from interdoc.element import Para

from remarkable.common.common_pattern import P_PERIOD_SEPARATOR, P_SEN_SEPARATOR
from remarkable.common.constants import <PERSON><PERSON><PERSON>ue<PERSON>num, CGEnum
from remarkable.common.pattern import (
    MatchMulti,
    NeglectPattern,
    PositionPattern,
    SplitBeforeMatch,
)
from remarkable.common.util import clean_txt
from remarkable.pdfinsight.interdoc_reader import <PERSON>doc<PERSON><PERSON><PERSON>
from remarkable.predictor.hkex_predictor.models.yoda_layer.base import ElementLocator
from remarkable.predictor.hkex_predictor.models.yoda_layer.filter import (
    ChapterFilter,
    ParaLensFilter,
    ParaTextFilter,
)
from remarkable.predictor.hkex_predictor.models.yoda_layer.form import Answer, ParaAsPos
from remarkable.predictor.hkex_predictor.models.yoda_layer.operator import Operator
from remarkable.predictor.hkex_predictor.models.yoda_layer.rule.by_pos import (
    ParaFromPos,
    SentenceFromSplitPara,
)
from remarkable.predictor.hkex_predictor.models.yoda_layer.rule.root import CGCascadeChapterLocator
from remarkable.predictor.hkex_predictor.schemas.cg_schema.cg_h import R_NO_INTERNAL_AUDIT
from remarkable.predictor.hkex_predictor.schemas.cg_schema.common_models import (
    get_all_attend_model,
    get_eb_models,
    get_p_no_meetings,
    get_root_chapter_config,
)
from remarkable.predictor.hkex_predictor.schemas.pattern import (
    P_CHAPTER_RISK,
    P_MEETING_SKIP,
    P_NEED_IGNORE,
    P_SKIP_FORMED,
    R_AC,
    R_AS_FOLLOW_END,
    R_ATTEND,
    R_COMPOSE,
    R_DISCHARGED,
    R_DURING_THE_YEAR,
    R_DUTIES,
    R_EC_BOARD,
    R_EC_CELL,
    R_EC_NEG,
    R_EC_NO_MEET_NEG,
    R_ENG_TIMES,
    R_EXIST_SPACE,
    R_INTERNAL_AUDIT,
    R_REVIEWED,
    R_RIC,
    R_RISK_CONTROL,
    R_RMC,
    R_SPACE,
    gene_p_reviewed_sth,
)
from remarkable.predictor.models.base_model import R_PRIMARY_ROLE

P_RIC_NO_MEETING = get_p_no_meetings(R_RIC)
R_DISCHARGED_MEETING = rf"{R_DISCHARGED}|meeting|\bmet\s*{R_ENG_TIMES}\b"
P_NOT_BOARD = re.compile(
    r"senior|other(?!\s*member)|(?<!chief )executive(?!\s*(director|committee))|department\s*head", re.I
)
P_RMC_NOT_BOARD = SplitBeforeMatch(MatchMulti.compile(R_RMC, P_NOT_BOARD, operator=all), separator=P_SEN_SEPARATOR)
P_ED_IV_2_COMPLY = SplitBeforeMatch(
    NeglectPattern.compile(match=PositionPattern.compile(R_REVIEWED, R_INTERNAL_AUDIT), unmatch="annually"),
    separator=P_SEN_SEPARATOR,
)
# P_ED_IV_2_DISCHARGED = SplitBeforeMatch(
#     MatchMulti.compile(
#         rf"{R_DURING_THE_YEAR}|held|convened|effective|adequa", R_DISCHARGED_MEETING, R_INTERNAL_AUDIT, operator=all
#     ),
#     separator=P_PERIOD_SEPARATOR,
# )


def rmc_are_not_all_board(interdoc: InterdocReader, answer_head: Answer, answer_follow: Answer):
    if not answer_follow:
        return Answer()
    for sentence in answer_follow.sentences:
        sentence_str = interdoc.find_by_index(sentence.para_index).text[sentence.start : sentence.end]
        if P_NOT_BOARD.search(sentence_str):
            return Answer()
    for para_idx in answer_follow.para_indices:
        para_text = clean_txt(interdoc.find_by_index(para_idx).text)
        if P_NOT_BOARD.search(para_text):
            return Answer()
    return answer_follow


def rmc_is_ric(enum_value=None, with_elem_box=True):
    """
    risk management相关章节下，提到RMC且全部由director组成，则认为RMC=RIC，否则不是
    """
    p_rmc_compose = [
        # http://************:55647/#/project/remark/245393?treeId=9863&fileId=66223&schemaId=28&projectId=17&schemaKey=E(d)(iv)-2
        MatchMulti.compile(R_RMC, r"executive\s*committee", r"delegat|acting\s*as|performed", operator=all),
        MatchMulti.compile(R_RMC, R_COMPOSE, r"chief|chair|director|board|\bg?ceo\b|\bI?N?EDs?\b", operator=all),
    ]
    model = {
        "name": "multi_models",
        "operator": "any",
        "enum_value": enum_value,
        "with_element_box": with_elem_box,
        "models": [
            # 找RMC全部由董事组成的句子
            {
                "name": "yoda_layer",
                "use_model_answer": False,
                "rule": Operator.any(
                    *[
                        CGCascadeChapterLocator(
                            chapter_patterns=[MatchMulti.compile(title, operator=any)],
                            strict=True,
                            dest=ElementLocator[Para](
                                stop=ChapterFilter(
                                    skip_pattern=MatchMulti.compile(
                                        P_NEED_IGNORE, P_CHAPTER_RISK, r"member", R_COMPOSE, operator=any
                                    )
                                ),
                                skip=ParaTextFilter(pattern=P_NEED_IGNORE) | ParaLensFilter(0, 30),
                                filter=ParaTextFilter(MatchMulti.compile(*p_rmc_compose, operator=any)),
                                limit=20,
                                size=1,
                                adapter=ParaAsPos(),
                            ).link_next(
                                SentenceFromSplitPara(
                                    separator=P_PERIOD_SEPARATOR,
                                    ordered_patterns=p_rmc_compose,
                                ),
                                filter=rmc_are_not_all_board,
                            ),
                        )
                        for title in (
                            R_RMC,
                            r"committ?ee?s\s*under\s*the\s*board|board\s*committ?ee?s",
                            r"risk\s*management",
                        )
                    ],
                ),
            },
            # 找董事会议表格且包含RMC列
            # http://************:55647/#/project/remark/244571?treeId=16619&fileId=67045&schemaId=28&projectId=17&schemaKey=E(d)(iv)-1
            {
                "name": "special_cells",
                **get_root_chapter_config(syllabus_regs=r"board"),
                "force_use_all_elements": True,
                "title_patterns": MatchMulti.compile(
                    r"board|each\s*(director|committ?ee)|directors", rf"{R_COMPOSE}|{R_ATTEND}", operator=all
                ),
                "col_header_pattern": R_RMC,
            },
        ],
    }
    if enum_value:
        model["enum"] = enum_value
    return model


def has_risk_committee_models(enum_value):
    return [
        # 1. 全文找rick committee章节或Risk management committee章节，找到返回N/A
        {
            "name": "yoda_layer",
            "use_model_answer": True,
            "multi_elements": True,
            "enum": enum_value,
            "with_element_box": False,
            "rule": Operator.any(
                CGCascadeChapterLocator(
                    chapter_patterns=[MatchMulti.compile(R_RIC, operator=any)], strict=True, dest=ParaFromPos()
                ),
                CGCascadeChapterLocator(
                    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/4189#note_495807
                    chapter_patterns=[NeglectPattern.compile(match=R_RIC, unmatch=r"risk\s*control\s*committee")],
                    strict=True,
                    dest=ParaFromPos(
                        limit=5,
                        size=1,
                        skip_pattern=P_RMC_NOT_BOARD,
                    ),
                ),
            ),
        },
        # 2. 全文找risk management章节下的关键词`risk management committee`且必须全部由董事构成，找到返回N/A
        rmc_is_ric(enum_value, False),
    ]


def risk_committee_models(r_keyword):
    """
    优先找Risk committee章节下的相关内容，有则返回Comply，如果没有相关内容，但符合以下规则，则返回N/A：
    1. 有risk committee章节
    2. 有risk management commit章节，但章节中说明它的组成都是董事，不能是高级管理或者其他
    """
    return [
        # 1. 在Risk committee章节下找包含review+risk control的表格
        # http://************:55647/#/project/remark/244323?treeId=8837&fileId=67293&schemaId=28&projectId=17&schemaKey=E(d)(iv)-1
        {
            "name": "special_cells",
            **get_root_chapter_config(syllabus_regs=R_RIC),
            "multi": True,
            "row_col_relation": "or",
            "row_header_pattern": rf"work|{R_DURING_THE_YEAR}|during\s*the\s*meeting",
            "col_header_pattern": r"content",
            "enum_pattern": r_keyword,
        },
        # 2. 在Risk committee章节下找表格
        # http://************:55647/#/project/remark/244779?treeId=7424&fileId=66837&schemaId=28&projectId=17&schemaKey=E(d)(iv)-1
        {
            "name": "special_cells",
            **get_root_chapter_config(syllabus_regs=R_RIC),
            "paragraph_pattern": gene_p_reviewed_sth(r_keyword),
            "cell_pattern": MatchMulti.compile(rf"work|{R_DISCHARGED}", R_DURING_THE_YEAR, operator=all),
            "enum_pattern": r_keyword,
        },
        # 3. 在Risk committee章节下找段落+as follows
        {
            "name": "para_match",
            "multi_elements": True,
            **get_root_chapter_config(syllabus_regs=R_RIC),
            "paragraph_pattern": gene_p_reviewed_sth(r_keyword),
            "as_follow_pattern": MatchMulti.compile(
                rf"{R_DURING_THE_YEAR}|{R_DISCHARGED_MEETING}", R_AS_FOLLOW_END, operator=all
            ),
            "below_pattern": r_keyword,
        },
        # 4. Ric章节下找关键词
        {
            "name": "score_filter",
            "multi_elements": True,
            "threshold": 0.3,
            **get_root_chapter_config(syllabus_regs=R_RIC),
            "skip_syllabus_title": True,
            "pattern": r_keyword,
            "neglect_pattern": [R_DUTIES],  # 小序号开头的一般是描述职责
        },
        # 5. 找Risk management committee且组成全部为董事的描述，若有，则当做risk committee处理
        {
            "name": "multi_models",
            "operator": "all",
            "results_filter": lambda x, y: y,
            "models": [
                rmc_is_ric(),
                {
                    "name": "multi_models",
                    "operator": "any",
                    "models": [
                        {
                            "name": "para_match",
                            "multi_elements": True,
                            **get_root_chapter_config(syllabus_regs=R_RMC),
                            "paragraph_pattern": gene_p_reviewed_sth(r_keyword),
                            "as_follow_pattern": MatchMulti.compile(
                                rf"{R_DURING_THE_YEAR}|{R_DISCHARGED_MEETING}", R_AS_FOLLOW_END, operator=all
                            ),
                            "below_pattern": r_keyword,
                            "neglect_pattern": [P_MEETING_SKIP, P_SKIP_FORMED, rf"^the\s*({R_PRIMARY_ROLE})\s*include"],
                        },
                        {
                            "name": "score_filter",
                            **get_root_chapter_config(syllabus_regs=R_RMC),
                            "multi_elements": True,
                            "threshold": 0.3,
                            "skip_syllabus_title": True,
                            "pattern": r_keyword,
                            "neglect_pattern": [
                                P_MEETING_SKIP,
                                P_SKIP_FORMED,
                                rf"^the\s*({R_PRIMARY_ROLE})\s*include",
                                R_DUTIES,
                            ],  # 小序号开头的一般是描述职责
                        },
                    ],
                },
            ],
        },
        # 6-7. 全文找Risk management committee章节，找到返回N/A
        *has_risk_committee_models(AnswerValueEnum.NA.value),
    ]


predictor_options = [
    {
        "path": ["RiC-E(a)-Role and function"],
        "models": [
            {
                "name": "para_match",
                "multi_elements": True,
                "paragraph_pattern": (
                    r"to review the internal control system".replace(" ", R_SPACE),
                    r"the main duties and authorities of the audit committee include".replace(" ", R_SPACE),
                    r"to monitor the internal audit system of".replace(" ", R_SPACE),
                    r"risk management and internal monitoring system".replace(" ", R_SPACE),
                ),
            },
            {
                "name": "syllabus_based",
                "only_inject_features": True,
                "multi": True,
                "inject_syllabus_features": [
                    r"__regex__Investment and Risk Management Committee|Risk Committee".replace(" ", R_SPACE),
                    r"__regex__RISK MANAGEMENT AND INTERNAL CONTROL".replace(" ", R_SPACE),
                ],
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"the investment and risk management committee was established on".replace(" ", R_SPACE),
                        r"the principal role and responsibilities of the risk committee include",
                        r"the group established a risk management committee".replace(" ", R_SPACE),
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "syllabus_based",
                "only_inject_features": True,
                "multi": True,
                "inject_syllabus_features": [
                    r"__regex__AUTHORITY AND DUTIES".replace(" ", R_SPACE),
                ],
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"overseeing financial reporting, risk management and internal control systems of the Company".replace(
                            " ", R_SPACE
                        ),
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "syllabus_based",
                "only_inject_features": True,
                "paragraph_model": "para_match",
                "use_crude_answer": True,
                "para_config": {
                    "paragraph_pattern": (
                        r"the company has established the strategy and risk management committee".replace(" ", R_SPACE),
                        r"reviewing the group’s financial control, internal control and risk management systems".replace(
                            " ", R_SPACE
                        ),
                        r"established a risk prevention and digital assets management committee".replace(" ", R_SPACE),
                        r"and reviews effectiveness of internal control and risk evaluation".replace(" ", R_SPACE),
                        r"the audit committee is delegated.*?risk management and internal control systems of the group".replace(
                            " ", R_SPACE
                        ),
                        r"the internal control committee.*?effectiveness of the risk management and internal control systems".replace(
                            " ", R_SPACE
                        ),
                        (
                            r"the audit committee is responsible for conducting internal audit and supervision on the company’s "
                            r"financial income, expenditure and economic activities".replace(" ", R_SPACE)
                        ),
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "syllabus_based",
                "only_inject_features": True,
                "multi": True,
                "inject_syllabus_features": [
                    r"__regex__AUDIT COMMITTEE".replace(" ", R_SPACE),
                ],
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        rf"the {R_AC} is primarily.*?risk management and internal control systems of the group",
                    ),
                },
                "table_model": "row_match",
                "table_config": {
                    "row_pattern": [
                        r"Role & Function",
                        r"角色與職能",
                    ]
                },
            },
            {
                "name": "kmeans_classification",
                "filter_low_score_threshold": 0.3,
            },
            {
                "name": "twice_para_match",
                "use_all_elements": False,  # 基于初步定位结果再次过滤，避免结果范围过大
                "syllabus_regs": [],
                "paragraph_pattern": (  # 主要的
                    rf"develop{R_SPACE}and{R_SPACE}review{R_SPACE}risk{R_SPACE}management{R_SPACE}system",
                    rf"the{R_SPACE}(principal|primary)",
                ),
                "neglect_pattern": (r"the principal roles of the board",),
                "second_pattern": (
                    rf"risk({R_SPACE}(control|management))?{R_SPACE}(committee|system)",  # 风险委员会
                    rf"risk{R_SPACE}management",  # 风险管理
                    rf"{R_SPACE}(ac|audit|auditors){R_EXIST_SPACE}",  # 审计委员会
                ),
            },
            {
                "name": "syllabus_based",
                "only_inject_features": True,
                "paragraph_model": "para_match",
                "use_crude_answer": True,
                "para_config": {
                    "paragraph_pattern": (
                        (
                            r"the (principal|primary)( role and)? function of the (audit|risk management) "
                            r"committee is to assist"
                        ),
                        r"the (principal|primary) duties of the (ac|audit committee|audit|risk management)",
                        (
                            r"reviewing with the group’s management, external auditors and internal auditors, the adequacy of the "
                            r"group’s policies and procedures regarding internal controls"
                        ),
                        (
                            r"evaluating and determining the nature and extent of the risks "
                            r"the board is willing to take in achieving"
                        ),
                        r"the audit committee is also responsible for making recommendation to the board on the appointment",
                        (
                            r"the purpose of the establishment of the audit committee is for reviewing and supervising the "
                            r"financial reporting process"
                        ),
                        r"the primary responsibilities of the strategy and risk management committee include reviewing",
                        r"discussing the risk management and internal control system with management",
                        r"extent and effectiveness of the system of internal control and risk management of the Group",
                        r"to provide leadership to the management in relation to risk management and internal control",
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "score_filter",
                "threshold": 0.618,
            },
            {
                "name": "twice_para_match",
                "use_all_elements": True,
                "syllabus_regs": [],
                "paragraph_pattern": (  # 主要的
                    rf"the {R_AC}",
                ),
                "neglect_pattern": (),
                "second_pattern": (
                    r"internal control,? risk management",
                    r"risk management and internal control systems",
                ),
            },
        ],
    },
    {
        "path": ["RiC-E(b)-Composition"],
        "default_enum_value": CGEnum.NA.value,
        # ----------------------规则说明---------------------
        # 1. 优先从risk management committee(以下简称为RMC)章节下找人员组成
        # 2. 若第2步找不到，则找章节标题`risk management committee`，找到返回ND
        # 3. 没有RMC章节，则在risk management章节下找关键词`risk management committee`且它全部由董事组成，则返回NA
        # 4. 2、3都没有，则取：AC-E(b)-Composition
        "models": [
            # 1~10: 先找ric章节下成员构成
            *get_eb_models(R_RIC, skip_answer_value=CGEnum.NA.value, add_score_model=False, add_yoda_model=True),
            # 11. 再找RMC章节下的成员构成，且所有成员都由director构成
            {
                "name": "para_match",
                "operator": "all",
                **get_root_chapter_config(syllabus_regs=R_RMC),
                "multi_elements": True,
                "paragraph_pattern": MatchMulti.compile(R_RMC, R_COMPOSE, operator=all),
                "neglect_pattern": P_RMC_NOT_BOARD,
            },
            # 12-13: 有RiC章节，直接返回N/A
            *has_risk_committee_models(CGEnum.NA.value),
            # 14: 找不到则取AC-E(b)
            {
                "name": "reference",
                "from_answer_value": CGEnum.C.value,
                "from_path": "AC-E(b)-Composition",
            },
        ],
    },
    {
        "path": ["RiC-E(c)-Number of meetings and record of attendance"],
        "default_enum_value": CGEnum.NA.value,
        "models": [
            # 模型1：初步预测答案中的表格，内容满足条件，章节为RiC
            {
                "name": "special_cells",
                "syllabus_regs": R_RIC,
                "title_patterns": R_RIC,
                "whole_table": True,
                "cell_pattern": R_EC_CELL,
            },
            # 模型2：遍历所有表格，表名必须包含RiC
            {
                "name": "special_cells",
                "force_use_all_elements": True,
                "whole_table": True,
                "title_patterns": MatchMulti.compile(R_RIC, R_ATTEND, operator=all),
                "neglect_title_patterns": R_EC_NEG,
                "cell_pattern": R_EC_CELL,
            },
            # 模型3：遍历所有表格，单元格必须包含RiC
            {
                "name": "special_cells",
                "force_use_all_elements": True,
                "whole_table": True,
                "title_patterns": MatchMulti.compile(R_EC_BOARD, R_ATTEND, operator=all),
                "neglect_title_patterns": R_EC_NEG,
                "cell_pattern": R_RIC,
            },
            # 模型4：遍历所有表格，表名的上一个段落中必须包含RiC https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/3537#note_429844
            {
                "name": "special_cells",
                "force_use_all_elements": True,
                "whole_table": True,
                "title_patterns": MatchMulti.compile(
                    rf"meeting|會議|\bmet\s*{R_ENG_TIMES}", r"attend|出席", r"[:：,]$", operator=all
                ),
                "neglect_title_patterns": R_EC_NEG,
                "title_above_pattern": R_RIC,
                "cell_pattern": R_EC_CELL,
            },
            # 模型5：RiC（即：风险委员会）全员参加的情况
            get_all_attend_model(R_RIC),
            # 模型6：RiC（即：风险委员会）没有召开会议的情况
            {
                "name": "para_match",
                "enum": AnswerValueEnum.NA.value,
                "paragraph_pattern": P_RIC_NO_MEETING,
                "neglect_pattern": R_EC_NO_MEET_NEG,
            },
            # 模型7-8：有RiC部门，但没有相关描述，直接N/A
            *has_risk_committee_models(AnswerValueEnum.NA.value),
            # 模型9：没有找到RIC相关内容，说明RIC职责由AC承担，找AC相关内容
            {
                "name": "reference",
                "from_path": "AC-E(c)-Number of meetings and record of attendance",
            },
            # 模型10：取高分
            {
                "name": "score_filter",
                "aim_types": ["TABLE"],
                "threshold": 0.618,
                "pattern": R_EC_CELL,
            },
            # 模型11： kmeans
            {
                "name": "kmeans_classification",
                "para_pattern": R_RIC,
                "filter_low_score_threshold": 0.1,
            },
        ],
    },
    {
        # ----------------------规则说明---------------------
        # 1. 首先所有的Comply都必须有关键词`internal audit`
        # 2. 优先从risk management committee(以下简称为RMC)章节下找reviewed internal audit
        # 3. 若第2步找不到，则找章节标题`risk management committee`，找到返回NA
        # 4. 没有章节，则在risk management章节下找关键词`risk management committee`且它全部由董事组成，则返回NA
        # 5. 找关键词risk management | internal control
        # TODO 特殊： http://************:55647/#/project/remark/245374?treeId=6982&fileId=66242&schemaId=28&projectId=17&schemaKey=E%28d%29%28iv%29-1
        # --------------------------------------------------
        # alias: E(d)(iv)-1 - Review of risk management and internal control
        "path": ["E(d)(iv)-1-How responsibility met in review of risk management and internal control"],
        "default_enum_value": CGEnum.NA.value,
        "models": [
            # 1~7： 找risk committee下相关内容
            *risk_committee_models(R_RISK_CONTROL),
            # 8. 直接取E(d)(i)-1结果
            {
                "name": "reference",
                "from_answer_value": CGEnum.C.value,
                "from_path": "E(d)(i)-1-Review of risk management and internal control system",
            },
        ],
    },
    {
        # --------------------规则说明-------------------
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/4190
        # 1. 首先所有的Comply都必须有关键词`internal audit`
        # 2. 优先从risk management committee(以下简称为RMC)章节下找reviewed internal audit
        # 3. 若第2步找不到，则找章节标题`risk management committee`，找到返回NA
        # 4. 没有章节，则在risk management章节下找关键词`risk management committee`且它全部由董事组成，则返回NA
        # 5. 基于初步定位，找包含reviewed internal audit的表格
        # 6. 基于初步定位，找during the year+履行了以下职责的`as following`语句，及follow条目中包含`intrenal audit`句子
        # 7. 基于初步定位，在段落中找during the year+履行了以下职责+关键词`internal audit`
        # 8. 初步定位结果中找不到，全文定位`audit committee`章节，找满足第7步的段落
        # ----------------------------------------------
        # alias: E(d)(iv)-2 - Effectiveness of internal audit
        "path": ["E(d)(iv)-2-Effectiveness of issuer's internal audit function"],
        "default_enum_value": CGEnum.NA.value,
        "models": [
            # 1. Risk committee章节下：公司没有internal audit function
            {
                "name": "para_match",
                **get_root_chapter_config(syllabus_regs=R_RIC),
                "paragraph_pattern": R_NO_INTERNAL_AUDIT,
            },
            # 2~8： 找risk committee下相关内容
            *risk_committee_models(R_INTERNAL_AUDIT),
            # 9. 直接取E(d)(i)-2结果
            {
                "name": "reference",
                "from_answer_value": CGEnum.C.value,
                "from_path": "E(d)(i)-2-Effectiveness of internal audit function",
            },
        ],
    },
]
