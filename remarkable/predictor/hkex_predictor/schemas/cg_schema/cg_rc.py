import re

from remarkable.common.common_pattern import (
    P_PERIOD_SEPARATOR,
    P_SEN_SEPARATOR,
    R_CG_CHAPTER_TITLES,
    R_FOLLOW_PREFIX,
    R_MIDDLE_DASH,
)
from remarkable.common.constants import Answer<PERSON><PERSON>ue<PERSON>num, <PERSON>GEnum
from remarkable.common.pattern import (
    MatchMulti,
    NeglectPattern,
    SplitBeforeMatch,
)
from remarkable.predictor.hkex_predictor.models.yoda_layer.operator import Operator
from remarkable.predictor.hkex_predictor.models.yoda_layer.rule.by_pos import (
    SentenceFromSplitPara,
)
from remarkable.predictor.hkex_predictor.models.yoda_layer.rule.root import (
    <PERSON><PERSON><PERSON><PERSON>erLocator,
    ScoreDestFilter,
    ScoreParaFilter,
)
from remarkable.predictor.hkex_predictor.schemas.cg_schema.common_models import (
    get_eb_models,
    get_ec_models,
    get_work_done,
)
from remarkable.predictor.hkex_predictor.schemas.pattern import (
    P_MEETING_SKIP,
    P_NEED_IGNORE,
    R_AS_<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>,
    R_CEO,
    <PERSON>_<PERSON><PERSON>CHARGE<PERSON>,
    R_DURING_THE_YEAR,
    R_JURA_CG_IGNORE_CHAPTER,
    R_<PERSON>,
    R_REVIEWED,
    gene_p_committee_reviewed_sth,
    gene_p_reviewed_sth,
)
from remarkable.predictor.models.base_model import R_PRIMARY_ROLE
from remarkable.predictor.models.para_match import AsFollowType

R_DURING = r"during|(for|in)\s*the\s*year|in\s*20\d{2}\b"
# 履行了当年的职责
SCORE_FILTER_DISCHARGED = ScoreParaFilter(
    pattern=MatchMulti.compile(
        R_RC,
        R_DURING,
        MatchMulti.compile(
            r"(have|ha[sd])\s*(been\s*)?(discharged|performed|undertaken|undertook)",
            r"(review|assess|evaluat)ed.*?remuneration.*?other\s*related\s*matters",
            operator=any,
        ),
        operator=all,
    )
)

R_ED_II_NEG_CHAPTERS = [r"remuneration\s*package", rf"duties\s*of\s*(the\s*)?{R_RC}"]
P_ED_II_AS_FOLLOW = MatchMulti.compile(
    R_DURING,
    r"summary\s*of\s*work|work.*?performed|meeting|\bmet\b",
    r"[:：]$",
    operator=all,
)

R_ED_II_1_KEYWORD = r"(remuneration|compensation)(?!\s*(and|&|committ?e))|bonus|package|salary|payment"
P_ED_II_2_COMPLY = NeglectPattern.compile(
    match=MatchMulti.compile(
        r"review|assess|consider|evaluat|based\s*on\s*individual\s*performance",
        r"performance\s",
        rf"director|senior\s*(management|Executives)|{R_CEO}",
        operator=all,
    ),
    unmatch=MatchMulti.compile(
        r"group['‘’]s\s*performance",
        r"company['‘’]s\s*performance",
        r"performance\s*(target|bonus)",
        r"financial\s*performance",
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/3768#note_455287
        r"determined\s*(by|with)\s*reference[^.;；]+?performance",
        operator=any,
    ),
)

P_ED_II_2_NEG = NeglectPattern.compile(
    match=MatchMulti.compile(R_PRIMARY_ROLE, r"dut(y|ies)|responsib|meets", operator=any),
    unmatch=re.compile(rf"\bmet\b|meeting|discharge|{R_DURING}|[:：]$", re.I),
)
R_APPROVE = r"approv|recommend|propose|review|assess|evaluat|renew|proposal"
R_CONTRACT = r"director['‘’]?s['‘’]?\s*(services?\s*)?(contract|agreement)|services?\s*(contract|agreement)|terms\s*of\s*letter|appointment\s*letter"
P_ED_II_3_COMPLY = NeglectPattern(
    match=MatchMulti.compile(
        # 场景1：批准或审核了服务条款
        rf"(approved|renewed|reviewed|assessed|evaluated|made\s*recommendations)[^.]+?({R_CONTRACT})",
        rf"({R_CONTRACT})[^.]+?been\s*(approved|renewed|reviewed|assessed|evaluated)",
        MatchMulti.compile(
            R_DURING,
            rf"({R_APPROVE})[^.]+?({R_CONTRACT})",
            operator=all,
        ),
        # 场景2：开会讨论了服务条款相关事项
        MatchMulti.compile(
            R_DURING, r"\bmet\b|meetings|one\s*meeting", rf"({R_APPROVE})[^.]+?({R_CONTRACT})", operator=all
        ),
        operator=any,
    ),
    unmatch=MatchMulti.compile(
        r"not\s*allowed\s*to\s*approve", rf"(save|except)\s*(for|as)\s*({R_CONTRACT})", operator=any
    ),
)

ALL_RC_E12 = [
    MatchMulti.compile(
        r"recommend|propose|determining",
        r"remuneration|Board|bonus",
        R_DURING_THE_YEAR,
        operator=all,
    ),
    MatchMulti.compile(
        rf"^[(（•\d]|^{R_MIDDLE_DASH}",
        r"performe|review|recommend|determine|determining|recommendation",
        r"remuneration|fee",
        r"((all|executive) director)|INED|management",
        # R_DURING_THE_YEAR,
        operator=all,
    ),
    re.compile(r"reviewed the new service contracts and remuneration package", re.I),
    re.compile(r"E\.1\.2 \(c\)\(ii\) of the CG Code has been adopted", re.I),
]
P_ALL_RC_E12_MEETING = MatchMulti.compile(*ALL_RC_E12, operator=any)

RC_CHAPTER_LOCATOR = CGChapterLocator(
    min_page=5,
    pattern=re.compile(R_RC, re.I),
)

R_AS_FOLLOW_SUFFIX = r"(tasks to|include|limited to|following|amongst other things):$"
R_ROLE = r"(responsib(le|ilities)|functions|duties|roles?|objectives?|terms of reference)"
R_SHARE_SCHEME = "|".join(
    [
        r"\b(share|stock)s?\s*(option|award|scheme|plan)",
        r"\baward(s|ed)?\s*share",
        r"(share|stock|long\s*term)\s*incentiv",
        r"(incentive|ownership)\s*(scheme|plan)",
        r"\bof\s*(option|award|share)s",
        rf"(share|equity|incentive){R_MIDDLE_DASH}based",
        r"\bRS\b",
        r"\brestricted\s*(unite?s?\s*)?(share|a\s*ward|stock)",
        r"\b(rsus?|MSOP|ESOP|ESPP|LTIP?)\b" r"\b[AH]\s*shares",
        r"grant\s*(scheme|plan)",
    ]
)

predictor_options = [
    {
        "path": ["RC-E(a)-Role and function"],
        "models": [
            {
                "name": "syllabus_based",
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__REMUNERATION OF DIRECTORS AND SENIOR MANAGEMENT AND BOARD EVALUATION",
                ],
                "paragraph_model": "empty",
                "table_model": "row_match",
                "table_config": {
                    "row_pattern": [
                        "A Remuneration Committee has been established by the Board",
                    ],
                },
            },
            {
                "name": "syllabus_based",
                "multi": True,
                "only_inject_features": True,
                "ignore_pattern": [P_NEED_IGNORE],
                "inject_syllabus_features": [
                    r"__regex__^REMUNERATION COMMITTEE$",
                    r"__regex__Remuneration Committee Report",
                ],
                "as_follow_pattern": [
                    r"The Remuneration Committee is given the tasks to:$",
                    rf"{R_PRIMARY_ROLE}.*?{R_AS_FOLLOW_SUFFIX}",
                    r"responsibilities mainly include, among others:$",
                ],
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"The Remuneration Committee is given the tasks to:$",
                        rf"{R_PRIMARY_ROLE}.*?{R_AS_FOLLOW_SUFFIX}",
                        r"responsibilities mainly include, among others:$",
                    ),
                    "neglect_pattern": (
                        r"shall be held",
                        r"Details of the number of Remuneration Committee.*?on pages",
                    ),
                    "neglect_below_pattern": (
                        r"^Code provision E.1.2 of the Corporate Governance Code",
                        r"^Corporate Governance Report$",
                    ),
                },
                "table_model": "row_match",
                "table_config": {
                    "row_pattern": [
                        r"Role & Function",
                        r"角色與職能",
                    ]
                },
            },
            {
                "name": "score_filter",
                "threshold": 0.6,
                "pattern": [
                    rf"{R_RC}",
                ],
            },
            {
                "name": "score_filter",
                "threshold": 0.1,
                "pattern": [
                    rf"{R_RC}.*?{R_ROLE}",
                    rf"{R_ROLE}.*?{R_RC}",
                ],
            },
        ],
    },
    {
        # 最优先位置，各个committee标题下披露的委员会成员及其类型
        # 次优位置，在1没有披露的情况下，提取各个committee标题下披露的委员会成员开会参与情况，即规则E(c)-Number of meetings and record 内容
        # 再次优位置，在1和2均没有披露的情况下，提取board meeting下汇总披露的各个委员会开会成员参与情况
        # 最后，在1，2，3均没有披露的情况下，提取board composition或board training中的董事list及其类型
        "path": ["RC-E(b)-Composition"],
        "element_candidate_count": 20,
        "models": [
            # 模型1-12：通用查找句子及as follow
            *get_eb_models(R_RC, add_yoda_model=True),
            # 模型13：特例 http://100.64.0.105:55647/#/project/remark/245397?treeId=6025&fileId=66219&schemaId=28&projectId=17&schemaKey=NC-E(b)
            {
                "name": "syllabus_based",
                "enum": CGEnum.C.value,
                "only_inject_features": True,
                "ignore_pattern": [P_NEED_IGNORE],
                "include_shape": True,
                "shape_as_table": True,
                "inject_syllabus_features": [
                    r"__regex__^Composition of th1e Board and Board Committees$",
                ],
                "table_model": "shape_title",
                "table_config": {
                    "only_inject_features": True,
                    "regs": [
                        r"Composition of the Board and Board Committees",
                    ],
                },
            },
        ],
    },
    {
        "path": ["RC-E(c)-Number of meetings and record of attendance"],
        "models": get_ec_models(R_RC, r"(remuneration|compensation)"),
    },
    {
        # alias: E(d)(ii) - Remuneration policy of EDs
        "path": ["E(d)(ii)-1-Policy for remuneration of executive directors"],
        "models": [
            # 模型1~7
            *get_work_done(R_RC, R_ED_II_1_KEYWORD),
            # 模型10
            {"name": "score_filter", "threshold": 0.618, "pattern": R_ED_II_1_KEYWORD},
        ],
    },
    {
        # alias : E(d)(ii)-1 - Performance assessment of EDs
        "path": ["E(d)(ii)-2-Assessing performance of executive directors"],
        "default_enum_value": CGEnum.ND.value,
        "models": [
            # 1. 高分直接取
            {
                "name": "score_filter",
                "threshold": 0.618,
            },
            # 2. 0.3分以上做过滤
            {
                "name": "kmeans_classification",
                "filter_low_score_threshold": 0.3,
                "para_pattern": MatchMulti.compile(
                    r"performance", rf"\bmet\b|meeting|{R_DURING}|(review|assess|evaluat)ed", operator=all
                ),
                "neglect_pattern": P_ED_II_2_COMPLY.unmatch,
            },
            # 3. 不限制章节，必须要RC关键词
            {
                "name": "para_match",
                "filter_primary_role_elements": True,
                "neglect_syllabus_regs": R_JURA_CG_IGNORE_CHAPTER + R_ED_II_NEG_CHAPTERS,
                # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/3768#note_441691
                "as_follow_pattern": MatchMulti.compile(R_RC, P_ED_II_AS_FOLLOW, operator=all),
                "below_pattern": P_ED_II_2_COMPLY,
                "paragraph_pattern": MatchMulti.compile(
                    R_RC,
                    R_DURING,
                    r"(review|assess|consider|evaluat)(ed|ing)|\bmet\b|meeting",
                    P_ED_II_2_COMPLY,
                    operator=all,
                ),
                "neglect_pattern": P_ED_II_2_NEG,
            },
            # 4. 限制RC章节，取符合条件的单元格
            # http://100.64.0.105:55647/#/project/remark/244323?treeId=8837&fileId=67293&schemaId=28&projectId=17&schemaKey=E(d)(ii)-2
            {
                "name": "special_cells",
                "multi": True,
                "syllabus_regs": R_RC,
                "title_patterns": P_ED_II_AS_FOLLOW,
                "cell_pattern": P_ED_II_2_COMPLY,
            },
            # 5. 限制RC章节，不需要RC关键词
            {
                "name": "para_match",
                "filter_primary_role_elements": True,
                "syllabus_regs": [R_RC],
                "neglect_syllabus_regs": R_ED_II_NEG_CHAPTERS,
                # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/3768#note_441691
                "as_follow_pattern": P_ED_II_AS_FOLLOW,
                "as_follow_type": AsFollowType.ANY,
                "below_pattern": P_ED_II_2_COMPLY,
                "paragraph_pattern": MatchMulti.compile(
                    R_DURING,
                    r"(review|assess|consider|evaluat)(ed|ing)|\bmet\b|meeting",
                    P_ED_II_2_COMPLY,
                    operator=all,
                ),
                "neglect_pattern": P_ED_II_2_NEG,
            },
            # 6. 段落被识别为表格，不判断表名
            # http://100.64.0.105:55647/#/project/remark/245359?treeId=37644&fileId=66257&schemaId=28&projectId=17&schemaKey=E(d)(ii)-2
            {
                "name": "special_cells",
                "cell_pattern": MatchMulti.compile(
                    R_DURING,
                    r"review|assess|consider|evaluat",
                    P_ED_II_2_COMPLY,
                    operator=all,
                ),
            },
            # 7. 描述了RC的职责，职责中包含评估执行董事的表现，最后描述已完成职责
            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/3768#note_442942
            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/3768#note_442006
            {
                "name": "yoda_layer",
                "multi_elements": True,
                "threshold": 0,
                "rule": Operator.all(
                    ScoreParaFilter(
                        pattern=MatchMulti.compile(
                            R_RC,
                            R_PRIMARY_ROLE,
                            P_ED_II_2_COMPLY,
                            operator=all,
                        )
                    ),
                    SCORE_FILTER_DISCHARGED,
                ),
            },
            # 8. 特殊文件
            # http://100.64.0.105:55647/#/project/remark/244922?treeId=37922&fileId=66694&schemaId=28&projectId=17&schemaKey=E(d)(ii)-2
            {
                "name": "para_match",
                "filter_primary_role_elements": True,
                "paragraph_pattern": MatchMulti.compile(
                    rf"^[(（•\d]|^{R_MIDDLE_DASH}",
                    P_ED_II_2_COMPLY,
                    r"Remuneration|compensation",
                    operator=all,
                ),
            },
        ],
    },
    {
        # alias: E(d)(ii)-2 - Approval of directors' service contracts
        "path": ["E(d)(ii)-3-Approving terms of directors' service contracts"],
        "default_enum_value": CGEnum.ND.value,
        "models": [
            # 1. 高分直接取
            {
                "name": "score_filter",
                "threshold": 0.618,
            },
            # 2. 0.1以上用关键词过滤
            {
                "name": "kmeans_classification",
                "filter_low_score_threshold": 0.1,
                "para_pattern": P_ED_II_3_COMPLY,
                "neglect_pattern": [
                    P_ED_II_2_NEG,
                    r"not\s*allowed\s*to\s*approve",
                    rf"(save|except)\s*(for|as)\s*({R_CONTRACT})",
                ],
            },
            # 3. 找表格
            {
                "name": "special_cells",
                "syllabus_regs": [R_RC],
                "cell_pattern": MatchMulti.compile(R_APPROVE, R_CONTRACT, operator=all),
            },
            # 4. 限制RC章节，不需要RC关键词
            {
                "name": "para_match",
                "filter_primary_role_elements": True,
                "syllabus_regs": [R_RC],
                "neglect_syllabus_regs": R_ED_II_NEG_CHAPTERS,
                # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/3768#note_441691
                "as_follow_pattern": P_ED_II_AS_FOLLOW,
                "below_pattern": MatchMulti.compile(R_APPROVE, R_CONTRACT, operator=all),
                "paragraph_pattern": [
                    P_ED_II_3_COMPLY,
                    MatchMulti.compile(rf"^[(（•\d]|^{R_MIDDLE_DASH}", R_APPROVE, R_CONTRACT, operator=all),
                ],
                "neglect_pattern": P_ED_II_2_NEG,
            },
            # 5. 不限制RC章节，必须有关键词
            {
                "name": "para_match",
                "filter_primary_role_elements": True,
                "neglect_syllabus_regs": R_JURA_CG_IGNORE_CHAPTER + R_ED_II_NEG_CHAPTERS,
                "as_follow_pattern": MatchMulti.compile(R_RC, P_ED_II_AS_FOLLOW, operator=all),
                "below_pattern": MatchMulti.compile(R_APPROVE, R_CONTRACT, operator=all),
                "paragraph_pattern": MatchMulti.compile(R_RC, P_ED_II_3_COMPLY, operator=all),
                "neglect_pattern": P_ED_II_2_NEG,
            },
            # 6. 描述了RC的职责，职责中包含关键词，最后描述已完成职责
            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/3769#note_442009
            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/3769#note_442954
            {
                "name": "yoda_layer",
                "multi_elements": True,
                "threshold": 0,
                "rule": Operator.all(
                    ScoreParaFilter(
                        pattern=MatchMulti.compile(
                            R_RC,
                            R_PRIMARY_ROLE,
                            r"approv|renew|recommend",
                            R_CONTRACT,
                            operator=all,
                        )
                    ),
                    SCORE_FILTER_DISCHARGED,
                ),
            },
        ],
    },
    {
        # alias: E(d)(ii)-3 - Review and/or approval of share scheme
        "path": ["E(d)(ii)-4-Reviewing and or approving share scheme matters"],
        "default_enum_value": CGEnum.ND.value,
        "models": [
            # 1. 找出否定语句，枚举值为N/A
            {
                "name": "yoda_layer",
                "threshold": 0,
                "enum": AnswerValueEnum.NA.value,
                "syllabus_regs": R_RC,
                "multi_elements": True,
                "rule": ScoreDestFilter(
                    dest=SentenceFromSplitPara(
                        separator=P_SEN_SEPARATOR,
                        ordered_patterns=[
                            MatchMulti.compile(
                                r"\b(no|neither|nor)\s*((material\s*)?matter|scheme|plan|meeting)",
                                R_SHARE_SCHEME,
                                operator=all,
                            ),
                        ],
                    ),
                ),
            },
            # 2. 找包含review+internal audit的表格，限制行名
            {
                "name": "special_cells",
                # "threshold": 0.01,
                "multi": True,
                "syllabus_regs": R_RC,
                "row_header_pattern": rf"work|{R_DURING_THE_YEAR}",
                "enum_pattern": R_SHARE_SCHEME,
            },
            # # 3. 找包含review+internal audit的表格，限制列名，暂未用到
            # {
            #     "name": "special_cells",
            #     "threshold": 0.01,
            #     "multi": True,
            #     "title_patterns": R_RC,
            #     "col_header_pattern": r"meetings?\s*content",
            #     "enum_pattern": R_SHARE_SCHEME,
            # },
            # 3. 限制章节+段落+as follow：履行了以下职责+reviewed internal audit、或履行了以下职责+reviewed internal audit
            {
                "name": "para_match",
                "threshold": 0.01,
                "syllabus_regs": R_RC,
                "paragraph_pattern": gene_p_reviewed_sth(R_SHARE_SCHEME),
                "as_follow_pattern": NeglectPattern.compile(
                    match=MatchMulti.compile(
                        rf"(has|have|had)[:：]|meeting|\bmet\b|{R_DISCHARGED}|{R_DURING_THE_YEAR}",
                        R_AS_FOLLOW_END,
                        operator=all,
                    ),
                    unmatch=r"remuneration\s*package",
                ),
                "below_pattern": R_SHARE_SCHEME,
                # 小序号开头的，包含在了as_follow_pattern的结果里，这里需要排除
                "neglect_pattern": [R_FOLLOW_PREFIX, P_MEETING_SKIP],
            },
            # 4. 关键词AC+段落+as follow：AC履行了以下职责+reviewed internal audit、或履行了以下职责+reviewed internal audit
            {
                "name": "para_match",
                "threshold": 0.01,
                # "multi_elements": True,
                "parent_features": R_CG_CHAPTER_TITLES,
                "parent_must_be_root": True,
                "page_first_as_parent_syllabus": True,
                "paragraph_pattern": gene_p_committee_reviewed_sth(R_RC, R_SHARE_SCHEME),
                "as_follow_pattern": MatchMulti.compile(
                    R_RC,
                    rf"(has|have|had)[:：]|meeting|\bmet\b|{R_DISCHARGED}|{R_DURING_THE_YEAR}",
                    R_AS_FOLLOW_END,
                    operator=all,
                ),
                "below_pattern": R_SHARE_SCHEME,
                # 小序号开头的一般包含在了as_follow_pattern中是描述职责
                "neglect_pattern": [R_FOLLOW_PREFIX, P_MEETING_SKIP],
            },
            # 5. kmeans过滤，限制关键词internal audit
            {
                "name": "kmeans_classification",
                "parent_features": R_CG_CHAPTER_TITLES,
                "page_first_as_parent_syllabus": True,
                "parent_must_be_root": True,
                "neglect_syllabus_regs": r"(remuneration|compensation)\s*(policy|package)",
                "filter_low_score_threshold": 0.3,
                "skip_syllabus_title": True,
                "para_pattern": SplitBeforeMatch(
                    MatchMulti.compile(
                        rf"{R_DURING_THE_YEAR}|{R_DISCHARGED}|{R_REVIEWED}|meeting|\bmet\b",
                        R_SHARE_SCHEME,
                        operator=all,
                    ),
                    separator=P_PERIOD_SEPARATOR,
                ),
                "neglect_pattern": [R_FOLLOW_PREFIX, P_MEETING_SKIP],
            },
        ],
    },
    {
        # alias: E(d)(ii)-4 - Determination models of EDs' remuneration
        "path": ["E(d)(ii)-5-Disclose which of the models in E.1.2(c) adopted"],
        "location_threshold": 0.005,
        "models": [
            # 特例
            {
                "name": "syllabus_based",
                "multi": True,
                "only_inject_features": True,
                "ignore_pattern": [P_NEED_IGNORE],
                "inject_syllabus_features": [
                    r"__regex__Board Committees of the Board of Directors",
                ],
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"Compensation Committee.*responsible for",
                        MatchMulti.compile(
                            rf"^[(（•\d]|^{R_MIDDLE_DASH}",
                            r"at least annually",
                            r"approving|recommending",
                            r"executive officers|executive compensation ",
                            operator=all,
                        ),
                    ),
                    "neglect_pattern": (),
                },
            },
            {
                "name": "para_match",
                "paragraph_pattern": (r"E\.1\.2 \(c\)\(ii\) of the CG Code has been adopted",),
            },
            # 优先提取优先Remuneration Committee本年完成的工作中关于determine或recommend薪酬方案，
            # during the year 或者 The Remuneration Committee held  开头的 或者单句的答案
            {
                "name": "para_match",
                "filter_primary_role_elements": True,
                "paragraph_pattern": (
                    MatchMulti.compile(
                        r"performe|recommend|make recommendation|propose|approve|determine",
                        r"remuneration|Board|bonus",
                        r"^during ([^\s]+\s*){,3} (period|year|(fy)?\d{4})|^The Remuneration Committee held.*meetings",
                        operator=all,
                    ),
                ),
                "neglect_syllabus_regs": [
                    r"Remuneration of Directors",
                    r"Compensation",
                ],
                "neglect_pattern": (
                    r"Nomination Committee",
                    r":$",
                ),
            },
            # 优先提取优先Remuneration Committee本年完成的工作中关于determine或recommend薪酬方案，
            {
                "name": "para_match",
                "multi_elements": True,
                "combine_paragraphs": True,
                "ignore_pattern": [P_NEED_IGNORE],
                "filter_primary_role_elements": True,
                "paragraph_pattern": (P_ALL_RC_E12_MEETING,),
                "neglect_syllabus_regs": [
                    r"Remuneration of Directors",
                    r"AUDIT COMMITTEE",
                    r"Compensation",
                ],
                "neglect_pattern": (
                    r"Nomination Committee",
                    r":$",
                ),
            },
            {
                "name": "syllabus_based",
                "only_inject_features": True,
                "multi": True,
                "ignore_pattern": [P_NEED_IGNORE],
                "inject_syllabus_features": [
                    r"__regex__Remuneration and Management Development Committee",
                    r"__regex__^Remuneration Committee$",
                    r"__regex__REMUNERATION OF DIRECTORS",
                ],
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (P_ALL_RC_E12_MEETING),
                    "neglect_pattern": (r"Nomination Committee",),
                },
                "table_model": "special_cells",
                "table_config": {
                    "cell_pattern": [
                        r"making recommendation.*?remuneration packages",
                        r"^Reviewed and recommended.*remuneration policy.*the board",
                    ],
                },
            },
            # 若本年完成工作中无则提取 RC role and function 中的相关内容
            {
                "name": "para_match",
                "multi_elements": True,
                "paragraph_pattern": (
                    MatchMulti.compile(
                        rf"^[(（•\d]|^{R_MIDDLE_DASH}",
                        r"performe|review|recommend|mak.*recommendation|approving",
                        r"remuneration",
                        r"(all|executive) director",
                        operator=all,
                    ),
                    MatchMulti.compile(
                        R_PRIMARY_ROLE,
                        r"performe|review|recommend|mak.*recommendation|approving",
                        r"remuneration (Committee|policy)",
                        r"((all|executive) director)|senior management|INED",
                        operator=all,
                    ),
                ),
                "neglect_syllabus_regs": [
                    r"Remuneration of Directors",
                ],
            },
            {
                "name": "kmeans_classification",
                "filter_low_score_threshold": 0.12,
                "para_pattern": [],
                "neglect_pattern": [
                    r"principal role and responsibi",
                    r"(primary|principal) duties",
                    r"primarily responsible",
                    r"primary functions",
                    r"mainly responsible",
                    r"responsible for.*?remuneration package",
                    r"falls within the following bands:",
                ],
            },
            {
                "name": "score_filter",
                "threshold": 0.1,
            },
        ],
    },
]
