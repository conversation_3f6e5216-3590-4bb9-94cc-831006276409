from typing import List

from remarkable.common.common import is_table_elt
from remarkable.common.pattern import MatchMulti
from remarkable.common.util import clean_txt
from remarkable.predictor.models.base_model import BaseModel
from remarkable.predictor.schema_answer import PredictorResult, TableResult

P_HAS_FOLLOW_DETAIL = MatchMulti.compile(
    MatchMulti.compile(
        r"\b(details|records)\b",
        r"\battendance\b",
        r"following|as follows|\bbelow\b",
        operator=any,
    ),
    "[;；:：]$",
    operator=all,
)


def add_follow_table(answers: List[dict], **kwargs) -> List[dict]:
    # 如果答案中不包含表格 且最后一段以冒号结尾 获取下方的表格
    elements = BaseModel.get_elements_from_answer_result(answers)
    if not elements or any(is_table_elt(e) for e in elements):
        return answers
    last_element = elements[-1]
    if not P_HAS_FOLLOW_DETAIL.search(clean_txt(last_element.get("text", ""))):
        return answers
    predictor = kwargs["predictor"]
    next_tables = predictor.pdfinsight.find_elements_near_by(last_element["index"], step=1, aim_types=["TABLE"])
    if not next_tables or not is_table_elt(next_tables[0]):
        return answers
    table_result = PredictorResult([TableResult(next_tables[0])], value=None, schema=predictor.schema)
    answers.append(table_result)
    return answers
