import re

from remarkable.common.common_pattern import (
    R_CHAPTER_PREFIX,
    R_CHAPTER_SUFFIX,
    R_FOLLOW_PREFIX,
)
from remarkable.common.constants import Answer<PERSON><PERSON><PERSON><PERSON><PERSON>, CGEnum
from remarkable.common.pattern import (
    MATCH_ALWAYS,
    MatchMulti,
    NeglectPattern,
    PositionPattern,
)
from remarkable.predictor.hkex_predictor.models.yoda_layer.operator import Operator
from remarkable.predictor.hkex_predictor.models.yoda_layer.rule.by_pos import ParaFromPos
from remarkable.predictor.hkex_predictor.models.yoda_layer.rule.root import (
    CGChapterLocator,
    Reference,
    StrictMode,
)
from remarkable.predictor.hkex_predictor.pattern import R_BE
from remarkable.predictor.hkex_predictor.schemas.cg_schema.cg_h import R_NO_INTERNAL_AUDIT
from remarkable.predictor.hkex_predictor.schemas.cg_schema.common_models import (
    get_eb_models,
    get_ec_models,
    get_p_no_meetings,
    get_root_chapter_config,
    get_work_done,
)
from remarkable.predictor.hkex_predictor.schemas.pattern import (
    P_<PERSON>_<PERSON><PERSON><PERSON>,
    P_NEED_IGNORE,
    R_<PERSON>,
    R_<PERSON><PERSON><PERSON>LATION,
    R_AS_FOLLOW_END,
    R_COMPOSE,
    R_DUTIES,
    R_ENG_TIMES,
    R_HOLD,
    R_INTERNAL_AUDIT,
    R_JURA_CG_IGNORE_CHAPTER,
    R_REVIEW,
    R_REVIEWED,
    R_RISK_CONTROL,
)
from remarkable.predictor.models.base_model import R_PRIMARY_ROLE

R_MEETINGS = [
    rf"{R_ENG_TIMES}\s*{R_AC}?\s*meetings?\s*({R_BE}|(has|have|had)(\s*been)?)\s*{R_HOLD}",
    rf"{R_ENG_TIMES}\s*meetings?\s*of\s*{R_AC}\s*({R_BE}|(has|have|had)(\s*been)?)\s*{R_HOLD}",
    rf"{R_HOLD}\s*{R_ENG_TIMES}\s*meetings?",
    rf"審計委員會召開{R_ENG_TIMES}次會議",
    rf"{R_AC}\s*met\s*{R_ENG_TIMES}",
    r"the\s*meetings\s*held",
]
# 规则AC-E(c)：Not Applicable关键词
P_AC_NO_MEETING = get_p_no_meetings(R_AC)

# https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/3535#note_429028

R_NEGLECT_PARENTS = (
    [
        r"^CORPORATE\s*INFORMATION$",
        r"^EXECUTIVE\s*DIRECTORS$",
        r"^DIRECTORS['‘’]\s*REPORT$",
        r"REPORT\s*OF\s*(THE\s*)?(BOARD\s*OF\s*)?DIRECTORS",
        r"董事會報告",
    ],
)

# 规则AC-E(a)：审计委员会指定章节
R_AC_CHAPTERS = [
    rf"{R_CHAPTER_PREFIX}Audit\s*Committee{R_CHAPTER_SUFFIX}$",
    rf"{R_CHAPTER_PREFIX}Audit\s*Committee\s*REPORT{R_CHAPTER_SUFFIX}$",
    rf"{R_CHAPTER_PREFIX}AUDIT\s*COMMITTEE\s*AND\s*ACCOUNTABILITY{R_CHAPTER_SUFFIX}$",
]
# 规则AC-E(a)：as follows关键词
R_LIST_EA_FOLLOWS = [
    r"include\s*the\s*following[:：]$",
    r"include\s*but\s*are\s*not\s*limited\s*to[:：]$",
    rf"{R_DUTIES}(of|for){R_AC}.*[:：]",
    rf"{R_PRIMARY_ROLE}and{R_DUTIES}.*[:：]$",
]
# 规则AC-E(a)：Comply关键词
R_TUPLE_EA_COMPLY = (
    rf"(?<!include)(?<!including)(?<!includingthe){R_DUTIES}(of|for){R_AC}",
    rf"{R_AC}['‘’]s{R_PRIMARY_ROLE}",
    rf"{R_AC}['‘’]s{R_DUTIES}",
    rf"{R_AC}{R_BE}.{{0,10}}responsiblefor",
    rf"{R_PRIMARY_ROLE}and{R_DUTIES}(of|for){R_AC}",
    rf"{R_AC}{R_BE}requiredto",
)

# 规则E(d)(i)：关键词
R_ED_REVIEWED_FINANCIAL_REPORT = NeglectPattern.compile(
    match=MatchMulti.compile(
        rf"(?<!to)(?<!will|need){R_REVIEW}[^;；.。、：:]+?((financial|annual|interim)(\s*and|[,，])?){{1,2}}\s*(report|results|statement|performance)",
        rf"(?<!to)(?<!will|need){R_REVIEW}[^;；.。、：:]+?((final|financial|quarterly|interim|annual)(\s*and|[,，])?){{1,2}}\s*[^;；.。、：:]+?(report|results|statement|performance)",
        rf"((financial|annual|interim)(\s*and|[,，])?){{1,2}}\s*(report|result|statement|performance)s?[^;；.。、：:]+?({R_BE}|(have|had|has)\s*been)\s*{R_REVIEW}",
        operator=any,
    ),
    unmatch=r"^(\d+\.)?The\s*resolution",
)

# 规则E(d)(i)：Comply关键词
P_ED_I_KEYWORDS = MatchMulti.compile(
    R_AC,
    # r"during|the\s*year",
    R_ED_REVIEWED_FINANCIAL_REPORT,
    operator=all,
)
# 规则E(d)(i)：不匹配关键词
P_ED_I_NEGLECT = NeglectPattern.compile(
    match=MatchMulti.compile(*R_TUPLE_EA_COMPLY, R_PRIMARY_ROLE, operator=any),
    unmatch=MatchMulti.compile(
        r"(discharge?|perform|undertaken)(ed|ing)?\s*(the|its)\s*(duties|responsibilities)",
        # r"Under\s*its\s*terms\s*of\s*reference",
        rf"{R_REVIEWED}.*?(financial|annual|interim)\s*(report|results|statement|performance)",
        rf"{R_REVIEWED}.*?(final|financial|quarterly|interim|annual)\s*\w+(report|results|statement|performance)",
        operator=any,
    ),
)

R_ED_II = (
    rf"(?P<content>The Committee\s?{R_REVIEW}.*?{R_INTERNAL_AUDIT}.*)",
    rf"(?P<content>during [the|FY].*?{R_REVIEW}.*?{R_INTERNAL_AUDIT}.*)",
    rf"(?P<content>The Group.*?{R_INTERNAL_AUDIT}.*?{R_REVIEW}\s?by the Audit Committee)",
    rf"The (Audit )?Committee.*?{R_REVIEW}.*?{R_INTERNAL_AUDIT}",
    rf"during [the|FY].*?{R_INTERNAL_AUDIT}.*?analysis",
    rf"discuss auditing, {R_INTERNAL_AUDIT}",
    rf"the effectiveness of {R_INTERNAL_AUDIT} function",
    rf"^[•].*?{R_REVIEW}.*?{R_INTERNAL_AUDIT}",
    rf"^\([a-z]\).*?{R_REVIEW}.*?{R_INTERNAL_AUDIT}",
    rf"^\(?\d.*?{R_REVIEW}.*?{R_INTERNAL_AUDIT}",
    rf"\((i{{0,3}}|iv|vi?|viii?|ix|xi?)\).*?{R_REVIEW}.*?{R_INTERNAL_AUDIT}",
    rf"^The Audit Committee held\s\w+\smeetings to.*?{R_REVIEW}.*?{R_INTERNAL_AUDIT}",
    rf"^•{R_INTERNAL_AUDIT}\s?plan",
)

R_E_DI_3 = (
    r"does not meet the minimum number of members in the audit committee required under Rule 3.21",  # 66203
    r"failed to fulfil.*Rule 3.21",
)

predictor_options = [
    {
        "path": ["AC-E(a)-Role and function"],
        "models": [
            {
                "name": "para_match",
                "remove_blank": True,
                "syllabus_regs": R_AC_CHAPTERS,
                "as_follow_pattern": R_LIST_EA_FOLLOWS + [pattern + ".*[:：]" for pattern in R_TUPLE_EA_COMPLY],
                "need_default_follows": True,
                "ignore_pattern": [P_NEED_IGNORE],
                "neglect_below_pattern": (
                    r"^(CORPORATE\s*GOVERNANCE\s*REPORT\s*)?企業管治報告書?$",
                    r"^Corporate\s*Governance\s*Report$",
                    rf"^BOARD\s*COMMITTEES{R_CHAPTER_SUFFIX}$",
                    rf"^\(?[iv]+\)?\s{R_AC}{R_CHAPTER_SUFFIX}$",
                ),
                "paragraph_pattern": R_TUPLE_EA_COMPLY + (rf"{R_AC}\s*reports\s*to\s*the\s*Board",),
            },
            {
                "name": "score_filter",
                "skip_syllabus_title": True,
                "threshold": 0.8,
            },
            {
                "name": "syllabus_based",
                "multi": True,
                "only_inject_features": True,
                "ignore_pattern": [P_NEED_IGNORE],
                "inject_syllabus_features": [
                    r"__regex__^AUDIT COMMITTEE$",
                ],
                "paragraph_model": "empty",
                "table_model": "row_match",
                "table_config": {
                    "row_pattern": [
                        r"^Role & Function",
                        r"角色與職能",
                    ],
                },
            },
            {
                "name": "para_match",
                "remove_blank": True,
                "ignore_pattern": [P_NEED_IGNORE],
                "as_follow_pattern": R_LIST_EA_FOLLOWS + [pattern + ".*[:：]" for pattern in R_TUPLE_EA_COMPLY],
                "neglect_below_pattern": (
                    r"^(CORPORATE\s*GOVERNANCE\s*REPORT\s*)?企業管治報告書?$",
                    r"^Corporate\s*Governance\s*Report$",
                    rf"^BOARD\s*COMMITTEES{R_CHAPTER_SUFFIX}$",
                    rf"^\(?[iv]+\)?\s{R_AC}{R_CHAPTER_SUFFIX}$",
                ),
                "paragraph_pattern": tuple(pattern + ".*[:：]" for pattern in R_TUPLE_EA_COMPLY),
            },
            {
                "name": "score_filter",
                "skip_syllabus_title": True,
                "threshold": 0.8,
            },
            {
                "name": "para_match",
                "remove_blank": True,
                "paragraph_pattern": R_TUPLE_EA_COMPLY
                + tuple(R_LIST_EA_FOLLOWS)
                + (rf"{R_AC}\s*reports\s*to\s*the\s*Board",),
            },
            {
                "name": "kmeans_classification",
                "skip_syllabus_title": True,
                "filter_low_score_threshold": 0.1,
            },
        ],
    },
    {
        # 最优先位置，各个committee标题下披露的委员会成员及其类型
        # 次优位置，在1没有披露的情况下，提取各个committee标题下披露的委员会成员开会参与情况，即规则E(c)-Number of meetings and record 内容
        # 再次优位置，在1和2均没有披露的情况下，提取board meeting下汇总披露的各个委员会开会成员参与情况
        # 最后，在1，2，3均没有披露的情况下，提取board composition或board training中的董事list及其类型
        "path": ["AC-E(b)-Composition"],
        "default_enum_value": CGEnum.ND.value,
        "element_candidate_count": 20,
        "models": [
            # 模型1-11：通用查找句子及as follow
            *get_eb_models(R_AC),
            # 模型12：特例 http://************:55647/#/project/remark/245397?treeId=6025&fileId=66219&schemaId=28&projectId=17&schemaKey=AC-E(b)
            {
                "name": "syllabus_based",
                "enum": AnswerValueEnum.COMPLY.value,
                "only_inject_features": True,
                "ignore_pattern": [P_NEED_IGNORE],
                "include_shape": True,
                "shape_as_table": True,
                "inject_syllabus_features": [
                    r"__regex__^Composition of th1e Board and Board Committees$",
                ],
                "table_model": "shape_title",
                "table_config": {
                    "only_inject_features": True,
                    "regs": [
                        r"Composition of the Board and Board Committees",
                    ],
                },
            },
        ],
    },
    {
        "path": ["AC-E(c)-Number of meetings and record of attendance"],
        # "post_process": add_follow_table,
        "models": get_ec_models(R_AC, r"audit"),
    },
    {
        # alias: E(d)(i)-1 - Review of financial reports
        "path": ["E(d)(i)-Review of financial reports"],
        "models": [
            {
                "name": "para_match",
                "remove_blank": True,
                "paragraph_pattern": [P_AC_NO_MEETING, rf"{R_AC}\s*did\s*not\s*meet\s*with\s*external\s*audit"],
                "neglect_parent_features": R_NEGLECT_PARENTS,
            },
            {
                "name": "score_filter",
                "multi_elements": True,
                "neglect_parent_features": R_NEGLECT_PARENTS,
                "threshold": 0.8,
            },
            {
                "name": "special_cells",
                "remove_blank": True,
                "multi": True,
                "multi_elements": True,
                "force_use_all_elements": True,
                "whole_table": False,
                "neglect_parent_features": R_NEGLECT_PARENTS,
                "title_patterns": MatchMulti.compile(
                    R_AC,
                    MatchMulti.compile(
                        *R_MEETINGS,
                        operator=any,
                    ),
                    operator=all,
                ),
                "neglect_title_patterns": "attend",
                "cell_pattern": R_ED_REVIEWED_FINANCIAL_REPORT,
            },
            {
                "name": "para_match",
                "remove_blank": True,
                "multi_elements": True,
                "syllabus_regs": R_AC_CHAPTERS,
                "ignore_pattern": [P_NEED_IGNORE],
                # "parent_features": [
                #     r"^CORPORATE\s*GOVERNANCE\s*REPORT$",
                #     r"^(CORPORATE\s*GOVERNANCE\s*REPORT\s*)?企業管治報告書?$",
                # ],
                "neglect_parent_features": R_NEGLECT_PARENTS,
                "need_default_follows": True,
                "as_follow_pattern": NeglectPattern.compile(
                    match=MatchMulti.compile(
                        R_AC,
                        # r"during",
                        MatchMulti.compile(
                            *R_MEETINGS,
                            r"set\s*out\s*below",
                            rf"At\s*such\s*meeting,?{R_AC}",
                            r"[:：,]$",
                            operator=any,
                        ),
                        operator=all,
                    ),
                    unmatch=r"attend",
                ),
                "neglect_pattern": P_ED_I_NEGLECT,
            },
            {
                "name": "para_match",
                "remove_blank": True,
                "multi_elements": True,
                "syllabus_regs": R_AC_CHAPTERS
                + [
                    r"^\d\.\s?audit\s*Committee$",
                    r"^AUDIT\s*COMMITTEE\s*AND\s*ACCOUNTABILITY",
                ],
                "neglect_parent_features": R_NEGLECT_PARENTS,
                "paragraph_pattern": P_ED_I_KEYWORDS,
                "neglect_pattern": P_ED_I_NEGLECT,
            },
            {
                "name": "kmeans_classification",
                "remove_blank": True,
                "filter_low_score_threshold": 0.1,
                "neglect_parent_features": R_NEGLECT_PARENTS,
                "para_pattern": P_ED_I_KEYWORDS,
                "neglect_pattern": P_ED_I_NEGLECT,
            },
            {
                "name": "kmeans_classification",
                "remove_blank": True,
                # "filter_low_score_threshold": 0.1,
                "neglect_pattern": P_ED_I_NEGLECT,
            },
        ],
    },
    {
        # alias: E(d)(i)-2 - Review of risk management and internal control
        "path": ["E(d)(i)-1-Review of risk management and internal control system"],
        "default_enum_value": CGEnum.ND.value,
        "models": [
            # 模型1~7：找当年工作内容中包含关键词的段落
            *get_work_done(R_AC, R_RISK_CONTROL, neg_syllabus_regs=[P_CHAPTER_RISK]),
            # 模型8：取高分句子，但必须包含关键词
            {
                "name": "score_filter",
                "filter_primary_role_elements": True,
                "neglect_parent_features": R_JURA_CG_IGNORE_CHAPTER,
                "page_first_as_parent_syllabus": True,
                "parent_must_be_root": True,
                "neglect_syllabus_regs": P_CHAPTER_RISK,
                "threshold": 0.618,
                "pattern": R_RISK_CONTROL,
            },
            # 模型9：kmeans，限制AC章节+关键词
            {
                "name": "kmeans_classification",
                "filter_low_score_threshold": 0.1,
                "neglect_parent_features": R_JURA_CG_IGNORE_CHAPTER,
                "page_first_as_parent_syllabus": True,
                "parent_must_be_root": True,
                "neglect_syllabus_regs": P_CHAPTER_RISK,
                "syllabus_regs": R_AC,
                "skip_syllabus_title": True,
                "para_pattern": R_RISK_CONTROL,
                "neglect_pattern": [R_FOLLOW_PREFIX, R_DUTIES, "will"],
            },
        ],
    },
    {
        # alias: E(d)(i)-3 - Effectiveness of internal audit
        "path": ["E(d)(i)-2-Effectiveness of internal audit function"],
        "models": [
            # 模型1. AC章节下：公司没有internal audit function
            {
                "name": "para_match",
                **get_root_chapter_config(syllabus_regs=R_AC, neg_syllabus_regs=[P_CHAPTER_RISK]),
                "paragraph_pattern": R_NO_INTERNAL_AUDIT,
            },
            # 模型2~8
            *get_work_done(R_AC, R_INTERNAL_AUDIT, neg_syllabus_regs=[P_CHAPTER_RISK]),
            # 模型9. kmeans过滤，限制关键词internal audit
            {
                "name": "kmeans_classification",
                **get_root_chapter_config(neg_syllabus_regs=[P_CHAPTER_RISK]),
                "filter_low_score_threshold": 0.3,
                "skip_syllabus_title": True,
                "para_pattern": R_INTERNAL_AUDIT,
                "neglect_pattern": [R_FOLLOW_PREFIX, R_AS_FOLLOW_END],  # 小序号开头的一般是描述职责
            },
        ],
    },
    {
        # alias: E(d)(i)-4 - Non-compliance with AC composition
        # 如果没有明确描述遵守3.21规则的
        # 框选内容为AC的董事成员组成，需要框选董事类型以及董事姓名，常规位置披露在audit committee下的文段描述
        # 如果没有则可以框选会议出席人数列表，亦或board composition的章节的董事组成情况
        "path": ["E(d)(i)-3-Non-compliance with Rule 3.21 and remedial steps"],
        "default_enum_value": CGEnum.NA.value,
        "models": [
            # 第一个
            {
                "name": "para_match",
                "syllabus_regs": [
                    r"audit Committee",
                    r"Audit and Compliance Committee",
                    r"BOARD COMMITTEES",
                ],
                "paragraph_pattern": R_E_DI_3,
            },
            # 第二个
            {
                "name": "special_cells",
                "cell_pattern": [
                    r"failed to fulfil.*Rule 3.21",
                ],
            },
            # 第三个
            {
                "name": "syllabus_based",
                "multi": True,
                "only_inject_features": True,
                "ignore_pattern": [P_NEED_IGNORE],
                "neglect_parent_features": R_JURA_CG_IGNORE_CHAPTER,
                "inject_syllabus_features": [
                    r"__regex__^COMPOSITION AND ATTENDANCE",
                    r"__regex__^AUDIT COMMITTEE$",
                    r"__regex__^\d\s*?AUDIT COMMITTEE$",
                    r"__regex__^Audit Committee Report$",
                    r"__regex__AUDIT AND COMPLIANCE COMMITTEE",
                    r"__regex__AUDIT AND RISK MANAGEMENT COMMITTEE",
                    # r"__regex__^BOARD COMMITTEES",  # 拆出去重新加一段配置
                ],
                "as_follow_pattern": [
                    r"composition.*?Audit Committee.*?as follows:$",
                    r"non-executive Directors.*?as follows:$" r" below:$",
                    r"being independent non-executive Directors:$",
                    r"attendance of members of the Audit Committee.*set out in the following table:$",
                ],
                "special_as_follow_start": [
                    re.compile(R_APPELLATION),
                    r"^Independent non-executive Directors$",
                    r"^\(app",
                    r"^\([1-4]\)",
                    r"^Notes:$",
                ],
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"complied with Rule 5.28",
                        r"composition.*?Audit Committee.*?as follows:$",
                        r"The Audit Committee comprises.*?members",
                        r"Audit.*?Committee.*?(comprised|consists?) of",
                        r"The Audit Committee.*?comprises the following",
                        r"The members of the Audit Committee",
                        r"All the Committee members are Independent",
                        r"^Non-executive Directors of the Company",
                        r"non-executive Directors.*?as follows:$",
                        r"attendance of members of the Audit Committee.*set out in the following table:$",
                        PositionPattern.compile(R_AC, R_COMPOSE, r"Non-executive Directors"),
                    ),
                    # "neglect_pattern": [],
                    "neglect_below_pattern": (
                        r"^(CORPORATE GOVERNANCE REPORT )?企業管治報告書?$",
                        r"^Corporate Governance Report$",
                        r"^BOARD COMMITTEES \(Continued\)$",
                        r"^\(?[iv]+\)?\sAudit Committee \(Continued\)$",
                        r"The current members of the Nomination Committee",
                    ),
                },
                "table_model": "special_cells",
                "table_config": {
                    "whole_table": True,
                    "cell_pattern": [
                        r"Members of the Audit Committee",
                        r"name of member",
                        r"Audit Committee Members",
                        r"^Audit Committee$",
                        r"Independent Non-executive Director",
                        r"Times of meetings should attend",
                        r"NUMBER OF MEETINGS ATTENDED",
                    ],
                },
            },
            # 第四个
            {
                "name": "special_cells",
                "whole_table": True,
                "cell_pattern": [
                    r"Audit Committee Members",
                ],
            },
            # 第五个
            {
                "name": "para_match",
                "threshold": 0.618,
                "multi_elements": True,
                "paragraph_pattern": (
                    MatchMulti.compile(
                        PositionPattern.compile(R_AC, R_COMPOSE, R_APPELLATION),
                        PositionPattern.compile(R_COMPOSE, r"(three|3)"),
                        operator=any,
                    ),
                    r"complied with Rule 3.21",
                    r"Pursuant to Rule 3.21 of the Listing Rules.*confirm that we had complied with such requirements",
                ),
                "neglect_pattern": (r"nomination committee",),
            },
            # 第六个
            {
                "name": "kmeans_classification",
                "para_pattern": [
                    MatchMulti.compile(
                        MatchMulti.compile(
                            r"(comprise[sd]|consists?) of",
                            r"comprised (three|3)",
                            re.compile(R_APPELLATION),
                            operator=any,
                        ),
                        r"executive Director|INED",
                        r"chairman|chaired by",
                        operator=all,
                    ),
                    MatchMulti.compile(
                        r"executive Director|INED",
                        r"chairman|chaired by",
                        r"Members",
                        operator=all,
                    ),
                    MatchMulti.compile(r"Meetings attended.*Eligible to attend", operator=any),
                ],
                "neglect_pattern": [
                    r"nomination committee",
                ],
            },
            # 第七个
            {
                "name": "yoda_layer",
                "threshold": 1,
                "rule": Operator.union(
                    CGChapterLocator(
                        min_page=5,
                        strict=StrictMode.ROOT,
                        pattern=MATCH_ALWAYS,
                        dest=ParaFromPos(
                            pattern=MatchMulti.compile(
                                rf"members.*{R_AC}.*INEDs.*?professional.*",
                                r"Pursuant to Rule 3.21 of the Listing Rules.*confirm that we had complied with such requirements",
                                r"complied with Rule 3.21",
                                r"Meetings attended.*Eligible to attend",
                                operator=any,
                            ),
                        ),
                    ),
                    CGChapterLocator(
                        min_page=5,
                        strict=StrictMode.ROOT,
                        pattern=MATCH_ALWAYS,
                        dest=Reference(
                            path="AC-E(b)-Composition",
                        ),
                    ).link_next(
                        # https://jura-uat.paodingai.com/#/hkex/cg-report-checking/report-review/251344?fileId=80170&schemaId=28&rule=E%28d%29%28i%29-3-Non-compliance%20with%20Rule%203.21%20and%20remedial%20steps&delist=0
                        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/3965#note_466542
                        # NOTE 记录：经与客户讨论确定，针对规则33，若AC成员涉及到其专业技能和背景的描述没在AC标题下，暂无需提取，待遇到更多示例后再处理
                        ParaFromPos(
                            start=0,
                            limit=300,
                            pattern=MatchMulti.compile(
                                r"possesses the appropriate professional qualifications",
                                r"accounting or related financial management expertise",
                                operator=any,
                            ),
                        ),
                    ),
                ),
            },
            # 第八个
            {
                "name": "reference",
                "from_path": "B(a)-Board Composition by category of directors",
            },
        ],
    },
]
