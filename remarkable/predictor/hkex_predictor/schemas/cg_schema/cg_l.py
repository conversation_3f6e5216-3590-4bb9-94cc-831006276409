import re

from remarkable.common.common_pattern import P_CONTINUED, R_CG_CHAPTER_TITLES
from remarkable.common.pattern import <PERSON><PERSON>ult<PERSON>
from remarkable.pdfinsight.interdoc_reader import Para, Table
from remarkable.pdfinsight.reader import Pd<PERSON>sightReader
from remarkable.predictor.hkex_predictor.models.yoda_layer.filter import <PERSON><PERSON><PERSON><PERSON>, ParaLensFilter, ParaTextFilter
from remarkable.predictor.hkex_predictor.models.yoda_layer.operator import Operator
from remarkable.predictor.hkex_predictor.models.yoda_layer.rule.by_pos import (
    ContinuedElem,
    SentenceFromSplitPara,
)
from remarkable.predictor.hkex_predictor.models.yoda_layer.rule.root import (
    CGCascadeChapterLocator,
    CGChapterLocator,
    ScoreDestFilter,
    ScoreParaFilter,
)
from remarkable.predictor.hkex_predictor.pattern import R_BE
from remarkable.predictor.hkex_predictor.schemas.pattern import P_NEED_IGNORE

R_L_A_CHAPTERS = [r"constitution(al)?\s*document", r"BYE-LAWS", r"articles\s*of\s*association$"]
P_CHANGE = MatchMulti.compile(r"change|amend", operator=any)
R_L_A_COMPLY = MatchMulti.compile(P_CHANGE, r"constitution|bye-laws|articles\s*of\s*association", operator=all)

# 规则L(b)：第一优先级的章节
R_L_B_CHAPTERS = [
    r"COMMUNICATIONS\s*WITH\s*SHAREHOLDERS\s*AND\s*INVESTORS\s*RELATIONS",
    r"communications?\s*policy",
    r"communications?\s*with\s*(the\s*)?((stake|share)holder|investor)",
    r"(Shareholder|investor)s?['‘’]?\s*communication",
    r"investors?\s*relations\s*and\s*communication",
    r"communications?\s*of\s*((stake|share)holder|investor)",
]
# 规则(b)：跳过跨页段落
P_L_B_SKIP_PATTERN = MatchMulti.compile(
    CGCascadeChapterLocator.chapter_pattern,
    r"^SHAREHOLDERS?['‘’]?\s*RIGHTS$",
    r"^CORPORATE$",
    r"^GOVERNANCE\s*REPORT$",
    r"^A N N U A L R E P O R T 2022$",
    r"^ANNUAL\s*REPORT\s*2022$",
    r"[-（(]?continued[\)）]?$",
    operator=any,
)
L_B_SKIP_FILTER = ParaTextFilter(pattern=P_L_B_SKIP_PATTERN) | ParaLensFilter(0, 8)

# 规则L(c)：限制章节
R_L_C_CHAPTERS = [r"communication", r"(share|stake)holder"]
# 规则L(c)：Comply
P_L_C_COMPLY = MatchMulti.compile(
    MatchMulti.compile(r"communication", rf"{R_BE}\s*(effective|satisf)", operator=all),
    MatchMulti.compile(
        r"considered|expressed|reviewing|(?<!willbe)(?<!regularly)reviewed"
        + rf"|conducted|achieved|{R_BE}\s*subject\s*to\s*review|{R_BE}\s*of\s*(the\s*)?view",
        r"effective|satisf|implement",
        # rf"(throughout|during|in|at)\s*{reg_words(0,3)}(year|(FY)?20\d{{2}}|period)",
        operator=all,
    ),
    operator=any,
)


def l_a_break(pdfinsight: PdfinsightReader, element: dict) -> bool:
    return pdfinsight.is_syllabus_title(element)


predictor_options = [
    {
        "path": ["L(a)-Significant changes in constitutional documents"],
        "models": [
            # 1. 基于初步定位结果找章节
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": [P_NEED_IGNORE],
                "include_title": True,
                "only_inject_features": True,
                "break_function": l_a_break,
                "inject_syllabus_features": [rf"__regex__{chapter}" for chapter in R_L_A_CHAPTERS],
                "enum_pattern": r"change|amend",
            },
            # 2. 关键词过滤
            {
                "name": "para_match",
                "threshold": 0.1,
                "multi_elements": True,
                # "paragraph_pattern": (r"changes.*?articles of association".replace(" ", R_SPACE),),
                "paragraph_pattern": R_L_A_COMPLY,
            },
            # 3. 分值过滤，不要章节标题
            {
                "name": "score_filter",
                "threshold": 0.5,
                "multi_elements": True,
                "skip_syllabus_title": True,
            },
            # 4： kmeans保底，不要章节标题
            {
                "name": "kmeans_classification",
                "multi_elements": True,
                "filter_low_score_threshold": 0.1,
                "skip_syllabus_title": True,
                "para_pattern": R_L_A_COMPLY,
            },
        ],
    },
    {
        "path": ["L(b)-Shareholders' communication policy"],
        "models": [
            # 模型1：适配文件66209,66258
            {
                "name": "syllabus_elt_v2",
                "include_title": True,
                "only_inject_features": True,
                "parent_features": CGChapterLocator.chapter_pattern,
                "ignore_pattern": [P_L_B_SKIP_PATTERN] + [P_NEED_IGNORE],
                "inject_syllabus_features": [r"__regex__^SHAREHOLDERS['‘’]\s*COMMUNICATION\s*POLICY$"],
            },
            # 模型2：取COMMUNICATION policy、COMMUNICATION WITH SHAREHOLDERS章节数据，要章节标题
            {
                "name": "yoda_layer",
                "multi_elements": True,
                "threshold": 0.618,
                "rule": Operator.join(
                    CGCascadeChapterLocator(
                        chapter_patterns=[MatchMulti.compile(*R_L_B_CHAPTERS, operator=any)],
                        remove_blank=True,
                        dest=ContinuedElem[Para | Table](
                            stop=ChapterFilter(skip_pattern=P_CONTINUED),
                            skip=L_B_SKIP_FILTER
                            | ParaTextFilter(pattern=MatchMulti.compile(*R_CG_CHAPTER_TITLES, operator=any)),
                            limit=100,
                            size=2,
                            start=0,
                        ),
                    ),
                    ScoreParaFilter(),
                ),
            },
            # 模型3：适配文档62536
            {
                "name": "yoda_layer",
                "multi_elements": True,
                "threshold": 1,
                "rule": CGCascadeChapterLocator(
                    chapter_patterns=[
                        MatchMulti.compile(
                            r"^COMMUNICATION\s*WITH\s*SHAREHOLDERS/INVESTOR\s*RELATIONS", flag=0, operator=any
                        )
                    ],
                    # only_chapter=False表示把段落当做章节来匹配
                    only_chapter=False,
                    remove_blank=True,
                    dest=ContinuedElem[Para](
                        skip=ParaTextFilter(pattern=MatchMulti.compile(*R_CG_CHAPTER_TITLES, operator=any)),
                        stop=ParaTextFilter(
                            pattern=MatchMulti.compile(r"^SHAREHOLDER['‘’]?S['‘’]?\s*RIGHT$", operator=any)
                        ),
                        limit=10,
                        size=2,
                        start=0,
                    ),
                ),
            },
            # 模型4：用syllabus_elt_v2再取一次，要章节标题
            {
                "name": "syllabus_elt_v2",
                "include_title": True,
                "only_inject_features": True,
                "parent_features": CGChapterLocator.chapter_pattern,
                "ignore_syllabus_pattern": [r"^dividend\s*policy$"],
                "ignore_pattern": [P_L_B_SKIP_PATTERN] + [P_NEED_IGNORE],
                "inject_syllabus_features": [rf"__regex__{chapter}" for chapter in R_L_B_CHAPTERS],
            },
            # 模型5：适配文档67299
            {
                "name": "yoda_layer",
                "multi_elements": True,
                "threshold": 1,
                "rule": CGCascadeChapterLocator(
                    chapter_patterns=[
                        re.compile(r"INVESTOR\s*RELATIONS?$", re.I),
                        re.compile(r"investor\s*relations\s*management", re.I),
                    ],
                    dest=ContinuedElem[Para](
                        stop=ChapterFilter(skip_pattern=P_CONTINUED),
                        skip=L_B_SKIP_FILTER
                        | ParaTextFilter(pattern=MatchMulti.compile(*R_CG_CHAPTER_TITLES, operator=any)),
                        limit=20,
                        size=2,
                        start=1,
                    ),
                ),
            },
            # 模型6：取INVESTOR RELATIONS章节数据和预测结果的交集，不要章节标题
            {
                "name": "yoda_layer",
                "multi_elements": True,
                "threshold": 0.08,
                "rule": Operator.intersection(
                    Operator.union(
                        CGCascadeChapterLocator(
                            chapter_patterns=[
                                MatchMulti.compile(
                                    r"INVESTOR.*RELATIONS?", r"RELATIONS?\s*WITH\s*SHAREHOLDERS", operator=any
                                )
                            ],
                            dest=ContinuedElem[Para](
                                stop=ChapterFilter(skip_pattern=P_CONTINUED),
                                skip=L_B_SKIP_FILTER
                                | ParaTextFilter(pattern=MatchMulti.compile(*R_CG_CHAPTER_TITLES, operator=any)),
                                limit=20,
                                size=2,
                                start=1,
                            ),
                        ),
                        CGCascadeChapterLocator(
                            chapter_patterns=[
                                MatchMulti.compile(
                                    r"^SHAREHOLDERS?\s*RELATIONS?$",
                                    r"General\s*Meetings\s*and\s*Shareholder’s\s*Rights$",
                                    r"^4.\s*investor\s*relations\s*management",
                                    operator=any,
                                )
                            ],
                            dest=ContinuedElem[Para](
                                stop=ChapterFilter(skip_pattern=P_CONTINUED),
                                skip=L_B_SKIP_FILTER
                                | ParaTextFilter(pattern=MatchMulti.compile(*R_CG_CHAPTER_TITLES, operator=any)),
                                limit=20,
                                size=2,
                                start=1,
                            ),
                        ),
                    ),
                    ScoreParaFilter(),
                ),
            },
            # 模型7：不限制章节，取所有高分段落，不要章节标题
            {
                "name": "score_filter",
                "threshold": 0.618,
                "multi_elements": True,
                "skip_syllabus_title": True,
            },
            # 模型8： kmeans保底，不要章节标题
            {
                "name": "kmeans_classification",
                "multi_elements": True,
                "filter_low_score_threshold": 0.1,
                "skip_syllabus_title": True,
            },
            # 模型9：适配文档67305，纽交所的文件，没有上述章节，用关键词匹配
            {
                "name": "score_filter",
                "threshold": 0.003,
                "multi_elements": True,
                "skip_syllabus_title": True,
                "pattern": MatchMulti.compile(r"communication", r"(stake|share)holder", operator=any),
            },
        ],
    },
    {
        "path": ["L(c)-Annual review on effectiveness of communication policy"],
        "models": [
            # 模型1：如果存在标题review\s*of.*communication，直接取整个章节
            {
                "name": "syllabus_elt_v2",
                "include_title": True,
                "only_inject_features": True,
                # "only_first": True,
                "parent_features": CGChapterLocator.chapter_pattern,
                "syllabus_regs": R_L_C_CHAPTERS,
                "inject_syllabus_features": [r"__regex__review\s*of.*communication"],
                "ignore_pattern": [P_NEED_IGNORE],
            },
            # 模型2：communication相关章节下，不限制分值匹配句子
            {
                "name": "yoda_layer",
                "threshold": 0.01,
                "syllabus_regs": R_L_C_CHAPTERS,
                "multi_elements": True,
                "rule": ScoreDestFilter(
                    multi=True,
                    dest=SentenceFromSplitPara(
                        ordered_patterns=[P_L_C_COMPLY],
                        multi=True,
                        remove_blank=True,
                    ),
                ),
            },
            # 模型3：不限制章节，限制分值匹配句子
            {
                "name": "yoda_layer",
                "threshold": 0.1,
                "multi_elements": True,
                "rule": ScoreDestFilter(
                    multi=True,
                    dest=SentenceFromSplitPara(
                        ordered_patterns=[P_L_C_COMPLY],
                        multi=True,
                        remove_blank=True,
                    ),
                ),
            },
            # 模型4：取高分段落
            {
                "name": "score_filter",
                "threshold": 0.618,
            },
            # 模型5：不限制章节，不限制分值，限制段落关键字communication匹配句子
            {
                "name": "yoda_layer",
                "threshold": 0.01,
                "multi_elements": True,
                "rule": ScoreDestFilter(
                    pattern=re.compile(r"communication"),
                    multi=True,
                    dest=SentenceFromSplitPara(
                        ordered_patterns=[P_L_C_COMPLY],
                        multi=True,
                        remove_blank=True,
                    ),
                ),
            },
            # 模型6：保底模型
            {
                "name": "kmeans_classification",
                "syllabus_regs": R_L_C_CHAPTERS,
                "filter_low_score_threshold": 0.5,
            },
        ],
    },
]
