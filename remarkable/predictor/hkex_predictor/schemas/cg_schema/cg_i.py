import logging
import re

from remarkable.common.common import is_table_elt
from remarkable.common.common_pattern import P_SEN_SEPARATOR, R_MIDDLE_DASH
from remarkable.common.constants import CGEnum, TableType
from remarkable.common.pattern import (
    MatchMulti,
    NeglectPattern,
    PositionPattern,
    SplitBeforeMatch,
)
from remarkable.common.util import clean_txt
from remarkable.pdfinsight.parser import parse_table
from remarkable.pdfinsight.reader import PdfinsightSyllabus
from remarkable.predictor.hkex_predictor.pattern import R_BE
from remarkable.predictor.hkex_predictor.schemas.cg_schema.utils import add_follow_table
from remarkable.predictor.hkex_predictor.schemas.pattern import P_NEED_IGNORE, R_SPACE, reg_words
from remarkable.predictor.models.base_model import BaseModel
from remarkable.predictor.models.para_match import AsFollowType
from remarkable.predictor.schema_answer import OutlineResult, PredictorResult, TableResult

logger = logging.getLogger(__name__)

P_NOAUDIT_KEY = re.compile(r"non-?\s*audit", re.I)
P_NEG_TOTAL = NeglectPattern.compile(match=re.compile(r"\w{5,}"), unmatch=re.compile(r"\btotal\b", re.I))
P_NUM = re.compile("[1-9]")
P_MIDDLE_DASH_NUM = NeglectPattern(
    match=MatchMulti.compile(rf"{R_MIDDLE_DASH}\t\d+", operator=any),
    unmatch=MatchMulti.compile(rf"\d+\t{R_MIDDLE_DASH}\t\d+", operator=any),
)

R_NOAUDIT = r"non-?(\s*statutory)?\s*audit(ing)?\s*(services?|fees?|matters?)"
P_NOAUDIT = re.compile(R_NOAUDIT, re.I)

# 非审计服务明细内容
P_NOAUDIT_SERVICES_OTHER = MatchMulti.compile(
    r"Others?\b",
    operator=any,
)
P_NOAUDIT_SERVICES = MatchMulti.compile(
    r"risk\s*management\s*review",
    r"ESG\s*consulting",
    r"tax(ation)?.*(advisory|service|compliance|management|consultation)",
    MatchMulti.compile(r"advisory", r"\btax", operator=all),
    r"(for|including) tax (Consultation|consulting)",
    r"interim\s*or\s*quarterly\s*results\s*review",
    r"Review of interim.*financial statements",
    r"Review.*interim.*financial (statements|information)",
    r"interim financial statements review services",
    r"[-—－–] interim report",
    rf"{R_NOAUDIT}.*interim (financial )?review",
    PositionPattern.compile(R_NOAUDIT, r"interim", r"review|financial\s*information"),
    r"(Consultation|consulting) services",
    r"remuneration paid to reporting accountant",
    r"billed for services rendered by our principal auditors",
    r"review\s*of\s*continuing\s*connected\s*transaction",
    r"\bORSO\b\s*audit",
    r"unaudited\s*proforma\s*financial\s*information",
    r"internal\s*control\s*review",
    PositionPattern.compile(
        r"Internal control|Transaction",
        r"\bservices?\b",
    ),
    r"^Internal control auditor$",
    r"Valuation report and due diligence",
    rf"other\s*(assurance\s*service|{R_NOAUDIT})",
    operator=any,
)

# 以下项目可以认为是确定的 `non-audit service` 内容 可以不结合 `non-audit` 关键字独立判断
P_CERTAIN_NOAUDIT_SERVICES = MatchMulti.compile(
    r"internal\s*control",
    r"risk\s*management\s*review",
    r"agreed.*upon\s*procedure",
    r"\bESG\b\s*consulting",
    r"(advisory|review)\s?service",
    MatchMulti.compile(r"\btax", "advisory|service|compliance|report|expense|advice", operator=all),
    MatchMulti.compile("interim", "review|service|information", operator=all),
    rf"non-?\s*statutory\s*audit\s*services?\s*{reg_words(0, 2)}advisory",
    r"audit\s*services?\s*and\s*other\s*assurance\s*services?",
    r"((constitute|continuing) connected transaction|\bCCT\b)",
    r"Transfer of Listing",
    r"(Audit-Related|other) Fees",
    operator=any,
)

# 非审计服务包含
P_NOAUDIT_SERVICE_INCLUDE = MatchMulti.compile(
    # 非审计服务包含xxx
    PositionPattern.compile(
        R_NOAUDIT,
        r"\b(includ|in\s*connection\s*with|compris|consist|compris|made\s*up|compos|construct|assembl)",
    ),
    # 以下是非审计服务的详情，有可能会有别的描述
    PositionPattern.compile(
        R_NOAUDIT,
        r"listed below",
    ),
    PositionPattern.compile(
        R_NOAUDIT,
        r"(related|relation|relating) to|mainly for|for reviewing|review of|\brepresented\b",
    ),
    # PositionPattern.compile(
    #     r"fees? for",
    #     R_NOAUDIT,
    # ),
    # 除了审计服务 还提供了...
    PositionPattern.compile(
        r"(Apart\s*from|Save\s*for|save\s*as).*?audit\s*service",
        rf"also\s*{reg_words(0, 3)}provide",
    ),
    # 包含 no audit service和其中一个子项 默认认为其数值非0， 如果是0的话就不必专门写出子项
    PositionPattern.compile(
        R_NOAUDIT,
        r"[-—－–]",
        r"Other services|advisory",
    ),
    operator=any,
)

# TODO: 获取实际的NOTE表格
# http://100.64.0.105:55647/#/project/remark/244709?treeId=2422&fileId=66907&schemaId=28&projectId=2422&schemaKey=I%28b%29-Details%20of%20nature%20of%20significant%20non
P_IB_NOTE = MatchMulti.compile(rf"{R_NOAUDIT}.*set out in note \d\(\w\)", operator=any)

P_COMPLY_IB_PARA = MatchMulti.compile(
    P_NOAUDIT_SERVICES,
    # P_NOAUDIT_SERVICES_OTHER,
    P_NOAUDIT_SERVICE_INCLUDE,
    P_IB_NOTE,
    operator=any,
)
R_ZERO = r"(Nil|0|zero|o)(?!\.\d)\b"
R_IB_NA = [
    rf"audit (services )?and non-?audit (service|fee)s?.*?(are|were|amounted|provided).*?and(\sRMB|\sHK\$)?\s?{R_ZERO}",
    rf"the provision of non-audit services is (RMB|HK$)\s?{R_ZERO}",
    rf"no fees were payable to.*{R_NOAUDIT}",
    rf"there was no other {R_NOAUDIT} provided by",
    rf"and\s*(RMB|HK\$)?\s*{R_ZERO}.*?for\s*{reg_words(0, 3)}\s*audit\s*(services? )?and\s*{R_NOAUDIT}",
    rf"audit and non-?audit services? (are|were).*?and\s{R_ZERO}",
    rf"Save\s*for\s*{reg_words(0, 3)}audit\s*services.*?no\s*other\s*service",
    rf"\b{R_ZERO}\s*for\s*{R_NOAUDIT}",
    rf"\bno\s*{reg_words(0, 2)}{R_NOAUDIT}\s*{reg_words(0, 2)}provide",
    rf"\bno\s*(remuneration|fees?)\s*{reg_words(0, 5)}(for|in\s*respect\s*of)\s*{R_NOAUDIT}",
    rf"\bno\s*{R_NOAUDIT}.*?(pay|paid)",
    rf"\bnot\s*engaged?\s*{reg_words(0, 5)}{R_NOAUDIT}",
    rf"\bno\s*(paid|pay|fee|remuneration).*?(for|in\s*respect\s*of)\s*{reg_words(0, 3)}{R_NOAUDIT}",
    SplitBeforeMatch(MatchMulti.compile(rf"{R_NOAUDIT}.*?{R_BE}\s*{R_ZERO}", operator=any), P_SEN_SEPARATOR),
    rf"\bnot\s*provide\s*any\s*{reg_words(0, 3)}{R_NOAUDIT}",
    rf"did not (conduct|perform|render) any {R_NOAUDIT}",
    rf"{R_NOAUDIT} (was|were) (render|conduct|perform)ed by.*during the year",
]


def post_process_ib(answers, **kwargs):
    predictor = kwargs["predictor"]
    elements = BaseModel.get_elements_from_answer_result(answers)
    enum_values = BaseModel.get_enums_from_answer_result(answers)
    table_elements = [ele for ele in elements if is_table_elt(ele)]
    new_answer = []
    if not table_elements:
        return answers
    noaudit_row_idx, noaudit_cell_idx = None, None
    for table_ele in table_elements:
        table = parse_table(table_ele, tabletype=TableType.TUPLE, pdfinsight_reader=predictor.pdfinsight)
        # 1. 判断是否在表格中披露了 non-audit service的明细
        # http://100.64.0.105:55647/#/project/remark/244249?treeId=11891&fileId=67367&schemaId=28&projectId=11891&schemaKey=I%28b%29-Details
        for row_idx, row in enumerate(table.rows):
            row_text = "\t".join(cell.text for cell in row)
            if (
                P_NOAUDIT_SERVICE_INCLUDE.search(row_text)
                or P_CERTAIN_NOAUDIT_SERVICES.search(row_text)
                or P_NOAUDIT_SERVICES.search(row_text)
            ) and P_NUM.search(row_text):
                answer_value = CGEnum.C
                if P_MIDDLE_DASH_NUM.search(row_text):
                    answer_value = CGEnum.NA
                new_answer.append(
                    PredictorResult(
                        [TableResult(table_ele, parsed_table=table)], value=answer_value, schema=predictor.schema
                    )
                )
                break
            if not noaudit_row_idx:
                for cell_idx, cell in enumerate(row):
                    if P_NOAUDIT_KEY.search(cell.text):
                        noaudit_row_idx = row_idx
                        noaudit_cell_idx = cell_idx
        if noaudit_row_idx is not None and noaudit_cell_idx is not None and not new_answer:
            for below_row in table.rows[noaudit_row_idx + 1 :]:
                if below_row[noaudit_cell_idx].dummy:
                    continue
                if P_NEG_TOTAL.search(below_row[noaudit_cell_idx].text) and any(
                    P_NUM.search(cell.text) for cell in below_row[noaudit_cell_idx + 1 :]
                ):
                    new_answer.append(
                        PredictorResult(
                            [TableResult(table_ele, parsed_table=table)], value=CGEnum.C, schema=predictor.schema
                        )
                    )
                    break
            # 如果表格中没找到明细且非审计服务费用为0 则确定为NA
            if not new_answer and not any(
                P_NUM.search(cell.text) for cell in table.rows[noaudit_row_idx][noaudit_cell_idx + 1 :]
            ):
                new_answer.append(
                    PredictorResult(
                        [TableResult(table_ele, parsed_table=table)], value=CGEnum.NA, schema=predictor.schema
                    )
                )
        # 2. 判断是否在表格附近披露了 non-audit service的明细
        if not new_answer:
            extra_ele = None
            prev_elements = predictor.pdfinsight.find_elements_near_by(
                table_ele["index"], step=-1, amount=3, aim_types=["PARAGRAPH"]
            )
            # 明细在表格前段落中 http://100.64.0.105:55647/#/project/remark/244250?treeId=22437&fileId=67366&schemaId=28&projectId=22437&schemaKey=I%28b%29-Details
            for prev_ele in prev_elements:
                if P_COMPLY_IB_PARA.search(clean_txt(prev_ele.get("text", ""))):
                    extra_ele = prev_ele
                    break
            # 明细在脚注中 http://100.64.0.105:55647/#/project/remark/231464?treeId=43974&fileId=66198&schemaId=28&projectId=43974&schemaKey=I%28b%29-Details
            if not extra_ele:
                possible_footnotes = []

                for item in table.footnotes + predictor.pdfinsight.find_elements_near_by(
                    table_ele["index"], step=1, amount=3, aim_types=["PARAGRAPH"]
                ):
                    if predictor.pdfinsight.syllabus_reader.is_syllabus_elt(item):
                        break
                    possible_footnotes.append(item)
                possible_footnotes_indices = [foot_note["index"] for foot_note in possible_footnotes]
                for foot_note in table.footnotes:
                    if foot_note["index"] not in possible_footnotes_indices:
                        possible_footnotes.append(foot_note)
                for foot_note in possible_footnotes:
                    if P_COMPLY_IB_PARA.search(foot_note.get("text", "")):
                        extra_ele = foot_note
                        break
            if extra_ele:
                all_elements = [table_ele, extra_ele]
                page_box = PdfinsightSyllabus.elements_outline(all_elements)
                new_answer.append(
                    PredictorResult(
                        [OutlineResult(page_box=page_box, element=table_ele, origin_elements=all_elements)],
                        value=CGEnum.C,
                        schema=predictor.schema,
                    )
                )

        if not new_answer:
            # NA的答案在后处理里不再修改成ND
            answer_value = CGEnum.NA.value if CGEnum.NA.value in enum_values else CGEnum.ND.value
            new_answer.append(
                PredictorResult(
                    [TableResult(table_ele, parsed_table=table)], value=answer_value, schema=predictor.schema
                )
            )
    for answer in new_answer:
        logger.debug(f"post_process_ib: {answer.answer_value=}")
    return new_answer


predictor_options = [
    {
        "path": ["I(a)Analysis of remuneration of audit and non-audit services"],
        "post_process": add_follow_table,
        "models": [
            {
                "name": "syllabus_based",
                "only_inject_features": True,
                "ignore_pattern": [P_NEED_IGNORE],
                "inject_syllabus_features": [
                    r"__regex__^AUDITOR’S\s*REMUNERATION$",
                ],
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": [
                        r"non-audit\s*service",
                    ],
                },
                "table_model": "special_cells",
                "table_config": {
                    "whole_table": True,
                    "cell_pattern": [
                        r"audit\s*service",
                        r"Audit and audit related service",
                    ],
                },
            },
            {
                "name": "score_filter",
                "multi_elements": True,
                "aim_types": ["TABLE"],
                "threshold": 0.4,
                "pattern": [
                    r"audit\s*services",
                ],
            },
            {
                "name": "score_filter",
                "multi_elements": True,
                "threshold": 0.618,
            },
            {
                "name": "syllabus_based",
                "only_inject_features": True,
                "ignore_pattern": [P_NEED_IGNORE],
                "inject_syllabus_features": [
                    r"__regex__^Directors’ responsibility for the Financial Statements$".replace(" ", R_SPACE),
                ],
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (r"audit and non audit services".replace(" ", R_SPACE),),
                },
                "table_model": "empty",
            },
            {
                "name": "para_match",
                "multi_elements": True,
                "paragraph_pattern": (
                    r"provision of non-audit services to the company was provided".replace(" ", R_SPACE),
                    r"audit fee payable to".replace(" ", R_SPACE),
                    r"payment in relation to non-auditing services".replace(" ", R_SPACE),
                    r"no non audit services was provided".replace(" ", R_SPACE),
                ),
            },
            {
                "name": "twice_para_match",
                "use_all_elements": False,  # 基于初步定位结果再次过滤，避免结果范围过大
                "syllabus_regs": [],
                "paragraph_pattern": (  # 主要的
                    rf"non-audit{R_SPACE}(advisory{R_SPACE})?(services|service)",  # 非审计
                ),
                "neglect_pattern": (),
                "second_pattern": (
                    rf"audit{R_SPACE}(advisory{R_SPACE})?(services|service)",  # 审计
                ),
            },
            {
                "name": "special_cells",
                "use_all_elements": False,  # 基于初步定位
                "whole_table": True,
                "cell_pattern": (rf"non-audit{R_SPACE}(services|service)",),
            },
        ],
    },
    {
        "path": ["I(b)-Details of nature of significant non-audit services and fees paid"],
        "location_threshold": 0.001,
        "post_process": post_process_ib,
        "models": [
            # 1. 取只有一个高分的答案
            {
                "name": "kmeans_classification",
                "threshold": 0.9,
                "para_pattern": [R_NOAUDIT],
                "keep_high_score_threshold": True,
                "high_score_elements_count": 1,
            },
            # 2.处理段落中C的情况
            {
                "name": "para_match",
                "threshold": 0.5,
                "enum": CGEnum.C.value,
                "paragraph_pattern": [
                    NeglectPattern.compile(
                        match=MatchMulti.compile(
                            P_NOAUDIT_SERVICE_INCLUDE,
                            MatchMulti.compile(
                                PositionPattern.compile(
                                    R_NOAUDIT,
                                    r"related to",
                                ),
                                operator=any,
                            ),
                            SplitBeforeMatch(
                                NeglectPattern.compile(match=P_CERTAIN_NOAUDIT_SERVICES, unmatch=r"\bnot\s*provide"),
                                separator=P_SEN_SEPARATOR,
                            ),
                            operator=any,
                        ),
                        unmatch=MatchMulti.compile(
                            rf"audit (services )?and non-?audit (service|fee)s?.*?(are|were|amounted|provided).*?and(\sRMB|\sHK\$)?\s?{R_ZERO}",
                            operator=any,
                        ),
                    ),
                ],
            },
            # 3
            {
                "name": "syllabus_based",
                "only_inject_features": True,
                "ignore_pattern": [P_NEED_IGNORE],
                "inject_syllabus_features": [
                    r"__regex__^Directors’?\s*responsibility\s*for\s*the\s*Financial\s*Statements$",
                ],
                "paragraph_model": "para_match",
                "para_config": {
                    "as_follow_include_title": True,
                    "as_follow_pattern": (r"details of the significant non audit services.*?as follows",),
                    "paragraph_pattern": (r"professional\s*services\s*rendered",),
                },
                "table_model": "empty",
            },
            # 4
            {
                "name": "syllabus_based",
                "only_inject_features": True,
                "ignore_pattern": [P_NEED_IGNORE],
                "inject_syllabus_features": [
                    r"__regex__Remunerations\s*of\s*auditors$",
                ],
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (r"none\s*of.*?provision\s*of\s*non-audit\s*services",),
                },
                "table_model": "empty",
            },
            # 5 正则只匹配了NA的情况
            {
                "name": "para_match",
                "multi_elements": True,
                "enum": CGEnum.NA.value,
                "paragraph_pattern": (r"did\s*not\s*provide\s*any\s*non-?audit\s*services?",),
                "as_follow_type": AsFollowType.ANY,
                "ignore_pattern": [P_NEED_IGNORE],
                "as_follow_pattern": (r"fees for provision of the non-audit services.*?as follows",),
            },
            # 6 提取表格
            {
                "name": "special_cells",
                "threshold": 0.1,
                "whole_table": True,
                "cell_pattern": MatchMulti.compile(
                    P_NOAUDIT,
                    P_NOAUDIT_SERVICES,
                    r"other\s*assurance\s*service",
                    P_CERTAIN_NOAUDIT_SERVICES,
                    operator=any,
                ),
            },
            # 7 段落中C的情况 (非审计服务包含), 限定需要在 审核薪酬\审核 相关段落
            {
                "name": "para_match",
                "enum": CGEnum.C.value,
                "threshold": 0.1,
                "syllabus_regs": [
                    MatchMulti.compile("audit", "remuneration|committee|engagement", operator=all),
                    r"external\s*audit",
                ],
                "paragraph_pattern": [
                    NeglectPattern.compile(
                        match=MatchMulti.compile(
                            P_NOAUDIT_SERVICE_INCLUDE,
                            SplitBeforeMatch(
                                NeglectPattern.compile(
                                    match=P_CERTAIN_NOAUDIT_SERVICES,
                                    unmatch=MatchMulti.compile(
                                        r"\bnot\s*provide", rf"non-audit services is RMB {R_ZERO}", operator=any
                                    ),
                                ),
                                separator=P_SEN_SEPARATOR,
                            ),
                            operator=any,
                        ),
                        unmatch=MatchMulti.compile(
                            rf"audit (services )?and non-?audit (service|fee)s?.*?(are|were|amounted|provided).*?and(\sRMB|\sHK\$)?\s?{R_ZERO}",
                            operator=any,
                        ),
                    )
                ],
            },
            # 8 处理一部分段落中NA的情况
            {
                "name": "para_match",
                # "threshold": 0.4,
                "enum": CGEnum.NA.value,
                "paragraph_pattern": [
                    NeglectPattern.compile(
                        match=MatchMulti.compile(*R_IB_NA, operator=any),
                        unmatch=MatchMulti.compile(
                            r"audit (services )?and non-?audit (service|fee)s? (during the Year )?(are|were|amounted|provided).*?and(\sRMB|\sHK\$)?\s?\d\.\d+",
                            P_NOAUDIT_SERVICE_INCLUDE,
                            operator=any,
                        ),
                    ),
                    MatchMulti.compile(
                        rf"audit (services )?and non-?audit (service|fee)s?.*?(are|were|amounted|provided).*?and(\sRMB|\sHK\$)?\s?{R_ZERO}",
                        operator=any,
                    ),
                ],
            },
            # 9 分别披露了审计服务和非审计服务的费用 以及详情
            {
                "name": "para_match",
                "enum": CGEnum.C.value,
                "paragraph_pattern": [
                    MatchMulti.compile(
                        r"audit (services )?and non-?audit (service|fee)s?.*?(are|were|amounted|provided).*?and(\sRMB|\sHK\$)?\s?\d\.\d+",
                        PositionPattern.compile(
                            R_NOAUDIT,
                            r"\b(includ|in\s*connection\s*with|compris|consist|compris|made\s*up|compos|construct|assembl)",
                        ),
                        operator=all,
                    ),
                ],
            },
            # # 10 提取表格 没有分数限制 多提取出很多答案先注释掉
            # # http://100.64.0.105:55647/#/project/remark/244311?treeId=44531&fileId=67305&schemaId=28&projectId=44531&schemaKey=I%28b%29-Details%20of%20nature%20of%20significant%20non
            # {
            #     "name": "special_cells",
            #     "whole_table": True,
            #     "cell_pattern": MatchMulti.compile(
            #         P_CERTAIN_NOAUDIT_SERVICES,
            #         operator=any,
            #     ),
            # },
        ],
    },
]
