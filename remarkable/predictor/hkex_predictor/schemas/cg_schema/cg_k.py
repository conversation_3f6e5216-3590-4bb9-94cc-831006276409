import re

from remarkable.common.common_pattern import R_CG_CHAPTER_TITLES
from remarkable.common.constants import CGEnum
from remarkable.common.pattern import (
    MatchMulti,
    NeglectPattern,
    PatternCollection,
    PositionPattern,
    SplitBeforeMatch,
)
from remarkable.pdfinsight.interdoc_reader import Para
from remarkable.predictor.hkex_predictor.models.yoda_layer.filter import ParaTextFilter
from remarkable.predictor.hkex_predictor.models.yoda_layer.operator import Operator
from remarkable.predictor.hkex_predictor.models.yoda_layer.rule.by_pos import (
    ContinuedElem,
    ContinuedParaFromPos,
    ForwardParaMatch,
    PageRangePara,
    ParaFromPos,
)
from remarkable.predictor.hkex_predictor.models.yoda_layer.rule.root import (
    CGChapterLocator,
    CIChapterLocator,
    ScoreLensFilter,
    ScoreParaFilter,
    StrictMode,
)
from remarkable.predictor.hkex_predictor.models.yoda_layer.rule.special import ParaBetweenPageRange
from remarkable.predictor.hkex_predictor.schemas.pattern import IGNORE_CG_CHAPTER_TITLE_FILTER, P_NEED_IGNORE, R_SPACE
from remarkable.predictor.models.para_match import AsFollowType

CONTACT_DETAIL_LOCATOR = CIChapterLocator(
    pattern=MatchMulti.compile(
        R_SPACE.join(["^corporate", "communicat"]),
        R_SPACE.join(["^company.?s", "website"]),
        operator=any,
    ),
    strict=StrictMode.CHAPTER,
    dest=ContinuedElem[Para](start=0, limit=10, skip=IGNORE_CG_CHAPTER_TITLE_FILTER),
)

P_CORPORATE_INFO = PositionPattern.compile(r"section\s*headed", "corporate", "information")
P_ENQUIRE_CHAPTER = NeglectPattern.compile(
    match=MatchMulti.compile(
        r"^shareholders.\s*enquir",
        MatchMulti.compile("procedure", "enquir", "board", operator=all),
        PositionPattern.compile("procedure", "shareholder", "propos"),
        operator=any,
    ),
    unmatch=r"election",
)
R_ENQUIRIES = "[ei]nquiri?es"
R_EGM = r"(SGM|EGM|extraordinary general meetings?)"

P_K_C_NEED_IGNORE = MatchMulti.compile(
    "^At the annual general meeting held by the Company",  # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/4158#note_485750
    operator=any,
)

predictor_options = [
    {
        "path": ["K(a)-How to convene EGM"],
        "models": [
            {
                "name": "syllabus_elt_v2",
                "only_inject_features": True,
                "ignore_pattern": [
                    P_NEED_IGNORE,
                    r"求Corporate Governance Report",  # 特例 识别问题
                ],
                "remove_blank": True,
                "inject_syllabus_features": [
                    r"__regex__CONVENINGOFSPECIALGENERALMEETING",
                    rf"__regex__Convening (of|an) {R_EGM} and putting forward proposals",
                    rf"__regex__Convening (of|an) {R_EGM} (on Requisition )?by Shareholders",
                    r"__regex__^GENERAL MEETING$",
                    r"__regex__Convening Shareholders’ General Meetings",
                ],
            },
            {
                "name": "para_match",
                "multi_elements": True,
                "paragraph_pattern": (
                    r"extraordinary general meeting".replace(" ", R_SPACE),
                    r"general meeting by the Board".replace(" ", R_SPACE),
                ),
                "neglect_pattern": (r"put forward proposals at general meetings",),
            },
            {
                "name": "score_filter",
                "threshold": 0.618,
                "multi_elements": True,
            },
            {
                "name": "kmeans_classification",
            },
        ],
    },
    {
        "path": ["K(b)-Enquiry Procedure and contact details"],
        "models": [
            {
                "name": "syllabus_elt_v2",
                "multi": True,
                "only_inject_features": True,
                "only_before_first_chapter": True,
                "include_shape": True,
                "include_title": True,
                "ignore_pattern": [
                    P_NEED_IGNORE,
                    f"{R_ENQUIRIES}.*?about Corporate Governance",
                ],
                "inject_syllabus_features": [
                    r"__regex__contact persons and contact methods",
                    rf"__regex__Procedure.*?raising {R_ENQUIRIES}",
                    rf"__regex__{R_ENQUIRIES}.*?the board",
                    rf"__regex__Shareholder.*?({R_ENQUIRIES}|inquiry right)",
                    r"__regex__^Contact Details$",
                ],
            },
            {
                "name": "yoda_layer",
                "multi_elements": True,
                "threshold": 0.1,
                "rule": Operator.all(
                    CIChapterLocator(
                        pattern=MatchMulti.compile(r"^Office of Board of Directors$", operator=any),
                        strict=StrictMode.ROOT,
                        dest=ContinuedElem[Para](
                            skip=ParaTextFilter(pattern=MatchMulti.compile(*R_CG_CHAPTER_TITLES, operator=any)),
                            filter=ParaTextFilter(
                                pattern=MatchMulti.compile(
                                    r"(Tel|Email)[:：]", r"Office of Board of Directors", operator=any
                                )
                            ),
                            limit=30,
                            size=1,
                        ),
                    ),
                    ScoreParaFilter(
                        pattern=MatchMulti.compile(
                            r"the office phone number and email address of the Board office",
                            operator=any,
                        )
                    ),
                ),
            },
            {
                "name": "para_match",
                "multi_elements": True,
                "as_follow_type": AsFollowType.ANY,
                "ignore_pattern": [P_NEED_IGNORE],
                "as_follow_pattern": rf"Shareholder.*?{R_ENQUIRIES}.*?the Board.*?as follows",
                "paragraph_pattern": (
                    r"enquiries to the board".replace(" ", R_SPACE),
                    r"enquiry.*?Board".replace(" ", R_SPACE),
                    r"contact details are set out in".replace(" ", R_SPACE),
                    r"head office at".replace(" ", R_SPACE),
                    r"are set out in the section".replace(" ", R_SPACE),
                    r"channels of communication with its shareholders".replace(" ", R_SPACE),
                    r"face-to-face meetings and conference calls with investors".replace(" ", R_SPACE),
                ),
            },
            {
                "name": "syllabus_based",
                "multi_elements": True,
                "only_inject_features": True,
                "ignore_pattern": [P_NEED_IGNORE],
                "inject_syllabus_features": [
                    r"__regex__^SHAREHOLDERS COMMUNICATION POLICY$",
                ],
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (rf"{R_ENQUIRIES} may be put to the Board by contacting the Company",),
                },
                "table_model": "empty",
            },
            {
                "name": "yoda_layer",  # 66202
                "threshold": 1,
                "rule": Operator.join(
                    CGChapterLocator(
                        pattern=re.compile(R_SPACE.join(["^communication", "with", "shareholder"]), re.I),
                        dest=ParaFromPos(pattern=P_CORPORATE_INFO),
                    ),
                    CONTACT_DETAIL_LOCATOR,
                ),
            },
            {
                "name": "yoda_layer",  # 66233
                "threshold": 1,
                "rule": Operator.join(
                    CGChapterLocator(
                        pattern=P_ENQUIRE_CHAPTER,
                        dest=ContinuedParaFromPos(
                            pattern=PositionPattern.compile(
                                r"section\s*headed",
                                r"corporate\s*information",
                            ),
                            forward=ForwardParaMatch(1),
                            limit=1,
                        ),
                    ),
                    CONTACT_DETAIL_LOCATOR,
                ),
            },
            {
                "name": "yoda_layer",  # 62536,66261,66887
                "threshold": 1,
                "rule": CGChapterLocator(
                    pattern=P_ENQUIRE_CHAPTER,
                    strict=StrictMode.CHAPTER,
                    dest=ContinuedElem[Para](
                        start=0,
                        stop=ParaTextFilter(re.compile("^change", re.I)),
                        skip=IGNORE_CG_CHAPTER_TITLE_FILTER,
                    ),
                ),
            },
            {
                "name": "score_filter",
                "threshold": 0.9,
                "multi_elements": True,
            },
            {
                "name": "score_filter",
                "threshold": 0.618,
            },
            {
                "name": "kmeans_classification",
                "filter_low_score_threshold": 0.1,
            },
        ],
    },
    {
        "path": ["K(c)-Procedure to put forward proposal in shareholders' meetings"],
        "default_enum_value": CGEnum.ND.value,
        "models": [
            # 1.
            {
                "name": "middle_paras",
                "only_use_syllabus_model": True,
                "only_inject_features": True,
                "include_top_anchor": True,
                "use_top_crude_neighbor": False,
                "top_default": True,
                "ignore_pattern": [],
                "inject_syllabus_features": [
                    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/4158#note_485781 元素块识别错误
                    r"__regex__Under the Companies Act$",
                    r"__regex__Procedures and contact details for put(ting)? forward proposals at (General|Shareholders?[’‘`]?) meeting",
                ],
                "top_anchor_regs": [
                    MatchMulti.compile(
                        r"Rights\s?to\s?put\s?forward\s?proposals\s?at\s?general\s?meetings",
                        operator=any,
                    )
                ],
                "bottom_anchor_regs": [
                    r"Rights\s?to\s?put\s?enquiries\s?to\s?the Board",
                    r"The\s?procedures\s?by.*sufficient\s?contact\s?details",
                ],
            },
            # 2.
            {
                "name": "syllabus_elt_v2",
                "multi": True,
                "only_inject_features": True,
                "include_title": True,
                "ignore_pattern": [P_NEED_IGNORE, P_K_C_NEED_IGNORE],
                "inject_syllabus_features": [
                    r"__regex__Put.*Proposals at (General|Shareholders?[’‘`]?) Meeting",
                    r"__regex__Procedures?.*for put(ting)? forward proposals at (General|Shareholders?[’‘`]?) meeting",
                    r"__regex__Procedures? for shareholders.*resolutions? for.*meeting ",
                    r"__regex__Procedures? for (shareholders|put(ting)? forward|SUBMITTING).*(at|for).*General meeting",
                    r"__regex__procedures? for shareholders to propose a person for election as a director",
                    r"__regex__Proposing a Person for Election as a Director (at|for).*(General meeting|AGM)",
                    r"__regex__Putting Forward Enquiry.*to the Board",
                    r"__regex__General Meeting.*Put.*Forward Proposals",
                    r"__regex__Giving shareholders an opportunity to ask questions",
                    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/3767#note_443448  元素块识别错误 仅识别了一行章节
                    r"__regex__^SHAREHOLDERS.*RIGHTS$__regex__^Shareholders’?\s?meeting$",
                ],
            },
            # 3.
            {
                "name": "para_match",
                "multi_elements": True,
                "paragraph_pattern": [
                    r"Procedures for shareholders to put( forward)? proposals at (General meeting|AGM)",
                    r"(including|put forward).*an extraordina\s?ry (General meeting|AGM)",
                    MatchMulti.compile(
                        MatchMulti.compile(
                            r"proposals submitted by the shareholders concerned",
                            r"propose (((any|a) )|new) proposals in writing to the Company",
                            r"put(ting)? forward proposals at.*(general|shareholders?[’‘`]?).*meeting",
                            r"wish to move a resolution may request the Company.*meeting",
                            r"refer.*Propose a Person for Election as a Director",
                            r"mak(e|ing) ((any|a) )?proposals? at.*(general|shareholders?[’‘`]?).*meeting",
                            r"put forward to the Shareholders?[’‘`]? for approval at next",
                            r"including a resolution to propose.*at (General meeting|AGM)",
                            operator=any,
                        ),
                        MatchMulti.compile(
                            r"\bwrite|writing|written\b",
                            r"\bcontact details\b",
                            operator=any,
                        ),
                        operator=all,
                    ),
                ],
            },
            # 4.
            {
                "name": "yoda_layer",  # 66205, 66258,66259
                "threshold": 0.618,
                "indices_filter": set.issuperset,
                "rule": CGChapterLocator(
                    pattern=MatchMulti.compile(
                        PositionPattern.compile(r"\bput", r"\bforward", r"\bpropos"),
                        operator=any,
                    ),
                    strict=StrictMode.CHAPTER,
                    dest=ContinuedElem[Para](start=0, skip=IGNORE_CG_CHAPTER_TITLE_FILTER),
                ),
            },
            # 5.
            {
                "name": "score_filter",
                "threshold": 0.618,
                "multi_elements": True,
                "syllabus_regs": [re.compile(r"\bpropos", re.I)],
            },
            # 6.
            {
                "name": "yoda_layer",  # 66198
                "threshold": 1,
                "rule": CGChapterLocator(
                    pattern=MatchMulti.compile(
                        PositionPattern.compile("^rights", "demand", "Director", "propos", "convocation"),
                        PositionPattern.compile(r"\bput", r"\bforward", r"\bpropos"),
                        operator=any,
                    ),
                    strict=StrictMode.CHAPTER,
                    dest=ContinuedElem[Para](start=0, skip=IGNORE_CG_CHAPTER_TITLE_FILTER),
                ),
            },
            # 7.
            {
                "name": "yoda_layer",  # 66219
                "threshold": 1,
                "rule": CGChapterLocator(
                    pattern=PositionPattern.compile("^shareholder", r"\bright"),
                    strict=StrictMode.CHAPTER,
                    dest=PageRangePara(
                        pattern=PatternCollection(
                            [
                                re.compile(R_SPACE.join([r"pages", r"(\d+)", "to", r"(\d+)"])),
                            ]
                        ),
                        include_self=False,
                        dest=ParaBetweenPageRange(pattern=re.compile(R_SPACE.join(["put", "forward", r"propos"]))),
                    ),
                ),
            },
            # 8.
            {
                "name": "yoda_layer",  # 66207,66213
                "threshold": 1,
                "rule": CGChapterLocator(
                    pattern=PositionPattern.compile("^shareholder", r"\bright"),
                    strict=StrictMode.CHAPTER,
                    dest=ParaFromPos(
                        pattern=SplitBeforeMatch.compile(
                            PositionPattern.compile("put", "forward", r"\bpropos"), separator=r"\.", operator=any
                        ),
                        size=1,
                    ),
                ),
            },
            # 9.
            {
                "name": "kmeans_classification",
                "para_pattern": [
                    MatchMulti.compile(
                        MatchMulti.compile(
                            r"put(ting)? forward (any )?proposals",
                            r"(submit|written) the proposed.*to the Company Secretary",
                            r"made in writing to the Board or the Company secretary",
                            r"notice in writing.*have been lodged at",
                            r"submit a written request stating the resolution.*at the AGM",
                            r"submit it to the convener in writing within",
                            r"a shareholder who signed the requisition may call the meeting",
                            r"convening an extraordinary general meeting.*set out in the paragraph above",
                            r"Giving shareholders an opportunity to ask questions",
                            r"put forward any proposals at any shareholders meeting",
                            r"The procedures for Shareholder to propose a person for election as a director",
                            r"an extraordinary general meeting shall be convened on the written requisition",
                            r"The written requisition.*proposals to be put forward at such meetin",
                            operator=any,
                        ),
                        MatchMulti.compile(
                            r"\b(written|writing)\b",
                            r"\bcontact\b",
                            r"\bvia email\b",
                            operator=any,
                        ),
                        operator=all,
                    ),
                    # MatchMulti.compile(
                    #     r"\b(written|writing)\b",
                    #     MatchMulti.compile(
                    #         r"\bconvener\b",
                    #         r"\bproposal\b",
                    #         r"\bnotice\b",
                    #         operator=any,
                    #     ),
                    #     operator=all,
                    # ),
                ],
            },
            # 10.
            {
                # 有且只有一个分数大于0.4
                "name": "yoda_layer",
                "threshold": 0.4,
                "rule": ScoreLensFilter(length=1),
            },
        ],
    },
]
