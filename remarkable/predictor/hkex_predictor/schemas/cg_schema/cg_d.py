from remarkable.common.common_pattern import P_SEN_SEPARATOR, R_CG_CHAPTER_TITLES, R_CHAPTER_PREFIX
from remarkable.common.constants import CGEnum
from remarkable.common.pattern import (
    MATCH_ALWAYS,
    MatchMulti,
    NeglectPattern,
    PatternCollection,
    SplitBeforeMatch,
)
from remarkable.predictor.hkex_predictor.models.yoda_layer.operator import Operator
from remarkable.predictor.hkex_predictor.models.yoda_layer.rule.by_pos import PageRangePara, ParaFromPos
from remarkable.predictor.hkex_predictor.models.yoda_layer.rule.root import (
    CGChapterLocator,
    RDChapterLocator,
    ScoreParaFilter,
)
from remarkable.predictor.hkex_predictor.models.yoda_layer.rule.special import ParaBetweenPageRange
from remarkable.predictor.hkex_predictor.schemas.pattern import P_NEED_IGNORE, R_CG, reg_words

R_CHAPTER = r"CORPORATE\s*?GOVERNANCE\s*?REPORT"
R_DSC_CHAPTER = r"DIRECTORS['‘’]?\s*SERVICE\s*CONTRACTS"

R_PARA = (
    r"non-executive.*?Director.*?service\s*?contract.*?(term.*?of.*?|\brenew.*?)\byears?",
    # 常见描述2: 没有明确披露签订了合同，但是有披露任期年限
    r"non-executive.*?Director.*?\bappoint.*?\b(year|term)s?",
    # 常见描述3: 初任期为xx年
    r"non-executive.*?Director.*?\binit.*?\bterms?.*?\byears?",
    # 常见描述4: 明确披露没有任期
    r"non-executive.*?Director.*?\b(without|not?)\b.*?\b(spec|any).*?\b(term|appoint|service.*?contract)",
    r"\bnone\b.*Director.*?service\s*?contract",
    # 常见描述5: 仅披露董事轮换周期
    r"\ball\b.*?Director.*?\brotat.*?\b.*?\b(year|term)s?",
)


DSC_CHAPTER_LOCATOR = RDChapterLocator(
    min_page=5,
    pattern=MatchMulti.compile(rf"{R_CHAPTER_PREFIX}{R_DSC_CHAPTER}", operator=any),
)

# 非执行董事
R_NED = r"(\bNEDs?\b|non-\s?executive\s*Director)"

P_NED = MatchMulti.compile(
    R_NED,
    NeglectPattern.compile(match=r"\bDIRECTORS?\b", unmatch=r"executive\s*Director"),
    operator=any,
)

predictor_options = [
    {
        "path": ["D-Term of appointment of NED"],
        "models": [
            # 1. 先提取描述在 set on page *, 再到具体页码提取内容
            {
                "name": "yoda_layer",
                "threshold": 1,
                "rule": CGChapterLocator(
                    pattern=MATCH_ALWAYS,
                    dest=PageRangePara(
                        pattern=PatternCollection(
                            [
                                r"appointment[\w\s]+?pages\s*(\d+)\s*to\s*(\d+)",
                                r"terms of appointment of the INEDs are set.*on page\s*(\d+)",
                            ]
                        ),
                        dest=ParaBetweenPageRange(pattern=MatchMulti.compile(*R_PARA, operator=any)),
                    ),
                ),
            },
            # 2. 如果在CG report中关于董事任期的内容链接到了其他章节，需要提取CG report中的链接描述，也需要提取被链接的那段描述
            # http://100.64.0.105:55647/#/project/remark/244250?treeId=22437&fileId=67366&schemaId=28&projectId=22437&schemaKey=D
            {
                "name": "yoda_layer",
                "threshold": 0.01,
                "rule": Operator.join(
                    ScoreParaFilter(
                        pattern=MatchMulti.compile(
                            MatchMulti.compile(
                                r"\bterms?\b",
                                R_NED,
                                r"set\s*out\s*in\s*(the\s*)?(section|chapter)",
                                R_DSC_CHAPTER,
                                operator=all,
                            ),
                            # MatchMulti.compile(*R_DR_CHAPTER_TITLES, operator=any),
                            operator=all,
                        )
                    ),
                    DSC_CHAPTER_LOCATOR.with_dest(
                        dest=ParaFromPos(
                            pattern=MatchMulti.compile(
                                MatchMulti.compile(*R_PARA, operator=any),
                                P_NED,
                                operator=all,
                            ),
                            limit=20,
                        )
                    ),
                ),
            },
            {
                "name": "syllabus_based",
                "only_inject_features": True,
                "ignore_pattern": [P_NEED_IGNORE],
                "syllabus_level": 1,
                "min_level": 1,
                "inject_syllabus_features": [
                    r"__regex__",
                ],
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": R_PARA,
                },
                "table_model": "empty",
            },
            {
                "name": "table_title",
                "only_inject_features": True,
                "feature_white_list": [
                    r"Term of Office of Non-executive Directors",
                ],
            },
            # 5. 在表格中披露
            # http://100.64.0.105:55647/#/project/remark/244259?treeId=2414&fileId=67357&schemaId=28&projectId=2414&schemaKey=D
            {
                "name": "special_cells",
                "threshold": 0.001,
                "title_pattern": MatchMulti.compile(rf"compliance\s*procedures?.*?{R_CG}\s*code", operator=any),
                "row_pattern": MatchMulti.compile(
                    r"\bterms?\b", P_NED, r"\bappoint", r"\b(rotat|retire)", operator=all
                ),
            },
            {
                "name": "score_filter",
                "threshold": 0.618,
            },
            # 7. 提取每个董事的任期都是n年
            # http://100.64.0.105:55647/#/project/remark/244273?projectId=17&treeId=24090&fileId=67343&schemaId=28
            {
                "name": "para_match",
                "multi_elements": True,
                "threshold": 0.01,
                "parent_features": R_CG_CHAPTER_TITLES,
                "parent_must_be_root": True,
                "page_first_as_parent_syllabus": True,
                "enum": CGEnum.C,
                "paragraph_pattern": SplitBeforeMatch(
                    MatchMulti.compile(
                        rf"\bdirectors?\b|{R_NED}",
                        rf"serve\s*for\s*{reg_words(1, 3)}year",
                        r"\bterm\b",
                        operator=all,
                    ),
                    separator=P_SEN_SEPARATOR,
                ),
            },
            {
                "name": "kmeans_classification",
                "parent_features": R_CG_CHAPTER_TITLES,
                "parent_must_be_root": True,
                "page_first_as_parent_syllabus": True,
                "para_pattern": [
                    NeglectPattern(
                        match=MatchMulti.compile(
                            r"\bterms?\b", r"non-executive.*?Director|All Directors|\bNED\b", operator=all
                        ),
                        unmatch=MatchMulti.compile(
                            r"Note 1: Due to the expiration of terms of office",
                            r"subject to termination.*terms and may be renewed.*Listing Rule",  # 67301
                            r"terminated by the Company or the INED giving at least.*months",  # 67303
                            operator=any,
                        ),
                    ),
                ],
            },
        ],
    },
]
