from remarkable.common.common_pattern import (
    R_ASSETS,
    R_CN_SPACE,
    R_<PERSON>N_SPACE_COLON,
    R_CN_SPACE_MIDDLE_DASH,
    R_CURRENT_ASSETS,
    R_MIDDLE_DASH,
    R_MIDDLE_DASHES,
    R_<PERSON>N_CURRENT_ASSETS,
    R_TOTAL_ASSETS,
)
from remarkable.common.pattern import NeglectPattern
from remarkable.predictor.hkex_predictor.models.total_assets import (
    R_BS_TBL_TITLES,
    R_SIGNI_INVEST_NEG_SUBJECTS,
)
from remarkable.predictor.hkex_predictor.schemas.ratio_check_schema import (
    NEGLECT_BS_SYLLABUS_PATTERN,
    NEGLECT_BS_TITLE_ABOVE_PATTERN,
    NEGLECT_BS_TITLE_PATTERN,
)

# 一些需要排除的表名关键词
# parent: https://www1.hkexnews.hk/listedco/listconews/sehk/2025/0422/2025042201685.pdf code=1202 year=2024
R_NEGLECT_BS_TITLE_FOR_AR = [
    *NEGLECT_BS_TITLE_PATTERN,
    rf"Analysis|following|below|subsidiary|\b(5|five|ten|10)[\s{R_MIDDLE_DASHES}]*year",
    # 排除其他财务报表
    NeglectPattern.compile(
        match=r"cash\s*flow|change|profit\s*or\s*loss|comprehensive|income", unmatch=r"balance|financial\s*position"
    ),
    # http://100.64.0.105:55647/#/project/remark/411619?treeId=4498&fileId=113827&schemaId=29&projectId=17 stock=2009 year=2024
    NeglectPattern.compile(match=r"audited", unmatch=r"audited\s*Consolidated"),
    # http://100.64.0.105:55647/#/project/remark/413444?treeId=14583&fileId=114030&schemaId=29&projectId=17&page=104
    # company limited: http://100.64.0.105:55647/#/project/remark/383853?treeId=37622&fileId=113005&schemaId=29&projectId=17
    NeglectPattern.compile(match=r"parent|company", unmatch=r"Consolidated|limited|bank"),
]

R_KEYWORD = rf"(investment|(other\s*)?financial|equity\s*(securities|instrument)|^{R_MIDDLE_DASH})"
# 重大投资科目
R_SIGNI_INVEST_SUBJECTS = [
    # 1. 通用
    rf"{R_KEYWORD}.*\b(fair\s*value|FVT?PL|FVT?OCI)\b",
    rf"\bFVT?(PL|OCI)[\"“”)）]*{R_CN_SPACE}$",
    rf"\bthrough{R_CN_SPACE}profit\s*or\s*loss{R_CN_SPACE}$",
    rf"\bthrough{R_CN_SPACE}other\s*comprehensive\s*income{R_CN_SPACE}$",
    rf"{R_KEYWORD}.*\bheld[{R_MIDDLE_DASHES}\s]for[{R_MIDDLE_DASHES}\s](sale|trade|trading)",
    rf"held[{R_MIDDLE_DASHES}\s]for[{R_MIDDLE_DASHES}\s](sale|trade|trading).*{R_KEYWORD}",
    # http://100.64.0.105:55647/#/project/remark/405806?treeId=3359&fileId=113173&schemaId=29&projectId=17
    rf"\btrading.*{R_KEYWORD}",
    rf"{R_KEYWORD}.*\bamorti[sz]ed\s*cost",
    # 2.Investments
    rf"^[{R_MIDDLE_DASHES}\s]*(other\s*)?Investments{R_CN_SPACE}$",
    rf"^{R_CN_SPACE_MIDDLE_DASH}(other\s*)?Investments$",
    # 3. short/long term investment
    rf"(short|long)[{R_MIDDLE_DASHES}\s]term\s*investment",
    # 4. financial assets/investment
    rf"^{R_CN_SPACE_MIDDLE_DASH}financial\s*(asset|investment)s?$",
    rf"^[{R_MIDDLE_DASHES}\s]*f\s?i\s?nancial\s*(asset|investment)s?{R_CN_SPACE}$",
    # 5. other financial assets (under current/non-current assets)
    rf"^{R_CN_SPACE_MIDDLE_DASH}other\s*financial\s*assets?(\s*under\s*(non{R_MIDDLE_DASH})?current\s*assets?)?$",
    rf"^[{R_MIDDLE_DASHES}\s]*other\s*financial\s*assets?(\s*under\s*(non{R_MIDDLE_DASH})?current\s*assets?)?{R_CN_SPACE}$",
    rf"^{R_CN_SPACE_MIDDLE_DASH}other\s*((non{R_MIDDLE_DASH})?current\s*)?financial\s*assets?$",
    rf"^[{R_MIDDLE_DASHES}\s]*other\s*((non{R_MIDDLE_DASH})?current\s*)?financial\s*assets?{R_CN_SPACE}$",
    # 6. equity investment
    rf"equity\s*(instruments?\s*)?investments?{R_CN_SPACE}$",
    rf"equity\s*instruments?{R_CN_SPACE}$",
    r"(investment|instrument)s?\s*in\s*equity",
    # 7. investment in securities
    # r"equity\s*Securit",
    r"Investments?\s*in\s*Securit(y|i?es)",
    # 8. Trading securit
    rf"^{R_CN_SPACE_MIDDLE_DASH}Trading\s*securit",
    # 9. Trading portfolio investment
    rf"^{R_CN_SPACE_MIDDLE_DASH}Trading\s*portfolio\s*investment",
    # 10. 其他
    r"(Investment|instrument)s?\s*in\s*Fund\s*(Entities|Entity)",
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/621#note_720116 科目待定
    # r"Investments accounted for using.*?equity method",
    # 11. 加密货币 https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7350
    r"crypto\s*currenc(y|ies)",
    # TODO stock=0001, page=130 `Liquid funds and other listed investments` 在notes下表格中可看出是重大投资，但不好处理，暂不提取
]
R_SUBTOTAL_FEATURES = [
    # 中文在前
    *[
        rf"(^|\|){R_CN_SPACE}{r}[：:\s]*(\||$)"
        for r in [R_NON_CURRENT_ASSETS, R_CURRENT_ASSETS, R_TOTAL_ASSETS, R_ASSETS]
    ],
    # 中文在后
    *[
        rf"(^|\|){r}{R_CN_SPACE_COLON}(\||$)"
        for r in [R_NON_CURRENT_ASSETS, R_CURRENT_ASSETS, R_TOTAL_ASSETS, R_ASSETS]
    ],
]

predictor_options = [
    {
        "path": ["Total Assets"],
        "sub_primary_key": ["Time Period", "Account", "Currency"],
        "strict_group": True,
        "models": [
            {
                "name": "significant_investment_ratio",
                "multi_elements": True,
                "feature_from": "row_headers",
                "table_title_pattern": R_BS_TBL_TITLES,
                "neglect_title_pattern": R_NEGLECT_BS_TITLE_FOR_AR,
                "neglect_title_above_pattern": NEGLECT_BS_TITLE_ABOVE_PATTERN,
                "only_inject_features": True,
                "sub_total_headers_features": R_SUBTOTAL_FEATURES,
                "strict_sub_total_as_feature": True,
                "feature_white_list": [
                    # 中文在前
                    *[
                        rf"__regex__(^|\|){R_CN_SPACE}{r}[：:\s]*(\||$)"
                        for r in [R_NON_CURRENT_ASSETS, R_CURRENT_ASSETS, R_TOTAL_ASSETS, R_ASSETS]
                    ],
                    # 中文在后
                    *[
                        rf"__regex__(^|\|){r}{R_CN_SPACE_COLON}(\||$)"
                        for r in [R_NON_CURRENT_ASSETS, R_CURRENT_ASSETS, R_TOTAL_ASSETS, R_ASSETS]
                    ],
                ],
            },
        ],
    },
    {
        "path": ["Significant Investment"],
        "sub_primary_key": ["Time Period", "Account", "Value"],
        "strict_group": True,
        "models": [
            {
                "name": "significant_investment_ratio",
                "feature_from": "row_headers",
                # 这里配置特性时候，注意特性可能是|串起来，需要匹配|
                "sub_total_headers_features": R_SUBTOTAL_FEATURES,
                "table_title_pattern": R_BS_TBL_TITLES,
                "neglect_title_pattern": R_NEGLECT_BS_TITLE_FOR_AR,
                "neglect_syllabus_regs": NEGLECT_BS_SYLLABUS_PATTERN,
                "multi_elements": True,
                "feature_black_list": [rf"__regex__{r}" for r in R_SIGNI_INVEST_NEG_SUBJECTS],
                "feature_white_list": [rf"__regex__{r}" for r in R_SIGNI_INVEST_SUBJECTS],
                "need_significant": True,
            },
        ],
    },
]
