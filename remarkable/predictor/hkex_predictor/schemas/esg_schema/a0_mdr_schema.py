"""
Jura4 ESG
"""

from remarkable.common.common_pattern import EXPLAIN_PATTERN as COMMON_EXPLAIN_PATTERN
from remarkable.predictor.hkex_predictor.schemas.esg_schema.common_pattern import (
    DEFAULT_MODEL,
    NEGLECT_PAGE_HEADER_PATTERN,
)

EXPLAIN_PATTERN = tuple(COMMON_EXPLAIN_PATTERN)


predictor_options = [
    {
        # 一般位于ESG report 前面几页，常在board statement或governance  structure小标题下面，
        # 提取涉及“board对整个公司ESG相关的措施或report负责的描述-包括制定，实施，检查，复核，监督，批准，风险评估”等，
        # 主要突出有responsibility或role
        # 常包含关键词
        # oversee/oversight/overall/full/ultimate/solely +
        # responsibility/accountability/leadership/direction/charge of或responsible等等
        # 有时会在多个段落出现相同的描述，可同时提取。
        "path": ["MDR 13 i - board oversight"],
        "models": [
            {
                "name": "para_match",
                "syllabus_regs": [r"Governance", r"STATEMENT", "board"],
                "paragraph_pattern": (
                    r"Board.*?respons.*?overseeing",
                    r"overall.*?responsib.*?strateg",
                    r"board(?:(?!\.).)*(oversee|responsib)",
                ),
                "multi_elements": True,
            },
            {
                "name": "syllabus_elt_v2",
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__esg governance structure",
                ],
            },
            {
                "name": "syllabus_elt_v2",
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__governance structure",
                ],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__Social.*?Governance.*?Report",
                    r"__regex__MESSAGE FROM THE BOARD",
                ],
                "multi_elements": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"(?P<content>(oversee|oversight|overall|full|ultimate|solely).*?("
                        r"responsibility|accountability|leadership|direction|responsible|governance|committee))",
                        r"(?P<content>(responsibility|accountability|leadership|direction|responsible|governance|committee).*?(integrity))",
                    ),
                },
                "table_model": "empty",
            },
            {  # 当章节识别有误时
                "name": "para_match",
                "paragraph_pattern": (r"board(?:(?!\.).)*(oversee|oversight|responsib)",),
            },
            DEFAULT_MODEL,
        ],
    },
    {
        # 涉及到“board自己,或者通过建立各种committee/working team, 或授权senior management 来评估ESG相关的风险，
        # 制定相关的政策，监督政策的实施，评估政策的有效性”的描述。
        # 较为经常的描述为“board 通过ESG相关的committee或其他组织来监管ESG相关事务”
        # 可能会涉及的关键词有：ESG Committee, ESG, committee（各种各样的committee）
        # working team，working group，establish, set up，delegate, authorise, formulated, strategy，identify，evaluate，priority, approach, strategy等等
        "path": ["MDR 13 ii - board management approach"],
        "models": [
            {  # 精确的段落匹配
                "name": "para_match",
                "paragraph_pattern": (
                    r"(ESG (Committee|taskforce)|working (team|group))(?:(?!\.).)*((?<!ESG )report|establish|identif(?:(?!\.).)*risk)",  # file63962 章节识别错误
                ),
            },
            {
                "name": "para_match",
                "strict_limit": True,
                "syllabus_regs": [
                    r"board statement",
                    r"Statement of( the)? Board",
                    r"governance",
                    r"approach|strategy",
                ],
                "paragraph_pattern": (
                    r"set up.*?ESG working group",
                    r"Group.*?establish.*?ESG",
                    r"Board.*?strategy(?:(?!\.).)*ESG",
                    r"establish.*?working group",
                    r"to (propose|oversee|review|monitor)",  # file64001
                    r"(review|propose)(?:(?!\.).)*(ESG (Committee|taskforce)|working (team|group))",  # file63988
                    r"(ESG (Committee|taskforce)|working (team|group))(?:(?!\.).)*((?<!ESG )report|establish|identif(?:(?!\.).)*risk)",  # file63988 file63962
                    r"Board(?:(?!\.).)*identified risks",  # file63988
                    r"The principal duties of the Board",
                ),
                "multi_elements": True,
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                # "only_inject_features": True,
                "multi": True,
                "inject_syllabus_features": [
                    r"__regex__ESG Working Group",
                    r"__regex__ESG.*?(approach|strategy)",
                ],
            },
            {  # syllabus识别有误时
                "name": "para_match",
                "multi_elements": True,
                "paragraph_pattern": (
                    r"(ESG (Committee|taskforce)|working (team|group))(?:(?!\.).)*(report|establish)",
                ),
            },
            DEFAULT_MODEL,
        ],
    },
    {
        # how the board reviews progress made against ESG-related goals and targets with an explanation of how they relate to the issuer’s businesses.
        # 董事会如何审查与 ESG 相关的目标所取得的进展，并解释它们与发行人业务的关系
        # 一般位于ESG report 前面几页，常在board statement或governance structure小标题下面，提取涉及到review ESG问题的描述。
        # 优先提取涉及到“ESG相关的target或goal的review”或”progress review”的描述。
        # 定位：一般位于ESG report 前面几页。有些描述清晰的文档，在段落标题会有target或goal等关键词。
        # i.若ESG report中无board statement,就可以去governance或者其他含有关键词的段落;
        # ii.若ESG report中同时存在board statement、governance和其他含有关键词的段落，可优先框选board statement中的相关内容；
        # iii.若ESG无board statement、governance相关内容时，可通过关键词进行查找，框选对应内容；
        # iv.若ESG无board statement、governance相关内容时且通过关键词也未查找到相关内容时，则为ND;
        # 可能会涉及的关键词有：report, briefing, meet, frequency，monitor, review等等，较多会有monitor, review，assess
        "path": ["MDR 13 iii - board progress review"],
        "models": [
            {
                "name": "syllabus_elt_v2",
                "only_inject_features": True,
                "include_title": True,
                "inject_syllabus_features": [
                    r"__regex__Review of Progress against ESG-related Goals and Targets",
                ],
            },
            {
                "name": "para_match",
                "multi_elements": True,
                "strict_limit": True,
                "syllabus_regs": [
                    r"board statement",
                    r"governance",
                    r"target|goal",
                ],
                "paragraph_pattern": (
                    r"review.*esg.*target",
                    r"corporate governance report",
                    r"ESG.*?review.*?report",
                    r"Report.*progress.*?ESG",
                    r"Board(?:(?!\.).)*(determin|integrat|monitor|evaluate)",
                    r"(?:monitor|evaluate|review|assess)(?:(?!\.).)*ESG performance",  # file63974
                ),
            },
            {  # syllabus识别有误时
                "name": "para_match",
                "multi_elements": True,
                "paragraph_pattern": (
                    r"Board(?:(?!\.).)*(integrat|monitor|evaluate|review)\s",
                    # r'Board(?:(?!\.).)*determin'
                ),
            },
            {
                # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/1032#note_251173
                "name": "syllabus_elt_v2",
                "ignore_missing_crude_answer": True,
                "only_inject_features": True,
                "include_title": True,
                "multi": True,
                "inject_syllabus_features": [
                    r"MATERIALITY ASSESSMENT",
                    r"STRATEGIC PRIORITIES",
                    r"FOUR-PHASE PROCESS",
                ],
            },
            DEFAULT_MODEL,
        ],
    },
    {
        # MDR14-materiality:重要的 ESG要素的确认过程和选择标准；如有stakeholder参与，要有已确认stakeholder的描述和其参与的过程和结果
        # 位置：
        #
        # i.一般位置在ESG报告的前几页，通常在reporting principle/standard/framework标题下方，有明确的标题materiality,直接框选标题及内容，枚举选择C
        #
        # ii.标注materiality+ stakeholder engagement相关的所有内容（内容较多，会多出存在），枚举选择C
        #
        #
        # 以上内容是需要同时标注，如果存在多处需要都框选
        # 常见关键词：stakeholder engagement, stakeholder, material analysis, materiality, materiality assessment，
        # materiality matrix,communication with the stakeholder
        "path": ["MDR 14 part 1 - materiality application"],
        "pick_answer_strategy": "all",
        "models": [
            {
                "name": "row_match",
                "row_pattern": [r"materiality"],
            },
            {
                "name": "para_match",
                "force_use_all_elements": True,
                "paragraph_pattern": (
                    r"the Group has conducted a materiality assessment.*?(esg|social)",
                    r"the materiality assessment based on",
                ),
            },
            {
                "name": "esg_materiality",
                "paragraph_pattern": (
                    r"Materiality.*?Principle.*?esg",
                    r"Materiality[–:：].*?(esg|materiality matrix)",
                    r"Materiality[–:：].*?confirmed by the management",
                    r"Materiality.*?[-:: ]",
                    r"^•\s?Materiality\s?[–:：]",
                    r"^\d.*?Materiality[–:：].*?(esg|social)",
                ),
            },
            {
                "name": "syllabus_elt_v2",
                "multi": True,
                "only_inject_features": True,
                "include_title": True,
                "include_shape": True,
                "ignore_pattern": [
                    r"ANTI-EPIDEMIC HYGIENE MEASURES",
                ],
                "inject_syllabus_features": [
                    r"__regex__WITH STAKEHOLDERS?",
                    r"__regex__STAKEHOLDER ENGAGEMENT AND MATERIALITY ASSESSMENT",
                    r"__regex__MATERIALITY ASSESSMENT",
                    r"__regex__STAKEHOLDER ENGAGEMENT",
                    r"__regex__STAKEHOLDER identification",
                    r"__regex__MATERIAL ANALYSIS",
                    r"__regex__MATERIALITY",
                    r"__regex__MATERIALITY MATRIX",
                    r"__regex__STAKEHOLDERS ENGAGEMENT",
                    r"__regex__stakeholder.*?communication",
                    r"__regex__our stakeholders",
                    r"__regex__material ESG topics",
                    r"__regex__Communication with the stakeholders",
                    r"__regex__Stakeholders.*?Engagement",
                    r"__regex__Stakeholder Engagement Approach",
                    r"__regex__report.*?(principles?|standard|framework|rules?)__regex__importance",
                    r"__regex__List of important topics",
                    r"__regex__Stakeholder Communication",
                    r"__regex__Stakeholders.*?Involvement",
                ],
            },
            {
                "name": "table_title",
                "only_inject_features": True,
                "feature_white_list": [
                    r"materiality assessment",
                ],
            },
            {
                "name": "special_cells",
                "whole_table": True,
                "cell_pattern": (r"Relevant ESG issues to the Group",),
            },
        ],
    },
    {
        # 量化：披露排放物或能源消耗相关的标准、方法、假设、计算工具以及转换因数的来源。
        #  定位:
        #  1.一般是在reporting principle/standard标题下的相关内容；
        #  2.需要框选涉及描述的整个段落及其小标题;
        #  3.只需要A rule中的量化，B rule中的不算。
        # 注意：当有明确的quantitative相关内容时，直接框选相关principal；
        # 当没有quantitative相关内容时，去找各个指标如何计算的内容。
        # 关键词：quantitative；methodology(ies)；assumption(s)； conversion factor(s)； calculation
        "path": ["MDR 14 part 2 - quantitative application"],
        "models": [
            {
                "name": "special_cells",
                "cell_pattern": [
                    r"^quanti(?:tative|fication)[\s\w]{5,}",  # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/939#note_233032
                    r"practice the principle of quanti(?:tative|fication)",
                ],
            },
            {
                "name": "row_match",
                "ignore_index_table": True,
                "row_pattern": [r"^quanti(?:tative?|fication)"],
            },
            {
                "name": "syllabus_elt_v2",  # 章节
                "only_inject_features": True,
                "include_title": True,
                "inject_syllabus_features": [
                    r"__regex__report.*?(principles?|standards?|frameworks?|rules?)__regex__quanti(?:tative|fication)",
                    r"__regex__BASIS OF PREPARATION__regex__quanti(?:tative|fication)",
                ],
                "break_para_pattern": [r"balance", r"consistency"],
            },
            {
                "name": "para_match",
                "strict_limit": True,
                "syllabus_regs": [
                    r"report.*?(principles?|standards?|frameworks?|rules?|guides?)",
                    r"standards?",
                    r"^environment",
                    r"BASIS OF PREPARATION",
                ],
                "neglect_syllabus_regs": [
                    r"Response to Product Complaint",
                    r"Ensuring Stable Supply of Gas",
                    r"RESPONSIBLE GOVERNANCE",
                    r"Green Procurement Process",
                    r"Internally Carried out Anti-corruption",
                ],
                "paragraph_pattern": (r"quanti(?:tative|fication)”?(?:\s*:?)",),
                "neglect_pattern": (
                    # r'^•Quantitative Indicators:',
                ),
            },
            {
                "name": "para_match",
                "strict_limit": True,
                "multi_elements": True,
                "neglect_syllabus_regs": [
                    r"Response to Product Complaint",
                    r"Ensuring Stable Supply of Gas",
                    r"RESPONSIBLE GOVERNANCE",
                    r"Green Procurement Process",
                    r"Internally Carried out Anti-corruption",
                ],
                "syllabus_regs": [
                    r"environment",
                    r"report.*?(principle|standard|framework|rules?)",
                ],
                "paragraph_pattern": (
                    # r'quantitative|methodology(ies)?|assumptions?|conversion factors?',
                    # r're-calculates|calculated|recalculated|calculation|calculating',
                    r"GHG emissions data.*?based on.*?but not limited to",
                ),
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__MATERIALITY AND REPORTING BOUNDARY",
                ],
                "only_inject_features": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"quantitative|methodology(ies)?|assumptions?|conversion factors?",
                        r"re-calculates|calculated|recalculated|calculation|calculating",
                    ),
                },
                "table_model": "empty",
                "break_para_pattern": [r"balance", r"consistency"],
            },
            {
                "name": "table_footnote",
                "multi_elements": True,
                "force_use_all_tables": True,
                "cell_regs": [
                    r"Environmental$",
                    r"emissions",
                    r"energy consumption",
                    r"waste consumption",
                    r"water consumption",
                    r"hazardous wastes",
                ],
                "title_patterns": [
                    r"Environmental",
                ],
                "neglect_title_patterns": [
                    r"ESG issues",
                    r"Environmental Emission Data",
                ],
            },
            {
                "name": "para_match",
                "multi_elements": True,
                "paragraph_pattern": (
                    # r'quanti(?:tative|fication)',
                    # r'|methodology(ies)?|assumptions?|conversion factors?',
                    # r're-calculates|calculated|recalculated|calculation|calculating',
                    r"GHG emissions data.*?based on.*?but not limited to",
                    r"^(viii?\.|x\.|xiv\.)",
                    r"^quanti(?:tative|fication):",
                ),
            },
            # {
            #     "name": "score_filter",
            #     "threshold": 0.618,
            # },
            {
                "name": "esg_quantitative",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__emission|consumption|Energy",
                ],
                # 'multi_elements': True, # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/997#note_243309
                "multi": True,
                "paragraph_model": "empty",
                "table_model": "special_cells",
                "table_config": {
                    "whole_table": True,
                    "cell_pattern": (
                        r"Energy Consumption",
                        r"Emissions",
                    ),
                },
            },
        ],
    },
    {
        # MDR14-consistency:发行人应在 ESG 报告中披露所用方法或 KPI 的变化，或影响对比的相关因素。
        #  定位:
        #  1.一般是在reporting principle/standard标题下的相关内容；
        #  2.需要框选涉及描述的整个段落及其小标题;
        #  3.A\B rule中的所有consistency都算。
        # 注意：当有明确的consistency相关内容时，直接框选相关principal；
        # 当没有consistency相关内容时，去找各个指标两年的数据列，框选两年数据作为对比或consistent等字眼。
        # 关键词：consistency；methodology(ies)；assumption(s)；conversion factor(s)
        "path": ["MDR 14 part 3 - consistency application"],
        "models": [
            {
                "name": "para_match",
                "paragraph_pattern": (r"^.?(?<!in)consistency\s?[:-•]",),
                "syllabus_regs": [
                    r"report.*?(principles?|standards?|frameworks?|rules|guides?)",
                    r"standards?",
                    r"BASIS OF PREPARATIONS?",
                    r"Consistency.*?Principle",
                ],
            },
            {
                "name": "para_match",
                "paragraph_pattern": (r"(?P<content>^.?(?<!in)consistency\s?[:-•].*?)“Balance",),
                "content_pattern": True,
            },
            {
                "name": "para_match",
                "paragraph_pattern": (r"^.?(?<!in)consistency\s?[:-•]",),
            },
            # {"name": "para_match", "use_all_elements": True, "paragraph_pattern": (r'.*?consistency\s?[:-• ]',)},
            {
                "name": "row_match",
                "neglect_syllabus_regs": [r"Independent Assurance Statement", r"Reporting Principles"],
                "row_pattern": [r"(?<!in)consistency"],
            },
            {"name": "col_match", "col_pattern": [r"(?<!in)consistency"]},
            {
                "name": "special_cells",
                "cell_pattern": [r"^consistency[\s\w]{5,}"],
            },
            # {  # 特征在第一行的表格
            #     "name": "table_row",
            #     "feature_from": "self",
            #     "parse_by": "col",
            #     "neglect_syllabus_regs": [r'Independent Assurance Statement'],
            #     "feature_white_list": [
            #         r'__regex__consistency',
            #     ],
            # },
            {
                "name": "row_match",
                "ignore_index_table": True,
                "row_pattern": [r"^consistency"],
            },
            {
                "name": "special_cells",
                "cell_pattern": [r"consistency.*?methods"],
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__report.*?(principles?|standards?|frameworks?|rules?|guides?)__regex__consistency",
                ],
            },
            {
                "name": "syllabus_based",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                # "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__reporting principle",
                    r"__regex__About this Report",
                    r"__regex__MATERIALITY",  # MATERIALITY AND REPORTING BOUNDARY
                ],
                "multi_elements": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"consistent methodology",
                        r"used consistent reporting",
                        r"adopt consistent reporting",
                        r"consistency",
                        r"(?P<content>aligns with)",
                        r"adopt consistent report",
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "esg_consistency",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                # "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__reporting principle",
                    r"__regex__About this Report",
                    r"__regex__MATERIALITY",  # MATERIALITY AND REPORTING BOUNDARY
                ],
                "multi_elements": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"consistent methodology",
                        r"used consistent reporting",
                        r"adopt consistent reporting",
                        r"(?P<content>consistency|methodology(ies)?|assumptions?|conversion factors?)",
                        r"(?P<content>aligns with)",
                        r"adopt consistent report",
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"consistent methodology",
                    r"used consistent reporting",
                    r"adopt consistent reporting",
                    r"(?P<content>consistency|methodology(ies)?|assumptions?|conversion factors?)",
                    r"(?P<content>aligns with)",
                ),
                "syllabus_regs": [
                    r"reporting principle",
                    r"standard",
                ],
            },
            # {
            #     "name": "score_filter",
            #     'multi_elements': True,
            #     'aim_types': ['TABLE'],
            #     'threshold': 0.618,
            # },
        ],
    },
    {
        # 说明ESG 报告的报告界限,并描述用于识别 ESG 报告中包含哪些实体或业务的过程。 范围发生变更的，发行人应当说明差异及变更原因
        # 位置：
        #
        # i.一般位置在ESG报告的前几页，通常在scope and reporting period/reporting scope/reporting boundary/reporting scope and boundaries标题下，主要描述report包含的xxx operation/business，或在xxx地方的operation,或xxx entities或其包含的xxx子公司，直接框选段落，枚举C;
        #
        # ii.当ESG报告中有包含scope的标题，下方段落除boundary内容外还披露相关的准则或者是ESG报告的报告期则只需要框选boundary相关的内容，即report包含的xxx operation/business，xxx地方，xxxentitites
        #
        # iii.当无明确标题时或关键词也不明显但是披露了公司及其包含的子公司的主营相关内容也属于此规则，枚举应判断为C
        # 常见关键词：boundary,scope,cover,focus,engaged in
        "path": ["MDR 15 - reporting boundary"],
        "models": [
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"^Scope of (organization|report)",
                    r"We regularly review the scope of the ESG Report",
                    r"ESG Report includes",
                    r"has two major operating segments",
                    r"information contained herein.* sourced.*",  # file63861
                    r"data used.*report.*sourced.*",  # file63861
                    r"focusing on providing environmental hygiene services",  # file64593
                    r"This report covers",  # file63963
                    r"環境有重要關連的下列業務",  # 65870
                ),
                "neglect_pattern": (r"major properties of the Group",),
            },
            # 优先提取指定章节的整个段落
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "break_para_pattern": [
                    r"^Reporting Principles",
                ],
                "inject_syllabus_features": [
                    r"__regex__ESG Report Scope and Boundary",
                    r"__regex__Report(ing)? Scope and (Boundary|Boundaries)",
                    r"__regex__REPORT(ING)? (PERIOD AND |Organizational )?SCOPE",  # REPORTING PERIOD AND SCOPE file64641 | REPORT Organizational SCOPE file63932
                    r"__regex__REPORT(ING)? BOUNDAR(Y|ies)",  # Reporting Boundaries file64443   Reporting Boundaries and Scope file64592
                    r"__regex__Scope of (THE |THIS )?(ESG )?Report(ing)?",
                    r"__regex__^SCOPE:?$",  # file 63665 64554
                    r"__regex__\d+\.\sscope$",  # file64554
                    r"__regex__^報告範圍",  # file 63665
                    r"__regex__^PREPARATION BASIS AND SCOPE",  # file 64638
                    r"__regex__ABOUT (THE |THIS )?REPORT__regex__coverage",  # file 64593
                    r"__regex__Scope and Boundar",  # file 64593
                    r"__regex__Scope and Reporting Period",  # file 64593
                    r"__regex__Scope of this ESG Report",  # file 64593
                    # r'__regex__Concept and principle of the report',  # file 64593
                ],
            },
            {  # file64546 同时存在about this report 和about this group,优先取about this report
                "name": "syllabus_based",
                # "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__Concept and principle of the report",  # file64629
                    r"__regex__About (this|the) Report",
                ],
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"report(?:(?!(period|\.)).)*?(focus|operati(ng|ons?)|businesses|cover)(?!s? the period)",
                        # covers: file 63622  (?! the period) file83785  report(?:(?!(period)).) file63825
                        r"as.*environmental disclosure",  # file64629
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "syllabus_based",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__AND REPORT(ING)? BOUNDARY",
                    r"__regex__REPORTING BOUNDARIES( AND PRINCIPLES)?",
                    r"__regex__(REPORT(ING)? )?(SCOPE |PERIOD )AND (PERIOD|BOUNDARY|REPORTING)",
                    # REPORTING SCOPE AND BOUNDARY file63624   SCOPE AND BOUNDARY file63895  SCOPE AND REPORTING file63622
                    r"__regex__SCOPE OF (THE|THIS) REPORT",  # SCOPE OF THE REPORT file63637 63661
                    r"__regex__About (this|the) (Report|group|company)",
                    r"__regex__Report Description",
                    r"__regex__報告範圍",
                ],
                "multi_elements": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"(the )?(?:(?!\.).)*scope of",  # file63692
                        r"(the|this) ESG report (cover|focus)",
                        r"(the|this) ESG report.*summarises?.*cover(s|ing).*operati(ng|ons?)",
                        r"(the|this) ESG report.*to cover.*ESG ",  # .*to cover.*ESG: file 2
                        r"report contains details.*?social responsibilities",
                        r"report(?:(?!(period|\.)).)*?(focus|operati(ng|ons?)|businesses|cover)(?!s? the period)",
                        # covers: file 63622  (?! the period) file83785  report(?:(?!(period)).) file63825
                        r"^(?!.*Materiality)focus|engag(?:e[ds]|ing) in",  # ^(?!.*Materiality): file 63600
                        r"focusing on",  # file 64593
                        r"principal activities.*?which include",
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "row_match",
                "ignore_index_table": True,
                "first_cell": True,
                "row_pattern": [
                    r"report(ing)? scope",  # file63928
                ],
            },
            {
                "name": "special_cells",
                "cell_pattern": [
                    r"Scope of (organization|report)",
                    r"Report.*?encompasses.*?subsidiaries",
                    r"(?:This |the )?report covers? (?!the period)",  # (?!the period) file63807
                ],
            },
            {
                "name": "para_match",
                "force_use_all_elements": True,
                "paragraph_pattern": (
                    r"(?P<content>report(?:(?!(\.|period)).)*?(boundary|scope|cover|focus|operations|businesses|highlight|discuss))",
                    # (?:(?!\.).)*? file63734   (?:(?!(\.|period)).)*? file63917
                    r"(?P<content>report contains details.*?social responsibilities)",
                    r"(the )?scope of",
                    r"the ESG report covers",
                    r"report contains details.*?social responsibilities",
                    r"^(?:(?!In the investment).)*(engaged in)",  # ^(?:(?!In the investment).)* file63807
                ),
                "neglect_pattern": (
                    r"a lasting standstill for the whole of the reporting period",
                    r"put together a new team",  # file197593
                    r"covering the period",  # file63654
                ),
            },
            # {
            #     "name": "score_filter",
            #     "threshold": 0.07,  # file63844无规则答案且分数过低
            # },
        ],
    },
]
