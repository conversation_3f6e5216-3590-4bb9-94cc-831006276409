"""
Jura4 ESG
"""

from remarkable.predictor.hkex_predictor.schemas.esg_schema.common_pattern import (
    COMPLY_LAW_PATTERN,
    DEFAULT_MODEL,
    NEGLECT_PAGE_HEADER_PATTERN,
)

predictor_options = [
    {
        # 提取涉及到employment政策的所有段落。常见的描述是“公司遵守法律法规或者有相关的手册规定来保护员工的合法权益，不被歧视。同时员工也要遵守自己的职业道德”等。 一般在employment相关的标题下，
        # 可提取employment下的全部内容{明显能与后面细则内容区分的(例如laws相关及KPI相关的)，就不框入B1其他细则的内容}
        #
        # 常见关键词： employment, labour, workforce
        # 可从index给出的各个KPI的位置去定位框选；或在 Employment/ Employment Practices/Human Capital标题下
        "path": ["B1 policies - employment"],
        "models": [
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN + [r"continued$"],
                # "only_before_first_chapter": True,
                "multi": True,
                # "include_shape": True,
                "inject_syllabus_features": [
                    r"__regex__B[.]1",
                    r"__regex__Employment__regex__General Disclosure and KPIs",
                    r"__regex__\d.*?employment$",
                    r"__regex__Employment and Labor Practices",
                    r"__regex__Employee.*?(Right|Interest|policy)",
                    r"__regex__Protection of Employees.*?Rights",
                    r"__regex__Employee Recruitment",
                    r"__regex__Diversified Talent Attraction Policies",
                    r"__regex__Human.*?Labour Rights",
                    r"__regex__Improve employee wellbeing",
                    r"__regex__BUILDING A DIVERSE TEAM",
                    r"__regex__Create a diverse and inclusive workplace",
                    r"__regex__STAFF|COMPENSATION|BENEFITS|COMMUNICATION",
                    r"__regex__Diverse and Fair Recruitment",
                    r"__regex__Promotion and Remuneration",
                    r"__regex__Supporting Health and Wellness",
                    r"__regex__Personnel management",
                    r"__regex__Equal and compliant employment",
                    r"__regex__Diversified talent reserve",
                    r"__regex__Building a diversified team",
                    r"__regex__Well-being and Creative Activities",
                    r"__regex__Staff Relations",
                    r"__regex__Equal and legal employment",
                    r"__regex__Talent Attraction and Recruitment",
                    r"__regex__Broadening Channels for Introducing Professionals",
                    r"__regex__Diversity and Equal Opportunity",
                    r"__regex__TALENT ACQUISITION AND RETENTION",
                ],
                "neglect_parent_features": [
                    r"STAFF RECOGNITION",
                    r"ENGAGING OUR STAFF",
                    r"Staff Movement",
                    r"Staff Communication",
                    r"STAFF CARING",
                ],
                "break_para_pattern": [
                    r"I Am the Speaker",
                    # r'program documents involving the vital interests of employees',
                ],
            },
            DEFAULT_MODEL,
        ],
    },
    {
        # Employment -Compliance with relevant laws and regulations that have a significant impact on the issuer
        # 遵守对发行人有重大影响的法律法规
        "path": ["B1 law compliance - employment"],
        "location_threshold": 0.01,
        "models": [
            # ----- 精确匹配 start -----
            {
                "name": "twice_row_match",
                "first_cell": False,
                "row_pattern": COMPLY_LAW_PATTERN,
                "merge_valid_rows": False,
                "neglect_syllabus_regs": [
                    r"Labou?r Standards?",
                    r"B[2-8]|A[1-3]",
                ],
                "second_pattern": [
                    r"B1(\s+|$|[a-zA-Z])",  # file63997披露在index表格中
                ],
            },
            {  # file63974 63979
                "name": "syllabus_based",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                # 'only_before_first_chapter': True,
                # 'multi': True,  # file63979
                # 'skip_child_syllabuses': False,  # file63979
                "only_inject_features": True,
                "break_para_pattern": [r"B1\.1"],
                "inject_syllabus_features": [
                    r"__regex__Modern SlaveryA ct Transparency Statement",  # file64000
                    r"__regex__B1(\.|\s+|$)",
                    r"__regex__Compliance Management",
                    r"__regex__Human Capital",
                    r"__regex__Employment Practices",
                    # r"__regex__Employee(s’)? Right",  # file63959 63954
                    r"__regex__COMPLIANCE OPERATIONS",  # file 63954
                ],
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": COMPLY_LAW_PATTERN,
                    "neglect_pattern": (r"carries out internal audit projects in accordance with laws and procedures",),
                },
                "table_model": "empty",
            },
            # ===== 精确匹配 end =====
            {
                "name": "twice_para_match",
                "multi_elements": True,
                "use_all_elements": True,
                "syllabus_regs": [],
                "neglect_syllabus_regs": [
                    r"Labou?r Standards?",
                    r"B[2-8]|A[1-3]",
                ],
                "paragraph_pattern": COMPLY_LAW_PATTERN,
                "neglect_pattern": (
                    r"^[a-zA-Z]\.",
                    r"^•",
                    r"(child|forced?) labou?r",  # 与B4-law区分开
                ),
                "second_pattern": (
                    r"employment",
                    r"labour",
                    r"workforce",
                    r"dismissal",
                    r"recruitment",
                    r"promotion",
                    r"working hours",
                    r"rest periods",
                    r"equal opportunity",
                    r"diversity",
                    r"anti-discrimination",
                    r"other benefits",
                    r"other welfare",
                ),
            },
            {
                "name": "shape_title",
                "force_use_all_elements": True,
                "regs": (r"Diversified Talent Attraction Policies",),  # file64003针对infographics的特殊规则
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__Compliance Management",
                    r"__regex__Human Capital",
                    r"__regex__Employment Practices",
                    r"__regex__employment",
                ],
                "paragraph_model": "para_match",
                "para_config": {"paragraph_pattern": COMPLY_LAW_PATTERN},
                "table_model": "row_match",
                "table_config": {
                    "row_pattern": [r"Employment"],
                },
            },
            {
                "name": "kmeans_classification",
                "threshold": 0.618,
                "para_pattern": [
                    r"(comply|not violate|abide).*?employment.*?(law|regulation)"
                    r"no material non-compliance with|are compliant with",
                    r"complied with all relevant laws",
                    r"complies with the relevant laws and regulations.*?employment",
                    r"(?P<content>non-compliance|employment|ordinance|legal|law|regulation)",
                ],
            },
        ],
    },
    {
        #  提取内容：涉员工总数按性别、雇佣类型、年龄和区域划分的图表或段落或句子。
        #  一般都是按照各类员工的数量及比例披露的。
        #  关键词是 B1.1, by gender, employment type，number of，age ，geographical，resign，resigned，resignation
        #  可从index给出的各个KPI的位置去定位；或在 Employment/ Employment Practices/Human Capital
        #  特殊情况：若仅有员工总数的合计数，没有各个分类下的数据，B1.1提取合计数，判断为Y
        "path": ["KPI B1.1 - workforce by types"],
        "models": [
            # {
            #     "name": "after_row_match",
            #     'row_pattern': [
            #         r'B1.1',
            #         r'Workforce',
            #     ],
            #     'para_pattern': [
            #         r'B1.1|by gender|employment type|number of|age|geographical|resign|resigned|resignation'
            #     ],
            # },
            {
                "name": "special_cells",
                "whole_table": True,
                # "multi_elements": True,
                # "just_first_row": True,
                "cell_pattern": (r"Employee structure", r"New recruitment"),
            },
            {
                "name": "shape_title",
                "multi_elements": True,
                "regs": (
                    r"Employee.*?distribution",
                    r"Employees by.*",
                    r"Age distribution",
                    r"Age group",
                    r"^gender$",
                    r"(Employment|Employee) Category",
                    r"本集團員工團隊人數統計資料概述如下",
                    r"僱員明細載列如下",
                    r"僱員分佈詳情如下",
                    r"僱員總數",
                    r"workforce by",
                    r"by (age|gender)",
                ),
                "neglect_regs": (r"turnover",),
            },
            {
                "name": "middle_rows",
                "multi_elements": True,  # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/971#note_240609
                "start_regs": [
                    r"Workforce 員工Total Headcount 員工總人數 By Geographical Distribution",
                    r"By Education Level 按教育程度劃分 Bachelor Degree or Above 學士學位或以上",
                ],
            },
            {
                "name": "middle_rows",
                # "multi_elements": True,
                "regs": [
                    r"workforce distribution",
                    r"workforce as",
                    r"by type",
                    r"male",
                    r"All staff",
                    r"by.*?(gender|age|location)",
                    r"B1.1",
                ],
                "start_regs": [
                    r"Employee breakdown",
                    r"number of employees",
                    r"workforce",
                    r"All staff",
                ],
                "end_regs": [
                    r"Employee turnover rate",
                    r"Development and training",
                    r"Woman Representation",
                    r"Average hours of training",
                    r"51 and above",  # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/971#note_240608
                    r"Staff Turnover Rate",
                ],
            },
            {
                "name": "table_title",
                "multi_elements": True,
                "only_inject_features": True,
                "feature_white_list": [
                    r"^Diversity$",
                    r"Summary of Employment Performance Indicators",
                    r"Employee.*?distribution",
                    r"Age distribution",
                    r"Age group",
                    r"gender",
                    r"Employment Category",
                    r"employee category",
                    r"workforce by",
                    r"本集團員工團隊人數統計資料概述如下",
                    r"僱員分佈詳情如下",
                    r"僱員總數",
                ],
            },
            {
                "name": "row_match",
                "ignore_index_table": True,
                "multi_elements": True,
                "row_pattern": [
                    r"distribution of employees",
                ],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__STRINGENT RECRUITMENT PROCESS",
                    r"__regex__Employment",
                    r"__regex__Employment Practices",
                    r"__regex__Human Capital",
                ],
                "multi_elements": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"B1.1|by gender|employment type|number of|geographical|resign|resigned|resignation",
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"(?P<content>B1.1|by gender|employment type|number of|age|geographical|resign|resigned|resignation)",
                ),
            },
            DEFAULT_MODEL,
        ],
    },
    {
        # 提取内容：涉员工turnover rate按性别、年龄和区域划分的数据所在的图表或段落或句子。
        # 常见的关键词：B1.2, turnover, by gender, age , geographical, employment type，leave，leaver，resign，
        # resigned，resignation，drop in
        #        可从index给出的各个KPI的位置去定位框选；或在 Employment/ Employment Practices/Human Capital标题下
        # 特殊情况：
        #     1. 仅有overall employees的turnover rate，没有披露按照gender, age group and geographical region分类下的
        #     turnover rate，B1.2属于Y
        #
        #     2. B1.2 只披露的离职人数，没有turnover离职了，属于Y
        "path": ["KPI B1.2 - employee turnover by types"],
        "models": [
            {
                "name": "after_row_match",
                "row_pattern": [r"B1.2"],
                "para_pattern": [r"B1.2|turnover"],
                "middle_rows": True,
                "multi_elements": True,
                "answer_both_syllabus_and_table": True,
                # "answer_table_first_row_as_title": True,
                "direct_answer_pattern": [r"Pending setting up"],
                "table_title": [
                    r"流失率",
                    r"Turnover Rate",
                    # r'^Turnover$',
                ],
                "start_regs": [r"Turnover Rate"],
                "end_regs": [
                    r"Training|Trained",
                    r"B2\.1",
                    r"Work-related fatality and injury",
                ],
            },
            {
                "name": "middle_rows",
                "ignore_index_table": True,
                "regs": [
                    r"B1\.2",
                ],
                "start_regs": [
                    r"Turnover Rate",
                    r"Employee turnover",
                    r"^Turnover$",
                ],
                "end_regs": [
                    r"Training|Trained",
                    r"Health and Safety",
                    r"B2\.1",
                    r"Work-related fatality and injury",
                ],
            },
            {
                "name": "table_title",
                "multi_elements": True,
                "only_inject_features": True,
                "feature_white_list": [
                    r"本集團僱員流失率概述如下",
                    r"turnover rates",
                    r"employee Structure and Turnover",
                ],
            },
            {
                "name": "shape_title",
                "multi_elements": True,
                "regs": (
                    r"Employee Turnover",
                    r"Geographical Region",
                ),
            },
            {
                "name": "middle_rows",
                "regs": [r"turnover"],
                "title_regs": [r"turnover"],
                "start_regs": [r"turnover rate"],
                "end_regs": [
                    r"Percentage of employees trained",
                    r"training",
                ],
            },
            {
                "name": "table_title",
                "only_inject_features": True,
                "feature_white_list": [
                    r"turnover",
                ],
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"(?P<content>not disclosed|sensitive)",
                    r"full-time employee turnover rate",
                    r"employee turnover rate for male and female",
                    r"average turnover rate by",
                    r"commercially sensitive",
                ),
            },
            # DEFAULT_MODEL,
        ],
    },
]
