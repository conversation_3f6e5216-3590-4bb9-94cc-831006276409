"""
Jura4 ESG
"""

from remarkable.predictor.hkex_predictor.schemas.esg_schema.common_pattern import (
    COMPLY_LAW_PATTERN,
    DEFAULT_MODEL,
    NEGLECT_PAGE_HEADER_PATTERN,
)

predictor_options = [
    {
        # B2涉及到的规则都是关于health and safety相关的内容，policy一般都为公司制定了安全管理政策的相关概括性描述；
        #  所在位置：1. 涉及到health and safety的大标题下（一般位置比较固定，都在这个章节下定位）
        #           2. 如果大标题分了小标题的情况，policy一般可以提取大标题和小标题中间的文段
        #           3. 小标题有policy/policies的关键词的，可以直接提取对应小标题下的内容
        #
        #  提取内容：1. 常见描述“集团重视员工健康和安全，以保护员工免受职业危害，按照适用的法律法规制定了安全管理政策。”等
        #           2. 如果大标题下有小标题，可以提取大标题和小标题中间的文段
        #           3. 对于篇幅较小的或没有按照小标题披露的，可以提取标题下全部内容
        #
        #  关键词：health, healthy and safe, working environment, established, safety management strategy,
        #  reduce potential workplace hazards, hazard-free working environment
        #
        #  判定方式：Y-披露并描述以上的提取内容（满足提取内容的任何一条或多条即可）
        #           E- 不常见，但可能会有些金融行业或者与员工劳动伤害很远的行业，在此标题下会有 not material之类的描述
        #           ND-没有任何相关描述
        "path": ["B2 policies - health and safety"],
        "models": [
            # {
            #     # 章节标题和第一个小标题之间
            #     "name": "policy",
            #     "only_inject_features": True,
            #     "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
            #     "ignore_pattern_match": True,
            #     'inject_syllabus_features': [
            #         r'__regex__Health and Safety',
            #     ],
            #     'neglect_features': [r'Product and Service Health and Safety'],
            # },
            {
                "name": "after_row_match",
                "row_pattern": [r"B2"],
                "just_a_para": True,
                "para_pattern": [
                    r"protect.*?health",
                    r"(?P<content>health|Safety|workplace|fire)",
                ],
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "multi": True,
                "only_inject_features": True,
                "include_shape": True,
                "inject_syllabus_features": [
                    r"__regex__Pay Attention to Occupational Health",
                    r"__regex__Maintain the Safety System",
                ],
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_before_first_chapter": True,
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__Health and Safety__regex__General Disclosure and KPI",
                ],
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "inject_syllabus_features": [
                    r"__regex__(?<!CUSTOMER )Healthy? and Safety",  # (?<!CUSTOMER) file63972
                    r"__regex__(?<!CUSTOMER )HEALTH AND WELL-BEING",  # file63983
                    r"__regex__(?<!CUSTOMER )Health and Safety__regex__General Disclosure and KPI",
                    r"__regex__employee.*health",
                    r"__regex__Occupational Health and Safety",
                ],
                "neglect_features": [
                    r"Safety Training",
                ],
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"(?P<content>health|Safety|workplace|fire)",
                    r"(?P<content>protect.*?health)",
                ),
            },
            DEFAULT_MODEL,
        ],
    },
    {
        "path": ["B2 law compliance - health and safety"],
        "models": [
            # ----- 精确匹配 start -----
            {
                "name": "twice_row_match",
                "first_cell": False,
                "row_pattern": COMPLY_LAW_PATTERN,
                "merge_valid_rows": False,
                "second_pattern": [
                    r"B2(\s+|$|[a-zA-Z])",  # file63997披露在index表格中
                ],
            },
            {  # file63974 63979
                "name": "syllabus_based",
                "only_inject_features": True,
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_before_first_chapter": True,
                "break_para_pattern": [r"B2\.1"],
                "inject_syllabus_features": [
                    r"__regex__Employees? Health and Safety",
                ],
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": COMPLY_LAW_PATTERN,
                },
                "table_model": "empty",
            },
            # ===== 精确匹配 end =====
            {
                "name": "twice_para_match",
                "multi_elements": True,
                "use_all_elements": True,
                "syllabus_regs": [],
                "neglect_syllabus_regs": [
                    r"Product Health and Safety",
                    r"production",
                    r"Labou?r Standards?",
                    r"B1|B[3-8]|A[1-3]",
                ],
                "paragraph_pattern": COMPLY_LAW_PATTERN,
                "neglect_pattern": (r"^[a-zA-Z]\.", r"^•", r"relat(?:(?!\.).)* to (products?|services?)"),
                "second_pattern": (
                    r"health",
                    r"Safety",
                    r"workplace",
                    r"working environment",
                    r"safe working environment",
                    r"protecting employees?",
                ),
            },
            {
                "name": "twice_row_match",
                "first_cell": False,
                "row_pattern": COMPLY_LAW_PATTERN,
                "merge_valid_rows": False,
                "second_pattern": [
                    r"B2(\s+|$)",
                    r"health",
                    r"Safety",
                    r"workplace",
                    r"safe working environment",
                    r"protecting employees",
                ],
            },
            {
                "name": "syllabus_based",
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__Compliance Management",
                ],
                "paragraph_model": "empty",
                "table_model": "row_match",
                "table_config": {
                    "row_pattern": [r"health and safety"],
                },
            },
            # ----- 宽泛规则 start -----
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_before_first_chapter": True,
                "only_first": True,
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"Health and Safety",
                ],
            },
            DEFAULT_MODEL,
            # ===== 宽泛规则 end =====
        ],
    },
    {
        # 此条规则是披露报告年度在内的过去三年中每年发生的与工作有关的死亡人数和发生率。
        # 提取内容：提取fatality相关的数据或描述所在的图表或段落或句子，nil或no或not判断为Y
        # 常见关键词：fatalities, fatal accidents, death，B2.1
        #         可从index给出的各个KPI的位置去定位框选；一般在标题Health and Safety下
        #    当无上述关键词，但是有“no work safety-related incident或no work safety-related accident，
        #    或no work injury”的描述，需要提取该描述判断为Y
        "path": ["KPI B2.1 - work-related fatalities"],
        "models": [
            {
                "name": "after_row_match",
                "row_pattern": [r"B2.1|B-2.1"],
                "just_a_para": True,
                "filter_content_answer": True,
                "para_pattern": [
                    # r'work[\-\s]related fatalities',
                    r"work-related fatalities in the past",
                    r"did not have any work-related fatalities",
                    r"not have any violation.*?work-related",
                    r"(?P<content>no work safety-related incident|no work safety-related accident|no work injury)",
                    r"There were no work-related fatalities occurred",
                    r"been injured or died due to work",
                ],
                "middle_rows": True,
                "table_title": [
                    r"職業健康",
                    r"安全表現",
                ],
                "start_regs": [r"B2\.1"],
                "end_regs": [r"B2\.2"],
                "direct_answer_pattern": [
                    r"work-related (deaths|fatalities)",
                ],
            },
            {
                "name": "middle_rows",
                "ignore_index_table": True,
                "regs": [
                    r"work-related",
                ],
                "start_regs": [
                    r"B2\.1",
                ],
                "end_regs": [
                    r"B2\.2",
                ],
            },
            {
                "name": "row_match",
                "ignore_index_table": True,
                "multi": True,
                "row_pattern": [
                    r"work-related deaths",
                    r"B2\.1",
                    r"Fatalit 死亡個",
                    r"Rate of work-related fatalities",
                    r"Number and rate of fatality caused by work-related",
                    r"Number of Work-\s?Related Fatality and Percentage",
                    r"Number of work-related fatalities",
                    r"^Fatalities",
                    r"^Fatality rate",
                ],
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"no fatality cases related to our employees occurred",
                    r"did not have any work-related fatalities",
                    r"protect.*?health",
                    r"not.*?violation",
                    r"incidents of workplace accidents",
                    r"(?P<content>work related|fatalities|fatal accidents|occupational|death|B2.1)",
                    r"(?P<content>no work safety-related incident|no work safety-related accident|no work injury)",
                    r"(?P<content>work-related fatalit)",
                    r"been injured or died due to work",
                ),
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [],
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "multi_elements": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"(?P<content>work related|fatalities|fatal accidents|occupational|death|B2.1)",
                        r"(?P<content>no work safety-related incident|no work safety-related accident|no work injury)",
                    ),
                },
                "table_model": "empty",
            },
            # DEFAULT_MODEL,
        ],
    },
    {
        # 提取内容：提取work injury+Lost相关的数据或描述所在的图表或段落或句子，nil或no或not判断为Y
        # 常见关键词：work related/lost days/sickness/work injury/accidents+ lost days,B2.2。
        #        可从index给出的各个KPI的位置去定位框选；一般在标题Health and Safety下
        #    当无上述关键词，但是有“no work safety-related incident或
        #    no work safety-related accident，或no work injury”的描述，需要提取该描述判断为Y
        "path": ["KPI B2.2 - work injury lost days"],
        "neglect_table_title_missing_crude_regs": [
            r"Employee work-related fatalities over the past three years",
        ],
        "models": [
            {
                "name": "after_row_match",
                "row_pattern": [r"B2.2"],
                "just_a_para": True,
                "para_pattern": [
                    r"injuries",
                    r"working-related",
                    r"(?P<content>work[\-\s]related|sickness|work injury|accidents|occupational|B2.2)",
                    r"(?P<content>no work safety-related incident|no work safety-related accident|no work injury)",
                    r"(Lost|days).*?(work related|lost days|sickness|work injury|accidents)",
                    r"no production accident",
                ],
                "second_pattern": [
                    r"lost days",
                    r"lost working days",
                    r"(had|was|were|is) (zero|\d+|no)",
                    r"no (work-related fatalities|lost day)",
                    r"no incident of work-related fatality",
                    r"no working day lost",
                    r"no production accident",
                ],
                "middle_rows": True,
                "table_title": [],
                "start_regs": [r"B2\.2"],
                "end_regs": [r"B2\.3"],
                "direct_answer_pattern": [
                    r"loss.*?work-related injur",
                    r"(had|was|were|is) (zero|\d+|no)",
                    r"no (work-related fatalities|lost day)",
                    r"no incident of work-related fatality",
                    r"no working day lost",
                    r"no production accident",
                ],
            },
            {
                "name": "row_match",
                "multi": True,
                "ignore_index_table": True,
                "row_pattern": [
                    r"Lost Days?.*?Work(-related)?\s?(Injury|injuries)",
                    r"Lost Days? due to Injury",
                    r"No\. of Injuries at work",
                    r"Workdays? lost.*?(work-related|fatalities)",
                    r"B2\.2",
                    r"work day lost due to work-related injuries",
                ],
            },
            {
                "name": "twice_para_match",
                "multi_elements": True,
                # "strict_limit": True,
                "syllabus_regs": [],
                "neglect_syllabus_regs": [],
                "paragraph_pattern": (
                    r"(?P<content>work related|lost days|sickness|work injury|accidents|occupational|B2.2)",
                    r"(?P<content>no work safety-related incident|no work safety-related accident|no work injury)",
                    r"(Lost|days).*?(work related|lost days|sickness|work injury|accidents)",
                    r"no production accident",
                ),
                # 'neglect_pattern': (
                # ),
                "second_pattern": (
                    r"lost (labor )days",
                    r"was (zero|\d+)",
                    r"nor? (any )?(work-related fatalities|l[ao]st day)",
                    r"no production accident",
                ),
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"incidents of work injuries arose",
                    r"(Lost|days).*?(work related|lost days|sickness|work injury|accidents)",
                    r"no production accident",
                ),
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__Accident Rate Analysis",
                    r"__regex__Employee Development",
                ],
                "multi_elements": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"(?P<content>work related|lost days|sickness|work injury|accidents|B2.2)",
                        r"(?P<content>no work safety-related incident|no work safety-related accident|no work injury)",
                        r"LTIFR",
                        r"no production accident",
                    ),
                },
                "table_model": "empty",
            },
        ],
    },
    {
        # B2涉及到的规则都是关于health and safety相关的内容，B2.3提取的是公司采用的职业健康和安全措施，以及如何实施和监测这些措施；
        #  所在位置：1. health and safety的大标题下（一般位置比较固定，都在这个章节下定位）
        #
        #  提取内容：1. 常见描述形式为一段话末尾加冒号，然后按条描述明细措施，需要提取这段话加下面的所有明细
        #           2. 有小标题的，提取小标题下面的所有文段；或者除了policy，law以及B2.1、B2.2数据描述以外的其他文段
        #           3. 单独存在关于新冠的措施的小标题下，包含COVID关键词的，提取小标题下的全部内容
        #           3. 对于篇幅较小的或没有按照小标题披露的，可以提取包含各类动词的文段，如 check，daily, provide, organize,
        #           required, regularly等
        #
        #  关键词：health, healthy and safe, occupational, healthy, safety, measures, training, check，daily, provide,
        #  organize, required, regularly, first-aid
        #
        #  判定方式：Y-披露并描述以上的提取内容（满足提取内容的任何一条或多条即可）
        #           E- 不常见，但可能会有些金融行业或者与员工劳动伤害很远的行业，在此标题下会有 not material之类的描述
        #           ND-没有任何相关描述
        "path": ["KPI B2.3 - health and safety measures"],
        "models": [
            {
                "name": "after_row_match",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "row_pattern": [r"B2.3"],
                "multi_elements": True,
                "multi_pages": True,
                "ignore_syllabus": [
                    r"^Health and Safety$",  # 忽略范围太大的标题
                ],
                "para_pattern": [
                    r"COVID-?19|preventive measures",
                    # r'facilities|safety performance',
                    r"adopted the following measures:$",
                    r"optimized the physical examination plan",
                    r"optimized.*?insurance",
                    r"provided.*?insurance",
                    r"formulated.*?emergencies",
                    r"monitors the exposure risks",
                    r"organised fire drills",
                    r"safety training",
                    r"safety monitoring mechanism",
                    r"strictly abided to relevant preventive",
                    r"conduct fire drills",
                    r"requires.*?suppliers.*?certified",
                    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/894#note_225686
                    r"physical and mental health of employees",
                ],
                "neglect_para_pattern": [
                    r"during the reporting period",
                    r"During the past.*?years",
                    r"safety performance:$",
                    r"lifelong learning",
                    r"Development and Training",
                ]
                + NEGLECT_PAGE_HEADER_PATTERN,
                "sub_syllabus": [
                    r"Normalized Management of the Pandemic",
                    r"Staff Health Protection",
                    r"Fire Safety Management",
                    r"Staff Communication and Care",
                ],
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "multi": True,
                "inject_syllabus_features": [
                    r"__regex__Fire Safety Management",
                    r"__regex__Preventative Measures on the Pandemic",
                    r"__regex__B2: Health and Safety",
                    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/971#note_240676
                ],
            },
            {
                "name": "syllabus_elt_v2",
                # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/971#note_240680
                "ignore_pattern": [
                    "Property Development and Investment|Hospitality|Leisure",
                    *NEGLECT_PAGE_HEADER_PATTERN,
                ],
                "only_inject_features": True,
                "multi": True,
                "inject_syllabus_features": [
                    r"__regex__Health and Safety__regex__Monitoring System",
                    r"__regex__Health and Safety__regex__Safety Organisation",
                    r"__regex__Health and Safety (train|Measures)",
                    r"__regex__Precautions Against Covid-19",
                    r"__regex__(Prevent|Response).*?(of|for).*?Covid[-\s]?19",
                    r"__regex__Employee Health and (Care|safety)",
                    r"__regex__Occupational Hea\s?lth( and Safety)?",
                    r"__regex__Work Environment Safety",  # file63976
                    r"__regex__emp ?loyee safety",  # file63976
                    r"__regex__Safety management",  # file63976
                    r"__regex__Pandemic Prevention",  # file63976
                ],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__Health and Safety",
                    r"__regex__Health & Safety",
                    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/894#note_230394
                    r"__regex__SAFETY TRAINING AND STAFF PHYSICAL EXAMINATION",  # file63983
                    r"__regex__(Prevent|Response).*?(of|for|to).*?Covid[-\s]?19",
                    r"__regex__Safe working environment",
                    r"__regex__Health management initiatives",
                    r"__regex__COVID-19 Pandemic: Office Preventive Measures",
                    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/971#note_240677
                ],
                "multi": True,
                "multi_elements": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"COVID-?19|preventive measures|facilities|safety performance",
                        r"safety training",
                        r"safety monitoring mechanism",
                        r"healthy and safe|safety|measures|training|daily|provide|organize|required|first-aid",
                        r"strictly abided to relevant preventive",
                        r"conduct fire drills",
                        r"take various measures to minimise",
                        r"optimized the physical examination plan",
                        r"optimized.*?insurance",
                        r"provided.*?insurance",
                        r"formulated.*?emergencies ",
                        r"Following the call of vaccination from Hong Kong government",
                        r"set a target rate for health checkups|the mental health|EAP counseling room",
                        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/971#note_240679
                    ),
                    "neglect_pattern": (
                        r"^Aspect B2:\s?Health and Safety$",
                        r"comply with relevant laws and regulations.*?Health Ordinance",
                        r"^during the year",
                        r"^During the Reporting Period",
                        r"number of work injuries",
                    ),
                },
                "table_model": "first_table",
                # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/971#note_240677
                "table_config": {
                    "regs": ["Office Preventive Measures"],
                },
            },
            {
                "name": "para_match",
                "multi_elements": True,
                "paragraph_pattern": (
                    r"(?P<content>COVID 19|preventive measures|facilities|safety performance)",
                    r"(?P<content>safety training)",
                    r"(?P<content>safety monitoring mechanism)",
                ),
            },
            {
                "name": "syllabus_elt_v2",
                "only_inject_features": True,
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "inject_syllabus_features": [
                    # r'__regex__Improvement of HSE System',
                    r"__regex__^B2.*?Health and Safety",
                    r"__regex__II.2.*?Health and Safety",
                    r"__regex__B\(II.*?HEALTH AND SAFETY",
                    # r'__regex__Occupational Health and Safety',
                ],
            },
            DEFAULT_MODEL,
        ],
    },
]
