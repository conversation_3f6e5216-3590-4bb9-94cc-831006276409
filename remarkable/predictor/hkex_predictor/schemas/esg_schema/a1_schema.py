"""
Jura4 ESG
"""

from remarkable.common.common_pattern import EXPLAIN_PATTERN as COMMON_EXPLAIN_PATTERN
from remarkable.predictor.hkex_predictor.schemas.esg_schema.common_pattern import (
    COMPLY_LAW_PATTERN,
    DEFAULT_MODEL,
    NEGLECT_PAGE_HEADER_PATTERN,
)

EXPLAIN_PATTERN = tuple(COMMON_EXPLAIN_PATTERN)


DIRECTORY_START = r"^\d+\.\d+.(\d+)?"  # 目录中的段落


predictor_options = [
    {
        # A1-POLICY 常见的提取内容、位置及判断规则
        # 提取内容：
        # 涉及emission政策的段落，段落中常包含 emission, environment, policy, management， GHG, pollution, carbon dioxide等词语。
        # 可提取包含emission标题下的全部内容。在有index的情况下，去index指明的位置提取会更准确
        # E的判断：
        # 【简单情况】：not disclosed, not available, NA, 【复杂情况】：not involve，或not engage，或not generate或no generation或not produce 大气污染排放物，not+ impact且没有任何政策相关的描述,或due to+nature
        # E的情况较少，大概不到10%的比例
        # Y的判断： 有：emission相关的policy或management或measure或reduce或control等措施的描述。
        # 【要注意】：当有“not involve，或not engage，或not generate或no generation或not produce 大气污染排放物”的描述，但是同时又有政策措施相关的描述，仍判断Y而非E
        "path": ["A1 policies - emissions"],
        "models": [
            {
                "name": "para_match",
                "paragraph_pattern": (r"KPI A1\.1 to KPI A1\.6 are not applicable",),
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "multi": True,
                "only_inject_features": True,
                "only_first": True,  # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/946#note_232893
                "inject_syllabus_features": [
                    r"__regex__ENVIRONMENT__regex__Emissions__regex__^(?:(?!Table).)*((Air|gas) emissions|ghg|green\s?house|types of Emissions)",
                    r"__regex__ENVIRONMENT__regex__OPERATION__regex__Emission",
                    # r'__regex__ENVIRONMENT__regex__Emission',
                ],
            },
            {
                "name": "twice_para_match",
                "multi_elements": True,
                "use_all_elements": False,  # 基于初步定位结果再次过滤，避免结果范围过大
                "syllabus_regs": [],
                "neglect_syllabus_regs": [r"climate change"],
                "paragraph_pattern": (  # 污染物，名词
                    r"GHG|greenhouse gas|(?<![“\"]peak )carbon dioxide|methane|(nitrous|nitrogen) oxides?|(air|carbon) emission|particulate matters|NOX|SO2|sulfur dioxide|CO2|CH4|N2O|O3|ozone",
                    r"(GHG|greenhouse gas|direct|pollutant) emissions",
                    r"embodied carbon",
                    r"carbon footprint|waste handling|air pollutant|air quality",
                ),
                "neglect_pattern": (),
                "second_pattern": (  # 对污染物的处理、政策，动词
                    r"not involve|not engage|not disclosed|not generate|no generation|not produce|not quantify",
                    r"include",  # many files
                    r"measures? to reduce",  # 63997
                    r"devis(?:(?!\.).)*plan",  # 63997
                    r"within our operations.",
                    r"operations are",  # 63998
                    r"implement(?:(?!\.).)*(actions)|(system)",  # 63988
                    r"carry out(?:(?!\.).)*activities",  # 63981
                    r"carbon footprint|waste handling|air pollutant|air quality",
                ),
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "multi": True,
                "only_inject_features": True,
                # 'break_when_table': True,
                # "only_before_first_chapter": True,  # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/803#note_212766
                "inject_syllabus_features": [
                    r"__regex__Emissions? Management",
                    r"__regex__ENVIRONMENT__regex__^(?:(?!Table).)*((Air|gas) emissions|ghg|green\s?house|types? of Emissions)",
                    r"__regex__ENVIRONMENTAL.*?REPORT__regex__ENVIRONMENTAL__regex__Environmental Policies",
                    r"__regex__General Disclosure and KPIs",
                    # r'__regex__A1.*?Emissions',
                    r"__regex__^(?:(?!Table).)*Air.*?Emissions",
                    r"__regex__^(?:(?!Table).)*gas.*?Emission",
                    r"__regex__^(?:(?!Table).)*type of emissions",
                    r"__regex__Emissions Contro",  # file63976章节识别错误
                    r"__regex__(?<!Resource Consumption and )ghg.*?Emission",
                    r"__regex__carbon neutrality",
                    r"__regex__Greenhouse Gas (GHG) and Energy Consumption Performance",  # file64001
                    r"__regex__Greenhouse gas management",
                    r"__regex__Carbon Emission",
                    r"__regex__GHG Emission",
                    r"__regex__^Emission$",
                ],
                "neglect_features": [
                    r"target",
                    r"reduction",
                    r"Publishing the Action Plan for Implementing Peak Carbon Dioxide Emissions and Carbon Neutrality",  # 63977
                ],
                "neglect_parent_features": [r"climate change"],
            },
            {
                "name": "para_match",
                "multi_elements": True,
                # "force_use_all_elements": True,
                # "enum_from_multi_element": True,
                "paragraph_pattern": (
                    r"(?P<content>(not involve|not engage|not disclosed|not generate|no generation|not produce|not quantify)(?:(?!\.).)*(NOX|SO2|CO2|CH4|N2O|O3|ozone))",
                    r"(?P<content>not own.*?emissions)",
                    r"(?P<content>GHG|(?<![“\"]peak )carbon dioxide|CO2|methane|CH4|nitrous oxide|N2O|ozone|O3|(air|carbon) emission|particulate matters)",
                    r"(?P<content>more environment-friendly fuel)",
                    r"^(?:(?!Note).)*(?P<content>greenhouse gas emissions? (?!generated by purchased electricity))",
                ),
                "neglect_pattern": (
                    # r'reduction',
                    r"carbon emission rights derivatives",  # 碳排放权衍生品file63977
                    r"was successfully issued on the Shenzhen Stock Exchange",  # 63977
                ),
            },
            DEFAULT_MODEL,
        ],
    },
    {
        #    1.**优先根据index去定位相关内容的位置去定位****是否遵守**的关键词框选其段落
        #    2.当不存在index的时候，先定位其**是否遵守**的关键词，在根据周围的描述判定属于哪一条规则的law
        #    （如：周围关键词为emission, pollutions, greenhouse gases, GHG等，则是A1law）
        #  涉及“明确表明没有相关政策和法律法规适用于公司”的描述。 比如no laws and regulations。E的情况一般较少出现
        "path": ["A1 law compliance - emissions"],
        "models": [
            {
                "name": "row_match",
                "ignore_index_table": True,
                "row_pattern": [r"^Emissions"],
            },
            {  # file63957
                "name": "syllabus_based",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "break_para_pattern": [r"A1\.1"],
                "inject_syllabus_features": [
                    r"__regex__A1(\.|\s+|$)",
                ],
                "paragraph_model": "para_match",
                "para_config": {"paragraph_pattern": COMPLY_LAW_PATTERN},
                "table_model": "empty",
            },
            {
                "name": "twice_row_match",
                "first_cell": False,
                "merge_valid_rows": False,
                "ignore_index_table": True,
                "row_pattern": COMPLY_LAW_PATTERN,
                "neglect_row_pattern": [
                    r"adopted corresponding measures",  # file63959
                    r"Product Health and Safety",
                    r"Labou?r Standards?",
                    r"B[1-8]|A2|A3",
                ],
                "second_pattern": [
                    r"A1(\s+|$|[a-zA-Z])",  # file63997披露在index表格中
                    r"Emission",  # file63983披露在index表格中
                ],
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"KPI A1\.1 to KPI A1\.6 are not applicable",
                    r"no relevant law",
                ),
                "neglect_row_pattern": [
                    r"adopted corresponding measures",  # file63959
                    r"Product Health and Safety",
                    r"Labou?r Standards?",
                    r"B[1-8]|A2|A3",
                ],
            },
            {
                "name": "twice_para_match",
                "multi_elements": True,
                "syllabus_regs": [],
                "neglect_row_pattern": [
                    r"adopted corresponding measures",  # file63959
                    r"Product Health and Safety",
                    r"Labou?r Standards?",
                    r"B[1-8]|A2|A3",
                ],
                "paragraph_pattern": COMPLY_LAW_PATTERN,
                "second_pattern": (
                    r"emission",
                    r"pollution",
                    r"greenhouse gas(es)?",
                    r"GHG",
                    r"Air Pollution Control Ordinance, ",
                    r"low-carbon",
                    r"environmental management|(environment|natural) resources?",  # file63959
                ),
            },
            {
                "name": "syllabus_based",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__ENVIRONMENTAL__regex__A1.*?Emissions",
                    r"__regex__Exhaust Management",
                ],
                "multi_elements": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (r"(?P<content>law|ordinance|regulation|legislation)",),
                },
                "table_model": "empty",
            },
            {
                "name": "para_match",
                "neglect_row_pattern": [
                    r"adopted corresponding measures",  # file63959
                    r"Product Health and Safety",
                    r"Labou?r Standards?",
                    r"B[1-8]|A2|A3",
                ],
                "paragraph_pattern": (
                    # r'(?P<content>law|ordinance|regulation|legislation)',
                    r"the Group did not identify any material non-compliance related to emissions",
                    r"complies with all applicable environmental regulations",
                ),
            },
            # {
            #     "name": "kmeans_classification",
            # },
        ],
    },
    {
        # 需要定位针对air emission排放类型和对应排放数据的表格段落，常见类型有NOx,SOx,PM等.
        # 出现位置：1.报告末尾的performance data summary,KPI summary,performance table,INDEX
        #          2.environment章节的末尾environmental data
        #          3.标题A1\EMISSION\A1.1\emission category下面
        #          4.如果没有直接明确给出NOx,SOx,PM等emission类型，Scope 1,Scope 2及Scope 3都算是types of emissions
        "path": ["KPI A1.1 - emission type and data"],
        "models": [
            {
                "name": "after_row_match",
                "row_pattern": [r"A1.1"],
                "middle_rows": True,
                "start_regs": [
                    r"Greenhouse Gas Emis",
                    r"GHG emissions",
                ],
                "end_regs": [r"Energy consumption"],
                "just_a_para": True,  # for special cases 61008
                "para_pattern": [
                    r"^溫室氣體排放概覽$",
                ],
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"KPI A1\.1 to KPI A1\.6 are not applicable",
                    r"not directly produce",
                ),
            },
            {
                "name": "syllabus_based",
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__Pollutant Discharge Management System and Goals",
                ],
                "paragraph_model": "empty",
                "para_config": {},
                "table_model": "row_match",
                "table_config": {
                    "first_cell": True,
                    "row_pattern": [
                        r"Emissions of (VOCs|CO2)",
                    ],
                },
            },
            {
                "name": "esg_row_match",
                "multi": True,
                # "first_cell": False,
                "row_pattern": [
                    r"NOx|SOx|Particles|Nitrogen Oxide|Sulphur Oxide",
                    r"Particular|Particulate",  # r'Particular matter', r'PM',
                    r"Air pollutants",
                    r"Air emissions",
                    r"oxides|particles",
                    r"^PM$",  # r'Particular matter', r'PM',
                    r"PM.*?(物|微粒)",  # r'Particular matter', r'PM',
                    r"Sulphur dioxide",  # r'Particular matter', r'PM',
                    r"–\s?(office|projects)",  # r'Particular matter', r'PM',
                    r"NO\s?氮氧",
                    r"SO\s?硫氧",
                    r"NO emissions X",
                    r"SO emissions X",
                    r"RSP emissions",
                ],
            },
            {
                "name": "row_match",
                # "multi": True,
                "ignore_index_table": True,
                "first_cell": False,
                "merge_row": False,
                "neglect_row_pattern": [r"\d+[-—–]\d+"],
                "row_pattern": [
                    r"A\s?1\.1$",
                ],
            },
            {
                "name": "row_match",
                "multi": True,
                # "first_cell": False,
                "ignore_index_table": True,
                "neglect_row_pattern": [r"\d+[-—–]\d+"],
                "row_pattern": [
                    r"NOx|SOx|Particles|Nitrogen Oxide|Sulphur Oxide",
                    r"Particular|Particulate",  # r'Particular matter', r'PM',
                    r"Air pollutants",
                    r"Air emissions",
                    r"oxides|particles",
                    r"^PM$",  # r'Particular matter', r'PM',
                    r"PM.*?物",  # r'Particular matter', r'PM',
                    r"Sulphur dioxide",  # r'Particular matter', r'PM',
                    r"–\s?(office|projects)",  # r'Particular matter', r'PM',
                ],
            },
            {
                "name": "table_title",
                "feature_white_list": [
                    r"GHG Emissions",
                    r"Waste Gas Generation and Management",
                    r"air emission",
                ],
            },
            {
                "name": "para_match",
                # "multi_elements": True,
                "paragraph_pattern": (
                    r"not? (directly )?(involve|engage|disclosed|generate|generation|produce)",
                    r"limited|not quantify",
                    r"A1.1|category",
                ),
            },
            DEFAULT_MODEL,
        ],
    },
    # 需要定位针对greenhouse gas emission/GHG emission中数据披露的情况，
    # 直接排放的是scope1，
    # 间接排放的是scope2，
    # 其他的是scope3。
    # 出现位置：1.报告末尾的performance data summary,KPI summary,performance table,INDEX
    #          2.environment章节的末尾environmental data
    #          3.标题A1\EMISSION\A1.2\greenhouse gas emission\GHG\direct emission下面
    #          4.如果GHG所在表格没有分scope1、2、3，但是根据表格附近段落可以知道所产生的排放是有日常业务产生的，那么把整个表框
    #            在scope1答案处，选择Y
    {
        "path": ["KPI A1.2 part 1 - Scope 1"],
        "models": [
            {
                "name": "syllabus_based",
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__Greenhouse gas emission",
                ],
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (r"Indirect emissions of carbon dioxide in greenhouse gases",),
                    "use_direct_elements": True,
                },
                "table_model": "empty",
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"KPI A1\.1 to KPI A1\.6 are not applicable",
                    r"did not consume resources.*?Scope 1 emission",
                    r"the Group did not discharge exhaust gas and direct GHG emission \(scope 1\)",
                ),
            },
            {
                "name": "middle_rows",
                "regs": [
                    r"Scope 1",
                    r"GHG emissions",
                    r"Direct emission",
                    r"Greenhouse gases",
                ],
                "title_regs": [r"TABLE OF KEY PERFORMANCE"],
                "neglect_title_regs": [r"^\d+$"],
                "start_regs": [
                    r"Scope 1",
                    r"Direct GHG emissions",
                    r"Direct emissions",
                    r"Total emission of greenhouse gas",
                    r"GRI 305-[123]",
                ],
                "end_regs": [
                    r"Scope 2",
                    r"A1\.3",
                    r"Indirect GHG emissions",
                    r"hazardous waste",
                    # r'Direct emissions',
                    r"GRI 305-4",
                ],
            },
            {
                "name": "row_match",
                "multi": True,
                "ignore_index_table": True,
                "first_cell": False,
                "row_pattern": [
                    r"Scope 1",
                    r"Scope I(?!I)",
                    r"(?<!in)Direct.*?emissions",
                    # r'Aspects 1.1|Nitrogen Oxides|Respiratory Suspended Particles' # #todo 60877 待修复
                ],
            },
            {
                "name": "first_table",
                "regs": [
                    r"Scope 1",
                ],
            },
            {
                "name": "scope",
                "regs": [r"Scope 1"],
                "start_regs": [
                    r"^Scope 1",
                ],
                "end_regs": [
                    r"^Scope 2",
                ],
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"not involve|not engage|not disclosed|not generate|no generation|not produce|not quantify",
                    r"carbon dioxide|methane|nitrous oxide|hydrofluorocarbons|perfluorocarbons and sulphur hexafluoride",
                    r"A1.2|direct emissions",
                    r"not to compile.*?scope\s?1",
                    # r'(?P<content>direct|reenhouse gas emission)',
                ),
            },
        ],
    },
    {
        "path": ["KPI A1.2 part 2 - Scope 2"],
        "models": [
            {
                "name": "syllabus_based",
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__Greenhouse gas emission",
                    r"__regex__Measures for reducing greenhouse gas",
                ],
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (r"Indirect emissions of carbon dioxide in greenhouse gases",),
                    "use_direct_elements": True,
                },
                "table_model": "empty",
            },
            {
                "name": "para_match",
                "paragraph_pattern": (r"KPI A1\.1 to KPI A1\.6 are not applicable"),
            },
            {
                "name": "middle_rows",
                "regs": [
                    r"Scope 2",
                    r"GHG emissions",
                    r"Indirect emissions",
                ],
                "title_regs": [r"TABLE OF KEY PERFORMANCE"],
                "neglect_title_regs": [r"^\d+$"],
                "start_regs": [
                    r"Scope 2",
                    r"Indirect GHG emissions",
                    r"Indirect emissions",
                    r"GRI 305-[123]",
                ],
                "end_regs": [
                    r"Scope 3",
                    r"^Total",
                    r"A\s?1\.3",
                    r"Scope 1 & Scope 2",
                    r"GRI 305-4",  # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/1562#note_284510
                ],
            },
            {
                "name": "row_match",
                "ignore_index_table": True,
                "first_cell": False,
                "multi": True,
                "row_pattern": [
                    r"Scope 2",
                    r"Scope II",
                    r"Indirect.*?emissions?",
                    r"CO2 equivalent emissions from purchased",
                ],
                "neglect_row_pattern": [
                    r"Scope 3",
                ],
            },
            {
                "name": "first_table",
                "regs": [r"Scope 2"],
            },
            {
                "name": "shape_title",
                "regs": (r"The (GHG|greenhouse gas) emission data is set out in the table below",),
            },
            {
                "name": "scope",
                "regs": [r"Scope 2"],
                "start_regs": [
                    r"^Scope 2",
                ],
                "end_regs": [
                    r"^Scope 3",
                ],
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"not involve|not engage|not disclosed|not generate|no generation|not produce|limited|not quantify",
                    r"carbon dioxide|methane|nitrous oxide|hydrofluorocarbons|perfluorocarbons and sulphur hexafluoride",
                    r"scope2",
                    # r'A1.2|scope2|greenhouse gas emission|energy',
                ),
            },
            # DEFAULT_MODEL,
        ],
    },
    {
        "path": ["KPI A1.2 part 3 - Scope 3"],
        "models": [
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"KPI A1\.1 to KPI A1\.6 are not applicable",
                    r"The only source of GHG emission is Scope 2 GHG emissions",  # only Scope 2, 说明没有 Scope 3
                ),
            },
            {
                "name": "scope",
                "regs": [
                    r"Scope 3",
                ],
                "start_regs": [
                    r"^Scope 3",
                ],
                "end_regs": [
                    r"^Scope 3",
                    r"A1\.3",
                ],
            },
            {
                "name": "middle_rows",
                "regs": [
                    r"Scope 3",
                    # r'GHG emissions',
                ],
                "title_regs": [r"TABLE OF KEY PERFORMANCE"],
                "start_regs": [
                    r"Scope 3",
                    r"GRI 305-[123]",
                ],
                "end_regs": [
                    r"total",
                    r"A\s?1\.3",
                    r"employee",
                    r"GRI 305-4",
                ],
            },
            {
                "name": "row_match",
                # "ignore_index_table": True,
                "first_cell": False,
                "multi": True,
                "row_pattern": [
                    r"^Scope 3",
                ],
            },
            {
                "name": "first_table",
                "regs": [r"Scope 3"],
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"(?P<content>not involve|not engage|not disclosed|no generation|not produce|not quantify)",
                    r"(?P<content>carbon dioxide|methane|nitrous oxide|hydrofluorocarbons|perfluorocarbons and sulphur hexafluoride)",
                    r"scope3",
                    r"other indirect emissions",
                ),
                "neglect_pattern": (
                    r"scope [12]",
                    DIRECTORY_START,
                ),
            },
            # DEFAULT_MODEL,
        ],
    },
    {
        # 需要定位到含有hazardous waste数据所在图表、段落或句子，包括产生的hazardous waste总量及强度（每单位产量、每项设施）
        # 出现位置：1.报告末尾的performance data summary,KPI summary,performance table,INDEX
        #          2.environment章节的末尾environmental data
        #          3.标题A1\EMISSION\A1.3\hazardous waste\waste下面
        "path": ["KPI A1.3 - hazardous waste"],
        "models": [
            {
                "name": "after_row_match",
                "first_cell": False,
                "row_pattern": [r"A1.3"],
                "middle_rows": True,
                "start_regs": [r"(?<!non-)hazardous wastes?"],
                "end_regs": [
                    r"non-hazardous wastes?",  # todo uat 65869
                    r"Construction material",
                ],
                "just_a_para": True,
                "para_pattern": [
                    r"A1.3|(?<!non-)hazardous wastes?",
                    r"not generate.*?hazardous wastes?",
                ],
                "direct_answer_pattern": [
                    r"not applicable",
                    r"No material.*?hazardous waste",
                    r"has not identified any hazardous waste",
                ],
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"KPI A1\.1 to KPI A1\.6 are not applicable",
                    r"KPI A1.3 are not applicable",
                    r"A1\.3.*?(not been|is not)\s?disclosed",
                    r"(?<!Non-)Hazardous waste.*?not include",
                    r"not generate.*?hazardous waste",
                    r"^(?!In ).*no (significant)?hazardous wastes? (is|was|are|were|or .* (is|was|are|were)) (generated|produced|recorded)",
                    r"hazardous waste generation was insignificant",
                    r"no significant hazardous wastes? (generated|produced)",
                    r"not produce (significant amounts of|any|any \w+) hazardous (wastes?)?",
                    r"Regarding hazardous wastes?.*not (produce|generated)",
                    r"the generation of hazardous and non-hazardous waste is not material to us",
                    r"he Group.*?operations do not involve hazardous waste",
                ),
            },
            {
                "name": "middle_rows",
                "ignore_index_table": True,
                "regs": [
                    r"A1.3",
                    r"^waste$",
                    r"(?<!non-)hazardous waste",
                ],
                "start_regs": [r"(?<!non-)hazardous wastes?"],
                "end_regs": [
                    r"non-hazardous wastes?",
                    r"Energy usage",
                    r"Use Of Energy",
                    r"Construction material used",
                ],
            },
            {
                "name": "esg_row_match",
                "row_pattern": [
                    r"(?<!non-)hazardous wastes?",
                ],
            },
            {
                "name": "row_match",
                "ignore_index_table": True,
                "multi": True,
                "row_pattern": [
                    r"(?<!non-)hazardous wastes?",
                    r"Waste (ink|toner)",
                ],
            },
            {
                "name": "table_title",
                "feature_black_list": [
                    r"__regex__^.$",
                    r"__regex__^(Environment[al,]*)$",
                    r"__regex__Emissions$",
                    r"__regex__環境",
                ],
                "feature_white_list": [
                    r"(?<!non-)hazardous wastes?",
                    r"有害廢棄物排放",
                ],
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"not involve|not engage|not disclosed|not generate|no generation|not produce|not quantify",
                    r"no hazardous waste is generated",
                    r"not produce any hazardous waste",
                    r"(?<!non-)hazardous waste",
                ),
            },
            DEFAULT_MODEL,
        ],
    },
    {
        # 需要定位到含有non-hazardous waste数据所在图表段落或句子，包括产生的non-hazardous waste总量及强度（每单位产量、每项设施）
        # 出现位置：1.报告末尾的performance data summary,KPI summary,performance table,INDEX
        #          2.environment章节的末尾environmental data
        #          3.标题A1\EMISSION\A1.4\non-hazardous waste\waste下面
        "path": ["KPI A1.4 - non-hazardous waste"],
        "models": [
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"KPI A1\.1 to KPI A1\.6 are not applicable",
                    r"A1\.4.*?not been disclosed",
                    r"non-hazardous waste generated by our Group ",
                    r"Group generates no hazardous waste",
                    r"the Group will expand its data .*?non-hazardous waste.*?future",
                    r"amount of waste are trivial and no KPI are identified and disclosed",
                    r"non-hazardous.*?wastes.*?negligible",
                    r"expand its data.*?non-hazardous waste.*?future",
                    r"no KPI are identified and disclosed",
                    r"waste are trivial",
                    r"The non-hazardous waste generated was around.*?tonnes",
                    r"non-hazardous waste.*?relatively limited",
                ),
            },
            {
                "name": "table_title",
                "only_inject_features": True,
                "feature_white_list": [
                    r"non-hazardous waste (was )?generate",
                    r"generate non-hazardous waste",
                    r"non-hazardous waste production performance",
                ],
            },
            {
                "name": "row_match",
                "ignore_index_table": True,
                "multi": True,
                "row_pattern": [
                    r"Waste fluorescent tube",  # 特例 64004
                    r"Office Waste",
                ],
            },
            {
                "name": "after_row_match",
                "row_pattern": [r"A\s?1\s?.4"],
                "para_pattern": [
                    r"The non-hazardous waste generated was around.*?tonnes",
                    r"non-hazardous waste.*?relatively limited",
                ],
                "just_a_para": True,
                "table_title": [r"non-hazardous"],
                "middle_rows": True,
                "start_regs": [r"non-hazardous"],
                "end_regs": [],
                "direct_answer_pattern": [
                    r"not applicable",
                    r"provided by the property management companies",
                ],
            },
            {
                "name": "middle_rows",
                "ignore_index_table": True,
                "regs": [
                    r"A1.4",
                    r"hazardous waste",
                    r"C&D.*?Waste",
                ],
                "start_regs": [
                    r"non-\s?hazardous waste",
                    r"C&D.*?Waste",
                ],
                "end_regs": [
                    r"^hazardous waste",
                    r"Use of Resources",
                    r"energy consumption",
                    r"Paper Consumption",
                    r"Exhaust Gas Emissions",
                    r"total hazardous waste",
                ],
            },
            {
                "name": "table_title",
                "only_inject_features": True,
                "feature_white_list": [
                    r"non-hazardous waste",
                ],
                "feature_black_list": [
                    r"__regex__^.$",
                    r"__regex__^(Environment[al,]*)$",
                ],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__A1.4",
                    r"__regex__non-hazardous waste",
                    r"__regex__^Wastes$",
                ],
                "only_inject_features": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        EXPLAIN_PATTERN,
                        r"insignificant",
                        r"waste collector approved by the Environmental Protection Department",
                        r"no plan to set a goal to reduce non-hazardous waste",
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "para_match",
                # "multi_elements": True,
                # "enum_from_multi_element": True,
                "paragraph_pattern": (
                    r"(?P<content>non-hazardous waste)",
                    r"(?P<content>A1.4)",
                    r"(?P<content>During.*?the total construction waste disposed.*?respectively)",
                    r"amount of waste are trivial",
                ),
            },
            DEFAULT_MODEL,
        ],
    },
    {
        # 提取内容：提取涉及到“GHG emission reduction target的数据”及为了reduce GHG/carbon emission 的一些措施”的描述
        #
        # 常见关键词： A1.5, target, aim，future plan, reduce，minimize
        #
        #        可从index给出的各个KPI的位置去定位；或在 emission/GHG emission
        "path": ["KPI A1.5 - emission target"],
        "models": [
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"KPI A1\.1 to KPI A1\.6 are not applicable",
                    r"A1\.5.*?not been disclosed",
                ),
            },
            {
                "name": "after_row_match",
                "row_pattern": [r"A1.5"],
                "multi_elements": True,
                "para_pattern": [
                    r"not set any environmental targets",
                    "(target|aims?) (is|to)",
                    "following energy-saving measures:",
                    "following.*?measures.*?:$",
                    "resources include:$",
                    "following methods to reduce.*?emissions:$",
                    "repairs to be carried out",
                    "in order to reduce.*?pollutants",
                    "improve (energy )?efficiency",
                    "energy consumption analysis",
                    "energy audit",
                    "saving about.*?electricity consumption",
                    "reducing.*?exhaust emissions",
                    "The Group targets to ",
                ],
                "middle_rows": False,
                "direct_answer_pattern": [
                    r"No.*?target is set",
                    r"not set.*?targets",
                ],
            },
            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/878#note_230741
            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/878#note_231052
            {
                "name": "twice_row_match",
                "first_cell": False,
                "row_pattern": [r"air pollutants|green\s?house gas(?: emissions)?"],
                "merge_valid_rows": False,
                "second_pattern": [
                    r"target|aim|future plan|reduce|minimize",
                ],
            },
            {
                "name": "table_title",
                "feature_white_list": [
                    r"steps taken to achieve the target",
                    r"Long-term Strategy and Targets",
                ],
                "only_inject_features": True,
                "first_row_as_title": True,
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                # "multi": True,
                "inject_syllabus_features": [
                    r"__regex__environmental protection__regex__target",
                    r"__regex__(Measures|Targets).*?Emissions",
                    r"__regex__Emissions.*?(Measures|Targets)",
                    r"__regex__environmental policy__regex__Our Environmental Targets",
                ],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__Emissions.*?(Measures|Targets)",
                    r"__regex__Greenhouse Gas Emission",
                    r"__regex__GHG Emissions",
                    r"__regex__Air Emissions",
                    r"__regex__CARBON MANAGEMENT",
                    r"__regex__WASTE GAS AND SEWAGE EMISSIONS",
                    r"__regex__EMISSIONS",
                    r"__regex__OPTIMIZING ENERGY SAVING",
                    r"__regex__RESOURCES CONSUMPTION",
                    r"__regex__Energy Development__regex__Environmental__regex__Policies and Use of Resources",
                    r"__regex__DECARBONISING LOGISTICS OPERATIONS",
                ],
                "multi": True,
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"not set any environmental targets",
                        "(target|aims?) (is|to)",
                        "following energy-saving measures:",
                        "following.*?measures.*?:$",
                        "resources include:$",
                        "following methods to reduce.*?emissions:$",
                        "repairs to be carried out",
                        "in order to reduce.*?pollutants",
                        "Reduce Emission",
                        "energy-saving equipment",
                        "Placing.*?Environmental Protection.*?reminders",
                        "CARBON REDUCTION TARGETS",
                    ),
                    "neglect_pattern": (
                        r"reduce the intensity of total hazardous waste",
                        r"set a target to gradually reduce the intensity of total non-hazardous waste",
                    ),
                },
                "table_model": "special_cells",
                "table_config": {
                    "whole_table": True,
                    "cell_pattern": (r"Reduce.*?Emissions",),
                },
            },
            {
                "name": "para_match",
                "multi_elements": True,
                "enum_from_multi_element": True,
                "neglect_syllabus_regs": [
                    r"CLIMATE CHANGE",
                ],
                "paragraph_pattern": (
                    r"reduce emission",
                    r"establish.*?environmental targets",
                    r"switch off lightings when not in use",
                ),
            },
            DEFAULT_MODEL,
        ],
    },
    {
        # 提取内容：提取涉及到废弃物各种处理方式及数据或描述所在的段落
        # 常见关键词： A1.6, dispose, recycle, reuse, recover
        #        可从index给出的各个KPI的位置去定位；或在 waste management
        # 特殊情况：个别文档仅给了处理（recycle，reuse）的数据，直接框选对应表格或段落
        "path": ["KPI A1.6 part 1 - waste handling"],
        "models": [
            {
                # for explain
                "name": "para_match",
                "paragraph_pattern": (
                    r"KPI A1\.1 to KPI A1\.6 are not applicable",
                    r"Due to the business nature of our Group, certain construction waste is generated in our construction process",
                    r"A1\.6.*?not been disclosed",
                    r"different waste management procedures",  # comply
                    r"took strong measure",  # comply
                ),
            },
            {
                "name": "after_row_match",
                "row_pattern": [r"A1.6"],
                "multi_elements": True,
                "para_pattern": [
                    r"dispose\s|recycle|reuse|recover",
                    r"dispose\s",
                    r"disposed of",
                    r"collected|collection",
                    r"classification|transfer and disposal of wastes",
                    r"disposed responsibly through donation",
                    r"Dispos.*?wastes",
                    r"different waste management procedures",
                    r"delegate to qualified third-party organization",
                    r"strictly standardized the classified collection",
                    r"took strong measure",
                    r"the Group has implemented waste management approach and initiatives",
                    r"keep hazardous waste generation",
                ],
                "middle_rows": True,
                "table_title": [
                    r"Dispos.*?wastes",
                ],
            },
            {
                "name": "special_cells",
                "whole_table": True,
                "cell_pattern": (
                    r"Disposal measure",
                    # r'hazardous waste',
                ),
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "multi": True,
                "inject_syllabus_features": [
                    r"__regex__MEASURES__regex__hazardous waste",
                    r"__regex__Waste.*?Management__regex__PERFORMANCE__regex__MEASURES",
                    r"__regex__Waste.*?Management__regex__Non-hazardous waste",
                    r"__regex__Waste.*?Management__regex__hazardous waste",
                    r"__regex__waste discharge",
                    r"__regex__Solid Waste Handling",
                    r"__regex__WASTE AND MATERIAL MANAGEMENT",
                ],
            },
            {
                "name": "syllabus_based",
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__hazardous Waste",
                    r"__regex__waste managemen",
                    r"__regex__waste discharge",
                    r"__regex__use of Resources",
                    r"__regex__Emissions",
                ],
                "multi_elements": True,
                "multi": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"(?P<content>immaterial|insignificant)",
                        r"dispose\s|reuse|recover",
                        r"disposed of",
                        r"collected (by|in)|collection",
                        r"classification|transfer and disposal of wastes",
                        r"disposed responsibly through donation",
                        r"disposed (\w+\s){0,3}wastes",
                        r"handled according to the requirements",
                        r"transfer and disposal of wastes",
                        r"delegate to qualified third-party organization",
                        r"strictly standardized the classified collection",
                    ),
                    "neglect_pattern": (
                        # r'reduc',
                        r"recovery rate",
                    ),
                    "neglect_syllabus_regs": [
                        # r'reduc',
                        r"recycling",
                    ],
                },
                "table_model": "special_cells",
                "table_config": {
                    "whole_table": True,
                    "cell_pattern": (
                        r"Disposal measure",
                        # r'(?<!non-)hazardous waste',
                    ),
                },
            },
            {
                "name": "para_match",
                # "multi_elements": True,
                "paragraph_pattern": (
                    r"(?P<content>not involve|not engage|not disclosed|not generate|no generation|not produce|limited|not quantify)",
                    # r'(?P<content>immaterial|insignificant|plan|continue)',
                    r"(?P<content>dispose|recycle|reuse|recover|recycling)",
                    r"dispose|recycle|reuse|recover",
                    r"managed centrally by the office property management company",
                    r"not undertake",
                    r"collect garbage",
                    r"local urban management",
                    r"waste category",
                    r"waste.*?taken out",
                    r"dispose\s",
                    r"disposed of",
                    r"collected|collection",
                    r"classification|transfer and disposal of wastes",
                    r"disposed responsibly through donation",
                    r"Dispos.*?wastes",
                ),
            },
            {
                "name": "kmeans_classification",
            },
            DEFAULT_MODEL,
        ],
    },
    {
        # 该规则是框选出公司为排放固体废物而设置的排放目标，或者为了减少产生废物而采取的措施；
        # 如果都没有也可以框选已经实现的减排目标，可以不是对未来目标的设置
        #  所在位置：1. 涉及到waste management的标题下关于waste reduction target的文字段落
        #           2. 在waste management的标题下有关键词waste reduction的小标题下的内容  优先级靠前
        #           3. 如果按照non-hazardous waste和hazardous waste披露的，一般是两处都会涉及到 reduction waste的内容
        #           4. 在ESG报告中单独有个environmental target的标题下，关键词waste的相关内容
        #
        #  提取内容：1. 提取涉及到“waste reduction target”的数据
        #           2. 没数据但有“为了reduce waste 而有一些措施”的描述
        #           3. 当年或目前为止已经实现的reduction，可以不是未来的target
        #           4. 如果上面几点都有，则可以按照上面序号的优先级来全部提取
        #
        #  关键词：（需要在waste相关位置范围内查找）target, aim, future plan, minimize, reduction, reduce, reuse, recycle, prevent
        #
        #  判定方式：Y-披露并描述以上的提取内容（满足提取内容的任何一条或多条即可）
        #           E-明确披露了 not material/not involve/not engage/ not disclosed/ not produce的
        #           E-写明了`没有waste target`的，即使有相关措施，也判定为E
        #           ND-没有任何描述
        "path": ["KPI A1.6 part 2 - waste reduction target"],
        "models": [
            # 特例 GRI 相关 https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/997#note_243521
            {
                "name": "esg_16_part_2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "ignore_missing_crude_answer": True,
                "inject_syllabus_features": [
                    # r'__regex__Measures for waste reduction',
                    r"__regex__Control o f wastewa ter discharge",
                ],
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"Automated rol-shearing machine.*?during the year",
                        r"Cold-cutting machine.*?dur",
                        r"plate casting machine",
                    ),
                },
                "table_model": "empty",
            },
            # for explain
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"KPI A1\.1 to KPI A1\.6 are not applicable",
                    r"A1\.6.*?not been disclosed",
                ),
            },
            {
                "name": "table_title",
                "multi_elements": True,
                "only_inject_features": True,
                "feature_white_list": [
                    r"Environment goals",
                    r"Waste Management Targets",
                ],
            },
            {
                "name": "shape_title",
                "regs": (
                    r"Environment goals",
                    r"Waste Management Targets",
                ),
            },
            {
                "name": "row_match",
                # "whole_table": True,
                "multi": True,
                "row_pattern": [
                    r"^waste (reduction|management)",
                    r"(water|waste|energy|emissions).*?reduce by",
                ],
            },
            # only for explain answer
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__green office",
                ],
                "only_inject_features": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"not set any environmental targets",
                        r"A1.6.*?not applicable",
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "multi": True,
                "inject_syllabus_features": [
                    r"__regex__MEASURES__regex__hazardous waste",
                    r"__regex__Waste.*?Management__regex__PERFORMANCE__regex__MEASURES",
                    r"__regex__Waste.*?Management__regex__Non-hazardous waste",
                    r"__regex__Waste.*?Management__regex__hazardous waste",
                    r"__regex__waste discharge",
                    r"__regex__Solid Waste Handling",
                    r"__regex__WASTE AND MATERIAL MANAGEMENT",
                ],
            },
            {
                "name": "after_row_match",
                "row_pattern": [r"A1.6"],
                "para_pattern": [
                    r"aim.*?(to|at).*?reduc.*?waste",
                    r"be recycled",
                    r"measures.*?handle wastes:",
                    r"measures.*?following:$",
                    r"following measures for reducing waste:$",
                    r"has set a target",
                    r"set.*?reduction target",
                    r"set target to reduce waste",
                    r"minimisation of waste generation",
                    r"reuse of materials",
                    r"recovery and recycling",
                    r"printed when needed",
                    r"reduction initiatives",
                    r"Use recyclable products",
                    r"Recycle office paper",
                    r"Print.*?when necessary|print on both",
                    r"Provide reusable",
                    r"waste reduction measures",
                    r"no specific reduction target",
                    r"targets? (is )?to reduce",
                    r"targeted a reduction",
                    r"initiatives to reduce",
                    r"measures.*?reduce.*?waste disposal",
                    r"managed centrally by the office property management company",
                    r"dedicated to proper management of the non-hazardous waste",
                    r"(?P<content>not undertake)",
                ],
                "multi_elements": True,
                "direct_answer_pattern": [
                    r"No.*?target is set",
                    r"not set.*?targets",
                ],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__A1\.6",
                    r"__regex__Waste Reduction",
                    r"__regex__Waste Handling",
                    r"__regex__wastes? managemen",
                    r"__regex__reduction Initiatives",
                    r"__regex__Handling Initiatives",
                    r"__regex__Hazardous waste emissions",
                    r"__regex__EMISSION REDUCTION",
                    r"__regex__^\s.*?wastes$",
                    r"__regex__^Use of Resources$",
                    r"__regex__^Classification of wastes",
                ],
                "multi_elements": True,
                "paragraph_model": "para_match",
                "as_follow_pattern": [
                    r"adopted appropriate equipment for reducing.*?：$",
                    r"Emission and Waste Reduction Targets$",
                ],
                "para_config": {
                    "paragraph_pattern": (
                        r"waste.*?including:$",
                        r"reduce waste",
                        r"aim.*?(to|at).*?reduc.*?waste",
                        r"be recycled",
                        r"measures.*?handle wastes:",
                        r"measures.*?following:$",
                        r"following measures for reducing waste:$",
                        r"has set a target",
                        r"set.*?reduction target",
                        r"minimisation of waste generation",
                        r"reuse of materials",
                        r"recovery and recycling",
                        r"printed when needed",
                        r"reduction initiatives",
                        r"Use recyclable products",
                        r"Recycle office paper",
                        r"Print.*?when necessary|print on both",
                        r"Provide reusable",
                        r"waste reduction measures",
                        r"no specific reduction target",
                        r"targets? (is )?to reduce",
                        r"targeted a reduction",
                        r"initiatives to reduce",
                        r"measures.*?reduce.*?waste disposal",
                        r"managed centrally by the office property management company",
                        r"dedicated to proper management of the non-hazardous waste",
                        r"adopted the following practices to reduce the consumption",
                        r"implementation of the measures.*?reduc",
                        r"implementation of the measures.*?reduc",
                        r"not undertake",
                        r"objective is to.*?reduction",
                        r"adopted.*?measures.*?to reduce",
                        r"to further reduce overall waste generation",
                        r"adopted appropriate equipment for reducing.*?：$",
                        r"reduced the amount of",
                        r"Emission and Waste Reduction Targets$",
                        r"^•",
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "para_match",
                "multi": True,
                "paragraph_pattern": (
                    r"aim.*?(to|at).*?reduc.*?waste",
                    r"be recycled",
                    r"measures.*?handle wastes:",
                    r"measures.*?following:$",
                    r"following measures for reducing waste:$",
                    r"has set a target",
                    r"set.*?reduction target",
                    r"set target to reduce waste",
                    r"minimisation of waste generation",
                    r"reuse of materials",
                    r"recovery and recycling",
                    r"printed when needed",
                    r"reduction initiatives",
                    r"Use recyclable products",
                    r"Recycle office paper",
                    r"Print.*?when necessary|print on both",
                    r"Provide reusable",
                    r"waste reduction measures",
                    r"no specific reduction target",
                    r"targets? (is )?to reduce",
                    r"targeted a reduction",
                    r"initiatives to reduce",
                    r"measures.*?reduce.*?waste disposal",
                    r"managed centrally by the office property management company",
                    r"dedicated to proper management of the non-hazardous waste",
                    r"in order to|waste reduction",
                    r"(?P<content>not undertake)",
                    r"adopted appropriate equipment for reducing",
                ),
            },
            DEFAULT_MODEL,
        ],
    },
]
