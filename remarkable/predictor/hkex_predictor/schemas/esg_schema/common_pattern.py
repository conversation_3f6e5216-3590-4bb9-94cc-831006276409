NEGLECT_PAGE_HEADER_PATTERN = [
    r"^ENVIRONMENTAL.*?SOCIAL.*?AND.*?GOVERNANCE",
    r"^環境.*?社會及管治報告",
    r"^ENVIRONMENTAL.*?SOCIAL.*?AND$",
    r"^GOVERNANCE.*?REPORT$",
    r"^SOCIAL\s*?AND\s*?GOVERNANCE\s*?REPORT$",  # esg换行
    r"^environment.*?social\s*?and$",  # esg换行
    r"^Report \d+$",
    r"^ENVIRONMENTAL,$",
    r"^Social$",
    r"\(?CONTINUED\)?$",
    r"\(?CONT’D\)?$",
]

DEFAULT_MODEL = {
    "name": "score_filter",
    "threshold": 0.8,
}

COMPLY_LAW_PATTERN = (
    r"(no|any) non- ?compliance",
    r"(no|any) (material|confirmed) non- ?compliance",
    r"(no|any) (\w+\s){0,3}non- ?compliance",
    r"not aware of any.*?(non- ?compliance|violation|breach)",
    r"abides",
    r"strictly (compl(y|ie)|observ)",
    r"ha(s|ve) (compl(y|ie)|observ)",
    r"strictly (abide|adheres?)",
    r"(strict|in) (compliance|conformity) with",
    r"keep abreast with|adheres to and complies with",
    r"no violation",
    r"not record violation",
    r"not violated?",
    r"(?<!procedures are )strictly follow",
    r"(comply|compl(ie(d|s)?|y(ing)?|iance)) with",
    r"not discover any material violation",
    r"accordance with (?:(?!\.).)*law",
    r"not received (any )?reports? of violations?",
    r"not encounter any cases of.*?laws and regulations",
)
