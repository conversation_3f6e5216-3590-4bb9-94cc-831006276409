"""
Jura4 ESG
"""

from remarkable.predictor.hkex_predictor.schemas.esg_schema.common_pattern import (
    COMPLY_LAW_PATTERN,
    DEFAULT_MODEL,
    NEGLECT_PAGE_HEADER_PATTERN,
)

predictor_options = [
    {
        # B7-policy：关于anti-corruption政策的披露，可提取涉及到anti-corruption政策下的所有内容，一般在anti-corruption相关的标题下。
        # （关键词：anti-corruption，anti-money laundering）
        #       1. 情况一：在anti-corruption大标题及下一个小标题之间有内容，则建议框选两个标题中间这段内容；
        #
        #       2. 情况二：在anti-corruption大标题下没有任何一个小标题，则建议框选标题下第一段内容；
        #
        #       3. 情况三：在anti-corruption大标题及下一个小标题之间没有内容，则建议框选大标题下全部内容。
        "path": ["B7 policies - anti-corruption"],
        "models": [
            {
                # 章节标题和第一个小标题之间
                "name": "policy",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "neglect_features": [
                    r"Supply Chain Management",
                ],
                "ignore_pattern_match": True,
                "inject_syllabus_features": [
                    r"__regex__MORAL INTEGRITY AND ANTI-CORRUPTION__regex__Laws and Regulations",
                    r"__regex__ANTI-CORRUPTION POLICY AND WHISTLEBLOWER PROCEDURE",
                    r"__regex__Business Integrity, Anti-Corruption and Anti-Money Laundering",
                    r"__regex__Fair and Honest Operations",
                    r"__regex__BRINGING BUSINESS RESPONSIBILITIES INTO PRACTICE",
                    r"__regex__8.3.3Anti-corruption",
                    r"__regex__Anti-Corruption Policies",
                    r"__regex__ETHICAL BUSINESS OPERATION",
                    r"__regex__Anti-corruption and Anti-graft",
                ],
                "neglect_parent_features": [
                    r"Improving Sustainability Governance",
                ],
                "break_para_pattern": [
                    r"Works carried out.*?anti-corruption.*?as follows:",
                ],
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "only_first": True,
                "only_before_first_chapter": True,
                "inject_syllabus_features": [
                    r"__regex__Anti-Corruption__regex__KPI B7",
                    r"__regex__Anti-Corruption__regex__Anti-Corruption",
                    r"__regex__Policies and Procedures",
                ],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__Anti-Corruption",
                ],
                "multi_elements": True,
                "only_before_first_chapter": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"(?P<content>regards.*?laws)",
                        r"(?P<content>comply.*?laws)",
                        r"(?P<content>no corruption-related violations)",
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_before_first_chapter": True,
                "only_first": True,
            },
            {
                "name": "para_match",
                "syllabus_regs": [r"Responsibility Managemen", r"Anti-Corruption and Business Ethics"],
                "paragraph_pattern": (
                    r"anti-corruption policy",
                    r"(?P<content>regards.*?laws)",
                    r"avoidance of bribery and corruption",
                    r"Group has established policies on anti-corruption",
                    r"policy.*?(money[\-\s]laundering|anti[\-\s]corruption)",
                    r"comply.*?(money[\-\s]laundering|anti[\-\s]corruption)",
                    r"anti-money laundering",
                    r"internal anti-corruption efforts",
                ),
            },
            DEFAULT_MODEL,
        ],
    },
    {
        "path": ["B7 law compliance - anti-corruption"],
        "models": [
            {
                "name": "twice_para_match",
                "multi_elements": True,
                # "strict_limit": True,
                "syllabus_regs": [],
                "neglect_syllabus_regs": [
                    r"IMPROvE OuR vALuE CHAIN",
                    r"Sustainability Pillars",
                    r"Product Health and Safety",
                    r"Labou?r Standards?",
                    # r'Data Privacy',
                    r"B[1-6]|B8|A[1-3]",
                ],
                "paragraph_pattern": tuple(
                    list(COMPLY_LAW_PATTERN)
                    + [
                        r"not tolerate",  # file63958
                        r"not aware of",  # file63957
                    ]
                ),
                # 'neglect_pattern': (),
                "second_pattern": (
                    # r'relevant laws and regulations',
                    r"Anti-corruption",
                    r"Anti-money laundering",
                    r"Prohibition of Commercial Bribery",
                    r"bribery|money laundering",  # file63961
                ),
            },
            {
                "name": "para_match",
                "multi_elements": True,  # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/793#note_210675
                "strict_limit": True,  # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/793#note_210675
                "syllabus_regs": [r"ANTI-CORRUPTION"],
                "neglect_syllabus_regs": [
                    r"IMPROvE OuR vALuE CHAIN",
                    r"Sustainability Pillars",
                    r"Product Health and Safety",
                    r"Labou?r Standards?",
                    r"Data Privacy",
                    r"B[1-6]|B8|A[1-3]",
                ],
                "paragraph_pattern": (
                    r"There was no legal cases regarding corrupt practices",
                    r"During the Period, there are no legal cases",
                    r"(?P<content>not aware.*?money laundering)",
                    r"(?P<content>strictly comply with.*?law)",
                    r"complied with relevant laws and regulations",
                    r"(law|regulation|Ordinance).*?(money|anti[-\s]|Bribery)",
                    r"(money|anti[-\s]|Bribery).*?(law|regulation|Ordinance)",
                    # r'in line with laws and regulations',
                    # r'compliance with laws and regulations'
                    # r'not violate|no legal case|no violation',
                    # r'avoidance of bribery and corruption',
                ),
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__Anti-Corruption",
                ],
                "multi_elements": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"(?P<content>not aware.*?money laundering)",
                        r"(?P<content>no corruption-related violations)",
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "kmeans_classification",
                "threshold": 0.618,
            },
        ],
    },
    {
        # 报告期内对发行人或其雇员提起的以身结的腐败行为法律案件数量，优先index，其次data summary，最后正文。
        # （关键词：bribery，legal，case，lawsuit，corruption charges）
        #       1. 0或nil或no或not等否定描述是Y；
        #       2. 当描述中没有涉及到case或lawsuit，仅有没有违反法规的相关描述（e.g. no non-compliance)，应判断为Y。
        "path": ["KPI B7.1 - legal cases on corruption"],
        "neglect_table_cell_missing_crude_regs": [r"Health and Safety"],
        "models": [
            {  # 特例 stock 00228
                "name": "para_match",
                "multi_elements": True,
                "paragraph_pattern": (r"complied with relevant laws and regulations relating to anti-\s?corruption",),
            },
            {
                "name": "after_row_match",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "just_a_para": True,
                "row_pattern": [r"B7.1"],
                "direct_answer_pattern": [
                    r"no (concluded )?legal case",
                    r"Zero case",
                    r"Number of concluded cases",
                ],
                "para_pattern": [
                    r"not involved.*?corruption",
                    r"not aware of any non-compliance with the relevant laws and regulations",
                    r"no (concluded )?legal case",
                    r"not aware of.*?cases",
                ],
            },
            {
                "name": "row_match",
                "force_use_all_elements": True,
                "ignore_index_table": True,
                "row_pattern": [
                    r"Incidents of Corruption",
                    r"^Legal Cases",
                    r"^Number of concluded cases",
                ],
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"no serious breach or non-compliance with relevant laws",
                    r"No reports or related complaints",
                    r"material impact on the Company in terms of anti-corruption",
                    r"not notify any material non-compliance with the relevant laws",
                    r"concluded legal case",
                    r"no legal case",
                    r"not have any legal.*?corruption",
                    r"no confirmed.*?legal case",
                    r"not aware of.*?cases",
                    r"have not involved.*?legal ",
                    r"No corruption charges",
                    r"no legal.*?corruption",
                    r"no reported case of corruption",
                    r"no.*?activities occurr",
                    r"not identified any.*?cases of",
                    r"not identify any material non-compliance cases",
                    r"had no non-compliance cases regarding violations",
                    r"subsidiaries experienced no litigation brought against",
                    r"involved in anti-corruption litigation cases",
                    r"no material non-compliance with the relevant laws",
                    r"nor violation of regulations related to corruption",
                    r"nor.*?was involved in any corruption lawsuits",
                    r"no corruption lawsuits against",
                    r"no litigation cases of corruption",
                ),
            },
            {
                "name": "row_match",
                "ignore_index_table": True,
                "first_cell": True,
                "row_pattern": [
                    r"legal cases",
                    r"corruption cases",
                ],
            },
            {
                "name": "ar_esg_71",
                "threshold": 0.1,
            },
        ],
    },
    {
        # 描述预防措施和举报程序，以及如何实施和监管。
        # （关键词：whistle-blowing, whistleblowing, misconduct, preventive, prevention, prevent）
        # reporting channels
        "path": ["KPI B7.2 - preventive measures & whistle-blowing procedures"],
        "models": [
            {
                "name": "para_match",
                "multi_elements": True,
                # "combine_paragraphs": True,
                "paragraph_pattern": (
                    r"To prevent corrupt practices ",
                    r"reporting channels",
                    r"CEEPIL has established a complete complaint mechanism",
                    r"whistleblowers will be kept confidential to protect their privacy",
                ),
            },
            {
                "name": "syllabus_elt_v2",
                "only_inject_features": True,
                "multi": True,
                "inject_syllabus_features": [
                    r"__regex__Anti-Corruption__regex__KPI B7.2",
                    r"__regex__Customer Due Diligence",
                    r"__regex__Suspicious Transactions Reporting",
                    r"__regex__Gifts and benefits",
                    r"__regex__Financial Crime",
                    r"__regex__Whistle-blowing Mechanism and Whistleblower Protection",
                    r"__regex__GOVERNANCE AND ETHICS__regex__Whistleblowing",
                ],
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
            },
            {
                "name": "after_row_match",
                "row_pattern": [r"B7.2"],
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "para_pattern": [
                    r"whistle.*?blowing|misconduct|B7.2",
                    r"(?P<content>not tolerate any.*?business activities)",
                    r"reporting channels",
                ],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__Customer Due Diligence",
                    r"__regex__ANTI-CORRUPTION POLICY AND WHISTLEBLOWER PROCEDURE",
                    r"__regex__BRINGING BUSINESS RESPONSIBILITIES INTO PRACTICE",
                ],
                "multi_elements": True,
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"(?P<content>whistle.*?blowing|misconduct|B7.2)",
                        r"(?P<content>not tolerate any.*?business activities)",
                        r"encouraged to report on suspected business irregularities",
                        r"misconduct|prevention|preventive|prevent",
                        r"Whistle-?blowing",
                        r"Whistleblowing (procedures|scope)",
                        r"reporting mechanism",
                        r"reporting channels",
                    ),
                    # 'neglect_pattern': (
                    #     r'\d+\s?cases',
                    # )
                },
                "table_model": "special_cells",
                "table_config": {
                    "cell_pattern": [
                        r"whistle-blowing|misconduct",
                        r"reporting channels",
                    ]
                },
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"(?P<content>whistle.*?blowing|misconduct|B7.2)",
                    r"(?P<content>not tolerate any.*?business activities)",
                    r"(?P<content>violate the company’s rules, we will directly dismiss the employee)",
                    r"reporting channels",
                    r"Whistle-?blowing Mechanism",
                ),
            },
            {
                "name": "kmeans_classification",
                "para_pattern": [
                    r"whistle.*?blowing|misconduct|B7.2",
                    r"(?P<content>not tolerate any.*?business activities)",
                    r"reporting channels",
                ],
            },
            DEFAULT_MODEL,
        ],
    },
    {
        # 描述向董事和员工提供的反腐败培训，提取涉及到anti-corruption training相关的段落，
        # 有时候会出现在B3的相关段落下，也可提取（关键词：anti-corruption，training，seminar
        # 没有明确表明提供相关training, 只是说会把相关的政策告知employee，不属于training相关，应判断为ND
        "path": ["KPI B7.3 - anti-corruption training"],
        "models": [
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"anti.*?corruption.*?training|B7.3",
                    r"training.*?anti.*?corruption",
                    r"training|seminar",
                ),
            },
            {
                "name": "after_row_match",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "just_a_para": True,
                "row_pattern": [r"B7.3"],
                "para_pattern": [
                    r"anti.*?corruption.*?training|B7.3",
                    r"training",
                    r"(?<!terrorism )seminar",
                ],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__Anti-corruption",
                    r"__regex__Anti-corruption Training",
                    r"__regex__Anti-corruption and Integrity",
                ],
                "multi_elements": True,
                "include_title": False,
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"anti.*?corruption.*?training|B7.3",
                        r"training|seminar",
                    ),
                },
                "table_model": "special_cells",
                "table_config": {
                    "cell_pattern": [
                        r"whistle-blowing|misconduct",
                    ]
                },
            },
            {
                "name": "para_match",
                "multi_elements": True,  # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/803#note_212983
                "paragraph_pattern": (
                    r"anti.*?corruption.*?training|B7.3",
                    r"training.*?anti.*?corruption",
                    r"training|seminar",
                ),
            },
        ],
    },
]
