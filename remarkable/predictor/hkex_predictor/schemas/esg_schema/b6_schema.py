"""
Jura4 ESG
"""

from remarkable.common.common_pattern import EXPLAIN_PATTERN as COMMON_EXPLAIN_PATTERN
from remarkable.predictor.hkex_predictor.schemas.esg_schema.common_pattern import (
    COMPLY_LAW_PATTERN,
    DEFAULT_MODEL,
    NEGLECT_PAGE_HEADER_PATTERN,
)

EXPLAIN_PATTERN = tuple(COMMON_EXPLAIN_PATTERN)


B6_CHAPTER_PATTERN = [
    r"__regex__Product\s?Responsibility",
    r"__regex__B6",
    r"__regex__Responsible\s?Services",
]

predictor_options = [
    {
        #  一般企业产品责任很多无非都是保证服务质量，保证产品质量，所以如果不存在明显的有小标题的概括性文字，
        #  很多时候B6会包含B6.4
        #
        #  框选内容：**优先从index定位的位置去找以下内容**
        #  <有明显概括性文字的>，一般在product/service responsibly相关的大标题与小标题之间的文字段落。
        #  <不存在明显概括性文字的>，当不存在任何小标题时候，可以直接框选大标题段落第一段关于质量控制的文字段落，
        #  如果第一段文字也和质量要求无关，那么可以直接框B6.4的quality assurance
        #
        #  关键词：ordinance, Product Quality Law, Ordinance
        # （在这些关键词附近可同时关注Product Responsibility, responsible services, high quality services,
        # meet the expectation, satisfaction。product responsibility,
        # product quality, safety and health，responsible, service等）
        # 大标题 与下一个小标题之间 Product  Responsibility，
        "path": ["B6 policies - product responsibility"],
        "models": [
            {
                "name": "after_row_match",  # 未披露的情况
                "first_cell": False,
                "force_use_all_elements": True,  # file63636 | file63686
                "row_pattern": [
                    r"(B6(?![_\d\.])).*not (an? )?(applicable|been disclosed|considered|material topic)",
                    r"(B6(?![_\d\.])).*(?<![a-zA-Z])N/?A(?![a-zA-Z])",
                    r"(B6(?![_\d\.])).*不適用",
                    r"(B6(?![_\d\.])).*be disclosed in the future",
                    r"(B6(?![_\d\.])).*not involve(?:(?!(\.)).)*recall",
                ],
                "direct_answer_pattern": [
                    r"not (an? )?(applicable|been disclosed|considered|material topic)",
                    r"(?<![a-zA-Z])N/?A(?![a-zA-Z])",
                    r"不適用",
                    r"be disclosed in the future",
                    r"not involve(?:(?!(\.)).)*recall",
                ],
            },
            # 特殊的badcase uat
            # https://hkex.test.paodingai.com/#/hkex/esg-report-checking/report-review/196200?fileId=58245&schemaId=1&rule=B4%20policies%20-%20labour%20standards
            {
                "name": "syllabus_based",
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__B3.*?Development and Training",
                    r"__regex__QUALITY CUSTOMER SERVICES",
                    r"__regex__Service Standards$",
                ],
                "multi_elements": True,
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"as are not addressed in this ESG",
                        r"The.*?consumer complaint handling process is divided into",
                        r"formulated the|has formulated and improved",
                        r"Customer Service Quality Assurance",
                        r"(Communicating|communicate) with Customers",
                        r"based on the principle of limited authorization",
                        r"prevent data leakage and ensure data information security",
                        r"respects customers.*?personal financial information security rights",
                        r"By moved and professional service level, let customers feel being blessed during dining process",
                    ),
                    "neglect_pattern": (),
                },
                "table_model": "empty",
            },
            {
                # 只有第一段
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "only_first": True,
                "inject_syllabus_features": [
                    r"__regex__Product Responsibility__regex__General Disclosure",
                ],
            },
            {
                "name": "after_row_match",
                "first_cell": False,
                "force_use_all_elements": True,
                "first_row_as_title": True,  # file63998, 在目录中写了对应章节，但表格第二行才是目标cell
                "row_pattern": [r"Product Responsibility"],
                "direct_answer_pattern": [r"not applicable"],
                "just_a_para": True,
                "para_pattern": [
                    r"quality control and assurance",
                ],
            },
            {  # 匹配到B6标题时只取大标题与第一个子标题之间的内容
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "ignore_syllabus_pattern": [r"law"],  # file63950
                "only_before_first_chapter": True,  # file63947
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__Product Responsibilit__regex__Quality management",
                    # file63950 https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/894#note_230686
                    r"__regex__Product Responsibilit",
                ]
                + B6_CHAPTER_PATTERN,
            },
            {
                # 下面是B6.5的章节标题+B6章节
                # 特例badcase
                # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/997#note_243515
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN + [r"^Table\s\d"],
                "only_inject_features": True,
                "multi": True,
                "inject_syllabus_features": [
                    r"__regex__5\.3\.\s?PRODUCT RESPONSIBILITY",
                    r"__regex__5\.4\.\s?PRIVACY PROTECTION",
                    r"__regex__Providing High-quality Products",
                ],
            },
            {
                # B6大标题与子标题之间不能存在段落时，框选符合的章节，否则框选大标题下所有段落和章节
                # 下面是B6.4的章节标题+B6章节
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__(DEEPENING )?QUALITY CONTROL",  # file63952 file63987
                    r"__regex__Product Management",  # file63949
                    r"__regex__Quality Assurance",
                    r"__regex__Quality Control",
                    r"__regex__Product and Service Quality Management",
                    r"__regex__Product Quality( Management)?",
                    r"__regex__Service pledge to our customer",
                    r"__regex__Product Responsibilit",
                ]
                + B6_CHAPTER_PATTERN,
            },
            DEFAULT_MODEL,
        ],
    },
    {
        "path": ["B6 law compliance - product responsibility"],
        "models": [
            {
                "name": "twice_para_match",
                "multi_elements": True,
                "use_all_elements": True,
                # "strict_limit": True,
                "syllabus_regs": [
                    r"Product\s?Responsibility",
                    r"B6",
                    r"Responsible\s?Services",
                    r"Service Quality",
                    r"Product.*?Quality",
                    r"(HIGH )?QUALITY (PRODUCTS|services)",
                    r"safety and health",
                    r"responsible",
                    r"Safeguarding Confidential Matters",
                ],
                "neglect_syllabus_regs": [
                    r"Environmental Policy",
                    r"health and safety",
                    r"supply chain",
                    r"Anti-Corruption",
                    r"B[1-5]|B7|B8|A[1-3]",
                ],
                "paragraph_pattern": COMPLY_LAW_PATTERN,
                "neglect_pattern": (
                    r"complies with the corresponding national standards",
                    r"selecting supplier",  # file63987章节supply chain management识别错误
                    r"Supply Chain Management",  # file63973
                ),
                "second_pattern": (
                    r"(high quality|responsible) service",
                    r"meet the expectation",
                    r"satisfaction",
                    r"(product|service|management) (responsibilit|qualit|characteristic|standards)",  # standards file63952
                    r"(responsibilit(y|ies)|qualit(y|ies)) (product|service|management)",  # file63982
                    r"(health|quality|safety) and (health|quality|safety)",
                    r"data.*?(Ordinance|law)",
                    r"intellectual property",
                    r"relating to advertising",  # file63959
                    r"Accountability of Violations",  # file63952
                    r"responsibility or privacy",
                ),
            },
            {
                "name": "para_match",
                "multi_elements": True,
                "paragraph_pattern": (
                    r"responsible to comply with the requirements of the rules and regulations",
                    r"no material non-compliance.*data protection and privacy",
                    r"not involve in any confirmed violations of laws and regulations",
                    r"Product Eco-responsibility Ordinance",
                    r"Consumer Goods Safety Ordinance",
                    r"B6.*?are not the key material matters.*?are not addressed in this ESG report",
                ),
                "neglect_syllabus_regs": [
                    r"Environmental Policy",
                    r"health and safety",
                    r"supply chain",
                    r"Anti-Corruption",
                    r"B[1-5]|B7|B8|A[1-3]",
                ],
            },
            {  # fileid 63987 章节识别错误
                "name": "after_row_match",
                # 'include_row': True,
                "row_pattern": [r"B6\s"],
                # 'just_a_para': True,
                "multi_elements": True,
                "para_pattern": COMPLY_LAW_PATTERN,
                # 'direct_answer_pattern': [r'No product.*?recalls'],
            },
            {
                "name": "twice_row_match",
                "first_cell": False,
                "row_pattern": COMPLY_LAW_PATTERN,
                "second_pattern": [r"B6(\s+|$)"],
            },
            DEFAULT_MODEL,
        ],
    },
    {
        # 关于出于安全和健康原因而进行召回的已售或运输产品的数据的披露，可提取涉product recall的数据所在的图表或段落或句子，优先提取index，
        # 其次data summary，最后正文。
        # （关键词：recall，recovery，return）0或nil或no或not等否定描述是Y
        "path": ["KPI B6.1 - products recall"],
        "models": [
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__B6\.1",  # file63946
                ],
            },
            {
                "name": "para_match",
                "syllabus_regs": [
                    r"product.*recall",
                    r"Healthy Gaming",
                    r"Product.*Respons",
                    r"Safe Meals",
                    r"Product and Service Responsibility",
                ],
                "paragraph_pattern": (
                    r"B6.*?are not the key material matters.*?are not addressed in this ESG report",
                    r"recall(?:(?!\.).)*?reason",
                    r"recall.*product.*health and safety",
                    r"product recalls during the.*?Year",
                    r"In.*?period.*?product recall",
                    r"product.*?recall.*(?:concerns|reasons)",
                    r"no recall case",
                    r"recall of product",
                ),
            },
            {
                "name": "after_row_match",
                "include_row": True,
                "first_cell": False,
                "row_pattern": [r"B6.1"],
                "just_a_para": True,
                "multi_elements": True,
                "para_pattern": [r"recall|B6.1|return"],
                "direct_answer_pattern": [r"No product.*?recalls"],
            },
            {
                "name": "row_match",
                "multi": True,
                "first_cell": False,
                "force_use_all_elements": True,
                "ignore_index_table": True,
                "row_pattern": [
                    r"B6\.1.*not (an? )?(applicable|been disclosed|considered)",
                    r"B6\.1.*(?<![a-zA-Z])N/?A(?![a-zA-Z])",
                    r"B6\.1.*(不適用|不适用)",
                    r"B6\.1.*be disclosed in the future",
                    r"B6\.1.*not involve(?:(?!(\.)).)*recall",
                    r"B6\.1.*not( a)? material",
                    r"to (returns/)?recalls",
                    r"Number of products returned/recalled",  # file63965
                    r"percentage of product recall",
                ],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__Quality Assurance",
                    r"__regex__High-quality\s\w+",
                ]
                + B6_CHAPTER_PATTERN,
                "multi_elements": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"B6.1",
                        r"not have( any)?.*?products.*?to recall",
                        r"no.*?products.*?to recall",
                        r"no recalled products",
                        r"no (goods|orders).*?to recalls for product quality",
                        r"No.*?were recalled due to safety and health issues",
                    ),
                    "neglect_pattern": (
                        r"so as to protect.*?any potential health and safety issue",
                        r"creating higher return",
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "twice_para_match",
                "strict_limit": True,
                "multi_elements": True,
                "syllabus_regs": [
                    r"product responsibility",
                ],
                "paragraph_pattern": (
                    r"recall|B6.1|return",
                    r"not have( any)?.*?products.*?to recall",
                    r"no.*?products.*?to recall",
                    r"no recalled products",
                    # EXPLAIN_PATTERN,
                ),
                "neglect_pattern": (
                    r"so as to protect.*?any potential health and safety issue",
                    r"creating higher return",
                ),
                "second_pattern": (
                    EXPLAIN_PATTERN,
                    r"(had|was|were|is) (zero|\d+|no)",
                    r"not have (any)?",
                    r"not have( any)?.*?products.*?to recall",
                    r"no.*?products.*?to recall",
                    r"no recalled products",
                    r"product quality",
                ),
            },
        ],
    },
    {
        # 收到的与产品和服务相关的投诉数量以及处理方式，优先提取index，其次data summary，最后正文。
        # （关键词：complaint, service-related complaints）0或nil或no或not等否定描述是Y。
        # 不仅要提取投诉数量，还要提取文中描述的处理投诉的方式措施。
        "path": ["KPI B6.2 - products related complaints"],
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/852#note_217847
        # "pick_answer_strategy": "all",
        "models": [
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__Handling of Customer Complaints",
                    r"__regex__Customer relationship management",
                    r"__regex__Handling Complaint",  # file63979
                    r"__regex__Customer Complaint",  # file63948
                    r"__regex__B6\.2",
                ],
            },
            {
                "name": "after_row_match",
                "first_cell": False,
                "row_pattern": [r"B6.2"],
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "multi_elements": True,
                "para_pattern": [
                    r"complaint handling |respond to complaint|Compliance Department",
                    r"no.*?complaints received",
                    r"not receive any material compliant.*?product quality",
                    r"not receive any compliant.*?product",
                    r"not receive any \w+ complaints",  # file63972
                    r"opinion.*?products",
                    r"\d+ complaint",
                ],
            },
            {
                "name": "row_match",  # 未披露的情况
                "first_cell": False,
                "force_use_all_elements": True,
                "row_pattern": [
                    r"B6\.2.*not (an? )?(applicable|been disclosed|considered)",
                    r"B6\.2.*(?<![a-zA-Z])N/?A(?![a-zA-Z])",
                    r"B6\.2.*不適用",
                    r"B6\.2.*be disclosed in the future",
                    r"B6\.2.*not involve(?:(?!(\.)).)*recall",
                    r"B6\.2.*not material",
                ],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__product\s?responsibility",
                    r"__regex__Service Quality",
                    r"__regex__(Product\s?Responsibility|Responsible\s?Services|Quality\s?Assurance)",
                    r"__regex__SUMMARY OF KEY PERFORMANCE INDICATORS",  # file63969在表格中披露了
                ],
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "multi_elements": True,
                "multi": True,
                "multi_level": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"(?P<content>complaint(?:(?!of customer privacy).)*\.|B6\.2)",  # file64005
                        r"complaint.*?handling |respond to complaint|Compliance Department",
                        r"no complaints received",
                        r"not receive any material compliant.*?product quality",
                        r"not receive any compliant.*?product",
                        r"opinion.*?products",
                        r"\d+ complaint",
                        r"value customer feedback for continuous improvement",  # file63995
                        # EXPLAIN_PATTERN,
                    ),
                },
                "table_model": "row_match",
                "table_config": {
                    "first_cell": False,
                    "row_pattern": [r"B6\.2", r"complaints? received?"],
                },
            },
            {
                "name": "para_match",
                "multi_elements": True,
                "paragraph_pattern": (
                    r"(?P<content>complaint|B6.2)",
                    r"handle complaints",
                    r"no complaints received",
                    r"not receive any material compliant.*?product quality",
                    r"not receive any compliant.*?product",
                    r"opinion.*?products",
                    r"\d+ complaint",
                    r"B6.*?are not the key material matters.*?are not addressed in this ESG report",
                    # EXPLAIN_PATTERN,
                ),
                "syllabus_regs": [
                    r"Product\s?Responsibility",
                    r"B6",
                    r"Responsible\s?Services",
                ],
                "neglect_syllabus_regs": [
                    r"Anti-corruption",
                    r"Sustainability Pillars",
                    r"Product Health and Safety",
                    r"Labou?r Standards?",
                    r"Data Privacy",
                    r"B[1-5]|B[78]|A[1-3]",
                ],
            },
            {  # file63997
                "name": "row_match",
                "ignore_index_table": True,
                "row_pattern": [
                    r"B6.2",
                    r"Number of complaints",
                ],
            },
            DEFAULT_MODEL,
        ],
    },
    {
        # B6.3 Product Responsibility- Description of practices relating to observing and protecting intellectual property rights.
        # 描述与遵守和保护知识产权有关的做法。
        # （关键词：intellectual property rights, intellectual property protection）
        # E:
        # 判断依据：没有关于遵守和保护知识产权有关的做法，但是有解释
        # 需标注内容：涉及“not applicable”等的描述
        # ***遇到其他描述反馈并补充在公盘
        # 常见关键词：not applicable
        #
        # Y:
        # 判断依据：披露了intellectual property rights政策的披露
        # 需标注内容：提取intellectual property rights政策的段落。一般在Intellectual Property Rights相关标题下方
        # 常见关键词：intellectual property rights, intellectual property protection
        #
        # ND: 没有披露相关内容,也没有进行解释
        #
        # 定位：可从KPI index给出的位置去定位框选；或Product Responsibility / Responsible Services下小标题为Intellectual Property Rights/ Intellectual Property Protection标题下内容
        "path": ["KPI B6.3 - IP rights protection"],
        "models": [
            {
                "name": "after_row_match",
                "row_pattern": [r"B6\.3"],
                "remove_section_cn_text": True,
                # 'only_syllabus_of_para': True,
                "multi_elements": True,
                "first_cell": False,
                "para_pattern": [
                    r"(intellectual property|ip) (rights|protection)?",
                    r"copyright training",  # file64001
                    r"disclosing confidential information",  # file64001
                ],
            },
            {
                "name": "row_match",
                "ignore_index_table": True,
                "row_pattern": [
                    r"B6.3",
                ],
            },
            {
                "name": "syllabus_elt_v2",
                "multi": True,
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__^Intellectual Property( Rights| protection)?",  # file63959 标题会有 xx AND Intellectual Property的情况
                    r"__regex__Safeguarding Intellectual Property",
                    r"__regex__B6\.3 Intellectual Property",
                ],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__Protection of Intellectual Property Rights",
                    r"__regex__(Product\s?Responsibility|Responsible\s?Services)__regex__(Intellectual\s?Property\s?"
                    r"Rights|Intellectual\s?Property\s?Protection)",
                    r"__regex__intellectual\s?Property\s?Protection",
                    r"__regex__product\s?responsibility",
                    r"__regex__RESPONSIBLE OPERATION",  # file63959
                    r"__regex__intellectual Property Rights",
                ],
                "multi_elements": True,
                "include_title": False,
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        # r'Copyright Ordinance', # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/852#note_218211
                        r"IP rights",
                        r"intellectual property right",
                        r"intellectual property protection",
                        r"protect intellectual property and data and privacy",
                        r"protect.*?data and privacy",
                        r"protect.*?our data assets",
                        r"protection of intellectual property",
                        r"secures its intellectual property",
                        r"B6.3",
                        r"respects intellectual property",
                        r"Company Trademark|Intellectual Property",
                        # r'personal data.*?ordinance',  # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/852#note_218211
                        r"maintained our trademark portfolio",
                        r"prohibited to use the IP of the Group",
                        r"apply for the patents",
                        r"Patent Licensing Contract",
                        r"exercise the priority right to apply for the invalidity",
                        # EXPLAIN_PATTERN,
                    ),
                    "neglect_pattern": (
                        r"not have physical products for sale",
                        r"laws(?:(?!\.).)*?labelling",
                        r"ensure.*?complies.*?laws.*?intellectual property right",
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "row_match",
                "first_cell": False,
                "force_use_all_elements": True,
                "row_pattern": [rf"B6\.3.*({e_pattern})" for e_pattern in COMMON_EXPLAIN_PATTERN + [r"N/?A"]],
            },
            {
                "name": "para_match",
                "multi": True,
                "multi_elements": True,
                "force_use_all_elements": True,
                "paragraph_pattern": (
                    # r'(?P<content>Copyright Ordinance)',# https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/852#note_218211
                    r"(?P<content>IP (rights|protection))",
                    r"(?P<content>protection of intellectual property)",
                    r"(?P<content>intellectual property( protection| right))",
                    r"(?P<content>protect intellectual property and data and privacy)",
                    r"(?P<content>protect.*?data and privacy)",
                    r"(?P<content>protect.*?our data assets)",
                    r"(?P<content>secures its intellectual property)",
                    r"(?P<content>B6.3)",
                    r"(?P<content>respects intellectual property)",
                    r"(?P<content>Company Trademark|Intellectual Property)",
                    # r'(?P<content>personal data.*?ordinance)', # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/852#note_218211
                    r"(?P<content>maintained our trademark portfolio)",
                    r"(?P<content>prohibited to use the IP of the Group)",
                    r"B6.*?are not the key material matters.*?are not addressed in this ESG report",
                    # EXPLAIN_PATTERN,
                ),
                "neglect_pattern": (
                    r"not have physical products for sale",
                    r"laws.*?labelling",
                    r"form(?:(?!\.).)*intellectual property products",  # file63976
                    r"ensure.*?complies.*?laws.*?intellectual property right",
                ),
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__Intellectual Property Rights",
                ],
            },
            DEFAULT_MODEL,
        ],
    },
    {
        # B6.4： 侧重于产品及服务质量保证的具体流程及方法措施。
        #  有的可能会具体到产品从原材料采购到产出的质量检测及要求的流程图。相对于B6policy的概括性文字会描述得具体很多，
        #  有的文章可能直接整段讲具体质量保证措施，无法明确区分**B6policy的概括性文字**和B6.4，那么B6和B6.4的内容也会重合
        #
        #  框选内容：**优先从index定位的位置去找以下内容**
        #  <当存在明显小标题quality assurance/control或者B6.4>时，直接框选该标题下所有内容，
        #  <当不存在明显小标题>或内容描述质控过程措施，优先框关键词关于质量控制的段落，如果没有则可以框选客户投诉complaint处理措施，
        #  产品召回recall处理措施
        #
        #  关键词quality, performance check, rectification, recall，compliant， B6.4, quality assurance
        "path": ["KPI B6.4 - quality assurance process"],
        "models": [
            {
                "name": "row_match",  # 未披露的情况
                "first_cell": False,
                "force_use_all_elements": True,  # file63636 | file63686
                "row_pattern": [
                    # B6.4 或者 B6 not applicable
                    # B6Product Responsibility Not applicable as our business does not manufacture or trade in productsNA* file63665
                    r"(B6(?![_\d\.])|B6\.4).*(?<!recall procedure is )not (an? )?(applicable|been disclosed|considered)",
                    # 注意这里的(?<!recall procedure is )下面after_row_match有备注，file63615 | not considered file63688
                    r"(B6(?![_\d\.])|B6\.4).*(?<![a-zA-Z])N/?A(?![a-zA-Z])",  # file63617
                    r"(B6(?![_\d\.])|B6\.4).*不適用",  # file63617
                    r"(B6(?![_\d\.])|B6\.4).*be disclosed in the future",  # file63630
                    r"(B6(?![_\d\.])|B6\.4).*not involve(?:(?!(\.)).)*recall",  # file63684
                    r"(B6(?![_\d\.])|B6\.4).*Quality assurance(?:(?!(\.)).)*(don\'t|not) apply",  # file63709
                    r"(B6(?![_\d\.])|B6\.4).*not material",  # file63960
                ],
            },
            {
                "name": "para_match",  # 未披露的情况 从上面的row_match复制
                "paragraph_pattern": (
                    r"(B6(?![\W_\d\.])|B6\.4).*not (applicable|been disclosed|considered)",
                    r"(B6(?![\W_\d\.])|B6\.4).*(?<![a-zA-Z])N/?A(?![a-zA-Z])",
                    r"(B6(?![\W_\d\.])|B6\.4).*不適用",
                    r"(B6(?![\W_\d\.])|B6\.4).*be disclosed in the future",
                    r"(B6(?![\W_\d\.])|B6\.4).*not involve(?:(?!(\.)).)*recall",
                    r"(B6(?![_\d\.])|B6\.4).*Quality assurance(?:(?!(\.)).)*(don\'t|not) apply",  # file63709
                    r"(B6(?![_\d\.])|B6\.4).*not material",  # file63960
                    r"enhanc(?:(?!\.).)*service quality",  # file63961 双栏画框错误已提issue：docs_scriber#912#note_232484
                ),
            },
            {  # 较为精确的匹配
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "include_shape": True,
                "inject_syllabus_features": [
                    r"__regex__QUALITY ASSURANCE",
                    r"__regex__B6.4",
                ],
            },
            {  # 因为B6.4分为两部分，quality assurance和recall procedures但在index表格中常写在一个cell中
                # 可能存在recall procedures是E, 但quality assurance是Y的情况(file63951)
                # 这时下面的after_row_match因为关键词匹配所以direct_answer会直接返回E，所以要在此之前进行章节精确匹配quality assurance
                "name": "after_row_match",
                "row_pattern": [r"B6\.4"],
                "remove_section_cn_text": True,
                # 'only_syllabus_of_para': True,
                # 'multi': True,
                "first_cell": False,
                # 'para_pattern': [r'intellectual property (rights|protection)'],
            },
            # 章节全部 适用于有明显的小标题，范围较大
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "include_shape": True,
                "break_para_pattern": [
                    r"Anti-corruption$",
                ],
                "inject_syllabus_features": [
                    r"__regex__B6.4",
                    r"__regex__Complaints Handling Procedures",
                    r"__regex__(?<!Air )Quality Management",  # (?<!Air ) file63682
                    r"__regex__(PRODUCT AND|IMPROVING) SERVICE( RESPONSIBILITY| QUALITY)?",  # IMPROVING SERVICE QUALITY file63663
                    r"__regex__Product(/Service)? Quality( Management and Control)?",  # Product Quality file63705
                    r"__regex__Quality (Control|Assurance)( and Product Warranty)?",
                    # r'__regex__PRODUCT RESPONSIBILIT',  # B6 大章节标题
                    r"__regex__PRODUCT RESPONSIBILITY__regex__SERVICE|BUSINESS",  # issues/894#note_224590
                ],
            },
            # 仅仅第一段
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "only_first": True,
                "inject_syllabus_features": [
                    r"__regex__Product Responsibility__regex__KPI B6.4",
                ],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__Quality\s?and Safety\s?of\s?Services",
                    r"__regex__Quality Management",
                    r"__regex__PRODUCT(/Service)? RESPONSIBILIT",
                ],
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "multi_elements": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"(?<!-)quality|performance check|rectification|recall|complaint|B6.4",  # (?<!-)quality file63704
                        r"advertisements are mainly based on word-of-mouth",
                        r"complaints (were|are|was|is) (?:(?!(not|\.)).)* addressed",  # NewAdd: 被处理也算 file3
                        EXPLAIN_PATTERN,
                        r"Examples of|includes the following",
                        # r'^\d+\.',  # ?
                        r"^•",
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "special_cells",  # 段落识别为表格时
                "cell_pattern": [
                    # ---- 从上面的syllabus_elt_v2复制 START ----
                    r"B6.4",
                    r"Complaints Handling Procedures",
                    r"Quality Management",
                    r"(PRODUCT AND|IMPROVING) SERVICE( RESPONSIBILITY| QUALITY)?",  # IMPROVING SERVICE QUALITY file63663
                    r"Product Quality Management and Control",
                    r"Quality Control and Product Warranty",
                    r"Quality Control",
                    r"Quality Assurance",
                    # r'PRODUCT RESPONSIBILIT',  # B6 大章节标题
                    # r'PRODUCT RESPONSIBILITY__regex__.* SERVICE',  # issues/894#note_224590
                    # ==== 从上面的syllabus_elt_v2复制 END ====
                ],
            },
            {
                "name": "special_cells",  # 段落识别为表格时
                "cell_pattern": [
                    r"PRODUCT RESPONSIBILIT",  # file63664 段落识别为表格时
                ],
            },
            {
                "name": "para_match",
                "force_use_all_elements": True,  # file63650
                "paragraph_pattern": (
                    r"putt(ing)? (our )?customers? first",  # file63650
                    r"(?P<content>quality|performance check|rectification|recall|compliant|B6.4)",
                    r"B6.*?are not the key material matters.*?are not addressed in this ESG report",
                    EXPLAIN_PATTERN,
                ),
                "neglect_pattern": (r"(supplier|subcontractors).*?quality",),
            },
            DEFAULT_MODEL,
        ],
    },
    {
        # B6.5 Product Responsibility- Description of consumer data protection and privacy policies, and how they are implemented and monitored.
        # 描述消费者数据保护和隐私政策，以及它们是如何实施和监管的。
        # （关键词：privacy, personal data, confidentiality）；
        # E:
        # 判断依据：没有相关内容的披露，但是有解释
        # 需标注内容：不常见，可参考其他规则的E描述
        #
        # Y:
        # 判断依据：披露了消费者数据保护和隐私政策，以及它们是如何实施和监管的
        # 需标注内容：提取发行人的消费者数据保护和隐私政策，以及它们是如何实施和监管的段落。一般在Product Responsibility 标题下有小标题关于Privacy Protection的内容；
        # 常见关键词：privacy, personal data, confidentiality
        #
        # ND: 没有披露相关内容,也没有进行解释
        #
        # 定位：可从KPI index给出的位置去定位框选；或Product Responsibility / Responsible Services下小标题为Privacy Protection标题下内容
        "path": ["KPI B6.5 - consumer data protection"],
        "models": [
            {
                "name": "after_row_match",
                "row_pattern": [r"B6.5"],
                "remove_section_cn_text": True,
                "multi_elements": True,  # file63952 index表格中给的章节范围过大
                "para_pattern": [
                    r"^B6\.5$",
                    r"privacy|personal data|confidentiality",
                    r"information protection",
                ],
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "multi": True,
                "inject_syllabus_features": [
                    r"__regex__Customer (Data )?Privacy",
                    r"__regex__Customer Privacy Protection",
                    r"__regex__(Customer|Personal) Data protection",
                    r"__regex__Data (Privacy )?protection",  # data protection file63979
                    r"__regex__Data Protection and Privacy",
                    r"__regex__information Security Management",
                    r"__regex__data protection and privacy policies",
                    r"__regex__Information Security (and Privacy )?Protection",
                    r"__regex__Privacy Matters",
                    r"__regex__Client Privacy Protection",  # file64002大章节`Client Service and Privacy Protection`小章节`Client Privacy Protection`都包含`Privacy Protection`
                    r"__regex__Protecting information security",
                    r"__regex__Privacy and Information Security",
                    r"__regex__Product Responsibility__regex__privacy",
                    r"__regex__Confidential Information",
                    r"__regex__PRIVACY PROTECTION",
                ],
                "break_para_pattern": [
                    r"Aspect B7: Anti-corruption",
                ],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__(Product\s?Responsibility|Responsible\s?Services)__regex__Privacy\s?Protection",
                    r"__regex__Data\s?Privacy\s?Protection",
                    r"__regex__B6.*?SERVICES RESPONSIBILITY",
                ],
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "multi_elements": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"data privacy|personal data|confidentiality|B6.5",
                        r"(?P<content>safeguard the information of the Group)",
                        r"(?P<content>collected only when it is necessary)",
                        EXPLAIN_PATTERN,
                    ),
                    "neglect_pattern": (r"had no non-compliance cases.*?on.*?data privacy",),
                },
                "table_model": "empty",
            },
            {
                "name": "para_match",
                "multi_elements": True,
                "paragraph_pattern": (
                    r"protect customers.*?privacy",
                    r"(?P<content>safeguard the information of the Group)",
                    r"(?P<content>collected only when it is necessary)",
                    r"contractual obligation to protect the information of clients",
                    r"B6.*?are not the key material matters.*?are not addressed in this ESG report",
                    r"customer (information and )?privacy",
                    r"customer data protection",
                    r"confident(?:(?!(\.)).)*customer information",
                    r"information security",
                    EXPLAIN_PATTERN,
                ),
            },
            {
                "name": "row_match",  # 未披露的情况
                "first_cell": False,
                "force_use_all_elements": True,
                "ignore_index_table": True,
                "row_pattern": [
                    r"B6\.5",
                    r"consumer data protection",
                    r"privacy policies",
                ],
            },
            DEFAULT_MODEL,
        ],
    },
]
