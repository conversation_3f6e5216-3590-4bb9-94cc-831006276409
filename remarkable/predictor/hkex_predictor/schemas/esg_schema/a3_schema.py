"""
Jura4 ESG
"""

from remarkable.common.common_pattern import EXPLAIN_PATTERN as COMMON_EXPLAIN_PATTERN
from remarkable.predictor.hkex_predictor.schemas.esg_schema.common_pattern import (
    DEFAULT_MODEL,
    NEGLECT_PAGE_HEADER_PATTERN,
)

EXPLAIN_PATTERN = tuple(COMMON_EXPLAIN_PATTERN)


predictor_options = [
    {
        # A3&A3.1 这两个规则的内容接近较难区分，A3侧重于总结性的政策，A3.1侧重于实际行动。
        # 在实际提取中描述涉及的关键词类似较难分开，可以在这两处提取相同的内容，这两规则的提取和判断规则是一致的。
        # 提取内容：
        # 【涉及natural resources，environment 政策的段落】
        # 在有包含“natural resources+environment”标题时，
        #       可提取该标题下的全部内容及environment相关标题下reduce+impact或impact+environment的段落。
        # 在有index的情况下，去index指明的位置提取会更准确
        # 当没有“natural resources+environment”相关的标题时，
        #        提取A1POLICY+A2POLICY的内容,及environment相关标题下reduce+impact或impact+environment的段落
        # E的判断：
        # 【简单情况】：not disclosed,not available, NA,
        # 【复杂情况】：not+impact，little significance+impact， not involve, not produce等 ，且没有任何政策措施相关的描述
        # Y的判断：
        # 有：natural resources，environment等相关的policy或management或measure或reduce或control等措施的描述。
        # 【要注意】：当有“not+impact，little significance+impact， not involve, not produce”等的描述，
        # 但是同时又有政策措施相关的描述，仍判断Y而非E
        "path": ["A3 policies - environment and natural resources"],
        "models": [
            # 优先提取
            # {
            #     "name": "a3_policy",
            #     "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
            #     "only_inject_features": True,
            #     "inject_syllabus_features": [
            #         r'__regex__natural resources.*?environment',
            #         r'__regex__environment.*?natural resources',
            #     ],
            # },
            {
                "name": "after_row_match",
                "row_pattern": [r"A3"],
                "multi_elements": True,
                "para_pattern": [
                    r"(minimise|minimize).*?impacts",
                    r"environmental protection",
                    r"environmental sustainability",
                    r"minimise|(minimize|lower|minimising).*?(impacts|effect)",
                    r"(?P<content>not own.*?emissions)",
                    r"(?P<content>not cause any significant impact on)",
                    r"developed environmental management system",  # 63972
                    r"any non-compliance cases regarding emissions",  # 63972
                    r"understand our various environmental requirements",  # 63972
                    r"promoting green building",  # 63972
                ],
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": [r"^climate change$"] + NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__green building",  # file63972 63993
                    r"__regex__GREEN OPERATION",  # file63970
                ],
                "break_para_pattern": [
                    r"^Green design$",
                ],
            },
            {  # file64005 63998 63973特殊情况
                "name": "syllabus_elt_v2",
                "ignore_pattern": [r"climate change"] + NEGLECT_PAGE_HEADER_PATTERN,
                "multi": True,
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__protecting (the )?environment",
                    r"__regex__our environment",
                    r"__regex__^environment$",
                ],
                "break_para_pattern": [
                    r"^CLIMATE CHANGE",
                ],
                "neglect_parent_features": [
                    r"^SUSTAINABILITY AT A GLANCE$",
                    r"^Sustainability Foundation$",
                ],
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": [r"^climate change$"] + NEGLECT_PAGE_HEADER_PATTERN,
                "multi": True,
                "only_before_first_chapter": True,
                "inject_syllabus_features": [
                    r"__regex__natural resources+environment",
                    r"__regex__ENVIRONMENTAL.*?REPORT__regex__ENVIRONMENTAL__regex__Environmental Policies",
                    r"__regex__A[.]?3",
                    r"__regex__Environment.*?Resources",
                    r"__regex__use of resources",
                    r"__regex__Ecological Protection in Operating Regions",
                    r"__regex__environment management",
                    r"__regex__Energy Conservation and Consumption Reduction",
                    r"__regex__Operational Eco-efficiency",  # file64001
                    r"__regex__ENVIRONMENTAL MANAGEMENT APPROACH",  # file63972
                ],
            },
            {
                "name": "para_match",
                "force_use_all_elements": True,
                "paragraph_pattern": (r"(?P<content>focus(ing)? on(?:(?!\.).)* impact on environment)",),
            },
            {
                "name": "para_match",
                "syllabus_regs": [
                    r"Environment|Resources",
                ],
                "paragraph_pattern": (
                    r"(?P<content>focus(ing)? on(?:(?!\.).)* impact on environment)",
                    r"(?P<content>not direct impact|not disclosed|of little significance|not involve|not produce|not applicable|non-material|not disclosed)",
                    r"(?P<content>not own.*?emissions)",
                    r"(?P<content>not cause any significant impact on)",
                    r"(?P<content>minimise|(minimize|lower|minimising).*?(impacts|effect)|environmental protection|environmental sustainability)",
                ),
            },
            DEFAULT_MODEL,
        ],
    },
    {
        "path": ["KPI A3.1 - impact on environment and natural resources"],
        "models": [
            {
                "name": "twice_row_match",
                "use_all_elements": True,
                "first_cell": False,
                "row_pattern": [r"A3\.1"],
                "second_pattern": EXPLAIN_PATTERN,
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": ["climate change"] + NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__A3\.1",
                    r"__regex__Impacts.*?Environment",
                    r"__regex__Environment.*?Resources",
                    r"__regex__use of resource",
                    r"__regex__green building",
                    r"__regex__Promoting Green Operation",
                ],
            },
            {
                "name": "after_row_match",
                "row_pattern": [r"A3"],
                "multi_elements": True,
                "para_pattern": [
                    r"(minimise|minimize).*?impacts",
                    r"environmental protection",
                    r"environmental sustainability",
                    r"minimise|(minimize|lower|minimising).*?(impacts|effect)",
                    r"(?P<content>not own.*?emissions)",
                    r"(?P<content>not cause any significant impact on)",
                ],
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "multi": True,
                "only_inject_features": True,
                "only_before_first_chapter": True,
                "inject_syllabus_features": [
                    r"__regex__natural resources+ environment",
                    r"__regex__ENVIRONMENTAL.*?REPORT__regex__ENVIRONMENTAL__regex__Environmental Policies",
                    r"__regex__A[.]?3",
                    r"__regex__Environment.*?Resources",
                    r"__regex__use of resources",
                    r"__regex__Ecological Protection in Operating Regions",
                    r"__regex__environment management",
                    r"__regex__Energy Conservation and Consumption Reduction",
                    r"__regex__KPI A3.1",
                ],
            },
            {  # file64005特殊情况
                "name": "syllabus_elt_v2",
                "ignore_pattern": [r"climate change"] + NEGLECT_PAGE_HEADER_PATTERN,
                "ignore_syllabus_pattern": [r"climate Change"],
                "multi": True,
                "inject_syllabus_features": [
                    r"__regex__protecting (the )?environment",
                ],
            },
            {
                "name": "para_match",
                "syllabus_regs": [
                    r"Environment|Resources",
                ],
                "paragraph_pattern": (
                    r"(?P<content>not direct impact|not disclosed|of little significance|not involve|not produce|not applicable|non-material|not disclosed)",
                    r"(?P<content>not own.*?emissions)",
                    r"(?P<content>not cause any significant impact on)",
                    r"(?P<content>minimise|(minimize|lower|minimising).*?(impacts|effect)|environmental protection|environmental sustainability)",
                ),
            },
            DEFAULT_MODEL,
        ],
    },
]
