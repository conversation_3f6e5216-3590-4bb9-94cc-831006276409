"""
Jura4 ESG
"""

from remarkable.predictor.hkex_predictor.schemas.esg_schema.common_pattern import NEGLECT_PAGE_HEADER_PATTERN

predictor_options = [
    {
        # A4 policies - climate-related issues
        # 提取内容：有关识别和缓解已影响和可能影响发行人的重大气候相关问题的政策
        # （侧重有没有去分析，或者识别，或者减缓等climate change 相关的政策或行动。）
        # 常见关键词：一般标题就有climate change, climate-related risks, responding to climate change；
        # 段落中也有climate change, carry on XXX analysis，mitigate xxx，take measures XXX。
        # 较多的是有没有相关的analysis
        # Y：只要有分析，或者识别，或者减缓等climate-related risks或者climate change的描述就是Y
        # （即使有类似“no significant impact“的描述）
        # E: 只披露了“没有进行climate change 相关的分析”，或者仅仅描述了“影响不大”但没有其他披露了
        "path": ["A4 policies - climate-related issues"],
        "models": [
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"formulated working mechanisms.*?climate change issues",
                    r"carries out the identification of climate change risks",  # file63981 多栏pdf的章节所辖段落识别错位
                    r"potential impacts of climate change on our Group",
                ),
            },
            {
                "name": "row_match",  # 未披露的情况
                "first_cell": False,
                "force_use_all_elements": True,
                "row_pattern": [
                    r"(A4(?![_\d\.])|climate-related|climate change).*not (an? )?(applicable|been disclosed|considered)",
                    r"(A4(?![_\d\.])|climate-related|climate change).*(?<![a-zA-Z])N/?A(?![a-zA-Z])",
                    r"(A4(?![_\d\.])|climate-related|climate change).*不適用",
                    r"(A4(?![_\d\.])|climate-related|climate change).*be disclosed in the future",
                    r"(A4(?![_\d\.])|climate-related|climate change).*not involve(?:(?!(\.)).)*recall",
                    r"(A4(?![_\d\.])|climate-related|climate change).*not material",
                ],
            },
            {  # 某些标题含`climate change`但子标题并不符合要求 https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/894#note_232219
                "name": "syllabus_elt_v2",
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__climate(-related)? risk",
                    r"__regex__Environmen?tal Risk Identif",  # 63965
                ],
                "multi": True,
                "include_title": True,
                "include_shape": True,
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN + [r"B.*?SOCIAL$"],
            },
            {
                "name": "syllabus_elt_v2",
                "multi": True,
                "include_title": True,
                "include_shape": True,
                "only_inject_features": True,
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN
                + [
                    r"B.*?SOCIAL$",
                    r"^[I]+\.",
                    r"[一二三四五六七八九十]+\、",
                ],
                "inject_syllabus_features": [
                    r"__regex__KPI A4(.1)?",
                    r"__regex__TCFD Report",
                    r"__regex__climate change",
                    r"__regex__The Group.s response",
                    r"__regex__Climate-related Risks?(, Opportunities, and Financial impact)?",
                    r"__regex__Physical risks",
                    r"__regex__Transition(?:al)? risks",
                    r"__regex__respond to climate change",
                    r"__regex__Tackling Climate Change",
                ],
                "neglect_patterns": [
                    r"Employment",
                ],
            },
            {
                "name": "shape_title",
                "force_use_all_elements": True,
                "regs": (
                    r"Transition Risks",
                    r"TCFD",
                    r"climate(-related)? risk",
                    r"combating climate change",  # file63981
                ),
            },
            {  # 较为精确的段落内容匹配
                "name": "para_match",
                "multi_elements": True,
                "enum_from_multi_element": True,
                "paragraph_pattern": (
                    r"(?P<content>climate change)",
                    r"(?P<content>(climate-related|Physical|Transition(?:al)?) risks)",
                ),
            },
            {  # file63949 特殊情况，披露在index表格中
                "name": "row_match",
                "first_cell": False,
                "force_use_all_elements": True,
                "row_pattern": [
                    r"(A4(?![_\d\.])|climate-related|climate change).*(identif|recogni)",
                ],
            },
            {  # ========== 所有手段失效，最宽泛的段落内容匹配 ==========
                "name": "para_match",
                "multi_elements": True,
                "enum_from_multi_element": True,
                "paragraph_pattern": (
                    r"(?P<content>not direct impact|not disclosed|of little significance|not involve|not produce|not applicable|non-material|not disclosed)",
                    r"(?P<content>identified|recognising|climate change|respond|climate)",
                    r"there are policies",
                ),
            },
            # DEFAULT_MODEL,
        ],
    },
    {
        # A4.1 - climate-related issues & impact:
        # 提取内容：描述已影响和可能影响发行人的重大气候相关问题，以及为管理这些问题而采取的措施。（侧重于发现了什么影响。）
        # A4跟A4.1定位框选时：
        #
        # i.若没有明显的标题区分可以直接框选climate change标题下方的内容;
        # ii.但若有明显的标题可以区分A4跟A4.1的内容时，需要分别框选。
        #
        # Y: 如果文档披露各种潜在可能的climate change造成的risk和likehood( 可能性），即使有类似“no significant impact“的描述，也是Y。
        # E: 没有披露可能的各种risk，仅描述了影响不大
        "path": ["KPI A4.1 - climate-related issues & impact"],
        "models": [
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"formulated working mechanisms.*?climate change issues",
                    r"carries out the identification of climate change risks",  # file63981
                    r"potential impacts of climate change on our Group",
                ),
            },
            {  # 某些标题含`climate change`但子标题并不符合要求  # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/894#note_232219
                "name": "syllabus_elt_v2",
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__climate(-related)? risk",
                    r"__regex__Environmen?tal Risk Identif",  # 63965
                ],
                "multi": True,
                "include_title": True,
                "include_shape": True,
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN + [r"B.*?SOCIAL$"],
            },
            {
                "name": "syllabus_elt_v2",
                "multi": True,
                "include_title": True,
                "include_shape": True,
                "only_inject_features": True,
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN
                + [
                    r"B.*?SOCIAL$",
                    r"[I]+\.",
                    r"[一二三四五六七八九十]+\、",
                ],
                "inject_syllabus_features": [
                    r"__regex__TCFD Report",  # file63997, TCFD: Task Force on Climate-Related Financial Disclosures
                    r"__regex__KPI A4.1",
                    r"__regex__climate change",
                    r"__regex__Climate-related Risks, Opportunities, and Financial impact",
                    r"__regex__Action on Climate Change",
                    r"__regex__The Group.s response",
                    r"__regex__Physical risks",
                    r"__regex__Transition(?:al)? risks",
                    r"__regex__respond to climate change",
                    r"__regex__Tackling Climate Change",
                ],
                "neglect_patterns": [
                    r"Employment",
                ],
            },
            {
                "name": "shape_title",
                "force_use_all_elements": True,
                "regs": (
                    r"Transition Risks",
                    r"TCFD",
                    r"climate(-related)? risk",
                    r"combating climate change",  # file63981
                ),
            },
            # -------- 当没有具体章节表示A4.1时，可能为A4 policy的E --------
            {
                "name": "row_match",
                "first_cell": False,
                "force_use_all_elements": True,
                "row_pattern": [
                    r"(A4(?![_\d\.])|climate-related|climate change).*not (an? )?(applicable|been disclosed|considered)",
                    r"(A4(?![_\d\.])|climate-related|climate change).*(?<![a-zA-Z])N/?A(?![a-zA-Z])",
                    r"(A4(?![_\d\.])|climate-related|climate change).*不適用",
                    r"(A4(?![_\d\.])|climate-related|climate change).*be disclosed in the future",
                    r"(A4(?![_\d\.])|climate-related|climate change).*not involve(?:(?!(\.)).)*recall",
                    r"(A4(?![_\d\.])|climate-related|climate change).*not material",
                ],
            },
            {  # 较为精确的段落内容匹配
                "name": "para_match",
                "multi_elements": True,
                "enum_from_multi_element": True,
                "paragraph_pattern": (
                    r"(?P<content>climate change)",
                    r"(?P<content>(climate-related|Physical|Transition(?:al)?) risks)",
                ),
            },
            {  # file63949 特殊情况，披露在index表格中
                "name": "row_match",
                "first_cell": False,
                "force_use_all_elements": True,
                "row_pattern": [
                    r"(A4\.1|climate-related|climate change).*(identif|recogni)",
                ],
            },
            # ========== 所有手段失效，最宽泛的段落内容匹配 ==========
            {
                "name": "para_match",
                "multi_elements": True,
                "enum_from_multi_element": True,
                "paragraph_pattern": (
                    r"(?P<content>not direct impact|not disclosed|of little significance|not involve|not produce|not applicable|non-material|not disclosed)",
                    r"(?P<content>identified|recognising|climate change|respond|climate)",
                    r"no significant climate-related issues",
                ),
            },
            # DEFAULT_MODEL,
        ],
    },
]
