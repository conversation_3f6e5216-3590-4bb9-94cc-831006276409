"""
Jura4 ESG
"""

from remarkable.predictor.hkex_predictor.schemas.esg_schema.common_pattern import (
    COMPLY_LAW_PATTERN,
    DEFAULT_MODEL,
    NEGLECT_PAGE_HEADER_PATTERN,
)

predictor_options = [
    {
        # 枚举判定为：E
        # ESG报告中没有相关劳工标准政策披露但是有解释
        # 常见表述“由于公司的业务性质是XXX，涉及到的劳动力较少，所以not applicable”
        # 或“由于公司的业务性质,雇佣的员工多为有一定资历或者工作经验的员工，童工或强制劳动的风险小 "
        # 或“no相关的问题” 或“not material" 的描述
        # 常见关键词：not material，not applicable, non-labour intensive
        #
        # 枚举判定为：Y
        # ESG报告中有关于劳工标准labour standards政策的披露
        # 提取涉及到labour standards政策的所有段落。常见的描述是“公司的劳工标准是……，遵守当地劳工法律法规，禁止雇佣童工、强迫劳动和非法用工等”。
        # i. 一般有单独的labour standard相关标题，能区分出后面B4.1和B4.2的就不勾进去
        # ii.无单独的labour standard相关标题，可能会跟B1 employment标题下方的内容混在一起，可通过关键词child,forced labor进行定位
        # 常见关键词： labour, labor，B4, workforce, forced labour, engaging child, Labour Standards，prevent
        #
        # ND: 没有披露相关内容,也没有进行解释
        "path": ["B4 policies - labour standards"],
        "models": [
            {
                "name": "para_match",
                "multi_elements": True,
                "paragraph_pattern": (
                    r"clearly lists the prohibited types of forced labor",
                    r"revised the Measures for Fair Management of Employees",
                    r"revised the Goldwind Rules for Employee Attendance",
                    r"strictly prohibiting and discouraging any form of child or forced labor",
                ),
            },
            {
                # 章节标题和第一个小标题之间
                "name": "policy",
                "only_inject_features": True,
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "ignore_pattern_match": True,
                "inject_syllabus_features": [
                    r"__regex__Aspect B4: Labour Standards",
                ],
            },
            {
                "name": "syllabus_based",
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__B3.*?Development and Training",
                    r"__regex__talent management",
                    r"__regex__Labou?r Standard",
                    r"__regex__Labou?r Practices",
                    r"__regex__Prevention of Child and Forced Labour",
                    r"__regex__Human Capital",
                    r"__regex__Employment and Interests",
                    r"__regex__Fair and Standard Employment",
                ],
                "neglect_parent_features": [
                    r"Laws and Regulations",
                ],
                "multi_elements": True,
                "multi": True,  # file63947
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"as are not addressed in this ESG",
                        r"any child labour",
                        r"forbids all types",
                        r"prevent.*?child labour",
                        r"(child|forced|illegal).*?(labour|labor)",
                        r"ensure compliance with relevant labour laws",
                        r"To prevent hiring child labour by mistake",
                        r"rights of labours",
                        r"Free chosen employment",
                        r"Remuneration and benefits",
                        r"Equal opportunity and no discrimination policy",
                        r"not force any employees to work",
                        r"Harassment and abuse",
                        r"forced and child labour",
                    ),
                    "neglect_pattern": (
                        r"have been no cases of ",
                        r"During the Reporting Period",
                        r"operates in compliance with relevant laws",
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_before_first_chapter": True,
                # "only_first": True,
                "inject_syllabus_features": [
                    r"__regex__Labour Standards",
                    r"__regex__Employment and Labour Standards",
                    r"__regex__Employment and Labour PRACTICES",  # file64005
                    r"__regex__PROTECTING EMPLOYEES.*?RIGHTS AND INTERESTS",
                    r"__regex__Human Capital",
                    r"__regex__Employment Policy",
                ],
            },
            {
                "name": "syllabus_elt_v2",
                "only_inject_features": True,
                "multi": True,  # file63948
                "only_first": True,  # file63947
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "inject_syllabus_features": [
                    r"__regex__forced? (labour|worker)",
                    r"__regex__child (labour|worker)",
                ],
            },
            {
                "name": "para_match",
                "strict_limit": True,
                "syllabus_regs": [
                    r"Labour Standards",
                    r"Employment and Labour Standards",
                    r"Human Capital",
                    r"Employment Policy",
                ],
                "paragraph_pattern": (
                    r"(?P<content>not material|not applicable|non-labour intensive)",
                    r"(?P<content>labour|labor|B4|workforce|forced labour|engaging child|Labour Standards)",
                    r"(?P<content>not have any violation relating)",
                ),
                "neglect_pattern": (r"to prevent any employment of child labour",),
            },
            {
                "name": "para_match",
                # 'multi_elements': True,
                "paragraph_pattern": (r"child and forced labour",),
            },
            {
                "name": "row_match",
                "ignore_index_table": True,
                "row_pattern": [r"forced? labour", "child labour"],
                "first_cell": False,
            },
            DEFAULT_MODEL,
        ],
    },
    {
        "path": ["B4 law compliance - labour standards"],
        "models": [
            {
                "name": "after_row_match",
                "row_pattern": [r"GD B4", r"B4\. Labour StandardsGeneral disclosure"],
                "direct_answer_pattern": [
                    r"comply with all applicable local laws.*?Labour Law",
                ],
            },
            {
                "name": "twice_row_match",
                "first_cell": False,
                "ignore_index_table": True,  # file63997披露在index表格中 通过after_row_match提取
                "row_pattern": COMPLY_LAW_PATTERN,
                "merge_valid_rows": False,
                "second_pattern": [
                    # r'B4(\s+|$|[a-zA-Z])',  # file63997披露在index表格中
                    r"Labou?r Standards?",  # file63983
                ],
            },
            {
                "name": "twice_para_match",
                "multi_elements": True,
                "use_all_elements": True,
                # "strict_limit": True,
                "syllabus_regs": [],
                "neglect_syllabus_regs": [
                    r"Supply chain management",
                    r"Anti.*?Corruption",
                    r"The Environment and Natural Resources",
                    r"Data Privacy",
                    r"B[1-3]|B[5-8]|A[1-3]",
                ],
                "paragraph_pattern": COMPLY_LAW_PATTERN,
                "second_pattern": (
                    r"workforce",
                    r"engaging child",
                    r"labou?r (Contract )?law",
                    r"(child|forced|illegal).*?labou?r",
                    r"labour related (law|regulation)",
                ),
            },
            {
                "name": "shape_title",
                "force_use_all_elements": True,
                "regs": (r"Diversified Talent Attraction Policies",),  # file64003针对infographics的特殊规则
            },
            {
                "name": "twice_row_match",
                "first_cell": False,
                "ignore_index_table": True,
                "row_pattern": COMPLY_LAW_PATTERN,
                "merge_valid_rows": False,
                "second_pattern": [
                    r"B4(\s+|$)",
                    r"workforce",
                    r"engaging child",
                    r"labou?r (Contract )?law",
                    r"(child|forced|illegal).*?labou?r",
                    r"labour related (law|regulation)",
                ],
            },
            {
                "name": "para_match",
                "syllabus_regs": [
                    r"Labour Standards",
                    r"Employment and Labour Standards",
                    r"Human Capital",
                    r"Employment Policy",
                    r"Supply chain management",
                    r"Anti.*?Corruption",
                    r"The Environment and Natural Resources",
                    r"Data Privacy",
                    r"B[1-3]|B[5-8]|A[1-3]",
                ],
                "paragraph_pattern": (
                    r"complied.*?laws and regulations.*?labour standards",
                    r"(?P<content>not applicable|no (law|ordinance|regulation))",
                    r"(?P<content>(law|regulations).*?(workforce|forced labor|child|engaging child))",
                    r"(?P<content>not have any violation relating)",
                ),
                "neglect_pattern": (r"Information on:",),
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__Labour Standards",
                    r"__regex__Employment and Labour Standards",
                    r"__regex__Human Capital",
                    r"__regex__Employment Policy",
                ],
                "multi_elements": True,
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"(?P<content>not applicable|no (law|ordinance|regulation))",
                        r"(?P<content>(law|regulations).*?(workforce|forced labor|child|engaging child))",
                        r"(?P<content>not have any violation relating)",
                        r"did not identify any material breaches",
                    ),
                    "neglect_pattern": (r"Information on:",),
                },
                "table_model": "empty",
            },
            {
                "name": "kmeans_classification",
                "threshold": 0.1,
            },
            # DEFAULT_MODEL,
        ],
    },
    {
        # 枚举判定为：E
        # ESG报告中没有相关披露但是有解释
        # 常见表述“这条规则不适用于本集团”或者 “N/A”或“not disclosure”
        # 关键词：not material，not applicable, N/A, not disclosure
        #
        # 枚举判定为：Y
        # ESG报告中有关于公司如何审查以避免child and labour force的措施的披露
        # 提取涉及child和forced labour措施的所有段落，侧重于labour force和用工年龄方面。
        # 常见的描述是“公司已制定招聘政策，符合年龄的申请人才可被聘用”或者“本集团要求求职者提供有效的身份证明文件，确保年龄符合规定”等。
        # i.一般在labour standard相关标题的内容中披露相关措施；
        # ii.无单独的labour standard相关标题，可能会跟B1 employment标题下方的内容混在一起，可通过关键词child,forced labor进行定位`
        # 关键词：workforce, forced labour, engaging child，child labour，establish，overtime，prohibition，prevent，
        # identity/identification  document,identity card,recruiting,above 18 years old,working visa
        #
        # ND: 没有披露相关内容,也没有进行解释
        "path": ["KPI B4.1 - review measures to avoid child & forced labour"],
        "models": [
            {
                "name": "after_row_match",
                "row_pattern": [r"B4.1"],
                "only_syllabus_of_para": True,
                "include_row": True,  # file63960E的情况框选整行
                "ignore_syllabus": [r"^Our Employees$"],
                "para_pattern": (
                    r"Protection of Employees' Rights and Interests",  # file63948
                    r"Measures Taken to Avoid Child and Forced Labour",  # file63979
                ),
                "ignore_syllabus_pattern": (
                    r"Prohibition of discrimination and sexual harassment in the workplace",
                    r"Protection of legal rights and interests",
                ),
            },
            {
                "name": "after_row_match",
                "row_pattern": [r"B4.1"],
                "ignore_syllabus": [r"^Our Employees$"],
                "multi_elements": True,
                "para_pattern": (
                    r"(?P<content>not material|not applicable|N/A|not disclosure)",
                    r"(?P<content> workforce|engaging child)",
                    # r'establish|overtime',
                    # r'prohibition|prevent',
                    # r'(forced|child) labou?r',
                    r"identity|identification document",
                    r"identity card",
                    r"recruiting",
                    r"above 18 years old",
                    r"working visa",
                ),
                # 'second_pattern': (),
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__KPI B4.1",
                    r"__regex__Employment Guidelines",
                    r"__regex__Employment and Labour Standards",
                    r"__regex__Labor Rights and Interests Protection",
                    r"__regex__Prevent.*?Child and Forced Labour",
                    # r'__regex__Preventative measures against child and forced labour',
                    r"__regex__Labour Standard",
                    r"__regex__Human Capital",
                    r"__regex__Employment Policy",
                    r"__regex__Employment and Interests",
                    r"__regex__Protection of Employees' Rights and Interests",
                ],
                "multi_elements": True,
                "multi": True,  # file63947
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"not material|not applicable|N/A|not disclosure",
                        r"workforce|engaging child",
                        # r'child labour', # 仅提到child labour 没有提到相关措施的不能提取
                        # r'establish|overtime',
                        r"complete pre-employment application",
                        r"provide.*?identification",
                        r"(identity|identification document)",
                        r"identity card",
                        r"recruiting",
                        r"above 18 years old",
                        r"working visa",
                        r"ensure no child labor",
                        r"checks the documents provided",
                        r"inspect applicant’s documents",
                        r"checks.*?identity documents",
                        r"Free chosen employment",
                        r"Remuneration and benefits",
                        r"Equal opportunity and no discrimination policy",
                        r"not force any employees to work",
                        r"Harassment and abuse",
                        r"forced and child labour",
                        r"forced labour is strictly prohibited",  # file63979
                    ),
                    "neglect_pattern": (
                        r"During the Year",
                        r"engage suppliers and contractors",
                    ),
                },
                "table_model": "row_match",
                "ignore_index_table": True,
                "table_config": {
                    "row_pattern": [
                        r"(child|forced) labour",
                    ],
                },
            },
            {
                "name": "syllabus_elt_v2",
                "only_inject_features": True,
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "inject_syllabus_features": [
                    r"__regex__B4.1",
                    # r'__regex__Labour Standard',
                    r"__regex__Employment and Labour Standards",
                    r"__regex__Human Capital",
                ],
            },
            {
                "name": "syllabus_elt_v2",
                "only_inject_features": True,
                "multi": True,  # file63948
                "only_first": True,  # file63947
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "inject_syllabus_features": [
                    r"__regex__forced? (labour|worker)",
                    r"__regex__child (labour|worker)",
                ],
            },
            {
                "name": "para_match",
                "syllabus_regs": [
                    r"Labour Standards",
                    r"Employment and Labour Standards",
                    r"Human Capital",
                ],
                # 'multi_elements': True,
                "paragraph_pattern": (
                    # r'(?P<content>not material|not applicable|N/A|not disclosure)',
                    # r'(?P<content>workforce|forced labour|engaging child|establish|overtime)',
                    r"complete pre-employment application",
                    r"provide.*?identification",
                    r"(identity|identification document)",
                    r"identity card",
                    r"recruiting",
                    r"above 18 years old",
                    r"working visa",
                    r"ensure no child labor",
                    r"checks the documents provided",
                    r"inspect applicant’s documents",
                    r"checks.*?identity documents",
                    r"B4.*?are not the key material matters.*?are not addressed in this ESG report",
                ),
            },
            {
                "name": "para_match",
                # 'multi_elements': True,
                "paragraph_pattern": (r"verif(ie(s|d)|y) the age",),
            },
            {
                "name": "row_match",
                "ignore_index_table": True,
                "row_pattern": [r"forced? labour", "child labour"],
                "first_cell": False,
            },
            # {
            #     "name": 'kmeans_classification',
            # },
            DEFAULT_MODEL,
        ],
    },
    {
        # 框选内容：<优先在index指引的正文内容去找>
        #
        # 如果存在明显的细分小标题**kpi B4.2**，则直接框该小标题下内容
        #
        #  一般描述中会有**比较明确的关键词**： in case of，once found, in the event这种表示
        #  如果发现forced labour，child labour的词。
        "path": ["KPI B4.2 - steps to avoid child & forced labour"],
        "models": [
            {
                "name": "para_match",
                "paragraph_pattern": (r"B4.*?are not the key material matters.*?are not addressed in this ESG report",),
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__Prevention of child labour and forced labour",
                    r"__regex__B4\.2",
                ],
            },
            {
                "name": "twice_para_match",
                "force_use_all_elements": True,
                "multi_elements": True,
                "syllabus_regs": [
                    r"Labour Standards",
                    r"Labour RIGHT",
                    r"Employment and Labour Standards",
                    r"Human Capital",
                    r"Employment Policy",
                    r"Prevention of child labour and forced labour",
                    r"Protecting employees’ rights and interests",
                    r"Employment Principles",
                    r"Retaining Talents",
                ],
                "paragraph_pattern": (
                    r"(child|forced|illegal).*?(labour|labor)",
                    r"l ?abour rights",  # file64000单词识别错误
                    r"any irregularities in ages",
                ),
                "second_pattern": (
                    r"in case of|once found|in the event| once|facing|once discovered",
                    r"if(?:(?!\.).)*(involve|found|occurrence|i ?dentified)",  # i ?dentified file64000单词识别错误
                    r"if(?:(?!\.).)*provided? forgery information",
                    r"if the case is justified",
                    r"regular investigation|severe treatment",  # file63988
                    r"for any illegal labour(?:(?!\.).)*take(?:(?!\.).)*actions? immediately",  # file63958
                ),
            },
            {
                "name": "after_row_match",
                "row_pattern": [r"B4.2"],
                # 'para_pattern': [
                #     r'(?P<content>not material|not applicable|non-labour intensive)',
                #     r'cases of|in case of|once found|in the event',
                # ],
                "para_pattern": (r"(child|forced|illegal).*?(labour|labor)",),
                "second_pattern": (
                    r"in case of|once found|in the event",
                    r"once|facing",
                    r"once discovered",
                ),
            },
            {
                "name": "row_match",
                "first_cell": False,
                "force_use_all_elements": True,
                "row_pattern": [
                    r"B4\.2.*not (an? )?(applicable|been disclosed|considered)",
                    r"B4\.2.*(?<![a-zA-Z])N/?A(?![a-zA-Z])",
                    r"B4\.2.*不適用",
                    r"B4\.2.*be disclosed in the future",
                    r"B4\.2.*not involve(?:(?!(\.)).)*recall",
                    r"B4\.2.*not material",
                    r"B4\.2.*not to disclose",
                ],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__B4.[12]",
                ],
                "multi_elements": True,
                "paragraph_model": "twice_para_match",
                "para_config": {
                    "paragraph_pattern": (r"(child|forced|illegal).*?(labour|labor)",),
                    "second_pattern": (
                        r"(?<!any )cases? of",
                        r"in case of|once found|in the event",
                        r"once|facing",
                        r"discovered",
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "para_match",
                "paragraph_pattern": (r"employees work voluntarily",),
            },
        ],
    },
]
