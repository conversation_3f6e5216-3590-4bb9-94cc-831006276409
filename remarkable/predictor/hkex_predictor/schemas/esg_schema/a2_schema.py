"""
Jura4 ESG
"""

from remarkable.common.common_pattern import EXPLAIN_PATTERN as COMMON_EXPLAIN_PATTERN
from remarkable.predictor.hkex_predictor.schemas.esg_schema.common_pattern import (
    DEFAULT_MODEL,
    NEGLECT_PAGE_HEADER_PATTERN,
)

EXPLAIN_PATTERN = tuple(COMMON_EXPLAIN_PATTERN)

predictor_options = [
    {
        # 提取内容：
        # 涉及resources政策的段落，段落中常包含 resource，energy，usage，water consumption等词语。
        # 在有包含use/usage+resources标题时，可提取该标题下的全部内容。在有index的情况下，去index指明的位置提取会更准确
        # 当没有use/usage+resources相关的标题时，可提取energy，water，paper等相关标题下的措施
        # E的判断：
        # 【简单情况】：not disclosed,not available, NA,
        # 该规则E的情况在标注过程中还没有遇到过
        # Y的判断：
        # 有：resources，energy，water，paper等相关的policy或management或measure或reduce或control等措施的描述。
        "path": ["A2 policies - use of resources"],
        "models": [
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_before_first_chapter": True,  # file64000 file63990
                "only_inject_features": True,
                # "only_first": True,
                "inject_syllabus_features": [
                    r"__regex__Use of Resources",
                    r"__regex__(use|usage).*?resources",
                    r"__regex__Emissions and Use of resources",
                    r"__regex__Use of Resources and Management",
                    r"__regex__utilisation.*?resources",
                    r"__regex__Resources.*?Utilisation",
                    r"__regex__Reducing Resource Waste",
                    r"__regex__ENVIRONMENT AND NATURAL RESOURCES",
                    r"__regex__Green and Low-Carbon Operation",
                    r"__regex__Renewable energy connections",
                ],
                "neglect_parent_features": [
                    r"Independent Limited Assurance Report",  # file63947 应该是r'Appendix'但层级错误
                ],
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_before_first_chapter": True,
                "multi": True,
                "inject_syllabus_features": [
                    r"__regex__Energy (Conservation|Efficiency|Usage|Saving)",  # file63958
                    r"__regex__Water Conservation",
                    r"__regex__Packaging Materials",
                    r"__regex__(resource|paper|ELECTRICITY)",
                ],
                "neglect_parent_features": [
                    r"Independent Limited Assurance Report",  # file63947 应该是r'Appendix'但层级错误
                    r"Our approach to human resources",
                ],
            },
            {
                "name": "kmeans_classification",
                "filter_content_answer": True,
            },
            {
                "name": "para_match",
                "multi_elements": True,
                "enum_from_multi_element": True,
                "paragraph_pattern": (
                    r"(?P<content>measures|policy|resource|energy|usage|consumption)",
                    r"turn off (lights|air-conditioners)|energy efficiency|energy conservation|switch off|avoid printing",
                    r"LED lighting|effectiveness|reduction|recycle|reuse|save|waste|garbage|electricity|power|solar|digitalise|wastepaper|renewable|new energy",
                ),
            },
            DEFAULT_MODEL,
        ],
    },
    {
        # 需要定位披露能源消耗的数据(包含intensity/density），通常为图表形式，有的报告可能涉及文字描述
        # 出现位置：1. 报告末尾的performance data summary,KPI summary, performance table
        #          2. environment章节的末尾environmental data
        #          3. use of resource标题下energy management或者energy consumption小标题下的表格
        #          4. 位置不固定，但可以通过energy consumption/resource consumption关键词来定位
        #          （此规则所述`能源`通常涉及到electricity/petroleum/gasoline/diesel）
        "path": ["KPI A2.1 - energy consumption"],
        "models": [
            {
                "name": "para_match",
                "paragraph_pattern": (r"Total electricity consumption.*?kWh",),
            },
            {
                "name": "special_cells",
                "whole_table": True,
                "multi_elements": True,
                "just_first_row": True,
                "cell_pattern": (
                    r"Types of energy consumption",
                    r"Energy consumption intensity",
                    r"Energy usage",
                ),
            },
            {
                "name": "after_row_match",
                "row_pattern": [r"A\s?2\s?.1"],
                "para_pattern": [
                    r"Total electricity consumption.*?kWh",
                ],
                "just_a_para": True,
                "middle_rows": True,
                "start_regs": [r"Energy (consumption|type)"],
                "end_regs": [
                    r"non-hazardous",
                    r"Water usage",
                ],
            },
            {
                "name": "syllabus_based",
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__Greenhouse gas emission",
                ],
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (r"The heat consumed by the company is mainly steam",),
                    "use_direct_elements": True,
                },
                "table_model": "empty",
            },
            {
                "name": "middle_rows",
                "regs": [
                    r"Energy|Electricity",
                ],
                "start_regs": [
                    r"energy consumption",
                    r"Energy Intensity",
                    # r'Energy',
                    r"electricity consumption",
                    r"electricity.*?processing",
                    r"power consumption",
                    r"Total Consumption",
                    r"Energy.*?unit",  # 特例 兼容 ^Energy$
                    r"Electricity\s?\(kWh\)",
                    r"Use of Energy",
                ],
                "end_regs": [
                    r"Use of water",
                    r"water consumption",
                    r"GRI 303-4",
                    r"hazardous waste",
                    r"non-hazardous",
                    r"Water\s?\(m",
                ],
            },
            {
                "name": "row_match",
                "multi": True,
                "first_cell": False,
                "ignore_index_table": True,
                "row_pattern": [
                    r"A2.1",
                    r"Energy",
                    r"Electricity",
                    r"LPG",
                    r"Diesel|Naphtha|Petrol",
                ],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    "__regex__KPI A2.1",
                    "__regex__nergy Consumption",
                ],
                "only_inject_features": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"energy consumption.*?not material",
                        r"electricity consumption amounted",
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "para_match",
                "multi_elements": True,
                "enum_from_multi_element": True,
                "paragraph_pattern": (
                    r"Total electricity consumption.*?kWh",
                    r"(?P<content>not available|not a concern)",
                    r"(?P<content>oil|petrol|gasoline)",
                    r"(?P<content>A2.1|consumption|indirect|direct)",
                    r"(?P<content>During the Year.*?consumed.*?electricity)",
                    r"(?P<content>During the Year.*?consumed.*?gas oil)",
                ),
            },
            DEFAULT_MODEL,
        ],
    },
    {
        # 需要定位披露水资源消耗的数据(包含intensity/density），通常为图表形式，有的报告可能涉及文字描述
        # 出现位置：1. 报告末尾的performance data summary,KPI summary, performance table
        #          2. environment章节的末尾environmental data
        #          3. use of resource标题下water management或者water consumption小标题下的表格
        #          4. 位置不固定，但可以通过water consumption关键词来定位
        "path": ["KPI A2.2 - water consumption"],
        "models": [
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"water usage data.*?not feasible",
                    r"no individual water consumption data",
                    r"use of water is not significant",
                    r"not possess information.*?water consumption for disclosure",
                    r"water consumption was.*?m3",
                    r"Total water consumption.*?tons",
                ),
            },
            {
                "name": "after_row_match",
                "row_pattern": [
                    r"A2.2",
                ],
                "para_pattern": [
                    r"no individual water consumption data",
                ],
                "direct_answer_pattern": [
                    r"not available",
                ],
            },
            {
                "name": "table_title",
                "multi_elements": True,
                "only_inject_features": True,
                "feature_white_list": [
                    "本集團的?用水表現概要",
                    "本集團的?耗水詳情如下",
                    "water consumption",
                ],
            },
            {
                "name": "middle_rows",
                "ignore_index_table": True,
                "regs": [
                    r"A2.2",
                    r"water consumption",
                ],
                "start_regs": [
                    r"A2.2",
                    r"water consumption",
                ],
                "end_regs": [
                    r"A2.[345]",
                    r"packaging material",
                    r"Greenhouse Gas",
                    r"Energy Consumption",
                    r"Paper Consumption",
                    r"total Workforce",
                    r"^Workforce",
                    r"^waste",
                ],
            },
            {
                "name": "row_match",
                "ignore_index_table": True,
                "row_pattern": [
                    r"water\s",
                    r"water.*?(m|tones)",
                    r"Water Consumption",
                    r"A2.2",
                ],
                "multi": True,
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    "__regex__KPI A2.2",
                    "__regex__Wat.*?er Consumption",
                    "__regex__Greenhouse gas emission",
                ],
                "only_inject_features": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"water consumption.*?not available",
                        r"water consumption.*?approximately",
                        r"Water usage in the Group.*?is minimal",
                        r"The Company enhanced water-saving consciousness",
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "para_match",
                "multi_elements": True,
                "enum_from_multi_element": True,
                "paragraph_pattern": (
                    r"not applicable|no significant",
                    # r'no material',
                    # r'(?P<content>A2.2|Water consumption|water consumed)',
                ),
            },
        ],
    },
    {
        # A2涉及到的规则都是关于资源使用效率相关的内容；A2.3需要框选出公司为实现节能而设置的节能目标，或者为了节能而采取的措施；
        # 如果都没有也可以框选已经实现的节能目标，可以不是对未来目标的设置。
        #  所在位置：1. 涉及到use of resources的标题下关于energy efficiency的文字段落
        #           2. 在use of resources的标题下有关键词energy management的小标题下的内容
        #           3. 如果按照energy的种类披露的，一般会涉及到electricity，fuel，natural gas的reduction/saving/conservation内容
        #           4. 在ESG报告中单独有个environmental target的标题下，关键词energy的相关内容
        #
        #  提取内容：1. 提取涉及到“energy efficiency target”的数据
        #           2. 没数据但有“为了energy conservation 而有一些measures”的描述
        #           3. 当年或目前为止已经实现的energy efficiency target，可以不是未来的target
        #           4. 如果上面几点都有，则可以按照上面序号的优先级来全部提取
        #
        #  关键词：（需要在energy相关位置范围内查找）target, efficiency, establish, goal, aim, future plan,
        #  energy conservation, reduction, prevent
        #
        #  判定方式：Y-披露并描述以上的提取内容（满足提取内容的任何一条或多条即可）
        #           E-明确披露了 not material/not involve/not engage/ not disclosed/ not produce的
        #           E-写明了`没有establish energy efficiency target`的，即使有相关措施，也判定为E
        #           ND-没有任何描述
        "path": ["KPI A2.3 - energy efficiency targets"],
        "location_threshold": 0.05,
        "models": [
            {
                "name": "row_match",
                "ignore_index_table": True,
                "row_pattern": [
                    r"actively reducing energy consumption",
                    # r'Energy Conservation',
                    # r'A2.3',
                ],
            },
            {
                "name": "table_title",
                "multi_elements": True,
                "only_inject_features": True,
                "feature_white_list": [
                    r"Measures for Saving Energy",
                ],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__Environmental Initiative and Mitigation Target",
                ],
                "only_inject_features": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (r"^•", r"target"),
                },
                "table_model": "empty",
            },
            {
                "name": "syllabus_elt_v2",
                "only_inject_features": True,
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "inject_syllabus_features": [
                    r"__regex__Environmental.*?Target__regex__Quantitative target",
                    # r'__regex__Environmental__regex__Energy',
                    r"__regex__ENERGY CONSUMPTION__regex__measure",
                    r"__regex__Energy Use Efficiency Initiatives",
                    r"__regex__sustainability target",
                    r"__regex__Energy Conservation.*?KPI A2\.3",
                    r"__regex__Electricity and Water Management",
                ],
            },
            {
                "name": "syllabus_based",
                "only_inject_features": True,
                "max_syllabus_range": 80,
                "inject_syllabus_features": [
                    r"__regex__Environmental.*?Target__regex__Quantitative target",
                    r"__regex__Policies and Use of Resources",
                    r"__regex__Energy and Emissions Management",
                    r"__regex__Energy Consumption",  # uat 58433
                    r"__regex__Energy usage",
                    r"__regex__use of resources?",  # uat 58426
                    r"__regex__Use of Natural Resources?",
                    r"__regex__Electricity",  # 60875
                    r"__regex__Fuel",  # 60875
                    r"__regex__A2\.3.*?energy",
                ],
                # 'multi': True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"following measures to reduce energy consumption",
                        r"reduction of waste.*?include:$",
                        r"group targets? to reduce",
                        r"Group closely monitors its energy usage to consumption",
                        r"our aim is",
                        r"energy conservation",
                        r"set targets to",
                        r"targets? (is )?to reduce",
                        r"targeted a reduction",
                        r"has set a target",
                        r"set.*?reduction target",
                        r"set target to reduce waste",
                        r"no specific reduction target",
                        r"target",
                        r"employing various initiatives and measures",
                        r"implemented.*?as follows:",
                        r"to improve our energy efficiency",
                        r"plan is to reduce energy",
                        r"in order to.*?energy efficiency",
                        r"deployed the following energy efficient measures",
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "twice_para_match",
                "multi_elements": True,
                # "strict_limit": True,
                "syllabus_regs": [],
                "neglect_syllabus_regs": [],
                "paragraph_pattern": (r"energy|efficiency",),
                "second_pattern": (
                    r"A2.3|target|goal|reduce|measure|establish|set|reduce consumption",
                    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/878#note_230135
                    # r'building',
                ),
            },
            DEFAULT_MODEL,
        ],
    },
    {
        # 需要定位披露公司是否有取水问题，通常描述是公司在取水方面没有问题，但文字的表达方式有多种，通过sourcing + water可以大致定位
        # 出现位置：1. 报告末尾的performance data summary,KPI summary, performance table
        #          2. environment章节的末尾environmental data
        #          3. use of resource标题下water management中的文字描述
        #          4. 有的出现在water consumption的表格下面的notes里
        #          5. 有的报告直接披露在index里面，其他rule在index中会refer到某个章节的标题，
        #          而这条规则有时描述就在这个index的最后一列位置（有时候为not material或者N/A）
        "path": ["KPI A2.4 part 1 - water sourcing"],
        "models": [
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"water conservation target",
                    r"sources water from.*?no problem",
                    r"water consumption.*?insignificant",
                    r"no sourcing issue",
                    r"sources water from the municipal supplies",
                ),
            },
            {
                "name": "after_row_match",
                "row_pattern": [r"A2.4"],
                "multi_elements": True,
                "para_pattern": [r"water source"],
                "direct_answer_pattern": [
                    r"involve no significant use of water.*?not disclosed",
                    r"does not encounter any issues in sourcing water",
                    r"not identified",
                    r"Defined to be irrelevant to the Group’s operation",
                ],
            },
            {
                "name": "row_match",
                # "ignore_index_table": True,
                "first_cell": False,
                "row_pattern": [
                    r"involve no significant use of water.*?not disclosed",
                    r"does not encounter any issues in sourcing water",
                    r"no issues? related to sourcing water",
                    r"no issues? in (sourcing.*?water|water.*?sourcing)",
                    r"reusing wastewater and rainwater",
                ],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": ["__regex__KPI A2.4"],
                "only_inject_features": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (r"water consumption.*?not available",),
                },
                "table_model": "empty",
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"(?P<content>not encounter|not deal with|not face|no issue|sourcing water)",
                    r"(?P<content>municipal water|ground water|sourcing water|water sourcing)",
                    r"(?P<content>not have problems.*?water resources)",
                    r"did not have problems with shortage of water supply",
                    r"use of water is not material",
                ),
            },
            {
                "name": "para_match",
                "multi_elements": True,
                "paragraph_pattern": (r"has not identified any issues in sourcing|water that is fit for purpose",),
            },
            {
                "name": "kmeans_classification",
                "para_pattern": [
                    r"water source|sources water|sourced water",
                    r"water supply",
                ],
            },
            DEFAULT_MODEL,
        ],
    },
    {
        # A2涉及到的规则都是关于资源使用效率相关的内容；A2.4-part2需要框选出公司为节水而设置的节水目标，或者为了节水而采取的措施；
        # 如果都没有也可以框选已经实现的节水目标，可以不是对未来目标的设置。
        #  所在位置：1. 涉及到use of resources的标题下关于water efficiency的文字段落
        #           2. 在use of resources的标题下有关键词water management/ water usage的小标题下的内容
        #           3. 在ESG报告中单独有个environmental target的标题下，关键词water的相关内容
        #
        #  提取内容：1. 提取涉及到“water efficiency/saving target”的数据
        #           2. 没数据但有“为了water conservation/saving 而有一些measures”的描述
        #           3. 当年或目前为止已经实现的water efficiency target，可以不是未来的target
        #           4. 如果上面几点都有，则可以按照上面序号的优先级来全部提取
        #
        #  关键词：（需要在water相关位置范围内查找）target ,conserve water, water-efficient, water scarcity issue, establish,
        #  goal, aim, future plan,water conservation, reduction, prevent, decreased, avoid
        #
        #  判定方式：Y-披露并描述以上的提取内容（满足提取内容的任何一条或多条即可）
        #           E-明确披露了 not material/not involve/not engage/ not disclosed/ not produce的
        #           E-写明了`没有establish water efficiency target`的，即使有相关措施，也判定为E
        #           ND-没有任何描述
        "path": ["KPI A2.4 part 2 - water efficiency targets"],
        "models": [
            {
                "name": "row_match",
                # "ignore_index_table": True,
                "first_cell": False,
                "row_pattern": [
                    r"water Conservation",
                    r"conserving water resource",
                    r"involve no significant use of water.*?not disclosed",
                    r"does not encounter any issues in sourcing water",
                ],
            },
            {
                "name": "para_match",
                "anchor_regs": (r"water.*?target",),
                "paragraph_pattern": (r"To reduce the water",),
            },
            {
                "name": "syllabus_elt_v2",
                "only_inject_features": True,
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "inject_syllabus_features": [
                    r"__regex__sustainability target",
                    r"__regex__use of resource__regex__(water management|water usage)",
                    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/878#note_241211
                    r"__regex__waste management__regex__water usage",
                ],
            },
            {
                "name": "after_row_match",
                "row_pattern": [r"A2.4"],
                "multi_elements": True,
                "para_pattern": [
                    r"not considered a material aspect",
                    r"(?P<content>water conservation)",
                    r"(?P<content>conserving clean water)",
                    r"(?P<content>saving water|save water)",
                    r"(?P<content>data on water consumption is not available)",
                    r"(?P<content>saving.*?methods)",
                    r"(?P<content>saving.*?instruments)",
                    r"covering from.*?water.*?:$",
                    r"Clean water dispensers",
                    r"\swater recycling",
                    r"build drainage ditches",
                    r"wastewater generated by clean",
                    r"collect\s.*?water",
                    r"single-sided paper",
                    r"reduce wastage",
                    r"water-saving",
                    r"reduce the loss of water resources",
                ],
                "direct_answer_pattern": [
                    r"No issues in sourcing water",
                    r"No.*?target is set",
                    r"not set.*?targets",
                    r"not identified",
                    r"Defined to be irrelevant to the Group’s operation",
                ],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__Water Usage",
                    r"__regex__water.*?targets",
                    r"__regex__KPI A2.4",
                    r"__regex__use of resource__regex__water",
                    r"__regex__environmental__regex__water",
                    r"__regex__sustainability target",
                    r"__regex__Use of Resources",
                    r"__regex__EMISSION",
                ],
                "only_inject_features": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"water.*?follows.*?sources:$",
                        r"water consumption.*?not available",
                        r"Waste management.*?not been disclosed",
                        r"set a reduction target to reduce the water",
                        r"in order to reduce.*?water",
                        r"(reuse|recycling).*?water",
                        r"(saving|save|conserving) water",
                        r"water efficiency",
                        r"water Conservation",
                        r"target is.*?water",
                        r"plan is to reduce.*?water",
                        r"target.*?reduc.*?water",
                        r"achieve.*?recycling rate",
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "para_match",
                "multi_elements": True,
                "enum_from_multi_element": True,
                "neglect_pattern": (r"Water Consumption by Facility Locations",),
                "paragraph_pattern": (
                    r"(?P<content>not encounter|not deal with|not face|no issue|sourcing water)",
                    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/878#note_239988
                    r"(?P<content>municipal water|ground water|sourcing water|water sourcing|water\s+consumption)",
                    r"not considered a material aspect",
                    r"(?P<content>water conservation)",
                    r"(?P<content>conserving clean water)",
                    r"(?P<content>saving water|save water)",
                    r"(?P<content>data on water consumption is not available)",
                    r"(?P<content>saving.*?methods)",
                    r"(?P<content>saving.*?instruments)",
                    r"(?P<content>water.*?target)",
                ),
            },
            DEFAULT_MODEL,
        ],
    },
    {
        # 需要定位披露包装物材料的消耗量，通常为图表形式，也有很多报告涉及否定的文字描述
        # 出现位置：1. 报告末尾的performance data summary,KPI summary, performance table
        #          2. environment章节的末尾environmental data
        #          3. use of resource标题下packaging material 小标题下的表格
        #          4. 这条规则有时描述在index的最后一列位置（有时候为not material或者N/A，或者一句否定描述）
        #          5. 位置不固定，但关键词比较固定，可以通过packaging关键词来定位
        "path": ["KPI A2.5 - packaging material"],
        "models": [
            {
                "name": "middle_paras",
                "use_syllabus_model": True,
                "include_top_anchor": False,
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "inject_syllabus_features": [
                    r"__regex__pack(?:ag)?ing material",
                ],
                "top_anchor_regs": [
                    r"pack(?:ag)?ing material.*as (below|follow(ing)?)",
                ],
                "bottom_anchor_regs": [
                    r"Notes?.*?pack(?:ag)?ing material",
                ],
            },
            {
                "name": "row_match",
                # "ignore_index_table": True,
                "row_pattern": [
                    r"not directly consume any packaging materials",
                    r"did not involve consumption of packaging materials",
                    r"No packaging materials was used",
                    r"any relevant indicators for the packaging of products",
                    r"A2.5.*?commercially sensitive information",
                    r"Packaging materials consumption",
                    # r'Packaging cartons',
                ],
            },
            {
                "name": "after_row_match",
                "row_pattern": [r"A2.5"],
                "multi_elements": True,
                "para_pattern": [
                    r"not produce.*?utilize packaging materials",
                ],
                "index_header_patterns": [
                    r"\bKPI\b",
                ],
                "dst_row_patterns": [
                    r"packaging material",
                    r"packing material",
                    r"Intensity",
                ],
                # 拆分索引单元格内容
                "split_cell_pattern": r"－|—|:|：|\d+\.(?:\d+)?",
                "direct_answer_pattern": [
                    r"no packaging materials involved",
                ],
            },
            {
                "name": "middle_rows",
                "neglect_title_regs": [
                    r"\d+\.HK",
                ],
                "ignore_index_table": True,
                "regs": [
                    r"packaging material",
                    r"packing material",
                    r"Package Material",
                ],
                "start_regs": [
                    r"packaging material",
                    r"packing material",
                    r"Package Material",
                ],
                "end_regs": [
                    r"Air emissions",
                ],
            },
            {
                "name": "syllabus_based",
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__Greenhouse gas emission",
                ],
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (r"The Company consumes very few packing materials",),
                    "use_direct_elements": True,
                },
                "table_model": "empty",
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    "__regex__KPI A2.5",
                    "__regex__Packaging",
                ],
                "only_inject_features": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"pack(?:ag)?.*?not material",
                        r"total pack(?:ag)?ing material used",
                        r"The Group.*?setting up comprehensive pack(?:ag)?ing materials.*?system",
                        r"No significant raw material or packaging material waste",
                    ),
                },
                "table_model": "first_table",
                "table_config": {"regs": [r"pack(?:ag)?ing material"], "title_regs": [r"pack(?:ag)?ing material"]},
            },
            {
                "name": "para_match",
                "multi_elements": True,
                "enum_from_multi_element": True,
                "paragraph_pattern": (
                    # 'not involve|not produce|not applicable|non-material|not disclosed',
                    r"pack(?:ag)?ing material.*\d",
                    r"none?-material",
                ),
            },
            # DEFAULT_MODEL,
        ],
    },
]
