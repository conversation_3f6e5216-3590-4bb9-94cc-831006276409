"""
Jura4 ESG
"""

from remarkable.predictor.hkex_predictor.schemas.esg_schema.common_pattern import (
    DEFAULT_MODEL,
    NEGLECT_PAGE_HEADER_PATTERN,
)

predictor_options = [
    {
        # 社区参与相关的政策，以了解发行人经营所在社区的需求，并确保其活动考虑到社区的利益。
        # （关键词：community）
        # 一般在community investment/public welfare/Giving back to society/the community相关标题下的第一个段落描述，
        # 或第一个小标题下内容。E描述不常见。
        "path": ["B8 policies - community investment"],
        "neglect_para_missing_crude_regs": [
            r"focused on strengthening industry exchange",
            r"hopes to share cutting-edge information",
        ],  # 63993
        "models": [
            {
                "name": "syllabus_elt_v2",
                "only_inject_features": True,
                "only_before_first_chapter": True,
                "multi": True,  # file63947 章节识别错误，两个`COMMUNITY INVESTMENT`
                "break_when_table": True,  # file63947 避免table
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "inject_syllabus_features": [
                    # r'__regex__Supporting the Community',
                    r"__regex__Community Investment__regex__General Disclosure and KPIs",
                    r"__regex__Aspect B8",
                    r"__regex__COMMUNITY INVESTMENT",
                    r"__regex__OUR COMMUNITY",  # file63998
                    r"__regex__COMMUNITY VITALITY",  # file63993
                    r"__regex__Bringing Harmony to the Community",  # file63951
                    r"__regex__PUBLIC WELFARE ACTIVITIES",  # file63954
                ],
                "neglect_parent_features": [r"Materiality"],  # 63947章节识别问题
            },
            {
                "name": "row_match",
                "row_pattern": [
                    r"The Group actively performs its corporate social responsibility"
                ],  # file63951段落识别为表格
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__Aspect B8: Community Investment",
                    r"__regex__COMMUNITY INVESTMENT",
                    r"__regex__COMMUNITY CONTRIBUTION",
                ],
                "multi_elements": True,
                "only_before_first_chapter": True,
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"(?P<content>engagement|focus|key|dedicate|commit|B8\.1|community|contribut)",
                        r"(?P<content>encouraging.*?communities.)",
                    ),
                },
                "table_model": "empty",
                "neglect_parent_features": [r"Materiality"],  # 63947章节识别问题
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"(?P<content>)give back to the society",
                ),  # file63952需要排除下面的`Public Welfare Activit`规则
            },
            {
                "name": "para_match",
                "force_use_all_elements": True,
                "paragraph_pattern": (r"(?P<content>Public Welfare Activit)",),  # file 63970
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"participated in the",  # file63956
                    r"(?P<content>engagement|focus|key|dedicate|commit|B8.1|community|social responsibilit|donate)",  # social responsibilit file63952; key file63956因为donate被劈开了
                    r"(?P<content>encourag.*?(communit|society))",
                ),
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
            },
            DEFAULT_MODEL,
        ],
    },
    {
        # 公司重点在哪些领域做贡献，例如：education, environmental concerns, labour needs, health, culture, sport。
        # （关键词：engagement, focus, key, dedicate，commit）如果没有描述重点领域，可框选B8.2的内容
        "path": ["KPI B8.1 - community investment focus"],
        "models": [
            {
                "name": "after_row_match",
                "row_pattern": [r"B8\.1"],
                "multi": True,
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "para_pattern": [
                    r"engagement|focus|key|dedicate|B8.1",
                    r"community development",
                    r"community investment",
                    r"(?P<content>encouraging.*?communities)",
                ],
                "neglect_parent_features": [r"Materiality"],  # 63947章节识别问题
                "break_para_pattern": [r"Appendix"],  # file63956 段落跟index表格没有分开
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "multi": True,
                "inject_syllabus_features": [
                    r"__regex__Community Investment__regex__KPI B8\.1",
                    r"__regex__Community-in-need",  # file64000已能够正确识别英文双栏文档
                    r"__regex__Community Engagement",
                    r"__regex__SOCIAL WELFARE",
                ],
                "neglect_parent_features": [r"Materiality"],  # 63947章节识别问题
                "break_para_pattern": [r"Appendix"],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__kpi B8.1",
                    r"__regex__COMMUNITY INVESTMENT",
                ],
                "multi_elements": True,
                "break_when_table": True,
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"engagement|focus|key|dedicate|B8.1",
                        r"(?P<content>encouraging.*?communities)",
                        r"(?P<content>made donations)",
                        r"(?P<content>contribute to the (society|communiti))",
                        r"for social responsibilities purposes",
                        r"distribute gifts and coupons",
                        r"donat.*?to",
                        r"made great efforts in education",
                        r"education|environmental concerns|labour needs|health|culture|sport",
                        r"not(?:(?!\.).)*participate in(?:(?!\.).)*due to",
                        r"offer opportunities for our staff to participate in volunteering activities",
                        r"contribute to our community",
                        r"support community programmes",
                    ),
                },
                "table_model": "table_title",
                "table_config": {
                    "only_inject_features": True,
                    "feature_white_list": [
                        r"made donations",
                        r"捐贈",
                    ],
                },
                "neglect_parent_features": [r"Materiality"],  # 63947章节识别问题
                "break_para_pattern": [r"Appendix"],
            },
            {  # 如果没有描述重点领域，可框选B8.2的内容
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "multi": True,
                # "only_before_first_chapter": True,
                # "only_first": True,
                "include_shape": True,
                "inject_syllabus_features": [
                    r"__regex__Community Investment__regex__KPI B8\.2",
                    r"__regex__Community Investment",  # file63996有很多`XXX Continue`已能正确识别
                    r"__regex__Community (Service and Charity)",  # file64003 64002
                    r"__regex__Empowering the Community",  # file63948
                    r"__regex__Bringing Harmony to the Community",  # file63951
                ],
                "neglect_parent_features": [r"Materiality"],  # 63947章节识别问题
                "break_para_pattern": [r"Appendix"],
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"(?P<content>engagement|focus|key|dedicate|B8.1)",
                    # r'(?P<content>encourag.*?(communit|society))',
                ),
            },
            DEFAULT_MODEL,
        ],
    },
    {
        # 为重点领域贡献的资源（例如金钱或时间），优先提取index，再到data summary，最后正文。
        # （关键词：community，donation，prepare，provide, distribute）。
        # 表示未来计划会关注或投入community的描述，属于E（in the future, plan等），
        # 其他属于E的关键词还有stop, suspended, did not engage等
        "path": ["KPI B8.2 - resources contributed"],
        "models": [
            {
                "name": "after_row_match",
                "row_pattern": [r"B8.2"],
                "remove_section_cn_text": True,
                "include_shape": True,
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "multi_elements": True,
                "para_pattern": [
                    r"(?P<content>donat(ion|e)|prepare|provide|distribute|dedicate|B8.2)",
                    r"(?P<content>organised? (?:(?!\.).)* community (?:(?!\.).)* events)",  # file63998
                    r"(?P<content>Community public welfare)",  # file63970
                ]
                + [
                    r"\s*".join(keyword)
                    for keyword in ["donation", "donate", "prepare", "provide", "distribute", "dedicate"]
                ],  # file63956字符劈开
                "middle_rows": True,
                "answer_table_first_row_as_title": True,
                "answer_both_syllabus_and_table": True,
                "table_title": [
                    r"(?P<content>donat(ion|e)|prepare|provide|distribute|dedicate|B8.2)",
                    r"(?P<content>organised? (?:(?!\.).)* community (?:(?!\.).)* events)",  # file63998
                    r"(?P<content>Community public welfare)",  # file63970
                ]
                + [
                    r"\s*".join(keyword)
                    for keyword in ["donation", "donate", "prepare", "provide", "distribute", "dedicate"]
                ],  # file63956字符劈开,
                "break_para_pattern": ["Appendix"],
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "multi": True,
                # "only_before_first_chapter": True,
                # "only_first": True,
                "break_when_table": True,
                "include_shape": True,
                "inject_syllabus_features": [
                    r"__regex__Community Investment__regex__KPI B8\.2",
                    r"__regex__Community Investment",  # file63996有很多`XXX Continue`已能正确识别
                    r"__regex__Community (Service and Charity|Care)",  # file64003 64002
                    r"__regex__Empowering the Community",  # file63948
                    r"__regex__Bringing Harmony to the Community",  # file63951
                ],
                "neglect_parent_features": [r"Materiality"],  # 63947章节识别问题
                "break_para_pattern": [r"Appendix"],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__kpi B8.2",
                    r"__regex__COMMUNITY INVESTMENT",
                    r"__regex__COMMUNITY BUILDING",  # file63959
                    r"__regex__Community Service and Charity",
                ],
                "multi_elements": True,
                "break_when_table": True,
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "paragraph_model": "para_match",
                "include_shape": True,
                "multi": True,
                "para_config": {
                    "paragraph_pattern": (
                        r"(?P<content>donation|provide|distribute|dedicate|B8.2)",
                        r"(?P<content>made donations)",
                        r"(?P<content>cash donations)",
                        r"donat(?:(?!\.).)*?to",
                        r"for social responsibilities purposes",
                        r"distribute gifts and coupons",
                        r"participated in",
                        r"delivered to the needy",
                        r"support (the )?community",
                        r"contribution to (the )?community",
                        r"participate in",
                        r"\$[\d,]+",
                        r"contribute to our community",
                        r"support community programmes",
                        # r'(?P<content>contribution)',  # 遇到badcase 添加更详细的正则
                        # r'(?P<content>prepare)',  # 遇到badcase 添加更详细的正则
                    ),
                },
                "table_model": "table_title",
                "table_config": {
                    "only_inject_features": True,
                    "feature_white_list": [
                        r"made donations",
                        r"捐贈",
                    ],
                },
                "break_para_pattern": [r"Appendix"],
            },
            {
                "name": "para_match",
                # 'multi': True,
                "paragraph_pattern": (
                    r"\$[\d,]+",
                    r"(?P<content>community|donation|prepare|provide|distribute|B8\.2)",
                    r"(?P<content>contribution)",
                    r"(?P<content>prepare|provide|distribute)",
                    r"(?P<content>cash donations)",
                ),
            },
            DEFAULT_MODEL,
        ],
    },
]
