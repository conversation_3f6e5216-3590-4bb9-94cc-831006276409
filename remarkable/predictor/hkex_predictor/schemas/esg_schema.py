"""
Jura4 ESG
"""

from remarkable.common.common_pattern import EX<PERSON><PERSON><PERSON>_PATTERN as COMMON_EXPLAIN_PATTERN

EXPLAIN_PATTERN = tuple(COMMON_EXPLAIN_PATTERN)

DEFAULT_MODEL = {
    "name": "score_filter",
    "threshold": 0.8,
}

DIRECTORY_START = r"^\d+\.\d+.(\d+)?"  # 目录中的段落

NEGLECT_PAGE_HEADER_PATTERN = [
    r"^ENVIRONMENTAL.*?SOCIAL.*?AND.*?GOVERNANCE",
    r"^環境.*?社會及管治報告",
    r"^ENVIRONMENTAL.*?SOCIAL.*?AND$",
    r"^GOVERNANCE.*?REPORT$",
    r"^SOCIAL\s*?AND\s*?GOVERNANCE\s*?REPORT$",  # esg换行
    r"^environment.*?social\s*?and$",  # esg换行
    r"^Report \d+$",
    r"^ENVIRONMENTAL,$",
    r"^Social$",
    r"\(?CONTINUED\)?$",
    r"\(?CONT’D\)?$",
]

B6_CHAPTER_PATTERN = [
    r"__regex__Product\s?Responsibility",
    r"__regex__B6",
    r"__regex__Responsible\s?Services",
]

COMPLY_LAW_PATTERN = (
    r"(no|any) non- ?compliance",
    r"(no|any) (material|confirmed) non- ?compliance",
    r"(no|any) (\w+\s){0,3}non- ?compliance",
    r"not aware of any.*?(non- ?compliance|violation|breach)",
    r"abides",
    r"strictly (compl(y|ie)|observ)",
    r"ha(s|ve) (compl(y|ie)|observ)",
    r"strictly (abide|adheres?)",
    r"(strict|in) (compliance|conformity) with",
    r"keep abreast with|adheres to and complies with",
    r"no violation",
    r"not record violation",
    r"not violated?",
    r"(?<!procedures are )strictly follow",
    r"(comply|compl(ie(d|s)?|y(ing)?|iance)) with",
    r"not discover any material violation",
    r"accordance with (?:(?!\.).)*law",
    r"not received (any )?reports? of violations?",
    r"not encounter any cases of.*?laws and regulations",
)

LAW_COMMON_MODEL = {
    "name": "para_match",
    "paragraph_pattern": (r"(no|any) (\w+\s){0,3}non-compliance",),
}

health_pattern = [
    r"Health and Care",
    r"Health and Safety",
]
predictor_options = [
    {
        # 一般位于ESG report 前面几页，常在board statement或governance  structure小标题下面，
        # 提取涉及“board对整个公司ESG相关的措施或report负责的描述-包括制定，实施，检查，复核，监督，批准，风险评估”等，
        # 主要突出有responsibility或role
        # 常包含关键词
        # oversee/oversight/overall/full/ultimate/solely +
        # responsibility/accountability/leadership/direction/charge of或responsible等等
        # 有时会在多个段落出现相同的描述，可同时提取。
        "path": ["MDR 13 i - board oversight"],
        "models": [
            {
                "name": "para_match",
                "syllabus_regs": [r"Governance", r"STATEMENT", "board"],
                "paragraph_pattern": (
                    r"Board.*?respons.*?overseeing",
                    r"overall.*?responsib.*?strateg",
                    r"board(?:(?!\.).)*(oversee|responsib)",
                ),
                "multi_elements": True,
            },
            {
                "name": "syllabus_elt_v2",
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__esg governance structure",
                ],
            },
            {
                "name": "syllabus_elt_v2",
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__governance structure",
                ],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__Social.*?Governance.*?Report",
                    r"__regex__MESSAGE FROM THE BOARD",
                ],
                "multi_elements": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"(?P<content>(oversee|oversight|overall|full|ultimate|solely).*?("
                        r"responsibility|accountability|leadership|direction|responsible|governance|committee))",
                        r"(?P<content>(responsibility|accountability|leadership|direction|responsible|governance|committee).*?(integrity))",
                    ),
                },
                "table_model": "empty",
            },
            {  # 当章节识别有误时
                "name": "para_match",
                "paragraph_pattern": (r"board(?:(?!\.).)*(oversee|oversight|responsib)",),
            },
            DEFAULT_MODEL,
        ],
    },
    {
        # 涉及到“board自己,或者通过建立各种committee/working team, 或授权senior management 来评估ESG相关的风险，
        # 制定相关的政策，监督政策的实施，评估政策的有效性”的描述。
        # 较为经常的描述为“board 通过ESG相关的committee或其他组织来监管ESG相关事务”
        # 可能会涉及的关键词有：ESG Committee, ESG, committee（各种各样的committee）
        # working team，working group，establish, set up，delegate, authorise, formulated, strategy，identify，evaluate，priority, approach, strategy等等
        "path": ["MDR 13 ii - board management approach"],
        "models": [
            {  # 精确的段落匹配
                "name": "para_match",
                "paragraph_pattern": (
                    r"(ESG (Committee|taskforce)|working (team|group))(?:(?!\.).)*((?<!ESG )report|establish|identif(?:(?!\.).)*risk)",  # file63962 章节识别错误
                ),
            },
            {
                "name": "para_match",
                "strict_limit": True,
                "syllabus_regs": [
                    r"board statement",
                    r"Statement of( the)? Board",
                    r"governance",
                    r"approach|strategy",
                ],
                "paragraph_pattern": (
                    r"set up.*?ESG working group",
                    r"Group.*?establish.*?ESG",
                    r"Board.*?strategy(?:(?!\.).)*ESG",
                    r"establish.*?working group",
                    r"to (propose|oversee|review|monitor)",  # file64001
                    r"(review|propose)(?:(?!\.).)*(ESG (Committee|taskforce)|working (team|group))",  # file63988
                    r"(ESG (Committee|taskforce)|working (team|group))(?:(?!\.).)*((?<!ESG )report|establish|identif(?:(?!\.).)*risk)",  # file63988 file63962
                    r"Board(?:(?!\.).)*identified risks",  # file63988
                    r"The principal duties of the Board",
                ),
                "multi_elements": True,
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                # "only_inject_features": True,
                "multi": True,
                "inject_syllabus_features": [
                    r"__regex__ESG Working Group",
                    r"__regex__ESG.*?(approach|strategy)",
                ],
            },
            {  # syllabus识别有误时
                "name": "para_match",
                "multi_elements": True,
                "paragraph_pattern": (
                    r"(ESG (Committee|taskforce)|working (team|group))(?:(?!\.).)*(report|establish)",
                ),
            },
            DEFAULT_MODEL,
        ],
    },
    {
        # how the board reviews progress made against ESG-related goals and targets with an explanation of how they relate to the issuer’s businesses.
        # 董事会如何审查与 ESG 相关的目标所取得的进展，并解释它们与发行人业务的关系
        # 一般位于ESG report 前面几页，常在board statement或governance structure小标题下面，提取涉及到review ESG问题的描述。
        # 优先提取涉及到“ESG相关的target或goal的review”或”progress review”的描述。
        # 定位：一般位于ESG report 前面几页。有些描述清晰的文档，在段落标题会有target或goal等关键词。
        # i.若ESG report中无board statement,就可以去governance或者其他含有关键词的段落;
        # ii.若ESG report中同时存在board statement、governance和其他含有关键词的段落，可优先框选board statement中的相关内容；
        # iii.若ESG无board statement、governance相关内容时，可通过关键词进行查找，框选对应内容；
        # iv.若ESG无board statement、governance相关内容时且通过关键词也未查找到相关内容时，则为ND;
        # 可能会涉及的关键词有：report, briefing, meet, frequency，monitor, review等等，较多会有monitor, review，assess
        "path": ["MDR 13 iii - board progress review"],
        "models": [
            {
                "name": "syllabus_elt_v2",
                "only_inject_features": True,
                "include_title": True,
                "inject_syllabus_features": [
                    r"__regex__Review of Progress against ESG-related Goals and Targets",
                ],
            },
            {
                "name": "para_match",
                "multi_elements": True,
                "strict_limit": True,
                "syllabus_regs": [
                    r"board statement",
                    r"governance",
                    r"target|goal",
                ],
                "paragraph_pattern": (
                    r"review.*esg.*target",
                    r"corporate governance report",
                    r"ESG.*?review.*?report",
                    r"Report.*progress.*?ESG",
                    r"Board(?:(?!\.).)*(determin|integrat|monitor|evaluate)",
                    r"(?:monitor|evaluate|review|assess)(?:(?!\.).)*ESG performance",  # file63974
                ),
            },
            {  # syllabus识别有误时
                "name": "para_match",
                "multi_elements": True,
                "paragraph_pattern": (
                    r"Board(?:(?!\.).)*(integrat|monitor|evaluate|review)\s",
                    # r'Board(?:(?!\.).)*determin'
                ),
            },
            {
                # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/1032#note_251173
                "name": "syllabus_elt_v2",
                "ignore_missing_crude_answer": True,
                "only_inject_features": True,
                "include_title": True,
                "multi": True,
                "inject_syllabus_features": [
                    r"MATERIALITY ASSESSMENT",
                    r"STRATEGIC PRIORITIES",
                    r"FOUR-PHASE PROCESS",
                ],
            },
            DEFAULT_MODEL,
        ],
    },
    {
        # MDR14-materiality:重要的 ESG要素的确认过程和选择标准；如有stakeholder参与，要有已确认stakeholder的描述和其参与的过程和结果
        # 位置：
        #
        # i.一般位置在ESG报告的前几页，通常在reporting principle/standard/framework标题下方，有明确的标题materiality,直接框选标题及内容，枚举选择C
        #
        # ii.标注materiality+ stakeholder engagement相关的所有内容（内容较多，会多出存在），枚举选择C
        #
        #
        # 以上内容是需要同时标注，如果存在多处需要都框选
        # 常见关键词：stakeholder engagement, stakeholder, material analysis, materiality, materiality assessment，
        # materiality matrix,communication with the stakeholder
        "path": ["MDR 14 part 1 - materiality application"],
        "pick_answer_strategy": "all",
        "models": [
            {
                "name": "row_match",
                "row_pattern": [r"materiality"],
            },
            {
                "name": "para_match",
                "force_use_all_elements": True,
                "paragraph_pattern": (
                    r"the Group has conducted a materiality assessment.*?(esg|social)",
                    r"the materiality assessment based on",
                ),
            },
            {
                "name": "esg_materiality",
                "paragraph_pattern": (
                    r"Materiality.*?Principle.*?esg",
                    r"Materiality[–:：].*?(esg|materiality matrix)",
                    r"Materiality[–:：].*?confirmed by the management",
                    r"Materiality.*?[-:: ]",
                    r"^•\s?Materiality\s?[–:：]",
                    r"^\d.*?Materiality[–:：].*?(esg|social)",
                ),
            },
            {
                "name": "syllabus_elt_v2",
                "multi": True,
                "only_inject_features": True,
                "include_title": True,
                "include_shape": True,
                "ignore_pattern": [
                    r"ANTI-EPIDEMIC HYGIENE MEASURES",
                ],
                "inject_syllabus_features": [
                    r"__regex__WITH STAKEHOLDERS?",
                    r"__regex__STAKEHOLDER ENGAGEMENT AND MATERIALITY ASSESSMENT",
                    r"__regex__MATERIALITY ASSESSMENT",
                    r"__regex__STAKEHOLDER ENGAGEMENT",
                    r"__regex__STAKEHOLDER identification",
                    r"__regex__MATERIAL ANALYSIS",
                    r"__regex__MATERIALITY",
                    r"__regex__MATERIALITY MATRIX",
                    r"__regex__STAKEHOLDERS ENGAGEMENT",
                    r"__regex__stakeholder.*?communication",
                    r"__regex__our stakeholders",
                    r"__regex__material ESG topics",
                    r"__regex__Communication with the stakeholders",
                    r"__regex__Stakeholders.*?Engagement",
                    r"__regex__Stakeholder Engagement Approach",
                    r"__regex__report.*?(principles?|standard|framework|rules?)__regex__importance",
                    r"__regex__List of important topics",
                    r"__regex__Stakeholder Communication",
                    r"__regex__Stakeholders.*?Involvement",
                ],
            },
            {
                "name": "table_title",
                "only_inject_features": True,
                "feature_white_list": [
                    r"materiality assessment",
                ],
            },
            {
                "name": "special_cells",
                "whole_table": True,
                "cell_pattern": (r"Relevant ESG issues to the Group",),
            },
        ],
    },
    {
        # 量化：披露排放物或能源消耗相关的标准、方法、假设、计算工具以及转换因数的来源。
        #  定位:
        #  1.一般是在reporting principle/standard标题下的相关内容；
        #  2.需要框选涉及描述的整个段落及其小标题;
        #  3.只需要A rule中的量化，B rule中的不算。
        # 注意：当有明确的quantitative相关内容时，直接框选相关principal；
        # 当没有quantitative相关内容时，去找各个指标如何计算的内容。
        # 关键词：quantitative；methodology(ies)；assumption(s)； conversion factor(s)； calculation
        "path": ["MDR 14 part 2 - quantitative application"],
        "models": [
            {
                "name": "special_cells",
                "cell_pattern": [
                    r"^quanti(?:tative|fication)[\s\w]{5,}",  # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/939#note_233032
                    r"practice the principle of quanti(?:tative|fication)",
                ],
            },
            {
                "name": "row_match",
                "ignore_index_table": True,
                "row_pattern": [r"^quanti(?:tative?|fication)"],
            },
            {
                "name": "syllabus_elt_v2",  # 章节
                "only_inject_features": True,
                "include_title": True,
                "inject_syllabus_features": [
                    r"__regex__report.*?(principles?|standards?|frameworks?|rules?)__regex__quanti(?:tative|fication)",
                    r"__regex__BASIS OF PREPARATION__regex__quanti(?:tative|fication)",
                ],
                "break_para_pattern": [r"balance", r"consistency"],
            },
            {
                "name": "para_match",
                "strict_limit": True,
                "syllabus_regs": [
                    r"report.*?(principles?|standards?|frameworks?|rules?|guides?)",
                    r"standards?",
                    r"^environment",
                    r"BASIS OF PREPARATION",
                ],
                "neglect_syllabus_regs": [
                    r"Response to Product Complaint",
                    r"Ensuring Stable Supply of Gas",
                    r"RESPONSIBLE GOVERNANCE",
                    r"Green Procurement Process",
                    r"Internally Carried out Anti-corruption",
                ],
                "paragraph_pattern": (r"quanti(?:tative|fication)”?(?:\s*:?)",),
                "neglect_pattern": (
                    # r'^•Quantitative Indicators:',
                ),
            },
            {
                "name": "para_match",
                "strict_limit": True,
                "multi_elements": True,
                "neglect_syllabus_regs": [
                    r"Response to Product Complaint",
                    r"Ensuring Stable Supply of Gas",
                    r"RESPONSIBLE GOVERNANCE",
                    r"Green Procurement Process",
                    r"Internally Carried out Anti-corruption",
                ],
                "syllabus_regs": [
                    r"environment",
                    r"report.*?(principle|standard|framework|rules?)",
                ],
                "paragraph_pattern": (
                    # r'quantitative|methodology(ies)?|assumptions?|conversion factors?',
                    # r're-calculates|calculated|recalculated|calculation|calculating',
                    r"GHG emissions data.*?based on.*?but not limited to",
                ),
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__MATERIALITY AND REPORTING BOUNDARY",
                ],
                "only_inject_features": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"quantitative|methodology(ies)?|assumptions?|conversion factors?",
                        r"re-calculates|calculated|recalculated|calculation|calculating",
                    ),
                },
                "table_model": "empty",
                "break_para_pattern": [r"balance", r"consistency"],
            },
            {
                "name": "table_footnote",
                "multi_elements": True,
                "force_use_all_tables": True,
                "cell_regs": [
                    r"Environmental$",
                    r"emissions",
                    r"energy consumption",
                    r"waste consumption",
                    r"water consumption",
                    r"hazardous wastes",
                ],
                "title_patterns": [
                    r"Environmental",
                ],
                "neglect_title_patterns": [
                    r"ESG issues",
                    r"Environmental Emission Data",
                ],
            },
            {
                "name": "para_match",
                "multi_elements": True,
                "paragraph_pattern": (
                    # r'quanti(?:tative|fication)',
                    # r'|methodology(ies)?|assumptions?|conversion factors?',
                    # r're-calculates|calculated|recalculated|calculation|calculating',
                    r"GHG emissions data.*?based on.*?but not limited to",
                    r"^(viii?\.|x\.|xiv\.)",
                    r"^quanti(?:tative|fication):",
                ),
            },
            # {
            #     "name": "score_filter",
            #     "threshold": 0.618,
            # },
            {
                "name": "esg_quantitative",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__emission|consumption|Energy",
                ],
                # 'multi_elements': True, # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/997#note_243309
                "multi": True,
                "paragraph_model": "empty",
                "table_model": "special_cells",
                "table_config": {
                    "whole_table": True,
                    "cell_pattern": (
                        r"Energy Consumption",
                        r"Emissions",
                    ),
                },
            },
        ],
    },
    {
        # MDR14-consistency:发行人应在 ESG 报告中披露所用方法或 KPI 的变化，或影响对比的相关因素。
        #  定位:
        #  1.一般是在reporting principle/standard标题下的相关内容；
        #  2.需要框选涉及描述的整个段落及其小标题;
        #  3.A\B rule中的所有consistency都算。
        # 注意：当有明确的consistency相关内容时，直接框选相关principal；
        # 当没有consistency相关内容时，去找各个指标两年的数据列，框选两年数据作为对比或consistent等字眼。
        # 关键词：consistency；methodology(ies)；assumption(s)；conversion factor(s)
        "path": ["MDR 14 part 3 - consistency application"],
        "models": [
            {
                "name": "para_match",
                "paragraph_pattern": (r"^.?(?<!in)consistency\s?[:-•]",),
                "syllabus_regs": [
                    r"report.*?(principles?|standards?|frameworks?|rules|guides?)",
                    r"standards?",
                    r"BASIS OF PREPARATIONS?",
                    r"Consistency.*?Principle",
                ],
            },
            {
                "name": "para_match",
                "paragraph_pattern": (r"(?P<content>^.?(?<!in)consistency\s?[:-•].*?)“Balance",),
                "content_pattern": True,
            },
            {
                "name": "para_match",
                "paragraph_pattern": (r"^.?(?<!in)consistency\s?[:-•]",),
            },
            # {"name": "para_match", "use_all_elements": True, "paragraph_pattern": (r'.*?consistency\s?[:-• ]',)},
            {
                "name": "row_match",
                "neglect_syllabus_regs": [r"Independent Assurance Statement", r"Reporting Principles"],
                "row_pattern": [r"(?<!in)consistency"],
            },
            {"name": "col_match", "col_pattern": [r"(?<!in)consistency"]},
            {
                "name": "special_cells",
                "cell_pattern": [r"^consistency[\s\w]{5,}"],
            },
            # {  # 特征在第一行的表格
            #     "name": "table_row",
            #     "feature_from": "self",
            #     "parse_by": "col",
            #     "neglect_syllabus_regs": [r'Independent Assurance Statement'],
            #     "feature_white_list": [
            #         r'__regex__consistency',
            #     ],
            # },
            {
                "name": "row_match",
                "ignore_index_table": True,
                "row_pattern": [r"^consistency"],
            },
            {
                "name": "special_cells",
                "cell_pattern": [r"consistency.*?methods"],
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__report.*?(principles?|standards?|frameworks?|rules?|guides?)__regex__consistency",
                ],
            },
            {
                "name": "syllabus_based",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                # "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__reporting principle",
                    r"__regex__About this Report",
                    r"__regex__MATERIALITY",  # MATERIALITY AND REPORTING BOUNDARY
                ],
                "multi_elements": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"consistent methodology",
                        r"used consistent reporting",
                        r"adopt consistent reporting",
                        r"consistency",
                        r"(?P<content>aligns with)",
                        r"adopt consistent report",
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "esg_consistency",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                # "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__reporting principle",
                    r"__regex__About this Report",
                    r"__regex__MATERIALITY",  # MATERIALITY AND REPORTING BOUNDARY
                ],
                "multi_elements": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"consistent methodology",
                        r"used consistent reporting",
                        r"adopt consistent reporting",
                        r"(?P<content>consistency|methodology(ies)?|assumptions?|conversion factors?)",
                        r"(?P<content>aligns with)",
                        r"adopt consistent report",
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"consistent methodology",
                    r"used consistent reporting",
                    r"adopt consistent reporting",
                    r"(?P<content>consistency|methodology(ies)?|assumptions?|conversion factors?)",
                    r"(?P<content>aligns with)",
                ),
                "syllabus_regs": [
                    r"reporting principle",
                    r"standard",
                ],
            },
            # {
            #     "name": "score_filter",
            #     'multi_elements': True,
            #     'aim_types': ['TABLE'],
            #     'threshold': 0.618,
            # },
        ],
    },
    {
        # 说明ESG 报告的报告界限,并描述用于识别 ESG 报告中包含哪些实体或业务的过程。 范围发生变更的，发行人应当说明差异及变更原因
        # 位置：
        #
        # i.一般位置在ESG报告的前几页，通常在scope and reporting period/reporting scope/reporting boundary/reporting scope and boundaries标题下，主要描述report包含的xxx operation/business，或在xxx地方的operation,或xxx entities或其包含的xxx子公司，直接框选段落，枚举C;
        #
        # ii.当ESG报告中有包含scope的标题，下方段落除boundary内容外还披露相关的准则或者是ESG报告的报告期则只需要框选boundary相关的内容，即report包含的xxx operation/business，xxx地方，xxxentitites
        #
        # iii.当无明确标题时或关键词也不明显但是披露了公司及其包含的子公司的主营相关内容也属于此规则，枚举应判断为C
        # 常见关键词：boundary,scope,cover,focus,engaged in
        "path": ["MDR 15 - reporting boundary"],
        "models": [
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"^Scope of (organization|report)",
                    r"We regularly review the scope of the ESG Report",
                    r"ESG Report includes",
                    r"has two major operating segments",
                    r"information contained herein.* sourced.*",  # file63861
                    r"data used.*report.*sourced.*",  # file63861
                    r"focusing on providing environmental hygiene services",  # file64593
                    r"This report covers",  # file63963
                    r"環境有重要關連的下列業務",  # 65870
                ),
                "neglect_pattern": (r"major properties of the Group",),
            },
            # 优先提取指定章节的整个段落
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "break_para_pattern": [
                    r"^Reporting Principles",
                ],
                "inject_syllabus_features": [
                    r"__regex__ESG Report Scope and Boundary",
                    r"__regex__Report(ing)? Scope and (Boundary|Boundaries)",
                    r"__regex__REPORT(ING)? (PERIOD AND |Organizational )?SCOPE",  # REPORTING PERIOD AND SCOPE file64641 | REPORT Organizational SCOPE file63932
                    r"__regex__REPORT(ING)? BOUNDAR(Y|ies)",  # Reporting Boundaries file64443   Reporting Boundaries and Scope file64592
                    r"__regex__Scope of (THE |THIS )?(ESG )?Report(ing)?",
                    r"__regex__^SCOPE:?$",  # file 63665 64554
                    r"__regex__\d+\.\sscope$",  # file64554
                    r"__regex__^報告範圍",  # file 63665
                    r"__regex__^PREPARATION BASIS AND SCOPE",  # file 64638
                    r"__regex__ABOUT (THE |THIS )?REPORT__regex__coverage",  # file 64593
                    r"__regex__Scope and Boundar",  # file 64593
                    r"__regex__Scope and Reporting Period",  # file 64593
                    r"__regex__Scope of this ESG Report",  # file 64593
                    # r'__regex__Concept and principle of the report',  # file 64593
                ],
            },
            {  # file64546 同时存在about this report 和about this group,优先取about this report
                "name": "syllabus_based",
                # "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__Concept and principle of the report",  # file64629
                    r"__regex__About (this|the) Report",
                ],
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"report(?:(?!(period|\.)).)*?(focus|operati(ng|ons?)|businesses|cover)(?!s? the period)",
                        # covers: file 63622  (?! the period) file83785  report(?:(?!(period)).) file63825
                        r"as.*environmental disclosure",  # file64629
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "syllabus_based",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__AND REPORT(ING)? BOUNDARY",
                    r"__regex__REPORTING BOUNDARIES( AND PRINCIPLES)?",
                    r"__regex__(REPORT(ING)? )?(SCOPE |PERIOD )AND (PERIOD|BOUNDARY|REPORTING)",
                    # REPORTING SCOPE AND BOUNDARY file63624   SCOPE AND BOUNDARY file63895  SCOPE AND REPORTING file63622
                    r"__regex__SCOPE OF (THE|THIS) REPORT",  # SCOPE OF THE REPORT file63637 63661
                    r"__regex__About (this|the) (Report|group|company)",
                    r"__regex__Report Description",
                    r"__regex__報告範圍",
                ],
                "multi_elements": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"(the )?(?:(?!\.).)*scope of",  # file63692
                        r"(the|this) ESG report (cover|focus)",
                        r"(the|this) ESG report.*summarises?.*cover(s|ing).*operati(ng|ons?)",
                        r"(the|this) ESG report.*to cover.*ESG ",  # .*to cover.*ESG: file 2
                        r"report contains details.*?social responsibilities",
                        r"report(?:(?!(period|\.)).)*?(focus|operati(ng|ons?)|businesses|cover)(?!s? the period)",
                        # covers: file 63622  (?! the period) file83785  report(?:(?!(period)).) file63825
                        r"^(?!.*Materiality)focus|engag(?:e[ds]|ing) in",  # ^(?!.*Materiality): file 63600
                        r"focusing on",  # file 64593
                        r"principal activities.*?which include",
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "row_match",
                "ignore_index_table": True,
                "first_cell": True,
                "row_pattern": [
                    r"report(ing)? scope",  # file63928
                ],
            },
            {
                "name": "special_cells",
                "cell_pattern": [
                    r"Scope of (organization|report)",
                    r"Report.*?encompasses.*?subsidiaries",
                    r"(?:This |the )?report covers? (?!the period)",  # (?!the period) file63807
                ],
            },
            {
                "name": "para_match",
                "force_use_all_elements": True,
                "paragraph_pattern": (
                    r"(?P<content>report(?:(?!(\.|period)).)*?(boundary|scope|cover|focus|operations|businesses|highlight|discuss))",
                    # (?:(?!\.).)*? file63734   (?:(?!(\.|period)).)*? file63917
                    r"(?P<content>report contains details.*?social responsibilities)",
                    r"(the )?scope of",
                    r"the ESG report covers",
                    r"report contains details.*?social responsibilities",
                    r"^(?:(?!In the investment).)*(engaged in)",  # ^(?:(?!In the investment).)* file63807
                ),
                "neglect_pattern": (
                    r"a lasting standstill for the whole of the reporting period",
                    r"put together a new team",  # file197593
                    r"covering the period",  # file63654
                ),
            },
            # {
            #     "name": "score_filter",
            #     "threshold": 0.07,  # file63844无规则答案且分数过低
            # },
        ],
    },
    {
        # A1-POLICY 常见的提取内容、位置及判断规则
        # 提取内容：
        # 涉及emission政策的段落，段落中常包含 emission, environment, policy, management， GHG, pollution, carbon dioxide等词语。
        # 可提取包含emission标题下的全部内容。在有index的情况下，去index指明的位置提取会更准确
        # E的判断：
        # 【简单情况】：not disclosed, not available, NA, 【复杂情况】：not involve，或not engage，或not generate或no generation或not produce 大气污染排放物，not+ impact且没有任何政策相关的描述,或due to+nature
        # E的情况较少，大概不到10%的比例
        # Y的判断： 有：emission相关的policy或management或measure或reduce或control等措施的描述。
        # 【要注意】：当有“not involve，或not engage，或not generate或no generation或not produce 大气污染排放物”的描述，但是同时又有政策措施相关的描述，仍判断Y而非E
        "path": ["A1 policies - emissions"],
        "models": [
            {
                "name": "para_match",
                "paragraph_pattern": (r"KPI A1\.1 to KPI A1\.6 are not applicable",),
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "multi": True,
                "only_inject_features": True,
                "only_first": True,  # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/946#note_232893
                "inject_syllabus_features": [
                    r"__regex__ENVIRONMENT__regex__Emissions__regex__^(?:(?!Table).)*((Air|gas) emissions|ghg|green\s?house|types of Emissions)",
                    r"__regex__ENVIRONMENT__regex__OPERATION__regex__Emission",
                    # r'__regex__ENVIRONMENT__regex__Emission',
                ],
            },
            {
                "name": "twice_para_match",
                "multi_elements": True,
                "use_all_elements": False,  # 基于初步定位结果再次过滤，避免结果范围过大
                "syllabus_regs": [],
                "neglect_syllabus_regs": [r"climate change"],
                "paragraph_pattern": (  # 污染物，名词
                    r"GHG|greenhouse gas|(?<![“\"]peak )carbon dioxide|methane|(nitrous|nitrogen) oxides?|(air|carbon) emission|particulate matters|NOX|SO2|sulfur dioxide|CO2|CH4|N2O|O3|ozone",
                    r"(GHG|greenhouse gas|direct|pollutant) emissions",
                    r"embodied carbon",
                    r"carbon footprint|waste handling|air pollutant|air quality",
                ),
                "neglect_pattern": (),
                "second_pattern": (  # 对污染物的处理、政策，动词
                    r"not involve|not engage|not disclosed|not generate|no generation|not produce|not quantify",
                    r"include",  # many files
                    r"measures? to reduce",  # 63997
                    r"devis(?:(?!\.).)*plan",  # 63997
                    r"within our operations.",
                    r"operations are",  # 63998
                    r"implement(?:(?!\.).)*(actions)|(system)",  # 63988
                    r"carry out(?:(?!\.).)*activities",  # 63981
                    r"carbon footprint|waste handling|air pollutant|air quality",
                ),
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "multi": True,
                "only_inject_features": True,
                # 'break_when_table': True,
                # "only_before_first_chapter": True,  # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/803#note_212766
                "inject_syllabus_features": [
                    r"__regex__Emissions? Management",
                    r"__regex__ENVIRONMENT__regex__^(?:(?!Table).)*((Air|gas) emissions|ghg|green\s?house|types? of Emissions)",
                    r"__regex__ENVIRONMENTAL.*?REPORT__regex__ENVIRONMENTAL__regex__Environmental Policies",
                    r"__regex__General Disclosure and KPIs",
                    # r'__regex__A1.*?Emissions',
                    r"__regex__^(?:(?!Table).)*Air.*?Emissions",
                    r"__regex__^(?:(?!Table).)*gas.*?Emission",
                    r"__regex__^(?:(?!Table).)*type of emissions",
                    r"__regex__Emissions Contro",  # file63976章节识别错误
                    r"__regex__(?<!Resource Consumption and )ghg.*?Emission",
                    r"__regex__carbon neutrality",
                    r"__regex__Greenhouse Gas (GHG) and Energy Consumption Performance",  # file64001
                    r"__regex__Greenhouse gas management",
                    r"__regex__Carbon Emission",
                    r"__regex__GHG Emission",
                    r"__regex__^Emission$",
                ],
                "neglect_features": [
                    r"target",
                    r"reduction",
                    r"Publishing the Action Plan for Implementing Peak Carbon Dioxide Emissions and Carbon Neutrality",  # 63977
                ],
                "neglect_parent_features": [r"climate change"],
            },
            {
                "name": "para_match",
                "multi_elements": True,
                # "force_use_all_elements": True,
                # "enum_from_multi_element": True,
                "paragraph_pattern": (
                    r"(?P<content>(not involve|not engage|not disclosed|not generate|no generation|not produce|not quantify)(?:(?!\.).)*(NOX|SO2|CO2|CH4|N2O|O3|ozone))",
                    r"(?P<content>not own.*?emissions)",
                    r"(?P<content>GHG|(?<![“\"]peak )carbon dioxide|CO2|methane|CH4|nitrous oxide|N2O|ozone|O3|(air|carbon) emission|particulate matters)",
                    r"(?P<content>more environment-friendly fuel)",
                    r"^(?:(?!Note).)*(?P<content>greenhouse gas emissions? (?!generated by purchased electricity))",
                ),
                "neglect_pattern": (
                    # r'reduction',
                    r"carbon emission rights derivatives",  # 碳排放权衍生品file63977
                    r"was successfully issued on the Shenzhen Stock Exchange",  # 63977
                ),
            },
            DEFAULT_MODEL,
        ],
    },
    {
        #    1.**优先根据index去定位相关内容的位置去定位****是否遵守**的关键词框选其段落
        #    2.当不存在index的时候，先定位其**是否遵守**的关键词，在根据周围的描述判定属于哪一条规则的law
        #    （如：周围关键词为emission, pollutions, greenhouse gases, GHG等，则是A1law）
        #  涉及“明确表明没有相关政策和法律法规适用于公司”的描述。 比如no laws and regulations。E的情况一般较少出现
        "path": ["A1 law compliance - emissions"],
        "models": [
            {
                "name": "row_match",
                "ignore_index_table": True,
                "row_pattern": [r"^Emissions"],
            },
            {  # file63957
                "name": "syllabus_based",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "break_para_pattern": [r"A1\.1"],
                "inject_syllabus_features": [
                    r"__regex__A1(\.|\s+|$)",
                ],
                "paragraph_model": "para_match",
                "para_config": {"paragraph_pattern": COMPLY_LAW_PATTERN},
                "table_model": "empty",
            },
            {
                "name": "twice_row_match",
                "first_cell": False,
                "merge_valid_rows": False,
                "ignore_index_table": True,
                "row_pattern": COMPLY_LAW_PATTERN,
                "neglect_row_pattern": [
                    r"adopted corresponding measures",  # file63959
                    r"Product Health and Safety",
                    r"Labou?r Standards?",
                    r"B[1-8]|A2|A3",
                ],
                "second_pattern": [
                    r"A1(\s+|$|[a-zA-Z])",  # file63997披露在index表格中
                    r"Emission",  # file63983披露在index表格中
                ],
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"KPI A1\.1 to KPI A1\.6 are not applicable",
                    r"no relevant law",
                ),
                "neglect_row_pattern": [
                    r"adopted corresponding measures",  # file63959
                    r"Product Health and Safety",
                    r"Labou?r Standards?",
                    r"B[1-8]|A2|A3",
                ],
            },
            {
                "name": "twice_para_match",
                "multi_elements": True,
                "syllabus_regs": [],
                "neglect_row_pattern": [
                    r"adopted corresponding measures",  # file63959
                    r"Product Health and Safety",
                    r"Labou?r Standards?",
                    r"B[1-8]|A2|A3",
                ],
                "paragraph_pattern": COMPLY_LAW_PATTERN,
                "second_pattern": (
                    r"emission",
                    r"pollution",
                    r"greenhouse gas(es)?",
                    r"GHG",
                    r"Air Pollution Control Ordinance, ",
                    r"low-carbon",
                    r"environmental management|(environment|natural) resources?",  # file63959
                ),
            },
            {
                "name": "syllabus_based",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__ENVIRONMENTAL__regex__A1.*?Emissions",
                    r"__regex__Exhaust Management",
                ],
                "multi_elements": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (r"(?P<content>law|ordinance|regulation|legislation)",),
                },
                "table_model": "empty",
            },
            {
                "name": "para_match",
                "neglect_row_pattern": [
                    r"adopted corresponding measures",  # file63959
                    r"Product Health and Safety",
                    r"Labou?r Standards?",
                    r"B[1-8]|A2|A3",
                ],
                "paragraph_pattern": (
                    # r'(?P<content>law|ordinance|regulation|legislation)',
                    r"the Group did not identify any material non-compliance related to emissions",
                    r"complies with all applicable environmental regulations",
                ),
            },
            # {
            #     "name": "kmeans_classification",
            # },
        ],
    },
    {
        # 需要定位针对air emission排放类型和对应排放数据的表格段落，常见类型有NOx,SOx,PM等.
        # 出现位置：1.报告末尾的performance data summary,KPI summary,performance table,INDEX
        #          2.environment章节的末尾environmental data
        #          3.标题A1\EMISSION\A1.1\emission category下面
        #          4.如果没有直接明确给出NOx,SOx,PM等emission类型，Scope 1,Scope 2及Scope 3都算是types of emissions
        "path": ["KPI A1.1 - emission type and data"],
        "models": [
            {
                "name": "after_row_match",
                "row_pattern": [r"A1.1"],
                "middle_rows": True,
                "start_regs": [
                    r"Greenhouse Gas Emis",
                    r"GHG emissions",
                ],
                "end_regs": [r"Energy consumption"],
                "just_a_para": True,  # for special cases 61008
                "para_pattern": [
                    r"^溫室氣體排放概覽$",
                ],
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"KPI A1\.1 to KPI A1\.6 are not applicable",
                    r"not directly produce",
                ),
            },
            {
                "name": "syllabus_based",
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__Pollutant Discharge Management System and Goals",
                ],
                "paragraph_model": "empty",
                "para_config": {},
                "table_model": "row_match",
                "table_config": {
                    "first_cell": True,
                    "row_pattern": [
                        r"Emissions of (VOCs|CO2)",
                    ],
                },
            },
            {
                "name": "esg_row_match",
                "multi": True,
                # "first_cell": False,
                "row_pattern": [
                    r"NOx|SOx|Particles|Nitrogen Oxide|Sulphur Oxide",
                    r"Particular|Particulate",  # r'Particular matter', r'PM',
                    r"Air pollutants",
                    r"Air emissions",
                    r"oxides|particles",
                    r"^PM$",  # r'Particular matter', r'PM',
                    r"PM.*?(物|微粒)",  # r'Particular matter', r'PM',
                    r"Sulphur dioxide",  # r'Particular matter', r'PM',
                    r"–\s?(office|projects)",  # r'Particular matter', r'PM',
                    r"NO\s?氮氧",
                    r"SO\s?硫氧",
                    r"NO emissions X",
                    r"SO emissions X",
                    r"RSP emissions",
                ],
            },
            {
                "name": "row_match",
                # "multi": True,
                "ignore_index_table": True,
                "first_cell": False,
                "merge_row": False,
                "neglect_row_pattern": [r"\d+[-—–]\d+"],
                "row_pattern": [
                    r"A\s?1\.1$",
                ],
            },
            {
                "name": "row_match",
                "multi": True,
                # "first_cell": False,
                "ignore_index_table": True,
                "neglect_row_pattern": [r"\d+[-—–]\d+"],
                "row_pattern": [
                    r"NOx|SOx|Particles|Nitrogen Oxide|Sulphur Oxide",
                    r"Particular|Particulate",  # r'Particular matter', r'PM',
                    r"Air pollutants",
                    r"Air emissions",
                    r"oxides|particles",
                    r"^PM$",  # r'Particular matter', r'PM',
                    r"PM.*?物",  # r'Particular matter', r'PM',
                    r"Sulphur dioxide",  # r'Particular matter', r'PM',
                    r"–\s?(office|projects)",  # r'Particular matter', r'PM',
                ],
            },
            {
                "name": "table_title",
                "feature_white_list": [
                    r"GHG Emissions",
                    r"Waste Gas Generation and Management",
                    r"air emission",
                ],
            },
            {
                "name": "para_match",
                # "multi_elements": True,
                "paragraph_pattern": (
                    r"not? (directly )?(involve|engage|disclosed|generate|generation|produce)",
                    r"limited|not quantify",
                    r"A1.1|category",
                ),
            },
            DEFAULT_MODEL,
        ],
    },
    # 需要定位针对greenhouse gas emission/GHG emission中数据披露的情况，
    # 直接排放的是scope1，
    # 间接排放的是scope2，
    # 其他的是scope3。
    # 出现位置：1.报告末尾的performance data summary,KPI summary,performance table,INDEX
    #          2.environment章节的末尾environmental data
    #          3.标题A1\EMISSION\A1.2\greenhouse gas emission\GHG\direct emission下面
    #          4.如果GHG所在表格没有分scope1、2、3，但是根据表格附近段落可以知道所产生的排放是有日常业务产生的，那么把整个表框
    #            在scope1答案处，选择Y
    {
        "path": ["KPI A1.2 part 1 - Scope 1"],
        "models": [
            {
                "name": "syllabus_based",
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__Greenhouse gas emission",
                ],
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (r"Indirect emissions of carbon dioxide in greenhouse gases",),
                    "use_direct_elements": True,
                },
                "table_model": "empty",
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"KPI A1\.1 to KPI A1\.6 are not applicable",
                    r"did not consume resources.*?Scope 1 emission",
                    r"the Group did not discharge exhaust gas and direct GHG emission \(scope 1\)",
                ),
            },
            {
                "name": "middle_rows",
                "regs": [
                    r"Scope 1",
                    r"GHG emissions",
                    r"Direct emission",
                    r"Greenhouse gases",
                ],
                "title_regs": [r"TABLE OF KEY PERFORMANCE"],
                "neglect_title_regs": [r"^\d+$"],
                "start_regs": [
                    r"Scope 1",
                    r"Direct GHG emissions",
                    r"Direct emissions",
                    r"Total emission of greenhouse gas",
                    r"GRI 305-[123]",
                ],
                "end_regs": [
                    r"Scope 2",
                    r"A1\.3",
                    r"Indirect GHG emissions",
                    r"hazardous waste",
                    # r'Direct emissions',
                    r"GRI 305-4",
                ],
            },
            {
                "name": "row_match",
                "multi": True,
                "ignore_index_table": True,
                "first_cell": False,
                "row_pattern": [
                    r"Scope 1",
                    r"Scope I(?!I)",
                    r"(?<!in)Direct.*?emissions",
                    # r'Aspects 1.1|Nitrogen Oxides|Respiratory Suspended Particles' # #todo 60877 待修复
                ],
            },
            {
                "name": "first_table",
                "regs": [
                    r"Scope 1",
                ],
            },
            {
                "name": "scope",
                "regs": [r"Scope 1"],
                "start_regs": [
                    r"^Scope 1",
                ],
                "end_regs": [
                    r"^Scope 2",
                ],
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"not involve|not engage|not disclosed|not generate|no generation|not produce|not quantify",
                    r"carbon dioxide|methane|nitrous oxide|hydrofluorocarbons|perfluorocarbons and sulphur hexafluoride",
                    r"A1.2|direct emissions",
                    r"not to compile.*?scope\s?1",
                    # r'(?P<content>direct|reenhouse gas emission)',
                ),
            },
        ],
    },
    {
        "path": ["KPI A1.2 part 2 - Scope 2"],
        "models": [
            {
                "name": "syllabus_based",
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__Greenhouse gas emission",
                    r"__regex__Measures for reducing greenhouse gas",
                ],
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (r"Indirect emissions of carbon dioxide in greenhouse gases",),
                    "use_direct_elements": True,
                },
                "table_model": "empty",
            },
            {
                "name": "para_match",
                "paragraph_pattern": (r"KPI A1\.1 to KPI A1\.6 are not applicable"),
            },
            {
                "name": "middle_rows",
                "regs": [
                    r"Scope 2",
                    r"GHG emissions",
                    r"Indirect emissions",
                ],
                "title_regs": [r"TABLE OF KEY PERFORMANCE"],
                "neglect_title_regs": [r"^\d+$"],
                "start_regs": [
                    r"Scope 2",
                    r"Indirect GHG emissions",
                    r"Indirect emissions",
                    r"GRI 305-[123]",
                ],
                "end_regs": [
                    r"Scope 3",
                    r"^Total",
                    r"A\s?1\.3",
                    r"Scope 1 & Scope 2",
                    r"GRI 305-4",  # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/1562#note_284510
                ],
            },
            {
                "name": "row_match",
                "ignore_index_table": True,
                "first_cell": False,
                "multi": True,
                "row_pattern": [
                    r"Scope 2",
                    r"Scope II",
                    r"Indirect.*?emissions?",
                    r"CO2 equivalent emissions from purchased",
                ],
                "neglect_row_pattern": [
                    r"Scope 3",
                ],
            },
            {
                "name": "first_table",
                "regs": [r"Scope 2"],
            },
            {
                "name": "shape_title",
                "regs": (r"The (GHG|greenhouse gas) emission data is set out in the table below",),
            },
            {
                "name": "scope",
                "regs": [r"Scope 2"],
                "start_regs": [
                    r"^Scope 2",
                ],
                "end_regs": [
                    r"^Scope 3",
                ],
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"not involve|not engage|not disclosed|not generate|no generation|not produce|limited|not quantify",
                    r"carbon dioxide|methane|nitrous oxide|hydrofluorocarbons|perfluorocarbons and sulphur hexafluoride",
                    r"scope2",
                    # r'A1.2|scope2|greenhouse gas emission|energy',
                ),
            },
            # DEFAULT_MODEL,
        ],
    },
    {
        "path": ["KPI A1.2 part 3 - Scope 3"],
        "models": [
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"KPI A1\.1 to KPI A1\.6 are not applicable",
                    r"The only source of GHG emission is Scope 2 GHG emissions",  # only Scope 2, 说明没有 Scope 3
                ),
            },
            {
                "name": "scope",
                "regs": [
                    r"Scope 3",
                ],
                "start_regs": [
                    r"^Scope 3",
                ],
                "end_regs": [
                    r"^Scope 3",
                    r"A1\.3",
                ],
            },
            {
                "name": "middle_rows",
                "regs": [
                    r"Scope 3",
                    # r'GHG emissions',
                ],
                "title_regs": [r"TABLE OF KEY PERFORMANCE"],
                "start_regs": [
                    r"Scope 3",
                    r"GRI 305-[123]",
                ],
                "end_regs": [
                    r"total",
                    r"A\s?1\.3",
                    r"employee",
                    r"GRI 305-4",
                ],
            },
            {
                "name": "row_match",
                # "ignore_index_table": True,
                "first_cell": False,
                "multi": True,
                "row_pattern": [
                    r"^Scope 3",
                ],
            },
            {
                "name": "first_table",
                "regs": [r"Scope 3"],
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"(?P<content>not involve|not engage|not disclosed|no generation|not produce|not quantify)",
                    r"(?P<content>carbon dioxide|methane|nitrous oxide|hydrofluorocarbons|perfluorocarbons and sulphur hexafluoride)",
                    r"scope3",
                    r"other indirect emissions",
                ),
                "neglect_pattern": (
                    r"scope [12]",
                    DIRECTORY_START,
                ),
            },
            # DEFAULT_MODEL,
        ],
    },
    {
        # 需要定位到含有hazardous waste数据所在图表、段落或句子，包括产生的hazardous waste总量及强度（每单位产量、每项设施）
        # 出现位置：1.报告末尾的performance data summary,KPI summary,performance table,INDEX
        #          2.environment章节的末尾environmental data
        #          3.标题A1\EMISSION\A1.3\hazardous waste\waste下面
        "path": ["KPI A1.3 - hazardous waste"],
        "models": [
            {
                "name": "after_row_match",
                "first_cell": False,
                "row_pattern": [r"A1.3"],
                "middle_rows": True,
                "start_regs": [r"(?<!non-)hazardous wastes?"],
                "end_regs": [
                    r"non-hazardous wastes?",  # todo uat 65869
                    r"Construction material",
                ],
                "just_a_para": True,
                "para_pattern": [
                    r"A1.3|(?<!non-)hazardous wastes?",
                    r"not generate.*?hazardous wastes?",
                ],
                "direct_answer_pattern": [
                    r"not applicable",
                    r"No material.*?hazardous waste",
                    r"has not identified any hazardous waste",
                ],
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"KPI A1\.1 to KPI A1\.6 are not applicable",
                    r"KPI A1.3 are not applicable",
                    r"A1\.3.*?(not been|is not)\s?disclosed",
                    r"(?<!Non-)Hazardous waste.*?not include",
                    r"not generate.*?hazardous waste",
                    r"^(?!In ).*no (significant)?hazardous wastes? (is|was|are|were|or .* (is|was|are|were)) (generated|produced|recorded)",
                    r"hazardous waste generation was insignificant",
                    r"no significant hazardous wastes? (generated|produced)",
                    r"not produce (significant amounts of|any|any \w+) hazardous (wastes?)?",
                    r"Regarding hazardous wastes?.*not (produce|generated)",
                    r"the generation of hazardous and non-hazardous waste is not material to us",
                    r"he Group.*?operations do not involve hazardous waste",
                ),
            },
            {
                "name": "middle_rows",
                "ignore_index_table": True,
                "regs": [
                    r"A1.3",
                    r"^waste$",
                    r"(?<!non-)hazardous waste",
                ],
                "start_regs": [r"(?<!non-)hazardous wastes?"],
                "end_regs": [
                    r"non-hazardous wastes?",
                    r"Energy usage",
                    r"Use Of Energy",
                    r"Construction material used",
                ],
            },
            {
                "name": "esg_row_match",
                "row_pattern": [
                    r"(?<!non-)hazardous wastes?",
                ],
            },
            {
                "name": "row_match",
                "ignore_index_table": True,
                "multi": True,
                "row_pattern": [
                    r"(?<!non-)hazardous wastes?",
                    r"Waste (ink|toner)",
                ],
            },
            {
                "name": "table_title",
                "feature_black_list": [
                    r"__regex__^.$",
                    r"__regex__^(Environment[al,]*)$",
                    r"__regex__Emissions$",
                    r"__regex__環境",
                ],
                "feature_white_list": [
                    r"(?<!non-)hazardous wastes?",
                    r"有害廢棄物排放",
                ],
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"not involve|not engage|not disclosed|not generate|no generation|not produce|not quantify",
                    r"no hazardous waste is generated",
                    r"not produce any hazardous waste",
                    r"(?<!non-)hazardous waste",
                ),
            },
            DEFAULT_MODEL,
        ],
    },
    {
        # 需要定位到含有non-hazardous waste数据所在图表段落或句子，包括产生的non-hazardous waste总量及强度（每单位产量、每项设施）
        # 出现位置：1.报告末尾的performance data summary,KPI summary,performance table,INDEX
        #          2.environment章节的末尾environmental data
        #          3.标题A1\EMISSION\A1.4\non-hazardous waste\waste下面
        "path": ["KPI A1.4 - non-hazardous waste"],
        "models": [
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"KPI A1\.1 to KPI A1\.6 are not applicable",
                    r"A1\.4.*?not been disclosed",
                    r"non-hazardous waste generated by our Group ",
                    r"Group generates no hazardous waste",
                    r"the Group will expand its data .*?non-hazardous waste.*?future",
                    r"amount of waste are trivial and no KPI are identified and disclosed",
                    r"non-hazardous.*?wastes.*?negligible",
                    r"expand its data.*?non-hazardous waste.*?future",
                    r"no KPI are identified and disclosed",
                    r"waste are trivial",
                    r"The non-hazardous waste generated was around.*?tonnes",
                    r"non-hazardous waste.*?relatively limited",
                ),
            },
            {
                "name": "table_title",
                "only_inject_features": True,
                "feature_white_list": [
                    r"non-hazardous waste (was )?generate",
                    r"generate non-hazardous waste",
                    r"non-hazardous waste production performance",
                ],
            },
            {
                "name": "row_match",
                "ignore_index_table": True,
                "multi": True,
                "row_pattern": [
                    r"Waste fluorescent tube",  # 特例 64004
                    r"Office Waste",
                ],
            },
            {
                "name": "after_row_match",
                "row_pattern": [r"A\s?1\s?.4"],
                "para_pattern": [
                    r"The non-hazardous waste generated was around.*?tonnes",
                    r"non-hazardous waste.*?relatively limited",
                ],
                "just_a_para": True,
                "table_title": [r"non-hazardous"],
                "middle_rows": True,
                "start_regs": [r"non-hazardous"],
                "end_regs": [],
                "direct_answer_pattern": [
                    r"not applicable",
                    r"provided by the property management companies",
                ],
            },
            {
                "name": "middle_rows",
                "ignore_index_table": True,
                "regs": [
                    r"A1.4",
                    r"hazardous waste",
                    r"C&D.*?Waste",
                ],
                "start_regs": [
                    r"non-\s?hazardous waste",
                    r"C&D.*?Waste",
                ],
                "end_regs": [
                    r"^hazardous waste",
                    r"Use of Resources",
                    r"energy consumption",
                    r"Paper Consumption",
                    r"Exhaust Gas Emissions",
                    r"total hazardous waste",
                ],
            },
            {
                "name": "table_title",
                "only_inject_features": True,
                "feature_white_list": [
                    r"non-hazardous waste",
                ],
                "feature_black_list": [
                    r"__regex__^.$",
                    r"__regex__^(Environment[al,]*)$",
                ],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__A1.4",
                    r"__regex__non-hazardous waste",
                    r"__regex__^Wastes$",
                ],
                "only_inject_features": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        EXPLAIN_PATTERN,
                        r"insignificant",
                        r"waste collector approved by the Environmental Protection Department",
                        r"no plan to set a goal to reduce non-hazardous waste",
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "para_match",
                # "multi_elements": True,
                # "enum_from_multi_element": True,
                "paragraph_pattern": (
                    r"(?P<content>non-hazardous waste)",
                    r"(?P<content>A1.4)",
                    r"(?P<content>During.*?the total construction waste disposed.*?respectively)",
                    r"amount of waste are trivial",
                ),
            },
            DEFAULT_MODEL,
        ],
    },
    {
        # 提取内容：提取涉及到“GHG emission reduction target的数据”及为了reduce GHG/carbon emission 的一些措施”的描述
        #
        # 常见关键词： A1.5, target, aim，future plan, reduce，minimize
        #
        #        可从index给出的各个KPI的位置去定位；或在 emission/GHG emission
        "path": ["KPI A1.5 - emission target"],
        "models": [
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"KPI A1\.1 to KPI A1\.6 are not applicable",
                    r"A1\.5.*?not been disclosed",
                ),
            },
            {
                "name": "after_row_match",
                "row_pattern": [r"A1.5"],
                "multi_elements": True,
                "para_pattern": [
                    r"not set any environmental targets",
                    "(target|aims?) (is|to)",
                    "following energy-saving measures:",
                    "following.*?measures.*?:$",
                    "resources include:$",
                    "following methods to reduce.*?emissions:$",
                    "repairs to be carried out",
                    "in order to reduce.*?pollutants",
                    "improve (energy )?efficiency",
                    "energy consumption analysis",
                    "energy audit",
                    "saving about.*?electricity consumption",
                    "reducing.*?exhaust emissions",
                    "The Group targets to ",
                ],
                "middle_rows": False,
                "direct_answer_pattern": [
                    r"No.*?target is set",
                    r"not set.*?targets",
                ],
            },
            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/878#note_230741
            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/878#note_231052
            {
                "name": "twice_row_match",
                "first_cell": False,
                "row_pattern": [r"air pollutants|green\s?house gas(?: emissions)?"],
                "merge_valid_rows": False,
                "second_pattern": [
                    r"target|aim|future plan|reduce|minimize",
                ],
            },
            {
                "name": "table_title",
                "feature_white_list": [
                    r"steps taken to achieve the target",
                    r"Long-term Strategy and Targets",
                ],
                "only_inject_features": True,
                "first_row_as_title": True,
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                # "multi": True,
                "inject_syllabus_features": [
                    r"__regex__environmental protection__regex__target",
                    r"__regex__(Measures|Targets).*?Emissions",
                    r"__regex__Emissions.*?(Measures|Targets)",
                    r"__regex__environmental policy__regex__Our Environmental Targets",
                ],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__Emissions.*?(Measures|Targets)",
                    r"__regex__Greenhouse Gas Emission",
                    r"__regex__GHG Emissions",
                    r"__regex__Air Emissions",
                    r"__regex__CARBON MANAGEMENT",
                    r"__regex__WASTE GAS AND SEWAGE EMISSIONS",
                    r"__regex__EMISSIONS",
                    r"__regex__OPTIMIZING ENERGY SAVING",
                    r"__regex__RESOURCES CONSUMPTION",
                    r"__regex__Energy Development__regex__Environmental__regex__Policies and Use of Resources",
                    r"__regex__DECARBONISING LOGISTICS OPERATIONS",
                ],
                "multi": True,
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"not set any environmental targets",
                        "(target|aims?) (is|to)",
                        "following energy-saving measures:",
                        "following.*?measures.*?:$",
                        "resources include:$",
                        "following methods to reduce.*?emissions:$",
                        "repairs to be carried out",
                        "in order to reduce.*?pollutants",
                        "Reduce Emission",
                        "energy-saving equipment",
                        "Placing.*?Environmental Protection.*?reminders",
                        "CARBON REDUCTION TARGETS",
                    ),
                    "neglect_pattern": (
                        r"reduce the intensity of total hazardous waste",
                        r"set a target to gradually reduce the intensity of total non-hazardous waste",
                    ),
                },
                "table_model": "special_cells",
                "table_config": {
                    "whole_table": True,
                    "cell_pattern": (r"Reduce.*?Emissions",),
                },
            },
            {
                "name": "para_match",
                "multi_elements": True,
                "enum_from_multi_element": True,
                "neglect_syllabus_regs": [
                    r"CLIMATE CHANGE",
                ],
                "paragraph_pattern": (
                    r"reduce emission",
                    r"establish.*?environmental targets",
                    r"switch off lightings when not in use",
                ),
            },
            DEFAULT_MODEL,
        ],
    },
    {
        # 提取内容：提取涉及到废弃物各种处理方式及数据或描述所在的段落
        # 常见关键词： A1.6, dispose, recycle, reuse, recover
        #        可从index给出的各个KPI的位置去定位；或在 waste management
        # 特殊情况：个别文档仅给了处理（recycle，reuse）的数据，直接框选对应表格或段落
        "path": ["KPI A1.6 part 1 - waste handling"],
        "models": [
            {
                # for explain
                "name": "para_match",
                "paragraph_pattern": (
                    r"KPI A1\.1 to KPI A1\.6 are not applicable",
                    r"Due to the business nature of our Group, certain construction waste is generated in our construction process",
                    r"A1\.6.*?not been disclosed",
                    r"different waste management procedures",  # comply
                    r"took strong measure",  # comply
                ),
            },
            {
                "name": "after_row_match",
                "row_pattern": [r"A1.6"],
                "multi_elements": True,
                "para_pattern": [
                    r"dispose\s|recycle|reuse|recover",
                    r"dispose\s",
                    r"disposed of",
                    r"collected|collection",
                    r"classification|transfer and disposal of wastes",
                    r"disposed responsibly through donation",
                    r"Dispos.*?wastes",
                    r"different waste management procedures",
                    r"delegate to qualified third-party organization",
                    r"strictly standardized the classified collection",
                    r"took strong measure",
                    r"the Group has implemented waste management approach and initiatives",
                    r"keep hazardous waste generation",
                ],
                "middle_rows": True,
                "table_title": [
                    r"Dispos.*?wastes",
                ],
            },
            {
                "name": "special_cells",
                "whole_table": True,
                "cell_pattern": (
                    r"Disposal measure",
                    # r'hazardous waste',
                ),
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "multi": True,
                "inject_syllabus_features": [
                    r"__regex__MEASURES__regex__hazardous waste",
                    r"__regex__Waste.*?Management__regex__PERFORMANCE__regex__MEASURES",
                    r"__regex__Waste.*?Management__regex__Non-hazardous waste",
                    r"__regex__Waste.*?Management__regex__hazardous waste",
                    r"__regex__waste discharge",
                    r"__regex__Solid Waste Handling",
                    r"__regex__WASTE AND MATERIAL MANAGEMENT",
                ],
            },
            {
                "name": "syllabus_based",
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__hazardous Waste",
                    r"__regex__waste managemen",
                    r"__regex__waste discharge",
                    r"__regex__use of Resources",
                    r"__regex__Emissions",
                ],
                "multi_elements": True,
                "multi": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"(?P<content>immaterial|insignificant)",
                        r"dispose\s|reuse|recover",
                        r"disposed of",
                        r"collected (by|in)|collection",
                        r"classification|transfer and disposal of wastes",
                        r"disposed responsibly through donation",
                        r"disposed (\w+\s){0,3}wastes",
                        r"handled according to the requirements",
                        r"transfer and disposal of wastes",
                        r"delegate to qualified third-party organization",
                        r"strictly standardized the classified collection",
                    ),
                    "neglect_pattern": (
                        # r'reduc',
                        r"recovery rate",
                    ),
                    "neglect_syllabus_regs": [
                        # r'reduc',
                        r"recycling",
                    ],
                },
                "table_model": "special_cells",
                "table_config": {
                    "whole_table": True,
                    "cell_pattern": (
                        r"Disposal measure",
                        # r'(?<!non-)hazardous waste',
                    ),
                },
            },
            {
                "name": "para_match",
                # "multi_elements": True,
                "paragraph_pattern": (
                    r"(?P<content>not involve|not engage|not disclosed|not generate|no generation|not produce|limited|not quantify)",
                    # r'(?P<content>immaterial|insignificant|plan|continue)',
                    r"(?P<content>dispose|recycle|reuse|recover|recycling)",
                    r"dispose|recycle|reuse|recover",
                    r"managed centrally by the office property management company",
                    r"not undertake",
                    r"collect garbage",
                    r"local urban management",
                    r"waste category",
                    r"waste.*?taken out",
                    r"dispose\s",
                    r"disposed of",
                    r"collected|collection",
                    r"classification|transfer and disposal of wastes",
                    r"disposed responsibly through donation",
                    r"Dispos.*?wastes",
                ),
            },
            {
                "name": "kmeans_classification",
            },
            DEFAULT_MODEL,
        ],
    },
    {
        # 该规则是框选出公司为排放固体废物而设置的排放目标，或者为了减少产生废物而采取的措施；
        # 如果都没有也可以框选已经实现的减排目标，可以不是对未来目标的设置
        #  所在位置：1. 涉及到waste management的标题下关于waste reduction target的文字段落
        #           2. 在waste management的标题下有关键词waste reduction的小标题下的内容  优先级靠前
        #           3. 如果按照non-hazardous waste和hazardous waste披露的，一般是两处都会涉及到 reduction waste的内容
        #           4. 在ESG报告中单独有个environmental target的标题下，关键词waste的相关内容
        #
        #  提取内容：1. 提取涉及到“waste reduction target”的数据
        #           2. 没数据但有“为了reduce waste 而有一些措施”的描述
        #           3. 当年或目前为止已经实现的reduction，可以不是未来的target
        #           4. 如果上面几点都有，则可以按照上面序号的优先级来全部提取
        #
        #  关键词：（需要在waste相关位置范围内查找）target, aim, future plan, minimize, reduction, reduce, reuse, recycle, prevent
        #
        #  判定方式：Y-披露并描述以上的提取内容（满足提取内容的任何一条或多条即可）
        #           E-明确披露了 not material/not involve/not engage/ not disclosed/ not produce的
        #           E-写明了`没有waste target`的，即使有相关措施，也判定为E
        #           ND-没有任何描述
        "path": ["KPI A1.6 part 2 - waste reduction target"],
        "models": [
            # 特例 GRI 相关 https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/997#note_243521
            {
                "name": "esg_16_part_2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "ignore_missing_crude_answer": True,
                "inject_syllabus_features": [
                    # r'__regex__Measures for waste reduction',
                    r"__regex__Control o f wastewa ter discharge",
                ],
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"Automated rol-shearing machine.*?during the year",
                        r"Cold-cutting machine.*?dur",
                        r"plate casting machine",
                    ),
                },
                "table_model": "empty",
            },
            # for explain
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"KPI A1\.1 to KPI A1\.6 are not applicable",
                    r"A1\.6.*?not been disclosed",
                ),
            },
            {
                "name": "table_title",
                "multi_elements": True,
                "only_inject_features": True,
                "feature_white_list": [
                    r"Environment goals",
                    r"Waste Management Targets",
                ],
            },
            {
                "name": "shape_title",
                "regs": (
                    r"Environment goals",
                    r"Waste Management Targets",
                ),
            },
            {
                "name": "row_match",
                # "whole_table": True,
                "multi": True,
                "row_pattern": [
                    r"^waste (reduction|management)",
                    r"(water|waste|energy|emissions).*?reduce by",
                ],
            },
            # only for explain answer
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__green office",
                ],
                "only_inject_features": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"not set any environmental targets",
                        r"A1.6.*?not applicable",
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "multi": True,
                "inject_syllabus_features": [
                    r"__regex__MEASURES__regex__hazardous waste",
                    r"__regex__Waste.*?Management__regex__PERFORMANCE__regex__MEASURES",
                    r"__regex__Waste.*?Management__regex__Non-hazardous waste",
                    r"__regex__Waste.*?Management__regex__hazardous waste",
                    r"__regex__waste discharge",
                    r"__regex__Solid Waste Handling",
                    r"__regex__WASTE AND MATERIAL MANAGEMENT",
                ],
            },
            {
                "name": "after_row_match",
                "row_pattern": [r"A1.6"],
                "para_pattern": [
                    r"aim.*?(to|at).*?reduc.*?waste",
                    r"be recycled",
                    r"measures.*?handle wastes:",
                    r"measures.*?following:$",
                    r"following measures for reducing waste:$",
                    r"has set a target",
                    r"set.*?reduction target",
                    r"set target to reduce waste",
                    r"minimisation of waste generation",
                    r"reuse of materials",
                    r"recovery and recycling",
                    r"printed when needed",
                    r"reduction initiatives",
                    r"Use recyclable products",
                    r"Recycle office paper",
                    r"Print.*?when necessary|print on both",
                    r"Provide reusable",
                    r"waste reduction measures",
                    r"no specific reduction target",
                    r"targets? (is )?to reduce",
                    r"targeted a reduction",
                    r"initiatives to reduce",
                    r"measures.*?reduce.*?waste disposal",
                    r"managed centrally by the office property management company",
                    r"dedicated to proper management of the non-hazardous waste",
                    r"(?P<content>not undertake)",
                ],
                "multi_elements": True,
                "direct_answer_pattern": [
                    r"No.*?target is set",
                    r"not set.*?targets",
                ],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__A1\.6",
                    r"__regex__Waste Reduction",
                    r"__regex__Waste Handling",
                    r"__regex__wastes? managemen",
                    r"__regex__reduction Initiatives",
                    r"__regex__Handling Initiatives",
                    r"__regex__Hazardous waste emissions",
                    r"__regex__EMISSION REDUCTION",
                    r"__regex__^\s.*?wastes$",
                    r"__regex__^Use of Resources$",
                    r"__regex__^Classification of wastes",
                ],
                "multi_elements": True,
                "paragraph_model": "para_match",
                "as_follow_pattern": [
                    r"adopted appropriate equipment for reducing.*?：$",
                    r"Emission and Waste Reduction Targets$",
                ],
                "para_config": {
                    "paragraph_pattern": (
                        r"waste.*?including:$",
                        r"reduce waste",
                        r"aim.*?(to|at).*?reduc.*?waste",
                        r"be recycled",
                        r"measures.*?handle wastes:",
                        r"measures.*?following:$",
                        r"following measures for reducing waste:$",
                        r"has set a target",
                        r"set.*?reduction target",
                        r"minimisation of waste generation",
                        r"reuse of materials",
                        r"recovery and recycling",
                        r"printed when needed",
                        r"reduction initiatives",
                        r"Use recyclable products",
                        r"Recycle office paper",
                        r"Print.*?when necessary|print on both",
                        r"Provide reusable",
                        r"waste reduction measures",
                        r"no specific reduction target",
                        r"targets? (is )?to reduce",
                        r"targeted a reduction",
                        r"initiatives to reduce",
                        r"measures.*?reduce.*?waste disposal",
                        r"managed centrally by the office property management company",
                        r"dedicated to proper management of the non-hazardous waste",
                        r"adopted the following practices to reduce the consumption",
                        r"implementation of the measures.*?reduc",
                        r"implementation of the measures.*?reduc",
                        r"not undertake",
                        r"objective is to.*?reduction",
                        r"adopted.*?measures.*?to reduce",
                        r"to further reduce overall waste generation",
                        r"adopted appropriate equipment for reducing.*?：$",
                        r"reduced the amount of",
                        r"Emission and Waste Reduction Targets$",
                        r"^•",
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "para_match",
                "multi": True,
                "paragraph_pattern": (
                    r"aim.*?(to|at).*?reduc.*?waste",
                    r"be recycled",
                    r"measures.*?handle wastes:",
                    r"measures.*?following:$",
                    r"following measures for reducing waste:$",
                    r"has set a target",
                    r"set.*?reduction target",
                    r"set target to reduce waste",
                    r"minimisation of waste generation",
                    r"reuse of materials",
                    r"recovery and recycling",
                    r"printed when needed",
                    r"reduction initiatives",
                    r"Use recyclable products",
                    r"Recycle office paper",
                    r"Print.*?when necessary|print on both",
                    r"Provide reusable",
                    r"waste reduction measures",
                    r"no specific reduction target",
                    r"targets? (is )?to reduce",
                    r"targeted a reduction",
                    r"initiatives to reduce",
                    r"measures.*?reduce.*?waste disposal",
                    r"managed centrally by the office property management company",
                    r"dedicated to proper management of the non-hazardous waste",
                    r"in order to|waste reduction",
                    r"(?P<content>not undertake)",
                    r"adopted appropriate equipment for reducing",
                ),
            },
            DEFAULT_MODEL,
        ],
    },
    {
        # 提取内容：
        # 涉及resources政策的段落，段落中常包含 resource，energy，usage，water consumption等词语。
        # 在有包含use/usage+resources标题时，可提取该标题下的全部内容。在有index的情况下，去index指明的位置提取会更准确
        # 当没有use/usage+resources相关的标题时，可提取energy，water，paper等相关标题下的措施
        # E的判断：
        # 【简单情况】：not disclosed,not available, NA,
        # 该规则E的情况在标注过程中还没有遇到过
        # Y的判断：
        # 有：resources，energy，water，paper等相关的policy或management或measure或reduce或control等措施的描述。
        "path": ["A2 policies - use of resources"],
        "models": [
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_before_first_chapter": True,  # file64000 file63990
                "only_inject_features": True,
                # "only_first": True,
                "inject_syllabus_features": [
                    r"__regex__Use of Resources",
                    r"__regex__(use|usage).*?resources",
                    r"__regex__Emissions and Use of resources",
                    r"__regex__Use of Resources and Management",
                    r"__regex__utilisation.*?resources",
                    r"__regex__Resources.*?Utilisation",
                    r"__regex__Reducing Resource Waste",
                    r"__regex__ENVIRONMENT AND NATURAL RESOURCES",
                    r"__regex__Green and Low-Carbon Operation",
                    r"__regex__Renewable energy connections",
                ],
                "neglect_parent_features": [
                    r"Independent Limited Assurance Report",  # file63947 应该是r'Appendix'但层级错误
                ],
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_before_first_chapter": True,
                "multi": True,
                "inject_syllabus_features": [
                    r"__regex__Energy (Conservation|Efficiency|Usage|Saving)",  # file63958
                    r"__regex__Water Conservation",
                    r"__regex__Packaging Materials",
                    r"__regex__(resource|paper|ELECTRICITY)",
                ],
                "neglect_parent_features": [
                    r"Independent Limited Assurance Report",  # file63947 应该是r'Appendix'但层级错误
                    r"Our approach to human resources",
                ],
            },
            {
                "name": "kmeans_classification",
                "filter_content_answer": True,
            },
            {
                "name": "para_match",
                "multi_elements": True,
                "enum_from_multi_element": True,
                "paragraph_pattern": (
                    r"(?P<content>measures|policy|resource|energy|usage|consumption)",
                    r"turn off (lights|air-conditioners)|energy efficiency|energy conservation|switch off|avoid printing",
                    r"LED lighting|effectiveness|reduction|recycle|reuse|save|waste|garbage|electricity|power|solar|digitalise|wastepaper|renewable|new energy",
                ),
            },
            DEFAULT_MODEL,
        ],
    },
    {
        # 需要定位披露能源消耗的数据(包含intensity/density），通常为图表形式，有的报告可能涉及文字描述
        # 出现位置：1. 报告末尾的performance data summary,KPI summary, performance table
        #          2. environment章节的末尾environmental data
        #          3. use of resource标题下energy management或者energy consumption小标题下的表格
        #          4. 位置不固定，但可以通过energy consumption/resource consumption关键词来定位
        #          （此规则所述`能源`通常涉及到electricity/petroleum/gasoline/diesel）
        "path": ["KPI A2.1 - energy consumption"],
        "models": [
            {
                "name": "para_match",
                "paragraph_pattern": (r"Total electricity consumption.*?kWh",),
            },
            {
                "name": "special_cells",
                "whole_table": True,
                "multi_elements": True,
                "just_first_row": True,
                "cell_pattern": (
                    r"Types of energy consumption",
                    r"Energy consumption intensity",
                    r"Energy usage",
                ),
            },
            {
                "name": "after_row_match",
                "row_pattern": [r"A\s?2\s?.1"],
                "para_pattern": [
                    r"Total electricity consumption.*?kWh",
                ],
                "just_a_para": True,
                "middle_rows": True,
                "start_regs": [r"Energy (consumption|type)"],
                "end_regs": [
                    r"non-hazardous",
                    r"Water usage",
                ],
            },
            {
                "name": "syllabus_based",
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__Greenhouse gas emission",
                ],
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (r"The heat consumed by the company is mainly steam",),
                    "use_direct_elements": True,
                },
                "table_model": "empty",
            },
            {
                "name": "middle_rows",
                "regs": [
                    r"Energy|Electricity",
                ],
                "start_regs": [
                    r"energy consumption",
                    r"Energy Intensity",
                    # r'Energy',
                    r"electricity consumption",
                    r"electricity.*?processing",
                    r"power consumption",
                    r"Total Consumption",
                    r"Energy.*?unit",  # 特例 兼容 ^Energy$
                    r"Electricity\s?\(kWh\)",
                    r"Use of Energy",
                ],
                "end_regs": [
                    r"Use of water",
                    r"water consumption",
                    r"GRI 303-4",
                    r"hazardous waste",
                    r"non-hazardous",
                    r"Water\s?\(m",
                ],
            },
            {
                "name": "row_match",
                "multi": True,
                "first_cell": False,
                "ignore_index_table": True,
                "row_pattern": [
                    r"A2.1",
                    r"Energy",
                    r"Electricity",
                    r"LPG",
                    r"Diesel|Naphtha|Petrol",
                ],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    "__regex__KPI A2.1",
                    "__regex__nergy Consumption",
                ],
                "only_inject_features": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"energy consumption.*?not material",
                        r"electricity consumption amounted",
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "para_match",
                "multi_elements": True,
                "enum_from_multi_element": True,
                "paragraph_pattern": (
                    r"Total electricity consumption.*?kWh",
                    r"(?P<content>not available|not a concern)",
                    r"(?P<content>oil|petrol|gasoline)",
                    r"(?P<content>A2.1|consumption|indirect|direct)",
                    r"(?P<content>During the Year.*?consumed.*?electricity)",
                    r"(?P<content>During the Year.*?consumed.*?gas oil)",
                ),
            },
            DEFAULT_MODEL,
        ],
    },
    {
        # 需要定位披露水资源消耗的数据(包含intensity/density），通常为图表形式，有的报告可能涉及文字描述
        # 出现位置：1. 报告末尾的performance data summary,KPI summary, performance table
        #          2. environment章节的末尾environmental data
        #          3. use of resource标题下water management或者water consumption小标题下的表格
        #          4. 位置不固定，但可以通过water consumption关键词来定位
        "path": ["KPI A2.2 - water consumption"],
        "models": [
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"water usage data.*?not feasible",
                    r"no individual water consumption data",
                    r"use of water is not significant",
                    r"not possess information.*?water consumption for disclosure",
                    r"water consumption was.*?m3",
                    r"Total water consumption.*?tons",
                ),
            },
            {
                "name": "after_row_match",
                "row_pattern": [
                    r"A2.2",
                ],
                "para_pattern": [
                    r"no individual water consumption data",
                ],
                "direct_answer_pattern": [
                    r"not available",
                ],
            },
            {
                "name": "table_title",
                "multi_elements": True,
                "only_inject_features": True,
                "feature_white_list": [
                    "本集團的?用水表現概要",
                    "本集團的?耗水詳情如下",
                    "water consumption",
                ],
            },
            {
                "name": "middle_rows",
                "ignore_index_table": True,
                "regs": [
                    r"A2.2",
                    r"water consumption",
                ],
                "start_regs": [
                    r"A2.2",
                    r"water consumption",
                ],
                "end_regs": [
                    r"A2.[345]",
                    r"packaging material",
                    r"Greenhouse Gas",
                    r"Energy Consumption",
                    r"Paper Consumption",
                    r"total Workforce",
                    r"^Workforce",
                    r"^waste",
                ],
            },
            {
                "name": "row_match",
                "ignore_index_table": True,
                "row_pattern": [
                    r"water\s",
                    r"water.*?(m|tones)",
                    r"Water Consumption",
                    r"A2.2",
                ],
                "multi": True,
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    "__regex__KPI A2.2",
                    "__regex__Wat.*?er Consumption",
                    "__regex__Greenhouse gas emission",
                ],
                "only_inject_features": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"water consumption.*?not available",
                        r"water consumption.*?approximately",
                        r"Water usage in the Group.*?is minimal",
                        r"The Company enhanced water-saving consciousness",
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "para_match",
                "multi_elements": True,
                "enum_from_multi_element": True,
                "paragraph_pattern": (
                    r"not applicable|no significant",
                    # r'no material',
                    # r'(?P<content>A2.2|Water consumption|water consumed)',
                ),
            },
        ],
    },
    {
        # A2涉及到的规则都是关于资源使用效率相关的内容；A2.3需要框选出公司为实现节能而设置的节能目标，或者为了节能而采取的措施；
        # 如果都没有也可以框选已经实现的节能目标，可以不是对未来目标的设置。
        #  所在位置：1. 涉及到use of resources的标题下关于energy efficiency的文字段落
        #           2. 在use of resources的标题下有关键词energy management的小标题下的内容
        #           3. 如果按照energy的种类披露的，一般会涉及到electricity，fuel，natural gas的reduction/saving/conservation内容
        #           4. 在ESG报告中单独有个environmental target的标题下，关键词energy的相关内容
        #
        #  提取内容：1. 提取涉及到“energy efficiency target”的数据
        #           2. 没数据但有“为了energy conservation 而有一些measures”的描述
        #           3. 当年或目前为止已经实现的energy efficiency target，可以不是未来的target
        #           4. 如果上面几点都有，则可以按照上面序号的优先级来全部提取
        #
        #  关键词：（需要在energy相关位置范围内查找）target, efficiency, establish, goal, aim, future plan,
        #  energy conservation, reduction, prevent
        #
        #  判定方式：Y-披露并描述以上的提取内容（满足提取内容的任何一条或多条即可）
        #           E-明确披露了 not material/not involve/not engage/ not disclosed/ not produce的
        #           E-写明了`没有establish energy efficiency target`的，即使有相关措施，也判定为E
        #           ND-没有任何描述
        "path": ["KPI A2.3 - energy efficiency targets"],
        "location_threshold": 0.05,
        "models": [
            {
                "name": "row_match",
                "ignore_index_table": True,
                "row_pattern": [
                    r"actively reducing energy consumption",
                    # r'Energy Conservation',
                    # r'A2.3',
                ],
            },
            {
                "name": "table_title",
                "multi_elements": True,
                "only_inject_features": True,
                "feature_white_list": [
                    r"Measures for Saving Energy",
                ],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__Environmental Initiative and Mitigation Target",
                ],
                "only_inject_features": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (r"^•", r"target"),
                },
                "table_model": "empty",
            },
            {
                "name": "syllabus_elt_v2",
                "only_inject_features": True,
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "inject_syllabus_features": [
                    r"__regex__Environmental.*?Target__regex__Quantitative target",
                    # r'__regex__Environmental__regex__Energy',
                    r"__regex__ENERGY CONSUMPTION__regex__measure",
                    r"__regex__Energy Use Efficiency Initiatives",
                    r"__regex__sustainability target",
                    r"__regex__Energy Conservation.*?KPI A2\.3",
                    r"__regex__Electricity and Water Management",
                ],
            },
            {
                "name": "syllabus_based",
                "only_inject_features": True,
                "max_syllabus_range": 80,
                "inject_syllabus_features": [
                    r"__regex__Environmental.*?Target__regex__Quantitative target",
                    r"__regex__Policies and Use of Resources",
                    r"__regex__Energy and Emissions Management",
                    r"__regex__Energy Consumption",  # uat 58433
                    r"__regex__Energy usage",
                    r"__regex__use of resources?",  # uat 58426
                    r"__regex__Use of Natural Resources?",
                    r"__regex__Electricity",  # 60875
                    r"__regex__Fuel",  # 60875
                    r"__regex__A2\.3.*?energy",
                ],
                # 'multi': True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"following measures to reduce energy consumption",
                        r"reduction of waste.*?include:$",
                        r"group targets? to reduce",
                        r"Group closely monitors its energy usage to consumption",
                        r"our aim is",
                        r"energy conservation",
                        r"set targets to",
                        r"targets? (is )?to reduce",
                        r"targeted a reduction",
                        r"has set a target",
                        r"set.*?reduction target",
                        r"set target to reduce waste",
                        r"no specific reduction target",
                        r"target",
                        r"employing various initiatives and measures",
                        r"implemented.*?as follows:",
                        r"to improve our energy efficiency",
                        r"plan is to reduce energy",
                        r"in order to.*?energy efficiency",
                        r"deployed the following energy efficient measures",
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "twice_para_match",
                "multi_elements": True,
                # "strict_limit": True,
                "syllabus_regs": [],
                "neglect_syllabus_regs": [],
                "paragraph_pattern": (r"energy|efficiency",),
                "second_pattern": (
                    r"A2.3|target|goal|reduce|measure|establish|set|reduce consumption",
                    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/878#note_230135
                    # r'building',
                ),
            },
            DEFAULT_MODEL,
        ],
    },
    {
        # 需要定位披露公司是否有取水问题，通常描述是公司在取水方面没有问题，但文字的表达方式有多种，通过sourcing + water可以大致定位
        # 出现位置：1. 报告末尾的performance data summary,KPI summary, performance table
        #          2. environment章节的末尾environmental data
        #          3. use of resource标题下water management中的文字描述
        #          4. 有的出现在water consumption的表格下面的notes里
        #          5. 有的报告直接披露在index里面，其他rule在index中会refer到某个章节的标题，
        #          而这条规则有时描述就在这个index的最后一列位置（有时候为not material或者N/A）
        "path": ["KPI A2.4 part 1 - water sourcing"],
        "models": [
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"water conservation target",
                    r"sources water from.*?no problem",
                    r"water consumption.*?insignificant",
                    r"no sourcing issue",
                    r"sources water from the municipal supplies",
                ),
            },
            {
                "name": "after_row_match",
                "row_pattern": [r"A2.4"],
                "multi_elements": True,
                "para_pattern": [r"water source"],
                "direct_answer_pattern": [
                    r"involve no significant use of water.*?not disclosed",
                    r"does not encounter any issues in sourcing water",
                    r"not identified",
                    r"Defined to be irrelevant to the Group’s operation",
                ],
            },
            {
                "name": "row_match",
                # "ignore_index_table": True,
                "first_cell": False,
                "row_pattern": [
                    r"involve no significant use of water.*?not disclosed",
                    r"does not encounter any issues in sourcing water",
                    r"no issues? related to sourcing water",
                    r"no issues? in (sourcing.*?water|water.*?sourcing)",
                    r"reusing wastewater and rainwater",
                ],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": ["__regex__KPI A2.4"],
                "only_inject_features": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (r"water consumption.*?not available",),
                },
                "table_model": "empty",
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"(?P<content>not encounter|not deal with|not face|no issue|sourcing water)",
                    r"(?P<content>municipal water|ground water|sourcing water|water sourcing)",
                    r"(?P<content>not have problems.*?water resources)",
                    r"did not have problems with shortage of water supply",
                    r"use of water is not material",
                ),
            },
            {
                "name": "para_match",
                "multi_elements": True,
                "paragraph_pattern": (r"has not identified any issues in sourcing|water that is fit for purpose",),
            },
            {
                "name": "kmeans_classification",
                "para_pattern": [
                    r"water source|sources water|sourced water",
                    r"water supply",
                ],
            },
            DEFAULT_MODEL,
        ],
    },
    {
        # A2涉及到的规则都是关于资源使用效率相关的内容；A2.4-part2需要框选出公司为节水而设置的节水目标，或者为了节水而采取的措施；
        # 如果都没有也可以框选已经实现的节水目标，可以不是对未来目标的设置。
        #  所在位置：1. 涉及到use of resources的标题下关于water efficiency的文字段落
        #           2. 在use of resources的标题下有关键词water management/ water usage的小标题下的内容
        #           3. 在ESG报告中单独有个environmental target的标题下，关键词water的相关内容
        #
        #  提取内容：1. 提取涉及到“water efficiency/saving target”的数据
        #           2. 没数据但有“为了water conservation/saving 而有一些measures”的描述
        #           3. 当年或目前为止已经实现的water efficiency target，可以不是未来的target
        #           4. 如果上面几点都有，则可以按照上面序号的优先级来全部提取
        #
        #  关键词：（需要在water相关位置范围内查找）target ,conserve water, water-efficient, water scarcity issue, establish,
        #  goal, aim, future plan,water conservation, reduction, prevent, decreased, avoid
        #
        #  判定方式：Y-披露并描述以上的提取内容（满足提取内容的任何一条或多条即可）
        #           E-明确披露了 not material/not involve/not engage/ not disclosed/ not produce的
        #           E-写明了`没有establish water efficiency target`的，即使有相关措施，也判定为E
        #           ND-没有任何描述
        "path": ["KPI A2.4 part 2 - water efficiency targets"],
        "models": [
            {
                "name": "row_match",
                # "ignore_index_table": True,
                "first_cell": False,
                "row_pattern": [
                    r"water Conservation",
                    r"conserving water resource",
                    r"involve no significant use of water.*?not disclosed",
                    r"does not encounter any issues in sourcing water",
                ],
            },
            {
                "name": "para_match",
                "anchor_regs": (r"water.*?target",),
                "paragraph_pattern": (r"To reduce the water",),
            },
            {
                "name": "syllabus_elt_v2",
                "only_inject_features": True,
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "inject_syllabus_features": [
                    r"__regex__sustainability target",
                    r"__regex__use of resource__regex__(water management|water usage)",
                    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/878#note_241211
                    r"__regex__waste management__regex__water usage",
                ],
            },
            {
                "name": "after_row_match",
                "row_pattern": [r"A2.4"],
                "multi_elements": True,
                "para_pattern": [
                    r"not considered a material aspect",
                    r"(?P<content>water conservation)",
                    r"(?P<content>conserving clean water)",
                    r"(?P<content>saving water|save water)",
                    r"(?P<content>data on water consumption is not available)",
                    r"(?P<content>saving.*?methods)",
                    r"(?P<content>saving.*?instruments)",
                    r"covering from.*?water.*?:$",
                    r"Clean water dispensers",
                    r"\swater recycling",
                    r"build drainage ditches",
                    r"wastewater generated by clean",
                    r"collect\s.*?water",
                    r"single-sided paper",
                    r"reduce wastage",
                    r"water-saving",
                    r"reduce the loss of water resources",
                ],
                "direct_answer_pattern": [
                    r"No issues in sourcing water",
                    r"No.*?target is set",
                    r"not set.*?targets",
                    r"not identified",
                    r"Defined to be irrelevant to the Group’s operation",
                ],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__Water Usage",
                    r"__regex__water.*?targets",
                    r"__regex__KPI A2.4",
                    r"__regex__use of resource__regex__water",
                    r"__regex__environmental__regex__water",
                    r"__regex__sustainability target",
                    r"__regex__Use of Resources",
                    r"__regex__EMISSION",
                ],
                "only_inject_features": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"water.*?follows.*?sources:$",
                        r"water consumption.*?not available",
                        r"Waste management.*?not been disclosed",
                        r"set a reduction target to reduce the water",
                        r"in order to reduce.*?water",
                        r"(reuse|recycling).*?water",
                        r"(saving|save|conserving) water",
                        r"water efficiency",
                        r"water Conservation",
                        r"target is.*?water",
                        r"plan is to reduce.*?water",
                        r"target.*?reduc.*?water",
                        r"achieve.*?recycling rate",
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "para_match",
                "multi_elements": True,
                "enum_from_multi_element": True,
                "neglect_pattern": (r"Water Consumption by Facility Locations",),
                "paragraph_pattern": (
                    r"(?P<content>not encounter|not deal with|not face|no issue|sourcing water)",
                    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/878#note_239988
                    r"(?P<content>municipal water|ground water|sourcing water|water sourcing|water\s+consumption)",
                    r"not considered a material aspect",
                    r"(?P<content>water conservation)",
                    r"(?P<content>conserving clean water)",
                    r"(?P<content>saving water|save water)",
                    r"(?P<content>data on water consumption is not available)",
                    r"(?P<content>saving.*?methods)",
                    r"(?P<content>saving.*?instruments)",
                    r"(?P<content>water.*?target)",
                ),
            },
            DEFAULT_MODEL,
        ],
    },
    {
        # 需要定位披露包装物材料的消耗量，通常为图表形式，也有很多报告涉及否定的文字描述
        # 出现位置：1. 报告末尾的performance data summary,KPI summary, performance table
        #          2. environment章节的末尾environmental data
        #          3. use of resource标题下packaging material 小标题下的表格
        #          4. 这条规则有时描述在index的最后一列位置（有时候为not material或者N/A，或者一句否定描述）
        #          5. 位置不固定，但关键词比较固定，可以通过packaging关键词来定位
        "path": ["KPI A2.5 - packaging material"],
        "models": [
            {
                "name": "middle_paras",
                "use_syllabus_model": True,
                "include_top_anchor": False,
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "inject_syllabus_features": [
                    r"__regex__pack(?:ag)?ing material",
                ],
                "top_anchor_regs": [
                    r"pack(?:ag)?ing material.*as (below|follow(ing)?)",
                ],
                "bottom_anchor_regs": [
                    r"Notes?.*?pack(?:ag)?ing material",
                ],
            },
            {
                "name": "row_match",
                # "ignore_index_table": True,
                "row_pattern": [
                    r"not directly consume any packaging materials",
                    r"did not involve consumption of packaging materials",
                    r"No packaging materials was used",
                    r"any relevant indicators for the packaging of products",
                    r"A2.5.*?commercially sensitive information",
                    r"Packaging materials consumption",
                    # r'Packaging cartons',
                ],
            },
            {
                "name": "after_row_match",
                "row_pattern": [r"A2.5"],
                "multi_elements": True,
                "para_pattern": [
                    r"not produce.*?utilize packaging materials",
                ],
                "index_header_patterns": [
                    r"\bKPI\b",
                ],
                "dst_row_patterns": [
                    r"packaging material",
                    r"packing material",
                    r"Intensity",
                ],
                # 拆分索引单元格内容
                "split_cell_pattern": r"－|—|:|：|\d+\.(?:\d+)?",
                "direct_answer_pattern": [
                    r"no packaging materials involved",
                ],
            },
            {
                "name": "middle_rows",
                "neglect_title_regs": [
                    r"\d+\.HK",
                ],
                "ignore_index_table": True,
                "regs": [
                    r"packaging material",
                    r"packing material",
                    r"Package Material",
                ],
                "start_regs": [
                    r"packaging material",
                    r"packing material",
                    r"Package Material",
                ],
                "end_regs": [
                    r"Air emissions",
                ],
            },
            {
                "name": "syllabus_based",
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__Greenhouse gas emission",
                ],
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (r"The Company consumes very few packing materials",),
                    "use_direct_elements": True,
                },
                "table_model": "empty",
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    "__regex__KPI A2.5",
                    "__regex__Packaging",
                ],
                "only_inject_features": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"pack(?:ag)?.*?not material",
                        r"total pack(?:ag)?ing material used",
                        r"The Group.*?setting up comprehensive pack(?:ag)?ing materials.*?system",
                        r"No significant raw material or packaging material waste",
                    ),
                },
                "table_model": "first_table",
                "table_config": {"regs": [r"pack(?:ag)?ing material"], "title_regs": [r"pack(?:ag)?ing material"]},
            },
            {
                "name": "para_match",
                "multi_elements": True,
                "enum_from_multi_element": True,
                "paragraph_pattern": (
                    # 'not involve|not produce|not applicable|non-material|not disclosed',
                    r"pack(?:ag)?ing material.*\d",
                    r"none?-material",
                ),
            },
            # DEFAULT_MODEL,
        ],
    },
    {
        # A3&A3.1 这两个规则的内容接近较难区分，A3侧重于总结性的政策，A3.1侧重于实际行动。
        # 在实际提取中描述涉及的关键词类似较难分开，可以在这两处提取相同的内容，这两规则的提取和判断规则是一致的。
        # 提取内容：
        # 【涉及natural resources，environment 政策的段落】
        # 在有包含“natural resources+environment”标题时，
        #       可提取该标题下的全部内容及environment相关标题下reduce+impact或impact+environment的段落。
        # 在有index的情况下，去index指明的位置提取会更准确
        # 当没有“natural resources+environment”相关的标题时，
        #        提取A1POLICY+A2POLICY的内容,及environment相关标题下reduce+impact或impact+environment的段落
        # E的判断：
        # 【简单情况】：not disclosed,not available, NA,
        # 【复杂情况】：not+impact，little significance+impact， not involve, not produce等 ，且没有任何政策措施相关的描述
        # Y的判断：
        # 有：natural resources，environment等相关的policy或management或measure或reduce或control等措施的描述。
        # 【要注意】：当有“not+impact，little significance+impact， not involve, not produce”等的描述，
        # 但是同时又有政策措施相关的描述，仍判断Y而非E
        "path": ["A3 policies - environment and natural resources"],
        "models": [
            # 优先提取
            # {
            #     "name": "a3_policy",
            #     "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
            #     "only_inject_features": True,
            #     "inject_syllabus_features": [
            #         r'__regex__natural resources.*?environment',
            #         r'__regex__environment.*?natural resources',
            #     ],
            # },
            {
                "name": "after_row_match",
                "row_pattern": [r"A3"],
                "multi_elements": True,
                "para_pattern": [
                    r"(minimise|minimize).*?impacts",
                    r"environmental protection",
                    r"environmental sustainability",
                    r"minimise|(minimize|lower|minimising).*?(impacts|effect)",
                    r"(?P<content>not own.*?emissions)",
                    r"(?P<content>not cause any significant impact on)",
                    r"developed environmental management system",  # 63972
                    r"any non-compliance cases regarding emissions",  # 63972
                    r"understand our various environmental requirements",  # 63972
                    r"promoting green building",  # 63972
                ],
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": [r"^climate change$"] + NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__green building",  # file63972 63993
                    r"__regex__GREEN OPERATION",  # file63970
                ],
                "break_para_pattern": [
                    r"^Green design$",
                ],
            },
            {  # file64005 63998 63973特殊情况
                "name": "syllabus_elt_v2",
                "ignore_pattern": [r"climate change"] + NEGLECT_PAGE_HEADER_PATTERN,
                "multi": True,
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__protecting (the )?environment",
                    r"__regex__our environment",
                    r"__regex__^environment$",
                ],
                "break_para_pattern": [
                    r"^CLIMATE CHANGE",
                ],
                "neglect_parent_features": [
                    r"^SUSTAINABILITY AT A GLANCE$",
                    r"^Sustainability Foundation$",
                ],
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": [r"^climate change$"] + NEGLECT_PAGE_HEADER_PATTERN,
                "multi": True,
                "only_before_first_chapter": True,
                "inject_syllabus_features": [
                    r"__regex__natural resources+environment",
                    r"__regex__ENVIRONMENTAL.*?REPORT__regex__ENVIRONMENTAL__regex__Environmental Policies",
                    r"__regex__A[.]?3",
                    r"__regex__Environment.*?Resources",
                    r"__regex__use of resources",
                    r"__regex__Ecological Protection in Operating Regions",
                    r"__regex__environment management",
                    r"__regex__Energy Conservation and Consumption Reduction",
                    r"__regex__Operational Eco-efficiency",  # file64001
                    r"__regex__ENVIRONMENTAL MANAGEMENT APPROACH",  # file63972
                ],
            },
            {
                "name": "para_match",
                "force_use_all_elements": True,
                "paragraph_pattern": (r"(?P<content>focus(ing)? on(?:(?!\.).)* impact on environment)",),
            },
            {
                "name": "para_match",
                "syllabus_regs": [
                    r"Environment|Resources",
                ],
                "paragraph_pattern": (
                    r"(?P<content>focus(ing)? on(?:(?!\.).)* impact on environment)",
                    r"(?P<content>not direct impact|not disclosed|of little significance|not involve|not produce|not applicable|non-material|not disclosed)",
                    r"(?P<content>not own.*?emissions)",
                    r"(?P<content>not cause any significant impact on)",
                    r"(?P<content>minimise|(minimize|lower|minimising).*?(impacts|effect)|environmental protection|environmental sustainability)",
                ),
            },
            DEFAULT_MODEL,
        ],
    },
    {
        "path": ["KPI A3.1 - impact on environment and natural resources"],
        "models": [
            {
                "name": "twice_row_match",
                "use_all_elements": True,
                "first_cell": False,
                "row_pattern": [r"A3\.1"],
                "second_pattern": EXPLAIN_PATTERN,
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": ["climate change"] + NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__A3\.1",
                    r"__regex__Impacts.*?Environment",
                    r"__regex__Environment.*?Resources",
                    r"__regex__use of resource",
                    r"__regex__green building",
                    r"__regex__Promoting Green Operation",
                ],
            },
            {
                "name": "after_row_match",
                "row_pattern": [r"A3"],
                "multi_elements": True,
                "para_pattern": [
                    r"(minimise|minimize).*?impacts",
                    r"environmental protection",
                    r"environmental sustainability",
                    r"minimise|(minimize|lower|minimising).*?(impacts|effect)",
                    r"(?P<content>not own.*?emissions)",
                    r"(?P<content>not cause any significant impact on)",
                ],
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "multi": True,
                "only_inject_features": True,
                "only_before_first_chapter": True,
                "inject_syllabus_features": [
                    r"__regex__natural resources+ environment",
                    r"__regex__ENVIRONMENTAL.*?REPORT__regex__ENVIRONMENTAL__regex__Environmental Policies",
                    r"__regex__A[.]?3",
                    r"__regex__Environment.*?Resources",
                    r"__regex__use of resources",
                    r"__regex__Ecological Protection in Operating Regions",
                    r"__regex__environment management",
                    r"__regex__Energy Conservation and Consumption Reduction",
                    r"__regex__KPI A3.1",
                ],
            },
            {  # file64005特殊情况
                "name": "syllabus_elt_v2",
                "ignore_pattern": [r"climate change"] + NEGLECT_PAGE_HEADER_PATTERN,
                "ignore_syllabus_pattern": [r"climate Change"],
                "multi": True,
                "inject_syllabus_features": [
                    r"__regex__protecting (the )?environment",
                ],
            },
            {
                "name": "para_match",
                "syllabus_regs": [
                    r"Environment|Resources",
                ],
                "paragraph_pattern": (
                    r"(?P<content>not direct impact|not disclosed|of little significance|not involve|not produce|not applicable|non-material|not disclosed)",
                    r"(?P<content>not own.*?emissions)",
                    r"(?P<content>not cause any significant impact on)",
                    r"(?P<content>minimise|(minimize|lower|minimising).*?(impacts|effect)|environmental protection|environmental sustainability)",
                ),
            },
            DEFAULT_MODEL,
        ],
    },
    {
        # A4 policies - climate-related issues
        # 提取内容：有关识别和缓解已影响和可能影响发行人的重大气候相关问题的政策
        # （侧重有没有去分析，或者识别，或者减缓等climate change 相关的政策或行动。）
        # 常见关键词：一般标题就有climate change, climate-related risks, responding to climate change；
        # 段落中也有climate change, carry on XXX analysis，mitigate xxx，take measures XXX。
        # 较多的是有没有相关的analysis
        # Y：只要有分析，或者识别，或者减缓等climate-related risks或者climate change的描述就是Y
        # （即使有类似“no significant impact“的描述）
        # E: 只披露了“没有进行climate change 相关的分析”，或者仅仅描述了“影响不大”但没有其他披露了
        "path": ["A4 policies - climate-related issues"],
        "models": [
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"formulated working mechanisms.*?climate change issues",
                    r"carries out the identification of climate change risks",  # file63981 多栏pdf的章节所辖段落识别错位
                    r"potential impacts of climate change on our Group",
                ),
            },
            {
                "name": "row_match",  # 未披露的情况
                "first_cell": False,
                "force_use_all_elements": True,
                "row_pattern": [
                    r"(A4(?![_\d\.])|climate-related|climate change).*not (an? )?(applicable|been disclosed|considered)",
                    r"(A4(?![_\d\.])|climate-related|climate change).*(?<![a-zA-Z])N/?A(?![a-zA-Z])",
                    r"(A4(?![_\d\.])|climate-related|climate change).*不適用",
                    r"(A4(?![_\d\.])|climate-related|climate change).*be disclosed in the future",
                    r"(A4(?![_\d\.])|climate-related|climate change).*not involve(?:(?!(\.)).)*recall",
                    r"(A4(?![_\d\.])|climate-related|climate change).*not material",
                ],
            },
            {  # 某些标题含`climate change`但子标题并不符合要求 https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/894#note_232219
                "name": "syllabus_elt_v2",
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__climate(-related)? risk",
                    r"__regex__Environmen?tal Risk Identif",  # 63965
                ],
                "multi": True,
                "include_title": True,
                "include_shape": True,
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN + [r"B.*?SOCIAL$"],
            },
            {
                "name": "syllabus_elt_v2",
                "multi": True,
                "include_title": True,
                "include_shape": True,
                "only_inject_features": True,
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN
                + [
                    r"B.*?SOCIAL$",
                    r"^[I]+\.",
                    r"[一二三四五六七八九十]+\、",
                ],
                "inject_syllabus_features": [
                    r"__regex__KPI A4(.1)?",
                    r"__regex__TCFD Report",
                    r"__regex__climate change",
                    r"__regex__The Group.s response",
                    r"__regex__Climate-related Risks?(, Opportunities, and Financial impact)?",
                    r"__regex__Physical risks",
                    r"__regex__Transition(?:al)? risks",
                    r"__regex__respond to climate change",
                    r"__regex__Tackling Climate Change",
                ],
                "neglect_patterns": [
                    r"Employment",
                ],
            },
            {
                "name": "shape_title",
                "force_use_all_elements": True,
                "regs": (
                    r"Transition Risks",
                    r"TCFD",
                    r"climate(-related)? risk",
                    r"combating climate change",  # file63981
                ),
            },
            {  # 较为精确的段落内容匹配
                "name": "para_match",
                "multi_elements": True,
                "enum_from_multi_element": True,
                "paragraph_pattern": (
                    r"(?P<content>climate change)",
                    r"(?P<content>(climate-related|Physical|Transition(?:al)?) risks)",
                ),
            },
            {  # file63949 特殊情况，披露在index表格中
                "name": "row_match",
                "first_cell": False,
                "force_use_all_elements": True,
                "row_pattern": [
                    r"(A4(?![_\d\.])|climate-related|climate change).*(identif|recogni)",
                ],
            },
            {  # ========== 所有手段失效，最宽泛的段落内容匹配 ==========
                "name": "para_match",
                "multi_elements": True,
                "enum_from_multi_element": True,
                "paragraph_pattern": (
                    r"(?P<content>not direct impact|not disclosed|of little significance|not involve|not produce|not applicable|non-material|not disclosed)",
                    r"(?P<content>identified|recognising|climate change|respond|climate)",
                    r"there are policies",
                ),
            },
            # DEFAULT_MODEL,
        ],
    },
    {
        # A4.1 - climate-related issues & impact:
        # 提取内容：描述已影响和可能影响发行人的重大气候相关问题，以及为管理这些问题而采取的措施。（侧重于发现了什么影响。）
        # A4跟A4.1定位框选时：
        #
        # i.若没有明显的标题区分可以直接框选climate change标题下方的内容;
        # ii.但若有明显的标题可以区分A4跟A4.1的内容时，需要分别框选。
        #
        # Y: 如果文档披露各种潜在可能的climate change造成的risk和likehood( 可能性），即使有类似“no significant impact“的描述，也是Y。
        # E: 没有披露可能的各种risk，仅描述了影响不大
        "path": ["KPI A4.1 - climate-related issues & impact"],
        "models": [
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"formulated working mechanisms.*?climate change issues",
                    r"carries out the identification of climate change risks",  # file63981
                    r"potential impacts of climate change on our Group",
                ),
            },
            {  # 某些标题含`climate change`但子标题并不符合要求  # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/894#note_232219
                "name": "syllabus_elt_v2",
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__climate(-related)? risk",
                    r"__regex__Environmen?tal Risk Identif",  # 63965
                ],
                "multi": True,
                "include_title": True,
                "include_shape": True,
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN + [r"B.*?SOCIAL$"],
            },
            {
                "name": "syllabus_elt_v2",
                "multi": True,
                "include_title": True,
                "include_shape": True,
                "only_inject_features": True,
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN
                + [
                    r"B.*?SOCIAL$",
                    r"[I]+\.",
                    r"[一二三四五六七八九十]+\、",
                ],
                "inject_syllabus_features": [
                    r"__regex__TCFD Report",  # file63997, TCFD: Task Force on Climate-Related Financial Disclosures
                    r"__regex__KPI A4.1",
                    r"__regex__climate change",
                    r"__regex__Climate-related Risks, Opportunities, and Financial impact",
                    r"__regex__Action on Climate Change",
                    r"__regex__The Group.s response",
                    r"__regex__Physical risks",
                    r"__regex__Transition(?:al)? risks",
                    r"__regex__respond to climate change",
                    r"__regex__Tackling Climate Change",
                ],
                "neglect_patterns": [
                    r"Employment",
                ],
            },
            {
                "name": "shape_title",
                "force_use_all_elements": True,
                "regs": (
                    r"Transition Risks",
                    r"TCFD",
                    r"climate(-related)? risk",
                    r"combating climate change",  # file63981
                ),
            },
            # -------- 当没有具体章节表示A4.1时，可能为A4 policy的E --------
            {
                "name": "row_match",
                "first_cell": False,
                "force_use_all_elements": True,
                "row_pattern": [
                    r"(A4(?![_\d\.])|climate-related|climate change).*not (an? )?(applicable|been disclosed|considered)",
                    r"(A4(?![_\d\.])|climate-related|climate change).*(?<![a-zA-Z])N/?A(?![a-zA-Z])",
                    r"(A4(?![_\d\.])|climate-related|climate change).*不適用",
                    r"(A4(?![_\d\.])|climate-related|climate change).*be disclosed in the future",
                    r"(A4(?![_\d\.])|climate-related|climate change).*not involve(?:(?!(\.)).)*recall",
                    r"(A4(?![_\d\.])|climate-related|climate change).*not material",
                ],
            },
            {  # 较为精确的段落内容匹配
                "name": "para_match",
                "multi_elements": True,
                "enum_from_multi_element": True,
                "paragraph_pattern": (
                    r"(?P<content>climate change)",
                    r"(?P<content>(climate-related|Physical|Transition(?:al)?) risks)",
                ),
            },
            {  # file63949 特殊情况，披露在index表格中
                "name": "row_match",
                "first_cell": False,
                "force_use_all_elements": True,
                "row_pattern": [
                    r"(A4\.1|climate-related|climate change).*(identif|recogni)",
                ],
            },
            # ========== 所有手段失效，最宽泛的段落内容匹配 ==========
            {
                "name": "para_match",
                "multi_elements": True,
                "enum_from_multi_element": True,
                "paragraph_pattern": (
                    r"(?P<content>not direct impact|not disclosed|of little significance|not involve|not produce|not applicable|non-material|not disclosed)",
                    r"(?P<content>identified|recognising|climate change|respond|climate)",
                    r"no significant climate-related issues",
                ),
            },
            # DEFAULT_MODEL,
        ],
    },
    {
        # 提取涉及到employment政策的所有段落。常见的描述是“公司遵守法律法规或者有相关的手册规定来保护员工的合法权益，不被歧视。同时员工也要遵守自己的职业道德”等。 一般在employment相关的标题下，
        # 可提取employment下的全部内容{明显能与后面细则内容区分的(例如laws相关及KPI相关的)，就不框入B1其他细则的内容}
        #
        # 常见关键词： employment, labour, workforce
        # 可从index给出的各个KPI的位置去定位框选；或在 Employment/ Employment Practices/Human Capital标题下
        "path": ["B1 policies - employment"],
        "models": [
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN + [r"continued$"],
                # "only_before_first_chapter": True,
                "multi": True,
                # "include_shape": True,
                "inject_syllabus_features": [
                    r"__regex__B[.]1",
                    r"__regex__Employment__regex__General Disclosure and KPIs",
                    r"__regex__\d.*?employment$",
                    r"__regex__Employment and Labor Practices",
                    r"__regex__Employee.*?(Right|Interest|policy)",
                    r"__regex__Protection of Employees.*?Rights",
                    r"__regex__Employee Recruitment",
                    r"__regex__Diversified Talent Attraction Policies",
                    r"__regex__Human.*?Labour Rights",
                    r"__regex__Improve employee wellbeing",
                    r"__regex__BUILDING A DIVERSE TEAM",
                    r"__regex__Create a diverse and inclusive workplace",
                    r"__regex__STAFF|COMPENSATION|BENEFITS|COMMUNICATION",
                    r"__regex__Diverse and Fair Recruitment",
                    r"__regex__Promotion and Remuneration",
                    r"__regex__Supporting Health and Wellness",
                    r"__regex__Personnel management",
                    r"__regex__Equal and compliant employment",
                    r"__regex__Diversified talent reserve",
                    r"__regex__Building a diversified team",
                    r"__regex__Well-being and Creative Activities",
                    r"__regex__Staff Relations",
                    r"__regex__Equal and legal employment",
                    r"__regex__Talent Attraction and Recruitment",
                    r"__regex__Broadening Channels for Introducing Professionals",
                    r"__regex__Diversity and Equal Opportunity",
                    r"__regex__TALENT ACQUISITION AND RETENTION",
                ],
                "neglect_parent_features": [
                    r"STAFF RECOGNITION",
                    r"ENGAGING OUR STAFF",
                    r"Staff Movement",
                    r"Staff Communication",
                    r"STAFF CARING",
                ],
                "break_para_pattern": [
                    r"I Am the Speaker",
                    # r'program documents involving the vital interests of employees',
                ],
            },
            DEFAULT_MODEL,
        ],
    },
    {
        # Employment -Compliance with relevant laws and regulations that have a significant impact on the issuer
        # 遵守对发行人有重大影响的法律法规
        "path": ["B1 law compliance - employment"],
        "location_threshold": 0.01,
        "models": [
            # ----- 精确匹配 start -----
            {
                "name": "twice_row_match",
                "first_cell": False,
                "row_pattern": COMPLY_LAW_PATTERN,
                "merge_valid_rows": False,
                "neglect_syllabus_regs": [
                    r"Labou?r Standards?",
                    r"B[2-8]|A[1-3]",
                ],
                "second_pattern": [
                    r"B1(\s+|$|[a-zA-Z])",  # file63997披露在index表格中
                ],
            },
            {  # file63974 63979
                "name": "syllabus_based",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                # 'only_before_first_chapter': True,
                # 'multi': True,  # file63979
                # 'skip_child_syllabuses': False,  # file63979
                "only_inject_features": True,
                "break_para_pattern": [r"B1\.1"],
                "inject_syllabus_features": [
                    r"__regex__Modern SlaveryA ct Transparency Statement",  # file64000
                    r"__regex__B1(\.|\s+|$)",
                    r"__regex__Compliance Management",
                    r"__regex__Human Capital",
                    r"__regex__Employment Practices",
                    # r"__regex__Employee(s’)? Right",  # file63959 63954
                    r"__regex__COMPLIANCE OPERATIONS",  # file 63954
                ],
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": COMPLY_LAW_PATTERN,
                    "neglect_pattern": (r"carries out internal audit projects in accordance with laws and procedures",),
                },
                "table_model": "empty",
            },
            # ===== 精确匹配 end =====
            {
                "name": "twice_para_match",
                "multi_elements": True,
                "use_all_elements": True,
                "syllabus_regs": [],
                "neglect_syllabus_regs": [
                    r"Labou?r Standards?",
                    r"B[2-8]|A[1-3]",
                ],
                "paragraph_pattern": COMPLY_LAW_PATTERN,
                "neglect_pattern": (
                    r"^[a-zA-Z]\.",
                    r"^•",
                    r"(child|forced?) labou?r",  # 与B4-law区分开
                ),
                "second_pattern": (
                    r"employment",
                    r"labour",
                    r"workforce",
                    r"dismissal",
                    r"recruitment",
                    r"promotion",
                    r"working hours",
                    r"rest periods",
                    r"equal opportunity",
                    r"diversity",
                    r"anti-discrimination",
                    r"other benefits",
                    r"other welfare",
                ),
            },
            {
                "name": "shape_title",
                "force_use_all_elements": True,
                "regs": (r"Diversified Talent Attraction Policies",),  # file64003针对infographics的特殊规则
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__Compliance Management",
                    r"__regex__Human Capital",
                    r"__regex__Employment Practices",
                    r"__regex__employment",
                ],
                "paragraph_model": "para_match",
                "para_config": {"paragraph_pattern": COMPLY_LAW_PATTERN},
                "table_model": "row_match",
                "table_config": {
                    "row_pattern": [r"Employment"],
                },
            },
            {
                "name": "kmeans_classification",
                "threshold": 0.618,
                "para_pattern": [
                    r"(comply|not violate|abide).*?employment.*?(law|regulation)"
                    r"no material non-compliance with|are compliant with",
                    r"complied with all relevant laws",
                    r"complies with the relevant laws and regulations.*?employment",
                    r"(?P<content>non-compliance|employment|ordinance|legal|law|regulation)",
                ],
            },
        ],
    },
    {
        #  提取内容：涉员工总数按性别、雇佣类型、年龄和区域划分的图表或段落或句子。
        #  一般都是按照各类员工的数量及比例披露的。
        #  关键词是 B1.1, by gender, employment type，number of，age ，geographical，resign，resigned，resignation
        #  可从index给出的各个KPI的位置去定位；或在 Employment/ Employment Practices/Human Capital
        #  特殊情况：若仅有员工总数的合计数，没有各个分类下的数据，B1.1提取合计数，判断为Y
        "path": ["KPI B1.1 - workforce by types"],
        "models": [
            # {
            #     "name": "after_row_match",
            #     'row_pattern': [
            #         r'B1.1',
            #         r'Workforce',
            #     ],
            #     'para_pattern': [
            #         r'B1.1|by gender|employment type|number of|age|geographical|resign|resigned|resignation'
            #     ],
            # },
            {
                "name": "special_cells",
                "whole_table": True,
                # "multi_elements": True,
                # "just_first_row": True,
                "cell_pattern": (r"Employee structure", r"New recruitment"),
            },
            {
                "name": "shape_title",
                "multi_elements": True,
                "regs": (
                    r"Employee.*?distribution",
                    r"Employees by.*",
                    r"Age distribution",
                    r"Age group",
                    r"^gender$",
                    r"(Employment|Employee) Category",
                    r"本集團員工團隊人數統計資料概述如下",
                    r"僱員明細載列如下",
                    r"僱員分佈詳情如下",
                    r"僱員總數",
                    r"workforce by",
                    r"by (age|gender)",
                ),
                "neglect_regs": (r"turnover",),
            },
            {
                "name": "middle_rows",
                "multi_elements": True,  # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/971#note_240609
                "start_regs": [
                    r"Workforce 員工Total Headcount 員工總人數 By Geographical Distribution",
                    r"By Education Level 按教育程度劃分 Bachelor Degree or Above 學士學位或以上",
                ],
            },
            {
                "name": "middle_rows",
                # "multi_elements": True,
                "regs": [
                    r"workforce distribution",
                    r"workforce as",
                    r"by type",
                    r"male",
                    r"All staff",
                    r"by.*?(gender|age|location)",
                    r"B1.1",
                ],
                "start_regs": [
                    r"Employee breakdown",
                    r"number of employees",
                    r"workforce",
                    r"All staff",
                ],
                "end_regs": [
                    r"Employee turnover rate",
                    r"Development and training",
                    r"Woman Representation",
                    r"Average hours of training",
                    r"51 and above",  # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/971#note_240608
                    r"Staff Turnover Rate",
                ],
            },
            {
                "name": "table_title",
                "multi_elements": True,
                "only_inject_features": True,
                "feature_white_list": [
                    r"^Diversity$",
                    r"Summary of Employment Performance Indicators",
                    r"Employee.*?distribution",
                    r"Age distribution",
                    r"Age group",
                    r"gender",
                    r"Employment Category",
                    r"employee category",
                    r"workforce by",
                    r"本集團員工團隊人數統計資料概述如下",
                    r"僱員分佈詳情如下",
                    r"僱員總數",
                ],
            },
            {
                "name": "row_match",
                "ignore_index_table": True,
                "multi_elements": True,
                "row_pattern": [
                    r"distribution of employees",
                ],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__STRINGENT RECRUITMENT PROCESS",
                    r"__regex__Employment",
                    r"__regex__Employment Practices",
                    r"__regex__Human Capital",
                ],
                "multi_elements": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"B1.1|by gender|employment type|number of|geographical|resign|resigned|resignation",
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"(?P<content>B1.1|by gender|employment type|number of|age|geographical|resign|resigned|resignation)",
                ),
            },
            DEFAULT_MODEL,
        ],
    },
    {
        # 提取内容：涉员工turnover rate按性别、年龄和区域划分的数据所在的图表或段落或句子。
        # 常见的关键词：B1.2, turnover, by gender, age , geographical, employment type，leave，leaver，resign，
        # resigned，resignation，drop in
        #        可从index给出的各个KPI的位置去定位框选；或在 Employment/ Employment Practices/Human Capital标题下
        # 特殊情况：
        #     1. 仅有overall employees的turnover rate，没有披露按照gender, age group and geographical region分类下的
        #     turnover rate，B1.2属于Y
        #
        #     2. B1.2 只披露的离职人数，没有turnover离职了，属于Y
        "path": ["KPI B1.2 - employee turnover by types"],
        "models": [
            {
                "name": "after_row_match",
                "row_pattern": [r"B1.2"],
                "para_pattern": [r"B1.2|turnover"],
                "middle_rows": True,
                "multi_elements": True,
                "answer_both_syllabus_and_table": True,
                # "answer_table_first_row_as_title": True,
                "direct_answer_pattern": [r"Pending setting up"],
                "table_title": [
                    r"流失率",
                    r"Turnover Rate",
                    # r'^Turnover$',
                ],
                "start_regs": [r"Turnover Rate"],
                "end_regs": [
                    r"Training|Trained",
                    r"B2\.1",
                    r"Work-related fatality and injury",
                ],
            },
            {
                "name": "middle_rows",
                "ignore_index_table": True,
                "regs": [
                    r"B1\.2",
                ],
                "start_regs": [
                    r"Turnover Rate",
                    r"Employee turnover",
                    r"^Turnover$",
                ],
                "end_regs": [
                    r"Training|Trained",
                    r"Health and Safety",
                    r"B2\.1",
                    r"Work-related fatality and injury",
                ],
            },
            {
                "name": "table_title",
                "multi_elements": True,
                "only_inject_features": True,
                "feature_white_list": [
                    r"本集團僱員流失率概述如下",
                    r"turnover rates",
                    r"employee Structure and Turnover",
                ],
            },
            {
                "name": "shape_title",
                "multi_elements": True,
                "regs": (
                    r"Employee Turnover",
                    r"Geographical Region",
                ),
            },
            {
                "name": "middle_rows",
                "regs": [r"turnover"],
                "title_regs": [r"turnover"],
                "start_regs": [r"turnover rate"],
                "end_regs": [
                    r"Percentage of employees trained",
                    r"training",
                ],
            },
            {
                "name": "table_title",
                "only_inject_features": True,
                "feature_white_list": [
                    r"turnover",
                ],
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"(?P<content>not disclosed|sensitive)",
                    r"full-time employee turnover rate",
                    r"employee turnover rate for male and female",
                    r"average turnover rate by",
                    r"commercially sensitive",
                ),
            },
            # DEFAULT_MODEL,
        ],
    },
    {
        # B2涉及到的规则都是关于health and safety相关的内容，policy一般都为公司制定了安全管理政策的相关概括性描述；
        #  所在位置：1. 涉及到health and safety的大标题下（一般位置比较固定，都在这个章节下定位）
        #           2. 如果大标题分了小标题的情况，policy一般可以提取大标题和小标题中间的文段
        #           3. 小标题有policy/policies的关键词的，可以直接提取对应小标题下的内容
        #
        #  提取内容：1. 常见描述“集团重视员工健康和安全，以保护员工免受职业危害，按照适用的法律法规制定了安全管理政策。”等
        #           2. 如果大标题下有小标题，可以提取大标题和小标题中间的文段
        #           3. 对于篇幅较小的或没有按照小标题披露的，可以提取标题下全部内容
        #
        #  关键词：health, healthy and safe, working environment, established, safety management strategy,
        #  reduce potential workplace hazards, hazard-free working environment
        #
        #  判定方式：Y-披露并描述以上的提取内容（满足提取内容的任何一条或多条即可）
        #           E- 不常见，但可能会有些金融行业或者与员工劳动伤害很远的行业，在此标题下会有 not material之类的描述
        #           ND-没有任何相关描述
        "path": ["B2 policies - health and safety"],
        "models": [
            # {
            #     # 章节标题和第一个小标题之间
            #     "name": "policy",
            #     "only_inject_features": True,
            #     "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
            #     "ignore_pattern_match": True,
            #     'inject_syllabus_features': [
            #         r'__regex__Health and Safety',
            #     ],
            #     'neglect_features': [r'Product and Service Health and Safety'],
            # },
            {
                "name": "after_row_match",
                "row_pattern": [r"B2"],
                "just_a_para": True,
                "para_pattern": [
                    r"protect.*?health",
                    r"(?P<content>health|Safety|workplace|fire)",
                ],
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "multi": True,
                "only_inject_features": True,
                "include_shape": True,
                "inject_syllabus_features": [
                    r"__regex__Pay Attention to Occupational Health",
                    r"__regex__Maintain the Safety System",
                ],
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_before_first_chapter": True,
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__Health and Safety__regex__General Disclosure and KPI",
                ],
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "inject_syllabus_features": [
                    r"__regex__(?<!CUSTOMER )Healthy? and Safety",  # (?<!CUSTOMER) file63972
                    r"__regex__(?<!CUSTOMER )HEALTH AND WELL-BEING",  # file63983
                    r"__regex__(?<!CUSTOMER )Health and Safety__regex__General Disclosure and KPI",
                    r"__regex__employee.*health",
                    r"__regex__Occupational Health and Safety",
                ],
                "neglect_features": [
                    r"Safety Training",
                ],
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"(?P<content>health|Safety|workplace|fire)",
                    r"(?P<content>protect.*?health)",
                ),
            },
            DEFAULT_MODEL,
        ],
    },
    {
        "path": ["B2 law compliance - health and safety"],
        "models": [
            # ----- 精确匹配 start -----
            {
                "name": "twice_row_match",
                "first_cell": False,
                "row_pattern": COMPLY_LAW_PATTERN,
                "merge_valid_rows": False,
                "second_pattern": [
                    r"B2(\s+|$|[a-zA-Z])",  # file63997披露在index表格中
                ],
            },
            {  # file63974 63979
                "name": "syllabus_based",
                "only_inject_features": True,
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_before_first_chapter": True,
                "break_para_pattern": [r"B2\.1"],
                "inject_syllabus_features": [
                    r"__regex__Employees? Health and Safety",
                ],
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": COMPLY_LAW_PATTERN,
                },
                "table_model": "empty",
            },
            # ===== 精确匹配 end =====
            {
                "name": "twice_para_match",
                "multi_elements": True,
                "use_all_elements": True,
                "syllabus_regs": [],
                "neglect_syllabus_regs": [
                    r"Product Health and Safety",
                    r"production",
                    r"Labou?r Standards?",
                    r"B1|B[3-8]|A[1-3]",
                ],
                "paragraph_pattern": COMPLY_LAW_PATTERN,
                "neglect_pattern": (r"^[a-zA-Z]\.", r"^•", r"relat(?:(?!\.).)* to (products?|services?)"),
                "second_pattern": (
                    r"health",
                    r"Safety",
                    r"workplace",
                    r"working environment",
                    r"safe working environment",
                    r"protecting employees?",
                ),
            },
            {
                "name": "twice_row_match",
                "first_cell": False,
                "row_pattern": COMPLY_LAW_PATTERN,
                "merge_valid_rows": False,
                "second_pattern": [
                    r"B2(\s+|$)",
                    r"health",
                    r"Safety",
                    r"workplace",
                    r"safe working environment",
                    r"protecting employees",
                ],
            },
            {
                "name": "syllabus_based",
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__Compliance Management",
                ],
                "paragraph_model": "empty",
                "table_model": "row_match",
                "table_config": {
                    "row_pattern": [r"health and safety"],
                },
            },
            # ----- 宽泛规则 start -----
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_before_first_chapter": True,
                "only_first": True,
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"Health and Safety",
                ],
            },
            DEFAULT_MODEL,
            # ===== 宽泛规则 end =====
        ],
    },
    {
        # 此条规则是披露报告年度在内的过去三年中每年发生的与工作有关的死亡人数和发生率。
        # 提取内容：提取fatality相关的数据或描述所在的图表或段落或句子，nil或no或not判断为Y
        # 常见关键词：fatalities, fatal accidents, death，B2.1
        #         可从index给出的各个KPI的位置去定位框选；一般在标题Health and Safety下
        #    当无上述关键词，但是有“no work safety-related incident或no work safety-related accident，
        #    或no work injury”的描述，需要提取该描述判断为Y
        "path": ["KPI B2.1 - work-related fatalities"],
        "models": [
            {
                "name": "after_row_match",
                "row_pattern": [r"B2.1|B-2.1"],
                "just_a_para": True,
                "filter_content_answer": True,
                "para_pattern": [
                    # r'work[\-\s]related fatalities',
                    r"work-related fatalities in the past",
                    r"did not have any work-related fatalities",
                    r"not have any violation.*?work-related",
                    r"(?P<content>no work safety-related incident|no work safety-related accident|no work injury)",
                    r"There were no work-related fatalities occurred",
                    r"been injured or died due to work",
                ],
                "middle_rows": True,
                "table_title": [
                    r"職業健康",
                    r"安全表現",
                ],
                "start_regs": [r"B2\.1"],
                "end_regs": [r"B2\.2"],
                "direct_answer_pattern": [
                    r"work-related (deaths|fatalities)",
                ],
            },
            {
                "name": "middle_rows",
                "ignore_index_table": True,
                "regs": [
                    r"work-related",
                ],
                "start_regs": [
                    r"B2\.1",
                ],
                "end_regs": [
                    r"B2\.2",
                ],
            },
            {
                "name": "row_match",
                "ignore_index_table": True,
                "multi": True,
                "row_pattern": [
                    r"work-related deaths",
                    r"B2\.1",
                    r"Fatalit 死亡個",
                    r"Rate of work-related fatalities",
                    r"Number and rate of fatality caused by work-related",
                    r"Number of Work-\s?Related Fatality and Percentage",
                    r"Number of work-related fatalities",
                    r"^Fatalities",
                    r"^Fatality rate",
                ],
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"no fatality cases related to our employees occurred",
                    r"did not have any work-related fatalities",
                    r"protect.*?health",
                    r"not.*?violation",
                    r"incidents of workplace accidents",
                    r"(?P<content>work related|fatalities|fatal accidents|occupational|death|B2.1)",
                    r"(?P<content>no work safety-related incident|no work safety-related accident|no work injury)",
                    r"(?P<content>work-related fatalit)",
                    r"been injured or died due to work",
                ),
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [],
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "multi_elements": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"(?P<content>work related|fatalities|fatal accidents|occupational|death|B2.1)",
                        r"(?P<content>no work safety-related incident|no work safety-related accident|no work injury)",
                    ),
                },
                "table_model": "empty",
            },
            # DEFAULT_MODEL,
        ],
    },
    {
        # 提取内容：提取work injury+Lost相关的数据或描述所在的图表或段落或句子，nil或no或not判断为Y
        # 常见关键词：work related/lost days/sickness/work injury/accidents+ lost days,B2.2。
        #        可从index给出的各个KPI的位置去定位框选；一般在标题Health and Safety下
        #    当无上述关键词，但是有“no work safety-related incident或
        #    no work safety-related accident，或no work injury”的描述，需要提取该描述判断为Y
        "path": ["KPI B2.2 - work injury lost days"],
        "neglect_table_title_missing_crude_regs": [
            r"Employee work-related fatalities over the past three years",
        ],
        "models": [
            {
                "name": "after_row_match",
                "row_pattern": [r"B2.2"],
                "just_a_para": True,
                "para_pattern": [
                    r"injuries",
                    r"working-related",
                    r"(?P<content>work[\-\s]related|sickness|work injury|accidents|occupational|B2.2)",
                    r"(?P<content>no work safety-related incident|no work safety-related accident|no work injury)",
                    r"(Lost|days).*?(work related|lost days|sickness|work injury|accidents)",
                    r"no production accident",
                ],
                "second_pattern": [
                    r"lost days",
                    r"lost working days",
                    r"(had|was|were|is) (zero|\d+|no)",
                    r"no (work-related fatalities|lost day)",
                    r"no incident of work-related fatality",
                    r"no working day lost",
                    r"no production accident",
                ],
                "middle_rows": True,
                "table_title": [],
                "start_regs": [r"B2\.2"],
                "end_regs": [r"B2\.3"],
                "direct_answer_pattern": [
                    r"loss.*?work-related injur",
                    r"(had|was|were|is) (zero|\d+|no)",
                    r"no (work-related fatalities|lost day)",
                    r"no incident of work-related fatality",
                    r"no working day lost",
                    r"no production accident",
                ],
            },
            {
                "name": "row_match",
                "multi": True,
                "ignore_index_table": True,
                "row_pattern": [
                    r"Lost Days?.*?Work(-related)?\s?(Injury|injuries)",
                    r"Lost Days? due to Injury",
                    r"No\. of Injuries at work",
                    r"Workdays? lost.*?(work-related|fatalities)",
                    r"B2\.2",
                    r"work day lost due to work-related injuries",
                ],
            },
            {
                "name": "twice_para_match",
                "multi_elements": True,
                # "strict_limit": True,
                "syllabus_regs": [],
                "neglect_syllabus_regs": [],
                "paragraph_pattern": (
                    r"(?P<content>work related|lost days|sickness|work injury|accidents|occupational|B2.2)",
                    r"(?P<content>no work safety-related incident|no work safety-related accident|no work injury)",
                    r"(Lost|days).*?(work related|lost days|sickness|work injury|accidents)",
                    r"no production accident",
                ),
                # 'neglect_pattern': (
                # ),
                "second_pattern": (
                    r"lost (labor )days",
                    r"was (zero|\d+)",
                    r"nor? (any )?(work-related fatalities|l[ao]st day)",
                    r"no production accident",
                ),
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"incidents of work injuries arose",
                    r"(Lost|days).*?(work related|lost days|sickness|work injury|accidents)",
                    r"no production accident",
                ),
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__Accident Rate Analysis",
                    r"__regex__Employee Development",
                ],
                "multi_elements": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"(?P<content>work related|lost days|sickness|work injury|accidents|B2.2)",
                        r"(?P<content>no work safety-related incident|no work safety-related accident|no work injury)",
                        r"LTIFR",
                        r"no production accident",
                    ),
                },
                "table_model": "empty",
            },
        ],
    },
    {
        # B2涉及到的规则都是关于health and safety相关的内容，B2.3提取的是公司采用的职业健康和安全措施，以及如何实施和监测这些措施；
        #  所在位置：1. health and safety的大标题下（一般位置比较固定，都在这个章节下定位）
        #
        #  提取内容：1. 常见描述形式为一段话末尾加冒号，然后按条描述明细措施，需要提取这段话加下面的所有明细
        #           2. 有小标题的，提取小标题下面的所有文段；或者除了policy，law以及B2.1、B2.2数据描述以外的其他文段
        #           3. 单独存在关于新冠的措施的小标题下，包含COVID关键词的，提取小标题下的全部内容
        #           3. 对于篇幅较小的或没有按照小标题披露的，可以提取包含各类动词的文段，如 check，daily, provide, organize,
        #           required, regularly等
        #
        #  关键词：health, healthy and safe, occupational, healthy, safety, measures, training, check，daily, provide,
        #  organize, required, regularly, first-aid
        #
        #  判定方式：Y-披露并描述以上的提取内容（满足提取内容的任何一条或多条即可）
        #           E- 不常见，但可能会有些金融行业或者与员工劳动伤害很远的行业，在此标题下会有 not material之类的描述
        #           ND-没有任何相关描述
        "path": ["KPI B2.3 - health and safety measures"],
        "models": [
            {
                "name": "after_row_match",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "row_pattern": [r"B2.3"],
                "multi_elements": True,
                "multi_pages": True,
                "ignore_syllabus": [
                    r"^Health and Safety$",  # 忽略范围太大的标题
                ],
                "para_pattern": [
                    r"COVID-?19|preventive measures",
                    # r'facilities|safety performance',
                    r"adopted the following measures:$",
                    r"optimized the physical examination plan",
                    r"optimized.*?insurance",
                    r"provided.*?insurance",
                    r"formulated.*?emergencies",
                    r"monitors the exposure risks",
                    r"organised fire drills",
                    r"safety training",
                    r"safety monitoring mechanism",
                    r"strictly abided to relevant preventive",
                    r"conduct fire drills",
                    r"requires.*?suppliers.*?certified",
                    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/894#note_225686
                    r"physical and mental health of employees",
                ],
                "neglect_para_pattern": [
                    r"during the reporting period",
                    r"During the past.*?years",
                    r"safety performance:$",
                    r"lifelong learning",
                    r"Development and Training",
                ]
                + NEGLECT_PAGE_HEADER_PATTERN,
                "sub_syllabus": [
                    r"Normalized Management of the Pandemic",
                    r"Staff Health Protection",
                    r"Fire Safety Management",
                    r"Staff Communication and Care",
                ],
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "multi": True,
                "inject_syllabus_features": [
                    r"__regex__Fire Safety Management",
                    r"__regex__Preventative Measures on the Pandemic",
                    r"__regex__B2: Health and Safety",  # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/971#note_240676
                ],
            },
            {
                "name": "syllabus_elt_v2",
                # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/971#note_240680
                "ignore_pattern": [
                    "Property Development and Investment|Hospitality|Leisure",
                    *NEGLECT_PAGE_HEADER_PATTERN,
                ],
                "only_inject_features": True,
                "multi": True,
                "inject_syllabus_features": [
                    r"__regex__Health and Safety__regex__Monitoring System",
                    r"__regex__Health and Safety__regex__Safety Organisation",
                    r"__regex__Health and Safety (train|Measures)",
                    r"__regex__Precautions Against Covid-19",
                    r"__regex__(Prevent|Response).*?(of|for).*?Covid[-\s]?19",
                    r"__regex__Employee Health and (Care|safety)",
                    r"__regex__Occupational Hea\s?lth( and Safety)?",
                    r"__regex__Work Environment Safety",  # file63976
                    r"__regex__emp ?loyee safety",  # file63976
                    r"__regex__Safety management",  # file63976
                    r"__regex__Pandemic Prevention",  # file63976
                ],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__Health and Safety",
                    r"__regex__Health & Safety",
                    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/894#note_230394
                    r"__regex__SAFETY TRAINING AND STAFF PHYSICAL EXAMINATION",  # file63983
                    r"__regex__(Prevent|Response).*?(of|for|to).*?Covid[-\s]?19",
                    r"__regex__Safe working environment",
                    r"__regex__Health management initiatives",
                    r"__regex__COVID-19 Pandemic: Office Preventive Measures",  # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/971#note_240677
                ],
                "multi": True,
                "multi_elements": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"COVID-?19|preventive measures|facilities|safety performance",
                        r"safety training",
                        r"safety monitoring mechanism",
                        r"healthy and safe|safety|measures|training|daily|provide|organize|required|first-aid",
                        r"strictly abided to relevant preventive",
                        r"conduct fire drills",
                        r"take various measures to minimise",
                        r"optimized the physical examination plan",
                        r"optimized.*?insurance",
                        r"provided.*?insurance",
                        r"formulated.*?emergencies ",
                        r"Following the call of vaccination from Hong Kong government",
                        r"set a target rate for health checkups|the mental health|EAP counseling room",  # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/971#note_240679
                    ),
                    "neglect_pattern": (
                        r"^Aspect B2:\s?Health and Safety$",
                        r"comply with relevant laws and regulations.*?Health Ordinance",
                        r"^during the year",
                        r"^During the Reporting Period",
                        r"number of work injuries",
                    ),
                },
                "table_model": "first_table",  # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/971#note_240677
                "table_config": {
                    "regs": ["Office Preventive Measures"],
                },
            },
            {
                "name": "para_match",
                "multi_elements": True,
                "paragraph_pattern": (
                    r"(?P<content>COVID 19|preventive measures|facilities|safety performance)",
                    r"(?P<content>safety training)",
                    r"(?P<content>safety monitoring mechanism)",
                ),
            },
            {
                "name": "syllabus_elt_v2",
                "only_inject_features": True,
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "inject_syllabus_features": [
                    # r'__regex__Improvement of HSE System',
                    r"__regex__^B2.*?Health and Safety",
                    r"__regex__II.2.*?Health and Safety",
                    r"__regex__B\(II.*?HEALTH AND SAFETY",
                    # r'__regex__Occupational Health and Safety',
                ],
            },
            DEFAULT_MODEL,
        ],
    },
    {
        # 框选涉及到员工training及development政策的所有内容。
        # 一般为Development and Training标题下的所有内容(明显能区分出B3.1和3.2的，rule39里面就不框入B3.1和3.2的内容)。
        #
        #
        # 常见关键词： development， training
        #
        # 可从index给出的位置去定位框选；一般为标题Development and Training/staff development下对应内容
        "path": ["B3 policies - development and training"],
        "models": [
            {
                "name": "para_match",
                "syllabus_regs": [
                    r"EMPLOYEE DEVELOPMENT",
                    r"Development and Training",
                ],
                "paragraph_pattern": (
                    r"Employee development is a key priority for us",
                    r"The Group is concerned about common growth and development opportunities of employees",
                    r"The continuous growth and development of employees is an important guarantee",
                ),
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "include_shape": True,  # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/971#note_240686
                # "only_before_first_chapter": True,
                "inject_syllabus_features": [
                    r"__regex__III.3 Training and Development",
                    r"__regex__Development and Training",
                    r"__regex__Development and Retention",
                    r"__regex__Talent Training and Growth",
                    r"__regex__staff development",
                    r"__regex__Promoting Talent Development",
                    r"__regex__Development and Retention",
                    r"__regex__employee.*?(advancement|development)",
                    r"__regex__training and development",
                    r"__regex__performance management system",
                    r"__regex__Broadening development channels",
                    r"__regex__talent training",
                    r"__regex__training for",
                    r"__regex__training system",
                    r"__regex__development and promotion",
                    r"__regex__development channels",
                    r"__regex__training programs",
                    r"__regex__employee skill training",
                    r"__regex__employee training",
                    r"__regex__Diverse Training Programs",
                ],
            },
            DEFAULT_MODEL,
        ],
    },
    {
        # B3.1 此条规则是披露员工中受培训的情况。具体是按性别和员工类别（例如高级管理人员、中层管理人员）培训的员工百分比（重点是百分比数据）
        # 常见关键词有B3.1，train, gender, employee category。
        #  所在位置：1、优先在index或者data summary的地方找对应的图表、表格及段落；
        #           2、根据index给出KPI的位置去定位，一般在标题Development and Training/staff development下
        #
        #  判断依据：1、报告中没有相关数据但有解释，涉及no training的描述（常见描述如“本年因为疫情原因，集团未给员工提供培训”），为E；
        #           2、报告中披露了参与培训的各类员工人数对应的百分比数据，都是Y；
        #           3、报告没有披露相关内容，也没有进行解释，为ND
        #
        #  判断特殊情况：1、仅有total training hours，没有披露按照gender和category分类下的average training hours ，也没有
        #               percentage of employee trained，B3.1属于Y；
        #               2、有时B3.1描述100%完成了相关的培训项目，出现completion xxx也属于B3.1
        "path": ["KPI B3.1 - percentage of employees trained"],
        "models": [
            {  # 特例
                "name": "table_title",
                "only_inject_features": True,
                "feature_white_list": (r"Training.*?Employees",),
            },
            {
                "name": "middle_rows",
                "ignore_index_table": True,
                "regs": [
                    r"B3\.1",
                    r"Percentage of employees trained",
                ],
                "start_regs": [
                    r"B3\.1",
                    r"Training Indicators",
                    r"(Percentage|%|proportion).*?(traine\s?d|training)",
                    r"(trained|training).*?(Percentage|%|proportion)",
                    r"Full-time Employee trained by employee category",
                ],
                "end_regs": [
                    r"Average (hours|training)",
                    r"B3\.2",
                    r"health and safety",
                    r"workforce",
                ],
            },
            {
                "name": "after_row_match",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "row_pattern": [r"B3.1"],
                "para_pattern": [
                    r"(?P<content>During the reporting period.*?training)",
                    r"(?P<content>no training)",
                    r"training.*?employee coverage rate",
                    r"%\s?Training hours",
                ],
                "middle_rows": True,
                "remove_section_cn_text": True,
                "multi_elements": True,
                "table_title": [
                    r"trained by.*?(gender|category)",
                    r"受訓僱員百分比",
                    r"trained by",
                    r"(Percentage|%|proportion).*?(traine\s?d|training)",
                    r"(trained|training).*?(Percentage|%|proportion)",
                    r"Below are the details of employee training",
                ],
                "start_regs": [r"Percentage of employees trained", r"By employee category"],
                "end_regs": [r"Average training hours"],
            },
            {
                "name": "shape_title",
                "regs": (
                    r"受訓僱員百分比",
                    r"trained by",
                    r"(Percentage|%|proportion).*?(traine\s?d|training)",
                    r"(trained|training).*?(Percentage|%|proportion)",
                ),
            },
            {
                "name": "table_title",
                "only_inject_features": True,
                "multi_elements": True,
                "feature_white_list": (
                    r"受訓僱員百分比",
                    r"trained by",
                    r"(Percentage|%|proportion).*?(traine\s?d|training)",
                    r"(trained|training).*?(Percentage|%|proportion)",
                ),
            },
            {
                "name": "special_cells",
                "whole_table": True,
                "cell_pattern": (
                    r"受訓僱員百分比",
                    r"發展與培訓 \(%\)",
                    r"(Percentage|%|proportion).*?(traine\s?d|training)",
                    r"(trained|training).*?(Percentage|%|proportion)",
                    r"number.*?trained",
                ),
            },
            {
                "name": "para_match",
                "syllabus_regs": [
                    r"train|development",
                ],
                "paragraph_pattern": (
                    # r'(?P<content>B3.1|train|gender|employee category)',
                    r"(?P<content>no training)",
                    r"\d+% of our employees are trained",
                ),
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__Summary of Development and Training",
                    r"__regex__Summary of Development and Training",
                    r"__regex__Provision of Training Opportunities",
                    r"__regex__Training and Development Management",
                    r"__regex__Development and training",
                    r"__regex__training",
                ],
                "multi_elements": True,
                "only_inject_features": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"(?P<content>no training)",
                        r"\d%.*?attended training courses",
                        r"did not arrange any training courses",
                        r"proportions of training",
                        r"(Percentage|%|proportion).*?(traine\s?d|training)",
                        r"(trained|training).*?(Percentage|%|proportion)",
                        r"number.*?trained",
                    ),
                },
                "table_model": "table_title",
                "table_config": {
                    "feature_white_list": [
                        r"Summary of Development and Training",
                    ],
                },
            },
            {
                "name": "para_match",
                # 'syllabus_regs': [],
                "paragraph_pattern": (
                    r"(Percentage|%|proportion).*?(traine\s?d|training)",
                    r"(trained|training).*?(Percentage|%|proportion)",
                    r"number.*?trained",
                ),
            },
        ],
    },
    {
        #  规则是针对employment下面的Development and Training，披露按性别和员工类别划分的每位员工完成的平均培训时数。
        #  关键词是average training hours、employee category
        #  所在位置：1、优先去index/summary/key performance indicators中找寻；
        #           2、根据index给出的KPI位置去定位；
        #           3、在Development and Training/staff development标题下。
        #
        #  判断标准：1、如果报告中说没有培训并且解释原因，那么选E，比如`本年因为疫情原因，集团未给员工提供培训`，关键词：no training；
        #           2、找到对应图表、段落或者句子，就算Y；
        #           3、如果报告中没有对此有任何描述，那么就是ND。
        "path": ["KPI B3.2 - training hours completed"],
        "models": [
            {
                "name": "middle_rows",
                "ignore_index_table": True,
                "neglect_cell_regs": [
                    r"(?<!total ) train.*?hours",
                    r"(?<!total ) hours.*?train",
                ],
                "start_regs": [
                    r"B3\.2",
                    r"train.*?hours",
                    r"hours.*?train",
                ],
                "end_regs": [
                    r"Occupational safety",
                    r"operational indicator",
                    r"ESG Indicator",
                    r"B5\.1",
                    r"Health and Safety",
                    r"suppliers",
                    r"workforce",
                ],
            },
            {
                "name": "special_cells",
                "multi_elements": True,
                "whole_table": True,
                "cell_pattern": [
                    r"training hours by",
                    r"Average training hours",
                    r"Hours of employees trained",
                ],
            },
            {
                "name": "table_title",
                "multi_elements": True,
                "only_inject_features": True,
                "feature_white_list": [
                    r"training hours.*?by",
                ],
            },
            {
                "name": "shape_title",
                # "force_use_all_elements": True,
                "regs": (
                    r"average training hours",
                    r"Training hours of",
                    r"percentage of employees trained",
                ),
            },
            {
                "name": "after_row_match",
                "row_pattern": [r"B3.2"],
                "para_pattern": [
                    r"(?P<content>average training hours)",
                    r"(?P<content>no training)",
                    r"(?P<content>average.*?hours.*?training)",
                    r"每位員工平均完成培訓時數",
                ],
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "inject_syllabus_features": [r"__regex__^KPI B3.2"],
            },
            {
                "name": "para_match",
                "syllabus_regs": [
                    r"Development and Training",
                    r"Training and Development",
                    r"staff development",
                ],
                "paragraph_pattern": (
                    r"the Group provided.*?trainings hours to our employees",
                    r"arranged.*?hours training",
                    r"(?P<content>no training)",
                    r"(?P<content>average.*?hours.*?training)",
                ),
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__Development and Training",
                    r"__regex__Training and Development",
                ],
                "only_inject_features": True,
                "multi_elements": True,
                "paragraph_model": "empty",
                "table_model": "table_title",
                "table_config": {
                    "multi_elements": True,
                    "feature_white_list": [
                        r"Average training hours per employee by (Gender|Category)",
                    ],
                },
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__Training and Development",
                ],
                "multi_elements": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"no training",
                        r"average.*?hours.*?training",
                        r"average.*?training.*?hours",
                        r"total number of training hours",
                        r"\d+\s?hours",
                    ),
                },
                "table_model": "empty",
            },
            DEFAULT_MODEL,
        ],
    },
    {
        # 枚举判定为：E
        # ESG报告中没有相关劳工标准政策披露但是有解释
        # 常见表述“由于公司的业务性质是XXX，涉及到的劳动力较少，所以not applicable”
        # 或“由于公司的业务性质,雇佣的员工多为有一定资历或者工作经验的员工，童工或强制劳动的风险小 "
        # 或“no相关的问题” 或“not material" 的描述
        # 常见关键词：not material，not applicable, non-labour intensive
        #
        # 枚举判定为：Y
        # ESG报告中有关于劳工标准labour standards政策的披露
        # 提取涉及到labour standards政策的所有段落。常见的描述是“公司的劳工标准是……，遵守当地劳工法律法规，禁止雇佣童工、强迫劳动和非法用工等”。
        # i. 一般有单独的labour standard相关标题，能区分出后面B4.1和B4.2的就不勾进去
        # ii.无单独的labour standard相关标题，可能会跟B1 employment标题下方的内容混在一起，可通过关键词child,forced labor进行定位
        # 常见关键词： labour, labor，B4, workforce, forced labour, engaging child, Labour Standards，prevent
        #
        # ND: 没有披露相关内容,也没有进行解释
        "path": ["B4 policies - labour standards"],
        "models": [
            {
                "name": "para_match",
                "multi_elements": True,
                "paragraph_pattern": (
                    r"clearly lists the prohibited types of forced labor",
                    r"revised the Measures for Fair Management of Employees",
                    r"revised the Goldwind Rules for Employee Attendance",
                    r"strictly prohibiting and discouraging any form of child or forced labor",
                ),
            },
            {
                # 章节标题和第一个小标题之间
                "name": "policy",
                "only_inject_features": True,
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "ignore_pattern_match": True,
                "inject_syllabus_features": [
                    r"__regex__Aspect B4: Labour Standards",
                ],
            },
            {
                "name": "syllabus_based",
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__B3.*?Development and Training",
                    r"__regex__talent management",
                    r"__regex__Labou?r Standard",
                    r"__regex__Labou?r Practices",
                    r"__regex__Prevention of Child and Forced Labour",
                    r"__regex__Human Capital",
                    r"__regex__Employment and Interests",
                    r"__regex__Fair and Standard Employment",
                ],
                "neglect_parent_features": [
                    r"Laws and Regulations",
                ],
                "multi_elements": True,
                "multi": True,  # file63947
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"as are not addressed in this ESG",
                        r"any child labour",
                        r"forbids all types",
                        r"prevent.*?child labour",
                        r"(child|forced|illegal).*?(labour|labor)",
                        r"ensure compliance with relevant labour laws",
                        r"To prevent hiring child labour by mistake",
                        r"rights of labours",
                        r"Free chosen employment",
                        r"Remuneration and benefits",
                        r"Equal opportunity and no discrimination policy",
                        r"not force any employees to work",
                        r"Harassment and abuse",
                        r"forced and child labour",
                    ),
                    "neglect_pattern": (
                        r"have been no cases of ",
                        r"During the Reporting Period",
                        r"operates in compliance with relevant laws",
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_before_first_chapter": True,
                # "only_first": True,
                "inject_syllabus_features": [
                    r"__regex__Labour Standards",
                    r"__regex__Employment and Labour Standards",
                    r"__regex__Employment and Labour PRACTICES",  # file64005
                    r"__regex__PROTECTING EMPLOYEES.*?RIGHTS AND INTERESTS",
                    r"__regex__Human Capital",
                    r"__regex__Employment Policy",
                ],
            },
            {
                "name": "syllabus_elt_v2",
                "only_inject_features": True,
                "multi": True,  # file63948
                "only_first": True,  # file63947
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "inject_syllabus_features": [
                    r"__regex__forced? (labour|worker)",
                    r"__regex__child (labour|worker)",
                ],
            },
            {
                "name": "para_match",
                "strict_limit": True,
                "syllabus_regs": [
                    r"Labour Standards",
                    r"Employment and Labour Standards",
                    r"Human Capital",
                    r"Employment Policy",
                ],
                "paragraph_pattern": (
                    r"(?P<content>not material|not applicable|non-labour intensive)",
                    r"(?P<content>labour|labor|B4|workforce|forced labour|engaging child|Labour Standards)",
                    r"(?P<content>not have any violation relating)",
                ),
                "neglect_pattern": (r"to prevent any employment of child labour",),
            },
            {
                "name": "para_match",
                # 'multi_elements': True,
                "paragraph_pattern": (r"child and forced labour",),
            },
            {
                "name": "row_match",
                "ignore_index_table": True,
                "row_pattern": [r"forced? labour", "child labour"],
                "first_cell": False,
            },
            DEFAULT_MODEL,
        ],
    },
    {
        "path": ["B4 law compliance - labour standards"],
        "models": [
            {
                "name": "after_row_match",
                "row_pattern": [r"GD B4", r"B4\. Labour StandardsGeneral disclosure"],
                "direct_answer_pattern": [
                    r"comply with all applicable local laws.*?Labour Law",
                ],
            },
            {
                "name": "twice_row_match",
                "first_cell": False,
                "ignore_index_table": True,  # file63997披露在index表格中 通过after_row_match提取
                "row_pattern": COMPLY_LAW_PATTERN,
                "merge_valid_rows": False,
                "second_pattern": [
                    # r'B4(\s+|$|[a-zA-Z])',  # file63997披露在index表格中
                    r"Labou?r Standards?",  # file63983
                ],
            },
            {
                "name": "twice_para_match",
                "multi_elements": True,
                "use_all_elements": True,
                # "strict_limit": True,
                "syllabus_regs": [],
                "neglect_syllabus_regs": [
                    r"Supply chain management",
                    r"Anti.*?Corruption",
                    r"The Environment and Natural Resources",
                    r"Data Privacy",
                    r"B[1-3]|B[5-8]|A[1-3]",
                ],
                "paragraph_pattern": COMPLY_LAW_PATTERN,
                "second_pattern": (
                    r"workforce",
                    r"engaging child",
                    r"labou?r (Contract )?law",
                    r"(child|forced|illegal).*?labou?r",
                    r"labour related (law|regulation)",
                ),
            },
            {
                "name": "shape_title",
                "force_use_all_elements": True,
                "regs": (r"Diversified Talent Attraction Policies",),  # file64003针对infographics的特殊规则
            },
            {
                "name": "twice_row_match",
                "first_cell": False,
                "ignore_index_table": True,
                "row_pattern": COMPLY_LAW_PATTERN,
                "merge_valid_rows": False,
                "second_pattern": [
                    r"B4(\s+|$)",
                    r"workforce",
                    r"engaging child",
                    r"labou?r (Contract )?law",
                    r"(child|forced|illegal).*?labou?r",
                    r"labour related (law|regulation)",
                ],
            },
            {
                "name": "para_match",
                "syllabus_regs": [
                    r"Labour Standards",
                    r"Employment and Labour Standards",
                    r"Human Capital",
                    r"Employment Policy",
                    r"Supply chain management",
                    r"Anti.*?Corruption",
                    r"The Environment and Natural Resources",
                    r"Data Privacy",
                    r"B[1-3]|B[5-8]|A[1-3]",
                ],
                "paragraph_pattern": (
                    r"complied.*?laws and regulations.*?labour standards",
                    r"(?P<content>not applicable|no (law|ordinance|regulation))",
                    r"(?P<content>(law|regulations).*?(workforce|forced labor|child|engaging child))",
                    r"(?P<content>not have any violation relating)",
                ),
                "neglect_pattern": (r"Information on:",),
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__Labour Standards",
                    r"__regex__Employment and Labour Standards",
                    r"__regex__Human Capital",
                    r"__regex__Employment Policy",
                ],
                "multi_elements": True,
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"(?P<content>not applicable|no (law|ordinance|regulation))",
                        r"(?P<content>(law|regulations).*?(workforce|forced labor|child|engaging child))",
                        r"(?P<content>not have any violation relating)",
                        r"did not identify any material breaches",
                    ),
                    "neglect_pattern": (r"Information on:",),
                },
                "table_model": "empty",
            },
            {
                "name": "kmeans_classification",
                "threshold": 0.1,
            },
            # DEFAULT_MODEL,
        ],
    },
    {
        # 枚举判定为：E
        # ESG报告中没有相关披露但是有解释
        # 常见表述“这条规则不适用于本集团”或者 “N/A”或“not disclosure”
        # 关键词：not material，not applicable, N/A, not disclosure
        #
        # 枚举判定为：Y
        # ESG报告中有关于公司如何审查以避免child and labour force的措施的披露
        # 提取涉及child和forced labour措施的所有段落，侧重于labour force和用工年龄方面。
        # 常见的描述是“公司已制定招聘政策，符合年龄的申请人才可被聘用”或者“本集团要求求职者提供有效的身份证明文件，确保年龄符合规定”等。
        # i.一般在labour standard相关标题的内容中披露相关措施；
        # ii.无单独的labour standard相关标题，可能会跟B1 employment标题下方的内容混在一起，可通过关键词child,forced labor进行定位`
        # 关键词：workforce, forced labour, engaging child，child labour，establish，overtime，prohibition，prevent，
        # identity/identification  document,identity card,recruiting,above 18 years old,working visa
        #
        # ND: 没有披露相关内容,也没有进行解释
        "path": ["KPI B4.1 - review measures to avoid child & forced labour"],
        "models": [
            {
                "name": "after_row_match",
                "row_pattern": [r"B4.1"],
                "only_syllabus_of_para": True,
                "include_row": True,  # file63960E的情况框选整行
                "ignore_syllabus": [r"^Our Employees$"],
                "para_pattern": (
                    r"Protection of Employees' Rights and Interests",  # file63948
                    r"Measures Taken to Avoid Child and Forced Labour",  # file63979
                ),
                "ignore_syllabus_pattern": (
                    r"Prohibition of discrimination and sexual harassment in the workplace",
                    r"Protection of legal rights and interests",
                ),
            },
            {
                "name": "after_row_match",
                "row_pattern": [r"B4.1"],
                "ignore_syllabus": [r"^Our Employees$"],
                "multi_elements": True,
                "para_pattern": (
                    r"(?P<content>not material|not applicable|N/A|not disclosure)",
                    r"(?P<content> workforce|engaging child)",
                    # r'establish|overtime',
                    # r'prohibition|prevent',
                    # r'(forced|child) labou?r',
                    r"identity|identification document",
                    r"identity card",
                    r"recruiting",
                    r"above 18 years old",
                    r"working visa",
                ),
                # 'second_pattern': (),
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__KPI B4.1",
                    r"__regex__Employment Guidelines",
                    r"__regex__Employment and Labour Standards",
                    r"__regex__Labor Rights and Interests Protection",
                    r"__regex__Prevent.*?Child and Forced Labour",
                    # r'__regex__Preventative measures against child and forced labour',
                    r"__regex__Labour Standard",
                    r"__regex__Human Capital",
                    r"__regex__Employment Policy",
                    r"__regex__Employment and Interests",
                    r"__regex__Protection of Employees' Rights and Interests",
                ],
                "multi_elements": True,
                "multi": True,  # file63947
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"not material|not applicable|N/A|not disclosure",
                        r"workforce|engaging child",
                        # r'child labour', # 仅提到child labour 没有提到相关措施的不能提取
                        # r'establish|overtime',
                        r"complete pre-employment application",
                        r"provide.*?identification",
                        r"(identity|identification document)",
                        r"identity card",
                        r"recruiting",
                        r"above 18 years old",
                        r"working visa",
                        r"ensure no child labor",
                        r"checks the documents provided",
                        r"inspect applicant’s documents",
                        r"checks.*?identity documents",
                        r"Free chosen employment",
                        r"Remuneration and benefits",
                        r"Equal opportunity and no discrimination policy",
                        r"not force any employees to work",
                        r"Harassment and abuse",
                        r"forced and child labour",
                        r"forced labour is strictly prohibited",  # file63979
                    ),
                    "neglect_pattern": (
                        r"During the Year",
                        r"engage suppliers and contractors",
                    ),
                },
                "table_model": "row_match",
                "ignore_index_table": True,
                "table_config": {
                    "row_pattern": [
                        r"(child|forced) labour",
                    ],
                },
            },
            {
                "name": "syllabus_elt_v2",
                "only_inject_features": True,
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "inject_syllabus_features": [
                    r"__regex__B4.1",
                    # r'__regex__Labour Standard',
                    r"__regex__Employment and Labour Standards",
                    r"__regex__Human Capital",
                ],
            },
            {
                "name": "syllabus_elt_v2",
                "only_inject_features": True,
                "multi": True,  # file63948
                "only_first": True,  # file63947
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "inject_syllabus_features": [
                    r"__regex__forced? (labour|worker)",
                    r"__regex__child (labour|worker)",
                ],
            },
            {
                "name": "para_match",
                "syllabus_regs": [
                    r"Labour Standards",
                    r"Employment and Labour Standards",
                    r"Human Capital",
                ],
                # 'multi_elements': True,
                "paragraph_pattern": (
                    # r'(?P<content>not material|not applicable|N/A|not disclosure)',
                    # r'(?P<content>workforce|forced labour|engaging child|establish|overtime)',
                    r"complete pre-employment application",
                    r"provide.*?identification",
                    r"(identity|identification document)",
                    r"identity card",
                    r"recruiting",
                    r"above 18 years old",
                    r"working visa",
                    r"ensure no child labor",
                    r"checks the documents provided",
                    r"inspect applicant’s documents",
                    r"checks.*?identity documents",
                    r"B4.*?are not the key material matters.*?are not addressed in this ESG report",
                ),
            },
            {
                "name": "para_match",
                # 'multi_elements': True,
                "paragraph_pattern": (r"verif(ie(s|d)|y) the age",),
            },
            {
                "name": "row_match",
                "ignore_index_table": True,
                "row_pattern": [r"forced? labour", "child labour"],
                "first_cell": False,
            },
            # {
            #     "name": 'kmeans_classification',
            # },
            DEFAULT_MODEL,
        ],
    },
    {
        # 框选内容：<优先在index指引的正文内容去找>
        #
        # 如果存在明显的细分小标题**kpi B4.2**，则直接框该小标题下内容
        #
        #  一般描述中会有**比较明确的关键词**： in case of，once found, in the event这种表示
        #  如果发现forced labour，child labour的词。
        "path": ["KPI B4.2 - steps to avoid child & forced labour"],
        "models": [
            {
                "name": "para_match",
                "paragraph_pattern": (r"B4.*?are not the key material matters.*?are not addressed in this ESG report",),
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__Prevention of child labour and forced labour",
                    r"__regex__B4\.2",
                ],
            },
            {
                "name": "twice_para_match",
                "force_use_all_elements": True,
                "multi_elements": True,
                "syllabus_regs": [
                    r"Labour Standards",
                    r"Labour RIGHT",
                    r"Employment and Labour Standards",
                    r"Human Capital",
                    r"Employment Policy",
                    r"Prevention of child labour and forced labour",
                    r"Protecting employees’ rights and interests",
                    r"Employment Principles",
                    r"Retaining Talents",
                ],
                "paragraph_pattern": (
                    r"(child|forced|illegal).*?(labour|labor)",
                    r"l ?abour rights",  # file64000单词识别错误
                    r"any irregularities in ages",
                ),
                "second_pattern": (
                    r"in case of|once found|in the event| once|facing|once discovered",
                    r"if(?:(?!\.).)*(involve|found|occurrence|i ?dentified)",  # i ?dentified file64000单词识别错误
                    r"if(?:(?!\.).)*provided? forgery information",
                    r"if the case is justified",
                    r"regular investigation|severe treatment",  # file63988
                    r"for any illegal labour(?:(?!\.).)*take(?:(?!\.).)*actions? immediately",  # file63958
                ),
            },
            {
                "name": "after_row_match",
                "row_pattern": [r"B4.2"],
                # 'para_pattern': [
                #     r'(?P<content>not material|not applicable|non-labour intensive)',
                #     r'cases of|in case of|once found|in the event',
                # ],
                "para_pattern": (r"(child|forced|illegal).*?(labour|labor)",),
                "second_pattern": (
                    r"in case of|once found|in the event",
                    r"once|facing",
                    r"once discovered",
                ),
            },
            {
                "name": "row_match",
                "first_cell": False,
                "force_use_all_elements": True,
                "row_pattern": [
                    r"B4\.2.*not (an? )?(applicable|been disclosed|considered)",
                    r"B4\.2.*(?<![a-zA-Z])N/?A(?![a-zA-Z])",
                    r"B4\.2.*不適用",
                    r"B4\.2.*be disclosed in the future",
                    r"B4\.2.*not involve(?:(?!(\.)).)*recall",
                    r"B4\.2.*not material",
                    r"B4\.2.*not to disclose",
                ],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__B4.[12]",
                ],
                "multi_elements": True,
                "paragraph_model": "twice_para_match",
                "para_config": {
                    "paragraph_pattern": (r"(child|forced|illegal).*?(labour|labor)",),
                    "second_pattern": (
                        r"(?<!any )cases? of",
                        r"in case of|once found|in the event",
                        r"once|facing",
                        r"discovered",
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "para_match",
                "paragraph_pattern": (r"employees work voluntarily",),
            },
        ],
    },
    {
        # B5 policy 供应链环境和社会风险管理的政策
        # 枚举判定为：E
        # ESG报告中没有相关政策，但是有解释
        # 常见描述“由于我们的业务性质，我们没有进行供应链环境管理”或“no supplier”等的描述
        # 关键词：no suppliers, not material
        #
        # 枚举判定为：Y
        # 关于supply chain management政策的披露，一般有在Supply Chain Management标题下。
        # i.框选归纳总结的段落，可框选大标题与小标题之间的内容；
        # ii.若大小标题无内容，可框选小标题Supply Chain Management Structure标题下方不含数据的内容（5.1 关联方数量）；
        #
        # 一般不会出现ND
        "path": ["B5 policies - supply chain"],
        "neglect_para_missing_crude_regs": [
            r"a new service model.*?on the cloud",
        ],
        "models": [
            # for explain
            {
                "name": "para_match",
                "paragraph_pattern": (r"not considered to have significant.*?supply chain",),
            },
            # for special comply answer
            {
                "name": "para_match",
                "paragraph_pattern": (r"strict select.*?suppliers.*?in order to.*?suppliers",),
            },
            {
                "name": "after_row_match",
                "first_cell": False,
                # 'multi': True,
                "row_pattern": [
                    r"B5",
                    r"KPI B5\.1",
                ],
                "neglect_row_pattern": [
                    r"Aspect B5: Supply Chain Management",
                ],
                "just_a_para": True,
                "para_pattern": [
                    r"supply chain",
                ],
            },
            {
                "name": "shape_title",
                "multi_elements": True,
                "regs": (
                    r"Access of suppliers",
                    r"Supplier management",
                ),
            },
            {
                # 章节标题和第一个小标题之间
                "name": "policy",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "ignore_pattern_match": True,
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__Procedures of Supplier Management",
                    r"__regex__Supply Chain Management Structure",
                    r"__regex__Supply Chain Management__regex__General Disclosure and KPIs",
                    r"__regex__B(5:|\(V\)) Supply Chain Management",
                    r"__regex__(Supply.*?Management|Operating\s?Procedures)",
                    r"__regex__^Supplier management$",
                ],
                "neglect_features": [
                    r"Anti-Corruption",
                    r"Operating Practices",
                    r"Materiality Assessment",
                    r"ESG REPORTING GUIDE INDEX",
                ],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__Supplier management system",
                    r"__regex__SUPPLIER ENGAGEMENT",
                    r"__regex__Coordinated Development",
                    r"__regex__Development and Training",
                ],
                "only_inject_features": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"(supply|procument|supplier) management",
                        r"hese areas are not addressed in this ESG report",
                        r"evaluate suppliers",
                        r"To strengthen the dynamic management of suppliers",
                        r"suppliers.*?encouraged to adopt ",
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"(supply|procument|supplier) management",
                    r"formulated a standardised.*?suppliers and subcontractors",
                    r"The Group understands the importance of supply chain management",
                    r"no suppliers|not material",
                    r"SUPPLIER ENGAGEMENT",
                ),
            },
            {
                "name": "kmeans_classification",
            },
        ],
    },
    {
        # 优先取表格
        # B5.1 按地理区域划分的供应商数量
        # 枚举判定为：E
        # ESG报告中没有按地理区域划分供应商数量的披露，但是有解释，常见描述“由于我们的业务性质，我们没有进行供应链环境管理”或“no supplier”等的描述；
        # 关键词：no suppliers, not material, not disclosed, N/A, not applicable, not set up
        #
        # 枚举判定为：Y
        # B5.1 提取按地理区域划分供应商数量的段落或图表或句子，当句子或图表同时出现时，优先框选图表
        # 常见关键词：suppliers, geographical, B5.1,region，based，located in
        #
        # 枚举判定为：：ND
        # ESG报告中无相关描述则为ND
        "path": ["KPI B5.1 - number of suppliers"],
        "models": [
            {
                "name": "row_match",
                # "ignore_index_table": True,
                "force_use_all_elements": True,
                "row_pattern": [r"Number of suppliers in Mainland China", r"suppliers are local supplier"],
            },
            {
                "name": "after_row_match",
                "row_pattern": [r"B5.1"],
                "just_a_para": True,
                "neglect_para_pattern": [
                    r"\d{4}/\d{2}\s?([\w\d()（）:%]+\s){1,5}\s?suppl",
                    r"100% of the suppliers on which the same set of engagement practices are being implemented",
                ],
                "para_pattern": [
                    r"had.*?suppliers (from|in) (Hong Kong|Mainland China)",
                    r"\d{1,}\s?([\w\d()（）:%]+\s){1,5}\s?suppl",
                    r"major suppliers for its head office",
                ],
                "direct_answer_pattern": [
                    r"\d{1,}\s?([\w\d()（）:%]+\s){1,5}\s?suppliers",
                    r"suppliers are local supplier",
                ],
                # 'middle_rows': True,
                # 'table_title': [
                #     r'xxx'
                # ],
            },
            {
                "name": "middle_rows",
                "ignore_index_table": True,
                "filter_content_answer": True,
                "title_regs": [],
                "neglect_title_regs": [
                    r"honoured to receive several different awards",
                ],
                "start_regs": [
                    r"Supply Chain",
                    r"供應.*?鏈",
                ],
                "end_regs": [
                    r"Product and Service",
                ],
            },
            {
                "name": "shape_title",
                "regs": (
                    r"suppl.*?GEOGRAPHICAL REGION",
                    r"NUMBER OF SUPPLIERS",
                    r"SUPPLIER DISTRIBUTION",
                    r"major suppliers that were directly related",
                ),
            },
            {
                "name": "table_title",
                "only_inject_features": True,
                "feature_white_list": [
                    r"suppl.*?GEOGRAPHICAL REGION",
                    r"NUMBER OF.*?SUPPLIERS",
                    r"SUPPLIER DISTRIBUTION",
                    r"major suppliers that were directly related",
                ],
            },
            {
                "name": "special_cells",
                "whole_table": True,
                "cell_pattern": [
                    # r'location',
                    r"Number.*?suppliers",
                    r"No\. of supplier",
                    r"Total.*?suppliers",
                    r"^Supplier\s?\(Note",
                    # r'Mainland China|RPC|region',
                    # r'China|rest of asia',
                ],
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"the Group had a total of.*?major suppl",
                    r"(had|have) a total of.*?suppl",
                    r"had engaged.*?suppl",
                    r"located in Hong Kong",
                    r"no.*?major suppli",
                    r"suppliers are immaterial",
                    r"not? material.*?suppl",
                ),
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"Suppliers of the Group",
                ],
                "multi_elements": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"had a total of.*?major suppliers",
                        r"geographical|region",
                        r"engaged.*?business partners",
                        r"total.*?\d{1,}\s?suppl",
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"had a total of.*?major suppliers",
                    r"geographical",
                    r"engaged.*?business partners",
                    r"B5.*?are not the key material matters.*?are not addressed in this ESG report",
                    r"Suppliers by geographical locations",
                    r"\d{1,}\s?([\w\d()（）:%]+\s){0,5}\s?suppl",
                ),
            },
        ],
    },
    {
        # 框选有整个段落关发行人如何选择聘用的供应商，以及这些方式是如何实施和监管的(侧重supplier的选择标准，确定，评估，监管**等)
        # 一般都会描述几个不同的选取及评估标准，如：价格低，质量好，信用好，售后保障好等
        #
        # 框选内容：<优先在index指引的正文内容去找>
        #  如果存在明显的细分小标题**kpi B5.2**，则直接框该小标题下内容
        #  一般描述中会有**比较明确的关键词**： select, selection, vendors, suppliers, based on ，price等相关描述
        # 以上两个规则index及正文描述如果是not applicable, N/A，则判断为E
        "path": ["KPI B5.2 - suppliers engagement"],
        "models": [
            {
                "name": "special_cells",
                "cell_pattern": [
                    r"^Engaging Suppliers",
                ],
            },
            {
                "name": "para_match",
                "paragraph_pattern": (r"in terms of supplier admission.*?collect",),
            },
            {
                "name": "after_row_match",
                "first_cell": False,
                "row_pattern": [r"B5.2"],
                "multi_elements": True,
                "para_pattern": [
                    r"select|selection|vendors|B5.2",
                    r"select process",
                    r"suppliers.*?selected",
                    r"in selecting.*?suppliers.*?considerations",
                    r"select suppliers based on",
                    r"engage suppliers.*?based on",
                    r"select.*?supplier.*?selection",
                    r"(Suppliers|Subcontractors).*?are chosen",
                    r"selecting suppliers",
                    r"B5.*?are not the key material matters.*?are not addressed in this ESG report",
                ],
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__KPI B5.2",
                    r"__regex__Engaging Suppliers",
                    # r'__regex__Supply Chain Management',
                    r"__regex__selection criteria",
                ],
            },
            {
                "name": "syllabus_based",
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__Service Provider Management",
                    r"__regex__Procurement (Process|Policy)",
                    r"__regex__Supplier Engagement",
                    r"__regex__Managing Our Vendors and Suppliers",
                ],
                "neglect_features": [
                    r"LABOUR STANDARDS",
                ],
                "multi": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"select|selection|vendors|based on|B5.2",
                        r"undertake annual performance reviews with our suppliers",
                        r"choosing a new supplier",
                        r"consider supplier",
                        r"after meeting entry qualifications",
                        r"procurement criteria is based",
                        r"through a comprehensive supplier audit",
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "special_cells",
                "whole_table": True,
                "cell_pattern": [
                    r"select|selection|vendors|based on|B5.2",
                    r"Selection Criteria",
                    r"Procurement.*?include",
                ],
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"select|selection|vendors|B5.2",
                    r"select process",
                    r"suppliers.*?selected",
                    r"in selecting.*?suppliers.*?considerations",
                    r"select suppliers based on",
                    r"engage suppliers.*?based on",
                    r"select.*?supplier.*?selection",
                    r"(Suppliers|Subcontractors).*?are chosen",
                    r"selecting suppliers",
                    r"B5.*?are not the key material matters.*?are not addressed in this ESG report",
                ),
            },
            {
                "name": "shape_title",
                "multi_elements": True,
                "regs": (
                    r"Access of suppliers",
                    r"Supplier management",
                ),
            },
            {"name": "kmeans_classification"},
        ],
    },
    {
        # B5涉及到的规则都是关于supply chain management相关的内容；
        # B5.3需要框选出用于识别供应链中环境和社会风险的做法（即ESG risk），以及实施和监控这些风险的方法。
        #  所在位置：1. 涉及到supply chain management的标题（一般位置比较固定，都在这个大标题下定位）
        #           2. 小标题为Environmental and Social Responsibility of Suppliers或者
        #           Supply Chain Risk Management的文字段落（有单独标题的可以只提取这个标题下的内容）
        #
        #  提取内容：1. 提取涉及到与suppliers管理和合作过程中，与其识别到的ESG风险有关的描述
        #           2. 通常涵盖范围较广，如识别供应商的童工和强制劳工情况、反腐建设情况、环保情况等描述，
        #           所以会涉及到对应的关键词
        #           3. 对于未能达到环境和社会标准的供应商，会终止合作的描述
        #           4. 由于本条规则侧重整个供应链环节内的风险识别及监控措施，所以有时也会包含B5.2的内容
        #
        #  关键词：（需要在supply chain management相关位置范围内查找）suppliers +
        #  take environmental and social risks into considerations,
        #  environmental and social criteria,
        #  prohibition on the recruitment of child and forced labour,
        #  eliminating discrimination to employees,
        #  providing a safe working environment,
        #  considering if the products and services provided are beneficial to environmental protection.
        #
        #  判定方式：Y-披露并描述以上的提取内容（满足提取内容的任何一条或多条即可）
        #           E-明确披露了 not involve significant risk to the environment and society的；
        #           关键词有not applicable，no suppliers, not material的
        #           ND-没有任何相关描述
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/803#note_212925
        "path": ["KPI B5.3 - supply chain ESG risks identification"],
        "models": [
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"B5.*?are not the key material matters.*?are not addressed in this ESG report",
                    r"failed the assessment.*?engaged on Group level",
                ),
            },
            {
                "name": "after_row_match",
                "row_pattern": [r"B5.3"],
                "first_cell": False,
                "multi_elements": True,
                "neglect_para_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "para_pattern": [
                    r"(Supplier|service providers).*?status are assessed using a.*?evaluation",
                    r"social risks|supplier risks|review|B5.3",
                    r"requires suppliers to comply.*?law",
                    r"suppliers.*?review annually",
                    r"instability of the supply chain",
                    r"required to take remedial actions",
                    r"meet our quality requirements",
                    r"safety and employment matters",
                    r"made it clear to our subcontractors that compliance with the labor laws",
                    r"hold meetings with suppliers to communicate our requirements",
                    r"environmental and social considerations",
                    "take environmental and social risks into considerations,",
                    "environmental and social criteria",
                    "prohibition on the recruitment of child and forced labour",
                    "eliminating discrimination to employees",
                    "providing a safe working environment",
                    "considering if the products and services provided are beneficial to environmental protection",
                    "termination of cooperation",
                    "review.*?annually",
                    "social responsibilities to the society",
                ],
            },
            {
                "name": "special_cells",
                "whole_table": True,
                "cell_pattern": [
                    r"Supply Chain Sustainability",
                ],
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "multi": True,
                "inject_syllabus_features": [
                    r"__regex__Supply Chain Management__regex__KPI B5.3",
                    r"__regex__Supply Chain Risk Management",
                    r"__regex__Supply Chain Environmental and Social Risk Management",
                    r"__regex__Supply Chain Sustainability",
                    r"__regex__Sustainable Procurement",
                ],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__supply chain sustainability",
                    r"__regex__kpi b5.3",
                    r"__regex__sustainable supply chain",
                    r"__regex__supply chain management",
                ],
                "multi_elements": True,
                "multi": True,
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        # r'environment',
                        # r'assess',
                        # r'assreviewess',
                        r"social risks|supplier risks|B5.3",
                        r"requires suppliers to comply.*?law",
                        r"suppliers.*?review annually",
                        r"instability of the supply chain",
                        r"required to take remedial actions",
                        r"meet our quality requirements",
                        r"safety and employment matters",
                        r"made it clear to our subcontractors that compliance with the labor laws",
                        r"hold meetings with suppliers to communicate our requirements",
                        r"environmental and social considerations",
                        "take environmental and social risks into considerations,",
                        "environmental and social criteria",
                        "prohibition on the recruitment of child and forced labour",
                        "eliminating discrimination to employees",
                        "providing a safe working environment",
                        "considering if the products and services provided are beneficial to environmental protection",
                        "termination of cooperation",
                        "review.*?annually",
                        "social responsibilities to the society",
                        "maximizing the use of recyclable products",
                        "conduct site visits to suppl",
                        "emphasis on the procurement principles",
                        "encourages suppliers to promote corporate social responsibility",
                        "considers environmental factors when considering suppliers",
                        "keeps track of the work processes done by the suppliers",
                    ),
                    "neglect_pattern": (
                        r"Environmental, Social and Governance",
                        r"ENVIRONMENTAL, SOCIAL AND GOVERNANCE REPORT",
                        r"^ENVIRONMENTAL,",
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "para_match",
                "multi_elements": True,
                "paragraph_pattern": (
                    r"environment",
                    r"social risks|supplier risks|review|B5.3",
                    r"requires suppliers to comply.*?law",
                    r"suppliers.*?review annually",
                    r"instability of the supply chain",
                    r"required to take remedial actions",
                    r"meet our quality requirements",
                    r"safety and employment matters",
                    r"made it clear to our subcontractors that compliance with the labor laws",
                    r"hold meetings with suppliers to communicate our requirements",
                    r"environmental and social considerations",
                    r"take environmental and social risks into considerations,",
                    r"environmental and social criteria",
                    r"prohibition on the recruitment of child and forced labour",
                    r"eliminating discrimination to employees",
                    r"providing a safe working environment",
                    r"considering if the products and services provided are beneficial to environmental protection",
                    r"termination of cooperation",
                    r"review.*?annually",
                    r"social responsibilities to the society",
                    r"B5.*?are not the key material matters.*?are not addressed in this ESG report",
                    r"maximizing the use of recyclable products",
                    r"suppliers meets the requirements",
                ),
                "neglect_pattern": (
                    r"Environmental, Social and Governance",
                    r"ENVIRONMENTAL, SOCIAL AND GOVERNANCE REPORT",
                ),
                "neglect_syllabus_regs": [
                    r"Corporate governance structure",
                ],
            },
            DEFAULT_MODEL,
        ],
    },
    {
        # B5涉及到的规则都是关于supply chain management相关的内容；
        # B5.4需要框选出供应链中选择供应商时用于推广环保产品和服务的做法，以及实施和监控这些风险的方法。
        #  所在位置：1. 涉及到supply chain management的标题（一般位置比较固定，都在这个大标题下定位）
        #           2. 小标题为Green Procurement或者Green Purchasing等的文字段落
        #
        #  提取内容：1. 提取涉及到在suppliers合作过程中，优先选择环保产品的描述
        #           2. 优先本地供应商采购，减少碳足迹的描述
        #           3. 由于涉及到供应商选择，有时可能与B5.2指向同一段落
        #
        #  关键词：（需要在supply chain management相关位置范围内查找）
        #  suppliers +  eco-friendly, environmentally preferable,
        #  minimizing the negative impact to natural environment,
        #  takes into account the geographical location,
        #  product delivery process,reduce the  carbon footprint from transportation
        #
        # environment，green ,local suppliers, sustainability
        #
        #  判定方式：Y-披露并描述以上的提取内容（满足提取内容的任何一条或多条即可）
        #           E-明确披露了due to its business nature, did not set up rules on supply chain management的；
        #           或关键词有not applicable，no suppliers, not material的
        #           ND-没有任何相关描述
        "path": ["KPI B5.4 - practice to promote environmentally preferable products"],
        "models": [
            {
                "name": "para_match",
                "strict_limit": True,
                "syllabus_regs": [
                    r"Development and Training",
                ],
                "paragraph_pattern": (r"B5.*?are not the key material matters.*?are not addressed in this ESG report",),
                "neglect_pattern": (
                    r"^\d+\..*?Supply Chain Management$",
                    r"^\d+\..*?Our Suppliers$",
                ),
            },
            {
                "name": "after_row_match",
                "row_pattern": [r"B5.4"],
                "multi_elements": True,
                "first_cell": False,
                "para_pattern": [
                    # r'vendors|environment',
                    # r'eco-friendly|environmentally preferable|local suppliers|sustainability|B5.4',
                    r"eco-friendly|environmentally preferable|local suppliers|B5.4",
                    r"Group maintains close communication with its suppliers",
                    r"Group will also consider.*?suppliers.*?environment and society",
                    r"product.*?beneficial to environment",
                    r"environmentally preferable products",
                    r"preference of using sustainable construction materials",
                    r"environmental management plan",
                    r"consider the suppliers.*?environmental protection policies",
                    r"green and environmental friendly products",
                    r"green elements",
                    r"environmentally friendly",
                    r"Environmental Working Group",
                    r"environmental-friendliness in consumer products",
                    r"environmental safety",
                    r"environmental protection",
                    r"greener supply",
                    r"energy efficient with lower emission models",
                    r"environmental and social performance",
                    r"constantly monitor the environmental and social risks",
                    r"(evaluate|manage).*?the environmental protection materials",
                    r"as follow:",  # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/853#note_217953
                    r"including:",
                ],
                "neglect_para_pattern": [
                    r"^Strengthening Environmental Protection Through Training$",
                ],
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__KPI B5.4",
                    r"__regex__green procurement",
                    r"__regex__green sourcing",
                    r"__regex__green Purchasing",
                    r"__regex__environmentally preferable",
                ],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__Supply Chain Sustainability",
                    r"__regex__KPI B5.4",
                    r"__regex__Sustainable Supply Chain",
                    r"__regex__SUPPLY CHAIN MANAGEMENT",
                    r"__regex__Environmental Risk Management",
                ],
                "multi": True,
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "as_follow_pattern": [
                    r"in the periodic audit of suppliers:$",
                ],
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        # r'sustainability',
                        r"eco-friendly|environmentally preferable|local suppliers|B5.4",
                        r"Group maintains close communication with its suppliers",
                        r"Group will also consider.*?suppliers.*?environment and society",
                        r"product.*?beneficial to environment",
                        r"environmentally preferable products",
                        r"preference of using sustainable construction materials",
                        r"environmental management plan",
                        r"consider the suppliers.*?environmental protection policies",
                        r"green elements",
                        r"environmentally friendly",
                        r"Environmental Working Group",
                        r"environmental-friendliness in consumer products",
                        r"environmental safety",
                        r"environmental protection",
                        r"Environmental compliance",
                        r"environmental performance",
                        r"greener supply",
                        r"energy efficient with lower emission models",
                        r"environmental and social performance",
                        r"constantly monitor the environmental and social risks",
                        r"(evaluate|manage).*?the environmental protection materials",
                        "as follow:",  # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/853#note_217953
                        "including:",
                        # r'^\d+\.',
                        # r'^•',
                    ),
                    "neglect_pattern": (
                        r"^\d+\..*?Supply Chain Management$",
                        r"^\d+\..*?Our Suppliers$",
                    ),
                },
                "table_model": "special_cells",
                "table_config": {
                    "cell_pattern": [
                        r"eco-friendly|environmentally preferable|local suppliers|sustainability|B5.4",
                    ]
                },
            },
            {
                "name": "para_match",
                # "strict_limit": True,
                "multi_elements": True,
                "syllabus_regs": [
                    r"Supply Chain Sustainability",
                    r"KPI B5.4",
                    r"Sustainable Supply Chain",
                    r"SUPPLY CHAIN MANAGEMENT",
                ],
                "paragraph_pattern": (
                    # r'sustainability',
                    r"eco-friendly|environmentally preferable|local suppliers|B5.4",
                    r"Group maintains close communication with its suppliers",
                    r"Group will also consider.*?suppliers.*?environment and society",
                    r"product.*?beneficial to environment",
                    r"environmentally preferable products",
                    r"preference of using sustainable construction materials",
                    r"environmental management plan",
                    r"consider the suppliers.*?environmental protection policies",
                    r"green and environmental friendly products",
                    r"green elements",
                    r"environmentally friendly",
                    r"Environmental Working Group",
                    r"environmental-friendliness in consumer products",
                    r"environmental safety",
                    "environmental protection",
                    "as follow:",  # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/853#note_217953
                    "including:",
                    # r'^\d+\.',
                    # r'^•',
                    r"B5.*?are not the key material matters.*?are not addressed in this ESG report",
                    r"providing more environmental-friendly products",
                    r"recyclable products",
                ),
                "neglect_pattern": (
                    r"^\d+\..*?Supply Chain Management$",
                    r"^\d+\..*?Our Suppliers$",
                ),
            },
            {
                "name": "special_cells",
                # 'whole_table': True,
                "cell_pattern": [
                    r"B5.4",
                    r"Environmentally Preferable",
                ],
            },
            {
                "name": "kmeans_classification",
                "para_pattern": [
                    r"eco-friendly|environmentally preferable|local suppliers|B5.4|sustainability",
                    r"environmental friendly products",
                ],
            },
            DEFAULT_MODEL,
        ],
    },
    {
        #  一般企业产品责任很多无非都是保证服务质量，保证产品质量，所以如果不存在明显的有小标题的概括性文字，
        #  很多时候B6会包含B6.4
        #
        #  框选内容：**优先从index定位的位置去找以下内容**
        #  <有明显概括性文字的>，一般在product/service responsibly相关的大标题与小标题之间的文字段落。
        #  <不存在明显概括性文字的>，当不存在任何小标题时候，可以直接框选大标题段落第一段关于质量控制的文字段落，
        #  如果第一段文字也和质量要求无关，那么可以直接框B6.4的quality assurance
        #
        #  关键词：ordinance, Product Quality Law, Ordinance
        # （在这些关键词附近可同时关注Product Responsibility, responsible services, high quality services,
        # meet the expectation, satisfaction。product responsibility,
        # product quality, safety and health，responsible, service等）
        # 大标题 与下一个小标题之间 Product  Responsibility，
        "path": ["B6 policies - product responsibility"],
        "models": [
            {
                "name": "after_row_match",  # 未披露的情况
                "first_cell": False,
                "force_use_all_elements": True,  # file63636 | file63686
                "row_pattern": [
                    r"(B6(?![_\d\.])).*not (an? )?(applicable|been disclosed|considered|material topic)",
                    r"(B6(?![_\d\.])).*(?<![a-zA-Z])N/?A(?![a-zA-Z])",
                    r"(B6(?![_\d\.])).*不適用",
                    r"(B6(?![_\d\.])).*be disclosed in the future",
                    r"(B6(?![_\d\.])).*not involve(?:(?!(\.)).)*recall",
                ],
                "direct_answer_pattern": [
                    r"not (an? )?(applicable|been disclosed|considered|material topic)",
                    r"(?<![a-zA-Z])N/?A(?![a-zA-Z])",
                    r"不適用",
                    r"be disclosed in the future",
                    r"not involve(?:(?!(\.)).)*recall",
                ],
            },
            # 特殊的badcase uat
            # https://hkex.test.paodingai.com/#/hkex/esg-report-checking/report-review/196200?fileId=58245&schemaId=1&rule=B4%20policies%20-%20labour%20standards
            {
                "name": "syllabus_based",
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__B3.*?Development and Training",
                    r"__regex__QUALITY CUSTOMER SERVICES",
                    r"__regex__Service Standards$",
                ],
                "multi_elements": True,
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"as are not addressed in this ESG",
                        r"The.*?consumer complaint handling process is divided into",
                        r"formulated the|has formulated and improved",
                        r"Customer Service Quality Assurance",
                        r"(Communicating|communicate) with Customers",
                        r"based on the principle of limited authorization",
                        r"prevent data leakage and ensure data information security",
                        r"respects customers.*?personal financial information security rights",
                        r"By moved and professional service level, let customers feel being blessed during dining process",
                    ),
                    "neglect_pattern": (),
                },
                "table_model": "empty",
            },
            {
                # 只有第一段
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "only_first": True,
                "inject_syllabus_features": [
                    r"__regex__Product Responsibility__regex__General Disclosure",
                ],
            },
            {
                "name": "after_row_match",
                "first_cell": False,
                "force_use_all_elements": True,
                "first_row_as_title": True,  # file63998, 在目录中写了对应章节，但表格第二行才是目标cell
                "row_pattern": [r"Product Responsibility"],
                "direct_answer_pattern": [r"not applicable"],
                "just_a_para": True,
                "para_pattern": [
                    r"quality control and assurance",
                ],
            },
            {  # 匹配到B6标题时只取大标题与第一个子标题之间的内容
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "ignore_syllabus_pattern": [r"law"],  # file63950
                "only_before_first_chapter": True,  # file63947
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__Product Responsibilit__regex__Quality management",
                    # file63950 https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/894#note_230686
                    r"__regex__Product Responsibilit",
                ]
                + B6_CHAPTER_PATTERN,
            },
            {
                # 下面是B6.5的章节标题+B6章节
                # 特例badcase
                # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/997#note_243515
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN + [r"^Table\s\d"],
                "only_inject_features": True,
                "multi": True,
                "inject_syllabus_features": [
                    r"__regex__5\.3\.\s?PRODUCT RESPONSIBILITY",
                    r"__regex__5\.4\.\s?PRIVACY PROTECTION",
                    r"__regex__Providing High-quality Products",
                ],
            },
            {
                # B6大标题与子标题之间不能存在段落时，框选符合的章节，否则框选大标题下所有段落和章节
                # 下面是B6.4的章节标题+B6章节
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__(DEEPENING )?QUALITY CONTROL",  # file63952 file63987
                    r"__regex__Product Management",  # file63949
                    r"__regex__Quality Assurance",
                    r"__regex__Quality Control",
                    r"__regex__Product and Service Quality Management",
                    r"__regex__Product Quality( Management)?",
                    r"__regex__Service pledge to our customer",
                    r"__regex__Product Responsibilit",
                ]
                + B6_CHAPTER_PATTERN,
            },
            DEFAULT_MODEL,
        ],
    },
    {
        "path": ["B6 law compliance - product responsibility"],
        "models": [
            {
                "name": "twice_para_match",
                "multi_elements": True,
                "use_all_elements": True,
                # "strict_limit": True,
                "syllabus_regs": [
                    r"Product\s?Responsibility",
                    r"B6",
                    r"Responsible\s?Services",
                    r"Service Quality",
                    r"Product.*?Quality",
                    r"(HIGH )?QUALITY (PRODUCTS|services)",
                    r"safety and health",
                    r"responsible",
                    r"Safeguarding Confidential Matters",
                ],
                "neglect_syllabus_regs": [
                    r"Environmental Policy",
                    r"health and safety",
                    r"supply chain",
                    r"Anti-Corruption",
                    r"B[1-5]|B7|B8|A[1-3]",
                ],
                "paragraph_pattern": COMPLY_LAW_PATTERN,
                "neglect_pattern": (
                    r"complies with the corresponding national standards",
                    r"selecting supplier",  # file63987章节supply chain management识别错误
                    r"Supply Chain Management",  # file63973
                ),
                "second_pattern": (
                    r"(high quality|responsible) service",
                    r"meet the expectation",
                    r"satisfaction",
                    r"(product|service|management) (responsibilit|qualit|characteristic|standards)",  # standards file63952
                    r"(responsibilit(y|ies)|qualit(y|ies)) (product|service|management)",  # file63982
                    r"(health|quality|safety) and (health|quality|safety)",
                    r"data.*?(Ordinance|law)",
                    r"intellectual property",
                    r"relating to advertising",  # file63959
                    r"Accountability of Violations",  # file63952
                    r"responsibility or privacy",
                ),
            },
            {
                "name": "para_match",
                "multi_elements": True,
                "paragraph_pattern": (
                    r"responsible to comply with the requirements of the rules and regulations",
                    r"no material non-compliance.*data protection and privacy",
                    r"not involve in any confirmed violations of laws and regulations",
                    r"Product Eco-responsibility Ordinance",
                    r"Consumer Goods Safety Ordinance",
                    r"B6.*?are not the key material matters.*?are not addressed in this ESG report",
                ),
                "neglect_syllabus_regs": [
                    r"Environmental Policy",
                    r"health and safety",
                    r"supply chain",
                    r"Anti-Corruption",
                    r"B[1-5]|B7|B8|A[1-3]",
                ],
            },
            {  # fileid 63987 章节识别错误
                "name": "after_row_match",
                # 'include_row': True,
                "row_pattern": [r"B6\s"],
                # 'just_a_para': True,
                "multi_elements": True,
                "para_pattern": COMPLY_LAW_PATTERN,
                # 'direct_answer_pattern': [r'No product.*?recalls'],
            },
            {
                "name": "twice_row_match",
                "first_cell": False,
                "row_pattern": COMPLY_LAW_PATTERN,
                "second_pattern": [r"B6(\s+|$)"],
            },
            DEFAULT_MODEL,
        ],
    },
    {
        # 关于出于安全和健康原因而进行召回的已售或运输产品的数据的披露，可提取涉product recall的数据所在的图表或段落或句子，优先提取index，
        # 其次data summary，最后正文。
        # （关键词：recall，recovery，return）0或nil或no或not等否定描述是Y
        "path": ["KPI B6.1 - products recall"],
        "models": [
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__B6\.1",  # file63946
                ],
            },
            {
                "name": "para_match",
                "syllabus_regs": [
                    r"product.*recall",
                    r"Healthy Gaming",
                    r"Product.*Respons",
                    r"Safe Meals",
                    r"Product and Service Responsibility",
                ],
                "paragraph_pattern": (
                    r"B6.*?are not the key material matters.*?are not addressed in this ESG report",
                    r"recall(?:(?!\.).)*?reason",
                    r"recall.*product.*health and safety",
                    r"product recalls during the.*?Year",
                    r"In.*?period.*?product recall",
                    r"product.*?recall.*(?:concerns|reasons)",
                    r"no recall case",
                    r"recall of product",
                ),
            },
            {
                "name": "after_row_match",
                "include_row": True,
                "first_cell": False,
                "row_pattern": [r"B6.1"],
                "just_a_para": True,
                "multi_elements": True,
                "para_pattern": [r"recall|B6.1|return"],
                "direct_answer_pattern": [r"No product.*?recalls"],
            },
            {
                "name": "row_match",
                "multi": True,
                "first_cell": False,
                "force_use_all_elements": True,
                "ignore_index_table": True,
                "row_pattern": [
                    r"B6\.1.*not (an? )?(applicable|been disclosed|considered)",
                    r"B6\.1.*(?<![a-zA-Z])N/?A(?![a-zA-Z])",
                    r"B6\.1.*(不適用|不适用)",
                    r"B6\.1.*be disclosed in the future",
                    r"B6\.1.*not involve(?:(?!(\.)).)*recall",
                    r"B6\.1.*not( a)? material",
                    r"to (returns/)?recalls",
                    r"Number of products returned/recalled",  # file63965
                    r"percentage of product recall",
                ],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__Quality Assurance",
                    r"__regex__High-quality\s\w+",
                ]
                + B6_CHAPTER_PATTERN,
                "multi_elements": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"B6.1",
                        r"not have( any)?.*?products.*?to recall",
                        r"no.*?products.*?to recall",
                        r"no recalled products",
                        r"no (goods|orders).*?to recalls for product quality",
                        r"No.*?were recalled due to safety and health issues",
                    ),
                    "neglect_pattern": (
                        r"so as to protect.*?any potential health and safety issue",
                        r"creating higher return",
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "twice_para_match",
                "strict_limit": True,
                "multi_elements": True,
                "syllabus_regs": [
                    r"product responsibility",
                ],
                "paragraph_pattern": (
                    r"recall|B6.1|return",
                    r"not have( any)?.*?products.*?to recall",
                    r"no.*?products.*?to recall",
                    r"no recalled products",
                    # EXPLAIN_PATTERN,
                ),
                "neglect_pattern": (
                    r"so as to protect.*?any potential health and safety issue",
                    r"creating higher return",
                ),
                "second_pattern": (
                    EXPLAIN_PATTERN,
                    r"(had|was|were|is) (zero|\d+|no)",
                    r"not have (any)?",
                    r"not have( any)?.*?products.*?to recall",
                    r"no.*?products.*?to recall",
                    r"no recalled products",
                    r"product quality",
                ),
            },
        ],
    },
    {
        # 收到的与产品和服务相关的投诉数量以及处理方式，优先提取index，其次data summary，最后正文。
        # （关键词：complaint, service-related complaints）0或nil或no或not等否定描述是Y。
        # 不仅要提取投诉数量，还要提取文中描述的处理投诉的方式措施。
        "path": ["KPI B6.2 - products related complaints"],
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/852#note_217847
        # "pick_answer_strategy": "all",
        "models": [
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__Handling of Customer Complaints",
                    r"__regex__Customer relationship management",
                    r"__regex__Handling Complaint",  # file63979
                    r"__regex__Customer Complaint",  # file63948
                    r"__regex__B6\.2",
                ],
            },
            {
                "name": "after_row_match",
                "first_cell": False,
                "row_pattern": [r"B6.2"],
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "multi_elements": True,
                "para_pattern": [
                    r"complaint handling |respond to complaint|Compliance Department",
                    r"no.*?complaints received",
                    r"not receive any material compliant.*?product quality",
                    r"not receive any compliant.*?product",
                    r"not receive any \w+ complaints",  # file63972
                    r"opinion.*?products",
                    r"\d+ complaint",
                ],
            },
            {
                "name": "row_match",  # 未披露的情况
                "first_cell": False,
                "force_use_all_elements": True,
                "row_pattern": [
                    r"B6\.2.*not (an? )?(applicable|been disclosed|considered)",
                    r"B6\.2.*(?<![a-zA-Z])N/?A(?![a-zA-Z])",
                    r"B6\.2.*不適用",
                    r"B6\.2.*be disclosed in the future",
                    r"B6\.2.*not involve(?:(?!(\.)).)*recall",
                    r"B6\.2.*not material",
                ],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__product\s?responsibility",
                    r"__regex__Service Quality",
                    r"__regex__(Product\s?Responsibility|Responsible\s?Services|Quality\s?Assurance)",
                    r"__regex__SUMMARY OF KEY PERFORMANCE INDICATORS",  # file63969在表格中披露了
                ],
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "multi_elements": True,
                "multi": True,
                "multi_level": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"(?P<content>complaint(?:(?!of customer privacy).)*\.|B6\.2)",  # file64005
                        r"complaint.*?handling |respond to complaint|Compliance Department",
                        r"no complaints received",
                        r"not receive any material compliant.*?product quality",
                        r"not receive any compliant.*?product",
                        r"opinion.*?products",
                        r"\d+ complaint",
                        r"value customer feedback for continuous improvement",  # file63995
                        # EXPLAIN_PATTERN,
                    ),
                },
                "table_model": "row_match",
                "table_config": {
                    "first_cell": False,
                    "row_pattern": [r"B6\.2", r"complaints? received?"],
                },
            },
            {
                "name": "para_match",
                "multi_elements": True,
                "paragraph_pattern": (
                    r"(?P<content>complaint|B6.2)",
                    r"handle complaints",
                    r"no complaints received",
                    r"not receive any material compliant.*?product quality",
                    r"not receive any compliant.*?product",
                    r"opinion.*?products",
                    r"\d+ complaint",
                    r"B6.*?are not the key material matters.*?are not addressed in this ESG report",
                    # EXPLAIN_PATTERN,
                ),
                "syllabus_regs": [
                    r"Product\s?Responsibility",
                    r"B6",
                    r"Responsible\s?Services",
                ],
                "neglect_syllabus_regs": [
                    r"Anti-corruption",
                    r"Sustainability Pillars",
                    r"Product Health and Safety",
                    r"Labou?r Standards?",
                    r"Data Privacy",
                    r"B[1-5]|B[78]|A[1-3]",
                ],
            },
            {  # file63997
                "name": "row_match",
                "ignore_index_table": True,
                "row_pattern": [
                    r"B6.2",
                    r"Number of complaints",
                ],
            },
            DEFAULT_MODEL,
        ],
    },
    {
        # B6.3 Product Responsibility- Description of practices relating to observing and protecting intellectual property rights.
        # 描述与遵守和保护知识产权有关的做法。
        # （关键词：intellectual property rights, intellectual property protection）
        # E:
        # 判断依据：没有关于遵守和保护知识产权有关的做法，但是有解释
        # 需标注内容：涉及“not applicable”等的描述
        # ***遇到其他描述反馈并补充在公盘
        # 常见关键词：not applicable
        #
        # Y:
        # 判断依据：披露了intellectual property rights政策的披露
        # 需标注内容：提取intellectual property rights政策的段落。一般在Intellectual Property Rights相关标题下方
        # 常见关键词：intellectual property rights, intellectual property protection
        #
        # ND: 没有披露相关内容,也没有进行解释
        #
        # 定位：可从KPI index给出的位置去定位框选；或Product Responsibility / Responsible Services下小标题为Intellectual Property Rights/ Intellectual Property Protection标题下内容
        "path": ["KPI B6.3 - IP rights protection"],
        "models": [
            {
                "name": "after_row_match",
                "row_pattern": [r"B6\.3"],
                "remove_section_cn_text": True,
                # 'only_syllabus_of_para': True,
                "multi_elements": True,
                "first_cell": False,
                "para_pattern": [
                    r"(intellectual property|ip) (rights|protection)?",
                    r"copyright training",  # file64001
                    r"disclosing confidential information",  # file64001
                ],
            },
            {
                "name": "row_match",
                "ignore_index_table": True,
                "row_pattern": [
                    r"B6.3",
                ],
            },
            {
                "name": "syllabus_elt_v2",
                "multi": True,
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__^Intellectual Property( Rights| protection)?",  # file63959 标题会有 xx AND Intellectual Property的情况
                    r"__regex__Safeguarding Intellectual Property",
                    r"__regex__B6\.3 Intellectual Property",
                ],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__Protection of Intellectual Property Rights",
                    r"__regex__(Product\s?Responsibility|Responsible\s?Services)__regex__(Intellectual\s?Property\s?"
                    r"Rights|Intellectual\s?Property\s?Protection)",
                    r"__regex__intellectual\s?Property\s?Protection",
                    r"__regex__product\s?responsibility",
                    r"__regex__RESPONSIBLE OPERATION",  # file63959
                    r"__regex__intellectual Property Rights",
                ],
                "multi_elements": True,
                "include_title": False,
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        # r'Copyright Ordinance', # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/852#note_218211
                        r"IP rights",
                        r"intellectual property right",
                        r"intellectual property protection",
                        r"protect intellectual property and data and privacy",
                        r"protect.*?data and privacy",
                        r"protect.*?our data assets",
                        r"protection of intellectual property",
                        r"secures its intellectual property",
                        r"B6.3",
                        r"respects intellectual property",
                        r"Company Trademark|Intellectual Property",
                        # r'personal data.*?ordinance',  # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/852#note_218211
                        r"maintained our trademark portfolio",
                        r"prohibited to use the IP of the Group",
                        r"apply for the patents",
                        r"Patent Licensing Contract",
                        r"exercise the priority right to apply for the invalidity",
                        # EXPLAIN_PATTERN,
                    ),
                    "neglect_pattern": (
                        r"not have physical products for sale",
                        r"laws(?:(?!\.).)*?labelling",
                        r"ensure.*?complies.*?laws.*?intellectual property right",
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "row_match",
                "first_cell": False,
                "force_use_all_elements": True,
                "row_pattern": [rf"B6\.3.*({e_pattern})" for e_pattern in COMMON_EXPLAIN_PATTERN + [r"N/?A"]],
            },
            {
                "name": "para_match",
                "multi": True,
                "multi_elements": True,
                "force_use_all_elements": True,
                "paragraph_pattern": (
                    # r'(?P<content>Copyright Ordinance)',# https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/852#note_218211
                    r"(?P<content>IP (rights|protection))",
                    r"(?P<content>protection of intellectual property)",
                    r"(?P<content>intellectual property( protection| right))",
                    r"(?P<content>protect intellectual property and data and privacy)",
                    r"(?P<content>protect.*?data and privacy)",
                    r"(?P<content>protect.*?our data assets)",
                    r"(?P<content>secures its intellectual property)",
                    r"(?P<content>B6.3)",
                    r"(?P<content>respects intellectual property)",
                    r"(?P<content>Company Trademark|Intellectual Property)",
                    # r'(?P<content>personal data.*?ordinance)', # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/852#note_218211
                    r"(?P<content>maintained our trademark portfolio)",
                    r"(?P<content>prohibited to use the IP of the Group)",
                    r"B6.*?are not the key material matters.*?are not addressed in this ESG report",
                    # EXPLAIN_PATTERN,
                ),
                "neglect_pattern": (
                    r"not have physical products for sale",
                    r"laws.*?labelling",
                    r"form(?:(?!\.).)*intellectual property products",  # file63976
                    r"ensure.*?complies.*?laws.*?intellectual property right",
                ),
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__Intellectual Property Rights",
                ],
            },
            DEFAULT_MODEL,
        ],
    },
    {
        # B6.4： 侧重于产品及服务质量保证的具体流程及方法措施。
        #  有的可能会具体到产品从原材料采购到产出的质量检测及要求的流程图。相对于B6policy的概括性文字会描述得具体很多，
        #  有的文章可能直接整段讲具体质量保证措施，无法明确区分**B6policy的概括性文字**和B6.4，那么B6和B6.4的内容也会重合
        #
        #  框选内容：**优先从index定位的位置去找以下内容**
        #  <当存在明显小标题quality assurance/control或者B6.4>时，直接框选该标题下所有内容，
        #  <当不存在明显小标题>或内容描述质控过程措施，优先框关键词关于质量控制的段落，如果没有则可以框选客户投诉complaint处理措施，
        #  产品召回recall处理措施
        #
        #  关键词quality, performance check, rectification, recall，compliant， B6.4, quality assurance
        "path": ["KPI B6.4 - quality assurance process"],
        "models": [
            {
                "name": "row_match",  # 未披露的情况
                "first_cell": False,
                "force_use_all_elements": True,  # file63636 | file63686
                "row_pattern": [
                    # B6.4 或者 B6 not applicable
                    # B6Product Responsibility Not applicable as our business does not manufacture or trade in productsNA* file63665
                    r"(B6(?![_\d\.])|B6\.4).*(?<!recall procedure is )not (an? )?(applicable|been disclosed|considered)",
                    # 注意这里的(?<!recall procedure is )下面after_row_match有备注，file63615 | not considered file63688
                    r"(B6(?![_\d\.])|B6\.4).*(?<![a-zA-Z])N/?A(?![a-zA-Z])",  # file63617
                    r"(B6(?![_\d\.])|B6\.4).*不適用",  # file63617
                    r"(B6(?![_\d\.])|B6\.4).*be disclosed in the future",  # file63630
                    r"(B6(?![_\d\.])|B6\.4).*not involve(?:(?!(\.)).)*recall",  # file63684
                    r"(B6(?![_\d\.])|B6\.4).*Quality assurance(?:(?!(\.)).)*(don\'t|not) apply",  # file63709
                    r"(B6(?![_\d\.])|B6\.4).*not material",  # file63960
                ],
            },
            {
                "name": "para_match",  # 未披露的情况 从上面的row_match复制
                "paragraph_pattern": (
                    r"(B6(?![\W_\d\.])|B6\.4).*not (applicable|been disclosed|considered)",
                    r"(B6(?![\W_\d\.])|B6\.4).*(?<![a-zA-Z])N/?A(?![a-zA-Z])",
                    r"(B6(?![\W_\d\.])|B6\.4).*不適用",
                    r"(B6(?![\W_\d\.])|B6\.4).*be disclosed in the future",
                    r"(B6(?![\W_\d\.])|B6\.4).*not involve(?:(?!(\.)).)*recall",
                    r"(B6(?![_\d\.])|B6\.4).*Quality assurance(?:(?!(\.)).)*(don\'t|not) apply",  # file63709
                    r"(B6(?![_\d\.])|B6\.4).*not material",  # file63960
                    r"enhanc(?:(?!\.).)*service quality",  # file63961 双栏画框错误已提issue：docs_scriber#912#note_232484
                ),
            },
            {  # 较为精确的匹配
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "include_shape": True,
                "inject_syllabus_features": [
                    r"__regex__QUALITY ASSURANCE",
                    r"__regex__B6.4",
                ],
            },
            {  # 因为B6.4分为两部分，quality assurance和recall procedures但在index表格中常写在一个cell中
                # 可能存在recall procedures是E, 但quality assurance是Y的情况(file63951)
                # 这时下面的after_row_match因为关键词匹配所以direct_answer会直接返回E，所以要在此之前进行章节精确匹配quality assurance
                "name": "after_row_match",
                "row_pattern": [r"B6\.4"],
                "remove_section_cn_text": True,
                # 'only_syllabus_of_para': True,
                # 'multi': True,
                "first_cell": False,
                # 'para_pattern': [r'intellectual property (rights|protection)'],
            },
            # 章节全部 适用于有明显的小标题，范围较大
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "include_shape": True,
                "break_para_pattern": [
                    r"Anti-corruption$",
                ],
                "inject_syllabus_features": [
                    r"__regex__B6.4",
                    r"__regex__Complaints Handling Procedures",
                    r"__regex__(?<!Air )Quality Management",  # (?<!Air ) file63682
                    r"__regex__(PRODUCT AND|IMPROVING) SERVICE( RESPONSIBILITY| QUALITY)?",  # IMPROVING SERVICE QUALITY file63663
                    r"__regex__Product(/Service)? Quality( Management and Control)?",  # Product Quality file63705
                    r"__regex__Quality (Control|Assurance)( and Product Warranty)?",
                    # r'__regex__PRODUCT RESPONSIBILIT',  # B6 大章节标题
                    r"__regex__PRODUCT RESPONSIBILITY__regex__SERVICE|BUSINESS",  # issues/894#note_224590
                ],
            },
            # 仅仅第一段
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "only_first": True,
                "inject_syllabus_features": [
                    r"__regex__Product Responsibility__regex__KPI B6.4",
                ],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__Quality\s?and Safety\s?of\s?Services",
                    r"__regex__Quality Management",
                    r"__regex__PRODUCT(/Service)? RESPONSIBILIT",
                ],
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "multi_elements": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"(?<!-)quality|performance check|rectification|recall|complaint|B6.4",  # (?<!-)quality file63704
                        r"advertisements are mainly based on word-of-mouth",
                        r"complaints (were|are|was|is) (?:(?!(not|\.)).)* addressed",  # NewAdd: 被处理也算 file3
                        EXPLAIN_PATTERN,
                        r"Examples of|includes the following",
                        # r'^\d+\.',  # ?
                        r"^•",
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "special_cells",  # 段落识别为表格时
                "cell_pattern": [
                    # ---- 从上面的syllabus_elt_v2复制 START ----
                    r"B6.4",
                    r"Complaints Handling Procedures",
                    r"Quality Management",
                    r"(PRODUCT AND|IMPROVING) SERVICE( RESPONSIBILITY| QUALITY)?",  # IMPROVING SERVICE QUALITY file63663
                    r"Product Quality Management and Control",
                    r"Quality Control and Product Warranty",
                    r"Quality Control",
                    r"Quality Assurance",
                    # r'PRODUCT RESPONSIBILIT',  # B6 大章节标题
                    # r'PRODUCT RESPONSIBILITY__regex__.* SERVICE',  # issues/894#note_224590
                    # ==== 从上面的syllabus_elt_v2复制 END ====
                ],
            },
            {
                "name": "special_cells",  # 段落识别为表格时
                "cell_pattern": [
                    r"PRODUCT RESPONSIBILIT",  # file63664 段落识别为表格时
                ],
            },
            {
                "name": "para_match",
                "force_use_all_elements": True,  # file63650
                "paragraph_pattern": (
                    r"putt(ing)? (our )?customers? first",  # file63650
                    r"(?P<content>quality|performance check|rectification|recall|compliant|B6.4)",
                    r"B6.*?are not the key material matters.*?are not addressed in this ESG report",
                    EXPLAIN_PATTERN,
                ),
                "neglect_pattern": (r"(supplier|subcontractors).*?quality",),
            },
            DEFAULT_MODEL,
        ],
    },
    {
        # B6.5 Product Responsibility- Description of consumer data protection and privacy policies, and how they are implemented and monitored.
        # 描述消费者数据保护和隐私政策，以及它们是如何实施和监管的。
        # （关键词：privacy, personal data, confidentiality）；
        # E:
        # 判断依据：没有相关内容的披露，但是有解释
        # 需标注内容：不常见，可参考其他规则的E描述
        #
        # Y:
        # 判断依据：披露了消费者数据保护和隐私政策，以及它们是如何实施和监管的
        # 需标注内容：提取发行人的消费者数据保护和隐私政策，以及它们是如何实施和监管的段落。一般在Product Responsibility 标题下有小标题关于Privacy Protection的内容；
        # 常见关键词：privacy, personal data, confidentiality
        #
        # ND: 没有披露相关内容,也没有进行解释
        #
        # 定位：可从KPI index给出的位置去定位框选；或Product Responsibility / Responsible Services下小标题为Privacy Protection标题下内容
        "path": ["KPI B6.5 - consumer data protection"],
        "models": [
            {
                "name": "after_row_match",
                "row_pattern": [r"B6.5"],
                "remove_section_cn_text": True,
                "multi_elements": True,  # file63952 index表格中给的章节范围过大
                "para_pattern": [
                    r"^B6\.5$",
                    r"privacy|personal data|confidentiality",
                    r"information protection",
                ],
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "multi": True,
                "inject_syllabus_features": [
                    r"__regex__Customer (Data )?Privacy",
                    r"__regex__Customer Privacy Protection",
                    r"__regex__(Customer|Personal) Data protection",
                    r"__regex__Data (Privacy )?protection",  # data protection file63979
                    r"__regex__Data Protection and Privacy",
                    r"__regex__information Security Management",
                    r"__regex__data protection and privacy policies",
                    r"__regex__Information Security (and Privacy )?Protection",
                    r"__regex__Privacy Matters",
                    r"__regex__Client Privacy Protection",  # file64002大章节`Client Service and Privacy Protection`小章节`Client Privacy Protection`都包含`Privacy Protection`
                    r"__regex__Protecting information security",
                    r"__regex__Privacy and Information Security",
                    r"__regex__Product Responsibility__regex__privacy",
                    r"__regex__Confidential Information",
                    r"__regex__PRIVACY PROTECTION",
                ],
                "break_para_pattern": [
                    r"Aspect B7: Anti-corruption",
                ],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__(Product\s?Responsibility|Responsible\s?Services)__regex__Privacy\s?Protection",
                    r"__regex__Data\s?Privacy\s?Protection",
                    r"__regex__B6.*?SERVICES RESPONSIBILITY",
                ],
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "multi_elements": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"data privacy|personal data|confidentiality|B6.5",
                        r"(?P<content>safeguard the information of the Group)",
                        r"(?P<content>collected only when it is necessary)",
                        EXPLAIN_PATTERN,
                    ),
                    "neglect_pattern": (r"had no non-compliance cases.*?on.*?data privacy",),
                },
                "table_model": "empty",
            },
            {
                "name": "para_match",
                "multi_elements": True,
                "paragraph_pattern": (
                    r"protect customers.*?privacy",
                    r"(?P<content>safeguard the information of the Group)",
                    r"(?P<content>collected only when it is necessary)",
                    r"contractual obligation to protect the information of clients",
                    r"B6.*?are not the key material matters.*?are not addressed in this ESG report",
                    r"customer (information and )?privacy",
                    r"customer data protection",
                    r"confident(?:(?!(\.)).)*customer information",
                    r"information security",
                    EXPLAIN_PATTERN,
                ),
            },
            {
                "name": "row_match",  # 未披露的情况
                "first_cell": False,
                "force_use_all_elements": True,
                "ignore_index_table": True,
                "row_pattern": [
                    r"B6\.5",
                    r"consumer data protection",
                    r"privacy policies",
                ],
            },
            DEFAULT_MODEL,
        ],
    },
    {
        # B7-policy：关于anti-corruption政策的披露，可提取涉及到anti-corruption政策下的所有内容，一般在anti-corruption相关的标题下。
        # （关键词：anti-corruption，anti-money laundering）
        #       1. 情况一：在anti-corruption大标题及下一个小标题之间有内容，则建议框选两个标题中间这段内容；
        #
        #       2. 情况二：在anti-corruption大标题下没有任何一个小标题，则建议框选标题下第一段内容；
        #
        #       3. 情况三：在anti-corruption大标题及下一个小标题之间没有内容，则建议框选大标题下全部内容。
        "path": ["B7 policies - anti-corruption"],
        "models": [
            {
                # 章节标题和第一个小标题之间
                "name": "policy",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "neglect_features": [
                    r"Supply Chain Management",
                ],
                "ignore_pattern_match": True,
                "inject_syllabus_features": [
                    r"__regex__MORAL INTEGRITY AND ANTI-CORRUPTION__regex__Laws and Regulations",
                    r"__regex__ANTI-CORRUPTION POLICY AND WHISTLEBLOWER PROCEDURE",
                    r"__regex__Business Integrity, Anti-Corruption and Anti-Money Laundering",
                    r"__regex__Fair and Honest Operations",
                    r"__regex__BRINGING BUSINESS RESPONSIBILITIES INTO PRACTICE",
                    r"__regex__8.3.3Anti-corruption",
                    r"__regex__Anti-Corruption Policies",
                    r"__regex__ETHICAL BUSINESS OPERATION",
                    r"__regex__Anti-corruption and Anti-graft",
                ],
                "neglect_parent_features": [
                    r"Improving Sustainability Governance",
                ],
                "break_para_pattern": [
                    r"Works carried out.*?anti-corruption.*?as follows:",
                ],
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "only_first": True,
                "only_before_first_chapter": True,
                "inject_syllabus_features": [
                    r"__regex__Anti-Corruption__regex__KPI B7",
                    r"__regex__Anti-Corruption__regex__Anti-Corruption",
                    r"__regex__Policies and Procedures",
                ],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__Anti-Corruption",
                ],
                "multi_elements": True,
                "only_before_first_chapter": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"(?P<content>regards.*?laws)",
                        r"(?P<content>comply.*?laws)",
                        r"(?P<content>no corruption-related violations)",
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_before_first_chapter": True,
                "only_first": True,
            },
            {
                "name": "para_match",
                "syllabus_regs": [r"Responsibility Managemen", r"Anti-Corruption and Business Ethics"],
                "paragraph_pattern": (
                    r"anti-corruption policy",
                    r"(?P<content>regards.*?laws)",
                    r"avoidance of bribery and corruption",
                    r"Group has established policies on anti-corruption",
                    r"policy.*?(money[\-\s]laundering|anti[\-\s]corruption)",
                    r"comply.*?(money[\-\s]laundering|anti[\-\s]corruption)",
                    r"anti-money laundering",
                    r"internal anti-corruption efforts",
                ),
            },
            DEFAULT_MODEL,
        ],
    },
    {
        "path": ["B7 law compliance - anti-corruption"],
        "models": [
            {
                "name": "twice_para_match",
                "multi_elements": True,
                # "strict_limit": True,
                "syllabus_regs": [],
                "neglect_syllabus_regs": [
                    r"IMPROvE OuR vALuE CHAIN",
                    r"Sustainability Pillars",
                    r"Product Health and Safety",
                    r"Labou?r Standards?",
                    # r'Data Privacy',
                    r"B[1-6]|B8|A[1-3]",
                ],
                "paragraph_pattern": tuple(
                    list(COMPLY_LAW_PATTERN)
                    + [
                        r"not tolerate",  # file63958
                        r"not aware of",  # file63957
                    ]
                ),
                # 'neglect_pattern': (),
                "second_pattern": (
                    # r'relevant laws and regulations',
                    r"Anti-corruption",
                    r"Anti-money laundering",
                    r"Prohibition of Commercial Bribery",
                    r"bribery|money laundering",  # file63961
                ),
            },
            {
                "name": "para_match",
                "multi_elements": True,  # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/793#note_210675
                "strict_limit": True,  # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/793#note_210675
                "syllabus_regs": [r"ANTI-CORRUPTION"],
                "neglect_syllabus_regs": [
                    r"IMPROvE OuR vALuE CHAIN",
                    r"Sustainability Pillars",
                    r"Product Health and Safety",
                    r"Labou?r Standards?",
                    r"Data Privacy",
                    r"B[1-6]|B8|A[1-3]",
                ],
                "paragraph_pattern": (
                    r"There was no legal cases regarding corrupt practices",
                    r"During the Period, there are no legal cases",
                    r"(?P<content>not aware.*?money laundering)",
                    r"(?P<content>strictly comply with.*?law)",
                    r"complied with relevant laws and regulations",
                    r"(law|regulation|Ordinance).*?(money|anti[-\s]|Bribery)",
                    r"(money|anti[-\s]|Bribery).*?(law|regulation|Ordinance)",
                    # r'in line with laws and regulations',
                    # r'compliance with laws and regulations'
                    # r'not violate|no legal case|no violation',
                    # r'avoidance of bribery and corruption',
                ),
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__Anti-Corruption",
                ],
                "multi_elements": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"(?P<content>not aware.*?money laundering)",
                        r"(?P<content>no corruption-related violations)",
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "kmeans_classification",
                "threshold": 0.618,
            },
        ],
    },
    {
        # 报告期内对发行人或其雇员提起的以身结的腐败行为法律案件数量，优先index，其次data summary，最后正文。
        # （关键词：bribery，legal，case，lawsuit，corruption charges）
        #       1. 0或nil或no或not等否定描述是Y；
        #       2. 当描述中没有涉及到case或lawsuit，仅有没有违反法规的相关描述（e.g. no non-compliance)，应判断为Y。
        "path": ["KPI B7.1 - legal cases on corruption"],
        "neglect_table_cell_missing_crude_regs": [r"Health and Safety"],
        "models": [
            {  # 特例 stock 00228
                "name": "para_match",
                "multi_elements": True,
                "paragraph_pattern": (r"complied with relevant laws and regulations relating to anti-\s?corruption",),
            },
            {
                "name": "after_row_match",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "just_a_para": True,
                "row_pattern": [r"B7.1"],
                "direct_answer_pattern": [
                    r"no (concluded )?legal case",
                    r"Zero case",
                    r"Number of concluded cases",
                ],
                "para_pattern": [
                    r"not involved.*?corruption",
                    r"not aware of any non-compliance with the relevant laws and regulations",
                    r"no (concluded )?legal case",
                    r"not aware of.*?cases",
                ],
            },
            {
                "name": "row_match",
                "force_use_all_elements": True,
                "ignore_index_table": True,
                "row_pattern": [
                    r"Incidents of Corruption",
                    r"^Legal Cases",
                    r"^Number of concluded cases",
                ],
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"no serious breach or non-compliance with relevant laws",
                    r"No reports or related complaints",
                    r"material impact on the Company in terms of anti-corruption",
                    r"not notify any material non-compliance with the relevant laws",
                    r"concluded legal case",
                    r"no legal case",
                    r"not have any legal.*?corruption",
                    r"no confirmed.*?legal case",
                    r"not aware of.*?cases",
                    r"have not involved.*?legal ",
                    r"No corruption charges",
                    r"no legal.*?corruption",
                    r"no reported case of corruption",
                    r"no.*?activities occurr",
                    r"not identified any.*?cases of",
                    r"not identify any material non-compliance cases",
                    r"had no non-compliance cases regarding violations",
                    r"subsidiaries experienced no litigation brought against",
                    r"involved in anti-corruption litigation cases",
                    r"no material non-compliance with the relevant laws",
                    r"nor violation of regulations related to corruption",
                    r"nor.*?was involved in any corruption lawsuits",
                    r"no corruption lawsuits against",
                    r"no litigation cases of corruption",
                ),
            },
            {
                "name": "row_match",
                "ignore_index_table": True,
                "first_cell": True,
                "row_pattern": [
                    r"legal cases",
                    r"corruption cases",
                ],
            },
            {
                "name": "ar_esg_71",
                "threshold": 0.1,
            },
        ],
    },
    {
        # 描述预防措施和举报程序，以及如何实施和监管。
        # （关键词：whistle-blowing, whistleblowing, misconduct, preventive, prevention, prevent）
        # reporting channels
        "path": ["KPI B7.2 - preventive measures & whistle-blowing procedures"],
        "models": [
            {
                "name": "para_match",
                "multi_elements": True,
                # "combine_paragraphs": True,
                "paragraph_pattern": (
                    r"To prevent corrupt practices ",
                    r"reporting channels",
                    r"CEEPIL has established a complete complaint mechanism",
                    r"whistleblowers will be kept confidential to protect their privacy",
                ),
            },
            {
                "name": "syllabus_elt_v2",
                "only_inject_features": True,
                "multi": True,
                "inject_syllabus_features": [
                    r"__regex__Anti-Corruption__regex__KPI B7.2",
                    r"__regex__Customer Due Diligence",
                    r"__regex__Suspicious Transactions Reporting",
                    r"__regex__Gifts and benefits",
                    r"__regex__Financial Crime",
                    r"__regex__Whistle-blowing Mechanism and Whistleblower Protection",
                    r"__regex__GOVERNANCE AND ETHICS__regex__Whistleblowing",
                ],
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
            },
            {
                "name": "after_row_match",
                "row_pattern": [r"B7.2"],
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "para_pattern": [
                    r"whistle.*?blowing|misconduct|B7.2",
                    r"(?P<content>not tolerate any.*?business activities)",
                    r"reporting channels",
                ],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__Customer Due Diligence",
                    r"__regex__ANTI-CORRUPTION POLICY AND WHISTLEBLOWER PROCEDURE",
                    r"__regex__BRINGING BUSINESS RESPONSIBILITIES INTO PRACTICE",
                ],
                "multi_elements": True,
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"(?P<content>whistle.*?blowing|misconduct|B7.2)",
                        r"(?P<content>not tolerate any.*?business activities)",
                        r"encouraged to report on suspected business irregularities",
                        r"misconduct|prevention|preventive|prevent",
                        r"Whistle-?blowing",
                        r"Whistleblowing (procedures|scope)",
                        r"reporting mechanism",
                        r"reporting channels",
                    ),
                    # 'neglect_pattern': (
                    #     r'\d+\s?cases',
                    # )
                },
                "table_model": "special_cells",
                "table_config": {
                    "cell_pattern": [
                        r"whistle-blowing|misconduct",
                        r"reporting channels",
                    ]
                },
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"(?P<content>whistle.*?blowing|misconduct|B7.2)",
                    r"(?P<content>not tolerate any.*?business activities)",
                    r"(?P<content>violate the company’s rules, we will directly dismiss the employee)",
                    r"reporting channels",
                    r"Whistle-?blowing Mechanism",
                ),
            },
            {
                "name": "kmeans_classification",
                "para_pattern": [
                    r"whistle.*?blowing|misconduct|B7.2",
                    r"(?P<content>not tolerate any.*?business activities)",
                    r"reporting channels",
                ],
            },
            DEFAULT_MODEL,
        ],
    },
    {
        # 描述向董事和员工提供的反腐败培训，提取涉及到anti-corruption training相关的段落，
        # 有时候会出现在B3的相关段落下，也可提取（关键词：anti-corruption，training，seminar
        # 没有明确表明提供相关training, 只是说会把相关的政策告知employee，不属于training相关，应判断为ND
        "path": ["KPI B7.3 - anti-corruption training"],
        "models": [
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"anti.*?corruption.*?training|B7.3",
                    r"training.*?anti.*?corruption",
                    r"training|seminar",
                ),
            },
            {
                "name": "after_row_match",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "just_a_para": True,
                "row_pattern": [r"B7.3"],
                "para_pattern": [
                    r"anti.*?corruption.*?training|B7.3",
                    r"training",
                    r"(?<!terrorism )seminar",
                ],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__Anti-corruption",
                    r"__regex__Anti-corruption Training",
                    r"__regex__Anti-corruption and Integrity",
                ],
                "multi_elements": True,
                "include_title": False,
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"anti.*?corruption.*?training|B7.3",
                        r"training|seminar",
                    ),
                },
                "table_model": "special_cells",
                "table_config": {
                    "cell_pattern": [
                        r"whistle-blowing|misconduct",
                    ]
                },
            },
            {
                "name": "para_match",
                "multi_elements": True,  # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/803#note_212983
                "paragraph_pattern": (
                    r"anti.*?corruption.*?training|B7.3",
                    r"training.*?anti.*?corruption",
                    r"training|seminar",
                ),
            },
        ],
    },
    {
        # 社区参与相关的政策，以了解发行人经营所在社区的需求，并确保其活动考虑到社区的利益。
        # （关键词：community）
        # 一般在community investment/public welfare/Giving back to society/the community相关标题下的第一个段落描述，
        # 或第一个小标题下内容。E描述不常见。
        "path": ["B8 policies - community investment"],
        "neglect_para_missing_crude_regs": [
            r"focused on strengthening industry exchange",
            r"hopes to share cutting-edge information",
        ],  # 63993
        "models": [
            {
                "name": "syllabus_elt_v2",
                "only_inject_features": True,
                "only_before_first_chapter": True,
                "multi": True,  # file63947 章节识别错误，两个`COMMUNITY INVESTMENT`
                "break_when_table": True,  # file63947 避免table
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "inject_syllabus_features": [
                    # r'__regex__Supporting the Community',
                    r"__regex__Community Investment__regex__General Disclosure and KPIs",
                    r"__regex__Aspect B8",
                    r"__regex__COMMUNITY INVESTMENT",
                    r"__regex__OUR COMMUNITY",  # file63998
                    r"__regex__COMMUNITY VITALITY",  # file63993
                    r"__regex__Bringing Harmony to the Community",  # file63951
                    r"__regex__PUBLIC WELFARE ACTIVITIES",  # file63954
                ],
                "neglect_parent_features": [r"Materiality"],  # 63947章节识别问题
            },
            {
                "name": "row_match",
                "row_pattern": [
                    r"The Group actively performs its corporate social responsibility"
                ],  # file63951段落识别为表格
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__Aspect B8: Community Investment",
                    r"__regex__COMMUNITY INVESTMENT",
                    r"__regex__COMMUNITY CONTRIBUTION",
                ],
                "multi_elements": True,
                "only_before_first_chapter": True,
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"(?P<content>engagement|focus|key|dedicate|commit|B8\.1|community|contribut)",
                        r"(?P<content>encouraging.*?communities.)",
                    ),
                },
                "table_model": "empty",
                "neglect_parent_features": [r"Materiality"],  # 63947章节识别问题
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"(?P<content>)give back to the society",
                ),  # file63952需要排除下面的`Public Welfare Activit`规则
            },
            {
                "name": "para_match",
                "force_use_all_elements": True,
                "paragraph_pattern": (r"(?P<content>Public Welfare Activit)",),  # file 63970
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"participated in the",  # file63956
                    r"(?P<content>engagement|focus|key|dedicate|commit|B8.1|community|social responsibilit|donate)",  # social responsibilit file63952; key file63956因为donate被劈开了
                    r"(?P<content>encourag.*?(communit|society))",
                ),
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
            },
            DEFAULT_MODEL,
        ],
    },
    {
        # 公司重点在哪些领域做贡献，例如：education, environmental concerns, labour needs, health, culture, sport。
        # （关键词：engagement, focus, key, dedicate，commit）如果没有描述重点领域，可框选B8.2的内容
        "path": ["KPI B8.1 - community investment focus"],
        "models": [
            {
                "name": "after_row_match",
                "row_pattern": [r"B8\.1"],
                "multi": True,
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "para_pattern": [
                    r"engagement|focus|key|dedicate|B8.1",
                    r"community development",
                    r"community investment",
                    r"(?P<content>encouraging.*?communities)",
                ],
                "neglect_parent_features": [r"Materiality"],  # 63947章节识别问题
                "break_para_pattern": [r"Appendix"],  # file63956 段落跟index表格没有分开
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "multi": True,
                "inject_syllabus_features": [
                    r"__regex__Community Investment__regex__KPI B8\.1",
                    r"__regex__Community-in-need",  # file64000已能够正确识别英文双栏文档
                    r"__regex__Community Engagement",
                    r"__regex__SOCIAL WELFARE",
                ],
                "neglect_parent_features": [r"Materiality"],  # 63947章节识别问题
                "break_para_pattern": [r"Appendix"],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__kpi B8.1",
                    r"__regex__COMMUNITY INVESTMENT",
                ],
                "multi_elements": True,
                "break_when_table": True,
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"engagement|focus|key|dedicate|B8.1",
                        r"(?P<content>encouraging.*?communities)",
                        r"(?P<content>made donations)",
                        r"(?P<content>contribute to the (society|communiti))",
                        r"for social responsibilities purposes",
                        r"distribute gifts and coupons",
                        r"donat.*?to",
                        r"made great efforts in education",
                        r"education|environmental concerns|labour needs|health|culture|sport",
                        r"not(?:(?!\.).)*participate in(?:(?!\.).)*due to",
                        r"offer opportunities for our staff to participate in volunteering activities",
                        r"contribute to our community",
                        r"support community programmes",
                    ),
                },
                "table_model": "table_title",
                "table_config": {
                    "only_inject_features": True,
                    "feature_white_list": [
                        r"made donations",
                        r"捐贈",
                    ],
                },
                "neglect_parent_features": [r"Materiality"],  # 63947章节识别问题
                "break_para_pattern": [r"Appendix"],
            },
            {  # 如果没有描述重点领域，可框选B8.2的内容
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "multi": True,
                # "only_before_first_chapter": True,
                # "only_first": True,
                "include_shape": True,
                "inject_syllabus_features": [
                    r"__regex__Community Investment__regex__KPI B8\.2",
                    r"__regex__Community Investment",  # file63996有很多`XXX Continue`已能正确识别
                    r"__regex__Community (Service and Charity)",  # file64003 64002
                    r"__regex__Empowering the Community",  # file63948
                    r"__regex__Bringing Harmony to the Community",  # file63951
                ],
                "neglect_parent_features": [r"Materiality"],  # 63947章节识别问题
                "break_para_pattern": [r"Appendix"],
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"(?P<content>engagement|focus|key|dedicate|B8.1)",
                    # r'(?P<content>encourag.*?(communit|society))',
                ),
            },
            DEFAULT_MODEL,
        ],
    },
    {
        # 为重点领域贡献的资源（例如金钱或时间），优先提取index，再到data summary，最后正文。
        # （关键词：community，donation，prepare，provide, distribute）。
        # 表示未来计划会关注或投入community的描述，属于E（in the future, plan等），
        # 其他属于E的关键词还有stop, suspended, did not engage等
        "path": ["KPI B8.2 - resources contributed"],
        "models": [
            {
                "name": "after_row_match",
                "row_pattern": [r"B8.2"],
                "remove_section_cn_text": True,
                "include_shape": True,
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "multi_elements": True,
                "para_pattern": [
                    r"(?P<content>donat(ion|e)|prepare|provide|distribute|dedicate|B8.2)",
                    r"(?P<content>organised? (?:(?!\.).)* community (?:(?!\.).)* events)",  # file63998
                    r"(?P<content>Community public welfare)",  # file63970
                ]
                + [
                    r"\s*".join(keyword)
                    for keyword in ["donation", "donate", "prepare", "provide", "distribute", "dedicate"]
                ],  # file63956字符劈开
                "middle_rows": True,
                "answer_table_first_row_as_title": True,
                "answer_both_syllabus_and_table": True,
                "table_title": [
                    r"(?P<content>donat(ion|e)|prepare|provide|distribute|dedicate|B8.2)",
                    r"(?P<content>organised? (?:(?!\.).)* community (?:(?!\.).)* events)",  # file63998
                    r"(?P<content>Community public welfare)",  # file63970
                ]
                + [
                    r"\s*".join(keyword)
                    for keyword in ["donation", "donate", "prepare", "provide", "distribute", "dedicate"]
                ],  # file63956字符劈开,
                "break_para_pattern": ["Appendix"],
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "multi": True,
                # "only_before_first_chapter": True,
                # "only_first": True,
                "break_when_table": True,
                "include_shape": True,
                "inject_syllabus_features": [
                    r"__regex__Community Investment__regex__KPI B8\.2",
                    r"__regex__Community Investment",  # file63996有很多`XXX Continue`已能正确识别
                    r"__regex__Community (Service and Charity|Care)",  # file64003 64002
                    r"__regex__Empowering the Community",  # file63948
                    r"__regex__Bringing Harmony to the Community",  # file63951
                ],
                "neglect_parent_features": [r"Materiality"],  # 63947章节识别问题
                "break_para_pattern": [r"Appendix"],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__kpi B8.2",
                    r"__regex__COMMUNITY INVESTMENT",
                    r"__regex__COMMUNITY BUILDING",  # file63959
                    r"__regex__Community Service and Charity",
                ],
                "multi_elements": True,
                "break_when_table": True,
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "paragraph_model": "para_match",
                "include_shape": True,
                "multi": True,
                "para_config": {
                    "paragraph_pattern": (
                        r"(?P<content>donation|provide|distribute|dedicate|B8.2)",
                        r"(?P<content>made donations)",
                        r"(?P<content>cash donations)",
                        r"donat(?:(?!\.).)*?to",
                        r"for social responsibilities purposes",
                        r"distribute gifts and coupons",
                        r"participated in",
                        r"delivered to the needy",
                        r"support (the )?community",
                        r"contribution to (the )?community",
                        r"participate in",
                        r"\$[\d,]+",
                        r"contribute to our community",
                        r"support community programmes",
                        # r'(?P<content>contribution)',  # 遇到badcase 添加更详细的正则
                        # r'(?P<content>prepare)',  # 遇到badcase 添加更详细的正则
                    ),
                },
                "table_model": "table_title",
                "table_config": {
                    "only_inject_features": True,
                    "feature_white_list": [
                        r"made donations",
                        r"捐贈",
                    ],
                },
                "break_para_pattern": [r"Appendix"],
            },
            {
                "name": "para_match",
                # 'multi': True,
                "paragraph_pattern": (
                    r"\$[\d,]+",
                    r"(?P<content>community|donation|prepare|provide|distribute|B8\.2)",
                    r"(?P<content>contribution)",
                    r"(?P<content>prepare|provide|distribute)",
                    r"(?P<content>cash donations)",
                ),
            },
            DEFAULT_MODEL,
        ],
    },
]

prophet_config = {"depends": {}, "predictor_options": predictor_options}
