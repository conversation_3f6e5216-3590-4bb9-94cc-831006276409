policy_esg_e5_prompt = """你的任务是分析ESG报告中的文本元素，提取采用指定场景进行气候相关方案分析的信息。

1. 确认发行人是否已经进行了 climate-related scenario analysis，且使用了 IPCC、IEA、NGFS、RCP、SSP、NZE 等 climate-related scenario analysis sources 进行风险分析。
   - 若未明确提及或未引用以上sources进行情景分析，则返回 enum: No Disclosure。
   - 没有使用特定的气候情景分析来源, 需返回 enum: No Disclosure。

2. 若发行人已进行 climate-related scenario analysis，则提取相关信息，并确保满足以下要求：
   - 需明确列出所采用的 scenario source，并给出解释。
   - 识别的风险需关联到具体气候场景（如 "Typhoon"、"Extreme Heat"、"Flood" 等），不可接受泛泛的分类（如 "Extreme weather"、"Climate risks"）。
   - 提供风险程度、影响及应对措施的信息。
   - 提取包含具体气候情景的风险评估和应对的数据表格。
   - 排除以下场景: 极端天气变化, 自然资源稀缺, 强台风, 强的台风, 气温/温度上升, 三种气候风险, 极端降水, Stronger Typhoon, 强台风, 气候变化, 暴雨天气, 照常经营, 气候相关, ERM, 关注的气候相关风险

3.【数据格式化】
按以下JSON结构组织结果, 其中items仅保留comply相关的：
{
  "items": [
    {
      "index": 保持与输入的数据一致,
      "element_sequence": 保持与输入的数据一致,
      "reasoning": 你的理由和总结(使用简体中文)
    }
  ],
  "enum": "Comply|No Disclosure",
  "reasoning": "分类依据说明（使用简体中文）"
}
"""


policy_esg_e6_prompt = """
你的任务是负责从ESG报告中提取和分析与风险情景分析标准或来源相关的信息，特别关注IPCC、IEA、NGFS或其他相关来源。您的目标是确定给定文本是否披露了使用了哪些与气候相关的情景分析来源，并将最高优先级放在明确的使用声明上。

文本数据中的每个条目将具有以下结构:
{
    "index": 文本索引,
    "text": 文本内容,
    "element_sequence": 文本中的位置序号
}

你的任务是分析这些数据，并以以下格式给出回应：

ResponseModel:
- index: int | None
- position: int | None

ResponseModels:
- items: List[ResponseModel]  # 仅保留comply相关的
- enum: "Comply" | "No Disclosure" | None
- reasoning: str | None

要完成此任务，必须严格遵循以下步骤：
1. 发行人使用了哪些climate-related scenario analysis sources的哪种场景进行了气候相关风险分析。
2. 关注IPCC、IEA、NGFS或其他相关来源：
    - IPCC: 政府间气候变化专门委员会，包括RCP和SSP情景.
    - IEA: 国际能源机构，包括NZE。
    - NGFS: 绿色金融体系网络，包括NZE和Net Zero 2050 Scenario and Below 2°C Scenario。
3. 未明确具体场景需要排除, 以下场景也需要排除: GWP

最后，按照如下格式显示输出：
{
  "items": [
    {
      "index": int,
      "element_sequence": int,
      "reasoning": 你的理由和总结(使用简体中文)
    },
    ...  # items仅保留comply相关的
  ],
  "enum": "Comply" or "No Disclosure",
  "reasoning": "Brief explanation of your classification" (使用简体中文)
}

现在，分析以下文本元素：
"""


policy_esg_e8_prompt = """
你是一名人工智能助理，专门分析环境、社会和治理（ESG）报告，重点是温室气体排放披露。您的任务是检查所提供的ESG数据，并确定报告是否充分披露了范围3的温室气体排放源。

文本数据中的每个条目将具有以下结构：
{
    "index": 原文段落编号,
    "text": 文本内容,
    "element_sequence": 段落位置序号,
}

您的分析应侧重于识别提及的范围3温室气体排放源。《温室气体议定书》将第3类排放分为以下15类：
1. Purchased goods and services
2. Capital goods
3. Fuel- and energy-related activities not included in Scope 1 or Scope 2
4. Upstream transportation and distribution|Upstream transportation & distribution
5. Waste generated in operations|wastepaper disposal
6. Business travel
7. Employee commuting
8. Upstream leased assets|Upstream emissions
9. Downstream transportation and distribution|Downstream transportation & distribution
10. Processing of sold products
11. Use of sold products
12. End-of-life treatment of sold products
13. Downstream leased assets|Downstream emissions
14. Franchises
15. Investments

分析过程:
1. 仔细检查ESG数据中提到的这些类别或相关活动。
2. 对于每个类别，引用找到的任何相关信息，包括文本的索引和位置，并解释其与范围3类别的相关性。
3. 在分析了所有类别之后，总结你的发现。
4. 考虑每种可能分类的论据："Comply" 或 "No Disclosure"。
5. 根据你的发现和论点确定适当的分类。
6. 如果内容格式仅为分类名词，数值，则可将其归于该分类。
7. 如果仅提出了部分分类，也可以归类为"Comply"

将分析包装在<scope3_analysis>标签中。在这些标签中，系统地浏览每个Scope 3类别，注意找到的任何相关信息。在你的分析之后，用下面的结构给出你的最终答案：

ResponseModel:
- index: int 保持与输入的数据一致
- position: int | None  保持与输入的数据一致
- enum: "Comply" | "No Disclosure"
- reasoning: str | None


ResponseModels:
- items: List[ResponseModel]
- enum: "Comply" | "No Disclosure"
- reasoning: str | None


分类标准:
- "Comply": 报告明确披露了哪些类型的第3类温室气体排放源被包括在内。
- "No Disclosure": 报告并未披露包括哪类第3类温室气体排放源。

在你的推理中，解释你的思维过程并证明你的分类是正确的。如果您将其归类为“Comply”，请提及披露了哪些特定的范围3类别。
如果你归类为“No Disclosure”，解释为什么你认为必要的信息没有出现。

给出的所有数据都需要进行分类, 最终答案中的index、position必须和输入的数据保持一致

记住，在进行分类之前，要彻底分析并考虑所有提供的信息。

以下是ESG数据来进行分析:
"""
