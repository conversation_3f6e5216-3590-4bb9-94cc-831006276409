from remarkable.common.common_pattern import P_PERIOD_SEPARATOR
from remarkable.common.constants import AnswerValueEnum
from remarkable.common.pattern import MatchMulti, PatternCollection, PositionPattern, SplitBeforeMatch

P_B98 = SplitBeforeMatch.compile(
    MatchMulti.compile(
        PositionPattern.compile(
            r"\b(issuers?|directors?|boards?|compan(y|ies)|Nomination Committee|NC)\b",
            r"\b(of|maintains?)\s*the\s*view|\b(consider|confirm|review|assess)(s|ed|ing)?\b",
            r"\bindependen(ce|t)\b",
        ),
        r"\bINED|\bindependent non-executive director",
        operator=all,
    ),
    separator=P_PERIOD_SEPARATOR,
    operator=any,
)

predictor_options = [
    {
        "path": ["B98", "Content"],
        "models": [
            {
                "name": "syllabus_based",
                "only_inject_features": True,
                "neglect_syllabus_regs": [r"Board Diversity Policy"],
                "inject_syllabus_features": [
                    r"__regex__Independent non-executive Directors",
                    r"__regex__Board of Directors",
                    r"__regex__Confirmation of Independence",
                    r"__regex__Appointment and Re-election of Director",
                    r"__regex__^THE BOARD$",
                    r"__regex__Our view on independence",
                ],
                "multi": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "neglect_pattern": [
                        # http://************:55647/#/project/remark/259343?treeId=9735&fileId=69083&schemaId=15&projectId=17&schemaKey=B98
                        r"\baffect.*?suitabit yasa director",  # 影响某人出任董事
                        r"such non-executive (role|directorship).*?(has|have|had).*?bearings on.*?independence",
                    ],
                    "paragraph_pattern": P_B98,
                },
                "skip_syllabus_title": True,
                "enum": AnswerValueEnum.PS.value,
            },
            # http://************:55647/#/project/remark/268382?projectId=17&treeId=18134&fileId=70532&schemaId=15&schemaKey=B98
            # 最近的标题识别为段落，使用父章节定位
            {
                "name": "syllabus_based",
                "multi": True,
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__CORPORATE GOVERNANCE REPORT|REPORT ON CORPORATE GOVERNANCE",
                ],
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": SplitBeforeMatch.compile(
                        MatchMulti.compile(
                            r"\b(issuers?|directors?|boards?|compan(y|ies)|Nomination Committee|NC)\b",
                            r"of\s*the\s*view|consider(s|ed)?|confirm(s|ed|ing)?",
                            r"\bindependen(ce|t)\b",
                            r"rule 3\.13",
                            operator=all,
                        ),
                        separator=P_PERIOD_SEPARATOR,
                        operator=any,
                    )
                },
                "skip_syllabus_title": True,
                "enum": AnswerValueEnum.PS.value,
            },
            {
                "name": "special_cells",
                "extend_candidates_syllabus_regs": r"CORPORATE GOVERNANCE REPORT|REPORT ON CORPORATE GOVERNANCE",
                "ignore_col_header": True,
                "enum": AnswerValueEnum.PS.value,
                "neglect_cell_pattern": [
                    r"principal roles and responsibilities of the board",
                    r"process of re-electing incumbent Directors by the (Nomination Committee|NC)",
                ],
                "cell_pattern": P_B98,
            },
            {
                "name": "score_filter",
                "threshold": 0.75,
                "pattern": P_B98,
                "skip_syllabus_title": True,
                "enum": AnswerValueEnum.PS.value,
            },
        ],
    },
]

if __name__ == "__main__":
    text = """The process of re-electing incumbent Directors by the NC is as follows:
1.    the NC would assess the performance of the Director in
accordance with the performance criteria set by the Board
further elaborated below and consider the current needs of
the Board; and
2.    subject to the NC’s satisfactory assessment, the NC would
recommend the proposed re-appointment of the Director to
the Board for its consideration and approval.
Criteria to be considered as part of the process for the re-
appointment of Directors include the composition and progressive
renewal of the Board and each Director’s competencies, commitment,
contribution and performance (e.g. attendance, preparedness,
participation and candour).
As a broad-based nomination policy, the Board’s nomination process
for evaluating an executive Director vis-à-vis an INED is different.
For an executive Director, the nomination process would in general
be tied to his or her ability to contribute through his or her business
acumen and strategic thinking process for the business. As for
an INED, his or her nominations are hinged on a myriad of criteria
whereby he or she should possess the independence of mind despite
confirmation via in writing, as evaluated by the NC. The NC and the
Management have assessed and are satisfied that the existing INEDs
was able to give an independent view to take the Group’s business
to a higher level."""
    text = text.replace("\n", "")
    PP_B98 = PatternCollection([P_B98])
    print(P_B98.search(text))
