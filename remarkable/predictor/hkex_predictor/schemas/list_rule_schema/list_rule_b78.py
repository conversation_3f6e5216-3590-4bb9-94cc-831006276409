from remarkable.common.common_pattern import P_FOLLOW_PREFIX
from remarkable.common.constants import AnnualReportEnum, AnswerValueEnum
from remarkable.common.pattern import MatchMulti, NeglectPattern, SplitBeforeMatch
from remarkable.common.protocol import SearchPatternLike
from remarkable.common.util import clean_txt
from remarkable.predictor.common_pattern import R_CANCELLED
from remarkable.predictor.hkex_predictor.models.yoda_layer.base import BaseLocator, FollowMustExist
from remarkable.predictor.hkex_predictor.models.yoda_layer.filter import TParaTextFilter
from remarkable.predictor.hkex_predictor.models.yoda_layer.form import TableCellAsPos
from remarkable.predictor.hkex_predictor.models.yoda_layer.rule.by_pos import DefaultLocator, ParaFromPos, TableFromPos
from remarkable.predictor.hkex_predictor.models.yoda_layer.rule.root import ScoreDestFilter, ScoreFirstTableLocator
from remarkable.predictor.hkex_predictor.schemas.list_rule_schema.common_models import get_no_grant_models
from remarkable.predictor.hkex_predictor.schemas.list_rule_schema.list_rule_b76 import P_NEG_TITLE_OPTION
from remarkable.predictor.hkex_predictor.schemas.list_rule_schema.list_rule_b94 import (
    P_NS_COL_VALUE,
    P_PS_COL_VALUE,
)
from remarkable.predictor.hkex_predictor.schemas.pattern import (
    P_NUMBER_PS,
    R_AWARD_KEYWORD,
    R_AWARD_SHARES,
    R_DURING_THE_YEAR_NO_END,
    R_OPTION_KEYWORD,
)
from remarkable.predictor.schema_answer import TableCellsResult

R_LAPSED = r"(lapsed?|lasped?)"
R_FORFEITED = r"forfeited"

P_CANCELLED_LAPSED = SplitBeforeMatch.compile(
    MatchMulti.compile(R_CANCELLED, R_LAPSED, operator=all), separator="\t", operator=any
)
P_LAPSED = SplitBeforeMatch.compile(MatchMulti.compile(R_LAPSED, operator=any), separator="\t", operator=any)
P_LAPSED_WITHOUT_CANCEL = SplitBeforeMatch.compile(
    NeglectPattern.compile(match=R_LAPSED, unmatch=R_CANCELLED), separator="\t", operator=any
)
P_NEG_HEADERS = MatchMulti.compile(r"(number|\bno\.)\s*of\s*(share\s*)options", operator=any)
# P_NS_78 = MatchMulti.compile(
#     rf"no ((restricted|Matching)\s*)?(Shares?|{R_AWARD}|RSUs)[^.]*(grant|cancelled|lapse)",
#     rf"no unvested {R_AWARD} grant",
#     r"not? grant(ed)? (any|of) restricted shares?",
#     r"no outstanding share",
#     rf"None {reg_words(1,2)} shares",
#     rf"mandate has not? {reg_words(1,2)} used {reg_words(1,2)} {R_LAPSED}",
#     rf"{R_LAPSED}\s*due\s*{reg_words(1,2)}termination",
#     operator=any,
# )
#
# P_NS_SKIP_78 = MatchMulti.compile(
#     r"share options?",
#     rf"under {reg_words(1,3)} grant",
#     NeglectPattern.compile(
#         match=MatchMulti.compile(
#             # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/3304#note_457340
#             R_DURING_THE_YEAR,
#             r"financial year",
#             operator=any,
#         ),
#         unmatch=MatchMulti.compile(
#             # NeglectPattern.compile(
#             #     match=R_LAPSED,
#             #     unmatch=rf"No {reg_words(1,2)}((granted|vested|cancelled|{R_LAPSED})(\s*and\s*|\s*or\s*|\s*,\s*)?){{1,4}}",
#             # ),
#             R_LAPSED,
#             rf"adoption {reg_words(1,3)} Share Award (Scheme|Plan)",
#             r"up to the date of this annual report",
#             operator=any,
#         ),
#     ),
#     operator=any,
# )


def select_table_col_row(
    header_pattern,
    value_pattern,
):
    return TParaTextFilter(
        NeglectPattern.compile(
            match=MatchMulti.compile(
                header_pattern,
                value_pattern,
                operator=all,
            ),
            unmatch=r"number\s*of\s*(share\s*)?options",
        )
    ).as_table_filter("col", "row")


def table_col_row_locator(
    enum_value: str,
    header_pattern: SearchPatternLike | str,
    value_pattern: SearchPatternLike | str,
    locator: BaseLocator = DefaultLocator(),
    **kwargs,
):
    return {
        "name": "yoda_layer",
        "threshold": 0,
        "enum": enum_value,
        "neglect_syllabus_regs": NeglectPattern.compile(match=R_OPTION_KEYWORD, unmatch=R_AWARD_KEYWORD),
        "rule": ScoreFirstTableLocator(
            dest=TableFromPos.from_bothway(
                start=0,
                limit=0,
                filter=select_table_col_row(
                    header_pattern,
                    value_pattern,
                ),
                types=("col", "row"),
                adapter=TableCellAsPos(size=20),
            ).link_next(locator=locator, **kwargs),
        ),
    }


def enum_func(table_results: list[TableCellsResult], footnotes: list):
    table_cells = [cell for result in table_results for cell in result.parsed_cells]
    if any(P_NUMBER_PS.search(clean_txt(cell.text)) for cell in table_cells):
        return AnswerValueEnum.PS.value
    return AnswerValueEnum.NS.value


predictor_options = [
    {
        "path": ["B78"],
        "fake_leaf": True,
        "models": [
            {
                "name": "group_based",
                "share_type": "award",
                "group_default_enum": AnnualReportEnum.ND.value,
                "column_configs": {
                    "Content": [
                        {
                            "name": "no_share",
                            "with_element_box": True,
                            "enum": AnnualReportEnum.NS.value,
                        },
                        {
                            "name": "special_cells",
                            "multi": True,
                            "title_patterns": MatchMulti.compile(R_OPTION_KEYWORD, R_AWARD_KEYWORD, operator=all),
                            "neglect_header_pattern": P_NEG_HEADERS,
                            "row_pattern": [r"\brsus\b"],
                            "col_header_pattern": ["nature", P_LAPSED_WITHOUT_CANCEL],
                            "enum_function": enum_func,
                        },
                        {
                            "name": "special_cells",
                            # "title_patterns": R_TABLE_TITLES,
                            "neglect_title_patterns": P_NEG_TITLE_OPTION,
                            "neglect_header_pattern": P_NEG_HEADERS,
                            "col_header_pattern": P_LAPSED_WITHOUT_CANCEL,
                            "enum_function": enum_func,
                        },
                        table_col_row_locator(AnnualReportEnum.NS, P_LAPSED, P_NS_COL_VALUE),
                        table_col_row_locator(AnnualReportEnum.PS, P_LAPSED_WITHOUT_CANCEL, P_PS_COL_VALUE),
                        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/4571#note_521311
                        table_col_row_locator(
                            AnnualReportEnum.PS,
                            P_CANCELLED_LAPSED,
                            P_PS_COL_VALUE,
                            locator=ParaFromPos(
                                pattern=MatchMulti.compile(r"lapsed during the year", P_FOLLOW_PREFIX, operator=all),
                            ),
                            filter=FollowMustExist(),
                        ),
                        table_col_row_locator(AnnualReportEnum.ND, P_CANCELLED_LAPSED, P_PS_COL_VALUE),
                        # B74~B78通用：no award was granted + outstanding期初期末都为0
                        *get_no_grant_models(R_LAPSED),
                        # 不能拆分句子适配，适用于 During time , (i) ;(ii) no grant; and (iii) 这样情况
                        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/4571#note_521595
                        {
                            "name": "yoda_layer",
                            "threshold": 0,
                            "multi_elements": True,
                            "enum_from_multi_element": True,
                            "enum": AnswerValueEnum.NS.value,
                            "rule": ScoreDestFilter(
                                dest=ParaFromPos(
                                    start=0,
                                    pattern=MatchMulti.compile(
                                        MatchMulti.compile(
                                            MatchMulti.compile(
                                                rf"\b(no|none\s*of\s*the\s*|nor\s*any)\s*{R_AWARD_SHARES}",
                                                R_LAPSED,
                                                rf"{R_DURING_THE_YEAR_NO_END}|since\s*(the|its)\s*adoption",
                                                operator=all,
                                            ),
                                            operator=any,
                                        ),
                                        operator=any,
                                    ),
                                ),
                            ),
                        },
                    ],
                },
            },
        ],
    },
]
