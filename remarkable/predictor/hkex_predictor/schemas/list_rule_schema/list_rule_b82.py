import re

from remarkable.common.common_pattern import P_SEN_SEPARATOR, R_CHAPTER_PREFIX
from remarkable.common.constants import AnnualReportEnum
from remarkable.common.pattern import (
    MatchMulti,
    NeglectPattern,
)
from remarkable.predictor.common_pattern import R_EN_NUM
from remarkable.predictor.hkex_predictor.models.yoda_layer.rule.by_pos import (
    SentenceFromSplitPara,
)
from remarkable.predictor.hkex_predictor.models.yoda_layer.rule.root import (
    ScoreDestFilter,
)
from remarkable.predictor.hkex_predictor.pattern import R_DAY
from remarkable.predictor.hkex_predictor.schemas.pattern import (
    R_AWARD_KEYWORD,
    R_ENG_ORDER,
    R_NO_AWARD_NS,
    R_OPTION_KEYWORD,
)

R_TABLE_TITLE = [
    r"share\s*award\s*scheme",
]


P_NEG_OPTION = NeglectPattern.compile(match=R_OPTION_KEYWORD, unmatch=R_AWARD_KEYWORD)
R_PERIOD = rf"\b(\d+|{R_EN_NUM})[)）]?\s*(year|month|day)|{R_ENG_ORDER}\s*anniversary"


P_B82_NS = MatchMulti.compile(
    r"(was|has|been)\s*terminated",
    # http://************:55647/#/project/remark/250474?treeId=5005&fileId=68535&schemaId=15&projectId=17&schemaKey=B82
    r"resolved\s*to\s*terminate",
    # http://************:55647/#/project/remark/250763?treeId=9450&fileId=68583&schemaId=15&projectId=17&schemaKey=B82
    r"resolved\s*by\s*the\s*Board\s*to\s*terminate",
    operator=any,
)


def sentence_model(enum_value, ordered_patterns, skip_patterns=None, threshold=0.0):
    skip_patterns = skip_patterns or []
    return {
        "name": "yoda_layer",
        "threshold": threshold,
        "enum": enum_value,
        "skip_syllabus_title": True,
        "rule": ScoreDestFilter(
            skip_pattern=P_NEG_OPTION,
            dest=SentenceFromSplitPara(
                separator=re.compile(
                    rf"{P_SEN_SEPARATOR.pattern}|\sto\s*replace\s|replaced\s*by|instead\s*(of\s*)?|\band\s*a\s*new",
                    re.I,
                ),
                ordered_patterns=ordered_patterns,
                skip_pattern=MatchMulti.compile(
                    R_OPTION_KEYWORD,
                    r"number\s*of\s*shares",
                    # http://************:55647/#/project/remark/250363?treeId=6141&fileId=68516&schemaId=15&projectId=17&schemaKey=B82&page=47
                    r"adopted\s*(two|three|four|five|[2-9])",
                    *skip_patterns,
                    operator=any,
                ),
            ),
        ),
    }


predictor_options = [
    {
        "path": ["B82"],
        "fake_leaf": True,
        "models": [
            {
                "name": "group_based",
                "share_type": "award",
                "group_default_enum": AnnualReportEnum.ND.value,
                "check_consumed": False,
                "column_configs": {
                    "Content": [
                        # 模型1: 通用NS场景
                        {
                            "name": "no_share",
                            "with_element_box": True,
                        },
                        # 模型2: NS之没有 share award
                        sentence_model(AnnualReportEnum.NS.value, [MatchMulti.compile(*R_NO_AWARD_NS, operator=any)]),
                        # 模型3：章节标题包含remaining life，可能NS也可能PS
                        {
                            "name": "para_match",
                            "skip_syllabus_title": True,
                            "syllabus_regs": [r"remaining\s*life", rf"{R_CHAPTER_PREFIX}duration$"],
                            "paragraph_pattern": [r".*"],
                        },
                        # 模型4：明确的remaining life句子
                        sentence_model(
                            AnnualReportEnum.PS.value,
                            [
                                MatchMulti.compile(r"remaining\s*life", R_PERIOD, operator=all),
                            ],
                            skip_patterns=[r"appoint|M(r|r?s)\."],
                        ),
                        # 模型5：NS之scheme已过期
                        sentence_model(AnnualReportEnum.NS.value, [P_B82_NS], skip_patterns=[r"\b(if|until)\b"]),
                        # 模型6：不太明确的PS场景，限制分值
                        sentence_model(
                            AnnualReportEnum.PS.value,
                            [
                                MatchMulti.compile(
                                    r"\b((be|is|are)\s*valid|remain|life|period|duration|adoption|adopted|listing)",
                                    R_PERIOD,
                                    operator=all,
                                ),
                                # http://************:55647/#/project/remark/234095?treeId=8278&fileId=66638&schemaId=15&projectId=17&schemaKey=B82
                                MatchMulti.compile(r"commenced\s*on", r"will\s*(expire|terminat)", operator=all),
                                # http://************:55647/#/project/remark/239915?treeId=37615&fileId=67609&schemaId=15&projectId=17&schemaKey=B82
                                MatchMulti.compile(
                                    r"valid",
                                    rf"from\s*(the\s*date|{R_DAY})",
                                    rf"\b(to|until)\s*(the\s*date|{R_DAY})",
                                    operator=all,
                                ),
                                # http://************:55647/#/project/remark/235650?treeId=4907&fileId=66897&schemaId=15&projectId=17&schemaKey=B82
                                MatchMulti.compile(r"remain|period", r"util", operator=all),
                                MatchMulti.compile(
                                    R_AWARD_KEYWORD,
                                    rf"\bterm\s*of\s*(\d+|{R_EN_NUM})\s*(year|month|day)s",
                                    operator=all,
                                ),
                            ],
                            skip_patterns=[r"appoint|M(r|r?s)\."],
                            threshold=0.01,
                        ),
                        # 模型7：0.1分以上段落可以取
                        {
                            "name": "score_filter",
                            "threshold": 0.1,
                            "pattern": [R_PERIOD, P_B82_NS],
                            "neglect_pattern": R_OPTION_KEYWORD,
                        },
                        # http://************:55647/#/project/remark/235176?treeId=5614&fileId=66818&schemaId=15&projectId=17&schemaKey=B82
                        # http://************:55647/#/project/remark/232965?treeId=4451&fileId=66449&schemaId=15&projectId=17&schemaKey=B82
                        {
                            "name": "special_cells",
                            "enum": AnnualReportEnum.PS.value,
                            "row_header_pattern": r"remaining\s*life",
                            "col_header_pattern": R_AWARD_KEYWORD,
                            "any_cell_pattern": R_PERIOD,
                        },
                    ],
                },
            },
        ],
    }
]
