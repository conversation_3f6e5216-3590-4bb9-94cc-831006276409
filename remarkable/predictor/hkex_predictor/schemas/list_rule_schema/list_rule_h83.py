from remarkable.common.common_pattern import P_PERIOD_SEPARATOR, R_MIDDLE_DASHES
from remarkable.common.constants import AnswerValueEnum
from remarkable.common.pattern import MatchMulti, NeglectPattern, PositionPattern
from remarkable.common.util import clean_txt
from remarkable.pdfinsight.reader import <PERSON>d<PERSON><PERSON>Reader
from remarkable.predictor.common_pattern import R_SHARE_AWARD, R_SHARE_INCENTIVE
from remarkable.predictor.hkex_predictor.models.yoda_layer.rule.by_pos import SentenceFromSplitPara
from remarkable.predictor.hkex_predictor.models.yoda_layer.rule.root import ScoreDestFilter
from remarkable.predictor.hkex_predictor.pattern import R_BE
from remarkable.predictor.hkex_predictor.schemas.pattern import R_APPELLATION, R_AWARD_KEYWORD
from remarkable.predictor.models.base_model import BaseModel
from remarkable.predictor.mold_schema import SchemaItem
from remarkable.predictor.schema_answer import PredictorResult

R_NO_NOT = r"(?<!\bany )(?<!\bno[tr] )(?<!\bno )(?<!['‘’]t )"
R_PURCHASE = r"(purchase|subscribe|subscription|buy)(?!\s*price)"
R_ISSUE_ALLOT = r"\b(issu(e\b|ing|ance|ed(?!\s*share))|allot(ed(?!\s*share)|ing|ment)?)"
R_SHARES = r"\bshares(?!\s*(available|granted|awarded))"
P_PARA_SKIP = MatchMulti.compile(
    r"disclose|headed", NeglectPattern.compile(match=r"\boption", unmatch=R_AWARD_KEYWORD), operator=any
)
# direct the trustee: http://************:55647/#/project/remark/?treeId=9989&fileId=80076&schemaId=15&projectId=17&schemaKey=H83&page=162 ,index=3131
P_SENTENCE_SKIP = MatchMulti.compile(
    R_APPELLATION,
    r"executive|rights",
    r"grant\s*price",
    r"whether",
    r"direct\s*the\s*trustee",
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/4761#note_548386
    rf"which\s*can\s*be\s*(funded|acquire|repurchase|{R_PURCHASE})",
    r"may\s*take\s*the\s*form\s*of\s*",
    operator=any,
)
H83_NEGLECT_SYLLABUS_REGS = (
    rf"equity[{R_MIDDLE_DASHES}\s]+linked|agreement|debenture",
    r"purpose|eligible|participa|rights",
    r"SHARE CAPITAL OF THE COMPANY",
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/3168#note_549631
    # r"remaining|life|period|\bterm",
)
# 购买股份
P_PURCHASE_SHARES = MatchMulti.compile(
    MatchMulti.compile(rf"{R_NO_NOT}{R_PURCHASE}", R_SHARES, r"\btrust", operator=all),
    MatchMulti.compile(r"repurchase|acquire|acquisition|funded\s*by|market(?!\s*price)", R_SHARES, operator=all),
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/4597#note_538866
    MatchMulti.compile(r"(?<!no )existing\s*shares", operator=any),
    operator=any,
)
# 发行新股
P_NEW_SHARES = [
    MatchMulti.compile(
        r"(?<!no )\bnew\s*(ordinary\s*)?([AH]\s*)?shares",
        r"fully?\s*issued",
        operator=any,
    ),
    MatchMulti.compile(
        rf"(?<!\b(be|in) )(?<!once )(?<!number of ){R_NO_NOT}{R_ISSUE_ALLOT}",
        r"(?<!issued )shares(?!\s*(available|granted|awarded))",
        operator=all,
    ),
]


# H38-41/H83-92:
# 枚举值YES: 文档披露share award scheme的股票是通过现有股票或者回购股票（existing shares），
#           或者文档披露share award scheme的股票是通过现有股票或者回购股票+发行新股（existing shares+new shares）
# 枚举值NO: 文档披露share award scheme的股票是完全通过发行新股（new shares），不标注任何东西，判断为NS
# 枚举值ND: 文档没有披露上述信息，或在文档中没有找到share award scheme的任何信息，判断为ND
H38_41_MODELS = [
    # 1. no share
    {
        "name": "no_share",
        "with_element_box": True,
        "share_type": "award",
        "enum": AnswerValueEnum.No.value,
    },
    # 1. 枚举值YES: 文档披露share award scheme的股票是通过现有股票或者回购股票（existing shares）
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/3820#note_467743
    {
        "name": "multi_models",
        "threshold": 0.0001,
        "operator": "join",
        "enum": AnswerValueEnum.Yes.value,
        "models": [
            {
                "name": "yoda_layer",
                "threshold": 0.0001,
                "enum": AnswerValueEnum.Yes.value,
                "neglect_syllabus_regs": H83_NEGLECT_SYLLABUS_REGS,
                "multi_elements": True,
                "rule": ScoreDestFilter(
                    multi=True,
                    pattern=MatchMulti.compile(*R_SHARE_AWARD, R_SHARE_INCENTIVE, operator=any),
                    skip_pattern=P_PARA_SKIP,
                    dest=SentenceFromSplitPara(
                        separator=P_PERIOD_SEPARATOR,
                        ordered_patterns=[P_PURCHASE_SHARES],
                        skip_pattern=P_SENTENCE_SKIP,
                        multi=True,
                    ),
                ),
            },
            {
                "name": "yoda_layer",
                "threshold": 0.0001,
                "enum": AnswerValueEnum.No.value,
                "neglect_syllabus_regs": H83_NEGLECT_SYLLABUS_REGS,
                "multi_elements": True,
                "rule": ScoreDestFilter(
                    multi=True,
                    pattern=MatchMulti.compile(*R_SHARE_AWARD, R_SHARE_INCENTIVE, operator=any),
                    skip_pattern=MatchMulti.compile(P_PARA_SKIP, r"\bno\s*new\s*shares", operator=any),
                    dest=SentenceFromSplitPara(
                        separator=P_PERIOD_SEPARATOR,
                        ordered_patterns=P_NEW_SHARES,
                        skip_pattern=P_SENTENCE_SKIP,
                    ),
                ),
            },
        ],
    },
    # 2.枚举值ND: 披露no new shares issued
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/4761#note_548386 page=51 index = 621
    {
        "name": "yoda_layer",
        "threshold": 0.0001,
        "enum": AnswerValueEnum.ND_CAP.value,
        "with_element_box": False,
        "neglect_syllabus_regs": H83_NEGLECT_SYLLABUS_REGS,
        "enum_from_multi_element": True,
        "multi_elements": True,
        "rule": ScoreDestFilter(
            pattern=MatchMulti.compile(*R_SHARE_AWARD, R_SHARE_INCENTIVE, operator=any),
            dest=SentenceFromSplitPara(
                separator=P_PERIOD_SEPARATOR,
                ordered_patterns=[
                    PositionPattern.compile(r"\bno\s*new\s*shares", rf"{R_BE}\s*(issue|allot)"),
                    PositionPattern.compile(rf"{R_NO_NOT}\s*(issue|allot)", r"new\s*shares"),
                ],
            ),
        ),
    },
    # 3.枚举值No: 文档披露share award scheme的股票是完全通过发行新股（new shares）
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/4597#note_521466
    {
        "name": "yoda_layer",
        "threshold": 0.0001,
        "enum": AnswerValueEnum.No.value,
        "neglect_syllabus_regs": H83_NEGLECT_SYLLABUS_REGS,
        "enum_from_multi_element": True,
        "multi_elements": True,
        "rule": ScoreDestFilter(
            multi=True,
            pattern=MatchMulti.compile(*R_SHARE_AWARD, R_SHARE_INCENTIVE, operator=any),
            skip_pattern=P_PARA_SKIP,
            dest=SentenceFromSplitPara(
                separator=P_PERIOD_SEPARATOR,
                ordered_patterns=P_NEW_SHARES,
                multi=True,
                skip_pattern=MatchMulti.compile(
                    P_SENTENCE_SKIP,
                    r"\bno[tr]?\s*(any\s*)?new\s*(ordinary\s*)?share",
                    rf"\bno[tr]?\s*{R_ISSUE_ALLOT}",
                    rf"will\s*not\s*(involve|{R_ISSUE_ALLOT})",
                    rf"not\s*required\s*to\s*{R_ISSUE_ALLOT}",
                    r"been (issued|allotted) to the Non-connected",
                    operator=any,
                ),
            ),
        ),
    },
]


def h83_post_process_answers(
    answers: list[PredictorResult], model: BaseModel, schema: SchemaItem, pdfinsight: PdfinsightReader
):
    """
    回购语句可能不符合分组条件从而被`filter_group_answer`过滤掉了，过滤之后判断剩余元素块是否符合Yes
    """
    if not answers:
        return answers
    elem_results = []
    if not any(ans.answer_value == AnswerValueEnum.Yes.value for ans in answers):
        return answers
    for result in BaseModel.get_common_predictor_results(answers):
        for element_res in result.element_results:
            elem_results.append(element_res)
    for result in elem_results:
        if P_PURCHASE_SHARES.search(clean_txt(result.text)):
            return answers
    # 答案为Yes时，没有购买股或者回购股等相关描述，将答案修改为No
    return [model.create_result(elem_results, schema=schema, value=AnswerValueEnum.No.value)]


predictor_options = [
    {
        "path": ["丨H83-92"],
        "fake_leaf": True,
        "models": [
            {
                "name": "group_based",
                "share_type": "award",
                "no_column": True,
                "group_default_enum": AnswerValueEnum.ND_CAP.value,
                "filter_group_answer": True,
                "post_process_answers": h83_post_process_answers,
                "column_configs": H38_41_MODELS,
            }
        ],
    },
]
