from remarkable.common.common_pattern import P_SEN_SEPARATOR, R_MIDDLE_DASH
from remarkable.common.constants import AnnualReportEnum, AnswerValueEnum
from remarkable.common.pattern import MatchMulti, NeglectPattern, SplitBeforeMatch
from remarkable.predictor.hkex_predictor.pattern import R_BE
from remarkable.predictor.hkex_predictor.schemas.pattern import R_AWARD_KEYWORD, R_OPTION_KEYWORD, R_PRICE

R_FAIR_VALUE = r"fair value"  # 公允价值
R_PURCHASE_PRICE = r"(purchase|exercise) price"

R_ND_TEXT = MatchMulti.compile(
    r"no shares to the grantee have not yet been issued",
    R_FAIR_VALUE,
    r"issue price",  # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/3307#note_465886
    r"expected volatility",
    operator=any,
)

NIL_PRICE_WORDS = [
    rf"\b(nil|not?|without)\b\s*(consideration|purchase price|amount ({R_BE} )?payable)",
    r"price was nil",
    r"make any payment",
    r"not? purchase price",
    r"not? required to pay (for|any)",
]
R_NIL_PRICE_WORD = r"|".join(NIL_PRICE_WORDS)
R_NOT_ADOPTED_WORD = r"not adopted? any"
NEGLECT_AWARD_SYLLABUS_REGS = [
    r"Pre-IPO ESOP$",
    NeglectPattern.compile(match=MatchMulti.compile(R_OPTION_KEYWORD, operator=any), unmatch=R_AWARD_KEYWORD),
    r"Share Appreciation Rights",
    r"Restricted Stock or Restricted Stock Units",
    r"^Consideration$",
    r"Payment on acceptance of the RSUs",
    r"Operation of the",
    r"Contribution to the",
    r"DIRECTORS’ RIGHT",
    r"Share option schema",
]
R_PER_A_SHARE = r"per\s*[AH]?\s*shares?"
P_PRICE_DESCRIPTION = MatchMulti.compile(
    SplitBeforeMatch(
        MatchMulti.compile(
            "maximum|average|range of|closing|fixed|variable",
            "price",
            operator=all,
        ),
        separator=P_SEN_SEPARATOR,
        operator=any,
    ),
    r"be paid an amount to the trustee for the purchase",
    operator=any,
)
R_NO_CONSIDERATION = "(nil|not?|no) (cash )?consideration"


P_NOT_AWARD = SplitBeforeMatch(
    NeglectPattern.compile(
        match=MatchMulti.compile(
            MatchMulti.compile(
                R_NOT_ADOPTED_WORD,
                rf"{R_AWARD_KEYWORD}|other share scheme",
                operator=all,
            ),
            r"not required to pay for the grant of any RSU",
            r"pay fees for.*?under the RSU Scheme",
            r"No such provision in the scheme",
            r"at purchase price of zero",
            r"^Not applicable",
            rf"{R_AWARD_KEYWORD} is nil",
            rf"been awarded.*?\b{R_NO_CONSIDERATION}",
            MatchMulti.compile(
                r"vest",
                "shares",
                rf"at (no cost|{R_NO_CONSIDERATION})",
                operator=all,
            ),
            operator=any,
        ),
        unmatch=R_ND_TEXT,
    ),
    separator=P_SEN_SEPARATOR,
    operator=any,
)
P_UNMATCH_PS = MatchMulti.compile(
    r"based on.*?Share Incentive Plan is between",
    r"Share Option Scheme",
    rf"^basis of determining the {R_PURCHASE_PRICE}$",
    operator=any,
)


def b81_post_process(answers: list[dict], **kwargs) -> list[dict]:
    """
    将前面的ND放到后面
    """
    if not answers:
        return answers
    for answer in answers:
        for items in answer.values():
            for predictor_result_group in items:
                nd_groups = []
                ns_ps_groups = []
                for group in predictor_result_group.groups:
                    if all(x.answer_value == AnswerValueEnum.ND for x in group):
                        nd_groups.append(group)
                    else:
                        ns_ps_groups.append(group)
                predictor_result_group.groups = ns_ps_groups + nd_groups

    return answers


predictor_options = [
    {
        "path": ["B81"],
        "fake_leaf": True,
        "post_process": b81_post_process,
        "models": [
            {
                "name": "group_based",
                "share_type": "award",
                "group_default_enum": AnnualReportEnum.ND.value,
                "check_consumed": False,
                "column_configs": {
                    "Content": [
                        # 1. NS
                        {
                            "name": "no_share",
                            "with_element_box": True,
                            "enum": AnnualReportEnum.NS.value,
                            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/3307#note_466596
                            "need_award_option_word": True,
                        },
                        # 2. ND
                        {
                            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/4574#note_545347
                            "name": "para_match",
                            "with_element_box": False,
                            "enum": AnnualReportEnum.ND.value,
                            "paragraph_pattern": [
                                MatchMulti.compile(
                                    R_AWARD_KEYWORD,
                                    r"(?<!years )\b(since|after|from|upon|as\s*(at|of)|up\s*to|end(ed)?)\s.*\b20\d{2}\b",
                                    r"no cost",
                                    operator=all,
                                )
                            ],
                        },
                        # 匹配PS
                        {
                            "name": "para_match",
                            "multi_elements": False,
                            "neglect_syllabus_regs": NEGLECT_AWARD_SYLLABUS_REGS,
                            "neglect_pattern": (P_UNMATCH_PS,),
                            "paragraph_pattern": (
                                MatchMulti.compile(
                                    rf"Subscription Price.*?is.*?{R_PER_A_SHARE}",
                                    rf"grant is.*?{R_PER_A_SHARE}",
                                    rf"The grant price (of the Restricted {R_PER_A_SHARE} )?was",
                                    r"market value of the Shares",
                                    r"be paid an amount to the trustee for the purchase",
                                    r"closing (market )?price (per share|on the date of such a purchase)",
                                    rf"{R_AWARD_KEYWORD} Scheme shall be determined.*?of the Administrator",
                                    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/3307#note_457884
                                    # http://************:55647/#/project/remark/234504?treeId=6044&fileId=66706&schemaId=15&projectId=43974&schemaKey=B81
                                    NeglectPattern.compile(
                                        match=MatchMulti.compile(
                                            MatchMulti.compile(
                                                "(exercised?|grant(ed)?|shares?) price",
                                                "determine?|in respect of",  # 优先PS匹配
                                                operator=all,
                                            ),
                                            MatchMulti.compile(
                                                R_PURCHASE_PRICE,
                                                R_AWARD_KEYWORD,
                                                operator=all,
                                            ),
                                            operator=any,
                                        ),
                                        unmatch=MatchMulti.compile(
                                            r"determining the selected participants",
                                            r"(less any|paying the) exercise price",
                                            r"off-market transactions",
                                            R_FAIR_VALUE,
                                            operator=any,
                                        ),
                                    ),
                                    operator=any,
                                ),
                                # https://jura-uat2.paodingai.com/#/hkex/annual-report-checking/report-review/243231?fileId=80081&schemaId=5&rule=B81&delist=0
                                # 接受奖励 应付对价
                                SplitBeforeMatch(
                                    MatchMulti.compile(
                                        r"(consideration|amount) payable",
                                        r"cceptance of an Award",
                                        operator=all,
                                    ),
                                    separator=P_SEN_SEPARATOR,
                                    operator=any,
                                ),
                            ),
                        },
                        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/4574#note_545965
                        # 优先匹配段落，再匹配表格
                        # 3. NS匹配
                        {
                            "name": "para_match",
                            "enum": AnnualReportEnum.NS.value,
                            "multi_elements": False,
                            "paragraph_pattern": (
                                # http://************:55647/#/project/remark/232917?treeId=23392&fileId=66441&schemaId=15&projectId=43974&schemaKey=B81
                                SplitBeforeMatch(
                                    MatchMulti.compile(
                                        MatchMulti.compile(
                                            r"\b(no|not|without|nil)\b\s*consideration",
                                            r"\bgrant|transferred",
                                            operator=all,
                                        ),
                                        MatchMulti.compile(
                                            R_NIL_PRICE_WORD,
                                            f"{R_AWARD_KEYWORD}|acceptance of the award|purchased by",
                                            operator=all,
                                        ),
                                        MatchMulti.compile(
                                            r"purchase price",
                                            r"not required to (pay|(bear )?(any )?cost)",
                                            operator=all,
                                        ),
                                        operator=any,
                                    ),
                                    separator=P_SEN_SEPARATOR,
                                    operator=any,
                                ),
                            ),
                        },
                        # 4. 表格NS
                        {
                            "name": "row_match",
                            "row_pattern": (
                                NeglectPattern.compile(
                                    match=MatchMulti.compile(
                                        r"purchase price([：:]| to receive Shares awarded)",
                                        r"Exercise price / Purchase price\s?N/A",
                                        operator=any,
                                    ),
                                    unmatch=R_PRICE,
                                ),
                            ),
                        },
                        {
                            "name": "special_cells",
                            "enum": AnnualReportEnum.NS.value,
                            "multi": True,
                            "row_pattern": [
                                r"\trsus\t.*?\tNil\t",
                                r"purchase price of shares awarded\t.*?\tN/A    \t",
                                # https://jura-uat2.paodingai.com/#/hkex/annual-report-checking/report-review/243456?fileId=80164&schemaId=5&rule=B81&delist=0
                                R_MIDDLE_DASH,
                                r"N/A|Nil",
                            ],
                            "col_header_pattern": [
                                r"(Purchase|Issue).*price",
                                r"Nature",
                            ],
                        },
                        {
                            "name": "special_cells",
                            "enum": AnnualReportEnum.NS.value,
                            "multi": True,
                            "cell_pattern": [
                                r"N/A|Nil|Not applicable",
                                R_MIDDLE_DASH,
                            ],
                            "col_header_pattern": [r"Share Award Scheme", r"free share plan"],
                            "row_header_pattern": [r"(Purchase|Issue).*price", "Nature"],
                            "row_col_relation": "and",
                        },
                        # 5. 根据章节目录提取
                        {
                            "name": "syllabus_elt_v2",
                            "only_inject_features": True,
                            "inject_syllabus_features": [
                                r"__regex__Grant Price of Restricted Shares",
                                r"__regex__Consideration and exercise price",
                                r"__regex__Basis of determining (the )?purchase price",
                                # https://jura-uat2.paodingai.com/#/hkex/annual-report-checking/report-review/242542?fileId=79815&schemaId=5&rule=B81&delist=0
                                r"__regex__(amount|consideration) payable on acceptance of the award",
                            ],
                        },
                        # 限制章节 关键词泛化匹配
                        {
                            "name": "para_match",
                            "multi_elements": False,
                            "neglect_pattern": (P_UNMATCH_PS,),
                            "skip_syllabus_title": True,
                            "syllabus_regs": [
                                R_PURCHASE_PRICE,
                            ],
                            "neglect_syllabus_regs": NEGLECT_AWARD_SYLLABUS_REGS,
                            "paragraph_pattern": (
                                NeglectPattern.compile(
                                    match=MatchMulti.compile(
                                        r"at the prevailing market price",
                                        r"exercise price",
                                        r"closing sales price",
                                        r"Not applicable",
                                        P_PRICE_DESCRIPTION,
                                        operator=any,
                                    ),
                                    unmatch=P_UNMATCH_PS,
                                ),
                            ),
                        },
                    ],
                },
            },
        ],
    }
]
