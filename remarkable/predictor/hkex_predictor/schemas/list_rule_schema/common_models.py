from remarkable.common.common_pattern import P_PERIOD_SEPARATOR, P_SEN_SEPARATOR, R_FOLLOW_PREFIX
from remarkable.common.constants import AnswerValueEnum
from remarkable.common.pattern import Match<PERSON><PERSON><PERSON>, SplitBeforeMatch
from remarkable.common.protocol import SearchPatternLike
from remarkable.predictor.common_pattern import R_INCENTIVE_ABBREV
from remarkable.predictor.hkex_predictor.models.yoda_layer.operator import Operator
from remarkable.predictor.hkex_predictor.models.yoda_layer.rule.by_pos import SentenceFromSplitPara
from remarkable.predictor.hkex_predictor.models.yoda_layer.rule.root import ScoreDestFilter
from remarkable.predictor.hkex_predictor.schemas.pattern import (
    P_AWARD_NO_OUTSTANDING,
    P_DURING_REPORT_YEAR,
    P_NO_AWARD_GRANTED,
    P_NS_DATE,
    R_AWARD_SHARES,
    R_REPORT_YEAR,
)

# http://************:55647//#/project/remark/250860?treeId=10041&fileId=68599&schemaId=15&projectId=10041&schemaKey=B73 page: 62 index: 923
# The Company has not granted any RSUs under the 2020 Share Incentive Plan which will be satisfied by issuance of new Shares after the Listing date.
P_NS_SKIP = MatchMulti.compile(
    r"(Saved?|except)\s*(as|for)\s*disclosed\s*above",
    r"years\s*from\s*the\s*listing",
    r"\b(when|will)\b",  # r"\b(when|will|available)\b", code: 00973  fy: 2023 75B 包含available
    r"from\s*time\s*to\s*time",
    # http://************:55647/#/project/remark/233772?treeId=20051&fileId=66584&schemaId=15&projectId=20051&schemaKey=B64
    r"granted\s*to\s*(our\s*|the\s*|any\s*)?directors",
    # http://************:55647/#/project/remark/234672?treeId=37702&fileId=66734&schemaId=15&projectId=17&schemaKey=B64 index=603
    r"granted\s*to\s*the\s*following\s*persons",
    R_FOLLOW_PREFIX,
    operator=any,
)

# NS-2 skip_pattern
R_NS_2_SKIP_PATTERN = SplitBeforeMatch(
    MatchMulti.compile(
        # 从adopt明确有grant过
        MatchMulti.compile(
            r"\d+\s*shares?\s*ha(s|d|ve)\s*been\s*grant",
            r"since.*?(incept|adopt)",
            operator=all,
        ),
        # 授予不适用
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/4572#note_519005
        MatchMulti.compile(
            r"grant is not applicable",
            operator=all,
        ),
        operator=any,
    ),
    separator=P_SEN_SEPARATOR,
    operator=any,
)


def as_at_time_no_grant():
    """
    针对B74-B78： x年x月x日/自上市以来一直没有grant过
    """
    return {
        "name": "yoda_layer",
        # model_id用于B74及filter_no_grant_ns_answers()函数中做二次判断
        "model_id": "ns_since_adoption",
        "threshold": 0,
        "multi_elements": True,
        "enum_from_multi_element": True,
        "enum": AnswerValueEnum.NS.value,
        "rule": ScoreDestFilter(
            skip_pattern=R_NS_2_SKIP_PATTERN,
            dest=SentenceFromSplitPara(
                multi=True,
                separator=P_PERIOD_SEPARATOR,
                ordered_patterns=[
                    MatchMulti.compile(
                        P_NO_AWARD_GRANTED,
                        MatchMulti.compile(
                            r"(?<!years )\b(since|after|from|upon|as\s*(at|of)|up\s*to|end(ed)?)\s.*\b20\d{2}\b",
                            P_NS_DATE,
                            *R_REPORT_YEAR,
                            # # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/4697#note_527700
                            # r"\b(for|during)\s*the\s*years?\s*ended",
                            P_AWARD_NO_OUTSTANDING,
                            rf"\b(under|for|in)\s*the\s*[\w\s]+?(scheme|plan|{R_INCENTIVE_ABBREV})",
                            # NeglectPattern.compile(
                            #     match=r"under\s*the\s*[\w\s]+?(scheme|plan)", unmatch=R_DURING_THE_YEAR
                            # ),
                            operator=any,
                        ),
                        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/4761#note_531275 识别问题，1个段落被识别成了两段
                        rf"{R_FOLLOW_PREFIX}|[.;；]$",
                        operator=all,
                    ),
                ],
                skip_pattern=P_NS_SKIP,
            ),
        ),
    }


def nil_during_report_year(r_keyword: str = None, p_ns_other: SearchPatternLike = None):
    p_nils = []
    if r_keyword:
        p_nils = [
            MatchMulti.compile(rf"\b(no|none\s*of\s*the\s*|nor\s*any)\s*{R_AWARD_SHARES}", r_keyword, operator=all)
        ]
    if p_ns_other:
        p_nils.append(p_ns_other)
    if not p_nils:
        return []
    return [
        # 模型1：明确表明为NS的句子，财年内或一直没有unvested(B74), granted(B75), vested(B76)，cancelled(B77)，lapsed(B78)等等
        {
            "name": "yoda_layer",
            "model_id": "ns_2",
            "threshold": 0,
            "multi_elements": True,
            "enum_from_multi_element": True,
            "enum": AnswerValueEnum.NS.value,
            "rule": ScoreDestFilter(
                dest=SentenceFromSplitPara(
                    separator=P_PERIOD_SEPARATOR,
                    ordered_patterns=[MatchMulti.compile(nil, P_DURING_REPORT_YEAR, operator=all) for nil in p_nils],
                    skip_pattern=P_NS_SKIP,
                ),
            ),
        },
        # 模型2：表明没有unvested(B74), granted(B75), vested(B76)，cancelled(B77)，lapsed(B78)等等
        {
            "name": "yoda_layer",
            # model_id用于B74及filter_no_grant_ns_answers()函数中做二次判断
            "model_id": "ns_during_the_year",
            "threshold": 0,
            "multi_elements": True,
            "enum_from_multi_element": True,
            "enum": AnswerValueEnum.NS.value,
            "rule": ScoreDestFilter(
                dest=SentenceFromSplitPara(
                    separator=P_PERIOD_SEPARATOR,
                    ordered_patterns=p_nils,
                    skip_pattern=P_NS_SKIP,
                ),
            ),
        },
    ]


def get_no_grant_models(r_keyword: str = None, p_ns_other: SearchPatternLike = None):
    """
    B74~B78通用：
    1. 各规则当期内指定指标（unvested(B74), granted(B75), vested(B76)，cancelled(B77)，lapsed(B78)）为0的句子
    2. 自adoption以来从来没有grant过
    3. 当期内没有grant过，且期初与期末的outstanding都为0
    注意事项：
    model_id=ns_during_the_year, 要在GroupBased.filter_no_grant_ns_answers()中结合财报year_end来判断
    model_id=ns_since_adoption, 要在GroupBased.filter_no_grant_ns_answers()中结合adoption date判断
    """
    return [
        # 模型1-2：各规则特殊的NS的句子，财年内或一直没有unvested(B74), granted(B75), vested(B76)，cancelled(B77)，lapsed(B78)
        *nil_during_report_year(r_keyword, p_ns_other),
        # 模型3：截至x年x月x日/自上市以来一直没有grant过
        as_at_time_no_grant(),
        # 模型4：组合条件①在当年没有granted ②outstanding期初期末都为0
        # TODO  http://************:55647/#/project/remark/238299?treeId=3997&fileId=67339&schemaId=15&projectId=17&schemaKey=B76&page=94 两段句子在不同分组中
        {
            "name": "yoda_layer",
            "model_id": "ns_3",
            "threshold": 0,
            "multi_elements": True,
            "enum_from_multi_element": True,
            "enum": AnswerValueEnum.NS.value,
            "rule": Operator.all(
                ScoreDestFilter(
                    dest=SentenceFromSplitPara(
                        separator=P_PERIOD_SEPARATOR,
                        ordered_patterns=[MatchMulti.compile(P_NO_AWARD_GRANTED, P_DURING_REPORT_YEAR, operator=all)],
                        skip_pattern=P_NS_SKIP,
                    ),
                ),
                ScoreDestFilter(
                    dest=SentenceFromSplitPara(
                        ordered_patterns=[P_AWARD_NO_OUTSTANDING],
                    ),
                ),
            ),
        },
    ]
