from remarkable.common.common_pattern import R_MIDDLE_DASH
from remarkable.common.constants import AnswerValueEnum
from remarkable.common.pattern import <PERSON><PERSON>ult<PERSON>
from remarkable.predictor.hkex_predictor.schemas.list_rule_schema.list_rule_b1 import (
    B1_B7_NS_MODELS,
    B1_B10_NS_MODELS,
)
from remarkable.predictor.hkex_predictor.schemas.pattern import R_NUMBER
from remarkable.predictor.hkex_predictor.schemas.util import get_sentence_model

R_OVER_ALLOTMENT = rf"over{R_MIDDLE_DASH}allotment"

# https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/5323
predictor_options = [
    {
        "path": ["B2"],
        "fake_leaf": True,
        "element_candidate_count": 20,
        "models": [
            {
                "name": "fund_raising_group",
                "column_configs": {
                    # 子项1
                    "Class of equity securities": [
                        *B1_B10_NS_MODELS,
                        *B1_B7_NS_MODELS,
                        {
                            "name": "multi_models",
                            "operator": "union",
                            "enum": AnswerValueEnum.PS.value,
                            "models": [
                                {
                                    "name": "multi_models",
                                    "operator": "any",
                                    "enum": AnswerValueEnum.PS.value,
                                    "models": [
                                        # 先提取特殊描述
                                        get_sentence_model(
                                            [
                                                r"(?P<content>preference\s*shares?)",
                                                # r"(?P<content>warrants)",
                                                # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7352
                                                r"(convertible|warrants).*?\d\s*(?P<content>conversion\s*shares?)",
                                                r"\d\s*(?P<content>conversion\s*shares?).*?(convertible|warrants)",
                                                rf"{R_NUMBER}.*?(?P<content>rights?\s*shares?)",
                                                rf"{R_NUMBER}.*?(?P<content>placing\s*shares?)",
                                            ],
                                            enum=AnswerValueEnum.PS.value,
                                            multi_elements=False,
                                            multi=False,
                                            # 排除over-allotment，因为要在下方单独提取
                                            skip_pattern=MatchMulti.compile(R_OVER_ALLOTMENT, operator=any),
                                        ),
                                        get_sentence_model(
                                            [
                                                rf"{R_NUMBER}\s*(\w+\s+){{,5}}(?P<content>shares)",
                                                r"(ordinary|\bnew|rights|placing|[HA])\s*(?P<content>shares)",
                                            ],
                                            enum=AnswerValueEnum.PS.value,
                                            multi_elements=False,
                                            multi=False,
                                            # 排除over-allotment，因为要在下方单独提取
                                            skip_pattern=MatchMulti.compile(R_OVER_ALLOTMENT, operator=any),
                                        ),
                                    ],
                                },
                                # 提取over-allotment相关
                                get_sentence_model(
                                    [
                                        rf"{R_NUMBER}\s*{R_OVER_ALLOTMENT}\s*(\w+\s+){{,4}}(?P<content>shares)",
                                        rf"{R_NUMBER}\s*(\w+\s+){{,5}}(?P<content>shares).*?\s{R_OVER_ALLOTMENT}",
                                    ],
                                    enum=AnswerValueEnum.PS.value,
                                    multi_elements=True,
                                ),
                            ],
                        },
                        {
                            "name": "special_cells",
                            "enum": AnswerValueEnum.PS.value,
                            "model_id": "need_row_pattern",
                            "multi": False,
                            "need_continuous": False,
                            "without_col_header": True,  # 不用列名做匹配
                            "col_header_pattern": r"fund|raising",
                        },
                    ]
                },
            },
        ],
    },
]
