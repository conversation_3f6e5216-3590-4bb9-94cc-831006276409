import re

from remarkable.common.common_pattern import (
    P_CONTINUED,
    P_SEN_SEPARATOR,
    R_DR_CHAPTER_TITLES,
    R_MIDDLE_DASH,
    R_NOTES_CHAPTER_TITLES,
)
from remarkable.common.constants import AnswerValueEnum
from remarkable.common.pattern import (
    MATCH_ALWAYS,
    MatchMulti,
    NeglectPattern,
    PositionPattern,
    SplitBeforeMatch,
)
from remarkable.common.util import clean_txt
from remarkable.pdfinsight.interdoc_reader import ColPara, Para, Table
from remarkable.predictor.hkex_predictor.models.yoda_layer.filter import (
    Or<PERSON>ilter,
    ParaTextFilter,
    TableFilter,
    TableRowColValueFilter,
    TParaTextFilter,
)
from remarkable.predictor.hkex_predictor.models.yoda_layer.rule.by_pos import SentenceFromSplitPara
from remarkable.predictor.hkex_predictor.models.yoda_layer.rule.root import (
    ScoreDest<PERSON>ilter,
    ScoreFirstElementFilter,
)
from remarkable.predictor.hkex_predictor.pattern import P_EQUITY_CHAPTER, R_BE
from remarkable.predictor.hkex_predictor.schemas.list_rule_schema.common_models import (
    P_NS_SKIP,
    as_at_time_no_grant,
)
from remarkable.predictor.hkex_predictor.schemas.list_rule_schema.list_rule_b64 import (
    P_ONLY_ENG,
    R_TARGET,
)
from remarkable.predictor.hkex_predictor.schemas.list_rule_schema.list_rule_b74 import (
    P_NEG_TBL_TITLES,
    R_B74_NS,
    R_CELL_KEYWORDS,
    R_TITLE_GRANTS,
    date_of_grant_models,
    exercise_period_models,
    purchase_price_models,
    vesting_period_models,
)
from remarkable.predictor.hkex_predictor.schemas.list_rule_schema.list_rule_b76 import enum_closing_price_table
from remarkable.predictor.hkex_predictor.schemas.list_rule_schema.list_rule_b94 import P_NS_COL_VALUE, R_ROW_COL_HEADER
from remarkable.predictor.hkex_predictor.schemas.pattern import (
    P_DURING_REPORT_YEAR,
    P_NO_AWARD_GRANTED,
    P_PRICE_PS,
    R_AWARD_KEYWORD,
    R_DURING_THE_YEAR,
    R_NUMBER,
    R_OPTION_KEYWORD,
    R_PRICE,
)
from remarkable.predictor.schema_answer import TableCellsResult

P_CLOSING_PS = MatchMulti.compile(r"closing\s*price", P_PRICE_PS, operator=all)
R_B75_NUMBER_AWARD_HEADER = [*R_ROW_COL_HEADER] + [
    r"(number|no\.)\s*of\s*shares?\s*award(ed)?",
    r"RSUs?\s*(granted|Scheme)",
    # http://************:55647/#/project/remark/258078?treeId=6410&fileId=68830&schemaId=15&projectId=6410&schemaKey=B75
    r"award\s*shares?\s*granted",
]

P_VESTING_PERIOD_PS = SplitBeforeMatch(
    MatchMulti.compile(r"vested\s*(immediately)?\s*on\s*the\s*date", operator=any),
    separator=P_SEN_SEPARATOR,
)

P_TARGET = MatchMulti.compile(
    R_TARGET,
    rf"(?<!non{R_MIDDLE_DASH})vesting\s*conditions?",
    r"specif\w+\s*performance\s*review",
    operator=any,
)

P_NS_TARGET = SplitBeforeMatch(
    MatchMulti.compile(r"\b(no|without|not|None)\b", R_TARGET, operator=all),
    separator=P_SEN_SEPARATOR,
    operator=any,
)

P_ROW_COL_HEADER = SplitBeforeMatch.compile(
    MatchMulti.compile(*R_B75_NUMBER_AWARD_HEADER, operator=any), separator="\t", operator=any
)

P_FAIR_VALUE = MatchMulti.compile(
    r"fair\s*value",
    MatchMulti.compile(P_PRICE_PS, r"(equal\s*to|bas(ed)?\s*on)\s*(the\s*)?(market|closing)\s*price", operator=any),
    operator=all,
)
table_filter_b75 = OrFilter.from_filters(
    TableRowColValueFilter(
        pattern=P_ROW_COL_HEADER,
        value_pattern=MATCH_ALWAYS,
        type="col",
    ),
    TableRowColValueFilter(
        pattern=P_ROW_COL_HEADER,
        value_pattern=MATCH_ALWAYS,
        type="row",
    ),
)

P_UN_WEIGHTED_CLOSING = NeglectPattern.compile(
    match=MatchMulti.compile(r"closing", "price", operator=all),
    unmatch=MatchMulti.compile(r"weight|determined\s*accord", r"average", operator=all),
)

P_CLOSING_PRICE = MatchMulti.compile(
    # 明确有closing price 但是包含 weight average  http://************:55647/#/project/remark/257968?treeId=8163&fileId=68808&schemaId=15&projectId=8163&schemaKey=B75
    rf"the\s*closing\s*price.*?date\s*of\s*(this\s*)?grant\s*{R_BE}\s*{R_PRICE}",
    P_UN_WEIGHTED_CLOSING,
    operator=any,
)
COL_TEXT_FILTER_1 = TParaTextFilter(
    PositionPattern.compile(
        rf"^Number\s*of\s*(?:\w+\s+)?{R_AWARD_KEYWORD}",
        r"\b(?:grant|award)ed\s*during",
    ),
)  # 处理合并导致无法框选一列的问题
COL_TEXT_FILTER_2 = TParaTextFilter(
    pattern=MatchMulti.compile(
        r"^award",
        r"^Number\s*of\s*Shares?\s*grant",
        NeglectPattern.compile(
            match=MatchMulti.compile(
                "number",
                "of",
                "share",
                "grant|award",
                operator=all,
            ),
            unmatch=r"outstanding",
        ),
        *R_B75_NUMBER_AWARD_HEADER,
        SplitBeforeMatch.compile(
            MatchMulti.compile(
                r"^Restricted\s*Shares?\s*award",
                rf"^{R_MIDDLE_DASH}?$",
                operator=any,
            ),
            separator="\t",
            operator=all,
        ),
        operator=any,
    )
)


def b75_table_enum_func(table_results: list[TableCellsResult], footnotes: list):
    table_cells = [cell for result in table_results for cell in result.parsed_cells]
    if all(
        not clean_txt(cell.text) or P_NS_COL_VALUE.search(cell.clean_text) or P_ONLY_ENG.search(cell.clean_text)
        for cell in table_cells
        if not cell.is_header and cell.width == cell.height == 1
    ):
        return AnswerValueEnum.NS.value
    return AnswerValueEnum.PS.value


def first_table_locator(*filters: TableFilter, model_id: str):
    return [
        {
            "name": "yoda_layer",
            "threshold": 0.05,
            "model_id": f"{model_id}.{i}",
            "multi_elements": True,
            "rule": ScoreFirstElementFilter[Table[ColPara]](
                strict=False,
                filter=filter_,
            ),
        }
        for i, filter_ in enumerate(filters)
    ]


number_of_awards = [
    # 1
    {
        "name": "no_share",
        "model_id": "ns_1",
        "with_element_box": True,
    },
    # 2 优先匹配 含有 during the year/period/report的单元格
    # http://************:55647/#/project/remark/264708?treeId=20188&fileId=70156&schemaId=15&projectId=20188&schemaKey=B75
    {
        "name": "special_cells",
        "include_footnotes": True,
        "enum_function": b75_table_enum_func,
        "title_patterns": R_TITLE_GRANTS,
        "neglect_title_patterns": P_NEG_TBL_TITLES,
        "col_header_pattern": NeglectPattern.compile(
            match=MatchMulti.compile(
                MatchMulti.compile(*R_B75_NUMBER_AWARD_HEADER, operator=any), R_DURING_THE_YEAR, operator=all
            ),
            unmatch=r"before|after|outstanding|lapsed|vested",
        ),
    },
    # 3 http://************:55647/#/project/remark/251165?treeId=37592&fileId=68650&schemaId=15&projectId=37592&schemaKey=B75
    {
        "name": "special_cells",
        # "threshold": 0.5,
        "multi_elements": True,
        "include_footnotes": True,
        "enum_function": b75_table_enum_func,
        "title_patterns": R_TITLE_GRANTS,
        "neglect_title_patterns": P_NEG_TBL_TITLES,
        "col_header_pattern": NeglectPattern.compile(
            match=MatchMulti.compile(*R_B75_NUMBER_AWARD_HEADER, operator=any),
            unmatch=r"before|after|outstanding|lapsed|vested",
        ),
    },
    # http://************:55647/#/project/remark/251242?treeId=8654&fileId=68663&schemaId=15&projectId=8654&schemaKey=B75
    {
        "name": "table_tuple_select",
        "model_id": "ns_3",
        "threshold": 0.01,
        "report_year_as_largest_year": True,
        "header_pattern": re.compile(r"^grant(ed)?\s*授予$", re.I),
        "feature_white_list": [
            r"__regex__grant|__regex__largest_year_minus_0|__regex__number of share",
            r"__regex__grant|__regex__largest_year_minus_0|__regex__award",
        ],
    },
    # *first_table_locator(
    #     COL_TEXT_FILTER_1.as_table_filter("col"),
    #     COL_TEXT_FILTER_2.as_table_filter("col"),
    #     model_id="ns_4",
    # ),
    {
        "name": "yoda_layer",
        "threshold": 0,
        "model_id": "ns_5",
        "multi_elements": True,
        "enum": AnswerValueEnum.NS,
        "rule": ScoreFirstElementFilter[Para](
            strict=True,
            filter=ParaTextFilter(
                pattern=MatchMulti.compile(
                    *R_B74_NS,
                    operator=any,
                )
            ),
        ),
    },
    # grant number 描述在段落中
    # http://************:55647/#/project/remark/233191?treeId=10935&fileId=66487&schemaId=15&projectId=10935&schemaKey=B75
    {
        "name": "para_match",
        "multi_elements": True,
        "threshold": 0.05,
        "model_id": "ps_1",
        "enum": AnswerValueEnum.PS,
        "paragraph_pattern": SplitBeforeMatch.compile(
            MatchMulti.compile(rf"grant(ed)?\s*{R_NUMBER}\s*{R_AWARD_KEYWORD}", operator=any),
            separator=P_SEN_SEPARATOR,
            operator=any,
        ),
        "neglect_pattern": R_OPTION_KEYWORD,
    },
    as_at_time_no_grant(),
    {
        "name": "yoda_layer",
        "model_id": "ns_4",
        "threshold": 0,
        "multi_elements": True,
        "enum_from_multi_element": True,
        "enum": AnswerValueEnum.NS.value,
        "rule": ScoreDestFilter(
            dest=SentenceFromSplitPara(
                ordered_patterns=[
                    MatchMulti.compile(
                        P_NO_AWARD_GRANTED,
                        P_DURING_REPORT_YEAR,
                        operator=all,
                    ),
                ],
                skip_pattern=P_NS_SKIP,
            ),
        ),
    },
]

model_ids = [model["model_id"] for model in number_of_awards if "model_id" in model]


def enum_market_value(table_results: list[TableCellsResult], foot_notes):
    table_cells = [cell for result in table_results for cell in result.parsed_cells]
    text = "\n".join(
        {clean_txt(cell.text, remove_cn_text=True) for cell in table_cells}
        | {(note.get("text") or "") for note in foot_notes}
    )
    if P_CLOSING_PS.search(text):
        return AnswerValueEnum.PS.value
    return None


column_configs = {
    "Number of awards": number_of_awards,
    "Purchase price": purchase_price_models(),
    "Date of grant": date_of_grant_models(),
    "Vesting period": vesting_period_models(),
    "Exercise period": exercise_period_models(),
    "Performance targets(if any)": [
        {
            "name": "special_cells",
            "multi_elements": True,
            "multi": True,
            "threshold": 0.01,
            "include_footnotes": True,
            # "neglect_title_patterns": R_OPTION_KEYWORD,
            "col_header_pattern": [
                R_TARGET,
            ],
        },
        {
            "name": "special_cells",
            "multi_elements": True,
            "multi": True,
            "threshold": 0.01,
            "include_footnotes": True,
            "neglect_title_patterns": R_OPTION_KEYWORD,
            "row_header_pattern": [
                R_TARGET,
            ],
        },
        # NS 先匹配 performance targets
        {
            "name": "para_match",
            "enum": AnswerValueEnum.NS.value,
            "neglect_syllabus_regs": [
                R_OPTION_KEYWORD,
            ],
            "paragraph_pattern": P_NS_TARGET,
        },
        # PS 以 performance targets开始, 后面为详细内容
        {
            "name": "para_match",
            "enum": AnswerValueEnum.PS.value,
            "neglect_syllabus_regs": [
                R_OPTION_KEYWORD,
            ],
            "paragraph_pattern": r"^performance\s*targets\s*[:：]\s*\S+",
        },
        {
            "name": "para_match",
            "neglect_syllabus_regs": [
                R_OPTION_KEYWORD,
            ],
            "paragraph_pattern": P_TARGET,
        },
    ],
    "Closing price": [
        # 根据单元格关键词+列名匹配表格
        {
            "name": "special_cells",
            "skip_answer_value": AnswerValueEnum.ND.value,
            "include_footnotes": True,
            "title_patterns": R_TITLE_GRANTS,
            "neglect_title_patterns": P_NEG_TBL_TITLES,
            "col_header_pattern": P_UN_WEIGHTED_CLOSING,
            "any_cell_pattern": R_CELL_KEYWORDS,
            "allow_only_footnotes": True,
            "footnote_pattern": P_CLOSING_PRICE,
        },
        # http://************:55647/#/project/remark/234432?treeId=37922&fileId=66694&schemaId=15&projectId=17&schemaKey=B75
        {
            "name": "special_cells",
            "skip_answer_value": AnswerValueEnum.ND.value,
            "include_footnotes": True,
            "title_patterns": R_TITLE_GRANTS,
            "neglect_title_patterns": P_NEG_TBL_TITLES,
            "col_header_pattern": [
                P_UN_WEIGHTED_CLOSING,
                r"^price\s*on\s*day\s*prior\s*to\s*vest",
            ],
            "any_cell_pattern": R_CELL_KEYWORDS,
            "allow_only_footnotes": True,
            "footnote_pattern": P_CLOSING_PRICE,
            "enum_function": enum_closing_price_table,
        },
        # 特例：https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/4147#note_485214
        {
            "name": "special_cells",
            "skip_answer_value": AnswerValueEnum.ND.value,
            "include_footnotes": True,
            "title_patterns": R_TITLE_GRANTS,
            "neglect_title_patterns": P_NEG_TBL_TITLES,
            "any_cell_pattern": R_CELL_KEYWORDS,
            "col_header_pattern": r"market\s*value",
            "footnote_pattern": r"(?<!average )(?<!weight )(?<!weighted )closing\s*price",
            "enum_function": enum_market_value,
        },
        # 匹配段落
        {
            "name": "para_match",
            "multi_elements": True,
            "enum_from_multi_element": True,
            "paragraph_pattern": P_CLOSING_PRICE,
        },
        {"name": "score_filter", "threshold": 0.618, "pattern": P_CLOSING_PRICE},
        {
            "name": "kmeans_classification",
            "filter_low_score_threshold": 0.1,
            "neglect_syllabus_regs": P_EQUITY_CHAPTER,
            "para_pattern": P_CLOSING_PRICE,
        },
    ],
    "Fair value of awards at the date of grant": [
        {
            "name": "special_cells",
            "multi": True,
            "dr_chapter_first": True,
            "include_footnotes": True,
            "title_patterns": R_TITLE_GRANTS,
            "neglect_title_patterns": P_NEG_TBL_TITLES,
            "col_header_pattern": [
                r"Fair\s*value",
            ],
            "any_cell_pattern": R_CELL_KEYWORDS,
            "allow_only_footnotes": True,
            "footnote_pattern": P_FAIR_VALUE,
        },
        {
            "name": "para_match",
            "threshold": 0.01,
            "syllabus_regs": R_DR_CHAPTER_TITLES,
            "paragraph_pattern": P_FAIR_VALUE,
            "neglect_pattern": R_OPTION_KEYWORD,
        },
        {
            "name": "para_match",
            "threshold": 0.01,
            "paragraph_pattern": P_FAIR_VALUE,
            "neglect_pattern": R_OPTION_KEYWORD,
        },
        # 3. 匹配 fair value 为 0
        # http://************:55647/#/project/remark/250363?treeId=6141&fileId=68516&schemaId=15&projectId=6141&schemaKey=B75
        {
            "name": "para_match",
            "multi_elements": True,
            "threshold": 0.01,
            "enum": AnswerValueEnum.NS.value,
            "paragraph_pattern": MatchMulti.compile(
                r"fair\s*value",
                r"\b(is|was|be)\s*((RMB|HK\$?[DS]?\$?|US\$?|\$)?0|nil)",
                operator=all,
            ),
            "neglect_pattern": R_OPTION_KEYWORD,
        },
    ],
    "Accounting standard and policy adopted": [
        # {
        #     "name": "para_match",
        #     "multi_elements": False,
        #     "threshold": 0.05,
        #     "syllabus_regs": [
        #         *R_NOTES_CHAPTER_TITLES,
        #     ],
        #     "paragraph_pattern": [
        #         MatchMulti.compile(
        #             r"share.?base(d)?\s*payment",
        #             operator=any,
        #         )
        #     ],
        # },
        {
            "name": "syllabus_elt_v2",
            "ignore_pattern": MatchMulti.compile(P_CONTINUED, *R_NOTES_CHAPTER_TITLES, operator=any),
            "only_inject_features": True,
            "parent_features": [*R_NOTES_CHAPTER_TITLES],
            "parent_must_be_root": True,
            "page_first_as_parent_syllabus": True,
            "inject_syllabus_features": [
                r"__regex__Equity-settled\s*awards\s*granted\s*to",
                r"__regex__Equity-settled\s*share-based\s*payment\s*transactions"
                rf"__regex__Share-based\s*payment\s*arrangements?__regex__{R_AWARD_KEYWORD}",
                rf"__regex__share-based\s*(payments?|compensation)__regex__{R_AWARD_KEYWORD}",
                r"__regex__significant\s*account(ing)?\s*polic(y|ies)__regex__share-based\s*(payments?|compensation)",
                r"__regex__significant\s*account(ing)?\s*polic(y|ies)__regex__employee\s*benefits?__regex__share-based\s*(payments?|compensation)",
                r"__regex__significant\s*account(ing)?\s*polic(y|ies)__regex__employee\s*benefits?",
                r"__regex__share-based\s*(payment|compensation)",
            ],
        },
    ],
}

predictor_options = [
    {
        "path": ["B75"],
        "fake_leaf": True,
        "models": [
            {
                "name": "group_based",
                "share_type": "award",
                "pick_answer_strategy": "single",
                "follow_first_enum": AnswerValueEnum.NS.value,
                "group_default_enum": AnswerValueEnum.ND.value,
                "column_configs": column_configs,
            },
        ],
    },
]
