from remarkable.common.common_pattern import R_CG_CHAPTER_TITLES, R_DR_CHAPTER_TITLES
from remarkable.common.pattern import <PERSON><PERSON>ult<PERSON>, NeglectPattern, PositionPattern
from remarkable.predictor.hkex_predictor.models.yoda_layer.filter import SameChapterFilter
from remarkable.predictor.hkex_predictor.models.yoda_layer.rule.root import ScoreAnswerLocator, sort_by_index

P_EMOLUMENT = MatchMulti.compile(r"emolument", "polic", operator=all)
P_REMUNERATION = MatchMulti.compile(r"remuneration", "committee|polic", operator=all)
P_PARENT_FEATURES = MatchMulti.compile(
    *R_DR_CHAPTER_TITLES,
    *R_CG_CHAPTER_TITLES,
    operator=any,
)

P_B97_SYLLABUS = [
    PositionPattern.compile("EMOLUMENT|REMUNERATION", "OF|FOR", "DIRECTOR"),
    PositionPattern.compile("DIRECTOR", "EMOLUMENT|REMUNERATION"),
    P_EMOLUMENT,
    P_REMUNERATION,
    MatchMulti.compile(r"DIRECTOR.*MANAGEMENT", operator=any),
]

P_97_NEGLECT_PATTERN = MatchMulti.compile(
    "the remuneration of the members of the senior management by band", operator=any
)

predictor_options = [
    {
        "path": ["B97", "Directors’ remuneration policy"],
        "models": [
            {
                "name": "yoda_layer",
                "threshold": 0.1,
                "aim_types": ["PARAGRAPH"],
                "multi_elements": True,
                "pattern": NeglectPattern.compile(
                    match=MatchMulti.compile("determin", "decide", operator=any),
                    unmatch=MatchMulti.compile("Compensation of key executives of the Group", operator=any),
                ),
                "parent_features": P_PARENT_FEATURES,
                "syllabus_regs": P_B97_SYLLABUS,
                "rule": ScoreAnswerLocator(sorter=sort_by_index, filter=SameChapterFilter()),
            },
            {
                "name": "score_filter",
                "threshold": 0.01,
                "aim_types": ["PARAGRAPH"],
                "page_first_as_parent_syllabus": True,
                "parent_features": P_PARENT_FEATURES,
                "syllabus_regs": P_B97_SYLLABUS,
                "pattern": NeglectPattern.compile(
                    match=MatchMulti.compile(".", operator=any),
                    unmatch=MatchMulti.compile("Compensation of key executives of the Group", operator=any),
                ),
            },
            {
                "name": "score_filter",
                "threshold": 0.01,
                "parent_features": P_PARENT_FEATURES,
                "aim_types": ["TABLE"],
            },
        ],
    },
    {
        "path": ["B97", "Details of any remuneration payable to members of senior management by band"],
        "models": [
            {
                "name": "yoda_layer",
                "threshold": 0.1,
                "aim_types": ["PARAGRAPH", "TABLE"],
                "multi_elements": True,
                "rule": ScoreAnswerLocator(filter=SameChapterFilter(), neglect_pattern=P_97_NEGLECT_PATTERN),
                "parent_features": P_PARENT_FEATURES,
            }
        ],
    },
]
