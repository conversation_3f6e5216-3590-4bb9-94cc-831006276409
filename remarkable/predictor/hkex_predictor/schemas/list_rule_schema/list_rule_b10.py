from remarkable.common.common_pattern import P_PERIOD_SEPARATOR, R_MIDDLE_DASH
from remarkable.common.constants import AnswerV<PERSON>ue<PERSON>num
from remarkable.common.pattern import <PERSON><PERSON>ult<PERSON>, NeglectPattern, PositionPattern
from remarkable.common.util import clean_txt
from remarkable.predictor.common_pattern import R_EN_MONTH, R_PERCENT
from remarkable.predictor.hkex_predictor.pattern import R_BE
from remarkable.predictor.hkex_predictor.schemas.list_rule_schema.list_rule_b1 import (
    B1_B10_NS_MODELS,
    R_UNUSED,
    R_USED,
    R_UTILISE,
)
from remarkable.predictor.hkex_predictor.schemas.list_rule_schema.list_rule_b8 import (
    B8_B10_NS_MODELS,
    P_COL_REVISED_USE,
    P_USED_IN_CELL,
    R_INTENDED,
    R_UNMATCH_TIMELINE,
    R_USED_IN_PARA,
    table_models_for_proceeds,
)
from remarkable.predictor.hkex_predictor.schemas.list_rule_schema.list_rule_b9 import P_ACTUAL_USE, P_TIMELINE, P_UNUSED
from remarkable.predictor.hkex_predictor.schemas.list_rule_schema.post_processor import BasePostProcessor
from remarkable.predictor.hkex_predictor.schemas.pattern import P_SAVE_AS_ABOVE, R_MONEY_UNIT, R_NOT
from remarkable.predictor.hkex_predictor.schemas.util import get_sentence_model

R_ACCORDING = r"\b(accordance|according|in\s*(the\s*)?(line|manner|compliance)\s*([a-z]+\s*)?with)"
P_USED_AS_INTENDED = MatchMulti.compile(
    # http://100.64.0.105:55647/#/project/remark/268899?treeId=6940&fileId=70606&schemaId=18&projectId=6940&schemaKey=C1.3
    # *[rf"{r}\s*(for\s|as\s(?!at))" for r in R_FULLY_USED],
    PositionPattern.compile(
        rf"proposed|{R_USED}",
        rf"(\bas|{R_ACCORDING})\s+([a-z]+\s+){{0,3}}{R_INTENDED}",
    ),
    operator=any,
)
# http://100.64.0.105:55647/#/project/remark/268484?treeId=6340&fileId=70546&schemaId=18&projectId=6340&schemaKey=C1.3
P_HAS_REALLOCATED = MatchMulti.compile(r"reallocated|revised", operator=any)
P_COL_INTENDED_USE = MatchMulti.compile(
    # http://100.64.0.105:55647/#/project/remark/294353?treeId=7262&fileId=70763&schemaId=18&projectId=7262&schemaKey=C1.3
    r"^capital\s*structure",
    # http://100.64.0.105:55647/#/project/remark/293789?treeId=5154&fileId=70680&schemaId=18&projectId=5154&schemaKey=C1.3
    # http://100.64.0.105:55647/#/project/remark/294274?treeId=6556&fileId=70751&schemaId=18&projectId=6556&schemaKey=C1.3
    rf"to\s*be\s*{R_USED}|^(original\s*)?(allocation|amount|application)($|\s*(million|{R_MONEY_UNIT}))",
    NeglectPattern.compile(
        match=r"^net\s*(proceeds|amount)|(amount|allocation|application)\s*of\s*(the\s*)?(net\s*)?proceeds",
        unmatch=rf"{R_USED}|\buse\b|{R_UNUSED}|{R_UNMATCH_TIMELINE}|{R_NOT}",
    ),
    NeglectPattern.compile(
        match=MatchMulti.compile(R_INTENDED, rf"{R_USED}|\buses?\b|application|allocation", operator=all),
        # http://100.64.0.105:55647/#/project/remark/249104?treeId=5016&fileId=68307&schemaId=15&projectId=5016&schemaKey=B10
        unmatch=MatchMulti.compile(R_UNMATCH_TIMELINE, R_UNUSED, r"\bon\s*the\s*\s*intended", operator=any),
    ),
    operator=any,
)


P_INTENDED_USE = NeglectPattern.compile(
    match=MatchMulti.compile(
        PositionPattern.compile(r"intended|planned|proposed|intention", r"\b(uses?|unutili[sz]ed)\b", r"proceeds"),
        # rf"^((intended|planned|proposed)\s*uses?|intended\s*(uses?\s*)?and\s*actual\s*uses?)\s*of\s*(the\s*)?((net|un{R_MIDDLE_DASH}?{R_UTILISE}d)\s*)?proceeds\s*(from|$|{R_CURRENCY})",
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6245#note_663540
        r"total\s*investment\s*planned",
        operator=any,
    ),
    unmatch=rf"amount|percent|{R_EN_MONTH}|{R_PERCENT}",
)
P_B10_COLUMNS = [
    P_INTENDED_USE,
    P_ACTUAL_USE,
    P_USED_IN_CELL,
    P_COL_INTENDED_USE,
    P_COL_REVISED_USE,
    P_UNUSED,
    P_TIMELINE,
]
# http://100.64.0.105:55647/#/project/remark/266254?treeId=38021&fileId=70465&schemaId=18&projectId=38021&schemaKey=C1.3
P_B10_USE_CHANGE = MatchMulti.compile(
    # PS描述1: 用途有变化
    NeglectPattern.compile(
        match=MatchMulti.compile(r"\schanged?\b|revised?|revising", R_USED_IN_PARA, operator=all),
        unmatch=rf"{R_NOT}.*?change|\bun{R_MIDDLE_DASH}?change|\bwill|time(line|frame|table)|schedule|expected\s*(date|tim)",
    ),
    # PS描述2: 未使用资金被延迟
    # http://100.64.0.105:55647/#/project/remark/295264?treeId=38110&fileId=70883&schemaId=18&projectId=38110&schemaKey=C1.3
    PositionPattern.compile(R_UNUSED, r"proceeds", r"\b(delaye|reallocate|revise|adjuste)d"),
    # PS描述3: 使用资金被延迟
    # https://jura6-lir.paodingai.com/#/hkex/annual-report-checking/report-review/260207?fileId=69256&schemaId=5&rule=C1.3&delist=0
    PositionPattern.compile(r"delay|reallocate|revise|adjust|change", r"\buse of proceeds"),
    # http://100.64.0.105:55647/#/project/remark/294842?treeId=6497&fileId=70831&schemaId=18&projectId=6497&schemaKey=C1.3
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6414#note_674206
    PositionPattern.compile(r"resolved", r"delay|reallocate|revise|adjust|change", r"proceeds"),
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6840#note_709156
    # http://100.64.0.105:55647/#/hkex/annual-report-checking/report-review/258057?fileId=68826&schemaId=5&rule=C1.1.1&delist=0
    MatchMulti.compile(r"revis(ed?|ing)(\S+\s+){1,3}timeline", rf"proceeds|un{R_UTILISE}d", operator=all),
    operator=any,
)

B10_C1_3_PS_SEN_MODEL = get_sentence_model(
    [
        # 1. 按预期使用完毕
        P_USED_AS_INTENDED,
        # 2. 用途无变化
        MatchMulti.compile(R_USED_IN_PARA, PositionPattern.compile(R_NOT, r"\bchange"), operator=all),
        MatchMulti.compile(R_USED_IN_PARA, rf"\bun{R_MIDDLE_DASH}?change|{R_NOT}change", operator=all),
        MatchMulti.compile(r"\buse of.*?proceed", PositionPattern.compile(R_NOT, r"\bchange"), operator=all),
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6239#note_662482
        PositionPattern.compile(R_NOT, r"material\s*deviation", R_USED_IN_PARA),
        # 3. 用途发生变化或未使用的资金被延迟
        P_B10_USE_CHANGE,
        # 4. 用途按照公告中的目的使用
        PositionPattern.compile(
            rf"{R_USED_IN_PARA}|(utili(ti)?[sz]e|apply)|{R_UNUSED}",
            rf"{R_ACCORDING}|\s(for|as\s(?!at))",
            r"\b(application|purpose|intention|usage|plan)",
            r"prospectus|announce|disclosed",
        ),
        # 5. intended use of proceeds to be used as to
        # https://jura6-lir.paodingai.com/#/hkex/annual-report-checking/report-review/340606?fileId=104100&schemaId=5&rule=B10&delist=0 index=140
        PositionPattern.compile(r"the\s*intended\s*use", r"proceeds", rf"\sbe\s*{R_UTILISE}d\s*as\s"),
    ],
    skip_pattern=P_SAVE_AS_ABOVE,
    multi_elements=True,
    threshold=0,
    enum=AnswerValueEnum.PS.value,
)


class B10PostProcessor(BasePostProcessor):
    def split_col_cells(self, parsed_cells, ignore_first_col=True):
        """
        从结果表格中提取以下列：
        1. “实际使用”列
        2. “未使用”列
        3. “计划使用”列
        """
        intended_cols, actual_use_cols = set(), set()
        revised_cols, intend_cols, unused_cols, used_cols = set(), set(), set(), set()
        for col, text in self.get_header_from_cells(parsed_cells).items():
            if ignore_first_col and col == 0:
                continue
            if P_INTENDED_USE.search(text):
                intended_cols.add(col)
                intend_cols.add(col)
                continue
            if P_ACTUAL_USE.search(text):
                actual_use_cols.add(col)
                used_cols.add(col)
                continue
            if P_COL_REVISED_USE.search(text):
                revised_cols.add(col)
                continue
            if P_UNUSED.search(text):
                unused_cols.add(col)
                continue
            # intended use和actual use可能在同一列
            # http://100.64.0.105:55647/#/project/remark/268484?treeId=6340&fileId=70546&schemaId=18&projectId=6340&schemaKey=C1.3
            if P_COL_INTENDED_USE.search(text):
                intend_cols.add(col)
            if P_USED_IN_CELL.search(text):
                used_cols.add(col)

        return intended_cols, actual_use_cols, revised_cols, intend_cols, used_cols, unused_cols

    @staticmethod
    def get_detail_cells(parsed_cells):
        # 实际用途列中描述“已按照预期使用完毕”或“重新分配/修订”，取这一列
        has_reallocated = False
        effected_col_indices = set()
        for cell in parsed_cells:
            if cell.is_header:
                continue
            text = clean_txt(cell.text, remove_cn_text=True)
            if P_HAS_REALLOCATED.search(text):
                # C1.3表格中，若单元格有reallocated相关描述，则判定为NS
                has_reallocated = True
                effected_col_indices.add(cell.colidx)
                break
            if P_USED_AS_INTENDED.search(text):
                effected_col_indices.add(cell.colidx)
                break
        if not effected_col_indices:
            return False, []
        return has_reallocated, [c for c in parsed_cells if c.colidx in effected_col_indices]

    def filter_ps_table_cells(self, parsed_cells, group_type):
        """
        B10表格PS标准：
        1. 实际使用的描述为“已按照预期使用完毕”
        2. 同时有：计划使用+实际使用
        """
        # 实际用途列中描述“已按照预期使用完毕”，取这一列
        _, cells = self.get_detail_cells(parsed_cells)
        if cells:
            return cells
        intended_cols, actual_use_cols, revised_cols, intend_cols, used_cols, unused_cols = self.split_col_cells(
            parsed_cells, ignore_first_col=False
        )
        if revised_cols:
            return [c for c in parsed_cells if c.colidx in revised_cols | intend_cols]
        if not (intend_cols and used_cols):
            return []
        return self.add_first_col_cells([c for c in parsed_cells if c.colidx in intend_cols | used_cols | unused_cols])


predictor_options = [
    {
        # ------------------------------
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/5333
        # PS规则说明：
        # 1. 募集资金用途是否按照或者拟按照之前计划进行的段落；
        # 2. 募集资金用途表格中拟使用的分配与实际计划分配的一致的描述；
        # 3. 募集资金用途有变更或延迟的段落，并解释了原因；
        # 4. 募集资金用途表格中同时披露拟使用和实际使用，且两处不一致的表格；
        # 4. 实际用途和计划用途一致，不存在变更/延迟等情况
        # ------------------------------
        "path": ["B10"],
        "fake_leaf": True,
        "element_candidate_count": 20,
        "models": [
            {
                "name": "use_of_proceeds_group",
                "post_process_answers": B10PostProcessor(),
                "column_configs": {
                    # 子项1
                    "Use of proceeds": [
                        *B1_B10_NS_MODELS,
                        {
                            "name": "multi_models",
                            "operator": "union",
                            "require_unique_element": True,
                            "enum": AnswerValueEnum.PS.value,
                            "models": [
                                # 表格中明确提到修正金额
                                *table_models_for_proceeds(P_B10_COLUMNS),
                                # 各种PS句子
                                B10_C1_3_PS_SEN_MODEL,
                                # 5. intended use of proceeds to be used as to
                                # https://jura6-lir.paodingai.com/#/hkex/annual-report-checking/report-review/340606?fileId=104100&schemaId=5&rule=B10&delist=0 index=140
                                {
                                    "name": "para_match",
                                    "paragraph_pattern": MatchMulti.compile(
                                        PositionPattern.compile(
                                            r"proceeds",
                                            rf"\s{R_BE}\s*{R_UTILISE}d\s*as\s",
                                        ),
                                        rf"fully {R_UTILISE}d the net proceeds",
                                        operator=all,
                                    ),
                                },
                            ],
                        },
                        # 预计使用+实际使用，TODO 使用相似度判断用途相同
                        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6840#note_704393
                        {
                            "name": "multi_models",
                            "operator": "all",
                            "enum": AnswerValueEnum.PS.value,
                            "models": [
                                # 预期使用
                                {
                                    "name": "para_match",
                                    "paragraph_pattern": r"proceeds",
                                    "para_separator": P_PERIOD_SEPARATOR,
                                    "sentence_pattern": [
                                        MatchMulti.compile(
                                            rf"{R_MONEY_UNIT}[1-9]", R_INTENDED, r"\s(for|to)\s", operator=all
                                        )
                                    ],
                                },
                                # 实际使用
                                {
                                    "name": "para_match",
                                    "paragraph_pattern": r"proceeds",
                                    "para_separator": P_PERIOD_SEPARATOR,
                                    "sentence_pattern": [
                                        PositionPattern.compile(
                                            rf"{R_MONEY_UNIT}[1-9]", rf"\s(was|were)\s*{R_UTILISE}d\s*(for|to|as)\b"
                                        )
                                    ],
                                },
                            ],
                        },
                        *B8_B10_NS_MODELS,
                    ],
                },
            }
        ],
    }
]
