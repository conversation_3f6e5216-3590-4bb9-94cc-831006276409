import re

from remarkable.common.common import get_date_by_offset_days, to_datetime
from remarkable.common.common_pattern import R_AS_AT, R_MIDDLE_DASHES
from remarkable.common.constants import AnswerValueEnum
from remarkable.common.pattern import <PERSON><PERSON><PERSON><PERSON>, PatternCollection
from remarkable.common.protocol import SearchPatternLike
from remarkable.common.util import clean_txt
from remarkable.predictor.common_pattern import R_EN_MONTH
from remarkable.predictor.default_predictor.utils import is_table_result
from remarkable.predictor.hkex_predictor.pattern import R_DATES_FOR_EXTRACT
from remarkable.predictor.hkex_predictor.schemas.pattern import R_MONEY_UNIT
from remarkable.predictor.models.base_model import BaseModel
from remarkable.predictor.schema_answer import PredictorResult, TableCellsResult
from remarkable.predictor.utils import extract_date

R_NOTE = r"(\d{1,2}|[a-z]|([(（[]|note)[^）)]]+[）)]]?)"
P_VALID_CELL_VALUE = MatchMulti.compile(
    rf"[(（{R_MIDDLE_DASHES}]?({R_MONEY_UNIT})?\s*[1-9][,.\d]*\s*(million\s*)?[）)]?{R_NOTE}?$",
    # http://100.64.0.105:55647/#/hkex/annual-report-checking/report-review/250762?fileId=68583&schemaId=5&rule=B8&delist=0
    rf"[(（{R_MIDDLE_DASHES}]?[1-9][,.\d]*\s*{R_MONEY_UNIT}[)）]?",
    operator=any,
)
P_AS_AT_DATES = PatternCollection(
    [
        rf"{R_AS_AT}\s*((\d{{1,2}}[,，]?\s*|{R_EN_MONTH}[,，]?\s*){{2}}\d{{4}}\s*and\s*)?{dt}"
        for dt in R_DATES_FOR_EXTRACT
    ],
    flags=re.I,
)
P_AS_AT_FY = PatternCollection(
    [
        r"\b(end|beginn?ing)\s*of\s*FY\s*(?P<year>20\d{2})\b",
    ],
    flags=re.I,
)
P_DATES = PatternCollection(
    [
        rf"({R_AS_AT}|\bon|\bat|\bduring|\bin(\s*the\s*year)?)\s+((\d{{1,2}}\s+|{R_EN_MONTH}\s+){{2}}\d{{4}}\s*and\s*)?{dt}"
        for dt in R_DATES_FOR_EXTRACT
    ],
    flags=re.I,
)


class BasePostProcessor:
    def __init__(self):
        # 决定最终结果是否为NS
        self.answer_is_ns = False
        self._init_params()

    def _init_params(self):
        # 指定列index，则仅根据该列判断是否有值
        self.check_value_col = None
        # 根据该正则判断表格中的check_value_col列是否有值（即不为nil/0/-等）
        self.check_value_pattern = P_VALID_CELL_VALUE
        # 为True表示提取到的PS table需要修正枚举值为NS
        self.table_is_ns = False

    def filter_ps_table_cells(self, parsed_cells, group_type):
        """
        过滤PS表格单元格
        """
        return parsed_cells

    def filter_ns_table_cells(self, parsed_cells):
        """
        过滤NS表格单元格
        """
        return parsed_cells

    def is_ns_table(self, parsed_cells):
        """
        PS表格满足该条件，answer_value设置为NS
        """
        return False

    def is_ns_para(self, text):
        """
        PS段落满足该条件，answer_value设置为NS
        """
        return False

    def is_valid_ps_para(self, text, element):
        """
        PS段落不满足该条件，丢弃答案
        """
        return True

    def is_valid_ns_para(self, text):
        """
        NS段落不满足该条件，丢弃答案
        """
        return True

    def cells_have_value(self, parsed_cells):
        """
        如果整列数据未包含有效值，代表NS
        注意：如果结果有多列，需要视情况指定self.check_value_col
        """
        if not parsed_cells:
            return False
        return any(
            self.check_value_pattern.search(clean_txt(cell.text, remove_cn_text=True))
            for cell in parsed_cells
            if not cell.is_col_header and (self.check_value_col is None or cell.colidx == self.check_value_col)
        )

    @staticmethod
    def get_date_from_col_header(parsed_cells, date_pattern: PatternCollection, date_format="%Y-%m-%d"):
        """
        提取表格列名中的日期
        http://100.64.0.105:55647/#/project/remark/294922?treeId=14268&fileId=70841&schemaId=18&projectId=14268&schemaKey=C1.1.1
        http://100.64.0.105:55647/#/project/remark/296370?treeId=42602&fileId=71022&schemaId=18&projectId=42602&schemaKey=C1.1.1
        """
        if not parsed_cells:
            return {}
        indices = {c.colidx for c in parsed_cells}
        date_dict = {}
        for col_idx, text in parsed_cells[0].table.col_header_texts.items():
            if col_idx not in indices:
                continue
            if at_date := extract_date(date_pattern, clean_txt(text, remove_cn_text=True), str_format=date_format):
                date_dict[col_idx] = at_date
        return date_dict

    @staticmethod
    def get_date_from_row_header(parsed_cells, date_pattern: PatternCollection):
        """
        提取表格行头中的日期
        """
        date_dict = {}
        min_col = min(c.colidx for c in parsed_cells)
        table = parsed_cells[0].table
        for row_idx, text in table.row_header_texts.items():
            row_header = clean_txt(text, remove_cn_text=True)
            if at_date := extract_date(date_pattern, row_header, str_format="%Y-%m-%d"):
                date_dict[row_idx] = at_date
                continue
            # 考虑行名中是月+日，列名是日期的场景
            for cells in parsed_cells[0].table.col_header:
                if cells[0].colidx != min_col:
                    continue
                for cell in cells:
                    year = cell.no_cn_text[:4]
                    if not year.isdigit() or not year.startswith("20"):
                        continue
                    if at_date := extract_date(date_pattern, f"{row_header} {year}", str_format="%Y-%m-%d"):
                        date_dict[row_idx] = at_date
        return date_dict

    @staticmethod
    def get_header_from_cells(parsed_cells, invalid_col_indices: set[int] = ()):
        """
        header可能被拆分，这里要组合到一起做判断
        http://100.64.0.105:55647/#/project/remark/293603?treeId=38121&fileId=70653&schemaId=18&projectId=38121&schemaKey=C1.2.1
        """
        indices = {c.colidx for c in parsed_cells}
        return {
            idx: clean_txt(text, remove_cn_text=True)
            for idx, text in parsed_cells[0].table.col_header_texts.items()
            if idx in indices and idx not in invalid_col_indices
        }

    @staticmethod
    def add_first_col_cells(parsed_cells):
        if not parsed_cells:
            return []
        if any(not cell.is_col_header and cell.colidx == 0 for cell in parsed_cells):
            return parsed_cells
        row_indices = {cell.rowidx for cell in parsed_cells}
        return [cell for cell in parsed_cells[0].table.cols[0] if cell.rowidx in row_indices] + parsed_cells

    @classmethod
    def get_col_indices_by_pattern(cls, parsed_cells, pattern: SearchPatternLike, invalid_col_indices: set[int] = ()):
        col_headers = cls.get_header_from_cells(parsed_cells, invalid_col_indices)
        return {col for col, text in col_headers.items() if pattern.search(text)}

    def get_invalid_date_col_indices(self, parsed_cells):
        invalid_col_indices = set()
        col_date_dict = self.get_date_from_col_header(parsed_cells, P_AS_AT_DATES)
        if self.year_end:
            for col, date_str in col_date_dict.items():
                if date_str not in {self.year_end, get_date_by_offset_days(self.year_end, 1)}:
                    invalid_col_indices.add(col)
        elif len(col_date_dict) > 1:
            # 没有year_end时，排除日期最大的那列
            max_date_col = max(col_date_dict.items(), key=lambda x: x[1])[0]
            invalid_col_indices = {c for c in col_date_dict if c != max_date_col}
        return invalid_col_indices

    def extract_as_at_date(self, text):
        if up_to_date := extract_date(P_AS_AT_DATES, text, str_format="%Y-%m-%d"):
            return up_to_date
        if up_to_fy := extract_date(P_AS_AT_FY, text, str_format="%Y"):
            is_end = "beginning" not in text
            end_year, end_month, end_day = self.year_end.split("-")
            start_year, start_month, start_day = self.year_start.split("-")
            # 期初期末同一年份，直接取
            if end_year == start_year:
                return f"{up_to_fy}-{end_month}-{end_day}" if is_end else f"{up_to_fy}-{start_month}-{start_day}"
            # 期末年份=report_year, 则期初年份-1
            if end_year == self.report_year:
                return (
                    f"{up_to_fy}-{end_month}-{end_day}" if is_end else f"{int(up_to_fy) - 1}-{start_month}-{start_day}"
                )
            # 期初年份=report_year, 则期末年份+1
            return f"{int(up_to_fy) + 1}-{end_month}-{end_day}" if is_end else f"{up_to_fy}-{start_month}-{start_day}"
        return None

    def is_up_to_fy_end(self, text, strict=False):
        if up_to_date := self.extract_as_at_date(text):
            return up_to_date in {
                get_date_by_offset_days(self.year_end, -1),
                self.year_end,
                get_date_by_offset_days(self.year_end, 1),
            }
        # 严格模式下，必须有日期
        return not strict

    def is_up_to_fy_start(self, text, strict=True):
        if up_to_date := self.extract_as_at_date(text):
            return up_to_date in {get_date_by_offset_days(self.year_start, -1), self.year_start}
        # 严格日期下，必须有日期
        return not strict

    def is_in_current_fy(self, text):
        """
        段落中的日期是否在当前财年内
        """
        the_date = extract_date(P_DATES, text)
        if not the_date:
            return True
        return to_datetime(self.year_start) <= the_date <= to_datetime(self.year_end)

    def __call__(
        self,
        answers: list[PredictorResult],
        model: BaseModel,
        schema,
        pdfinsight,
        year_start: str,
        year_end: str,
        report_year: str,
    ):
        """
        NS条件：
        1. 当年实际使用额度为0，则修改枚举值为NS
        2. 明确描述net proceeds未被使用
        """
        self.answers = answers
        self.model = model
        self.schema = schema
        self.pdfinsight = pdfinsight
        self.year_start = year_start
        self.year_end = year_end
        self.report_year = report_year
        self.min_year_start = get_date_by_offset_days(year_start, -92) if year_start else ""
        self.has_valid_table = False

        new_answers = []
        for answer in answers:
            if answer.answer_value == AnswerValueEnum.ND:
                continue
            # 初始化：最终结果是否为NS
            self.answer_is_ns = False
            new_element_results = []
            tbl_ans_value, para_is_ns = "", False
            for element_result in answer.element_results:
                # 重置初始值
                self._init_params()
                if is_table_result(element_result):
                    if answer.answer_value == AnswerValueEnum.PS:
                        group_type = element_result.group_type if isinstance(element_result, TableCellsResult) else None
                        parsed_cells = self.filter_ps_table_cells(element_result.parsed_cells, group_type)
                        # PS表格不满足条件，丢弃答案
                        if not parsed_cells:
                            continue
                        # 如果tbl_ans_value已经为ps，说明至少有了一张PS表格，则不再判断是否为NS
                        if tbl_ans_value != "ps":
                            tbl_ans_value = "ns" if self.is_ns_table(parsed_cells) else "ps"
                    else:
                        parsed_cells = self.filter_ns_table_cells(element_result.parsed_cells)
                        # NS表格不满足条件，丢弃答案
                        if not parsed_cells:
                            continue
                    element_result.parsed_cells = parsed_cells
                    self.has_valid_table = True
                else:
                    if answer.answer_value == AnswerValueEnum.PS:
                        # PS句子不满足条件，丢弃答案
                        if not self.is_valid_ps_para(element_result.text, element_result.element):
                            continue
                        # PS句子满足条件，可转为NS句子
                        if self.is_ns_para(element_result.text):
                            para_is_ns = True
                    else:
                        # NS句子不满足条件，丢弃答案
                        if not self.is_valid_ns_para(element_result.text):
                            continue
                new_element_results.append(element_result)
            if not new_element_results:
                continue
            # http://100.64.0.105:55647/#/hkex/annual-report-checking/report-review/341105?fileId=104164&schemaId=5&rule=B8&delist=0
            if tbl_ans_value == "ns" and not para_is_ns:
                # 存在ns表格时，删除其他非表格答案
                new_element_results = [e for e in new_element_results if is_table_result(e)]
            if self.answer_is_ns or tbl_ans_value == "ns" or (not tbl_ans_value and para_is_ns):
                answer.update_answer_value(AnswerValueEnum.NS)
            answer.element_results = new_element_results
            new_answers.append(answer)
        return new_answers
