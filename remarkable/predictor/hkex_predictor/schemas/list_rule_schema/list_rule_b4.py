from remarkable.common.common_pattern import P_SEN_SEPARATOR, R_CURRENCY
from remarkable.common.constants import AnswerValueEnum
from remarkable.predictor.hkex_predictor.schemas.list_rule_schema.list_rule_b1 import (
    B1_B7_NS_MODELS,
    B1_B10_NS_MODELS,
)

R_PRICE = rf"\b{R_CURRENCY}\s*([0�]\.|[1-9�])[\d�.]+"
# offer price: https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6238#note_662447
R_PRICE_PREFIX = r"(placing|(?<!net )(?<!gross )subscription|conversion|issu(ed?|ing)|exercise|offer)"


# TODO https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/956#note_621042
# https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/5325
predictor_options = [
    {
        "path": ["B4"],
        "fake_leaf": True,
        "element_candidate_count": 20,
        "models": [
            {
                "name": "fund_raising_group",
                "column_configs": {
                    # 子项1
                    "Issue price": [
                        *B1_B10_NS_MODELS,
                        *B1_B7_NS_MODELS,
                        {
                            "name": "para_match",
                            "enum": AnswerValueEnum.PS.value,
                            "multi_elements": True,
                            "multi": True,
                            "para_separator": P_SEN_SEPARATOR,
                            "sentence_pattern": [
                                rf"(?P<content>{R_PRICE}(([，,]|and).*{R_PRICE})?)\s*per\s*(new\s*|ordinary\s*)?share",
                                # http://100.64.0.105:55647/#/hkex/annual-report-checking/report-review/265863?fileId=70387&schemaId=15&rule=B1&delist=0
                                rf"{R_PRICE_PREFIX}\s*price.*?[(（](?P<content>[^）)]*{R_PRICE})",
                                rf"{R_PRICE_PREFIX}\s*price.*?(?P<content>{R_PRICE}(([，,]|and).*{R_PRICE})?)(?!\s*million)",
                                rf"{R_PRICE_PREFIX}.*?\bat\s*(a|the)\s*price.*?(?P<content>{R_PRICE}(([，,]|and).*{R_PRICE})?)(?!\s*million)",
                            ],
                            # 排除不相关的price： http://100.64.0.105:55647/#/hkex/annual-report-checking/report-review/340994?fileId=104149&schemaId=5&rule=B4&delist=0
                            "neglect_sentence_pattern": [
                                r"\bnet\s*([a-z]+\s+)?price",
                                r"(closing|market|share)\s*price",
                            ],
                        },
                        {
                            "name": "special_cells",
                            "enum": AnswerValueEnum.PS.value,
                            "model_id": "need_row_pattern",
                            "multi": False,
                            "need_continuous": False,
                            "without_col_header": True,  # 不用列名做匹配
                            "col_header_pattern": r"fund|raising",
                        },
                        {
                            "name": "score_filter",
                            "enum": AnswerValueEnum.PS.value,
                            "pattern": R_PRICE,
                            "threshold": 0.9,
                        },
                    ]
                },
            },
        ],
    },
]
