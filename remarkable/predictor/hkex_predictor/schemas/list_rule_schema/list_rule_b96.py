from remarkable.common.constants import AnswerValueEnum

predictor_options = [
    {
        "path": ["B96", "Content"],
        "models": [
            {
                "name": "syllabus_elt_v2",
                "include_shape": True,
                "inject_syllabus_features": [
                    r"__regex__MANAGEMENT.*DISCUSSION.*ANALYSIS",
                ],
            },
            {
                "name": "syllabus_elt_v2",
                "include_shape": True,
                "inject_syllabus_features": [
                    r"__regex__(Financial|Business|Performance) Review",
                    r"__regex__Discussion and Analysis of Operations",
                ],
            },
            {"name": "empty", "enum": AnswerValueEnum.ND.value},
        ],
    },
]
