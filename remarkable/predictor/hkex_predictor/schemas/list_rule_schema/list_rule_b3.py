import re

from remarkable.common.common_pattern import R_MIDDLE_DASHES
from remarkable.common.constants import AnswerV<PERSON>ueEnum
from remarkable.common.pattern import MatchMulti
from remarkable.predictor.hkex_predictor.schemas.list_rule_schema.list_rule_b1 import (
    B1_B7_NS_MODELS,
    B1_B10_NS_MODELS,
)
from remarkable.predictor.hkex_predictor.schemas.list_rule_schema.post_processor import BasePostProcessor
from remarkable.predictor.hkex_predictor.schemas.pattern import R_NUMBER
from remarkable.predictor.hkex_predictor.schemas.util import get_sentence_model

P_NEG_SENTENCE = MatchMulti.compile(r"divided\s*into", r"un[a-z]+\s+shares", operator=any)
R_SHARES_WITH_PREFIX = rf"([{R_MIDDLE_DASHES}\w]+\s+){{,5}}(?<!existing )shares"

P_INVALID_TEXTS = [
    r"\b(?:January|February|March|April|May|June|July|August|September|October|November|December)\s+"  # 数字前面是月份
]


class B3PostProcessor(BasePostProcessor):
    def is_valid_ps_para(self, text, element):
        re_text = re.escape(text)
        if any(re.search(invalid_text + re_text, element["text"], re.I) for invalid_text in P_INVALID_TEXTS):
            return False
        return True


predictor_options = [
    {
        # --------------------------------------------
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/5324
        # --------------------------------------------
        "path": ["B3"],
        "fake_leaf": True,
        "element_candidate_count": 20,
        "models": [
            {
                "name": "fund_raising_group",
                "post_process_answers": B3PostProcessor(),
                "column_configs": {
                    # 子项1
                    "Number of issued": [
                        *B1_B10_NS_MODELS,
                        *B1_B7_NS_MODELS,
                        {
                            "name": "multi_models",
                            "operator": "union",
                            "require_unique_element": True,
                            "enum": AnswerValueEnum.PS,
                            "models": [
                                {
                                    "name": "special_cells",
                                    "enum": AnswerValueEnum.PS.value,
                                    "model_id": "need_row_pattern",
                                    "multi": False,
                                    "need_continuous": False,
                                    "without_col_header": True,  # 不用列名做匹配
                                    "col_header_pattern": r"fund|raising|(number|no\.?)\s*of\s*shares.*issue",
                                },
                                # 优先取aggregate/total数据，再尝试取其他数据
                                get_sentence_model(
                                    [
                                        rf"\b(amount|aggregate|total)\s*of\s*(?P<content>[1-9][\d,�]+\s*{R_SHARES_WITH_PREFIX}(\s*(and|(?<!\d)[,，])\s*[1-9][\d,�]+\s*{R_SHARES_WITH_PREFIX}){{0,2}})",
                                        rf"\b(issued|allott?ed)\s*(?P<content>[1-9][\d,�]+\s*{R_SHARES_WITH_PREFIX})",
                                        rf"(completed|completion).*?\s(?P<content>[1-9][\d,�]+\s*{R_SHARES_WITH_PREFIX}(\s*(and|(?<!\d)[,，])\s*[1-9][\d,�]+\s*{R_SHARES_WITH_PREFIX}){{0,2}})",
                                        rf"(^|\s)(?P<content>[1-9][\d,�]+\s*{R_SHARES_WITH_PREFIX}).*?(completed|completion)",
                                        rf"(^|\s)(?P<content>({R_NUMBER}\s*and\s*)?{R_NUMBER}\s*{R_SHARES_WITH_PREFIX}(\s*(and|(?<!\d)[,，])\s*[1-9][\d,�]+\s*{R_SHARES_WITH_PREFIX})*)",
                                    ],
                                    multi_elements=True,
                                    # http://************:55647/#/hkex/annual-report-checking/report-review/266163?fileId=70447&schemaId=15&rule=B3&delist=0
                                    skip_pattern=P_NEG_SENTENCE,
                                ),
                                # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7317#note_733108
                                {
                                    "name": "special_cells",
                                    "enum": AnswerValueEnum.PS.value,
                                    # "multi_elements": True,
                                    "multi": True,
                                    "col_header_pattern": [
                                        r"(number|no\.?)\s*of\s*shares.*issue",
                                        r"(number|no\.?)\s*of\s*(issued|ordinary)\s*shares",
                                    ],
                                    "row_header_pattern": [r"issuance|offering"],
                                    "enum_pattern": R_NUMBER,
                                },
                                {
                                    "name": "special_cells",
                                    "enum": AnswerValueEnum.PS.value,
                                    # "multi_elements": True,
                                    "multi": False,
                                    "col_header_pattern": [
                                        r"(number|no\.?)\s*of\s*shares.*issue",
                                        r"(number|no\.?)\s*of\s*(issued|ordinary)\s*shares",
                                    ],
                                    "enum_pattern": R_NUMBER,
                                },
                                {
                                    "name": "score_filter",
                                    "enum": AnswerValueEnum.PS.value,
                                    "pattern": R_NUMBER,
                                    "threshold": 0.9,
                                },
                            ],
                        },
                    ],
                },
            }
        ],
    },
]
