import re

from remarkable.common.common_pattern import P_SEN_SEPARATOR, R_PERIOD
from remarkable.common.constants import AnswerValue<PERSON>num
from remarkable.predictor.hkex_predictor.schemas.list_rule_schema.list_rule_b1 import (
    B1_B7_NS_MODELS,
    B1_B10_NS_MODELS,
    R_UNUSED,
)
from remarkable.predictor.hkex_predictor.schemas.list_rule_schema.list_rule_b4 import R_PRICE
from remarkable.predictor.hkex_predictor.schemas.pattern import R_MONEY_UNIT

# 每股净值
# P_B5_PS = MatchMulti.compile(
#     r"\bnet\s*(subscription|issue|placing)?\s*price|\bnet\s*proceeds",
#     R_PRICE,
#     r"\bper\s*(\w+\s+){0,3}share",
#     operator=all,
# )

R_NET_PROCEEDS = r"\b(net\s*proceeds?|total\s*subscription)"
# 总净值
# P_FUND_RAISED = MatchMulti.compile(R_NET_PROCEEDS, rf"{R_MONEY_UNIT}[1-9]", operator=all)


# https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/5326 Net price
predictor_options = [
    {
        "path": ["B5"],
        "fake_leaf": True,
        "element_candidate_count": 20,
        "models": [
            {
                "name": "fund_raising_group",
                "column_configs": {
                    # 子项1
                    "Net price": [
                        *B1_B10_NS_MODELS,
                        *B1_B7_NS_MODELS,
                        {
                            "name": "multi_models",
                            "enum": AnswerValueEnum.PS.value,
                            "operator": "union",
                            "models": [
                                {
                                    "name": "para_match",
                                    "multi_elements": True,
                                    "multi": True,
                                    "para_separator": P_SEN_SEPARATOR,
                                    "paragraph_pattern": [
                                        rf"\bnet\s*(subscription|issue|placing)?\s*price.*?(?P<content>{R_PRICE})\s*(of\s)?(per|each)\s*(\w+\s+){{,3}}share",
                                        # https://jura6-lir.paodingai.com/#/hkex/annual-report-checking/report-review/261628?fileId=69540&schemaId=5&rule=B5
                                        rf"\bnet\s*(subscription|issue|placing)?\s*price\s*(of\s)?(per|each)\s*(\w+\s+){{,3}}share.*?(?P<content>{R_PRICE})\b",
                                        rf"(\bnet\s*proceeds).*?(?P<content>{R_PRICE})\s*(of\s)?(per|each)\s*(\w+\s+){{,3}}share",
                                    ],
                                    "neglect_sentence_pattern": R_UNUSED,
                                },
                                # 总净值
                                {
                                    "name": "para_match",
                                    "enum": AnswerValueEnum.PS.value,
                                    "multi_elements": True,
                                    "multi": True,
                                    # 用句号/小标题/分号切开：https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6840#note_704385
                                    "para_separator": re.compile(
                                        rf"\s*({R_PERIOD}\s*|[;；]\s*|(?<!\d)([（(]([a-z]|[ivx]{{1,4}})[)）]\s))"
                                    ),
                                    "flags": 0,
                                    "paragraph_pattern": [
                                        rf"{R_NET_PROCEEDS}.*?(?P<content>({R_MONEY_UNIT}\s*[1-9][\d.,]+(\s*million)?\b[^\dA-Z]+)+)",
                                    ],
                                    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6840#note_708162
                                    "neglect_sentence_pattern": [R_UNUSED, r"\bexpect(ed)?\s*to\s*be"],
                                },
                            ],
                        },
                        # need_row_pattern， 动态行正则限制结果
                        {
                            "name": "special_cells",
                            "enum": AnswerValueEnum.PS.value,
                            "model_id": "need_row_pattern",
                            "multi": False,
                            "need_continuous": False,
                            "without_col_header": True,  # 不用列名做匹配
                            "col_header_pattern": r"net\s*proceeds",
                        },
                        # 不限制行正则，取整列
                        {
                            "name": "special_cells",
                            "enum": AnswerValueEnum.PS.value,
                            "multi": False,
                            "need_continuous": False,
                            "without_col_header": True,  # 不用列名做匹配
                            "col_header_pattern": r"net\s*proceeds",
                        },
                        {
                            "name": "score_filter",
                            "skip_notes": True,
                            "enum": AnswerValueEnum.PS.value,
                            "pattern": R_PRICE,
                            "threshold": 0.9,
                        },
                    ]
                },
            },
        ],
    },
]
