from remarkable.common.common_pattern import P_SEN_SEPARATOR
from remarkable.common.constants import AnswerValueEnum
from remarkable.common.pattern import <PERSON><PERSON>ult<PERSON>, PositionPattern
from remarkable.predictor.common_pattern import R_DATES, R_EN_MONTH

# http://100.64.0.105:55647/#/project/remark/268444?projectId=17&treeId=12643&fileId=70541&schemaId=15&schemaKey=B99
P_DATE = MatchMulti.compile(
    *[rf"\b(?<!effect )on\s*{r_date}" for r_date in R_DATES],
    rf"\b\d{{1,2}}(st|nd|rd|th)?\s*{R_EN_MONTH}[,，]?\s*\d{{4}}\.",
    operator=any,
)
# 每位拟任董事获得法律建议的日期
P_B99_DATE_LEGAL_ADVICE_PATTERN = MatchMulti.compile(
    r"\blegal\s*advi(ce|so?e?r)s?\b|\bnewly\s*appointed\b|\blegal opinion|obtain(s|ed)?.*?advice", P_DATE, operator=all
)

# 每位拟任董事已确认他理解自己作为上市公司董事的义务
P_B99_DIR_CONF_PATTERN = PositionPattern.compile(
    r"\bconfirm(ed|s)?\b", r"\b(obligation|responsibilit(y|ies))", r"as\s*(a\s*)?director"
)
R_SYLLABUS_REGS = [r"Independent non-executive Directors", r"BOARD OF DIRECTORS"]

P_EXTEND_SYLLABUS = (
    [
        r"EXECUTIVE DIRECTOR",
        r"DIRECTORS, SUPERVISORS, SENIOR MANAGEMENT AND STAFF",
        r"^THE BOARD$",
        r"CORPORATE GOVERNANCE REPORT",
        r"INDUCTION AND PROFESSIONAL DEVELOPMENT",
    ],
)

predictor_options = [
    {
        "path": ["B99", "Date of legal advice"],
        "models": [
            {
                "name": "para_match",
                "syllabus_regs": R_SYLLABUS_REGS,
                "para_separator": P_SEN_SEPARATOR,
                "extend_candidates_syllabus_regs": P_EXTEND_SYLLABUS,
                # "neglect_pattern": MatchMulti.compile(r"businesses|operations", operator=any),
                "paragraph_pattern": P_B99_DATE_LEGAL_ADVICE_PATTERN,
                "enum": AnswerValueEnum.PS.value,
            },
            {
                "name": "special_cells",
                "enum": AnswerValueEnum.PS.value,
                "whole_table": True,
                "col_header_pattern": MatchMulti.compile(
                    r"date\s*of\s*obtaining", r"legal\s*advi(ce|so?e?r)s?", operator=all
                ),
            },
            {
                "name": "score_filter",
                "threshold": 0.81,
                "enum": AnswerValueEnum.PS.value,
            },
        ],
    },
    {
        "path": ["B99", "Directors' confirmation"],
        "models": [
            {
                "name": "para_match",
                "syllabus_regs": R_SYLLABUS_REGS,
                "para_separator": P_SEN_SEPARATOR,
                "extend_candidates_syllabus_regs": P_EXTEND_SYLLABUS,
                "paragraph_pattern": P_B99_DIR_CONF_PATTERN,
                "enum": AnswerValueEnum.PS.value,
            },
            {
                "name": "score_filter",
                "threshold": 0.8,
                "enum": AnswerValueEnum.PS.value,
            },
        ],
    },
]
