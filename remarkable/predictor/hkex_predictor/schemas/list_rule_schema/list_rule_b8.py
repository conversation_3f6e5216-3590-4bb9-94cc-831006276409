import re

from remarkable.common.common import get_date_by_offset_days
from remarkable.common.common_pattern import P_CELL_NIL, P_PERIOD_SEPARATOR, R_MIDDLE_DASH
from remarkable.common.constants import AnswerValueEnum
from remarkable.common.pattern import MatchMulti, NeglectPattern, PatternCollection, PositionPattern
from remarkable.common.util import clean_txt
from remarkable.predictor.common_pattern import R_EN_MONTH, R_PERCENT
from remarkable.predictor.hkex_predictor.pattern import R_DATES_FOR_EXTRACT
from remarkable.predictor.hkex_predictor.schemas.list_rule_schema.list_rule_b1 import (
    B1_B10_NS_MODELS,
    R_REASON,
    R_UNUSED,
    R_USED,
    R_UTILISE,
)
from remarkable.predictor.hkex_predictor.schemas.list_rule_schema.list_rule_b5 import (
    R_MONEY_UNIT,
    R_NET_PROCEEDS,
)
from remarkable.predictor.hkex_predictor.schemas.list_rule_schema.post_processor import (
    P_VALID_CELL_VALUE,
    BasePostProcessor,
)
from remarkable.predictor.hkex_predictor.schemas.pattern import (
    P_DURING_THE_YEAR,
    R_ALL,
    R_AS_FOLLOW_END,
    R_DURING_THE_YEAR,
    R_FULLY,
    R_HAVE,
    R_NOT,
)
from remarkable.predictor.hkex_predictor.schemas.util import get_sentence_model
from remarkable.predictor.models.fund_raising_group import P_AS_AT_DATES
from remarkable.predictor.utils import extract_date

R_USED_IN_PARA = rf"(?<!not )(?<!['‘’]t){R_USED}|(^|\s)use\s*of\s*([a-z]+\s+){{0,3}}{R_NET_PROCEEDS}"
# entirely: http://************:55647/#/hkex/annual-report-checking/report-review/265163?fileId=70247&schemaId=15&rule=B1&delist=0

R_USE_PREP = r"\b(as|for|to|in)\b"
R_INTENDED = r"(intention|(intend|plann?|propos)e?d?)"
R_REMAIN_UNUSED = rf"remain(ed)?\s*un{R_MIDDLE_DASH}?{R_UTILISE}d"
R_UNMATCH_TIMELINE = r"\b(time|timing|schedule|expect|estimate|remaining)"
# prospectus: https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6243#note_663744
P_COL_REVISED_USE = NeglectPattern.compile(match=r"\b(revise|reallocat|adjust|change)", unmatch=r"price|prospectus")
# 排除to be fully used： http://************:55647/#/project/remark/266108?treeId=7506&fileId=70436&schemaId=15&projectId=7506&schemaKey=B9
# http://************:55647/#/project/remark/219079?treeId=2883&fileId=65445&schemaId=15&projectId=2883&schemaKey=B9
P_FULLY_USED_IN_CELL = MatchMulti.compile(rf"(?<!be ){R_FULLY}|\bin\s*full|\b100{R_PERCENT}", R_USED, operator=all)

P_PROCEEDS_TBL_TITLE = NeglectPattern.compile(
    match=MatchMulti.compile(
        r"proceeds?|fund|raising",
        R_USED,
        R_INTENDED,
        R_UNUSED,
        rf"warrant|convertible|placing|rights?\s*issue|open\s*offer|{R_NET_PROCEEDS}",
        operator=any,
    ),
    # http://************:55647/#/project/remark/268879?treeId=5175&fileId=70603&schemaId=18&projectId=5175&schemaKey=C1.3
    unmatch=r"outstanding",
)

# 判断列名/行名单元格中是否包含during等描述在当年内的描述
P_DURING_IN_CELL = NeglectPattern.compile(
    match=MatchMulti.compile(
        r"\b(during|between|period|financial\s*year|FY\d{4}|for\s*the\s*year)",
        # http://************:55647/#/project/remark/294922?treeId=14268&fileId=70841&schemaId=18&projectId=14268&schemaKey=C1.1.1
        rf"from\s*(\d{{1,2}}[,，]?\s*|{R_EN_MONTH}[,，]?\s*)+\d{{4}}\s*to\s*(\d{{1,2}}|{R_EN_MONTH})\b",
        # http://************:55647/#/project/remark/266324?treeId=44871&fileId=70479&schemaId=18&projectId=44871&schemaKey=C1.1.1
        r"\b(ended|months)\b",
        operator=any,
    ),
    unmatch=r"from\s*the\s*(closing|listing)\s*date",
)
# http://************:55647/#/hkex/annual-report-checking/report-review/306384?fileId=71347&schemaId=5&rule=B8&delist=0
P_USED_IN_HEADER = NeglectPattern.compile(
    match=MatchMulti.compile(
        rf"(\s|^)(?<!not )(?<!un)(actual|use|utili(ti)?[sz]|applied\s*{R_USE_PREP})", r"proceeds", operator=all
    ),
    unmatch=R_UNMATCH_TIMELINE,
)
# http://************:55647/#/project/remark/230623?treeId=18259&fileId=61097&schemaId=15&projectId=18259&schemaKey=B8
# timing: https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6414#note_674331
P_USED_IN_CELL = NeglectPattern.compile(
    match=R_USED_IN_PARA, unmatch=rf"(?<!and )(?<!\bon the ){R_INTENDED}|{R_UNMATCH_TIMELINE}|{R_NOT}"
)
P_TEXT_DATES = PatternCollection(R_DATES_FOR_EXTRACT, flags=re.I)
P_NOT_USED = PositionPattern.compile(rf"\bnot\s*(been\s*)?{R_UTILISE}d")


B8_B10_NS_MODELS = [
    {
        "name": "para_match",
        "enum": AnswerValueEnum.NS.value,
        "para_separator": P_PERIOD_SEPARATOR,
        "paragraph_pattern": P_NOT_USED,
    },
]


class B8PostProcessor(BasePostProcessor):
    """
    B8表格后处理步骤：
    1. 必须在当年内或者截至当年期末
    2. 答案为PS表格：取最后一列，判断是否包含数值以及fully used相关描述，不包含则修改结果为NS
    3. 答案为PS段落：如果描述为全部未使用，修改为NS
    """

    P_B8_VALID_CELL_VALUE = MatchMulti.compile(P_VALID_CELL_VALUE, P_FULLY_USED_IN_CELL, operator=any)
    P_ALL_UNUSED = PositionPattern.compile(R_DURING_THE_YEAR, R_NET_PROCEEDS, rf"(are|were)\s*not\s*{R_UTILISE}d")

    def filter_ps_table_cells(self, parsed_cells, group_type):
        invalid_col_indices = {0}
        col_date_dict = self.get_date_from_col_header(parsed_cells, P_AS_AT_DATES)
        if self.year_end:
            for col, date_str in col_date_dict.items():
                if col == 0:
                    continue
                if date_str not in {self.year_end, get_date_by_offset_days(self.year_end, 1)}:
                    invalid_col_indices.add(col)
        elif len(col_date_dict) > 1:
            # 排除日期最大的那列
            max_date_col = max(col_date_dict.items(), key=lambda x: x[1])[0]
            invalid_col_indices = {c for c in col_date_dict if c != max_date_col}
        used_col_indices = {c.colidx for c in parsed_cells if c.colidx not in invalid_col_indices}
        if not used_col_indices:
            return []
        # 优先找包含during的列
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6840#note_710492
        col_headers = parsed_cells[0].table.col_header_texts
        if cols := {col for col in col_date_dict if "during" in col_headers[col].lower()}:
            used_col_indices = cols
        self.check_value_col = max(used_col_indices)
        return self.add_first_col_cells(
            [
                c
                for c in parsed_cells
                if c.colidx == self.check_value_col or c.colidx not in (invalid_col_indices | used_col_indices)
            ]
        )

    def is_ns_table(self, parsed_cells):
        self.check_value_pattern = self.P_B8_VALID_CELL_VALUE
        if not self.cells_have_value(parsed_cells):
            return True
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6840#note_706161
        cell_texts = [
            clean_txt(c.text, remove_cn_text=True)
            for c in parsed_cells
            if c.colidx == self.check_value_col and not c.is_header
        ]
        return all(not text or P_CELL_NIL.search(text) or P_NOT_USED.search(text) for text in cell_texts)

    def is_ns_para(self, text):
        return self.P_ALL_UNUSED.search(text)

    def is_valid_ns_para(self, text):
        if P_DURING_IN_CELL.search(text):
            return True
        return self.is_up_to_fy_end(text)

    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6236#note_682447
    def is_valid_ps_para(self, text, element):
        if element and P_DURING_THE_YEAR.search(text):
            return True
        date_str = extract_date(P_TEXT_DATES, text, str_format="%Y-%m-%d")
        if not date_str:
            if element and (elt_text := element.get("text")):
                # 句子里找不到日期，就在元素块中找
                return self.is_valid_ps_para(elt_text, None)
            return True
        return self.year_start <= date_str <= self.year_end


def table_models_for_proceeds(
    col_header_patterns, *, title_pattern=P_PROCEEDS_TBL_TITLE, enum=None, multi_elements=False, footnote_pattern=None
):
    return [
        {
            "name": "special_cells",
            "multi_elements": multi_elements,
            "enum": enum,
            "title_patterns": title_pattern,
            "model_id": "need_row_pattern",
            "multi": True,
            "remove_cn_text": True,
            "need_continuous": False,
            "without_col_header": True,  # 不用列名做匹配
            "row_pattern_as_section": True,
            "col_header_pattern": col_header_patterns,
        },
        {
            "name": "special_cells",
            "multi_elements": multi_elements,
            "enum": enum,
            "threshold": 0,
            "title_patterns": title_pattern,
            "multi": True,
            "need_continuous": True,
            "footnote_pattern": footnote_pattern,
            "col_header_pattern": col_header_patterns,
        },
    ]


predictor_options = [
    {
        # ------------------------------
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/5331
        # ------------------------------
        "path": ["B8"],
        "fake_leaf": True,
        "element_candidate_count": 20,
        "models": [
            {
                "name": "use_of_proceeds_group",
                "post_process_answers": B8PostProcessor(),
                "column_configs": {
                    # 子项1
                    "Detailed breakdown and description": [
                        *B1_B10_NS_MODELS,
                        # 表格和段落信息都要，应取尽取
                        {
                            "name": "multi_models",
                            "operator": "union",
                            "require_unique_element": True,
                            "sort_by_elt_index": True,
                            "enum": AnswerValueEnum.PS,
                            "models": [
                                *table_models_for_proceeds([P_USED_IN_HEADER, P_USED_IN_CELL]),
                                # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6463#note_676984
                                {
                                    "name": "special_cells",
                                    "enum": AnswerValueEnum.PS,
                                    "title_patterns": MatchMulti.compile(
                                        rf"{R_DURING_THE_YEAR}|breakdown|detail",
                                        # http://************:55647/#/project/remark/265273?treeId=7102&fileId=70269&schemaId=15&projectId=17&schemaKey=B8
                                        PositionPattern.compile(
                                            r"\bproceeds?\b",
                                            rf"(were|was)\s*{R_UTILISE}d|applied\s*{R_USE_PREP}.*[:：]$",
                                        ),
                                        operator=all,
                                    ),
                                    "whole_table": True,
                                    # "need_continuous": True,
                                },
                                get_sentence_model(
                                    [
                                        # http://************:55647/#/project/remark/264498?treeId=23794&fileId=70114&schemaId=15&projectId=17&schemaKey=
                                        PositionPattern.compile(rf"{R_NET_PROCEEDS}|\bfund\b", R_USED, R_USE_PREP),
                                        PositionPattern.compile(R_USED, R_USE_PREP, R_REASON),
                                        # http://************:55647/#/hkex/annual-report-checking/report-review/264947?fileId=70204&schemaId=15&rule=B8&delist=0
                                        PositionPattern.compile(
                                            rf"{R_HAVE}\s*{R_USED}\b",
                                            rf"{R_MONEY_UNIT}[1-9][,.\d]*\s*(million\s*)?{R_USE_PREP}",
                                        ),
                                        # http://************:55647/#/project/remark/266278?treeId=14511&fileId=70470&schemaId=15&projectId=17&schemaKey=
                                        PositionPattern.compile(
                                            rf"{R_HAVE}\s*{R_USED}\b", R_ALL, R_NET_PROCEEDS, R_USE_PREP
                                        ),
                                        rf"{R_MONEY_UNIT}[1-9][,.\d]*\s*(million\s*)?((have|has|had)\s*been|was|were)\s*{R_USED}\s*{R_USE_PREP}",
                                        MatchMulti.compile(
                                            rf"[(（][iv]{{1,4}}[）)]approximately\s*{R_MONEY_UNIT}[1-9][,.\d]*",
                                            rf"{R_MONEY_UNIT}[1-9][,.\d]*\s*(million\s*)?(was|were|(have|ha[ds])\s*been)\s*{R_USED}",
                                            operator=any,
                                        ),
                                    ],
                                    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6239#note_662312
                                    skip_pattern=[R_REMAIN_UNUSED, r"\s(as|with)\s*(the\s*)?(intended|planned)"],
                                    multi_elements=True,
                                    enum=AnswerValueEnum.PS.value,
                                ),
                            ],
                        },
                        # B8的NS描述：当年内实际使用为0, TODO 第一个正则太宽泛
                        get_sentence_model(
                            # remained unutilized: http://************:55647/#/hkex/annual-report-checking/report-review/265268?fileId=70268&schemaId=15&rule=B8&delist=0
                            [rf"{R_NOT}[^,，]*proceeds[^,，]*{R_USED}", R_REMAIN_UNUSED],
                            multi_elements=True,
                            skip_pattern=rf"{R_NOT}{R_FULLY}",
                            enum=AnswerValueEnum.NS,
                        ),
                        {
                            "name": "multi_models",
                            "operator": "all",
                            "models": [
                                get_sentence_model(
                                    [MatchMulti.compile(R_NET_PROCEEDS, rf"{R_MONEY_UNIT}[1-9]", operator=all)],
                                    skip_pattern=[R_AS_FOLLOW_END, R_REMAIN_UNUSED],
                                    multi_elements=False,
                                    enum=None,
                                    threshold=0.2,
                                ),
                                get_sentence_model(
                                    [
                                        MatchMulti.compile(
                                            R_NET_PROCEEDS,
                                            # http://************:55647/#/project/remark/266443?treeId=10192&fileId=70503&schemaId=15&projectId=10192&schemaKey=B9
                                            rf"{R_USED_IN_PARA}|\bby\s*way\s*of\b",
                                            operator=all,
                                        ),
                                    ],
                                    skip_pattern=[rf"will\s*be\s*{R_USED}|(?<!\bas )\bintend", R_REMAIN_UNUSED],
                                    multi_elements=True,
                                    enum=None,
                                    threshold=0.2,
                                ),
                            ],
                        },
                        *B8_B10_NS_MODELS,
                    ],
                },
            }
        ],
    }
]
