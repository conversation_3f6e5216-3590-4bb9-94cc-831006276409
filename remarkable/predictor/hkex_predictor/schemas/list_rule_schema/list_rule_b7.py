from remarkable.common.common_pattern import P_SEN_SEPARATOR
from remarkable.common.constants import AnswerValueEnum
from remarkable.common.pattern import Match<PERSON>ult<PERSON>
from remarkable.predictor.common_pattern import R_DATES
from remarkable.predictor.hkex_predictor.schemas.list_rule_schema.list_rule_b1 import (
    B1_B7_NS_MODELS,
    B1_B10_NS_MODELS,
)
from remarkable.predictor.hkex_predictor.schemas.list_rule_schema.list_rule_b4 import R_PRICE

R_DATE = r"\b(\d{1,2}\s*|[a-z]{3,9}\s*){2}\s*\d{4}\b"
# P_B7_PS = [
#     MatchMulti.compile(
#         r"(closing|market)\s*price", R_PRICE, r"the\s*date", r"agreement|announce(d|ments?)", operator=all
#     ),
#     # http://100.64.0.105:55647/#/project/remark/266408?treeId=17835&fileId=70496&schemaId=15&projectId=17835&schemaKey=B7
#     MatchMulti.compile(
#         r"(closing|market)\s*price",
#         R_PRICE,
#         r"\bon\s*(\d{1,2}\s*[a-z]{3,9}|[a-z]{3,9}\s*\d{1,2})\s*\d{4}",
#         operator=all,
#     ),
# ]
# share price: https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6236#note_662817
# average closing price不提取：https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6840#note_704392
R_MKT_PRICE = r"((?<!average )closing|market|share)\s*price"


# https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/5328
predictor_options = [
    {
        "path": ["B7"],
        "fake_leaf": True,
        "element_candidate_count": 20,
        "models": [
            {
                "name": "fund_raising_group",
                "column_configs": {
                    # 子项1
                    "Market price": [
                        *B1_B10_NS_MODELS,
                        *B1_B7_NS_MODELS,
                        {
                            "name": "para_match",
                            "enum": AnswerValueEnum.PS.value,
                            "para_separator": P_SEN_SEPARATOR,
                            "multi_elements": True,
                            "multi": True,
                            "paragraph_pattern": [
                                rf"{R_MKT_PRICE}.*?(?P<content>{R_PRICE}).*?the\s*date.*?(agreement|announce)",
                                rf"{R_MKT_PRICE}.*?the\s*date.*?(agreement|announce).*?(?P<content>{R_PRICE})",
                                rf"the\s*date.*?(agreement|announce).*?{R_MKT_PRICE}.*?(?P<content>{R_PRICE})",
                            ],
                        },
                        {
                            "name": "para_match",
                            "enum": AnswerValueEnum.PS.value,
                            "para_separator": P_SEN_SEPARATOR,
                            "multi_elements": True,
                            "multi": True,
                            "paragraph_pattern": [
                                rf"{R_MKT_PRICE}.*?(?P<content>{R_PRICE}).*?\bon\s*{R_DATE}",
                                rf"{R_MKT_PRICE}.*?\bon\s*{R_DATE}.*?(?P<content>{R_PRICE})\b",
                                rf"\bon\s*{R_DATE}.*?{R_MKT_PRICE}.*?(?P<content>{R_PRICE})\b",
                            ],
                        },
                        {
                            "name": "para_match",
                            "enum": AnswerValueEnum.PS.value,
                            "multi_elements": True,
                            "paragraph_pattern": [
                                rf"^on\s*{R_DATE}.*?{R_MKT_PRICE}.*?(?P<content>{R_PRICE})\b" for R_DATE in R_DATES
                            ],
                        },
                        {
                            "name": "special_cells",
                            "enum": AnswerValueEnum.PS.value,
                            "model_id": "need_row_pattern",
                            "multi": False,
                            "need_continuous": False,
                            "without_col_header": True,  # 不用列名做匹配
                            "col_header_pattern": r"fund|raising",
                        },
                        {
                            "name": "score_filter",
                            "pattern": MatchMulti.compile(r"(closing|market)\s*price", R_PRICE, operator=all),
                            "threshold": 0.9,
                        },
                    ]
                },
            },
        ],
    },
]
