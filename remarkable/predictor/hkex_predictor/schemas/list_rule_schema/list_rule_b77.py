import re

from remarkable.common.common_pattern import P_FOLLOW_PREFIX, P_SEN_SEPARATOR, R_MIDDLE_DASH
from remarkable.common.constants import AnnualReportEnum, AnswerValueEnum
from remarkable.common.pattern import MatchMulti, NeglectPattern, SplitBeforeMatch
from remarkable.common.util import clean_txt
from remarkable.predictor.common_pattern import R_CANCELLED
from remarkable.predictor.hkex_predictor.model_util import get_cell_note
from remarkable.predictor.hkex_predictor.models.yoda_layer.base import FollowMustExist
from remarkable.predictor.hkex_predictor.models.yoda_layer.rule.by_pos import ParaFromPos, SentenceFromSplitPara
from remarkable.predictor.hkex_predictor.models.yoda_layer.rule.root import ScoreDestFilter
from remarkable.predictor.hkex_predictor.schemas.list_rule_schema.common_models import get_no_grant_models
from remarkable.predictor.hkex_predictor.schemas.list_rule_schema.list_rule_b74 import (
    purchase_price_models,
)
from remarkable.predictor.hkex_predictor.schemas.list_rule_schema.list_rule_b76 import P_NEG_TITLE_OPTION
from remarkable.predictor.hkex_predictor.schemas.list_rule_schema.list_rule_b78 import (
    P_NEG_HEADERS,
    R_FORFEITED,
    R_LAPSED,
    table_col_row_locator,
)
from remarkable.predictor.hkex_predictor.schemas.pattern import (
    P_NUMBER_PS,
    R_AWARD_KEYWORD,
    R_AWARD_SHARES,
    R_DURING_THE_YEAR,
    R_NUMBER,
    R_NUMBER_PS,
    R_OPTION_KEYWORD,
)
from remarkable.predictor.schema_answer import ParagraphResult, TableCellsResult

P_CANCELLED = MatchMulti.compile(R_CANCELLED, operator=any)
P_CANCELLED_WITH_OTHER = SplitBeforeMatch.compile(
    NeglectPattern.compile(
        match=MatchMulti.compile(P_CANCELLED, rf"{R_FORFEITED}|{R_LAPSED}", operator=all),
        unmatch=r"before|price\s*for|repurchase",
    ),
    separator="\t",
    operator=any,
)
P_CANCELLED_WITHOUT_OTHER = NeglectPattern.compile(
    match=R_CANCELLED, unmatch=rf"{R_FORFEITED}|{R_LAPSED}|price\s*for|repurchase|locked-up"
)

P_ENUM_MATCHER = {
    AnswerValueEnum.ND.value: MatchMulti.compile(*R_NUMBER_PS, operator=any),
    AnswerValueEnum.NS.value: re.compile(".+"),
}

P_NOTE_PREFIX = re.compile(r"^(note\s*)?[(（ ]?(?P<num>\d+|[a-z]|[iv]+)[）) .]")
P_CELL_NIL = MatchMulti.compile(rf"^(0|nil|{R_MIDDLE_DASH}|nil|rsus)$", r"N/A|不適用", operator=any)

P_LAPSED_SHARES = MatchMulti.compile(
    r"(award|restrict)(ed)?\s*shares|shares?\s*awards?|rsus", rf"{R_FORFEITED}|{R_LAPSED}", operator=all
)
P_THESE = SplitBeforeMatch.compile(
    MatchMulti.compile(rf"these|^the\s*share\s*awards\s*({R_LAPSED}|{R_FORFEITED})", P_LAPSED_SHARES, operator=all),
    separator=P_SEN_SEPARATOR,
    operator=any,
)


def enum_cancelled(table_results: list[TableCellsResult], footnotes: list):
    table_cells = [cell for result in table_results for cell in result.parsed_cells]
    if any(P_NUMBER_PS.search(cell.text) for cell in table_cells if not cell.is_header):
        return AnswerValueEnum.PS.value
    return AnswerValueEnum.NS.value


def enum_cancelled_with_other(
    table_results: list[TableCellsResult], footnotes: list[ParagraphResult], only_has_cancelled=False
):
    table_cells = [cell for result in table_results for cell in result.parsed_cells]
    # 1. 所有非标题单元格都是nil/0/N/A/-，返回NS
    if all(P_CELL_NIL.search(cell.text) for cell in table_cells if not cell.is_header):
        return AnswerValueEnum.NS.value
    if not footnotes:
        # 2. 同时有cancelled和forfeited/lapsed，没有脚注，有数值则返回ND，但是要框选内容
        if P_NUMBER_PS.search("\t".join(cell.text for cell in table_cells)):
            if only_has_cancelled:
                return AnswerValueEnum.PS.value
            return AnswerValueEnum.ND.value
        return AnswerValueEnum.NS.value
    # 3. 同时有cancelled和forfeited/lapsed，有脚注，有数值，则要和脚注一起判断
    note_texts = {clean_txt(note.text) for note in footnotes}
    # if any(P_CANCELLED.search(text) for text in note_texts) or not any(P_FORFEITED_LAPSED.search(text) for text in note_texts):
    #     # 脚注中包含了cancelled或者没包含forfeited/lapsed，当做无脚注处理
    #     return enum_cells_and_footnotes(table_cells, [])
    total_cells = [
        cell
        for cell in table_cells
        if any(header.is_row_header and "total" in header.text.lower() for header in cell.headers)
    ]
    if total_cells:
        # 脚注描述1：共forfeited/lapsed了total rsus，说明cancelled为0
        total_pattern = SplitBeforeMatch.compile(
            MatchMulti.compile(clean_txt(total_cells[-1].text, remove_blank=True), P_LAPSED_SHARES, operator=all),
            separator=P_SEN_SEPARATOR,
            operator=any,
        )
        # http://100.64.0.105:55647/#/project/remark/233808?treeId=37712&fileId=66590&schemaId=15&projectId=17&schemaKey=B77&page=49
        if any(total_pattern.search(text) for text in note_texts):
            return AnswerValueEnum.NS.value
    # 脚注描述2：有脚注的是forfeited/lapsed，并用排除这些序号后的单元格做匹配
    # http://100.64.0.105:55647/#/project/remark/233057?treeId=37999&fileId=66465&schemaId=15&projectId=17&schemaKey=B77&page=51
    all_nums = {
        P_NOTE_PREFIX.search(text).group("num")
        for text in note_texts
        if P_NOTE_PREFIX.search(text) and P_LAPSED_SHARES.search(text)
    }
    if not all_nums:
        return enum_cancelled_with_other(table_cells, [])
    new_cells = [cell for cell in table_cells if not (get_cell_note(cell) & all_nums) and cell not in total_cells]
    return enum_cancelled_with_other(new_cells, [], only_has_cancelled=True)


predictor_options = [
    {
        "path": ["B77"],
        "fake_leaf": True,
        "models": [
            {
                "name": "group_based",
                "share_type": "award",
                "check_consumed": False,
                "follow_first_enum": AnswerValueEnum.NS.value,
                "column_configs": {
                    "Number of awards cancelled": [
                        # 模型1：通用NS
                        {
                            "name": "no_share",
                            "with_element_box": True,
                        },
                        # 模型2：①表名不包含option ②列名不包含forfeited、lapsed
                        {
                            "name": "special_cells",
                            "model_id": "table_1",
                            # "title_patterns": R_TABLE_TITLES,
                            "neglect_title_patterns": P_NEG_TITLE_OPTION,
                            "neglect_header_pattern": P_NEG_HEADERS,
                            "col_header_pattern": P_CANCELLED_WITHOUT_OTHER,
                            "enum_function": enum_cancelled,
                        },
                        # 模型3：①表名不包含option ②行名仅包含cancelled
                        # http://100.64.0.105:55647/#/project/remark/234000?treeId=42548&fileId=66622&schemaId=15&projectId=17&schemaKey=B77&page=172
                        {
                            "name": "special_cells",
                            "model_id": "table_2",
                            "include_footnotes": True,
                            "neglect_title_patterns": P_NEG_TITLE_OPTION,
                            "neglect_header_pattern": P_NEG_HEADERS,
                            "row_header_pattern": P_CANCELLED_WITHOUT_OTHER,
                            "col_header_pattern": rf"(number|\bno\.)\s*of\s*{R_AWARD_SHARES}",
                            "enum_function": enum_cancelled,
                        },
                        # 模型4：①表名包含了option + award ②列名不包含forfeited/lapsed
                        # http://100.64.0.105:55647/#/project/remark/231684?treeId=4265&fileId=66236&schemaId=15&projectId=17&schemaKey=B77&page=29
                        {
                            "name": "special_cells",
                            "model_id": "table_3",
                            "multi": True,
                            "title_patterns": MatchMulti.compile(R_OPTION_KEYWORD, R_AWARD_KEYWORD, operator=all),
                            "neglect_header_pattern": P_NEG_HEADERS,
                            "row_pattern": [r"\brsus\b"],
                            "col_header_pattern": ["nature", P_CANCELLED_WITHOUT_OTHER],
                            "enum_function": enum_cancelled,
                        },
                        # 模型5: 通过表格判断为ND时，再通过脚注判断为NS
                        table_col_row_locator(
                            AnnualReportEnum.NS,
                            P_CANCELLED_WITH_OTHER,
                            MatchMulti.compile(r"(^|\t)[\(（]?[\d.,，]+[\)）]?(\t|$)", operator=any),
                            locator=ParaFromPos(
                                pattern=MatchMulti.compile(r"lapsed during the year", P_FOLLOW_PREFIX, operator=all),
                            ),
                            filter=FollowMustExist(),
                        ),
                        # 模型6：匹配句子中的cancelled数量
                        # http://100.64.0.105:55647/#/project/remark/238023?treeId=8837&fileId=67293&schemaId=15&projectId=17&schemaKey=B77&page=182
                        {
                            "name": "yoda_layer",
                            "threshold": 0,
                            "multi_elements": True,
                            "enum_from_multi_element": True,
                            "enum": AnswerValueEnum.PS.value,
                            "rule": ScoreDestFilter(
                                multi=True,
                                dest=SentenceFromSplitPara(
                                    ordered_patterns=[
                                        MatchMulti.compile(
                                            R_DURING_THE_YEAR,
                                            MatchMulti.compile(
                                                rf"(\b{R_CANCELLED}|cancell?ation\s*of)\s*(the\s*)?{R_NUMBER}\s*{R_AWARD_SHARES}",
                                                rf"{R_NUMBER}\s*{R_AWARD_SHARES}\s*(have\s*|ha[sd]\s*)?(been|was|were)\s*{R_CANCELLED}",
                                                operator=any,
                                            ),
                                            operator=all,
                                        ),
                                    ],
                                    skip_pattern=re.compile(r"repurchase|locked-up", re.I),
                                ),
                            ),
                        },
                        # 模型7：①表名不包含option ②列名包含forfeited或lapsed，需要结合脚注判断
                        {
                            "name": "special_cells",
                            "model_id": "table_4",
                            # "title_patterns": R_TABLE_TITLES,
                            "include_footnotes": True,
                            "footnote_pattern": P_LAPSED_SHARES,
                            "neglect_title_patterns": P_NEG_TITLE_OPTION,
                            "neglect_header_pattern": P_NEG_HEADERS,
                            "col_header_pattern": P_CANCELLED_WITH_OTHER,
                            "enum_function": enum_cancelled_with_other,
                        },
                        # 模型8：①表名包含了option + award ②列名包含forfeited/lapsed，需要结合脚注判断
                        {
                            "name": "special_cells",
                            "model_id": "table_5",
                            "multi": True,
                            "include_footnotes": True,
                            "footnote_pattern": P_LAPSED_SHARES,
                            "title_patterns": MatchMulti.compile(R_OPTION_KEYWORD, R_AWARD_KEYWORD, operator=all),
                            "neglect_header_pattern": P_NEG_HEADERS,
                            "row_pattern": [r"\brsus\b"],
                            "col_header_pattern": ["nature", P_CANCELLED_WITH_OTHER],
                            # "enum_pattern": re.compile(rf"\b([\d,，]+|{R_MIDDLE_DASH})\b", re.I),
                            "enum_function": enum_cancelled_with_other,
                        },
                        # 模型9~11：B74~B78通用：no award was granted + outstanding期初期末都为0
                        *get_no_grant_models(r"cancell?ed"),
                    ],
                    "Purchase price": purchase_price_models(),
                },
            },
        ],
    },
]
