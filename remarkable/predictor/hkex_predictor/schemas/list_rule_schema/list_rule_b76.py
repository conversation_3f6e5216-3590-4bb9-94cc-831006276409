from remarkable.common.constants import <PERSON><PERSON><PERSON>ue<PERSON>num
from remarkable.common.pattern import <PERSON><PERSON><PERSON><PERSON>, NeglectPattern
from remarkable.common.util import clean_txt
from remarkable.predictor.hkex_predictor.models.yoda_layer.rule.by_pos import <PERSON><PERSON><PERSON>FromSplitPara
from remarkable.predictor.hkex_predictor.models.yoda_layer.rule.root import ScoreDestFilter
from remarkable.predictor.hkex_predictor.pattern import P_LIST_RULE_IGNORE_CHAPTERS, R_DAY
from remarkable.predictor.hkex_predictor.schemas.list_rule_schema.common_models import get_no_grant_models
from remarkable.predictor.hkex_predictor.schemas.list_rule_schema.list_rule_b74 import (
    R_CELL_START,
    R_COL_NUMBER_OF,
    purchase_price_models,
)
from remarkable.predictor.hkex_predictor.schemas.pattern import (
    R_AWARD_KEYWORD,
    R_AWARD_SHARES,
    R_DURING_THE_YEAR,
    R_NUMBER,
    R_OPTION_KEYWORD,
    R_PRICE,
)
from remarkable.predictor.models.special_cells import <PERSON>Mode
from remarkable.predictor.schema_answer import Paragraph<PERSON>esult, TableCellsResult

R_VEST = r"((attribute?|(?<!\bnot )(?<!\bno )\bvest|unlock|\blift)ed|已歸屬)"
R_VESTED = [
    rf"{R_CELL_START}(award\s*)?shares\s*{R_VEST}",
    rf"{R_CELL_START}(number|no\.)\s*of\s.*?{R_VEST}",
    rf"{R_VEST}\s*(\s*$| *\t| *\n|du?ring|\bin\b|\ba[st]\b|\bafter)",
    # http://************:55647/#/project/remark/232965?treeId=4451&fileId=66449&schemaId=15&projectId=17&schemaKey=B76
    rf"{R_CELL_START}vested\s*and\s*exercisable\s*at\s*",
    rf"{R_VEST}$",
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/4601#note_521648
    rf"{R_CELL_START}{R_VEST}\s*[（(]",
]
R_CELL_KEYWORDS = [
    rf"{R_CELL_START}(number|no\.)\s*of\s*([a-z]+\s*)?(shares\s*granted|awarded\s*shares|share\s*award|RSU|awards)",
    *R_VESTED,
]
# R_PURCHASE_PRICE_COLS = [r"(purchase|grant|issue)\s*price(?!\s*for)", r"grant\s*fee"]
# set out的场景： https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/4569#note_521077
P_CLOSING_PRICE = NeglectPattern.compile(
    match=MatchMulti.compile(r"weighted[\s-]*average", r"closing", r"before|prior|preced", operator=all),
    unmatch=r"\bset\s*out",
)

# 150,000 Awarded Shares have been vested
# 1,262,500 RSUs granted on 27 August 2021 have been vested
P_B76_PS_SENTENCE = MatchMulti.compile(
    MatchMulti.compile(
        rf"{R_NUMBER}\s*(awarded\s*shares|RSU)",
        r"\bvest(ed|ing)",
        R_DURING_THE_YEAR,
        operator=all,
    ),
    MatchMulti.compile(
        r"\bvest\s*\d+%",
        R_AWARD_KEYWORD,
        R_DURING_THE_YEAR,
        # r"the\s*total\s*number\s*of\s*vested",
        # rf"{R_BE}\s*{R_NUMBER}",
        operator=all,
    ),
    MatchMulti.compile(
        R_VEST, rf"{R_DURING_THE_YEAR}|\bon\s*{R_DAY}", rf"{R_NUMBER}\s*{R_AWARD_SHARES}|\d+%", operator=all
    ),
    operator=any,
)

P_CELL_PRICE = MatchMulti.compile(rf"^([*#^]+)?{R_PRICE}([*#^]+|[(（]?note\s*\w+[）)]?)?$", operator=any)
P_NEG_TITLE_OPTION = NeglectPattern.compile(match=R_OPTION_KEYWORD, unmatch=R_AWARD_KEYWORD)


def enum_closing_price_table(table_results: list[TableCellsResult], footnote_results: list[ParagraphResult]):
    table_cells = [cell for result in table_results for cell in result.parsed_cells]
    footnotes = [r.text for r in footnote_results]
    if not any(P_CLOSING_PRICE.search(clean_txt(note["text"])) for note in footnotes) and not any(
        P_CLOSING_PRICE.search(cell.no_cn_text) for cell in table_cells if cell.is_header
    ):
        return AnswerValueEnum.ND.value
    if any(P_CELL_PRICE.search(cell.no_cn_text) for cell in table_cells if not cell.is_header):
        return AnswerValueEnum.PS.value
    return AnswerValueEnum.NS.value


predictor_options = [
    {
        "path": ["B76"],
        "fake_leaf": True,
        "models": [
            {
                "name": "group_based",
                "share_type": "award",
                "check_consumed": False,
                "follow_first_enum": AnswerValueEnum.NS.value,
                "column_configs": {
                    "Number of awards vested": [
                        # 模型1：通用NS场景
                        {"name": "no_share", "with_element_box": True},
                        # 模型2：匹配表格的列
                        {
                            "name": "special_cells",
                            "model_id": "table_1",
                            # "title_patterns": R_TABLE_TITLES,
                            "neglect_title_patterns": P_NEG_TITLE_OPTION,
                            "col_header_pattern": R_VESTED,
                        },
                        # 模型3：分值高的表格尝试匹配
                        {
                            "name": "special_cells",
                            "model_id": "table_2",
                            "threshold": 0.8,
                            "col_pattern": R_VESTED,
                            "row_pattern": R_VESTED,
                            "without_col_header": True,
                            "without_row_header": True,
                        },
                        # 模型4：表格中的单元格：number of restricted
                        {
                            "name": "special_cells",
                            "model_id": "table_3",
                            "multi": True,
                            "last_mode": {LastMode.LAST_ROW, LastMode.FIRST_COL},
                            # "title_patterns": R_TABLE_TITLES,
                            "neglect_title_patterns": P_NEG_TITLE_OPTION,
                            "row_header_pattern": R_VESTED,
                            "col_header_pattern": R_COL_NUMBER_OF,
                        },
                        # 模型5：根据行名匹配表格
                        {
                            "name": "special_cells",
                            "model_id": "table_4",
                            # "title_patterns": R_TABLE_TITLES,
                            "neglect_title_patterns": P_NEG_TITLE_OPTION,
                            "row_header_pattern": R_VESTED,
                        },
                        # 模型6：提取PS句子
                        {
                            "name": "yoda_layer",
                            "threshold": 0,
                            "multi_elements": True,
                            "enum_every_element": True,
                            "skip_answer_value": AnswerValueEnum.ND.value,
                            "skip_syllabus_title": True,
                            "neglect_syllabus_regs": P_LIST_RULE_IGNORE_CHAPTERS.patterns,
                            "rule": ScoreDestFilter(
                                multi=True,
                                dest=SentenceFromSplitPara(
                                    ordered_patterns=[P_B76_PS_SENTENCE],
                                    # skip_pattern=MatchMulti.compile(*neg_patterns, operator=any),
                                    multi=True,
                                ),
                            ),
                        },
                        # 模型7~9：B74~B78通用：no award was granted + outstanding期初期末都为0
                        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/4881#note_545283
                        *get_no_grant_models(r"(\bvested|to\s*vesting)(?!\s*but)"),
                    ],
                    "Purchase price": purchase_price_models(),
                    "Closing price": [
                        # 模型1：通用NS场景
                        {
                            "name": "no_share",
                        },
                        # 模型2: 特例：https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/4147#note_485214
                        {
                            "name": "special_cells",
                            "skip_answer_value": AnswerValueEnum.ND.value,
                            "include_footnotes": True,
                            "neglect_title_patterns": R_OPTION_KEYWORD,
                            "any_cell_pattern": R_CELL_KEYWORDS,
                            "col_header_pattern": MatchMulti.compile(r"market\s*value", "vest", operator=all),
                            "footnote_pattern": P_CLOSING_PRICE,
                            "enum_function": enum_closing_price_table,
                        },
                        # 模型3：根据单元格关键词+列名匹配表格，严格匹配 加权平均+before
                        {
                            "name": "special_cells",
                            "skip_answer_value": AnswerValueEnum.ND.value,
                            "neglect_title_patterns": R_OPTION_KEYWORD,
                            "col_header_pattern": P_CLOSING_PRICE,
                            "any_cell_pattern": R_CELL_KEYWORDS,
                        },
                        # 模型4：根据单元格关键词+列名匹配表格，匹配加权平均
                        {
                            "name": "special_cells",
                            "skip_answer_value": AnswerValueEnum.ND.value,
                            "include_footnotes": True,
                            # "title_patterns": R_AWARD_KEYWORD,
                            "neglect_title_patterns": R_OPTION_KEYWORD,
                            "col_header_pattern": [
                                MatchMulti.compile(r"weighted[\s-]*average", r"closing", "price", operator=all),
                                # http://************:55647/#/project/remark/234378?treeId=5980&fileId=66685&schemaId=15&projectId=17&schemaKey=B76&page=96
                                MatchMulti.compile(
                                    r"price\s*of\s*company", r"immediately\s*preceding\s*the\s*vesting", operator=all
                                ),
                            ],
                            "any_cell_pattern": R_CELL_KEYWORDS,
                            "allow_only_footnotes": True,
                            "footnote_pattern": P_CLOSING_PRICE,
                            "enum_function": enum_closing_price_table,
                        },
                        # 模型5：根据单元格关键词+列名匹配表格
                        {
                            "name": "special_cells",
                            "skip_answer_value": AnswerValueEnum.ND.value,
                            "include_footnotes": True,
                            # "title_patterns": R_AWARD_KEYWORD,
                            "neglect_title_patterns": R_OPTION_KEYWORD,
                            "col_header_pattern": [
                                MatchMulti.compile(r"closing", "price", operator=all),
                                # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/3302#note_453412
                                r"^price\s*on\s*day\s*prior\s*to\s*vest",
                            ],
                            "any_cell_pattern": R_CELL_KEYWORDS,
                            "allow_only_footnotes": True,
                            "footnote_pattern": P_CLOSING_PRICE,
                            "enum_function": enum_closing_price_table,
                        },
                        # 模型6：匹配句子
                        {
                            "name": "yoda_layer",
                            "model_id": "ns_1",
                            "threshold": 0,
                            "multi_elements": True,
                            "enum_from_multi_element": True,
                            "rule": ScoreDestFilter(
                                dest=SentenceFromSplitPara(
                                    ordered_patterns=[P_CLOSING_PRICE],
                                ),
                                skip_pattern=MatchMulti.compile(R_OPTION_KEYWORD, operator=any),
                            ),
                        },
                        {
                            "name": "score_filter",
                            "threshold": 0.618,
                            "pattern": P_CLOSING_PRICE,
                            "neglect_pattern": [R_OPTION_KEYWORD],
                        },
                    ],
                },
            }
        ],
    },
]
