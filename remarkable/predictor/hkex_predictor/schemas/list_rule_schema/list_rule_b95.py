from remarkable.common.common_pattern import P_SEN_SEPARATOR
from remarkable.common.constants import AnswerValueEnum
from remarkable.common.pattern import <PERSON><PERSON><PERSON><PERSON>, SplitBeforeMatch
from remarkable.predictor.hkex_predictor.schemas.pattern import R_B95_NS_REGS, R_RC, R_REVIEW, reviewing_approving

review_remuneration_texts = (
    rf"{R_REVIEW} the remuneration policy and package of the executive Directors",
    rf"{R_REVIEW} the matters relating to share schemes adopted by the Company",
    rf"{reviewing_approving} relating to the share schemes",
    rf"{R_RC} held.*?meetings during the year ended",
)

predictor_options = [
    {
        "path": ["B95.1", "Content"],
        "default_enum_value": AnswerValueEnum.ND.value,
        "models": [
            {
                "name": "para_match",
                "paragraph_pattern": R_B95_NS_REGS,
            },
            {
                "name": "para_match",
                "multi_elements": True,
                "paragraph_pattern": (
                    SplitBeforeMatch(
                        MatchMulti.compile(
                            R_REVIEW,
                            r"share option",
                            operator=all,
                        ),
                        separator=P_SEN_SEPARATOR,
                        operator=any,
                    ),
                    rf"the remuneration and assessment committee {R_REVIEW} the remuneration",
                    r"in respect of any share option schemes as may be implemented",
                    r"approve the grant of options under the Post-IPO Share Option Plan",
                    rf"{R_REVIEW} and approving matters related to share schemes",
                    rf"{R_RC} convened one meeting during the Relevant Period to",
                    rf"{R_REVIEW} and approve.*?share incentive plan",
                    rf"{R_RC} during the year included the following",
                    r"tasks completed by the Remuneration Committee during the Year",
                    *review_remuneration_texts,
                ),
            },
            {
                "name": "syllabus_elt_v2",
                "inject_syllabus_features": [
                    r"__regex__New share option scheme",
                ],
            },
            {
                "name": "score_filter",
                "threshold": 0.2,
            },
            {"name": "empty", "enum": AnswerValueEnum.ND.value},
        ],
    },
    {
        "path": ["B95.2", "Content"],
        "default_enum_value": AnswerValueEnum.ND.value,
        "models": [
            {
                "name": "para_match",
                "multi_elements": True,
                "paragraph_pattern": (
                    rf"{R_RC} convened one meeting during the Relevant Period to",
                    rf"{R_RC} had performed the following works",
                    rf"{R_RC} held.*?meetings with the following",
                    rf"{R_RC} held a meeting and passed a written resolution",
                    rf"{R_REVIEW} and approve.*?share incentive plan",
                    rf"{R_REVIEW}.*?Co-Ownership Plan",
                    rf"{reviewing_approving} relating to the share schemes",
                    rf"{R_RC} has not {reviewing_approving}",
                    SplitBeforeMatch(
                        MatchMulti.compile(
                            r"recommendations",
                            r"share award",
                            operator=all,
                        ),
                        separator=P_SEN_SEPARATOR,
                        operator=any,
                    ),
                ),
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    SplitBeforeMatch(
                        MatchMulti.compile(
                            rf"{R_REVIEW}|recommendations",
                            r"share award|Co-Ownership Plan",
                            operator=all,
                        ),
                        separator=P_SEN_SEPARATOR,
                        operator=any,
                    ),
                    rf"{R_RC} has {R_REVIEW} and approved the grant of share options",
                    rf"{R_RC} held.*?outstanding awarded shares",
                    rf"{R_REVIEW} the remuneration policy and package of the executive",
                    *review_remuneration_texts,
                ),
            },
            {
                "name": "score_filter",
                "neglect_pattern": (r"no share option granted under the Share Option",),
                "threshold": 0.2,
            },
            {"name": "empty", "enum": AnswerValueEnum.ND.value},
        ],
    },
]
