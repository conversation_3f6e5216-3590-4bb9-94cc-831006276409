# Jura 2.1 Listing Rules
from remarkable.predictor.common_pattern import R_AWARD
from remarkable.predictor.hkex_predictor.schemas.pattern import reg_words
from remarkable.predictor.share_group_utils import R_SHARE_AWARD

R_NO_SHARE_OPTIONS = [
    # rf"\bNot?\b(?:(?!\s*\blapsed\b|\boptions\b|\bcancelled\b|be more than|award|exceed|period)[^.,]){reg_words(0, 5)}(share\s*)?(option|(Pre|Post)-IPO|scheme)",
    r"\bNot?\s*\b(?:(?!\s*\blapsed\b|\bcancelled\b|\bcancel\b|(more|less) than|award|exceed|period)(\b\w+\b\s*)){0,5}(option|(Pre|Post)-IPO|scheme)",
    rf"No\s*options?\s*{reg_words(0, 4)}grant",
    rf"Share\s*Option\s*Scheme\s*{reg_words(0, 7)}(as|was)\s*expired",
    rf"(not|nor) (implement|constitute|grant)(ed)? {reg_words(0, 3)} (share )?option( scheme)?",
    r"\bNo\b options and/or share awards were grant",
    r"Share Option Scheme was terminated",
    r"nor agreed to grant options",
]

R_NO_SHARE_AWARDS = [
    r"\bno\b\s*share\s*award(?!\s*expense)",
    rf"\bno\b\s*({R_AWARD}|restricted)?\s*([a-z]\s*)?(shares?|RSUs){{1,2}}(?:(?!\s*\blapsed\b|\boptions\b|\bcancelled\b)[^.])*(award|grant)",
    rf"\bno\b RSUs? {reg_words(1, 2)}granted {reg_words(1, 3)}pursuant {reg_words(1, 2)}RSUs? Scheme",
    rf"\bno\b\s*({R_AWARD})?\s*shares? {reg_words(0, 4)} purchase",
    rf"\bno\b {reg_words(0, 3)} ({R_AWARD}|Shares) {reg_words(0, 3)} award",
    rf"\bno\b\s*((({R_AWARD}|Shares?)\s){{1,2}}|Matching)\s*{reg_words(1, 2)}granted\s*({reg_words(0, 3)}under the ((Share\s*)?{R_AWARD}\s*Schem|ESPP|Plan)|since its adoption|during)",
    rf"\bno\b\s*{R_AWARD}\s*{reg_words(1, 2)}granted[^.]*({R_SHARE_AWARD}\s*Schemes?\s*)?since\s*{reg_words(1, 2)}adopt(ed|ion)",
    rf"\bnot\b grant(ed)? any ({R_AWARD}|share){{1,2}}",
    # rf"did not grant any {R_AWARD} shares",  # 66695
    r"\bnot\b granted any restricted shares.*Restricted Share Award",
    rf"None of {reg_words(0, 3)} has been {reg_words(1, 2)} under the Share {R_AWARD} Scheme",
    rf"terminated\s*{reg_words(0, 2)}\s*share\s*award\s*scheme",
    r"has not granted any RSUs",  # 66623
    r"Such mandate has not been used and will lapse",  # 66477
    r"\bRS\b Plan was terminated in its entirety ",  # 66458
]

predictor_options = []
