from remarkable.common.common_pattern import P_SEN_SEPARATOR
from remarkable.common.constants import AnswerValueEnum
from remarkable.common.pattern import <PERSON><PERSON><PERSON><PERSON>, PositionPattern
from remarkable.predictor.hkex_predictor.schemas.list_rule_schema.list_rule_b1 import (
    B1_B7_NS_MODELS,
    B1_B10_NS_MODELS,
)
from remarkable.predictor.hkex_predictor.schemas.pattern import R_APPELLATION, R_ENG_NUMBER, R_NOT
from remarkable.predictor.hkex_predictor.schemas.util import get_sentence_model

R_SUBSCRIBERS = r"\b(?i:allott?ee?|subscriber|place[er]|underwriter)[sS]?"
R_NOT_LESS_THAN_SIX = r"\b(more|not?\s*(less|fewer))\s*than\s*(six|6)\b"
R_MORE_THAN_SIX = rf"\b([6-9]|six|seven|eight|nine|ten|eleven|twelve|(thir|fou?r|fif|six|seven|eigh|nin)teen|twenty|[1-9]\d+)\s*({R_SUBSCRIBERS}|independent|institutional|individual\s*investors)"
# http://************:55647/#/hkex/annual-report-checking/report-review/265208?fileId=70256&schemaId=15&rule=B6&delist=0
R_ONLY_AVAIL_FOR = r"\bonly\s*available\s*(to|for)\s.*?Shareholders"
R_COMPANY_NAME = r"(\s[A-Z][a-zA-Z.,]+){3,}\s|\s[A-Z].*\b((?i:Ltd|Co|Inc|Corp|Cie)\b|L(?i:imited)|C(?i:orporation))"

P_B6_NOT_LESS_THAN_SIX = MatchMulti.compile(R_NOT_LESS_THAN_SIX, R_MORE_THAN_SIX, R_ONLY_AVAIL_FOR, operator=any)
# http://************:55647/#/hkex/annual-report-checking/report-review/264947?fileId=70204&schemaId=15&rule=B4&delist=0
P_SUBSCRIBER = PositionPattern.compile(
    # http://************:55647/#/hkex/annual-report-checking/report-review/266373?fileId=70489&schemaId=15&rule=B6&delist=0
    r"\b(?i:allott?e[ed]?|subscribe[dr]|place[ed]|underwriter)\b",
    r"\b(to|with)\b",
    # https://************:55647/#/hkex/annual-report-checking/report-review/306376?fileId=71346&schemaId=5&rule=B6&delist=0
    rf"{R_APPELLATION}|(\s[A-Z][a-zA-Z.,]+){{3,}}\s|\b[A-Z].*\b((?i:Ltd|Co|Inc|Corp|Cie)\b|L(?i:imited)|C(?i:orporation))|{R_ENG_NUMBER}\s*{R_SUBSCRIBERS}",
    flag=0,
)
P_B6_PS = [
    # 不少于6人，要有明确表述
    P_B6_NOT_LESS_THAN_SIX,
    # 少于6人时，要披露人名
    P_SUBSCRIBER,
    # http://************:55647/#/hkex/annual-report-checking/report-review/306377?fileId=71346&schemaId=15&rule=B6&delist=0
    rf"[“”\"][a-z]*\s*{R_SUBSCRIBERS}\b[^“”\"]*[“”\"]",
    PositionPattern.compile(
        r"\b(?i:allott?e[ed]|subscribe[dr]|place[ed]|underwriter)\b",
        r"\b(to|with)\b",
        # https://************:55647/#/hkex/annual-report-checking/report-review/306376?fileId=71346&schemaId=5&rule=B6&delist=0
        rf"{R_APPELLATION}|{R_COMPANY_NAME}|{R_ENG_NUMBER}\s*{R_SUBSCRIBERS}",
        flag=0,
    ),
    PositionPattern.compile(
        r"\b(?i:allott?e[ed]|subscribe[dr]|place[ed]|underwriter)\b",
        R_APPELLATION,
        flag=0,
    ),
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6236#note_661196
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6238#note_662741
    MatchMulti.compile(
        r"entered\s*into|(placing|subscription)\s*agreement",
        rf"{R_APPELLATION}|{R_COMPANY_NAME}",
        r"\bagreed\s*to\s*subscribe",
        flag=0,
        operator=all,
    ),
    # http://************:55647/#/hkex/annual-report-checking/report-review/266373?fileId=70489&schemaId=15&rule=B6&delist=0
    MatchMulti.compile(rf"\b(?i:the)\s*{R_SUBSCRIBERS}[,，]?\s*[A-Z]", operator=any, flag=0),
]


# https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/5327  Names of allottes
predictor_options = [
    {
        "path": ["B6"],
        "fake_leaf": True,
        "element_candidate_count": 20,
        "models": [
            {
                "name": "fund_raising_group",
                "column_configs": {
                    # 子项1
                    "Names of allottes": [
                        *B1_B10_NS_MODELS,
                        *B1_B7_NS_MODELS,
                        {
                            "name": "para_match",
                            "enum": AnswerValueEnum.PS.value,
                            "multi_elements": True,
                            "multi": True,
                            "para_separator": P_SEN_SEPARATOR,
                            "paragraph_pattern": [
                                rf"(?P<content>{R_NOT_LESS_THAN_SIX}\s*({R_SUBSCRIBERS}|.*?s\b))",
                                rf"(?P<content>{R_MORE_THAN_SIX})",
                                rf"(?P<content>{R_ONLY_AVAIL_FOR})",
                            ],
                            # http://************:55647/#/hkex/annual-report-checking/report-review/265268?fileId=70268&schemaId=15&rule=B6&delist=0
                            "as_follow_pattern": PositionPattern.compile(r"entered\s*into", R_SUBSCRIBERS, r"[:：]$"),
                        },
                        # 指定人名的句子
                        get_sentence_model(
                            P_B6_PS, skip_pattern=MatchMulti.compile(R_NOT, operator=any), multi_elements=True
                        ),
                        {
                            "name": "special_cells",
                            "enum": AnswerValueEnum.PS.value,
                            "model_id": "need_row_pattern",
                            "multi": True,
                            "need_continuous": False,
                            "without_col_header": True,  # 不用列名做匹配
                            "col_header_pattern": r"fund|raising",
                            "enum_pattern": P_B6_PS,
                        },
                        {
                            "name": "score_filter",
                            "enum": AnswerValueEnum.PS.value,
                            "pattern": r"allottees|subscriber|placees",
                            "threshold": 0.9,
                        },
                    ]
                },
            },
        ],
    },
]
