import re
from typing import Literal

from remarkable.common.common_pattern import P_SEN_SEPARATOR, R_MIDDLE_DASHES
from remarkable.common.constants import AnnualReportEnum
from remarkable.common.pattern import (
    MatchMulti,
    NeglectPattern,
    PositionPattern,
    SplitBeforeMatch,
)
from remarkable.pdfinsight.interdoc_reader import <PERSON><PERSON><PERSON>, RowPara, Table
from remarkable.pdfinsight.parser import ParsedTable<PERSON>ell
from remarkable.predictor.common_pattern import R_AWARD, R_EN_MONTH
from remarkable.predictor.hkex_predictor.cg_enum import R_EN_NUM
from remarkable.predictor.hkex_predictor.models.yoda_layer.base import KeepFollowAnswerFilter, StopAtChapter
from remarkable.predictor.hkex_predictor.models.yoda_layer.filter import TParaTextFilter
from remarkable.predictor.hkex_predictor.models.yoda_layer.operator import OptionOperator
from remarkable.predictor.hkex_predictor.models.yoda_layer.rule.by_pos import (
    ParaFromPos,
    SentenceFromSplitPara,
    TableFromPos,
)
from remarkable.predictor.hkex_predictor.models.yoda_layer.rule.root import (
    RDChapterLocator,
    ScoreDestFilter,
    ScoreFirstElementFilter,
    ScoreFirstTableLocator,
)
from remarkable.predictor.hkex_predictor.schemas.list_rule_schema.list_rule_schema import (
    R_NO_SHARE_AWARDS,
    R_NO_SHARE_OPTIONS,
)
from remarkable.predictor.hkex_predictor.schemas.pattern import (
    R_AWARD_KEYWORD_LIST,
    R_NO_OPTION_GRANTED,
    R_NO_OPTION_NS,
    R_NUMBER_PS,
    reg_words,
)

P_SCHEME_SEPARATOR = re.compile(rf"{P_SEN_SEPARATOR.pattern}|\sto\s*replace\s|instead\s*(of\s*)?|\band\s*a\s*new", re.I)
R_ROW_COL_HEADER = [
    rf"(Granted|{R_AWARD})\s*during\s*the\s*(Report(ing)?)?(year|report|period)",
    r"Number\s*of\s*Grant",
    r"Number\s*of\s*restricted\s*A\s*shares\s*newly\s*granted\s*during\s*the\s*reporting\s*period",
    r"^Granted",
]

R_B94_2_CHAPTER = [
    r"share\s*award\s*scheme$",
    r"^Number\s*of\s*Share\s*Awards$",
    r"Share\s*(Award|Purchase)\s*Plan\s*(Limit)?",
    r"Grant\s*of\s*RSUs",
    r"SHARE\s*INCENTIVE\s*SCHEME",
    r"Total\s*number\s*of\s*Shares\s*to\s*be\s*grant",
    r"RSUs?\s*(granted|Scheme)",
    r"RS\s*Plan",
]
P_B94_2_CHAPTER = MatchMulti.compile(*R_B94_2_CHAPTER, operator=any)

R_B94_1_CHAPTER = [
    r"share\s*option\s*scheme$",
    # http://100.64.0.105:55647/#/project/remark/250333?treeId=10931&fileId=68511&schemaId=15&projectId=17
    # 针对68511章节识别问题
    r"(1) The Company",
]
P_B94_1_CHAPTER = MatchMulti.compile(*R_B94_1_CHAPTER, operator=any)


# save as，no granted 描述不清楚时，需要上下找表格
R_EXCEPT_SHARE_AWARDS = [
    r"saved?\s*as\s*disclosed\s*above",
    r"except\s*for",
    r"apart\s*from",
    r"referred to in the table above",
]

R_EXCEPT_SHARE_OPTION = [
    rf"under\s*{reg_words(1, 2)}above",
    r"Save\s*(as|for)",
    r"or\s*awarded\s*Shares",
    r"Apart\s*from",
    # r"Other\s*than",
]

R_NEGLECT_SHARE_AWARDS = [
    rf"no\s*{R_AWARD}\s*shares was lapsed",  # 66685
    r"no Shares.*were acquired by the Trustee"  # 66660
    rf"({'|'.join(R_EN_NUM)})\s*of\s*({'|'.join(R_EN_NUM)}).*under\s*the\s*Share\s*Award\s*Plan",  # 66822
    r"Restricted Shares remained unvested",
    r"no unvested awarded shares",
    r"charged to profit or loss",
    r"to (our Directors|employees)",
    r"under\s*the\s*said\s*specific\s*mandate",
    r"under\s*\S+\s*scheme\s*since\s*\d{4}",
    rf"no\s*outstanding\s*{R_AWARD}\s*shares?",
    r"No Shares shall be purchased",
    rf"Unless approved\s*{reg_words(0, 2)}Board",
    rf"to {reg_words(1, 3)} (Directors|highest paid individuals)",
    r"options? granted",
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/3606#note_446103
    r"Saved?\s*as\s*disclosed\s*above",
    # http://100.64.0.105:55647/#/project/remark/251165?treeId=37592&fileId=68650&schemaId=15&projectId=17
    r"in certain circumstances",
]

R_NEGLECT_SHARE_OPTIONS = [
    # r"No further[^.]*under",
    r"as defined below",
    # rf"outstanding|{R_CANCELLED}",
    r"Save as disclosed above",
    # http://100.64.0.105:55647/#/project/remark/233030?treeId=10192&fileId=66460&schemaId=15&projectId=10192&schemaKey=B94.1
    r"remain in force and effect in all other respects",
    # http://100.64.0.105:55647/#/project/remark/233197?treeId=13539&fileId=66488&schemaId=15&projectId=13539&schemaKey=B94.1
    r"(ha[sd]|was|are|were|be(en))\s*(expired|exercised)",
    r"options?\s*expired",
    # http://100.64.0.105:55647/#/project/remark/232929?treeId=16807&fileId=66443&schemaId=15&projectId=16807&schemaKey=B94.1
    r"option shall lapse",
    # http://100.64.0.105:55647/#/project/remark/232971?treeId=5530&fileId=66450&schemaId=15&projectId=5530&schemaKey=B94.1
    r"years after",
    r"limit being exceeded",
    r"after the Listing Date",
    # http://100.64.0.105:55647/#/project/remark/233922?treeId=4887&fileId=66609&schemaId=15&projectId=4887&schemaKey=B94.1
    r"latest grant exceeds",
]

R_SHARE_OPTION_SCHEMA = r"Shares?\s*Options?(\s*Scheme)?"


R_NS_PERCENT_NIL = [
    r"weighted average number of total Shares.*is nil",
    r"representing 0[%％] of the weighted average number",
    r"no\s*Shares\s*[(（].*[:：]\s*nil[)）]",
]
R_POSITIVE_NEGLECT = [
    r"exceed.*of the total number",  # 66759
    r"(not\s*exceed|representing).*share",
    r"remained\s*outstanding\s*under\s*the\s*Scheme",  # 66771
    r"representing approximately",  # 66774
    r"(permitted\s*to|may)\s*be\s*awarded",  # 66685
    # http://100.64.0.105:55647/#/project/remark/232929?treeId=16807&fileId=66443&schemaId=15&projectId=16807&schemaKey=B94.2
    r"approval by the Shareholders",
    # http://100.64.0.105:55647/#/project/remark/234077?treeId=37672&fileId=66635&schemaId=15&projectId=37672&schemaKey=B94.2
    r"not grant any Awarded Shares",
    # http://100.64.0.105:55647/#/project/remark/250429?treeId=9318&fileId=68527&schemaId=15&projectId=17
    r"shall not make",
    # http://100.64.0.105:55647/#/project/remark/250757?treeId=7412&fileId=68582&schemaId=15&projectId=17
    r"relevant class in issue for the same period",
]


R_NUMBER_SHARE_AWARD = r"number\s*of\s*(Shares|award)"
R_PERCENT = r"(%|0\.\d+)"
R_GRANT = rf"(options|RSUs|awards)\s*{reg_words(0, 1)}\s*grant"  # # 66774

R_SHARE_AWARDS = [
    R_GRANT,
    r"divide",
    r"shares",
    R_PERCENT,
    R_NUMBER_SHARE_AWARD,
]

R_DEADLINE = [rf"as\s*at\s*\d{{1,2}}\s*({R_EN_MONTH})\s*(?P<year>20\d{{2}})"]
R_CURRENT_TIME = [rf"on\s*\d{{1,2}}\s*({R_EN_MONTH})\s*(?P<year>20\d{{2}})"]

P_POSITIVE = [
    PositionPattern.compile(
        *R_SHARE_AWARDS,
    ),
    PositionPattern.compile(
        R_NUMBER_SHARE_AWARD,
        R_GRANT,
        r"divide",
        R_PERCENT,
    ),
    PositionPattern.compile(
        R_NUMBER_SHARE_AWARD,
        r"award",
        r"divide",
        R_PERCENT,
    ),
    PositionPattern.compile(
        R_NUMBER_SHARE_AWARD,
        R_PERCENT,
        r"shares",
    ),
]

P_PS_COL_VALUE = NeglectPattern.compile(
    match=MatchMulti.compile(*R_NUMBER_PS, operator=any),
    unmatch=SplitBeforeMatch.compile(
        MatchMulti.compile(
            r"\(share\(s\)\)",
            r"\d{4} Incentive Scheme",
            r"\u4e00-\u9fa5",
            operator=any,
        ),
        separator="\t",
        operator=any,
    ),
)
P_NS_COL_VALUE = NeglectPattern.compile(
    match=MatchMulti.compile(
        rf"(^|\t)[\(（]?[{R_MIDDLE_DASHES}0oO\s*][\)）]?(\t|$)", r"(^|\t)(N/A|nil)($|\t)", operator=any
    ),
    unmatch=P_PS_COL_VALUE,
)

P_ROW_COL_HEADER = SplitBeforeMatch.compile(
    MatchMulti.compile(*R_ROW_COL_HEADER, operator=any), separator="\t", operator=any
)


def select_table_col_row(
    header_pattern,
    value_pattern,
    types: tuple[Literal["row", "col"], ...] = ("col", "row"),
):
    return TParaTextFilter(
        MatchMulti.compile(
            header_pattern,
            value_pattern,
            operator=all,
        )
    ).as_table_filter(*types)


ns_para_pos_94_2 = ParaFromPos(
    start=0,
    limit=200,
    only_dest_answers=True,
    pattern=MatchMulti.compile(*(R_NO_SHARE_AWARDS + R_NS_PERCENT_NIL), operator=any),
    dest=SentenceFromSplitPara(
        ordered_patterns=[
            NeglectPattern.compile(
                match=MatchMulti.compile(*(R_NO_SHARE_AWARDS + R_NS_PERCENT_NIL), operator=any),
                unmatch=MatchMulti.compile(
                    *R_NEGLECT_SHARE_AWARDS,
                    NeglectPattern.compile(
                        match=MatchMulti.compile(R_SHARE_OPTION_SCHEMA, operator=any),
                        unmatch=MatchMulti.compile(*R_EXCEPT_SHARE_OPTION, operator=any),
                    ),
                    operator=any,
                ),
            ),
        ],
        skip_pattern=NeglectPattern.compile(
            match=MatchMulti.compile(*R_EXCEPT_SHARE_AWARDS, operator=any),
            unmatch=MatchMulti.compile(*(R_NO_SHARE_AWARDS + R_NS_PERCENT_NIL), operator=any),
        ),
    ),
)

ps_para_pos_94_2 = ParaFromPos(
    start=0,
    size=5,
    only_dest_answers=True,
    pattern=MatchMulti.compile(*R_SHARE_AWARDS, operator=any),
    dest=SentenceFromSplitPara(
        skip_pattern=MatchMulti.compile(
            *R_POSITIVE_NEGLECT,
            operator=any,
        ),
        ordered_patterns=P_POSITIVE,
        multi=True,
    ),
)

P_OPTION_SEPARATOR = re.compile(rf"{P_SCHEME_SEPARATOR.pattern}|\band\s*there\s*were", re.I)

R_NS_94_1 = [*R_NO_SHARE_OPTIONS, *R_NO_OPTION_NS, *R_NO_OPTION_GRANTED]
ns_para_pos_94_1 = SentenceFromSplitPara(
    separator=P_OPTION_SEPARATOR,
    ordered_patterns=[
        NeglectPattern.compile(
            match=MatchMulti.compile(*R_NS_94_1, operator=any),
            unmatch=MatchMulti.compile(
                *(R_NEGLECT_SHARE_OPTIONS + R_EXCEPT_SHARE_OPTION),
                operator=any,
            ),
        ),
    ],
    skip_pattern=NeglectPattern.compile(
        match=MatchMulti.compile(*R_EXCEPT_SHARE_AWARDS, operator=any),
        unmatch=MatchMulti.compile(*R_NS_94_1, operator=any),
    ),
)
P_PS_94_1 = MatchMulti.compile(
    "shares that may be issued",
    MatchMulti.compile(
        r"divided\s*by",
        r"weighted\s*average\s*number\s*of\s*(the\s*)?(ordinary\s*)?shares",
        operator=any,
    ),
    operator=all,
)

ps_para_pos_94_1 = SentenceFromSplitPara(
    ordered_patterns=[
        P_PS_94_1,
    ]
)

P_ROW_COL_94_1_HEADER = SplitBeforeMatch.compile(
    NeglectPattern.compile(
        match=MatchMulti.compile(*R_ROW_COL_HEADER, operator=any),
        unmatch=MatchMulti.compile(*R_AWARD_KEYWORD_LIST, operator=any),
    ),
    separator="\t",
    operator=any,
)


def enum_cancelled(table_cells: list[ParsedTableCell], footnotes: list[dict]):
    if any(P_PS_COL_VALUE.search(cell.text) for cell in table_cells if not cell.is_header):
        return AnnualReportEnum.PS.value
    return AnnualReportEnum.NS.value


def save_for_table(chapter):
    return [
        {
            "name": "yoda_layer",
            "threshold": 0.618,
            "enum": AnnualReportEnum.ND,
            "rule": OptionOperator.handoff(
                RDChapterLocator(
                    pattern=chapter,
                    dest=ParaFromPos(
                        size=5,
                        pattern=MatchMulti.compile(*R_EXCEPT_SHARE_AWARDS, operator=any),
                    ),
                ),
                TableFromPos.from_bothway(
                    filter=select_table_col_row(P_ROW_COL_HEADER, P_PS_COL_VALUE),
                    types=("row", "col"),
                    stop=StopAtChapter,
                ),
                answer_filter=KeepFollowAnswerFilter(),
            ),
        },
        {
            "name": "yoda_layer",
            "threshold": 1,
            "enum": AnnualReportEnum.NS,
            "rule": OptionOperator.handoff(
                ScoreDestFilter(
                    pattern=MatchMulti.compile(*R_EXCEPT_SHARE_AWARDS, operator=any),
                    dest=ParaFromPos(
                        size=5,
                        start=0,
                        pattern=MatchMulti.compile(*R_EXCEPT_SHARE_AWARDS, operator=any),
                    ),
                ),
                TableFromPos.from_bothway(
                    filter=select_table_col_row(P_ROW_COL_HEADER, P_NS_COL_VALUE),
                    types=("row", "col"),
                    stop=StopAtChapter,
                ),
                answer_filter=KeepFollowAnswerFilter(),
            ),
        },
        {
            "name": "yoda_layer",
            "threshold": 0.618,
            "enum": AnnualReportEnum.NS,
            "rule": OptionOperator.handoff(
                RDChapterLocator(
                    pattern=chapter,
                    dest=ParaFromPos(
                        size=5,
                        pattern=MatchMulti.compile(*R_EXCEPT_SHARE_AWARDS, operator=any),
                    ),
                ),
                TableFromPos.from_bothway(
                    filter=select_table_col_row(P_ROW_COL_HEADER, P_NS_COL_VALUE),
                    types=("row", "col"),
                    stop=StopAtChapter,
                ),
                answer_filter=KeepFollowAnswerFilter(),
            ),
        },
    ]


predictor_options = [
    {
        "path": ["B94.1"],
        "fake_leaf": True,
        "models": [
            {
                "name": "group_based",
                "share_type": "option",
                "check_consumed": False,
                "group_default_enum": AnnualReportEnum.ND.value,
                "column_configs": {
                    "Content": [
                        {
                            "name": "no_share",
                            "with_element_box": True,
                            "share_type": "option",
                            "enum": AnnualReportEnum.NS.value,
                        },
                        *save_for_table(P_B94_1_CHAPTER),
                        {
                            "name": "yoda_layer",
                            "threshold": 0,
                            "enum": AnnualReportEnum.NS,
                            "rule": ScoreDestFilter(
                                dest=ns_para_pos_94_1,
                            ),
                        },
                        {
                            "name": "yoda_layer",
                            "threshold": 0,
                            "enum": AnnualReportEnum.NS,
                            "rule": ScoreFirstElementFilter[Table[ColPara | RowPara]](
                                strict=False,
                                filter=select_table_col_row(P_ROW_COL_94_1_HEADER, P_NS_COL_VALUE, ("col", "row")),
                            ),
                        },
                        # {
                        #     "name": "yoda_layer",
                        #     "threshold": 0,
                        #     "enum": AnnualReportEnum.PS,
                        #     "with_element_box": False,
                        #     "rule": ScoreFirstElementFilter[Table[ColPara | RowPara]](
                        #         strict=False,
                        #         filter=select_table_col_row(P_ROW_COL_94_1_HEADER, P_PS_COL_VALUE, ("col", "row")),
                        #     ),
                        # },
                        # {
                        #     "name": "yoda_layer",
                        #     "threshold": 0,
                        #     "enum": AnnualReportEnum.PS,
                        #     "rule": ScoreDestFilter(
                        #         dest=ps_para_pos_94_1,
                        #     ),
                        # },
                        {
                            "name": "yoda_layer",
                            "threshold": 0,
                            "enum": AnnualReportEnum.PS,
                            "rule": ScoreDestFilter(
                                dest=ps_para_pos_94_1,
                            ),
                        },
                        {
                            "name": "para_match",
                            "skip_syllabus_title": True,
                            # "neglect_syllabus_regs": P_NEG_CHAPTER,
                            "as_follow_pattern": P_PS_94_1,
                            "neglect_pattern": [
                                r"Without taking into account",
                            ],
                            "enum": AnnualReportEnum.PS.value,
                        },
                        # {
                        #     "name": "yoda_layer",
                        #     "threshold": 0,
                        #     "enum": AnnualReportEnum.NS,
                        #     "rule": RDChapterLocator(
                        #         pattern=P_B94_1_CHAPTER,
                        #         dest=ns_para_pos_94_1,
                        #     ),
                        # },
                    ],
                },
            },
        ],
    },
    {
        "path": ["B94.2"],
        "fake_leaf": True,
        "models": [
            {
                "name": "group_based",
                "share_type": "award",
                "column_configs": {
                    "Content": [
                        {
                            "name": "no_share",
                            "with_element_box": True,
                            "enum": AnnualReportEnum.NS.value,
                        },
                        *save_for_table(P_B94_2_CHAPTER),
                        {
                            "name": "yoda_layer",
                            "threshold": 1,
                            "enum": AnnualReportEnum.NS,
                            "rule": ScoreDestFilter(
                                dest=ns_para_pos_94_2,
                            ),
                        },
                        {
                            "name": "yoda_layer",
                            "threshold": 0,
                            "enum": AnnualReportEnum.NS,
                            "rule": ScoreFirstTableLocator(
                                dest=TableFromPos.from_bothway(
                                    start=0,
                                    limit=0,
                                    filter=select_table_col_row(P_ROW_COL_HEADER, P_NS_COL_VALUE),
                                    types=("col", "row"),
                                ),
                            ),
                        },
                        {
                            "name": "yoda_layer",
                            "threshold": 0,
                            "enum": AnnualReportEnum.ND,
                            # "with_element_box": False,
                            "rule": ScoreFirstTableLocator(
                                dest=TableFromPos.from_bothway(
                                    start=0,
                                    limit=0,
                                    filter=select_table_col_row(P_ROW_COL_HEADER, P_PS_COL_VALUE),
                                    types=("col", "row"),
                                ),
                            ),
                        },
                        {
                            "name": "yoda_layer",
                            "threshold": 0,
                            "enum": AnnualReportEnum.NS,
                            "rule": RDChapterLocator(
                                pattern=P_B94_2_CHAPTER,
                                dest=ns_para_pos_94_2,
                            ),
                        },
                        {
                            "name": "yoda_layer",
                            "threshold": 1,
                            "enum": AnnualReportEnum.PS,
                            "rule": ScoreDestFilter(
                                dest=ps_para_pos_94_2,
                            ),
                        },
                        {
                            "name": "yoda_layer",
                            "threshold": 0,
                            "enum": AnnualReportEnum.PS,
                            "rule": RDChapterLocator(
                                pattern=P_B94_2_CHAPTER,
                                dest=ps_para_pos_94_2,
                            ),
                        },
                        {
                            "name": "b94",
                            "col_header": R_ROW_COL_HEADER,
                            "ns_exclude": R_EXCEPT_SHARE_AWARDS,
                            "ns_neglect": R_NEGLECT_SHARE_AWARDS,
                            "ns_no_share_options": R_NO_SHARE_AWARDS + [],
                            "ns_percent_nil": R_NS_PERCENT_NIL,
                            "positive": P_POSITIVE,
                            "positive_neglect": R_POSITIVE_NEGLECT,
                            "deadline": R_DEADLINE,
                            "current_time": R_CURRENT_TIME,
                            "weighted_average": [
                                r"weighted average",
                            ],
                            "ns_col_value": P_NS_COL_VALUE,
                            "ps_col_value": P_PS_COL_VALUE,
                        },
                    ]
                },
            },
        ],
    },
]
