from remarkable.common.common_pattern import P_PERIOD_SEPARATOR, R_CURRENCY, R_MIDDLE_DASH, R_NOTES_CHAPTER_TITLES
from remarkable.common.constants import AnswerValueEnum
from remarkable.common.pattern import (
    MatchMulti,
    PositionPattern,
)
from remarkable.predictor.hkex_predictor.models.yoda_layer.rule.by_pos import SentenceFromSplitPara
from remarkable.predictor.hkex_predictor.models.yoda_layer.rule.root import ScoreDestFilter
from remarkable.predictor.hkex_predictor.schemas.pattern import R_AS_FOLLOW_END, R_NOT, R_SAVE_AS
from remarkable.predictor.hkex_predictor.schemas.util import get_sentence_model
from remarkable.predictor.models.fund_raising_group import P_KW_CHAPTER_TITLES
from remarkable.predictor.models.para_match import AsFollowType

P_B1_TBL_TITLE = MatchMulti.compile(r"proceeds", r"\b(used?\b|utili[sz]|actual|intend|plan|applied)", operator=all)

R_UTILISE = r"(use|utili(ti)?[sz]e)"
R_USED = rf"\b(?<!un)(?<!un{R_MIDDLE_DASH})(({R_UTILISE}|applie|injecte)d|usage|utili[sz]ation?)"  # intended
R_UNUSED = rf"\b(un{R_MIDDLE_DASH}?{R_UTILISE}d|not\s*(yet\s*)?({R_UTILISE}|propose|applie)d|remain|balance|unalloc|brought\s*forward)"

# 一些常见用途
R_REASON = r"\b(development|repayment|loans|debts|general\s*work|(additional|working)\s*capital)\b"
R_PURPOSE = [
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6240#note_661276
    r"\bin\s*order\s*to",
    r"\bthe\s*(reason|purpose)\b",
    r"\bintend(ed|s)?\s*to\b",
    r"\bto\s*(support|enhance|enlarge|improve)",
    R_REASON,
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6240#note_662337
    r"\bfor\s*(the\s*|its\s*)?(expansion|enhance|increase|improve)",
    # http://************:55647/#/project/remark/264498?treeId=23794&fileId=70114&schemaId=15&projectId=17&schemaKey=
    rf"\b({R_CURRENCY}[\d.,]+(\s*million)?|proceeds|fund\s*rais).*?\b(utili[sz]ed?|used|applied)\s*(for|as|to)\b",
    # http://************:55647/#/hkex/annual-report-checking/report-review/266223?fileId=70459&schemaId=15&rule=B1&delist=0
    r"raise\s*funds?\s*(for|to)\s*[(（][ivx]{1,4}[)）]",
]
P_B1_PS = [
    MatchMulti.compile(*R_PURPOSE, operator=any),
    MatchMulti.compile(
        r"(directors|company)\s*(consider(ed|s)?|([a-z]+\s*)?of\s*the\s*view)\s+[a-z]{2,}\s", operator=any
    ),
    MatchMulti.compile(r"approximate|proceeds", r"repayment|working|redemption|(in)?debt", operator=all),
    # http://************:55647/#/project/remark/263863?treeId=45534&fileId=69987&schemaId=15&projectId=17&schemaKey=B1 index=347
    # NeglectPattern.compile(match=P_B1_TBL_TITLE, unmatch=r"disclosed|headed]s*[“”]"),
    P_B1_TBL_TITLE,
]
P_B1_SKIP = MatchMulti.compile(R_AS_FOLLOW_END, r"according\s*to\b", r"\bentered\s*into", operator=any)


B1_B10_NS_MODELS = [
    {
        "name": "para_match",
        "enum": AnswerValueEnum.NS.value,
        "para_separator": P_PERIOD_SEPARATOR,
        "paragraph_pattern": [
            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6239#note_662313
            # http://************:55647/#/project/remark/266438?treeId=23612&fileId=70502&schemaId=15&projectId=23612&schemaKey=B9
            MatchMulti.compile(
                r"agreement", r"(?<!shall )(?<!shall be )(terminate|cancell?e?|lapse|expire)d?\b", operator=all
            ),
        ],
        # http://************:55647/#/project/remark/251147?treeId=5730&fileId=68647&schemaId=15&projectId=5730&schemaKey=B9 元素块329
        # http://************:55647/#/project/remark/264498?treeId=23794&fileId=70114&schemaId=15&projectId=17&schemaKey=B1 元素块1877
        "neglect_sentence_pattern": [
            r"\bat\s*any\s*time",
            rf"{R_NOT}[^，,]*(terminate|cancell?e?|lapse|expire)d",
            r"\bany\s*unsubscribed",
        ],
    },
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6240#note_661184
    {
        "name": "para_match",
        "enum": AnswerValueEnum.NS.value,
        "extend_candidates_syllabus_regs": [r"issue\s*of\s*(shares|securities)"],
        "paragraph_pattern": [r"(√|✔|✅|☑)\s*(Not\s*applicable|N/A)\s*$"],
    },
]
B1_B7_NS_MODELS = [
    {
        "name": "yoda_layer",
        "threshold": 0,
        "enum": AnswerValueEnum.NS.value,
        "rule": ScoreDestFilter(
            dest=SentenceFromSplitPara(
                separator=P_PERIOD_SEPARATOR,
                skip_pattern=MatchMulti.compile(R_SAVE_AS, operator=any),
                ordered_patterns=[
                    MatchMulti.compile(
                        MatchMulti.compile(
                            r"\b(no[tr]|didn.t)\s+(issued?|conduct(ed)?|proceed|ma[kd]e)\s*any\b",
                            r"\b(no[tr]|didn.t)\s+ma[kd]e\s*any\s*fund",
                            r"\bno\b.+?(are|were)\s+(issued?|conduct(ed)?|proceed)",
                            operator=any,
                        ),
                        r"\b(fund\s*raising|activit|rights|share)",
                        operator=all,
                    ),
                    MatchMulti.compile(
                        r"condition\s*precedents", r"\bnot\s*(been\s*|fully\s*)fullfilled", operator=all
                    ),
                    MatchMulti.compile(r"\bnot\s*complete", operator=any),
                ],
            ),
        ),
    },
]


# https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/5322
predictor_options = [
    {
        "path": ["B1"],
        "fake_leaf": True,
        "element_candidate_count": 20,
        "models": [
            {
                "name": "fund_raising_group",
                "column_configs": {
                    # 子项1
                    "Issue reason": [
                        *B1_B10_NS_MODELS,
                        *B1_B7_NS_MODELS,
                        # 表格和段落内容都要
                        {
                            "name": "multi_models",
                            "operator": "union",
                            "enum": AnswerValueEnum.PS,
                            "require_unique_element": True,
                            "sort_by_elt_index": True,
                            "models": [
                                # 优先提句子中的准确描述
                                {
                                    "name": "para_match",
                                    "multi_elements": True,
                                    "enum": AnswerValueEnum.PS.value,
                                    "syllabus_regs": P_KW_CHAPTER_TITLES,
                                    "neglect_syllabus_regs": [r"capital\s*structure"],
                                    "para_separator": P_PERIOD_SEPARATOR,
                                    "paragraph_pattern": R_PURPOSE,
                                    "as_follow_pattern": PositionPattern.compile(
                                        r"\bin\s*order\s*to|\bthe\s*(reason|purpose)\b|\bintend(ed|s)?\s*to\b",
                                        r"[:：]$",
                                    ),
                                    # 排除as follow的起始句
                                    "neglect_sentence_pattern": [
                                        # r"prospectus",
                                        r"[:：]$",
                                        r"\bas\s*(plann?ed|intention|intend)",
                                    ],
                                },
                                {
                                    "name": "special_cells",
                                    # "neglect_syllabus_regs": [r"share\s*capital", *R_NOTES_CHAPTER_TITLES],
                                    "enum": AnswerValueEnum.PS.value,
                                    "neglect_syllabus_regs": [r"capital\s*structure"],
                                    "multi": True,
                                    "need_continuous": False,
                                    "model_id": "need_row_pattern",
                                    "any_cell_pattern": [r"proceeds"],
                                    # http://************:55647/#/project/remark/219378?treeId=10231&fileId=65505&schemaId=15&projectId=17&schemaKey=B1
                                    "row_pattern_as_section": True,
                                    # 取第一列
                                    "special_col_indices": [0],
                                },
                                {
                                    "name": "multi_models",
                                    "enum": AnswerValueEnum.PS.value,
                                    "operator": "any",
                                    "deduplicate_elements": True,
                                    "models": [
                                        # 提取标题明确的列： https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6239#note_662320
                                        {
                                            "name": "special_cells",
                                            "neglect_syllabus_regs": [
                                                r"capital\s*structure",
                                                r"share\s*capital",
                                                *R_NOTES_CHAPTER_TITLES,
                                            ],
                                            "any_cell_pattern": [r"proceeds"],
                                            "multi_elements": True,
                                            "col_header_pattern": [r"\b(purpose|reason)\b"],
                                        },
                                        {
                                            "name": "special_cells",
                                            "neglect_syllabus_regs": [r"capital\s*structure"],
                                            # "neglect_syllabus_regs": [r"share\s*capital", *R_NOTES_CHAPTER_TITLES],
                                            "enum": AnswerValueEnum.PS.value,
                                            "title_patterns": [
                                                r"use\s*of\s*(the\s*)?(net\s*)?proceeds",
                                                P_B1_TBL_TITLE,
                                            ],
                                            "any_cell_pattern": [r"proceeds"],
                                            "multi_elements": True,
                                            "multi": True,
                                            "need_continuous": False,
                                            # 取第一列
                                            "special_col_indices": [0],
                                        },
                                        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6414#note_673267
                                        {
                                            "name": "special_cells",
                                            "neglect_syllabus_regs": [
                                                r"capital\s*structure",
                                                r"share\s*capital",
                                                *R_NOTES_CHAPTER_TITLES,
                                            ],
                                            "any_cell_pattern": [r"proceeds"],
                                            "multi_elements": True,
                                            # 取第一列
                                            "special_col_indices": [0],
                                        },
                                    ],
                                },
                            ],
                        },
                        # 再提句子中不太准确的描述
                        get_sentence_model(P_B1_PS, skip_pattern=P_B1_SKIP, multi_elements=True, threshold=0.618),
                        # http://************:55647/#/project/remark/232375?treeId=6800&fileId=66351&schemaId=15&projectId=6800&schemaKey=B5
                        {
                            "name": "para_match",
                            "threshold": 0.7,
                            "as_follow_pattern": MatchMulti.compile(
                                MatchMulti.compile(*P_B1_PS, operator=any), R_AS_FOLLOW_END, operator=all
                            ),
                            "as_follow_type": AsFollowType.PARA,
                        },
                        {
                            "name": "score_filter",
                            "skip_notes": True,
                            "enum": AnswerValueEnum.PS.value,
                            "pattern": P_B1_PS,
                            "threshold": 0.9,
                        },
                    ],
                },
            }
        ],
    }
]
