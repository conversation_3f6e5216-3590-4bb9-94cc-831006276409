from remarkable.common.common_pattern import P_SEN_SEPARATOR
from remarkable.common.constants import AnnualReportEnum, AnswerValueEnum
from remarkable.common.pattern import <PERSON><PERSON>ulti, SplitBeforeMatch
from remarkable.common.protocol import SearchPatternLike
from remarkable.predictor.hkex_predictor.models.yoda_layer.base import FollowMustExist
from remarkable.predictor.hkex_predictor.models.yoda_layer.form import FirstParaAsPos
from remarkable.predictor.hkex_predictor.models.yoda_layer.rule.by_pos import ParaFromPos, SentenceFromSplitPara
from remarkable.predictor.hkex_predictor.models.yoda_layer.rule.root import ScoreDestFilter
from remarkable.predictor.hkex_predictor.schemas.list_rule_schema.list_rule_b74 import R_NOT_REQ_TO_PAY
from remarkable.predictor.hkex_predictor.schemas.pattern import (
    R_AWARD_KEYWORD,
    R_NO_AWARD_NS,
    R_OPTION_KEYWORD,
    reg_words,
)

# PS条件1
P_PS_AMOUNT = MatchMulti.compile(
    r"accept|application",
    r"(RMB|HK\$?[DS]?\$?|US\$?|\$)\s*([1-9][\d.,]*|0\.([1-9]|0+[1-9])\d*)",
    r"\b(pay|paid)|consideration",
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/4573#note_521759
    f"{R_AWARD_KEYWORD}|award offer",
    operator=all,
)
# PS条件2
P_PS_SPECIFY = MatchMulti.compile(
    r"determine|specif|subject\s*to", r"board|committee|administrator|conditions", operator=all
)
P_PS_SKIP = MatchMulti.compile(
    r"per\s*(shares?\s*award|award(s|ed)?\s*share)", rf"{R_OPTION_KEYWORD}(?!\s*(and/or|or|and)\s*award)", operator=any
)
# NS条件
P_NS_AMOUNT = MatchMulti.compile(
    *R_NO_AWARD_NS,
    R_NOT_REQ_TO_PAY,
    r"\b(no|nil|without)\s*consideration",
    MatchMulti.compile(r"\bnot\sprovide|\bno\s*price", r"\b(paid|pay)", r"accept|application", operator=all),
    MatchMulti.compile(r"\bno\s*payment", r"accept|application", operator=all),
    r"\b(no|nil|without)\s*(amount|cost|acceptance\s*price|(subscription/)?purchase\s*price)",
    r"grant\s*price\s*was\s*nil",
    MatchMulti.compile(
        r"accept|application", r"\b(amount|pay|paid)", r"is\s*(RMB|HK\$?[DS]?\$?|US\$?|\$)?\s*(nil|0)\b", operator=all
    ),
    # http://************:55647/#/project/remark/239670?treeId=20582&fileId=67568&schemaId=15&projectId=17&schemaKey=B80
    r"\bnot?\s*specif(y|ic)\s*(the)?\s*amount\s*(to\s*)?(be\s*paid|pay)",
    r"purchase\s*price\s*of\s*all\s*the\s*above\s*share\s*awards\s*granted\s*is\s*(HK\$?[DS]?\$?)?(0|nil)\b",
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/3306#note_444869
    rf"\bno\s*such\s*provision\s*{reg_words(0, 2)}scheme\.?$",
    r"^None\.?$",
    operator=any,
)
P_NS_SKIP = MatchMulti.compile(
    R_OPTION_KEYWORD,
    # http://************:55647/#/project/remark/231828?treeId=8594&fileId=66260&schemaId=15&projectId=17&schemaKey=B80
    r"third\s*parties",
    # 排除明确数量的股份
    # rf"{R_NUMBER}\s*(of\s*the\s*)?{R_AWARD_SHARES}",
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/3306#note_443517
    r"exercisable|fair\s*value",
    r"executive\s*director|\bM(r|r?s)\.",
    # http://************:55647/#/project/remark/232965?treeId=4451&fileId=66449&schemaId=15&projectId=17&schemaKey=B80&page=303
    r"received",
    # http://************:55647/#/project/remark/233808?treeId=37712&fileId=66590&schemaId=15&projectId=17&schemaKey=B80&page=173
    r"ordinary\s*shares",
    operator=any,
)

PS_MODELS = [
    # PS场景2：关键词+determine/specify+board/committee/administrator
    {
        "name": "yoda_layer",
        "threshold": 0,
        "enum": AnnualReportEnum.PS,
        "rule": ScoreDestFilter(
            dest=SentenceFromSplitPara(
                ordered_patterns=[
                    MatchMulti.compile(r"accept|application", r"\b(amount|pay|paid)", P_PS_SPECIFY, operator=all)
                ],
                skip_pattern=P_PS_SKIP,
            ),
        ),
    },
    # PS场景3：acceptance章节 + 关键词+determine/specify+board/committee/administrator
    # http://************:55647/#/project/remark/231557?treeId=4700&fileId=66215&schemaId=15&projectId=17&schemaKey=B80
    {
        "name": "yoda_layer",
        "threshold": 0,
        "enum": AnnualReportEnum.PS,
        "syllabus_regs": [r"acceptance"],
        "rule": ScoreDestFilter(
            dest=SentenceFromSplitPara(
                ordered_patterns=[
                    MatchMulti.compile(r"\b(amount|pay|paid)", P_PS_SPECIFY, operator=all),
                ],
                skip_pattern=P_PS_SKIP,
            ),
        ),
    },
    # PS场景4：payment+acceptance章节 + 关键词+determine/specify+board/committee/administrator
    # http://************:55647/#/project/remark/231557?treeId=4700&fileId=66215&schemaId=15&projectId=17&schemaKey=B80
    {
        "name": "yoda_layer",
        "threshold": 0,
        "enum": AnnualReportEnum.PS,
        "syllabus_regs": [r"(pay|paid|amount).*acceptance"],
        "rule": ScoreDestFilter(
            dest=SentenceFromSplitPara(
                ordered_patterns=[P_PS_SPECIFY],
                skip_pattern=P_PS_SKIP,
            ),
        ),
    },
]


def table_kv_model(
    enum_value: str,
    amount_pattern: SearchPatternLike | str,
):
    return {
        "name": "table_kv",
        "enum": enum_value,
        "feature_white_list": [
            "__regex__(amount payable|Purchase price)",
        ],
        "regs": {
            "Amount": [
                amount_pattern,
            ],
        },
        "only_matched_value": True,
        "multi": True,
    }


predictor_options = [
    {
        "path": ["B80"],
        "fake_leaf": True,
        "models": [
            {
                "name": "group_based",
                "share_type": "award",
                "group_default_enum": AnnualReportEnum.ND.value,
                "follow_first_enum": AnswerValueEnum.NS.value,
                "check_consumed": False,
                "column_configs": {
                    "Amount": [
                        {
                            "name": "no_share",
                            "with_element_box": True,
                        },
                        # PS场景1:段落：明确提到价格
                        {
                            "name": "yoda_layer",
                            "threshold": 0,
                            "enum": AnnualReportEnum.PS,
                            "rule": ScoreDestFilter(
                                dest=SentenceFromSplitPara(
                                    ordered_patterns=[P_PS_AMOUNT],
                                    skip_pattern=P_PS_SKIP,
                                ),
                            ),
                        },
                        # PS场景2：表格
                        table_kv_model(enum_value=AnnualReportEnum.PS, amount_pattern=P_PS_AMOUNT),
                        # NS场景
                        {
                            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/4573#note_521317
                            "name": "yoda_layer",
                            "threshold": 0,
                            "enum": AnnualReportEnum.NS,
                            "rule": ScoreDestFilter(
                                dest=ParaFromPos(
                                    start=0,
                                    limit=1,
                                    pattern=MatchMulti.compile(
                                        r"(amount payable|Payment) on ((application|acceptance)\s*(or|and)?\s*){1,2} of (the )?award",
                                        operator=any,
                                    ),
                                    skip_pattern=P_NS_SKIP,
                                    adapter=FirstParaAsPos(),
                                ).link_next(
                                    locator=ParaFromPos(
                                        limit=2,
                                        pattern=P_NS_AMOUNT,
                                    ),
                                    filter=FollowMustExist(),
                                ),
                            ),
                        },
                        {
                            "name": "yoda_layer",
                            "threshold": 0,
                            "enum": AnnualReportEnum.NS,
                            "rule": ScoreDestFilter(
                                dest=SentenceFromSplitPara(ordered_patterns=[P_NS_AMOUNT], skip_pattern=P_NS_SKIP),
                            ),
                        },
                        table_kv_model(enum_value=AnnualReportEnum.NS, amount_pattern=P_NS_AMOUNT),
                        # http://************:55647/#/project/remark/231888?treeId=9951&fileId=66270&schemaId=15&projectId=17&schemaKey=B80
                        {
                            "name": "special_cells",
                            "row_header_pattern": r"acceptance",
                            "col_header_pattern": R_AWARD_KEYWORD,
                        },
                        # PS其它场景：价格由董事会/薪酬委员会决定
                        *PS_MODELS,
                    ],
                    "Period": [
                        # PS场景1：明确提到价格
                        {
                            "name": "yoda_layer",
                            "threshold": 0,
                            "enum": AnnualReportEnum.PS,
                            "rule": ScoreDestFilter(
                                # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/4573#note_521759
                                # 使用SentenceFromSplitPara 只匹配到amount部分，会被截断，后面Period不会框选
                                dest=ParaFromPos(
                                    start=0,
                                    pattern=SplitBeforeMatch(
                                        P_PS_AMOUNT,
                                        separator=P_SEN_SEPARATOR,
                                        operator=any,
                                    ),
                                    skip_pattern=P_PS_SKIP,
                                )
                            ),
                        },
                        # PS其它场景：价格由董事会/薪酬委员会决定
                        *PS_MODELS,
                    ],
                },
            }
        ],
    }
]
