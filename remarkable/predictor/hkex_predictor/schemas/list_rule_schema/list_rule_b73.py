from remarkable.common.common_pattern import P_PERIOD_SEPARATOR
from remarkable.common.constants import AnswerValueEnum
from remarkable.common.pattern import (
    MatchMulti,
    NeglectPattern,
    SplitBeforeMatch,
)
from remarkable.predictor.hkex_predictor.pattern import P_EQUITY_CHAPTER, R_BE
from remarkable.predictor.hkex_predictor.schemas.list_rule_schema.common_models import get_no_grant_models
from remarkable.predictor.hkex_predictor.schemas.list_rule_schema.list_rule_b63 import (
    P_B63_1_ENUM_PATTERN,
    P_B63_1_NAMES,
    P_B63_1_TABLE_COL,
)
from remarkable.predictor.hkex_predictor.schemas.list_rule_schema.list_rule_b74 import (
    P_B74_NO_UNVESTED,
)
from remarkable.predictor.hkex_predictor.schemas.pattern import (
    P_DURING_REPORT_YEAR,
    R_APPELLATION,
    R_AWARD_KEYWORD,
    R_AWARD_SHARES,
    R_MOVEMENT,
)

P_B73_TITLE_NEG = [
    NeglectPattern.compile(match=r"\boption|meeting|\bmet\b|retir", unmatch=R_AWARD_KEYWORD),
    # # http://100.64.0.105:55647/#/project/remark/257803?treeId=21303&fileId=68775&schemaId=15&projectId=17&schemaKey=B73
    # r"shares\s*repurchased|position",
]
R_NUMBER_OF_AWARDS = r"(number|no\.)\s*of\s*(share|award|rsu)s"

P_B73_TBL_TITLE = [
    MatchMulti.compile(R_AWARD_KEYWORD, R_MOVEMENT, operator=all),
    MatchMulti.compile(r"outstanding|grant|allocation", R_AWARD_KEYWORD, operator=all),
    R_NUMBER_OF_AWARDS,
]
R_ANY_CELL = [
    r"grant\s*date",
    r"date\s*of\s*grant",
    r"(Granted|vested|vesting|Forfeited|Cancell?ed|Lapsed|lasped|reclassified)",
    P_DURING_REPORT_YEAR,
]
P_PS_B73_PAPA = MatchMulti.compile(
    P_DURING_REPORT_YEAR,
    R_AWARD_SHARES,
    SplitBeforeMatch.compile(
        MatchMulti.compile(
            rf"grantee|awardee|\b(granted|issued|awarded|held)\s*(to|by|under)\b|selected\s*(person|participant|eligible)s?\s*{R_BE}",
            rf"senior|director|employee|consultant|management|Consultant|{R_APPELLATION}",
            operator=all,
        ),
        separator=P_PERIOD_SEPARATOR,
    ),
    operator=all,
)

predictor_options = [
    {
        "path": ["B73"],
        "fake_leaf": True,
        "models": [
            {
                "name": "group_based",
                "share_type": "award",
                "group_default_enum": AnswerValueEnum.ND.value,
                "column_configs": {
                    "Content": [
                        # 模型1: 通用NS
                        {"name": "no_share", "with_element_box": True},
                        # 模型2: 找表格中的人名列
                        {
                            "name": "multi_models",
                            "operator": "union",
                            "enum": AnswerValueEnum.PS.value,
                            "require_unique_element": True,
                            "models": [
                                # 1: 找表格，表名为movement且列名包含name, grantee, category等
                                {
                                    "name": "special_cells",
                                    "multi_elements": True,
                                    "multi": True,
                                    "include_footnotes": True,
                                    "neglect_title_patterns": P_B73_TITLE_NEG,
                                    "title_patterns": P_B73_TBL_TITLE,
                                    "col_header_pattern": P_B63_1_TABLE_COL,
                                    "col_pattern": P_B63_1_NAMES,
                                    "without_col_header": True,
                                    "enum_pattern": P_B63_1_ENUM_PATTERN,
                                },
                                # 2: 找表格，存在单元格`number of shares`且列名包含name, grantee, category等
                                {
                                    "name": "special_cells",
                                    "multi_elements": True,
                                    "multi": True,
                                    "include_footnotes": True,
                                    "neglect_title_patterns": P_B73_TITLE_NEG,
                                    "any_cell_pattern": R_ANY_CELL,
                                    "col_header_pattern": P_B63_1_TABLE_COL,
                                    "col_pattern": P_B63_1_NAMES,
                                    "without_col_header": True,
                                    "enum_pattern": P_B63_1_ENUM_PATTERN,
                                },
                                # 3: 高分表格，找出单元格有人名的列
                                {
                                    "name": "special_cells",
                                    "multi_elements": True,
                                    "multi": True,
                                    "threshold": 0.5,
                                    "include_footnotes": True,
                                    "title_patterns": R_AWARD_KEYWORD,
                                    "neglect_title_patterns": P_B73_TITLE_NEG,
                                    "any_cell_pattern": R_ANY_CELL,
                                    "col_pattern": P_B63_1_NAMES,
                                    "without_col_header": True,
                                    "enum_pattern": P_B63_1_ENUM_PATTERN,
                                },
                                # 4: 0.2 分以上 + grant date
                                {
                                    "name": "special_cells",
                                    "multi_elements": True,
                                    "multi": True,
                                    "threshold": 0.2,
                                    "include_footnotes": True,
                                    "title_patterns": R_AWARD_KEYWORD,
                                    "neglect_title_patterns": P_B73_TITLE_NEG,
                                    "any_cell_pattern": R_ANY_CELL,
                                    "col_header_pattern": [P_B63_1_TABLE_COL],
                                    "enum_pattern": P_B63_1_ENUM_PATTERN,
                                },
                            ],
                        },
                        # 模型3: 自adoption以来没有grant
                        *get_no_grant_models(P_B74_NO_UNVESTED),
                        # 模型4: PS句子
                        {
                            "name": "para_match",
                            "enum": AnswerValueEnum.PS.value,
                            "neglect_syllabus_regs": P_EQUITY_CHAPTER,
                            "paragraph_pattern": P_PS_B73_PAPA,
                        },
                        # 模型5: 分值过滤
                        {
                            "name": "score_filter",
                            "neglect_syllabus_regs": P_EQUITY_CHAPTER,
                            "threshold": 0.618,
                            "aim_types": ["PARAGRAPH"],
                        },
                    ]
                },
            }
        ],
    }
]
