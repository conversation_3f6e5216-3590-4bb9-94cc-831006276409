from remarkable.common.common_pattern import P_PERIOD_SEPARATOR, R_MIDDLE_DASHES
from remarkable.common.constants import AnswerV<PERSON>ueEnum
from remarkable.common.pattern import (
    MatchMulti,
    NeglectPattern,
    PositionPattern,
)
from remarkable.pdfinsight.parser import FOOTNOTE_START_PATTERN
from remarkable.predictor.hkex_predictor.models.yoda_layer.operator import Operator
from remarkable.predictor.hkex_predictor.models.yoda_layer.rule.by_pos import SentenceFromSplitPara
from remarkable.predictor.hkex_predictor.models.yoda_layer.rule.root import (
    ScoreDestFilter,
)
from remarkable.predictor.hkex_predictor.pattern import R_BE
from remarkable.predictor.hkex_predictor.schemas.list_rule_schema.common_models import P_NS_SKIP
from remarkable.predictor.hkex_predictor.schemas.pattern import (
    P_DURING_REPORT_YEAR,
    P_NO_OPTION_GRANTED,
    P_NS_DATE,
    P_OPTION_NO_OUTSTANDING,
    R_APPELLATION,
    R_AWARD_KEYWORD,
    R_MOVEMENT,
    R_OPTION_KEYWORD,
    R_REPORT_YEAR,
)

P_B63_TITLE_NEG = [
    NeglectPattern.compile(match=rf"{R_AWARD_KEYWORD}|meeting|\bmet\b|retir", unmatch=r"\boption"),
    # http://************:55647/#/project/remark/236700?treeId=38062&fileId=67072&schemaId=15&projectId=38062&schemaKey=B63.1 排除P53表格
    NeglectPattern.compile(match=r"outstanding\s*(award|rsu)", unmatch=r"\boptions"),
]
R_NUMBER_OF_OPTIONS = r"(number|no\.)\s*of\s*(share\s*)?options"

P_OPTION_MOVEMENT_TBL = [
    MatchMulti.compile(r"\boption", R_MOVEMENT, operator=all),
    MatchMulti.compile(r"outstanding|grant", r"\boptions", operator=all),
    R_NUMBER_OF_OPTIONS,
]

P_B63_1_TABLE_TITLE = [P_OPTION_MOVEMENT_TBL, r"(management|employee)s?\s*options"]
P_B63_1_TABLE_COL = MatchMulti.compile(
    # http://************:55647/#/project/remark/257728?projectId=17&treeId=21876&fileId=68760&schemaId=15
    NeglectPattern.compile(match=r"name", unmatch=r"scheme|plan|retir"),
    r"Grantee",
    r"awardee",
    r"Participant",
    # http://************:55647/#/project/remark/236280?treeId=38044&fileId=67002&schemaId=15&projectId=38044&schemaKey=B63.1
    r"categor(y|ies)",
    # http://************:55647/#/project/remark/257968?treeId=8163&fileId=68808&schemaId=15&projectId=17&schemaKey=B63.1
    r"director|employee|consultant|management|Consultant|highest",
    r"eligible|person",
    r"\brank|position",
    r"Holder|\bmember",
    operator=any,
)

# P_B63_1_TABLE_COL_NEGLECT = NeglectPattern.compile(
#     match=MatchMulti.compile(
#         # r"Name\s*of\s*grantees?",
#         # r"Name.*?participants|參與者姓名",
#         r"(?<!scheme )Name|姓名",
#         r"Grantees?",
#         # r"date\s*of\s*grant",
#         r"Participants?",
#         # r"Eligible person",`
#         operator=any,
#     ),
#     unmatch=MatchMulti.compile("percentage", operator=any),  # Cumulative percentage of
# )

R_PERSON_NAME = rf"([A-Z(（][{R_MIDDLE_DASHES}a-zA-Z.，,()（）]* *){{2,6}}"
P_B63_1_NAMES = MatchMulti.compile(
    P_B63_1_TABLE_COL,
    R_APPELLATION,
    # rf"(?i:{R_APPELLATION})",
    # director必须有具体人名
    MatchMulti.compile(
        r"(?i:director)", rf"{R_APPELLATION}|\t([A-Z][a-zA-Z.，,()（）-]* +){{2,6}}(\t|$)", operator=any, flag=0
    ),
    # 至少有3个人名
    rf"^{R_PERSON_NAME}(\t+{R_PERSON_NAME}){{2}}",
    # 除列头和total外都是人名
    rf"^{R_PERSON_NAME}(\t+{R_PERSON_NAME})?(\s+[Tt]otal)?$",
    operator=any,
    flag=0,
)
P_B63_1_ENUM_PATTERN = [
    FOOTNOTE_START_PATTERN,  # 保留脚注
    NeglectPattern.compile(match=r".+", unmatch=r"total|average|price|\b(during|end(ed)?)\b"),
]

P_NO_GRANT_SKIP = MatchMulti.compile(rf"\d+\s*options\s*(have\s*|ha[sd]\s*)?{R_BE}\s*granted", operator=any)


def no_grant_during_the_year():
    return ScoreDestFilter(
        skip_pattern=P_NO_GRANT_SKIP,
        dest=SentenceFromSplitPara(
            ordered_patterns=[
                MatchMulti.compile(P_NO_OPTION_GRANTED, P_DURING_REPORT_YEAR, operator=all),
                # http://************:55647/#/project/remark/234234?treeId=9073&fileId=66661&schemaId=15&projectId=17&schemaKey=B64
                PositionPattern.compile(r"\ball\s*of\s*(the\s*)?(share\s*)?options", r"expired|terminat"),
            ],
            # http://************:55647/#/project/remark/233772?treeId=20051&fileId=66584&schemaId=15&projectId=17&schemaKey=B64 index=656
            # https://jura-uat2.paodingai.com/#/hkex/annual-report-checking/report-review/243354?fileId=80117&schemaId=5&rule=B64&delist=0 index=699
            skip_pattern=MatchMulti.compile(
                P_NS_SKIP,
                r"schemes?\s*(ha[sd]|have)\s*been\s*exercised",
                r"among\s*these\s*[\d,，]+\s*options",
                operator=any,
            ),
        ),
    )


NO_GRANT_MODELS = [
    # 模型1：截至x年x月x日/自上市以来一直没有grant过
    {
        "name": "yoda_layer",
        "threshold": 0,
        "model_id": "ns_since_adoption",
        "multi_elements": True,
        "enum_every_element": True,
        "enum": AnswerValueEnum.NS.value,
        "rule": ScoreDestFilter(
            multi=True,
            skip_pattern=P_NO_GRANT_SKIP,
            dest=SentenceFromSplitPara(
                multi=True,
                separator=P_PERIOD_SEPARATOR,
                ordered_patterns=[
                    MatchMulti.compile(
                        P_NO_OPTION_GRANTED,
                        MatchMulti.compile(
                            r"(?<!years )\b(since|after|from|upon|as\s*(at|of)|up\s*to|end(ed)?)\s.*\b20\d{2}\b",
                            P_NS_DATE,
                            *R_REPORT_YEAR,
                            operator=any,
                        ),
                        operator=all,
                    ),
                ],
                skip_pattern=P_NS_SKIP,
            ),
        ),
    },
    # 模型2：组合条件①在当年没有granted ②outstanding期初期末都为0或者启用日就在最近一期
    {
        "name": "yoda_layer",
        "threshold": 0,
        "multi_elements": True,
        "enum_from_multi_element": True,
        "enum": AnswerValueEnum.NS.value,
        "rule": Operator.all(
            no_grant_during_the_year(),
            ScoreDestFilter(
                dest=SentenceFromSplitPara(
                    ordered_patterns=[P_OPTION_NO_OUTSTANDING],
                ),
            ),
        ),
    },
]


predictor_options = [
    {
        "path": ["B63.1"],
        "fake_leaf": True,
        "models": [
            {
                "name": "group_based",
                "share_type": "option",
                "group_default_enum": AnswerValueEnum.ND.value,
                "column_configs": {
                    "Name of grantee(Description of grantees)": [
                        # 模型1: NS场景-no equity或没有设立share option
                        {"name": "no_share", "with_element_box": True},
                        # 模型2: 找表格中的人名列
                        {
                            "name": "multi_models",
                            "operator": "union",
                            "enum": AnswerValueEnum.PS.value,
                            "require_unique_element": True,
                            "models": [
                                # 1: 找表格，表名为movement且列名包含name, grantee, category等
                                {
                                    "name": "special_cells",
                                    "multi_elements": True,
                                    "include_footnotes": True,
                                    "neglect_title_patterns": P_B63_TITLE_NEG,
                                    "title_patterns": P_B63_1_TABLE_TITLE,
                                    "col_header_pattern": P_B63_1_TABLE_COL,
                                    "col_pattern": P_B63_1_NAMES,
                                    "without_col_header": True,
                                    "enum_pattern": P_B63_1_ENUM_PATTERN,
                                },
                                # 2: 找表格，存在单元格`number of options`且列名包含name, grantee, category等
                                {
                                    "name": "special_cells",
                                    "multi_elements": True,
                                    "include_footnotes": True,
                                    "neglect_title_patterns": P_B63_TITLE_NEG,
                                    "any_cell_pattern": R_NUMBER_OF_OPTIONS,
                                    "col_header_pattern": P_B63_1_TABLE_COL,
                                    "col_pattern": P_B63_1_NAMES,
                                    "without_col_header": True,
                                    "enum_pattern": P_B63_1_ENUM_PATTERN,
                                },
                                # 3: 高分表格，找出单元格有人名的列
                                {
                                    "name": "special_cells",
                                    "multi_elements": True,
                                    "threshold": 0.5,
                                    "include_footnotes": True,
                                    "title_patterns": R_OPTION_KEYWORD,
                                    "neglect_title_patterns": P_B63_TITLE_NEG,
                                    "col_pattern": P_B63_1_NAMES,
                                    "without_col_header": True,
                                    "enum_pattern": P_B63_1_ENUM_PATTERN,
                                },
                                # 4: 0.2 分以上 + grant date
                                # http://************:55647/#/project/remark/?treeId=2779&fileId=68795&schemaId=15&projectId=17&schemaKey=B63.1
                                {
                                    "name": "special_cells",
                                    "multi_elements": True,
                                    "multi": True,
                                    "threshold": 0.2,
                                    "include_footnotes": True,
                                    "title_patterns": R_OPTION_KEYWORD,
                                    "neglect_title_patterns": P_B63_TITLE_NEG,
                                    "any_cell_pattern": [r"grant\s*date", r"date\s*of\s*grant"],
                                    "col_header_pattern": [P_B63_1_TABLE_COL, r"(^|\t)position(\t|$)"],
                                    "enum_pattern": P_B63_1_ENUM_PATTERN,
                                },
                            ],
                        },
                        # 模型3: NS场景-没有grant过或当年没有grant过且期初期末balance为0
                        *NO_GRANT_MODELS,
                        # # http://************:55647/#/project/remark/250733?treeId=11548&fileId=68578&schemaId=15&projectId=17
                        # {
                        #     "name": "b63_class_1",
                        #     "col_header_pattern": P_TABLE_COL_HEADER_PATTERN,
                        #     "table_title_pattern": P_SHARE_OPTION_TABLE_TITLE,
                        #     "table_col_pattern": P_B63_1_TABLE_COL.union("employee"),
                        # },
                        # {
                        #     "name": "b63_class_1",
                        #     "table_title_pattern": P_SHARE_OPTION_TABLE_TITLE,
                        #     "table_col_pattern": P_B63_1_TABLE_COL_NEGLECT,
                        # },
                        {
                            "name": "score_filter",
                            "threshold": 0.5,
                            "aim_types": ["PARAGRAPH"],
                        },
                    ]
                },
            }
        ],
    }
]
