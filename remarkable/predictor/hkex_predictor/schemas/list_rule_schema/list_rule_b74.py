from remarkable.common.common_pattern import P_SEN_SEPARATOR, R_MIDDLE_DASH
from remarkable.common.constants import Answer<PERSON><PERSON>ue<PERSON>num, PDFInsightClassEnum
from remarkable.common.pattern import (
    MatchMulti,
    NeglectPattern,
    PositionPattern,
    SplitBeforeMatch,
)
from remarkable.predictor.common_pattern import R_DATES, R_EN_MONTH, R_EN_NUM
from remarkable.predictor.hkex_predictor.models.yoda_layer.rule.by_pos import Sen<PERSON>ce<PERSON><PERSON>SplitPara
from remarkable.predictor.hkex_predictor.models.yoda_layer.rule.root import ScoreDestFilter
from remarkable.predictor.hkex_predictor.pattern import (
    P_EQUITY_CHAPTER,
    R_BE,
    R_DAY,
)
from remarkable.predictor.hkex_predictor.schemas.list_rule_schema.common_models import get_no_grant_models
from remarkable.predictor.hkex_predictor.schemas.pattern import (
    R_AWARD_KEYWORD,
    R_DURING_THE_YEAR,
    R_<PERSON><PERSON>_TIMES,
    R_<PERSON>OVEMENT,
    R_<PERSON><PERSON>,
    R_NO_AWARD_NS,
    R_<PERSON>UMBE<PERSON>,
    R_<PERSON><PERSON><PERSON><PERSON>_KEYWORD,
    R_PRICE,
)
from remarkable.predictor.models.special_cells import LastMode

R_B74_NS = [
    *R_NO_AWARD_NS,
    # http://************:55647/#/project/remark/233131?treeId=3182&fileId=66477&schemaId=15&projectId=17&schemaKey=B76
    r"\bmandate\s*has\s*not\s*been\s*used",
]
R_TABLE_TITLES = MatchMulti.compile(
    rf"{R_AWARD_KEYWORD}|grant",
    rf"{R_MOVEMENT}|purchased|first\s*grant|number\s*of",
    operator=all,
)

R_CELL_START = r"(^|[\t\n])\s*"
R_CELL_END = r"(\s*$| *[\t\n]|\s*[\u4e00-\u9fa5])"
R_NUM_KEYWORDS = r"(award|grant|(un|not\s*yet)?vest|call|laps|cancell|exercis|(un)?lock|attribut|forfeit)ed"
R_GRANTED = [
    rf"{R_CELL_START}(award\s*)?shares\s*{R_NUM_KEYWORDS}",
    rf"{R_CELL_START}(number|no\.)\s*of\s.*{R_NUM_KEYWORDS}\s*(du?ring|\bin\b|\bafter)",
    rf"{R_CELL_START}{R_NUM_KEYWORDS}\s*(du?ring|\bin\b|\bafter)",
    rf"{R_CELL_START}{R_NUM_KEYWORDS}{R_CELL_END}",
    rf"{R_CELL_START}share\s*dividend{R_CELL_END}",
    r"lapsed\s*(and|or|/)\s*cancelled",
    rf"{R_CELL_START}{R_NUM_KEYWORDS}({R_CELL_END}|during)",
]
R_CELL_KEYWORDS = [
    rf"{R_CELL_START}(number|no\.)\s*of\s*([a-z]+\s*)?(shares\s*(granted|under\b)|(award(ed)?|restricted)\s*(A\s*)?shares|shares?\s*award(ed)?|RSU|awards|restricted\s*(stock|share)\s*unite?s)",
    # http://************:55647/#/project/remark/251242?treeId=8654&fileId=68663&schemaId=15&projectId=17&schemaKey=B74
    rf"{R_CELL_START}\d{{4}}\s*awarded\s*shares",
    rf"{R_CELL_START}total\s*(number|no\.)\s*of\s*shares?\s*award(ed)?{R_CELL_END}",
    *R_GRANTED,
]
R_CELL_NUMBER = rf"{R_CELL_START}({R_NIL}|[-(（]?{R_NUMBER}[)）]?)"
R_AT_DAY = [
    rf"{R_CELL_START}(as\s*)?(at\s*|of\s*){R_DAY}(?!\s*[,，])",
    rf"{R_CELL_START}(as\s*)?at\s*(\d{{1,2}}\s*{R_EN_MONTH}|{R_EN_MONTH}\s*\d{{1,2}}){R_CELL_END}",
]

R_BEGIN_END_COLS = [
    r"((un|not\s*yet\s*)vested\s*|award(ed|s)?\s*|shares?\s*|[RSU ]{3,}\s*|units?\s*){1,3}as\s*(at|of)",
    rf"((un|not\s*yet\s*)vested\s*|award(ed|s)?\s*|shares?\s*|[RSU ]{{3,}}\s*|units?\s*){{1,3}}at\s*{R_DAY}",
    r"(outstanding|balance|held)\s*as\s*(at|of)",
    rf"(outstanding|balance|held)\s*a[ts]\s*{R_DAY}(?!\s*[,，])",
    # http://************:55647/#/project/remark/234095?treeId=8278&fileId=66638&schemaId=15&projectId=17&schemaKey=B74
    rf"Unvested\s*RS\s*U\s*s\s*a\s*s\s*a\s*t\s*{R_DAY}",
    *R_AT_DAY,
]
R_BEGIN_COLS = [
    # r"((un|not\s*yet\s*)vested\s*|award(ed|s)?\s*|shares?\s*){1,3}at\s*(the\s*)?beginning",
    # r"(outstanding|balance|held)\s*at\s*(the\s*)?beginning",
    # r"(^\s*|[\t\n]\s*)(as\s*)?at\s*(the\s*)?beginning",
    *R_BEGIN_END_COLS,
    r"at\s*(the\s*)?beginning",
]
R_END_COLS = [
    # r"((un|not\s*yet\s*)vested\s*|award(ed|s)?\s*|shares?\s*){1,3}at\s*(the\s*)?end",
    # r"(outstanding|balance|held)\s*at\s*(the\s*)?end\b",
    # r"(^\s*|[\t\n]\s*)(as\s*)?at\s*(the\s*)?end\b",
    *R_BEGIN_END_COLS,
    r"at\s*(the\s*)?end\b",
]
R_NUMBER_COLS = [
    # R_CELL_KEYWORDS,
    *R_GRANTED,
    *R_BEGIN_COLS,
    *R_END_COLS,
    R_DURING_THE_YEAR,
]
R_DATE_OF_GRANT_COLS = [
    r"(?<!before\s)(?<!after\s)(?<!before)(?<!after)date\s*of\s*(grant|award)",
    rf"{R_CELL_START}(grant|award)\s*date",
]
R_VESTING_COLS = [r"\b(vest(ing)?|attribution|lock-up)\s*(start\s*)?(period|dates?|schedule|criteria)"]
R_VESTING_CELL = [
    *R_DATES,
    rf"\b(\d+|{R_EN_NUM})\s*(year|month|day)s?",
    # http://http://************:55647/#/project/remark/250769?treeId=3710&fileId=68584&schemaId=15&projectId=17&schemaKey=B74
    r"\b(from|before|after|date)\b",
]
R_NO_VESTING = [r"\bno\s*vesting\s*period"]
P_EXERCISE_COLS = NeglectPattern.compile(match=r"exercise\s*(and\s*vesting\s*)?period", unmatch="weight|average")
R_PURCHASE_PRICE_COLS = [r"(?<!average )\b(purchase|subscription)\s*price(?!\s*for)"]
R_OTHER_PRICE_COLS = [r"(?<!average )\b(exercise|grant|issue|award)\s*price(?!\s*for)", r"grant\s*fee"]
P_PURCHASE_PRICE = MatchMulti.compile(
    r"(?<!average )\b(purchase|subscription)\s*price", *R_OTHER_PRICE_COLS, operator=any
)
R_COL_NUMBER_OF = rf"{R_CELL_START}(\d{{4}}\s*)?(number|no\.)\s*of\s*(restricted|shares(?!\s*(granted|awared))|awards|rsu)(?!.*?issue)"

R_NOT_REQ_TO_PAY = r"\bnot\s*required\s*to\s*(pay\b|be\s*paid|make\s*any\s*pay|provide\s*any\s*consideration)"
P_NO_CONSIDER = MatchMulti.compile(
    r"\b(no|nil|without)\s*(cash\s*|additional\s*)?consideration",
    r"\bnot\s*applicable",
    r"\b(no|nil)\s*purchase\s*price",
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/4714#note_530185
    r"\bno\s*cost\s*upon\s*vesting",
    R_NOT_REQ_TO_PAY,
    operator=any,
)
P_NEG_TBL_TITLES = [NeglectPattern.compile(match=R_OPTION_KEYWORD, unmatch=R_AWARD_KEYWORD)]
# http://************:55647/#/project/remark/258003?treeId=3607&fileId=68815&schemaId=15&projectId=17&schemaKey=B74
R_TITLE_GRANTS = [R_AWARD_KEYWORD, r"grants\s*(were|are)\s*made"]

P_NEG_OPTION = MatchMulti.compile(R_OPTION_KEYWORD, operator=any)
P_B74_NO_UNVESTED = MatchMulti.compile(
    MatchMulti.compile(r"\bno\s*unvested", R_AWARD_KEYWORD, operator=all),
    PositionPattern.compile(r"\ball\s*of\s*", r"(ha[sd]|have)\s*been\s*vested"),
    operator=any,
)


# 规则B74~B78的`Purchase price`提取规则相同
def purchase_price_models():
    return [
        # 模型2：根据单元格关键词+列名匹配表格
        {
            "name": "special_cells",
            "model_id": "table_1",
            "skip_answer_value": AnswerValueEnum.ND.value,
            "include_footnotes": True,
            "footnote_pattern": P_PURCHASE_PRICE,
            "title_patterns": R_TITLE_GRANTS,
            "neglect_title_patterns": R_OPTION_KEYWORD,
            "multi": True,
            "col_header_pattern": R_PURCHASE_PRICE_COLS,
            "row_header_pattern": R_PURCHASE_PRICE_COLS,
            "row_col_relation": "or",
            "any_cell_pattern": R_CELL_KEYWORDS,
        },
        # 模型3：匹配表格的列
        {
            "name": "special_cells",
            "model_id": "table_2",
            "skip_answer_value": AnswerValueEnum.ND.value,
            "include_footnotes": True,
            "footnote_pattern": P_PURCHASE_PRICE,
            "title_patterns": [
                R_TABLE_TITLES,
                MatchMulti.compile(R_AWARD_KEYWORD, r"vesting\s*schedules", operator=all),
            ],
            "neglect_title_patterns": R_OPTION_KEYWORD,
            # "row_pattern": NeglectPattern.compile(match=r".+", unmatch=r"\toptions?"),
            "multi": True,
            "col_header_pattern": R_PURCHASE_PRICE_COLS,
        },
        # 模型4：找不到purchase price，找exercise price，根据单元格关键词+列名匹配表格
        {
            "name": "special_cells",
            "model_id": "table_3",
            "skip_answer_value": AnswerValueEnum.ND.value,
            "include_footnotes": True,
            "footnote_pattern": P_PURCHASE_PRICE,
            "title_patterns": R_TITLE_GRANTS,
            "neglect_title_patterns": R_OPTION_KEYWORD,
            "multi": True,
            "col_header_pattern": R_OTHER_PRICE_COLS,
            "any_cell_pattern": R_CELL_KEYWORDS,
        },
        # 模型5：找不到purchase price，找exercise price，匹配表格的列
        {
            "name": "special_cells",
            "model_id": "table_4",
            "skip_answer_value": AnswerValueEnum.ND.value,
            "include_footnotes": True,
            "footnote_pattern": P_PURCHASE_PRICE,
            "title_patterns": [
                R_TABLE_TITLES,
                MatchMulti.compile(R_AWARD_KEYWORD, r"vesting\s*schedules", operator=all),
            ],
            "neglect_title_patterns": R_OPTION_KEYWORD,
            "multi": True,
            "col_header_pattern": R_OTHER_PRICE_COLS,
            # "without_col_header": True,
        },
        # 模型6：匹配表格的行
        {
            "name": "special_cells",
            "model_id": "table_5",
            "skip_answer_value": AnswerValueEnum.ND.value,
            "title_patterns": R_TABLE_TITLES,
            "neglect_title_patterns": R_OPTION_KEYWORD,
            "multi": True,
            "row_header_pattern": P_PURCHASE_PRICE,
        },
        # 模型7：匹配PS句子
        # http://************:55647/#/project/remark/239670?treeId=20582&fileId=67568&schemaId=15&projectId=17&schemaKey=B76&page=91
        {
            "name": "yoda_layer",
            "threshold": 0,
            "enum": AnswerValueEnum.PS.value,
            "rule": ScoreDestFilter(
                multi=True,
                dest=SentenceFromSplitPara(
                    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/4569#note_521609
                    ordered_patterns=[
                        MatchMulti.compile(
                            P_PURCHASE_PRICE,
                            rf"\b(is|was|be|of)\s*{R_PRICE}|\bset\s*out\s*in\s*the\s*offer",
                            operator=all,
                        )
                    ],
                    multi=True,
                ),
                skip_pattern=P_NEG_OPTION,
            ),
        },
        # 模型8：匹配关键词no consideration
        # http://************:55647/#/project/remark/233191?treeId=10935&fileId=66487&schemaId=15&projectId=17&schemaKey=B76
        {
            "name": "yoda_layer",
            "threshold": 0,
            "multi_elements": True,
            "enum_from_multi_element": True,
            "enum": AnswerValueEnum.NS.value,
            "rule": ScoreDestFilter(
                multi=True,
                dest=SentenceFromSplitPara(
                    ordered_patterns=[
                        P_NO_CONSIDER,
                        MatchMulti.compile(
                            P_PURCHASE_PRICE, r"\b(is|was|be)\s*((RMB|HK\$?[DS]?\$?|US\$?|\$)?0|nil)", operator=all
                        ),
                    ],
                    multi=True,
                ),
                skip_pattern=P_NEG_OPTION,
            ),
        },
        # 模型9: 分值过滤
        {
            "name": "score_filter",
            "aim_types": [PDFInsightClassEnum.PARAGRAPH.value],
            "model_id": "para_1",
            "threshold": 0.618,
            "pattern": [*R_PURCHASE_PRICE_COLS, *R_OTHER_PRICE_COLS],
            "neglect_pattern": [R_OPTION_KEYWORD],
        },
    ]


def date_of_grant_models():
    return [
        # 模型2：特例，指定章节的表格全都要
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/4761#note_531475
        {
            "name": "special_cells",
            "multi_elements": True,
            "multi": True,
            "syllabus_regs": [rf"^(equity|cash){R_MIDDLE_DASH}settled\s*restricted\s*stock\s*units$"],
            "skip_answer_value": AnswerValueEnum.ND.value,
            "include_footnotes": True,
            "title_patterns": R_TITLE_GRANTS,
            "neglect_title_patterns": P_NEG_TBL_TITLES,
            "row_header_pattern": R_DATE_OF_GRANT_COLS,
            "col_header_pattern": R_DATE_OF_GRANT_COLS,
            "row_col_relation": "or",
            "cell_pattern": R_DATES,
            "any_cell_pattern": R_CELL_KEYWORDS,
        },
        *common_date_of_grant_models(),
    ]


def common_date_of_grant_models():
    return [
        # 模型3：根据单元格关键词+列名匹配表格
        {
            "name": "special_cells",
            "skip_answer_value": AnswerValueEnum.ND.value,
            "include_footnotes": True,
            "title_patterns": R_TITLE_GRANTS,
            "neglect_title_patterns": P_NEG_TBL_TITLES,
            "row_header_pattern": R_DATE_OF_GRANT_COLS,
            "col_header_pattern": R_DATE_OF_GRANT_COLS,
            "row_col_relation": "or",
            "cell_pattern": R_DATES,
            "any_cell_pattern": R_CELL_KEYWORDS,
        },
        # 模型4：匹配表格的列
        {
            "name": "special_cells",
            "skip_answer_value": AnswerValueEnum.ND.value,
            "include_footnotes": True,
            "title_patterns": R_TABLE_TITLES,
            "neglect_title_patterns": P_NEG_TBL_TITLES,
            "col_header_pattern": R_DATE_OF_GRANT_COLS,
            "row_header_pattern": R_DATE_OF_GRANT_COLS,
            "row_col_relation": "or",
            "cell_pattern": R_DATES,
        },
        # 模型5：分值高的表格尝试匹配
        {
            "name": "special_cells",
            "multi_elements": True,
            "neglect_title_patterns": P_NEG_TBL_TITLES,
            "skip_answer_value": AnswerValueEnum.ND.value,
            "threshold": 0.8,
            "include_footnotes": True,
            "col_header_pattern": R_DATE_OF_GRANT_COLS,
        },
        # 模型6：一些句子
        {
            "name": "para_match",
            "multi_elements": True,
            "paragraph_pattern": [
                rf"^In\s*(?P<content>{R_DAY})[,，]\s*the\s*Company\s*ha[sd]\s*appointed[^.]*?as\s*(a\s*)trustee",
                rf"^on\s*(?P<content>{R_DAY}\s*(and\s*{R_DAY}\s*)?)[,，]\s*the\s*Company\s*(ha[sd]\s*)?granted.*{R_AWARD_KEYWORD}",
                rf"\bon\s*(?P<content>{R_DAY})\s*[(（]the\s*[\"“]grant\s*date\s*[iv]+[\"”][)）]",
                rf"\bon\s*(?P<content>{R_DAY})[,，]\s*[\d,，]+\s*\s*(share\s*awards|shares)[^.]*?\s*(have\s*)?{R_BE}\s*granted",
                rf"\bAll\s*the\s*RSUs\s*[^.]*{R_BE}\s*granted\s*on\s*(?P<content>{R_DAY})",
                rf"\bAll\s*the\s*RSUs\s*[^.]*{R_BE}\s*granted\s*on\s*(?P<content>{R_DAY})",
                # http://http://************:55647/#/project/remark/264603?treeId=37545&fileId=70135&schemaId=15&projectId=17&schemaKey=B74
                rf"^.*grant\s*[\d,]+\s*(restricted|award)?\s*shares\s*[^.]+?\son\s*(?P<content>{R_DAY})",
            ],
        },
        # 模型7: 分值过滤
        {"name": "score_filter", "threshold": 0.618, "pattern": R_DATE_OF_GRANT_COLS},
    ]


def vesting_period_models():
    return [
        # 模型2：根据单元格关键词+列名匹配表格
        {
            "name": "special_cells",
            "skip_answer_value": AnswerValueEnum.ND.value,
            "enum_from_multi_element": True,
            "include_footnotes": True,
            "title_patterns": R_TITLE_GRANTS,
            "neglect_title_patterns": P_NEG_TBL_TITLES,
            "row_header_pattern": R_VESTING_COLS,
            "col_header_pattern": R_VESTING_COLS,
            "row_col_relation": "or",
            "cell_pattern": [R_VESTING_CELL, R_NIL],
            "any_cell_pattern": R_CELL_KEYWORDS,
            "allow_only_footnotes": True,
            "footnote_pattern": [*R_VESTING_COLS, r"\b(vested|vesting|vest\s*on)\b"],
        },
        # 模型3：匹配表格的列
        {
            "name": "special_cells",
            "skip_answer_value": AnswerValueEnum.ND.value,
            "include_footnotes": True,
            "title_patterns": [
                R_TABLE_TITLES,
                MatchMulti.compile(R_AWARD_KEYWORD, r"vesting\s*schedules", operator=all),
            ],
            "neglect_title_patterns": P_NEG_TBL_TITLES,
            "col_header_pattern": R_VESTING_COLS,
            "row_header_pattern": R_VESTING_COLS,
            "row_col_relation": "or",
            "cell_pattern": [R_VESTING_CELL, R_NIL],
        },
        # 模型4：分值高的表格尝试匹配
        {
            "name": "special_cells",
            "multi_elements": True,
            "skip_answer_value": AnswerValueEnum.ND.value,
            "threshold": 0.8,
            "include_footnotes": True,
            "col_header_pattern": R_VESTING_COLS,
            "row_header_pattern": R_VESTING_COLS,
            "row_col_relation": "or",
            "cell_pattern": [R_VESTING_CELL, R_NIL],
            "allow_only_footnotes": True,
            "footnote_pattern": [*R_VESTING_COLS, r"vested|vesting|\bvest\s*on\b"],
        },
        # 模型5： 针对特例文件66433
        # http://************:55647/#/project/remark/232869?treeId=5113&fileId=66433&schemaId=15&projectId=17&schemaKey=B74
        # http://************:55647/#/project/remark/264603?treeId=37545&fileId=70135&schemaId=15&projectId=17&schemaKey=B74
        {
            "name": "special_cells",
            "multi": True,
            "include_footnotes": True,
            # "title_patterns": [r"notes?[:：]"],
            "neglect_title_patterns": P_NEG_TBL_TITLES,
            "any_cell_pattern": R_VESTING_COLS,
            "col_header_pattern": [*R_VESTING_COLS, r"percentage\s*of\b"],
        },
        # 模型6: 匹配段落
        {
            "name": "para_match",
            "multi_elements": True,
            # http://************:55647/#/project/remark/251195?treeId=37638&fileId=68655&schemaId=15&projectId=17&schemaKey=B74
            "as_follow_pattern": MatchMulti.compile(r"\bvested|vesting", r"[:：-]\s*$", operator=all),
            "paragraph_pattern": [
                *R_NO_VESTING,
                rf"awarded\s*shares\s*(shall\s*)?vest(ed)?\s*(in\s*them\s*)?on\s*{R_DAY}",
                SplitBeforeMatch(
                    MatchMulti.compile(
                        R_AWARD_KEYWORD,
                        r"vested|vesting|\bvest\s*on\b",
                        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/4714#note_529697
                        # http://************:55647/#/project/remark/257968?treeId=8163&fileId=68808&schemaId=15&projectId=17&schemaKey=B74
                        rf"(within|after)\s*{R_ENG_TIMES}\s*(month|year)s?|\bon\s*(the\s*date|{R_DAY}|each\s*anniversary\s*of\s*the\s*date)",
                        operator=all,
                    ),
                    separator=P_SEN_SEPARATOR,
                    operator=any,
                ),
            ],
        },
        # 模型7: 分值过滤
        {"name": "score_filter", "threshold": 0.618, "pattern": R_VESTING_COLS},
    ]


def exercise_period_models():
    return [
        # 模型2：根据单元格关键词+列名匹配表格
        {
            "name": "special_cells",
            "include_footnotes": True,
            "title_patterns": R_TITLE_GRANTS,
            "neglect_title_patterns": P_NEG_TBL_TITLES,
            "col_header_pattern": P_EXERCISE_COLS,
            "row_header_pattern": P_EXERCISE_COLS,
            "row_col_relation": "or",
            "any_cell_pattern": R_CELL_KEYWORDS,
        },
        # 模型3：匹配表格的列
        {
            "name": "special_cells",
            "include_footnotes": True,
            "title_patterns": R_TABLE_TITLES,
            "neglect_title_patterns": P_NEG_TBL_TITLES,
            "col_header_pattern": P_EXERCISE_COLS,
            "row_header_pattern": P_EXERCISE_COLS,
            "row_col_relation": "or",
        },
        # 模型4：分值高的表格尝试匹配
        {
            "name": "special_cells",
            "threshold": 0.8,
            "include_footnotes": True,
            "col_header_pattern": P_EXERCISE_COLS,
            "row_header_pattern": P_EXERCISE_COLS,
            "row_col_relation": "or",
        },
        # 模型5：匹配关键词exercise period
        {
            "name": "para_match",
            "threshold": 0.3,
            "paragraph_pattern": MatchMulti.compile(
                R_AWARD_KEYWORD, r"exercis(e|able)\s*period|exercised[^.]*?vest", operator=all
            ),
        },
        # 模型6：匹配关键词
        {
            "name": "para_match",
            # "threshold": 0.3,
            "multi_elements": False,
            "paragraph_pattern": [
                r"No\s*purchase\s*price\s*is\s*involved\s*as\s*there\s*is\s*no\s*mechanism\s*for\s*exercise\s*of\s*RSUs.",
            ],
        },
        # 模型7: 分值过滤
        {"name": "score_filter", "threshold": 0.618, "pattern": P_EXERCISE_COLS},
        # 模型8: kmeans
        {
            "name": "kmeans_classification",
            "filter_low_score_threshold": 0.1,
            "neglect_syllabus_regs": P_EQUITY_CHAPTER,
            "para_pattern": P_EXERCISE_COLS,
        },
    ]


BEGINNING_AMOUNT_MODELS = [
    # 模型1：通用NS场景
    {"name": "no_share", "with_element_box": True, "model_id": "ns_1"},
    # 模型2：特例，限制章节
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/4761#note_531475
    {
        "name": "special_cells",
        "multi_elements": True,
        "syllabus_regs": [rf"^(equity|cash){R_MIDDLE_DASH}settled\s*restricted\s*stock\s*units$"],
        "title_patterns": R_TABLE_TITLES,
        "neglect_title_patterns": P_NEG_TBL_TITLES,
        "col_header_pattern": R_BEGIN_COLS,
        "any_cell_pattern": R_CELL_KEYWORDS,
        "cell_pattern": R_CELL_NUMBER,
    },
    # 模型3：匹配表格的列
    {
        "name": "special_cells",
        "title_patterns": R_TABLE_TITLES,
        "neglect_title_patterns": P_NEG_TBL_TITLES,
        "col_header_pattern": R_BEGIN_COLS,
        "any_cell_pattern": R_CELL_KEYWORDS,
        "cell_pattern": R_CELL_NUMBER,
    },
    # 模型4：分值高的表格尝试匹配
    {
        "name": "special_cells",
        "threshold": 0.8,
        "col_header_pattern": R_BEGIN_COLS,
        "row_header_pattern": R_BEGIN_COLS,
        "row_col_relation": "or",
    },
    # 模型5：表格中的单元格：number of restricted
    # http://************:55647/#/project/remark/233215?treeId=37654&fileId=66491&schemaId=15&projectId=17&schemaKey=B74
    {
        "name": "special_cells",
        # "title_patterns": R_TABLE_TITLES,
        "neglect_title_patterns": P_NEG_TBL_TITLES,
        "row_header_pattern": [
            *R_AT_DAY,
            rf"{R_CELL_START}(as\s*)?at\s*(the\s*)?beginning",
        ],
        "col_header_pattern": R_COL_NUMBER_OF,
        "any_cell_pattern": R_CELL_KEYWORDS,
        "cell_pattern": R_CELL_NUMBER,
    },
    # 模型6：根据行名匹配表格 文件66590
    {
        "name": "special_cells",
        "title_patterns": R_TABLE_TITLES,
        "neglect_title_patterns": P_NEG_TBL_TITLES,
        "row_header_pattern": R_BEGIN_COLS,
        "any_cell_pattern": R_CELL_KEYWORDS,
    },
    # 模型7~9：B74~B78通用：no award was granted + outstanding期初期末都为0
    *get_no_grant_models(r"unvested", P_B74_NO_UNVESTED),
    # 模型10: 分值过滤
    {
        "name": "score_filter",
        "threshold": 0.618,
    },
]


# https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/4147#note_543802
FOLLOW_FIRST_NS = {
    "name": "reference",
    "from_answer_value": AnswerValueEnum.NS.value,
    "from_path": ["B74", "Beginning amount"],
    "model_ids": [model["model_id"] for model in BEGINNING_AMOUNT_MODELS if "model_id" in model],
}


predictor_options = [
    {
        "path": ["B74"],
        "fake_leaf": True,
        "models": [
            {
                "name": "group_based",
                "share_type": "award",
                "column_configs": {
                    # 子项1
                    "Beginning amount": BEGINNING_AMOUNT_MODELS,
                    # 子项2
                    "Ending amount": [
                        # 模型1：通用NS场景
                        FOLLOW_FIRST_NS,
                        # 模型2：特例，限制章节
                        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/4761#note_531475
                        {
                            "name": "special_cells",
                            "multi_elements": True,
                            "syllabus_regs": [rf"(equity|cash){R_MIDDLE_DASH}settled"],
                            "multi": True,
                            "last_mode": {LastMode.LAST_COL},
                            "title_patterns": R_TABLE_TITLES,
                            "neglect_title_patterns": P_NEG_TBL_TITLES,
                            "col_header_pattern": R_END_COLS,
                            "any_cell_pattern": R_CELL_KEYWORDS,
                            "cell_pattern": R_CELL_NUMBER,
                        },
                        # 模型3：匹配表格的列
                        {
                            "name": "special_cells",
                            "multi": True,
                            "last_mode": {LastMode.LAST_COL},
                            "title_patterns": R_TABLE_TITLES,
                            "neglect_title_patterns": P_NEG_TBL_TITLES,
                            "col_header_pattern": R_END_COLS,
                            "any_cell_pattern": R_CELL_KEYWORDS,
                            "cell_pattern": R_CELL_NUMBER,
                        },
                        # 模型4：分值高的表格尝试匹配
                        {
                            "name": "special_cells",
                            "multi": True,
                            "last_mode": {LastMode.LAST_COL, LastMode.LAST_ROW},
                            "threshold": 0.8,
                            "col_header_pattern": R_END_COLS,
                            "row_header_pattern": R_END_COLS,
                            "row_col_relation": "or",
                        },
                        # 模型5：表格中的单元格：number of restricted
                        {
                            "name": "special_cells",
                            "multi": True,
                            # "title_patterns": R_TABLE_TITLES,
                            "neglect_title_patterns": P_NEG_TBL_TITLES,
                            "row_header_pattern": [
                                *R_AT_DAY,
                                rf"{R_CELL_START}at\s*(the\s*)?end",
                            ],
                            "col_header_pattern": R_COL_NUMBER_OF,
                            "last_mode": {LastMode.LAST_ROW, LastMode.FIRST_COL},
                            "any_cell_pattern": R_CELL_KEYWORDS,
                            "cell_pattern": R_CELL_NUMBER,
                        },
                        # 模型6：根据行名匹配表格
                        {
                            "name": "special_cells",
                            "multi": True,
                            "last_mode": {LastMode.LAST_ROW},
                            "title_patterns": R_TABLE_TITLES,
                            "neglect_title_patterns": P_NEG_TBL_TITLES,
                            "row_header_pattern": R_END_COLS,
                            "any_cell_pattern": R_CELL_KEYWORDS,
                        },
                        # # 模型7~8：B74~B78通用：no award was granted + outstanding期初期末都为0
                        # *get_no_grant_models(P_NS),
                        # 模型9: 分值过滤
                        {
                            "name": "score_filter",
                            "threshold": 0.618,
                        },
                        # 模型10: kmeans
                        {
                            "name": "kmeans_classification",
                            "filter_low_score_threshold": 0.3,
                            "neglect_syllabus_regs": P_EQUITY_CHAPTER,
                        },
                    ],
                    # 子项3
                    "The number of unvested awards": [
                        # 模型1：通用NS场景
                        FOLLOW_FIRST_NS,
                        # 模型2：特例，指定章节的表格全都要
                        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/4761#note_531475
                        {
                            "name": "special_cells",
                            "multi_elements": True,
                            "syllabus_regs": [rf"^(equity|cash){R_MIDDLE_DASH}settled\s*restricted\s*stock\s*units$"],
                            "multi": True,
                            "title_patterns": R_TABLE_TITLES,
                            "neglect_title_patterns": P_NEG_TBL_TITLES,
                            "col_header_pattern": R_NUMBER_COLS,
                            "any_cell_pattern": R_CELL_KEYWORDS,
                            "cell_pattern": R_CELL_NUMBER,
                        },
                        # 模型3：匹配表格的列
                        {
                            "name": "special_cells",
                            "multi": True,
                            "title_patterns": R_TABLE_TITLES,
                            "neglect_title_patterns": P_NEG_TBL_TITLES,
                            "col_header_pattern": R_NUMBER_COLS,
                            "any_cell_pattern": R_CELL_KEYWORDS,
                            "cell_pattern": R_CELL_NUMBER,
                        },
                        # 模型4：分值高的表格尝试匹配
                        {
                            "name": "special_cells",
                            "multi": True,
                            "threshold": 0.8,
                            "col_header_pattern": R_NUMBER_COLS,
                            "row_header_pattern": R_NUMBER_COLS,
                            "row_col_relation": "or",
                        },
                        # 模型5：表格中的单元格：number of restricted
                        {
                            "name": "special_cells",
                            "multi": True,
                            "title_patterns": R_TABLE_TITLES,
                            "neglect_title_patterns": P_NEG_TBL_TITLES,
                            "row_header_pattern": R_NUMBER_COLS,
                            "col_header_pattern": R_COL_NUMBER_OF,
                            "any_cell_pattern": R_CELL_KEYWORDS,
                            "last_mode": {LastMode.FIRST_COL},
                        },
                        # 模型6：根据行名匹配表格
                        {
                            "name": "special_cells",
                            "multi": True,
                            # "title_patterns": R_TABLE_TITLES,
                            "neglect_title_patterns": P_NEG_TBL_TITLES,
                            "row_header_pattern": R_NUMBER_COLS,
                            "any_cell_pattern": R_CELL_KEYWORDS,
                        },
                        # # 模型7~8：B74~B78通用：no award was granted + outstanding期初期末都为0
                        # *get_no_grant_models(P_NS),
                        {
                            "name": "score_filter",
                            "threshold": 0.618,
                        },
                        # 模型9: kmeans
                        {
                            "name": "kmeans_classification",
                            "filter_low_score_threshold": 0.3,
                            "neglect_syllabus_regs": P_EQUITY_CHAPTER,
                        },
                    ],
                    # 子项4
                    "Date of grant": [FOLLOW_FIRST_NS, *date_of_grant_models()],
                    # 子项5
                    "Vesting period": [FOLLOW_FIRST_NS, *vesting_period_models()],
                    # 子项6
                    "Exercise period": [FOLLOW_FIRST_NS, *exercise_period_models()],
                    # 子项7
                    "Purchase price": [FOLLOW_FIRST_NS, *purchase_price_models()],
                },
            }
        ],
    },
]
