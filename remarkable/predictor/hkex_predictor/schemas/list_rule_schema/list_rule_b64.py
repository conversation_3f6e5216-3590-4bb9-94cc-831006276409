import re
from typing import List

from interdoc.element import Para

from remarkable.common.common_pattern import P_CONTINUED, P_SEN_SEPARATOR, R_MIDDLE_DASHES, R_NOTES_CHAPTER_TITLES
from remarkable.common.constants import AnswerValueEnum
from remarkable.common.pattern import (
    MatchMulti,
    NeglectPattern,
    PositionPattern,
    SplitBeforeMatch,
)
from remarkable.common.util import clean_txt
from remarkable.models import BaseModel
from remarkable.pdfinsight.reader import <PERSON>d<PERSON><PERSON><PERSON>eader
from remarkable.predictor.common_pattern import R_EN_MONTH
from remarkable.predictor.hkex_predictor.models.yoda_layer.filter import ChapterFilter, ParaLensFilter, ParaTextFilter
from remarkable.predictor.hkex_predictor.models.yoda_layer.operator import Operator
from remarkable.predictor.hkex_predictor.models.yoda_layer.rule.by_pos import (
    Continued<PERSON>lem,
    SentenceFromSplitPara,
)
from remarkable.predictor.hkex_predictor.models.yoda_layer.rule.root import (
    NotesCascade<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    ScoreDestFilter,
)
from remarkable.predictor.hkex_predictor.pattern import R_DAY
from remarkable.predictor.hkex_predictor.schemas.list_rule_schema.list_rule_b63 import (
    P_B63_TITLE_NEG,
    P_OPTION_MOVEMENT_TBL,
    R_NUMBER_OF_OPTIONS,
    no_grant_during_the_year,
)
from remarkable.predictor.hkex_predictor.schemas.list_rule_schema.list_rule_b94 import P_NS_COL_VALUE
from remarkable.predictor.hkex_predictor.schemas.pattern import (
    R_AS_FOLLOW_END,
    R_OPTION_KEYWORD,
    R_PRICE,
    reg_words,
)
from remarkable.predictor.models.para_match import AsFollowType
from remarkable.predictor.mold_schema import SchemaItem
from remarkable.predictor.schema_answer import PredictorResult, TableCellsResult

# performance targets
# https://jura-uat2.paodingai.com/#/hkex/annual-report-checking/report-review/243354?fileId=80117&schemaId=5&rule=B64&delist=0
R_TARGET = r"(?<!long[- ]term )performance\s*(target|index|review)|\bkey\s*performance"

# fair value
P_FAIR_VALUE_PS = MatchMulti.compile(r"fair\s*value", rf"grant\s*date|{R_DAY}", R_PRICE, operator=all)

# accounting standard and policy adopted
R_SHARE_BASED_TITLE = rf"share[{R_MIDDLE_DASHES} ]based\s*(payment|compensation).*[a-z]$"
P_SHARE_BASED_TITLE = re.compile(R_SHARE_BASED_TITLE, re.I)
R_SIGN_ACCOUNT = r"(principal|significant)\s*account(ing)?\s*polic(y|ies)$"
P_SIGN_ACCOUNT = re.compile(R_SIGN_ACCOUNT, re.I)
R_EMPLOYEE_BENEFIT = r"employee\s*benefits?$"
P_EMPLOYEE_BENEFIT = re.compile(R_EMPLOYEE_BENEFIT, re.I)
P_SHARE_OPTION = re.compile(rf"{R_OPTION_KEYWORD}.*[a-z]$", re.I)

P_B64_UN_GRANTED = MatchMulti.compile(
    # during the year
    MatchMulti.compile(
        r"during\s*the\s*year",
        r"during\s*the\s*Reporting\s*Period",
        r"date\s*of\s*adoption\s*to\s*the\s*year\s*end(ed)?",
        r"As\s*(of|at)\s*the\s*date\s*of\s*this\s*(annual)?\s*report",
        r"up\s*to\s*the\s*date\s*of\s*this\s*report",
        r"During\s*(the\s*)?(FY|financial\s*year)",
        r"up\s*to\s*(the\s*)?date\s*of\s*(this\s*)?report",
        rf"as\s*at\s*\d{{1,2}}\s*({R_EN_MONTH}|\d{{1,2}})\s*\d{{4}}\s*",
        rf"from\s*{reg_words(0, 5)}date\s*of\s*the\s*adoption\s*of\s*the\s*{R_OPTION_KEYWORD}",
        rf"Since\s*(the)?\s*{reg_words(0, 5)}Adopt(ion|ed)?",
        operator=any,
    ),
    SplitBeforeMatch(
        NeglectPattern.compile(
            match=MatchMulti.compile(
                rf"(no|not)\s*{reg_words(0, 5)}(share\s*)?options?\s*{reg_words(0, 4)}grant(ed)?",
                rf"(no|not)\s*{reg_words(0, 4)}grant(ed)?\s*(any|further)\s*(shares?\s*)?options?\s*under\s*{reg_words(0, 4)}Shares?\s*Option\s*",
                rf"(no|not)\s*{reg_words(0, 4)}shares?\s*options?\s*were\s*conditionally\s*or\s*unconditionally.*?grant(ed)?",
                operator=any,
            ),
            unmatch=MatchMulti.compile(
                rf"\b(no|not)\b\s*further\s*options?\s*{reg_words(0, 4)}grant",
                rf"{R_OPTION_KEYWORD}\s*granted\s*was\s*cancelled",
                operator=any,
            ),
        ),
        separator=P_SEN_SEPARATOR,
        operator=any,
    ),
    operator=all,
)

R_B64_ROW_COL_HEADER = [
    # r"(^|\t)granted($|\t)|(number|no\.)\s*of\s*grant",
    MatchMulti.compile(r"Granted", r"during", operator=all),
    # r"Number\s*of\s*Grant",
    # r"^(Number\s*of\s*share\s*options?)?\s*Granted",
]

P_ONLY_ENG = re.compile(r"^[a-z\s]+$", re.I)


def b64_table_enum_func(table_results: list[TableCellsResult], footnote_results):
    # http://************:55647/#/project/remark/233197?treeId=13539&fileId=66488&schemaId=15&projectId=13539&schemaKey=B64
    table_cells = [cell for result in table_results for cell in result.parsed_cells]
    if all(
        not clean_txt(cell.text) or P_NS_COL_VALUE.search(cell.clean_text) or P_ONLY_ENG.search(cell.clean_text)
        for cell in table_cells
        if not cell.is_header and cell.width == cell.height == 1
    ):
        return AnswerValueEnum.NS.value
    return AnswerValueEnum.ND.value


def post_process_answers(
    answers: List[PredictorResult], model: BaseModel, schema: SchemaItem, pdfinsight: PdfinsightReader
):
    if model.config.get("model_id") not in ["ns_4"] or not answers:
        return answers
    if any(AnswerValueEnum.PS.value == answer.answer_value for answer in answers):
        return []
    return answers


B64_COMMON_NS = [
    # 模型1：通用NS场景
    {"name": "no_share", "model_id": "ns_1", "enum": AnswerValueEnum.NS.value},
    # 模型2：NS场景之当年内没有grant过
    {
        "name": "yoda_layer",
        "model_id": "ns_2",
        "threshold": 0,
        "multi_elements": True,
        "enum_from_multi_element": True,
        # http://************:55647/#/project/remark/234012?treeId=38073&fileId=66624&schemaId=15&projectId=38073&schemaKey=B64
        "neglect_syllabus_regs": [r"life|duration\s*of"],
        "enum": AnswerValueEnum.NS.value,
        "rule": no_grant_during_the_year(),
    },
    # 模型3：当年grant数为0，限制必须存在number of options的单元格
    {
        "name": "special_cells",
        "model_id": "ns_3",
        "include_footnotes": True,
        "skip_answer_value": AnswerValueEnum.ND.value,
        "enum_function": b64_table_enum_func,
        # "title_patterns": P_SHARE_OPTION_TABLE_TITLE,
        # "neglect_title_patterns": P_OPTION_NEG,
        "any_cell_pattern": [R_NUMBER_OF_OPTIONS, r"^granted\s*during\s*the\s*year$"],
        "col_header_pattern": R_B64_ROW_COL_HEADER,
        "row_header_pattern": R_B64_ROW_COL_HEADER,
        "row_col_relation": "or",
    },
    # 模型4：当年grant数为0，限制表名必须为movement，按行+列
    # http://************:55647/#/project/remark/233808?treeId=37712&fileId=66590&schemaId=15&projectId=37712&schemaKey=B64 index=2075
    {
        "name": "special_cells",
        "model_id": "ns_4",
        "include_footnotes": True,
        "skip_answer_value": AnswerValueEnum.ND.value,
        "enum_function": b64_table_enum_func,
        "title_patterns": P_OPTION_MOVEMENT_TBL,
        "neglect_title_patterns": P_B63_TITLE_NEG,
        "col_header_pattern": R_NUMBER_OF_OPTIONS,
        "row_header_pattern": R_B64_ROW_COL_HEADER,
        "row_col_relation": "and",
    },
    # 模型5：当年grant数为0，限制表名必须为movement，按列
    {
        "name": "special_cells",
        "model_id": "ns_5",
        "include_footnotes": True,
        "skip_answer_value": AnswerValueEnum.ND.value,
        "enum_function": b64_table_enum_func,
        "title_patterns": P_OPTION_MOVEMENT_TBL,
        "neglect_title_patterns": P_B63_TITLE_NEG,
        "col_header_pattern": R_B64_ROW_COL_HEADER,
    },
]


def account_policy_locator(chapter_patterns):
    return NotesCascadeChapterLocator(
        chapter_patterns=chapter_patterns,
        strict=True,
        dest=ContinuedElem[Para](
            stop=ChapterFilter(skip_pattern=P_CONTINUED),
            skip=ParaLensFilter(0, 8)
            | ParaTextFilter(pattern=MatchMulti.compile(*R_NOTES_CHAPTER_TITLES, P_CONTINUED, operator=any)),
            limit=10,
            size=2,
            start=1,
        ),
    )


# temporary sample
predictor_options = [
    {
        "path": ["B64"],
        "fake_leaf": True,
        "models": [
            {
                "name": "group_based",
                "share_type": "option",
                "pick_answer_strategy": "single",
                "group_default_enum": AnswerValueEnum.ND.value,
                "post_process_answers": post_process_answers,
                "filter_group_answer": True,
                "dr_chapter_first": True,  # 优先DR章节
                "column_configs": {
                    "Performance targets (if any)": [
                        *B64_COMMON_NS,
                        # 模型6：NS场景之没有performance targets
                        # http://************:55647/#/project/remark/233994?treeId=37676&fileId=66621&schemaId=15&projectId=37676&schemaKey=B64
                        {
                            "name": "yoda_layer",
                            "threshold": 0,
                            "multi_elements": True,
                            "skip_syllabus_title": True,
                            "enum": AnswerValueEnum.NS.value,
                            "rule": ScoreDestFilter(
                                dest=SentenceFromSplitPara(
                                    ordered_patterns=[
                                        PositionPattern.compile(
                                            r"(\bnot|n['‘’]t)\s*(stipulate|set\b)",
                                            R_TARGET,
                                        ),
                                    ],
                                ),
                            ),
                        },
                        # 模型7：找表格
                        {
                            "name": "special_cells",
                            "multi_elements": True,
                            "multi": True,
                            "include_footnotes": True,
                            "neglect_title_patterns": P_B63_TITLE_NEG,
                            "col_header_pattern": [R_TARGET],
                            "row_header_pattern": [R_TARGET],
                            "row_col_relation": "or",
                        },
                        # 模型8：找表格，限制单元格内容
                        {
                            "name": "special_cells",
                            "multi_elements": True,
                            "multi": True,
                            "include_footnotes": True,
                            "neglect_title_patterns": P_B63_TITLE_NEG,
                            "cell_pattern": R_TARGET,
                        },
                        # 模型9：找出as follow
                        {
                            "name": "para_match",
                            "neglect_syllabus_regs": P_B63_TITLE_NEG,
                            "as_follow_type": AsFollowType.ANY,
                            "as_follow_pattern": SplitBeforeMatch(
                                MatchMulti.compile(
                                    MatchMulti.compile(
                                        R_TARGET,
                                        r"vesting\s*condition",
                                        MatchMulti.compile(r"vesting", "condition", operator=all),
                                        operator=any,
                                    ),
                                    R_AS_FOLLOW_END,
                                    operator=all,
                                ),
                                separator=P_SEN_SEPARATOR,
                            ),
                        },
                        # 模型10:找performance target段落
                        {
                            "name": "para_match",
                            "neglect_syllabus_regs": P_B63_TITLE_NEG,
                            "paragraph_pattern": R_TARGET,
                        },
                        # 模型11:找vesting conditions段落
                        {
                            "name": "para_match",
                            "neglect_syllabus_regs": P_B63_TITLE_NEG,
                            "paragraph_pattern": MatchMulti.compile(
                                r"vesting\s*conditions?",
                                r"offer\s*letter",
                                operator=any,
                            ),
                        },
                        # 模型12:分值过滤
                        {
                            "name": "score_filter",
                            "multi_elements": True,
                            "threshold": 0.618,
                            "enum_every_element": True,
                        },
                    ],
                    "Fair value of options at the date of grant": [
                        # 模型1:取子项1的NS结果
                        {
                            "name": "reference",
                            "model_ids": ["ns_1", "ns_2", "ns_3", "ns_4", "ns_5"],
                            "from_path": ["B64", "Performance targets (if any)"],
                            "from_answer_value": AnswerValueEnum.NS.value,
                        },
                        # 模型2: 找出as follow表格或段落
                        {
                            "name": "para_match",
                            "multi_elements": True,
                            "as_follow_type": AsFollowType.ANY,
                            "as_follow_pattern": SplitBeforeMatch.compile(
                                MatchMulti.compile(r"fair\s*value", R_AS_FOLLOW_END, operator=all),
                                separator=P_SEN_SEPARATOR,
                            ),
                        },
                        # 模型3: 找表格
                        {
                            "name": "special_cells",
                            "multi_elements": True,
                            "threshold": 0,
                            "neglect_title_patterns": P_B63_TITLE_NEG,
                            "row_header_pattern": r"fair\s*value",
                            "col_header_pattern": r"fair\s*value",
                            "row_pattern": R_PRICE,
                            "col_pattern": R_PRICE,
                            "row_col_relation": "or",
                            "include_footnotes": True,
                        },
                        # 模型4:找句子
                        {
                            "name": "yoda_layer",
                            "threshold": 0,
                            "multi_elements": True,
                            "enum_every_element": True,
                            "skip_syllabus_title": True,
                            "rule": ScoreDestFilter(
                                multi=False,
                                dest=SentenceFromSplitPara(multi=True, ordered_patterns=[P_FAIR_VALUE_PS]),
                            ),
                        },
                    ],
                    "Accounting standard and policy adopted": [
                        # 模型1:取子项1的NS结果
                        {
                            "name": "reference",
                            "model_ids": ["ns_1", "ns_2", "ns_3", "ns_4", "ns_5"],
                            "from_path": ["B64", "Performance targets (if any)"],
                            "from_answer_value": AnswerValueEnum.NS.value,
                        },
                        # 模型2: yodalayer找章节
                        {
                            "name": "yoda_layer",
                            "rule": Operator.any(
                                # account_policy_locator([P_SIGN_ACCOUNT, P_SHARE_BASED_TITLE, P_SHARE_OPTION]),
                                account_policy_locator(
                                    [P_SIGN_ACCOUNT, P_SHARE_BASED_TITLE, P_EMPLOYEE_BENEFIT, P_SHARE_OPTION]
                                ),
                                account_policy_locator(
                                    [P_SIGN_ACCOUNT, P_EMPLOYEE_BENEFIT, P_SHARE_BASED_TITLE, P_SHARE_OPTION]
                                ),
                                account_policy_locator([P_SIGN_ACCOUNT, P_EMPLOYEE_BENEFIT, P_SHARE_BASED_TITLE]),
                                account_policy_locator([P_SIGN_ACCOUNT, P_SHARE_BASED_TITLE]),
                                account_policy_locator([P_SIGN_ACCOUNT, P_EMPLOYEE_BENEFIT]),
                            ),
                        },
                        # 模型3:syllabus找章节
                        {
                            "name": "syllabus_elt_v2",
                            "only_inject_features": True,
                            # https://jura-uat2.paodingai.com/#/hkex/annual-report-checking/report-review/243354?fileId=80117&schemaId=5&rule=B64&delist=0&page=235
                            "neglect_parent_features": r"fair\s*value",
                            "inject_syllabus_features": [
                                rf"__regex__{R_SIGN_ACCOUNT}__regex__{R_SHARE_BASED_TITLE}__regex__{R_OPTION_KEYWORD}",
                                rf"__regex__{R_SIGN_ACCOUNT}__regex__{R_EMPLOYEE_BENEFIT}__regex__{R_SHARE_BASED_TITLE}",
                                rf"__regex__{R_SIGN_ACCOUNT}__regex__{R_SHARE_BASED_TITLE}",
                                rf"__regex__{R_SIGN_ACCOUNT}__regex__{R_EMPLOYEE_BENEFIT}",
                            ],
                        },
                    ],
                },
            },
        ],
    },
]


if __name__ == "__main__":
    cell = "--"
    pattern = MatchMulti.compile(r"(^|\t)[\(（]?[-—－–0oO\s*][\)）]?(\t|$)", r"(^|\t)(N/A|nil)($|\t)", operator=any)
    print(pattern.search(cell))
