from remarkable.common.constants import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from remarkable.common.pattern import <PERSON><PERSON>ult<PERSON>, NeglectPattern, PositionPattern
from remarkable.predictor.common_pattern import R_DATES, R_EN_MONTH, R_PERCENT
from remarkable.predictor.hkex_predictor.schemas.list_rule_schema.list_rule_b1 import (
    B1_B10_NS_MODELS,
    R_UNUSED,
    R_USED,
    R_UTILISE,
)
from remarkable.predictor.hkex_predictor.schemas.list_rule_schema.list_rule_b8 import (
    B8_B10_NS_MODELS,
    P_COL_REVISED_USE,
    P_FULLY_USED_IN_CELL,
    P_PROCEEDS_TBL_TITLE,
    P_USED_IN_CELL,
    R_UNMATCH_TIMELINE,
    R_USED_IN_PARA,
    table_models_for_proceeds,
)
from remarkable.predictor.hkex_predictor.schemas.list_rule_schema.post_processor import BasePostProcessor
from remarkable.predictor.hkex_predictor.schemas.pattern import R_<PERSON><PERSON>, R_<PERSON>ULL<PERSON>, <PERSON>_<PERSON><PERSON><PERSON>, R_MONEY_UNIT, R_NOT
from remarkable.predictor.hkex_predictor.schemas.util import get_sentence_model

P_ACTUAL_USE = NeglectPattern.compile(
    match=MatchMulti.compile(
        r"^actual\s*(uses?|usage)\s*((of\s*)?(the\s*)?((net|unutili[sz]ed|unused)\s*)?proceeds)?|intended\s*(uses?\s*)?and\s*actual\s*use",
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6245#note_663540
        # https://jura6-lir.paodingai.com/#/hkex/annual-report-checking/report-review/263942?fileId=70003&schemaId=5&rule=C1.3&delist=0
        r"accumulated\s*(use|investment\s*amount)",
        operator=any,
    ),
    unmatch=rf"percent|{R_PERCENT}",
)
# http://************:55647/#/project/remark/232587?treeId=3602&fileId=66386&schemaId=15&projectId=3602&schemaKey=B9
R_FULLY_USED = [
    rf"(?<!\bbe ){R_FULLY}\s*{R_USED}",
    rf"(?<!not )(?<!['‘’]t ){R_USED}\s*(in\s*full)",
    rf"{R_HAVE}\s*{R_USED}\s*([a-z]+\s+)?(the\s*)?{R_ALL}\b",
    rf"\b{R_ALL}\s*([a-z]+\s+){{0,3}}(was|were|{R_HAVE}\s*been)\s*{R_USED}",
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6238#note_662031
    # http://************:55647/#/project/remark/233221?treeId=13168&fileId=66492&schemaId=15&projectId=13168&schemaKey=B9
    rf"\b{R_ALL}\s[^,，]*?proceeds[^,，]*\s(was|were|{R_HAVE}\s*been)\s*{R_UTILISE}d",
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/5332#note_620495
    r"(subscription|proceeds)[^,，]*?satisfied\s*in\s*full",
]
# 截至日期
# http://************:55647/#/hkex/annual-report-checking/report-review/266249?fileId=70464&schemaId=18&rule=C1.2.1&delist=0
R_TIME_PREP = rf"(\b(before|end\s*of|by|quarter\s*of|by\s*([a-z]+\s+){{1,3}})\s*(\d{{,2}}\s*|{R_EN_MONTH}\s*){{,2}}|[a-z]\s*(?<!in ){R_EN_MONTH})\s*20\d{{2}}($|[^\d])"
R_TIME_WITHIN = r"\b(with)?in\s+(\w+\s+){1,5}(year|month|days)"
P_VALID_PS_PARA = PositionPattern.compile(R_UNUSED, rf"{R_TIME_PREP}|{R_TIME_WITHIN}|{R_UNMATCH_TIMELINE}")

P_TIME_IN_CELL = MatchMulti.compile(R_EN_MONTH, r"\d", r"\b(years|months?|days?|by|on|before|within)\b", operator=any)
# timing: https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6414#note_674331
P_TIMELINE = MatchMulti.compile(
    r"(?<!revised )(\btime(line|frame|table|ing)|\bschedule)|\b(expected|estimated)\s*(date|tim)",
    NeglectPattern.compile(
        match=r"\b(expected|estimated)",
        unmatch=rf"\b(percent|revise|realloc|adjust|amount|million|{R_MONEY_UNIT}[^a-z])",
    ),
    operator=any,
)
P_PARA_TIMELINE = MatchMulti.compile(*[rf"put into use by {_date}" for _date in R_DATES], operator=any)
P_UNUSED = NeglectPattern.compile(match=R_UNUSED, unmatch=rf"{R_UNMATCH_TIMELINE}|revise|reallocat|adjust|change")
P_B9_COLUMNS = [
    NeglectPattern.compile(
        match=MatchMulti.compile(
            # 未使用资金
            R_UNUSED,
            # 预期时限
            P_TIMELINE,
            operator=any,
        ),
        # 排除包含修订的列： http://************:55647/#/project/remark/219632?treeId=37586&fileId=65556&schemaId=15&projectId=37586&schemaKey=B9
        unmatch=P_COL_REVISED_USE,
    )
]


class B9PostProcessor(BasePostProcessor):
    """
    B9表格后处理步骤：
    1. 先判断有没有unused列，若没有，丢弃答案，返回ND
    2. 若有多个unused列，取最后一个（后续也可视情况判断year_end，但不一定准）
    3. 若unused列全部为0，或者无有效的金额描述，枚举值修改为NS
    4. 不为NS的场景下，若没有expected timeline，丢弃答案，返回ND
    5. 如果答案为NS,但是actual used列中没有说明fully applied，则为ND
    """

    def filter_ps_table_cells(self, parsed_cells, group_type):
        # 1. operator=all的模型，要和PS段落结合判断，不处理
        # http://************:55647/#/project/remark/233018?treeId=24500&fileId=66458&schemaId=15&projectId=24500&schemaKey=B9
        if self.model.name == "multi_models" and self.model.config.get("operator") == "all":
            return parsed_cells
        # 2. used列中，有全部使用的描述，则为NS
        used_col_indices = self.get_col_indices_by_pattern(parsed_cells, P_USED_IN_CELL)
        if used_col_indices:
            used_cells = [c for c in parsed_cells if c.colidx in used_col_indices]
            if any(P_FULLY_USED_IN_CELL.search(c.text) for c in used_cells if not c.is_header):
                self.table_is_ns = True
                return self.add_first_col_cells(parsed_cells)
        # 3. 若有unused列，判定是否为NS
        unused_cells = []
        if unused_col_indices := self.get_col_indices_by_pattern(parsed_cells, P_UNUSED, used_col_indices):
            # 取unused中index最大的那一列
            self.check_value_col = max(unused_col_indices)
            unused_cells = [cell for cell in parsed_cells if cell.colidx == self.check_value_col]
            if not self.cells_have_value(unused_cells):
                self.table_is_ns = True
        # 4. 找timeline列
        if timeline_cols := self.get_col_indices_by_pattern(parsed_cells, P_TIMELINE):
            self.check_value_col = max(timeline_cols)
            self.check_value_pattern = P_TIME_IN_CELL
            return self.add_first_col_cells(unused_cells + [c for c in parsed_cells if c.colidx in timeline_cols])
        # 特例：列名为actual use时，直接取
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6840#note_705247
        if self.get_col_indices_by_pattern(parsed_cells, P_ACTUAL_USE):
            return self.add_first_col_cells(parsed_cells)
        # 特例：脚注带有timeline描述的语句
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6886#note_705875
        footnotes = parsed_cells[0].table.footnotes
        for footnote in footnotes:
            if P_PARA_TIMELINE.search(footnote["text"]):
                return self.add_first_col_cells(parsed_cells)
        return []

    def is_ns_table(self, parsed_cells):
        # 根据unused列是否有值，判断是否为NS
        return self.table_is_ns or not self.cells_have_value(parsed_cells)

    def is_valid_ps_para(self, text, element):
        if self.has_valid_table:
            return True
        # 只有段落时，必须包含预计使用期限
        return P_VALID_PS_PARA.search(text)


def unused_sentence_model(enum=None):
    return get_sentence_model(
        [
            MatchMulti.compile(
                # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6236#note_661996
                rf"{R_UNUSED}|\bgross\s*proceeds.*?after\s*deduction",
                rf"{R_MONEY_UNIT}[1-9]|{R_PERCENT}",
                operator=all,
            ),
            # http://************:55647/#/project/remark/251344?treeId=10808&fileId=68680&schemaId=15&projectId=10808&schemaKey=B9
            PositionPattern.compile(
                r"\b(none\s*of\s*the\s*|no)(net\s*)?proceeds\s*",
                rf"(was|were|(have|ha[ds])\s*been)\s*{R_USED}",
            ),
            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6247#note_663244
            MatchMulti.compile(
                R_UNUSED,
                rf"{R_MONEY_UNIT}[1-9]|{R_PERCENT}",
                rf"\b(to|will)\s*be\s*({R_FULLY}\s*)?{R_USED}",
                operator=all,
            ),
        ],
        skip_pattern=MatchMulti.compile(rf"{R_NOT}\s*{R_UNUSED}|received|\bchange", operator=any),
        enum=enum,
        multi_elements=True,
    )


def timeline_sentence_model(enum=None):
    return get_sentence_model(
        [
            PositionPattern.compile(
                r"(?<!revised )(\btime(line|frame|table)|schedule)|\b(expect|estimat)e?d?\s*to\b",
                rf"{R_TIME_PREP}|{R_TIME_WITHIN}",
            ),
            PositionPattern.compile(R_USED_IN_PARA, R_TIME_WITHIN),
            # http://************:55647/#/project/remark/230567?treeId=18628&fileId=61124&schemaId=15&projectId=18628&schemaKey=B9
            PositionPattern.compile(
                R_UNUSED, rf"\b(to|will)\s*be\s*({R_FULLY}\s*)?{R_USED}", rf"{R_TIME_PREP}|\bas\s*intend"
            ),
            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6247#note_663244
            MatchMulti.compile(
                r"\b(expect|estimat)",
                R_UNUSED,
                rf"\b(to|will)\s*be\s*({R_FULLY}\s*)?{R_USED}",
                rf"{R_TIME_PREP}|{R_TIME_WITHIN}",
                operator=all,
            ),
        ],
        enum=enum,
        multi_elements=True,
        threshold=0,
    )


# NS模型：资金已经全部使用
# http://************:55647/#/project/remark/268484?projectId=17&treeId=6340&fileId=70546&schemaId=18&projectId=23578&schemaKey=C1.1.1
# http://************:55647/#/project/remark/268446?treeId=12643&fileId=70541&schemaId=18&projectId=12643&schemaKey=C1.2.1  元素块index=309
NS_FULLY_USED = get_sentence_model(
    [MatchMulti.compile(*R_FULLY_USED, operator=any)],
    # 前面是-的场景，分开描述了多组fully used，不代表全部
    # http://************:55647/#/project/remark/268580?treeId=38058&fileId=70560&schemaId=18&projectId=38058&schemaKey=C1.2.1 P26
    skip_pattern=MatchMulti.compile(rf"({R_NOT}\s*been|\bbe)\s*{R_FULLY}|\d{R_PERCENT} (?!for|as|in|to)", operator=any),
    enum=AnswerValueEnum.NS,
    multi_elements=True,
)


predictor_options = [
    {
        # ------------------------------
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/5332
        # 规则说明：
        # PS: 存在截止当前财报年度未使用的募集资金用途及金额的描述， 并且未使用的金额不为0，以及剩余资金预计使用时限 ；或者已募集的所有金额截止到报告期均未开始使用的描述+该资金预计使用时限
        # NS: 1. 存在直接披露已募集的金额截止到报告期已全部使用完毕的描述
        #     2. 当年发行的计划被terminated/cancelled/lapsed，对应那一组发行事项标注相关描述，枚举NS
        # ND: 无相关信息披露；只披露了未使用金额(非零)，未披露预计使用时限（即关键点没披露全）
        # ------------------------------
        "path": ["B9"],
        "fake_leaf": True,
        "element_candidate_count": 20,
        "models": [
            {
                "name": "use_of_proceeds_group",
                "post_process_answers": B9PostProcessor(),
                "column_configs": {
                    # 子项g
                    "Detailed breakdown and description of utilized amount": [
                        *B1_B10_NS_MODELS,
                        *table_models_for_proceeds(
                            P_B9_COLUMNS,
                            enum=AnswerValueEnum.PS,
                            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6238#note_661670
                            footnote_pattern=MatchMulti.compile(R_UNUSED, rf"{R_NOT}[^.,，]*?{R_USED}", operator=any),
                        ),
                        # 表格和段落信息都要，应取尽取
                        {
                            "name": "multi_models",
                            "operator": "all",
                            "require_unique_element": True,
                            "sort_by_elt_index": True,
                            "enum": AnswerValueEnum.PS.value,
                            "models": [
                                # 句子描述未使用金额及预计使用期限
                                {
                                    "name": "multi_models",
                                    "operator": "any",
                                    "models": [
                                        {
                                            "name": "special_cells",
                                            "threshold": 0,
                                            "title_patterns": P_PROCEEDS_TBL_TITLE,
                                            "multi": True,
                                            "need_continuous": True,
                                            "col_header_pattern": P_UNUSED,
                                        },
                                        unused_sentence_model(),
                                    ],
                                },
                                timeline_sentence_model(),
                            ],
                        },
                        # http://************:55647/#/hkex/annual-report-checking/report-review/264947?fileId=70204&schemaId=15&rule=B9&delist=0
                        get_sentence_model(
                            [
                                MatchMulti.compile(
                                    R_UNUSED,
                                    rf"{R_MONEY_UNIT}[1-9]|{R_PERCENT}",
                                    rf"{R_TIME_PREP}|{R_TIME_WITHIN}",
                                    operator=all,
                                )
                            ],
                            enum=AnswerValueEnum.PS.value,
                        ),
                        # 在表格中找句子
                        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6840#note_705247
                        {
                            "name": "special_cells",
                            "enum": AnswerValueEnum.PS.value,
                            "extend_candidates_syllabus_regs": [r"proceeds|\bfund\b|raising"],
                            "multi": True,
                            "need_continuous": True,
                            "col_header_pattern": P_ACTUAL_USE,
                            "cell_pattern": [R_UNMATCH_TIMELINE, R_TIME_WITHIN, R_TIME_PREP],
                        },
                        # NS句子
                        NS_FULLY_USED,
                        *B8_B10_NS_MODELS,
                    ],
                },
            }
        ],
    }
]
