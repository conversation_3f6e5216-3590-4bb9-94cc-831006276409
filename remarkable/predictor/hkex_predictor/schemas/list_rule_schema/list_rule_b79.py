import re

from interdoc.element import Para

from remarkable.common.common_pattern import (
    P_CONTINUED,
    P_FOLLOW_PREFIX,
    P_PERIOD_SEPARATOR,
    R_DR_CHAPTER_TITLES,
    R_MIDDLE_DASHES,
)
from remarkable.common.constants import AnnualReportEnum
from remarkable.common.pattern import (
    MATCH_NEVER,
    MatchMulti,
    NeglectPattern,
)
from remarkable.common.protocol import SearchPatternLike
from remarkable.predictor.common_pattern import R_PERCENT
from remarkable.predictor.hkex_predictor.model_util import R_NUM
from remarkable.predictor.hkex_predictor.models.yoda_layer.base import ElementLocator, FollowExistAnswerFilter
from remarkable.predictor.hkex_predictor.models.yoda_layer.filter import ParaTextFilter
from remarkable.predictor.hkex_predictor.models.yoda_layer.form import FirstParaAsPos
from remarkable.predictor.hkex_predictor.models.yoda_layer.rule.by_pos import (
    ParaFromPos,
    SentenceFromSplitPara,
    SyllabusLocator,
)
from remarkable.predictor.hkex_predictor.models.yoda_layer.rule.root import (
    Score<PERSON>est<PERSON>ilter,
)
from remarkable.predictor.hkex_predictor.pattern import R_BE
from remarkable.predictor.hkex_predictor.schemas.pattern import (
    R_AWARD_KEYWORD,
    R_AWARD_SHARES,
    R_DURING_THE_YEAR,
    R_ENG_NUMBER,
    R_ENG_ORDER,
    R_NUMBER,
    R_OPTION_KEYWORD,
    reg_words,
)
from remarkable.predictor.models.para_match import AsFollowType

P_NEG_OPTION = NeglectPattern.compile(
    match=R_OPTION_KEYWORD,
    unmatch=MatchMulti.compile(
        r"\brestricted\s*(units?\s*)?(share|award|stock)",
        r"\brs[ua]?\b",
        r"\b[AH]\s*shares",
        r"shares?\s*award",
        r"award(s|ed)?\s*share",
        r"and/or award",
        operator=any,
    ),
)

P_NEG_CHAPTER = MatchMulti.compile(P_NEG_OPTION, R_DURING_THE_YEAR, operator=any)

R_TABLE_TITLE = [
    r"\bVesting\s*Date.+",
]
R_TABLE_VALUE = [
    rf"satisfied\s*{reg_words(1, 2)}\bvesting\s*conditions",
]

# NS场景
P_NS_79 = MatchMulti.compile(
    r"\b(not|neither|nor)\s*specif(y|ied)\s*(a|the)\s*(minimum|maximum)",
    r"\b(no|without)\s*(minimum\s*)?vesting\s*period",
    operator=any,
)

# TODO: the term: http://************:55647/#/project/remark/233808?treeId=37712&fileId=66590&schemaId=15&projectId=17&schemaKey=B79
R_VESTING_PERIOD = rf"(?<!remaining )(vesting|lock[{R_MIDDLE_DASHES}\s]?up)\s*(period|date|schedule)"

# PS场景1：明确的归属期
P_PS_79 = MatchMulti.compile(
    rf"{R_VESTING_PERIOD}",
    rf"({R_ENG_NUMBER}|\d+)\s*(business\s*)?(month|year|day)|date shall {R_BE} deem",
    operator=all,
)
# PS场景2：由board/committee或根据相关timetable而定
P_PS_79_BOARD = MatchMulti.compile(
    r"board|committ?ee?|timetable|schedule|Administrator|letter", r"determine|specif", operator=all
)
# PS场景3：由满足一定的条件后方可be attributed（vested)
P_PS_79_AFTER = MatchMulti.compile(rf"{R_BE} (attributed|vested) after satisfying", operator=all)

# PS场景4：固定数量的非限制性股票在某个日期归属
P_PS_79_NUM_VEST = MatchMulti.compile(rf"\d+{R_PERCENT}", r"vest on each anniversary of the date", operator=all)

P_NOTES = re.compile(rf"^(Notes?|[{{(（\[#*]|{R_NUM}\.)", re.I)
# PS场景遇到以下描述则跳过
R_NEGLECT = [
    # 1. 排除option
    P_NEG_OPTION,
    # 2. 排除股数
    rf"{R_NUMBER}\s*{R_AWARD_SHARES}",
    # 3、排除当期
    R_DURING_THE_YEAR,
    # 4、延长、延期
    # http://************:55647/#/project/remark/234000?treeId=42548&fileId=66622&schemaId=15&projectId=17&schemaKey=B79
    # http://************:55647/#/project/remark/236304?treeId=3039&fileId=67006&schemaId=15&projectId=17&schemaKey=B79
    r"prolong|postpone",
]
P_NEGLECT = MatchMulti.compile(
    *R_NEGLECT,
    # 5.排除授予xx
    r"\b(grant|award|vest)(ed)?[^.;；]*?\bto\s*any\b",
    r"\b(grant|award|vest)(ed)\s*to\b",
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/4572#note_543626
    rf"grant(ed)? shares? {R_BE} vest(ed)?",
    # # http://************:55647/#/project/remark/234893?treeId=9336&fileId=66771&schemaId=15&projectId=17&schemaKey=B79
    # rf"(grant|award|vest)ed\s*shares",
    # 6. 排除第N次grant
    rf"{R_ENG_ORDER}\s*grant",
    # 8. 排除授予
    operator=any,
)


def sentence_pos(pattern: SearchPatternLike, skip_pattern: SearchPatternLike = MATCH_NEVER):
    return ParaFromPos(
        start=0,
        limit=20,
        only_dest_answers=True,
        pattern=pattern,
        dest=SentenceFromSplitPara(
            ordered_patterns=[
                pattern,
            ],
            skip_pattern=skip_pattern,
        ),
    )


predictor_options = [
    {
        "path": ["B79"],
        "fake_leaf": True,
        "models": [
            {
                "name": "group_based",
                "share_type": "award",
                "group_default_enum": AnnualReportEnum.ND.value,
                "column_configs": {
                    "Content": [
                        # 模型1：通用NS
                        {
                            "name": "no_share",
                            "with_element_box": True,
                            "enum": AnnualReportEnum.NS.value,
                        },
                        # 模型2：基于初步定位结果，找出否定描述
                        {
                            "name": "yoda_layer",
                            "threshold": 0,
                            "skip_syllabus_title": True,
                            "neglect_syllabus_regs": P_NEG_CHAPTER,
                            "enum": AnnualReportEnum.NS,
                            "rule": ScoreDestFilter(
                                dest=SentenceFromSplitPara(
                                    ordered_patterns=[P_NS_79],
                                    skip_pattern=MatchMulti.compile(R_DURING_THE_YEAR, operator=any),
                                ),
                            ),
                        },
                        # 模型3: 根据Vesting Period章节判断
                        {
                            "name": "yoda_layer",
                            "threshold": 0,
                            "enum": AnnualReportEnum.PS.value,
                            "rule": ScoreDestFilter(
                                dest=SyllabusLocator(
                                    pattern=MatchMulti.compile(r"^vesting\s*period$", operator=any),
                                    skip_pattern=NeglectPattern.compile(
                                        match=R_OPTION_KEYWORD, unmatch=R_AWARD_KEYWORD
                                    ),
                                ),
                            ),
                        },
                        # 模型4: 因章节识别问题，查找上个段落是否为vesting period
                        {
                            "name": "yoda_layer",
                            "threshold": 0,
                            "skip_syllabus_title": True,
                            "neglect_syllabus_regs": P_NEG_CHAPTER,
                            "enum": AnnualReportEnum.PS.value,
                            "rule": ScoreDestFilter(
                                dest=ParaFromPos(
                                    size=1,
                                    start=0,
                                    adapter=FirstParaAsPos(),
                                ).link_next(
                                    ParaFromPos(
                                        pattern=MatchMulti.compile(r"^vesting\s*period$", operator=all),
                                        start=0,
                                        step=-1,
                                        limit=1,
                                    ),
                                    filter=FollowExistAnswerFilter(),
                                ),
                            ),
                        },
                        # 模型5：根据列名/行名找表格
                        {
                            "name": "special_cells",
                            "title_patterns": R_TABLE_TITLE + ["Schemethe Adoption Date"],
                            "neglect_title_patterns": P_NEG_OPTION,
                            "enum": AnnualReportEnum.PS.value,
                            "row_header_pattern": r"vesting\s*(date|period|schedule)\s*(under|of)",
                            "col_header_pattern": r"vesting\s*(date|period|schedule)\s*(under|of)",
                            "row_col_relation": "or",
                        },
                        # 模型6：结合列名+行名找表格
                        # http://************:55647/#/project/remark/232965?treeId=4451&fileId=66449&schemaId=15&projectId=17&schemaKey=B79
                        {
                            "name": "special_cells",
                            # "title_patterns": R_TABLE_TITLE,
                            "neglect_title_patterns": P_NEG_OPTION,
                            "enum": AnnualReportEnum.PS.value,
                            "row_header_pattern": r"vesting\s*(date|period|schedule)",
                            "col_header_pattern": r"(share\s*award\s*(scheme|plan)|\brs[ua]?s?)$",
                            "any_cell_pattern": MatchMulti.compile(P_PS_79, P_PS_79_BOARD, operator=any),
                            # "whole_table": True,
                        },
                        # 模型7：找出肯定描述1，基于初步定位结果
                        {
                            "name": "yoda_layer",
                            "threshold": 0,
                            "skip_syllabus_title": True,
                            "neglect_syllabus_regs": P_NEG_CHAPTER,
                            "enum": AnnualReportEnum.PS.value,
                            "rule": ScoreDestFilter(
                                dest=SentenceFromSplitPara(
                                    ordered_patterns=[
                                        P_PS_79,
                                        MatchMulti.compile(
                                            R_AWARD_KEYWORD,
                                            MatchMulti.compile(
                                                r"vest\s*\d+%",
                                                rf"\d+%\s*of (the total )?{R_AWARD_KEYWORD} (shall|will) {R_BE} vest",
                                                operator=any,
                                            ),
                                            rf"{R_ENG_ORDER}\s*(service\s*year|anniversary)",
                                            operator=all,
                                        ),
                                    ],
                                    skip_pattern=P_NEGLECT,
                                    separator=P_PERIOD_SEPARATOR,
                                ),
                                skip_pattern=P_NOTES,
                            ),
                        },
                        # 模型8：找出肯定描述2，基于初步定位结果
                        {
                            "name": "yoda_layer",
                            "threshold": 0,
                            "skip_syllabus_title": True,
                            "neglect_syllabus_regs": P_NEG_CHAPTER,
                            "enum": AnnualReportEnum.PS.value,
                            "rule": ScoreDestFilter(
                                dest=SentenceFromSplitPara(
                                    separator=P_PERIOD_SEPARATOR,
                                    # http://************:55647/#/project/remark/238215?treeId=42600&fileId=67325&schemaId=15&projectId=17&schemaKey=B79
                                    ordered_patterns=[
                                        MatchMulti.compile(
                                            r"(?<!remaining )\bvest(ing|ed)?\s+.+", P_PS_79_BOARD, operator=all
                                        )
                                    ],
                                    skip_pattern=MatchMulti.compile(*R_NEGLECT, operator=any),
                                    adapter=FirstParaAsPos(),
                                ).link_next(
                                    ElementLocator[Para](
                                        size=20,
                                        limit=20,
                                        filter=ParaTextFilter(
                                            NeglectPattern.compile(
                                                match=MatchMulti.compile(r"[:：]$", P_FOLLOW_PREFIX, operator=any),
                                                unmatch=MatchMulti.compile(P_CONTINUED, operator=any),
                                            )
                                        ),
                                        stop=ParaTextFilter(
                                            NeglectPattern.compile(
                                                match=r".?",
                                                unmatch=MatchMulti.compile(
                                                    r"[:：]$",
                                                    *R_DR_CHAPTER_TITLES,
                                                    P_FOLLOW_PREFIX,
                                                    P_CONTINUED,
                                                    operator=any,
                                                ),
                                            ),
                                        ),
                                    )
                                ),
                                skip_pattern=P_NOTES,
                            ),
                        },
                        # 模型9: 满足条件后才能归属
                        {
                            "name": "yoda_layer",
                            "threshold": 0,
                            "skip_syllabus_title": True,
                            "neglect_syllabus_regs": P_NEG_CHAPTER,
                            "enum": AnnualReportEnum.PS.value,
                            "rule": ScoreDestFilter(
                                dest=SentenceFromSplitPara(
                                    separator=P_PERIOD_SEPARATOR,
                                    ordered_patterns=[
                                        P_PS_79_AFTER,
                                        P_PS_79_NUM_VEST,
                                    ],
                                    skip_pattern=MatchMulti.compile(*R_NEGLECT, operator=any),
                                ),
                                skip_pattern=P_NOTES,
                            ),
                        },
                        # 模型10：章节中带有vesting period，段落中只需要有determined by the board
                        # http://************:55647/#/project/remark/234396?treeId=3116&fileId=66688&schemaId=15&projectId=17&schemaKey=B79
                        {
                            "name": "yoda_layer",
                            "threshold": 0,
                            "skip_syllabus_title": True,
                            "neglect_syllabus_regs": P_NEG_CHAPTER,
                            "syllabus_regs": [r"(?<!remaining )vesting\s*(period|date|schedule)"],
                            "enum": AnnualReportEnum.PS.value,
                            "rule": ScoreDestFilter(
                                dest=SentenceFromSplitPara(
                                    ordered_patterns=[P_PS_79_BOARD],
                                ),
                                skip_pattern=P_NOTES,
                            ),
                        },
                        # 模型11：高分句子直接取
                        {
                            "name": "score_filter",
                            "skip_syllabus_title": True,
                            "threshold": 0.618,
                            "enum": AnnualReportEnum.PS.value,
                        },
                        # 模型12：as follow
                        {
                            "name": "para_match",
                            "skip_syllabus_title": True,
                            "neglect_syllabus_regs": P_NEG_CHAPTER,
                            "as_follow_pattern": MatchMulti.compile(
                                "vesting", r"follow|below", r"[:：]$", operator=all
                            ),
                            "as_follow_type": AsFollowType.ANY,
                            "enum": AnnualReportEnum.PS.value,
                        },
                        # 模型13: kmeans 保底
                        {
                            "name": "score_filter",
                            "skip_syllabus_title": True,
                            "threshold": 0.3,
                            "enum": AnnualReportEnum.PS.value,
                            "neglect_pattern": [P_NOTES, R_NEGLECT],
                        },
                    ],
                },
            },
        ],
    },
]
