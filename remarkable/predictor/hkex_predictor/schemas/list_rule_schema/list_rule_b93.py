import re
from itertools import chain

from remarkable.common.common_pattern import P_SEN_SEPARATOR, R_FOLLOW_PREFIX
from remarkable.common.constants import AnswerV<PERSON>ue<PERSON><PERSON>, TableCellsResultType
from remarkable.common.pattern import MATCH_ALWAYS, MatchMulti, NeglectPattern
from remarkable.common.util import clean_txt
from remarkable.pdfinsight.reader import Pd<PERSON><PERSON><PERSON>eader
from remarkable.predictor.hkex_predictor.models.yoda_layer.filter import (
    AdjacentElemFilter,
    And<PERSON><PERSON><PERSON>,
    Or<PERSON>ilter,
    ParaTextFilter,
    TableRowColFilter,
)
from remarkable.predictor.hkex_predictor.models.yoda_layer.operator import Operator
from remarkable.predictor.hkex_predictor.models.yoda_layer.rule.by_pos import (
    ParaFromPos,
    SentenceFromSplitPara,
    TableCellsFromPos,
)
from remarkable.predictor.hkex_predictor.models.yoda_layer.rule.root import (
    DRCascadeChapterLocator,
    ScoreDestFilter,
)
from remarkable.predictor.hkex_predictor.pattern import (
    P_LIST_RULE_IGNORE_CHAPTERS,
    R_DAY,
)
from remarkable.predictor.hkex_predictor.schemas.pattern import (
    P_DR_CHAPTER,
    P_NOTE_CHAPTER,
    R_AWARD_KEYWORD,
    R_EXPIRED_OR_ADOPTED,
    R_NO_AWARD_NS,
    R_NO_OPTION_NS,
    R_OPTION_KEYWORD,
    reg_words,
)
from remarkable.predictor.models.base_model import AS_FOLLOW_START_PATTERN, BaseModel
from remarkable.predictor.mold_schema import SchemaItem
from remarkable.predictor.schema_answer import PredictorResult
from remarkable.predictor.share_group_utils import R_AWARD_TYPE, R_OPTION_TYPE

R_SERVICE = r"service\s*provider\s*sub[-\s]?limit"
# https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/3603#note_434836
R_PERIOD = rf"only\s*in\s*force\s*during\s*the\s*period\s*from\s*{R_DAY}\s*to\s*{R_DAY}\s*[,，]\s*and\s*no\s*further"
# R_OPTION = rf"{r'|'.join(R_OPTION_TYPES)}|\boption.*?(grant|scheme|available|plan)\b|\b(scheme|available|grant).*?\boptions?\b|\b\d{{4}}\s*scheme\b"
# R_AWARD = rf"\b({R_AWARD_TYPE}|award(s|ed)|shares?(?!\s*option))\b"
# R_CHAPTER_NOTES = R"Notes.*Financial\s*Statements"

# ---- B93.1相关正则 ------
# R_NO_OPTION_NS = [
#     r"not\s*adopted\s*any\s*(shares?\s*)?options?\s*scheme",
#     r"(did|do|does)\s*not\s*(establish|have|ha[sd])\s*(any\s*)?share\s*option\s*scheme(?!\s*other\s*than)",
#     rf"no\s*{R_OPTION_KEYWORD}\s*(have\s*|ha[sd]\s*)?{R_BE}\s*granted\s*to\s*any",
# ]
# https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/3383#note_428438
R_B93_1_NEGS = [
    r"\b(restricted\s*(shares?|award(s|ed)?)|RSUs?|shares?\s*award|award(s|ed)\s*shares?)",
    # r"number\s*of\s*shares\s*(secur|in\s*respect)",
    r"number\s*of\s*shares(?!\s*option)",
    # http://************:55647/#/project/remark/250619?treeId=37591&fileId=68559&schemaId=15&projectId=17&schemaKey=B93.1
    r"number\s*of\s*securit",
    r"shares\s*available",
    r"maximum\s*number\s*of",
    r"\bKPL\b",
    # http://************:55647/#/project/remark/250631?treeId=15176&fileId=68561&schemaId=15&projectId=17&schemaKey=B93.1
    r"Performance\s*Rights\s*Plan",
]
# P_B93_1_NEG_CHAPTERS = MatchMulti.compile(
#     NeglectPattern.compile(
#         MatchMulti.compile(R_AWARD_KEYWORD, R_AWARD_TYPE, operator=any),
#         re.compile(r"option|schemes|number\s*of\s*shares", re.I),
#     ),
#     r"\bKPL\b",
#     operator=any,
# )
R_B93_1_AVAILABLE = r"available\s*for\s*(grant|issue)|grantable"

# member\s*of\s*the\s*group?   (the\s*|^|\W+\s*)
R_NO_SERVICE = [rf"no\s*participants?{reg_words(1, 3)}\s*granted", rf"\bno\s*{R_SERVICE}"]
# 规则93.1(3)、93.1(4)的NS条件：参与者仅为公司董事及/或员工
P_PARTICIPANT = MatchMulti.compile(
    r"eligible|(the|[.,，;；])\s*participant|^participant|^any\s*(individual|person)|personnel|entitled\s*to\s*participate",
    r"\binclud(e|ing)|entitle|to\s*participate",
    operator=all,
)
P_B93_PARTI_NS = NeglectPattern.compile(
    match=re.compile(r"director(?!['‘’])|employee", re.I),
    unmatch=re.compile(
        r"\bother(?!\s*(employee|director))|partner|customer|supplier|agent|consultant|advisor|attract|distributor"
        r"|shareholder|associate|talent|respective|service\s*provider|invested\s*entity|high[-\s]?calibre"
        r"|to\s*determine|by\s*the\s*directors|any\s*(eligible|memeber|person)|\bnot\s*limited|\bno\s*participant",
        re.I,
    ),
)

P_B93_1_CELL_PATTERN = MatchMulti.compile(
    rf"(^|\t){R_OPTION_TYPE}",
    r"(^|\t)\d{4}\s*(Share\s*Option\s*)?Scheme",
    r"(^|\t)Share\s*Option\s*Scheme",
    operator=any,
)
P_B93_1_HEADER = MatchMulti.compile(
    rf"number\s*of\s*{R_OPTION_KEYWORD}\s*available\s*for\s*grant", r"life\s*of\s*the\s*scheme", operator=any
)


# ---- B93.2相关正则 ------
R_B93_2_AVAILABLE = r"available\s*for\s*(grant|award|allocation)|may\s*(be|further)\s*grant|grantable|underlying"
# R_NO_AWARD_NS = [
#     # rf"not\s*adopted\s*any\s*{R_AWARD_KEYWORDS}\s*scheme",
#     rf"(did|do|does)\s*not\s*(establish|have|ha[sd])\s*(any\s*)?{R_AWARD_KEYWORD}\s*scheme(?!\s*other\s*than)",
#     rf"no\s*{R_AWARD_KEYWORD}\s*(have\s*|ha[sd]\s*)?{R_BE}\s*granted\s*to\s*any",
# ]
R_B93_2_NEGS = [
    R_OPTION_KEYWORD,
    r"maximum\s*number\s*of",
    r"\bKPL\b",
    # http://************:55647/#/project/remark/250631?treeId=15176&fileId=68561&schemaId=15&projectId=17&schemaKey=B93.1
    r"Performance\s*Rights\s*Plan",
]
# P_B93_2_NEG_CHAPTERS = MatchMulti.compile(
#     NeglectPattern.compile(MatchMulti.compile(R_OPTION_TYPE, operator=any), re.compile(R_AWARD_KEYWORD, re.I)),
#     r"\bKPL\b",
#     operator=any,
# )

P_B93_2_CELL_PATTERN = MatchMulti.compile(
    rf"(^|\t){R_AWARD_TYPE}",
    r"(^|\t)\d{4}\s*(Share\s*Award\s*)?(Scheme|Plan)",
    r"(^|\t)Share\s*Award\s*(Scheme|Plan)",
    operator=any,
)
P_B93_2_HEADER = MatchMulti.compile(r"number\s*of\s*shares\s*available\s*for\s*grant", operator=any)


def get_table_filter(header_pattern, cell_pattern, title_pattern):
    return AndFilter.from_filters(
        TableRowColFilter(
            pattern=cell_pattern,
            limit=10,
            type="row",
        ),
        # 表名或者表头必须匹配关键词
        OrFilter.from_filters(
            TableRowColFilter(
                pattern=header_pattern,
                limit=10,
                type="row",
            ),
            AndFilter.from_filters(
                AdjacentElemFilter(
                    -2,
                    ParaTextFilter(pattern=MatchMulti.compile(title_pattern, operator=any)),
                ),
                AdjacentElemFilter(
                    -2,
                    ParaTextFilter(pattern=MatchMulti.compile(r"beginning and the end", operator=any)),
                ),
            ),
        ),
    )


def get_b93_mandate_models(keyword, begin_or_end):
    chapter_patterns = [r"Equity\s*Plans"]
    r_begin_end = rf"\s+{begin_or_end}\s+of\s+" if begin_or_end == "end" else rf"at\s*(the\s*)?{begin_or_end}"
    if "B93.1" == keyword:
        key_pattern = R_OPTION_KEYWORD
        # pattern_1 = MatchMulti.compile(r"available\s*for\s*grant", r_begin_end, operator=all)
        pattern = MatchMulti.compile(
            key_pattern,
            MatchMulti.compile(rf"{R_B93_1_AVAILABLE}|available\s*scheme\s*mandate", *R_NO_OPTION_NS, operator=any),
            operator=all,
        )
        neg_patterns = [*R_B93_1_NEGS, R_SERVICE, r"outstanding"]
        # neg_chapters = P_B93_1_NEG_CHAPTERS
        chapter_patterns += [r"share\s*option\s*scheme"]
        cell_pattern = P_B93_1_CELL_PATTERN
        table_filter = get_table_filter(P_B93_1_HEADER, cell_pattern, R_B93_1_AVAILABLE)
    else:
        key_pattern = R_AWARD_KEYWORD
        # pattern_1 = MatchMulti.compile(r"available\s*for\s*grant", r_begin_end, operator=all)
        pattern = MatchMulti.compile(
            key_pattern, MatchMulti.compile(R_B93_2_AVAILABLE, *R_NO_AWARD_NS, operator=any), operator=all
        )
        neg_patterns = [*R_B93_2_NEGS, R_SERVICE]
        # neg_patterns = [*R_B93_2_NEGS, R_SERVICE]
        # neg_chapters = P_B93_2_NEG_CHAPTERS
        chapter_patterns += [r"restricted\s*(share\s*|award\s*){1,2}(scheme|plan)"]
        cell_pattern = P_B93_2_CELL_PATTERN
        table_filter = get_table_filter(P_B93_2_HEADER, cell_pattern, R_B93_2_AVAILABLE)
    return [
        {"name": "no_share"},
        # 模型2：B93 通用NS场景
        {
            "name": "yoda_layer",
            "model_id": "ns_1",
            "threshold": 0,
            "skip_answer_value": AnswerValueEnum.ND.value,
            "multi_elements": True,
            "enum_every_element": True,
            "skip_syllabus_title": True,
            "neglect_syllabus_regs": P_LIST_RULE_IGNORE_CHAPTERS.patterns,
            "rule": ScoreDestFilter(
                multi=True,
                pattern=re.compile(key_pattern, re.I),
                dest=SentenceFromSplitPara(
                    separator=re.compile(
                        rf"{P_SEN_SEPARATOR.pattern}|\sto\s*replace\s|replaced\s*by|instead\s*(of\s*)?|\band\s*a\s*new",
                        re.I,
                    ),
                    ordered_patterns=[
                        MatchMulti.compile(R_PERIOD, operator=any),
                        MatchMulti.compile(key_pattern, R_EXPIRED_OR_ADOPTED, operator=all),
                    ],
                    skip_pattern=MatchMulti.compile(
                        *neg_patterns,
                        rf"expired\s*on\s*{R_DAY}\s*and\s*{R_DAY}",
                        rf"{R_FOLLOW_PREFIX}[^.]+?options\s*of",
                        operator=any,
                    ),
                    multi=True,
                ),
            ),
        },
        # 模型3：初步结果+章节提取句子
        {
            "name": "yoda_layer",
            "threshold": 0,
            "multi_elements": True,
            "enum_every_element": True,
            "skip_answer_value": AnswerValueEnum.ND.value,
            "skip_syllabus_title": True,
            "neglect_syllabus_regs": P_LIST_RULE_IGNORE_CHAPTERS.patterns,
            "rule": Operator.union(
                ScoreDestFilter(
                    multi=True,
                    pattern=re.compile(key_pattern, re.I),
                    dest=SentenceFromSplitPara(
                        ordered_patterns=[
                            MatchMulti.compile(key_pattern, r"available\s*for\s*grant", r_begin_end, operator=all),
                            pattern,
                        ],
                        skip_pattern=MatchMulti.compile(*neg_patterns, operator=any),
                        multi=True,
                    ),
                ),
                DRCascadeChapterLocator(
                    chapter_patterns=[MATCH_ALWAYS],
                    dest=Operator.all(
                        TableCellsFromPos(
                            cell_pattern=cell_pattern,
                            result_type=TableCellsResultType.ROWS.value,
                            multi=True,
                            limit=100,
                            size=1,
                            filter=table_filter,
                        ),
                        TableCellsFromPos(
                            cell_pattern=cell_pattern,
                            result_type=TableCellsResultType.COLS.value,
                            multi=True,
                            limit=100,
                            size=1,
                            filter=table_filter,
                        ),
                    ),
                ),
                DRCascadeChapterLocator(
                    chapter_patterns=[MatchMulti.compile(*chapter_patterns, operator=any)],
                    dest=ParaFromPos(
                        limit=500,
                        size=20,
                        # pattern=pattern,
                        skip_pattern=MatchMulti.compile(*neg_patterns, operator=any),
                        only_dest_answers=True,
                        dest=SentenceFromSplitPara(
                            ordered_patterns=[
                                MatchMulti.compile(r"available\s*for\s*grant", r_begin_end, operator=all),
                                pattern,
                            ],
                            skip_pattern=MatchMulti.compile(*neg_patterns, operator=any),
                            multi=True,
                        ),
                    ),
                ),
            ),
        },
        # 模型4：分值+正则过滤
        {
            "name": "score_filter",
            "threshold": 0.618,
            "multi_elements": True,
            "enum_every_element": True,
            "skip_syllabus_title": True,
            "neglect_syllabus_regs": P_LIST_RULE_IGNORE_CHAPTERS.patterns,
            "pattern": pattern,
            "neglect_pattern": neg_patterns,
        },
    ]


def get_b93_service_models(keyword, begin_or_end):
    r_begin_end = rf"\s+{begin_or_end}\s+of" if begin_or_end == "end" else rf"at\s*(the\s*)?{begin_or_end}"
    if "B93.1" in keyword:
        key_pattern = R_OPTION_KEYWORD
        # pattern_1 = MatchMulti.compile(
        #     rf"options?\s*available\s*for\s*grant\s*at\s*(the\s*)?{begin_or_end}", R_SERVICE, operator=all
        # )
        # pattern_1 = MatchMulti.compile(R_SERVICE, rf"available\s*for\s*grant|{R_BE}\s*{R_NUMBER}", r_begin_end, operator=all)
        ps_pattern = MatchMulti.compile(key_pattern, R_B93_1_AVAILABLE, R_SERVICE, operator=all)
        ns_pattern = MatchMulti.compile(
            key_pattern,
            MatchMulti.compile(*R_NO_OPTION_NS, operator=any),
            operator=all,
        )
        neg_patterns = [*R_B93_1_NEGS, r"mandate\s*limit", r"outstanding"]
        # neg_patterns = [*R_B93_1_NEGS, r"mandate\s*limit", r"outstanding"]
        # neg_chapters = P_B93_1_NEG_CHAPTERS
        cell_pattern = P_B93_1_CELL_PATTERN
        table_filter = get_table_filter(re.compile(r"life\s*of\s*the\s*scheme"), cell_pattern, R_B93_1_AVAILABLE)
    else:
        key_pattern = R_AWARD_KEYWORD
        # pattern_1 = MatchMulti.compile(
        #     rf"(share|award)s?\s*available\s*for\s*grant\s*at\s*(the\s*)?{begin_or_end}", R_SERVICE, operator=all
        # )
        # pattern_1 = MatchMulti.compile(R_SERVICE, rf"available\s*for\s*grant|{R_BE}\s*{R_NUMBER}", r_begin_end, operator=all)
        ps_pattern = MatchMulti.compile(key_pattern, R_B93_2_AVAILABLE, R_SERVICE, operator=all)
        ns_pattern = MatchMulti.compile(
            key_pattern,
            MatchMulti.compile(*R_NO_AWARD_NS, operator=any),
            operator=all,
        )
        neg_patterns = [*R_B93_2_NEGS, r"mandate\s*limit"]
        # neg_patterns = [*R_B93_2_NEGS, r"mandate\s*limit"]
        # neg_chapters = P_B93_2_NEG_CHAPTERS
        cell_pattern = P_B93_2_CELL_PATTERN
        table_filter = get_table_filter(re.compile(r"life\s*of\s*the\s*scheme"), cell_pattern, R_B93_2_AVAILABLE)
    return [
        # 模型1：通用NS
        {"name": "no_share"},
        # 模型2：B93 通用NS场景
        {
            "name": "reference",
            "model_ids": ["ns_1"],
            "from_path": [keyword, "Scheme mandate at the beginning of year"]
            if "beginning" == begin_or_end
            else [keyword, "Scheme mandate at the end of the year"],
            "from_answer_value": AnswerValueEnum.NS.value,
        },
        # 模型3：提取available for grant
        {
            "name": "yoda_layer",
            "threshold": 0,
            "skip_answer_value": AnswerValueEnum.ND.value,
            "multi_elements": True,
            "enum_every_element": True,
            "skip_syllabus_title": True,
            "neglect_syllabus_regs": P_LIST_RULE_IGNORE_CHAPTERS.patterns,
            "rule": ScoreDestFilter(
                multi=True,
                pattern=re.compile(key_pattern, re.I),
                dest=SentenceFromSplitPara(
                    ordered_patterns=[
                        MatchMulti.compile(R_SERVICE, r"available\s*for\s*grant", r_begin_end, operator=all),
                        ps_pattern,
                    ],
                    skip_pattern=MatchMulti.compile(*neg_patterns, operator=any),
                    multi=True,
                ),
            ),
        },
        # 模型4：提取表格的整行/整列
        {
            "name": "yoda_layer",
            "threshold": 1,
            "enum_every_element": True,
            "skip_answer_value": AnswerValueEnum.ND.value,
            "neglect_syllabus_regs": P_LIST_RULE_IGNORE_CHAPTERS.patterns,
            "rule": DRCascadeChapterLocator(
                chapter_patterns=[MATCH_ALWAYS],
                dest=Operator.all(
                    TableCellsFromPos(
                        cell_pattern=cell_pattern,
                        result_type=TableCellsResultType.ROWS.value,
                        multi=True,
                        limit=100,
                        size=1,
                        filter=table_filter,
                    ),
                    TableCellsFromPos(
                        cell_pattern=cell_pattern,
                        result_type=TableCellsResultType.COLS.value,
                        multi=True,
                        limit=100,
                        size=1,
                        filter=table_filter,
                    ),
                ),
            ),
        },
        # 模型5：提取no service provider
        {
            "name": "yoda_layer",
            "threshold": 0,
            "multi_elements": True,
            "enum_every_element": True,
            "skip_syllabus_title": True,
            "skip_answer_value": AnswerValueEnum.ND.value,
            "neglect_syllabus_regs": P_LIST_RULE_IGNORE_CHAPTERS.patterns,
            "rule": ScoreDestFilter(
                multi=True,
                dest=SentenceFromSplitPara(
                    ordered_patterns=[MatchMulti.compile(key_pattern, r"|".join(R_NO_SERVICE), operator=all)],
                    # skip_pattern=MatchMulti.compile(*neg_patterns, operator=any),
                    multi=True,
                ),
            ),
        },
        # 模型6：找出描述参与者为director and/or employee的as following
        {
            "name": "para_match",
            "threshold": 0,
            "skip_answer_value": AnswerValueEnum.ND.value,
            "skip_syllabus_title": True,
            "neglect_syllabus_regs": P_LIST_RULE_IGNORE_CHAPTERS.patterns,
            # "syllabus_regs": [fr"participant|eligible|\bwho|join|share|{key_pattern}"],
            "as_follow_pattern": MatchMulti.compile(
                rf"{key_pattern}|scheme|plan",
                r"participant|eligible\s*person",
                r"[:：]\s*$",
                operator=all,
            ),
            "paragraph_pattern": MatchMulti.compile(P_PARTICIPANT, P_B93_PARTI_NS, operator=all),
            "neglect_pattern": neg_patterns,
        },
        # 模型7：参与者信息在表格中
        # http://************:55647/#/project/remark/250830?treeId=9858&fileId=68594&schemaId=15&projectId=17&schemaKey=B93.1
        {
            "name": "special_cells",
            "force_use_all_elements": True,
            "neglect_syllabus_regs": P_LIST_RULE_IGNORE_CHAPTERS.patterns,
            "title_patterns": MatchMulti.compile(key_pattern, r"summary", r"[:：]$", operator=all),
            "row_header_pattern": r"participant",
            "any_cell_pattern": P_B93_PARTI_NS,
        },
        # 模型8: 提取NS的句子
        {
            "name": "yoda_layer",
            "threshold": 0,
            "multi_elements": True,
            "enum_every_element": True,
            "skip_answer_value": AnswerValueEnum.ND.value,
            "skip_syllabus_title": True,
            "neglect_syllabus_regs": P_LIST_RULE_IGNORE_CHAPTERS.patterns,
            "rule": ScoreDestFilter(
                multi=True,
                # pattern=ns_pattern,
                dest=SentenceFromSplitPara(
                    ordered_patterns=[
                        ns_pattern,
                        NeglectPattern.compile(
                            match=MatchMulti.compile(P_PARTICIPANT, P_B93_PARTI_NS, operator=all),
                            unmatch=MatchMulti.compile(*AS_FOLLOW_START_PATTERN.patterns, operator=any),
                        ),
                    ],
                    skip_pattern=MatchMulti.compile(*neg_patterns, operator=any),
                    multi=True,
                ),
            ),
        },
        # 模型9：分值+正则过滤
        {
            "name": "score_filter",
            "threshold": 0.618,
            "multi_elements": True,
            "enum_every_element": True,
            "skip_syllabus_title": True,
            "neglect_syllabus_regs": P_LIST_RULE_IGNORE_CHAPTERS.patterns,
            "pattern": ns_pattern,
            "neglect_pattern": neg_patterns,
        },
    ]


def filter_and_sort_candidates(pdfinsight: PdfinsightReader, elements: list[dict]) -> list[dict]:
    """
    1. 当有DR章节时，丢弃note以外章节的elements
    2. 按照DR章节、其他章节、Notes章节的顺序排序所有元素块
    3. 如果DR章节有NS相关描述，则丢弃其他章节类似描述
       http://************:55647/#/project/remark/?treeId=37638&fileId=68655&schemaId=15&projectId=17&schemaKey=B93.2
    """
    dr_elements, other_elements, note_elements, parti_indices = [], [], [], []
    dr_has_parti = False
    for element in elements:
        syllabuses = pdfinsight.get_full_syll_path_of_elem(element)
        if not syllabuses:
            other_elements.append(element)
            continue
        if any(P_LIST_RULE_IGNORE_CHAPTERS.nexts(item["title"]) for item in syllabuses):
            continue
        if not (text := clean_txt(element.get("text") or "")):
            continue
        if any(P_DR_CHAPTER.search(item["title"]) for item in syllabuses):
            if not dr_has_parti and P_PARTICIPANT.search(text):
                dr_has_parti = True
            dr_elements.append(element)
            continue
        if P_PARTICIPANT.search(text):
            parti_indices.append(element["index"])
        if any(P_NOTE_CHAPTER.search(item["title"]) for item in syllabuses):
            note_elements.append(element)
            continue
        other_elements.append(element)
    # http://************:55647/#/project/remark/?treeId=37638&fileId=68655&schemaId=15&projectId=17&schemaKey=B93.2
    exclude_ids = parti_indices if dr_has_parti else []
    # 三种章节都有： http://************:55647/#/project/remark/250811?treeId=42901&fileId=68591&schemaId=15&projectId=17&schemaKey=B93.1
    return dr_elements + other_elements + [elem for elem in note_elements if elem["index"] not in exclude_ids]


def post_process_answers(
    answers: list[PredictorResult], model: BaseModel, schema: SchemaItem, pdfinsight: PdfinsightReader
):
    if not answers:
        return answers
    values = {answer.answer_value for answer in answers}
    ele_results = list(chain(*[ans.element_results for ans in answers]))
    if len(values) == 1:
        answer_value = list(values)[0]
    elif {AnswerValueEnum.PS.value, AnswerValueEnum.NS.value} & values:
        if AnswerValueEnum.ND.value in values:
            values.remove(AnswerValueEnum.ND.value)
        if len(values) > 1:
            # 同时有PS和NS答案，需要合并起来重新计算一次答案
            answer_value = model.create_result(ele_results, schema=schema).answer_value
        else:
            answer_value = list(values)[0]
    else:
        answer_value = AnswerValueEnum.ND.value
    # 答案为ND时不需要元素块
    if answer_value == AnswerValueEnum.ND.value:
        return [model.create_result([], value=AnswerValueEnum.ND.value, schema=schema)]
    return [ans for ans in answers if ans.answer_value == answer_value]


def sort_groups(result_group_keys):
    priority = [
        "shareoptionscheme",
        "oldsunevisionshareoptionscheme",
        "newsunevisionshareoptionscheme",
        "oldshareoptionscheme",
        "newshareoptionscheme",
        "smartoneshareoptionscheme",
        "pre-iposhareoptionscheme",
        "post-iposhareoptionscheme",
        "restrictedshareawardscheme",
        "globalpartnerprogramsharescheme",
    ]
    return sorted(result_group_keys, key=lambda x: (priority.index(x) if x in priority else -len(priority), x))


predictor_options = [
    {
        "path": ["B93.1"],
        "fake_leaf": True,
        "models": [
            {
                "name": "group_based",
                "share_type": "option",
                "post_process_answers": post_process_answers,
                "filter_group_answer": True,
                "check_consumed": False,
                "filter_candidates": filter_and_sort_candidates,
                "column_configs": {
                    "Scheme mandate at the beginning of year": get_b93_mandate_models("B93.1", "beginning"),
                    "Scheme mandate at the end of the year": get_b93_mandate_models("B93.1", "end"),
                    "Service provider sublimit at the beginning of year": get_b93_service_models("B93.1", "beginning"),
                    "Service provider sublimit at the end of the year": get_b93_service_models("B93.1", "end"),
                },
            }
        ],
    },
    {
        "path": ["B93.2"],
        "fake_leaf": True,
        "models": [
            {
                "name": "group_based",
                "share_type": "award",
                "post_process_answers": post_process_answers,
                "filter_group_answer": True,
                "check_consumed": False,
                "filter_candidates": filter_and_sort_candidates,
                "column_configs": {
                    "Scheme mandate at the beginning of year": get_b93_mandate_models("B93.2", "beginning"),
                    "Scheme mandate at the end of the year": get_b93_mandate_models("B93.2", "end"),
                    "Service provider sublimit at the beginning of year": get_b93_service_models("B93.2", "beginning"),
                    "Service provider sublimit at the end of the year": get_b93_service_models("B93.2", "end"),
                },
            }
        ],
    },
]
