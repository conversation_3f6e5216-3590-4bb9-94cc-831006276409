from re import Pattern

from remarkable.common.common_pattern import P_PERIOD_SEPARATOR
from remarkable.common.constants import AnswerValueEnum
from remarkable.common.pattern import MATCH_ALWAYS, MATCH_NEVER
from remarkable.common.protocol import SearchPatternLike
from remarkable.predictor.hkex_predictor.models.yoda_layer.rule.by_pos import Senten<PERSON><PERSON>romSplitPara
from remarkable.predictor.hkex_predictor.models.yoda_layer.rule.root import ScoreDestFilter
from remarkable.predictor.models.para_match import AsFollowType


def get_sentence_model(
    ps_patterns: list[SearchPatternLike | str] | SearchPatternLike | str,
    skip_pattern: list | SearchPatternLike | str = None,
    neg_pattern: list | SearchPatternLike | str = None,
    enum: str | None = AnswerValueEnum.PS.value,
    multi_elements: bool = False,
    multi: bool = True,
    threshold: float = 0.0,
    para_separator: Pattern | None = P_PERIOD_SEPARATOR,
    syllabus_regs: list | SearchPatternLike | str = None,
    neglect_syllabus_regs: list | SearchPatternLike | str = None,
    root_parent_regs: list | SearchPatternLike | str = None,
    neglect_root_parent_regs: list | SearchPatternLike | str = None,
    extend_by_enum: bool = False,
    extend_syllabus_regs: list | SearchPatternLike | str = None,
    extend_disclosed_regs: list | SearchPatternLike | str = None,
    extend_disclosed_neg_regs: list | SearchPatternLike | str = None,
    post_process_model_answers: callable = None,
    as_follow_pattern: list | SearchPatternLike | str = None,
    as_follow_type: int = AsFollowType.PARA,
    skip_syllabus_title: bool = True,
    model_id: str = None,
):
    return {
        "name": "para_match",
        "model_id": model_id,
        "enum": enum,
        "threshold": threshold,
        "paragraph_pattern": ps_patterns,
        "syllabus_regs": syllabus_regs,
        "neglect_syllabus_regs": neglect_syllabus_regs,
        "page_first_as_parent_syllabus": True,
        "parent_must_be_root": bool(root_parent_regs or neglect_root_parent_regs),
        "parent_features": root_parent_regs,
        "neglect_parent_features": neglect_root_parent_regs,
        "para_separator": para_separator,
        "neglect_pattern": neg_pattern,
        "neglect_sentence_pattern": skip_pattern,
        "multi": multi,
        "multi_elements": multi_elements,
        "extend_candidates_by_enum": extend_by_enum,
        "extend_candidates_syllabus_regs": extend_syllabus_regs,
        "extend_by_disclosed_kw_regs": extend_disclosed_regs,
        "extend_by_disclosed_neg_regs": extend_disclosed_neg_regs,
        "post_process_model_answers": post_process_model_answers,
        "as_follow_pattern": as_follow_pattern,
        "as_follow_type": as_follow_type,
        "skip_syllabus_title": skip_syllabus_title,
    }


def get_ps_yoda_sentence_model(
    ps_patterns: list[SearchPatternLike],
    skip_pattern: SearchPatternLike = MATCH_NEVER,
    enum=AnswerValueEnum.PS,
    multi=False,
    para_pattern: SearchPatternLike = None,
    threshold=0.0,
):
    return {
        "name": "yoda_layer",
        "threshold": threshold,
        "enum": enum,
        "rule": ScoreDestFilter(
            multi=multi,
            pattern=para_pattern or MATCH_ALWAYS,
            dest=SentenceFromSplitPara(
                multi=True,
                skip_pattern=skip_pattern,
                separator=P_PERIOD_SEPARATOR,
                ordered_patterns=ps_patterns,
            ),
        ),
    }
