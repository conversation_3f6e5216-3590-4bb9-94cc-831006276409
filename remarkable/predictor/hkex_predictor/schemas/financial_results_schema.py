"""
HKEX Financial Results Demo
"""

predictor_options = [
    {
        "path": ["Trading fees and trading tariffs"],
        "sub_primary_key": ["Time Period", "Currency", "Value"],
        "strict_group": True,
        "models": [
            {
                "name": "financial_result",
                "feature_white_list": [
                    r"$m|D_date_en|trading fees and trading tariff",
                    r"$m|nine months ended D_date_en|trading fees and trading tariff",
                ],
            },
        ],
    },
    {
        "path": ["Clearing and settlement fees"],
        "sub_primary_key": ["Time Period", "Currency", "Value"],
        "strict_group": True,
        "models": [
            {
                "name": "financial_result",
            },
        ],
    },
    {
        "path": ["Stock Exchange listing fees"],
        "sub_primary_key": ["Time Period", "Currency", "Value"],
        "strict_group": True,
        "models": [
            {
                "name": "financial_result",
            },
        ],
    },
]

prophet_config = {"depends": {}, "predictor_options": predictor_options}
