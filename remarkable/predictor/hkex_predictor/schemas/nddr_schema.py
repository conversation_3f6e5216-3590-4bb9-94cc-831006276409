from remarkable.common.common_pattern import R<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from remarkable.common.pattern import <PERSON><PERSON><PERSON><PERSON>, PositionPattern
from remarkable.predictor.common_pattern import <PERSON>_<PERSON><PERSON>_<PERSON>ONTH
from remarkable.predictor.hkex_predictor.schemas.pattern import R_MONEY_UNIT

P_PURCHASE_REPORT = MatchMulti.compile(r"\b(re)?purchase\s*report", operator=any)
R_DATE = rf"\b\d{{1,2}}\s*{R_EN_MONTH}\s*20\d{{2}}\b"
P_TREASURY_SHARE_HEADER = PositionPattern.compile(r"^closing\s*balance", R_DATE)
P_TREASURY_SHARES_KW = MatchMulti.compile(r"^(number|no\.)\s*of\s*treasury\s*shares", operator=any)


predictor_options = [
    ################################################################
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/5749#note_646792
    ################################################################
    {
        "path": ["Trading date"],
        "models": [
            {
                "name": "nddr_repurchase",
                "keyword_pattern": P_PURCHASE_REPORT,
                "col_pattern": PositionPattern.compile(r"^trading\s*date", R_DATE),
                "only_value": True,
            },
        ],
    },
    {
        "path": ["Method of repurchase"],
        "models": [
            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6281#note_663070
            # 识别太差，不能用列名来找，直接找整行，后面入库时再解析
            {
                "name": "nddr_repurchase",
                "keyword_pattern": P_PURCHASE_REPORT,
                # 交易所是两行 http://100.64.0.105:55647/#/project/remark/325276?treeId=51184&fileId=89581&schemaId=35&projectId=40
                "row_pattern": MatchMulti.compile(
                    rf"(?i:\d{{2}}\s*{R_EN_MONTH}\s*20\d{{2}}\s*[\d,]*\s*(o\s*n|b\s*y)).+?([Ee]xchange$|{R_CURRENCY}\s)",
                    operator=any,
                    flag=0,
                ),
                "multi": True,
                "start_row_pattern": MatchMulti.compile(r"^trading\s*date", operator=any),
                "stop_row_pattern": MatchMulti.compile(
                    r"^total\s*number\s*of\s*shares", r"(aggregate|total)\s*(price\s*)?paid", operator=any
                ),
                "only_value": True,
            },
        ],
    },
    {
        "path": ["Aggregate price paid"],
        "models": [
            {
                "name": "nddr_repurchase",
                "keyword_pattern": P_PURCHASE_REPORT,
                "row_pattern": PositionPattern.compile(r"(aggregate|total)\s*(price\s*)?paid", rf"{R_MONEY_UNIT}\s*\d"),
            },
        ],
    },
    {
        "path": ["Closing balance of treasury shares"],
        "models": [
            # 直接找包含closing balance as at关键词的整行
            # 记录：找不到列名： http://100.64.0.105:55647/#/project/remark/325204?treeId=51203&fileId=89509&schemaId=35&projectId=40
            {
                "name": "nddr_repurchase",
                # "keyword_pattern": P_TREASURY_SHARES_KW,
                # "find_kw_in_prev": True,
                "row_pattern": P_TREASURY_SHARE_HEADER,
                # "col_pattern": MatchMulti.compile(r"^(number|no\.)\s*of\s*treasury\s*shares", operator=any),
            },
            # {
            #     "name": "nddr_repurchase",
            #     "find_kw_in_prev": True,
            #     "keyword_pattern": P_TREASURY_SHARES_KW,
            #     "row_pattern": P_TREASURY_SHARE_HEADER,
            #     # "col_index": -3,
            # },
        ],
    },
]


prophet_config = {"depends": {}, "predictor_options": predictor_options}
