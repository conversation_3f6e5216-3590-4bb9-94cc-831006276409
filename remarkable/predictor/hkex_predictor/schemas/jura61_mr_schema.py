from typing import List

from remarkable.common.pattern import MatchMulti, PositionPattern

P_TITLE_ISSUED_SHARE = PositionPattern.compile(r"\b(?<!other )movement", r"\bissued share")
P_TITLE_TREASURY_SHARE = PositionPattern.compile(r"\b(?<!other )movement", r"\btreasury share")
P_NOT_APPLICABLE = MatchMulti.compile(r"Not applicable", operator=any)
P_PREFERENCE_SHARES = MatchMulti.compile(r"\b(preference|preferred) shares", operator=any)


def post_process(answers: List, **kwargs):
    """
    如果返回的share_class是Not applicable则将share_class column置空；如果share_class是preference share则删除掉这个答案
    """
    for answer in answers[::-1]:
        answer_result = answer["Relevant share class"][0]
        if P_NOT_APPLICABLE.search(answer_result.text):
            answer["Relevant share class"] = []
        elif P_PREFERENCE_SHARES.search(answer_result.text):
            answers.remove(answer)
    return answers


predictor_options = [
    {
        "path": ["Total number of issued shares"],
        "sub_primary_key": ["Relevant share class", "Value"],
        "strict_group": True,
        "post_process": post_process,
        "models": [
            {
                "name": "mr_shares_issued",
                "title_patterns": P_TITLE_ISSUED_SHARE,
            },
        ],
    },
    {
        "path": ["Number of treasury shares"],
        "sub_primary_key": ["Relevant share class", "Value"],
        "strict_group": True,
        "post_process": post_process,
        "models": [
            {
                "name": "mr_shares_issued",
                "title_patterns": P_TITLE_TREASURY_SHARE,
            }
        ],
    },
]


prophet_config = {"depends": {}, "predictor_options": predictor_options}
