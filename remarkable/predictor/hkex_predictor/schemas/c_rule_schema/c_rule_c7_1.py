import re
from collections import defaultdict
from itertools import chain

from remarkable.common.common import is_para_elt, is_table_elt
from remarkable.common.common_pattern import P_PERIOD_SEPARATOR, R_FOLLOW_PREFIX, R_PERIOD
from remarkable.common.constants import AnswerV<PERSON>ue<PERSON>num, PDFInsightClassEnum
from remarkable.common.pattern import (
    MatchMulti,
    NeglectPattern,
    PatternCollection,
    PositionPattern,
    SplitBeforeMatch,
)
from remarkable.common.util import split_paragraph
from remarkable.pdfinsight.reader import PdfinsightReader
from remarkable.predictor.common_pattern import R_DATES, R_EN_MONTH
from remarkable.predictor.hkex_predictor.models.c7 import (
    P_C7_CHAPTER_CT,
    P_C7_FOLLOW_PREFIX,
    P_C7_NO_CT,
    P_EXEMPT,
    R_CT,
    R_FALL_UNDER,
    R_FULLY_EXEMPT,
    R_IS_CT,
    R_RPT,
    R_TRANSACTIONS,
)
from remarkable.predictor.hkex_predictor.pattern import R_B<PERSON>
from remarkable.predictor.hkex_predictor.schemas.c_rule_schema.c_rule_c7 import (
    C7_ND_MODELS,
    P_AGREEMENT_SYLLABUS,
    P_C7_NEG_DISCLOSED,
    P_C7_NO_CCT,
    P_C7_NS,
    P_C7_NS_SKIP,
    P_C7_OTHER_CHAPTERS,
    R_AGREEMENT,
    R_ANY_DATE,
    R_C7_SKIP_SYLLABUS,
    R_CONTRACTUAL_ARRANGEMENTS,
    R_EFFECTED,
    R_ENTERED_INTO,
    R_MATERIAL_TRANSACTION,
    R_ON_DATE,
    c7_above_model,
    c7_contract_model,
    c7_ct_chapter_ps_model,
    c7_follow_model,
    extract_transaction_date,
    get_valid_syllabus,
    is_exempt_element,
    need_check_exempt_chapter,
    sort_c7_results,
)
from remarkable.predictor.hkex_predictor.schemas.pattern import (
    P_NOTE_CHAPTER,
    P_SAVE_AS_ABOVE,
    R_APPELLATION,
    R_ENG_NUMBER,
    R_NOT,
)
from remarkable.predictor.hkex_predictor.schemas.util import get_sentence_model
from remarkable.predictor.models.base_model import BaseModel
from remarkable.predictor.schema_answer import CharResult

# P_TERM_DATES = MatchMulti.compile(
#     *[rf"\b(from|to|util)\s*{dt}\b" for dt in R_DATES],
#     *[rf"{R_EFFECTED}\s*on\s*{dt}\b" for dt in R_DATES],
#     rf"{R_EFFECTED}\s*from\s*the\s*listing\s*date",
#     operator=any,
# )
P_DATES = MatchMulti.compile(*R_DATES, operator=any)
# P_C71_PS = SplitBeforeMatch.compile(
#     MatchMulti.compile(P_CONNECTED_TRANSACTIONS, P_ON_DATES, operator=all), separator=P_PERIOD_SEPARATOR
# )
R_C7_COMPANY = rf"(\b(company|group|board|subsidiary)|{R_APPELLATION})"
R_C7_REVISE = r"\b(revised|amended|amendment|restated)"
P_C71_PS = [
    r"\senter(ed)?\s*(into\s*)?(an?|the)\s+([^,，\s]+\s+){1,10}(agreement|plan)",
    r"\sentered\s*into\s*(a|the)\s*new\s",
    MatchMulti.compile(R_ENTERED_INTO, rf"{R_AGREEMENT}(\s*[IV]{{1,4}})?(\s*[(（][^)）]+[)）])?[“”\"]", operator=all),
    PositionPattern.compile(R_C7_COMPANY, R_ENTERED_INTO, R_AGREEMENT, r"\swith\s(?!other|one\s*connected)"),
    # http://************:55647/#/project/remark/265194?treeId=45544&fileId=70253&schemaId=18&projectId=17&schemaKey=C7.1
    MatchMulti.compile(rf"\b(together\s*with|between|among)|{R_ON_DATE}", R_ENTERED_INTO, R_AGREEMENT, operator=all),
    # PositionPattern.compile(rf"{R_AGREEMENT}", r"was|were", R_ENTERED_INTO),
    PositionPattern.compile(rf"{R_C7_COMPANY}\s*and\s", R_ENTERED_INTO, R_AGREEMENT),
    # http://************:55647/#/project/remark/265849?treeId=10898&fileId=70384&schemaId=18&projectId=17&schemaKey=C7.1
    PositionPattern.compile(
        r"([\"“”][)）]|\b(ltd|co|etc|limited|corp|inc)[.，,]?)\s+(entered\s*into|sign|renew)", R_AGREEMENT
    ),
    # 行头中的信息
    rf"(^|{R_FOLLOW_PREFIX})(lease|agreements?)?\s*(date|term)[:：]",
    # http://************:55647/#/project/remark/265134?treeId=10062&fileId=70241&schemaId=18&projectId=17&schemaKey=C7.1
    rf"{R_FOLLOW_PREFIX}.*?{R_AGREEMENT}\s*of\s.+?[:：]\s+on\s*{R_ANY_DATE}",
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6027#note_652987
    # http://************:55647/#/project/remark/293981?projectId=17&treeId=4072&fileId=70708&schemaId=18
    # http://************:55647/#/project/remark/265589?treeId=24500&fileId=70332&schemaId=18&projectId=17&schemaKey=C7.1
    # http://************:55647/#/hkex/annual-report-checking/report-review/261315?fileId=69478&schemaId=5&rule=C7.1&delist=0 index=2818
    rf"^on\s*{R_ANY_DATE}.+the\s*(company|group)\s*(sell|sold|resolved\s*to\s*grant|advanced\s*a\s*loan)",
    rf"{R_AGREEMENT}\s*(was|were)\s*(made|making)",
    NeglectPattern.compile(
        match=PositionPattern.compile(r"^Pursuant\s*to", rf"{R_AGREEMENT}\s*([,，]|entered)", R_ENTERED_INTO),
        unmatch=r"shall|will|should",
    ),
    # http://************:55647/#/hkex/annual-report-checking/report-review/340939?fileId=104142&schemaId=5&rule=C7.1&delist=0 index=954
    PositionPattern.compile(rf"^on\s*{R_ANY_DATE}", R_ENTERED_INTO, r"pursuant\s*to\s*which"),
    MatchMulti.compile(r"reference", R_ON_DATE, r"[:：]$", operator=all),
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6593#note_688452
    rf"{R_AGREEMENT}\s*[\[(（].*?{R_AGREEMENT}.*?[）)\]]\s*{R_BE}\s*(made|making)",
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6593#note_688452
    PositionPattern.compile(rf"{R_FOLLOW_PREFIX}an?\s", r"agreement", r"was\s*made"),
    # https://jura6-lir.paodingai.com/#/hkex/annual-report-checking/report-review/260712?fileId=69357&schemaId=5&rule=C7.2&delist=0
    PositionPattern.compile(rf"^[（(](\d{{1,2}}|[a-z])[）)](\s*[(（][a-z][)）])?\s*on\s*{R_ANY_DATE}", R_ENTERED_INTO),
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6593#note_689724
    PositionPattern.compile(r"\sin\s*relation\s*to\s*the\s*entering\s*into", R_AGREEMENT),
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6593#note_689724
    PositionPattern.compile(rf"^on\s*{R_ANY_DATE}", rf"{R_ENTERED_INTO}|approved", R_AGREEMENT),
]

# 协议有效期间 TODO 在后处理中提取
P_C71_TERM = PatternCollection(
    [
        rf"(?P<content>\b(remain|(\ba\s*|\bthe\s*)?term|(\ba\s*|the\s*)?period|effect(ed|ive)?)\s+.*?\b(from|to|util|on)\s*{R_ANY_DATE}(\s*([a-z]+\s+){{1,4}}\s*{R_ANY_DATE})?)",
        rf"(?P<content>\bcommenc(ed|ing)\s*(from|to|util|on)\s*{R_ANY_DATE}(\s*([a-z]+\s+){{1,4}}\s*{R_ANY_DATE})?)",
        rf"(?P<content>\b(remain|term|period\s*of|expired?|lapsed?)\s.*?({R_ENG_NUMBER}|\d{{1,3}})\s+(days|weeks|months|years)\b)",
    ],
    flags=re.I,
)
P_C71_TERM_SKIP = MatchMulti.compile(
    r"\b(limit|exceed)",
    r"\bfor\s*the\s*year\s*ended",
    r"expected\s*(not\s*)?to",
    r"\b(polic(y|ies)|notice|\sPRC\s|regulatory)",
    operator=any,
)


P_ENTERED_INTO = MatchMulti.compile(
    r"entered\s*into\s|sign|renew",
    rf"^on\s(\d{{1,2}}(st|nd|rd|th)?|{R_EN_MONTH})\s",
    r"\bdate[:：]",
    R_EFFECTED,
    rf"\sfrom\s*{R_ANY_DATE}",
    operator=any,
)
P_C71_SKIP = MatchMulti.compile(
    # P_FOLLOW_CT,
    r"complied\s*with",
    # http://************:55647/#/project/remark/294512?treeId=3847&fileId=70786&schemaId=18&projectId=17&schemaKey=C7.1
    r"confirmed|reviewed",
    # http://************:55647/#/project/remark/268543?treeId=38006&fileId=70554&schemaId=18&projectId=17&schemaKey=C7.1
    # https://jura6-lir.paodingai.com/#/hkex/annual-report-checking/report-review/261202?fileId=69455&schemaId=5&rule=C7.2&delist=0 index=776
    NeglectPattern.compile(
        match=r"following|below|\sexempt|[:：]$", unmatch=rf"{R_ENTERED_INTO}|{R_ON_DATE}|detail|^Also[，,]"
    ),
    # # http://************:55647/#/project/remark/266124?treeId=37674&fileId=70439&schemaId=18&projectId=17&schemaKey=C7.1 index=709
    # rf"entered\s*into\s*one\s*{R_CT}",
    # http://************:55647/#/project/remark/266319?treeId=19167&fileId=70478&schemaId=18&projectId=17&schemaKey=C7.1 idnex=2677
    r"with\s*other\s*related",
    # http://************:55647/#/project/remark/293526?treeId=15473&fileId=70642&schemaId=18&projectId=17&schemaKey=C7.1
    r"^given\s*that",
    # https://jura6-lir.paodingai.com/#/hkex/annual-report-checking/report-review/260937?fileId=69402&schemaId=5&rule=C7.1 index=533,541
    r"^consider(ed)?\s*that|^since\s|\b(that|the)\s*entering\s*into",
    P_SAVE_AS_ABOVE,
    # http://************:55647/#/hkex/annual-report-checking/report-review/261487?fileId=69512&schemaId=5&rule=C7.1&delist=0&page=63
    # http://************:55647/#/hkex/annual-report-checking/report-review/341441?fileId=104206&schemaId=5&rule=C7.5&delist=0 index=2088
    NeglectPattern.compile(
        match=r"\s(in|under)\snote\s\d|percentage\s*ratio|notwithstanding", unmatch=r"\bentered\s*into"
    ),
    # https://jura6-lir.paodingai.com/#/hkex/annual-report-checking/report-review/263877?fileId=69990&schemaId=5&rule=C7.1&delist=0 index=565
    r"^as\s*a\s*transitional\s*arrangement",
    # https://jura6-lir.paodingai.com/#/hkex/annual-report-checking/report-review/340835?fileId=104129&schemaId=5&rule=C7.1&delist=0 index=1475
    r"^as\s*at\s*the\s*date\s*of\s*entering\s*into",
    # https://jura6-lir.paodingai.com/#/hkex/annual-report-checking/report-review/340939?fileId=104142&schemaId=5&rule=C7.1&delist=0 index=814
    r"^as\s*part\s*of\s*the\s*",
    # https://jura6-lir.paodingai.com/#/hkex/annual-report-checking/report-review/261487?fileId=69512&schemaId=5&rule=C7.1&delist=0 index=769
    r"\s*agreements\s*to\s*be\s*entered\s*into",
    operator=any,
)
P_C71_SEN_SEPARATOR = re.compile(rf"\s*({R_PERIOD}\s*|[;；]\s*|(?<!\d)([（(]([a-z]|[ivx]{{1,4}})[)）]\s))")

# 不能排除amount章节，因为误识别： https://jura6-lir.paodingai.com/#/hkex/annual-report-checking/report-review/259937?fileId=69202&schemaId=5&rule=C7.1&delist=0 index=178
P_C71_SKIP_SYLLABUS = MatchMulti.compile(
    *R_C7_SKIP_SYLLABUS,
    # http://************:55647/#/project/remark/265979?treeId=45550&fileId=70410&schemaId=18&projectId=17&schemaKey=C7.1
    r"\b(reason|purpose|price|pricing|annual\s*cap|description)",
    r"requirements|polici(y|es)|^parties",
    r"connected\s*persons\s*(of|at)",
    operator=any,
)
# http://************:55647/#/project/remark/295383?treeId=11713&fileId=70898&schemaId=18&projectId=17&schemaKey=C7.1
# https://jura6-lir.paodingai.com/#/hkex/annual-report-checking/report-review/260377?fileId=69290&schemaId=5&rule=C7.2&delist=0
R_C71_HEADER_TRANSACTIONS = r"^transactions?(?!\s*part)($|[（(]|\d)"
R_NON_CT_CHAPTERS = rf"{R_CONTRACTUAL_ARRANGEMENTS}|{R_MATERIAL_TRANSACTION}|{R_RPT}"


def add_c7_1_term_results(element_results, pdfinsight: PdfinsightReader, model: BaseModel):
    new_results = []
    all_indices = sorted(r.element["index"] for r in element_results)
    for index, next_index in zip(all_indices, all_indices[1:] + [all_indices[-1] + 10]):
        # 可能有多个
        current_results = sort_c7_results([r for r in element_results if r.index == index])
        new_results.extend(r for r in current_results if r not in new_results)
        if any(P_C71_TERM.nexts(r.text) for r in current_results):
            # 结果中包含了term信息，无需再追加
            continue
        # 在当前元素块和下一个元素块之间找term信息，注意遇到章节则终止
        for element in model.get_candidate_elements_by_range(
            [index, next_index], aim_types={PDFInsightClassEnum.PARAGRAPH.value}, need_score=False, include_start=True
        ):
            if pdfinsight.syllabus_reader.is_syllabus_elt(element):
                break
            # 跳过豁免描述
            text = element.get("text") or ""
            if not text or P_EXEMPT.search(text):
                continue
            for sentence, (sen_start, _) in split_paragraph(text, need_pos=True):
                if P_C71_TERM_SKIP.search(sentence):
                    continue
                if matched := P_C71_TERM.nexts(sentence):
                    start, end = [sen_start + i for i in matched.span("content")]
                    # 增加flag=‘term’，确保不影响分组
                    new_results.append(
                        CharResult(element, element["chars"][start:end], start=start, end=end, flag="term")
                    )
                    break
    return sort_c7_results(new_results)


def set_flag_to_c71_results(element_results):
    """
    为C7.1的答案设置flag=c7.1
    """
    for element_result in element_results:
        element_result.flag = "c7.1"


def post_process_c7_1_answer(answers, **kwargs):
    """
    TODO CT下多个章节或多个序号，后处理补全信息
    """
    if not answers:
        return answers
    schema, year_end = kwargs.get("schema"), kwargs.get("year_end")
    model, pdfinsight = kwargs.get("model"), kwargs.get("pdfinsight")
    ps_answer_dict, ns_answer_dict = defaultdict(list), defaultdict(list)
    ps_answer_syll_dict, ns_answer_syll_dict = defaultdict(list), defaultdict(list)
    common_results = BaseModel.get_common_predictor_results(answers)
    if len(common_results) == 1 and common_results[0].answer_value != AnswerValueEnum.PS.value:
        # 只有一个非PS答案，直接返回
        return answers
    if all(r.answer_value == AnswerValueEnum.NS.value for r in common_results):
        # 都是NS答案，直接返回
        return answers

    ps_results, ns_results = [], []
    if not any(r.answer_value == AnswerValueEnum.NS.value for r in common_results):
        # 只有PS答案
        ps_results = list(chain.from_iterable(r.element_results for r in common_results))
    else:
        # 记录PS和NS答案各自所在的元素块
        for answer in common_results:
            if answer.answer_value == AnswerValueEnum.PS.value:
                for element_result in answer.element_results:
                    ps_answer_dict[element_result.element["index"]].append(element_result)
            elif answer.answer_value == AnswerValueEnum.NS.value:
                for element_result in answer.element_results:
                    ns_answer_dict[element_result.element["index"]].append(element_result)
        # 先找出在同一个元素块的PS+NS
        found_ns_indices = set()
        for idx, elt_results in sorted(ps_answer_dict.items(), key=lambda x: x[0]):
            if idx in ns_answer_dict:
                # 元素块有NS描述，取NS
                found_ns_indices.add(idx)
                ns_results.extend(ns_answer_dict[idx])
            else:
                # 元素块没有NS描述，取PS
                ps_results.extend(elt_results)
        if not ps_results:
            # 只取到NS，说明每个PS都有对应NS，直接返回所有NS
            return [model.create_result(sort_c7_results(ns_results), value=AnswerValueEnum.NS.value, schema=schema)]
        # 记录其余PS和NS答案各自所在的章节
        for element_result in ps_results:
            if syllabus := get_valid_syllabus(pdfinsight, element_result.element):
                ps_answer_syll_dict[syllabus["index"]].append(element_result)
        for element_result in ns_results:
            if element_result.element["index"] in found_ns_indices:
                continue
            if syllabus := get_valid_syllabus(pdfinsight, element_result.element, is_ns_answer=True):
                ns_answer_syll_dict[syllabus["index"]].append(element_result)
        # 按照所在章节来找PS与NS对应关系
        if ps_answer_syll_dict and ns_answer_syll_dict:
            for idx, elt_results in sorted(ps_answer_syll_dict.items(), key=lambda x: x[0]):
                if (
                    (ns_res := ns_answer_syll_dict[idx])
                    and len(ns_res) == 1
                    and ns_res[0].element["index"] > elt_results[-1].element["index"]
                ):
                    # 找到PS和NS对应关系的交易，不提取
                    for elt_result in elt_results:
                        ps_answer_dict.pop(elt_result.element["index"], None)
                    ns_results.extend(ns_answer_syll_dict[idx])
                else:
                    # 章节下没有NS描述，则取PS
                    ps_results.extend(elt_results)
        # 每个PS所属章节最后都有NS描述，则判定为NS
        if not ps_results:
            return (
                [model.create_result(sort_c7_results(ns_results), value=AnswerValueEnum.NS.value, schema=schema)]
                if ns_results
                else []
            )

    real_ps_results, nd_results = [], []
    check_exempt = need_check_exempt_chapter(pdfinsight)
    for ps_result in sorted(ps_results, key=lambda x: x.element["index"]):
        # 表格不限制日期
        ps_element = ps_result.element
        if is_table_elt(ps_element):
            real_ps_results.append(ps_result)
            continue
        # 如果属于豁免章节的following，需要排除
        # https://jura6-lir.paodingai.com/#/hkex/annual-report-checking/report-review/340939?fileId=104142&schemaId=5&rule=C7.1&delist=0
        if check_exempt and is_exempt_element(pdfinsight, ps_result.element):
            continue
        # 没有日期信息，则为ND
        # if not P_DATES.search(ps_result.text):
        #     nd_results.append(ps_result)
        #     continue
        transaction_date = extract_transaction_date(ps_result.text)
        if transaction_date:
            # 过滤期后交易
            if year_end and transaction_date > year_end:
                continue
            real_ps_results.append(ps_result)
            continue
        # 提取内容前面带章节序号，且没有提取到有效的日期，则判定为ND
        # http://************:55647/#/project/remark/265504?treeId=38029&fileId=70315&schemaId=18&projectId=17&schemaKey=C7.1
        if is_para_elt(ps_element) and P_C7_FOLLOW_PREFIX.search(ps_element["text"]):
            nd_results.append(ps_result)
            continue
        # # 上一个元素块是章节，且没有提取到有效的日期，则判定为ND TODO 后续视情况应该判断章节范围内无有效日期
        # if (
        #     syllabus := pdfinsight.syllabus_reader.elt_syllabus_dict.get(ps_element["index"] - 1)
        # ) and P_AGREEMENT_SYLLABUS.search(syllabus["title"]):
        #     nd_results.append(ps_result)
        #     continue
        real_ps_results.append(ps_result)
        # TODO 其他提取段落中，没有有效的日期，也应该ND，但是由于误判太多，暂不实现
    if nd_results:
        set_flag_to_c71_results(real_ps_results + nd_results)
        return [
            model.create_result(
                sort_c7_results(real_ps_results + nd_results), value=AnswerValueEnum.ND.value, schema=schema
            )
        ]
    if real_ps_results:
        # 这里追加term信息
        set_flag_to_c71_results(real_ps_results)
        results = add_c7_1_term_results(real_ps_results, pdfinsight, model)
        return [model.create_result(results, value=AnswerValueEnum.PS.value, schema=schema)]
    # 提到的PS答案可能是期后答案，这里如果有NS就取NS
    if not ns_answer_dict:
        return []
    return [
        model.create_result(
            sort_c7_results(chain.from_iterable(ns_answer_dict.values())),
            value=AnswerValueEnum.NS.value,
            schema=schema,
        )
    ]


def c71_filter_near_by(pdfinsight: PdfinsightReader, start_element, elements: list, step: int):
    """
    使用extend_by_near_regs可能会找到过多元素块，这里对元素块进行过滤
    """
    if not elements:
        return elements
    # 必须是一个章节
    syllabus = pdfinsight.syllabus_reader.elt_syllabus_dict.get(start_element["index"])
    index_range = syllabus["range"]
    if children := syllabus["children"]:
        # 如果有子章节，找第一个不排除的子章节作为候选范围
        for idx in children:
            child_syllabus = pdfinsight.syllabus_reader.syllabus_dict[idx]
            if P_C71_SKIP_SYLLABUS.search(child_syllabus["title"]):
                continue
            index_range = child_syllabus["range"]
    if not index_range:
        return []
    for index in range(index_range[0] + 1, index_range[1]):
        _, element = pdfinsight.find_element_by_index(index)
        if not element or pdfinsight.is_skip_element(
            element, aim_types={PDFInsightClassEnum.PARAGRAPH.value}, skip_chinese=True, skip_tbl_unit=True
        ):
            continue
        return [element]
    return []


def agreement_syllabus_near_model(syllabus_regs):
    """
    取指定章节下，标题为xx agreement等内容下的第一个包含entered into等关键词的段落第一句
    # https://jura6-lir.paodingai.com/#/hkex/annual-report-checking/report-review/260377?fileId=69290&schemaId=5&rule=C7.2&delist=0
    """
    return {
        "name": "near_paras",
        "model_id": "ps_para_4",
        "enum": AnswerValueEnum.PS.value,
        "multi_elements": True,
        "multi": False,
        "para_separator": P_PERIOD_SEPARATOR,
        "start_is_syllabus": True,
        "syllabus_regs": syllabus_regs,
        "neglect_syllabus_regs": P_C71_SKIP_SYLLABUS,
        "extend_candidates_syllabus_regs": syllabus_regs,
        "start_regs": P_AGREEMENT_SYLLABUS,
        "near_amount": 1,
        "paragraph_pattern": [R_ENTERED_INTO, R_C7_REVISE, R_AGREEMENT],
        "neglect_sentence_pattern": P_C71_SKIP,
        "neglect_pattern": rf"\snot\s*constituted?\s*{R_CT}",
        "near_by_filter": c71_filter_near_by,
    }


predictor_options = [
    {
        "path": ["C7.1"],
        "element_candidate_count": 20,
        "models": [
            # 1. 确定的NS描述
            {"name": "c7ns"},
            # 2. ND场景： CT/CCT中提及RPT都不是CT，但RPT章节下有构成CT的描述，为ND
            # 3. ND场景： CT或CCT下说明部分RPT构成CT，详见xx章节，但在xx章节没有提取到内容
            *C7_ND_MODELS,
            # TODO # http://************:55647/#/project/remark/265134?treeId=10062&fileId=70241&schemaId=18&projectId=17&schemaKey=C7.1 page=100
            # 4. 全文找PS + NS
            {
                "name": "multi_models",
                "operator": "union",
                "enum_every_element": True,
                "sort_by_elt_index": True,
                "post_process_model_answers": post_process_c7_1_answer,
                "models": [
                    {
                        "name": "multi_models",
                        "operator": "union",
                        "enum": AnswerValueEnum.PS.value,
                        "enum_every_element": True,
                        # PS元素块去重
                        "require_unique_element": True,
                        "deduplicate_elements": True,
                        "models": [
                            # PS-1: 优先在CT章节下找交易
                            c7_ct_chapter_ps_model(
                                P_C71_PS,
                                P_C71_SKIP,
                                neglect_syllabus_regs=P_C71_SKIP_SYLLABUS,
                                model_id="ps_para_1",
                                # skip_syllabus_title=True,
                            ),
                            # PS-2: 提取表格中的日期
                            {
                                "name": "special_cells",
                                "model_id": "ps_table_1",
                                "extend_candidates_syllabus_regs": P_C7_CHAPTER_CT,
                                "multi_elements": True,
                                "multi": True,
                                "remove_cn_text": True,
                                "syllabus_regs": P_C7_CHAPTER_CT,
                                "neglect_syllabus_regs": P_C71_SKIP_SYLLABUS,
                                "need_continuous": False,
                                # "neglect_parent_features": R_CG_CHAPTER_TITLES,
                                "row_col_relation": "or",
                                # http://************:55647/#/project/remark/294526?treeId=23578&fileId=70788&schemaId=18&projectId=17&schemaKey=C7.1
                                "row_header_pattern": MatchMulti.compile(r"\b(date|term)\b", operator=any),
                                "col_header_pattern": [
                                    MatchMulti.compile(
                                        r"\b(date|terms?)\b",
                                        # http://************:55647/#/project/remark/295773?treeId=10817&fileId=70947&schemaId=18&projectId=17&schemaKey=C7.1
                                        r"\b(lease\s*)?term\s*of\s.+?agreement",
                                        R_C71_HEADER_TRANSACTIONS,
                                        operator=any,
                                    )
                                ],
                                "cell_pattern": [R_ANY_DATE, R_ENTERED_INTO],
                                "enum": AnswerValueEnum.PS.value,
                            },
                            # PS-2.1 Structured\s*contracts章节下内容全取
                            # https://jura6-lir.paodingai.com/#/hkex/annual-report-checking/report-review/341314?fileId=104190&schemaId=5&rule=C7.1&delist=0
                            agreement_syllabus_near_model(r"Structured\s*contracts"),
                            # PS-3: 只要提及contractual arrangements等构成CT，则在contractual arrangements章节提取
                            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6586#note_700702
                            # http://************:55647/#/hkex/annual-report-checking/report-review/263863?fileId=69987&schemaId=18&rule=C7.1&delist=0
                            # https://jura6-lir.paodingai.com/#/hkex/annual-report-checking/report-review/263862?fileId=69987&schemaId=5&rule=C7.2&delist=0
                            c7_contract_model(
                                [
                                    get_sentence_model(
                                        P_C71_PS,
                                        multi_elements=True,
                                        syllabus_regs=R_CONTRACTUAL_ARRANGEMENTS,
                                        neglect_syllabus_regs=[
                                            P_C71_SKIP_SYLLABUS,
                                            r"\b(policy|policies|terms)",
                                        ],
                                        extend_syllabus_regs=R_CONTRACTUAL_ARRANGEMENTS,
                                        skip_pattern=P_C71_SKIP,
                                        model_id="ps_para_3",
                                        # skip_syllabus_title=True,
                                    ),
                                    # model_id=ps_para_4
                                    agreement_syllabus_near_model(R_CONTRACTUAL_ARRANGEMENTS),
                                ]
                            ),
                            # PS-4: 找following CT之后的章节+元素块
                            # http://************:55647/#/project/remark/265979?treeId=45550&fileId=70410&schemaId=18&projectId=17&schemaKey=C7.1
                            # http://************:55647/#/project/remark/265989?treeId=8711&fileId=70412&schemaId=18&projectId=17&schemaKey=C7.1
                            c7_follow_model(
                                P_C71_PS,
                                P_C71_SKIP,
                                # para_separator=P_C71_SEN_SEPARATOR,
                                neglect_syllabus_regs=[P_C71_SKIP_SYLLABUS, R_CONTRACTUAL_ARRANGEMENTS],
                                model_id="ps_para_5",
                            ),
                            # PS-5: 全文找xx also CT的描述，然后提取这个段落之前的描述
                            # http://************:55647/#/project/remark/265064?treeId=5762&fileId=70227&schemaId=18&projectId=17&schemaKey=C7.1
                            # http://************:55647/#/project/remark/265504?treeId=38029&fileId=70315&schemaId=18&projectId=17&schemaKey=C7.1
                            # http://************:55647/#/project/remark/266358?treeId=5331&fileId=70486&schemaId=18&projectId=17&schemaKey=C7.1
                            # http://************:55647/#/project/remark/293773?treeId=3965&fileId=70678&schemaId=18&projectId=17&schemaKey=C7.1
                            # todo: http://************:55647/#/hkex/annual-report-checking/report-review/266042?fileId=70423&schemaId=5&rule=C7.1&delist=0
                            c7_above_model(
                                P_C71_PS,
                                P_C71_SKIP,
                                # para_separator=P_C71_SEN_SEPARATOR,
                                neglect_syllabus_regs=[P_C71_SKIP_SYLLABUS, R_CONTRACTUAL_ARRANGEMENTS],
                                model_id="ps_para_6",
                            ),
                            # PS-6: note章节下找xx构成CT的段落，也认为是一次交易
                            {
                                "name": "para_match",
                                "model_id": "ps_para_7",
                                "enum": AnswerValueEnum.PS.value,
                                "multi_elements": True,
                                "syllabus_regs": R_RPT,
                                "parent_features": P_NOTE_CHAPTER,
                                "parent_must_be_root": True,
                                # "para_separator": P_C71_SEN_SEPARATOR,
                                "paragraph_pattern": [R_IS_CT],
                                "neglect_pattern": [R_NOT, P_EXEMPT, rf"\sexempt\s*{R_CT}"],
                            },
                        ],
                    },
                    {
                        "name": "multi_models",
                        "operator": "union",
                        "enum_every_element": True,
                        "enum": AnswerValueEnum.NS.value,
                        # NS元素块去重
                        "require_unique_element": True,
                        "deduplicate_elements": True,
                        "models": [
                            # NS-1: NS模型： CT章节下，可以不排除save as
                            get_sentence_model(
                                P_C7_NS,
                                multi_elements=True,
                                syllabus_regs=P_C7_CHAPTER_CT,
                                neglect_syllabus_regs=P_C71_SKIP_SYLLABUS,
                                extend_syllabus_regs=P_C7_CHAPTER_CT,
                                extend_disclosed_regs=R_IS_CT,
                                # 注意： 如果提及详见xxx章节，但又提及这些交易都被豁免，则不跳转详情章节
                                extend_disclosed_neg_regs=P_C7_NEG_DISCLOSED,
                                # neg_pattern=MatchMulti.compile(R_SAVE_AS, r"(headed|under|in)\s*(the\s*)?([\"“”]|notes?\s*\d+)", operator=any),
                                enum=AnswerValueEnum.NS.value,
                                # 排除： subject to
                                skip_pattern=P_C7_NS_SKIP,
                                model_id="ns_para_1",
                            ),
                            # NS-2: NS模型：非CT章节下，提取no ct或no cct
                            get_sentence_model(
                                # http://************:55647/#/project/remark/268790?treeId=20335&fileId=70590&schemaId=18&projectId=17&schemaKey=C7.1
                                [P_C7_NO_CT, *P_C7_NO_CCT],
                                multi_elements=True,
                                syllabus_regs=[P_C7_OTHER_CHAPTERS],
                                neglect_syllabus_regs=[P_C7_CHAPTER_CT, P_C71_SKIP_SYLLABUS],
                                extend_syllabus_regs=[P_C7_OTHER_CHAPTERS],
                                enum=AnswerValueEnum.NS.value,
                                # 排除： https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6643#note_692122
                                skip_pattern=P_C7_NS_SKIP,
                                model_id="ns_para_2",
                            ),
                            # NS-3: NS模型：非CT章节下，提取xx构成CT + EXEMPT
                            get_sentence_model(
                                MatchMulti.compile(R_CT, P_EXEMPT, operator=all),
                                multi_elements=True,
                                syllabus_regs=[P_C7_OTHER_CHAPTERS],
                                neglect_syllabus_regs=[P_C7_CHAPTER_CT, P_C71_SKIP_SYLLABUS],
                                extend_syllabus_regs=[P_C7_OTHER_CHAPTERS],
                                enum=AnswerValueEnum.NS.value,
                                # 排除： https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6643#note_692122
                                neg_pattern=SplitBeforeMatch.compile(
                                    MatchMulti.compile(*P_C7_NS_SKIP, operator=any), separator=P_PERIOD_SEPARATOR
                                ),
                                para_separator=None,
                                model_id="ns_para_3",
                            ),
                        ],
                    },
                ],
            },
            # 最后再判断一次NS
            {
                "name": "para_match",
                "enum": AnswerValueEnum.NS.value,
                "syllabus_regs": P_C7_CHAPTER_CT,
                "para_separator": P_PERIOD_SEPARATOR,
                "sentence_pattern": [
                    P_C7_NO_CT,
                    *P_C7_NO_CCT,
                    PositionPattern.compile(R_NOT, r"\bnon-exempt", R_CT),
                    rf"(?<!no[tr] )(?<!no )(?<!include )(?<!including ){R_FULLY_EXEMPT}",
                ],
            },
            # http://************:55647/#/hkex/annual-report-checking/report-review/306564?fileId=71411&schemaId=5&rule=C7.1&delist=0&page=45 stock=00591, year=2024, index=632
            {
                "name": "para_match",
                "enum": AnswerValueEnum.NS.value,
                "syllabus_regs": P_C7_CHAPTER_CT,
                "paragraph_pattern": [
                    PositionPattern.compile(
                        rf"{R_NOT}",
                        r"\bother transaction",
                        r"\.\s+",
                        R_TRANSACTIONS,
                        rf"{R_NOT}{R_FALL_UNDER}",
                        R_CT,
                    ),
                ],
            },
        ],
    },
]

if __name__ == "__main__":
    para = "On 20 December 2024, Shenzhen Yuanqi, Hainan Pusen and Nanjing Jingyuming entered into a supplemental agreement to the new Structured Contracts with Huihuang Company, Yun Ai Group and the New Registered Shareholders to reflect the latest arrangement in the provision of the consulting services under the Business Cooperation Agreement (2024) and the Exclusive Technical Service and Management Consultancy Agreement (2024) (the “Tenth Supplemental Agreement”)."

    p = PatternCollection(P_C71_PS, re.I)
    print(p.nexts(para))
