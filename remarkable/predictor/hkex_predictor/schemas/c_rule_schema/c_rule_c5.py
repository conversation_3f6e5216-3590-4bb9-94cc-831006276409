"""
Jura2.1 Jura 2.0 Additional Rules
"""

import logging
import re
from typing import List

from remarkable.common.common_pattern import P_SEN_SEPARATOR
from remarkable.common.constants import AnswerValueEnum
from remarkable.common.pattern import Match<PERSON>ult<PERSON>, SplitBeforeMatch
from remarkable.common.util import clean_txt
from remarkable.predictor.hkex_predictor.models.c5 import P_NS, P_NS_WITHOUT_CONTINUE, R_CCT
from remarkable.predictor.models.base_model import BaseModel
from remarkable.predictor.schema_answer import PredictorResult

logger = logging.getLogger(__name__)

R_RPT = r"related party transactions"
R_DO_NOT = r"(were|did|do) not"

P_RPT = re.compile(R_RPT)

P_RPT_NOT_BELONG_CCT = SplitBeforeMatch(
    MatchMulti.compile(
        rf"{R_RPT}.*{R_DO_NOT}.*{R_CCT}",
        rf"{R_RPT}.*{R_DO_NOT}.*connected transaction",  # CCT 没有第一个C 少数情况
        operator=any,
    ),
    separator=P_SEN_SEPARATOR,
    operator=any,
)


def delete_special_answer(answers: List[dict | PredictorResult], **kwargs) -> List[dict]:
    # 必须要有明确披露不存在CCT，
    # RPT不属于CCT的描述应该判断为ND, 不能判断为NS
    origin_enums = BaseModel.get_enums_from_answer_result(answers)
    if any(i == AnswerValueEnum.ND.value for i in origin_enums):
        logger.debug("find ND answer, return []")
        return []
    elements = BaseModel.get_elements_from_answer_result(answers)
    all_text = clean_txt("\n".join(element.get("text", "") for element in elements))
    if not P_RPT.search(all_text):
        # 没有 RPT 提前返回
        return answers
    for element in elements:
        clean_text = clean_txt(element.get("text", ""))
        for sentence in clean_text.split("."):
            if P_NS.search(sentence):
                return answers
            if P_RPT_NOT_BELONG_CCT.search(sentence):
                logger.debug(f"delete_special_answer for C5, {sentence}")
                return []
    return answers


predictor_options = [
    # -----------------------------------------------
    # 披露公司聘用了审计师针对CCT发布信函给董事会确认该交易符合相关监管的(1)-(4)要求
    # 定位：多在directors’report中的continuingconnectedtransaction,CCT标题下
    # 关键词：(continuing)connectedtransaction,CCT,auditor,(1)-(4)
    # 枚举值判断：
    #   •PS：披露了上述相关内容
    #   •NS：公司声明没有continuingconnectedtransactions
    #   •ND：全文没有CCT相关段落（ND,C),或有CCT相关段落但没有披露相关内容（ND,NC)
    # -------------------------------------------------
    {
        "path": ["C5"],
        "post_process": delete_special_answer,
        "models": [
            # 1.
            {
                "name": "c5",
                "filter_low_score_threshold": 0.08,
                "neglect_pattern": [
                    r"^DIRECTORS’\s?REPORT$",
                ],
            },
            # 2.关联交易章节下的
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__^Connected Transactions$",
                    r"__regex__^CONTINUING CONNECTED TRANSACTIONS$",
                    # r"__regex__RELATED PARTY TRANSACTIONS$",
                ],
                "only_inject_features": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        P_NS,
                        # r"There was no connected transaction",
                        # r"\(a\)\(i\) above also constitute connected transactions",
                    ),
                    "neglect_pattern": (r"Save as disclosed above",),
                },
                "table_model": "empty",
            },
            # 3. 从初步定位中 提取没有包含 constitute|continuing 的段落
            {
                "name": "para_match",
                "paragraph_pattern": [
                    SplitBeforeMatch(
                        P_NS_WITHOUT_CONTINUE,
                        separator=P_SEN_SEPARATOR,
                        operator=any,
                    ),
                ],
            },
            # 4. 其他章节下 有可能没有包含  constitute|continuing
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__RELATED PARTY TRANSACTIONS",
                    r"__regex__Connected Transactions",
                ],
                "only_inject_features": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        P_NS_WITHOUT_CONTINUE,
                        # r"There was no connected transaction",
                        r"\(a\)\(i\) above also constitute connected transactions",
                    ),
                    "neglect_pattern": (r"Save as disclosed above",),
                },
                "table_model": "empty",
            },
            # 5.
            {
                "name": "score_filter",
                "threshold": 0.9,
            },
        ],
    },
]

prophet_config = {"depends": {}, "predictor_options": predictor_options}
