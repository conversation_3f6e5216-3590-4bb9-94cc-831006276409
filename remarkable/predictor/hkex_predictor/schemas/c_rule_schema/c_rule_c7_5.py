from remarkable.common.common import is_para_elt
from remarkable.common.common_pattern import P_PERIOD_SEPARATOR
from remarkable.common.constants import AnswerValueEnum
from remarkable.common.pattern import Match<PERSON>ulti
from remarkable.predictor.default_predictor.utils import is_table_result
from remarkable.predictor.hkex_predictor.schemas.c_rule_schema.c_rule_c7 import (
    C7_ND_MODELS,
    C7_NS_REFERENCE_C71,
    C71_ND_NO_ANSWER_MODEL,
    R_C7_SKIP_SYLLABUS,
    R_CONTRACTUAL_ARRANGEMENTS,
    c7_contract_model,
    c7_merge_outlines,
    get_c7_char_results,
    group_c7_element_results,
    is_after_year_end,
    is_c71_base_result,
    is_c71_term_result,
    is_exempt_element,
    is_null_nd,
    need_check_exempt_chapter,
)
from remarkable.predictor.hkex_predictor.schemas.c_rule_schema.c_rule_c7_2 import (
    P_C72_SKIP_SYLLABUS,
    find_c72_matched_answers,
)
from remarkable.predictor.hkex_predictor.schemas.pattern import R_NOT
from remarkable.predictor.hkex_predictor.schemas.util import get_sentence_model
from remarkable.predictor.models.base_model import BaseModel

P_C75_ROLES = MatchMulti.compile(
    r"(?<! U[KS] )\b(lender|borrower|li[cs]en[sc](or|ee)|Recipient|buyer|Seller|subscriber|underwriter)\b(?!['‘’]s)",
    # 提供
    r"\b(?<!as )provide(?!\s*for|in)",
    # 授权并委托
    # https://jura6-lir.paodingai.com/#/hkex/annual-report-checking/report-review/341372?fileId=104197&schemaId=5&rule=C7.5&delist=0 index=1082
    r"\bauthorized\s*and\s*entrusted",
    r"\b(tenant|landlord|lease(s|ing|d)?|lessee|lessor|Appointee|entrustee|(?<!foreign )investor|investment manager)\b(?!['‘’]s)",
    r"\b(?<!the )(purchase|procure|buy|bought)\b.*?\bfrom\b",
    r"(sell|sold).*?\b(by\b|to\s|products?\b)",
    r"grant.*?\bto\s",
    r"provision\s*of.*?(to|service)",
    r"\b(provided|granted)\s*by",
    # 受益方
    r"Beneficiary|\sin\s*favor\s*\s*of\s",
    r"service\s*user",
    r"\bas\s+([a-z]+\s+){1,5}manager",
    operator=any,
)

P_C75_SKIP = MatchMulti.compile(
    r"^[A-Z]\s*=",
    r"pricing\s*for",
    "prices",
    "\bin terms? of",
    r"following\s*(conditions|circumstances)",
    r"not\s*exceed",
    r"\sin\s*violation\s*of\s",
    r"detail|waivers",
    r"annual\s*cap",
    r"(headed|under)\s*[“”\"]",
    R_NOT,
    operator=any,
)


def post_process_c7_5_answer(answers, **kwargs):
    """
    C7.5后处理函数，基于C7.1的答案基础上做提取，提取逻辑：
    1. 在C7.1的结果中找交易各方的角色
    2. 若找不到，则在邻近段落找
    """
    if not answers:
        return answers
    schema, year_end = kwargs.get("schema"), kwargs.get("year_end")
    model, pdfinsight = kwargs.get("model"), kwargs.get("pdfinsight")
    common_results = BaseModel.get_common_predictor_results(answers)
    if is_null_nd(common_results):
        # 只有一个空ND答案，直接返回
        return answers
    # enum_value = AnswerValueEnum.PS.value
    check_exempt = need_check_exempt_chapter(pdfinsight)
    element_results = BaseModel.get_common_element_results(common_results)
    c7_group = group_c7_element_results(element_results, pdfinsight, model)
    grouped_elements = c7_group.grouped_elements
    new_elt_results, found_indices, _ = find_c72_matched_answers(
        common_results, P_C75_ROLES, P_C75_SKIP, pdfinsight, kwargs["year_end"], check_exempt
    )
    nd_results = []
    # found_indices存储明确找到关联关系的index
    for element_result in element_results:
        elt_idx = element_result.element["index"]
        # 没在分组中，说明不是分组的第一个元素块，一个分组提取一个角色即可
        if is_c71_term_result(element_result):
            continue
        # 追加所有C7.1/C7.2答案
        if elt_idx not in grouped_elements:
            if not is_c71_base_result(element_result) or is_para_elt(element_result.element):
                new_elt_results.append(element_result)
            continue
        # 过滤期后数据
        if is_after_year_end(pdfinsight, element_result.element, year_end, text=element_result.text):
            continue
        # 过滤豁免段落
        if check_exempt and is_exempt_element(pdfinsight, element_result.element):
            continue
        group_elements = grouped_elements[elt_idx]
        # 如果没有交易双方的关联关系，则找邻近段落
        if char_results := get_c7_char_results(
            group_elements, P_C75_ROLES, pdfinsight, year_end, P_C72_SKIP_SYLLABUS, P_C75_SKIP
        ):
            # 追加C7.1的答案
            if not is_table_result(element_result) and char_results[0] != element_result:
                new_elt_results.append(element_result)
            new_elt_results.extend(char_results)
            found_indices.update(r.index for r in char_results)
        elif is_para_elt(element_result.element) and is_c71_base_result(element_result):
            nd_results.append(element_result)
            # 任意一个答案没有找到关联关系，判定为ND
            # enum_value = AnswerValueEnum.ND.value

    # if nd_results and any(not any(is_in_range(i, rng) for i in found_indices) for rng in c7_group.parent_ranges.values()):
    #     # 有ND答案时，C7.1答案所属大章节下没有任何相关描述，判断为ND
    #     # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6845#note_704467
    #     enum_value = AnswerValueEnum.ND.value
    #     new_elt_results = new_elt_results + nd_results
    # else:
    # TODO 分组不准确，导致PS被判定为NS，这里只要找到任意一个交易的交易方关联关系，则判定为PS
    enum_value = AnswerValueEnum.PS.value if new_elt_results else AnswerValueEnum.ND.value
    return [
        model.create_result(
            c7_merge_outlines(pdfinsight, new_elt_results + nd_results), value=enum_value, schema=schema
        )
    ]


predictor_options = [
    {
        "path": ["C7.5"],
        "models": [
            # 1. NS: 若C7.1为NS，则认为是NS
            C7_NS_REFERENCE_C71,
            # 2-3. 特殊ND
            *C7_ND_MODELS,
            # 4. C7.1为无答案的ND，则直接ND
            C71_ND_NO_ANSWER_MODEL,
            # 5. ND: 若C7.2为ND，则认为是ND
            {
                "name": "reference",
                "from_path": ["C7.2"],
                "from_answer_value": AnswerValueEnum.ND.value,
            },
            # 6. PS/ND
            {
                "name": "multi_models",
                "operator": "union",
                "deduplicate_elements": True,
                "sort_by_elt_index": True,
                "enum_every_element": True,
                "post_process_model_answers": post_process_c7_5_answer,
                "models": [
                    # 6.1 提取C7.2的PS答案
                    {
                        "name": "reference",
                        "from_path": ["C7.2"],
                        "from_answer_value": AnswerValueEnum.PS.value,
                    },
                    # 6.2 基于C7.1的答案找其它
                    {
                        "name": "reference",
                        "from_path": ["C7.1"],
                        "from_answer_value": [AnswerValueEnum.PS.value, AnswerValueEnum.ND.value],
                        # 过滤没有答案的answer
                        "enum_pattern": r".",
                    },
                    # 6.3 指定合同章节下的相关描述
                    # https://jura6-lir.paodingai.com/#/hkex/annual-report-checking/report-review/341372?fileId=104197&schemaId=5&rule=C7.5&delist=0
                    c7_contract_model(
                        get_sentence_model(
                            P_C75_ROLES,
                            multi_elements=True,
                            syllabus_regs=R_CONTRACTUAL_ARRANGEMENTS,
                            neglect_syllabus_regs=[*R_C7_SKIP_SYLLABUS, r"material\s*changes"],
                            extend_syllabus_regs=R_CONTRACTUAL_ARRANGEMENTS,
                            skip_pattern=P_C75_SKIP,
                        ),
                    ),
                    # 表格行被识别为了章节，导致未提取到：stock=06669, year=2024, page=54, index=657
                    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7316
                    {
                        "name": "para_match",
                        "syllabus_regs": r"major\s*terms\s*[:：]",
                        "para_separator": P_PERIOD_SEPARATOR,
                        "sentence_pattern": P_C75_ROLES,
                        "neglect_sentence_pattern": P_C75_SKIP,
                    },
                ],
            },
            # 以下为备选方案，后续效果太差，可以考虑增加下述模型
            # # 5. 相关章节下找关键词
            # {
            #     "name": "multi_models",
            #     "operator": "union",
            #     "enum": AnswerValueEnum.PS.value,
            #     "require_unique_element": True,
            #     "sort_by_elt_index": True,
            #     "deduplicate_elements": True,
            #     "post_process_model_answers": C7PostProcessor(),
            #     "models": [
            #         # 5.1 基于C7.1的答案找其它
            #         {
            #             "name": "reference",
            #             "from_path": ["C7.1"],
            #             "from_answer_value": [AnswerValueEnum.PS.value, AnswerValueEnum.ND.value],
            #             "post_process_model_answers": post_process_c7_5_answer,
            #         },
            #         # 5.2 先找CT及详情章节下内容
            #         c7_ct_chapter_ps_model(P_C75_ROLES, P_C75_SKIP),
            #         # 5.3 按照CT关键词向后找
            #         c7_follow_model([P_C75_ROLES], P_C75_SKIP),
            #         # 5.4 按照CT关键词向前找
            #         c7_above_model([P_C75_ROLES], P_C75_SKIP),
            #         # 5.5 提取表格中的角色
            #         {
            #             "name": "special_cells",
            #             "multi_elements": True,
            #             "enum": AnswerValueEnum.PS.value,
            #             "multi": True,
            #             "need_continuous": False,
            #             "syllabus_regs": P_C7_CHAPTER_CT,
            #             "neglect_syllabus_regs": P_C71_SKIP_SYLLABUS,
            #             "extend_candidates_syllabus_regs": P_C7_CHAPTER_CT,
            #             # "row_col_relation": "or",
            #             # https://jura6-lir.paodingai.com/#/hkex/annual-report-checking/report-review/260712?fileId=69357&schemaId=5&rule=C7.5&delist=0
            #             "row_header_pattern": MatchMulti.compile(r"services(\s*[:：])?\s*$", operator=any),
            #         },
            #     ],
            # },
        ],
    },
]
