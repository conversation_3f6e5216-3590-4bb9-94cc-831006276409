import re
from datetime import datetime

from remarkable.common.common_pattern import (
    P_SEN_SEPARATOR,
    R_CURRENCY,
)
from remarkable.common.constants import AnswerValueEnum
from remarkable.common.pattern import MatchMulti, NeglectPattern, PatternCollection, PositionPattern
from remarkable.common.util import is_in_range
from remarkable.pdfinsight.reader_util import find_table_title
from remarkable.predictor.common_pattern import R_EN_MONTH
from remarkable.predictor.default_predictor.utils import is_table_result
from remarkable.predictor.hkex_predictor.models.c1 import (
    P_CHAPTER_PURCHASE_SALE,
    P_CHAPTER_SHARE_CAPITAL,
    P_NO_PURCHASE,
    R_PURCHASE,
    R_SHARES,
)
from remarkable.predictor.hkex_predictor.pattern import R_DATES_FOR_EXTRACT, R_MONTH_FOR_EXTRACT
from remarkable.predictor.hkex_predictor.schemas.list_rule_schema.list_rule_b1 import R_USED
from remarkable.predictor.hkex_predictor.schemas.pattern import P_DURING_THE_YEAR, R_MONEY_UNIT, R_NOT
from remarkable.predictor.models.base_model import BaseModel
from remarkable.predictor.models.special_cells import LastMode
from remarkable.predictor.schema_answer import PredictorResult
from remarkable.services.embedding import split_sentence

# 子项1-3常见章节： purchase, sale or redemption of listed securities
P_IN_OR_ON_DATE = PatternCollection(
    [
        rf"(^in|^on|year\s*end(ed)?|from\s*(\d+\s*)?[a-z]+(\s*\d{{4}})?\s*to)\s*{dt}"
        for dt in [*R_DATES_FOR_EXTRACT, R_MONTH_FOR_EXTRACT]
    ],
    flags=re.I,
)
# http://100.64.0.105:55647/#/project/remark/294254?treeId=8761&fileId=70748&schemaId=18&projectId=8761&schemaKey=C1
R_TREASURY_SHARE = r"(?<![（(])(?<!to )\btreasury\s*(share|stock)"
R_TREASURY_SHARE_NUM = NeglectPattern.compile(
    match=r"(number|no\.)\s*of\s*([a-z]+\s+)?shares", unmatch=r"purchase|issue"
)
R_TREASURY_ROW = PositionPattern.compile(R_EN_MONTH, r"\b20\d{2}\b")
P_TREASURY_SHARES_REASON = MatchMulti.compile(
    R_TREASURY_SHARE,
    MatchMulti.compile(
        R_USED,
        r"\b(purpose|reason|\bmade\s*for|effected)\b",
        r"considered|\bwith\s*(a|the)\s*view",
        r"benefit|increas|\benhanc(e|ing)",
        r"share\s*award\s*(scheme|plan)",
        r"(long|short)[.\s]+term",
        r"incentive\s*(award\s*)?(scheme|plan)",
        r"\bcash(es)?\b",
        operator=any,
    ),
    operator=all,
)
R_SHARE_CAPITAL = r"share\s*capital$"
R_EXTEND_CHAPTERS = [
    R_SHARE_CAPITAL,
    NeglectPattern.compile(match=r"share\s*award\s*scheme|issued\s*shares", unmatch=r"\boption"),
]
R_NEGLECT_SYLLABUS = [r"transactions", r"placing", r"rights?\s*issue", r"convertible"]
P_PRIOR_YEAR = MatchMulti.compile(r"(prior|last|previous)\s*year", operator=any)
R_AGGREGATE = rf"(aggregate|total|amounting)\s+[^\d]*?{R_MONEY_UNIT}[1-9]"
R_COMPANY = r"company|subsidiary|trustee"
P_NO_TREASURY_SHARES = [PositionPattern.compile(R_NOT, rf"{R_TREASURY_SHARE}(?!s?[）)])")]

TREASURY_SHARE_NS_MODELS = [
    # 子项4及子项5的NS: 没有treasury shares，不限制章节
    {
        "name": "para_match",
        "enum": AnswerValueEnum.NS,
        "multi_elements": True,
        "para_separator": P_SEN_SEPARATOR,
        "extend_candidates_by_enum": True,
        "extend_candidates_syllabus_regs": [P_CHAPTER_PURCHASE_SALE],
        "paragraph_pattern": P_NO_TREASURY_SHARES,
    },
    # NS: 指定章节+没有treasury shares
    {
        "name": "para_match",
        "syllabus_regs": [r"share\s*award|issued\s*share"],
        "enum": AnswerValueEnum.NS,
        "multi_elements": True,
        "para_separator": P_SEN_SEPARATOR,
        "extend_candidates_syllabus_regs": [r"share\s*award|issued\s*share"],
        "paragraph_pattern": P_NO_TREASURY_SHARES,
    },
]


def c1_para_model(patterns1: list, patterns2: list = None, para_patterns: list = None):
    """
    优先从repurchase, sale章节提取，若提取不到再从share award scheme章节提取
    """
    return {
        "name": "multi_models",
        "operator": "any",
        "require_unique_element": True,
        "enum": AnswerValueEnum.PS,
        "models": [
            # 优先repurchase sale章节
            {
                "name": "para_match",
                "enum": AnswerValueEnum.PS,
                "syllabus_regs": P_CHAPTER_PURCHASE_SALE,
                "multi_elements": True,
                "para_separator": P_SEN_SEPARATOR,
                "extend_candidates_by_enum": True,
                # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6328#note_666035
                "extend_candidates_syllabus_regs": P_CHAPTER_PURCHASE_SALE,
                "sentence_pattern": patterns1,
                "paragraph_pattern": para_patterns,
            },
            # 限制分值
            {
                "name": "para_match",
                "threshold": 0.618,
                "enum": AnswerValueEnum.PS,
                "multi_elements": True,
                "para_separator": P_SEN_SEPARATOR,
                "neglect_syllabus_regs": R_NEGLECT_SYLLABUS,
                "extend_candidates_by_enum": True,
                "sentence_pattern": patterns1,
                "paragraph_pattern": para_patterns,
            },
            # 从share award schema中提取总价
            {
                "name": "para_match",
                "enum": AnswerValueEnum.PS,
                "multi_elements": True,
                "para_separator": P_SEN_SEPARATOR,
                "syllabus_regs": R_EXTEND_CHAPTERS,
                "neglect_syllabus_regs": R_NEGLECT_SYLLABUS,
                "extend_candidates_by_enum": True,
                # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6328#note_666035
                "extend_candidates_syllabus_regs": R_EXTEND_CHAPTERS,
                "sentence_pattern": patterns2 or patterns1,
                "paragraph_pattern": para_patterns,
            },
        ],
    }


def filter_para_in_the_year(answers: list[PredictorResult], **kwargs):
    """
    TODO 针对子项1，总价必须为当年内的总价
    TODO 是否子项1为ND，其余子项就可以不提取
    """
    pdfinsight = kwargs.get("predictor").pdfinsight
    year_start, year_end = kwargs.get("year_start"), kwargs.get("year_end")
    if not year_end:
        return answers
    new_answers = []
    for answer in BaseModel.get_common_predictor_results(answers):
        if answer.answer_value != AnswerValueEnum.PS:
            new_answers.append(answer)
            continue
        new_element_results = []
        for element_result in answer.element_results:
            if is_table_result(element_result):
                element_text = find_table_title(pdfinsight, element_result.element["index"])
            else:
                element_text = element_result.element.get("text") or element_result.text
            # http://100.64.0.105:55647/#/project/remark/265839?treeId=3584&fileId=70382&schemaId=18&projectId=17&schemaKey=C1 idx=1356
            if P_PRIOR_YEAR.search(element_text):
                continue
            if P_DURING_THE_YEAR.search(element_text):
                new_element_results.append(element_result)
                continue
            has_valid, has_invalid = False, False
            for sentence in split_sentence(element_text):
                if matched := P_IN_OR_ON_DATE.nexts(sentence):
                    month, year = matched.group("mon"), matched.group("year")
                    if (
                        year_start
                        <= datetime.strptime(f"{year}-{month}-01", "%Y-%b-%d").strftime("%Y-%m-%d")
                        <= year_end
                    ):
                        # 句子中，任意一个日期在当年内，就认为答案可用
                        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6839#note_705209 子项2
                        has_valid = True
                        break
                    has_invalid = True
            if not has_valid and has_invalid:
                continue
            # 没有提取到任何可用日期时，需要答案
            new_element_results.append(element_result)
        if not new_element_results:
            continue
        answer.element_results = new_element_results
        new_answers.append(answer)
    return new_answers


def filter_the_exchanges(answers: list[PredictorResult], **kwargs):
    """
    针对第2个子项
    若`purchase, sale or redemption of listed securities`章节下有多个子章节，那么每个子章节都需要披露交易所
    """
    pdfinsight = kwargs.get("predictor").pdfinsight
    # 多个同名章节： http://100.64.0.105:55647/#/project/remark/294637?treeId=7130&fileId=70804&schemaId=18&projectId=17&schemaKey=C1
    all_syllabus_ranges = []
    for syllabus in pdfinsight.syllabus_reader.syllabuses:
        if not P_CHAPTER_PURCHASE_SALE.search(syllabus["title"]):
            continue
        syllabus_ranges = []
        for child_index in syllabus["children"]:
            child_syllabus = pdfinsight.syllabus_dict[child_index]
            syllabus_ranges.append(child_syllabus["range"])
        # 2个以上子章节才做校验
        if len(syllabus_ranges) <= 1:
            continue
        all_syllabus_ranges.append(syllabus_ranges)
    if not all_syllabus_ranges:
        return answers
    all_indices = set()
    for answer in BaseModel.get_common_predictor_results(answers):
        if answer.answer_value != AnswerValueEnum.PS:
            return answers
        for elt in BaseModel.get_elements_from_answer_result(answer.element_results):
            all_indices.add(elt["index"])
    for syllabus_ranges in all_syllabus_ranges:
        # 每个章节中都需要有一个交易所
        if all(any(is_in_range(idx, rng) for idx in all_indices) for rng in syllabus_ranges):
            return answers
    return []


NS_MODEL = {
    "name": "reference",
    "from_path": ["C1", "Aggregate price"],
    "from_answer_value": AnswerValueEnum.NS.value,
}
ND_MODEL = {
    "name": "reference",
    "from_path": ["C1", "Aggregate price"],
    "from_answer_value": AnswerValueEnum.ND.value,
}


# TODO 86768 汇丰
predictor_options = [
    ################################################################
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/5916
    # TODO 购买主题必须为公司/子公司，需要把公司名称传进来一起做判断: https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6839#note_706442
    ################################################################
    # ----- 子项1 -----
    {
        "path": ["C1", "Aggregate price"],
        "element_candidate_count": 20,
        "post_process": filter_para_in_the_year,
        "models": [
            {"name": "c1_ns"},
            {
                "name": "multi_models",
                "operator": "union",
                "require_unique_element": True,
                "enum": AnswerValueEnum.PS,
                "models": [
                    # 用章节标题限制
                    {
                        "name": "special_cells",
                        "enum": AnswerValueEnum.PS,
                        "syllabus_regs": [P_CHAPTER_PURCHASE_SALE, P_CHAPTER_SHARE_CAPITAL],
                        "extend_candidates_by_enum": True,
                        "extend_candidates_syllabus_regs": [R_SHARE_CAPITAL],
                        "multi": True,
                        "need_continuous": True,
                        "need_headers_meta": True,
                        "col_header_pattern": [
                            r"\b(aggregate|total).*?\b(price|amount|consideration|paid)",
                            r"settlement\s*cost",
                        ],
                    },
                    # http://100.64.0.105:55647/#/project/remark/296354?treeId=4927&fileId=71020&schemaId=18&projectId=4927&schemaKey=C1
                    # 用分值+标题限制（章节识别不好）
                    {
                        "name": "special_cells",
                        "enum": AnswerValueEnum.PS,
                        "threshold": 0.5,
                        "title_patterns": [R_PURCHASE],
                        "multi": True,
                        "need_continuous": True,
                        "need_headers_meta": True,
                        "col_header_pattern": [r"\b(aggregate|total).*?\b(price|amount|consideration|paid)"],
                    },
                    c1_para_model(
                        [MatchMulti.compile(R_PURCHASE, R_SHARES, R_AGGREGATE, operator=all)],
                        # 句子所在的段落必须包含关键词purchase/acquire等
                        para_patterns=[R_PURCHASE],
                    ),
                ],
            },
            # 没有找到任何PS，再找一次NS
            {
                "name": "para_match",
                "enum": AnswerValueEnum.NS.value,
                "para_separator": P_SEN_SEPARATOR,
                "extend_candidates_syllabus_regs": P_CHAPTER_PURCHASE_SALE,
                "sentence_pattern": P_NO_PURCHASE,
            },
        ],
    },
    # ----- 子项2 -----
    {
        "path": ["C1", "The exchangs/arrangement"],
        "element_candidate_count": 20,
        "post_process": filter_the_exchanges,
        "models": [
            NS_MODEL,
            c1_para_model(
                [
                    r"\s(?P<content>on\s*the\s*stock\s*exchange(\s*of\s*Hong\s*Kong\s*Limited)?)",
                    r"\s(?P<content>on\s*the\s*([a-z]+\s+){1,4}\s*Exchange)",
                ],
                # 在share award/capital章节需要严格限制
                [
                    rf"{R_PURCHASE}[^;；]*?{R_SHARES}.*?\s(?P<content>on\s*the\s*stock\s*exchange(\s*of\s*Hong\s*Kong\s*Limited)?)",
                    rf"{R_PURCHASE}[^;；]*?{R_SHARES}.*?\s(?P<content>on\s*the\s*([a-z]+\s+){{1,4}}\s*Exchange)",
                    rf"{R_PURCHASE}.*?\s(?P<content>on\s*the\s*([a-z]+\s+){{1,4}}\s*Exchange).*?[:：]$",
                ],
            ),
        ],
    },
    # ----- 子项3 -----
    {
        "path": ["C1", "Purchase by issuer/subsidiary"],
        "element_candidate_count": 20,
        "post_process": filter_para_in_the_year,
        "models": [
            NS_MODEL,
            c1_para_model(
                [
                    MatchMulti.compile(R_COMPANY, R_PURCHASE, rf"{R_SHARES}|\bper\s*share", operator=all),
                    # http://100.64.0.105:55647/#/project/remark/294692?treeId=12752&fileId=70812&schemaId=18&projectId=12752&schemaKey=C1
                    MatchMulti.compile(R_COMPANY, R_PURCHASE, r"shares", r"[:：]", operator=all),
                ],
                # 在share award/capital章节需要严格限制
                [
                    MatchMulti.compile(R_COMPANY, R_PURCHASE, rf"{R_SHARES}|\bper\s*share", R_AGGREGATE, operator=all),
                    # http://100.64.0.105:55647/#/project/remark/294692?treeId=12752&fileId=70812&schemaId=18&projectId=12752&schemaKey=C1
                    MatchMulti.compile(R_COMPANY, R_PURCHASE, r"shares", r"[:：]", operator=all),
                ],
            ),
        ],
    },
    # ----- 子项4 -----
    {
        "path": ["C1", "Number of treasury share held"],
        "models": [
            NS_MODEL,
            # 2. 用章节标题限制
            {
                "name": "special_cells",
                "enum": AnswerValueEnum.PS,
                # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/5916#note_642308
                "syllabus_regs": [
                    NeglectPattern.compile(match=R_TREASURY_SHARE, unmatch=r"issued|(?<!treasury )share")
                ],
                "extend_candidates_by_enum": True,
                "multi": True,
                "need_continuous": True,
                "col_header_pattern": [R_TREASURY_SHARE_NUM],
                "row_header_pattern": [R_TREASURY_ROW],
                "last_mode": {LastMode.LAST_ROW},
            },
            # 3. 用表名限制
            {
                "name": "special_cells",
                "enum": AnswerValueEnum.PS,
                "title_patterns": [
                    NeglectPattern.compile(match=R_TREASURY_SHARE, unmatch=r"issued|(?<!treasury )share")
                ],
                "force_use_all_elements": True,
                "multi": True,
                "need_continuous": True,
                "col_header_pattern": [R_TREASURY_SHARE_NUM],
                "row_header_pattern": [R_TREASURY_ROW],
                "last_mode": {LastMode.LAST_ROW},
            },
            # 4. 扩展share capital章节，并用列名限制 （TODO 若有需要，后处理判断行名为期末）
            {
                "name": "special_cells",
                "enum": AnswerValueEnum.PS,
                "syllabus_regs": [R_SHARE_CAPITAL],
                "extend_candidates_by_enum": True,
                "extend_candidates_syllabus_regs": [R_SHARE_CAPITAL],
                "multi": True,
                "need_continuous": True,
                "col_header_pattern": [
                    NeglectPattern.compile(
                        match=R_TREASURY_SHARE,
                        unmatch=rf"{R_MONEY_UNIT}[^\w]|{R_CURRENCY}\s*['‘’]000|[bm]illion|¥",
                        flag=0,
                    )
                ],
                "row_header_pattern": [R_TREASURY_ROW],
                "last_mode": {LastMode.LAST_ROW},
            },
            # 5. PS句子: treasury shares
            {
                "name": "para_match",
                "enum": AnswerValueEnum.PS,
                "multi_elements": True,
                "para_separator": P_SEN_SEPARATOR,
                "extend_candidates_by_enum": True,
                "neglect_syllabus_regs": R_NEGLECT_SYLLABUS,
                "extend_candidates_syllabus_regs": R_EXTEND_CHAPTERS,
                "paragraph_pattern": [
                    MatchMulti.compile(r"\bh[oe]ld\b", rf"\d\s*{R_TREASURY_SHARE}", operator=all),
                    # http://100.64.0.105:55647/#/project/remark/294154?treeId=13022&fileId=70734&schemaId=18&projectId=13022&schemaKey=C1
                    MatchMulti.compile(
                        rf"number\s*of\s*{R_TREASURY_SHARE}",
                        r"\b(is|are|was|were)\s*([a-z]+\s+){0,3}[\d,]+\s",
                        operator=all,
                    ),
                ],
            },
            # 6-7. NS句子
            *TREASURY_SHARE_NS_MODELS,
        ],
    },
    # ----- 子项5 -----
    {
        "path": ["C1", "Intended use"],
        "models": [
            NS_MODEL,
            # 2-3. NS句子
            *TREASURY_SHARE_NS_MODELS,
            # 4. PS句子: 库存股用于。。。
            {
                "name": "multi_models",
                "operator": "union",
                "require_unique_element": True,
                "enum": AnswerValueEnum.PS,
                "models": [
                    {
                        "name": "para_match",
                        "enum": AnswerValueEnum.PS,
                        "multi_elements": True,
                        "para_separator": P_SEN_SEPARATOR,
                        "extend_candidates_by_enum": True,
                        "neglect_syllabus_regs": R_NEGLECT_SYLLABUS,
                        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6328#note_666035
                        "extend_candidates_syllabus_regs": R_EXTEND_CHAPTERS,
                        "sentence_pattern": P_TREASURY_SHARES_REASON,
                    },
                    # 这个模型仅针对表格下方的notes提取句子
                    # http://100.64.0.105:55647/#/project/remark/294380?treeId=42761&fileId=70767&schemaId=18&projectId=42761&schemaKey=C1
                    {
                        "name": "table_footnote",
                        "enum": AnswerValueEnum.PS.value,
                        "multi_elements": True,
                        "force_use_all_elements": True,
                        "cell_regs": [R_TREASURY_SHARE],
                        "footnote_pattern": P_TREASURY_SHARES_REASON,
                    },
                ],
            },
            # # ND: 子项4为ND
            # {
            #     "name": "reference",
            #     "enum": AnswerValueEnum.ND,
            #     "from_path": ["C1", "Number of treasury share held"],
            #     "from_answer_value": AnswerValueEnum.ND,
            # },
            # # ND: 其他章节提到了treasury share
            # # 注：只在purchase相关标题下披露了treasury share用途，其他位置没有披露treasury share 的信息，视为treasury share数量只源于该回购，可提取回购share用途
            # {
            #     "name": "para_match",
            #     "enum": AnswerValueEnum.ND,
            #     "parent_features": [*R_DR_CHAPTER_TITLES, *R_MDA_CHAPTER_TITLES, *R_NOTES_CHAPTER_TITLES],
            #     "parent_must_be_root": True,
            #     "neglect_syllabus_regs": [R_PURCHASE],
            #     "multi_elements": False,
            #     "force_use_all_elements": True,
            #     "extend_candidates_by_enum": True,
            #     "paragraph_pattern": [
            #         MatchMulti.compile(R_TREASURY_SHARE, rf"{R_MONEY_UNIT}|\d(,\d{{3}}|h[eo]ld)", operator=all)
            #     ],
            # },
            # # PS句子: 未提及库存股，用purchase
            # {
            #     "name": "para_match",
            #     "syllabus_regs": [R_PURCHASE],
            #     "enum": AnswerValueEnum.PS,
            #     "multi_elements": True,
            #     "para_separator": P_SEN_SEPARATOR,
            #     "extend_candidates_by_enum": True,
            #     "paragraph_pattern": [MatchMulti.compile(r"purchase", P_REASON, operator=all)],
            # },
        ],
    },
]
