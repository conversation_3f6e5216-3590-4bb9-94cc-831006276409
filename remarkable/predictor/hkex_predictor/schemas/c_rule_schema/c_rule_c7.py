"""
一些C7.1~C7.2共用的正则、模型、函数
"""

import dataclasses
import re
from collections import defaultdict
from operator import itemgetter

from remarkable.common.common import is_para_elt, is_table_elt
from remarkable.common.common_pattern import P_PERIOD_SEPARATOR, R_MIDDLE_DASH
from remarkable.common.constants import AnswerV<PERSON>ueEnum, PDFInsightClassEnum, TableType
from remarkable.common.pattern import MatchMulti, NeglectPattern, PatternCollection, PositionPattern
from remarkable.common.protocol import SearchPatternLike
from remarkable.common.util import clean_txt, is_in_range, split_paragraph
from remarkable.pdfinsight.parser import parse_table
from remarkable.pdfinsight.reader import Pd<PERSON><PERSON><PERSON>eader, PdfinsightSyllabus
from remarkable.pdfinsight.reader_util import find_real_syllabus, get_prefix_format
from remarkable.predictor.common_pattern import R_EN_MONTH
from remarkable.predictor.default_predictor.utils import is_sentence_result, is_table_result
from remarkable.predictor.hkex_predictor.model_util import is_note_element
from remarkable.predictor.hkex_predictor.models.c7 import (
    P_C7_CHAPTER_CT,
    P_C7_FOLLOW_PREFIX,
    P_C7_NO_CT,
    P_C7_NO_CT_SKIP,
    P_CT_ALL_EXEMPT,
    P_EXEMPT,
    P_IS_CT,
    P_IS_NOT_CT,
    P_RPT_IS_NOT_CT,
    P_SUBJECT_TO,
    R_CT,
    R_FULLY_EXEMPT,
    R_IS_CT,
    R_RPT,
    R_RPT_CHAPTER_TITLES,
)
from remarkable.predictor.hkex_predictor.pattern import R_DATES_FOR_EXTRACT
from remarkable.predictor.hkex_predictor.schemas.pattern import P_NOTE_CHAPTER, P_SAVE_AS_ABOVE, R_NOT, R_SAVE_AS
from remarkable.predictor.hkex_predictor.schemas.util import get_sentence_model
from remarkable.predictor.models.base_model import R_DISCLOSED, BaseModel
from remarkable.predictor.schema_answer import (
    AnswerResult,
    CharResult,
    ElementResult,
    OutlineResult,
    ParagraphResult,
    TableResult,
)
from remarkable.predictor.utils import extract_date

# http://************:55647/#/hkex/annual-report-checking/report-review/341372?fileId=104197&schemaId=5&rule=C7.1
# https://jura6-lir.paodingai.com/#/hkex/annual-report-checking/report-review/341314?fileId=104190&schemaId=5&rule=C7.5
R_CONTRACTUAL_ARRANGEMENTS = r"contractual\s*arrangements"
P_CONTRACTUAL_ARRANGEMENTS = MatchMulti.compile(R_CONTRACTUAL_ARRANGEMENTS, operator=any)
R_MATERIAL_TRANSACTION = r"materials?\s*transactions?"
P_C7_OTHER_CHAPTERS = MatchMulti.compile(
    R_RPT,
    # http://************:55647/#/project/remark/294851?treeId=4700&fileId=70832&schemaId=29&projectId=17
    R_RPT_CHAPTER_TITLES,
    # http://************:55647/#/project/remark/265504?treeId=38029&fileId=70315&schemaId=18&projectId=17&schemaKey=C7.1
    R_CONTRACTUAL_ARRANGEMENTS,
    R_MATERIAL_TRANSACTION,
    # http://************:55647/#/project/remark/268399?treeId=3659&fileId=70534&schemaId=18&projectId=17&schemaKey=C7.1
    # http://************:55647/#/project/remark/268790?treeId=20335&fileId=70590&schemaId=18&projectId=17&schemaKey=C7.1
    r"transaction.+?arrangement.+contract",
    # http://************:55647/#/project/remark/268995?treeId=9886&fileId=70619&schemaId=18&projectId=17&schemaKey=C7.1
    r"interests?\s*in\s*contract",
    operator=any,
)
P_EXEMPT_CHAPTER = PositionPattern.compile(rf"(?<!{R_MIDDLE_DASH})(?<!no )(?<!no[nt] )\bexempt", R_CT)
P_NON_EXEMPT_CHAPTER = PositionPattern.compile(rf"\bnon{R_MIDDLE_DASH}exempt", R_CT)
R_C7_SKIP_SYLLABUS = [
    # http://************:55647/#/project/remark/293735?treeId=5882&fileId=70672&schemaId=18&projectId=17&schemaKey=C7.1
    r"annual\s*review",
    r"auditor|confirmation",
    # 不提取豁免交易 https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6586#note_689787
    P_EXEMPT_CHAPTER,
    # https://jura6-lir.paodingai.com/#/hkex/annual-report-checking/report-review/261387?fileId=69492&schemaId=5&rule=C7.1&delist=0&page=124
    # todo： 这个章节是目录识别问题
    rf"^non{R_MIDDLE_DASH}competition\s*agreements$",
    r"\brisks?\b",
    # 期后事项
    r"\b(after|subsequent|consecutive).*report",
    r"significant\s*accounting",
    # 监管
    r"REGULATORY",
]

C7_INJECT_SYLLABUSES = [
    r"__regex__CONNECTED.*?TRANSACTIONS?",
    r"__regex__RELATED.*?PARTY.*?TRANSACTIONS?",
    r"__regex__^MATERIAL TRANSACTIONS$",
    # TODO 容易误匹
    r"__regex__^(\d{3})?\s*Report\s*of\s*the\s*Directors?\s*(\d{3})?$",
]
R_C7_DISCLOSED_IN_OTHER = [
    r"\b(disclosed|set\s*out).*(under|headed)[\"“”]",
    r"\b(in|under)\s(the\s*)?\bnotes?\s*\d",
    r"financial\s*statements",
]

P_C7_NEG_DISCLOSED = [
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6643#note_696524
    r"\bcertain\s",
    P_IS_NOT_CT,
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6643#note_691453
    rf"(?<!\bnot )(?<!including ){R_FULLY_EXEMPT}",
    # http://************:55647/#/project/remark/268637?treeId=6218&fileId=70568&schemaId=18&projectId=17&schemaKey=C7.1
    PositionPattern.compile(R_IS_CT, r"set\s*out|disclosed", rf"\b(headed|in|under)\s+[“”\"]{R_CT}"),
    PositionPattern.compile(r"remuneration\s*of\s*the Directors", R_IS_CT),
    P_CT_ALL_EXEMPT,
]

R_ANY_DATE = rf"\b((\d{{1,2}}(st|nd|rd|th)?\s*)?{R_EN_MONTH}|{R_EN_MONTH}\s*\d{{1,2}}(st|nd|rd|th)?)[，,]?\s+\d{{4}}\b"
R_ENTERED_INTO = (
    r"(?<!\bbe )(?<!\bwill )(?<!\bwill also )(?<!\bby )\b(enter(ed|ing)\s*into\s|renew|signed|agreed\s*to\s*sign)"
)
P_ENTERED_INTO = MatchMulti.compile(R_ENTERED_INTO, operator=any)
R_EFFECTED = r"\b(eff?ect(ed|ive)?|comm?enc(ed|ing))"
# P_EXPIRED = re.compile(r"(laps|expir|complet|terminat|cancell?)e?(d|ing)?", re.I)
# 在提取交易日期时，需要排除一些干扰日期，例如过期日期、公告日期等
P_INVALID_DATE = [
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6643#note_690257
    # http://************:55647/#/project/remark/265849?treeId=10898&fileId=70384&schemaId=18&projectId=17&schemaKey=C7.1 index=356
    # http://************:55647/#/project/remark/295460?treeId=12218&fileId=70908&schemaId=18&projectId=17&schemaKey=C7.1 index=732
    # 2025.4.10 说明：公告日期如果和entered into等一起披露，则认为就是交易日期
    # re.compile(rf"(disclosed|announce(d|ment))[^,，]+?\bdated\s+{R_ANY_DATE}", re.I),
    # re.compile(r"announcments?\s*dated\s+(\w+\s+){2}\s*\d{4}"),
    re.compile(rf"(laps|expir|complet|terminat|cancell?)e?(d|ing)?\s*(on|at|dated)\s+{R_ANY_DATE}", re.I),
]
P_ENTERED_INTO_DATES = PatternCollection(
    [
        # 优先生效日期，生效日期在期后，则不提取
        *[rf"^on\s*{r_date}" for r_date in R_DATES_FOR_EXTRACT],
        *[rf"\bdate[:：]\s+{r_date}" for r_date in R_DATES_FOR_EXTRACT],
        *[rf"{R_EFFECTED}\s*(on|from)\s*{r_date}" for r_date in R_DATES_FOR_EXTRACT],
        *[rf"\benter(ed|ing)\s*into\b.*?\b(on|from|dated|since)\s*{r_date}" for r_date in R_DATES_FOR_EXTRACT],
        *[rf"(on|from|dated|since)\s*{r_date}" for r_date in R_DATES_FOR_EXTRACT],
    ],
    flags=re.I,
)

# http://************:55647/#/project/remark/293816?treeId=37581&fileId=70684&schemaId=18&projectId=17&schemaKey=C7.1
P_C7_NO_CCT = [
    PositionPattern.compile(R_NOT, R_CT, r"\benter(s|ed|ing)?\s*into"),
    PositionPattern.compile(R_NOT, r"\benter(s|ed|ing)?\s*into", R_CT),
    # 2. 没有任何CT+CCT
    # http://************:55647/#/project/remark/293816?treeId=37581&fileId=70684&schemaId=18&projectId=17&schemaKey=C7.1
    NeglectPattern.compile(match=PositionPattern.compile(rf"{R_NOT}", R_CT), unmatch=P_C7_NO_CT_SKIP),
]
# https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6643#note_692993
P_C7_NS = [
    # 1. 没有任何CT
    P_C7_NO_CT,
    # 2. 没有任何CT+CCT
    *P_C7_NO_CCT,
    # 2. 都被豁免
    P_EXEMPT,
]
P_C7_NS_SKIP = [P_SUBJECT_TO, P_RPT_IS_NOT_CT, r"\bother\s*(\S+\s+){0,3}transactions(?!\s*which\s*constitut)"]
R_AGREEMENT = r"\b(Powers?\s*of\s*Attorney|arrangements?|undertakings?|agreement\b(?!\s*amount)|agreements|contracts?\b|plan\b|letter\b)"  # undertaking
P_AGREEMENT = MatchMulti.compile(R_AGREEMENT, operator=any)
P_AGREEMENT_SYLLABUS = MatchMulti.compile(
    R_AGREEMENT,
    r"lease|loan|business|project|service|disposal|product|sale|purchase|procurement|acquisition|provision",
    # https://jura6-lir.paodingai.com/#/hkex/annual-report-checking/report-review/261387?fileId=69492&schemaId=5&rule=C7.2&delist=0 index=1229
    r"connected\s*transaction\s*of\s*",
    r"^[1-9]\d?\.",
    operator=any,
)
# http://************:55647/#/project/remark/295460?treeId=12218&fileId=70908&schemaId=18&projectId=17&schemaKey=C7.1
P_C7_CONSTITUTE_CT = NeglectPattern.compile(
    match=PositionPattern.compile(rf"between|among|with|{R_AGREEMENT}", r"constitut", R_CT),
    unmatch=r"confirmed|reviewed|above|mentioned|aforesaid|disclosed|certain",
)

P_FOLLOW_CT = NeglectPattern.compile(
    match=MatchMulti.compile(
        rf"following\s*{R_CT}",
        # http://************:55647/#/project/remark/269059?treeId=6651&fileId=70628&schemaId=18&projectId=17&schemaKey=C7.1
        PositionPattern.compile(r"following\s*(related\s*party\s*)?transactions?", R_IS_CT),
        NeglectPattern.compile(match=PositionPattern.compile(R_CT, r"[:：]$"), unmatch=R_NOT),
        # http://************:55647/#/project/remark/265279?treeId=14095&fileId=70270&schemaId=18&projectId=17&schemaKey=C7.1 index=1660
        PositionPattern.compile(
            R_SAVE_AS,
            r"disclosed\s*under\s*this\s*(sector|section)",
            r"\bother.+?transactions\s*constitut([ed]+|ing)\s*no\s*connected",
        ),
        operator=any,
    ),
    unmatch=R_NOT,
)
R_ON_DATE = rf"(?<!(lapsed|expire|cancel) )(?<!(expired) )(?<!(expiring|complete|canceled) )(?<!(cancelled|completed) )\b(on|dated|in)\s*{R_ANY_DATE}"

P_UNNOT_CT = NeglectPattern.compile(match=R_IS_CT, unmatch=R_NOT)

C7_NS_REFERENCE_C71 = {
    "name": "reference",
    "from_path": ["C7.1"],
    "from_answer_value": AnswerValueEnum.NS.value,
}
P_C7_NOTES = re.compile(r"^notes?[：:]?$", re.I)
R_CONNECTED_PERSONS = r"connected\s*person"
P_CONNECTED_PERSONS = re.compile(R_CONNECTED_PERSONS, re.I)
P_C7_FOOTNOTE_PREFIX = re.compile(r"^[(（]?[ivx]{,4}[)）.]")


def filter_nd(answers1, answers2):
    if not answers1 or answers2:
        return []
    return answers1


# 基于C7.1的ND模型：C7.1为ND且没有答案文本（有答案表示：有CT交易但是未披露C7.1要求的交易日期）
C71_ND_NO_ANSWER_MODEL = {
    "name": "reference",
    "from_path": ["C7.1"],
    "from_answer_value": AnswerValueEnum.ND.value,
    "enum_pattern": [r"^$"],
}


C7_ND_MODELS = [
    # 1. ND场景： CT/CCT中提及RPT都不是CT，但RPT章节下有构成CT的描述，为ND
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6643#note_693812
    {
        "name": "multi_models",
        "operator": "all",
        "model_id": "nd_1",
        "enum": AnswerValueEnum.ND.value,
        # "with_element_box": False,
        "require_unique_element": True,
        "deduplicate_elements": True,
        "enum_every_element": True,
        "models": [
            # 1.1 CT/RPT章节下找 rpt不是ct
            {
                "name": "para_match",
                "model_id": "nd_1_paras_1",
                "enum": AnswerValueEnum.ND.value,
                "syllabus_regs": [P_C7_CHAPTER_CT, R_RPT_CHAPTER_TITLES],
                "para_separator": P_PERIOD_SEPARATOR,
                "extend_candidates_syllabus_regs": [P_C7_CHAPTER_CT, R_RPT_CHAPTER_TITLES],
                "neglect_parent_features": P_NOTE_CHAPTER,
                "parent_must_be_root": True,
                "sentence_pattern": P_RPT_IS_NOT_CT,
                "paragraph_pattern": R_RPT,
                "neglect_pattern": R_SAVE_AS,
            },
            # 1.2 在RPT章节披露了CT
            {
                "name": "para_match",
                "model_id": "nd_1_paras_2",
                "enum": AnswerValueEnum.ND.value,
                "para_separator": P_PERIOD_SEPARATOR,
                "sentence_pattern": [R_IS_CT, MatchMulti.compile(r"following\s", R_CT, operator=all)],
                "neglect_sentence_pattern": [R_NOT, P_EXEMPT],
                "syllabus_regs": NeglectPattern.compile(match=R_RPT_CHAPTER_TITLES, unmatch=R_CT),
                "extend_candidates_syllabus_regs": NeglectPattern.compile(match=R_RPT_CHAPTER_TITLES, unmatch=R_CT),
            },
        ],
    },
    # 2. ND场景： CT或CCT下说明部分RPT构成CT，详见xx章节，但在xx章节没有提取到内容  TODO 如果不需要框选ND段落，可以注释该模型
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6643#note_702378
    {
        "name": "multi_models",
        "operator": "union",
        "model_id": "nd_2",
        "enum": AnswerValueEnum.ND.value,
        # "with_element_box": False,
        "require_unique_element": True,
        "results_filter": filter_nd,
        "enum_every_element": True,
        "models": [
            # 2.1 CT章节下找: 部分RPT构成CT，详见xx
            {
                "name": "para_match",
                "model_id": "nd_2_paras_1",
                "enum": AnswerValueEnum.ND.value,
                "para_separator": P_PERIOD_SEPARATOR,
                "syllabus_regs": P_C7_CHAPTER_CT,
                "extend_candidates_syllabus_regs": P_C7_CHAPTER_CT,
                "sentence_pattern": PositionPattern.compile(r"\bcertain|\bpart\s", R_IS_CT),
                "neglect_pattern": P_EXEMPT,
                "paragraph_pattern": MatchMulti.compile(R_RPT, rf"{R_DISCLOSED}.*?(under|headed)", operator=all),
            },
            # 2.2 在详见章节找内容，若找不到则为ND
            {
                "name": "para_match",
                "model_id": "nd_2_paras_2",
                "enum": AnswerValueEnum.ND.value,
                "para_separator": P_PERIOD_SEPARATOR,
                "sentence_pattern": R_IS_CT,
                "neglect_sentence_pattern": R_NOT,
                "syllabus_regs": R_RPT_CHAPTER_TITLES,
                "extend_candidates_syllabus_regs": R_RPT_CHAPTER_TITLES,
                "neglect_syllabus_regs": P_C7_CHAPTER_CT,
            },
        ],
    },
]


def c7_ct_chapter_ps_model(
    patterns: list | SearchPatternLike | str,
    skip_patterns: list | SearchPatternLike | str = None,
    neg_pattern: list | SearchPatternLike | str = None,
    para_separator: re.Pattern | None = P_PERIOD_SEPARATOR,
    neglect_syllabus_regs: list | SearchPatternLike | str = None,
    skip_syllabus_title: bool = False,
    model_id: str = None,
    neglect_root_parent_regs: list | SearchPatternLike | str = None,
):
    return get_sentence_model(
        patterns,
        multi_elements=True,
        syllabus_regs=P_C7_CHAPTER_CT,
        neglect_syllabus_regs=neglect_syllabus_regs,
        neglect_root_parent_regs=neglect_root_parent_regs,
        extend_syllabus_regs=P_C7_CHAPTER_CT,
        extend_disclosed_regs=NeglectPattern.compile(match=R_IS_CT, unmatch=R_NOT),
        # 注意： 如果提及详见xxx章节，但又提及这些交易都被豁免，则不跳转详情章节
        extend_disclosed_neg_regs=P_C7_NEG_DISCLOSED,
        # http://************:55647/#/project/remark/295117?treeId=4455&fileId=70865&schemaId=18&projectId=17&schemaKey=C7.1
        as_follow_pattern=MatchMulti.compile(r"reference", R_ON_DATE, r"[:：]$", operator=all),
        skip_pattern=skip_patterns,
        neg_pattern=neg_pattern,
        para_separator=para_separator,
        skip_syllabus_title=skip_syllabus_title,
        model_id=model_id,
    )


def c7_follow_model(
    patterns: list[SearchPatternLike | str],
    skip_patterns: list | SearchPatternLike | str = None,
    enum=AnswerValueEnum.PS.value,
    para_separator: re.Pattern | None = P_PERIOD_SEPARATOR,
    neglect_syllabus_regs: list = (),
    model_id: str = None,
):
    """
    根据following都是CT关键词找下方章节做提取
    """
    return {
        "name": "near_paras",
        "model_id": model_id,
        "enum": enum,
        "multi_elements": True,
        "multi": True,
        "para_separator": para_separator,
        "start_regs": P_FOLLOW_CT,
        "neglect_start_regs": P_EXEMPT,
        "near_amount": 30,
        "extend_candidates_syllabus_regs": [P_C7_CHAPTER_CT, P_C7_OTHER_CHAPTERS],
        "neglect_syllabus_regs": [*neglect_syllabus_regs, P_C7_CHAPTER_CT, r"\blisting\s*rule"],
        "paragraph_pattern": patterns,
        "neglect_sentence_pattern": skip_patterns,
        "near_by_filter": filter_near_by,
    }


def c7_above_model(
    patterns: list[SearchPatternLike | str],
    skip_patterns: list | SearchPatternLike | str = None,
    enum=AnswerValueEnum.PS.value,
    para_separator: re.Pattern | None = P_PERIOD_SEPARATOR,
    neglect_syllabus_regs: list = (),
    model_id: str = None,
):
    """
    根据xx构成CT关键词向上找同目录元素块做提取
    """
    return {
        "name": "near_paras",
        "model_id": model_id,
        "enum": enum,
        "multi_elements": True,
        "multi": True,
        "para_separator": para_separator,
        "force_use_all_elements": True,
        "start_regs": NeglectPattern.compile(
            match=MatchMulti.compile(R_IS_CT, rf"(above|aforesaid)\s*{R_CT}", operator=any),
            unmatch=rf"{R_NOT}|(under|headed)\s*[\"“”]{R_CT}",
        ),
        # unmatch: https://jura6-lir.paodingai.com/#/hkex/annual-report-checking/report-review/306958?fileId=71585&schemaId=5&rule=C7.1&delist=0 index=1055
        "neglect_start_regs": NeglectPattern.compile(match=P_EXEMPT, unmatch=r"^also[,，]"),
        "near_amount": -50,
        "neglect_syllabus_regs": [*neglect_syllabus_regs, P_C7_CHAPTER_CT],
        "paragraph_pattern": patterns,
        "neglect_sentence_pattern": skip_patterns,
        "near_by_filter": filter_near_by,
    }


def c7_syllabus_model(inject_syllabus_features, parent_features=P_C7_CHAPTER_CT, neglect_parent_features=None):
    return {
        "name": "syllabus_elt_v2",
        "parent_features": parent_features,
        "neglect_parent_features": neglect_parent_features,
        "enum": AnswerValueEnum.PS.value,
        "enum_every_element": True,
        "extend_candidates_syllabus_regs": P_C7_CHAPTER_CT,
        "multi": True,
        "aim_types": {PDFInsightClassEnum.PARAGRAPH.value, PDFInsightClassEnum.TABLE.value},
        "only_inject_features": True,
        "inject_syllabus_features": inject_syllabus_features,
    }


def filter_contractual_near_by(pdfinsight: PdfinsightReader, start_element, elements: list, step: int):
    # 向下找时，如果下一个元素块是指定章节，则返回章节名称
    # 针对需要处理的章节在ct的同级下个章节的情况： https://jura6-lir.paodingai.com/#/hkex/annual-report-checking/report-review/341372?fileId=104197&schemaId=5&rule=B98
    if syllabus := pdfinsight.syllabus_reader.elt_syllabus_dict.get(elements[0]["index"]):
        if P_CONTRACTUAL_ARRANGEMENTS.search(syllabus["title"]):
            return [elements[0]]
    return []


def c7_contract_model(last_models: dict | list[dict]) -> dict:
    """
    只要提及contractual arrangements等构成CT，则在contractual arrangements章节提取
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6586#note_700702
    # http://************:55647/#/hkex/annual-report-checking/report-review/263863?fileId=69987&schemaId=18&rule=C7.1&delist=0
    """
    if isinstance(last_models, list):
        next_model = {
            "name": "multi_models",
            "operator": "union",
            "enum_every_element": True,
            "model_id": "multi_contract_2",
            "models": last_models,
        }
    else:
        next_model = last_models

    return {
        "name": "multi_models",
        "operator": "last",
        "enum": AnswerValueEnum.PS.value,
        "enum_every_element": True,
        "filter_duplicate_elements": False,
        "model_id": "multi_contract",
        "models": [
            {
                "name": "multi_models",
                "operator": "any",
                "model_id": "multi_contract_1",
                "models": [
                    # 1. 描述CONTRACTUAL ARRANGEMENTS章节构成CT
                    get_sentence_model(
                        [PositionPattern.compile(R_CONTRACTUAL_ARRANGEMENTS, R_IS_CT)],
                        syllabus_regs=[P_C7_CHAPTER_CT, R_CONTRACTUAL_ARRANGEMENTS],
                        extend_syllabus_regs=[P_C7_CHAPTER_CT, R_CONTRACTUAL_ARRANGEMENTS],
                        skip_pattern=[R_NOT],
                        model_id="multi_contract_para",
                    ),
                    # 2. 以下构成CT，且下方的第一个章节标题是：CONTRACTUAL ARRANGEMENTS章节构成CT
                    # https://jura6-lir.paodingai.com/#/hkex/annual-report-checking/report-review/341372?fileId=104197&schemaId=5&rule=C7.1&delist=0
                    {
                        "name": "near_paras",
                        "model_id": "multi_contract_near",
                        "multi_elements": False,
                        "start_regs": P_FOLLOW_CT,
                        "neglect_start_regs": P_EXEMPT,
                        "near_amount": 20,
                        "syllabus_regs": [P_C7_CHAPTER_CT, R_CONTRACTUAL_ARRANGEMENTS],
                        "extend_candidates_syllabus_regs": P_C7_CHAPTER_CT,
                        "near_by_filter": filter_contractual_near_by,
                        "paragraph_pattern": r".",
                    },
                ],
            },
            next_model,
        ],
    }


def filter_near_by(pdfinsight: PdfinsightReader, start_element, elements: list, step: int):
    """
    使用near_paras可能会找到过多元素块，这里对元素块进行过滤
    """
    if not elements:
        return elements
    if step < 0:
        # 向回找，遇到满足正则的标题即止
        must_has_ct = P_C7_FOLLOW_PREFIX.search(clean_txt(start_element["text"]))
        result_elements = [start_element]
        for element in elements:
            if not (syllabus := get_valid_syllabus(pdfinsight, element)):
                continue
            syllabuses = pdfinsight.get_real_parent_syllabuses(syllabus)
            # 必须在CT或RPT章节下
            if not any(
                P_C7_CHAPTER_CT.search(s["title"]) or P_C7_OTHER_CHAPTERS.search(s["title"]) for s in syllabuses
            ):
                continue
            if pdfinsight.syllabus_reader.is_syllabus_elt(element):
                break
            text = clean_txt(element["text"])
            if must_has_ct and not (is_para_elt(element) or P_IS_CT.search(text)):
                continue
            # 找到notes则终止
            if P_C7_NOTES.search(text):
                break
            result_elements.append(element)
            # note章节下找到小序号即停止，不需要整章都找
            if not must_has_ct and P_C7_FOLLOW_PREFIX.search(text):
                break
        return sorted(result_elements, key=itemgetter("index"))
    # 向下找时，如果下一个元素块是章节，则找整个章节，否则找到最近的章节
    first_element = elements[0]
    if next_syllabus := pdfinsight.syllabus_reader.elt_syllabus_dict.get(first_element["index"]):
        # # 下一个元素块是agreement章节，则找所有并列章节 TODO 需要再放开
        # if P_AGREEMENT_SYLLABUS.search(next_syllabus["title"]):
        #     parent = find_real_syllabus(pdfinsight, pdfinsight.syllabus_reader.syllabus_dict.get(next_syllabus["parent"]))
        #     if parent and not (P_C7_OTHER_CHAPTERS.search(parent["title"]) or not P_AGREEMENT_SYLLABUS.search(parent["title"])):
        #         start, end = next_syllabus["range"]
        #         for child_idx in parent["children"]:
        #             if child_idx <= next_syllabus["index"]:
        #                 continue
        #             if not (child := pdfinsight.syllabus_reader.syllabus_dict.get(child_idx)):
        #                 continue
        #             child_title = child["title"]
        #             if P_C7_CHAPTER_CT.search(child_title) or P_C7_OTHER_CHAPTERS.search(child_title) or not P_AGREEMENT_SYLLABUS.search(child_title):
        #                 break
        #             end = max(end, child["range"][1])
        #         results = []
        #         if end > start > 0:
        #             for i in range(start, end):
        #                 _, element = pdfinsight.find_element_by_index(i)
        #                 if pdfinsight.is_skip_element(element, skip_merged=True, skip_chinese=True):
        #                     continue
        #                 new_element = copy.copy(element)
        #                 new_element["score"] = 0
        #                 results.append(new_element)
        #         return results
        ret_indices = [e["index"] for e in elements if is_in_range(e["index"], next_syllabus["range"])]
        # 有 constituted  CT的描述就 继续向下找有
        for ele in elements:
            if ele["index"] in ret_indices:
                continue
            for sentence in split_paragraph(ele.get("text", "")):
                if P_UNNOT_CT.search(clean_txt(sentence)):
                    ele_syllabuse = pdfinsight.find_syllabuses_by_index(ele["index"])[-1]
                    ret_indices.extend(range(ele_syllabuse["range"][0], ele_syllabuse["range"][1] - 1))
                    break
        return [e for e in elements if e["index"] in ret_indices]
    if syllabuses := pdfinsight.find_syllabuses_by_index(first_element["index"]):
        return [e for e in elements if is_in_range(e["index"], syllabuses[-1]["range"])]
    return elements[:20]


def extract_transaction_date(text):
    # 删除句子中的公告日期/过期日期等，再提取交易日
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6643#note_690257
    for pattern in P_INVALID_DATE:
        text = pattern.sub("", text)
    return extract_date(P_ENTERED_INTO_DATES, text, str_format="%Y-%m-%d")


def _is_after_year_end(text, year_end) -> tuple[bool, bool]:
    """
    返回值中，第一个表示是否包含日期，第二个表示日期在期后
    """
    if not text:
        return False, False
    if P_ENTERED_INTO.search(text) and (entered_date := extract_transaction_date(text)):
        return True, entered_date > year_end
    return False, False


def is_after_year_end(pdfinsight, element, year_end, text=None):
    text = element.get("text") or text
    has_date, is_after = _is_after_year_end(text, year_end)
    if is_after:
        return True
    if has_date:
        return False
    if P_C7_FOLLOW_PREFIX.search(element.get("text") or text):
        return False
    # 当前段落中没有提到日期，向前找最近的章节范围内的日期
    start_index = element["index"]
    for i in range(1, 10):
        _, prev_element = pdfinsight.find_element_by_index(start_index - i)
        if pdfinsight.is_skip_element(prev_element, aim_types={"PARAGRAPH"}, skip_chinese=True, skip_merged=True):
            continue
        text = prev_element.get("text") or ""
        has_date, is_after = _is_after_year_end(text, year_end)
        if is_after:
            return True
        if has_date or P_C7_FOLLOW_PREFIX.search(text) or pdfinsight.syllabus_reader.is_syllabus_elt(prev_element):
            break
    return False


def need_check_exempt_chapter(pdfinsight):
    return any(s["parent"] != -1 and P_EXEMPT_CHAPTER.search(s["title"]) for s in pdfinsight.syllabus_reader.syllabuses)


def is_exempt_element(pdfinsight: PdfinsightReader, element):
    """
    1. 所属章节/所属章节的父章节包含了exempt，则丢弃答案
    2. 所属章节或所属章节的父章节的兄弟章节包含exempt，且exempt章节下只有一个段落，描述following构成CT
    https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6845#note_705604
    https://jura6-lir.paodingai.com/#/hkex/annual-report-checking/report-review/340939?fileId=104142&schemaId=5&rule=C7.1&delist=0
    """
    syllabuses = pdfinsight.find_syllabuses_by_index(element["index"])
    if not syllabuses:
        return False
    near_syllabus = find_real_syllabus(pdfinsight, syllabuses[-1])
    parent_syllabuses = pdfinsight.get_real_parent_syllabuses(near_syllabus)
    count_parents = len(parent_syllabuses)
    for i, syllabus in enumerate(parent_syllabuses):
        if P_EXEMPT_CHAPTER.search(syllabus["title"]):
            return True
        if i == count_parents - 1 or syllabus["parent"] == -1:
            break
        parent_syllabus = parent_syllabuses[i + 1]
        # 由近及远，有可能最近的章节是NON-EXEMPT章节
        for child_index in [idx for idx in parent_syllabus["children"][::-1] if idx < syllabus["index"]]:
            child_syllabus = pdfinsight.syllabus_reader.syllabus_dict.get(child_index)
            if not child_syllabus:
                continue
            # 最近章节是非豁免，不排除
            if P_NON_EXEMPT_CHAPTER.search(child_syllabus["title"]):
                # https://jura6-lir.paodingai.com/#/hkex/annual-report-checking/report-review/340939?fileId=104142&schemaId=5&rule=A23&delist=0 index=932
                return False
            # 最近章节是豁免章节，需要看是否提及following CT
            if P_EXEMPT_CHAPTER.search(child_syllabus["title"]):
                if is_following_ct_chapter(pdfinsight, child_syllabus["range"]):
                    return True
    return False


def is_following_ct_chapter(pdfinsight, chapter_range):
    start, end = chapter_range
    for index in range(end - 1, start, -1):
        _, element = pdfinsight.find_element_by_index(index)
        if pdfinsight.is_skip_element(
            element,
            skip_merged=True,
            skip_chinese=True,
        ):
            continue
        if not is_para_elt(element):
            break
        if P_FOLLOW_CT.search(clean_txt(element.get("text") or "", remove_cn_text=True)):
            return True
    return False


def sort_c7_results(element_results):
    return sorted(
        element_results,
        key=lambda x: (
            x.element["index"],
            (x.start or 0, -(x.end or 999999999)) if hasattr(x, "start") else (0, 0),
        ),
    )


def format_c7_results(pdfinsight, element_results):
    """
    将所有段落/句子结果都转为CharResult，以方便做合并
    """
    new_results = []
    for element_result in element_results:
        cur_element = element_result.element
        if not is_para_elt(cur_element):
            result = element_result
        else:
            if not hasattr(element_result, "start") or element_result.start is None:
                # 非CharResult，或没有start,end的结果，换成CharResult
                for element in element_result.elements:
                    if is_table_elt(element):
                        table = parse_table(element, tabletype=TableType.TUPLE.value, pdfinsight_reader=pdfinsight)
                        new_results.append(TableResult(element, parsed_table=table))
                        continue
                    if not is_para_elt(element):
                        new_results.append(ElementResult(element))
                        continue
                    result = CharResult(element, element["chars"], start=0, end=len(element["chars"]))
                    if result not in new_results:
                        new_results.append(result)
                continue
            start, end = element_result.start or 0, element_result.end or len(cur_element["chars"])
            result = CharResult(cur_element, cur_element["chars"][start:end], start=start, end=end)
        if result not in new_results:
            new_results.append(result)
    return new_results


def merge_c7_results(pdfinsight, element_results):
    """
    C7提取内容很多，对其做排序，优先element_index，其次判断句子的起始位置
    """
    i = 0
    merged_element_results = []
    existed_indices = set()  # 存储整段答案的index
    # 以下先合并同一个元素块的句子，同时去重
    element_results = sort_c7_results(format_c7_results(pdfinsight, element_results))
    while i < len(element_results):
        current_result = element_results[i]
        element = current_result.element
        current_index = element["index"]
        if current_index in existed_indices:
            i += 1
            continue
        if not (is_para_elt(element) and is_sentence_result(current_result)):
            merged_element_results.append(current_result)
            existed_indices.add(current_index)
            i += 1
            continue
        j = i + 1
        while j < len(element_results):
            start, end = current_result.start, current_result.end
            next_result = element_results[j]
            if (
                not is_sentence_result(next_result)
                or current_index != next_result.element["index"]
                or not start <= next_result.start <= end
            ):
                break
            next_end = next_result.end
            if next_end > end:
                # 合并元素
                current_result.end = next_end
                current_result.chars = element["chars"][start:next_end]
            element_results.pop(j)
        if current_result.start == 0 and current_result.end == len(element["chars"]):
            # 还原成段落
            existed_indices.add(current_index)
            merged_element_results.append(ParagraphResult(element, element["chars"]))
        else:
            merged_element_results.append(current_result)
        i += 1
    return merged_element_results


def c7_post_processor(answers, **kwargs):
    if not answers:
        return answers
    common_results = BaseModel.get_common_predictor_results(answers)
    if is_null_nd(common_results):
        # 只有一个空ND答案，直接返回
        return answers
    pdfinsight, year_end = kwargs["pdfinsight"], kwargs["year_end"]
    new_results = []
    check_exempt = need_check_exempt_chapter(pdfinsight)
    for common_result in common_results:
        for element_result in common_result.element_results:
            if is_table_result(element_result):
                new_results.append(element_result)
                continue
            if is_after_year_end(pdfinsight, element_result.element, year_end, text=element_result.text):
                continue
            if check_exempt and is_exempt_element(pdfinsight, element_result.element):
                continue
            if element_result not in new_results:
                new_results.append(element_result)
    schema, model = kwargs.get("schema"), kwargs.get("model")
    return [
        model.create_result(c7_merge_outlines(pdfinsight, new_results), value=AnswerValueEnum.PS.value, schema=schema)
    ]


def c7_merge_outlines(pdfinsight, element_results):
    """
    C7.4.1-Terms子项后处理： 连续完整的元素块画大框框
    """
    whole_element_results, new_element_results = [], []
    for element_result in merge_c7_results(pdfinsight, element_results):
        if is_table_result(element_result):
            cells = [c for row in element_result.parsed_table.rows for c in row]
            if cells == element_result.parsed_cells:
                # 整表
                whole_element_results.append(element_result)
            else:
                new_element_results.append(element_result)
        elif is_para_elt(element_result.element):
            if is_sentence_result(element_result):
                # 句子
                new_element_results.append(element_result)
            else:
                # 整段
                whole_element_results.append(element_result)
        else:
            # 其他，如shape
            whole_element_results.append(element_result)
    whole_elements = []
    for element_result in whole_element_results:
        whole_elements.extend(element_result.elements)
    for page_box in PdfinsightSyllabus.elements_outline(whole_elements):
        box_elements = page_box["elements"]
        if not box_elements:
            continue
        new_element_results.append(
            OutlineResult(page_box=[page_box], element=box_elements[0], origin_elements=box_elements)
        )
    return sort_c7_results(new_element_results)


def get_valid_syllabus(pdfinsight: PdfinsightReader, element: dict, is_ns_answer=False) -> dict | None:
    """
    找PS/NS答案元素块所在章节
    """
    # 如果是小序号，例如(a)/(b)等，就不能按照章节来判断PS与NS关系，必须按照元素块做判断
    text = clean_txt(element.get("text") or "")
    # 提及save as above/following/no ct，则不按章节做判断
    # http://************:55647/#/project/remark/265184?treeId=2494&fileId=70251&schemaId=18&projectId=17&schemaKey=C7.1
    if is_ns_answer and any(p.search(text) for p in [P_SAVE_AS_ABOVE, P_C7_FOLLOW_PREFIX, *P_C7_NO_CCT]):
        return None
    if syllabus := pdfinsight.get_nearest_syllabus(element):
        # NS场景下：只有章节的最后一个元素块是NS描述，才按章节做判断，否则必须按照元素块做判断
        if is_ns_answer:
            element_index = element["index"]
            start, end = syllabus["range"]
            found_last = False
            for index in range(end - 1, start + 1, -1):
                _, element = pdfinsight.find_element_by_index(index)
                if not is_para_elt(element) or pdfinsight.is_skip_element(element):
                    continue
                if index == element_index:
                    return syllabus
                # NS描述不在章节最后，在倒数第二句
                # http://************:55647/#/project/remark/296598?treeId=37800&fileId=70687&schemaId=29&projectId=17
                if not found_last:
                    found_last = True
                    continue
                return None
        return syllabus
    return None


def is_null_nd(common_results):
    return (
        len(common_results) == 1
        and common_results[0].answer_value == AnswerValueEnum.ND.value
        and not common_results[0].element_results
    )


def get_c7_char_results(
    elements,
    pattern: SearchPatternLike,
    pdfinsight: PdfinsightReader,
    year_end: str,
    p_neg_chapter: SearchPatternLike = None,
    skip_pattern: SearchPatternLike = None,
) -> list[CharResult]:
    results = []
    for element in elements:
        if not is_para_elt(element) or pdfinsight.syllabus_reader.is_syllabus_elt(element):
            continue
        if is_after_year_end(pdfinsight, element, year_end, text=element["text"]):
            continue
        syllabus = pdfinsight.find_syllabuses_by_index(element["index"])[-1]
        if p_neg_chapter and any(
            p_neg_chapter.search(s["title"]) for s in pdfinsight.get_real_parent_syllabuses(syllabus)
        ):
            continue
        for sentence, (start, end) in split_paragraph(element["text"], need_pos=True):
            if pattern.search(sentence) and not (skip_pattern and skip_pattern.search(sentence)):
                results.append(CharResult(element, element["chars"][start:end], start=start, end=end))
    return results


def find_syllabus(pdfinsight, element):
    """
    向回找两级，找CT章节下的子章节，再找CT章节本身的范围
    """
    near_syllabus = pdfinsight.get_nearest_syllabus(element)
    parent_syllabuses = pdfinsight.get_real_parent_syllabuses(near_syllabus)
    parent_syllabus = None
    for syll in parent_syllabuses:
        if P_C7_CHAPTER_CT.search(syll["title"]) or P_C7_OTHER_CHAPTERS.search(syll["title"]):
            parent_syllabus = syll
            break
    else:
        near_index = near_syllabus["index"]
        # 没有找到CT章节，则可能是CT的兄弟章节，找兄弟章节中是否有包含following描述的CT章节，如果有，则当前章节自己就是一个判断范围
        # http://************:55647/#/hkex/annual-report-checking/report-review/260937?fileId=69402&schemaId=5&rule=C7.1&delist=0
        found_ct = False
        for parent_syll in parent_syllabuses[:2][::-1]:
            if cur_parent := pdfinsight.syllabus_reader.syllabus_dict.get(parent_syll["parent"]):
                for child_index in sorted(i for i in cur_parent["children"] if i < near_index)[::-1]:
                    child = pdfinsight.syllabus_reader.syllabus_dict.get(child_index)
                    if P_C7_CHAPTER_CT.search(child["title"]) or P_C7_OTHER_CHAPTERS.search(child["title"]):
                        found_ct = True
                        if is_following_ct_chapter(pdfinsight, child["range"]):
                            return None, parent_syll, cur_parent
                        break
            if found_ct:
                break
    if len(parent_syllabuses) < 2:
        return None, None, parent_syllabus or near_syllabus

    # 找出agreement章节的父章节
    agreement_syllabus, strict_parent_syllabus = None, None
    for i, syllabus in enumerate(parent_syllabuses[1:], start=1):
        if (
            len(
                [
                    i
                    for i in syllabus["children"]
                    if P_AGREEMENT_SYLLABUS.search(pdfinsight.syllabus_reader.syllabus_dict[i].get("title") or "")
                ]
            )
            > 1
        ):
            strict_parent_syllabus = syllabus
            agreement_syllabus = parent_syllabuses[i - 1]
            break
    # 找不到agreement则找CT/RPT/contracts章节
    if not agreement_syllabus:
        agreement_syllabus = parent_syllabuses[0]
        for syllabus in parent_syllabuses[:3][1:]:
            if (
                syllabus == parent_syllabus
                or P_C7_CHAPTER_CT.search(syllabus["title"])
                or P_C7_OTHER_CHAPTERS.search(syllabus["title"])
            ):
                strict_parent_syllabus = syllabus
                break
            agreement_syllabus = syllabus
    # 未找到CT/RPT等章节，则认为不能按照章节分组
    if not strict_parent_syllabus:
        return None, None, parent_syllabus or near_syllabus
    return strict_parent_syllabus, agreement_syllabus, parent_syllabus or near_syllabus


def set_syllabus_range(result, syllabus_index, syllabus_range):
    """
    同一个章节被找到多次，合并找到的章节范围
    """
    if result.get(syllabus_index):
        start, end = result[syllabus_index]
        result[syllabus_index] = [min(syllabus_range[0], start), max(syllabus_range[1], end)]
    else:
        result[syllabus_index] = syllabus_range


@dataclasses.dataclass
class C7GroupResult:
    grouped_elements: dict[int, list]
    agreement_syllabus_ranges: dict[int, list[int]]
    parent_syllabus_ranges: dict[int, list[int]]


def is_c71_term_result(elt_result):
    # 跳过C7.1的term元素块
    return elt_result.flag == "term"


def is_c71_base_result(elt_result):
    return elt_result.flag == "c7.1"


def group_c7_element_results(
    element_results: list[ElementResult | AnswerResult], pdfinsight: PdfinsightReader, model: BaseModel
) -> C7GroupResult:
    """
    根据以下规则确定交易描述段落范围:
    1. 所有答案的父章节都是兄弟章节，则按照每个章节1个分组 TODO 拆分root来判断
    2. 答案所属章节下，只有1个交易，则整个章节为一个分组
    3. 答案所属章节下，有多个交易，则按照多个交易位置做拆分
    """

    def find_elements(start_index, end_index, prefix_fmt=None) -> list[dict]:
        result_elements = []
        pos = all_indices.index(start_index)
        if not (prefix_fmt or start_index in footnote_indices) and pos < len(all_indices) - 1:
            end_index = max(end_index, all_indices[pos + 1])
        for idx in range(start_index + 1, end_index):
            if (not prefix_fmt and idx in all_indices) or idx in pdfinsight.syllabus_reader.elt_syllabus_dict:
                break
            _, next_elt = pdfinsight.find_element_by_index(idx)
            if pdfinsight.is_skip_element(
                next_elt,
                aim_types={
                    PDFInsightClassEnum.PARAGRAPH.value,
                    PDFInsightClassEnum.TABLE.value,
                    PDFInsightClassEnum.SHAPE.value,
                },
            ):
                continue
            if is_para_elt(next_elt):
                next_text = clean_txt(next_elt["text"])
                if P_C7_FOLLOW_PREFIX.search(next_text) or P_C7_FOOTNOTE_PREFIX.search(next_text):
                    if not prefix_fmt:
                        break
                    # 遇到序号就终止
                    if get_prefix_format(next_text) == prefix_fmt:
                        break
            result_elements.append(next_elt)
        return result_elements

    all_indices = set()
    result = defaultdict(list)
    # 记录非note章节元素块的章节及祖父章节
    elt_syllabus, syllabus_ranges = {}, {}
    has_prefix_indices, footnote_indices = set(), set()
    parent_children, syllabus_elt_indices, parent_elt_indices = defaultdict(set), defaultdict(set), defaultdict(set)
    ct_rpt_chapter_ranges = {}
    for element_result in element_results:
        # 仅在c7.1的答案周围做提取
        if not is_c71_base_result(element_result):
            continue
        element = element_result.element
        elt_index = element["index"]
        all_indices.add(elt_index)
        root_title = pdfinsight.get_root_syllabus_title(element)
        if root_title and P_NOTE_CHAPTER.search(root_title) and is_note_element(pdfinsight, element):
            footnote_indices.add(elt_index)
        strict_parent_syllabus, agreement_syllabus, parent_syllabus = find_syllabus(pdfinsight, element)
        if parent_syllabus:
            set_syllabus_range(syllabus_ranges, parent_syllabus["index"], parent_syllabus["range"])
            set_syllabus_range(ct_rpt_chapter_ranges, parent_syllabus["index"], parent_syllabus["range"])
            parent_elt_indices[parent_syllabus["index"]].add(elt_index)
        if agreement_syllabus:
            syllabus_index = agreement_syllabus["index"]
            # TODO 注意这里有误识别，所以用答案中有章节标题来跳过该章节
            # https://jura6-lir.paodingai.com/#/hkex/annual-report-checking/report-review/261202?fileId=69455&schemaId=5&rule=C7.2&delist=0 index=774
            if agreement_syllabus["element"] in all_indices:
                continue
            elt_syllabus[elt_index] = agreement_syllabus
            syllabus_elt_indices[syllabus_index].add(elt_index)
            # 存储各章节范围，及章节对应关系
            set_syllabus_range(syllabus_ranges, syllabus_index, agreement_syllabus["range"])
            if strict_parent_syllabus is None:
                parent_children[parent_syllabus["index"]].add(syllabus_index)
                # 这种场景说明CT是包含following描述的兄弟章节，这里的父章节可能是root章节，所以叠加有答案的子章节的范围，不取父章节本身的范围
                set_syllabus_range(syllabus_ranges, parent_syllabus["index"], agreement_syllabus["range"])
            else:
                parent_index = strict_parent_syllabus["index"]
                set_syllabus_range(syllabus_ranges, parent_index, strict_parent_syllabus["range"])
                parent_children[parent_index].update({syllabus_index, *strict_parent_syllabus["children"]})
        # 找出特殊序号
        if is_para_elt(element) and P_C7_FOLLOW_PREFIX.search(element["text"]):
            has_prefix_indices.add(elt_index)
    has_chapter_indices = set()
    grouped_syll_ranges = {}
    for parent_index, children in parent_children.items():
        if not children:
            continue
        child_min, child_max = 9999999999, 0
        key_indices = set()
        for child_index in children:
            if child_range := syllabus_ranges.get(child_index):
                child_min, child_max = min(child_min, child_range[0]), max(child_max, child_range[1])
                has_chapter_indices.update(syllabus_elt_indices.get(child_index) or [])
                grouped_syll_ranges[child_index] = child_range
                for elt_index in sorted(syllabus_elt_indices.get(child_index) or []):
                    key_indices.add(elt_index)
                    result[elt_index] = model.get_candidate_elements_by_range(
                        syllabus_ranges[child_index], need_score=False
                    )
                    break
            elif child := pdfinsight.syllabus_reader.syllabus_dict.get(child_index):
                # 补充未提取的子章节
                child_range = child["range"]
                child = find_real_syllabus(pdfinsight, child)
                child_min, child_max = min(child_min, child_range[0]), max(child_max, child_range[1])
                key_index = child_range[0] + 1
                key_indices.add(key_index)
                result[key_index] = model.get_candidate_elements_by_range(child["range"], need_score=False)
            else:
                continue
        # 大章节和小章节之间的部分，追加给每个小章节
        parent_start, parent_end = syllabus_ranges[parent_index]
        for start, end in [(parent_start, child_min), (child_max, parent_end)]:
            if not 1 < end - start < 5:
                continue
            for index in key_indices:
                result[index].extend(model.get_candidate_elements_by_range([parent_start, child_min], need_score=False))

    # 有小序号开头，则按照序号分组
    if rest_indices := (all_indices - has_chapter_indices):
        all_indices = sorted(all_indices)
        parent_map = {}
        for parent_index, indices in parent_elt_indices.items():
            for index in indices:
                parent_map[index] = parent_index
        for element_result in sort_c7_results(element_results):
            element = element_result.element
            element_index = element["index"]
            if element_index not in rest_indices:
                continue
            result[element_index].append(element)
            start, end = syllabus_ranges[parent_map[element_index]]
            prefix_format = None
            if element_index in has_prefix_indices | footnote_indices:
                text = clean_txt(element["text"])
                # 当前段落带序号，找到下个序号为止
                # 注意下方这个(i)被识别为()，导致prefix_format为空：
                # https://jura6-lir.paodingai.com/#/hkex/annual-report-checking/report-review/259342?fileId=69083&schemaId=5&rule=C7.2&delist=0 index=1569
                prefix_format = get_prefix_format(text)
            result[element_index].extend(find_elements(element_index, end, prefix_format))
    return C7GroupResult(result, grouped_syll_ranges, ct_rpt_chapter_ranges)


predictor_options = []
