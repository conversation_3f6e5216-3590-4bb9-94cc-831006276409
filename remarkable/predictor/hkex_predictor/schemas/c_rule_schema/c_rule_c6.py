from itertools import chain

from remarkable.common.common_pattern import P_SEN_SEPARATOR
from remarkable.common.constants import AnswerValueEnum
from remarkable.common.pattern import (
    MATCH_ALWAYS,
    MatchMulti,
    NeglectPattern,
    PositionPattern,
)
from remarkable.common.util import clean_txt
from remarkable.optools.table_util import P_NUMBER
from remarkable.predictor.common_pattern import R_DATES
from remarkable.predictor.default_predictor.utils import is_table_result
from remarkable.predictor.hkex_predictor.schemas.pattern import P_DATE, P_SAVE_AS_ABOVE, R_MONEY_UNIT, gen_regex_by_date
from remarkable.predictor.models.base_model import BaseModel
from remarkable.predictor.schema_answer import ParagraphResult, PredictorResult

P_BONUS = NeglectPattern.compile(
    match=r"(discretionary|performance[\s-]*related)?\b(incentive payments?|bonus(es)?)\b|\bincentive performance bonus",
    unmatch=r"\bsalar(y|ies)\b",
)

P_DIRECTOR = MatchMulti.compile(r"\bdirectors?\b", operator=any)

P_NONE_STR = r"\b(no|not|none|nor|nil|neither)\b"

P_NO_BONUS = MatchMulti.compile(rf"{P_NONE_STR}.*?\b(emoluments?|bonus(es)?)\b", operator=any)

P_REWARD = (
    r"join[,，]? or upon joining|accept(ing)? office|as an inducement|incentive payment|received.*?other emoluments?"
)

P_NEGLECT_REWARD = r"\bperformance related incentive payment"

P_NO_REWARD = NeglectPattern.compile(
    match=MatchMulti.compile(
        rf"{P_NONE_STR}.*?\b(remunerations?|payment|amounts?|emoluments?|directors?)\b", P_REWARD, operator=all
    ),
    unmatch=NeglectPattern.compile(match=r"five highest paid", unmatch=r"\bdirectors?"),
)


P_COMPENSATION = MatchMulti.compile(
    r"\b(termination benefits?|benefits? in respect of (the )?termination|termination.*?appointments?)",
    MatchMulti.compile(
        r"\bcompensation\b", r"loss of office|termination of service|non-renewal of employment", operator=all
    ),
    operator=any,
)

P_NO_COMPENSATION = MatchMulti.compile(P_COMPENSATION, P_NONE_STR, operator=all)

P_HAS_COMPENSATION = MatchMulti.compile(
    P_COMPENSATION,
    MatchMulti.compile(rf"{R_MONEY_UNIT}(\d{{1,3}}(,\d{{3}})*(\.\d+)?).*?is\s*paid\s*to", operator=any),
    operator=all,
)

SYLLABUS_REGS = [r"(Director|Chief)s?.*?emoluments", r"EMPLOYEE BENEFITS EXPENSE", r"NOTES.*?FINANCIAL STATEMENTS"]

P_IGNORE_ABOVE = MatchMulti.compile(
    rf"^{R_MONEY_UNIT}.\d+$", r"[^\s0-9a-z）)”.]?[\u4e00-\u9fa5][^a-z]*[\u4e00-\u9fa5][^\s0-9a-z(（“]?", operator=any
)
P_MONEY_UNIT = MatchMulti.compile(rf"^{R_MONEY_UNIT}", operator=any)
P_SUPERVISOR = MatchMulti.compile(r"\bsupervisor", operator=any)
P_DATES = MatchMulti.compile(*R_DATES, operator=any)
P_YEAR = MatchMulti.compile(r"(^|[^\d]\s*)(?P<year>20\d{2})($|\s*[^\d])", operator=any)
P_NON_DIRECTOR = NeglectPattern.compile(
    match=r"highest (paid|individual)|\bnon-director|\bsupervisor", unmatch=r"\b(?<!non-)director"
)
P_NOT_EXCESSIVE = MatchMulti.compile(r"\bnot excessive\b", operator=any)
P_RECOGNISE = MatchMulti.compile(r"\brecognise", operator=any)
R_EMOLUMENT = [
    r"termination benefit",
    r"benefit.*?director",
    r"remuneration of director|director.*?remuneration",
    r"remuneration (policy|committee)",
    r"emoluments? of (the )?director|director.*?emolument",
    r"service contracts? of director",
    r"highest paid individual",
]
P_ROLE_FUNCTIONS_AS_FOLLOWING = PositionPattern.compile(
    r"the.*?roles? and function", r"\bremunerations? committee", r"as follows?[:：]"
)


def get_date_from_col_row(col_header_texts, row_header_texts):
    dates = []
    for text in chain(col_header_texts, row_header_texts):
        if P_DATE.search(text) or P_YEAR.search(text):
            dates.append(text)
    return dates


def match_year_end(table, year_end_reg, report_year):
    dates = get_date_from_col_row(table.col_header_texts.values(), table.row_header_texts.values())
    if dates:
        if any(year_end_reg.search(date) or report_year in date for date in dates):
            return True
        else:
            return False
    if any(
        year_end_reg.search(clean_txt(text, remove_cn_text=True)) or report_year in text
        for text in table.possible_titles
    ):
        return True
    date_above = [
        above
        for above in table.elements_above
        if (P_DATES.search(above["text"]) or P_YEAR.search(above["text"]) and not P_IGNORE_ABOVE.search(above["text"]))
    ]
    if date_above and (year_end_reg.search(date_above[0]["text"]) or report_year in date_above[0]["text"]):
        return True
    # for element in date_above:
    #     if P_IGNORE_ABOVE.search(element["text"]):
    #         continue
    #     if year_end_reg.search(element["text"]) or report_year in element["text"]:
    #         return True
    #     # 最近段落没有匹配到当年时间的就退出
    #     break
    return False


def match_director(table):
    if any(P_DIRECTOR.search(text) for text in chain(table.col_header_texts.values(), table.row_header_texts.values())):
        return True
    for element in table.elements_above:
        if P_IGNORE_ABOVE.search(element["text"]):
            continue
        if P_DIRECTOR.search(element["text"]):
            return True
        break
    return False


def exclude_answer_element_cells(answer, header_texts, tags, start_reg, end_reg=MATCH_ALWAYS, reverse=False):
    row_start, row_end = None, None
    for current_index, (text, tag) in enumerate(zip(header_texts, tags)):
        if start_reg.search(text) and tag == "subtitle":
            row_start = current_index
        if (
            row_start
            and row_start != current_index
            and (
                tag != "dataline" and end_reg.search(text) or current_index == min(len(header_texts) - 1, len(tags) - 1)
            )
        ):
            row_end = current_index
        if row_start and row_end:
            break
    if row_start is not None and row_end is not None:
        for element_result in answer.element_results:
            if reverse:
                element_result.parsed_cells = [
                    cell for cell in element_result.parsed_cells if not (row_start <= cell.rowidx <= row_end)
                ]
            else:
                element_result.parsed_cells = [
                    cell for cell in element_result.parsed_cells if (row_start <= cell.rowidx < row_end)
                ]
            element_result.chars = [char for cell in element_result.parsed_cells for char in cell.raw_cell["chars"]]
    return answer


def ns_when_no_values(answers: list[PredictorResult], **kwargs):
    """
    NS条件：
    1. 数据全部为0/短横线/nil
    2. 有明确的否定词no, neither…nor, none等
    """
    answers = BaseModel.get_common_predictor_results(answers)
    # filter answers by year_end
    answers_ = []
    predictor = kwargs["predictor"]
    year_end = predictor.prophet.metadata.get("year_end")
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6585#note_691637
    # 过滤掉横向表格并且数据列只有两列，这种表格大概率是bonus总表
    for answer in answers[::-1]:
        if is_table_result(answer.element_results[0]):
            result = answer.element_results[0]
            direction = "row"
            if len(set(cell.colidx for cell in result.parsed_cells)) == 1:  # noqa: C401
                direction = "col"
            if direction == "row":
                table = result.parsed_table
                valid_col_header_texts = [header_text for header_text in table.col_header_texts if header_text]
                if len(valid_col_header_texts) == 2:
                    answers.remove(answer)
    if year_end:
        report_year = year_end[:4]
        year_end_reg = gen_regex_by_date(year_end)
        for answer in answers:
            if is_table_result(answer.element_results[0]):
                result = answer.element_results[0]
                table = result.parsed_table
                # 只要本年以及与director相关的表格
                if match_year_end(table, year_end_reg, report_year) and match_director(table):
                    answers_.append(answer)
            else:
                answers_.append(answer)
    for answer in answers_:
        if is_table_result(answer.element_results[0]):
            result = answer.element_results[0]
            table = result.parsed_table
            row_header_texts = []
            row_tags = []
            for region in table.regions:
                row_header_texts.extend(
                    " ".join(header.text for header in header_list) for header_list in region.row_header_list
                )
                row_tags.extend(region.row_tags)
            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6585#note_691668
            # 排除表格中supervisor的单元格
            exclude_answer_element_cells(answer, row_header_texts, row_tags, start_reg=P_SUPERVISOR, reverse=True)
            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6841#note_708671
            # 排除表格中不在当年内的单元格
            if year_end:
                year_end_reg = gen_regex_by_date(year_end)
                exclude_answer_element_cells(answer, row_header_texts, row_tags, start_reg=year_end_reg, end_reg=P_DATE)
    has_table = False
    for answer in answers_:
        if answer.answer_value != AnswerValueEnum.PS:
            continue
        for element_result in answer.element_results:
            if not is_table_result(element_result):
                continue
            has_table = True
            if any(
                P_NUMBER.search(clean_txt(cell.text, remove_cn_text=True))
                and not P_MONEY_UNIT.search(clean_txt(cell.text, remove_cn_text=True))
                for cell in element_result.parsed_cells
                if not (cell.is_header or cell.rowidx == 0)
            ):
                return answers_
    if has_table:
        for answer in answers_:
            answer.update_answer_value(AnswerValueEnum.NS)

    return filter_answer_element(answers_, **kwargs)


def filter_answer_element(answers: list[PredictorResult], **kwargs):
    pdfinsight = kwargs["predictor"].pdfinsight
    answers = BaseModel.get_common_predictor_results(answers)
    for answer in answers:
        if isinstance(answer.element_results[0], ParagraphResult):
            for element_result in answer.element_results[:]:
                for para in pdfinsight.find_elements_near_by(
                    element_result.element["index"],
                    amount=5,
                    step=-1,
                    aim_types="PARAGRAPH",
                ):
                    if P_ROLE_FUNCTIONS_AS_FOLLOWING.search(para["text"]):
                        answer.element_results.remove(element_result)
                        break

    return [answer for answer in answers if answer.element_results]


R_EMOLUMENT = r"(EMOLUMENT|REMUNERATION|BENEFIT)"

P_C61_SYLLABUS = [
    rf"director.*?{R_EMOLUMENT}",
    rf"{R_EMOLUMENT}.*?of director",
]

P_C62_SYLLABUS = [
    rf"DIRECTOR.*?{R_EMOLUMENT}",
    rf"{R_EMOLUMENT}.*?OF DIRECTOR",
    rf"EMPLOYEE.*?{R_EMOLUMENT}",
    r"PAID EMPLOYEE",
]

P_C63_SYLLABUS = [rf"{R_EMOLUMENT}|PAID EMPLOYEE"]

predictor_options = [
    {
        "path": ["C6.1"],
        "post_process": ns_when_no_values,
        "models": [
            {
                "name": "multi_models",
                "operator": "union",
                "enum_every_element": True,
                "enum": AnswerValueEnum.PS.value,
                "require_unique_element": True,
                "models": [
                    {
                        "name": "special_cells",
                        "multi_elements": True,
                        "multi": True,
                        "extend_candidates_syllabus_regs": P_C61_SYLLABUS + [r"consolidated financial statement"],
                        "col_header_pattern": P_BONUS,
                        "enum": AnswerValueEnum.PS.value,
                    },
                    {
                        "name": "special_cells",
                        "multi_elements": True,
                        "syllabus_regs": P_C61_SYLLABUS,
                        "extend_candidates_syllabus_regs": P_C61_SYLLABUS,
                        "row_header_pattern": P_BONUS,
                        "enum": AnswerValueEnum.PS.value,
                    },
                ],
            },
            {
                "name": "para_match",
                "paragraph_pattern": P_NO_BONUS,
                "extend_candidates_syllabus_regs": P_C61_SYLLABUS,
                "enum": AnswerValueEnum.NS.value,
            },
            {
                "name": "score_filter",
                "threshold": 0.8,
                "enum": AnswerValueEnum.PS.value,
            },
        ],
    },
    {
        "path": ["C6.2"],
        "post_process": ns_when_no_values,
        "models": [
            {
                "name": "para_match",
                "extend_candidates_syllabus_regs": P_C62_SYLLABUS,
                "paragraph_pattern": P_NO_REWARD,
                "enum": AnswerValueEnum.NS.value,
            },
            {
                "name": "special_cells",
                "multi": False,
                "multi_elements": True,
                "extend_candidates_syllabus_regs": P_C62_SYLLABUS,
                "col_header_pattern": NeglectPattern.compile(match=P_REWARD, unmatch=P_NEGLECT_REWARD),
                "enum": AnswerValueEnum.PS.value,
            },
            {
                "name": "score_filter",
                "threshold": 0.9,
                "enum": AnswerValueEnum.NS.value,
            },
        ],
    },
    {
        "path": ["C6.3"],
        "post_process": ns_when_no_values,
        "models": [
            {
                "name": "para_match",
                # "syllabus_regs": SYLLABUS_REGS,
                "para_separator": P_SEN_SEPARATOR,
                "extend_candidates_syllabus_regs": P_C63_SYLLABUS,
                "neglect_syllabus_regs": [
                    NeglectPattern.compile(match=r"retirement benefit", unmatch=r"termination benefit"),
                    # MatchMulti.compile(r"five highest paid", operator=any),
                    MatchMulti.compile(r"REPORT OF THE DIRECTOR", operator=any),
                ],
                "neglect_pattern": [P_RECOGNISE, P_NON_DIRECTOR, P_SAVE_AS_ABOVE, P_NOT_EXCESSIVE],
                "paragraph_pattern": P_NO_COMPENSATION,
                "enum": AnswerValueEnum.NS.value,
            },
            {
                "name": "para_match",
                "para_separator": P_SEN_SEPARATOR,
                "extend_candidates_syllabus_regs": P_C63_SYLLABUS,
                # "syllabus_regs": SYLLABUS_REGS,
                "neglect_pattern": P_NON_DIRECTOR,
                "paragraph_pattern": P_HAS_COMPENSATION,
                "enum": AnswerValueEnum.PS.value,
            },
            {
                "name": "special_cells",
                "multi": False,
                "multi_elements": True,
                "col_header_pattern": MatchMulti.compile(r"compensation", operator=any),
                "enum": AnswerValueEnum.PS.value,
            },
            {
                "name": "score_filter",
                "threshold": 0.9,
                "enum": AnswerValueEnum.NS.value,
            },
        ],
    },
]
