from remarkable.common.common import get_date_by_offset_days
from remarkable.common.common_pattern import R_MIDDLE_DASH
from remarkable.common.constants import AnswerValueEnum
from remarkable.common.pattern import MatchMulti, PositionPattern
from remarkable.predictor.hkex_predictor.schemas.list_rule_schema.list_rule_b1 import R_UNUSED, R_UTILISE
from remarkable.predictor.hkex_predictor.schemas.list_rule_schema.list_rule_b8 import (
    table_models_for_proceeds,
)
from remarkable.predictor.hkex_predictor.schemas.list_rule_schema.list_rule_b9 import (
    P_UNUSED,
)
from remarkable.predictor.hkex_predictor.schemas.list_rule_schema.list_rule_b10 import (
    B10_C1_3_PS_SEN_MODEL,
    P_B10_COLUMNS,
    P_B10_USE_CHANGE,
    B10PostProcessor,
)
from remarkable.predictor.hkex_predictor.schemas.pattern import R_NOT
from remarkable.predictor.hkex_predictor.schemas.util import get_sentence_model
from remarkable.predictor.models.fund_raising_group import P_AS_AT_DATES


class C13PostProcessor(B10PostProcessor):
    def filter_ps_table_cells(self, parsed_cells, group_type):
        """
        C1.3 表格PS标准：
        1. 实际使用的描述为“已按照预期使用完毕”
        2. 同时有：计划使用+实际使用+未使用
        """
        # 实际用途/timeline列中描述“已按照预期使用完毕”，取这一列
        # http://************:55647/#/project/remark/294467?treeId=6800&fileId=70779&schemaId=18&projectId=6800&schemaKey=C1.3
        self.table_is_ns, cells = self.get_detail_cells(parsed_cells)
        if cells:
            return cells
        intended_cols, actual_use_cols, revised_cols, intend_cols, used_cols, unused_cols = self.split_col_cells(
            parsed_cells
        )
        # 找出不包含日期、或日期为期末、或日期为下期期初的列，若没有则丢弃答案
        col_date_dict = self.get_date_from_col_header(parsed_cells, P_AS_AT_DATES)
        invalid_indices = set()
        if col_date_dict and self.year_end:
            invalid_indices = {
                col
                for col, date_str in col_date_dict.items()
                if date_str not in {self.year_end, get_date_by_offset_days(self.year_end, 1)}
            }
            # 所有有日期的列，都是非法列，说明不是需要的表格
            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6243#note_664486
            if set(col_date_dict) == invalid_indices:
                return []
        # revised中的日期不是当年：https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6840#note_704396
        # if revised_cells := [c for c in parsed_cells if c.colidx in revised_cols - invalid_indices]:
        # 需求又变了，又不用判断往年了，有revised就是NS
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6840#note_711085
        if revised_cells := [c for c in parsed_cells if c.colidx in revised_cols]:
            # C1.3表格中，若有revised列，则判定为NS
            if self.cells_have_value(revised_cells):
                self.table_is_ns = True
            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6246#note_663881
            return revised_cells + [c for c in parsed_cells if c.colidx in intended_cols]
        if intended_cols and actual_use_cols:
            # 有明确的intended 和actual use对照组，则认为是PS
            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/5611#note_635192
            # http://************:55647/#/project/remark/293642?treeId=3726&fileId=70658&schemaId=18&projectId=3726&schemaKey=C1.3
            return self.add_first_col_cells(
                [c for c in parsed_cells if c.colidx in intended_cols | actual_use_cols | unused_cols]
            )
        if used_cols and intend_cols and unused_cols:
            return self.add_first_col_cells(
                [c for c in parsed_cells if c.colidx in used_cols | intend_cols | unused_cols]
            )
        return []

    def is_ns_table(self, parsed_cells):
        return self.table_is_ns

    def is_ns_para(self, text):
        if P_B10_USE_CHANGE.search(text):
            # http://************:55647/#/project/remark/294051?treeId=16378&fileId=70719&schemaId=18&projectId=16378&schemaKey=C1.3
            # 只要段落描述中包含“用途发生变化”，则判定为NS
            self.answer_is_ns = True
            return True
        return False


# NS模型： 没有未使用的资金
# http://************:55647/#/project/remark/294533?treeId=37743&fileId=70789&schemaId=18&projectId=37743&schemaKey=C1.1.1
# http://************:55647/#/project/remark/295216?treeId=10707&fileId=70877&schemaId=18&projectId=10707&schemaKey=C1.1.1
NS_NO_UNUSED = get_sentence_model(
    [
        PositionPattern.compile(R_NOT, r"proceeds", rf"brought\s*forward|{R_UNUSED}"),
        PositionPattern.compile(R_NOT, R_UNUSED, r"proceeds"),
        # http://************:55647/#/project/remark/294236?treeId=45609&fileId=70746&schemaId=18&projectId=45609&schemaKey=C1.2.1
        # PositionPattern.compile(r"\b(no|not|nor|neither)\b", R_USED, r"\bany\b", r"proceeds"),
    ],
    skip_pattern=MatchMulti.compile(r"((save|except)\s*(for|in|as)|except)\s*disclosed", operator=any),
    enum=AnswerValueEnum.NS,
    multi_elements=True,
    threshold=0,
)

predictor_options = [
    {
        # ------------------------------
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/5611
        # ------------------------------
        "path": ["C1.3"],
        "fake_leaf": True,
        "models": [
            {
                "name": "use_of_proceeds_group",
                "element_candidate_count": 20,
                "post_process_answers": C13PostProcessor(),
                "column_configs": {
                    "Whether the proceeds were used, or are proposed to be used, according to the issuer’s intention and the reason for change or delay": [
                        {
                            "name": "multi_models",
                            "operator": "union",
                            "require_unique_element": True,
                            "enum": AnswerValueEnum.PS.value,
                            "models": [
                                # PS表格：intend use列+actual use列+unused列，或某列中提到已按照预期使用完毕
                                # NS表格：revised/reallocation/adjusted列，或某列中包含未按照预期使用
                                *table_models_for_proceeds([*P_B10_COLUMNS, P_UNUSED], enum=AnswerValueEnum.PS.value),
                                # PS句子：按照预期使用/用途无变化/按照公告使用/
                                # NS句子：用途发生了变化/未按照预期用途使用。后处理对句子进行判断，用途发生变化则表示为NS
                                B10_C1_3_PS_SEN_MODEL,
                            ],
                        },
                        # 预计使用 + 实际使用
                        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6840#note_703842
                        {
                            "name": "multi_models",
                            "operator": "all",
                            "strict_merge": False,
                            "require_unique_element": True,
                            "enum": AnswerValueEnum.PS.value,
                            "models": [
                                {
                                    "name": "para_match",
                                    "as_follow_pattern": PositionPattern.compile(
                                        r"proceeds", r"will", rf"{R_UTILISE}d", r"[:：]$"
                                    ),
                                },
                                {
                                    "name": "para_match",
                                    "as_follow_pattern": PositionPattern.compile(
                                        r"(has|had|have)", rf"{R_UTILISE}d", r"proceeds", r"[:：]$"
                                    ),
                                },
                            ],
                        },
                        get_sentence_model(
                            [
                                PositionPattern.compile(R_NOT, r"proceeds", rf"brought\s*forward|{R_UNUSED}"),
                                PositionPattern.compile(
                                    r"\s(not|neither|nor)\s",
                                    r"\bany\s",
                                    rf"un{R_MIDDLE_DASH}?{R_UTILISE}d",
                                    r"proceeds",
                                ),
                            ],
                            # skip_pattern=MatchMulti.compile(r"((save|except)\s*(for|in|as)|except)\s*disclosed", operator=any),
                            enum=AnswerValueEnum.NS,
                            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6840#note_707092
                            skip_pattern=[r"\s*at\s*the\s*date\s*of\s*(this|the)\s*(annual\s*)?report"],
                            multi_elements=True,
                        ),
                    ]
                },
            }
        ],
    },
]
