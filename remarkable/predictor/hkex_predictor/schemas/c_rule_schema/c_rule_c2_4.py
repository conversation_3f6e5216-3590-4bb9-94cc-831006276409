import re
from enum import IntEnum

from remarkable.common.common import get_date_by_offset_days, is_para_elt
from remarkable.common.common_pattern import (
    P_CELL_NIL,
    P_ONLY_NOTES,
    P_SEN_SEPARATOR,
    R_AS_AT,
    R_CURRENCY,
    R_MIDDLE_DASH,
    R_MIDDLE_DASHES,
)
from remarkable.common.constants import AnswerValueEnum
from remarkable.common.pattern import (
    MatchMulti,
    NeglectPattern,
    PositionPattern,
)
from remarkable.common.util import split_paragraph
from remarkable.converter.hkex import P_CURRENT_DATE
from remarkable.pdfinsight.parser import P_HEADER_UNIT, ParsedTable, ParsedTableCell
from remarkable.predictor.common_pattern import R_EN_MONTH
from remarkable.predictor.default_predictor.utils import is_table_result
from remarkable.predictor.hkex_predictor.model_util import get_foot_note_by_cells
from remarkable.predictor.hkex_predictor.models.c2 import P_NS_NO_MORE_THAN_5_PERCENT
from remarkable.predictor.hkex_predictor.schemas.c_rule_schema.c_rule_c2 import (
    P_C2_DATE,
    P_C2_ONLY_YEAR,
    C2PostProcessor,
    extend_significant_elements,
    filter_c2_cells_by_date,
    get_c2_table_title,
)
from remarkable.predictor.hkex_predictor.schemas.list_rule_schema.list_rule_b77 import P_NOTE_PREFIX
from remarkable.predictor.hkex_predictor.schemas.list_rule_schema.post_processor import BasePostProcessor
from remarkable.predictor.hkex_predictor.schemas.pattern import P_NOTE_CHAPTER, R_MONEY_UNIT
from remarkable.predictor.models.base_model import BaseModel
from remarkable.predictor.models.special_cells import LastMode
from remarkable.predictor.predictor_metadata.rule_c2 import (
    P_C2_NEG_ACCOUNTS,
    P_C2_NEG_ELEMENTS,
    P_C2_POSSIBLE_CHAPTERS,
    P_C2_TOTAL_ROW_HEADER,
    R_COMPANY_NAMES,
    R_FIXED_RATE,
    R_FVTOCI,
    R_FVTPL,
)
from remarkable.predictor.schema_answer import PredictorResult, TableCellsResult
from remarkable.predictor.utils import extract_dates

P_C2_4_TABLE_TITLE = MatchMulti.compile(r"detail.*significant\s*investment", operator=any)
# book value: https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7436#note_759367
P_CARRYING_AMOUNT = PositionPattern.compile(r"carrying\s*(amount|value)|book\s*value", rf"{R_MONEY_UNIT}\d")
# R_VALUE_AMOUNT = r"\b((fair|market|carrying|book)\s*value(s|d)?\b(?!\s*(change|through|gain|loss))|carrying\s*amount)"
R_NEG_VALUE_PREFIX = (
    r"(?<!changes\sin\s)(?<!change\sin\s)(?<!(gain|loss)\sin\s)(?<!(increase|decrease)\sin\s)"  # (?<!determinable )
)
R_VALUE_AMOUNT = rf"{R_NEG_VALUE_PREFIX}\b(fair|market)\s*value[sd]?\b(?!\s*(change|gain|loss|increase|decrease))"

# changes in fair value: http://************:55647/#/project/remark/409332?treeId=9934&fileId=113570&schemaId=18&projectId=17&schemaKey=C2.4
R_HEADER_VALUE_AMOUNT = r"|".join(
    [
        # http://************:55647/#/project/remark/269023?treeId=7151&fileId=70623&schemaId=18&projectId=17&schemaKey=C2.4
        r"(book|market)\s*value",
        r"carrying\s*(amount|value)",
        r"(?<!changes\sin\s)(?<!change\sin\s)\bfair\s*va\b",
        r"(?<!changes\sin\s)(?<!change\sin\s)\bfair\s*value[sd]?\b(?!\s*(change|through|gain|loss))",
        # http://************:55647/#/project/remark/416995?treeId=28543&fileId=114427&schemaId=18&projectId=17&schemaKey=C2.4
        r"book\s*balance",
    ]
)
R_HEADER_VALUE_NEG = r"\b(catego|gain|loss|increase|decrease|change|determi)"
P_MONEY_HEADER = MatchMulti.compile(r"[$¥€￡].000", R_CURRENCY, r"million", operator=any)
P_CELL_MONEY = MatchMulti.compile(r"^[（(]?[\d,.]+[)）]?$", rf"^{R_MIDDLE_DASH}$", operator=any)
# https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7436#note_760804
P_C24_ROW_HEADER = MatchMulti.compile(
    *R_COMPANY_NAMES,
    r"\b(Bond|Bill|REIT|ETF|SP|WMP|Trust(?!s?\s*[Ss]hare)|Fund|Deposit|Portfolio|Commodit(y|ie))\b",
    r"(?i:convertible|treasur[ey]\s*(bond|bill))",
    # http://************:55647/#/project/remark/420076?treeId=8556&fileId=114774&schemaId=18&projectId=17&schemaKey=C2.1.1
    # http://************:55647/#/project/remark/410522?treeId=18134&fileId=113704&schemaId=18&projectId=17&schemaKey=C2.1.1
    r"(?i:(management|investment|structured)\s*product)\b",
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7436#note_763007
    r"China\s*Asset\s*Management\b",
    # http://************:55647/#/project/remark/419067?treeId=4055&fileId=114660&schemaId=18&projectId=17&schemaKey=C2.4&page=242
    # r"(?i:Contingent\s*consideration)",
    operator=any,
    flag=0,
)
# https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7436#note_760106
P_C24_INVALID_ROW_HEADER = MatchMulti.compile(
    r"\bsubsidiar(y|ies)|companies",
    r"investments|instruments|securities|liabilities",
    r"\b(products|deposits|(?<!convertible )bonds|funds|banks|assets|bills)\b",
    # http://************:55647/#/project/remark/419441?treeId=5436&fileId=114702&schemaId=18&projectId=17&schemaKey=C2.4&page=307
    rf"^[{R_MIDDLE_DASHES}\s]*(([Un]n)?[Ll]isted|[Oo]thers)\b",
    # http://************:55647/#/project/remark/376729?treeId=2985&fileId=112921&schemaId=18&projectId=17&schemaKey=C2.4&page=143
    rf"{R_CURRENCY}\b",
    P_C2_TOTAL_ROW_HEADER,
    operator=any,
    flag=0,
)
R_C24_AS_AT = rf"\b(at|of)\s*([1-9]\d?(st|nd|rd|th)?\b|{R_EN_MONTH}|(the\s*)?(year.\s*)?(end|closing))"  # C2.4句子中包含如下关键词，才认为披露了唯一明确的fair value
R_C24_MULTI_INVESTEES_KW = r"respectively|mainly\s*included"
P_C24_ONE_INVESTEE = MatchMulti.compile(
    # http://************:55647/#/project/remark/407253?treeId=8019&fileId=113336&schemaId=18&projectId=17&schemaKey=C2.4
    r"\san?\s+([a-z]+\s+)?(company|entity)\b",
    # http://************:55647/#/project/remark/265089?treeId=3097&fileId=70232&schemaId=18&projectId=17&schemaKey=C2.6
    r"\san?\s+(fund|bank|investment)",
    # http://************:55647/#/project/remark/293963?treeId=3815&fileId=70706&schemaId=18&projectId=17&schemaKey=C2.4
    r"\sa\s*Hong\s*Kong\s*listed\s*company",
    # http://************:55647/#/project/remark/266194?treeId=45553&fileId=70453&schemaId=18&projectId=17&schemaKey=C2.4
    # http://************:55647/#/project/remark/418749?treeId=6944&fileId=114623&schemaId=18&projectId=17&schemaKey=C2.4
    # http://************:55647/#/project/remark/405911?treeId=3319&fileId=113185&schemaId=18&projectId=17&schemaKey=C2.4
    # a cluster: http://************:55647/#/project/remark/411099?treeId=9500&fileId=113769&schemaId=18&projectId=17&schemaKey=C2.6&page=26
    # XX shares or 0.26% of HKC: http://************:55647/#/project/remark/414392?treeId=11113&fileId=114137&schemaId=18&projectId=17&schemaKey=C2.6
    r"\b(deposit|fund|h[eo]lds?|holding|owne?d?|equity\s*interests?|capital|(?<!listed )[Ss]hares?(\s*or\s*[\d.]+[%％])?)\s*(of\s*the\s*[Gg]roup\s*)?(in|of|from)\s+(a\s(?!cluster|group)|an\s|(the\s*)?[A-Z])",
    r"\b(?<!listed )(?<!unlisted )(?<!equity )(?<!no )[Ii]nvestment\s*(income\s*)?(of\s*the\s*[Gg]roup\s*)?(in|from)\s+(an?\s|(the\s*)?[A-Z])",
    # http://************:55647/#/project/remark/266194?treeId=45553&fileId=70453&schemaId=18&projectId=17&schemaKey=C2.4
    PositionPattern.compile(r"\bentered\s*into\b", r"\bwith\s+([A-Z]|an?\s)", flag=0),
    # http://************:55647/#/project/remark/268835?treeId=6079&fileId=70597&schemaId=18&projectId=17&schemaKey=C2.6
    # http://************:55647/#/project/remark/296224?treeId=37978&fileId=71003&schemaId=18&projectId=17&schemaKey=C2.6
    r"\s(in|of)\s*(the|this|our)\s*([Ff]und(?! (are|were))|[Dd]eposit(?! (are|were))|[Ii]nvestment(?! (are|were)))\b",
    r"\b(\s+Fund|\s+Investment|\s+Deposit|\s+Bonds?)",
    NeglectPattern.compile(
        match=rf"{R_NEG_VALUE_PREFIX}\b(fair|book|carrying)\s*(value|amount)\s*of\s*([A-Z]|an?\s)",
        # 排除币种和复数形式描述：http://************:55647/#/project/remark/407508?treeId=7885&fileId=113365&schemaId=18&projectId=17&schemaKey=C2.4&page=154
        unmatch=rf"fair\s*values?\s*of\s*({R_CURRENCY}\d|[A-Z]+s\b)",
        flag=0,
    ),
    # http://************:55647/#/project/remark/268617?treeId=4962&fileId=70565&schemaId=18&projectId=17&schemaKey=C2.4
    # http://************:55647/#/project/remark/295209?treeId=9863&fileId=70876&schemaId=18&projectId=17&schemaKey=C2.4
    # http://************:55647/#/project/remark/296224?treeId=37978&fileId=71003&schemaId=18&projectId=17&schemaKey=C2.4
    rf"{R_NEG_VALUE_PREFIX}\bfair\s*value\s*of\s*(the|these)\s*(equity\s*interest|capital|([a-zA-Z]+\s+)?shares)",
    # http://************:55647/#/project/remark/412632?treeId=7144&fileId=113940&schemaId=18&projectId=17&schemaKey=C2.4
    # http://************:55647/#/project/remark/415658?treeId=15962&fileId=114277&schemaId=18&projectId=17&schemaKey=C2.4
    rf"{R_NEG_VALUE_PREFIX}\b(fair|book|carrying)\s*(value|amount)\s*of\s*(the|such|this)\s*(investment\b|fund\b|[A-Z])",
    # http://************:55647/#/project/remark/414392?treeId=11113&fileId=114137&schemaId=18&projectId=17&schemaKey=C2.4 todo 章节识别问题导致提取不到
    r"\b(such|the|this)\s*(investment|fund)\s*(was|is)\s*carried\s*at\s*fair\s*value\b",
    # http://************:55647/#/project/remark/414123?treeId=9364&fileId=114106&schemaId=18&projectId=17&schemaKey=C2.6
    r"\b(fair|book|carrying)\s*(value|amount)\s*of\s*this\s",
    # http://************:55647/#/project/remark/407226?treeId=44539&fileId=113333&schemaId=18&projectId=17&schemaKey=C2.6
    r"(products|subscribed)\s*from(\s*the)?\s+(an?\s|(the\s*)?[A-Z])",
    operator=any,
    flag=0,
)
P_C24_NEGLECT = MatchMulti.compile(
    R_FIXED_RATE,
    r"(equity|trust)\s*shares",  # 具有约……公允价值的信托份额
    r"(impaire|decrease|increase|pledge|subscribe)d",  # 被减值/增加/减少/质押/认购
    # # code:1911 year:2024
    # r"fair value of call option",
    # 利息/股息收入 https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7334 elt_index=111
    r"(interest|dividend)\s*income",
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7436#note_754306
    r"\b(on|at|of)\s*the\s*([a-z]+\s*)?date\s*of",
    # http://************:55647/#/project/remark/405813?treeId=6649&fileId=113174&schemaId=18&projectId=17&schemaKey=C2.4&page=131 index=1748
    r"before",
    # http://************:55647/#/project/remark/410333?treeId=7424&fileId=113682&schemaId=18&projectId=17&schemaKey=C2.4&page=267 index=2849
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7436#note_760163
    r"\b(certain|debt\b|loan\b|call\s*option)",
    r"(market|values?|fair\s*values?|carrying\s*amounts?)\s*of\s*(the\s*|these\s*)?([a-z]+\s+){0,2}(investments|securities|instruments|companies|products|funds|assets)",
    # http://************:55647/#/project/remark/409196?treeId=11684&fileId=113554&schemaId=18&projectId=17&schemaKey=C2.4
    r"\b(two|three|four|five|six|seven|eight|nine|ten)\b",
    # http://************:55647/#/project/remark/411259?treeId=52960&fileId=113787&schemaId=18&projectId=17&schemaKey=C2.4
    r"\b(un)?listed\s*investments?\s*of\s*((carrying|book|market|fair)\s*(value|amount)|FVT?PL|FVT?OCI)",
    r"fair\s*values?\s*of\s*the\s*(FVT?PL|FVT?OCI)\b",
    P_NS_NO_MORE_THAN_5_PERCENT,
    operator=any,
)


P_C24_FAIR_VALUES = {
    re.compile(rf"{R_MONEY_UNIT}[\d,.]+[,，]\s*{R_MONEY_UNIT}[\d,.]+\s*and\s*{R_MONEY_UNIT}\d"): 3,
    re.compile(rf"{R_MONEY_UNIT}[\d,.]+\s*and\s*{R_MONEY_UNIT}\d"): 2,
}
P_C24_NAME_HEADER = MatchMulti.compile(r"Name|Counterparty|company|investee|nature", operator=any)
# http://************:55647/#/project/remark/409332?treeId=9934&fileId=113570&schemaId=18&projectId=17&schemaKey=C2.4
P_C24_TBL_DATE = MatchMulti.compile(r"closing|end(ing)?\s*of", operator=any)


class FootnoteMatchResult(IntEnum):
    """脚注匹配结果枚举

    NOT_MATCHED - 脚注未匹配到任何信息，这种需要进一步判断自身
    MATCHED - 匹配到投资方公司，这种是目标行
    MATCHED_BLACKLIST - 脚注匹配到黑名单，这种情况要忽略
    """

    NOT_MATCHED = 0
    MATCHED = 1
    MATCHED_BLACKLIST = 2


def check_c24_footnote(pdfinsight, table, cells):
    """
    0 - 脚注未匹配到任何信息，这种需要进一步判断自身
    1 - 匹配到投资方公司，这种是目标行
    2 - 脚注匹配到黑名单，这种情况要忽略
    """
    if any(P_C24_ROW_HEADER.search(c.clean_text) for c in cells):
        return FootnoteMatchResult.MATCHED
    foot_elements = get_foot_note_by_cells(table, cells)
    if not foot_elements:
        data_rows = [row for row in table.rows if not row[0].is_col_header]
        if len(data_rows) == 1:
            # 只有1个数据行时，结合notes或表格下方的段落看一下
            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7436#note_759374
            if table.footnotes:
                foot_elements = [table.footnotes[0]]
            else:
                # 向下找两个元素块（第一个元素块可能是notes:）
                for offset in range(1, 3):
                    next_index = table.index + offset
                    if next_index < pdfinsight.max_index:
                        _, next_element = pdfinsight.find_element_by_index(next_index)
                        if not is_para_elt(next_element) or pdfinsight.syllabus_reader.is_syllabus_elt(next_element):
                            break
                        if P_ONLY_NOTES.search(next_element["text"]):
                            continue
                        foot_elements = [next_element]
                        break
        if not foot_elements:
            return FootnoteMatchResult.NOT_MATCHED
    if any(P_C2_NEG_ELEMENTS.search(e["text"]) for e in foot_elements):
        return FootnoteMatchResult.MATCHED_BLACKLIST
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7436#note_760804
    # 只能有1个投资方
    for sentence in split_paragraph(foot_elements[0]["text"]):
        # http://************:55647/#/project/remark/407307?treeId=2472&fileId=113342&schemaId=18&projectId=17&schemaKey=C2.4&page=139
        if P_C24_NEGLECT.search(sentence):
            return FootnoteMatchResult.NOT_MATCHED
        if P_C24_ONE_INVESTEE.search(sentence):
            return FootnoteMatchResult.MATCHED
        if P_C24_ROW_HEADER.search(sentence):
            return FootnoteMatchResult.MATCHED
        # llm_res = get_investment_companies_by_llm(element["text"], file_id)
        # if llm_res and len(llm_res.investees) == 1:
        #     return FootnoteMatchResult.MATCHED
        # break
    return FootnoteMatchResult.NOT_MATCHED


def modify_c24_answer_value(answers: list[PredictorResult], **kwargs):
    """
    C2.4特殊表格后处理
    https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7436
    """
    common_results = BaseModel.get_common_predictor_results(answers)
    if not any(r.answer_value == AnswerValueEnum.PS.value for r in common_results):
        return answers
    pdfinsight = kwargs["pdfinsight"]
    new_element_results = []
    for element_result in BaseModel.get_common_element_results(common_results):
        if not is_table_result(element_result):
            continue
        table: ParsedTable = element_result.parsed_table
        # 第一个数据列
        data_col = element_result.parsed_cells
        valid_row_indices = set()
        is_invalid_table = False
        for cell in data_col:
            row_idx, header_text = cell.rowidx, table.row_header_texts.get(cell.rowidx)
            # 需要跳过的行： 1. 没有row_header的行 2.subtitle行 3.col_header行 4.数据列为空/-/NIL/0的行 5. subtotal/total/less行
            if (
                not header_text
                or row_idx in table.all_subtitles
                or table.rows[row_idx][0].is_col_header
                or P_CELL_NIL.search(cell.text_no_superscript)
                or not P_CELL_MONEY.search(cell.text_no_superscript)
                or P_C2_TOTAL_ROW_HEADER.search(header_text)
            ):
                continue
            row_header_cells = [c for cells in table.row_header for c in cells if c.rowidx == row_idx]
            checked = check_c24_footnote(pdfinsight, table, row_header_cells)
            if checked == FootnoteMatchResult.MATCHED_BLACKLIST:
                # footnote中提及内容为黑名单，则跳过
                continue
            if checked == FootnoteMatchResult.MATCHED:
                # footnote中提及具体公司名称，则认为披露了明细，可以提取
                valid_row_indices.add(row_idx)
            elif P_C24_INVALID_ROW_HEADER.search(header_text):
                is_invalid_table = True
                break
            elif P_C24_ROW_HEADER.search(header_text):
                valid_row_indices.add(row_idx)
            else:
                is_invalid_table = True
                break
        # 多个表格时，任一表格不满足条件，则不要所有答案
        # http://************:55647/#/project/remark/407992?treeId=38018&fileId=113419&schemaId=18&projectId=17&schemaKey=C2.4&page=259
        if is_invalid_table or not valid_row_indices:
            return []
        result_cells = [c for c in data_col if c.rowidx in valid_row_indices]
        new_element_results.append(TableCellsResult(element_result.element, cells=result_cells))
    if not new_element_results:
        return []
    model, schema = kwargs["model"], kwargs["schema"]
    return [model.create_result(new_element_results, value=AnswerValueEnum.PS.value, schema=schema)]


class C24PostProcessor(BasePostProcessor):
    """
    C2段落后处理逻辑： 必须在当年内或者截至当年期末
    """

    def is_valid_ps_para(self, text: str, element: dict):
        """
        句子中有日期，则判断日期是否为当年期末
        """
        day_after = get_date_by_offset_days(self.year_end, 1)
        # 1. 先判断日期是否满足
        if at_dates := extract_dates(P_C2_DATE, text, str_format="%Y-%m-%d"):
            if not any(d in {self.year_end, day_after} for d in at_dates):
                return False
        return P_C24_ONE_INVESTEE.search(text) or P_C24_ROW_HEADER.search(element["text"])

    def is_valid_table_date(self, texts: list[str], day_after, is_header=True) -> bool | None:
        for text in texts:
            if at_dates := extract_dates(P_C2_DATE, text, str_format="%Y-%m-%d"):
                return bool(set(at_dates) & {self.year_end, day_after})
            if years := extract_dates(P_C2_ONLY_YEAR, text, str_format="%Y"):
                return self.report_year in years
            if is_header and P_C24_TBL_DATE.search(text):
                return True
        return False

    def filter_table_by_date(self, parsed_cells: list[ParsedTableCell], group_type: str, strict=True):
        parsed_cells = filter_c2_cells_by_date(parsed_cells, group_type, self.year_end, self.report_year)
        if not parsed_cells:
            return []
        day_after = get_date_by_offset_days(self.year_end, 1)
        col_indices = {c.colidx for c in parsed_cells}
        header_texts = [text for idx, text in parsed_cells[0].table.col_header_texts.items() if idx in col_indices]
        if group_type == "row":
            # 按行取答案时，结合列名和行名一起看
            row_indices = {c.rowidx for c in parsed_cells}
            header_texts.extend(
                text for idx, text in parsed_cells[0].table.row_header_texts.items() if idx in row_indices
            )
        if self.is_valid_table_date(header_texts, day_after):
            return parsed_cells
        valid_dates = {self.year_end, day_after}
        if group_type == "row":
            # 考虑下一行是日期: http://************:55647/#/project/remark/294126?treeId=7342&fileId=70730&schemaId=18&projectId=17&schemaKey=C2.6
            row_dates = self.get_date_from_row_header(parsed_cells, P_C2_DATE)
            valid_date_rows = {row_idx for row_idx, date_str in row_dates.items() if date_str in valid_dates}
            if valid_cells := [c for c in parsed_cells if c.rowidx + 1 in valid_date_rows]:
                return valid_cells
        title = get_c2_table_title(self.pdfinsight, parsed_cells[0].table)
        if strict:
            if self.is_valid_table_date([title], day_after, is_header=False):
                return parsed_cells
            return []
        # strict=False时，若表名未提到日期，则不校验日期
        if (at_dates := extract_dates(P_C2_DATE, title, str_format="%Y-%m-%d")) and not set(at_dates) & valid_dates:
            return []
        if (years := extract_dates(P_C2_ONLY_YEAR, title, str_format="%Y")) and self.report_year not in years:
            return []
        return parsed_cells

    @staticmethod
    def filter_cells_by_row_header(parsed_cells, group_type):
        if all(c.is_header or P_HEADER_UNIT.search(c.no_cn_text) for c in parsed_cells):
            return []
        if group_type == "row":
            # 按行取的表格，不需要进一步判断明细
            return parsed_cells
        if any(P_C24_NAME_HEADER.search(t) for t in parsed_cells[0].table.col_header_texts.values()):
            return parsed_cells
        row_indices = {c.rowidx for c in parsed_cells}
        min_data_rowidx = 0
        valid_row_indices = set()
        for cells in parsed_cells[0].table.data_row_header:
            if min_data_rowidx == 0:
                # 最小数据行index之前的行，是标题行，结果中依然需要
                min_data_rowidx = cells[0].rowidx
            if cells[0].rowidx not in row_indices:
                continue
            # subtitle是黑名单，排除答案
            if any(P_C2_NEG_ACCOUNTS.search(c.clean_text) for c in cells[0].subtitle_cells):
                continue
            row_header = "\n".join(c.clean_text for c in cells)
            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7436#note_764304
            if any(
                p.search(row_header) for p in [P_C2_NEG_ACCOUNTS, P_C24_INVALID_ROW_HEADER, *P_C2_POSSIBLE_CHAPTERS]
            ):
                continue
            valid_row_indices.add(cells[0].rowidx)
        if not valid_row_indices:
            return []
        return [c for c in parsed_cells if c.rowidx < min_data_rowidx or c.rowidx in valid_row_indices]

    def filter_ps_table_cells(self, parsed_cells: list[ParsedTableCell], group_type: str):
        """
        1. 如果标题或列名中都没有当年，则丢弃表格
        2. 如果答案都没有值，则丢弃表格
        3. 如果答案的行头都是大类描述，也丢弃表格
        """
        # 只取结果中的第一个列并判断表头中的日期
        parsed_cells = self.filter_table_by_date(parsed_cells, group_type)
        if not parsed_cells:
            return []
        if all(P_CELL_NIL.search(c.text_no_superscript) for c in parsed_cells):
            return []
        return self.filter_cells_by_row_header(parsed_cells, group_type)


predictor_options = [
    {
        "path": ["C2.4"],
        "models": [
            {
                "name": "c2ns",
            },
            {
                "name": "multi_models",
                "operator": "union",
                "require_unique_element": True,
                "enum": AnswerValueEnum.PS.value,
                "models": [
                    # 章节下表格中列头有明确时间
                    {
                        "name": "special_cells",
                        "inject_elements_func": extend_significant_elements,
                        "post_process_model_answers": C24PostProcessor(),
                        # http://************:55647/#/project/remark/410324?treeId=12154&fileId=113681&schemaId=18&projectId=17&schemaKey=C2.4
                        "neglect_syllabus_regs": [r"loans|Fair\s*value\s*hierarchy"],
                        "neglect_title_patterns": [r"details?\s*of\s*loans?"],
                        "multi": True,
                        "multi_elements": True,
                        "col_header_pattern": NeglectPattern.compile(
                            match=R_HEADER_VALUE_AMOUNT, unmatch=R_HEADER_VALUE_NEG
                        ),
                        "enum": AnswerValueEnum.PS.value,
                    },
                    {
                        "name": "special_cells",
                        "inject_elements_func": extend_significant_elements,
                        "post_process_model_answers": C24PostProcessor(),
                        "title_patterns": MatchMulti.compile(
                            r"detail|below|follow|[:：]$",
                            rf"{R_FVTPL}|{R_FVTPL}|investment",
                            R_C24_AS_AT,
                            operator=all,
                        ),
                        "multi": True,
                        "row_header_pattern": NeglectPattern.compile(
                            match=R_HEADER_VALUE_AMOUNT, unmatch=R_HEADER_VALUE_NEG
                        ),
                        # 只要第一个数据列
                        "last_mode": {LastMode.FIRST_COL},
                        "cell_pattern": [P_CELL_MONEY, rf"{R_CURRENCY}\d"],
                        "enum": AnswerValueEnum.PS.value,
                    },
                    {
                        "name": "special_cells",
                        "inject_elements_func": extend_significant_elements,
                        "post_process_model_answers": C24PostProcessor(),
                        "multi": True,
                        "row_header_pattern": MatchMulti.compile(
                            MatchMulti.compile(R_HEADER_VALUE_AMOUNT, R_C24_AS_AT, operator=all),
                            # r"investments? in equity instrument",
                            # stock 8081 2024
                            r"fair value of share held by the group",
                            operator=any,
                        ),
                        # 只要第一个数据列
                        "last_mode": {LastMode.FIRST_COL},
                        "cell_pattern": [P_CELL_MONEY, rf"{R_CURRENCY}\d"],
                        "enum": AnswerValueEnum.PS.value,
                    },
                    # 段落: 固定句式可以直接提取，提及多个投资方及对应的fair value
                    {
                        "name": "para_match",
                        "inject_elements_func": extend_significant_elements,
                        "post_process_model_answers": C2PostProcessor(),
                        "para_separator": P_SEN_SEPARATOR,
                        "multi_elements": True,
                        "paragraph_pattern": MatchMulti.compile(
                            R_AS_AT, rf"{R_NEG_VALUE_PREFIX}fair\s*value|FVT?PL|FVT?OCI", operator=all
                        ),
                        "sentence_pattern": [
                            MatchMulti.compile(
                                # 下方是多家投资分别披露
                                # stock=2511,year=2024; stock=1541,year=2024,stock=2423,year=2024
                                # http://************:55647/#/project/remark/416347?treeId=38056&fileId=114355&schemaId=18&projectId=17&schemaKey=C2.4
                                # http://************:55647/#/project/remark/419014?treeId=45528&fileId=114654&schemaId=18&projectId=17&schemaKey=C2.4
                                r"(?<!\b\d{4}[,，] )respectively|mainly\s*included",
                                # http://************:55647/#/project/remark/419014?treeId=45528&fileId=114654&schemaId=18&projectId=17&schemaKey=C2.4
                                rf"\s{R_MONEY_UNIT}[\d.,]+\s*(\s*[bm]illion)?\s*([,，]\s*{R_MONEY_UNIT}[\d.,]+\s*(\s*[bm]illion)?\s*)?and\s*{R_MONEY_UNIT}\d",
                                operator=all,
                            ),
                        ],
                        "neglect_sentence_pattern": P_C24_NEGLECT,
                        "enum": AnswerValueEnum.PS.value,
                    },
                    # 段落: 固定句式可以直接提取，不需要再判断是否只有1个被投资方，后处理只判断日期
                    {
                        "name": "para_match",
                        "inject_elements_func": extend_significant_elements,
                        "post_process_model_answers": C2PostProcessor(),
                        "para_separator": P_SEN_SEPARATOR,
                        "multi_elements": True,
                        "paragraph_pattern": MatchMulti.compile(R_AS_AT, r"fair\s*value", operator=all),
                        "sentence_pattern": [
                            MatchMulti.compile(
                                P_C24_ONE_INVESTEE,
                                PositionPattern.compile(R_VALUE_AMOUNT, rf"{R_MONEY_UNIT}\d"),
                                operator=all,
                            ),
                            # http://************:55647/#/project/remark/409241?treeId=5193&fileId=113559&schemaId=18&projectId=17&schemaKey=C2.4
                            PositionPattern.compile(
                                r"\bthe\s*fair\s*value\s*of\s*(the\s*investment\s*in\s*)?[A-Z]",
                                rf"\bamounted\s*to\s*{R_MONEY_UNIT}\d",
                                flag=0,
                            ),
                        ],
                        "neglect_sentence_pattern": [P_C24_NEGLECT, R_C24_MULTI_INVESTEES_KW],
                        "enum": AnswerValueEnum.PS.value,
                    },
                    # 段落: 包含明确的fair value 或 FVTPL/FVOCI + carrying amount，需要后处理排除大类描述
                    {
                        "name": "para_match",
                        "inject_elements_func": extend_significant_elements,
                        "post_process_model_answers": C24PostProcessor(),
                        "para_separator": P_SEN_SEPARATOR,
                        "multi_elements": True,
                        "paragraph_pattern": [R_AS_AT, P_NOTE_PREFIX],
                        "sentence_pattern": [
                            PositionPattern.compile(R_VALUE_AMOUNT, rf"{R_MONEY_UNIT}\d"),
                            # 有carrying amount/value等描述时，fair value后可以跟through
                            MatchMulti.compile(P_CARRYING_AMOUNT, rf"fair\s*value|{R_FVTOCI}|{R_FVTPL}", operator=all),
                        ],
                        "neglect_sentence_pattern": [P_C24_NEGLECT, R_C24_MULTI_INVESTEES_KW],
                        "enum": AnswerValueEnum.PS.value,
                    },
                    # 章节为FVTPL/FVOCI或fair value，段落中有carrying amount，需要后处理排除大类描述
                    {
                        "name": "para_match",
                        "inject_elements_func": extend_significant_elements,
                        "post_process_model_answers": C24PostProcessor(),
                        "para_separator": P_SEN_SEPARATOR,
                        "multi_elements": True,
                        "syllabus_regs": [R_FVTPL, R_FVTOCI, R_VALUE_AMOUNT],
                        "paragraph_pattern": [R_AS_AT, P_NOTE_PREFIX],
                        "sentence_pattern": P_CARRYING_AMOUNT,
                        "neglect_sentence_pattern": [P_C24_NEGLECT, R_C24_MULTI_INVESTEES_KW],
                        "enum": AnswerValueEnum.PS.value,
                    },
                ],
            },
            # 针对特定文件的特定句式
            {
                "name": "para_match",
                "inject_elements_func": extend_significant_elements,
                # 这里不能调用`C24PostProcessor`，因为句式特殊性，不需要再判断被投资方
                "post_process_model_answers": C2PostProcessor(),
                "para_separator": P_SEN_SEPARATOR,
                "multi_elements": True,
                "paragraph_pattern": [R_AS_AT, P_NOTE_PREFIX],
                "sentence_pattern": [
                    # http://************:55647/#/project/remark/412477?treeId=12776&fileId=113923&schemaId=18&projectId=17&schemaKey=C2.4
                    PositionPattern.compile(
                        r"investment\s*in\s*(an?\s|[A-Z])",
                        rf"({R_FVTPL}|{R_FVTOCI})\s*([a-z]+\s+){{,2}}\s*of\s*{R_MONEY_UNIT}\d",
                        flag=0,
                    ),
                    # http://************:55647/#/project/remark/383905?treeId=10446&fileId=113011&schemaId=18&projectId=17&schemaKey=C2.4
                    PositionPattern.compile(
                        rf"{R_FVTPL}|{R_FVTOCI}",
                        r"\b(shares|equity\s*interests)\s*of\s*([A-Z]|an?\s)",
                        rf"\bamount(ed|ing)\s*to\s*([a-z]+\s+)?{R_MONEY_UNIT}\d",
                    ),
                ],
                "neglect_sentence_pattern": P_C24_NEGLECT,
                "enum": AnswerValueEnum.PS.value,
            },
            # 提取不到具体信息时，在notes章节提取表格中的明细信息
            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7436
            {
                "name": "special_cells",
                "inject_elements_func": extend_significant_elements,
                "post_process_model_answers": modify_c24_answer_value,
                "parent_features": P_NOTE_CHAPTER,
                "parent_must_be_root": True,
                "multi_elements": True,
                "enum": AnswerValueEnum.PS.value,
                "neglect_title_patterns": [r"change|movement.+[:：]$"],
                "col_header_pattern": [P_CURRENT_DATE, rf"{R_CURRENCY}($|\s|['‘’])|[$¥€￡]", r"^20\d{2}"],
                # 找出第一个数据列
                "last_mode": {LastMode.FIRST_COL},
            },
        ],
    },
]
