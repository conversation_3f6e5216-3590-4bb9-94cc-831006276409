from remarkable.common.common import get_date_by_offset_days
from remarkable.common.constants import AnswerV<PERSON>ue<PERSON>num
from remarkable.common.pattern import <PERSON><PERSON>ult<PERSON>, PositionPattern
from remarkable.predictor.common_pattern import R_DATES
from remarkable.predictor.hkex_predictor.schemas.c_rule_schema.c_rule_c1_1 import is_ns_para_text
from remarkable.predictor.hkex_predictor.schemas.list_rule_schema.list_rule_b1 import R_UNUSED
from remarkable.predictor.hkex_predictor.schemas.list_rule_schema.list_rule_b8 import (
    table_models_for_proceeds,
)
from remarkable.predictor.hkex_predictor.schemas.list_rule_schema.list_rule_b9 import (
    NS_FULLY_USED,
    P_TIME_IN_CELL,
    P_TIMELINE,
    P_UNUSED,
    timeline_sentence_model,
    unused_sentence_model,
)
from remarkable.predictor.hkex_predictor.schemas.list_rule_schema.post_processor import BasePostProcessor
from remarkable.predictor.hkex_predictor.schemas.pattern import R_NOT
from remarkable.predictor.hkex_predictor.schemas.util import get_sentence_model
from remarkable.predictor.models.fund_raising_group import P_AS_AT_DATES


class C121PostProcessor(BasePostProcessor):
    def get_invalid_cols(self, parsed_cells):
        # 找出不包含日期、或日期为期末、或日期为下期期初的列，若没有则丢弃答案
        invalid_col_indices = set()
        timeline_cols = self.get_col_indices_by_pattern(parsed_cells, P_TIMELINE)
        col_date_dict = self.get_date_from_col_header(parsed_cells, P_AS_AT_DATES)
        col_date_dict = {k: v for k, v in col_date_dict.items() if k not in timeline_cols}
        if self.year_end:
            for col, date_str in col_date_dict.items():
                if date_str not in {self.year_end, get_date_by_offset_days(self.year_end, 1)}:
                    invalid_col_indices.add(col)
        elif len(col_date_dict) > 1:
            # 排除日期最大的那列
            max_date_col = max(col_date_dict.items(), key=lambda x: x[1])[0]
            invalid_col_indices = {c for c in col_date_dict if c != max_date_col}
        return invalid_col_indices

    def get_unused_and_time_cols(self, parsed_cells, invalid_col_indices):
        unused_col_indices, timeline_col_indices = set(), set()
        for col, text in self.get_header_from_cells(parsed_cells, invalid_col_indices).items():
            if P_TIMELINE.search(text):
                timeline_col_indices.add(col)
            elif P_UNUSED.search(text):
                unused_col_indices.add(col)
        return unused_col_indices, timeline_col_indices

    def filter_unused_proceeds_cells(self, parsed_cells):
        """
        针对C1.2.1的子项1
        """
        # 1. 找出不包含日期、或日期为期末、或日期为下期期初的列，若没有则丢弃答案
        invalid_col_indices = self.get_invalid_cols(parsed_cells)
        # 合并单元格有unused关键词，但是下面有两列，其中一列包含了timeline，需要排除time的列
        # http://************:55647/#/project/remark/269115?treeId=6438&fileId=70636&schemaId=18&projectId=6438&schemaKey=C1.2.1
        unused_indices = self.get_col_indices_by_pattern(parsed_cells, P_UNUSED, invalid_col_indices)
        if not unused_indices:
            return []
        self.check_value_col = max(unused_indices)
        # 追加第一列（即用途列）
        return [cell for cell in parsed_cells if cell.colidx == self.check_value_col]
        # return self.add_first_col_cells([cell for cell in parsed_cells if cell.colidx == self.check_value_col])

    def filter_timeline_cells(self, parsed_cells):
        """
        针对C1.2.1的子项2的NS表格
        """
        # 1. 找出不包含日期、或日期为期末、或日期为下期期初的列，若没有则丢弃答案
        invalid_col_indices = self.get_invalid_cols(parsed_cells)
        # 2. 这里提取unused和timeline列
        unused_col_indices, timeline_col_indices = self.get_unused_and_time_cols(parsed_cells, set())
        # 3. unused无有效值时，同时取unused列和timeline列
        timeline_cells = [c for c in parsed_cells if c.colidx in timeline_col_indices]
        if unused_col_indices:
            # 有unused列，但年份都不是当前年份，丢弃答案
            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6243#note_664483
            if unused_col_indices.issubset(invalid_col_indices):
                return []
            unused_col_indices = unused_col_indices - timeline_col_indices
            self.check_value_col = max(unused_col_indices)
            unused_cells = [cell for cell in parsed_cells if cell.colidx == self.check_value_col]
            if not self.cells_have_value(unused_cells):
                self.table_is_ns = True
                return unused_cells + timeline_cells
        # 4. unused有值时，必须有且仅取timeline列
        if not timeline_col_indices:
            return []
        self.check_value_col = max(timeline_col_indices)
        self.check_value_pattern = P_TIME_IN_CELL
        return timeline_cells

    def filter_ps_table_cells(self, parsed_cells, group_type):
        if self.schema.name == "Unutilized proceeds":
            # C1.2.1子项1的表格处理逻辑
            return self.filter_unused_proceeds_cells(parsed_cells)
        if self.schema.name == "Expected timeline":
            # C1.2.1子项2的表格过滤逻辑
            return self.filter_timeline_cells(parsed_cells)

    def is_ns_table(self, parsed_cells):
        return self.table_is_ns or not self.cells_have_value(parsed_cells)

    def is_valid_ps_para(self, text, element):
        if self.schema.name == "Unutilized proceeds":
            return self.is_up_to_fy_end(text)
        return True

    def is_valid_ns_para(self, text):
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6245#note_686848
        if self.year_end and is_ns_para_text(text, self.year_start, self.year_end):
            return True
        return self.is_up_to_fy_end(text)


# NS模型： 没有未使用的资金
# http://************:55647/#/project/remark/294533?treeId=37743&fileId=70789&schemaId=18&projectId=37743&schemaKey=C1.1.1
# http://************:55647/#/project/remark/295216?treeId=10707&fileId=70877&schemaId=18&projectId=10707&schemaKey=C1.1.1
NS_NO_UNUSED = get_sentence_model(
    [
        PositionPattern.compile(R_NOT, r"proceeds", rf"brought\s*forward|{R_UNUSED}"),
        PositionPattern.compile(R_NOT, R_UNUSED, r"proceeds"),
        # http://************:55647/#/project/remark/294236?treeId=45609&fileId=70746&schemaId=18&projectId=45609&schemaKey=C1.2.1
        # PositionPattern.compile(r"\b(no|not|nor|neither)\b", R_USED, r"\bany\b", r"proceeds"),
    ],
    skip_pattern=MatchMulti.compile(r"((save|except)\s*(for|in|as)|except)\s*disclosed", operator=any),
    enum=AnswerValueEnum.NS,
    multi_elements=True,
    threshold=0,
)

predictor_options = [
    {
        # ------------------------------
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/5610
        # ------------------------------
        "path": ["C1.2.1"],
        "fake_leaf": True,
        "models": [
            {
                "name": "use_of_proceeds_group",
                "element_candidate_count": 20,
                "post_process_answers": C121PostProcessor(),
                "column_configs": {
                    # 子项1, 披露截至当年的未使用金额
                    "Unutilized proceeds": [
                        {
                            "name": "multi_models",
                            "operator": "union",
                            "require_unique_element": True,
                            "enum": AnswerValueEnum.PS.value,
                            "models": [
                                *table_models_for_proceeds([P_UNUSED], multi_elements=True),
                                # 句子描述未使用金额及预计使用期限
                                unused_sentence_model(),
                            ],
                        },
                        NS_NO_UNUSED,
                        NS_FULLY_USED,
                    ],
                    # 子项2，披露未使用金额的预计使用期限
                    "Expected timeline": [
                        {
                            "name": "multi_models",
                            "operator": "union",
                            "require_unique_element": True,
                            "enum": AnswerValueEnum.PS.value,
                            "models": [
                                *table_models_for_proceeds(
                                    [P_UNUSED, P_TIMELINE],
                                    multi_elements=True,
                                    footnote_pattern=MatchMulti.compile(
                                        *[rf"put into use by {_date}" for _date in R_DATES], operator=any
                                    ),
                                ),
                                # 句子描述未使用金额及预计使用期限
                                timeline_sentence_model(),
                            ],
                        },
                        NS_NO_UNUSED,
                        NS_FULLY_USED,
                    ],
                },
            }
        ],
    },
]
