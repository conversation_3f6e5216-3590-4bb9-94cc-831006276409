from remarkable.common.common_pattern import P_PERIOD_SEPARATOR, R_CN_SPACE
from remarkable.common.constants import AnswerV<PERSON>ueEnum
from remarkable.common.pattern import <PERSON><PERSON><PERSON><PERSON>, PositionPattern
from remarkable.predictor.hkex_predictor.schemas.c_rule_schema.c_rule_c2 import (
    extend_significant_elements,
    filter_c2_answer,
)
from remarkable.predictor.hkex_predictor.schemas.c_rule_schema.c_rule_c2_1 import R_C21_NEG_TBL_TITLES, R_C21_SUBSCRIBE
from remarkable.predictor.hkex_predictor.schemas.pattern import R_MONEY_UNIT

R_C23_KW = r"(investment|subscription|carrying\s*book|acquisition)"
# principle: https://jura6-lir.paodingai.com/#/hkex/annual-report-checking/report-review/258192?fileId=68853&schemaId=5&rule=C2.3&delist=0
R_C23_AMOUNT = rf"\b({R_C23_KW}|total|aggregate|princip(le|al))\s*(amount|cost)s?\b"
P_C23_TBL_CELL_PS = [
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7334#note_733130
    PositionPattern.compile(
        R_C23_AMOUNT,
        rf"{R_MONEY_UNIT}[1-9]",
    ),
    PositionPattern.compile(
        rf"(?<!carrying )\b(amount|costs?)\s*of\s*([\-a-z]+\s+){{,5}}{R_C23_KW}", rf"{R_MONEY_UNIT}[1-9]"
    ),
    PositionPattern.compile(rf"\b(total|aggregate)\s*{R_C23_KW}", rf"{R_MONEY_UNIT}[1-9]"),
]

predictor_options = [
    {
        "path": ["C2.3"],
        "post_process": filter_c2_answer,
        "models": [
            {
                "name": "c2ns",
            },
            {
                "name": "multi_models",
                "operator": "union",
                "enum": AnswerValueEnum.PS.value,
                "require_unique_element": True,
                "deduplicate_elements": False,
                "models": [
                    {
                        "name": "special_cells",
                        "multi": True,
                        "multi_elements": True,
                        "inject_elements_func": extend_significant_elements,
                        "col_header_pattern": MatchMulti.compile(
                            rf"(^|\t){R_CN_SPACE}(at)?\s*costs?\b",
                            R_C23_AMOUNT,
                            rf"(?<!carrying )(amount|costs?)\s*of\s*{R_C23_KW}",
                            r"investment\s*costs?\b",
                            operator=any,
                        ),
                        "enum": AnswerValueEnum.PS.value,
                        "neglect_title_patterns": R_C21_NEG_TBL_TITLES,
                        "cell_pattern": [r"[1-9]"],
                    },
                    {
                        "name": "special_cells",
                        "inject_elements_func": extend_significant_elements,
                        "cell_pattern": P_C23_TBL_CELL_PS,
                        "enum": AnswerValueEnum.PS.value,
                    },
                    # 指定章节下 忽略投资公司名称之后 明确说明投资金额的句式
                    # http://************:55647/#/project/remark/265089?treeId=3097&fileId=70232&schemaId=18&projectId=3097&schemaKey=C2.3
                    {
                        "name": "para_match",
                        "inject_elements_func": extend_significant_elements,
                        # C2.3 不过滤日期：https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7327#note_737714
                        # "post_process_model_answers": C2PostProcessor(),
                        "neglect_syllabus_regs": [
                            # stock_code=1468, year=2024
                            r"disposal\s*of\s.+equity",
                        ],
                        "multi_elements": True,
                        "para_separator": P_PERIOD_SEPARATOR,
                        "sentence_pattern": [
                            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7334#note_733130
                            *P_C23_TBL_CELL_PS,
                            # unit price: http://************:55647/#/project/remark/295936?treeId=8143&fileId=70967&schemaId=18&projectId=8143&schemaKey=C2.3
                            PositionPattern.compile(
                                r"subscribed|subscription|invested|investment|acquired|entered\s*into|equity\s*interest",
                                r"\b(costs?|consideration|\sat\s*the\s*(unit\s*)?price)\b",
                                rf"{R_MONEY_UNIT}[1-9]",
                            ),
                            # http://************:55647/#/project/remark/266194?treeId=45553&fileId=70453&schemaId=18&projectId=45553&schemaKey=C2.3
                            PositionPattern.compile(
                                r"acquired\s*an\s*equity", rf"\bfor\s*approximately\s*{R_MONEY_UNIT}\d"
                            ),
                            # http://************:55647/#/project/remark/293982?treeId=38114&fileId=70709&schemaId=18&projectId=38114&schemaKey=C2.3
                            # http://************:55647/#/project/remark/293888?treeId=5220&fileId=70694&schemaId=18&projectId=5220&schemaKey=C2.3
                            MatchMulti.compile(
                                rf"{R_MONEY_UNIT}[\d,.]+\d(\s*million)?(\s*[(（].+[)）])?\s*was\s*paid\s*",
                                rf"{R_C21_SUBSCRIBE}|\sto\s*(acquire|subscribe|invest)|\s*the\s*(company|group)",
                                operator=all,
                            ),
                            PositionPattern.compile(
                                # 耗资，投资，认购价格为
                                r"company|group",
                                r"at a cost of|invested(?! in)|subscription price of|principal of (the )?investments of|principal amount of",
                                rf"{R_MONEY_UNIT}[1-9]",
                            ),
                            PositionPattern.compile(
                                rf"{R_MONEY_UNIT}[1-9]",
                                # 由...出资
                                "was contributed by",
                                "limited partner",
                            ),
                        ],
                        "neglect_sentence_pattern": [
                            # http://************:55647/#/project/remark/266014?treeId=38069&fileId=70417&schemaId=18&projectId=38069&schemaKey=C2.3
                            r"exit\s*amount|disposal|disposed",
                            # 赎回：http://************:55647/#/project/remark/294346?treeId=10726&fileId=70762&schemaId=18&projectId=17&schemaKey=C2.3
                            r"\sredeem\b",
                            # 罚款利息：http://************:55647/#/project/remark/409848?treeId=37945&fileId=113628&schemaId=18&projectId=17&schemaKey=C2.3&page=110 index=1314
                            r"penalty interest",
                            r"\debt",
                        ],
                        "enum": AnswerValueEnum.PS.value,
                    },
                ],
            },
        ],
    },
]
