import re

from remarkable.common.common import get_date_by_offset_days
from remarkable.common.common_pattern import P_SEN_SEPARATOR
from remarkable.common.constants import AnswerValueEnum
from remarkable.common.pattern import MatchMulti, NeglectPattern, PatternCollection, PositionPattern
from remarkable.pdfinsight.parser import ParsedTableCell
from remarkable.predictor.common_pattern import R_PERCENT
from remarkable.predictor.default_predictor.utils import is_table_result
from remarkable.predictor.hkex_predictor.models.c2 import (
    P_NO_SIGNIFICANT_INVESTMENT,
    P_NS_NO_MORE_THAN_5_PERCENT,
)
from remarkable.predictor.hkex_predictor.pattern import R_SIM_MONTH
from remarkable.predictor.hkex_predictor.schemas.c_rule_schema.c_rule_c2 import (
    P_C2_DATE,
    P_C2_ONLY_YEAR,
    extend_significant_elements,
)
from remarkable.predictor.hkex_predictor.schemas.c_rule_schema.c_rule_c2_4 import (
    P_C24_ONE_INVESTEE,
    C24PostProcessor,
)
from remarkable.predictor.hkex_predictor.schemas.list_rule_schema.post_processor import (
    P_VALID_CELL_VALUE,
)
from remarkable.predictor.hkex_predictor.schemas.pattern import R_MONEY_UNIT, R_NOT
from remarkable.predictor.models.base_model import BaseModel
from remarkable.predictor.predictor_metadata.rule_c2 import R_C2_NEG_KW
from remarkable.predictor.schema_answer import PredictorResult
from remarkable.predictor.utils import extract_dates

P_PROFIT_LOSS = r"((un)?realiz?s?ed|fair\s*value|investment)[\s(（]*(gain|loss|income)|changes?\s*in\s*fair\s*value|fair\s*value\s*changed?|(interest|coupon)\s*received|(profit|loss)\s*of\s*the\s*investee|(gain|loss) on investment"


P_C26_NEGLECT = MatchMulti.compile(
    r"\breconciliation\b",
    # 不能配置的原因： http://100.64.0.105:55647/#/project/remark/268552?treeId=31014&fileId=70556&schemaId=18&projectId=31014&schemaKey=C2.6
    # r"\bcash (dividend|equivalent)s?\b",
    # stock=00519, year=2023, index=116
    # http://100.64.0.105:55647/#/project/remark/265449?treeId=5990&fileId=70304&schemaId=18&projectId=17&schemaKey=C2.6 index=395
    r"receivable|payable",  # 应收/应付
    r"\b(certain|debt\b|loan\b|call\s*option)",
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7197#note_729946
    r"contingent",
    # http://100.64.0.105:55647/#/project/remark/266094?treeId=8392&fileId=70433&schemaId=18&projectId=8392&schemaKey=C2.6
    P_NO_SIGNIFICANT_INVESTMENT,
    # http://100.64.0.105:55647/#/project/remark/411169?treeId=8641&fileId=113777&schemaId=18&projectId=17&schemaKey=C2.6
    r"\bcivil complaint\b",  # 说的是因民事诉讼而损失的赔偿，不是说重大投资的损失
    # r"disposal\s*of\s*convertible\s*notes",  # 可转换的 票据/债券
    r"fair\s*value.*?compared\s*to",
    # http://100.64.0.105:55647/#/project/remark/405784?treeId=8116&fileId=113170&schemaId=18&projectId=17&schemaKey=C2.6&page=21 index=355
    r"gain\s*on\s*redee?m",
    # 错误的日期
    # r"\b(on|at|of)\s*the\s*([a-z]+\s*)?date\s*of",
    # 这种属于大类描述，要排除
    r"fair\s*values?\s*of\s*the\s*(FVT?PL|FVT?OCI)\b",
    # http://100.64.0.105:55647/#/project/remark/410522?treeId=18134&fileId=113704&schemaId=18&projectId=17&schemaKey=C2.6&page=216 index=2373
    r"aggregate\s*gain",
    P_NS_NO_MORE_THAN_5_PERCENT,
    operator=any,
)
# transferred: http://100.64.0.105:55647/#/project/remark/268552?treeId=31014&fileId=70556&schemaId=18&projectId=31014&schemaKey=C2.6
R_REALIZED = r"\b((un)?reali[sz]e|receive|obtaine|recogni[sz]e|recorde|transfer[rs]?e?|declare)d"
R_CHANGE = r"\b(increase|decrease|change|adjustment|movement)"
R_GAIN_LOSS = r"(?<!other )(?<!Supreme )\b(gain|(?<!profit or )(?<!credit )loss|profit(?!\s*(or\s*loss|guarantee))|deficit|investment\s*income|group['‘’]s\s*income)"
P_FAIR_VALUE_CHANGE = MatchMulti.compile(
    rf"(?<!other )\b(fair|assets?)\s*value\s*{R_CHANGE}",
    rf"\b{R_CHANGE}e?s?\s*(of|on|in)\s*fair\s*value",
    operator=any,
)
P_C26_PS = [
    # http://100.64.0.105:55647/#/project/remark/414010?treeId=22388&fileId=114094&schemaId=18&projectId=17&schemaKey=C2.6
    # stock:1911 2024
    MatchMulti.compile(r"\b(fair|assets?)\s*value(?!\s*through)", R_GAIN_LOSS, rf"{R_MONEY_UNIT}\d", operator=all),
    MatchMulti.compile(R_CHANGE, R_GAIN_LOSS, rf"{R_MONEY_UNIT}\d", operator=all),
    MatchMulti.compile(R_REALIZED, R_GAIN_LOSS, rf"{R_MONEY_UNIT}\d", operator=all),
    # MatchMulti.compile(r"\b(investment|investee|invested)", R_GAIN_LOSS, rf"{R_MONEY_UNIT}\d", operator=all),
    # http://100.64.0.105:55647/#/project/remark/295936?treeId=8143&fileId=70967&schemaId=18&projectId=8143&schemaKey=C2.6
    MatchMulti.compile(P_FAIR_VALUE_CHANGE, rf"{R_MONEY_UNIT}\d", operator=all),
    # MatchMulti.compile(r"fair\s*value\s*gain", rf"{R_MONEY_UNIT}\d", operator=all),
    # MatchMulti.compile(r"movement\s*(in|of)\s*fair\s*value", R_CHANGE, rf"{R_MONEY_UNIT}\d", operator=all),
    # http://100.64.0.105:55647/#/project/remark/413974?treeId=10475&fileId=114090&schemaId=18&projectId=17&schemaKey=C2.6
    PositionPattern.compile(r"during", r"\bthe\s*group.s\s*income", rf"{R_MONEY_UNIT}\d", R_CHANGE),
]
# after the dividends: stock=00519, year=2023, index=116
R_DIVIDEND = r"\b((?<!after the )dividends?(?! yield)|coupon|(?<!equity )(?<!effective )interest)\b"
P_DIVIDEND = MatchMulti.compile(R_DIVIDEND, operator=any)
P_NO_DIVIDENDS = [
    # http://100.64.0.105:55647/#/project/remark/412568?treeId=13135&fileId=113933&schemaId=18&projectId=17&schemaKey=C2.6
    # http://100.64.0.105:55647/#/project/remark/382713?treeId=20944&fileId=113424&schemaId=18&projectId=17&schemaKey=C2.6
    PositionPattern.compile(rf"{R_NOT}(([a-z]+\s+)?any\s*)?([(（].+?[)）])?(dividend|income|interest|coupon)"),
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7334#note_733178
    PositionPattern.compile(R_NOT, R_REALIZED, R_DIVIDEND),
    # PPT中的例子 stock=00612, year=2023
    PositionPattern.compile(R_NOT, R_DIVIDEND, R_REALIZED),
    # http://100.64.0.105:55647/#/project/remark/412568?treeId=13135&fileId=113933&schemaId=18&projectId=13135&schemaKey=C2.6
    PositionPattern.compile(rf"{R_NOT}(receive|reali[sz]e|obtain)\s*any", R_DIVIDEND),
]
# 当仅披露了dividend/interest/coupon相关时，返回ND
P_DIVIDENDS = [
    MatchMulti.compile(R_REALIZED, R_DIVIDEND, rf"{R_MONEY_UNIT}\d", operator=all),
]
# 每项投资在已实现/未实现的损益数据为0/-，返回NS
P_NO_GAIN_OR_LOSS = MatchMulti.compile(
    PositionPattern.compile(R_NOT, R_REALIZED, R_GAIN_LOSS),
    PositionPattern.compile(R_NOT, R_GAIN_LOSS, R_REALIZED),
    r"\bno\s*changes?\s*in\s*fair\s*value",
    # http://100.64.0.105:55647/#/project/remark/266109?treeId=7506&fileId=70436&schemaId=18&projectId=17&schemaKey=C2.6
    PositionPattern.compile(r"\bno\s*fair\s*value\s*(change|gain|loss)", R_REALIZED),
    # http://100.64.0.105:55647/#/project/remark/414019?treeId=6722&fileId=114095&schemaId=18&projectId=17&schemaKey=C2.6
    PositionPattern.compile(r"\bno\s*signifi\s*cant\s*change", r"fair\s*value"),
    MatchMulti.compile(R_GAIN_LOSS, r"unchanged|\sno\s*change", operator=all),
    PositionPattern.compile(R_NOT, R_GAIN_LOSS, r"\bchanged"),
    # http://100.64.0.105:55647/#/project/remark/413974?treeId=10475&fileId=114090&schemaId=18&projectId=17&schemaKey=C2.6
    PositionPattern.compile(R_NOT, r"gains?\s*or\s*loss"),
    operator=any,
)

P_C26_TBL_HEADER = NeglectPattern.compile(
    match=MatchMulti.compile(
        R_GAIN_LOSS,
        P_FAIR_VALUE_CHANGE,
        R_DIVIDEND,
        operator=any,
    ),
    unmatch=MatchMulti.compile(
        # http://100.64.0.105:55647/#/project/remark/412397?treeId=40785&fileId=113914&schemaId=18&projectId=17&schemaKey=C2.6&page=231 index=2738
        r"gross",
        rf"rate|ratio|percent|proportion|{R_PERCENT}",
        R_C2_NEG_KW,
        r"call\s*option",
        r"loan",
        r"[權权]",  # interest有股权的意思，需要排除
        # r"\b(gains|losses)",
        # r"\b(of|on|in)\s*(financial\s*assets|equity|u?n?listed|investments)",
        operator=any,
    ),
)


def modify_c26_answer_value(answers: list[PredictorResult], **kwargs):
    """
    C2.6后处理
    1. 过滤表格中，日期不为当年的列
    2. 排除包含dividend/interest/coupon关键词的列或者段落，做枚举判断
    3. 若排除完没有答案，则判定ND；否则若任一表格的gain/loss列或文本中描述的gain/loss不为0，则为PS；再否则为NS
    # TODO 未考虑row_header
    """
    common_results = BaseModel.get_common_predictor_results(answers)
    if not any(r.answer_value == AnswerValueEnum.PS.value for r in common_results):
        return answers
    has_ps, has_ns = False, False
    all_element_results = BaseModel.get_common_element_results(common_results)
    for element_result in all_element_results:
        if is_table_result(element_result):
            parsed_cells = element_result.parsed_cells
            # 排除dividend/interest等不影响枚举值的列
            headers = parsed_cells[0].table.col_header_texts.items()
            valid_col_indices = {c for c, text in headers if not P_DIVIDEND.search(text)}
            value_cells = [c for c in parsed_cells if c.colidx in valid_col_indices and not c.is_header]
            if not value_cells:
                continue
            if any(P_VALID_CELL_VALUE.search(cell.no_cn_text) for cell in value_cells):
                return answers
            has_ns = True
        else:
            text = element_result.text
            if not has_ps and not has_ns and P_NO_GAIN_OR_LOSS.search(text):
                has_ns = True
                continue
            if not has_ps and any(pattern.search(text) for pattern in P_C26_PS):
                has_ps = True
                continue
    if has_ps:
        return answers
    answer_value = AnswerValueEnum.NS.value if has_ns else AnswerValueEnum.ND.value
    for result in common_results:
        result.answer_value = answer_value
    return common_results


class C26PostProcessor(C24PostProcessor):
    """
    C2段落后处理逻辑： 必须在当年内或者截至当年期末
    """

    P_ROW_HEADER_DATE = PatternCollection(
        [
            rf"^(at|as\s*(at|of))?\s*(?P<day>\d{{1,2}})(st|nd|rd|th)?\s*(?P<mon>{R_SIM_MONTH})[a-z]{{0,6}}[,，]?\s*(?P<year>\d{{4}})\b(?![:：].)",
            rf"^(at|as\s*(at|of))?\s*(?P<mon>{R_SIM_MONTH})[a-z]{{0,6}}\s*(?P<day>\d{{1,2}})(st|nd|rd|th)?[,，]?\s*(?P<year>\d{{4}})\b(?![:：].)",
        ],
        flags=re.I,
    )

    def is_valid_ps_para(self, text: str, element: dict):
        """
        句子中有日期，则判断日期是否为当年期末
        C2.6需要during the year/in 2024等类似描述，所以需要判断年份
        """
        day_after = get_date_by_offset_days(self.year_end, 1)
        # 先判断日期是否满足
        if at_dates := extract_dates(P_C2_DATE, text, str_format="%Y-%m-%d"):
            return any(d in {self.year_end, day_after} for d in at_dates)
        if years := extract_dates(P_C2_ONLY_YEAR, text, str_format="%Y"):
            return self.report_year in years
        # todo: 是否有必要针对整个element判断日期
        return True

    def filter_ps_table_cells(self, parsed_cells: list[ParsedTableCell], group_type: str):
        """
        1. 如果标题或列名中都没有当年，则丢弃表格
        2. 如果答案都没有值，则丢弃表格
        3. 如果答案的行头都是大类描述，也丢弃表格
        """
        # 只取结果中的第一列并判断表头中的日期
        parsed_cells = self.filter_table_by_date(parsed_cells, group_type, strict=False)
        if not parsed_cells:
            return []
        return self.filter_cells_by_row_header(parsed_cells, group_type)


predictor_options = [
    {
        "path": ["C2.6"],
        "post_process": modify_c26_answer_value,
        "models": [
            {
                "name": "c2ns",
            },
            {
                "name": "multi_models",
                "operator": "union",
                "enum": AnswerValueEnum.PS.value,
                "deduplicate_elements": True,
                "models": [
                    # 1. 提取表格，按列
                    {
                        "name": "special_cells",
                        "inject_elements_func": extend_significant_elements,
                        "post_process_model_answers": C26PostProcessor(),
                        "multi": True,
                        "multi_elements": True,
                        "col_header_pattern": P_C26_TBL_HEADER,
                        "enum": AnswerValueEnum.PS.value,
                    },
                    # 2. 提取表格按行+脚注
                    {
                        "name": "special_cells",
                        "inject_elements_func": extend_significant_elements,
                        "post_process_model_answers": C26PostProcessor(),
                        "multi": True,
                        "multi_elements": True,
                        "title_patterns": [
                            P_C24_ONE_INVESTEE,
                            PositionPattern.compile(r"following", r"\bgain\b", R_REALIZED),
                            # http://100.64.0.105:55647/#/project/remark/420144?treeId=45523&fileId=114782&schemaId=18&projectId=17&schemaKey=C2.6
                            PositionPattern.compile(r"\b(major|significant|material)\s*investment", r"[:：]$"),
                        ],
                        "row_header_pattern": P_C26_TBL_HEADER,
                        "enum": AnswerValueEnum.PS.value,
                    },
                    # 3. 提取表格，按行
                    {
                        "name": "special_cells",
                        "inject_elements_func": extend_significant_elements,
                        "post_process_model_answers": C26PostProcessor(),
                        "multi_elements": True,
                        "title_patterns": [
                            # http://100.64.0.105:55647/#/project/remark/414491?treeId=8452&fileId=114148&schemaId=18&projectId=17&schemaKey=C2.6
                            P_C24_ONE_INVESTEE,
                            # http://100.64.0.105:55647/#/project/remark/413828?treeId=3238&fileId=114074&schemaId=18&projectId=17&schemaKey=C2.6
                            r"Investment\s*[IVX]{1,4}\b",
                            PositionPattern.compile(r"\b(major|significant|material)\s*investment", r"[:：]$"),
                        ],
                        "row_header_pattern": [r"performance"],
                        "row_pattern": P_C26_PS,
                        "enum": AnswerValueEnum.PS.value,
                    },
                    # 4. 提取段落： 否定描述no gain/no dividend，可以是大类描述
                    {
                        "name": "para_match",
                        "inject_elements_func": extend_significant_elements,
                        "post_process_model_answers": C26PostProcessor(),
                        "multi": True,
                        "multi_elements": True,
                        # 这里用句号/分号分割
                        "para_separator": P_SEN_SEPARATOR,  # re.compile(rf"\s*({R_PERIOD}|[)(）（；;])\s*"),
                        # "neglect_syllabus_regs": [P_C26_NEGLECT],
                        "sentence_pattern": [P_NO_GAIN_OR_LOSS, *P_NO_DIVIDENDS],
                        "neglect_sentence_pattern": [
                            P_C26_NEGLECT,
                            # 排除not yet: http://100.64.0.105:55647/#/project/remark/409581?treeId=44928&fileId=113598&schemaId=18&projectId=17&schemaKey=C2.6&page=242 index=2634
                            r"\snot\s*yet",
                            # http://100.64.0.105:55647/#/project/remark/411713?treeId=37922&fileId=113838&schemaId=18&projectId=17&schemaKey=C2.6&page=224 index=2573
                            r"\bno\s*allowance",
                            # http://100.64.0.105:55647/#/project/remark/269080?treeId=4265&fileId=70631&schemaId=18&projectId=17&schemaKey=C2.6
                            rf"{R_NOT}(elected|qualify)",
                            rf"{R_NOT}(be\s*)?(re)?classified",
                            NeglectPattern.compile(
                                match=rf"{R_MONEY_UNIT}\d", unmatch=rf"[(（][^)）]+{R_MONEY_UNIT}\d[^)）]+[)）]"
                            ),
                            # http://100.64.0.105:55647/#/project/remark/413974?treeId=10475&fileId=114090&schemaId=18&projectId=17&schemaKey=C2.6
                            rf"{R_NOT}transfer",
                        ],
                        "enum": AnswerValueEnum.PS.value,
                    },
                    # 5. 提取段落： 提取实际gain/loss，必须针对具体的投资
                    {
                        "name": "para_match",
                        "inject_elements_func": extend_significant_elements,
                        "post_process_model_answers": C26PostProcessor(),
                        "multi": True,
                        "multi_elements": True,
                        "para_separator": P_SEN_SEPARATOR,
                        # "neglect_syllabus_regs": [P_C26_NEGLECT],
                        # "paragraph_pattern": MatchMulti.compile(P_C24_ONE_INVESTEE, r"\b(fair\s*value|FVT?PL|FVT?OCI)\b", operator=all),
                        "paragraph_pattern": [P_C24_ONE_INVESTEE],
                        "sentence_pattern": [*P_C26_PS, *P_DIVIDENDS],
                        "neglect_sentence_pattern": P_C26_NEGLECT,
                        "enum": AnswerValueEnum.PS.value,
                    },
                ],
            },
        ],
    },
]
