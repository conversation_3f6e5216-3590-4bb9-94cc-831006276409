import re
from copy import copy

from remarkable.common.common import get_date_by_offset_days, is_para_elt, year_month_from_str
from remarkable.common.common_pattern import R_AS_AT
from remarkable.common.constants import AnswerValueEnum
from remarkable.common.pattern import <PERSON><PERSON><PERSON><PERSON>, PatternCollection
from remarkable.pdfinsight.reader_util import is_merged_elt
from remarkable.predictor.default_predictor.utils import is_table_result
from remarkable.predictor.hkex_predictor.pattern import R_SIM_MONTH
from remarkable.predictor.hkex_predictor.schemas.list_rule_schema.post_processor import BasePostProcessor
from remarkable.predictor.models.base_model import BaseModel
from remarkable.predictor.schema_answer import PredictorResult, TableCellsResult
from remarkable.predictor.utils import extract_dates

P_C2_ONLY_YEAR = PatternCollection(
    patterns=[
        r"\b(in|ended)\s*(\d{4}\s*and\s*)?(?P<year>\d{4})\b(?![:：].)",
        r"\b(in|ended)\s*(?P<year>\d{4})\b(?![:：].)",
        r"(^|\t)(?P<year>\d{4})\b",
    ],
    flags=re.I,
)

R_C2_DATES = [
    rf"(?P<day>\d{{1,2}})(st|nd|rd|th)?\s*(?P<mon>{R_SIM_MONTH})[a-z]{{0,6}}[,，]?\s*\d{{4}}\s*and\s*(?P<year>\d{{4}})\b(?![:：].)",
    rf"(?P<mon>{R_SIM_MONTH})[a-z]{{0,6}}\s*(?P<day>\d{{1,2}})(st|nd|rd|th)?[,，]?\s*\d{{4}}\s*and\s*(?P<year>\d{{4}})\b(?![:：].)",
    rf"(?P<day>\d{{1,2}})(st|nd|rd|th)?\s*(?P<mon>{R_SIM_MONTH})[a-z]{{0,6}}[,，]?\s*(?P<year>\d{{4}})\b(?![:：].)",
    rf"(?P<mon>{R_SIM_MONTH})[a-z]{{0,6}}\s*(?P<day>\d{{1,2}})(st|nd|rd|th)?[,，]?\s*(?P<year>\d{{4}})\b(?![:：].)",
    rf"(?P<year>\d{{4}})\s*(?P<day>\d{{1,2}})(st|nd|rd|th)?\s*(?P<mon>{R_SIM_MONTH})[a-z]{{0,6}}\b",
]
P_C2_DATE = PatternCollection(
    patterns=[
        *[rf"{R_AS_AT}\s*{dt}" for dt in R_C2_DATES],
        *[rf"\b(on|at|ended|ending)\s*{dt}" for dt in R_C2_DATES],
        rf"\b({R_AS_AT}|on|at|in)\s*(?P<mon>{R_SIM_MONTH})[a-z]{{0,6}}\s*(?P<year>\d{{4}})\b(?![:：])",
    ],
    flags=re.I,
)
P_C2_YEAR = PatternCollection(patterns=[r"^(?P<year>20\d{2})\b"])
# 某行数据中有关键词debt，则认为该行数据不应该提取
P_C2_BLACK_ROW_CELL = MatchMulti.compile(
    r"^debt\s*(securities|investments)?$",
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7436#note_760231
    r"fixed.*notes?\b",
    # http://100.64.0.105:55647/#/project/remark/413918?treeId=38036&fileId=114084&schemaId=18&projectId=17&schemaKey=C2.4&page=26 index=272
    r"treasury\s*notes?\b",
    operator=any,
)
predictor_options = []


def extend_significant_elements(elements, predictor, **kwargs):
    significant_elements = predictor.prophet.metadata.get("c2_candidate_elements") or []
    all_indices = [e["index"] for e in significant_elements]
    existed_indices, new_elements = set(), []
    for element in elements:
        index = element["index"]
        if index not in all_indices:
            continue
        existed_indices.add(index)
        new_elements.append(element)
    # 扩展满足>=5%章节下的元素块
    for element in significant_elements:
        if element["index"] in existed_indices:
            continue
        new_element = copy(element)
        new_element["score"] = 0
        new_elements.append(new_element)
    return new_elements


class C2PostProcessor(BasePostProcessor):
    """
    C2段落后处理逻辑： 必须在当年内或者截至当年期末
    """

    def is_valid_ps_para(self, text, element):
        """
        句子中有日期，则判断日期是否在当年范围内
        """
        day_before = get_date_by_offset_days(self.year_end, -1)
        day_after = get_date_by_offset_days(self.year_end, 1)
        if at_dates := extract_dates(P_C2_DATE, text, str_format="%Y-%m-%d"):
            return any(d in {self.year_end, day_before, day_after} for d in at_dates)
        if years := extract_dates(P_C2_ONLY_YEAR, text, str_format="%Y"):
            return self.report_year in years
        return True


def get_c2_table_title(pdfinsight, table):
    prev_element = None
    # 表格上方可能是分栏右侧的中文，跳过中文行和被合并行来找相邻标题
    for offset in range(1, 10):
        prev_index = table.index - offset
        if prev_index <= 0:
            break
        _, prev_element = pdfinsight.find_element_by_index(prev_index)
        if pdfinsight.is_chinese_elt(prev_element) or is_merged_elt(prev_element):
            continue
        break
    if is_para_elt(prev_element) and (
        P_C2_DATE.nexts(prev_element["text"]) or P_C2_ONLY_YEAR.nexts(prev_element["text"])
    ):
        # 这里要取表格上方最近的日期，例如： for the year ended ...
        # http://100.64.0.105:55647/#/project/remark/379680?treeId=4473&fileId=113004&schemaId=18&projectId=17&schemaKey=C2.4&page=141 index=1494
        return prev_element["text"]
    return table.elements_above[0]["text"] if table.elements_above else table.possible_titles[-1]


def get_valid_row_or_col_indices(date_dict, valid_dates, report_year):
    valid_indices = [idx for idx, date_str in date_dict.items() if date_str in valid_dates]
    if valid_indices:
        return valid_indices
    return [idx for idx, date_str in date_dict.items() if date_str[:4] == report_year]


def filter_c2_cells_by_date(parsed_cells, group_type, year_end, report_year):
    valid_dates = {year_end, get_date_by_offset_days(year_end, 1)}
    # 按行时，判断行名中的日期
    if group_type == "row" and (date_dict := BasePostProcessor.get_date_from_row_header(parsed_cells, P_C2_DATE)):
        no_dated_cells = [c for c in parsed_cells if c.rowidx not in date_dict]
        if valid_row_indices := get_valid_row_or_col_indices(date_dict, valid_dates, report_year):
            return no_dated_cells + [c for c in parsed_cells if c.rowidx in valid_row_indices]
    if date_dict := BasePostProcessor.get_date_from_col_header(parsed_cells, P_C2_DATE):
        # 没有时间的列和有时间的列,element_result.parsed_cells中没有时间的列直接保留，有时间的列再去判断时间是否有效
        no_dated_cells = [c for c in parsed_cells if c.colidx not in date_dict]
        valid_col_indices = get_valid_row_or_col_indices(date_dict, valid_dates, report_year)
        return no_dated_cells + [c for c in parsed_cells if c.colidx in valid_col_indices]
    elif year_dict := BasePostProcessor.get_date_from_col_header(parsed_cells, P_C2_YEAR):
        # 尝试用年份取一次
        no_dated_cells = [c for c in parsed_cells if c.colidx not in year_dict]
        valid_col_indices = get_valid_row_or_col_indices(year_dict, [report_year], report_year)
        return no_dated_cells + [c for c in parsed_cells if c.colidx in valid_col_indices]
    return parsed_cells


def filter_c2_answer(answers: list[PredictorResult], **kwargs):
    # 有PS答案才走下方逻辑： https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7558
    if not any(r.answer_value == AnswerValueEnum.PS.value for r in BaseModel.get_common_predictor_results(answers)):
        return answers
    predictor = kwargs.get("predictor")
    pdfinsight = predictor.pdfinsight
    report_year = predictor.prophet.metadata["report_year"] if predictor else ""
    year_end = kwargs.get("year_end", "")
    result = []
    for answer in answers:
        new_element_results = []
        for predictor_result in BaseModel.get_common_predictor_results([answer]):
            for element_result in predictor_result.element_results:
                if is_table_result(element_result):
                    table = element_result.parsed_table
                    # http://100.64.0.105:55647/#/project/remark/416632?treeId=19309&fileId=114387&schemaId=18&projectId=17&schemaKey=C2.4&page=21 index=281
                    # 每个有效数据行都包含debt/fixed notes关键词，则丢弃该答案
                    if all(
                        any(P_C2_BLACK_ROW_CELL.search(c.no_cn_text) for c in row)
                        for row in table.rows
                        if not row[0].is_col_header
                    ):
                        continue
                    title = get_c2_table_title(pdfinsight, table)
                    table_dates = set()
                    for matched in P_C2_DATE.finditer(title):
                        group_dict = matched.groupdict()
                        year = group_dict["year"]
                        _, month = year_month_from_str(group_dict["mon"]) if group_dict.get("mon") else ("", "")
                        month = month or ""
                        day = group_dict.get("day") or ""
                        table_dates.add("-".join([year, month, day]).strip("-"))
                    # 先从列头提取每列时间，那这些时间去匹配year_end, 匹配不到去匹配report_year
                    group_type = element_result.group_type if isinstance(element_result, TableCellsResult) else None
                    if filtered_cells := filter_c2_cells_by_date(
                        element_result.parsed_cells, group_type, year_end, report_year
                    ):
                        element_result.parsed_cells = filtered_cells
                        new_element_results.append(element_result)
                    elif table_dates and year_end:
                        if year_end in table_dates or report_year in table_dates:
                            new_element_results.append(element_result)
                    else:
                        new_element_results.append(element_result)
                else:
                    new_element_results.append(element_result)

        if not new_element_results:
            continue
        if isinstance(answer, dict):
            for answer_results in answer.values():
                for answer_result in answer_results:
                    answer_result.element_results = new_element_results
        elif isinstance(answer, PredictorResult):
            answer.element_results = new_element_results
        result.append(answer)
    return result


# class C2InvestmentResponseModel(PydanticBaseModel):
#     investor: str
#     investees: list[str]
#
#
# P_INVALID_INVESTEE = MatchMulti.compile(
#     r"securities|assets|investments|securities|instruments|bonds|funds|products|subsidiaries", operator=any
# )
#
#
# @lru_cache(maxsize=10000)
# def get_investment_companies_by_llm(sentence: str, file_id:str=None) -> C2InvestmentResponseModel | None:
#     """
#     根据给定句子，提取其中的投资方和被投资方
#     """
#     prompt = """
# 您的任务是根据提供的句子，提取句子中的明确的投资方和被投资方，提取要求如下：
# 1. `投资`不仅仅代表invest，可以是收购、认购、持股、签订协议等企业间的商业行为；
# 2. 给定句子中，可能既无投资方也无被投资方，这种时候投资方和被投资方均不提取。
# 3. 投资方可以是`the Company`, `the Group`或其子公司；
# 4. 被投资方必须是明确的公司名称、明确的投资产品、明确的理财产品或者`a bank`、`a company`、`a fund`等能代表1个公司或一种产品的描述；
# 5. 如果被投资方是某公司及其子公司，但未提及子公司的名称，则不提取被投资方。
# 6. 如果被投资方是复数形式的表述，则不提取。
#
# 严格要求：
# - 被投资方名称中，若包含如下关键词，则不能提取：`securities`,`assets`,`investments`,`securities`,`instruments`,`bonds`,`funds`,`products`,`subsidiaries`,`banks`
# - 相同的被投资方名称仅提取一次
#
#     """
#     try:
#         res = OpenAIClient().send_message(
#             [{"role": "system", "content": prompt}, {"role": "user", "content": sentence}],
#             options={"timeout": 5},
#             response_format=C2InvestmentResponseModel,
#         )
#     except Exception as e:
#         mm_notify(f"{e=}, failed to predict for fid: {file_id}, rule: C2.4", error=True)
#         return None
#     ret = res.parsed
#     if investees := ret.investees:
#         # 根据内容是否包含做去重
#         new_investees = []
#         for investee in set(investees):
#             if P_INVALID_INVESTEE.search(investee):
#                 continue
#             if not any(n in investee or investee in n for n in new_investees):
#                 new_investees.append(investee)
#         ret.investees = new_investees
#     return ret
#
#
# if __name__ == "__main__":
#     text = "Subsequently, following a series of reorganisation with additions of shell intermediate holding companies, the Group indirectly holds ordinary shares with 5.75% (2023: 9.55%) ownership of a company incorporated in the British Virgin Islands, with carrying amount of approximately HK$4,419,000 (2023: HK$1,473,000) as at 31 December 2024."
#     print(get_investment_companies_by_llm(text))
