from remarkable.common.common import is_para_elt
from remarkable.common.constants import Answer<PERSON><PERSON>ue<PERSON>num
from remarkable.common.pattern import (
    MatchMulti,
    NeglectPattern,
)
from remarkable.predictor.hkex_predictor.models.c7 import P_C7_CHAPTER_CT, P_EXEMPT, R_CT, R_RPT
from remarkable.predictor.hkex_predictor.schemas.c_rule_schema.c_rule_c7 import (
    C7_ND_MODELS,
    C7_NS_REFERENCE_C71,
    C71_ND_NO_ANSWER_MODEL,
    P_C7_NEG_DISCLOSED,
    P_C7_OTHER_CHAPTERS,
    R_AGREEMENT,
    R_C7_SKIP_SYLLABUS,
    R_CONTRACTUAL_ARRANGEMENTS,
    c7_contract_model,
    c7_post_processor,
    c7_syllabus_model,
    is_c71_base_result,
    is_null_nd,
)
from remarkable.predictor.hkex_predictor.schemas.pattern import R_NOT
from remarkable.predictor.hkex_predictor.schemas.util import get_sentence_model
from remarkable.predictor.models.base_model import BaseModel
from remarkable.predictor.schema_answer import ParagraphResult

# P_C73_PS = [
#     MatchMulti.compile(
#         MatchMulti.compile(
#             R_AGREEMENT,
#             r"\b(pursuant\s*to\s(?!rule)|according to|consistent with|as stated in|in accordance with)\b",
#             operator=all,
#         ),
#         operator=any,
#     )
# ]
P_C73_SKIP = [
    rf"following.*?{R_CT}",
    r"\bauditors?\b",
    r"pursuant\s*to\s*rule",
    r"\bfee\s*payable",
    r"connected\s*person",
]


P_C73_PS = [
    MatchMulti.compile(
        R_AGREEMENT, r"\b(according to|consistent with|as stated in|in accordance with)\b", operator=all
    ),
    MatchMulti.compile(
        # 目的
        r"\b(intend\s*to|in\s*order\s*to)\s",
        r"\b(purpose|reasons?\b)(?!\s*of\s(chapter|rule|calculation))",
        # 产生的效果
        r"\b(optimi[zs]|pursu|fulfill|facilitat|refer|improv|creat|generat|enhanc|expand|strengthen|improv|achiev|impos)(e?d?|ing)?\b"
        r"\b(return\s*for|benefits?)\b",
        operator=any,
    ),
]

C_73_AS_FOLLOW = [
    r"provide.*?following\stypes?\sof\spropert(y|ies)[:：]$",
    # r"details of the terms and conditions are set out as follows[:：]$",
]


def post_process_c7_3_ref_answers(answers, **kwargs):
    if not answers:
        return answers
    model = kwargs.get("model")
    common_results = BaseModel.get_common_predictor_results(answers)
    if is_null_nd(common_results):
        # 只有一个空ND答案，直接返回
        return answers
    new_results = []
    for elt_result in BaseModel.get_common_element_results(common_results):
        element = elt_result.element
        if not is_para_elt(element) or not is_c71_base_result(elt_result):
            # 不取C7.1的表格和term
            continue
        # 取整个元素块
        new_results.append(ParagraphResult(element=element, chars=element["chars"]))
    return [model.create_result(new_results, schema=kwargs.get("schema"), value=AnswerValueEnum.PS.value)]


predictor_options = [
    {
        "path": ["C7.3"],
        "models": [
            # 1. NS: 若C7.1为NS，则认为是NS
            C7_NS_REFERENCE_C71,
            # 2-3. 特殊ND
            *C7_ND_MODELS,
            # 4. C7.1为无答案的ND，则直接ND
            C71_ND_NO_ANSWER_MODEL,
            # 5. 指定章节找
            {
                "name": "multi_models",
                "operator": "union",
                "model_id": "multi_model_1",
                "enum": AnswerValueEnum.PS.value,
                "deduplicate_elements": True,
                "sort_by_elt_index": True,
                "enum_every_element": True,
                "post_process_model_answers": c7_post_processor,
                "models": [
                    # 5.1 先找CT下的purpose/reasons整章节
                    c7_syllabus_model([r"__regex__purpose|reason"]),
                    # 5.2 有明确的 purpose 小标题描述
                    # http://************:55647/#/hkex/annual-report-checking/report-review/264162?fileId=70047&schemaId=5&rule=C7.3
                    {
                        "name": "near_paras",
                        "enum": AnswerValueEnum.PS.value,
                        "multi_elements": True,
                        "multi": True,
                        "start_regs": [
                            r"^(description of the transaction |summary of principal terms )and its purpose$"
                        ],
                        "near_amount": 4,
                        "extend_candidates_syllabus_regs": [R_CT],
                        "neglect_syllabus_regs": R_C7_SKIP_SYLLABUS,
                        "paragraph_pattern": P_C73_PS,
                        "neglect_pattern": [P_EXEMPT, *P_C73_SKIP],
                        "model_id": "near_paras_1",
                    },
                    # 5.3 优先在CT及关联的详情章节下找交易
                    get_sentence_model(
                        P_C73_PS,
                        multi_elements=True,
                        syllabus_regs=P_C7_CHAPTER_CT,
                        neglect_syllabus_regs=R_C7_SKIP_SYLLABUS,
                        extend_syllabus_regs=P_C7_CHAPTER_CT,
                        # 注意： 如果提及详见xxx章节，但又提及这些交易都被豁免或者不是CT，则不提取
                        extend_disclosed_neg_regs=P_C7_NEG_DISCLOSED,
                        # # http://************:55647/#/project/remark/295117?treeId=4455&fileId=70865&schemaId=18&projectId=17&schemaKey=C7.1
                        neg_pattern=[*P_C73_SKIP, P_EXEMPT],
                        para_separator=None,
                        skip_syllabus_title=False,
                        as_follow_pattern=C_73_AS_FOLLOW,
                        model_id="para_1",
                        # as_follow_type=AsFollowType.ANY
                    ),
                    # 5.4 RPT is CT http://************:55647/#/hkex/annual-report-checking/report-review/261629?fileId=69541&schemaId=5&rule=C7.3
                    {
                        "name": "near_paras",
                        "enum": AnswerValueEnum.PS.value,
                        "multi_elements": True,
                        "multi": True,
                        # "para_separator": P_PERIOD_SEPARATOR,
                        "start_regs": NeglectPattern.compile(
                            match=rf"following\s{R_RPT}\sconstitute\s{R_CT}", unmatch=R_NOT
                        ),
                        "neglect_start_regs": P_EXEMPT,
                        "near_amount": 20,
                        "extend_candidates_syllabus_regs": [P_C7_CHAPTER_CT, P_C7_OTHER_CHAPTERS],
                        "neglect_syllabus_regs": [*R_C7_SKIP_SYLLABUS, P_C7_CHAPTER_CT, r"\blisting\s*rule"],
                        "paragraph_pattern": [*P_C73_PS, rf"approv(ed)?.*?{R_AGREEMENT}\sand\s{R_RPT}"],
                        "neglect_pattern": P_C73_SKIP,
                        # "near_by_filter": filter_near_by,
                        "model_id": "near_paras_3",
                    },
                    # 5.5 只要提及contractual arrangements等构成CT，则在contractual arrangements章节提取
                    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6586#note_700702
                    # http://************:55647/#/hkex/annual-report-checking/report-review/263863?fileId=69987&schemaId=18&rule=C7.1&delist=0
                    c7_contract_model(
                        [
                            {
                                "name": "syllabus_elt_v2",
                                "parent_features": R_CONTRACTUAL_ARRANGEMENTS,
                                "multi": True,
                                "model_id": "syllabus_2",
                                "enum": AnswerValueEnum.PS.value,
                                "inject_syllabus_features": [r"__regex__reason|purpose"],
                            },
                            {
                                "name": "para_match",
                                "enum": AnswerValueEnum.PS.value,
                                "model_id": "para_3",
                                "multi_elements": True,
                                "syllabus_regs": R_CONTRACTUAL_ARRANGEMENTS,
                                "extend_candidates_syllabus_regs": R_CONTRACTUAL_ARRANGEMENTS,
                                "neglect_syllabus_regs": [R_C7_SKIP_SYLLABUS, r"material\s*change|terms|polic"],
                                "paragraph_pattern": P_C73_PS,
                                "neglect_pattern": P_C73_SKIP,
                            },
                        ]
                    ),
                    # 5.6 在表格中描述交易
                    {
                        "name": "special_cells",
                        "multi_elements": True,
                        "multi": True,
                        "need_continuous": False,
                        "syllabus_regs": P_C7_CHAPTER_CT,
                        "neglect_syllabus_regs": R_C7_SKIP_SYLLABUS,
                        "extend_candidates_syllabus_regs": P_C7_CHAPTER_CT,
                        "row_header_pattern": [r"subject\smatter[:：]$", r"^nature$", r"transaction"],
                        "col_header_pattern": [
                            r"\btransaction",
                            # https://jura6-lir.paodingai.com/#/hkex/annual-report-checking/report-review/262652?fileId=69745&schemaId=5&rule=C7.2&delist=0
                            r"^nature$",
                        ],
                        "row_col_relation": "or",
                        "enum": AnswerValueEnum.PS.value,
                    },
                    # 5.7 基于C7.1的答案找
                    {
                        "name": "reference",
                        "enum": AnswerValueEnum.PS.value,
                        "from_path": ["C7.1"],
                        "from_answer_value": [AnswerValueEnum.PS.value, AnswerValueEnum.ND.value],
                        # 过滤没有答案的answer
                        "enum_pattern": r".",
                        "post_process_model_answers": post_process_c7_3_ref_answers,
                    },
                ],
            },
        ],
    },
]
