from remarkable.common.common_pattern import (
    P_PERIOD_SEPARATOR,
    R_CN_SPACE,
    R_CN_SPACE_COLON,
    R_MIDDLE_DASH,
    R_MIDDLE_DASHES,
)
from remarkable.common.constants import AnswerValueEnum
from remarkable.common.pattern import MatchMulti, NeglectPattern, PositionPattern
from remarkable.predictor.common_pattern import R_PERCENT
from remarkable.predictor.default_predictor.utils import is_table_result
from remarkable.predictor.hkex_predictor.models.c2 import R_SIGNIFICANT_INVESTMENT
from remarkable.predictor.hkex_predictor.pattern import R_BE
from remarkable.predictor.hkex_predictor.schemas.c_rule_schema.c_rule_c2 import (
    extend_significant_elements,
    filter_c2_answer,
)
from remarkable.predictor.hkex_predictor.schemas.c_rule_schema.c_rule_c7 import R_ANY_DATE
from remarkable.predictor.hkex_predictor.schemas.pattern import R_MONEY_UNIT, R_NOT
from remarkable.predictor.models.base_model import BaseModel
from remarkable.predictor.predictor_metadata.rule_c2 import (
    P_C2_NEG_ACCOUNTS,
    R_COMPANY_NAMES,
    R_FVTOCI,
    R_FVTPL,
    R_HELD_AMORTISED_COST,
    R_HELD_FOR_TRADING,
)
from remarkable.predictor.schema_answer import PredictorResult, TableCellsResult

R_C2_COMPANY_NAME = r"|".join(R_COMPANY_NAMES)
# owned: http://************:55647/#/project/remark/412632?treeId=7144&fileId=113940&schemaId=18&projectId=17&schemaKey=C2.1.1
R_C21_SUBSCRIBE = r"(?<!no longer )(h[eo]ld|subscribe|invested|entered\s*into|acquired|owned\s*[1-9])"
R_CAPITALIZE_WORD = rf"[A-Z][{R_MIDDLE_DASHES}A-Za-z\d.]*"
P_C21_NAME_PS = [
    # Entity: https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7431#note_740198
    r"\b(investee|Global|target\s*compan(y|ies)|Entity|Bank)[\"“”][)）]",
    PositionPattern.compile(r"\bSP[\"“”][)）]", flag=0),
    PositionPattern.compile(R_C21_SUBSCRIBE, r"[\"“”][A-Z]+[\"“”][)）]", flag=0),
    r"investment\s*holding\s*company",
    PositionPattern.compile(R_C2_COMPANY_NAME, R_C2_COMPANY_NAME, flag=0),
    PositionPattern.compile(R_C21_SUBSCRIBE, rf"\s(of|by|from|with)\s*{R_CAPITALIZE_WORD}\s(?!\d)", flag=0),
    MatchMulti.compile(
        rf"\b({R_C21_SUBSCRIBE}|invest(ed|ment)\b|financ(e|ial)|loan\b|acquired\s*[\d.]+{R_PERCENT})",
        rf"namely|{R_C2_COMPANY_NAME}",
        operator=all,
        flag=0,
    ),
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7334
    PositionPattern.compile(rf"the\s*(?i:group|company)\s*invested\s*in\s*({R_CAPITALIZE_WORD}[\s,，]+){{2}}", flag=0),
    NeglectPattern.compile(
        match=rf"investment\s*(target|objective|purpose)\s*of\s*({R_CAPITALIZE_WORD}[\s,，]+){{2}}",
        unmatch=r"\bFund",
        flag=0,
    ),
    # http://************:55647/#/project/remark/293717?treeId=7109&fileId=70670&schemaId=18&projectId=17&schemaKey=C2.1.1
    PositionPattern.compile(rf"equity\s*interest\s*(in|of)\s*({R_CAPITALIZE_WORD}[\s,，]+){{2}}", flag=0),
]

R_MAIN_BUSINESS = [
    r"(Principal|main|core|primary|key)\s*(Business|Activit(y|ies))(?!(es)?\s*[,，])",
    # http://************:55647/#/project/remark/379680?treeId=4473&fileId=113004&schemaId=18&projectId=17&schemaKey=C2.1.1
    r"(description|nature|brief)\s*of\s*(the\s*)?business(?!(es)?\s*[,，])",
]

P_C21_PRINCIPAL_BUSINESS = [
    *R_MAIN_BUSINESS,
    r"\bengag(e[ds]?|ing)\s*in\b",
    r"the\s*business\s*of\s*(the\s*)?[a-z]+ing\s",
    r"investment\s*holding\s*company",
    # http://************:55647/#/project/remark/265914?treeId=22819&fileId=70397&schemaId=18&projectId=22819&schemaKey=C2.1.1 index=188
    r"investment\s*objective",
    r"\sto\s*provide|through\s*providing",
    PositionPattern.compile(r"provide", r"property|service|product", flag=0),
    NeglectPattern.compile(
        match=PositionPattern.compile(r"subscribe", r"property|service|product", flag=0), unmatch=r"certain"
    ),
    # http://************:55647/#/project/remark/416797?treeId=6255&fileId=114405&schemaId=18&projectId=17&schemaKey=C2.1.1 index=435
    r"\bfocus(es|ing)?\s*(on|to)\s",
    # PositionPattern.compile(r"\b(is|are) a global", r"\bcompany\b"),
    # http://************:55647/#/project/remark/266194?treeId=45553&fileId=70453&schemaId=18&projectId=45553&schemaKey=C2.1.1
    NeglectPattern.compile(
        match=PositionPattern.compile(
            rf"equity\s*interest|{R_C21_SUBSCRIBE}", r"\san?\s+[a-z]([-a-zA-Z]+\s+){2,10}company", flag=0
        ),
        unmatch=r"\sa\s*(limited\s+|liability\s+|subsidiary\s+)+company",
    ),
    # http://************:55647/#/project/remark/411250?treeId=45553&fileId=113786&schemaId=18&projectId=17&schemaKey=C2.1.1
    PositionPattern.compile(
        rf"equity\s*interest|{R_C21_SUBSCRIBE}",
        rf"({R_CAPITALIZE_WORD}[\s,，]+){{2}}",
        r"(^|\s)a\s*company\s*in\s",
        flag=0,
    ),
    # http://************:55647/#/project/remark/269023?treeId=7151&fileId=70623&schemaId=18&projectId=7151&schemaKey=C2.1.1
    # http://************:55647/#/project/remark/296224?treeId=37978&fileId=71003&schemaId=18&projectId=17&schemaKey=C2.1.1
    # http://************:55647/#/project/remark/413974?treeId=10475&fileId=114090&schemaId=18&projectId=17&schemaKey=C2.1.1
    NeglectPattern.compile(
        match=rf"^({R_CAPITALIZE_WORD}[\s,，]+){{1,6}}{R_BE}\s*an?\s+([-a-z]+\s+){{2,10}}(company|bank)",
        # http://************:55647/#/project/remark/420056?treeId=44638&fileId=114772&schemaId=18&projectId=17&schemaKey=C2.1.1 index=580
        unmatch=r"\sa\s*(limited\s+|liability\s+|subsidiary\s+)+company",
    ),
    # http://************:55647/#/project/remark/269142?treeId=2573&fileId=70640&schemaId=18&projectId=2573&schemaKey=C2.1.1
    PositionPattern.compile(r"\san?\s*company", r"provide|service", flag=0),
    # 必须小写，大写的Property可能是公司名称： http://************:55647/#/project/remark/268580?treeId=38058&fileId=70560&schemaId=18&projectId=38058&schemaKey=C2.1.1&page=158 index=1773
    PositionPattern.compile(r"\b(owner|operator|invest)", r"property", flag=0),
    # http://************:55647/#/project/remark/405813?treeId=6649&fileId=113174&schemaId=18&projectId=17&schemaKey=C2.1.1
    r"(its|the)\s*business\s*in\s",
    # http://************:55647/#/project/remark/412477?treeId=12776&fileId=113923&schemaId=18&projectId=17&schemaKey=C2.1.1
    r"responsible\s*for\b",
    # http://************:55647/#/project/remark/414491?treeId=8452&fileId=114148&schemaId=18&projectId=17&schemaKey=C2.1.1
    r"which\s*is\s*active\s*in",
    # http://************:55647/#/project/remark/418114?treeId=6323&fileId=114552&schemaId=18&projectId=17&schemaKey=C2.1.1
    r"mainly\s*operating\s*in\s",
]
P_C21_FAIR_VALUE_BASED = PositionPattern.compile(
    r"fair\s*value", r"\bon\s*the\s*basis|(re)?assessed|(re)?classified|determined|carried\s*(out\s*)?"
)
# http://************:55647/#/project/remark/265369?treeId=37712&fileId=70288&schemaId=18&projectId=17&schemaKey=C2.1.1
R_C21_NEGLECT = [
    r"derecognize\s*the\s*assets",
    rf"spin{R_MIDDLE_DASH}off",
    # 民事诉讼
    r"\bcivil\s*action",
    # 刑事诉讼
    r"\bcriminal\s*proceeding",
    # 法院
    r"\bCourt\b",
    # 原告/被告
    r"\b(plaintiff|defendant)",
    # http://************:55647/#/project/remark/266104?treeId=21689&fileId=70435&schemaId=18&projectId=17&schemaKey=C2.1.1 index=1433
    r"determined\s*by\s*a\s*valuation",
    # 评估公司： http://************:55647/#/project/remark/266519?treeId=6860&fileId=70518&schemaId=18&projectId=6860&schemaKey=C2.1.1&page=170 index=2665
    P_C21_FAIR_VALUE_BASED,
    r"investment\s*(polic|strategy)",
    # 增值服务： http://************:55647/#/project/remark/266194?treeId=45553&fileId=70453&schemaId=18&projectId=45553&schemaKey=C2.1.1&page=22 index=317
    rf"value[{R_MIDDLE_DASHES}\s]added\s*support\s*include",
    # 审计标准： http://************:55647/#/project/remark/417481?treeId=11662&fileId=114481&schemaId=18&projectId=17&schemaKey=C2.1.1 index=2034
    r"Accounting\s*Standards|Business\s*Enterprises",
    r"obtain(s|ed|ing)? control of",
    # 实际控股人：http://************:55647/#/project/remark/420056?treeId=44638&fileId=114772&schemaId=18&projectId=17&schemaKey=C2.1.1 index=581
    r"\bactual\s*controller",
    # 转让：http://************:55647/#/project/remark/416677?treeId=12228&fileId=114392&schemaId=18&projectId=17&schemaKey=C2.1.1 index=2027
    PositionPattern.compile(r"\btransfer", rf"\d{R_PERCENT}|{R_MONEY_UNIT}\d|\d\s*([a-z]+\s+){{,3}}shares"),
]
R_C21_PARA_NEGLECT = [
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7333#note_737783
    rf"{R_BE}\s*closed\s*down",
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7431#note_739790
    PositionPattern.compile(r"entered\s*into", r"joint\s*venture"),
    # http://************:55647/#/project/remark/419432?treeId=10898&fileId=114701&schemaId=18&projectId=17&schemaKey=C2.1.1
    r"connected\s*transaction",
    # http://************:55647/#/project/remark/417481?treeId=11662&fileId=114481&schemaId=18&projectId=17&schemaKey=C2.1.1 index=1612
    r"\san?\s*associate",
]
R_FUND = r"\b(Bond|Bill|REIT|ETF|SP|Trust(?!s?\s*[Ss]hare)|Fund|Deposit|Portfolio|Commodit(y|ie)|convertible)[Ss]?\b"
P_C21_CELL_PRODUCTS = MatchMulti.compile(R_FUND, operator=any)
P_C21_PRODUCTS = MatchMulti.compile(
    r"treasur[ey]\s*(bond|bill)",
    r"\binsurance\s*polic(y|ies)",
    # http://************:55647/#/project/remark/420076?treeId=8556&fileId=114774&schemaId=18&projectId=17&schemaKey=C2.1.1
    # http://************:55647/#/project/remark/410522?treeId=18134&fileId=113704&schemaId=18&projectId=17&schemaKey=C2.1.1
    r"(management|investment|structured)\s*product",
    r"assets?\s*management",
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7431#note_743059
    PositionPattern.compile(r"\bWMP[sS]?\b", flag=0),
    operator=any,
)
R_C21_TBL_TITLES = [
    r"\b(investment|funds?\b|product|[Hh][eo]ld|subscribe|financial|instrument|securities|\bbond|fair\s*value\s*through|cost|FVT?PL|FVT?OCI)"
]
P_C21_NEGLECT_TBL_CELL = MatchMulti.compile(
    P_C2_NEG_ACCOUNTS,
    R_FVTPL,
    R_FVTOCI,
    R_HELD_FOR_TRADING,
    R_HELD_AMORTISED_COST,
    r"financial\s*asset",
    r"(trading|listed|debt).+(investment|securities|instrument)",
    # http://************:55647/#/project/remark/411591?treeId=19167&fileId=113824&schemaId=18&projectId=17&schemaKey=C2.1.1
    rf"^(un)?listed{R_CN_SPACE_COLON}$",
    r"\bfinancial\s*institutions?\b",
    r"^((sub)?total|net\s|less\s*[:：])",
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7431#note_741926
    r"(securities|investments?|fair\s*value|stocks?)(\s*[(（].+[)）])?$",
    r"[:：]$",
    r"fair\s*value",
    r"financial\s*products?",
    r"equity|security|securities|investments",
    # http://************:55647/#/project/remark/404559?treeId=12688&fileId=113028&schemaId=18&projectId=17&schemaKey=C2.1.1
    rf"^(non{R_MIDDLE_DASH})?current|(non{R_MIDDLE_DASH})?current$",
    rf"(as|at)\s*(of\s*)?{R_ANY_DATE}",
    # http://************:55647/#/project/remark/406886?treeId=4433&fileId=113294&schemaId=18&projectId=17&schemaKey=C2.1.1
    r"\bdue\s",
    operator=any,
)
R_C21_NEG_TBL_TITLES = [
    # http://************:55647/#/project/remark/295796?treeId=7681&fileId=70950&schemaId=18&projectId=17&schemaKey=C2.1.1 index=490
    # http://************:55647/#/project/remark/268890?treeId=3137&fileId=70604&schemaId=18&projectId=17&schemaKey=C2.1.1 index=1898
    NeglectPattern.compile(match=r"disposal|disposed", unmatch=rf"{R_NOT}|\binclud(e|ing)"),
]
R_C21_NAME_HEADER = [
    r"Name|Counterparty|company|investee",
    r"investment(?!s?\s*(income|cost|strateg))|invested|instrument|financial\s*asset|^\s*$",
]


def filter_c21_table_answer(answers: list[PredictorResult], **kwargs):
    if not answers:
        return answers
    new_answers = []
    common_results = BaseModel.get_common_predictor_results(answers)
    for common_result in common_results:
        new_results = []
        for element_result in common_result.element_results:
            if not is_table_result(element_result):
                continue
            new_cells = []
            all_subtitles = element_result.parsed_cells[0].table.all_subtitles
            parsed_cells = element_result.parsed_cells
            for cell in parsed_cells:
                # if P_C2_NEG_ACCOUNTS.search(cell.no_cn_text):
                #     continue
                if any(P_C2_NEG_ACCOUNTS.search(subtitle_cell.no_cn_text) for subtitle_cell in cell.subtitle_cells):
                    continue
                if (
                    not cell.clean_text
                    or cell.rowidx in all_subtitles
                    or (
                        P_C21_NEGLECT_TBL_CELL.search(cell.no_cn_text)
                        and not P_C21_PRODUCTS.search(cell.no_cn_text)
                        and not P_C21_CELL_PRODUCTS.search(cell.no_cn_text)
                    )
                ):
                    continue
                new_cells.append(cell)
            if not new_cells:
                continue
            if new_cells == parsed_cells:
                new_results.append(element_result)
            else:
                new_results.append(TableCellsResult(element_result.element, new_cells))
        if not new_results:
            continue
        common_result.element_results = new_results
        new_answers.append(common_result)
    return new_answers


PRODUCT_MODELS = [
    # 3. Investment为理财产品，比如fund, treasury bond, treasure bill, 国债，deposit，portfolio，ETF等，可以仅披露产品名称
    {
        "name": "special_cells",
        "inject_elements_func": extend_significant_elements,
        "post_process_model_answers": filter_c21_table_answer,
        "multi": True,
        "multi_elements": True,
        "title_patterns": R_C21_TBL_TITLES,
        "neglect_title_patterns": R_C21_NEG_TBL_TITLES,
        "col_header_pattern": [
            r"\b(Name|fun[dg]|issuer|product|project|investee|counterparty)|^\s*$",
        ],
        "cell_pattern": [
            P_C21_PRODUCTS,
            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7431#note_741926
            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7431#note_742824
            P_C21_CELL_PRODUCTS,
            # http://************:55647/#/project/remark/380334?treeId=13467&fileId=114715&schemaId=18&projectId=17&schemaKey=C2.1.1
            r"Mainstream[:$]",
        ],
        "enum": AnswerValueEnum.PS.value,
    },
    # 4. 段落模型，Investment为理财产品，比如fund, treasury bond, treasure bill, 国债，deposit，portfolio，ETF等，可以仅披露产品名称
    {
        "name": "para_match",
        "inject_elements_func": extend_significant_elements,
        "enum": AnswerValueEnum.PS.value,
        "multi_elements": True,
        "para_separator": P_PERIOD_SEPARATOR,
        "sentence_pattern": [
            PositionPattern.compile(rf"\b{R_FUND}[\"“”][）)]", flag=0),
            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7431#note_742833
            MatchMulti.compile(P_C21_PRODUCTS, rf"{R_C21_SUBSCRIBE}|investment", operator=all),
            # http://************:55647/#/project/remark/295439?treeId=37787&fileId=70905&schemaId=18&projectId=17&schemaKey=C2.1.1
            MatchMulti.compile(R_C21_SUBSCRIBE, rf"(?<![Ii]nvestment )(?<![Th]he ){R_FUND}", operator=all, flag=0),
            # http://************:55647/#/project/remark/355752?treeId=11586&fileId=112858&schemaId=18&projectId=17&schemaKey=C2.1.1
            PositionPattern.compile(
                rf"^({R_CAPITALIZE_WORD}\s+){{1,6}}(is|was)\s*an?\s+([a-zA-Z]+\s+){{1,6}}fund", flag=0
            ),
        ],
        "neglect_sentence_pattern": [
            *R_C21_NEGLECT,
            # http://************:55647/#/project/remark/419985?treeId=4353&fileId=114764&schemaId=18&projectId=17&schemaKey=C2.1.1 P213, index=2475
            rf"including\s*(bond|trust|(cash\s*)?fund|{R_FUND})",
            # http://************:55647/#/project/remark/406886?treeId=4433&fileId=113294&schemaId=18&projectId=17&schemaKey=C2.1.1
            r"financial\s*institutions?\s*(that\s*|which\s*)?provide",
        ],
        "neglect_pattern": R_C21_PARA_NEGLECT,
    },
]

predictor_options = [
    {
        "path": ["C2.1.1", "Name"],
        "models": [
            # 1. 通用NS
            {
                "name": "c2ns",
            },
            # 2. name分开披露，有明确的公司名称
            {
                "name": "multi_models",
                "operator": "union",
                "enum": AnswerValueEnum.PS.value,
                "require_unique_element": True,
                "deduplicate_elements": False,
                "models": [
                    # 2.1 精确的表名 + 模糊的公司名称
                    # http://************:55647/#/project/remark/266414?treeId=9016&fileId=70497&schemaId=18&projectId=9016&schemaKey=C2.1.1
                    {
                        "name": "special_cells",
                        "inject_elements_func": extend_significant_elements,
                        "multi_elements": True,
                        "multi": True,
                        "title_patterns": [
                            PositionPattern.compile(R_SIGNIFICANT_INVESTMENT, r"[:：]$"),
                            # http://************:55647/#/project/remark/379547?treeId=14732&fileId=112985&schemaId=18&projectId=17&schemaKey=C2.1.1
                            MatchMulti.compile(r"investments", rf"\s5{R_PERCENT}\s*or\s*more", r"[:：]$", operator=all),
                            MatchMulti.compile(
                                r"investments", rf"\b(not\s*less|more)\s*than\s*5{R_PERCENT}", r"[:：]$", operator=all
                            ),
                        ],
                        "neglect_title_patterns": R_C21_NEG_TBL_TITLES,
                        "row_col_relation": "or",
                        "col_header_pattern": R_C21_NAME_HEADER,
                        # http://************:55647/#/project/remark/413828?treeId=3238&fileId=114074&schemaId=18&projectId=17&schemaKey=C2.1.1
                        "row_header_pattern": R_C21_NAME_HEADER,
                        "cell_pattern": MatchMulti.compile(
                            R_C2_COMPANY_NAME,
                            rf"^[A-Z]+{R_CN_SPACE}$",
                            rf"^({R_CAPITALIZE_WORD}\s+){{2}}",
                            operator=any,
                            flag=0,
                        ),
                        "enum": AnswerValueEnum.PS.value,
                    },
                    # 2.2 模糊的表名 + 单元格中必须有公司名称
                    {
                        "name": "special_cells",
                        "inject_elements_func": extend_significant_elements,
                        "multi_elements": True,
                        "multi": True,
                        "title_patterns": R_C21_TBL_TITLES,
                        "neglect_title_patterns": R_C21_NEG_TBL_TITLES,
                        "row_col_relation": "or",
                        "col_header_pattern": R_C21_NAME_HEADER,
                        # http://************:55647/#/project/remark/413828?treeId=3238&fileId=114074&schemaId=18&projectId=17&schemaKey=C2.1.1
                        "row_header_pattern": R_C21_NAME_HEADER,
                        "cell_pattern": [PositionPattern.compile(R_C2_COMPANY_NAME, flag=0)],
                        "enum": AnswerValueEnum.PS.value,
                    },
                    # 2.3 段落模型，句子中包含公司名称
                    {
                        "name": "para_match",
                        "inject_elements_func": extend_significant_elements,
                        "enum": AnswerValueEnum.PS.value,
                        "multi_elements": True,
                        "para_separator": P_PERIOD_SEPARATOR,
                        "sentence_pattern": P_C21_NAME_PS,
                        "neglect_sentence_pattern": [
                            *R_C21_NEGLECT,
                            # debt：http://************:55647/#/project/remark/262628?projectId=17&treeId=12441&fileId=69740&schemaId=18&SchemaKey=C2.1.1 index=6146
                            r"\bdebt",
                        ],
                        "neglect_pattern": R_C21_PARA_NEGLECT,
                    },
                ],
            },
            # 表格+段落模型，Investment为理财产品，比如fund, treasury bond, treasure bill, 国债，deposit，portfolio，ETF等
            *PRODUCT_MODELS,
        ],
    },
    {
        "path": ["C2.1.1", "Principal business"],
        "post_process": filter_c2_answer,
        "models": [
            # 1. 通用NS
            {
                "name": "c2ns",
            },
            {
                "name": "multi_models",
                "operator": "union",
                "enum": AnswerValueEnum.PS.value,
                "require_unique_element": True,
                "deduplicate_elements": False,
                "models": [
                    # 2.1 模糊表名+指定列名
                    {
                        "name": "special_cells",
                        "inject_elements_func": extend_significant_elements,
                        "multi_elements": True,
                        "multi": True,
                        "title_patterns": R_C21_TBL_TITLES,
                        "neglect_title_patterns": R_C21_NEG_TBL_TITLES,
                        "row_col_relation": "or",
                        "col_header_pattern": [*R_MAIN_BUSINESS, r"\bproducts?\s*type", r"type\s*of\s*product"],
                        # http://************:55647/#/project/remark/413828?treeId=3238&fileId=114074&schemaId=18&projectId=17&schemaKey=C2.1.1
                        "row_header_pattern": [*R_MAIN_BUSINESS, r"\bproducts?\s*type", r"type\s*of\s*product"],
                        "enum": AnswerValueEnum.PS.value,
                    },
                    # 2.2 段落模型，句子中包含主营业务
                    {
                        "name": "para_match",
                        "inject_elements_func": extend_significant_elements,
                        # C2.1.1 不过滤日期：https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7327#note_737714
                        # "post_process_model_answers": C2PostProcessor(),
                        "multi_elements": True,
                        "multi": True,
                        "para_separator": P_PERIOD_SEPARATOR,
                        "sentence_pattern": P_C21_PRINCIPAL_BUSINESS,
                        "neglect_sentence_pattern": [
                            *R_C21_NEGLECT,
                            # http://************:55647/#/project/remark/420188?treeId=9379&fileId=114787&schemaId=18&projectId=17&schemaKey=C2.1.1
                            # http://************:55647/#/project/remark/419441?treeId=5436&fileId=114702&schemaId=18&projectId=17&schemaKey=C2.1.1&page=118 index=2085
                            r"^the\s*(group|company|investment|fair\s*value)",
                            # http://************:55647/#/project/remark/406886?treeId=4433&fileId=113294&schemaId=18&projectId=17&schemaKey=C2.1.1
                            r"financial\s*institutions?\s*(that\s*|which\s*)?provide",
                        ],
                        "neglect_pattern": R_C21_PARA_NEGLECT,
                        "enum": AnswerValueEnum.PS.value,
                    },
                ],
            },
            # 表格+段落模型，Investment为理财产品，比如fund, treasury bond, treasure bill, 国债，deposit，portfolio，ETF等
            *PRODUCT_MODELS,
        ],
    },
]
