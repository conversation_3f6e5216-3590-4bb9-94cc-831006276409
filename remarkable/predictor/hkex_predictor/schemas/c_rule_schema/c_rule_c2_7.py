from copy import copy
from operator import itemgetter

from remarkable.common.common_pattern import P_PERIOD_SEPARATOR
from remarkable.common.constants import AnswerValueEnum
from remarkable.common.pattern import (
    MatchMulti,
    NeglectPattern,
)
from remarkable.predictor.hkex_predictor.models.c2 import R_SIGNIFICANT_INVESTMENT
from remarkable.predictor.hkex_predictor.schemas.list_rule_schema.list_rule_b94 import R_PERCENT
from remarkable.predictor.hkex_predictor.schemas.pattern import R_NOT

# http://************:55647/#/project/remark/268685?treeId=9592&fileId=70575&schemaId=18&projectId=17&schemaKey=C2.7
# FVOCI/FVTOCI/FVTPL相关章节
# 投资/理财
R_INVESTMENT = r"(?<!total )\b[Ii]nvestments?\b(?!\s*grade)|wealth\s*management|structured\s*deposit|portfolio|assets\s*structure|invested"  # |strategic\s*investment
R_PURPOSE = r"\b(plan|target(?![,，；;.])|objective|(?<!strategic )purpose|in\s*order\s*to)"
R_C2_7_KW = [
    # 仅披露投资类型种类，不可以判定为PS https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/5608#note_671216
    # rf"\b(long|short)({R_MIDDLE_DASH}|\s*)term",
    # 对未来的投资展望、机遇
    r"\b(anticipate|(?<!estimated )future|believe|achiev(e|ing)|opportunit(y|ies)|Looking ahead)",
    # 对投资的态度（比如谨慎）
    r"\b(cautious|prudent|conservative|minimal\s*risk|optimistic|proactive)",
    # 投资策略 intended to: https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7331#note_735615
    r"strateg(y|ies)|intend(s|ed)?\s*to\b",
    # r"strateg(y|ies)|(strategic|investments?)\s*(plan|target|objetive|purpose|nature)",
    # r"\b(h[eo]ld\s*for\s.*?strategic|strategic holding)",
    # 投资目标
    R_PURPOSE,
    r"h[oe]ds?\s*for\s*[a-z]+ing\b",
    # r"directors\s*consider|[Ii]n\s*view\s*of\s)",
    # 密切监控相关因素调整投资组合: http://************:55647/#/project/remark/265624?treeId=9782&fileId=70339&schemaId=18&projectId=9782&schemaKey=C2.7
    r"\bmonitor.*?portfolio",
    # 目标收益率
    rf"(?<!bond )yield.*?\d{R_PERCENT}",
    # 最大限度地提高回报
    r"\b(maximize|better|reasonable|good)\s*return",
    # 保持灵活性及流动性: https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7454#note_741727
    r"flexibility|liquidity|effective|affect",
    # r"\bmaintaining flexibility and liquidity|\bstrong liquidity.*?safety feature and reasonable return|operational liquidity",
]
P_C2_7_PS = MatchMulti.compile(
    R_INVESTMENT, r"|".join(R_C2_7_KW), r"\b(?i:our|the)\s*(?i:group|company)", operator=all, flag=0
)
OTHER_C2_7_PS = [
    MatchMulti.compile(r"\bmarket capitalisation.*?improved", r"\benhancement.*?equity", operator=all),
    MatchMulti.compile(r"\blong-term competitive capabilities", operator=all),
    # # http://************:55647/#/project/remark/409332?treeId=9934&fileId=113570&schemaId=18&projectId=17&schemaKey=C2.7
    # MatchMulti.compile(r"\baffect.*?liquidity", r"good (returns|cooperative relationship)", operator=any),
]
R_C2_7_NEGLECT = [
    R_NOT,
    r"\bon\s*the\s*basis",
    # http://************:55647/#/project/remark/293642?treeId=3726&fileId=70658&schemaId=18&projectId=17&schemaKey=C2.7&page=133 index=2463
    r"(re)?assessed|(re)?classified|determined|carried\s*(out\s*)?",
]

P_C2_7_AS_FOLLOW = MatchMulti.compile(R_INVESTMENT, rf"{R_PURPOSE}|strateg(y|ies)", r"[：:]$", operator=all, flag=0)
R_INVESTMENT_STRATEGY = r"investments?\s*strateg(y|ies)"


def extend_c27_significant_elements(elements, predictor, **kwargs):
    """
    非NS场景下，C2.7候选元素块，不受科目是否>5%约束，所以这里需要把初步定位元素块合并到一起
    https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6172#note_741870
    """
    existed_indices, new_elements = set(), []
    for element in elements:
        if element.get("score") < 0.7:
            continue
        existed_indices.add(element["index"])
        new_elements.append(element)
    # 扩展满足>=5%章节下的元素块
    significant_elements = predictor.prophet.metadata.get("c2_candidate_elements") or []
    for element in significant_elements:
        if element["index"] in existed_indices:
            continue
        new_element = copy(element)
        new_element["score"] = 0
        new_elements.append(new_element)
    return sorted(new_elements, key=itemgetter("score"), reverse=True)


predictor_options = [
    {
        "path": ["C2.7"],
        "models": [
            {
                "name": "c2ns",
            },
            # http://************:55647/#/project/remark/266294?treeId=8130&fileId=70473&schemaId=18&projectId=17&schemaKey=C2.7
            {
                "name": "special_cells",
                "inject_elements_func": extend_c27_significant_elements,
                "enum": AnswerValueEnum.PS.value,
                "row_col_relation": "or",
                "col_header_pattern": R_INVESTMENT_STRATEGY,
                # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6684#note_692541
                # http://************:55647/#/project/remark/413828?treeId=3238&fileId=114074&schemaId=18&projectId=17&schemaKey=C2.1.1
                "row_header_pattern": R_INVESTMENT_STRATEGY,
            },
            {
                "name": "para_match",
                "inject_elements_func": extend_c27_significant_elements,
                "multi_elements": True,
                "multi": True,
                "enum": AnswerValueEnum.PS.value,
                "para_separator": P_PERIOD_SEPARATOR,
                "skip_syllabus_title": True,
                "sentence_pattern": [P_C2_7_PS, *OTHER_C2_7_PS],
                "neglect_sentence_pattern": R_C2_7_NEGLECT,
                "as_follow_pattern": NeglectPattern.compile(match=P_C2_7_AS_FOLLOW, unmatch=R_SIGNIFICANT_INVESTMENT),
            },
            {
                "name": "score_filter",
                "inject_elements_func": extend_c27_significant_elements,
                "multi_elements": True,
                "threshold": 0.8,
                "aim_types": ["PARAGRAPH"],
                "skip_syllabus_title": True,
                "enum": AnswerValueEnum.PS.value,
            },
        ],
    },
]
