from remarkable.common.common_pattern import P_PERIOD_SEPARATOR
from remarkable.common.constants import AnswerValueEnum
from remarkable.common.pattern import (
    MatchMulti,
    PositionPattern,
)
from remarkable.predictor.common_pattern import R_PERCENT
from remarkable.predictor.hkex_predictor.schemas.c_rule_schema.c_rule_c2 import (
    C2PostProcessor,
    extend_significant_elements,
    filter_c2_answer,
)
from remarkable.predictor.hkex_predictor.schemas.pattern import R_ONLY_ENG_NUMBER

# invested: http://************:55647/#/project/remark/268367?treeId=2418&fileId=70530&schemaId=18&projectId=17&schemaKey=C2.2.1
# https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/5603#note_673162
R_C2_2_HELD = r"\b(h[eo]ld|subscribed|agree(ed|ment)\s*to\s*subscribe|subscription|owned|invested|acquired|represent(s|ed)\s*\d|(has|had|have|represented)\s*(approximately\s*)?\d)"
# 一些例子: 7.8 million non-voting shares、88,150,000 (2022:88,150,000) Min Xin Shares
R_C2_2_SHARES = rf"\b([\d.,]+\s*million|{R_ONLY_ENG_NUMBER}|[\d,]+\d(?!{R_PERCENT}))\s*([（(][^)）]*?[)）]\s*)?([-\w]+\s+){{0,4}}(share|unit)s"
R_PERCENT_WITH_NUM = rf"\b\d(\d?|00)(\.\d+)?{R_PERCENT}(?! (to|per|for)\b)"
P_C23_SHARES_PS = [
    # r"(interest|share)s?\s*held|share\s*holdings?)",
    PositionPattern.compile(R_C2_2_HELD, R_C2_2_SHARES),
    # http://************:55647/#/project/remark/294979?treeId=37749&fileId=70848&schemaId=18&projectId=17&schemaKey=C2.2.1
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7334#note_733104
    PositionPattern.compile(r"\b(investment|securities)", R_C2_2_SHARES),
    # http://************:55647/#/project/remark/293916?treeId=3739&fileId=70699&schemaId=18&projectId=17&schemaKey=C2.2.1
    PositionPattern.compile(r"\s(neither|not|nor)|['‘’]t", r"h[eo]ld\s*any\s*shares"),
]
# P_NEG_PARENT = NeglectPattern.compile(match=r"auditor", unmatch=r"management|analysis|discussion|notes")
P_C23_PERCENT_PS = [
    PositionPattern.compile(R_PERCENT_WITH_NUM, r"\b(equity|ownership)\s*interests?\b|\bfund\s*commitments?\b"),
    PositionPattern.compile(r"shareholding\s*(proportion|percentage)", R_PERCENT_WITH_NUM),
    PositionPattern.compile(
        rf"{R_C2_2_HELD}|\binvest(ed|ment)",
        R_PERCENT_WITH_NUM,
        r"\b(shares|share\s*capital|interest)\b",
    ),
    PositionPattern.compile(rf"{R_C2_2_HELD}|\binvestment", R_C2_2_SHARES, R_PERCENT_WITH_NUM),
    # http://************:55647/#/project/remark/293981?treeId=4072&fileId=70708&schemaId=18&projectId=17&schemaKey=C2.2.1
    PositionPattern.compile(rf"{R_C2_2_HELD}|has|have|had", r"\bequity\s*interests?\b", R_PERCENT_WITH_NUM),
    PositionPattern.compile(r"a total of", R_PERCENT_WITH_NUM, r"\bshareholding\b"),
]
P_C23_PERCENT_NEGLECT = [
    r"convertible\s*bonds|(annual|expected)\s*interest",
    r"originally represented.*?\d+% of issued shares",  # 最初投资的持股比例
    r"investment holding company",  # 被投资公司对其他公司的持股占比
    r"\bOutstanding Bonds\b",  # 未清偿债券
    # 主语不是the group/company： https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7329#note_733105
    MatchMulti.compile(
        r"\s[A-Z][a-zA-Z.]*\s*[A-Z][a-zA-Z.]*\s*(h[oe]ld|owne?|investe?|acquire?|subscribe?)d?",
        operator=any,
        flag=0,
    ),
]


predictor_options = [
    {
        "path": ["C2.2.1", "Number of shares held"],
        "post_process": filter_c2_answer,
        "models": [
            {
                "name": "c2ns",
            },
            {
                "name": "multi_models",
                "operator": "union",
                "require_unique_element": True,
                "enum": AnswerValueEnum.PS.value,
                "models": [
                    {
                        "name": "special_cells",
                        "inject_elements_func": extend_significant_elements,
                        "multi_elements": True,
                        "enum": AnswerValueEnum.PS.value,
                        "multi": True,
                        "col_header_pattern": [
                            PositionPattern.compile(r"\b(number|no\.?)\s", r"\b(share|unit)s?\b"),
                        ],
                    },
                    {
                        "name": "para_match",
                        "enum": AnswerValueEnum.PS.value,
                        "inject_elements_func": extend_significant_elements,
                        "post_process_model_answers": C2PostProcessor(),
                        "neglect_syllabus_regs": [r"\bdisposal\s*of"],
                        "multi_elements": True,
                        "para_separator": P_PERIOD_SEPARATOR,
                        "multi": True,
                        "skip_syllabus_title": True,
                        "sentence_pattern": P_C23_SHARES_PS,
                    },
                ],
            },
            {
                "name": "special_cells",
                "inject_elements_func": extend_significant_elements,
                "multi_elements": True,
                "cell_pattern": [r"\d{1,3}(,\d{3})? ordinary shares? in"],
                "enum": AnswerValueEnum.PS.value,
            },
            # 找不到当年内的，就找其他年份（即不配置`post_process_model_answers）
            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7333#note_737735
            {
                "name": "para_match",
                "enum": AnswerValueEnum.PS.value,
                "inject_elements_func": extend_significant_elements,
                "neglect_syllabus_regs": [r"\bdisposal\s*of"],
                "multi_elements": True,
                "para_separator": P_PERIOD_SEPARATOR,
                "multi": True,
                "skip_syllabus_title": True,
                "sentence_pattern": P_C23_SHARES_PS,
            },
            {
                "name": "score_filter",
                "threshold": 0.8,
                "neglect_pattern": MatchMulti.compile(r"other persons[’'] interests and short positions", operator=any),
                "enum": AnswerValueEnum.PS.value,
            },
        ],
    },
    {
        "path": ["C2.2.1", "Percentage of shares held"],
        "post_process": filter_c2_answer,
        "models": [
            {
                "name": "c2ns",
            },
            {
                "name": "multi_models",
                "operator": "union",
                "require_unique_element": True,
                "enum": AnswerValueEnum.PS.value,
                "models": [
                    {
                        "name": "special_cells",
                        "inject_elements_func": extend_significant_elements,
                        "multi_elements": True,
                        "multi": True,
                        "col_header_pattern": [
                            PositionPattern.compile(r"proportion|percentage|[%％]", rf"share|interest|{R_C2_2_HELD}"),
                            MatchMulti.compile(
                                r"shareholding", r"[%％]|proportion|percentage|\binterest\b", operator=all
                            ),
                        ],
                        "enum": AnswerValueEnum.PS.value,
                    },
                    # http://************:55647/#/project/remark/296224?treeId=37978&fileId=71003&schemaId=18&projectId=17&schemaKey=C2.2.1
                    {
                        "name": "special_cells",
                        "inject_elements_func": extend_significant_elements,
                        "enum": AnswerValueEnum.PS.value,
                        "title_patterns": [
                            MatchMulti.compile(
                                r"proportion|percentage", r"\b(interest|shares)\b", R_C2_2_HELD, operator=all
                            )
                        ],
                        "multi": True,
                        "col_pattern": rf"\t[\d.]+{R_PERCENT}",
                    },
                    {
                        "name": "para_match",
                        "inject_elements_func": extend_significant_elements,
                        "post_process_model_answers": C2PostProcessor(),
                        "neglect_syllabus_regs": [r"\bdisposal\s*of"],
                        "enum": AnswerValueEnum.PS.value,
                        "multi_elements": True,
                        "para_separator": P_PERIOD_SEPARATOR,
                        "skip_syllabus_title": True,
                        "multi": True,
                        "sentence_pattern": P_C23_PERCENT_PS,
                        "neglect_sentence_pattern": P_C23_PERCENT_NEGLECT,
                    },
                ],
            },
            {
                "name": "score_filter",
                "inject_elements_func": extend_significant_elements,
                "post_process_model_answers": C2PostProcessor(),
                "multi_elements": True,
                "threshold": 0.8,
                "enum": AnswerValueEnum.PS.value,
            },
            # 找不到当年内的，就找其他年份（即不配置`post_process_model_answers）
            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7333#note_737735
            {
                "name": "para_match",
                "inject_elements_func": extend_significant_elements,
                "neglect_syllabus_regs": [r"\bdisposal\s*of"],
                "enum": AnswerValueEnum.PS.value,
                "multi_elements": True,
                "para_separator": P_PERIOD_SEPARATOR,
                "skip_syllabus_title": True,
                "multi": True,
                "sentence_pattern": P_C23_PERCENT_PS,
                "neglect_sentence_pattern": P_C23_PERCENT_NEGLECT,
            },
        ],
    },
]
