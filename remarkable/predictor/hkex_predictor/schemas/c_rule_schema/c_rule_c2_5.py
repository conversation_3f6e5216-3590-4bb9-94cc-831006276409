from remarkable.common.common_pattern import P_PERIOD_SEPARATOR
from remarkable.common.constants import AnswerValueEnum
from remarkable.common.pattern import (
    MatchMulti,
    NeglectPattern,
    PositionPattern,
)
from remarkable.predictor.hkex_predictor.schemas.c_rule_schema.c_rule_c2 import (
    C2PostProcessor,
    extend_significant_elements,
    filter_c2_answer,
)
from remarkable.predictor.hkex_predictor.schemas.pattern import R_NOT

P_ASSET_PERCENTAGE = MatchMulti.compile(
    MatchMulti.compile(
        # code：662 year：2024
        r"(total|gross)\s*((consolidated|group|company)\s)?assets?",
        r"proportion|Size\s*as\s*Compared\s*to|percentage|[%％]|relative\s*to",
        operator=all,
    ),
    PositionPattern.compile(r"investment['‘’]s size", r"\brelative to\b", r"\btotal asset"),
    operator=any,
)

predictor_options = [
    {
        "path": ["C2.5"],
        "post_process": filter_c2_answer,
        "models": [
            {
                "name": "c2ns",
            },
            {
                "name": "special_cells",
                "inject_elements_func": extend_significant_elements,
                "multi": True,
                "multi_elements": True,
                "col_header_pattern": NeglectPattern(
                    match=P_ASSET_PERCENTAGE, unmatch=MatchMulti.compile(r"interest|financial", operator=any)
                ),
                "enum": AnswerValueEnum.PS.value,
            },
            {
                "name": "special_cells",
                "inject_elements_func": extend_significant_elements,
                "multi_elements": True,
                "row_header_pattern": [
                    NeglectPattern(
                        match=P_ASSET_PERCENTAGE, unmatch=MatchMulti.compile(r"interest|financial", operator=any)
                    ),
                    # stock code 2367 year：2023
                    # http://************:55647/#/project/remark/265539?treeId=44695&fileId=70322&schemaId=18&projectId=44695&schemaKey=C2.5
                    r"assets\s*ratio",
                ],
                "enum": AnswerValueEnum.PS.value,
            },
            {
                "name": "special_cells",
                "inject_elements_func": extend_significant_elements,
                "syllabus_regs": [r"MATERIAL INVESTMENTS?"],
                "title_patterns": [r"\bwith a value of \d[%％].*?of the.*?total assets?"],
                "col_header_pattern": r"assets? ratios?",
                "enum": AnswerValueEnum.PS.value,
            },
            {
                "name": "para_match",
                "inject_elements_func": extend_significant_elements,
                "multi_elements": True,
                "para_separator": P_PERIOD_SEPARATOR,
                "paragraph_pattern": P_ASSET_PERCENTAGE,
                "post_process_model_answers": C2PostProcessor(),
                "neglect_sentence_pattern": [
                    # stock code 8006 year：2023
                    # http://************:55647/#/project/remark/265994?treeId=15429&fileId=70413&schemaId=18&projectId=15429&schemaKey=C2.5
                    rf"{R_NOT}investment",
                    # code:1585 year:2024
                    "less than 5% of the total assets",
                    # code:2509 year:2024
                    "held.*?wealth management products",
                    # code:02306 year:2024
                    "total subscription amount of",
                    # code:1753 year:2024
                    "the carrying amount of the investment in",
                ],
                "neglect_pattern": MatchMulti.compile(
                    r"more\s*of\s*the\s*Group[’']?s?\s*total\s*assets",  # fid: 70322
                    r"debt|liabilities",  # fid: 70518
                    r"not\s*have\s*any\s*significant\s*investments?",
                    r"Secured\s*Loan",
                    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/5606#note_671727
                    r"convertible\s*bonds",
                    operator=any,
                ),
                "enum": AnswerValueEnum.PS.value,
            },
            {
                "name": "score_filter",
                "inject_elements_func": extend_significant_elements,
                "threshold": 0.8,
                "enum": AnswerValueEnum.PS.value,
            },
        ],
    },
]
