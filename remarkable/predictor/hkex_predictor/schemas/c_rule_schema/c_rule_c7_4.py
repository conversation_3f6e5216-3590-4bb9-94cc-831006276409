from remarkable.common.common import is_para_elt
from remarkable.common.common_pattern import R_CHAPTER_PREFIX
from remarkable.common.constants import AnswerValueEnum
from remarkable.common.pattern import (
    MatchMulti,
    NeglectPattern,
    PositionPattern,
)
from remarkable.common.util import clean_txt, split_element
from remarkable.predictor.default_predictor.utils import is_table_result
from remarkable.predictor.hkex_predictor.models.c7 import P_C7_CHAPTER_CT, P_EXEMPT, R_IS_CT
from remarkable.predictor.hkex_predictor.schemas.c_rule_schema.c_rule_c7 import (
    C7_ND_MODELS,
    C7_NS_REFERENCE_C71,
    C71_ND_NO_ANSWER_MODEL,
    P_C7_NEG_DISCLOSED,
    P_C7_OTHER_CHAPTERS,
    R_AGREEMENT,
    R_C7_SKIP_SYLLABUS,
    R_CONTRACTUAL_ARRANGEMENTS,
    c7_above_model,
    c7_contract_model,
    c7_ct_chapter_ps_model,
    c7_follow_model,
    c7_post_processor,
    c7_syllabus_model,
    get_c7_char_results,
    group_c7_element_results,
    is_c71_base_result,
    is_null_nd,
)
from remarkable.predictor.hkex_predictor.schemas.pattern import (
    P_NOTE_CHAPTER,
    R_MONEY_UNIT,
    R_NOT,
    R_NUMBER,
    R_PRICE_NUM,
)
from remarkable.predictor.models.base_model import BaseModel
from remarkable.predictor.models.para_match import AsFollowType
from remarkable.predictor.schema_answer import (
    CharResult,
    ParagraphResult,
    PredictorResult,
    TableResult,
)

R_C741_MONEY = rf"{R_MONEY_UNIT}[1-9]|{R_NUMBER}"
P_C741_CONSIDERATION = [
    # http://************:55647/#/hkex/annual-report-checking/report-review/340939?fileId=104142&schemaId=5&rule=C7.4.1&delist=0 index=849
    MatchMulti.compile(
        rf"(approximately|exceed)\s*{R_MONEY_UNIT}[1-9]",
        rf"{R_MONEY_UNIT}{R_PRICE_NUM}\s*(per|each)\b",
        operator=any,
    ),
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6845#note_716874 stock_code=2309
    PositionPattern.compile(r"fee|payment|payable|bonus", rf"{R_MONEY_UNIT}[1-9]"),
    MatchMulti.compile(
        MatchMulti.compile(
            # https://jura6-lir.paodingai.com/#/hkex/annual-report-checking/report-review/260937?fileId=69402&schemaId=5&rule=C7.4.1&delist=0 index=504
            r"contract\s*price",
            r"\b(consideration|total|aggregate|annual\s*cap)\b|\s(income|sum)\s",
            r"principal\s*([a-z]+\s*)?(subscription|amount)",
            r"\b(commission|annual caps?|caps? amounts?|capitalisation shares?|renta?l?|credit line|tenancy|leases?|interest expenses?|((un)?secured|gross) loan)\b",
            r"value of the right-of-use assets",
            r"(monthly|annual|daily)\brent is approximately ",
            r"the amounts? of.*?transactions?.*?which constitutes? (continuing )?connected transactions?",
            r"the (loan|interest income|aggregate sale|principal) (to|from|of).*?amounted to",
            MatchMulti.compile("term loan", "amounts? of", operator=all),
            operator=any,
        ),
        R_C741_MONEY,
        operator=all,
    ),
]
P_C741_TERMS = NeglectPattern.compile(
    match=MatchMulti.compile(
        r"\bterms?\b",
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6408#note_672485
        r"\bpursuant to\b",
        operator=any,
    ),
    unmatch=MatchMulti.compile(
        r"\bcapitalised terms?\b",  # 大写术语
        r"pursuant to chapter \d+[A-Z]",
        operator=any,
    ),
)

# https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6028#note_653007
P_C741_TERMS_ALTERNATIVE = NeglectPattern.compile(
    match=r"\b(enter.*?for|pursuant to|sold.*?to)\b", unmatch=r"pursuant to rule"
)

P_REMOVE = MatchMulti.compile(
    r"entered into.*?agreement with",
    operator=any,
)
P_CURRENCY = MatchMulti.compile(
    rf"{R_MONEY_UNIT}[\d\.,，]+",
    operator=any,
)
P_C7_ADD_SYLLABUS_REGS = MatchMulti.compile(
    r"Framework Agreement$",
    operator=any,
)


def process_para_answer(elements):
    new_elements = []
    for element in elements:
        sub_elements = split_element(element)
        valid_sub_element_result = []
        for text, sub_element in sub_elements.items():
            if len(sub_elements) > 1 and sub_element["element_sequence"] == 0:
                continue
            text = clean_txt(text)
            if P_REMOVE.search(text):
                continue
            if P_CURRENCY.search(text):
                valid_sub_element_result.append(sub_element)
                # print(text)
        if not valid_sub_element_result:
            continue
        if len(sub_elements) == 1 or len(valid_sub_element_result) == len(sub_elements) - 1:  # 减去position=0
            new_elements.append({"element": element, "sub_element": element, "flag": True})
        else:
            fix_sub_element = valid_sub_element_result[0]
            for item in valid_sub_element_result[1:]:
                fix_sub_element["text"] += item["text"]
                fix_sub_element["chars"] += item["chars"]
            new_elements.append({"element": element, "sub_element": fix_sub_element, "flag": False})
    new_answers = []
    for answer in new_elements:
        if answer["flag"]:
            new_answers.append(ParagraphResult(answer["element"], answer["sub_element"]["chars"]))
        else:
            new_answers.append(CharResult(answer["element"], answer["sub_element"]["chars"]))
    return new_answers


def extract_consideration(answers: list[PredictorResult], **kwargs):
    predictor = kwargs.get("predictor")
    if not answers or not predictor:
        return answers
    common_answers = BaseModel.get_common_predictor_results(answers)
    if common_answers[0].answer_value != AnswerValueEnum.PS:
        return answers
    new_answers = []
    # pdfinsight = predictor.pdfinsight
    elements = BaseModel.get_elements_from_answer_result(common_answers)
    para_elements = [element for element in elements if element["class"] != "TABLE"]
    new_answers.extend(process_para_answer(para_elements))

    element_answers = BaseModel.get_common_element_results(answers)
    new_answers.extend(element_answers)
    if not new_answers:
        return answers
    result = PredictorResult(new_answers, value=AnswerValueEnum.PS.value, schema=predictor.schema)
    return [result]


R_CONSIDERATION_KW = r"\b(consideration|revenue|fee|amounts?|commission|interest expenses?|annual caps?)\b"

P_CONSIDERATION_TITLE = NeglectPattern.compile(
    match=MatchMulti.compile(
        R_CONSIDERATION_KW,
        r"\b(charges|deposit|rent)\b",
        # https://jura6-lir.paodingai.com/#/hkex/annual-report-checking/report-review/341372?fileId=104197&schemaId=5&rule=C7.4.1&delist=0
        r"revenue.*?assets",
        operator=any,
    ),
    unmatch=MatchMulti.compile(
        r"period",
        r"Basis for determining the consideration",
        operator=any,
    ),
)

P_SYLLABUS_REGS = [
    r"CONNECTED.*?TRANSACTIONS?",
    r"RELATED.*?PARTY.*?TRANSACTIONS?",
    r"TRANSACTIONS? AMOUNT DURING THE REPORTING PERIOD",
    r"^MATERIAL TRANSACTIONS$",
]

INJECT_SYLLABUS_FEATURES = [
    r"__regex__CONNECTED.*?TRANSACTIONS?",
    # 是否需要忽略note章节的rpt  http://************:55647/#/project/remark/262029?projectId=17&treeId=12525&fileId=69620&schemaId=18
    r"__regex__RELATED.*?PARTY.*?TRANSACTIONS?",
    #
    r"__regex__^TRANSACTIONS? AMOUNT DURING THE REPORTING PERIOD$",
]
R_C74_SKIP_SYLLABUS = [
    *R_C7_SKIP_SYLLABUS,
    r"\sto\s*(a\s*)?(related\s*)?(company|director)$",
    r"\sto\s*related\s*parties$",
    r"summary|overview",
    r"contribution",
    # r"sales to a related company$",
    # r"Consultancy fees to related parties$",
    # r"Loan to a director",
    # r"rental payment to related parties",
]


def post_process_c7_4_ref_for_consideration(answers, **kwargs):
    """
    C7.4.1-consideration后处理：依据C7.1的结果，在其中找相关描述
    """
    if not answers:
        return answers
    common_results = BaseModel.get_common_predictor_results(answers)
    if is_null_nd(common_results):
        # 只有一个空ND答案，直接返回
        return answers
    pdfinsight, model, year_end = kwargs["pdfinsight"], kwargs["model"], kwargs["year_end"]
    element_results = BaseModel.get_common_element_results(common_results)
    c7_group = group_c7_element_results(element_results, pdfinsight, model)
    grouped_elements = c7_group.grouped_elements
    new_element_results = []
    for element_result in element_results:
        if element_result in new_element_results or not is_c71_base_result(element_result):
            continue
        element = element_result.element
        if not is_para_elt(element):
            continue
        index = element["index"]
        elements = grouped_elements.get(index) or [element]
        for pattern in P_C741_CONSIDERATION:
            new_element_results.extend(get_c7_char_results(elements, pattern, pdfinsight, year_end))
    if not new_element_results:
        return []
    return [model.create_result(new_element_results, schema=kwargs.get("schema"), value=AnswerValueEnum.PS.value)]


def post_process_c7_4_ref_for_terms(answers, **kwargs):
    """
    C7.4.1-terms后处理：依据C7.1的结果，取结果所在的整个段落
    """
    if not answers:
        return answers
    common_results = BaseModel.get_common_predictor_results(answers)
    if is_null_nd(common_results):
        # 只有一个空ND答案，直接返回
        return answers
    element_results = []
    existed_indices = set()
    for element_result in BaseModel.get_common_element_results(common_results):
        element = element_result.element
        if element["index"] in existed_indices or not is_c71_base_result(element_result):
            continue
        existed_indices.add(element["index"])
        if is_table_result(element_result):
            # 取整张表
            element_results.append(TableResult(element, parsed_table=element_result.parsed_table))
        elif is_para_elt(element):
            # 取整个段落
            element_results.append(ParagraphResult(element, chars=element["chars"]))
        else:
            element_results.append(element_result)
    return [kwargs["model"].create_result(element_results, schema=kwargs.get("schema"), value=AnswerValueEnum.PS.value)]


predictor_options = [
    {
        "path": ["C7.4.1", "Consideration"],
        "models": [
            # 1. NS: 若C7.1为NS，则认为是NS
            C7_NS_REFERENCE_C71,
            # 2-3. 特殊ND
            *C7_ND_MODELS,
            # 4. C7.1为无答案的ND，则直接ND
            C71_ND_NO_ANSWER_MODEL,
            # 5. PS
            {
                "name": "multi_models",
                "operator": "union",
                "enum": AnswerValueEnum.PS.value,
                "sort_by_elt_index": True,
                "enum_every_element": True,
                "deduplicate_elements": True,
                "post_process_model_answers": c7_post_processor,
                "models": [
                    # 5.1 先找CT下的annual caps整章节
                    c7_syllabus_model([rf"__regex__{R_CHAPTER_PREFIX}annual\s*caps?$", r"__regex__^annual\s*caps?$"]),
                    # 5.2 先找CT及关联的详情章节下段落
                    c7_ct_chapter_ps_model(
                        P_C741_CONSIDERATION, neglect_syllabus_regs=R_C74_SKIP_SYLLABUS, neg_pattern=P_EXEMPT
                    ),
                    # 5.3 指定合同章节下的表格
                    c7_contract_model(
                        {
                            "name": "special_cells",
                            "enum": AnswerValueEnum.PS.value,
                            "syllabus_regs": R_CONTRACTUAL_ARRANGEMENTS,
                            "extend_candidates_syllabus_regs": R_CONTRACTUAL_ARRANGEMENTS,
                            "need_continuous": True,
                            "multi_elements": True,
                            "whole_table": True,
                            "title_patterns": P_CONSIDERATION_TITLE,
                            "any_cell_pattern": [rf"{R_MONEY_UNIT}[1-9]", r"^[\d,]+$"],
                        }
                    ),
                    # 5.3 找CT及及关联的详情章节下表格，按照列取
                    {
                        "name": "multi_models",
                        "operator": "union",
                        "enum": AnswerValueEnum.PS.value,
                        "require_unique_element": True,
                        "sort_by_elt_index": True,
                        "models": [
                            # 5.3.1 找annual caps下的表格，限制表名
                            # http://************:55647/#/hkex/annual-report-checking/report-review/260539?fileId=69322&schemaId=18&rule=C7.4.1&delist=0
                            {
                                "name": "special_cells",
                                "enum": AnswerValueEnum.PS.value,
                                "parent_features": [P_C7_CHAPTER_CT, P_C7_OTHER_CHAPTERS],
                                # "syllabus_regs": r"^annual\s*caps?$",
                                "extend_candidates_syllabus_regs": P_C7_CHAPTER_CT,
                                "extend_by_disclosed_regs": NeglectPattern.compile(match=R_IS_CT, unmatch=R_NOT),
                                "extend_by_disclosed_neg_regs": P_C7_NEG_DISCLOSED,
                                "need_continuous": False,
                                "multi_elements": True,
                                "whole_table": True,
                                "title_patterns": R_CONSIDERATION_KW,
                                "any_cell_pattern": [rf"{R_MONEY_UNIT}[1-9]", r"^[\d,]+$"],
                            },
                            # 5.3.2 找包含annual cap/consideration等列的表格
                            {
                                "name": "special_cells",
                                "enum": AnswerValueEnum.PS.value,
                                "syllabus_regs": P_C7_CHAPTER_CT,
                                "neglect_syllabus_regs": [*R_C74_SKIP_SYLLABUS, r"annual\s*caps?$"],
                                "extend_candidates_syllabus_regs": P_C7_CHAPTER_CT,
                                "extend_by_disclosed_regs": NeglectPattern.compile(match=R_IS_CT, unmatch=R_NOT),
                                "extend_by_disclosed_neg_regs": P_C7_NEG_DISCLOSED,
                                # "extend_candidates_by_enum": True,
                                # "extend_candidates_syllabus_regs": P_SYLLABUS_REGS + R_DR_CHAPTER_TITLES,
                                "need_continuous": False,
                                "multi_elements": True,
                                "whole_table": True,
                                "col_header_pattern": R_CONSIDERATION_KW,
                            },
                            # 5.3.3 找表格中的指定列
                            {
                                "name": "special_cells",
                                "enum": AnswerValueEnum.PS.value,
                                "syllabus_regs": P_C7_CHAPTER_CT,
                                "neglect_syllabus_regs": [*R_C74_SKIP_SYLLABUS, r"annual\s*caps?$"],
                                "extend_candidates_syllabus_regs": P_C7_CHAPTER_CT,
                                "extend_by_disclosed_regs": NeglectPattern.compile(match=R_IS_CT, unmatch=R_NOT),
                                "extend_by_disclosed_neg_regs": P_C7_NEG_DISCLOSED,
                                # "extend_candidates_by_enum": True,
                                # "extend_candidates_syllabus_regs": P_SYLLABUS_REGS + R_DR_CHAPTER_TITLES,
                                "need_continuous": False,
                                "multi_elements": True,
                                "multi": True,
                                "row_col_relation": "or",
                                "row_header_pattern": P_CONSIDERATION_TITLE,
                                "col_header_pattern": P_CONSIDERATION_TITLE,
                                "any_cell_pattern": [r"transaction", r"terms?", r"parties", "date"],
                            },
                        ],
                    },
                    # 5.4 按照CT关键词向后找
                    c7_follow_model(P_C741_CONSIDERATION, neglect_syllabus_regs=R_C74_SKIP_SYLLABUS),
                    # 5.5 按照CT关键词向前找
                    c7_above_model(P_C741_CONSIDERATION, neglect_syllabus_regs=R_C74_SKIP_SYLLABUS),
                    # 5.6 基于C7.1的答案找
                    {
                        "name": "reference",
                        "enum": AnswerValueEnum.PS.value,
                        "from_path": ["C7.1"],
                        "from_answer_value": [AnswerValueEnum.PS.value, AnswerValueEnum.ND.value],
                        # 过滤没有答案的answer
                        "enum_pattern": r".",
                        "post_process_model_answers": post_process_c7_4_ref_for_consideration,
                    },
                ],
            },
        ],
    },
    {
        "path": ["C7.4.1", "Terms"],
        "models": [
            # 1. NS: 若C7.1为NS，则认为是NS
            C7_NS_REFERENCE_C71,
            # 2-3. 特殊ND
            *C7_ND_MODELS,
            # 4. C7.1为无答案的ND，则直接ND
            C71_ND_NO_ANSWER_MODEL,
            # 5. PS
            {
                "name": "multi_models",
                "operator": "union",
                "enum": AnswerValueEnum.PS.value,
                "sort_by_elt_index": True,
                "enum_every_element": True,
                "deduplicate_elements": True,
                "post_process_model_answers": c7_post_processor,
                "models": [
                    # 5.1. 在CT章节下面找pricing policy/terms章节
                    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6408#note_673422
                    # https://jura6-lir.paodingai.com/#/hkex/annual-report-checking/report-review/341314?fileId=104190&schemaId=5&rule=C7.4.1&delist=0
                    # 5.2 找表格或表格中的描述
                    {
                        "name": "multi_models",
                        "operator": "any",
                        "enum": AnswerValueEnum.PS.value,
                        "models": [
                            # 先找terms
                            c7_syllabus_model([r"__regex__\bterms\b"], R_C74_SKIP_SYLLABUS),
                            # 找不到再找pricing policies
                            c7_syllabus_model([r"__regex__Pricing\s*Polic(y|ies)$"], R_C74_SKIP_SYLLABUS),
                        ],
                    },
                    # 5.3 特殊合同章节
                    c7_contract_model(
                        [
                            c7_syllabus_model([r"__regex__\bterms\b"], R_CONTRACTUAL_ARRANGEMENTS, R_C74_SKIP_SYLLABUS),
                            {
                                "name": "para_match",
                                "enum": AnswerValueEnum.PS.value,
                                "syllabus_regs": R_CONTRACTUAL_ARRANGEMENTS,
                                "extend_candidates_syllabus_regs": R_CONTRACTUAL_ARRANGEMENTS,
                                "neglect_syllabus_regs": R_C74_SKIP_SYLLABUS,
                                "as_follow_pattern": PositionPattern.compile(
                                    rf"services|consultation|covenant|warrant|procure|{R_AGREEMENT}", r"[：:]$"
                                ),
                                "as_follow_type": AsFollowType.ANY.value,
                                "multi_elements": True,
                            },
                        ]
                    ),
                    # 5.4 找表格或表格中的描述
                    {
                        "name": "multi_models",
                        "operator": "any",
                        "enum": AnswerValueEnum.PS.value,
                        "enum_every_element": True,
                        "models": [
                            # 4.4.1 找CT及扩展章节下表格的terms列
                            # https://jura6-lir.paodingai.com/#/hkex/annual-report-checking/report-review/260657?fileId=69346&schemaId=5&rule=C7.4.1&delist=0
                            {
                                "name": "special_cells",
                                "model_id": "ps_table_1",
                                "syllabus_regs": P_C7_CHAPTER_CT,
                                "neglect_syllabus_regs": [
                                    *R_C7_SKIP_SYLLABUS,
                                    r"annual\s*caps",
                                    r"reason",
                                    r"consideration",
                                ],
                                "extend_candidates_syllabus_regs": P_C7_CHAPTER_CT,
                                "extend_by_disclosed_regs": NeglectPattern.compile(match=R_IS_CT, unmatch=R_NOT),
                                "extend_by_disclosed_neg_regs": P_C7_NEG_DISCLOSED,
                                "enum": AnswerValueEnum.PS.value,
                                "need_continuous": True,
                                "multi_elements": True,
                                "row_col_relation": "or",
                                "col_header_pattern": [r"terms"],
                                "row_header_pattern": [r"terms[:：]"],
                            },
                            # 4.4.2 找CT及扩展章节下的所有表格
                            # https://jura6-lir.paodingai.com/#/hkex/annual-report-checking/report-review/260657?fileId=69346&schemaId=5&rule=C7.4.1&delist=0
                            {
                                "name": "special_cells",
                                "model_id": "ps_table_1",
                                "syllabus_regs": P_C7_CHAPTER_CT,
                                "neglect_syllabus_regs": [
                                    *R_C7_SKIP_SYLLABUS,
                                    r"annual\s*caps",
                                    r"reason",
                                    r"consideration",
                                ],
                                "neglect_parent_features": P_NOTE_CHAPTER,
                                "extend_candidates_syllabus_regs": P_C7_CHAPTER_CT,
                                "extend_by_disclosed_regs": NeglectPattern.compile(match=R_IS_CT, unmatch=R_NOT),
                                "extend_by_disclosed_neg_regs": P_C7_NEG_DISCLOSED,
                                "enum": AnswerValueEnum.PS.value,
                                "need_continuous": True,
                                "multi_elements": True,
                                # 找所有非豁免表格
                                "any_cell_pattern": [
                                    r"transaction",
                                    r"parties",
                                ],  # [PositionPattern.compile(r"terms?", r"follow|below", r"[:：]$"), R_IS_CT],
                                "neglect_title_patterns": [R_NOT, P_EXEMPT],
                                "whole_table": True,
                            },
                        ],
                    },
                    # 5.5 找其他章节的CT表格
                    # https://jura6-lir.paodingai.com/#/hkex/annual-report-checking/report-review/260657?fileId=69346&schemaId=5&rule=C7.4.1&delist=0
                    {
                        "name": "special_cells",
                        "model_id": "ps_table_2",
                        "syllabus_regs": P_C7_OTHER_CHAPTERS,
                        "neglect_syllabus_regs": R_C7_SKIP_SYLLABUS,
                        "extend_candidates_syllabus_regs": [P_C7_OTHER_CHAPTERS],
                        "extend_by_disclosed_regs": NeglectPattern.compile(match=R_IS_CT, unmatch=R_NOT),
                        "extend_by_disclosed_neg_regs": P_C7_NEG_DISCLOSED,
                        "enum": AnswerValueEnum.PS.value,
                        "need_continuous": True,
                        "multi_elements": True,
                        # 找所有描述构成了CT的表格
                        "title_patterns": R_IS_CT,
                        "any_cell_pattern": [r"transaction", r"parties"],
                        "neglect_title_patterns": [R_NOT, P_EXEMPT],
                        "whole_table": True,
                    },
                    # 5.6 找as follow描述
                    # http://************:55647/#/hkex/annual-report-checking/report-review/261632?fileId=69540&schemaId=18&rule=C7.4.1&delist=0
                    {
                        "name": "para_match",
                        "enum": AnswerValueEnum.PS.value,
                        "syllabus_regs": P_C7_CHAPTER_CT,
                        "extend_candidates_syllabus_regs": P_C7_CHAPTER_CT,
                        "neglect_syllabus_regs": [*R_C74_SKIP_SYLLABUS, R_CONTRACTUAL_ARRANGEMENTS],
                        "as_follow_pattern": PositionPattern.compile(
                            rf"services|consultation|covenant|warrant|procure|{R_AGREEMENT}", r"[：:]$"
                        ),
                        "as_follow_type": AsFollowType.ANY.value,
                        "multi_elements": True,
                    },
                    # 5.7 基于C7.1的答案找
                    {
                        "name": "reference",
                        "enum": AnswerValueEnum.PS.value,
                        "from_path": ["C7.1"],
                        "from_answer_value": [AnswerValueEnum.PS.value, AnswerValueEnum.ND.value],
                        # 过滤没有答案的answer
                        "enum_pattern": r".",
                        "post_process_model_answers": post_process_c7_4_ref_for_terms,
                    },
                    # 5.8 annual cap 描述的金额算是 term 内容
                    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6885#note_715336
                    c7_ct_chapter_ps_model(
                        MatchMulti.compile(
                            r"\bannual\s*cap\b",
                            R_C741_MONEY,
                            operator=all,
                        ),
                        neglect_syllabus_regs=R_C74_SKIP_SYLLABUS,
                        neglect_root_parent_regs=P_NOTE_CHAPTER,
                        neg_pattern=P_EXEMPT,
                    ),
                ],
            },
        ],
    },
]
