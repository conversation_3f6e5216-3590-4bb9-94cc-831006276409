import re
from datetime import datetime

from remarkable.common.common_pattern import R_AS_AT, R_CURRENCY
from remarkable.common.constants import AnswerV<PERSON>ueEnum
from remarkable.common.pattern import (
    MatchMulti,
    NeglectPattern,
    PatternCollection,
    PositionPattern,
)
from remarkable.predictor.common_pattern import R_EN_MONTH
from remarkable.predictor.hkex_predictor.pattern import R_DATES_FOR_EXTRACT
from remarkable.predictor.hkex_predictor.schemas.list_rule_schema.list_rule_b1 import (
    R_UNUSED,
    R_USED,
    R_UTILISE,
)
from remarkable.predictor.hkex_predictor.schemas.list_rule_schema.list_rule_b8 import (
    P_DURING_IN_CELL,
    table_models_for_proceeds,
)
from remarkable.predictor.hkex_predictor.schemas.list_rule_schema.list_rule_b9 import (
    P_TIMELINE,
    R_FULLY_USED,
)
from remarkable.predictor.hkex_predictor.schemas.list_rule_schema.post_processor import BasePostProcessor
from remarkable.predictor.hkex_predictor.schemas.pattern import R_DURING_THE_YEAR, R_MONEY_UNIT, R_NOT
from remarkable.predictor.hkex_predictor.schemas.util import get_sentence_model

P_BROUGHT_FORWARD_COLUMNS = [
    NeglectPattern.compile(
        match=MatchMulti.compile(  # 未使用资金或余额
            # actual net proceeds: https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6243#note_663599
            # usage: http://************:55647/#/project/remark/268383?treeId=18134&fileId=70532&schemaId=18&projectId=18134&schemaKey=C1.1.1
            rf"{R_UNUSED}|actual\s*(use\s*of|net)\s*proceeds",
            rf"\b(as\s*at|as\s*of|up\s*to|\d{{1,2}}\s*{R_EN_MONTH}|{R_EN_MONTH}\s*\d{{1,2}})\b",
            operator=all,
        ),
        unmatch=P_TIMELINE,
    )
]
# 当年内已使用资金
P_USED_DURING_FY = NeglectPattern.compile(
    match=MatchMulti.compile(rf"{R_USED}|\b(use|usage|amount)\b", P_DURING_IN_CELL, operator=all),
    # http://************:55647/#/project/remark/266499?treeId=8859&fileId=70514&schemaId=18&projectId=8859&schemaKey=C1.1.1
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/5609#note_628333
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6245#note_663353
    unmatch=rf"{R_UNUSED}|reallocation|\bas\s*at\b|\bas\s*of\b|\bat\s*the\s*end\b",
)

P_FULLY_USED_WITH_DATE = PatternCollection(
    [
        *[rf"({'|'.join(R_FULLY_USED)})[^（(,，；;]*?{dt}" for dt in R_DATES_FOR_EXTRACT],
        *[rf"(?<!(as at|as of|up to) ){dt}[^）)；;]*?({'|'.join(R_FULLY_USED)})" for dt in R_DATES_FOR_EXTRACT],
        *[
            rf"{r}[^（(,，；;]*?(\sFY|the\s*year\s*|period\s*|financial\s*year\s*)(?P<year>\d{{4}})\b"
            for r in R_FULLY_USED
        ],
        *[rf"(\sFY|the\s*year|period|financial\s*year)\s*(?P<year>20\d{{2}})[^（(,，；;]*?{r}" for r in R_FULLY_USED],
    ],
    flags=re.I,
)
P_FROM_DATE = PatternCollection([rf"\bfrom\s*{dt}" for dt in R_DATES_FOR_EXTRACT], flags=re.I)
P_CELL_DATES = PatternCollection(R_DATES_FOR_EXTRACT, flags=re.I)
P_AT_BEGINNING = MatchMulti.compile(r"beginning", operator=any)
P_COL_MONEY = MatchMulti.compile(rf"^{R_MONEY_UNIT}\s*(million|.000(.000)?)$", operator=any)


def is_ns_para_text(text, last_date, year_end):
    """
    提取NS句子中的日期，如果日期在期初之前，则说明NS正确，否则错误
    https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/5609#note_627184
    """
    text = text.lower()
    if " previous " in text or " prior " in text:
        # http://************:55647/#/project/remark/269142?treeId=2573&fileId=70640&schemaId=18&projectId=2573&schemaKey=C1.2.1
        return True
    if matched := P_FULLY_USED_WITH_DATE.nexts(text):
        _, end_mon, end_day = year_end.split("-")
        group = matched.groupdict()
        if month_en := group.get("mon"):
            month = datetime.strptime(month_en, "%b").strftime("%m")
        else:
            month = end_mon
        if day := group.get("day"):
            day = day if len(day) > 1 else day.zfill(2)
        else:
            day = end_day
        if not f"{matched.group('year')}-{month}-{day}" <= last_date:
            return False
    return True


class C111PostProcessor(BasePostProcessor):
    def filter_brought_forward_cells(self, parsed_cells):
        """
        针对C1.1.1的子项1
        """
        # 1. 找出日期为期初的列，若没有日期为期初的列，丢弃答案
        year_start_col_indices = set()
        col_date_dict = self.get_date_from_col_header(parsed_cells, P_CELL_DATES)
        if self.year_start:
            for col, date_str in col_date_dict.items():
                if self.min_year_start <= date_str <= self.year_start:
                    year_start_col_indices.add(col)
        elif len(col_date_dict) > 1:
            # 取日期最小的那列
            year_start_col_indices = {min(col_date_dict.items(), key=lambda x: x[1])[0]}
        if not year_start_col_indices:
            # beginning表示期初： https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6246#note_663921
            year_start_col_indices = self.get_col_indices_by_pattern(parsed_cells, P_AT_BEGINNING)
        if not year_start_col_indices:
            return []
        # 过滤掉不是期初的列
        self.check_value_col = min(year_start_col_indices)
        return [cell for cell in parsed_cells if cell.colidx in year_start_col_indices]

    def filter_used_during_fy_cells(self, parsed_cells):
        """
        针对C1.1.1的子项2
        """
        # http://************:55647/#/project/remark/268854?treeId=37584&fileId=70600&schemaId=18&projectId=37584&schemaKey=C1.1.1
        # 1. 如果列名是from xx to yy，则判断xx必须为年初，否则丢弃答案
        if col_from_date_dict := self.get_date_from_col_header(parsed_cells, P_FROM_DATE):
            invalid_col_indices = set()
            for col, date_str in col_from_date_dict.items():
                if not self.min_year_start <= date_str <= self.year_start:
                    invalid_col_indices.add(col)
            parsed_cells = [cell for cell in parsed_cells if cell.colidx not in invalid_col_indices]
            if not parsed_cells:
                return []
        # 总共只有两列，第一列为use of proceeds，另一列是金额，则框选整张表
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6840#note_704395
        table = parsed_cells[0].table
        if len({c.colidx for col in table.cols for c in col}) == 2:
            max_col, max_col_header = max(table.col_header_texts.items(), key=lambda x: x[0])
            if P_COL_MONEY.search(max_col_header):
                parsed_cells = [cell for col in table.cols for cell in col]
                self.check_value_col = max_col
                return parsed_cells
        # 2. 取最后一列
        self.check_value_col = max(cell.colidx for cell in parsed_cells)
        return [cell for cell in parsed_cells if cell.colidx == self.check_value_col]

    def filter_ps_table_cells(self, parsed_cells, group_type):
        if self.schema.name == "Proceeds brought forward":
            # C1.1.1子项1的表格处理逻辑
            return self.filter_brought_forward_cells(parsed_cells)
        if self.schema.name == "Used during the year":
            # C1.1.1子项2的表格处理逻辑
            return self.filter_used_during_fy_cells(parsed_cells)

    def is_ns_table(self, parsed_cells):
        return not self.cells_have_value(parsed_cells)

    def is_valid_ns_para(self, text):
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6247#note_663230
        if self.year_end and is_ns_para_text(text, self.year_start, self.year_end):
            return True
        return False

    def is_valid_ps_para(self, text, element):
        # 子项1要求截至期初或者上年末
        if self.schema.name == "Proceeds brought forward":
            return self.is_up_to_fy_start(text)
        # 子项2要求during the year，暂时不限制
        return True


# NS模型： 没有未使用的资金
# http://************:55647/#/project/remark/294533?treeId=37743&fileId=70789&schemaId=18&projectId=37743&schemaKey=C1.1.1
# http://************:55647/#/project/remark/295216?treeId=10707&fileId=70877&schemaId=18&projectId=10707&schemaKey=C1.1.1
NS_NO_UNUSED = get_sentence_model(
    [
        PositionPattern.compile(R_NOT, r"proceeds", rf"brought\s*forward|{R_UNUSED}"),
        PositionPattern.compile(R_NOT, R_UNUSED, r"proceeds"),
        # http://************:55647/#/project/remark/294236?treeId=45609&fileId=70746&schemaId=18&projectId=45609&schemaKey=C1.2.1
        # PositionPattern.compile(r"\b(no|not|nor|neither)\b", R_USED, r"\bany\b", r"proceeds"),
    ],
    skip_pattern=MatchMulti.compile(
        r"((save|except)\s*(for|in|as)|except)\s*disclosed",
        # http://************:55647/#/hkex/annual-report-checking/report-review/265069?fileId=70228&schemaId=18&rule=C1.1.1&delist=0
        rf"[“”\"]{R_UNUSED}\s*net\s*proceeds[“”\"]",
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6840#note_704263
        r"\s*at\s*the\s*date\s*of\s*(this|the)\s*(annual\s*)?report",
        R_CURRENCY,
        operator=any,
    ),
    enum=AnswerValueEnum.NS,
    multi_elements=True,
    threshold=0,
)

predictor_options = [
    {
        # ------------------------------
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/5609
        # -----------------------------
        "path": ["C1.1.1"],
        "fake_leaf": True,
        "models": [
            {
                "name": "use_of_proceeds_group",
                "element_candidate_count": 20,
                "post_process_answers": C111PostProcessor(),
                "column_configs": {
                    # 子项1
                    "Proceeds brought forward": [
                        *table_models_for_proceeds(
                            P_BROUGHT_FORWARD_COLUMNS, enum=AnswerValueEnum.PS, multi_elements=True
                        ),
                        NS_NO_UNUSED,
                        # 一些PS句子
                        get_sentence_model(
                            [
                                # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6247#note_663230
                                MatchMulti.compile(
                                    R_UNUSED, rf"{R_MONEY_UNIT}[1-9]", r"proceeds", R_AS_AT, operator=all
                                ),
                            ],
                            enum=AnswerValueEnum.PS.value,
                            multi_elements=True,
                        ),
                    ],
                    # 子项2
                    "Used during the year": [
                        *table_models_for_proceeds(P_USED_DURING_FY, enum=AnswerValueEnum.PS, multi_elements=True),
                        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6840#note_704600
                        get_sentence_model(
                            [
                                # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6247#note_663230
                                PositionPattern.compile(
                                    R_DURING_THE_YEAR, rf"{R_UTILISE}d", rf"{R_MONEY_UNIT}[1-9]", r"\bnet\s*proceeds"
                                ),
                            ],
                            enum=AnswerValueEnum.PS.value,
                            multi_elements=True,
                        ),
                        NS_NO_UNUSED,
                    ],
                },
            }
        ],
    },
]
