from itertools import chain

from remarkable.common.common import is_para_elt, is_shape_elt
from remarkable.common.common_pattern import P_PERIOD_SEPARATOR, R_CHAPTER_PREFIX, R_MIDDLE_DASH
from remarkable.common.constants import AnswerV<PERSON>ue<PERSON>num, PDFInsightClassEnum
from remarkable.common.pattern import <PERSON><PERSON><PERSON><PERSON>, Neglect<PERSON>attern
from remarkable.common.protocol import Search<PERSON>attern<PERSON>ike
from remarkable.common.util import clean_txt, is_in_range
from remarkable.pdfinsight.reader import <PERSON>d<PERSON><PERSON><PERSON>eader
from remarkable.pdfinsight.reader_util import find_real_syllabus, find_table_title
from remarkable.predictor.default_predictor.utils import is_table_result
from remarkable.predictor.hkex_predictor.models.c7 import P_C7_CHAPTER_CT, P_C7_FOLLOW_PREFIX, P_EXEMPT, R_CT
from remarkable.predictor.hkex_predictor.schemas.c_rule_schema.c_rule_c7 import (
    C7_ND_MODELS,
    C7_NS_REFERENCE_C71,
    C71_ND_NO_ANSWER_MODEL,
    P_CONNECTED_PERSONS,
    R_C7_DISCLOSED_IN_OTHER,
    R_C7_SKIP_SYLLABUS,
    R_CONTRACTUAL_ARRANGEMENTS,
    c7_merge_outlines,
    c7_syllabus_model,
    get_c7_char_results,
    group_c7_element_results,
    is_after_year_end,
    is_c71_base_result,
    is_c71_term_result,
    is_exempt_element,
    is_null_nd,
    need_check_exempt_chapter,
)
from remarkable.predictor.hkex_predictor.schemas.c_rule_schema.c_rule_c7_1 import (
    R_C71_HEADER_TRANSACTIONS,
)
from remarkable.predictor.models.base_model import BaseModel
from remarkable.predictor.schema_answer import (
    ParagraphResult,
    PredictorResult,
    TableCellsResult,
)

R_C72_STRICT_RELATED = [
    NeglectPattern.compile(
        match=r"(?<![)）] )(?<![)）])\bconnected\s*(person|subsidiar)\s*of\b",
        unmatch=r"approved|approval|shareholder['‘’]",
    ),
    r"\s(is|was|be|as)\s*an\s*associate|\bassociates?\s*of\b",
    r"\b(spouse|wife|husband|relative|family\s*members?)\b",
    rf"(wholly|\b(in)?directly){R_MIDDLE_DASH}?\s*owned",
    r"foreign\s*ownership",
    r"\scontrolling\s*(party|shareholder)",
    r"controller|substantial\s*shareholder",
    r"\s(is|are)\s*the\s*(company|group)['‘’]s\s*(shareholder|subsidiar|connected\s*person)",
]
# 交易双方的关系
P_C72_RELATED = MatchMulti.compile(
    *R_C72_STRICT_RELATED,
    NeglectPattern.compile(match=r"\b(?<!any )shareholder\b", unmatch=r"approved|approval|shareholder['‘’]"),
    r"beneficial(ly)?\s*owner",
    r"\b(is|was|are|were|company|which)\s*owned\s*by\s*|[a-z]['‘’]s\s*compan(y|ies)",
    # https://jura6-lir.paodingai.com/#/hkex/annual-report-checking/report-review/340835?fileId=104129&schemaId=5&rule=C7.2&delist=0 index=1477
    r"\bsubsidiar(y|ies)\s*of\s",
    operator=any,
)
P_C72_SKIP = MatchMulti.compile(
    r"[:：]$",
    *R_C7_DISCLOSED_IN_OTHER,
    P_C7_FOLLOW_PREFIX,
    r"detail",
    r"(headed|under)\s*[“”\"]",
    operator=any,
)
P_C72_SKIP_SYLLABUS = MatchMulti.compile(
    *R_C7_SKIP_SYLLABUS, r"\b(price|pricing|annual\s*cap|amount|requirements|polici(y|es))", operator=any
)
# transaction party: https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6593#note_689724
R_C72_HEADERS = r"person|name|landlord|party|parties|relation"
P_C72_PS_HEADERS = MatchMulti.compile(R_C71_HEADER_TRANSACTIONS, R_C72_HEADERS, operator=any)


def find_real_cells(element_result, attr, pattern):
    table = element_result.parsed_cells[0].table
    headers = getattr(table, f"{attr}_header")
    row_or_cols = {
        getattr(cells[0], f"{attr}idx")
        for cells in headers
        if all(c.width == 1 for c in cells)
        and P_C72_PS_HEADERS.search("\t".join(clean_txt(c.text, remove_cn_text=True) for c in cells))
    }
    if (
        indices := {getattr(c, f"{attr}idx") for c in element_result.parsed_cells if pattern.search(c.text)}
        | row_or_cols
    ):
        # 追加指定行/列
        cells = list(
            chain.from_iterable(
                rc
                for rc in getattr(element_result.parsed_cells[0].table, f"{attr}s")
                if getattr(rc[0], f"{attr}idx") in indices
            )
        )
        # 按行取之后，取的都是列名，说明需要按列
        return [] if all(c.is_col_header for c in cells) else cells
    return []


def find_c72_matched_answers(
    answer_results: list[PredictorResult],
    pattern: SearchPatternLike,
    skip_pattern: SearchPatternLike,
    pdfinsight: PdfinsightReader,
    year_end: str,
    check_exempt: bool,
):
    def is_matched(text):
        return pattern.search(text) and not skip_pattern.search(text)

    found_indices, new_elt_results, connected_person_indices = set(), [], set()
    for result in answer_results:
        for element_result in result.element_results:
            elt_idx = element_result.element["index"]
            # shape一般是C7.2的内容，直接取
            if is_shape_elt(element_result.element):
                new_elt_results.append(element_result)
                found_indices.add(elt_idx)
                connected_person_indices.add(elt_idx)
                continue
            if syllabuses := pdfinsight.find_syllabuses_by_index(elt_idx):
                syllabus = find_real_syllabus(pdfinsight, syllabuses[-1])
                titles = [syllabus["title"]]
                if (parent_idx := syllabus.get("parent")) and parent_idx > 0:
                    parent = find_real_syllabus(pdfinsight, pdfinsight.syllabus_reader.syllabus_dict[parent_idx])
                    titles.append(parent["title"])
                # connected person章节的内容直接取
                if any(P_CONNECTED_PERSONS.search(t) for t in titles):
                    new_elt_results.append(element_result)
                    found_indices.add(elt_idx)
                    connected_person_indices.add(elt_idx)
                    continue
            if is_table_result(element_result):
                # https://jura6-lir.paodingai.com/#/hkex/annual-report-checking/report-review/260377?fileId=69290&schemaId=5&rule=C7.2&delist=0
                # if any(P_C72_PS_HEADERS.search(c.text) for c in element_result.parsed_cells if c.is_col_header):
                # 找出结果中的列：任一单元格能匹配关键词
                new_cells = []
                if len(element_result.parsed_table.cols) < 3:
                    # 两列以内，按行取
                    new_cells = find_real_cells(element_result, "row", pattern)
                if not new_cells:
                    new_cells = find_real_cells(element_result, "col", pattern)
                if new_cells:
                    # C7.1/C7.2/C7.5 的列各不相同，这里合并一次
                    element_result = TableCellsResult(
                        element_result.element,
                        sorted(new_cells, key=lambda x: (x.rowidx, x.colidx)),
                    )
                    new_elt_results.append(element_result)
                    found_indices.add(elt_idx)
                if not any(pattern.search(c.text) for c in element_result.parsed_cells):
                    # 表格要提取上方段落，因为表格中可能只有交易方，而没有关联关系
                    # https://jura6-lir.paodingai.com/#/hkex/annual-report-checking/report-review/341441?fileId=104206&schemaId=5&rule=C7.2&delist=0
                    title_element = find_table_title(pdfinsight, elt_idx, return_element=True)
                    if title_element["index"] not in found_indices and is_matched(title_element["text"]):
                        new_elt_results.append(ParagraphResult(title_element, title_element["chars"]))
                        found_indices.add(title_element["index"])
            if element_result.element["class"] == PDFInsightClassEnum.SHAPE.value:
                if elt_idx not in found_indices:
                    new_elt_results.append(element_result)
                    found_indices.add(elt_idx)
                continue
            if is_c71_term_result(element_result):
                continue
            if is_para_elt(element_result.element) and is_matched(element_result.text):
                if is_after_year_end(pdfinsight, element_result.element, year_end, text=element_result.text):
                    continue
                if check_exempt and is_exempt_element(pdfinsight, element_result.element):
                    continue
                new_elt_results.append(element_result)
                found_indices.add(elt_idx)
    return new_elt_results, found_indices, connected_person_indices


def post_process_c7_2_answer(answers, **kwargs):
    """
    C7.2后处理函数
    C7.2基于C7.1的答案基础上做提取，提取逻辑：
    1. 在C7.1的结果中找交易双方及其关联关系
    2. 若找不到，则在邻近段落找
    3. 都找不到，则框选C7.1的答案，判定ND
    # TODO 如果某个章节下只有connected persons元素块，则这个章节不参与判断ND
    # https://jura6-lir.paodingai.com/#/hkex/annual-report-checking/report-review/263877?fileId=69990&schemaId=5&rule=C7.2&delist=0
    注意： C7.1的ND如果有答案，表示存在交易，但是没有交易日期，这种如果披露了交易双方及关联，C7.2为PS
    """
    if not answers:
        return answers
    schema, year_end = kwargs.get("schema"), kwargs.get("year_end")
    model, pdfinsight = kwargs.get("model"), kwargs.get("pdfinsight")
    common_results = BaseModel.get_common_predictor_results(answers)
    if is_null_nd(common_results):
        # 只有一个空ND答案，直接返回
        return answers
    # enum_value = AnswerValueEnum.PS.value
    element_results = BaseModel.get_common_element_results(common_results)
    c7_group = group_c7_element_results(element_results, pdfinsight, model)
    grouped_elements, agreement_syll_ranges = c7_group.grouped_elements, c7_group.agreement_syllabus_ranges
    parent_ranges = c7_group.parent_syllabus_ranges
    check_exempt = need_check_exempt_chapter(pdfinsight)
    new_elt_results, found_indices, connected_person_indices = find_c72_matched_answers(
        common_results, P_C72_RELATED, P_C72_SKIP, pdfinsight, kwargs["year_end"], check_exempt
    )
    found_flags, not_found = dict.fromkeys(agreement_syll_ranges, False), {}
    nd_results = []
    # found_indices存储明确找到关联关系的index
    for element_result in element_results:
        elt_idx = element_result.element["index"]
        if is_c71_term_result(element_result):
            continue
        # 追加C7.1及C7.2提取到的答案
        if elt_idx not in grouped_elements:
            if not is_c71_base_result(element_result) or is_para_elt(element_result.element):
                new_elt_results.append(element_result)
            continue
        if is_after_year_end(pdfinsight, element_result.element, year_end, text=element_result.text):
            continue
        if check_exempt and is_exempt_element(pdfinsight, element_result.element):
            continue
        group_elements = grouped_elements[elt_idx]
        # 直接在组内相邻段落找
        if any(e["index"] in found_indices for e in group_elements):
            found_flags[elt_idx] = True
        # 如果没有交易双方的关联关系，则找邻近段落
        if char_results := get_c7_char_results(
            group_elements, P_C72_RELATED, pdfinsight, year_end, P_C72_SKIP_SYLLABUS, P_C72_SKIP
        ):
            # 句子去重
            found_flags[elt_idx] = True
            char_index = char_results[0].element["index"]
            # 追加C7.1的答案
            # 表格没在found_indices中，说明不是需要的表格
            if not is_table_result(element_result) and char_results[0] != element_result:
                new_elt_results.append(element_result)
            new_elt_results.extend(char_results)
            found_indices.add(char_index)
        elif not is_para_elt(element_result.element):
            continue
        elif elt_idx not in not_found:
            if any(is_in_range(elt_idx, rng) for rng in agreement_syll_ranges.values()):
                # C7.2必须包含C7.1的内容（交易双方）
                new_elt_results.append(element_result)
                not_found[elt_idx] = element_result
            else:
                nd_results.append(element_result)
        else:
            new_elt_results.append(element_result)
    # 如果某个组没有找到关联关系，则判定为ND
    nd_results += [not_found[idx] for idx, v in found_flags.items() if not v and not_found.get(idx)]
    enum_value = AnswerValueEnum.PS.value
    if not new_elt_results:
        # 没有任何交易找到关系，为ND
        enum_value = AnswerValueEnum.ND.value
    elif (
        nd_results
        and not connected_person_indices
        and any(not any(is_in_range(i, rng) for i in found_indices) for rng in parent_ranges.values())
    ):
        # 有ND答案且没有connected persons描述时，C7.1答案所属大章节下没有任何相关描述，判断为ND
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6845#note_704467
        enum_value = AnswerValueEnum.ND.value
    new_elt_results = new_elt_results + nd_results
    if not new_elt_results:
        return []
    return [model.create_result(c7_merge_outlines(pdfinsight, new_elt_results), value=enum_value, schema=schema)]


predictor_options = [
    {
        "path": ["C7.2"],
        "models": [
            # 1. NS: 若C7.1为NS，则认为是NS
            C7_NS_REFERENCE_C71,
            # 2-3. ND: 若C7.1为特殊ND，则认为ND
            *C7_ND_MODELS,
            # 4. C7.1为无答案的ND，则直接ND
            C71_ND_NO_ANSWER_MODEL,
            # 5. PS/ND
            {
                "name": "multi_models",
                "operator": "union",
                "deduplicate_elements": True,
                "sort_by_elt_index": True,
                "enum_every_element": True,
                "post_process_model_answers": post_process_c7_2_answer,
                "models": [
                    # 5.1 如果有总体的connected persons章节，直接取
                    c7_syllabus_model(
                        [rf"__regex__{R_CHAPTER_PREFIX}connected\s*persons?$", r"__regex__^connected\s*persons?$"]
                    ),
                    # 5.2 找CT章节下所有的明显关系描述
                    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6593#note_688626
                    {
                        "name": "para_match",
                        "model_id": "ps_para_1",
                        "enum": AnswerValueEnum.PS.value,
                        "syllabus_regs": P_C7_CHAPTER_CT,
                        "para_separator": P_PERIOD_SEPARATOR,
                        "extend_candidates_syllabus_regs": P_C7_CHAPTER_CT,
                        "neglect_syllabus_regs": P_C72_SKIP_SYLLABUS,
                        "sentence_pattern": R_C72_STRICT_RELATED,
                        "neglect_sentence_pattern": P_C72_SKIP,
                        "neglect_pattern": P_EXEMPT,
                        "skip_syllabus_title": True,
                        "multi_elements": True,
                        "multi": True,
                    },
                    # 5.3 提取表格中的关联方
                    {
                        "name": "special_cells",
                        "multi_elements": True,
                        "enum": AnswerValueEnum.PS.value,
                        "multi": True,
                        "need_continuous": False,
                        "syllabus_regs": P_C7_CHAPTER_CT,
                        "neglect_syllabus_regs": P_C72_SKIP_SYLLABUS,
                        "extend_candidates_syllabus_regs": [P_C7_CHAPTER_CT],
                        "row_col_relation": "or",
                        # http://************:55647/#/project/remark/294526?treeId=23578&fileId=70788&schemaId=18&projectId=17&schemaKey=C7.1
                        "row_header_pattern": MatchMulti.compile(r"(parties|party|relation)", operator=any),
                        "col_header_pattern": MatchMulti.compile(
                            # http://************:55647/#/project/remark/295773?treeId=10817&fileId=70947&schemaId=18&projectId=17&schemaKey=C7.2
                            # http://************:55647/#/project/remark/295383?treeId=11713&fileId=70898&schemaId=18&projectId=17&schemaKey=C7.2
                            R_C72_HEADERS,
                            operator=any,
                        ),
                        "cell_pattern": P_C72_RELATED,
                    },
                    # 5.4 提取shape图中的关联方
                    # https://jura6-lir.paodingai.com/#/hkex/annual-report-checking/report-review/341314?fileId=104190&schemaId=5&rule=C7.2&delist=0
                    {
                        "name": "shapes",
                        "enum": AnswerValueEnum.PS.value,
                        "syllabus_regs": [P_C7_CHAPTER_CT, R_CONTRACTUAL_ARRANGEMENTS],
                        "multi_elements": True,
                        "include_footnotes": True,
                        "extend_candidates_syllabus_regs": [P_C7_CHAPTER_CT, R_CONTRACTUAL_ARRANGEMENTS],
                        "aim_types": {PDFInsightClassEnum.SHAPE.value},
                        "title_patterns": [
                            r"flow\s*of\s*economic\s*b\s*enefits",
                            # https://jura6-lir.paodingai.com/#/hkex/annual-report-checking/report-review/263862?fileId=69987&schemaId=5&rule=C7.1&delist=0 index=531
                            r"illustrate.*?structure",
                        ],
                    },
                    # 5.5 提取connected persons下的相关内容
                    {
                        "name": "para_match",
                        "model_id": "ps_para_2",
                        "enum": AnswerValueEnum.PS.value,
                        "syllabus_regs": [r"connected\s*persons"],
                        "extend_candidates_syllabus_regs": [r"connected\s*persons"],
                        "neglect_syllabus_regs": P_C72_SKIP_SYLLABUS,
                        "paragraph_pattern": P_C72_RELATED,
                        "multi_elements": True,
                    },
                    # 5.6 提取表格列名中有CT的footnote
                    # https://jura6-lir.paodingai.com/#/hkex/annual-report-checking/report-review/340955?fileId=104144&schemaId=5&rule=C7.2&delist=0
                    {
                        "name": "table_footnote",
                        "multi_elements": True,
                        "force_use_all_elements": True,
                        "enum": AnswerValueEnum.PS.value,
                        "multi": True,
                        "cell_regs": R_CT,
                        "footnote_pattern": P_C72_RELATED,
                    },
                    # 5.7 基于C7.1的答案找
                    {
                        "name": "reference",
                        "enum": AnswerValueEnum.PS.value,
                        "from_path": ["C7.1"],
                        "from_answer_value": [AnswerValueEnum.PS.value, AnswerValueEnum.ND.value],
                        # 过滤没有答案的answer
                        "enum_pattern": r".",
                    },
                ],
            },
        ],
    },
]
