"""
Jura4 Annual Report ESG
"""

from remarkable.common.common_pattern import EX<PERSON><PERSON><PERSON>_PATTERN as COMMON_EXPLAIN_PATTERN

EXPLAIN_PATTERN = tuple(COMMON_EXPLAIN_PATTERN)

DEFAULT_MODEL = {
    "name": "score_filter",
    "threshold": 0.1,
}

DIRECTORY_START = r"^\d+\.\d+.(\d+)?"  # 目录中的段落

NEGLECT_PAGE_HEADER_PATTERN = [
    r"^ENVIRONMENT(AL)?.*?SOCIAL.*?AND.*?GOVERNANCE",
    r"^環境.*?社會及管治報告",
    r"^ENVIRONMENT(AL)?.*?SOCIAL.*?AND$",
    r"^GOVERNANCE.*?REPORT$",
    r"^SOCIAL\s*?AND\s*?GOVERNANCE\s*?REPORT$",  # esg换行
    r"^environment(al)?.*?social\s*?and$",  # esg换行
    r"^Report \d+$",
    r"^ENVIRONMENTAL,$",
    r"\(?CONTINUED\)?$",
    r"\(?CONT’D\)?$",
]

B6_CHAPTER_PATTERN = [
    r"__regex__Product\s?Responsibility",
    r"__regex__B6",
    r"__regex__Responsible\s?Services",
]

COMPLY_LAW_PATTERN = (
    r"(no|any|neither) non-compliance",
    r"(no|any|neither) (material|confirmed|recordable) non-compliance",
    r"(no|any|neither) (\w+\s){0,3}non-compliance",
    r"not aware of any .*? non-compliance",
    r"abides",
    r"strictly (comply|complied|complies|complying)",
    # r'ordinance',
    # r'regulation',
    r"has (comply|complied|complies|complying)",
    r"strictly abide",
    r"strictly adheres",
    r"(strict|in) compliance with",
    r"no violation",
    r"not record violation",
    r"not violated",
    r"strictly follows",
    r"(comply|complied|complies|complying) with",
    r"not discover any material violation",
    r"ensuring compliance",
)

LAW_COMMON_PATTERN = (
    r"^(during|there was|the group).*?(no|any|neither) (\w+\s){0,4}non-compliance",
    r"\.\s(during|there was|the group).*?(no|any|neither) (\w+\s){0,4}non-compliance",
)

health_pattern = [
    r"Health and Care",
    r"Health and Safety",
]

predictor_options = [
    {
        # 一般位于ESG report 前面几页，常在board statement或governance  structure小标题下面，
        # 提取涉及“board对整个公司ESG相关的措施或report负责的描述-包括制定，实施，检查，复核，监督，批准，风险评估”等，
        # 主要突出有responsibility或role
        # 常包含关键词
        # oversee/oversight/overall/full/ultimate/solely +
        # responsibility/accountability/leadership/direction/charge of或responsible等等
        # 有时会在多个段落出现相同的描述，可同时提取。
        "path": ["MDR 13 i - board oversight"],
        "models": [
            {
                "name": "kmeans_classification",
                "para_pattern": [
                    r"(oversee|oversight|overall|full|ultimate|solely).*?(responsibility|accountability|leadership|direction|responsible|governance|committee|charge of)",
                    r"(responsibility|accountability|leadership|direction|responsible|governance|committee).*?(integrity)",
                ],
            },
            {
                "name": "para_match",
                "syllabus_regs": [
                    r"board statement",
                    r"governance structure",
                    r"approach|strategy",
                ],
                "paragraph_pattern": (
                    r"(oversee|oversight|overall|full|ultimate|solely).*?(responsibility|accountability|leadership|direction|responsible|governance|committee|charge of)",
                    r"(responsibility|accountability|leadership|direction|responsible|governance|committee).*?(integrity)",
                ),
                "multi_elements": True,
            },
            {
                "name": "para_match",
                "syllabus_regs": [
                    r"Social.*?Governance.*?Report",
                    r"esg",
                ],
                "paragraph_pattern": (
                    r"(oversee|oversight|overall|full|ultimate|solely).*?(responsibility|accountability|leadership|direction|responsible|governance|committee)",
                    r"(responsibility|accountability|leadership|direction|responsible|governance|committee).*?(integrity)",
                ),
                "multi_elements": True,
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__Social.*?Governance.*?Report",
                    r"__regex__esg",
                ],
                "multi_elements": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"(?P<content>(oversee|oversight|overall|full|ultimate|solely).*?("
                        r"responsibility|accountability|leadership|direction|responsible|governance|committee))",
                        r"(?P<content>(responsibility|accountability|leadership|direction|responsible|governance|committee).*?(integrity))",
                    ),
                },
                "table_model": "empty",
            },
            DEFAULT_MODEL,
        ],
    },
    {
        "path": ["MDR 13 ii - board management approach"],
        "models": [
            {
                "name": "kmeans_classification",
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "multi": True,
                "inject_syllabus_features": [
                    r"__regex__ESG Working Group",
                    r"__regex__ESG.*?(approach|strategy)",
                ],
            },
            {
                "name": "para_match",
                "syllabus_regs": [
                    r"board statement",
                    r"governance structure",
                    r"approach|strategy",
                ],
                "paragraph_pattern": (
                    r"(?P<content>ESG Committee|ESG|committee|orking team|working group|establish|set up|delegate|authorise|formulated|strategy|identify|evaluate|priority|approach|strategy)",
                ),
                "multi_elements": True,
            },
            {
                "name": "para_match",
                "syllabus_regs": [
                    r"Social.*?Governance.*?Report",
                    r"esg",
                ],
                "paragraph_pattern": (
                    r"(?P<content>ESG Committee|ESG|committee|orking team|working group|establish|set up|delegate|authorise|formulated|strategy|identify|evaluate|priority|approach|strategy)",
                ),
                "multi_elements": True,
            },
            DEFAULT_MODEL,
        ],
    },
    {
        # 一般位于ESG report 前面几页，常在board statement或governance structure小标题下面，提取涉及到review ESG问题的描述。
        # 优先提取涉及到“ESG相关的target或goal的review”或”progress review”的描述。
        "path": ["MDR 13 iii - board progress review"],
        "models": [
            {
                "name": "kmeans_classification",
                "para_pattern": [
                    r"briefing|meet|frequency|monitor|oversight|review|assess",
                    r"goals",
                    r"overseeing.*?esg",
                ],
            },
            {
                "name": "para_match",
                "multi_elements": True,
                "strict_limit": True,
                "syllabus_regs": [
                    r"board statement",
                    r"governance structure",
                    r"target|goal",
                ],
                "paragraph_pattern": (
                    r"briefing|meet|frequency|monitor|oversight|review|assess",
                    r"overseeing.*?esg",
                ),
            },
            # {
            #     "name": "para_match",
            #     'syllabus_regs': [
            #         r'Social.*?Governance.*?Report',
            #         r'esg',
            #     ],
            #     'paragraph_pattern': (r'(?P<content>report|briefing|meet|frequency|monitor|oversight|review|assess)'),
            #     'multi_elements': True,
            # },
            DEFAULT_MODEL,
        ],
    },
    {
        "path": ["MDR 14 part 1 - materiality application"],
        "pick_answer_strategy": "all",
        "models": [  # 添加参数 NEGLECT_PAGE_HEADER_PATTERN 准确率变低 下一步需要查看badcase
            {
                "name": "row_match",
                "row_pattern": [
                    r"materiality",
                    r"validates the list of material ESG issues",
                ],
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"Materiality.*?Principle.*?esg",
                    r"Materiality:.*?esg",
                    r"Materiality:.*?confirmed by the management",
                ),
            },
            {
                "name": "esg_materiality",
                "paragraph_pattern": (
                    r"Materiality.*?Principle.*?esg",
                    r"Materiality[–:：].*?(esg|materiality matrix)",
                    r"Materiality[–:：].*?confirmed by the management",
                    r"Materiality.*?[-:: ]",
                    r"^•\s?Materiality\s?[–:：]",
                    r"^\d.*?Materiality[–:：].*?(esg|social)",
                ),
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "multi": True,
                "only_inject_features": True,
                "include_title": True,
                "inject_syllabus_features": [
                    r"__regex__STAKEHOLDER ENGAGEMENT AND MATERIALITY ASSESSMENT",
                    r"__regex__MATERIALITY ASSESSMENT",
                    r"__regex__STAKEHOLDER ENGAGEMENT",
                    r"__regex__MATERIAL ANALYSIS",
                    r"__regex__MATERIALITY",
                    r"__regex__MATERIALITY MATRIX",
                    r"__regex__STAKEHOLDERS ENGAGEMENT",
                ],
            },
            {
                "name": "table_title",
                "only_inject_features": True,
                "feature_white_list": [
                    r"materiality assessment",
                ],
            },
            {
                "name": "special_cells",
                "whole_table": True,
                "cell_pattern": (r"Relevant ESG issues to the Group",),
            },
        ],
    },
    {
        # 1. 文档中是表格的时候，是一些特殊情况，将表格模型放在前面
        # 2. 优先匹配 report.*?(principle|standard|framework)---quantitative章节
        # 3. 其次匹配 quantitative: 开头的段落 其实也跟第二点类似，只不过没有quantitative的小标题
        # 4. 在 environment 和 report.*?(principle|standard|framework) 章节下根据关键词匹配
        #    其中 environment下面通常是表格的附注
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/839
        "path": ["MDR 14 part 2 - quantitative application"],
        "models": [
            {
                "name": "row_match",
                "row_pattern": [r"quantitative"],
            },
            {
                "name": "special_cells",
                "cell_pattern": [
                    r"quantitative.*?methods",
                    r"practice the principle of quantitative",
                ],
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__report.*?(principle|standard|framework)__regex__quantitative",
                ],
            },
            {
                "name": "para_match",
                "strict_limit": True,
                "syllabus_regs": [
                    r"report.*?(principle|standard|framework)",
                ],
                "paragraph_pattern": (r"quantitative”?:",),
            },
            {
                "name": "para_match",
                "strict_limit": True,
                "multi_elements": True,
                "syllabus_regs": [
                    r"environment",
                    r"report.*?(principle|standard|framework)",
                ],
                "paragraph_pattern": (
                    r"quantitative|methodology(ies)?|assumptions?|conversion factors?",
                    r"re-calculates|calculated|recalculated|calculation|calculating",
                    r"GHG emissions data.*?based on.*?but not limited to",
                ),
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__MATERIALITY AND REPORTING BOUNDARY",
                ],
                "only_inject_features": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"quantitative|methodology(ies)?|assumptions?|conversion factors?",
                        r"re-calculates|calculated|recalculated|calculation|calculating",
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "esg_quantitative",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__Community investment",  # 获取年报中的汇总表格
                    r"__regex__emission|consumption|Energy",
                ],
                # 'multi_elements': True, # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/997#note_243309
                "multi": True,
                "paragraph_model": "empty",
                "table_model": "special_cells",
                "table_config": {
                    "whole_table": True,
                    "cell_pattern": (
                        r"Energy Consumption",
                        r"Emissions",
                    ),
                },
            },
        ],
    },
    {
        # MDR14-consistency:发行人应在 ESG 报告中披露所用方法或 KPI 的变化，或影响对比的相关因素。
        # 位置：
        #
        # i.一般位置在ESG报告的前几页，通常在reporting principle/standard/framework标题下方，有明确的标题 consistency,
        # 直接框选标题及内容，枚举选择C
        #
        # iii.若有reporting principle/standard/framework时，但无consistency明确标题可通过关键词定位到段落中对应内容时，
        # 另需同时框选情况ii (找各个指标两年的数据列并框选两年数据作为对比) 的内容，枚举选择C
        # ii.若无reporting principle/standard/framework时，可先定位ESG报告中,找各个指标两年的数据列并框选两年数据作为对比,枚举选择C
        # iv.若ESG 报告中无相关描述则枚举选择ND
        "path": ["MDR 14 part 3 - consistency application"],
        "models": [
            {
                "name": "para_match",
                "paragraph_pattern": (r"^.?consistency\s?[:-•]",),
                "syllabus_regs": [
                    r"reporting principle",
                    r"standard",
                ],
            },
            {
                "name": "row_match",
                "row_pattern": [r"consistency"],
            },
            {
                "name": "special_cells",
                "cell_pattern": [r"consistency.*?methods"],
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__report.*?(principle|standard|framework)__regex__consistency",
                ],
            },
            {
                "name": "consistency",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                # "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__reporting principle",
                    r"__regex__About this Report",
                    r"__regex__MATERIALITY",  # MATERIALITY AND REPORTING BOUNDARY
                ],
                "multi_elements": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"consistent methodology",
                        r"used consistent reporting",
                        r"adopt consistent reporting",
                        r"(?P<content>consistency|methodology(ies)?|assumptions?|conversion factors?)",
                        r"(?P<content>aligns with)",
                        r"adopt consistent report",
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"consistent methodology",
                    r"used consistent reporting",
                    r"adopt consistent reporting",
                    r"(?P<content>consistency|methodology(ies)?|assumptions?|conversion factors?)",
                    r"(?P<content>aligns with)",
                ),
                "syllabus_regs": [
                    r"reporting principle",
                    r"standard",
                ],
            },
            {
                "name": "score_filter",
                "multi_elements": True,
                "aim_types": ["TABLE"],
                "threshold": 0.1,
            },
        ],
    },
    {
        # MDR15 reporting boundary：说明ESG 报告的报告界限,并描述用于识别 ESG 报告中包含哪些实体或业务的过程。
        # 范围发生变更的，发行人应当说明差异及变更原因
        # 位置：
        #
        # i.一般位置在ESG报告的前几页，通常在
        # scope and reporting period/reporting scope/reporting boundary/reporting scope and boundaries标题下，
        # 主要描述report包含的xxx operation/business，或在xxx地方的operation,或xxx entities或其包含的xxx子公司，直接框选段落，枚举C;
        #
        # ii.当ESG报告中有包含scope的标题，下方段落除boundary内容外还披露相关的准则或者是ESG报告的报告期则只需要框选boundary相关的内容，
        # 即report包含的xxx operation/business，xxx地方，xxxentitites
        #
        # iii.当无明确标题时或关键词也不明显但是披露了公司及其包含的子公司的主营相关内容也属于此规则，枚举应判断为C
        # 常见关键词：boundary,scope,cover,focus,engaged in
        "path": ["MDR 15 - reporting boundary"],
        "models": [
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"We regularly review the scope of the ESG Report",
                    r"ESG Report includes",
                    r"has two major operating segments",
                ),
            },
            # 优先提取指定章节的整个段落
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__ESG Report Scope and Boundary",
                    r"__regex__Reporting Scope and (Boundary|Boundaries)",
                    r"__regex__SCOPE OF REPORTING",
                    r"__regex__REPORT(ING)? SCOPE$",
                    r"__regex__REPORT(ING)? BOUNDARY$",
                    r"__regex__SCOPE OF THE REPORT",
                    r"__regex__Scope of this ESG Report",
                ],
            },
            {
                "name": "syllabus_based",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__AND REPORTING BOUNDARY",
                    r"__regex__REPORTING BOUNDARIES AND PRINCIPLES",
                    r"__regex__REPORTING SCOPE AND PERIOD",
                    r"__regex__REPORTING PERIOD AND BOUNDARY",
                    r"__regex__ESG Report Scope and Boundary",
                    r"__regex__SCOPE AND PERIOD OF REPORTING",
                    r"__regex__About this Report",
                ],
                "multi_elements": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"the scope of",
                        r"the ESG report covers",
                        r"report contains details.*?social responsibilities",
                        r"report.*?(focus|operations|businesses)",
                        r"focus|engaged in",
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"(?P<content>report.*?(boundary|scope|cover|focus|operations|businesses))",
                    r"(?P<content>report contains details.*?social responsibilities)",
                    r"the scope of",
                    r"the ESG report covers",
                    r"report contains details.*?social responsibilities",
                    r"focus|engaged in",
                ),
                "neglect_pattern": (r"a lasting standstill for the whole of the reporting period",),
            },
            {
                "name": "special_cells",
                "cell_pattern": [
                    # r'report contains details.*?social responsibilities',
                    r"Report.*?encompasses.*?subsidiaries",
                ],
            },
            DEFAULT_MODEL,
        ],
    },
    {
        # todo A1+A2:如果只提第一段或前两段可以符合大部分的情况
        # A3+A4 提取标题下的所有内容。这两个规则不涉及表格数据
        "path": ["A1 policies - emissions"],
        "models": [
            # 特例 待客户确认 https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/997#note_243518
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__^A\.\sENVIRONMENTAL$",
                ],
                "only_inject_features": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"the Group has policies for the reduction.*?use of resources",
                        r"The Group has established environmental policies",
                    ),
                },
                "table_model": "special_cells",
                "table_config": {
                    "whole_table": True,
                    "cell_pattern": (r"Environmental Policies",),
                },
            },
            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/997#note_243524
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__A\.\sENVIRONMENTAL__regex__A1:\sEmissions",
                    # 特例 GRI 相关 https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/997#note_243521
                    r"__regex__Emissions Management",
                ],
            },
            {
                "name": "para_match",
                "paragraph_pattern": (r"KPI A1\.1 to KPI A1\.6 are not applicable",),
            },
            {
                "name": "policy_row_match",
                "multi": True,
                "multi_elements": True,
                "row_pattern": ["^General Disclosure"],
                "anchor_pattern": ["Aspect A1"],
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN
                + [
                    # TODO: "During xxx"的描述到底要不要？
                    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/840#note_217387
                    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/840#note_217613
                    # 'during.*?(time|period|years|days|months|weeks)',
                ],
                "multi": True,
                "skip_child_syllabuses": False,
                "only_inject_features": True,
                "only_before_first_chapter": True,
                "only_first": True,
                "break_para_pattern": [
                    r"Overview of key performance indicators",
                ],
                "inject_syllabus_features": [
                    r"__regex__ENVIRONMENT__regex__Emissions__regex__\b(Air|gas|ghg|green\s?house)\b",
                    r"__regex__ENVIRONMENT__regex__OPERATION__regex__Emission",
                    r"__regex__ENVIRONMENT__regex__Emission",
                    r"__regex__ENVIRONMENT__regex__(Waste Management|Energy Management|Paper and Water Management)",
                ],
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_before_first_chapter": True,  # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/803#note_212766
                "inject_syllabus_features": [
                    r"__regex__ENVIRONMENTAL.*?REPORT__regex__ENVIRONMENTAL__regex__Environmental Policies",
                    r"__regex__General Disclosure and KPIs",
                    r"__regex__A1.*?Emissions",
                    r"__regex__Air.*?Emissions",
                    r"__regex__gas.*?Emission",
                    r"__regex__ghg.*?Emission",
                    r"Emissions",
                ],
            },
            {
                "name": "para_match",
                # "multi_elements": True,
                # "enum_from_multi_element": True,
                "paragraph_pattern": (
                    r"(?P<content>not involve|not engage|not disclosed|not generate|no generation|not produce|not quantify)",
                    r"(?P<content>(?<!not )limit)",
                    r"(?P<content>not own.*?emissions)",
                    r"(?P<content>environment|policy|management|GHG|carbon dioxide|CO2|methane|CH4|nitrous oxide|N2O|ozone|O3)",
                    r"(?P<content>more environment-friendly fuel)",
                    r"carbon footprint|waste handling|air pollutant|air quality",
                ),
            },
            {
                "name": "policy_row_match",
                "multi": True,
                "multi_elements": True,
                "row_pattern": ["^General Disclosure"],
                "anchor_pattern": ["Aspect A1"],
            },
            {
                "name": "kmeans_classification",
            },
            DEFAULT_MODEL,
        ],
    },
    {
        #    1.**优先根据index去定位相关内容的位置去定位****是否遵守**的关键词框选其段落
        #    2.当不存在index的时候，先定位其**是否遵守**的关键词，在根据周围的描述判定属于哪一条规则的law
        #    （如：周围关键词为emission, pollutions, greenhouse gases, GHG等，则是A1law）
        #  涉及“明确表明没有相关政策和法律法规适用于公司”的描述。 比如no laws and regulations。E的情况一般较少出现
        "path": ["A1 law compliance - emissions"],
        # "location_threshold": 0.02,
        "models": [
            {
                "name": "row_match",
                "ignore_index_table": True,
                "row_pattern": [r"^Emissions"],
            },
            {
                "name": "row_match",
                "syllabus_regs": [
                    r"HKEx ESG Guide Content Index",
                ],
                "row_pattern": [
                    r"not aware of any.*?laws and regulations that may have.*?impact on.*?air and GHG",
                ],
            },
            {
                "name": "para_match",
                "neglect_syllabus_regs": [
                    r"Product Health and Safety",
                    r"Labou?r Standards?",
                    r"B[1-8]|A2|A3",
                ],
                "paragraph_pattern": (
                    r"KPI A1\.1 to KPI A1\.6 are not applicable",
                    r"no relevant law",
                ),
            },
            {
                "name": "para_match",
                "neglect_syllabus_regs": [
                    r"Product Health and Safety",
                    r"Labou?r Standards?",
                    r"B[1-8]|A2|A3",
                ],
                "paragraph_pattern": LAW_COMMON_PATTERN,
            },
            {
                "name": "twice_para_match",
                "multi_elements": True,
                "ignore_index_table": True,
                "syllabus_regs": [],
                "neglect_syllabus_regs": [
                    r"Product Health and Safety",
                    r"Labou?r Standards?",
                    r"B[1-8]|A2|A3",
                ],
                "paragraph_pattern": COMPLY_LAW_PATTERN,
                "second_pattern": (
                    r"relevant laws and regulations",
                    r"emission",
                    r"pollution",
                    r"greenhouse gas(es)?",
                    r"GHG",
                    r"Air Pollution Control Ordinance, ",
                ),
            },
            {
                "name": "syllabus_based",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__ENVIRONMENTAL__regex__A1.*?Emissions",
                    r"__regex__Exhaust Management",
                    r"__regex__^Emissions Management",
                ],
                "multi_elements": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"(?P<content>law|ordinance|regulation|legislation)",
                        r"strictly abides by all relevant laws and regulations",
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "para_match",
                "neglect_syllabus_regs": [
                    r"Product Health and Safety",
                    r"Labou?r Standards?",
                    r"B[1-8]|A2|A3",
                ],
                "paragraph_pattern": (
                    # r'(?P<content>law|ordinance|regulation|legislation)',
                    r"the Group did not identify any material non-compliance related to emissions",
                ),
            },
            {
                "name": "policy_row_match",
                "multi": True,
                "multi_elements": True,
                "row_pattern": ["^General Disclosure"],
                "anchor_pattern": ["Aspect A1"],
            },
            DEFAULT_MODEL,
        ],
    },
    {
        #  所在位置：1、优先index或者data summary表格中；
        #           2、其次文档其他位置，根据index给出对的KPI位置去定位框选；
        #           3、有关键词的段落、表格：`emission category`、`A1.1`
        #
        #  判断依据：1、报告中没有相关披露但是有解释，比如`因为没有从事XXX活动导致大气污染物的排放较少`，或者出现关键词`not involve`、
        #           `not engage`、`not disclosed`、`not generate`、`no generation`、`not produce`，上述情况为E；
        #           2、有对应emission类型和数量的算Y；
        #           3、没有披露相应内容也没有解释，算ND
        #
        #  特殊情况：若遇到没有直接明确的表示出有emission类型时，Scope 1, Scope 2及 Scope 3 都算是 “types of emissions” 及
        #           “emissions data”，并且判断为Y
        "path": ["KPI A1.1 - emission type and data"],
        "neglect_table_cell_missing_crude_regs": [r"Energy Consumption"],
        "models": [
            {
                "name": "after_row_match",
                "row_pattern": [r"A1.1"],
                "middle_rows": True,
                "start_regs": [
                    r"Greenhouse Gas Emis",
                    r"GHG emissions",
                ],
                "end_regs": [r"Energy consumption"],
                "just_a_para": True,  # for special cases 61008
                "para_pattern": [
                    r"^溫室氣體排放概覽$",
                ],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__^Energy Conservation$",
                ],
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"Scope 1 carbon emissions refers to all fuels used directly by our companies",
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "syllabus_elt_v2",
                "multi": True,
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "inject_syllabus_features": [
                    # https://hkex.test.paodingai.com/#/hkex/esg-report-checking/report-review/203363?fileId=64701&schemaId=1&rule=KPI%20A1.1%20-%20emission%20type%20and%20data
                    r"__regex__^Pollutants Emissions$",
                    r"__regex__^Greenhouse Gas Emissions$",
                ],
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"KPI A1\.1 to KPI A1\.6 are not applicable",
                    r"not directly produce",
                ),
            },
            {
                "name": "shape_title",
                "regs": (r"Greenhouse gas emissions",),
            },
            {
                "name": "after_row_match",
                "first_cell": False,
                "middle_rows": True,
                "row_pattern": [r"A1.1"],
                "table_title": [
                    r"Gas Emission",
                    r"quantitative information on GHG emissions",
                ],
                "start_regs": [r"Greenhouse Gas Emis"],
                "end_regs": [],
                "direct_answer_pattern": [r"not applicable"],
                "just_a_para": True,  # for special cases 61008
                "para_pattern": [
                    r"^溫室氣體排放概覽$",
                ],
            },
            {
                "name": "row_match",
                "multi": True,
                "ignore_index_table": True,
                "row_pattern": [
                    r"NOx|SOx|Particles|Nitrogen Oxide|Sulphur Oxide",
                    r"Particular|Particulate",  # r'Particular matter', r'PM',
                    r"Air pollutants",
                    r"Air emissions",
                    r"oxides|particles",
                    r"^PM$",  # r'Particular matter', r'PM',
                    r"PM.*?物",  # r'Particular matter', r'PM',
                    r"Sulphur dioxide",  # r'Particular matter', r'PM',
                    r"–\s?(office|projects)",  # r'Particular matter', r'PM',
                ],
            },
            {
                # 汇总表中的某几行 本篇同样没有说emission有什么分类，所以框GHG所在表格即可，并且应该选Y，
                # 而在data summary表格中，只要Greenhouse gas emission这部分表格 60943 60939
                "name": "row_match",
                "ignore_index_table": True,
                "multi": True,
                "row_pattern": [
                    r"GHG|Scope\s?[123]",
                    r"A1.1",
                ],
            },
            {
                "name": "table_title",
                "feature_white_list": [
                    r"GHG Emissions",
                    r"Waste Gas Generation and Management",
                    r"air emission",
                ],
            },
            {
                "name": "para_match",
                # "multi_elements": True,
                "paragraph_pattern": (
                    r"not? (directly )?(involve|engage|disclosed|generate|generation|produce)",
                    r"limited|not quantify",
                    r"A1.1|emission|category|emission",
                ),
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "inject_syllabus_features": [
                    r"A1 Emissions & A2 Use of Resources",
                ],
            },
            DEFAULT_MODEL,
        ],
    },
    {
        "path": ["KPI A1.2 part 1 - Scope 1"],
        "neglect_table_cell_missing_crude_regs": [
            r"Energy Consumption",
            # https://hkex.test.paodingai.com/#/hkex/esg-report-checking/report-review/203403?fileId=64579&schemaId=1&rule=KPI%20A1.2%20part%201%20-%20Scope%201
            r"For the year ended 30 June",
        ],
        "models": [
            {
                "name": "ar_esg_multi_table",
                "title_regs": [
                    r"SUMMARY\s?OF ENVIRONMENTAL PERFORMANCE",
                    r"^7. Significant increase.*?dded cinemas in the reporting scope",
                ],
                "row_regs": [r"Scope 1"],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__^Energy Conservation$",
                ],
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"Scope 1 carbon emissions refers to all fuels used directly by our companies",
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"KPI A1\.1 to KPI A1\.6 are not applicable",
                    r"did not consume resources.*?Scope 1 emission",
                    r"relating to air emissions and scope 1 ",
                ),
            },
            {
                "name": "split_first_table",
                "regs": [
                    r"Scope 1",
                    r"GHG emissions",
                    r"Direct emission",
                    r"Greenhouse gases",
                ],
                "title_regs": [r"TABLE OF KEY PERFORMANCE"],
                "start_regs": [
                    r"Scope 1",
                    r"Direct GHG emission",
                    r"Direct emission",
                    r"Total emission of greenhouse gas",
                ],
                "end_regs": [
                    r"Scope 2",
                    r"A1\.3",
                    r"Indirect GHG emissions",
                    r"hazardous waste",
                    r"indirect emission",
                ],
                "neglect_regs": [
                    r"NOx|SOx|Particles|Nitrogen Oxide|Sulphur Oxide",
                    r"PM direct emission",
                ],
            },
            {
                "name": "row_match",
                "multi": True,
                "ignore_index_table": True,
                "first_cell": False,
                "row_pattern": [
                    r"Scope 1",
                    r"Scope I(?!I)",
                    r"(?<!in)Direct.*?emissions",
                    # r'^GHG emission',
                    # r'Aspects 1.1|Nitrogen Oxides|Respiratory Suspended Particles' # #todo 60877 待修复
                ],
            },
            {
                "name": "first_table",
                "regs": [
                    r"Scope 1",
                ],
            },
            {
                "name": "scope",
                "regs": [r"Scope 1"],
                "start_regs": [
                    r"^Scope 1",
                ],
                "end_regs": [
                    r"^Scope 2",
                ],
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"not involve|not engage|not disclosed|not generate|no generation|not produce|not quantify",
                    r"carbon dioxide|methane|nitrous oxide|hydrofluorocarbons|perfluorocarbons and sulphur hexafluoride",
                    r"A1.2|direct emissions",
                    r"not to compile.*?scope\s?1",
                    # r'(?P<content>direct|reenhouse gas emission)',
                ),
            },
        ],
    },
    {
        "path": ["KPI A1.2 part 2 - Scope 2"],
        "location_threshold": 0.01,
        "neglect_table_cell_missing_crude_regs": [r"Energy Consumption"],
        "models": [
            {
                "name": "ar_esg_multi_table",
                "title_regs": [
                    r"SUMMARY\s?OF ENVIRONMENTAL PERFORMANCE",
                    r"^7. Significant increase.*?dded cinemas in the reporting scope",
                ],
                "row_regs": [r"Scope 2"],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__^Greenhouse Gas Emissions$",
                    r"__regex__^Energy Conservation$",
                ],
                "multi_elements": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"scope (1|2|3)",
                        r"contributed to the emissions of 23.1",
                        r"Scope 1 carbon emissions refers to all fuels used directly by our companies",
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "para_match",
                "paragraph_pattern": (r"KPI A1\.1 to KPI A1\.6 are not applicable"),
            },
            {
                "name": "split_first_table",
                "regs": [
                    r"Scope 2",
                    r"GHG emissions",
                    r"Indirect emissions",
                ],
                "title_regs": [r"TABLE OF KEY PERFORMANCE"],
                "start_regs": [
                    r"Scope 2",
                    r"Indirect GHG emissions",
                    r"Indirect emissions",
                ],
                "end_regs": [
                    r"Scope 3",
                    r"^Total",
                    r"A1\.3",
                ],
            },
            {
                "name": "row_match",
                "ignore_index_table": True,
                "first_cell": False,
                "multi": True,
                "row_pattern": [
                    r"Scope 2",
                    r"Scope II",
                    r"Indirect.*?emissions?",
                    r"^–\s(Hepu Plantation|prc office|hong kong office)",
                ],
            },
            {
                "name": "first_table",
                "regs": [r"Scope 2"],
            },
            {
                "name": "shape_title",
                "regs": (r"The (GHG|greenhouse gas) emission data is set out in the table below",),
            },
            {
                "name": "scope",
                "regs": [r"Scope 2"],
                "start_regs": [
                    r"^Scope 2",
                ],
                "end_regs": [
                    r"^Scope 3",
                ],
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"not involve|not engage|not disclosed|not generate|no generation|not produce|limited|not quantify",
                    r"carbon dioxide|methane|nitrous oxide|hydrofluorocarbons|perfluorocarbons and sulphur hexafluoride",
                    r"scope2",
                    # r'A1.2|scope2|greenhouse gas emission|energy',
                ),
            },
            # DEFAULT_MODEL,
        ],
    },
    {
        "path": ["KPI A1.2 part 3 - Scope 3"],
        "neglect_table_cell_missing_crude_regs": [r"Energy Consumption"],
        "models": [
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"KPI A1\.1 to KPI A1\.6 are not applicable",
                    r"The only source of GHG emission is Scope 2 GHG emissions",  # only Scope 2, 说明没有 Scope 3
                ),
            },
            {
                "name": "scope",
                "regs": [
                    r"Scope 3",
                ],
                "start_regs": [
                    r"^Scope 3",
                ],
                "end_regs": [
                    r"^Scope 3",
                    r"A1\.3",
                ],
            },
            {
                "name": "split_first_table",
                "regs": [
                    r"Scope 3",
                    # r'GHG emissions',
                ],
                "title_regs": [r"TABLE OF KEY PERFORMANCE"],
                "start_regs": [
                    r"Scope 3",
                ],
                "end_regs": [
                    r"total",
                    r"Scope 3",
                    r"A1\.3",
                ],
            },
            {
                "name": "row_match",
                "ignore_index_table": True,
                "first_cell": False,
                "multi": True,
                "row_pattern": [
                    r"Scope 3",
                    # r'Aspects 1.1|Nitrogen Oxides|Respiratory Suspended Particles' # #todo 60877 待修复
                ],
            },
            {
                "name": "first_table",
                "regs": [r"Scope 3"],
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"(?P<content>not involve|not engage|not disclosed|no generation|not produce|not quantify)",
                    r"(?P<content>carbon dioxide|methane|nitrous oxide|hydrofluorocarbons|perfluorocarbons and sulphur hexafluoride)",
                    r"scope3",
                    r"other indirect emissions",
                ),
                "neglect_pattern": (
                    r"scope [12]",
                    DIRECTORY_START,
                ),
            },
            # DEFAULT_MODEL,
        ],
    },
    {
        "path": ["KPI A1.3 - hazardous waste"],
        "models": [
            {
                "name": "ar_esg_multi_table",
                "title_regs": [
                    r"SUMMARY\s?OF ENVIRONMENTAL PERFORMANCE",
                    r"^7. Significant increase.*?dded cinemas in the reporting scope",
                ],
                "start_regs": [r"A1.3"],
                "end_regs": [
                    r"A1.4",
                ],
            },
            {
                "name": "after_row_match",
                "middle_rows": True,
                "row_pattern": [r"A1.3"],
                "start_regs": [r"hazardous wastes?"],
                "end_regs": [
                    # r'hazardous wastes?',
                    r"non-hazardous wastes?",
                ],
                "para_pattern": [
                    r"A1.3|(?<!non-)hazardous wastes?",
                    r"not generate.*?hazardous wastes?",
                ],
                "direct_answer_pattern": [
                    r"not applicable",
                    r"No material.*?hazardous waste",
                ],
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"KPI A1\.1 to KPI A1\.6 are not applicable",
                    r"KPI A1.3 are not applicable",
                    r"A1\.3.*?(not been|is not)\s?disclosed",
                    r"(?<!Non-)Hazardous waste.*?not include",
                    r"not generate.*?hazardous waste",
                    r"^(?!In ).*no (significant)?hazardous wastes? (is|was|are|were|or .* (is|was|are|were)) (generated|produced|recorded)",
                    r"hazardous waste generation was insignificant",
                    r"no significant hazardous wastes? (generated|produced)",
                    r"not produce (significant amounts of|any|any \w+) hazardous (wastes?)?",
                    r"Regarding hazardous wastes?.*not (produce|generated)",
                ),
            },
            {
                "name": "split_first_table",  # 定位第一个表格的
                "regs": [
                    r"A1.3",
                    r"(?<!non-)hazardous waste",
                ],
                "start_regs": [r"(?<!non-)hazardous wastes?"],
                "end_regs": [r"non-hazardous wastes?"],
            },
            {
                "name": "row_match",
                "ignore_index_table": True,
                "row_pattern": [
                    r"(?<!non-)hazardous wastes?",
                ],
            },
            {
                "name": "table_title",
                "feature_black_list": [
                    r"__regex__^.$",
                    r"__regex__^(Environment[al,]*)$",
                    r"__regex__Emissions$",
                    r"__regex__環境",
                ],
                "feature_white_list": [
                    r"(?<!non-)hazardous wastes?",
                    r"有害廢棄物排放",
                ],
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"not involve|not engage|not disclosed|not generate|no generation|not produce|not quantify",
                    r"no hazardous waste is generated",
                    r"not produce any hazardous waste",
                    r"(?<!non-)hazardous waste",
                ),
            },
            {
                "name": "score_filter",
                "threshold": 0.618,
            },
        ],
    },
    {
        # A1.4 产生的无害废物总量（以吨为单位）以及在适当情况下的强度（例如，每单位产量、每项设施）。
        # 整个rule的关键词是non-hazardous waste
        #  所在位置：1、优先index或者data summary表格中；
        #           2、其次文档其他位置，根据index给出对的KPI位置去定位框选；
        #           3、有关键词的段落、表格：`non-hazardous waste`、`A1.4`
        #
        #  判断依据：1、报告中没有相关披露但是有解释，解释内容为“产生的无害废弃物对环境的影响小”或“not available 没有产生无害废弃
        #           物”或“由第三方公司处理相关waste，本公司无数据”，判断为E；
        #           2、有non-hazardous waste对应的数据披露判断为Y；
        #           3、没有披露相关内容也没有解释，算ND
        #
        #  特殊情况：1、当报告中non-hazardous waste明确表述出paper或者其他材料，但是报告中没有non-hazardous相关数据表格或者未E的描述
        #           时，在 emission的相关表格中有关于paper consumption的数据，可以提取再A1.4中，并判断为Y；
        #           2、当没有明确指出hazardous waste和non-hazardous waste的类型，仅讲了waste时，视为non-hazardous waste
        "path": ["KPI A1.4 - non-hazardous waste"],
        "models": [
            {
                "name": "ar_esg_multi_table",
                "title_regs": [
                    r"SUMMARY\s?OF ENVIRONMENTAL PERFORMANCE",
                    r"^7. Significant increase.*?dded cinemas in the reporting scope",
                ],
                "start_regs": [r"A1.4"],
                "end_regs": [
                    r"A2.1",
                ],
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"KPI A1\.1 to KPI A1\.6 are not applicable",
                    r"A1\.4.*?not been disclosed",
                    r"non-hazardous waste generated by our Group ",
                    r"Group generates no hazardous waste",
                    r"the Group will expand its data .*?non-hazardous waste.*?future",
                    r"amount of waste are trivial and no KPI are identified and disclosed",
                    r"non-hazardous.*?wastes.*?negligible",
                    r"expand its data.*?non-hazardous waste.*?future",
                    r"no KPI are identified and disclosed",
                    r"waste are trivial",
                    r"non-hazardous waste produced was [\d\.]+ tonnes",
                ),
            },
            {
                "name": "table_title",
                "only_inject_features": True,
                "feature_white_list": [
                    r"non-hazardous waste (was )?generate",
                    r"generate non-hazardous waste",
                    r"non-hazardous waste production performance",
                ],
            },
            {
                "name": "after_row_match",
                "row_pattern": [r"A\s?1\s?.4"],
                "para_pattern": [
                    r"For non-hazardous.*?handling by qualified agencies",
                ],
                "table_title": [
                    r"non-hazardous",
                ],
                "middle_rows": True,
                "start_regs": [
                    r"non-hazardous",
                ],
                "end_regs": [
                    # r'non-hazardous',
                    r"energy consumption",
                ],
            },
            {
                "name": "middle_rows",
                "regs": [
                    r"A1.4",
                    r"hazardous waste",
                    r"C&D.*?Waste",
                ],
                "start_regs": [
                    r"non-\s?hazardous waste",
                    r"C&D.*?Waste",
                ],
                "end_regs": [
                    r"Use of Resources",
                    r"energy consumption",
                    r"Exhaust Gas Emissions",
                    r"total hazardous waste",
                ],
            },
            {
                "name": "table_title",
                "only_inject_features": True,
                "feature_white_list": [
                    r"non-hazardous waste",
                    r"無害廢棄物表現如下",
                    r"Food Waste Reduction",
                ],
                "feature_black_list": [
                    r"__regex__^.$",
                    r"__regex__^(Environment[al,]*)$",
                ],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__A1.4",
                    r"__regex__non-hazardous waste",
                    r"__regex__^Wastes$",
                    r"__regex__Wastes? management",
                ],
                "only_inject_features": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        EXPLAIN_PATTERN,
                        r"insignificant",
                        r"non-hazardous wastes generated",
                        r"waste collector approved by the Environmental Protection Department",
                        r"no plan to set a goal to reduce non-hazardous waste",
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "para_match",
                "multi_elements": True,
                "enum_from_multi_element": True,
                "paragraph_pattern": (
                    r"(?P<content>non-hazardous waste)",
                    r"(?P<content>A1.4)",
                    r"(?P<content>During.*?the total construction waste disposed.*?respectively)",
                    r"amount of waste are trivial",
                ),
            },
            DEFAULT_MODEL,
        ],
    },
    {
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/811#note_214005
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/808#note_213626
        # add a special model for 1.5
        "path": ["KPI A1.5 - emission target"],
        "models": [
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"KPI A1\.1 to KPI A1\.6 are not applicable",
                    r"A1\.5.*?not been disclosed",
                ),
            },
            {
                "name": "table_title",
                "feature_white_list": [r"steps taken to achieve the target"],
                "only_inject_features": True,
                "first_row_as_title": True,
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                # "multi": True,
                "inject_syllabus_features": [
                    r"__regex__environmental protection__regex__target",
                    r"__regex__environmental policy__regex__Our Environmental Targets",
                    # r'__regex__environmental__regex__target',
                ],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__^A\.\s?environmental",
                    r"__regex__A1.*?Emissions",
                    r"Greenhouse Gas Emission and Energy Efficiency",
                ],
                # 'multi_elements': True,
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "as_follow_pattern": [
                    r"following.*?emission.*?measures.*?:$",
                    r"following.*?measures.*?emission.*?:$",
                ],
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"not set any environmental targets",
                        "(target|aim) is",  # uat 58245
                        "GHG emission and energy use targets",
                        r"following.*?emission.*?measures.*?:$",
                        r"following.*?measures.*?emission.*?:$",
                        r"in addition to the above measures",
                    ),
                    "neglect_pattern": (),
                },
                "table_model": "first_table",
                "table_config": {"regs": [r"base year performance"]},  # uat file 64382
            },
            {
                "name": "row_match",
                "first_cell": False,
                "ignore_index_table": True,
                "force_use_all_elements": True,
                "row_pattern": [
                    r"KPI\s?A1\.5",
                ],
            },
            {
                "name": "kmeans_classification",
                "remedy_low_score_element": True,
                "neglect_remedy_pattern": [
                    r"The table below highlights",
                    r"下表列示",
                ],
            },
            {
                "name": "para_match",
                "multi_elements": True,
                "enum_from_multi_element": True,
                "paragraph_pattern": (
                    r"(?P<content>not involve|not engage|not disclosed|not generate|no generation|not produce|limited|not quantify)",
                    r"(?P<content>immaterial|insignificant|plan|continue)",
                    r"(?P<content>target|aim|future plan|reduce|minimize)",
                    r"(?P<content>not undertake)",
                ),
            },
            DEFAULT_MODEL,
        ],
    },
    {
        "path": ["KPI A1.6 part 1 - waste handling"],
        "models": [
            {
                # for explain
                "name": "para_match",
                "paragraph_pattern": (
                    r"KPI A1\.1 to KPI A1\.6 are not applicable",
                    r"Due to the business nature of our Group, certain construction waste is generated in our construction process",
                    r"A1\.6.*?not been disclosed",
                ),
            },
            {
                # for comply
                "name": "para_match",
                "paragraph_pattern": (r"striving to classify the recyclable waste",),
            },
            {
                "name": "after_row_match",
                "row_pattern": [r"A1.6"],
                "multi_elements": True,
                "para_pattern": [
                    r"(?P<content>not involve|not engage|not disclosed|not generate|no generation|not produce|limited|not quantify)",
                    r"(?P<content>immaterial|insignificant|plan|continue)",
                    r"(?P<content>dispose|recycle|reuse|recover|recycling)",
                    r"(?P<content>managed centrally by the office property management company)",
                    r"(?P<content>not undertake)",
                ],
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__Non-hazardous Waste Management",
                    r"__regex__Waste.*?Management",
                    r"__regex__Non-hazardous wastes",
                    r"__regex__Non-hazardous waste emissions",
                ],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"waste managemen",
                ],
                "multi_elements": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"(?P<content>not involve|not engage|not disclosed|not generate|no generation|not produce|limited|not quantify)",
                        r"(?P<content>immaterial|insignificant|plan|continue)",
                        r"(?P<content>dispose|recycle|reuse|recover|recycling)",
                        r"(?P<content>managed centrally by the office property management company)",
                        r"(?P<content>not undertake)",
                        EXPLAIN_PATTERN,
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"(?P<content>not involve|not engage|not disclosed|not generate|no generation|not produce|limited|not quantify)",
                    r"(?P<content>immaterial|insignificant|plan|continue)",
                    r"(?P<content>dispose|recycle|reuse|recover|recycling)",
                    r"(?P<content>managed centrally by the office property management company)",
                    r"(?P<content>not undertake)",
                ),
            },
            {
                "name": "kmeans_classification",
            },
            DEFAULT_MODEL,
        ],
    },
    {
        # 该规则是框选出公司为排放固体废物而设置的排放目标，或者为了减少产生废物而采取的措施；
        # 如果都没有也可以框选已经实现的减排目标，可以不是对未来目标的设置
        #  所在位置：1. 涉及到waste management的标题下关于waste reduction target的文字段落
        #           2. 在waste management的标题下有关键词waste reduction的小标题下的内容  优先级靠前
        #           3. 如果按照non-hazardous waste和hazardous waste披露的，一般是两处都会涉及到 reduction waste的内容
        #           4. 在ESG报告中单独有个environmental target的标题下，关键词waste的相关内容
        #
        #  提取内容：1. 提取涉及到“waste reduction target”的数据
        #           2. 没数据但有“为了reduce waste 而有一些措施”的描述
        #           3. 当年或目前为止已经实现的reduction，可以不是未来的target
        #           4. 如果上面几点都有，则可以按照上面序号的优先级来全部提取
        #
        #  关键词：（需要在waste相关位置范围内查找）target, aim, future plan, minimize, reduction, reduce, reuse, recycle, prevent
        #
        #  判定方式：Y-披露并描述以上的提取内容（满足提取内容的任何一条或多条即可）
        #           E-明确披露了 not material/not involve/not engage/ not disclosed/ not produce的
        #           E-写明了`没有waste target`的，即使有相关措施，也判定为E
        #           ND-没有任何描述
        "path": ["KPI A1.6 part 2 - waste reduction target"],
        "models": [
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"KPI A1\.1 to KPI A1\.6 are not applicable",
                    r"A1\.6.*?not been disclosed",
                ),
            },
            {
                "name": "para_match",
                "anchor_regs": (r"waste management",),
                "current_regs": (r"to strengthen education and propaganda related to waste reduction",),
            },
            {
                "name": "row_match",
                "row_pattern": [r"^waste (reduction|management)"],
            },
            {
                "name": "para_match",
                "multi_elements": True,
                "paragraph_pattern": (
                    r"the Company does not have a reduction policy that may apply to the disposal of soil",
                    r"—Maintaining.?[(]or reducing[)].*?waste",
                    r"—Recycling.*?waste",
                    r"dedicated to proper management of the non-hazardous waste",
                    r"targets to reduce.*?waste",
                ),
            },
            # only for explain answer
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    # r'__regex__^A\.\s?environmental',
                    r"__regex__green office",
                ],
                # 'multi_elements': True,
                "only_inject_features": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"not set any environmental targets",
                        r"A1.6.*?not applicable",
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__A1.6: Wastes Reduction and Initiatives",
                    r"__regex__A1.6 Handling of Hazardous and Non-hazardous Waste",
                    r"__regex__waste reduction measures",
                    # r'__regex__Non-hazardous Waste Management',
                    r"__regex__Waste Generation and Management",
                    r"__regex__Generation of Waste",
                ],
            },
            {
                "name": "after_row_match",
                "row_pattern": [r"A1.6"],
                "para_pattern": [
                    r"aim.*?(to|at).*?reduc.*?waste",
                    r"be recycled",
                    r"be reused",
                    r"measures.*?handle wastes:",
                    r"measures.*?following:$",
                    r"following measures for reducing waste:$",
                    r"has set a target",
                    r"set.*?reduction target",
                    r"set target to reduce waste",
                    r"minimisation of waste generation",
                    r"reuse of materials",
                    r"recovery and recycling",
                    r"printed when needed",
                    r"reduction initiatives",
                    r"Use recyclable products",
                    r"Recycle office paper",
                    r"Print.*?when necessary|print on both",
                    r"Provide reusable",
                    r"waste reduction measures",
                    r"waste disposed",
                    r"no specific reduction target",
                    r"targets? (is )?to reduce",
                    r"targeted a reduction",
                    r"initiatives to reduce",
                    r"measures.*?reduce.*?waste disposal",
                    r"managed centrally by the office property management company",
                    r"dedicated to proper management of the non-hazardous waste",
                    r"(?P<content>not undertake)",
                ],
                "multi_elements": True,
                "direct_answer_pattern": [
                    r"No.*?target is set",
                    r"not set.*?targets",
                ],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__A1\.6",
                    r"__regex__Waste Reduction",
                    r"__regex__Waste Handling",
                    r"__regex__wastes? managemen",
                    r"__regex__reduction Initiatives",
                    r"__regex__Handling Initiatives",
                    r"__regex__Hazardous waste emissions",
                    r"__regex__EMISSION REDUCTION",
                    r"__regex__^\s.*?wastes$",
                ],
                "multi_elements": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"aim.*?(to|at).*?reduc.*?waste",
                        r"be recycled",
                        r"be reused",
                        r"measures.*?handle wastes:",
                        r"measures.*?following:$",
                        r"following measures for reducing waste:$",
                        r"has set a target",
                        r"set.*?reduction target",
                        r"set target to reduce waste",
                        r"minimisation of waste generation",
                        r"reuse of materials",
                        r"recovery and recycling",
                        r"printed when needed",
                        r"reduction initiatives",
                        r"Use recyclable products",
                        r"Recycle office paper",
                        r"Print.*?when necessary|print on both",
                        r"Provide reusable",
                        r"waste reduction measures",
                        r"waste disposed",
                        r"no specific reduction target",
                        r"targets? (is )?to reduce",
                        r"targeted a reduction",
                        r"initiatives to reduce",
                        r"measures.*?reduce.*?waste disposal",
                        r"managed centrally by the office property management company",
                        r"dedicated to proper management of the non-hazardous waste",
                        r"adopted the following practices to reduce the consumption",
                        r"implementation of the measures.*?reduc",
                        r"implementation of the measures.*?reduc",
                        r"not undertake",
                        r"objective is to.*?reduction",
                        r"adopted.*?measures.*?to reduce",
                        r"to further reduce overall waste generation",
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"aim.*?(to|at).*?reduc.*?waste",
                    r"be recycled",
                    r"be reused",
                    r"measures.*?handle wastes:",
                    r"measures.*?following:$",
                    r"following measures for reducing waste:$",
                    r"has set a target",
                    r"set.*?reduction target",
                    r"set target to reduce waste",
                    r"minimisation of waste generation",
                    r"reuse of materials",
                    r"recovery and recycling",
                    r"printed when needed",
                    r"reduction initiatives",
                    r"Use recyclable products",
                    r"Recycle office paper",
                    r"Print.*?when necessary|print on both",
                    r"Provide reusable",
                    r"waste reduction measures",
                    r"waste disposed",
                    r"no specific reduction target",
                    r"targets? (is )?to reduce",
                    r"targeted a reduction",
                    r"initiatives to reduce",
                    r"measures.*?reduce.*?waste disposal",
                    r"managed centrally by the office property management company",
                    r"dedicated to proper management of the non-hazardous waste",
                    r"control.*?generation.*?non-hazardous waste at source",
                    r"recycle station for further recycle",
                    r"^•.*?reduce usage",
                    r"(?P<content>not involve|not engage|not disclosed|not generate|no generation|not produce|limited|not quantify)",
                    r"(?P<content>immaterial|insignificant|plan|continue)",
                    r"in order to|waste reduction",
                    r"(?P<content>not undertake)",
                ),
            },
            {
                "name": "kmeans_classification",
            },
            DEFAULT_MODEL,
        ],
    },
    {
        # 提取内容：
        # 涉及resources政策的段落，段落中常包含 resource，energy，usage，water consumption等词语。
        # 在有包含use/usage+resources标题时，可提取该标题下的全部内容。在有index的情况下，去index指明的位置提取会更准确
        # 当没有use/usage+resources相关的标题时，可提取energy，water，paper等相关标题下的措施
        # E的判断：
        # 【简单情况】：not disclosed,not available, NA,
        # 该规则E的情况在标注过程中还没有遇到过
        # Y的判断：
        # 有：resources，energy，water，paper等相关的policy或management或measure或reduce或control等措施的描述。
        "path": ["A2 policies - use of resources"],
        "models": [
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                # "only_before_first_chapter": True,
                "only_inject_features": True,
                # "only_first": True,
                "inject_syllabus_features": [
                    r"__regex__Use of Resources",
                    r"__regex__(use|usage).*?resources",
                    r"__regex__Emissions and Use of resources",
                    r"__regex__Use of Resources and Management",
                    r"__regex__utilisation.*?resources",
                    r"__regex__Resources.*?Utilisation",
                ],
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_before_first_chapter": True,
                "multi": True,
                "break_para_pattern": [
                    r"Overview of key performance indicators",
                ],
                "inject_syllabus_features": [
                    r"__regex__(Energy|Water) (Conservation|management|Consumption|resource)",
                    r"__regex__Packaging Materials",
                    r"__regex__(energy usage|consumption|paper|ELECTRICITY)",
                    # r'__regex__resource',
                ],
            },
            {
                "name": "kmeans_classification",
                "filter_content_answer": True,
            },
            {
                "name": "para_match",
                "multi_elements": True,
                "enum_from_multi_element": True,
                "paragraph_pattern": (
                    r"(?P<content>measures|policy|resource|energy|usage|consumption)",
                    r"turn off (lights|air-conditioners)|energy efficiency|energy conservation|switch off|avoid printing",
                    r"LED lighting|effectiveness|reduction|recycle|reuse|save|waste|garbage|electricity|power|solar|digitalise|wastepaper|renewable|new energy",
                ),
            },
            DEFAULT_MODEL,
        ],
    },
    {
        # 直接或间接能源消耗按类型（例如电力、天然气或石油）的总量('000 千瓦时）和强度（例如每单位产量、每个设施）。
        # 一般搜索关键词energy consumption、electricity、gas、oil、indirect、direct
        #  所在位置：1、优先index或者data summary表格中；
        #           2、其次文档其他位置，根据index给出对的KPI位置去定位框选；
        #           3、有关键词的段落、表格：`energy consumption`、`A2.1`
        #
        #  判断依据：1、若报告披露`公司执行了环保政策，能源的消耗对公司来说not a concern”或“not available”或
        #  “公司正在建相关的信息收集系统，下一年会有相关信息披露`，算E；
        #           2、披露了energy的类型及数据的算Y；
        #           3、没有披露相关内容，也没有解释，算ND
        "path": ["KPI A2.1 - energy consumption"],
        "models": [
            {
                "name": "ar_esg_multi_table",
                "title_regs": [
                    r"SUMMARY\s?OF ENVIRONMENTAL PERFORMANCE",
                    r"^7. Significant increase.*?dded cinemas in the reporting scope",
                ],
                "start_regs": [r"A2.1"],
                "end_regs": [
                    r"A2.2",
                ],
            },
            {
                "name": "after_row_match",
                "row_pattern": [r"A\s?2\s?.1"],
                # 'para_pattern': [r'A\s?1\s?.4|hazardous waste'],
                # 'table_title': [
                #     r'non-hazardous',
                # ],
                "middle_rows": True,
                "start_regs": [r"Energy"],
                "end_regs": [r"non-hazardous"],
            },
            {
                "name": "first_table",
                "title_regs": [
                    "本集團的能源消耗表現概要",
                ],
            },
            {
                "name": "split_first_table",
                "regs": [
                    r"Energy|Electricity",
                ],
                "start_regs": [
                    r"energy consumption",
                    r"Energy",
                    r"electricity consumption",
                    r"electricity.*?processing",
                ],
                "end_regs": [
                    r"Water Consumption",
                    r"hazardous waste",
                    r"non-hazardous",
                ],
            },
            {
                "name": "row_match",
                "multi": True,
                "ignore_index_table": True,
                "neglect_row_pattern": [
                    r"Energy indirect emissions",
                ],
                "row_pattern": [
                    r"A2.1",
                    r"Energy|Electricity",
                    r"LPG",
                    r"Petrol",
                ],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    "__regex__KPI A2.1",
                    "__regex__nergy Consumption",
                    "__regex__Use of Energy and Resources",
                ],
                "only_inject_features": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"energy consumption.*?not material",
                        r"electricity consumption amounted",
                        r"total energy consumption.*?was.*?\d",
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "para_match",
                "multi_elements": True,
                "enum_from_multi_element": True,
                "paragraph_pattern": (
                    r"(?P<content>not available|not a concern)",
                    r"(?P<content>oil|petrol|gasoline)",
                    # r'(?P<content>energy|electricity|gas)',
                    r"(?P<content>A2.1|consumption|indirect|direct)",
                    r"(?P<content>During the Year.*?consumed.*?electricity)",
                    r"(?P<content>During the Year.*?consumed.*?gas oil)",
                ),
            },
            DEFAULT_MODEL,
        ],
    },
    {
        # A2.2 总耗水量和强度（例如，每单位产量、每项设施）。
        # 一般搜索关键词A2.2、Water consumption
        #  所在位置：1、优先index或者data summary表格中；
        #           2、其次文档其他位置，根据index给出对的KPI位置去定位框选；
        #           3、有关键词的段落、表格
        #
        #  判断依据：1、若报告披露“水资源的消耗不重大”或者“由物业等第三方公司处理相关water或not applicable”，算E；
        #           2、披露了water的类型及数据的算Y；
        #           3、没有披露相关内容，也没有解释，算ND
        "path": ["KPI A2.2 - water consumption"],
        "models": [
            {
                "name": "ar_esg_multi_table",
                "title_regs": [
                    r"SUMMARY\s?OF ENVIRONMENTAL PERFORMANCE",
                    r"^7. Significant increase.*?dded cinemas in the reporting scope",
                ],
                "start_regs": [r"A2.2"],
                "end_regs": [],
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"water usage data.*?not feasible",
                    r"no individual water consumption data",
                    r"use of water is not significant",
                    # r'Water consumption.*?insignificant',
                    r"not possess information.*?water consumption for disclosure",
                    r"unable to provide.*?water consumption",
                    r"does not have details of the exact amount used",
                    r"due to our business nature.*?not consume.*?water",
                ),
            },
            {
                "name": "after_row_match",
                "row_pattern": [
                    r"A2.2",
                ],
                "para_pattern": [
                    r"no individual water consumption data",
                ],
            },
            {
                "name": "first_table",
                "title_regs": [
                    "本集團的用水表現概要",
                    "water consumption",
                ],
            },
            {
                "name": "split_first_table",
                "regs": [
                    r"A2.2",
                    r"water consumption",
                ],
                "start_regs": [
                    r"A2.2",
                ],
                "end_regs": [
                    r"A2.[345]",
                    r"packaging material",
                ],
            },
            {
                "name": "row_match",
                "ignore_index_table": True,
                "row_pattern": [
                    r"water\s",
                    r"Water Consumption",
                    r"A2.2",
                    r"^water$",
                ],
                "multi": True,
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    "__regex__KPI A2.2",
                    "__regex__Wat.*?er Consumption",
                ],
                "only_inject_features": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"water consumption.*?not available",
                        r"water consumption.*?approximately",
                        r"Water usage in the Group.*?is minimal",
                        r"Water consumption.*?tonnes",
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "para_match",
                "multi_elements": True,
                "enum_from_multi_element": True,
                "paragraph_pattern": (
                    # r'not applicable|no significant',
                    # r'no material',
                    r"(?P<content>A2.2|Water consumption|water consumed)",
                ),
                "neglect_pattern": (r"To reduce the water consumption",),
            },
        ],
    },
    {
        # A2涉及到的规则都是关于资源使用效率相关的内容；A2.3需要框选出公司为实现节能而设置的节能目标，或者为了节能而采取的措施；
        # 如果都没有也可以框选已经实现的节能目标，可以不是对未来目标的设置。
        #  所在位置：1. 涉及到use of resources的标题下关于energy efficiency的文字段落
        #           2. 在use of resources的标题下有关键词energy management的小标题下的内容
        #           3. 如果按照energy的种类披露的，一般会涉及到electricity，fuel，natural gas的reduction/saving/conservation内容
        #           4. 在ESG报告中单独有个environmental target的标题下，关键词energy的相关内容
        #
        #  提取内容：1. 提取涉及到“energy efficiency target”的数据
        #           2. 没数据但有“为了energy conservation 而有一些measures”的描述
        #           3. 当年或目前为止已经实现的energy efficiency target，可以不是未来的target
        #           4. 如果上面几点都有，则可以按照上面序号的优先级来全部提取
        #
        #  关键词：（需要在energy相关位置范围内查找）target, efficiency, establish, goal, aim, future plan,
        #  energy conservation, reduction, prevent
        #
        #  判定方式：Y-披露并描述以上的提取内容（满足提取内容的任何一条或多条即可）
        #           E-明确披露了 not material/not involve/not engage/ not disclosed/ not produce的
        #           E-写明了`没有establish energy efficiency target`的，即使有相关措施，也判定为E
        #           ND-没有任何描述
        "path": ["KPI A2.3 - energy efficiency targets"],
        "models": [
            {
                "name": "row_match",
                "ignore_index_table": True,
                "row_pattern": [
                    r"Energy Conservation",
                    r"A2.3",
                ],
                # 'multi': True,
            },
            {
                "name": "after_row_match",
                "row_pattern": [r"A2.3"],
                # 'para_pattern': [r'water source'],
            },
            {
                "name": "syllabus_elt_v2",
                "only_inject_features": True,
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "inject_syllabus_features": [
                    r"__regex__energy conservation",
                    r"__regex__Energy Use Efficiency Initiatives",
                    r"__regex__sustainability target",
                    r"__regex__Energy Conservation.*?KPI A2\.3",
                    r"__regex__Electricity and Water Management",
                ],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__Energy Consumption",  # uat 58433
                    r"__regex__Energy usage",
                    r"__regex__use of resources?",  # uat 58426
                    r"__regex__Use of Natural Resources?",
                    r"__regex__Electricity",  # 60875
                    r"__regex__Fuel",  # 60875
                    r"__regex__A2\.3.*?energy",
                ],
                # 'multi': True,
                "only_inject_features": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"group targets? to reduce",
                        r"Group closely monitors its energy usage to consumption",
                        r"our aim is",
                        r"energy conservation",
                        r"set targets to",
                        r"targets? (is )?to reduce",
                        r"targeted a reduction",
                        r"has set a target",
                        r"set.*?reduction target",
                        r"set target to reduce waste",
                        r"no specific reduction target",
                        r"target",
                        r"employing various initiatives and measures",
                        r"implemented.*?as follows:",
                        r"to improve our energy efficiency",
                        r"plan is to reduce energy",
                        r"in order to.*?energy efficiency",
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "syllabus_elt_v2",
                # "only_first": True,  # id  60856 取的是整个章节
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "inject_syllabus_features": [
                    r"__regex__A2\.3.*?energy",
                    r"__regex__energy conservation",
                    r"Energy",  # 63571
                    r"__regex__KPI A2\.3.*?energy",
                    "__regex__nergy Consumption",
                    r"__regex__USE OF RESOURCES",  # 60879
                    r"__regex__Energy Management",
                    r"__regex__Electricity and Energy Usage",
                ],
            },
            {
                "name": "para_match",
                "multi_elements": True,
                "enum_from_multi_element": True,
                "paragraph_pattern": (
                    r"(?P<content>not set|not establish)",
                    r"(?P<content>target|goal|reduce|measure|establish|set|energy|efficiency)",
                ),
            },
            # DEFAULT_MODEL,
        ],
    },
    {
        # 该条规则需要框选公司遇到的取水问题，取水的水源。
        # Y的表述
        #  報告中有“明确表示没有遇到取水方面的问题” 或者水源取自“municipal water，ground water，或recycled water”或者
        # 有關“取水遇到的問題”的披露，
        #
        #  Y的常见表述：not encounter, not deal with, not face, no issue, source,municipal water,
        #  ground water, water sourcing, recycle，water supply by xxx
        # E的表述：
        #  报告中有解释水源與公司、業務不相關，沒有重大影響，
        #
        #  E的常见表述： irrelevant, non-material, no significant impact, not applicable, N/A
        "path": ["KPI A2.4 part 1 - water sourcing"],
        "models": [
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"water conservation target",
                    r"sources water from.*?no problem",
                    r"water consumption.*?insignificant",
                    r"no sourcing issue",
                    r"sources water from the municipal supplies",
                ),
            },
            {
                "name": "after_row_match",
                "row_pattern": [r"A2.4"],
                "para_pattern": [r"water source"],
                "direct_answer_pattern": [
                    r"No.*?target is set",
                    r"not set.*?targets",
                ],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": ["__regex__KPI A2.4"],
                "only_inject_features": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (r"water consumption.*?not available",),
                },
                "table_model": "empty",
            },
            {
                "name": "para_match",
                # "multi_elements": True,
                # "enum_from_multi_element": True,
                "paragraph_pattern": (
                    r"(?P<content>not encounter|not deal with|not face|no issue|sourcing water)",
                    r"(?P<content>municipal water|ground water|sourcing water|water sourcing)",
                    r"(?P<content>not have problems.*?water resources)",
                ),
            },
            {
                "name": "kmeans_classification",
                "para_pattern": [
                    r"water source|sources water|sourced water|sourcing suitable water",
                    r"Only water suppl",
                ],
            },
            # DEFAULT_MODEL,
        ],
    },
    {
        # A2涉及到的规则都是关于资源使用效率相关的内容；A2.4-part2需要框选出公司为节水而设置的节水目标，或者为了节水而采取的措施；
        # 如果都没有也可以框选已经实现的节水目标，可以不是对未来目标的设置。
        #  所在位置：1. 涉及到use of resources的标题下关于water efficiency的文字段落
        #           2. 在use of resources的标题下有关键词water management/ water usage的小标题下的内容
        #           3. 在ESG报告中单独有个environmental target的标题下，关键词water的相关内容
        #
        #  提取内容：1. 提取涉及到“water efficiency/saving target”的数据
        #           2. 没数据但有“为了water conservation/saving 而有一些measures”的描述
        #           3. 当年或目前为止已经实现的water efficiency target，可以不是未来的target
        #           4. 如果上面几点都有，则可以按照上面序号的优先级来全部提取
        #
        #  关键词：（需要在water相关位置范围内查找）target ,conserve water, water-efficient, water scarcity issue, establish,
        #  goal, aim, future plan,water conservation, reduction, prevent, decreased, avoid
        #
        #  判定方式：Y-披露并描述以上的提取内容（满足提取内容的任何一条或多条即可）
        #           E-明确披露了 not material/not involve/not engage/ not disclosed/ not produce的
        #           E-写明了`没有establish water efficiency target`的，即使有相关措施，也判定为E
        #           ND-没有任何描述
        "path": ["KPI A2.4 part 2 - water efficiency targets"],
        "models": [
            {
                "name": "row_match",
                "ignore_index_table": True,
                "row_pattern": [
                    r"water Conservation",
                    r"conserving water resource",
                ],
            },
            {
                "name": "after_row_match",
                "row_pattern": [r"A2.4"],
                # 'para_pattern': [r'water source'],
            },
            {
                "name": "para_match",
                "anchor_regs": (r"water.*?target",),
                "paragraph_pattern": (r"To reduce the water",),
            },
            {
                "name": "syllabus_elt_v2",
                "only_inject_features": True,
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "inject_syllabus_features": [
                    r"__regex__sustainability target",
                    r"__regex__Water Use Efficiency Initiatives",
                    r"__regex__Environmental Targets",
                    r"__regex__use of resource__regex__(water management|water usage)",
                ],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__Water Consumption$",
                    r"__regex__SAFEGUARDING THE ENVIRONMENT",
                    r"__regex__water.*?targets",
                    r"__regex__KPI A2.4",
                    r"__regex__Water management$",
                    r"__regex__sustainability target",
                    r"__regex__use of resource__regex__(^water|water$)",
                    r"__regex__environmental__regex__(^water|water$)",
                    r"__regex__Use of Resources",
                    r"__regex__EMISSION",
                ],
                "only_inject_features": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"water consumption.*?not available",
                        r"Waste management.*?not been disclosed",
                        r"set a reduction target to reduce the water",
                        r"in order to reduce.*?water",
                        r"(reuse|recycling).*?water",
                        r"(saving|save|conserving) water",
                        r"reducing.*?waste.*?water",
                        r"water efficiency",
                        r"water Conservation",
                        r"target is.*?water",
                        r"plan is to reduce.*?water",
                        r"^•.*?water",
                        r"cannot control what water saving measures",
                        r"return water is discharge",
                        r"have established a water-saving supervision",
                        r"reduce unnecessary water consumption",
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "para_match",
                "multi_elements": True,
                "enum_from_multi_element": True,
                "paragraph_pattern": (
                    r"(?P<content>not encounter|not deal with|not face|no issue|sourcing water)",
                    r"(?P<content>municipal water|ground water|sourcing water|water sourcing)",
                    r"(?P<content>water conservation)",
                    r"(?P<content>conserving clean water)",
                    r"(?P<content>saving water|save water)",
                    r"(?P<content>data on water consumption is not available)",
                    # r'(?P<content>target|source)',
                    r"(?P<content>saving.*?methods)",
                    r"(?P<content>saving.*?instruments)",
                    r"reducing.*?waste.*?water",
                    r"reduce unnecessary water consumption",
                ),
            },
            {
                "name": "kmeans_classification",
                "threshold": 0.618,
            },
            # DEFAULT_MODEL,
        ],
    },
    {
        "path": ["KPI A2.5 - packaging material"],
        "models": [
            {
                "name": "middle_paras",
                "use_syllabus_model": True,
                "include_top_anchor": False,
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "inject_syllabus_features": [
                    r"__regex__pack(?:ag)?ing material",
                ],
                "top_anchor_regs": [
                    r"pack(?:ag)?ing material.*as (below|follow(ing)?)",
                ],
                "bottom_anchor_regs": [
                    r"Notes?.*?pack(?:ag)?ing material",
                ],
            },
            {
                "name": "after_row_match",
                "row_pattern": [r"A2.5"],
                "index_header_patterns": [
                    r"\bKPI\b",
                ],
                "dst_row_patterns": [r"packaging material", r"packing material"],
                # 拆分索引单元格内容
                "split_cell_pattern": r"－|—|:|：|\d+\.(?:\d+)?",
            },
            {
                "name": "first_table",
                "regs": [
                    r"packaging material",
                    r"packing material",
                ],
                "title_regs": [
                    r"packaging material",
                    r"packing material",
                ],
            },
            {
                "name": "split_first_table",
                "second_table": True,
                "regs": [
                    r"packaging material",
                    r"packing material",
                ],
                "start_regs": [
                    r"packaging material",
                    r"packing material",
                ],
                "end_regs": [],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    "__regex__KPI A2.5",
                    "__regex__Packaging",
                ],
                "only_inject_features": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"pack(?:ag)?.*?not material",
                        r"total pack(?:ag)?ing material used",
                        r"The Group.*?setting up comprehensive pack(?:ag)?ing materials.*?system",
                        r"not involve using packaging materials",
                        r"KPI A2.5 are not applicable",
                        r"no.*?significant",
                        r"Due the Group’s business",
                    ),
                },
                "table_model": "first_table",
                "table_config": {"regs": [r"pack(?:ag)?ing material"], "title_regs": [r"pack(?:ag)?ing material"]},
            },
            {
                "name": "para_match",
                "multi_elements": True,
                "enum_from_multi_element": True,
                "paragraph_pattern": (
                    # 'not involve|not produce|not applicable|non-material|not disclosed',
                    r"pack(?:ag)?ing material",
                    r"none?-material",
                ),
            },
            {
                "name": "row_match",
                "row_pattern": [
                    r"KPI\sA2\.5",
                    r"packaging material",
                ],
            },
            # DEFAULT_MODEL,
        ],
    },
    {
        # A3&A3.1 这两个规则的内容接近较难区分，A3侧重于总结性的政策，A3.1侧重于实际行动。
        # 在实际提取中描述涉及的关键词类似较难分开，可以在这两处提取相同的内容，这两规则的提取和判断规则是一致的。
        # 提取内容：
        # 【涉及natural resources，environment 政策的段落】
        # 在有包含“natural resources+environment”标题时，
        #       可提取该标题下的全部内容及environment相关标题下reduce+impact或impact+environment的段落。
        # 在有index的情况下，去index指明的位置提取会更准确
        # 当没有“natural resources+environment”相关的标题时，
        #        提取A1POLICY+A2POLICY的内容,及environment相关标题下reduce+impact或impact+environment的段落
        # E的判断：
        # 【简单情况】：not disclosed,not available, NA,
        # 【复杂情况】：not+impact，little significance+impact， not involve, not produce等 ，且没有任何政策措施相关的描述
        # Y的判断：
        # 有：natural resources，environment等相关的policy或management或measure或reduce或control等措施的描述。
        # 【要注意】：当有“not+impact，little significance+impact， not involve, not produce”等的描述，
        # 但是同时又有政策措施相关的描述，仍判断Y而非E
        "path": ["A3 policies - environment and natural resources"],
        "models": [
            # 优先提取
            {
                "name": "a3_policy",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__natur(e|al) resources?.*?environment",
                    r"__regex__environment.*?natur(e|al) resources?",
                ],
                "include_title": True,
            },
            {
                "name": "policy_row_match",
                "multi": True,
                "multi_elements": True,
                "row_pattern": ["^General Disclosure"],
                "anchor_pattern": ["Aspect A3"],
            },
            {
                "name": "after_row_match",
                "row_pattern": [r"A3"],
                "multi_elements": True,
                "para_pattern": [
                    r"(minimise|minimize).*?impacts",
                    r"environmental protection",
                    r"environmental sustainability",
                    r"minimise|(minimize|lower|minimising).*?(impacts|effect)",
                    r"(?P<content>not own.*?emissions)",
                    r"(?P<content>not cause any significant impact on)",
                ],
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "multi": True,
                "only_before_first_chapter": True,
                "inject_syllabus_features": [
                    r"__regex__Sustainable Construction",
                    r"__regex__natural resources+environment",
                    r"__regex__ENVIRONMENTAL.*?REPORT__regex__ENVIRONMENTAL__regex__Environmental Policies",
                    r"__regex__A[.]?3",
                    r"__regex__Environment.*?Resources",
                    r"__regex__use of resources",
                    r"__regex__Ecological Protection in Operating Regions",
                    r"__regex__environment management",
                    r"__regex__Energy Conservation and Consumption Reduction",
                ],
            },
            {
                "name": "para_match",
                # NOTE: need_missing_crude_answer=False, 非常特例的情况，应该不需要补充初步定位答案
                "syllabus_regs": [
                    r"Environment|Resources",
                ],
                "anchor_regs": (r"^Sustainable\s*?Construction$",),  # 09998|2022
                "include_anchor": True,
                "paragraph_pattern": (r"(?P<content>implemented\s*?environment(al)?\s*?polic(ies|y))",),  # 09998|2022
            },
            {
                "name": "para_match",
                "syllabus_regs": [
                    r"Environment|Resources",
                ],
                "paragraph_pattern": (
                    r"(?P<content>not direct impact|not disclosed|of little significance|not involve|not produce|not applicable|non-material|not disclosed)",
                    r"(?P<content>not own.*?emissions)",
                    r"(?P<content>not cause any significant impact on)",
                    r"(?P<content>minimise|(minimize|lower|minimising).*?(impacts|effect)|environmental protection|environmental sustainability)",
                ),
            },
            DEFAULT_MODEL,
        ],
    },
    {
        "path": ["KPI A3.1 - impact on environment and natural resources"],
        "models": [
            # 优先提取
            {
                "name": "a3_policy",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__natural resources.*?environment",
                    r"__regex__environment.*?natural resources",
                ],
            },
            {
                "name": "policy_row_match",
                "multi": True,
                "multi_elements": True,
                "row_pattern": ["^General Disclosure"],
                "anchor_pattern": ["Aspect A3"],
            },
            {
                "name": "after_row_match",
                "row_pattern": [r"A3"],
                "multi_elements": True,
                "para_pattern": [
                    r"(minimise|minimize).*?impacts",
                    r"environmental protection",
                    r"environmental sustainability",
                    r"minimise|(minimize|lower|minimising).*?(impacts|effect)",
                    r"(?P<content>not own.*?emissions)",
                    r"(?P<content>not cause any significant impact on)",
                ],
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "multi": True,
                "only_before_first_chapter": True,
                "inject_syllabus_features": [
                    r"__regex__natural resources+environment",
                    r"__regex__ENVIRONMENTAL.*?REPORT__regex__ENVIRONMENTAL__regex__Environmental Policies",
                    r"__regex__A[.]?3",
                    r"__regex__Environment.*?Resources",
                    r"__regex__use of resources",
                    r"__regex__Ecological Protection in Operating Regions",
                    r"__regex__environment management",
                    r"__regex__Energy Conservation and Consumption Reduction",
                    r"__regex__KPI A3.1",
                ],
            },
            {
                "name": "para_match",
                "syllabus_regs": [
                    r"Environment|Resources",
                ],
                "paragraph_pattern": (
                    r"(?P<content>not direct impact|not disclosed|of little significance|not involve|not produce|not applicable|non-material|not disclosed)",
                    r"(?P<content>not own.*?emissions)",
                    r"(?P<content>not cause any significant impact on)",
                    r"(?P<content>minimise|(minimize|lower|minimising).*?(impacts|effect)|environmental protection|environmental sustainability)",
                ),
            },
            DEFAULT_MODEL,
        ],
    },
    {
        "path": ["A4 policies - climate-related issues"],
        "models": [
            {
                "name": "para_match",
                "paragraph_pattern": (r"formulated working mechanisms.*?climate change issues",),
            },
            {
                "name": "shape_title",
                "regs": (r"Transition Risks",),
            },
            {
                "name": "policy_row_match",
                "multi": True,
                "multi_elements": True,
                "row_pattern": ["^General Disclosure"],
                "anchor_pattern": ["Aspect A4"],
            },
            {
                "name": "syllabus_elt_v2",
                "multi": True,
                "include_title": True,
                "include_shape": True,
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN
                + [
                    r"B.*?SOCIAL$",
                ],
                "inject_syllabus_features": [
                    r"__regex__KPI A4(.1)?",
                    r"__regex__climate change",
                    r"__regex__The Group.s response",
                    r"__regex__Climate-related Risks, Opportunities, and Financial impact",
                    r"__regex__Physical risks",
                    r"__regex__Transition(?:al)? risks",
                    r"__regex__respond to climate change",
                    r"__regex__Tackling Climate Change",
                ],
                "neglect_patterns": [
                    r"Employment",
                ],
                "break_para_pattern": [r"^[\u4e00-\u9fa5]{1,}"],
            },
            {
                "name": "para_match",
                "multi_elements": True,
                "enum_from_multi_element": True,
                "paragraph_pattern": (
                    r"(?P<content>not direct impact|not disclosed|of little significance|not involve|not produce|not applicable|non-material|not disclosed)",
                    r"(?P<content>identified|recognising|climate change|respond|climate|measure)",
                    r"there are policies",
                ),
            },
            # DEFAULT_MODEL,
        ],
    },
    {
        "path": ["KPI A4.1 - climate-related issues & impact"],
        "models": [
            {
                "name": "para_match",
                "paragraph_pattern": (r"formulated working mechanisms.*?climate change issues",),
            },
            {
                "name": "shape_title",
                "regs": (r"Transition Risks",),
            },
            {
                "name": "policy_row_match",
                "multi": True,
                "multi_elements": True,
                "row_pattern": ["^General Disclosure"],
                "anchor_pattern": ["Aspect A4"],
            },
            {
                "name": "syllabus_elt_v2",
                "multi": True,
                "include_title": True,
                "include_shape": True,
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN
                + [
                    r"B.*?SOCIAL$",
                ],
                "inject_syllabus_features": [
                    r"__regex__KPI A4.1",
                    r"__regex__climate change",
                    r"__regex__Climate-related Risks, Opportunities, and Financial impact",
                    r"__regex__Action on Climate Change",
                    r"__regex__The Group.s response",
                    r"__regex__Physical risks",
                    r"__regex__Transition(?:al)? risks",
                    r"__regex__respond to climate change",
                    r"__regex__Tackling Climate Change",
                ],
                "neglect_patterns": [
                    r"Employment",
                ],
                "break_para_pattern": [r"^[\u4e00-\u9fa5]{1,}"],
            },
            {
                "name": "para_match",
                "multi_elements": True,
                "enum_from_multi_element": True,
                "paragraph_pattern": (
                    r"(?P<content>not direct impact|not disclosed|of little significance|not involve|not produce|not applicable|non-material|not disclosed)",
                    r"(?P<content>identified|recognising|climate change|respond|climate|measure)",
                    r"no significant climate-related issues",
                ),
            },
            # DEFAULT_MODEL,
        ],
    },
    {
        "path": ["B1 policies - employment"],
        # todo B1-3 policies 是标题下除了表格之外的内容全要 意思是不管表格出现在什么位置 表格之后 表格之前的段落都要 对吧
        # 如果能把“表格之后 表格之前的段落”中涉及到law，也就是B1-LAW，B2-LAW相关的内容剔除掉就更好了
        # B4,B5,B6,B7，B8如果只提第一段或前两段可以符合大部分的情况吧
        # 16.B1 polices 可以框选employment 标题下除数据、图表、child的所有段落
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/811#note_214095
        "models": [
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN
                + [
                    r"^Our Workforce by",
                    r"^Number of training hours by Gender",
                    r"^Human Capital Targets$",
                ],
                "only_inject_features": True,
                "break_para_pattern": [
                    r"Overview of key performance indicators",
                ],
                "inject_syllabus_features": [
                    r"__regex__OUR PEOPLE",
                ],
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_before_first_chapter": True,  # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/803#note_212861
                "inject_syllabus_features": [
                    # r'__regex__B1:', # todo id uat 58767
                    r"__regex__B[.]1",
                    r"__regex__Employment__regex__General Disclosure and KPIs",
                    r"__regex__\d.*?employment$",
                    r"__regex__Employment and Labor Practices",
                    r"__regex__Protection of Employees.*?Rights",
                ],
            },
            {
                "name": "policy_row_match",
                "multi": True,
                "multi_elements": True,
                "row_pattern": ["^General Disclosure"],
                "anchor_pattern": ["Aspect B1"],
            },
            {"name": "kmeans_classification"},
        ],
    },
    {
        "path": ["B1 law compliance - employment"],
        "location_threshold": 0.01,
        "models": [
            {
                "name": "para_match",
                "paragraph_pattern": LAW_COMMON_PATTERN,
                "neglect_syllabus_regs": [
                    r"Labou?r Standards?",
                    r"B[2-8]|A[1-3]",
                ],
            },
            {
                "name": "syllabus_based",
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__B1\.\semployment",
                    r"__regex__^employment$",
                ],
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"^(during|there was|the group).*?(no|any|neither) (\w+\s){0,4}non-compliance",
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "twice_para_match",
                "multi_elements": True,
                "syllabus_regs": [],
                "neglect_syllabus_regs": [
                    r"Labou?r Standards?",
                    r"B[2-8]|A[1-3]",
                ],
                "paragraph_pattern": COMPLY_LAW_PATTERN,
                "neglect_pattern": (
                    r"^[a-zA-Z]\.",
                    r"^•",
                ),
                "second_pattern": (
                    r"relevant laws and regulations",
                    r"employment",
                    r"labour",
                    r"workforce",
                    r"compensation",
                    r"dismissal",
                    r"recruitment",
                    r"promotion",
                    r"working hours",
                    r"rest periods",
                    r"equal opportunity",
                    r"diversity",
                    r"anti-discrimination",
                    r"other benefits",
                    r"other welfare",
                ),
            },
            {
                "name": "para_match",
                "neglect_syllabus_regs": [
                    r"Product Responsibility",
                    r"Labou?r Standards?",
                    r"B[2-8]|A[1-3]",
                ],
                "paragraph_pattern": (
                    r"no material non-compliance with|are compliant with",
                    r"complie[ds].*?(law|regulation|ordinance)",
                    r"abides.*?(law|regulation|ordinance)",
                    r"compliance.*?(law|regulation|ordinance)",
                    r"conforms.*?(law|regulation|ordinance)",
                    r"follow.*?(law|regulation|ordinance)",
                    r"comply(ing)? with.*?(law|regulation|ordinance)",
                    r"not violate.*?(law|regulation|ordinance)",
                    r"not contravened any relevant legislations or regulations",
                    r"no material non-compliance.*?employment",
                    r"(complie[ds]|abide|compliance).*?(law|regulation|ordinance)",
                ),
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__Compliance Management",
                    r"__regex__Human Capital",
                    r"__regex__Employment Practices",
                    r"__regex__employment",
                ],
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"no material non-compliance with",
                        r"are compliant with",
                        r"no known non-compliance with",
                    ),
                },
                "table_model": "row_match",
                "table_config": {
                    "row_pattern": [r"Employment"],
                },
            },
            {
                "name": "kmeans_classification",
                "threshold": 0.1,
                "para_pattern": [
                    r"(comply|not violate|abide).*?employment.*?(law|regulation)"
                    r"no material non-compliance with|are compliant with",
                    r"complied with all relevant laws",
                    r"complies with the relevant laws and regulations.*?employment",
                    r"(?P<content>non-compliance|employment|ordinance|legal|law|regulation)",
                ],
            },
            # DEFAULT_MODEL,
        ],
    },
    {
        #  B1.1 规则是披露员工人数情况，按性别、雇佣类型（例如，全职或兼职）、年龄组和地理区域划分的劳动力总数。
        #  一般都是按照各类员工的数量及比例披露的。
        #  关键词是by gender, employment type、number of、age 、geographical、resign、resigned、resignation
        #  所在位置：1、优先去index/summary/key performance indicators中找寻；
        #           2、根据index给出的KPI位置去定位；
        #           3、在Employment/ Employment Practices/Human Capital标题下。
        #
        #  判断标准：1、如果报告中说没有按员工类型总数来披露，并且解释了原因，那么选E，但是不常见；
        #           2、找到对应图表、段落或者句子，就算Y；
        #           3、如果报告中没有对此有任何描述，那么就是ND。
        "path": ["KPI B1.1 - workforce by types"],
        "models": [
            {
                "name": "after_row_match",
                "row_pattern": [
                    r"B1.1",
                    r"Workforce",
                ],
                "para_pattern": [
                    r"B1.1|by gender|employment type|number of|age|geographical|resign|resigned|resignation"
                ],
            },
            {
                "name": "shape_title",
                "multi_elements": True,
                "regs": (
                    r"Employee.*?distribution",
                    r"Employees by.*",
                    r"Age distribution",
                    r"Age group",
                    r"^gender$",
                    r"(Employment|Employee) Category",
                    r"本集團員工團隊人數統計資料概述如下",
                    r"僱員明細載列如下",
                    r"僱員分佈詳情如下",
                    r"僱員總數",
                    r"Employees’ breakdown by employment type",
                ),
                "neglect_regs": (r"turnover",),
            },
            {
                "name": "middle_rows",
                "second_table": True,
                "regs": [
                    r"workforce distribution",
                    r"workforce as",
                    r"by type",
                    r"male",
                    r"by.*?(gender|age|location)",
                    r"B1.1",
                ],
                "start_regs": [
                    r"Employee breakdown",
                    r"number of employees",
                    r"workforce",
                ],
                "end_regs": [
                    r"Employee turnover rate",
                    r"Development and training",
                ],
            },
            {
                "name": "table_title",
                "multi_elements": True,
                "feature_white_list": [
                    r"^Diversity$",
                    r"Summary of Employment Performance Indicators",
                    r"Employee.*?distribution",
                    r"Age distribution",
                    r"Age group",
                    r"gender",
                    r"Employment Category",
                    r"本集團員工團隊人數統計資料概述如下",
                    r"僱員分佈詳情如下",
                    r"僱員總數",
                ],
            },
            {
                "name": "row_match",
                "ignore_index_table": True,
                "multi_elements": True,
                "row_pattern": [
                    r"distribution of employees",
                ],
            },
            {
                "name": "elements_collector_based",
                "elements_collect_model": "syllabus_elt_v2",
                "elements_collect_config": {
                    "inject_syllabus_features": [r"__regex__B[.]1"],
                    "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                    "only_inject_features": True,
                },
                "table_model": "shape_title",
                "table_config": {
                    "regs": [
                        r"The total workforce and breakdown are listed as follows:",
                    ],
                    "ignore_regs_before_shape": [r"total|总数|總數"],
                },
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [],
                "multi_elements": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"B1.1|by gender|employment type|number of|geographical|resign|resigned|resignation",
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"(?P<content>B1.1|by gender|employment type|number of|age|geographical|resign|resigned|resignation)",
                ),
            },
            DEFAULT_MODEL,
        ],
    },
    {
        # B1.2 此条规则是用于披露员工流动率，一般是按照性别、年龄和地理区域划分的员工来描述流动率。
        # 常见关键词为B1.2,turnover, by gender, age , geographical, employment type，leave，leaver
        #  所在位置：1、优先在index或者data summary的地方找对应的图表、表格及段落；
        #           2、根据index给出KPI的位置去定位，一般在标题Employment/ Employment Practices/Human Capital下
        #
        #  位置特殊情况：有的报告会把B1.1和B1.2内容位置相近，如果能分开标注最好，如果不能就合并标注。
        #
        #  判断依据：1、报告中披露涉及发行人不披露流动率的原因（比如数据涉及商业敏感commercially sensitive）等描述，为E；
        #           2、报告中披露了员工turnover，并且是按照各类别披露的数据，为Y；
        #           3、报告没有披露相关内容，也没有进行解释，为ND
        #
        #  判断特殊情况：1、若报告中仅有overall employees的turnover rate，没有披露按照gender, age group and geographical region
        #               分类下的turnover rate，B1.2属于Y；
        #               2、B1.2 只披露的离职人数，没有turnover，属于Y
        "path": ["KPI B1.2 - employee turnover by types"],
        "models": [
            {
                "name": "after_row_match",
                "row_pattern": [r"B1.2"],
                "para_pattern": [r"B1.2|turnover|by gender|by age|geographical|employment type|leave|leaver"],
                "middle_rows": True,
                "start_regs": [r"Turnover Rate"],
                "end_regs": [r"Training"],
                "table_title": [
                    r"本集團僱員流失率概述如下",
                    r"員工流失率",
                    r"turnover rate",
                    r"employee Structure and Turnover",
                ],
            },
            {
                "name": "table_title",
                "multi_elements": True,
                "only_inject_features": True,
                "feature_white_list": [
                    r"本集團僱員流失率概述如下",
                    r"員工流失率",
                    r"turnover rate",
                    r"employee Structure and Turnover",
                ],
            },
            {
                "name": "shape_title",
                "multi_elements": True,
                "regs": (
                    r"Employee Turnover",
                    r"Geographical Region",
                    r"turnover rate",
                ),
            },
            {
                "name": "middle_rows",
                "ignore_index_table": False,
                "regs": [r"turnover"],
                "title_regs": [r"turnover"],
                "start_regs": [r"turnover rate", "Employee turnover"],
                "end_regs": [
                    r"Percentage of employees trained",
                    r"training",
                    r"B2",
                ],
            },
            {
                "name": "table_title",
                "only_inject_features": True,
                "feature_white_list": [
                    r"turnover",
                ],
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"(?P<content>not disclosed|sensitive)",
                    r"full-time employee turnover rate",
                    r"employee turnover rate for male and female",
                    r"average turnover rate by",
                    r"commercially sensitive",
                    r"low employee turnover rate",
                ),
            },
            # DEFAULT_MODEL,
        ],
    },
    {
        # B2涉及到的规则都是关于health and safety相关的内容，policy一般都为公司制定了安全管理政策的相关概括性描述；
        #  所在位置：1. 涉及到health and safety的大标题下（一般位置比较固定，都在这个章节下定位）
        #           2. 如果大标题分了小标题的情况，policy一般可以提取大标题和小标题中间的文段
        #           3. 小标题有policy/policies的关键词的，可以直接提取对应小标题下的内容
        #
        #  提取内容：1. 常见描述“集团重视员工健康和安全，以保护员工免受职业危害，按照适用的法律法规制定了安全管理政策。”等
        #           2. 如果大标题下有小标题，可以提取大标题和小标题中间的文段
        #           3. 对于篇幅较小的或没有按照小标题披露的，可以提取标题下全部内容
        #
        #  关键词：health, healthy and safe, working environment, established, safety management strategy,
        #  reduce potential workplace hazards, hazard-free working environment
        #
        #  判定方式：Y-披露并描述以上的提取内容（满足提取内容的任何一条或多条即可）
        #           E- 不常见，但可能会有些金融行业或者与员工劳动伤害很远的行业，在此标题下会有 not material之类的描述
        #           ND-没有任何相关描述
        "path": ["B2 policies - health and safety"],
        "models": [
            {
                # GRI 特例 https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/997#note_243522
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__Occupational Health and Safety",
                ],
            },
            {
                # 章节标题和第一个小标题之间
                "name": "policy",
                "only_inject_features": True,
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "ignore_pattern_match": True,
                "inject_syllabus_features": [
                    r"__regex__(?<!student )Health and Safety",
                ],
                "neglect_features": [
                    r"Product and Service Health and Safety",
                ],
            },
            {
                "name": "policy_row_match",
                "multi": True,
                "multi_elements": True,
                "row_pattern": ["^General Disclosure"],
                "anchor_pattern": ["Aspect B2"],
            },
            {
                "name": "after_row_match",
                "row_pattern": [r"B2"],
                "just_a_para": True,
                "para_pattern": [
                    r"protect.*?health",
                    r"(?P<content>health|Safety|workplace|fire)",
                ],
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_before_first_chapter": True,
                "inject_syllabus_features": [
                    r"__regex__Health and Safety__regex__General Disclosure and KPI",
                ],
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_before_first_chapter": True,
                "inject_syllabus_features": [
                    r"__regex__Health and Safety",
                    r"__regex__Health and Safety__regex__General Disclosure and KPI",
                ],
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"(?P<content>health|Safety|workplace|fire)",
                    r"(?P<content>protect.*?health)",
                ),
            },
            DEFAULT_MODEL,
        ],
    },
    {
        "path": ["B2 law compliance - health and safety"],
        "models": [
            {
                "name": "para_match",
                "paragraph_pattern": LAW_COMMON_PATTERN,
                "neglect_syllabus_regs": [
                    r"Product Health and Safety",
                    r"Labou?r Standards?",
                    r"B1|B[3-8]|A[1-3]",
                ],
            },
            {
                "name": "twice_para_match",
                "multi_elements": True,
                "strict_limit": True,
                "syllabus_regs": [
                    r"Health and Safety",
                    r"Employee Health and Care",
                    r"COVID-19",
                    r"CARING FOR EMPLOYEES",  # 特殊文档 文档解析错误
                ],
                "neglect_syllabus_regs": [
                    r"Product Health and Safety",
                    r"Labou?r Standards?",
                    r"B1|B[3-8]|A[1-3]",
                ],
                "paragraph_pattern": COMPLY_LAW_PATTERN,
                "neglect_pattern": (
                    r"^[a-zA-Z]\.",
                    r"^•",
                ),
                "second_pattern": (
                    r"relevant laws and regulations",
                    r"health",
                    r"Safety",
                    r"workplace",
                    r"safe working environment",
                    r"protecting employees",
                ),
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__Compliance Management",
                ],
                "paragraph_model": "empty",
                "table_model": "row_match",
                "table_config": {
                    "row_pattern": [r"health and safety"],
                },
            },
            {
                "name": "para_match",
                "multi_elements": True,
                "enum_from_multi_element": True,
                "neglect_syllabus_regs": [
                    r"Product Health and Safety",
                    r"Labou?r Standards?",
                    r"B1|B[3-8]|A[1-3]",
                ],
                "paragraph_pattern": (r"(?P<content>non-compliance|ordinance|law|regulation)",),
                "neglect_pattern": (r"Information on:",),
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_before_first_chapter": True,
                "only_first": True,
                "inject_syllabus_features": [
                    r"Health and Safety",
                ],
            },
            {
                "name": "kmeans_classification",
            },
            DEFAULT_MODEL,
        ],
    },
    {
        # 此条规则是披露报告年度在内的过去三年中每年发生的与工作有关的死亡人数和发生率。
        # 常见关键词为work related, fatalities, fatal accidents, occupational，death，B2.1
        #  所在位置：1、优先在index或者data summary的地方找对应的图表、表格及段落；
        #           2、根据index给出KPI的位置去定位，一般在标题Health and Safety下；
        #           3、如果没有index和data summary，在报告中既有段落的，又有表格的，优先表格部分
        #
        #  判断依据：1、报告中没有相关数据但是有解释，为E（此情况非常不常见）；
        #           2、报告中披露了三年内每年发生的fatality，无论数据是nil或no或not，都是Y；
        #           3、报告没有披露相关内容，也没有进行解释，为ND
        #
        #  判断特殊情况：1、若无上述关键词，仅出现了no work safety-related incident或no work safety-related accident或无work
        #               injury等描述，B2.1判断为Y；
        #               2、如果报告中只披露了两年的死亡人数及发生率也是可以的；
        #               3、没有work safety-related accident可视为无work injury lost day ，判断为Y
        "path": ["KPI B2.1 - work-related fatalities"],
        "models": [
            {
                "name": "after_row_match",
                "row_pattern": [r"B2.1|B-2.1"],
                "just_a_para": True,
                "include_row": True,
                "filter_content_answer": True,
                "direct_answer_pattern": [
                    r"No work-related fatality was recorded",
                ],
                "para_pattern": [
                    r"work[\-\s]related fatalit",
                    r"did not have any work-related fatalities",
                    r"not have any violation.*?work-related",
                    r"fatalities|fatal accidents|death|B2.1",
                    r"(?P<content>no work safety-related incident|no work safety-related accident|no work injury)",
                ],
                "middle_rows": True,
                "table_title": [
                    r"職業健康",
                    r"安全表現",
                ],
                "start_regs": [r"B2\.1"],
                "end_regs": [r"B2\.2"],
            },
            {
                "name": "row_match",
                "ignore_index_table": True,
                "multi": True,
                "row_pattern": [
                    r"work-related",
                    r"B2\.1",
                ],
            },
            {
                "name": "first_table",
                "regs": [
                    r"work.*?related",
                    r"Number.*?(injuries|fatalit)",
                    r"Fatalities rate",
                ],
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"no fatality cases related to our employees occurred",
                    r"did not have any work-related fatalities",
                    r"protect.*?health",
                    r"not.*?violation",
                    r"incidents of workplace accidents",
                    r"(?P<content>work related|fatalities|fatal accidents|occupational|death|B2.1)",
                    r"(?P<content>no work safety-related incident|no work safety-related accident|no work injury)",
                    r"(?P<content>work-related fatalit)",
                ),
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [],
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "multi_elements": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"(?P<content>work related|fatalities|fatal accidents|occupational|death|B2.1)",
                        r"(?P<content>no work safety-related incident|no work safety-related accident|no work injury)",
                    ),
                },
                "table_model": "empty",
            },
            # DEFAULT_MODEL,
        ],
    },
    {
        # B2.2 此条规则是披露员工因工伤损失的天数。
        # 常见的关键词有work related，lost days, sickness, work injury, accidents，occupational，B2.2
        #  所在位置：1、优先在index或者data summary的地方找对应的图表、表格及段落；
        #           2、根据index给出KPI的位置去定位，一般在标题Health and Safety下
        #           3、如果没有index和data summary，在报告中既有段落的，又有表格的，优先表格部分
        #
        #  判断依据：1、报告中没有相关数据但是有解释，为E（此情况非常不常见）；
        #           2、报告中有work injury天数或占比的披露，无论数据是nil或no或not，都是Y；
        #           3、报告没有披露相关内容，也没有进行解释，为ND
        #
        #  判断特殊情况：1、若没有披露injury，仅说没有规范heathy和safety相关的法律规定，B2.2为ND。
        "path": ["KPI B2.2 - work injury lost days"],
        "neglect_table_title_missing_crude_regs": [
            r"Employee work-related fatalities over the past three years",
        ],
        "models": [
            {
                "name": "table_title",
                "only_inject_features": True,
                "feature_black_list": [
                    r"__regex__^.$",
                    r"__regex__^(Environment[al,]*)$",
                    r"__regex__ENVIRONMENTAL, SOCIAL AND GOVERNANCE REPORT",
                ],
                "feature_white_list": [
                    r"工傷及損失天數統計",
                    r"職業健康",
                    r"安全表現",
                ],
            },
            {
                "name": "after_row_match",
                "row_pattern": [r"B2.2"],
                "just_a_para": True,
                "include_row": True,
                "para_pattern": [
                    r"injuries",
                    r"(?P<content>work[\-\s]related|sickness|work injury|accidents|occupational|B2.2)",
                    r"(?P<content>no work safety-related incident|no work safety-related accident|no work injury)",
                ],
                "second_pattern": [
                    r"lost days",
                    r"was (zero|\d+)",
                    r"no (work-related fatalities|lost day)",
                    r"no incident of work-related fatality",
                    r"no working day lost",
                ],
                "middle_rows": True,
                "table_title": [],
                "start_regs": [r"B2\.2"],
                "end_regs": [r"B2\.3"],
                "direct_answer_pattern": [
                    r"No lost day due to work injury was record",
                ],
            },
            {
                "name": "row_match",
                "ignore_index_table": True,
                "multi": True,
                "row_pattern": [
                    r"Lost Days.*?Work-related Injury",
                    r"Workdays lost.*?(work-related|fatalities)",
                    r"Number of lost days",
                    r"B2\.2",
                ],
            },
            {
                "name": "first_table",
                "regs": [
                    r"(?P<content>work related|lost days|sickness|work injury|occupational|B2.2)",
                    # r'accidents',
                ],
            },
            {
                "name": "twice_para_match",
                "multi_elements": True,
                # "strict_limit": True,
                "syllabus_regs": [],
                "neglect_syllabus_regs": [],
                "paragraph_pattern": (
                    r"(?P<content>work-?related|lost days|sickness|work injury|accidents|occupational|B2.2)",
                    r"(?P<content>no work safety-related incident|no work safety-related accident|no work injury)",
                ),
                # 'neglect_pattern': (
                # ),
                "second_pattern": (
                    r"lost (labor )days",
                    r"(had|was|were|is) (zero|\d+|no)",
                    r"nor? (any )?(work-related fatalities|l[ao]st day)",
                ),
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"incidents of work injuries arose",
                    # r'(?P<content>work-related)',
                ),
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__Accident Rate Analysis",
                    r"__regex__Employee Development",
                ],
                "multi_elements": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"(?P<content>work related|lost days|sickness|work injury|accidents|B2.2)",
                        r"(?P<content>no work safety-related incident|no work safety-related accident|no work injury)",
                        r"LTIFR",
                    ),
                },
                "table_model": "empty",
            },
        ],
    },
    {
        # B2涉及到的规则都是关于health and safety相关的内容，
        # B2.3提取的是公司采用的职业健康和安全措施，以及如何实施和监测这些措施；
        #  所在位置：1. health and safety的大标题下（一般位置比较固定，都在这个章节下定位）
        #
        #  提取内容：1. 常见描述形式为一段话末尾加冒号，然后按条描述明细措施，需要提取这段话加下面的所有明细
        #           2. 有小标题的，提取小标题下面的所有文段；或者除了policy，law以及B2.1、B2.2数据描述以外的其他文段
        #           3. 单独存在关于新冠的措施的小标题下，包含COVID关键词的，提取小标题下的全部内容
        #           3. 对于篇幅较小的或没有按照小标题披露的，可以提取包含各类动词的文段，如 check，daily, provide, organize,
        #           required, regularly等
        #
        #  关键词：health, healthy and safe, occupational, healthy, safety, measures, training, check，daily, provide,
        #  organize, required, regularly, first-aid
        #
        #  判定方式：Y-披露并描述以上的提取内容（满足提取内容的任何一条或多条即可）
        #           E- 不常见，但可能会有些金融行业或者与员工劳动伤害很远的行业，在此标题下会有 not material之类的描述
        #           ND-没有任何相关描述
        # todo 取 health and safety children syllabus
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/803#note_212892  todo
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/805#note_213474  todo
        "path": ["KPI B2.3 - health and safety measures"],
        "models": [
            {
                "name": "after_row_match",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "row_pattern": [r"B2.3"],
                "multi_elements": True,
                "ignore_syllabus": [
                    r"^Health and Safety$",  # 忽略范围太大的标题
                ],
                "para_pattern": [
                    r"(?P<content>COVID-?19|preventive measures|facilities|safety performance)",
                    r"adopted the following measures:$",
                ],
                "neglect_para_pattern": [
                    r"during the reporting period",
                    r"During the past.*?years",
                    r"safety performance:$",
                    r"lifelong learning",
                    r"Development and Training",
                ]
                + NEGLECT_PAGE_HEADER_PATTERN,
                "sub_syllabus": [
                    r"Normalized Management of the Pandemic",
                    r"Staff Health Protection",
                    r"Fire Safety Management",
                    r"Staff Communication and Care",
                ],
            },
            {
                "name": "ar_esg_b23",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "multi": True,
                "inject_syllabus_features": [
                    r"__regex__Health and Safety__regex__Monitoring System",
                    r"__regex__Health and Safety__regex__Safety Organisation",
                    r"__regex__Health and Safety (train|Measures)",
                    r"__regex__Precautions Against Covid-19",
                    r"__regex__(Prevent|Response).*?(of|for).*?Covid[-\s]?19",
                    r"__regex__Employee Health and Care",
                    r"__regex__Occupational Hea\s?lth and Safety",
                    r"__regex__Work Health",
                ],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__Health and Safety",
                    r"__regex__Health & Safety",
                    r"__regex__(Prevent|Response).*?(of|for|to).*?Covid[-\s]?19",
                ],
                "neglect_parent_features": [
                    r"Product Responsibility",
                    r"Contacting Customers",
                ],
                "multi": True,
                "multi_elements": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"COVID-?19|preventive measures|facilities|safety performance",
                        r"safety training",
                        r"safety monitoring mechanism",
                        r"healthy and safe|occupational|safety|measures|training|check|daily|provide|organize|required|first-aid",
                        r"strictly abided to relevant preventive",
                        r"conduct fire drills",
                        r"take various measures to minimise",
                        r"health and safety measures",
                    ),
                    "neglect_pattern": (
                        r"^Aspect B2:\s?Health and Safety$",
                        r"comply with relevant laws and regulations.*?Health Ordinance",
                        r"^during the year",
                        r"^During the Reporting Period",
                        r"number of work injuries",
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "para_match",
                "multi_elements": True,
                "paragraph_pattern": (
                    r"(?P<content>COVID 19|preventive measures|facilities|safety performance)",
                    r"(?P<content>safety training)",
                    r"(?P<content>safety monitoring mechanism)",
                ),
            },
            {
                "name": "syllabus_elt_v2",
                "only_inject_features": True,
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "inject_syllabus_features": [
                    # r'__regex__Improvement of HSE System',
                    r"__regex__^B2.*?Health and Safety",
                    r"__regex__II.2.*?Health and Safety",
                    r"__regex__B\(II.*?HEALTH AND SAFETY",
                    # r'__regex__Occupational Health and Safety',
                ],
            },
            DEFAULT_MODEL,
        ],
    },
    {
        "path": ["B3 policies - development and training"],
        "models": [
            # 特例 GRI 相关 https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/997#note_243521
            {
                "name": "para_match",
                "multi_elements": True,
                "syllabus_regs": [
                    r"Benefits and Employee Retention",
                ],
                "paragraph_pattern": (
                    r"create a culture of continuous learning",
                    r"designs its training.*?improve their job performance",
                ),
            },
            {
                "name": "after_row_match",
                "row_pattern": [r"B3"],
                "para_pattern": [
                    r"(?P<content>B3|development|training)",
                ],
            },
            {
                "name": "policy_row_match",
                "multi": True,
                "multi_elements": True,
                "row_pattern": ["^General Disclosure"],
                "anchor_pattern": ["Aspect B3"],
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                # "only_before_first_chapter": True,
                "inject_syllabus_features": [
                    r"__regex__III.3 Training and Development",
                    r"__regex__Development and Training",
                    r"__regex__staff development",
                ],
            },
            DEFAULT_MODEL,
        ],
    },
    {
        # B3.1 此条规则是披露员工中受培训的情况。具体是按性别和员工类别（例如高级管理人员、中层管理人员）培训的员工百分比（重点是百分比数据）
        # 常见关键词有B3.1，train, gender, employee category。
        #  所在位置：1、优先在index或者data summary的地方找对应的图表、表格及段落；
        #           2、根据index给出KPI的位置去定位，一般在标题Development and Training/staff development下
        #
        #  判断依据：1、报告中没有相关数据但有解释，涉及no training的描述（常见描述如“本年因为疫情原因，集团未给员工提供培训”），为E；
        #           2、报告中披露了参与培训的各类员工人数对应的百分比数据，都是Y；
        #           3、报告没有披露相关内容，也没有进行解释，为ND
        #
        #  判断特殊情况：1、仅有total training hours，没有披露按照gender和category分类下的average training hours ，也没有
        #               percentage of employee trained，B3.1属于Y；
        #               2、有时B3.1描述100%完成了相关的培训项目，出现completion xxx也属于B3.1
        "path": ["KPI B3.1 - percentage of employees trained"],
        "models": [
            {
                "name": "split_first_table",
                # "second_table": "True",
                "regs": [
                    r"B3\.1",
                    r"Percentage of employees trained",
                    r"Number.*?employees.*?train",
                ],
                "start_regs": [
                    r"B3\.1",
                    r"Training Indicators",
                    r"Number.*?employees.*?train",
                    r"(Percentage|%|proportion).*?(traine\s?d|training)",
                    r"(trained|training).*?(Percentage|%|proportion)",
                ],
                "end_regs": [
                    r"Average hours",
                    r"B3\.2",
                ],
            },
            {
                "name": "first_table",
                "title_regs": [
                    r"性別及僱員類別劃分之百分比及平均培訓時數",
                ],
                "regs": [
                    r"B3.1",
                    r"Percentage of employees trained",
                    r"Total number of employees received",
                ],
            },
            {
                "name": "after_row_match",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "row_pattern": [r"B3.1"],
                "para_pattern": [
                    r"(?P<content>During the reporting period.*?training)",
                    r"(?P<content>no training)",
                ],
                "middle_rows": True,
                "multi_elements": True,
                "table_title": [
                    r"trained by.*?(gender|category)",
                    r"受訓僱員百分比",
                    r"trained by",
                    r"(Percentage|%|proportion).*?(traine\s?d|training)",
                    r"(trained|training).*?(Percentage|%|proportion)",
                    r"Below are the details of employee training",
                    r"train.*?percentage by",
                ],
                "start_regs": [r"Percentage of employees trained", r"By employee category"],
                "end_regs": [r"Average training hours"],
            },
            {
                "name": "shape_title",
                "regs": (
                    r"受訓僱員百分比",
                    r"trained by",
                    r"(Percentage|%|proportion).*?(traine\s?d|training)",
                    r"(trained|training).*?(Percentage|%|proportion)",
                ),
            },
            {
                "name": "table_title",
                "only_inject_features": True,
                "multi_elements": True,
                "feature_white_list": (
                    r"受訓僱員百分比",
                    r"trained by",
                    r"(Percentage|%|proportion).*?(traine\s?d|training)",
                    r"(trained|training).*?(Percentage|%|proportion)",
                    r"Employees.*?provided.*?training sessions",
                ),
            },
            {
                "name": "special_cells",
                "whole_table": True,
                "cell_pattern": (
                    r"受訓僱員百分比",
                    r"發展與培訓 \(%\)",
                    r"(Percentage|%|proportion).*?(traine\s?d|training)",
                    r"(trained|training).*?(Percentage|%|proportion)",
                    r"number.*?trained",
                ),
            },
            {
                "name": "middle_rows",
                "ignore_index_table": True,
                "regs": [
                    r"B3\.1",
                    r"Percentage of employees trained",
                ],
                "start_regs": [
                    r"B3\.1",
                    r"Training Indicators",
                    r"(Percentage|%|proportion).*?(traine\s?d|training)",
                    r"(trained|training).*?(Percentage|%|proportion)",
                ],
                "end_regs": [
                    r"Average hours",
                    r"B3\.2",
                ],
            },
            {
                "name": "para_match",
                "syllabus_regs": [
                    r"train|development",
                ],
                "paragraph_pattern": (
                    # r'(?P<content>B3.1|train|gender|employee category)',
                    r"(?P<content>no training)",
                    r"\d+% of our employees are trained",
                    r"percentage of trained employees.*?\d+%",
                    r"the male and female composition ratio of the employees trained is approximately",
                ),
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__Summary of Development and Training",
                    r"__regex__Summary of Development and Training",
                    r"__regex__Provision of Training Opportunities",
                    r"__regex__Training and Development Management",
                    r"__regex__Development and training",
                    r"__regex__training",
                ],
                "multi_elements": True,
                "only_inject_features": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"(?P<content>no training)",
                        r"\d%.*?attended training courses",
                        r"did not arrange any training courses",
                        r"proportions of training",
                    ),
                },
                "table_model": "table_title",
                "table_config": {
                    "feature_white_list": [
                        r"Summary of Development and Training",
                    ],
                },
            },
            # DEFAULT_MODEL,
        ],
    },
    {
        #  规则是针对employment下面的Development and Training，披露按性别和员工类别划分的每位员工完成的平均培训时数。
        #  关键词是average training hours、employee category
        #  所在位置：1、优先去index/summary/key performance indicators中找寻；
        #           2、根据index给出的KPI位置去定位；
        #           3、在Development and Training/staff development标题下。
        #
        #  判断标准：1、如果报告中说没有培训并且解释原因，那么选E，比如`本年因为疫情原因，集团未给员工提供培训`，关键词：no training；
        #           2、找到对应图表、段落或者句子，就算Y；
        #           3、如果报告中没有对此有任何描述，那么就是ND。
        "path": ["KPI B3.2 - training hours completed"],
        "models": [
            {
                "name": "split_first_table",
                "second_table": "True",
                "regs": [
                    r"B3.2",
                    r"train.*?hours",
                    r"hours.*?train",
                ],
                "start_regs": [
                    r"B3.2",
                    r"hours.*?train",
                    r"train.*?hours",
                ],
                "end_regs": [
                    r"B5\.1",
                    r"Occupational safety",
                    r"operational indicator",
                    r"ESG Indicator",
                ],
            },
            {
                "name": "special_cells",
                "multi_elements": True,
                "whole_table": True,
                "cell_pattern": [
                    r"training hours by",
                    r"Average training hours",
                    r"Hours of employees trained",
                ],
            },
            {
                "name": "first_table",
                "regs": [
                    r"B3.2",
                    r"train.*?hours",
                    r"hours.*?train",
                ],
            },
            {
                "name": "shape_title",
                "regs": (r"The average training hours completed",),
            },
            {
                "name": "table_title",
                "multi_elements": True,
                "feature_white_list": [
                    r"training hours.*?by",
                    r"The average training hours completed",
                ],
            },
            {
                "name": "elements_collector_based",
                "elements_collect_model": "after_row_match",
                "elements_collect_config": {
                    "row_pattern": [r"B3.2"],
                    "para_pattern": [
                        r"(?P<content>no training)",
                    ],
                },
                "paragraph_model": "para_match",
                "para_config": {
                    # 'content_pattern': True,
                    "paragraph_pattern": (
                        r"(?P<content>During the reporting period.*?training)",
                        # r'(?P<content>B3.2|train|gender|employee category)',
                        r"(?P<content>no training)",
                    ),
                },
            },
            {
                "name": "after_row_match",
                "row_pattern": [r"B3.2"],
                "para_pattern": [
                    r"(?P<content>average training hours)",
                    r"(?P<content>no training)",
                    r"(?P<content>average.*?hours.*?training)",
                ],
            },
            {
                "name": "elements_collector_based",
                "elements_collect_model": "syllabus_elt_v2",
                "elements_collect_config": {
                    "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                    "include_shape": True,
                    "only_inject_features": True,
                    "inject_syllabus_features": [
                        r"__regex__^KPI B3.[12]",  # 60838
                        r"__regex__DEVELOPMENT AND TRAINING",  # 60879
                    ],
                },
                "shape_model": "shape_title",
                "shape_config": {
                    "regs": (
                        r"average training hours",
                        r"training hours completed",
                    ),
                },
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "inject_syllabus_features": [r"__regex__^KPI B3.2"],
            },
            {
                "name": "para_match",
                "syllabus_regs": [
                    r"Development and Training",
                    r"staff development",
                ],
                "paragraph_pattern": (
                    r"the Group provided.*?trainings hours to our employees",
                    r"arranged.*?hours training",
                    r"(?P<content>no training)",
                    r"(?P<content>average.*?hours.*?training)",
                ),
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [r"__regex__Development and Training"],
                "only_inject_features": True,
                "multi_elements": True,
                "paragraph_model": "empty",
                "table_model": "table_title",
                "table_config": {
                    "multi_elements": True,
                    "feature_white_list": [
                        r"Average training hours per employee by (Gender|Category)",
                    ],
                },
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [],
                "multi_elements": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        # r'(?P<content>B3.2|average training hours|employee category|gender)',
                        r"(?P<content>no training)",
                        r"(?P<content>average.*?hours.*?training)",
                    ),
                },
                "table_model": "empty",
            },
            DEFAULT_MODEL,
        ],
    },
    {
        # 枚举判定为：E
        # ESG报告中没有相关劳工标准政策披露但是有解释
        # 常见表述“由于公司的业务性质是XXX，涉及到的劳动力较少，所以not applicable”
        # 或“由于公司的业务性质,雇佣的员工多为有一定资历或者工作经验的员工，童工或强制劳动的风险小 "
        # 或“no相关的问题” 或“not material" 的描述
        # 常见关键词：not material，not applicable, non-labour intensive
        #
        # 枚举判定为：Y
        # ESG报告中有关于劳工标准labour standards政策的披露
        # 提取涉及到labour standards政策的所有段落。常见的描述是“公司的劳工标准是……，遵守当地劳工法律法规，禁止雇佣童工、强迫劳动和非法用工等”。
        # i. 一般有单独的labour standard相关标题，能区分出后面B4.1和B4.2的就不勾进去
        # ii.无单独的labour standard相关标题，可能会跟B1 employment标题下方的内容混在一起，可通过关键词child,forced labor进行定位
        # 常见关键词： labour, labor，B4, workforce, forced labour, engaging child, Labour Standards，prevent
        #
        # ND: 没有披露相关内容,也没有进行解释
        "path": ["B4 policies - labour standards"],
        "models": [
            {
                # 章节标题和第一个小标题之间
                "name": "policy",
                "only_inject_features": True,
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "ignore_pattern_match": True,
                "inject_syllabus_features": [
                    r"__regex__Labour Standards",
                    r"__regex__Aspect B4: Labour Standards",
                ],
            },
            {
                "name": "policy_row_match",
                "multi": True,
                "multi_elements": True,
                "row_pattern": ["^General Disclosure"],
                "anchor_pattern": ["Aspect B4"],
            },
            {
                "name": "syllabus_based",
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__B3.*?Development and Training",
                    r"__regex__talent management",
                    r"__regex__Labou?r Standard",
                    r"__regex__Labou?r Practices",
                    r"__regex__Prevention of Child and Forced Labour",
                ],
                "multi_elements": True,
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"as are not addressed in this ESG",
                        r"any child labour",
                        r"forbids all types",
                        r"prevent.*?child labour",
                        r"(child|forced|illegal).*?(labour|labor)",
                        r"ensure compliance with relevant labour laws",
                        r"To prevent hiring child labour by mistake",
                        r"rights of labours",
                        r"Free chosen employment",
                        r"Remuneration and benefits",
                        r"Equal opportunity and no discrimination policy",
                        r"not force any employees to work",
                        r"Harassment and abuse",
                    ),
                    "neglect_pattern": (
                        r"have been no cases of ",
                        r"During the Reporting Period",
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_before_first_chapter": True,
                "only_first": True,
                "inject_syllabus_features": [
                    r"__regex__Labou?r Standards",
                    r"__regex__Employment and Labour Standards",
                    r"__regex__Human Capital",
                    r"__regex__Employment Policy",
                ],
            },
            {
                "name": "para_match",
                "strict_limit": True,
                "syllabus_regs": [
                    r"Labour Standards",
                    r"Employment and Labour Standards",
                    r"Human Capital",
                    r"Employment Policy",
                ],
                "paragraph_pattern": (
                    r"(?P<content>not material|not applicable|non-labour intensive)",
                    r"(?P<content>labour|labor|B4|workforce|forced labour|engaging child|Labour Standards)",
                    r"(?P<content>not have any violation relating)",
                ),
                "neglect_pattern": (r"to prevent any employment of child labour",),
            },
            {
                "name": "kmeans_classification",
            },
        ],
    },
    {
        "path": ["B4 law compliance - labour standards"],
        "models": [
            {
                "name": "para_match",
                "paragraph_pattern": LAW_COMMON_PATTERN,
                "neglect_syllabus_regs": [
                    r"Supply chain management",
                    r"Anti.*?Corruption",
                    r"The Environment and Natural Resources",
                    r"Data Privacy",
                    r"B[1-3]|B[5-8]|A[1-3]",
                ],
            },
            {
                "name": "twice_para_match",
                "multi_elements": True,
                # "strict_limit": True,
                "syllabus_regs": [
                    r"labour standard",
                ],
                "neglect_syllabus_regs": [
                    r"Supply chain management",
                    r"Anti.*?Corruption",
                    r"The Environment and Natural Resources",
                    r"Data Privacy",
                    r"B[1-3]|B[5-8]|A[1-3]",
                ],
                "paragraph_pattern": COMPLY_LAW_PATTERN,
                # 'neglect_pattern': (
                #     r'^[a-zA-Z]\.',
                #     r'^•',
                # ),
                "second_pattern": (
                    r"workforce",
                    r"laws and regulations",
                    r"engaging child",
                    r"labour law",
                    r"(child|forced|illegal).*?(labour|labor)",
                    r"labour related (law|regulation)",
                ),
            },
            {
                "name": "para_match",
                "syllabus_regs": [
                    r"Labour Standards",
                    r"Employment and Labour Standards",
                    r"Human Capital",
                    r"Employment Policy",
                ],
                "neglect_syllabus_regs": [
                    r"Supply chain management",
                    r"Anti.*?Corruption",
                    r"The Environment and Natural Resources",
                    r"Data Privacy",
                    r"B[1-3]|B[5-8]|A[1-3]",
                ],
                "paragraph_pattern": (
                    r"complied.*?laws and regulations.*?labour standards",
                    r"(?P<content>not applicable|no (law|ordinance|regulation))",
                    r"(?P<content>(law|regulations).*?(workforce|forced labor|child|engaging child))",
                    r"(?P<content>not have any violation relating)",
                ),
                "neglect_pattern": (r"Information on:",),
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__Labour Standards",
                    r"__regex__Employment and Labour Standards",
                    r"__regex__Human Capital",
                    r"__regex__Employment Policy",
                ],
                "multi_elements": True,
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"(?P<content>not applicable|no (law|ordinance|regulation))",
                        r"(?P<content>(law|regulations).*?(workforce|forced labor|child|engaging child))",
                        r"(?P<content>notl have any violation relating)",
                        r"did not identify any material breaches",
                    ),
                    "neglect_pattern": (r"Information on:",),
                },
                "table_model": "empty",
            },
            {
                "name": "kmeans_classification",
            },
            # DEFAULT_MODEL,
        ],
    },
    {
        # 枚举判定为：E
        # ESG报告中没有相关披露但是有解释
        # 常见表述“这条规则不适用于本集团”或者 “N/A”或“not disclosure”
        # 关键词：not material，not applicable, N/A, not disclosure
        #
        # 枚举判定为：Y
        # ESG报告中有关于公司如何审查以避免child and labour force的措施的披露
        # 提取涉及child和forced labour措施的所有段落，侧重于labour force和用工年龄方面。
        # 常见的描述是“公司已制定招聘政策，符合年龄的申请人才可被聘用”或者“本集团要求求职者提供有效的身份证明文件，确保年龄符合规定”等。
        # i.一般在labour standard相关标题的内容中披露相关措施；
        # ii.无单独的labour standard相关标题，可能会跟B1 employment标题下方的内容混在一起，可通过关键词child,forced labor进行定位`
        # 关键词：workforce, forced labour, engaging child，child labour，establish，overtime，prohibition，prevent，
        # identity/identification  document,identity card,recruiting,above 18 years old,working visa
        #
        # ND: 没有披露相关内容,也没有进行解释
        "path": ["KPI B4.1 - review measures to avoid child & forced labour"],
        "models": [
            {
                "name": "after_row_match",
                "row_pattern": [r"B4.1"],
                "ignore_syllabus": [r"^Our Employees$"],
                "just_a_para": True,
                # 'multi_elements': True,
                "para_pattern": (
                    r"(?P<content>not material|not applicable|N/A|not disclosure)",
                    r"(?P<content>workforce|forced labour|engaging child|child labour|establish|overtime)",
                    # r'forced labour|child labour|engaging child',
                    # r'prevent|recruiting|prohibition|overtime|establish|workforce',
                    r"(identity|identification document|identity card)",
                    r"not material|not applicable|N/A|not disclosure",
                    r"workforce|engaging child|child labour",
                    # r'establish|overtime',
                    r"complete pre-employment application",
                    r"provide.*?identification",
                    r"(identity|identification document)",
                    r"identity card",
                    r"above 18 years old",
                    r"working visa",
                    r"ensure no child labor",
                    r"checks the documents provided",
                    r"inspect applicant’s documents",
                    r"checks.*?identity documents",
                    r"Free chosen employment",
                    r"Remuneration and benefits",
                    r"Equal opportunity and no discrimination policy",
                    r"not force any employees to work",
                    r"Harassment and abuse",
                ),
                "direct_answer_pattern": [
                    r"apply the same standard.*?prevent child and force",
                ],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__KPI B4.1",
                    r"__regex__Employment Guidelines",
                    r"__regex__Employment and Labour Standards",
                    r"__regex__Labor Rights and Interests Protection",
                    r"__regex__Prevent.*?Child and Forced Labour",
                    # r'__regex__Preventative measures against child and forced labour',
                    r"__regex__Labour Standard",
                    r"__regex__Human Capital",
                    r"__regex__Employment Policy",
                ],
                "multi_elements": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"not material|not applicable|N/A|not disclosure",
                        r"workforce|engaging child|child labour",
                        # r'establish|overtime',
                        r"complete pre-employment application",
                        r"provide.*?identification",
                        r"(identity|identification document)",
                        r"identity card",
                        r"recruiting",
                        r"above 18 years old",
                        r"working visa",
                        r"ensure no child labor",
                        r"checks the documents provided",
                        r"inspect applicant’s documents",
                        r"checks.*?identity documents",
                        r"Free chosen employment",
                        r"Remuneration and benefits",
                        r"Equal opportunity and no discrimination policy",
                        r"not force any employees to work",
                        r"Harassment and abuse",
                    ),
                    "neglect_pattern": (
                        r"During the Year",
                        r"engage suppliers and contractors",
                    ),
                },
                "table_model": "row_match",
                "ignore_index_table": True,
                "table_config": {
                    "row_pattern": [
                        r"(child|forced) labour",
                    ],
                },
            },
            {
                "name": "syllabus_elt_v2",
                "only_inject_features": True,
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "inject_syllabus_features": [
                    r"__regex__B4.1",
                    # r'__regex__Labour Standard',
                    r"__regex__Employment and Labour Standards",
                    r"__regex__Human Capital",
                    r"__regex__Employment Policy",
                ],
            },
            {
                "name": "para_match",
                "syllabus_regs": [
                    r"Labour Standards",
                    r"Employment and Labour Standards",
                    r"Human Capital",
                ],
                # 'multi_elements': True,
                "paragraph_pattern": (
                    # r'(?P<content>not material|not applicable|N/A|not disclosure)',
                    # r'(?P<content>workforce|forced labour|engaging child|establish|overtime)',
                    r"complete pre-employment application",
                    r"provide.*?identification",
                    r"(identity|identification document)",
                    r"identity card",
                    r"recruiting",
                    r"above 18 years old",
                    r"working visa",
                    r"ensure no child labor",
                    r"checks the documents provided",
                    r"inspect applicant’s documents",
                    r"checks.*?identity documents",
                    r"B4.*?are not the key material matters.*?are not addressed in this ESG report",
                ),
            },
            # {
            #     "name": 'kmeans_classification',
            # },
            # DEFAULT_MODEL,
        ],
    },
    {
        # 框选内容：<优先在index指引的正文内容去找>
        #
        # 如果存在明显的细分小标题**kpi B4.2**，则直接框该小标题下内容
        #
        #  一般描述中会有**比较明确的关键词**： in case of，once found, in the event这种表示
        #  如果发现forced labour，child labour的词。
        # todo 修改twice_para_match之后还需要再次重跑根据badcase 补充规则
        "path": ["KPI B4.2 - steps to avoid child & forced labour"],
        "models": [
            {
                "name": "para_match",
                "paragraph_pattern": (r"B4.*?are not the key material matters.*?are not addressed in this ESG report",),
            },
            {
                "name": "twice_para_match",
                "syllabus_regs": [
                    r"Labou?r Standards",
                    r"_Labou?r right",
                    r"Employment and Labour Standards",
                    r"Human Capital",
                    r"Employment Policy",
                ],
                "paragraph_pattern": (
                    r"(child|forced|illegal).*?(labour|labor)",
                    r"engaging child",
                    r"background check",
                    r"done voluntarily",
                    r"overtime work",
                ),
                "second_pattern": (
                    r"(?<!no )cases? of",
                    r"in case of|once found|in the event",
                    r" (once|facing|discovered)",
                    r"if.*?occurrence",
                    r"if(?:(?!\.).)*?needed",
                    r"background check|working age|disqualified|refuse|handle",
                    r"comprehensive identity check",
                    r"screening process",
                    r"review employment practices",
                    r"preventive procedures",
                    # 用于确定年龄的描述
                    r"verify(?:(?!\.).)*?age",
                    r"identity card and vocational qualification certificate",
                    r"social security card",
                    r"medical or health certificate",
                    r"recent photos",
                    r"other relevant information and documents",
                    r"travel documents",
                ),
            },
            {
                "name": "after_row_match",
                "row_pattern": [r"B4.2"],
                "para_pattern": (
                    r"(child|forced|illegal).*?(labour|labor)",
                    r"engaging child",
                    r"background check",
                    r"done voluntarily",
                ),
                "second_pattern": (
                    r"(?<!no )cases? of",
                    r"in case of|once found|in the event",
                    r"once|facing|discovered",
                    r"if.*?occurrence",
                    r"background check|working age|disqualified|refuse|handle|overtime work",
                    r"comprehensive identity check",
                    r"screening process",
                    r"review employment practices",
                    r"preventive procedures",
                    # 用于确定年龄的描述
                    r"verify.*?age",
                    r"identity card and vocational qualification certificate",
                    r"social security card",
                    r"medical or health certificate",
                    r"recent photos",
                    r"other relevant information and documents",
                    r"identification|travel documents",
                ),
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__B4.[12]",
                    r"Handling of non-compliance",
                ],
                "multi_elements": True,
                "paragraph_model": "twice_para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"(child|forced|illegal).*?(labour|labor)",
                        r"engaging child",
                        r"background check",
                        r"done voluntarily",
                    ),
                    "second_pattern": (
                        r"(?<!no )cases? of",
                        r"in case of|once found|in the event",
                        r"once|facing|discovered",
                        r"if.*?occurrence",
                        r"background check|disqualified|refuse|handle|overtime work",
                        r"comprehensive identity check",
                        r"screening process",
                        r"review employment practices",
                        r"preventive procedures",
                        # 用于确定年龄的描述
                        r"verify\s?age",
                        r"identity card and vocational qualification certificate",
                        r"social security card",
                        r"medical or health certificate",
                        r"recent photos",
                        r"other relevant information and documents",
                        r"identification|travel documents",
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "para_match",
                "paragraph_pattern": (r"employees work voluntarily",),
            },
        ],
    },
    {
        "path": ["B5 policies - supply chain"],
        "neglect_para_missing_crude_regs": [
            # 特例 GRI 相关 https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/997#note_243521
            r"minimise the generation of dust and reduce its",
            r"Provision of silt fences for temporary drain are made",
            r"adverse impact of climate change",
        ],
        "models": [
            # 特殊的badcase uat
            # https://hkex.test.paodingai.com/#/hkex/esg-report-checking/report-review/196200?fileId=58245&schemaId=1&rule=B4%20policies%20-%20labour%20standards
            {
                "name": "syllabus_based",
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__B3.*?Development and Training",
                ],
                "multi_elements": True,
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (r"as are not addressed in this ESG"),
                    "neglect_pattern": (),
                },
                "table_model": "empty",
            },
            {
                "name": "policy_row_match",
                "multi": True,
                "multi_elements": True,
                "row_pattern": ["^General Disclosure"],
                "anchor_pattern": ["Aspect B5"],
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"the Group maintains business relationships with suppliers",
                    r"Group’s policy in relation to.*?suppliers",
                ),
            },
            {
                # 章节标题和第一个小标题之间
                "name": "policy",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "ignore_pattern_match": True,
                "inject_syllabus_features": [
                    r"__regex__Supply Chain Management Structure",
                    r"__regex__Supply Chain Management__regex__General Disclosure and KPIs",
                    r"__regex__B(5:|\(V\)) Supply Chain Management",  # todo 改动这个正则导致正确率下降
                    r"__regex__(Supply.*?Management|Operating\s?Procedures)",
                ],
                "neglect_features": [
                    r"Anti-Corruption",
                    r"Operating Practices",
                ],
            },
            {
                "name": "after_row_match",
                "row_pattern": [r"B5\s"],
                "just_a_para": True,
                "para_pattern": [
                    r"highly valuing the responsibility management on its engineering contractors and material suppliers",
                    r"supply chain",
                ],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__Coordinated Development",
                    r"__regex__Development and Training",
                ],
                "only_inject_features": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"(supply|procument|supplier) management",
                        r"hese areas are not addressed in this ESG report",
                        r"evaluate suppliers",
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"(supply|procument|supplier) management",
                    r"formulated a standardised.*?suppliers and subcontractors",
                    r"The Group understands the importance of supply chain management",
                    r"no suppliers|not material",
                ),
            },
            # 特例 GRI 相关 https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/997#note_243521
            {
                "name": "syllabus_elt_v2",
                "only_inject_features": True,
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "inject_syllabus_features": [
                    r"__regex__Suppliers Selection",
                ],
            },
        ],
    },
    {
        # 优先取表格
        # B5.1 按地理区域划分的供应商数量
        # 枚举判定为：E
        # ESG报告中没有按地理区域划分供应商数量的披露，但是有解释，常见描述“由于我们的业务性质，我们没有进行供应链环境管理”或“no supplier”等的描述；
        # 关键词：no suppliers, not material, not disclosed, N/A, not applicable, not set up
        #
        # 枚举判定为：Y
        # B5.1 提取按地理区域划分供应商数量的段落或图表或句子，当句子或图表同时出现时，优先框选图表
        # 常见关键词：suppliers, geographical, B5.1,region，based，located in
        #
        # 枚举判定为：：ND
        # ESG报告中无相关描述则为ND
        "path": ["KPI B5.1 - number of suppliers"],
        "models": [
            {
                "name": "row_match",
                "multi": True,
                "row_pattern": [
                    r"Number of suppliers in Mainland China",
                    r"number of suppliers",
                    r"No\. of suppliers",
                ],
            },
            {
                "name": "shape_title",
                "regs": (
                    r"suppl.*?GEOGRAPHICAL REGION",
                    r"NUMBER OF SUPPLIERS",
                    r"SUPPLIER DISTRIBUTION",
                    r"major suppliers that were directly related",
                ),
            },
            {
                "name": "table_title",
                "only_inject_features": True,
                "feature_white_list": [
                    r"suppl.*?GEOGRAPHICAL REGION",
                    r"NUMBER OF.*?SUPPLIERS",
                    r"SUPPLIER DISTRIBUTION",
                    r"major suppliers that were directly related",
                ],
            },
            {
                "name": "special_cells",
                "whole_table": True,
                "cell_pattern": [
                    # r'location',
                    r"Number.*?suppliers",
                    r"No\. of supplier",
                    r"Total.*?suppliers",
                    # r'Mainland China|RPC|region',
                    # r'China|rest of asia',
                ],
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"the Group had a total of.*?major suppl",
                    r"(had|have) a total of.*?suppl",
                    r"had engaged.*?suppl",
                    r"located in Hong Kong",
                    r"no.*?major suppli",
                    r"suppliers are immaterial",
                    r"not? material.*?suppl",
                ),
            },
            {
                "name": "first_table",
                "regs": [
                    r"location",
                    r"Number.*?suppliers",
                    r"Total.*?suppliers",
                    r"Mainland China|RPC|region",
                    r"China|rest of asia",
                ],
                "title_regs": [
                    r"Number.*?suppliers",
                    r"Total.*?suppliers",
                ],
                "neglect_title_regs": [r"Environmental.*?Social and Governance Reporting Guide"],
            },
            {
                "name": "after_row_match",
                "row_pattern": [r"B5.1"],
                "just_a_para": True,
                "para_pattern": [
                    r"(?P<content>material|geographical|B5.1|region|based)",
                    # r'suppliers', # todo 添加跟suppliers相关更加严格的正则
                    r"Number.*?suppliers",
                    r"Total.*?suppliers",
                    r"Mainland China",
                ],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [],
                "multi_elements": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"had a total of.*?major suppliers",
                        r"geographical|B5.1|region",
                        r"engaged.*?business partners",
                        r"has approximately\s\d+.*?agents",
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"has approximately\s\d+.*?agents",
                    r"had a total of.*?major suppliers",
                    r"geographical|B5.1|region",
                    r"engaged.*?business partners",
                    r"\d{1,}.*?suppl",
                    r"B5.*?are not the key material matters.*?are not addressed in this ESG report",
                ),
            },
            {
                "name": "middle_rows",
                "regs": [],
                "start_regs": [
                    r"B5\.1",
                ],
                "end_regs": [
                    r"B5\.2",
                ],
            },
        ],
    },
    {
        # 框选有整个段落关发行人如何选择聘用的供应商，以及这些方式是如何实施和监管的(侧重supplier的选择标准，确定，评估，监管**等)
        # 一般都会描述几个不同的选取及评估标准，如：价格低，质量好，信用好，售后保障好等
        #
        # 框选内容：<优先在index指引的正文内容去找>
        #  如果存在明显的细分小标题**kpi B5.2**，则直接框该小标题下内容
        #  一般描述中会有**比较明确的关键词**： select, selection, vendors, suppliers, based on ，price等相关描述
        # 以上两个规则index及正文描述如果是not applicable, N/A，则判断为E
        "path": ["KPI B5.2 - suppliers engagement"],
        "models": [
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"suppliers.*?selected",
                    r"in selecting.*?suppliers.*?considerations",
                    r"select suppliers based on",
                    r"engage suppliers.*?based on",
                    r"select.*?supplier.*?selection",
                    r"(Suppliers|Subcontractors).*?are chosen",
                    r"selecting suppliers",
                    r"B5.*?are not the key material matters.*?are not addressed in this ESG report",
                ),
            },
            {
                "name": "after_row_match",
                "row_pattern": [r"B5.2"],
                "para_pattern": [
                    r"(?P<content>select|selection|vendors|based on|B5.2)",
                    # r'suppliers', # todo 添加跟suppliers相关更加严格的正则
                ],
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "multi": True,
                "inject_syllabus_features": [
                    r"__regex__Supply Chain Management__regex__KPI B5.2",
                ],
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__KPI B5.2",
                    r"__regex__III.1 Supply Chain Management",
                ],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__Service Provider Management",
                    r"__regex__Procurement Process",
                ],
                "neglect_features": [
                    r"LABOUR STANDARDS",
                ],
                "multi_elements": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"select|selection|vendors|based on|B5.2",
                        r"undertake annual performance reviews with our suppliers",
                        r"choosing a new supplier",
                        r"consider supplier",
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "special_cells",
                "whole_table": True,
                "cell_pattern": [
                    r"select|selection|vendors|supplier|based on|B5.2",
                ],
            },
            {
                "name": "para_match",
                "paragraph_pattern": (r"(?P<content>select|selection|vendors|supplier|based on|B5.2)",),
            },
            DEFAULT_MODEL,
        ],
    },
    {
        # B5涉及到的规则都是关于supply chain management相关的内容；
        # B5.3需要框选出用于识别供应链中环境和社会风险的做法（即ESG risk），以及实施和监控这些风险的方法。
        #  所在位置：1. 涉及到supply chain management的标题（一般位置比较固定，都在这个大标题下定位）
        #           2. 小标题为Environmental and Social Responsibility of Suppliers或者
        #           Supply Chain Risk Management的文字段落（有单独标题的可以只提取这个标题下的内容）
        #
        #  提取内容：1. 提取涉及到与suppliers管理和合作过程中，与其识别到的ESG风险有关的描述
        #           2. 通常涵盖范围较广，如识别供应商的童工和强制劳工情况、反腐建设情况、环保情况等描述，
        #           所以会涉及到对应的关键词
        #           3. 对于未能达到环境和社会标准的供应商，会终止合作的描述
        #           4. 由于本条规则侧重整个供应链环节内的风险识别及监控措施，所以有时也会包含B5.2的内容
        #
        #  关键词：（需要在supply chain management相关位置范围内查找）suppliers +
        #  take environmental and social risks into considerations,
        #  environmental and social criteria,
        #  prohibition on the recruitment of child and forced labour,
        #  eliminating discrimination to employees,
        #  providing a safe working environment,
        #  considering if the products and services provided are beneficial to environmental protection.
        #
        #  判定方式：Y-披露并描述以上的提取内容（满足提取内容的任何一条或多条即可）
        #           E-明确披露了 not involve significant risk to the environment and society的；
        #           关键词有not applicable，no suppliers, not material的
        #           ND-没有任何相关描述
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/803#note_212925
        "path": ["KPI B5.3 - supply chain ESG risks identification"],
        "models": [
            {
                "name": "para_match",
                "paragraph_pattern": (r"B5.*?are not the key material matters.*?are not addressed in this ESG report",),
            },
            {
                "name": "after_row_match",
                "row_pattern": [r"B5.3"],
                "multi_elements": True,
                "neglect_para_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "direct_answer_pattern": [
                    r"Due to the business nature, supply chain plays a minimal role in the Group."
                ],
                "para_pattern": [
                    r"(Supplier|service providers).*?status are assessed using a.*?evaluation",
                    r"social risks|supplier risks|review|B5.3",
                    r"requires suppliers to comply.*?law",
                    r"suppliers.*?review annually",
                    r"instability of the supply chain",
                    r"required to take remedial actions",
                    r"meet our quality requirements",
                    r"safety and employment matters",
                    r"made it clear to our subcontractors that compliance with the labor laws",
                    r"hold meetings with suppliers to communicate our requirements",
                    r"environmental and social considerations",
                    "take environmental and social risks into considerations,",
                    "environmental and social criteria",
                    "prohibition on the recruitment of child and forced labour",
                    "eliminating discrimination to employees",
                    "providing a safe working environment",
                    "considering if the products and services provided are beneficial to environmental protection",
                    "termination of cooperation",
                    "review.*?annually",
                    "social responsibilities to the society",
                ],
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "multi": True,
                "inject_syllabus_features": [
                    r"__regex__Supply Chain Management__regex__KPI B5.3",
                    r"__regex__Supply Chain Risk Management",
                    r"__regex__Supply Chain Environmental and Social Risk Management",
                ],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__environmental and social risks",
                    r"__regex__Supply Chain Environmental and Social Risk Management",
                    r"__regex__Supply Chain Management",
                    r"__regex__Environmental and Social Responsibility of Suppliers",
                    r"__regex__KPI B5.3",
                ],
                "multi_elements": True,
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"environment",
                        # r'assess',
                        r"social risks|supplier risks|review|B5.3",
                        r"requires suppliers to comply.*?law",
                        r"suppliers.*?review annually",
                        r"instability of the supply chain",
                        r"required to take remedial actions",
                        r"meet our quality requirements",
                        r"safety and employment matters",
                        r"made it clear to our subcontractors that compliance with the labor laws",
                        r"hold meetings with suppliers to communicate our requirements",
                        r"environmental and social considerations",
                        "take environmental and social risks into considerations,",
                        "environmental and social criteria",
                        "prohibition on the recruitment of child and forced labour",
                        "eliminating discrimination to employees",
                        "providing a safe working environment",
                        "considering if the products and services provided are beneficial to environmental protection",
                        "termination of cooperation",
                        "review.*?annually",
                        "social responsibilities to the society",
                    ),
                    "neglect_pattern": (
                        r"Environmental, Social and Governance",
                        r"ENVIRONMENTAL, SOCIAL AND GOVERNANCE REPORT",
                        r"^ENVIRONMENTAL,",
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "para_match",
                "multi_elements": True,
                "paragraph_pattern": (
                    r"environment",
                    # r'assess',
                    r"social risks|supplier risks|review|B5.3",
                    r"requires suppliers to comply.*?law",
                    r"suppliers.*?review annually",
                    r"instability of the supply chain",
                    r"required to take remedial actions",
                    r"meet our quality requirements",
                    r"safety and employment matters",
                    r"made it clear to our subcontractors that compliance with the labor laws",
                    r"hold meetings with suppliers to communicate our requirements",
                    r"environmental and social considerations",
                    "take environmental and social risks into considerations,",
                    "environmental and social criteria",
                    "prohibition on the recruitment of child and forced labour",
                    "eliminating discrimination to employees",
                    "providing a safe working environment",
                    "considering if the products and services provided are beneficial to environmental protection",
                    "termination of cooperation",
                    "review.*?annually",
                    "social responsibilities to the society",
                    r"B5.*?are not the key material matters.*?are not addressed in this ESG report",
                ),
                "neglect_pattern": (
                    r"Environmental, Social and Governance",
                    r"ENVIRONMENTAL, SOCIAL AND GOVERNANCE REPORT",
                ),
            },
            DEFAULT_MODEL,
        ],
    },
    {
        # B5涉及到的规则都是关于supply chain management相关的内容；
        # B5.4需要框选出供应链中选择供应商时用于推广环保产品和服务的做法，以及实施和监控这些风险的方法。
        #  所在位置：1. 涉及到supply chain management的标题（一般位置比较固定，都在这个大标题下定位）
        #           2. 小标题为Green Procurement或者Green Purchasing等的文字段落
        #
        #  提取内容：1. 提取涉及到在suppliers合作过程中，优先选择环保产品的描述
        #           2. 优先本地供应商采购，减少碳足迹的描述
        #           3. 由于涉及到供应商选择，有时可能与B5.2指向同一段落
        #
        #  关键词：（需要在supply chain management相关位置范围内查找）
        #  suppliers +  eco-friendly, environmentally preferable,
        #  minimizing the negative impact to natural environment,
        #  takes into account the geographical location,
        #  product delivery process,reduce the  carbon footprint from transportation
        #
        # environment，green ,local suppliers, sustainability
        #
        #  判定方式：Y-披露并描述以上的提取内容（满足提取内容的任何一条或多条即可）
        #           E-明确披露了due to its business nature, did not set up rules on supply chain management的；
        #           或关键词有not applicable，no suppliers, not material的
        #           ND-没有任何相关描述
        "path": ["KPI B5.4 - practice to promote environmentally preferable products"],
        "models": [
            {
                "name": "para_match",
                "strict_limit": True,
                "syllabus_regs": [
                    r"Development and Training",
                ],
                "paragraph_pattern": (r"B5.*?are not the key material matters.*?are not addressed in this ESG report",),
                "neglect_pattern": (
                    r"^\d+\..*?Supply Chain Management$",
                    r"^\d+\..*?Our Suppliers$",
                ),
            },
            {
                "name": "after_row_match",
                "row_pattern": [r"B5.4"],
                "multi_elements": True,
                "direct_answer_pattern": [
                    r"Due to the business nature, supply chain plays a minimal role in the Group."
                ],
                "para_pattern": [
                    # r'vendors|environment',
                    # r'eco-friendly|environmentally preferable|local suppliers|sustainability|B5.4',
                    r"eco-friendly|environmentally preferable|local suppliers|B5.4",
                    r"Group maintains close communication with its suppliers",
                    r"Group will also consider.*?suppliers.*?environment and society",
                    r"product.*?beneficial to environment",
                    r"environmentally preferable products",
                    r"preference of using sustainable construction materials",
                    r"environmental management plan",
                    r"consider the suppliers.*?environmental protection policies",
                    r"green and environmental friendly products",
                    r"green elements",
                    r"environmentally friendly",
                    r"Environmental Working Group",
                    r"environmental-friendliness in consumer products",
                    r"environmental safety",
                    r"environmental protection",
                    r"greener supply",
                    r"energy efficient with lower emission models",
                    r"environmental and social performance",
                    r"constantly monitor the environmental and social risks",
                    r"(evaluate|manage).*?the environmental protection materials",
                    r"as follow:",  # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/853#note_217953
                    r"including:",
                    r"sustainability practices",
                ],
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__KPI B5.4",
                    r"__regex__green procurement",
                    r"__regex__green sourcing",
                    r"__regex__green Purchasing",
                    r"__regex__environmentally preferable",
                    r"__regex__Classification of Suppliers",
                    r"__regex__Supplier Admittance",
                ],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__Supply Chain Management",
                    r"__regex__our Suppliers",
                    r"__regex__Supplier Management",
                    r"__regex__Responsible Procurement",
                    r"__regex__Number and Types of Suppliers",
                    r"__regex__Types of Suppliers",
                ],
                "multi_elements": True,
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        # r'sustainability',
                        r"Due to the nature of our business",
                        r"eco-friendly|environmentally preferable|local suppliers|B5.4",
                        r"Group maintains close communication with its suppliers",
                        r"Group will also consider.*?suppliers.*?environment and society",
                        r"product.*?beneficial to environment",
                        r"environmentally preferable products",
                        r"preference of using sustainable construction materials",
                        r"environmental management plan",
                        r"consider the suppliers.*?environmental protection policies",
                        r"green and environmental friendly products",
                        r"green elements",
                        r"environmentally friendly",
                        r"environmentally protection",
                        r"environmental protection",
                        r"Environmental Working Group",
                        r"environmental-friendliness in consumer products",
                        r"environmental safety",
                        r"greener supply",
                        r"energy efficient with lower emission models",
                        r"environmental and social performance",
                        r"constantly monitor the environmental and social risks",
                        r"(evaluate|manage).*?the environmental protection materials",
                        "as follow:",  # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/853#note_217953
                        "including:",
                        # r'^\d+\.',
                        # r'^•',
                        r"social responsibilit",
                        r"sustainability practices",
                        r"business sustainability",
                        r"eco-friendly",
                        r"environmentally preferable",
                        r"minimizing the negative impact to natural environment",
                        r"takes into account the geographical location",
                        r"product delivery process",
                        r"reduce the carbon footprint from transportation",
                        r"Regular reviews",
                        r"in a sustainable way",
                        r"comply with.*?(environmental|social).*?laws,",
                        r"ESG commitments",
                        r"identify any environmental or social risks",
                    ),
                    "neglect_pattern": (
                        r"^\d+\..*?Supply Chain Management$",
                        r"^\d+\..*?Our Suppliers$",
                    ),
                },
                "table_model": "special_cells",
                "table_config": {
                    "cell_pattern": [
                        r"eco-friendly|environmentally preferable|local suppliers|sustainability|B5.4",
                    ]
                },
            },
            {
                "name": "para_match",
                "strict_limit": True,
                "syllabus_regs": [
                    r"Supply Chain Management",
                    r"Supplier Management",
                    r"our Suppliers",
                    r"Coordinated Development",
                    r"Supply chain and risk management",
                    r"Responsible Procurement",
                ],
                "paragraph_pattern": (
                    # r'sustainability',
                    r"eco-friendly|environmentally preferable|local suppliers|B5.4",
                    r"Group maintains close communication with its suppliers",
                    r"Group will also consider.*?suppliers.*?environment and society",
                    r"product.*?beneficial to environment",
                    r"environmentally preferable products",
                    r"preference of using sustainable construction materials",
                    r"environmental management plan",
                    r"consider the suppliers.*?environmental protection policies",
                    r"green and environmental friendly products",
                    r"green elements",
                    r"environmentally friendly",
                    r"environmentally protection",
                    r"Environmental Working Group",
                    r"environmental-friendliness in consumer products",
                    r"environmental safety",
                    r"environmental protection",
                    r"as follow:",  # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/853#note_217953
                    r"including:",
                    r"B5.*?are not the key material matters.*?are not addressed in this ESG report",
                    r"social responsibilit",
                    r"sustainability practices",
                    r"business sustainability",
                    r"eco-friendly",
                    r"environmentally preferable",
                    r"minimizing the negative impact to natural environment",
                    r"takes into account the geographical location",
                    r"product delivery process",
                    r"reduce the carbon footprint from transportation",
                    r"Regular reviews",
                    r"in a sustainable way",
                    r"comply with.*?(environmental|social).*?laws,",
                    r"ESG commitments",
                    r"identify any environmental or social risks",
                ),
                "neglect_pattern": (
                    r"^\d+\..*?Supply Chain Management$",
                    r"^\d+\..*?Our Suppliers$",
                ),
            },
            {
                "name": "score_filter",
                "threshold": 0.618,
            },
        ],
    },
    {
        #  一般企业产品责任很多无非都是保证服务质量，保证产品质量，所以如果不存在明显的有小标题的概括性文字，
        #  很多时候B6会包含B6.4
        #
        #  框选内容：**优先从index定位的位置去找以下内容**
        #  <有明显概括性文字的>，一般在product/service responsibly相关的大标题与小标题之间的文字段落。
        #  <不存在明显概括性文字的>，当不存在任何小标题时候，可以直接框选大标题段落第一段关于质量控制的文字段落，
        #  如果第一段文字也和质量要求无关，那么可以直接框B6.4的quality assurance
        #
        #  关键词：ordinance, Product Quality Law, Ordinance
        # （在这些关键词附近可同时关注Product Responsibility, responsible services, high quality services,
        # meet the expectation, satisfaction。product responsibility,
        # product quality, safety and health，responsible, service等）
        # 大标题 与下一个小标题之间 Product  Responsibility，
        "path": ["B6 policies - product responsibility"],
        "models": [
            # 特殊的badcase uat
            # https://hkex.test.paodingai.com/#/hkex/esg-report-checking/report-review/196200?fileId=58245&schemaId=1&rule=B4%20policies%20-%20labour%20standards
            {
                "name": "syllabus_based",
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__B3.*?Development and Training",
                    r"__regex__Responsible Service",
                ],
                "multi_elements": True,
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"as are not addressed in this ESG",
                        r"The Group took precautionary measures",
                    ),
                    "neglect_pattern": (),
                },
                "table_model": "special_cells",
                "table_config": {
                    "whole_table": True,
                    "cell_pattern": (r"Data Privacy"),
                },
            },
            {
                "name": "policy_row_match",
                "multi": True,
                "multi_elements": True,
                "row_pattern": ["^General Disclosure"],
                "anchor_pattern": ["Aspect B6"],
            },
            {
                # 只有第一段
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "only_first": True,
                "inject_syllabus_features": [
                    r"__regex__Product Responsibility__regex__General Disclosure",
                ],
            },
            {  # 特例
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "multi": True,
                "inject_syllabus_features": [
                    r"__regex__Product Quality Control",
                    r"__regex__service excellen",
                    r"__regex__Customer Health and Safety",
                    r"__regex__Data Protection and Privacy",
                ],
            },
            {
                # 章节标题和第一个小标题之间
                "name": "policy",
                # "only_before_first_chapter": True,
                # "only_first": True,
                "para_pattern": [
                    r"quality",
                    # r'Quality manage',
                    # r'service quality',
                    # r'Quality responsibility',
                ],
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "neglect_features": [
                    r"Labour standards",
                    r"community",
                    r"STAKEHOLDER ENGAGEMENT",
                    r"ESG Reporting Guide Content Index",
                    r"Supply Chain Management",
                    r"Protection of Intellectual Property Rights",
                ],
                "inject_syllabus_features": [
                    r"__regex__Quality Assurance",
                ]
                + B6_CHAPTER_PATTERN
                + [
                    r"__regex__PRODUCT RESPONSIBILITIES",
                    r"__regex__Aspect B6: Product Responsibility",
                    r"__regex__Product Quality Management",
                    r"__regex__Service pledge to our customer",
                ],
            },
            {
                # 不存在明显概括性文字的，可以框质量保证程序B6.4的段落，
                # 也可通过关键词定位质量保证内容段落，此篇如product liabilities，rectification， to ensure the  quality
                # 下面是B6.4的章节标题
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__Quality Control",
                    r"__regex__service excellen",
                    r"__regex__Product and Service Quality Management",
                ],
            },
            DEFAULT_MODEL,
        ],
    },
    {
        "path": ["B6 law compliance - product responsibility"],
        "models": [
            {
                "name": "para_match",
                "paragraph_pattern": LAW_COMMON_PATTERN,
                "neglect_syllabus_regs": [
                    r"Environmental Policy",
                    r"health and safety",
                    r"Supply chain management",
                    r"Anti-Corruption",
                    r"B[1-5]|B7|B8|A[1-3]",
                ],
            },
            {
                "name": "twice_para_match",
                "multi_elements": True,
                # "strict_limit": True,
                "syllabus_regs": B6_CHAPTER_PATTERN
                + [
                    r"HIGH QUALITY PRODUCTS",
                    r"Protection of Personal Data",
                ],
                "neglect_syllabus_regs": [
                    r"Environmental Policy",
                    r"health and safety",
                    r"Supply chain management",
                    r"Anti-Corruption",
                    r"B[1-5]|B7|B8|A[1-3]",
                ],
                "paragraph_pattern": COMPLY_LAW_PATTERN,
                "neglect_pattern": (r"complies with the corresponding national standards",),
                "second_pattern": (
                    r"relevant laws and regulations",
                    r"Product Responsibility",
                    r"responsible services",
                    r"high quality services",
                    r"meet the expectation",
                    r"satisfaction",
                    r"product responsibility",
                    r"product quality",
                    r"safety and health",
                    r"responsible",
                    r"data.*?(Ordinance|law)",
                    r"Ordinance",
                    r"intellectual property rights",
                    r"with the aforementioned laws and regulations",
                    r"sales.*?properties",
                    r"responsibility or privacy",
                ),
            },
            {
                "name": "para_match",
                "multi_elements": True,
                "paragraph_pattern": (
                    r"responsible to comply with the requirements of the rules and regulations",
                    r"no material non-compliance.*data protection and privacy",
                    r"not involve in any confirmed violations of laws and regulations",
                    r"Product Eco-responsibility Ordinance",
                    r"Consumer Goods Safety Ordinance",
                    r"B6.*?are not the key material matters.*?are not addressed in this ESG report",
                ),
            },
            {
                "name": "kmeans_classification",
                "threshold": 0.1,
            },
        ],
    },
    {
        # 关于出于安全和健康原因而进行召回的已售或运输产品的数据的披露，可提取涉product recall的数据所在的图表或段落或句子，优先提取index，
        # 其次data summary，最后正文。
        # （关键词：recall，recovery，return）0或nil或no或not等否定描述是Y
        "path": ["KPI B6.1 - products recall"],
        "models": [
            {
                "name": "para_match",
                "syllabus_regs": [
                    r"product.*recall",
                    r"Healthy Gaming",
                    r"Product.*Respons",
                    r"Safe Meals",
                ],
                "paragraph_pattern": (
                    r"B6.*?are not the key material matters.*?are not addressed in this ESG report",
                    r"recall.*?reason",
                    r"recall.*product.*health and safety",
                    r"product recalls during the.*?Year",
                    r"In.*?period.*?product recall",
                    r"product.*?recall.*(?:concerns|reasons)",
                    r"no recall case",
                    r"not experience any material quality issues",
                    r"product labelling.*?not applicable",
                    r"\d.*?product returned",
                ),
            },
            {
                "name": "after_row_match",
                "first_cell": False,
                "row_pattern": [r"B6\.1"],
                "just_a_para": True,
                "multi_elements": True,
                "para_pattern": [r"recall|B6\.1|return"],
                "direct_answer_pattern": [
                    r"No product.*?recalls",
                    r"not applicable",
                    r"There were no recalls.*?products",
                ],
            },
            {
                "name": "row_match",
                "ignore_index_table": True,
                "first_cell": False,
                "syllabus_regs": [r"Governance Report"],
                "row_pattern": [
                    r"B6.1.*\d",
                    r"product.*?recall.*\d",
                    r"recall.*?product.*\d",
                ],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__Quality Assurance",
                ]
                + B6_CHAPTER_PATTERN,
                "multi_elements": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"B6.1",
                        r"not have( any)?.*?products.*?to recall",
                        r"no.*?products.*?to recall",
                        r"no recalled products",
                        r"no (goods|orders).*?to recalls for product quality",
                        # EXPLAIN_PATTERN,
                    ),
                    "neglect_pattern": (
                        r"so as to protect.*?any potential health and safety issue",
                        r"creating higher return",
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "twice_para_match",
                "strict_limit": True,
                "multi_elements": True,
                "syllabus_regs": [
                    r"product responsibility",
                ],
                "paragraph_pattern": (
                    r"recall|B6.1|return",
                    r"not have( any)?.*?products.*?to recall",
                    r"no.*?products.*?to recall",
                    r"no recalled products",
                    r"product returned",
                    # EXPLAIN_PATTERN,
                ),
                "neglect_pattern": (
                    r"so as to protect.*?any potential health and safety issue",
                    r"creating higher return",
                ),
                "second_pattern": (
                    EXPLAIN_PATTERN,
                    r"(had|was|were|is) (zero|\d+|no)",
                    r"not have (any)?",
                    r"not have( any)?.*?products.*?to recall",
                    r"no.*?products.*?to recall",
                    r"no recalled products",
                    r"product quality",
                ),
            },
            {
                "name": "score_filter",
                "threshold": 0.618,
            },
        ],
    },
    {
        # 收到的与产品和服务相关的投诉数量以及处理方式，优先提取index，其次data summary，最后正文。
        # （关键词：complaint, service-related complaints）0或nil或no或not等否定描述是Y。
        # 不仅要提取投诉数量，还要提取文中描述的处理投诉的方式措施。
        "path": ["KPI B6.2 - products related complaints"],
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/852#note_217847
        # "pick_answer_strategy": "all",
        "models": [
            {
                "name": "after_row_match",
                "first_cell": False,
                "row_pattern": [r"B6.2"],
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "multi_elements": True,
                "para_pattern": [
                    r"complaint handling |respond to complaint|Compliance Department",
                    r"no.*?complaints received",
                    r"not receive any material compliant.*?product quality",
                    r"not receive any compliant.*?product",
                    r"opinion.*?products",
                    r"\d+ complaint",
                ],
            },
            # 60943 https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/852#note_217847
            {
                "name": "row_match",
                "ignore_index_table": True,
                "row_pattern": [
                    r"B6.2",
                    r"Number of complaints",
                ],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__product\s?responsibility",
                    r"__regex__Service Quality",
                    r"__regex__(Product\s?Responsibility|Responsible\s?Services|Quality\s?Assurance)",
                ],
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "multi_elements": True,
                "multi": True,
                "multi_level": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"(?P<content>complaint|B6.2)",
                        r"complaint.*?handling |respond to complaint|Compliance Department",
                        r"no complaints received",
                        r"not receive any material compliant.*?product quality",
                        r"not receive any compliant.*?product",
                        r"opinion.*?products",
                        r"\d+ complaint",
                        EXPLAIN_PATTERN,
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__Handling of Customer Complaints",
                ],
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"(?P<content>complaint|B6.2)",
                    r"no complaints received",
                    r"not receive any material compliant.*?product quality",
                    r"not receive any compliant.*?product",
                    r"opinion.*?products",
                    r"\d+ complaint",
                    r"B6.*?are not the key material matters.*?are not addressed in this ESG report",
                    EXPLAIN_PATTERN,
                ),
            },
            # 兜底para_match没有匹配到的段落 阈值需要设置一个比较高的值
            {
                "name": "score_filter",
                "threshold": 0.618,
            },
        ],
    },
    {
        # B6.3：描述与遵守和保护知识产权有关的做法。
        # （关键词：intellectual property rights, intellectual property protection）
        "path": ["KPI B6.3 - IP rights protection"],
        "models": [
            {
                "name": "after_row_match",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "row_pattern": [r"B6.3"],
                "first_cell": False,
                "para_pattern": [r"intellectual property rights|intellectual property protection"],
                "direct_answer_pattern": [
                    r"not applicable",
                    r"The Group has established a Trademark Register which records the registration number",
                ],
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__(\d\.)*?\d\sIntellectual Property Rights$",
                    r"__regex__Intellectual Property.*?Rights$",
                ],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__Protection of Intellectual Property( Rights)?",
                    r"__regex__(Product\s?Responsibility|Responsible\s?Services)__regex__(Intellectual\s?Property\s?"
                    r"Rights|Intellectual\s?Property\s?Protection)",
                    r"__regex__intellectual\s?Property\s?Protection",
                    r"__regex__product\s?responsibility",
                    r"__regex__intellectual Property Rights",
                ],
                "multi_elements": True,
                "include_title": False,
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        # r'Copyright Ordinance', # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/852#note_218211
                        r"IP rights",
                        r"IP infringement",
                        r"intellectual property right",
                        r"intellectual property protection",
                        r"protect intellectual property and data and privacy",
                        r"protect.*?data and privacy",
                        r"protect.*?our data assets",
                        r"secures its intellectual property",
                        r"B6.3",
                        r"respects intellectual property",
                        r"Company Trademark|Intellectual Property",
                        # r'personal data.*?ordinance',  # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/852#note_218211
                        r"maintained our trademark portfolio",
                        r"prohibited to use the IP of the Group",
                        r"apply for the patents",
                        r"Patent Licensing Contract",
                        r"exercise the priority right to apply for the invalidity",
                        EXPLAIN_PATTERN,
                    ),
                    "neglect_pattern": (
                        r"not have physical products for sale",
                        r"laws.*?labelling",
                        r"ensure.*?complies.*?laws.*?intellectual property right",
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    # r'(?P<content>Copyright Ordinance)',# https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/852#note_218211
                    r"(?P<content>IP rights)",
                    r"(?P<content>intellectual property right)",
                    r"(?P<content>intellectual property protection)",
                    r"(?P<content>protect intellectual property and data and privacy)",
                    r"(?P<content>protect.*?data and privacy)",
                    r"(?P<content>protect.*?our data assets)",
                    r"(?P<content>secures its intellectual property)",
                    r"(?P<content>B6.3)",
                    r"(?P<content>respects intellectual property)",
                    r"(?P<content>Company Trademark|Intellectual Property)",
                    # r'(?P<content>personal data.*?ordinance)', # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/852#note_218211
                    r"(?P<content>maintained our trademark portfolio)",
                    r"(?P<content>prohibited to use the IP of the Group)",
                    r"B6.*?are not the key material matters.*?are not addressed in this ESG report",
                    EXPLAIN_PATTERN,
                ),
                "neglect_pattern": (
                    r"not have physical products for sale",
                    r"laws.*?labelling",
                    r"ensure.*?complies.*?laws.*?intellectual property right",
                ),
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__Intellectual Property Rights",
                ],
            },
            {
                "name": "row_match",
                "ignore_index_table": True,
                "row_pattern": [
                    r"B6.3",
                    r"Protection of\s?Intellectual Property Rights",
                ],
            },
            # DEFAULT_MODEL,
        ],
    },
    {
        # B6.4： 侧重于产品及服务质量保证的具体流程及方法措施。
        #  有的可能会具体到产品从原材料采购到产出的质量检测及要求的流程图。相对于B6policy的概括性文字会描述得具体很多，
        #  有的文章可能直接整段讲具体质量保证措施，无法明确区分**B6policy的概括性文字**和B6.4，那么B6和B6.4的内容也会重合
        #
        #  框选内容：**优先从index定位的位置去找以下内容**
        #  <当存在明显小标题quality assurance/control或者B6.4>时，直接框选该标题下所有内容，
        #  <当不存在明显小标题>或内容描述质控过程措施，优先框关键词关于质量控制的段落，如果没有则可以框选客户投诉complaint处理措施，
        #  产品召回recall处理措施
        #
        #  关键词quality, performance check, rectification, recall，compliant， B6.4, quality assurance
        "path": ["KPI B6.4 - quality assurance process"],
        "models": [
            # 章节全部 适用于有明显的小标题
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "break_para_pattern": [
                    r"Anti-corruption$",
                ],
                "inject_syllabus_features": [
                    r"__regex__B6.4",
                    r"__regex__Complaints Handling Procedures",
                    r"__regex__Quality Management",
                    r"__regex__PRODUCT AND SERVICE RESPONSIBILITY",
                    r"__regex__PRODUCT AND SERVICE QUALITY",
                    r"__regex__Product Quality Management and Control",
                    r"__regex__Quality Control and Product Warranty",
                    r"__regex__Quality Control",
                    r"__regex__Quality Assurance",
                    # r'__regex__PRODUCT RESPONSIBILIT',  # B6 大章节标题
                ],
            },
            {
                "name": "after_row_match",
                "row_pattern": [r"B6.4"],
                "para_pattern": [
                    r"quality|performance check|rectification|recall|compliant|B6.4",
                ],
            },
            # 仅仅第一段
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "only_first": True,
                "inject_syllabus_features": [
                    r"__regex__Product Responsibility__regex__KPI B6.4",
                ],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__Quality\s?and Safety\s?of\s?Services",
                    r"__regex__Quality Management",
                    r"__regex__PRODUCT RESPONSIBILIT",
                ],
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "multi_elements": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"quality|performance check|rectification|recall|compliant|B6.4",
                        r"advertisements are mainly based on word-of-mouth",
                        EXPLAIN_PATTERN,
                        r"Examples of|includes the following",
                        r"^\d+\.",
                        r"^•",
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"(?P<content>quality|performance check|rectification|recall|compliant|B6.4)",
                    r"B6.*?are not the key material matters.*?are not addressed in this ESG report",
                    EXPLAIN_PATTERN,
                ),
                "neglect_pattern": (r"(supplier|subcontractors).*?quality",),
            },
            DEFAULT_MODEL,
        ],
    },
    {
        # B6.5：描述消费者数据保护和隐私政策。
        # （关键词：privacy, personal data, confidentiality）；
        "path": ["KPI B6.5 - consumer data protection"],
        "models": [
            {
                "name": "after_row_match",
                "row_pattern": [r"B6.5"],
                "multi_elements": True,
                "para_pattern": [r"personal data|confidentiality|B6.5"],
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN
                + [
                    r"OPERATING PRACTICES – continued",
                ],
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__Customer Data Privacy",
                    r"__regex__CUSTOMER PRIVACY",
                    r"__regex__Customer Privacy Protection",
                    r"__regex__(Customer|Personal) Data protection",
                    r"__regex__Data Privacy protection",
                    r"__regex__Data Protection and Privacy",
                    r"__regex__information Security Management",
                    r"__regex__data protection and privacy policies",
                    r"__regex__Information Security and Privacy Protection",
                    r"__regex__Privacy Matters",
                    r"__regex__Protecting information security",
                    r"__regex__Privacy and Information Security",
                    r"__regex__Product Responsibility__regex__privacy",
                    r"__regex__Confidential Information",
                ],
                "break_para_pattern": [
                    r"Aspect B7: Anti-corruption",
                ],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__(Product\s?Responsibility|Responsible\s?Services)__regex__Privacy\s?Protection",
                    r"__regex__Data\s?Privacy\s?Protection",
                    r"__regex__B6.*?SERVICES RESPONSIBILITY",
                ],
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "multi_elements": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"data privacy|personal data|confidentiality|B6.5",
                        r"(?P<content>safeguard the information of the Group)",
                        r"(?P<content>collected only when it is necessary)",
                        EXPLAIN_PATTERN,
                    ),
                    "neglect_pattern": (r"had no non-compliance cases.*?on.*?data privacy",),
                },
                "table_model": "empty",
            },
            {
                "name": "para_match",
                "multi_elements": True,
                "paragraph_pattern": (
                    r"protect customers.*?privacy",
                    r"(?P<content>safeguard the information of the Group)",
                    r"(?P<content>collected only when it is necessary)",
                    r"contractual obligation to protect the information of clients",
                    r"B6.*?are not the key material matters.*?are not addressed in this ESG report",
                    r"ensure confidential information are properly kept",
                    EXPLAIN_PATTERN,
                ),
            },
            {
                "name": "row_match",
                "ignore_index_table": True,
                "row_pattern": [
                    r"Data Privacy",
                ],
            },
            {
                "name": "kmeans_classification",
                "threshold": 0.618,
            },
        ],
    },
    {
        # B7-policy：关于anti-corruption政策的披露，可提取涉及到anti-corruption政策下的所有内容，一般在anti-corruption相关的标题下。
        # （关键词：anti-corruption，anti-money laundering）
        #       1. 情况一：在anti-corruption大标题及下一个小标题之间有内容，则建议框选两个标题中间这段内容；
        #
        #       2. 情况二：在anti-corruption大标题下没有任何一个小标题，则建议框选标题下第一段内容；
        #
        #       3. 情况三：在anti-corruption大标题及下一个小标题之间没有内容，则建议框选大标题下全部内容。
        "path": ["B7 policies - anti-corruption"],
        "models": [
            # 特例 GRI 相关 https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/997#note_243521
            {
                "name": "syllabus_elt_v2",
                "only_inject_features": True,
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "inject_syllabus_features": [
                    r"__regex__FOCUS 1: GOVERNANCE AND ETHICS__regex__Anti-Corruption",
                ],
            },
            {
                # 章节标题和第一个小标题之间
                "name": "policy",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "neglect_features": [
                    r"Supply Chain Management",
                ],
                "ignore_pattern_match": True,
                "inject_syllabus_features": [
                    r"__regex__ANTI-CORRUPTION POLICY AND WHISTLEBLOWER PROCEDURE",
                    r"__regex__Business Integrity, Anti-Corruption and Anti-Money Laundering",
                ],
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "only_first": True,
                "only_before_first_chapter": True,
                "inject_syllabus_features": [
                    r"__regex__Anti-Corruption__regex__KPI B7",
                ],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__Anti-Corruption",
                ],
                "multi_elements": True,
                "only_before_first_chapter": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"(?P<content>regards.*?laws)",
                        r"(?P<content>comply.*?laws)",
                        r"(?P<content>no corruption-related violations)",
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "neglect_features": [
                    r"Supply Chain Management",
                ],
                "inject_syllabus_features": [
                    r"__regex__Ethical Business",
                ],
                # 'only_before_first_chapter': True,
                # 'only_first': True,
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"anti-corruption policy",
                    r"(?P<content>regards.*?laws)",
                    r"avoidance of bribery and corruption",
                    r"Group has established policies on anti-corruption",
                    r"policy.*?(money[\-\s]laundering|anti[\-\s]corruption)",
                    r"comply.*?(money[\-\s]laundering|anti[\-\s]corruption)",
                ),
            },
            DEFAULT_MODEL,
        ],
    },
    {
        "path": ["B7 law compliance - anti-corruption"],
        "models": [
            {
                "name": "para_match",
                "paragraph_pattern": LAW_COMMON_PATTERN,
                "neglect_syllabus_regs": [
                    r"IMPROvE OuR vALuE CHAIN",
                    r"Product Health and Safety",
                    r"Labou?r Standards?",
                    r"Data Privacy",
                    r"B[1-6]|B8|A[1-3]",
                ],
            },
            {
                "name": "twice_para_match",
                "multi_elements": True,
                # "strict_limit": True,
                "syllabus_regs": [],
                "neglect_syllabus_regs": [
                    r"IMPROvE OuR vALuE CHAIN",
                    r"Product Health and Safety",
                    r"Labou?r Standards?",
                    r"Data Privacy",
                    r"B[1-6]|B8|A[1-3]",
                ],
                "paragraph_pattern": COMPLY_LAW_PATTERN,
                # 'neglect_pattern': (),
                "second_pattern": (
                    r"Anti-corruption",
                    r"Anti-money laundering",
                    r"Prohibition of Commercial Bribery",
                    r"bribery",
                ),
            },
            {
                "name": "para_match",
                "multi_elements": True,  # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/793#note_210675
                "strict_limit": True,  # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/793#note_210675
                "syllabus_regs": [r"ANTI-CORRUPTION"],
                "neglect_syllabus_regs": [
                    r"corporate governance report",
                    r"Product Health and Safety",
                    r"Labou?r Standards?",
                    r"Data Privacy",
                    r"B[1-6]|B8|A[1-3]",
                ],
                "paragraph_pattern": (
                    r"There was no legal cases regarding corrupt practices",
                    r"During the Period, there are no legal cases",
                    r"(?P<content>not aware.*?money laundering)",
                    r"(?P<content>strictly comply with.*?law)",
                    r"complied with relevant laws and regulations",
                    r"(law|regulation|Ordinance).*?(money|anti[-\s]|Bribery)",
                    r"(money|anti[-\s]|Bribery).*?(law|regulation|Ordinance)",
                    r"no.*?violation.*?corruption",
                    r"no legal case.*?corruption",
                ),
                "neglect_pattern": (r"^—.*?Ordinance",),
            },
            {
                "name": "kmeans_classification",
                "threshold": 0.1,
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__Integrity and Discipline",
                    r"__regex__Anti-Corruption",
                ],
                "multi_elements": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"(?P<content>not aware.*?money laundering)",
                        r"(?P<content>no corruption-related violations)",
                        r"no.*?in violation of.*?(laws|regulations).*?corruption lawsuits",
                        r"implemented the Whistleblowing Policy",
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "score_filter",
                "threshold": 0.618,
            },
        ],
    },
    {
        # 报告期内对发行人或其雇员提起的以身结的腐败行为法律案件数量，优先index，其次data summary，最后正文。
        # （关键词：bribery，legal，case，lawsuit，corruption charges）
        #       1. 0或nil或no或not等否定描述是Y；
        #       2. 当描述中没有涉及到case或lawsuit，仅有没有违反法规的相关描述（e.g. no non-compliance)，应判断为Y。
        "path": ["KPI B7.1 - legal cases on corruption"],
        "neglect_table_cell_missing_crude_regs": [r"Health and Safety"],
        "models": [
            {
                "name": "after_row_match",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "just_a_para": True,
                "include_row": True,
                "row_pattern": [r"B7.1"],
                "direct_answer_pattern": [
                    r"no (concluded )?legal case",
                ],
                "para_pattern": [
                    r"not involved.*?corruption",
                    r"not aware of any non-compliance with the relevant laws and regulations",
                    r"no (concluded )?legal case",
                    r"not aware of.*?cases",
                ],
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"no serious breach or non-compliance with relevant laws",
                    r"No reports or related complaints",
                    r"material impact on the Company in terms of anti-corruption",
                    r"not notify any material non-compliance with the relevant laws",
                    r"concluded legal case",
                    r"no legal case",
                    r"not identified.*?anti-bribery",
                    r"not have any legal.*?corruption",
                    r"no confirmed.*?legal case",
                    r"not aware of.*?cases",
                    r"have not involved.*?legal ",
                    r"No corruption charges",
                    r"no legal.*?corruption",
                    r"no reported case of corruption",
                    r"no.*?activities occurr",
                    r"not identified any.*?cases of",
                    r"During the Period, there are no legal cases",
                    r"not identify any material non-compliance cases",
                    r"had no non-compliance cases regarding violations",
                    r"subsidiaries experienced no litigation brought against",
                    r"involved in anti-corruption litigation cases",
                    r"no material non-compliance with the relevant laws",
                    r"nor violation of regulations related to corruption",
                    r"no corruption lawsuits",
                ),
            },
            {
                "name": "row_match",
                "ignore_index_table": True,
                "first_cell": True,
                "row_pattern": [
                    r"legal cases",
                    r"Number.*?corruption",
                ],
            },
            {
                "name": "ar_esg_71",
                "threshold": 0.1,
            },
        ],
    },
    {
        # 描述预防措施和举报程序，以及如何实施和监管。
        # （关键词：whistle-blowing, whistleblowing, misconduct, preventive, prevention, prevent）
        # reporting channels
        "path": ["KPI B7.2 - preventive measures & whistle-blowing procedures"],
        "models": [
            {
                "name": "para_as_index",
                "only_inject_features": True,
                "inject_syllabus_features": [
                    r"__regex__Complaint\s*?handling",
                    r"__regex__satisfaction\s*?and\s*?feedback",
                ],
                "index_patterns": [
                    r"grievance procedures?.*refer\s*?to.*section.*?[\"“\(\[\‘](?P<dst>.+?)[\"”’\)\]]",  # 01752|2022
                ],
                "para_patterns": [
                    r"grievance\s*?polic(y|ies)",
                ],
            },
            {
                "name": "para_match",
                "multi_elements": True,
                # "combine_paragraphs": True,
                "paragraph_pattern": (
                    r"To prevent corrupt practices ",
                    r"Whistle-?blowing Mechanism",
                    r"reporting channels",
                    r"whistle-blowing.*?In the event.*?investigation",
                ),
            },
            {
                "name": "syllabus_elt_v2",
                "only_inject_features": True,
                "multi": True,
                "inject_syllabus_features": [
                    r"__regex__Anti-Corruption__regex__KPI B7.2",
                    r"__regex__Customer Due Diligence",
                    r"__regex__Suspicious Transactions Reporting",
                    r"__regex__Gifts and benefits",
                    r"__regex__GOVERNANCE AND ETHICS__regex__Whistleblowing",
                ],
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
            },
            {
                "name": "after_row_match",
                "row_pattern": [r"B7.2"],
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "para_pattern": [
                    r"whistle.*?blowing|misconduct|B7.2",
                    r"(?P<content>not tolerate any.*?business activities)",
                    r"reporting channels",
                ],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__Customer Due Diligence",
                    r"__regex__ANTI-CORRUPTION POLICY AND WHISTLEBLOWER PROCEDURE",
                ],
                "multi_elements": True,
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"(?P<content>whistle.*?blowing|misconduct|B7.2)",
                        r"(?P<content>not tolerate any.*?business activities)",
                        r"encouraged to report on suspected business irregularities",
                        r"misconduct|prevention|preventive|prevent",
                        r"Whistle-?blowing",
                        r"reporting mechanism",
                        r"reporting channels",
                    ),
                },
                "table_model": "special_cells",
                "table_config": {
                    "cell_pattern": [
                        r"whistle-blowing|misconduct",
                        r"reporting channels",
                    ]
                },
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"(?P<content>whistle.*?blowing|misconduct|B7.2)",
                    r"(?P<content>not tolerate any.*?business activities)",
                    r"(?P<content>violate the company’s rules, we will directly dismiss the employee)",
                    r"reporting channels",
                ),
            },
            {
                "name": "kmeans_classification",
                "para_pattern": [
                    r"whistle.*?blowing|misconduct|B7.2",
                    r"(?P<content>not tolerate any.*?business activities)",
                    r"reporting channels",
                ],
            },
            DEFAULT_MODEL,
        ],
    },
    {
        # 描述向董事和员工提供的反腐败培训，提取涉及到anti-corruption training相关的段落，
        # 有时候会出现在B3的相关段落下，也可提取（关键词：anti-corruption，training，seminar
        # 没有明确表明提供相关training, 只是说会把相关的政策告知employee，不属于training相关，应判断为ND
        "path": ["KPI B7.3 - anti-corruption training"],
        "models": [
            {
                "name": "para_match",
                "syllabus_regs": [
                    r"anti.*?corruption$",  # 01752|2022
                ],
                "paragraph_pattern": (
                    r"anti.*?corruption.*?training",  # 01752|2022
                    r"training.*?anti.*?corruption",
                ),
            },
            {
                "name": "after_row_match",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "just_a_para": True,
                "row_pattern": [r"B7.3"],
                "para_pattern": [
                    r"anti.*?corruption.*?training|B7.3",
                    r"training",
                    r"(?<!terrorism )seminar",
                ],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__Anti-corruption",
                    r"__regex__Anti-corruption Training",
                    r"__regex__Anti-corruption and Integrity",
                    r"__regex__Prevention of bribery",
                    r"__regex__money laundering",
                    r"__regex__other misconduct",
                    r"__regex__Bribery",
                ],
                "multi_elements": True,
                "include_title": False,
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"anti.*?corruption.*?training|B7.3",
                        r"anti.*?corruption.*?(training|seminar)",
                        r"(training|seminar).*?anti.*?corruption",
                    ),
                },
                "table_model": "special_cells",
                "table_config": {
                    "cell_pattern": [
                        r"whistle-blowing|misconduct",
                    ]
                },
            },
            {
                "name": "para_match",
                "strict_limit": True,
                "syllabus_regs": [
                    r"ANTI-CORRUPTION",
                    r"Integrity",
                    r"Business Ethics",
                    r"Enhance Compliance Awareness",
                    r"__regex__money laundering",
                    r"__regex__other misconduct",
                    r"__regex__Bribery",
                ],
                "multi_elements": True,  # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/803#note_212983
                "paragraph_pattern": (
                    r"anti.*?corruption.*?training|B7.3",
                    r"training.*?anti.*?corruption",
                    r"training|seminar",
                    r"study.*?anti-corruption",
                    r"sign.*?Compliance.*?Commitment",
                    r"sent.*?anti-corruption.*?articles",
                ),
            },
            {
                "name": "row_match",
                "first_cell": True,
                "row_pattern": [
                    r"anti-corruption.*?training",
                    r"training.*?anti-corruption",
                ],
            },
        ],
    },
    {
        # 社区参与相关的政策，以了解发行人经营所在社区的需求，并确保其活动考虑到社区的利益。
        # （关键词：community）
        # 一般在community investment/public welfare/Giving back to society/the community相关标题下的第一个段落描述，
        # 或第一个小标题下内容。E描述不常见。
        "path": ["B8 policies - community investment"],
        "models": [
            # 特例 GRI 相关 https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/997#note_243521
            {
                "name": "syllabus_based",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "inject_syllabus_features": [
                    r"__regex__FOCUS 5: OUR COMMUNITY",
                ],
                "multi_elements": True,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"engagement|focus|key|dedicate|B8.1|community",
                        r"giving back to society",
                        r"inculcate greater environmental awareness",
                        r"GRI 413-1",
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "policy",
                "only_inject_features": True,
                "ignore_pattern_match": True,
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "inject_syllabus_features": [
                    r"__regex__Community Investment__regex__General Disclosure and KPIs",
                    r"__regex__Aspect B8",
                    r"__regex__COMMUNITY INVESTMENT",
                ],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__Aspect B8: Community Investment",
                    r"__regex__COMMUNITY INVESTMENT",
                ],
                "multi_elements": True,
                "only_before_first_chapter": True,
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"(?P<content>engagement|focus|key|dedicate|commit|B8.1|community)",
                        r"(?P<content>encouraging.*?communities.)",
                    ),
                },
                "table_model": "empty",
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"(?P<content>engagement|focus|key|dedicate|commit|B8.1|community)",
                    r"(?P<content>encourag.*?(communit|society))",
                ),
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
            },
            DEFAULT_MODEL,
        ],
    },
    {
        # 公司重点在哪些领域做贡献，例如：education, environmental concerns, labour needs, health, culture, sport。
        # （关键词：engagement, focus, key, dedicate，commit）如果没有描述重点领域，可框选B8.2的内容
        "path": ["KPI B8.1 - community investment focus"],
        "models": [
            {
                "name": "after_row_match",
                "row_pattern": [r"B8.1"],
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "para_pattern": [
                    r"engagement|focus|key|dedicate|B8.1|community",
                    r"community development",
                    r"community investment",
                    r"(?P<content>encouraging.*?communities)",
                ],
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "multi": True,
                "inject_syllabus_features": [
                    r"__regex__Community Investment__regex__KPI B8\.1",
                    r"__regex__Community Engagement",
                ],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__kpi B8.1",
                    r"__regex__COMMUNITY INVESTMENT",
                ],
                "multi_elements": True,
                "break_when_table": True,
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"engagement|focus|key|dedicate|commit|B8.1",
                        r"(?P<content>encouraging.*?communities)",
                        r"(?P<content>made donations)",
                        r"(?P<content>contribute to the society)",
                        r"for social responsibilities purposes",
                        r"distribute gifts and coupons",
                        r"donat.*?to",
                        r"made great efforts in education",
                        r"education|environmental concerns|labour needs|health|culture|sport",
                        r"contribute to our community",
                        r"support community programmes",
                    ),
                },
                "table_model": "table_title",
                "table_config": {
                    "only_inject_features": True,
                    "feature_white_list": [
                        r"made donations",
                        r"捐贈",
                    ],
                },
            },
            {
                "name": "para_match",
                "paragraph_pattern": (
                    r"(?P<content>engagement|focus|key|dedicate|commit|B8.1|community)",
                    r"(?P<content>encourag.*?(communit|society))",
                ),
            },
            DEFAULT_MODEL,
        ],
    },
    {
        # 为重点领域贡献的资源（例如金钱或时间），优先提取index，再到data summary，最后正文。
        # （关键词：community，donation，prepare，provide, distribute）。
        # 表示未来计划会关注或投入community的描述，属于E（in the future, plan等），
        # 其他属于E的关键词还有stop, suspended, did not engage等
        "path": ["KPI B8.2 - resources contributed"],
        "models": [
            {
                "name": "after_row_match",
                "row_pattern": [r"B8.2"],
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "para_pattern": [
                    r"(?P<content>donation|prepare|provide|distribute|dedicate|B8.2)",
                    r"(?P<content>prepare|provide|distribute)",
                ],
                "middle_rows": True,
                "start_regs": [r"B8.2"],
                "end_regs": [],
            },
            {
                "name": "syllabus_elt_v2",
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "only_inject_features": True,
                "break_when_table": True,
                "multi": True,
                "inject_syllabus_features": [
                    r"__regex__Community Investment__regex__KPI B8\.2",
                ],
            },
            {
                "name": "syllabus_based",
                "inject_syllabus_features": [
                    r"__regex__kpi B8.2",
                    r"__regex__COMMUNITY INVESTMENT",
                ],
                "multi_elements": True,
                "break_when_table": True,
                "ignore_pattern": NEGLECT_PAGE_HEADER_PATTERN,
                "paragraph_model": "para_match",
                "para_config": {
                    "paragraph_pattern": (
                        r"(?P<content>donation|provide|distribute|dedicate|B8.2)",
                        r"(?P<content>made donations)",
                        r"(?P<content>cash donations)",
                        r"donat.*?to",
                        r"for social responsibilities purposes",
                        r"distribute gifts and coupons",
                        r"participated in",
                        r"delivered to the needy",
                        r"\$[\d,]+",
                        r"contribute to our community",
                        r"support community programmes",
                        # r'(?P<content>contribution)',  # 遇到badcase 添加更详细的正则
                        # r'(?P<content>prepare)',  # 遇到badcase 添加更详细的正则
                    ),
                },
                "table_model": "table_title",
                "table_config": {
                    "only_inject_features": True,
                    "feature_white_list": [
                        r"made donations",
                        r"捐贈",
                    ],
                },
            },
            {
                "name": "para_match",
                # 'multi': True,
                "paragraph_pattern": (
                    r"\$[\d,]+",
                    r"(?P<content>community|donation|prepare|provide|distribute|B8.2)",
                    r"(?P<content>contribution)",
                    r"(?P<content>prepare|provide|distribute)",
                    r"(?P<content>cash donations)",
                ),
            },
            DEFAULT_MODEL,
        ],
    },
]

prophet_config = {"depends": {}, "predictor_options": predictor_options}
