from remarkable.common.util import clean_txt
from remarkable.predictor.predictor import JudgeByRegex
from remarkable.predictor.utils import GPTDictator


class PollResultEnumChecker(JudgeByRegex):
    col_patterns = {
        "P3-shares of holders required to abstain from voting": {
            "Negative Statement": [r"(?<!Save as disclosed above, )There (was|were|is) no shares.*abstain"],
            "Positive Statement": [],
        },
    }

    def predict(self, predictor_result, schema, metadata=None):
        schema_name = schema.parent.name if schema.name == "Content" else schema.name
        if (pattern := self.col_patterns.get(schema_name)) and isinstance(pattern, GPTDictator):
            return pattern.judge(clean_txt(predictor_result.text, remove_blank=self.remove_blank))
        return super().predict(predictor_result, schema, metadata)
