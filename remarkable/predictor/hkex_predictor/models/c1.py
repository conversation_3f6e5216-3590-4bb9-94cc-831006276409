from remarkable.common.common import is_table_elt
from remarkable.common.common_pattern import R_CHAPTER_PREFIX
from remarkable.common.constants import AnswerV<PERSON>ueEnum, PDFInsightClassEnum
from remarkable.common.pattern import MatchMulti
from remarkable.common.util import clean_txt, split_paragraph
from remarkable.pdfinsight.reader_util import find_real_syllabus
from remarkable.predictor.hkex_predictor.schemas.pattern import R_NOT
from remarkable.predictor.models.base_model import BaseModel

P_NO_PURCHASE = MatchMulti.compile(
    MatchMulti.compile(
        rf"{R_NOT}(?!gain|loss|more)", r"\b(re)?(purchase|rede?em|bought|buy\b)", r"sale|sold|sell", operator=all
    ),
    # # http://100.64.0.105:55647/#/project/remark/266309?treeId=4016&fileId=70476&schemaId=18&projectId=17&schemaKey=C1
    # MatchMulti.compile(R_NOT, r"\bpurchase", r"sale|sold|sell", r"repurchase", operator=all),
    operator=any,
)
P_CHAPTER_PURCHASE_SALE = MatchMulti.compile(
    rf"{R_CHAPTER_PREFIX}((shares?\s*)?(r\s*e\s*)?p\s*u\s*r\s*c\s*h\s*a\s*s\s*e|buy)",
    r"\bs\s*a\s*l\s*e",
    # r"r\s*e\s*d\s*e\s*e?\s*m|repurchase",
    operator=all,
)
# https://100.64.0.105:55647/#/project/remark/294954?treeId=3688&fileId=70845&schemaId=18&projectId=17&schemaKey=C1
# http://100.64.0.105:55647/#/project/remark/269016?treeId=2687&fileId=70622&schemaId=18&projectId=17&schemaKey=C1
P_CHAPTER_SHARE_CAPITAL = MatchMulti.compile(rf"{R_CHAPTER_PREFIX}share\s*capital$", operator=any)
# http://100.64.0.105:55647/#/project/remark/266374?treeId=2530&fileId=70489&schemaId=18&projectId=17&schemaKey=C1
# http://100.64.0.105:55647/#/project/remark/266274?treeId=13539&fileId=70469&schemaId=18&projectId=17&schemaKey=C1   headed
P_C1_SAVE_FOR = MatchMulti.compile(
    r"(save|except)\s*(for|as)\s*(disclosed|above|afore|the\s*(re)?purchase)", operator=any
)
# acquired: http://100.64.0.105:55647/#/project/remark/294692?treeId=12752&fileId=70812&schemaId=18&projectId=17&schemaKey=C1
# 排除paid new shares: https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6839#note_705696
R_PURCHASE = r"\b((re)?purchase|acquire|paid(?!\s*new\s*shares)|bought)"
R_SHARES = r"\d(\s+[（(][^(（]+?[)）])?\s*([a-z]+\s+){0,3}(?<!new )shares\b"
P_PURCHASE = MatchMulti.compile(rf"{R_PURCHASE}|stock\s*exchange", R_SHARES, operator=all)
P_SHARES = MatchMulti.compile(r"(?<!new|per)\sshares?\b", operator=any)


class C1NS(BaseModel):
    """
    https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/5916
    规则C1的NS条件：
    1. 在purchase, sale or redemption章节下，且章节下有且仅有1个元素块; 或者有多个元素块，但是没有提及回购事项
    2. 元素块内容为否定描述，not purchase, sale or redeem any ...
    3. 结果中必须包含章节标题
    """

    @property
    def enum(self):
        return AnswerValueEnum.NS.value

    @property
    def extend_candidates_by_enum(self):
        return True

    def train(self, dataset, **kwargs):
        pass

    def find_ns_elements(self, elements, syllabus) -> []:
        ns_elements = []
        has_ns, has_save_for, has_shares = False, False, False
        for element in elements:
            if is_table_elt(element) or self.pdfinsight.is_syllabus_title(element):
                break
            text = clean_txt(element["text"], remove_cn_text=True)
            for sentence in split_paragraph(text):
                if P_PURCHASE.search(sentence):
                    # 有明确购买描述，不提NS
                    return []
                if not has_ns and P_NO_PURCHASE.search(sentence):
                    has_ns = True
                    if len(elements) == 1:
                        _, start_element = self.pdfinsight.find_element_by_index(syllabus["element"])
                        ns_elements = [start_element, element]
                    else:
                        ns_elements = [element]
                if not has_shares:
                    has_shares = P_SHARES.search(sentence)
        if not ns_elements:
            return []
        # 章节中有share关键词，且NS描述中有save for，则不能直接判定NS，需要先找NS
        if has_save_for and has_shares:
            return []
        return ns_elements

    def predict_schema_answer(self, elements):
        """
        以下都不是NS：
        # http://100.64.0.105:55647/#/project/remark/268427?treeId=11140&fileId=70538&schemaId=18&projectId=17&schemaKey=C1
        # http://100.64.0.105:55647/#/project/remark/266109?treeId=7506&fileId=70436&schemaId=18&projectId=17&schemaKey=C1
        # 误识别为多行： http://100.64.0.105:55647/#/project/remark/293850?treeId=7899&fileId=70689&schemaId=18&projectId=17&schemaKey=C1
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/5916#note_639041
        """
        found_chapter = False
        result_elements = []
        for syllabus in self.pdfinsight.syllabus_reader.syllabuses:
            syllabus = find_real_syllabus(self.pdfinsight, syllabus)
            if not P_CHAPTER_PURCHASE_SALE.search(syllabus["title"]):
                continue
            found_chapter = True
            candidate_elements = self.get_candidate_elements_by_range(syllabus["range"], need_score=False)
            result_elements = self.find_ns_elements(candidate_elements, syllabus)
            if result_elements:
                break
        if not found_chapter:
            # 找share capital章节
            for syllabus in self.pdfinsight.syllabus_reader.syllabuses:
                if not P_CHAPTER_SHARE_CAPITAL.search(syllabus["title"]):
                    continue
                for element in self.get_candidate_elements_by_range(
                    syllabus["range"], aim_types={PDFInsightClassEnum.PARAGRAPH.value}
                ):
                    text = clean_txt(element["text"], remove_cn_text=True)
                    if P_NO_PURCHASE.search(text):
                        if not P_C1_SAVE_FOR.search(text):
                            result_elements.append(element)
                        break
        if result_elements:
            return [
                self.create_result(
                    self.create_multi_outline_results(result_elements), schema=self.schema, value=self.enum
                )
            ]
        return []
