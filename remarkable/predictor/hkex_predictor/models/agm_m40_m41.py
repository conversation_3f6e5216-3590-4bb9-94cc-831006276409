import logging
import re

from remarkable.common.common import clean_director_name, rotate_values
from remarkable.common.common_pattern import R_MIDDLE_DASHES
from remarkable.common.constants import AnswerValueEnum, TableType
from remarkable.common.pattern import Match<PERSON>ult<PERSON>, PatternCollection
from remarkable.common.util import clean_txt
from remarkable.pdfinsight.parser import parse_table
from remarkable.predictor.models.base_model import BaseModel
from remarkable.predictor.schema_answer import ParagraphResult

logger = logging.getLogger(__name__)


class AGMM4X(BaseModel):
    P_RESELECT = MatchMulti.compile(r"\b(to|be)[\s-]*re[\s-]*elect?", operator=any)
    COLON = (":", "：")
    P_START_ELEMENT = MatchMulti.compile(r"\bre[\s-]*elect?\b", r"\bfollowing\b", r"[:：]$", operator=all)
    P_ORDINARY = MatchMulti.compile(r"^ordinary (resolutions?|business(es)?)$", operator=any)

    SAME_NAMES_MAPPING = {
        "CHEONG": "(CHEONG|CHEUNG)",
        "KARL": "(KARL|K.)",
        "SO": "(SO|SU)",
    }

    def train(self, dataset, **kwargs):
        pass

    @classmethod
    def convert_same_name(cls, en_names):
        for index, name in enumerate(en_names):
            if name.upper() in cls.SAME_NAMES_MAPPING:
                en_names[index] = cls.SAME_NAMES_MAPPING[name.upper()]
        return en_names

    @classmethod
    def build_director_name_patterns(cls, directors):
        director_names = []
        if not directors:
            return director_names
        for director in directors:
            d_names = [re.escape(director.chinese_name)] if director.chinese_name else []
            en_names = clean_director_name(director.english_name).split()
            en_names = cls.convert_same_name(en_names)
            d_names.extend(rotate_values(en_names, key=rf"[{R_MIDDLE_DASHES},\s]{{0,2}}"))
            d_names.extend(cls.add_prefix_name(en_names))
            director_names.append(
                (director.english_name, PatternCollection(d_names, re.I), MatchMulti.compile(*en_names, operator=all))
            )
        return director_names

    @staticmethod
    def add_prefix_name(names):
        prefix_names = []
        for name in names:
            prefix_names.append(rf"(Mr|Ms|Dr|Prof|Mdm|Mass|Mrs)\.\s*{name}")
        return prefix_names

    @staticmethod
    def find_director_element(element, name_patterns, found_mapping):
        element_found = False
        for english_name, d_names, en_names in name_patterns:
            if found_mapping.get(english_name):
                continue
            if d_names.nexts(element["text"]) or en_names.search(element["text"]):
                found_mapping[english_name] = True
                element_found = True
        if element_found:
            return ParagraphResult(element, element["chars"])


class AGMM40(AGMM4X):
    def train(self, dataset, **kwargs):
        pass

    def predict_schema_answer(self, elements):
        """
        先看有没有new directors ,这个逻辑跟director-list界面是一样的
        1.如果没有new directors --> ND
        2.如果有new directors，则需要找到每一个director 对应 `在本次AGM中重选的描述`的描述
        3.如果全部披露--> ps
        4.部分披露 --> nd
        """
        new_directors = self.predictor.prophet.metadata.get("new_director")
        found_mapping = {director.english_name: False for director in new_directors}

        results = []
        if not new_directors:
            logger.info("no new directors")
            return results

        name_patterns = self.build_director_name_patterns(new_directors)
        if not name_patterns:
            logger.info("no new name_patterns")
            return results

        start_element = None
        re_select_element = None
        exists_element_indexes = set()
        for element in elements:
            if element["index"] in exists_element_indexes:
                continue
            if element["class"] == "PARAGRAPH":
                if not self.P_RESELECT.search(element["text"]):
                    continue
                re_select_element = element
                if self.P_START_ELEMENT.search(element["text"]):
                    start_element = element
                    results.append(ParagraphResult(start_element, start_element["chars"]))
                    exists_element_indexes.add(element["index"])
                    break
                if name_element := self.find_director_element(element, name_patterns, found_mapping):
                    results.append(name_element)
                    exists_element_indexes.add(element["index"])
        if not start_element:
            syllabus = self.pdfinsight.find_sylls_by_pattern([self.P_ORDINARY])
            if syllabus:
                if start_elements := self.pdfinsight.find_paragraphs_by_pattern(
                    [self.P_START_ELEMENT], syllabus[-1]["range"][0], syllabus[-1]["range"][1], remove_blank=False
                ):
                    start_element = start_elements[0]
        if start_element:
            elements = self.find_blow_paras(start_element)
            for element in elements:
                if element["index"] in exists_element_indexes:
                    continue
                if name_element := self.find_director_element(element, name_patterns, found_mapping):
                    exists_element_indexes.add(element["index"])
                    results.append(name_element)
        if all(not value for value in found_mapping.values()):
            if re_select_element:
                if table_element := self.pdfinsight.find_elements_near_by(
                    re_select_element["index"], aim_types=["TABLE"]
                ):
                    table = parse_table(table_element[0], tabletype=TableType.TUPLE, pdfinsight_reader=self.pdfinsight)
                    for footnote in table.footnotes:
                        if name_element := self.find_director_element(footnote, name_patterns, found_mapping):
                            results.append(name_element)
                results.append(ParagraphResult(re_select_element, re_select_element["chars"]))
            if all(not value for value in found_mapping.values()):
                logger.info("not found new director in the file")
                return [self.create_result([], schema=self.schema, value=AnswerValueEnum.ND.value)]
        results = sorted(results, key=lambda x: x.element["index"])
        value = AnswerValueEnum.ND.value
        if all(value for value in found_mapping.values()):
            value = AnswerValueEnum.PS.value
        return [self.create_result([result], schema=self.schema, value=value) for result in results]


class AGMM41SeparateResolution(AGMM4X):
    P_SEPARATE = MatchMulti.compile(r"separate resolution", operator=any)
    P_EACH_SEPARATE_RESOLUTION = MatchMulti.compile(r"each as a separate resolution", operator=any)

    def train(self, dataset, **kwargs):
        pass

    def get_director_names(self, elements, name_patterns, found_mapping):
        """提取董事名字"""
        start_element_results = []
        name_results = []
        start_element = None
        name_element_index = None
        re_select_element = None
        for element in elements:
            if element["class"] == "TABLE":
                pass
            if element["class"] == "PARAGRAPH":
                if not self.P_RESELECT.search(element["text"]):
                    continue
                re_select_element = element
                if element["text"].endswith(self.COLON):
                    start_element = element
                    start_element_results.append(ParagraphResult(start_element, start_element["chars"]))
                    break
                if name_element := self.find_director_element(element, name_patterns, found_mapping):
                    name_results.append(name_element)
                    name_element_index = name_element.element["index"]
        if start_element:
            elements = self.find_blow_paras(start_element)
            for element in elements:
                if name_element := self.find_director_element(element, name_patterns, found_mapping):
                    name_results.append(name_element)
                    name_element_index = name_element.element["index"]
        if name_results:
            results = start_element_results + name_results
            return results, name_element_index
        elif re_select_element:
            results = []
            if table_element := self.pdfinsight.find_elements_near_by(re_select_element["index"], aim_types=["TABLE"]):
                table = parse_table(table_element[0], tabletype=TableType.TUPLE, pdfinsight_reader=self.pdfinsight)
                for footnote in table.footnotes:
                    if name_element := self.find_director_element(footnote, name_patterns, found_mapping):
                        results.append(name_element)
                        name_element_index = name_element.element["index"]
            results.append(ParagraphResult(re_select_element, re_select_element["chars"]))
            return results, name_element_index
        return [], name_element_index

    def predict_schema_answer(self, elements):
        """
        首先提取任职9年以上的Director
        如果没有 --> ND
        如果有，去文档里面提取Director是INED，且满足're-elect'的名字
        """
        nine_year_directors = self.predictor.prophet.metadata.get("nine_year_director")
        found_mapping = {director.english_name: False for director in nine_year_directors}
        results = []
        if not nine_year_directors:
            # 没有INED任职9年及以上情况
            return results
        name_patterns = self.build_director_name_patterns(nine_year_directors)
        if not name_patterns:
            return results

        # 提取董事名字
        results, name_element_index = self.get_director_names(elements, name_patterns, found_mapping)
        # 提取其他信息
        each_as_a_separate_resolution = False  # 每个都是单独决议
        ordinary_resolution_searched = False
        for element in elements:
            if element["class"] != "PARAGRAPH":
                continue
            if self.neglect_syllabus_regs and self.match_syllabus(
                element,
                self.neglect_syllabus_regs,
            ):
                continue
            if self.syllabus_regs and not self.match_syllabus(
                element,
                self.syllabus_regs,
            ):
                continue
            # 在文段下方的ordinary resolution不能提取
            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6167#note_659071
            if self.P_ORDINARY.search(element["text"]) and name_element_index and element["index"] < name_element_index:
                results.append(ParagraphResult(element, element["chars"]))
                ordinary_resolution_searched = True
            if self.P_SEPARATE.search(element["text"]):
                results.append(ParagraphResult(element, element["chars"]))
            if nine_year_directors and self.P_EACH_SEPARATE_RESOLUTION.search(element["text"]):
                each_as_a_separate_resolution = True
                results.append(ParagraphResult(element, element["chars"]))

        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6167#note_659137
        # 提取到董事名字，但是没有ordinary resolutions标题的，在董事名字上方再提一遍此标题
        if name_element_index and not ordinary_resolution_searched:
            for para in self.pdfinsight.find_elements_near_by(
                name_element_index, amount=10, step=-1, aim_types=["PARAGRAPH"]
            ):
                if self.P_ORDINARY.search(para["text"]):
                    results.append(ParagraphResult(para, para["chars"]))
                    break

        if all(not value for value in found_mapping.values()) and not each_as_a_separate_resolution:
            # 完全没有披露INED
            return [self.create_result([], schema=self.schema, value=AnswerValueEnum.ND.value)]
        results = sorted(results, key=lambda x: x.element["index"])
        value = AnswerValueEnum.PS.value
        return [self.create_result([result], schema=self.schema, value=value) for result in results]


class AGMM41Process(AGMM4X):
    def predict_schema_answer(self, elements):
        """
        在re_election of director章节下找到‘the nomination committee noted the following：’描述，然后提取其下的以序号开头的段落
        """
        nine_year_directors = self.predictor.prophet.metadata.get("nine_year_director")
        if nine_year_directors:
            return []
        return [self.create_result([], schema=self.schema, value=AnswerValueEnum.ND.value)]


class AGMM41NoDirector(AGMM41SeparateResolution):
    P_NINE_YEAR_DIRECTOR = MatchMulti.compile(r"\bdirector\b", r"\bnine year", operator=all)

    def predict_schema_answer(self, elements):
        nine_year_directors = self.predictor.prophet.metadata.get("nine_year_director")
        if nine_year_directors:
            found_mapping = {director.english_name: False for director in nine_year_directors}
            short_directors = MatchMulti.compile(
                *[
                    rf"(Mr|Ms|Dr|Prof|Mdm|Mass|Mrs)[.\s]*{clean_director_name(director.english_name).split()[0]}"
                    for director in nine_year_directors
                ],
                operator=any,
            )
            name_patterns = self.build_director_name_patterns(nine_year_directors)
            results, _ = self.get_director_names(self.pdfinsight.paragraphs, name_patterns, found_mapping)
            if not results:
                for element in elements:
                    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6584#note_685825
                    element_text = clean_txt(element.get("text") or "")
                    if self.P_NINE_YEAR_DIRECTOR.search(element_text) and (
                        self.find_director_element(element, name_patterns, found_mapping)
                        or short_directors.search(element_text)
                    ):
                        results = [ParagraphResult(element, element["chars"])]
            if not results:
                return [self.create_result([], schema=self.schema, value=AnswerValueEnum.ND.value)]
            return []
        return [self.create_result([], schema=self.schema, value=AnswerValueEnum.ND.value)]


class AGMM41DirectorProfile(AGMM41SeparateResolution):
    P_INED = MatchMulti.compile(r"\b(independent non-executive Directors?|INEDs?)", operator=any)

    def predict_schema_answer(self, elements):
        nine_year_directors = self.predictor.prophet.metadata.get("nine_year_director")
        if nine_year_directors:
            answer_results = []
            name_patterns = self.build_director_name_patterns(nine_year_directors)
            for name_pattern in name_patterns:
                name_element = None
                for para in self.pdfinsight.paragraphs:
                    if (
                        element := self.find_director_element(para, [name_pattern], {})
                    ) and self.pdfinsight_syllabus.is_syllabus_elt(element.element):
                        name_element = element
                        break
                # 如果有name_element, 并且name_element是标题，在这个标题下提取
                if name_element:
                    syllabuses = []
                    element_index = name_element.element["index"]
                    if sylls := self.pdfinsight_syllabus.find_by_elt_index(element_index):
                        syllabuses.append(sylls[-1])
                    if syllabuses:
                        paragraph_pattern = self.get_config("paragraph_pattern")
                        for syllabus in syllabuses:
                            for element in self.pdfinsight.find_elements_by_index_range(
                                0, syllabus["range"][0], syllabus["range"][1]
                            ):
                                if element["class"] == "PARAGRAPH" and paragraph_pattern.search(element["text"]):
                                    answer_results.append(ParagraphResult(element, element["chars"]))
            if answer_results:
                return [
                    self.create_result([result], schema=self.schema, value=AnswerValueEnum.PS.value)
                    for result in answer_results
                ]
            return []
