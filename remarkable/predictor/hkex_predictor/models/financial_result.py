import logging
import re
from collections import Counter
from copy import deepcopy
from typing import Dict, List

from remarkable.common.common import is_paragraph_elt, is_table_elt
from remarkable.common.common_pattern import R_CURRENCY
from remarkable.common.constants import DocType, TableType
from remarkable.common.pattern import Match<PERSON>ulti, PatternCollection
from remarkable.common.util import clean_txt
from remarkable.pdfinsight.parser import (
    ParsedTableCell,
    cell_data_patterns,
    date_en_patterns,
    parse_table,
)
from remarkable.predictor.common_pattern import R_PERCENT
from remarkable.predictor.dataset import DatasetItem
from remarkable.predictor.models.table_tuple import TupleTable
from remarkable.predictor.schema_answer import CellCharResult, Char<PERSON>esult, TableCellsResult

TIME_PERIOD = "Time Period"
CURRENCY = "Currency"
UNIT = "Unit"
ACCOUNT = "Account"
VALUE = "Value"

THREE_MONTH_PATTERN = re.compile(r"Three month", re.I)

P_CURRENCY = PatternCollection(
    [
        r"Financial figures in this announcement are expressed in\s?(?P<dst>.*)\s?unless otherwise stated",
        r"(?P<dst>Hong Kong dollar\s?(\(HKD\))?)\s?in (million|billion)",
    ],
    re.I,
)

P_CURRENCY_KEY = MatchMulti.compile(r"\$m|\$bn", operator=any)


COLUMNS_PATTERNS = {
    TIME_PERIOD: PatternCollection(
        [
            re.compile(r"as\s*?at", re.I),
            re.compile(r"Year\s*?ended", re.I),
            re.compile(r"20\d\d"),
            re.compile(r"Amount (incurred )?in (this|last)", re.I),
            re.compile(r"(current|previous|Preceding|this)\s*period", re.I),
            re.compile(r"\dM2\d", re.I),  # 50053
            re.compile(r"HKFRS 16"),
            re.compile(r"(ending|Closing)\s*?balance", re.I),
            # stock_code=1202, year=2024, mid=29: https://www1.hkexnews.hk/listedco/listconews/sehk/2025/0422/*************.pdf
            re.compile(r"(beginn?ing|Openn?ing)\s*?balance", re.I),
            # stock_code=01513, year=2024, mid=29
            re.compile(r"(end|beginn?ing)\s*of\s*the\s*year", re.I),
            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6172#note_715398
            # stock_code=00436, year=2024, mid=29
            re.compile(r"[二贰][零oO0〇].{2}年", re.I),
            # stock_code=00568, year=2024, page=119
            # stock_code=09977, year=2024, page=117
            re.compile(r"balance\s*at\s*(the\s*)?(begin|Open|end|Close?)", re.I),
            date_en_patterns,
        ],
    ),
    CURRENCY: PatternCollection(
        [
            re.compile(rf"(?P<dst>{R_CURRENCY})[^a-z]", re.I),
            re.compile(r"\b(?P<dst>R\s?[AM]B|renminbi|mop|[¥€￡])", re.I),
            re.compile(r"\b(?P<dst>(HK|AUD|MYR|RM)[$s’])", re.I),
            re.compile(r"(?P<dst>\$)[‘’']000", re.I),
            re.compile(r"\b(?P<dst>(USD?|US[$s’]|U\.S\.|CAD|S?\$))"),
            re.compile(r"\b(?P<dst>GBP|JPY|SGD|TWD|CHF|CNY|KRW|THB|MYR)", re.I),
            re.compile(r"\b(?P<dst>AUD)(?!it)", re.I),
        ],
    ),
    UNIT: PatternCollection(
        [
            r"(?P<dst>million)",
            r"(?P<dst>yuan)",
            r"(?<!\d)(?P<dst>[’,1\']\s?0\s?0\s?[0o]?)",
            rf"{R_CURRENCY}(?P<dst>(0\s?0[0o]|m))",
            r"(?P<dst>’m)",
            r"\$(?P<dst>m)",
            r"(?P<dst>thousands)",
        ],
        flags=re.I,
    ),
    ACCOUNT: PatternCollection([r""]),
    VALUE: PatternCollection([r""]),
}

INVALID_COLUMNS_PATTERNS = {
    TIME_PERIOD: {
        "default": PatternCollection(
            [
                r"(HK|RMB|\$|€).*?0\s?00",
                r"^[\d,()]*$",
                r"Operating (costs|income)",
                r"\d+\)\s?\(",
                r"未經?[審审]核",
            ]
            + COLUMNS_PATTERNS[CURRENCY].patterns,
            flags=re.I,
        ),
    },
    CURRENCY: {
        "default": PatternCollection(
            [
                r"(?P<dst>assets|August|causes)",
                r"(?P<dst>policyholders’)",
                r"HKFRS",
            ],
            flags=re.I,
        ),
    },
    UNIT: {
        "default": PatternCollection(
            [
                r"\d+%",
            ],
            flags=re.I,
        ),
    },
    VALUE: {
        "Total Assets": PatternCollection(
            [
                r"(?<!ASSETS AND )LIABILITIES$",
                r"Attributable.*?to",
                r"EQUITY$",
                r"Unaudit|未經?[審审]核",
            ],
            flags=re.I,
        ),
        "Revenue": PatternCollection(
            [
                r"\d%$",
            ],
            flags=re.I,
        ),
    },
}

INVALID_SUBTOTAL_PATTERNS = {
    VALUE: {
        "PL-Impairment": PatternCollection(
            [
                r"LIABILITIES",
                r"Current assets",
                r"^assets$",
            ],
            flags=re.I,
        ),
        "Notes-Impairment": PatternCollection(
            [
                r"depreciation",
                r"amorti[sz]ation",
            ],
            flags=re.I,
        ),
        "Each Individual Asset": PatternCollection(
            [
                r"Current liabilities",
            ],
            flags=re.I,
        ),
    },
}


CELL_DATA_PATTERN = PatternCollection(
    [
        cell_data_patterns.patterns,
        re.compile(rf"[(（]\d+(\.\d+)?{R_PERCENT}?[)）]"),
    ]
)

INVALID_CELL_DATA_PATTERN = PatternCollection(
    [
        r"20\d\d",
        r"Notes?",
        r"Restated",
        r"\d+\s*?million",  # file id revenue 42224
        r"^\($",  # file id revenue 42224
        r"(Un)?audit(ed)?|未經?[審审]核",
    ],
    re.I,
)

SPECIAL_TIME_PATTERNS = PatternCollection(
    [
        r"20\d\d",
    ]
)

NUMBER_PATTERN = PatternCollection(r"\d")
DOT_PATTERN = PatternCollection(r"\.")
COMMA_PATTERN = PatternCollection(r"[,，]")
MINUS_PATTERN = PatternCollection(r"^-")

CN_PATTERN_FOR_ACCOUNT = re.compile(r"([\u4e00-\u9fa5�流]+\s?)")

NCA_ACCOUNT = PatternCollection(
    [
        r"NON-CURRENT\s*?ASSETS",
        r"Current\s*?liabiliti",
        r"LIABILITIES",
    ],
    flags=re.I,
)

CA_PATTERN = PatternCollection(
    [r"CURRENT\s*?ASSETS"],
    flags=re.I,
)
NCA_PATTERN = PatternCollection(
    [r"non"],
    flags=re.I,
)

ASSETS_PATTERN = PatternCollection(
    [r"^Assets$"],
)

COMMON_FEATURE_BLACK_LIST = {
    "row_headers": [
        r"__regex__^D_date",
    ],
}

TABLE_ABOVE_BREAK_PATTERN = PatternCollection(
    [
        r"^The board (\(the “Board”\))? of Directors",  # 该描述一般意味着上方不太可能出现单位和币种
        r"^The Board is pleased to announce",
    ],
    re.I,
)


def get_pattern_collection(pattern_config, col=None, schema_name=None):
    pattern_collection = None
    if col and schema_name:
        col_pattern_collection = pattern_config.get(col, {})
        pattern_collection = col_pattern_collection.get(schema_name) or col_pattern_collection.get("default")
    elif col:
        pattern_collection = pattern_config.get(col)
    elif schema_name:
        pattern_collection = pattern_config.get(schema_name)

    if pattern_collection and not isinstance(pattern_collection, PatternCollection):
        pattern_collection = PatternCollection(pattern_collection)

    return pattern_collection


def match_subtitle_and_header(header: ParsedTableCell, patterns: Dict[str, List[str]]) -> bool:
    if not patterns:
        return False
    for subtitle, pattern_list in patterns.items():
        subtitle_p = PatternCollection(subtitle, re.I)
        patterns_p = PatternCollection(pattern_list, re.I)
        clean_subtitle = clean_txt(header.subtitle) if header.subtitle else ""
        if subtitle_p.nexts(clean_subtitle) and patterns_p.nexts(clean_txt(header.text)):
            return True
    return False


class FinanicalResult(TupleTable):
    @property
    def answer_counts(self):
        return self.get_config("answer_counts")

    @property
    def multi_elements(self):
        return self.get_config("multi_elements", False)

    @property
    def filter_later_elements(self):
        return self.get_config("filter_later_elements", False)

    @property
    def table_title_pattern(self):
        pattern = self.get_config("table_title_pattern", [])
        return PatternCollection(pattern, flags=re.I) if pattern else None

    @property
    def neglect_title_pattern(self):
        pattern = self.get_config("neglect_title_pattern", [])
        return PatternCollection(pattern, flags=re.I) if pattern else None

    @property
    def neglect_title_above_pattern(self):
        pattern = self.get_config("neglect_title_above_pattern", [])
        return PatternCollection(pattern, flags=re.I) if pattern else None

    @property
    def neglect_syllabus_pattern(self):
        pattern = self.get_config("neglect_syllabus_pattern", [])
        return PatternCollection(pattern, flags=re.I) if pattern else None

    @property
    def col_black_feature(self):
        default_black = [r"^notes?", r"^%$"]
        pattern = default_black + self.get_config("col_black_feature", [])
        return PatternCollection(pattern, flags=re.I) if pattern else None

    @property
    def full_match(self):
        """
        规则与训练特征完全匹配即 pattern==feature
        """
        return self.get_config("full_match", False)

    def train(self, dataset: List[DatasetItem], **kwargs):
        model_data = {}
        for col, col_path in self.columns_with_fullpath():
            if col not in ["Value"]:
                continue
            for item in dataset:
                for node in self.find_answer_nodes(item, col_path):
                    if node.data is None:
                        continue
                    features = self.extract_feature(item.data["elements"], node.data)
                    model_data.setdefault(col, Counter()).update(features)
        self.model_data = model_data

    def add_common_feature_black_list(self):
        common_feature_black_list = COMMON_FEATURE_BLACK_LIST.get(self.feature_from, [])
        self.config["feature_black_list"] = self.config.get("feature_black_list", []) + common_feature_black_list

    def predict_schema_answer(self, elements):
        self.add_common_feature_black_list()  # 添加公共的feature黑名单
        self.columns = ["Value"]  # 只有Value字段依赖table_tuple的结果
        ret = []
        schema_name = self.schema.parent.name
        for element in elements:
            answer_results = super().predict_schema_answer([element])
            value_answers = self.get_value_answer(answer_results, schema_name)
            if not value_answers:
                continue
            for value_answer in value_answers:
                time_answers = self.get_other_answer(value_answer, TIME_PERIOD, schema_name)
                if not time_answers:
                    if not time_answers:
                        time_answers = self.get_other_answer(
                            value_answer, TIME_PERIOD, schema_name, from_header_header=True
                        )
                    if not time_answers:
                        continue
                currency_answer = self.get_currency_answer()
                unit_answer = self.get_other_answer(value_answer, UNIT, schema_name)
                if not unit_answer:
                    unit_answer = self.get_answer_from_table_above(value_answer, UNIT, schema_name)
                    if not unit_answer:
                        unit_answer = self.get_other_answer(value_answer, UNIT, schema_name, from_header_header=True)
                answer_group = {
                    "Time Period": time_answers,
                    "Currency": currency_answer,
                    "Unit": unit_answer,
                    "Value": [value_answer],
                }
                ret.append(answer_group)
            if not self.multi_elements and ret:
                break

        return ret

    def get_value_answer(self, answer_results, schema_name):
        ret = []
        for answer_result in answer_results:
            for key, items in answer_result.items():
                for item in items:
                    if key == "Value" and self.is_valid_value(item, schema_name):
                        if not self.answer_counts:
                            ret.append(item)
                        else:
                            if len(ret) < self.answer_counts:
                                ret.append(item)
                            else:
                                break
        return ret

    def get_other_answer(self, value_answer, col, schema_name, from_col_header=True, from_header_header=False):
        ret = []
        patterns = get_pattern_collection(COLUMNS_PATTERNS, col=col)
        invalid_patterns = get_pattern_collection(INVALID_COLUMNS_PATTERNS, col=col, schema_name=schema_name)
        answer_cell = value_answer.element_results[0].parsed_cells[0]
        element = value_answer.relative_elements[0]
        headers = self.get_possible_headers_for_other(
            col, schema_name, answer_cell, from_col_header, from_header_header
        )
        for header_cell in headers:
            cell_txt = clean_txt(header_cell.text)
            if col == TIME_PERIOD and self.doc_type in [DocType.Q3, DocType.INTERIM, DocType.FINAL]:
                possibles = [clean_txt(c.text) for c in header_cell.col_header_cells] + [cell_txt]
                if any(THREE_MONTH_PATTERN.search(t) for t in possibles):
                    return []

            matcher = patterns.nexts(cell_txt)
            invalid_matchers = None
            if invalid_patterns:
                invalid_matchers = invalid_patterns.nexts(cell_txt)
                if col == TIME_PERIOD and SPECIAL_TIME_PATTERNS.nexts(cell_txt):
                    invalid_matchers = False
                if col == TIME_PERIOD and self.is_valid_time(cell_txt):
                    invalid_matchers = True
            if not matcher or invalid_matchers:
                continue
            value = matcher.groupdict().get("dst", None)
            if value:
                span = matcher.span()
                dst_chars = self.get_chars(header_cell.text, value, header_cell.raw_cell["chars"], span)
                answer = self.create_result(
                    [CellCharResult(element, dst_chars, [header_cell])],
                    column=col,
                    meta={"cell": [int(i) for i in header_cell.raw_cell["index"].split("_")]},
                )
            else:
                answer = self.create_result(
                    [TableCellsResult(element, [header_cell])],
                    column=col,
                    meta={"cell": [int(i) for i in header_cell.raw_cell["index"].split("_")]},
                )
            ret.append(answer)
        return ret

    def get_currency_answer(self):
        crude_elements = self.predictor.get_candidate_elements([self.predictor.parent.schema_name, "Currency"])
        for element in crude_elements:
            if is_paragraph_elt(element):
                element_text = clean_txt(element["text"])
                if matcher := P_CURRENCY.nexts(element_text):
                    dst_chars = self.get_dst_chars_from_matcher(matcher, element)
                    answer = self.create_result(
                        [CharResult(element, dst_chars)],
                        column=CURRENCY,
                    )
                    return [answer]
            if is_table_elt(element):
                table = parse_table(element, tabletype=TableType.TUPLE.value, pdfinsight_reader=self.pdfinsight)
                for row in table.rows[::-1]:
                    if len(row) < 2:
                        continue
                    first_cell = row[0]
                    if not P_CURRENCY_KEY.search(clean_txt(first_cell.text)):
                        continue
                    second_cell = row[1]
                    if matcher := P_CURRENCY.nexts(clean_txt(second_cell.text)):
                        dst_chars = self.get_dst_chars_from_matcher(matcher, second_cell.raw_cell)
                        answer = self.create_result(
                            [CellCharResult(element, dst_chars, [second_cell])],
                            column=CURRENCY,
                            meta={"cell": [int(i) for i in second_cell.raw_cell["index"].split("_")]},
                        )
                        return [answer]

        return []

    def get_answer_from_table_above(self, value_answer, col, schema_name):
        ret = []
        element = value_answer.relative_elements[0]
        table = parse_table(element, tabletype=TableType.TUPLE.value, pdfinsight_reader=self.pdfinsight)
        patterns = get_pattern_collection(COLUMNS_PATTERNS, col=col)
        invalid_patterns = get_pattern_collection(INVALID_COLUMNS_PATTERNS, col=col, schema_name=schema_name)
        for element in table.elements_above:
            if element["class"] == "PARAGRAPH":
                element_text = clean_txt(element["text"])
                if TABLE_ABOVE_BREAK_PATTERN.nexts(element_text):
                    break
                if (
                    len(element_text) > 300
                ):  # 太长的段落里有可能会出现比较多的金额的描述 跟表格里的单位币种没有关系 51425
                    continue
                matcher = patterns.nexts(element_text)
                invalid_matchers = None
                if invalid_patterns:
                    invalid_matchers = invalid_patterns.nexts(element_text)
                if not matcher or invalid_matchers:
                    continue
                value = matcher.groupdict().get("dst", None)
                if value:
                    span = matcher.span()
                    dst_chars = self.get_chars(element["text"], value, element["chars"], span)

                    answer = self.create_result(
                        [CharResult(element, dst_chars)],
                        column=col,
                    )
                    ret.append(answer)
                    break
            elif element["class"] == "TABLE":
                if len(element["cells"]) > 2:  # 忽略大表格 认为单位币种的表格只有1-2行
                    continue
                table = parse_table(element, tabletype=TableType.TUPLE.value, pdfinsight_reader=self.pdfinsight)
                for row in table.rows:
                    for cell in row:
                        cell_txt = clean_txt(cell.text)
                        matcher = patterns.nexts(cell_txt)
                        invalid_matchers = False
                        if invalid_patterns:
                            invalid_matchers = invalid_patterns.nexts(cell_txt)
                        if not matcher or invalid_matchers:
                            continue
                        value = matcher.groupdict().get("dst", None)
                        if value:
                            span = matcher.span()
                            dst_chars = self.get_chars(cell.text, value, cell.raw_cell["chars"], span)
                            answer = self.create_result(
                                [CellCharResult(element, dst_chars, [cell])],
                                column=col,
                                meta={"cell": [int(i) for i in cell.raw_cell["index"].split("_")]},
                            )
                            ret.append(answer)

        return ret

    def get_possible_headers_for_other(self, col, schema_name, answer_cell, from_col_header, from_header_header):
        headers = answer_cell.col_header_cells if from_col_header else answer_cell.row_header_cells
        headers = [header for header in headers if header.text != ""]
        patterns = get_pattern_collection(COLUMNS_PATTERNS, col=col)
        if from_col_header:
            headers = [header for header in headers if header.colidx == answer_cell.colidx]
        if from_header_header:  # from header row_header
            header_headers = []
            for header in headers:
                for item in header.row_header_cells:
                    matcher = patterns.nexts(clean_txt(item.text))
                    if not matcher:
                        continue
                    header_headers.append(item)
            # 添加header下一行第一个单元格 51420
            if not header_headers and headers:
                current_table = headers[0].table
                header_rowidx = headers[0].rowidx
                if len(current_table.rows) > header_rowidx + 1:
                    header_headers.append(current_table.rows[header_rowidx + 1][0])
                if len(current_table.rows) > header_rowidx + 2:
                    header_headers.append(current_table.rows[header_rowidx + 2][0])
            if not header_headers:  # 48871
                current_table = answer_cell.table
                # 表格仅有一年的数据
                # https://jura.paodingai.com/#/hkex/result-announcement/report-review/155214?fileId=40373&page=5 stock_code=00022, year=2020, type=Final
                header_headers = [cell for cell in current_table.rows[0][1:3] if cell.text]
            headers += header_headers
        if len({header.text for header in headers}) == 1:
            range_end = 2 if col not in [CURRENCY, UNIT] else 3
            for row in range(headers[0].rowidx + 1, headers[0].rowidx + range_end + 1):
                try:
                    header_next_cell = answer_cell.table.rows[row][answer_cell.colidx]
                except IndexError:
                    logging.info("not found header for irregular table")
                    continue
                else:
                    headers.append(header_next_cell)
        if not headers:
            # 先从上面往下找 找不到再从下往上尝试
            range_end = 2 if col not in [CURRENCY, UNIT] else 3
            for row in range(0, range_end + 1):
                try:
                    header_next_cell = answer_cell.table.cols[answer_cell.colidx][row]
                except IndexError:
                    logging.info("not found header for irregular table")
                    continue
                else:
                    matcher = patterns.nexts(clean_txt(header_next_cell.text))
                    if not matcher:
                        continue
                    headers.append(header_next_cell)
        headers = self.filter_heads(headers, col)
        if not headers:
            # 从answer_cell开始下到上尝试获取
            header_map = {}
            for cell in answer_cell.table.cols[answer_cell.colidx][: answer_cell.rowidx][::-1]:
                clean_text = clean_txt(cell.text)
                if SPECIAL_TIME_PATTERNS.nexts(clean_text) and not header_map.get("time"):
                    header_map["time"] = cell
                    continue
                time_matcher = COLUMNS_PATTERNS[TIME_PERIOD].nexts(clean_text)
                invalid_time_patterns = get_pattern_collection(INVALID_COLUMNS_PATTERNS, TIME_PERIOD, schema_name)
                invalid_time_matcher = invalid_time_patterns.nexts(clean_text)
                if (not invalid_time_matcher and time_matcher) and not header_map.get("time"):
                    header_map["time"] = cell
                    break  # 时间一般在最上方 找到时间之后就跳出
                currency_matcher = COLUMNS_PATTERNS[CURRENCY].nexts(clean_text)
                if currency_matcher and not header_map.get("currency"):
                    header_map["currency"] = cell
                    continue
                unit_matcher = COLUMNS_PATTERNS[UNIT].nexts(clean_text)
                if unit_matcher and not header_map.get("unit"):
                    header_map["unit"] = cell
                    continue
            headers = list(header_map.values())
        ret = []
        exists_headers = set()
        for header in headers:
            if header.text not in exists_headers:
                ret.append(header)
                exists_headers.add(header.text)
        return ret

    @staticmethod
    def filter_heads(headers, col):
        if col != TIME_PERIOD:  # 目前仅time_period字段作此处理 根据需要其他字段也可
            return headers
        ret = []
        for header in headers:
            clean_header_text = clean_txt(header.text)
            matcher = COLUMNS_PATTERNS[TIME_PERIOD].nexts(clean_header_text)
            invalid_matcher = INVALID_COLUMNS_PATTERNS[TIME_PERIOD]["default"].nexts(clean_header_text)
            if col == TIME_PERIOD and SPECIAL_TIME_PATTERNS.nexts(clean_header_text):
                invalid_matcher = False
            if matcher and not invalid_matcher:
                ret.append(header)
                # break
        return ret

    def is_valid_value(self, answer, schema_name):
        answer_cell = answer.element_results[0].parsed_cells[0]
        if self.col_black_feature:
            headers = deepcopy(answer_cell.col_header_cells)
            if headers and not headers[0].text:  # header的text为空, 可能是跨页表格导致，尝试取表格第一行的数据
                first_cell = answer_cell.table.rows[0][answer_cell.colidx]
                if first_cell.text:
                    headers = [first_cell]
            if answer_cell.rowidx - 1 > 0:
                cell_above = answer_cell.table.rows[answer_cell.rowidx - 1][answer_cell.colidx]
                headers += [cell_above]
            for header in headers:
                invalid_col_matchers = self.col_black_feature.nexts(clean_txt(header.text))
                if invalid_col_matchers:
                    return False
        invalid_pattern = get_pattern_collection(INVALID_COLUMNS_PATTERNS, col=VALUE, schema_name=schema_name)
        if invalid_pattern and answer_cell.subtitle and invalid_pattern.nexts(clean_txt(answer_cell.subtitle)):
            return False
        invalid_subtotal_pattern = get_pattern_collection(INVALID_SUBTOTAL_PATTERNS, col=VALUE, schema_name=schema_name)
        if (
            invalid_subtotal_pattern
            and answer_cell.sub_total_headers
            and invalid_subtotal_pattern.nexts(clean_txt("".join([i.text for i in answer_cell.sub_total_headers])))
        ):
            return False
        clean_answer_cell_text = clean_txt(answer_cell.text)
        if invalid_pattern and invalid_pattern.nexts(clean_answer_cell_text):
            return False
        if INVALID_CELL_DATA_PATTERN.nexts(clean_answer_cell_text):
            return False
        if CELL_DATA_PATTERN.nexts(clean_answer_cell_text):
            return True
        if COLUMNS_PATTERNS[TIME_PERIOD].nexts(clean_answer_cell_text):
            return False
        if COLUMNS_PATTERNS[CURRENCY].nexts(clean_answer_cell_text):
            return False
        if COLUMNS_PATTERNS[UNIT].nexts(clean_answer_cell_text):
            return False
        return True

    @staticmethod
    def is_valid_time(cell_text):
        #  同时出现 数字 , . 的金额类描述 不是时间
        number_matcher = NUMBER_PATTERN.nexts(cell_text)
        dot_matcher = DOT_PATTERN.nexts(cell_text)
        mius_matcher = MINUS_PATTERN.nexts(cell_text)
        comma_matcher = COMMA_PATTERN.nexts(cell_text)
        # 有可能没有小数点 有可能有负号
        return number_matcher and dot_matcher and (mius_matcher or comma_matcher)
