import re
from collections import defaultdict
from dataclasses import dataclass
from itertools import chain
from operator import itemgetter
from typing import List, Tuple, Union

from remarkable.common.common import clean_director_name, get_keys
from remarkable.common.common_pattern import R_MIDDLE_DASHES, R_PREFIX_NUM
from remarkable.common.constants import AnswerV<PERSON>ueEnum
from remarkable.common.pattern import MatchMulti, NeglectPattern, PatternCollection, PositionPattern
from remarkable.common.util import clean_txt, split_paragraph
from remarkable.predictor.hkex_predictor.schemas.pattern import R_APPELLATION, R_DIRECTOR, R_INED
from remarkable.predictor.models.base_model import BaseModel
from remarkable.predictor.schema_answer import AnswerResult, ParagraphResult
from remarkable.services.agm import P_APPENDIX, P_LETTER_SYLLABUS, DirectorCVMatcher


@dataclass
class INEDNamePattern:
    full_name: Union[None, PatternCollection]
    short_name: Union[None, PatternCollection]

    def match_name(self, content, full_name=False):
        if full_name:
            return self.full_name and self.full_name.nexts(content)
        return PatternCollection([self.full_name, self.short_name], re.I).nexts(content)


class AGMM29(BaseModel):
    """
    https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6001
    """

    COLUMN_1 = "Name and Companies"
    COLUMN_2 = "Sufficient time description"

    P_VALID_ELE = None

    P_INED = PatternCollection([R_INED], re.I)

    P_UPPER_CHAR = re.compile(r"([A-Z]\s*\.)")

    R_NAME_PREFIX = r"(Mrs?|Ms|Dr|Prof(essor)?|Mdm|Mass|Sir|MR|Miss?)(\.)?"
    P_NAME_PREFIX = PatternCollection([R_NAME_PREFIX])

    NUM_WORDS = {"zero", "one", "two", "three", "four", "five", "six"}

    R_COMPANY = r"(?:compan(?:y|ies)|group|Board)"

    P_OTHER_LISTED_COMPANIES = PatternCollection(
        [r"other\s*listed\s*companies"],
        re.I,
    )

    R_LISTED_GE_7_COMPANIES = [
        r"({name})[^.]*?serving\s*as\s*a?\s*director\s*for\s*more\s*than\s*(?P<num>(\d+|[a-zA-Z]+))\s*listed\s*companies",
        rf"({{name}})[^.]*?serv(?:ed|ing)\s*as\s*(a|an)?\s*(?:director|{R_INED})\s*(?:for|of)\s*(?P<num>(\d+|[a-zA-Z]+))\s*other\s*listed\s*companies",
    ]

    R_LISTED_LT_7_COMPANIES = [
        r"({name})[^.]*?not\s*more\s*than\s*(?P<num>(\d+|[a-zA-Z]+))\s*listed\s*companies",
    ]

    S_UN_SERVER_LISTED_COMPANIES = [
        r"({name}).*?(not[^.]*?h[oe]lds?|h[oe]ld\s*no)[^.]+(?:any)?\s*(other\s*)?(?:positions?|directorships?)[^.]+compan(?:y|ies)",
        r"({name}).*?not[^.]*?ha(?:ve|d)[^.]+(?:any)?\s*(other\s*)?(?:positions?|directorships?)[^.]+compan(?:y|ies)",
        r"({name})[^.]*?not[^.]*?an?\s*director\s*of\s*(?:any)?\s*(other\s*)?\s*listed\s*compan(?:y|ies)",
        rf"(?:not|nor)[^.]*?h[oe]lds?\s*any\s*(other\s*)?(?:positions?|directorships?)[^.]+compan(?:y|ies).*?{R_INED}",
        rf"(?:not|nor)[^.]*?have\s*any\s*(other\s*)?(?:positions?|directorships?)[^.]+compan(?:y|ies).*?{R_INED}",
        r"none\s*of\s*the\s*(?:following|above)\s*directors[^.]+h[oe]lds?\s*any\s*other\s*(?:positions?|directorships?)[^.]+compan(?:y|ies)",
        r"retiring\s*directors[^.]*?not\s*h[oe]ld\s*any\s*(other\s*)?(?:positions?|directorships?)[^.]*?compan(?:y|ies)",
        r"each\s*of[^.]+directors[^.]+not\s*h[oe]lds?\s*any\s*other\s*(?:positions?|directorships?)[^.]+compan(?:y|ies)",
    ]

    P_SAVE_AS_DISCLOSED = PatternCollection(
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6572#note_685280
        r"Save\s*as\s*disclosed.*?(?:not|nor)[^.]*?h[oe]lds?\s*any\s*(other\s*)?(?:positions?|directorships?)[^.]+compan(?:y|ies)",
        re.I,
    )

    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6155#note_656897
    P_FILTER_UN_SERVER_LISTED_COMPANIES = PatternCollection(
        [
            r"did\s*not\s*have\s*any\s*interests\s*or\s*short\s*positions",
            rf"(not[^.]*?h[oe]lds?|h[oe]ld\s*no)[^.]+(?:positions?|directorships?)[^.]+within\s*the\s*{R_COMPANY}",
        ],
        re.I,
    )

    P_ALL_DIRECTOR_UN_SERVER = PatternCollection(
        [
            rf"not[^.]*?h[oe]lds?\s*any\s*(other\s*)?(?:positions?|directorships?)[^.]+compan(?:y|ies).*?{R_INED}",
            rf"not[^.]*?have\s*any\s*(other\s*)?(?:positions?|directorships?)[^.]+compan(?:y|ies).*?{R_INED}",
            r"each\s*of[^.]+directors[^.]*?not\s*h[oe]lds?\s*any\s*(other\s*)?(?:positions?|directorships?)[^.]*?compan(?:y|ies)",
            r"none\s*of\s*the\s*(?:following|above|retiring)\s*directors[^.]*?h[oe]lds?[\s,]*any\s*(other\s*)?(?:positions?|directorships?)[^.]*?compan(?:y|ies)",
        ],
        re.I,
    )

    # 分段叙述， 如： 除本公告披露的情况外，如下/以上董事
    P_FOLLOWING_DISCLOSED_DESC = PositionPattern.compile(
        r"Save\s*as\s*disclosed",
        (
            r"(?:each|all)\s*of\s*the\s*(?:following|above)\s*retiring\s*Directors"
            r"|none\s*of\s*the\s*(?:above|following)\s*Directors"
        ),
        # MatchMulti.compile(
        #     r"(?:each|all)\s*of\s*the\s*(?:following|above)\s*retiring\s*Directors",
        #     r"none\s*of\s*the\s*(?:above|following)\s*Directors",
        #     operator=any,
        # ),
    )

    P_HELD_POSITION = PatternCollection(
        [
            r"(h[oe]lds?|have)\s*any\s*(other\s*)?(?:positions?|directorships?)[^.]+compan(?:y|ies)",
        ],
        re.I,
    )

    P_DEVOTED_SUFFICIENT = PatternCollection(
        [
            r"devote\s*sufficient\s*time\s*to\s*the\s*board",
            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6572#note_685280
            r"devoted\s*significant\s*time",
        ],
        re.I,
    )

    P_SYLLABUS = PatternCollection(
        [
            r"(?:(?:RE-)?ELECT(?:ION|ED)?)\s*OF\s*(THE\s*RETIRING\s*)?DIRECTORS",
            *P_LETTER_SYLLABUS.patterns,
            *P_APPENDIX.patterns,
        ],
        re.I,
    )

    P_DIRECTOR_ALIAS = PatternCollection(
        [rf"[(（]\s*([“‘]{{0,2}})\s*(?P<name>{R_NAME_PREFIX}[a-zA-Z.{R_MIDDLE_DASHES}\s]+)\s*([”’]{{0,2}})\s*[)）]"],
        re.I,
    )

    P_INED_COMPANY_NOT_RECORDED = MatchMulti.compile(
        rf"appointed|has been the {R_INED}|currently is a {R_DIRECTOR}", r"stock\s*code", r"since", operator=all
    )

    # 外企stock不在director表中
    P_INED_FOREIGN_COMPANY_NOT_RECORDED = PatternCollection(
        [
            r"listed\s*on[^.]+[A-Z][^.]+(Limited|Inc)",
            r"serves\s*as an?[^.]+[A-Z][^.]+(Limited|Inc)",  # fid: 87616
        ],
    )

    P_EXCEPT_INED = PatternCollection(r"except", re.I)

    def train(self, dataset, **kwargs):
        pass

    def prepare_syllabus_section_elements(self, syllabus_pattern: PatternCollection = None):
        return DirectorCVMatcher.prepare_syllabus_section_elements(self.pdfinsight, syllabus_pattern or self.P_SYLLABUS)

    @staticmethod
    def unique_list(list_obj):
        elements_dict = {}
        elements_idxes = []
        for ele in sorted(list_obj, key=itemgetter("index")):
            if ele["index"] in elements_idxes:
                continue
            elements_dict[ele["index"]] = ele
            elements_idxes.extend(get_keys(ele, ["page_merged_paragraph", "paragraph_indices"], default=[ele["index"]]))
        return list(elements_dict.values())

    def extend_elements_by_syllabus(self, elements):
        self.group_elements = defaultdict(list)
        self.director_name_mapping = defaultdict(list)
        elements = elements or []
        elements += self.prepare_syllabus_section_elements()
        elements = sorted(self.unique_list(elements), key=lambda x: x["index"])
        director = self.predictor.prophet.metadata.get("director")
        self.ined_companies: list = director and director.get("ined_companies")
        self.directors = [director for director in self.ined_companies if (director and director.get("director"))]
        self.group_elements_by_director(self.directors)
        return elements

    def build_results_by_column(self, results: list[AnswerResult], column: str, value: AnswerValueEnum):
        answers = [{column: [self.create_result(results, column=column)]}]
        for common_result in self.get_common_predictor_results(answers):
            common_result.update_answer_value(value)
        return answers

    def default_answer(self):
        results = []
        for column in (self.COLUMN_1, self.COLUMN_2):
            results.extend(self.build_results_by_column([], column, AnswerValueEnum.ND.value))
        return results

    def predict_schema_answer(self, elements):
        """
        flag self.predictor.prophet.metadata["notice_elements"]

        """
        elements = self.extend_elements_by_syllabus(elements)
        results = []
        ans_value: AnswerValueEnum = AnswerValueEnum.NS if self.ined_companies else AnswerValueEnum.ND
        if ans_value == AnswerValueEnum.ND:
            return self.default_answer()
        ge_7_companies = [data for data in self.ined_companies if len(data["companies"]) >= 7]
        short_name = "|".join(self.build_name_by_directors(self.directors, full_name=True))
        _, res = self.build_generality_desc_results(elements, short_name=short_name, ge_7=True)
        if res:
            ans_value = AnswerValueEnum.PS
            results = res
        elif ge_7_companies:
            ans_value = AnswerValueEnum.PS
            res, results = self.find_company_desc(ge_7_companies, elements, ge_7=True)
            if not res:
                ans_value = AnswerValueEnum.NS
        if not results:
            ans_value = AnswerValueEnum.NS
            lt_7_companies = [data for data in self.ined_companies if len(data["companies"]) < 7]
            res, results = self.find_company_desc(lt_7_companies, elements)
        if not results:
            return self.default_answer()
        results = sorted(results, key=lambda x: x.element["index"])
        if not results:
            ans_value = AnswerValueEnum.ND
        column_1_results = self.build_results_by_column(results, self.COLUMN_1, ans_value)
        column_2_results = self.check_column2_results(results, ans_value, elements)

        return column_1_results + column_2_results

    def check_column2_results(self, columns_1_results, ans_value, elements):
        column_2_results = []
        value = AnswerValueEnum.ND
        if ans_value == AnswerValueEnum.NS:
            value = ans_value
            column_2_results = columns_1_results
        elif ans_value == AnswerValueEnum.PS:
            for ele in elements:
                if ele["class"] != "PARAGRAPH":
                    continue
                element_text = ele.get("text") or ""
                start, end = 0, 0
                for sub_text in split_paragraph(element_text):
                    start = end
                    end = start + len(sub_text) + 1
                    if self.P_DEVOTED_SUFFICIENT.nexts(clean_txt(sub_text)):
                        column_2_results.append(ParagraphResult(ele, ele["chars"][start:end]))
                        value = AnswerValueEnum.PS

        return self.build_results_by_column(column_2_results, self.COLUMN_2, value)

    def build_name_by_directors(self, directors: list, full_name=False) -> List[str]:
        names = []
        for director in directors:
            names.append(
                self.build_short_name(
                    director["director"],
                    full_name=full_name,
                )
            )
            if alias_names := self.director_name_mapping.get(director["director"].english_name):
                names.extend([rf"{self.R_NAME_PREFIX}\s*{name}(\W|$)" for name in alias_names])
        return names

    def find_company_desc(self, directors: list, elements: List[dict], ge_7: bool = False):
        name_results, results = [], []
        for ined_data in directors:
            name_result = None
            p_name = self.build_director_name_pattern(ined_data["director"])
            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6001#note_652858
            d_short_name = self.build_short_name(ined_data["director"])
            for director_name, eles in self.group_elements.items():
                if not p_name.nexts(director_name):
                    continue
                director_elements = eles
                if res := p_name.nexts(director_elements[0].get("text") or ""):
                    name_result = ParagraphResult(
                        director_elements[0], director_elements[0]["chars"][res.start() : res.end()]
                    )
                break
            else:
                continue
            if not ge_7 and (res := self.check_lt7_companies_results(director_elements, d_short_name)):
                results.extend(res)
                continue
            ined_company_results = self.build_company_results(director_elements, ined_data["companies"])
            short_name = "|".join(self.build_name_by_directors([ined_data], full_name=True))
            re_write, desc_results = self.build_generality_desc_results(
                director_elements, short_name=short_name, ge_7=ge_7
            )
            if re_write and desc_results:
                ined_company_results = []
            if not ined_company_results and not desc_results and name_result:
                name_results.append(name_result)
                continue
            if ge_7 and ined_company_results:
                return True, ined_company_results
            results.extend(ined_company_results)
            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6572#note_685280
            results.extend(desc_results or self.build_disclosed_results(director_elements))
        match_elements = {result.element["index"] for result in results}
        elements = [ele for ele in elements if ele["class"] == "PARAGRAPH"]
        for idx, ele in enumerate(elements):
            if ele["index"] in match_elements:
                continue
            sub_results = []
            ele_text = ele["text"]
            for sub_text, (start, end) in split_paragraph(ele_text, need_pos=True):
                if self.P_ALL_DIRECTOR_UN_SERVER.nexts(clean_txt(sub_text)):
                    sub_results.append(ParagraphResult(ele, ele["chars"][start:end]))
            if not sub_results and self.P_FOLLOWING_DISCLOSED_DESC.search(clean_txt(ele_text)):
                for sub_ele in elements[idx + 1 :]:
                    if self.P_HELD_POSITION.nexts(clean_txt(sub_ele["text"])):
                        sub_results.append(ParagraphResult(sub_ele, sub_ele["chars"]))
                        match_elements.add(sub_ele["index"])
                if sub_results:
                    match_elements.add(ele["index"])
                    sub_results.append(ParagraphResult(ele, ele["chars"]))
            results.extend(sub_results)
        min_group_director_elements_idx = min([ele["index"] for ele in chain(*self.group_elements.values())], default=0)
        if not min_group_director_elements_idx or not any(
            self.P_FOLLOWING_DISCLOSED_DESC.search(answer.text)
            and answer.element["index"] < min_group_director_elements_idx
            for answer in results
        ):
            results.extend(name_results)
        return False, results

    def build_director_name_pattern(self, director) -> PatternCollection | None:
        if not director:
            return None
        convert_en_names = self.rotate_names(director)
        d_names = [
            rf"({self.R_NAME_PREFIX})?\s*(?:(?P<surname>[a-zA-Z\d]+)[-,]?\s*)?{name}\s*([(（]?{re.escape(director.chinese_name)}[)）]?)?"
            for name in convert_en_names
        ]
        d_names.extend(
            [
                rf"{re.escape(director.chinese_name)}\s*[(（]?{self.R_NAME_PREFIX}\s*(?:(?P<surname>[a-zA-Z\d]+)[-,]?\s*)?{name}[)）]?"
                for name in convert_en_names
            ]
        )
        return PatternCollection(d_names, re.I)

    @classmethod
    def build_director_company_pattern(cls, directors) -> List[PatternCollection]:
        if not directors:
            return []
        p_companies = []
        for director in directors:
            english_company = clean_txt(director.english_company).replace("(", r"\(")
            english_company = english_company.replace(")", r"\)")
            names = [r"\s*".join(english_company.split())]
            if director.chinese_company:
                names.append(director.chinese_company)
            new_code = str(director.stock_code).lstrip("0")
            names.append(rf"stock\s*code[:：]\s*0*?{new_code}")
            p_companies.append(PatternCollection(names, re.I))
        return p_companies

    @staticmethod
    def rotate_names(director) -> List[str]:
        return DirectorCVMatcher.rotate_director_names(director)

    def build_short_name(self, director, full_name=False) -> str:
        s_names = []
        director_names = clean_director_name(director.english_name).split()
        for name in director_names:
            s_names.append(rf"({self.R_NAME_PREFIX})\s*{name}(.s)?(\W|$)")
        if full_name:
            s_names.extend(self.rotate_names(director))
        if alias_names := self.director_name_mapping.get(director.english_name):
            s_names.extend([rf"{self.R_NAME_PREFIX}\s*{name}(\W|$)" for name in alias_names])
        return "|".join(s_names)

    def build_company_results(self, elements: List[dict], directors: list):
        results = []
        p_companies = self.build_director_company_pattern(directors)

        for ele in elements:
            if not p_companies:
                break
            if ele["class"] == "PARAGRAPH":
                element_text = ele.get("text") or ""
                # aged_element = DirectorCVMatcher.P_DIRECTOR_AGED.nexts(element_text)
                for sub_text, (start, end) in split_paragraph(element_text, need_pos=True):
                    match = False
                    if not p_companies:
                        break
                    c_sub_text = clean_txt(sub_text)
                    for p_company in reversed(p_companies):
                        if res := p_company.nexts(c_sub_text):
                            if (res.end() - res.start()) == len(clean_txt(element_text)):
                                continue
                            p_companies.remove(p_company)
                            match = True
                    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6155#note_656965
                    if match or (self.P_INED_COMPANY_NOT_RECORDED.search(c_sub_text)):
                        results.append(ParagraphResult(ele, ele["chars"][start:end]))
                    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6572#note_685192
                    if not match and self.P_INED_FOREIGN_COMPANY_NOT_RECORDED.nexts(c_sub_text):
                        results.append(ParagraphResult(ele, ele["chars"][start:end]))

        return results

    def build_generality_desc_results(self, elements: List[dict], short_name="", ge_7=False) -> Tuple[bool, list]:
        results = []
        p_un_server = PatternCollection(
            [template.format(name=short_name) for template in self.S_UN_SERVER_LISTED_COMPANIES], re.I
        )
        p_listed_companies = PatternCollection(
            [template.format(name=short_name) for template in self.R_LISTED_GE_7_COMPANIES], re.I
        )
        for ele in elements:
            if ele["class"] == "PARAGRAPH":
                element_text = ele.get("text") or ""
                for sub_text, (start, end) in split_paragraph(element_text, need_pos=True):
                    c_sub_text = clean_txt(sub_text)
                    res = p_listed_companies.nexts(c_sub_text)
                    if ge_7 and res:
                        num = res.group("num")
                        if num.isdigit() and int(num) >= 7 or num.lower() not in self.NUM_WORDS:
                            return True, [ParagraphResult(ele, ele["chars"][start:end])]
                        # 只披露了其余6家上市公司
                        elif num == "six" and self.P_OTHER_LISTED_COMPANIES.nexts(c_sub_text):
                            return True, [ParagraphResult(ele, ele["chars"][start:end])]
                    if not ge_7:
                        if res and self.P_OTHER_LISTED_COMPANIES.nexts(c_sub_text):
                            num = res.group("num")
                            if num.lower() in self.NUM_WORDS and num != "six":
                                return True, [ParagraphResult(ele, ele["chars"][start:end])]
                        if p_un_server.nexts(c_sub_text) and not self.P_FILTER_UN_SERVER_LISTED_COMPANIES.nexts(
                            c_sub_text
                        ):
                            return False, [ParagraphResult(ele, ele["chars"][start:end])]
                else:
                    if (
                        not ge_7
                        and p_un_server.nexts(clean_txt(element_text))
                        and not self.P_FILTER_UN_SERVER_LISTED_COMPANIES.nexts(element_text)
                    ):
                        return False, [ParagraphResult(ele, ele["chars"])]
        return False, results

    def build_disclosed_results(self, elements: List[dict]):
        results = []
        for ele in elements:
            if ele["class"] == "PARAGRAPH":
                element_text = ele.get("text") or ""
                for sub_text, (start, end) in split_paragraph(element_text, need_pos=True):
                    if self.P_SAVE_AS_DISCLOSED.nexts(clean_txt(sub_text)):
                        return [ParagraphResult(ele, ele["chars"][start:end])]
        return results

    def check_lt7_companies_results(self, elements: List[dict], short_name=""):
        results = []
        p_lt7_listed_companies = PatternCollection(
            [template.format(name=short_name) for template in self.R_LISTED_LT_7_COMPANIES], re.I
        )
        for ele in elements:
            if ele["class"] == "PARAGRAPH":
                element_text = ele.get("text") or ""
                for sub_text, (start, end) in split_paragraph(element_text, need_pos=True):
                    c_sub_text = clean_txt(sub_text)
                    res = p_lt7_listed_companies.nexts(c_sub_text)
                    if not res:
                        continue
                    num = res.group("num")
                    if (
                        self.P_OTHER_LISTED_COMPANIES.nexts(c_sub_text) and num.lower() in self.NUM_WORDS
                    ) or num == "seven":
                        return [ParagraphResult(ele, ele["chars"][start:end])]
        return results

    def group_elements_by_director(self, directors: List[dict]):
        directors = [director["director"] for director in directors]
        director_matcher = DirectorCVMatcher(self.pdfinsight)
        director_matcher.extract()
        director_matcher.filter_elements_data_by_director(directors, p_valid_ele=self.P_VALID_ELE)
        self.group_elements = director_matcher.group_elements
        self.director_name_mapping = director_matcher.director_name_mapping
        self.filter_director_data = director_matcher.filter_director_data
        self.director_mapping = director_matcher.filter_director_mapping

    @classmethod
    def build_p_name_by_title(cls, director):
        convert_en_names = cls.rotate_names(director)
        d_names = [
            rf"^({R_PREFIX_NUM})?({cls.R_NAME_PREFIX})?\s*(?:(?P<surname>[a-zA-Z\d]+)[-,]?\s*)?{name}"
            for name in convert_en_names
        ]
        return PatternCollection(d_names, re.I)

    @classmethod
    def search_ined_name(cls, p_name: INEDNamePattern, content: str, p_prefix: PatternCollection = None):
        if res := p_name.match_name(content):
            next_content = content[res.end() :].strip().split()
            upper_chars = []
            for c_chars in next_content:
                if not c_chars[0].isupper() or not cls.P_NAME_PREFIX.sub("", c_chars):
                    break
                upper_chars.append(c_chars)
            if upper_chars and not p_name.match_name(" ".join([res.group(), *upper_chars]), full_name=True):
                return None
            # fid: 85301
            if p_prefix:
                next_content = content[: res.start() + 1].strip().split()
                if next_content and p_prefix.nexts(next_content[-1]):
                    return None
            return res
        return None

    @classmethod
    def filter_element_by_ined(cls, p_name: INEDNamePattern, elements: list):
        new_elements = []
        for ele in elements:
            text = clean_txt(ele.get("text") or "")
            if cls.P_NAME_PREFIX.nexts(text) and not cls.search_ined_name(p_name, text):
                continue
            new_elements.append(ele)
        return new_elements


class AGMM281(AGMM29):
    """
    issue: # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/5973
    """

    P_PROCESS_SPECIAL_RULES = [
        # 评估独立性的过程也可视为选举的过程
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6571#note_688672
        r"(nomination\s*committee|board)\s*believes\s*that.*?\bnot\s*affect\s*.*?(responsibilities|functions)",
        # 84208 对ined的独立性进行审阅
        rf"(nomination\s*committee|board)\s*had\s*(assessed|reviewed).*?independence[^.]*?{R_INED}",
        # fid: 79886 提名**进入董事会，提议连任
        MatchMulti.compile(
            rf"nominated\s*{R_APPELLATION}", r".*?to\s*the\s*Board", r"re-(elect(?:ion)?|appointment)", operator=all
        ),
    ]

    P_KEYWORDS = NeglectPattern.compile(
        match=MatchMulti.compile(
            MatchMulti.compile(
                r"nomination\s*committee|board",
                MatchMulti.compile(
                    # 评估的过程
                    MatchMulti.compile(
                        r"evaluated|reviewed|assessed",
                        MatchMulti.compile(
                            r"procedure\s*and\s*process|re-election",
                            r"(?:professional|extensive|valuable\s*business)\s*experience",
                            r"continu(?:ing|e)\s*(to\s*fulfill|service)",
                            r"(expertise|performance).*?performance\s*satisfactory",
                            r"contributions.*?to\s*the\s*(?:Group|Board|Company)",
                            operator=any,
                        ),
                        operator=all,
                    ),
                    # 可以带来什么
                    MatchMulti.compile(
                        r"considers?(ed)?|of\s*the\s*(?:view|opinion)\s*that|believes",
                        MatchMulti.compile(
                            # docs_scriber#6571#note_685190、docs_scriber#6571#note_685049
                            r"bring(ing)?[^.]*?(?:contribution|perspectives|comments?)",
                            r"(?:(professional|management|business|constructive)[^.]*?(advices|experience|skills?))",
                            r"continue\s*to\s*contribute",
                            r"continu(?:ing|e)\s*((to\s*)?fulfill(ing)?|service)",
                            operator=any,
                        ),
                        operator=all,
                    ),
                    operator=any,
                ),
                operator=all,
            ),
            # 任职资格符合法律法规/公司章程
            MatchMulti.compile(
                r"meets",
                rf"requirements\s*to\s*serve\s*as\s*an?\s*{R_DIRECTOR}",
                r"relevant\s*laws|regulations|Articles\s*of\s*Association",
                operator=all,
            ),
            # 对服务或贡献进行审查/考虑了某些因素,推荐连任
            MatchMulti.compile(
                r"nomination\s*committee|board",
                r"recommendations?|recommend|nominated",
                r"re-(elect(?:ion)?|(re-)?appointment)",
                MatchMulti.compile(
                    r"review[^.]*?(contribution|service)\s*to\s*the\s*company",
                    r"satisfied\s*that.*?required",
                    r"considered[^.]*?but\s*not\s*limited",
                    r"considered\s*the\s*recommendation",
                    r"\bnot\s*affect[^.]*?independent",
                    operator=any,
                ),
                operator=all,
            ),
            # 根据提名政策/公司章程，考虑**/被任命/重新选举，
            MatchMulti.compile(
                MatchMulti.compile(
                    MatchMulti.compile(
                        r"recommendations?|recommend|nominated",
                        r"re-(elect(?:ion)?|appointment)",
                        operator=all,
                    ),
                    MatchMulti.compile(
                        rf"nominations?\s*(?:[^.]*?{R_APPELLATION}[^.]*?)?\s*(were|was)\s*made",
                        r"was\s*(?:appointed|(re-)?elected)",
                        operator=any,
                    ),
                    operator=any,
                ),
                r"(?:nomination|diversity)\s*policy|Article.*?of\s*the\s*Articles",
                r"(?:taking|taken|took)\s*into\s*account|considers?(ed)?|accordance\s*with",
                operator=all,
            ),
            MatchMulti.compile(
                # 任命已经审查和评估
                r"appointments[^.]*?had\s*been\s*(reviewed|assessed)",
                # docs_scriber#6154#note_656891、docs_scriber#6154#note_655781、docs_scriber#6154#note_656105、docs_scriber#6571#note_685194
                r"(nomination\s*committee|board)[^.]*?ha(?:s|d|ving)\s*(?:reviewed|assessed)\s*the\s*[^.]*?(?:structure|independence|composition)",
                # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6154#note_656901
                r"retiring\s*directors\s*standing\s*for\s*re-election[^.]*?bring[^.]*?diversity\s*of\s*perspectives",
                # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6571#note_686196
                # 提出..被选举或任命为ined
                rf"\b(proposed|followed\s*the\s*nomination\s*policy)[^.]*?(?:re-elected|appoint|re-appointment).*?as\s*(an\s*)?{R_DIRECTOR}",
                # 继续连任符合..最佳利益, 排除“建议重新选举”
                r"(?:continuing\s*service|(?<!proposed\s)re-elections?).*?(?:beneficial|best\s*interests?)",
                # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6154#note_655856
                r"(nomination\s*committee|board)[^.]*?(considers|recommended).*?(?:satisfactorily|re-election)",
                *P_PROCESS_SPECIAL_RULES,
                operator=any,
            ),
            operator=any,
        ),
        unmatch=MatchMulti.compile(
            # 忽略授予发行授权和回购授权
            r"grant of the Issuance Mandate",
            r"(?:buy-back|Repurchase|General) Mandate",
            # 再度当选为董事的详情载于本通函附录
            r"re-elected.*?Annual\s*General\s*Meeting.*?to\s*this\s*circular",
            operator=any,
        ),
    )

    P_INED = PatternCollection(
        [
            R_INED,
            r"Retiring\s*Directors",
            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6154#note_656162
            r"re-appointment\s*of\s*all\s*directors",
        ],
        re.I,
    )

    P_NOMINATION_POLICY = MatchMulti.compile(
        r"^In\s*accordance\s*with\s*the\s*nomination\s*policy",
        r"re-election\s*of\s*Directors\s*has\s*been\s*(?:reviewed|assessed)\s*by\s*the\s*Nomination\s*Committee",
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6154#note_656162
        r"^The\s*(nomination\s*committee|board)[^.]*?has\s*(?:reviewed|assessed)\s*the\s*structure.*?nomination\s*principles",
        operator=any,
    )

    def train(self, dataset, **kwargs):
        pass

    def predict_schema_answer(self, elements):
        results = self.predict_by_syllabus(P_LETTER_SYLLABUS) or self.predict_by_syllabus(P_APPENDIX) or []
        return (
            [
                self.create_result(
                    results,
                    schema=self.schema,
                    value=AnswerValueEnum.PS,
                )
            ]
            if results
            else []
        )

    def predict_by_syllabus(self, p_syllabus: PatternCollection):
        results = []
        self.extend_elements_by_syllabus(None)
        elements = self.prepare_syllabus_section_elements(p_syllabus)
        elements = sorted(self.unique_list(elements), key=lambda x: x["index"])
        if not self.directors:
            return results
        full_names, short_names = [], []
        for ined_data in self.directors:
            full_names.append(self.build_director_name_pattern(ined_data["director"]))
            short_names.append(self.build_short_name(ined_data["director"]))
        p_director_name = INEDNamePattern(
            full_name=PatternCollection(full_names, re.I),
            short_name=PatternCollection(short_names, re.I),
        )
        for ele in elements:
            if ele["class"] != "PARAGRAPH":
                continue
            element_text = clean_txt(ele.get("text") or "")
            if (
                p_director_name.match_name(element_text) or self.P_INED.nexts(element_text)
            ) and self.P_NOMINATION_POLICY.search(element_text):
                return [ParagraphResult(ele, ele["chars"])]
            if (p_director_name.match_name(element_text) or self.P_INED.nexts(element_text)) and self.P_KEYWORDS.search(
                element_text
            ):
                results.append(ParagraphResult(ele, ele["chars"]))
        return results


class AGMM282(AGMM29):
    P_INDEPENDENCE = PatternCollection(r"independence", re.I)

    def train(self, dataset, **kwargs):
        pass

    def predict_schema_answer(self, elements):
        results = []
        self.extend_elements_by_syllabus(elements)
        for eles in self.group_elements.values():
            for ele in eles:
                if ele["class"] != "PARAGRAPH" or not self.P_INDEPENDENCE.nexts(
                    self.P_INED.sub("", ele.get("text") or "")
                ):
                    continue
                results.append(ParagraphResult(ele, ele["chars"]))
                break
        return (
            [
                self.create_result(
                    results,
                    schema=self.schema,
                    value=AnswerValueEnum.PS,
                )
            ]
            if results
            else []
        )


class AGMM30(AGMM29):
    R_EXPERIENCE = r"(?:working\s*profiles|cultural|ethnicity|(professional\s*)?experience|skills|qualifications|sufficient\s*time|impartial\s*views|valuable\s*perspectives)"

    P_EXPERIENCE = PatternCollection(R_EXPERIENCE, re.I)

    R_FIELDS = r"medical|health|economic\s*services|housing|land\s*and\s*planning|home\s*affairs|social\s*welfare|civil\s*service|transport|disciplines"

    R_POSITIONS = r"accounting|accountants|financial"

    R_ORG = r"\b(?:board(?:.s)?|group|Nomination\s*Committee)\b"

    P_INVALID_ELEMENT = PatternCollection(
        rf"^[{R_MIDDLE_DASHES}]?\s*\d+\s*[{R_MIDDLE_DASHES}]?$",
    )

    P_EXPERIENCE_INED = PatternCollection(
        [
            rf"brought\s*to\s*the\s*{R_ORG}[^.]+{R_EXPERIENCE}[^.]+appendix",
            r"(?:accumulated|has)[^.]+years[’']?\s*of[^.]*?experience",
            rf"provide.*?(?:beneficial|independent|constructive\s*opinions)[^.]+to\s*the\s*{R_ORG}",
            rf"{R_EXPERIENCE}.*?(?:continuing|continue)\s*(to\s*)?(?:service|contribute).*?(?:beneficial|diversity)\s*(?:to|of)\s*the\s*{R_ORG}",
        ],
        re.I,
    )

    P_INED_POSITION = PatternCollection(
        [
            r"during[^.]+years.*?has\s*worked\s*in\s*many\s*fields",
            r"possesses\s*knowledge\s*in\s*relation",
            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7008#note_716485
            # rf"(?:is\s*a|served)[^.]+({R_POSITIONS})",
            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6156#note_659798  fid:82521
            r"(more\s*than|over)\s*[a-z\d]+\s*(?:years|decades).*?experience",
            r"(ha(ve|s|d)|acquired)\s*(accumulated\s*)?extensive\s*(management\s*)?experience",
            r"extensive\s*(management\s*)?experience[a-z\s]+(for\s*[a-z\d]+\s*years|in\s*the\s*areas)",
        ],
        re.I,
    )

    P_FEE_BY_SKILL = PatternCollection(
        [rf"entitled.*?fee.*?(?:approved|(taken?|took|taking)\s*into\s*account).*?{R_EXPERIENCE}"],
        re.I,
    )

    P_EXPERIENCE_OVERVIEW = PatternCollection(
        [
            rf"consider(?:ed|s)[^.]+continue\s*to\s*bring[^.]*?{R_EXPERIENCE}[^.]*?to\s*the\s*(?:diversity|board)",
            rf"{R_INED}[^.]+nomination\s*committee[^.]+{R_EXPERIENCE}",
            rf"{R_EXPERIENCE}.*?provide[^.]+professional\s*advices",
            rf"retiring\s*directors[^.]*?continue\s*to\s*contribute.*?{R_EXPERIENCE}",
            rf"{R_ORG}\s*is\s*satisfied[^.]+experience",
            rf"as\s*{R_INED}[^.]*?(?:provide|bring)\s*{R_EXPERIENCE}",
            *P_EXPERIENCE_INED.patterns,
            rf"^the\s*{R_ORG}.*?(reviewed|assessed|evaluated|considered).*?{R_EXPERIENCE}",
        ],
        re.I,
    )

    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6156#note_659344
    # 从公司角度，能为公司带来的观点、技能、所履行的职责
    P_COMPANY_PERSPECTIVE = PatternCollection(
        [
            rf"{R_ORG}[^.]+(?:consider(?:ed)?|believed?).*?continue\s*to\s*fulfill\s*[a-z]*?\s*role\s*as\s*{R_INED}",
            rf"^(In\s*reviewing\s*the\s*structure\s*of\s*the\s*{R_ORG}[^.]*?)?\s*The\s*{R_ORG}[^.]*?(reviewed|assessed|evaluated|consider(?:ed)?).*?{R_EXPERIENCE}",
        ],
        re.I,
    )

    # 优先级参考：https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6156#note_659860
    # 1.首先优先标注letter from board中的明确写了，INED将来能为董事带来的技能经验等，关键词：bring to the board，contribute to the board，provide skills ，experience to。。。等
    # 2.若letter from board没有明确写董事带来的技能经验等，则可以框选，董事会reviewed 其相关skills，experience等相关描述
    # 匹配到letter章节内指定段落内容后。直接返回
    # params：段落关键句式、None（默认取INED名字）/指定关键词、是否匹配到多个段落
    P_INED_DESC = PatternCollection([r"(Re-electing|retiring|re-election\s*(as)?)\s*Directors"], re.I)

    P_EXPERIENCE_LETTER_DESC: List[Tuple[MatchMulti, MatchMulti | None, bool]] = [
        (
            MatchMulti.compile(
                MatchMulti.compile(
                    r"(?<!will)\s+consider|^(?!.+\bconsider\b).*",
                    MatchMulti.compile(
                        rf"bring[a-z\s]*?[^.]*?to\s*the\s*{R_ORG}",
                        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6156#note_659414
                        r"(provided?|possess(es)?|bring|equipped)[a-z\s,、]*?(skills|professional\s*advices?|impartial\s*views)",
                        r"has\s*demonstrated\s*the\s*required",
                        rf"(?:contributions?|contribute)\s*(?:to|of)\s*the\s*{R_ORG}",
                        rf"{R_ORG}.*?of\s*the\s*view.*?experience",
                        # 董事会考虑*的某些能力
                        r"(taken?|took|taking)\s*into|including\s*but\s*not\s*limited|regard\s*to\s*the",
                        rf"{R_ORG}[^.]*?(reviewed|assessed|evaluated|considered|view.*?bring|satisfied.*?required|view.*?bring)",
                        r"^In\s*accordance\s*with\s*the\s*nomination\s*policy[^.]",
                        operator=any,
                    ),
                    operator=all,
                ),
                MatchMulti.compile(R_EXPERIENCE, operator=any),
                operator=all,
            ),
            None,
            False,
        ),
        # fid:80593
        (
            MatchMulti.compile(
                rf"^The\s*(?:board|nomination\s*(?:committee)?)[^.]*?nomination\s*policy[^.]*?including.*?{R_EXPERIENCE}",
                operator=any,
            ),
            MatchMulti.compile(R_EXPERIENCE, operator=any),
            False,
        ),
        # fid:85301
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6156#note_659827
        (
            MatchMulti.compile(
                rf"^The\s*{R_ORG}[^.]*?(reviewed|assessed|evaluated|considered).*?{R_EXPERIENCE}",
                operator=any,
            ),
            MatchMulti.compile(r"(Re-electing|retiring)\s*Directors", operator=any),
            False,
        ),
        (
            MatchMulti.compile(
                rf"{R_ORG}\s*has\s*(assessed|reviewed)\s*the\s*(?:(?:re-)?elect(?:ion|ed)?)", operator=any
            ),
            MatchMulti.compile(R_EXPERIENCE, R_FIELDS, operator=any),
            True,
        ),
    ]

    def predict_schema_answer(self, elements):
        results = []
        elements = self.filter_elements_by_director(self.extend_elements_by_syllabus(elements))
        if not self.directors:
            return []
        d_names = []
        value = AnswerValueEnum.PS
        director_elements = []
        for ined_data in self.directors:
            ined_results = []
            p_name = self.build_director_name_pattern(ined_data["director"])
            d_names.append(self.build_short_name(ined_data["director"], full_name=True))
            # 优先从letter章节获取当前董事为董事会带来相关技能经验的描述
            if res := self.find_element_from_letter_syllabus_by_director(ined_data["director"]):
                results.extend(res)
                director_elements.append((ined_data["director"], []))
                continue

            for director_name, eles in self.group_elements.items():
                if not p_name.nexts(director_name):
                    continue
                p_director_skill = MatchMulti.compile(
                    d_names[-1],
                    r"contribute|contribution|provide|bring",
                    self.R_EXPERIENCE,
                    operator=all,
                )
                if res := self.match_ele_by_patterns(eles, self.P_EXPERIENCE_INED):
                    ined_results.extend(res)
                elif res := self.match_ele_by_patterns(eles, p_director_skill):
                    ined_results.extend(res)
                elif res := self.match_ele_by_patterns(eles, self.P_INED_POSITION):
                    ined_results.extend(res)
                director_elements.append((ined_data["director"], ined_results))
                break
        r_names = "|".join(d_names)
        p_director_skill = MatchMulti.compile(
            r_names, "contribute|contribution|provide|bring", self.R_EXPERIENCE, operator=all
        )

        # 某某某可以带来某些经验
        results.extend(self.match_ele_by_patterns(elements, p_director_skill))
        # 董事会认为*拥有某些经验、能力
        results.extend(self.match_ele_by_patterns(elements, self.P_EXPERIENCE_OVERVIEW, multimatch=True))

        # 检查当前答案中不存在哪些新选举的ined，取对应简历中的技能经验
        for director, eles in director_elements:
            if self.filter_element_by_ined(self.build_ined_name_pattern(director), results):
                continue
            if not eles:
                value = AnswerValueEnum.ND
            results.extend(eles)
        # 公司观点：继续履行职责
        results = self.build_results(results, value=value)
        results_index = [ele.element["index"] for res in results for ele in res.element_results]
        return results + [
            self.create_result(
                [res],
                schema=self.schema,
                value=value,
            )
            for res in self.match_sentence_by_patterns(
                [ele for ele in elements if ele["index"] not in results_index],
                self.P_COMPANY_PERSPECTIVE,
            )
        ]

    def find_element_from_letter_syllabus_by_director(self, director) -> list:
        # LETTER FROM THE BOARD章节可能会对每个INED的经验分开描述
        if res := self.match_ined_experience_by_name([director]):
            return res
        p_name = self.build_ined_name_pattern(director)
        # 如在APPENDIX章节明确披露了ined的能力和经验，则无需提取细节
        elements = []
        for name, eles in self.director_mapping.items():
            if p_name.match_name(name, full_name=True):
                elements = eles
                break
        if res := self.match_experience_by_syllabus(p_ined_name=p_name, p_syllabus=P_APPENDIX, elements=elements):
            return res
        return []

    def build_ined_name_pattern(self, director):
        return INEDNamePattern(
            full_name=PatternCollection(self.build_director_name_pattern(director), re.I),
            short_name=PatternCollection(self.build_short_name(director), re.I),
        )

    def filter_elements_by_director(self, elements):
        """
        过滤包含董事简历的段落
        """
        element_idxes = [ele["index"] for ele in chain.from_iterable(self.group_elements.values())]
        elements = [ele for ele in elements if ele["index"] not in element_idxes]
        return sorted(self.unique_list(elements), key=lambda x: x["index"])

    def match_experience_by_syllabus(
        self, p_ined_name: INEDNamePattern, p_syllabus: PatternCollection = None, elements=None
    ):
        if not p_syllabus and not elements:
            return []
        elements = elements or self.prepare_syllabus_section_elements(
            syllabus_pattern=(p_syllabus or P_LETTER_SYLLABUS)
        )
        elements = sorted(self.unique_list(elements), key=lambda x: x["index"])
        results = []
        for p_desc, p_key, match_multi in self.P_EXPERIENCE_LETTER_DESC:
            for idx, ele in enumerate(elements):
                text = clean_txt(ele.get("text") or "")
                if p_desc.search(text):
                    end_pos = None
                    for _idx, _ele in enumerate(elements[idx : idx + 4]):
                        text = clean_txt(_ele.get("text") or "")
                        if (
                            (p_key and p_key.search(text))
                            or (self.search_ined_name(p_ined_name, text, self.P_EXCEPT_INED))
                            or self.P_INED_DESC.nexts(text)
                        ) and self.P_EXPERIENCE.nexts(text):
                            end_pos = _idx
                        if end_pos and not match_multi:
                            break
                    if end_pos is not None and self.filter_element_by_ined(
                        p_ined_name, elements[idx : (idx + end_pos + 1)]
                    ):
                        results.extend(elements[idx : (idx + end_pos + 1)])
            if results:
                return [
                    ele
                    for ele in results
                    if not self.P_INVALID_ELEMENT.nexts(clean_txt(ele.get("text") or ""))
                    and not self.P_SYLLABUS.nexts(clean_txt(ele.get("text") or ""))
                ]
        return []

    def match_ined_experience_by_name(self, directors):
        elements = self.prepare_syllabus_section_elements(syllabus_pattern=P_LETTER_SYLLABUS)
        results = []
        for director in directors:
            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6156#note_659860
            # 如在LETTER\s*FROM\s*THE\s*BOARD章节明确披露了ined的能力和经验，则无需提取细节
            p_name = self.build_ined_name_pattern(director)
            results.extend(self.match_experience_by_syllabus(p_name, elements=elements))
        elements = [
            ele
            for ele in sorted(self.unique_list(elements), key=lambda x: x["index"])
            if (
                self.P_EXPERIENCE_INED.nexts(clean_txt(ele.get("text") or ""))
                or self.P_INED_POSITION.nexts(clean_txt(ele.get("text") or ""))
            )
        ]
        for director in directors:
            p_name = self.build_director_name_pattern(director)
            s_name = self.build_short_name(director)
            for ele in elements:
                text = clean_txt(ele.get("text") or "")
                if p_name.nexts(text) or PatternCollection([s_name], re.I).nexts(text):
                    results.append(ele)
                    break
        return results

    def filter_results_by_director_name(self, elements, p_name: INEDNamePattern = None):
        elements = sorted(self.unique_list(elements), key=lambda x: x["index"])
        return self.filter_element_by_ined(p_name, elements) if p_name else elements

    def build_results(self, elements, p_name: INEDNamePattern = None, value=AnswerValueEnum.PS):
        elements = self.filter_results_by_director_name(elements, p_name)
        return [
            self.create_result(
                [ParagraphResult(ele, ele["chars"])],
                schema=self.schema,
                value=value,
            )
            for ele in elements
        ]

    def match_ele_by_patterns(self, elements, p_sentence: Union[PatternCollection, MatchMulti], multimatch=False):
        p_search = p_sentence.search if isinstance(p_sentence, MatchMulti) else p_sentence.nexts
        results = []
        for ele in elements:
            text = clean_txt(ele.get("text") or "")
            if p_search(text) and not self.P_FEE_BY_SKILL.nexts(text):
                results.append(ele)
                if not multimatch:
                    break
        return results

    @staticmethod
    def match_sentence_by_patterns(elements, p_sentence: PatternCollection):
        results = []
        for ele in elements:
            if ele["class"] != "PARAGRAPH":
                continue
            element_text = ele.get("text") or ""
            for sub_text, (start, end) in split_paragraph(element_text, need_pos=True):
                if p_sentence.nexts(clean_txt(sub_text)):
                    results.append(ParagraphResult(ele, ele["chars"][start:end]))
                    break
        return results


class AGMM31(AGMM30):
    P_VALID_ELE = PatternCollection([r"diversity\s*(?:factors|of\s*the\s*board)?"], re.I)

    P_SYLLABUS = P_APPENDIX

    P_CONTRIBUTIONS = MatchMulti.compile(
        r"(?:enhance|contribute|benefits|increase|reference|consider)[^.]*?divers(?:e|ity|ified)",
        r"divers(?:e|ity|ified)\s*(?:perspectives|factors|of\s*the\s*board|in\s*the\s*aspects)",
        r"(nomination|the)\s*committee[^.]+(?:take(n|ing)?|took)\s*into[^.]+divers(?:e|ity|ified)",
        r"(?:provide|bring|regard)[^.]+divers(?:e|ity|ified)",
        MatchMulti.compile(R_INED, r"divers(?:e|ity|ified)", operator=all),
        r"(?:contribution|contribute)\s*to\s*the\s*divers(?:e|ity|ified)(\s*development)?\s*of\s*the\s*board",
        operator=any,
    )

    def predict_schema_answer(self, elements):
        results = []
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6157#note_659492
        for ele in elements:
            if self.P_CONTRIBUTIONS.search(clean_txt(ele.get("text") or "")):
                results.append(ele)
        if not results:
            self.extend_elements_by_syllabus(None)
            if not self.directors:
                return []

            for ined_data in self.directors:
                p_name = self.build_director_name_pattern(ined_data["director"])
                for director_name, eles in self.group_elements.items():
                    if not p_name.nexts(director_name):
                        continue
                    if res := self.match_ele_by_patterns(eles, self.P_VALID_ELE):
                        results.extend(res)
                    break
        return self.build_results(self.filter_results_by_director_name(results))


if __name__ == "__main__":
    para = "Ms. WONG has over 20 years of experience in the accounting and corporate finance industry."
    print(AGMM30.P_EXPERIENCE.nexts(para))
