import re

from remarkable.common.common_pattern import R_<PERSON>XCHANGE
from remarkable.common.constants import AnswerV<PERSON>ue<PERSON>num
from remarkable.common.pattern import <PERSON><PERSON><PERSON><PERSON>, PatternCollection
from remarkable.common.util import clean_txt, split_paragraph
from remarkable.predictor.hkex_predictor.schemas.pattern import R_<PERSON><PERSON><PERSON>_CEO, R_<PERSON><PERSON>, R_SAVE_AS, reg_words
from remarkable.predictor.models.para_match import ParaMatch
from remarkable.predictor.schema_answer import Char<PERSON><PERSON><PERSON>, ParagraphResult

R_BE = r"\b(be|been|is|are|was|were)\b"
R_APPOINT = r"(appointed|joined|invited|serv(ed?|ing)|act(ed)?)"
R_PREFIX = r"(?:independent )?(?:(non-?)?executive)?"
R_POSITION = rf"\b((Co-)?{R_CHAIR_CEO}|\b(INED|NED)\b|({R_PREFIX}|board\sof)\s*directors?|(senior|junior)\s*Adviser|deputy\shead|assistant|Chief\sFinancial\sOfficer|CFO|research\s*advisor|partner|vice\s*president)\b"

R_GENERAL_POSITION = r"(manager|officer|director|assistant|advisor)"

P_POSITION = PatternCollection(R_POSITION, re.I)

PS_REGS = [
    # 1 系动词 + 职位
    rf"(?<!remuneration for ){R_BE}\s*{reg_words(0, 2)}(a|an|the)\s*{R_POSITION}",
    rf"(?<!remuneration for )\sisa\s{R_POSITION}",
    # 2 被委任为 + 职位
    rf"{R_APPOINT}( (to )?(the )?board )?\s*as\s*(a|an|the)?\s*(group’s\s)?{R_POSITION}",
    # 3 系动词 +  成员|职位 + 委员会
    rf"{R_BE}\s*{reg_words(0, 2)}(a|an|the)\s*({R_CHAIR_CEO}|member)\s*of\s*{reg_words(0, 3)}committee",
    # 4  serving on + 委员会
    rf"serving\s*on\s*{reg_words(0, 3)}committee",
    # 5 保留职位
    rf"stepp(ed)?\s*down.*remains\s*as\s*(a|an|the)\s*{R_POSITION}",
]

NEG_PS_REGS = PatternCollection(
    [
        rf"entitled\s{reg_words(0, 5)}(remuneration|emolument)",
        r"director’s\s*emolument",
        r"other\s*public\s*compan(y|ies)",
        r"prior\s*to\s*joining\s*the\s*(group|company|bank)",
    ],
    re.I,
)

P_M34_PS = PatternCollection(
    PS_REGS,
    re.I,
)
P_AGE = re.compile(r"aged?[,，]?\s*\d+")

P_OUR_COMP = MatchMulti.compile(
    r"(?<!prior toj oining )our\s*(company|group)", r"subsidiar(ies|y)\s*of\s*the\s*(company|group)", operator=any
)

R_OTHER_COMP = MatchMulti.compile(
    r"stock\s*code",
    r"other\s*compan(ies|y)",
    rf"listed\s*on.*(exchange|{R_EXCHANGE})",
    r"chinese\s*People’s\s*Political\s*Consultative\s*Conference",
    operator=any,
)
R_COMPANY = MatchMulti.compile(r"(?:公司|Limited|Co\.[,，]\sLtd)", operator=any)
R_THE_COMP = MatchMulti.compile(r"the\s*(company|group)", operator=any)

R_HOLD_ANY = r"(h[oe]lds?)\s(any\s)?(other\s)?positions?\s(in|with)\sthe\s(group|company|Bank or its subsidiaries)"

P_M34_NS = PatternCollection(
    [
        rf"{R_SAVE_AS}.*{R_NOT}{reg_words(0, 2)}{R_HOLD_ANY}",
    ],
    re.I,
)

P_M34_ALL_NS = PatternCollection(
    [
        rf"{R_SAVE_AS}.*none\sof\s{reg_words(0, 3)}directors?\s{R_HOLD_ANY}",
        rf"{R_SAVE_AS}.*each\sof\s{reg_words(0, 3)}directors?\s.*{R_NOT}\s*{R_HOLD_ANY}",
    ],
    re.I,
)


P_COMP_NAME = PatternCollection(
    [
        rf"(as|and)?\s*(a|an|the)?\s*{R_POSITION}\sof\s(?P<comp>{reg_words(1, 8)})\b(since|from|and)",
    ],
    re.I,
)

P_SPECIAL_COMP = MatchMulti.compile(
    r"party\s*committee|University",
    r"Belt & Road Culture Exchange Foundation",
    r"\b(Inventprise|NovaStream Biotech|GRID EUROPE LTD|MassBiologic|Nosocomial\s*Vaccine\s*Corporation)\b",
    r"\b[(（]?(UNOOSA|ROAP)[)）]?\b",
    r"\bCUVI\b",
    r"Jiusan\s*Society|Haier Group|Intesa Sanpaolo",
    operator=any,
)

P_COMP_SUFFIX = re.compile(r"[(（]?(HOLDINGS|COMPANY|LIMITED|SECURITIES)[)）]?", re.I)


class AGMM34(ParaMatch):
    def predict_schema_answer(self, elements):
        # 提取当前待选董事在本公司及分公司任职的职位经历
        answers = []
        re_elect_directors = self.predictor.prophet.metadata.get("re_elect_directors")
        comp_name = self.predictor.prophet.metadata.get("company_name")
        value = AnswerValueEnum.PS.value
        if not re_elect_directors:
            return answers
        name_ans = {}
        for re_elect_director in re_elect_directors:
            director_ans = []
            range_elements = [
                ele
                for ele in self.get_candidate_elements_by_range(re_elect_director["range"], need_score=False)
                if ele.get("class") == "PARAGRAPH"
            ]
            element_results = self.predict_director_answer(re_elect_director["name"], range_elements, comp_name)
            if element_results:
                director_ans.append({self.schema.name: [self.create_result(element_results, column=self.schema.name)]})
            elif other_ans := self.find_other_appointment(re_elect_director):
                director_ans.append({self.schema.name: [self.create_result(other_ans, column=self.schema.name)]})
            else:
                value = AnswerValueEnum.ND.value
            answers.extend(director_ans)
            name_ans[re_elect_director["name"][0]] = director_ans
        # 在描述每个董事范围外找全否定描述
        if all_ns_answers := self.find_all_ns_answer(re_elect_directors, elements):
            answers.append({self.schema.name: [self.create_result(all_ns_answers, column=self.schema.name)]})
            value = AnswerValueEnum.NS.value if value == AnswerValueEnum.ND.value else AnswerValueEnum.PS.value
        for common_result in self.get_common_predictor_results(answers):
            common_result.update_answer_value(value)
        return answers

    def predict_director_answer(self, names, elements, comp_name):
        res = []
        follow_indices = set()
        p_comp_name = None
        if comp_name:
            clean_comp_name = P_COMP_SUFFIX.sub("", comp_name).strip()
            p_comp_name = re.compile(rf"{R_BE}.*{R_GENERAL_POSITION}.*{re.escape(clean_comp_name)}", re.I)
        for element in elements:
            if element["index"] in follow_indices:
                continue
            ele_text = element.get("text", "")
            all_chars = element.get("chars") or []
            for sentence, pos in split_paragraph(element.get("text"), separator=self.para_separator, need_pos=True):
                start, end = pos
                c_sentence = clean_txt(sentence)
                if P_M34_PS.nexts(c_sentence) and not NEG_PS_REGS.nexts(c_sentence):
                    # 如果是在描述年龄之后，可以不用判断是否是本公司
                    if P_AGE.search(c_sentence):
                        res.append(CharResult(element, all_chars[start:end]))
                    elif self.is_the_company(element, c_sentence):
                        res.append(CharResult(element, all_chars[start:end]))
                elif P_M34_NS.nexts(c_sentence):
                    res.append(CharResult(element, all_chars[start:end]))
                elif p_comp_name and p_comp_name.search(c_sentence):
                    res.append(CharResult(element, all_chars[start:end]))
            if self.as_follow_pattern.nexts(ele_text):
                below_elements = self.find_blow_para_elements(element)
                if below_elements:
                    follow_indices.update({i["index"] for i in below_elements})
                    res.extend([ParagraphResult(i, i["chars"]) for i in below_elements])
        return res

    def find_all_ns_answer(self, re_elect_directors, elements):
        res = []
        for element in elements:
            if re_elect_directors[0]["range"][0] < element["index"] < re_elect_directors[-1]["range"][1]:
                continue
            res.extend(self.create_content_result(element, P_M34_ALL_NS))
        return res

    def find_other_appointment(self, re_elect_director):
        # 1. 从起始位置找
        _, first_ele = self.pdfinsight.find_element_by_index(re_elect_director["ele_idx"])
        if P_POSITION.nexts(first_ele["text"]) and len(first_ele["text"]) < 50:
            return [ParagraphResult(first_ele, first_ele["chars"])]
        # 从syllabus 中找
        syllabus = self.pdfinsight.syllabus_reader.syllabus_dict.get(first_ele["syllabus"])
        _, syllabus_ele = self.pdfinsight.find_element_by_index(syllabus["element"])
        syllabus_text = syllabus_ele.get("text", "")
        if P_POSITION.nexts(syllabus_text) and len(syllabus_text) < 50 and first_ele["index"] - 10 > syllabus["index"]:
            return [ParagraphResult(syllabus_ele, syllabus_ele["chars"])]
        # 找不到 从 notice of agm 中找 re-relect as 的描述
        name = "|".join(re.escape(name) for name in re_elect_director["name"])
        p_reelect = re.compile(rf"to\s*re-elect\s*({name})\s*as\s*(a|an|the)?\s*{P_POSITION}", re.I)
        elements = self.get_syllabus_candidates()
        for element in elements:
            ele_text = element.get("text", "")
            if p_reelect.search(ele_text):
                return [ParagraphResult(element, element["chars"])]
        return []

    @staticmethod
    def extract_comp_name(text):
        comp_names = []
        for matched in P_COMP_NAME.finditer(text):
            comp = matched.group("comp").strip()
            comp_names.append(comp)
        return comp_names

    @staticmethod
    def is_the_company(element, sentence):
        """判断描述的是不是本公司"""

        if P_SPECIAL_COMP.search(sentence):
            return False

        # 1  有our company/group，子公司 类似描述
        if P_OUR_COMP.search(sentence):
            return True

        # 2 为其他公司/上市公司
        if R_OTHER_COMP.search(sentence):
            return False

        # 3. 如果提取不到公司信息， 且有 the company ，the group 此类描述
        if R_COMPANY.search(sentence):
            return False

        return True
