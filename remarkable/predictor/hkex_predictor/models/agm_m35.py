import re
import zoneinfo

from dateutil.relativedelta import relativedelta

from remarkable.common.common import is_table_elt
from remarkable.common.common_pattern import R_MIDDLE_DASHES
from remarkable.common.constants import AnswerValueEnum
from remarkable.common.pattern import MatchMult<PERSON>, PatternCollection
from remarkable.common.util import clean_txt, index_in_space_string, split_paragraph
from remarkable.predictor.common_pattern import R_EN_MONTH
from remarkable.predictor.hkex_predictor.models.agm_m34 import R_POSITION
from remarkable.predictor.hkex_predictor.pattern import R_BE
from remarkable.predictor.hkex_predictor.schemas.pattern import R_APPELLATION, reg_words
from remarkable.predictor.models.para_match import ParaMatch
from remarkable.predictor.schema_answer import <PERSON><PERSON><PERSON><PERSON><PERSON>, Paragraph<PERSON><PERSON>ult
from remarkable.predictor.utils import extract_date

# 专业资格
R_CPA = r"\b(CPA|Certified Public Accountants?|FCCA)\b"
R_PROFESSION = r"(architect|professor|solicitor|barrister|pharmacist|accountant)"
R_ASSOCIATION = r"(medical\sassociation|association\s*of\s*chartered\s*certified)"
P_EXPRIENCE = PatternCollection(
    [
        rf"(qualified|qualification|admitted)\s(of|as)?\s*(a|an|the)?\s*{R_PROFESSION}",
        rf"(acquired|obtained)\s{reg_words(0, 2)}(qualification|certificate)",
        r"(professional|practice)\s*qualification",
        r"qualified\s*to\s*practice",
        r"certified.*?(practicing|qualification)",
        rf"(?<!founder )(member|fellow)\s*of\s*(the )?{reg_words(0, 3)}(institute|{R_CPA}|{R_ASSOCIATION})",
        rf"^{R_APPELLATION}\s*{reg_words(1, 5)}{R_CPA}",
        rf"({R_BE}|as)\s(a|an|the)\spracti[cs]ing\s{R_PROFESSION}",
        rf"^(she|he)\s*{R_BE}\s*(a|an|the)?\s*({R_CPA}|senior\s*engineer)\.?$",
    ],
    re.I,
)

R_EN_DATE = (
    rf"\b(\d{{1,2}}(st|nd|rd|th)?[{R_MIDDLE_DASHES}/\s]*)?({R_EN_MONTH}[,，]?[{R_MIDDLE_DASHES}/\s]*)?(20|19)\d{{2}}\b"
)
P_EN_DATE = PatternCollection(
    rf"\b(?P<day>\d{{1,2}})?(st|nd|rd|th)?[,，]?[{R_MIDDLE_DASHES}/\s]*(?P<mon>{R_EN_MONTH})?[,，]?[{R_MIDDLE_DASHES}/\s]*(?P<year>(20|19)\d{{2}})\b",
    re.I,
)
P_BETWEEN = PatternCollection(
    [
        rf"(between|from)\s(?P<start>{R_EN_DATE})\s(and|to)\s(?P<end>{R_EN_DATE})",
        rf"resign(ed)?\s{reg_words(0, 5)}(in|at)\s(?P<end>{R_EN_DATE})",
        rf"\bin\s(?P<end>{R_EN_DATE})",
    ],
    re.I,
)

P_FUZZY_TIME = PatternCollection(r"prior\sto\s*(j oin|join)ing\sthe\s(group|company)", re.I)

P_SINCE = PatternCollection([rf"since\s(from\s)?{R_EN_DATE}(?!\s(from|to)\b)"], re.I)

# https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6578#note_687640
P_APPOINT_FOLLOW = MatchMulti.compile(
    "(served|serves) as|appointed", "following listed companies", r"[:：]$", operator=all
)
P_QUALIFICATIONS_FOLLOW = MatchMulti.compile(r"qualified as and has been", r"[:：]$", operator=all)

P_M35_APPOINT = re.compile(
    rf"\b(be|been|is|are|was|were|serve(s|d))\b\s{reg_words(0, 3)}(a|an|the)?\s*{R_POSITION}", re.I
)

# 上述公司都是上市公司
P_ABOVBE_LISTED = PatternCollection(
    [r"the shares of those companies are listed on.*?stock exchange"],
    re.I,
)


class AGMM35(ParaMatch):
    """
    1. 找到M33中提取到的每个拟任董事
    2. 提取拟任董事其他公司重要任职情况
    3. 提取拟任董事的职业经验等描述
    4. 根据 2 提取的内容判断枚举
        NS - 所有董事都没有过去三年在其他公司任职的经历
        ND - 任意一名董事没有披露上述任何内容
        PS - 所有董事都有披露 任职经历 或 经验
    """

    @property
    def ps_pattern(self, col=None):
        regs = self.get_config("ps_pattern", column=col)
        return PatternCollection(regs, flags=self.flags)

    @property
    def ns_pattern(self, col=None):
        regs = self.get_config("ns_pattern", column=col)
        return PatternCollection(regs, flags=self.flags)

    @property
    def all_ns_pattern(self, col=None):
        regs = self.get_config("all_ns_pattern", column=col)
        return PatternCollection(regs, flags=self.flags)

    @property
    def item_separator(self):
        value = self.get_config("item_separator", None)
        if value and not isinstance(value, re.Pattern):
            raise ValueError(f"{value} is not a valid regex")
        return value

    def predict_schema_answer(self, elements):
        answers = []
        re_elect_directors = self.predictor.prophet.metadata.get("re_elect_directors")
        value = AnswerValueEnum.NS.value
        all_ns_answers = []
        values = []
        if not re_elect_directors:
            return answers

        for idx, re_elect_director in enumerate(re_elect_directors):
            # ps 在其他公司的任职经历
            ps_answers = []
            # ns 没有在其他公司任职经历
            ns_answers = []
            # 专业资格(不影响枚举)
            exprience_answers = []
            existed_indices = set()
            existed_para_res = set()
            for element in self.get_candidate_elements_by_range(re_elect_director["range"], need_score=False):
                if element["index"] in existed_indices:
                    continue
                if is_table_elt(element):
                    continue
                if appoint_follows := self.find_appoint_follow(element):
                    existed_indices.update({appoint_follow["index"] for appoint_follow in appoint_follows})
                    ps_answers.extend(
                        [ParagraphResult(appoint_follow, appoint_follow["chars"]) for appoint_follow in appoint_follows]
                    )
                elif q_follows := self.find_qualifications_follow(element):
                    existed_indices.update({q_follow["index"] for q_follow in q_follows})
                    exprience_answers.extend([ParagraphResult(q_follow, q_follow["chars"]) for q_follow in q_follows])
                all_ns_answer = []

                if idx == len(re_elect_directors) - 1:
                    # 先按分句子提取
                    all_ns_answer = self.create_content_result(element, self.all_ns_pattern)
                    # 分句提取不到 按整体判断
                    if not all_ns_answer:
                        all_ns_answer = self.create_all_ns_answer(element, self.all_ns_pattern)
                    if all_ns_answer:
                        all_ns_answers.extend(all_ns_answer)
                ps_answers.extend(self.create_ps_answer(element))
                if not all_ns_answer:
                    ns_answers.extend(self.create_content_result(element, self.ns_pattern))
                if not ns_answers and not all_ns_answer and self.item_separator:
                    ns_answers.extend(self.create_spilit_ns_content(element, self.item_separator))

                exprience_answers.extend(self.create_content_result(element, P_EXPRIENCE))
                # element_result.append(result)
            # 有任意一个人没有提取到就为ND:
            if not ps_answers and not ns_answers:
                values.append(AnswerValueEnum.ND.value)
            # 有任意一个人为ps则为ps
            elif ps_answers:
                values.append(AnswerValueEnum.PS.value)
            else:
                values.append(AnswerValueEnum.NS.value)
            all_answers = ps_answers + ns_answers + exprience_answers
            if new_answer := self.filter_existed_answer(all_answers, existed_para_res):
                answers.append({self.schema.name: [self.create_result(new_answer, column=self.schema.name)]})

        # 提取总体描述的为NS的描述
        if not all_ns_answers:
            # 前面提取不到 总体描述ns的情况 再从初步定位剩下的范围内找
            for element in elements:
                if re_elect_directors[0]["range"][0] < element["index"] < re_elect_directors[-1]["range"][1]:
                    continue
                if left_ns_answer := self.create_content_result(element, self.all_ns_pattern):
                    all_ns_answers.extend(left_ns_answer)
                elif left_ns_answer := self.create_all_ns_answer(element, self.all_ns_pattern):
                    all_ns_answers.extend(left_ns_answer)
        if all_ns_answers:
            # 提取总体描述了NS的情况
            if new_all_ns_ans := self.filter_existed_answer(all_ns_answers, existed_para_res):
                answers.append({self.schema.name: [self.create_result(new_all_ns_ans, column=self.schema.name)]})
            value = AnswerValueEnum.PS.value if AnswerValueEnum.PS.value in values else AnswerValueEnum.NS.value
        else:
            if AnswerValueEnum.ND in values:
                value = AnswerValueEnum.ND.value
            elif AnswerValueEnum.PS in values:
                value = AnswerValueEnum.PS.value
        for common_result in self.get_common_predictor_results(answers):
            common_result.update_answer_value(value)
        return answers

    def filter_existed_answer(self, answers, existed_indices):
        new_answers = []
        for answer in answers:
            if isinstance(answer, ParagraphResult):
                if answer.element["index"] not in existed_indices:
                    new_answers.append(answer)
                existed_indices.add(answer.element["index"])
            else:
                new_answers.append(answer)
        return new_answers

    def create_all_ns_answer(self, element, all_ns_pattern):
        clean_element_text = clean_txt(element.get("text", ""))
        if not clean_element_text:
            return []
        if matched := all_ns_pattern.nexts(clean_element_text):
            return self._create_content_result(element, matched)
        # 否定描述和关键词在分开描述
        if not self.as_follow_pattern.nexts(clean_element_text):
            return []
        follow_paras = self.find_blow_paras(element)
        if len(follow_paras) < 2:
            return []
        for follow_para in follow_paras[1:]:
            para_text = clean_txt(follow_para.get("text", ""))
            if all_ns_pattern.nexts(clean_element_text + para_text):
                return [ParagraphResult(i, i["chars"]) for i in [element, follow_para]]
        return []

    def create_ps_answer(self, element):
        result = []
        all_chars = element.get("chars") or []
        element_text = element.get("text")
        sen_list = list(split_paragraph(element_text, separator=self.para_separator, need_pos=True))
        sen_list.reverse()
        above_listed = False
        for sentence, pos in sen_list:
            start, end = pos
            c_sentence = clean_txt(sentence)
            if P_ABOVBE_LISTED.nexts(c_sentence):
                above_listed = True
                continue
            if not self.ps_pattern.nexts(c_sentence):
                if not above_listed or not P_M35_APPOINT.search(c_sentence):
                    continue
            # 提取时间判断是否是 agm报告发行的三年内的任职经历
            published_at = self.predictor.prophet.metadata.get("published_at")
            if not P_SINCE.nexts(c_sentence) and published_at:
                end_dates = self.extract_end_dates(c_sentence)
                # for matched in P_BETWEEN.finditer(c_sentence):
                #     end_date = extract_date(P_EN_DATE, matched.group("end"))
                #     if end_date:
                #         end_dates.append(end_date.replace(tzinfo=zoneinfo.ZoneInfo("Asia/Shanghai")))
                cutoff_date = published_at + relativedelta(years=-3)
                if end_dates and (max(end_dates) < cutoff_date):
                    continue
                elif P_FUZZY_TIME.nexts(c_sentence):
                    continue
            if start == 0 and end == len(element_text):
                # 如果匹配了整段，直接取段落，以确保框线
                return [ParagraphResult(element, all_chars)]
            result.append(CharResult(element, all_chars[start:end]))
        return result

    def extract_end_dates(self, text):
        end_dates = []
        for matched in P_BETWEEN.finditer(text):
            end_date = extract_date(P_EN_DATE, matched.group("end"))
            if end_date:
                end_dates.append(end_date.replace(tzinfo=zoneinfo.ZoneInfo("Asia/Shanghai")))
        return end_dates

    def find_qualifications_follow(self, element):
        res = []
        ele_text = element.get("text", "")
        if P_QUALIFICATIONS_FOLLOW.search(ele_text):
            below_elements = self.find_blow_para_elements(element)
            if below_elements:
                return below_elements
        return res

    def find_appoint_follow(self, element):
        res = []
        ele_text = element.get("text", "")
        if not P_APPOINT_FOLLOW.search(ele_text):
            return []
        below_elements = self.find_blow_para_elements(element)
        if not below_elements:
            return []
        published_at = self.predictor.prophet.metadata.get("published_at")
        if not published_at:
            return below_elements
        cutoff_date = published_at + relativedelta(years=-3)
        for below_element in below_elements:
            text = below_element.get("text", "")
            end_dates = self.extract_end_dates(text)
            if not P_SINCE.nexts(text) and end_dates and (max(end_dates) < cutoff_date):
                continue
            res.append(below_element)
        return res

    def create_spilit_ns_content(self, element, separator):
        # 段落拆成每个子项再和第一句拼接起来后匹配
        # http://100.64.0.105:55647/#/project/remark/324779?treeId=51075&fileId=89085&schemaId=33&projectId=51075&schemaKey=M38-interests%20in%20shares%20of%20the%20issuer
        clean_text = clean_txt(element["text"])
        ret = []
        all_chars = element.get("chars") or []
        items = []
        start = 0
        for matched in separator.finditer(clean_text):
            items.append((start, matched.start()))
            start = matched.end()
        items.append((start, len(clean_text)))
        if len(items) < 3:
            return ret
        pre_sen = clean_text[items[0][0] : items[0][1]]
        pre_sp_start, pre_sp_end = index_in_space_string(element["text"], (items[0][0], items[0][1]))
        for item_start, item_end in items[1:]:
            combine_sen = pre_sen + clean_text[item_start:item_end]
            if self.ns_pattern.nexts(combine_sen):
                sp_start, sp_end = index_in_space_string(element["text"], (item_start, item_end))
                ret.append(CharResult(element, all_chars[pre_sp_start:pre_sp_end] + all_chars[sp_start:sp_end]))
        return ret
