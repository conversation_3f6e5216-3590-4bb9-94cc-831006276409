import json
import logging
import re
from collections import defaultdict
from copy import copy
from itertools import chain

from remarkable.common.constants import AnswerValueEnum, PolicyEsgRules, TableType
from remarkable.common.pattern import Match<PERSON>ult<PERSON>, NeglectPattern
from remarkable.common.util import clean_txt, split_element
from remarkable.config import get_config
from remarkable.pdfinsight.parser import parse_table
from remarkable.pdfinsight.reader import PdfinsightSyllabus
from remarkable.predictor.eltype import ElementClassifier
from remarkable.predictor.hkex_predictor.models.policy_esg_base_model import PolicyEsgBaseModel, ResponseModels
from remarkable.predictor.hkex_predictor.models.policy_esg_e1 import prepare_data_prompt
from remarkable.predictor.schema_answer import Cha<PERSON><PERSON><PERSON><PERSON>, <PERSON>lineResult, Predictor<PERSON><PERSON>ult, TableCellsResult, TableResult
from remarkable.schemas.answer import AnswerData
from remarkable.schemas.esg import ESGCategorySchema

logger = logging.getLogger(__name__)
INVALID_E6_CATRGORY = MatchMulti.compile(r"IIASA", r"MAGICC", r"JRC", r"GECO", operator=any)

E6_CATEGORY = {
    "IPCC": NeglectPattern.compile(
        match=MatchMulti.compile(
            r"\b(?:IPCC|SSP|RCP|Intergovernmental\s*Panel\s*on\s*Climate\s*Change)(\d|\b)",
            operator=any,
        ),
        unmatch=INVALID_E6_CATRGORY,
    ),
    "IEA": NeglectPattern.compile(
        match=MatchMulti.compile(
            r"\b(?:IEA|NZE|International\s*Energy\s*Agency)(\d|\b)",
            operator=any,
        ),
        unmatch=INVALID_E6_CATRGORY,
    ),
    "NGFS": NeglectPattern.compile(
        match=MatchMulti.compile(
            r"\b(?:NGFS|Network\s*for\s*Greening\s*the\s*Financial\s*System)(\d|\b)",
            r"Net Zero 2050 Scenario and Below 2.C Scenario",
            operator=any,
        ),
        unmatch=INVALID_E6_CATRGORY,
    ),
}

E6_EXTRA_CATEGORY = {
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6346#note_705591 下面这个关键词不算
    # "NGFS": PatternCollection(
    #     [
    #         r"\bbelow\s*2°\s*C\s*\(turquoise\s*scenario\)",
    #     ]
    # )
}

P_E5_SUPPLEMENT_RISK = MatchMulti.compile(
    r"\b\w+\sRisks?\b",
    r"\b\w+\sScenario\b",
    r"\brisk type\b",
    r"Relative implication of each climate hazard",
    r"^Scenario$",
    r"^Risks & opportunities",
    operator=any,
)

P_E5_TABLE_TITLE = MatchMulti.compile(
    r"^Summary Table of Residual Climate Risks",
    r"^Key transition risks/opportunitie",
    operator=any,
)

P_E5_ANALYSIS_PARA = re.compile(r"^(\w+\s+)*Scenario(\s*[--─]\s*(\w+\s+)*Pathway)?[：:]")


class PolicyEsgE56(PolicyEsgBaseModel):
    TIMEOUT = get_config("ai.openai.timeout") * 2

    def train(self, dataset, **kwargs):
        pass

    @property
    def prompt(self):
        return self.get_config("prompt")

    def prepare_data_prompt(self, elements):
        embedding_elements = self.predictor.prophet.metadata[self.schema.name]
        if not embedding_elements:
            return [], {}
        crude_elements = []
        for element in elements:
            crude_element = copy(element)
            crude_element["element_sequence"] = 0
            crude_elements.append(crude_element)
        embedding_elements += crude_elements
        embedding_elements = sorted(
            {i.get("index"): i for i in embedding_elements}.values(),
            key=lambda x: x.get("element_index", x.get("index")),
        )
        data_prompt = prepare_data_prompt(embedding_elements, self.pdfinsight)
        embedding_element_map = {f"{i['index']}_{i['element_sequence']}": i for i in data_prompt}
        return data_prompt, embedding_element_map

    def predict_schema_answer(self, elements):
        answers = self.predict_from_llm(elements)
        if answers:
            result = self.create_result(answers, schema=self.schema, value=AnswerValueEnum.COMPLY)
        else:
            result = self.create_result([], schema=self.schema, value=AnswerValueEnum.ND)
        has_cate = self.add_meta(result)
        if not has_cate:
            result.value = AnswerValueEnum.ND
        return [result]

    def predict_from_llm(self, elements):
        answers = []
        data_prompt, embedding_element_map = self.prepare_data_prompt(elements)
        if not data_prompt:
            return []
        messages = [
            {"role": "system", "content": self.prompt},
            {"role": "user", "content": json.dumps(data_prompt, ensure_ascii=False)},
        ]

        response_model: ResponseModels = self.predict_results_by_llm(
            messages, ResponseModels, elements_mapping=embedding_element_map
        )
        if not response_model:
            return []
        logger.info(f"llm_res: {self.schema.name} = {response_model.enum}")
        for item in response_model.items:
            logger.info(f"index={item.index} llm reason={item.reasoning}")
        if not response_model.items:
            return []
        if response_model.enum == "No Disclosure":
            return []
        # 过滤E6重复的段落, 优先element_sequence不为0的. (E5的element_sequence均为0, 此处不影响)
        non_zero_element_sequence_indexes = {
            item.index for item in response_model.items if item.element_sequence and item.element_sequence > 0
        }
        answer_elements = []
        for item in response_model.items:
            if item.index in non_zero_element_sequence_indexes and item.element_sequence == 0:
                logging.info(f"filtered element_sequence zero element index={item.index}")
                continue
            embedding_element = embedding_element_map.get(f"{item.index}_{item.element_sequence}")
            if not embedding_element:
                continue
            _, element = self.pdfinsight.find_element_by_index(item.index)
            if not element:
                continue
            answer_elements.append(element)
            if ElementClassifier.is_table(element):
                if item.element_sequence == 0:
                    answers.append(TableResult(element))
                else:
                    table = parse_table(element, tabletype=TableType.TUPLE, pdfinsight_reader=self.pdfinsight)
                    table_rows_map = dict(enumerate(table.rows, start=1))
                    table_rows_map[0] = list(chain(*table.rows))

                    cells = table_rows_map[item.element_sequence]
                    if not cells:
                        continue
                    # origin_rows = [cell["text"] for cell in element["origin_cells"].values()]
                    # if not all(cell.text in origin_rows for cell in cells):
                    #     continue
                    answers.append(TableCellsResult(element, cells))
            elif ElementClassifier.is_chart(element):
                page_box = PdfinsightSyllabus.elements_outline(
                    [element],
                    pdfinsight=self.pdfinsight,
                    ignore_column_position=True,
                )
                # E6 子项需要文本
                answers.append(
                    OutlineResult(page_box=page_box, element=element, origin_elements=[element], text=item.text)
                )
            else:
                sub_embedding_elements = split_element(element)
                dst_chars = sub_embedding_elements[embedding_element["text"]]["chars"]
                if not dst_chars:
                    continue
                answers.append(CharResult(element, dst_chars))
        if answers and self.schema.name == PolicyEsgRules.E5:
            addition_answers = self.supplement_e5_answer(answers, answer_elements)
            answers += addition_answers
            answers.sort(key=lambda x: x.element["index"])

        return answers

    def supplement_e5_answer(self, answers, answer_elements):
        element_page_map = {i["index"]: i["page"] for i in answer_elements}

        # 扩展页码范围, 每页及其后一页
        extra_pages = sorted({p for page in set(element_page_map.values()) for p in range(page, page + 2)})
        results = []
        for page in extra_pages:
            page_elements = self.pdfinsight.find_elements_by_page(page)
            for element in page_elements:
                _, element = self.pdfinsight.find_element_by_index(element["index"])
                if not element:
                    continue
                if element["index"] in element_page_map:
                    continue
                if ElementClassifier.is_table(element):
                    table = parse_table(element, tabletype=TableType.TUPLE, pdfinsight_reader=self.pdfinsight)
                    match_count = sum(
                        P_E5_SUPPLEMENT_RISK.search(clean_txt(header_text))
                        for header_text in {
                            header.text for header in chain.from_iterable((table.row_header + table.col_header))
                        }
                    )
                    if match_count > 1:
                        page_box = PdfinsightSyllabus.elements_outline(
                            [element],
                            pdfinsight=self.pdfinsight,
                            ignore_column_position=True,
                        )
                        results.append(OutlineResult(page_box=page_box, element=element, origin_elements=[element]))
                        element_page_map[element["index"]] = element["page"]
                        continue

                    if any(P_E5_TABLE_TITLE.search(i) for i in table.possible_titles):
                        page_box = PdfinsightSyllabus.elements_outline(
                            [element],
                            pdfinsight=self.pdfinsight,
                            ignore_column_position=True,
                        )
                        results.append(OutlineResult(page_box=page_box, element=element, origin_elements=[element]))
                        element_page_map[element["index"]] = element["page"]
                        continue

                elif "text" in element and P_E5_ANALYSIS_PARA.search(element["text"] or ""):
                    page_box = PdfinsightSyllabus.elements_outline(
                        [element],
                        pdfinsight=self.pdfinsight,
                        ignore_column_position=True,
                    )
                    results.append(OutlineResult(page_box=page_box, element=element, origin_elements=[element]))
                    element_page_map[element["index"]] = element["page"]

        return results

    def add_meta(self, answer_result: PredictorResult):
        if self.schema.name != PolicyEsgRules.E6:
            return
        predictor = self.predictors.get(PolicyEsgRules.E5)
        e5_result = (predictor.answer_groups.get("") or [None])[0]
        e5_pages = (
            {er.element["page"] for er in e5_result.element_results}
            if e5_result and e5_result.answer_value == AnswerValueEnum.COMPLY.value
            else set()
        )
        min_page, max_page = min(e5_pages, default=-1), max(e5_pages, default=-1)
        categories = {category: ESGCategorySchema(data=[], enum="No", category=category) for category in E6_CATEGORY}
        categories_data = defaultdict(dict)
        for element_result in answer_result.element_results:
            page = element_result.element["page"]
            # E6答案 范围限制在E5前一页至后五页之间  fid: 112069
            if e5_pages and page < min_page - 1 or page > max_page + 5:
                logger.info(f"filter E6 element by page: index={element_result.element['index']} page={page}")
                continue
            answer = element_result.to_answer()
            for category, pattern in E6_CATEGORY.items():
                if pattern.search(element_result.text):
                    categories_data[category][element_result.element["index"]] = AnswerData.model_validate(
                        {"boxes": answer["boxes"], "text": "".join(i["text"] for i in answer["boxes"])}
                    )

        if not categories_data and e5_pages:
            for _element_result in e5_result.element_results:
                for _category, _pattern in E6_EXTRA_CATEGORY.items():  # fid: 111769
                    if _match := _pattern.nexts(_element_result.text):
                        _answer = CharResult(
                            _element_result.element, _element_result.chars[slice(*_match.span())]
                        ).to_answer()
                        categories_data[_category][_element_result.element["index"]] = AnswerData.model_validate(
                            {"boxes": _answer["boxes"], "text": "".join(i["text"] for i in _answer["boxes"])}
                        )

        for category, data in categories_data.items():
            categories[category].data = [data[k] for k in sorted(data)]
            categories[category].enum = "Yes"
            categories[category].category = category

        answer_result.meta["categories"] = [i.model_dump() for i in categories.values()]
        return any(categories[i].enum == "Yes" for i in categories)
