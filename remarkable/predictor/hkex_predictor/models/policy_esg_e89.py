import json
import logging
from collections import defaultdict
from copy import copy
from itertools import chain
from typing import List

from pydantic import BaseModel as PydanticBaseModel
from pydantic import Field

from remarkable.common.common import get_first_value, is_paragraph_elt
from remarkable.common.constants import AnswerValueEnum, CategoryTypeEnum, PolicyEsgRules, Scope3Categories, TableType
from remarkable.common.pattern import MatchMulti, PositionPattern
from remarkable.common.util import clean_txt, split_element
from remarkable.config import get_config
from remarkable.pdfinsight.parser import ParsedTable, ParsedTableCell, parse_footnotes_again, parse_table
from remarkable.pdfinsight.reader import PdfinsightSyllabus
from remarkable.pdfinsight.reader_table import PdfinsightTable
from remarkable.predictor.common_pattern import R_PERCENT
from remarkable.predictor.eltype import ElementClassifier
from remarkable.predictor.hkex_predictor.models.policy_esg_base_model import (
    BaseResponseModel,
    PolicyEsgBaseModel,
    ResponseModel,
    ResponseModels,
)
from remarkable.predictor.hkex_predictor.models.policy_esg_e1 import gen_helper_prompt
from remarkable.predictor.hkex_predictor.models.policy_esg_e2 import classify_esg_results
from remarkable.predictor.hkex_predictor.models.policy_esg_e7 import prepare_scope3_data_prompt
from remarkable.predictor.schema_answer import CharResult, OutlineResult, TableCellsResult, TableResult
from remarkable.schemas.answer import AnswerData
from remarkable.schemas.esg import E8_CATEGORIES, E9_CATEGORIES, ESGCategorySchema

logger = logging.getLogger(__name__)


SUMMARY_PROMPT = """你的任务是从给定文本中提取和总结范围3（Scope 3）。

你的目标是识别文本中所有出现的范围3类别，包括那些标记为不适用或没有排放数据的类别。

## 指导说明：

1. **识别数据块**：首先识别文本中专门描述排放量的数据块（如表格、图表、数据段落等）

2. **提取所有类别**：从这些数据块中寻找以下15个范围3排放类别，提取所有出现的类别：
   - Purchased goods and services|Purchased goods and services-paper
   - Capital goods
   - Fuel- and energy-related activities
   - Upstream transportation and distribution
   - Waste generated in operations
   - Business travel|business air travel by staff|business air travel by employee|air travel
   - Employee commuting
   - Upstream leased assets
   - Downstream transportation and distribution
   - Processing of sold products
   - Use of sold products
   - End-of-life treatment of sold products
   - Downstream leased assets
   - Franchises
   - Investments

3. **提取规则**：
   - 只有当文本中使用的术语与上述列表中的类别名称完全匹配或高度相似时，才提取该类别
   - 不要基于相似概念或间接描述推断类别
   - **提取所有在排放数据块中出现的类别**，无论其适用性状态如何
   - 如果类别标记为"No"或"不适用"，仍然要提取
   - 如果提供了具体的排放数值，请提取该数值（包括单位）
   - 如果没有提供数值、标记为"N/A"、或不适用，emission字段可以设为：
     - "N/A"（当明确标记为N/A时）
     - "-"（当明确列出但没有数值时）
     - ""（空字符串，当完全没有数据时）
   - 将类别名称标准化为与上面列表中的格式匹配

4. **完全忽略以下内容**：
   - 描述将来计划、战略或路线图的段落
   - 讨论方法论但不提供具体排放数据的段落
   - 仅提及类别名称但不在排放数据上下文中的段落
   - 使用模糊或间接描述而非标准类别名称的段落

5. **匹配规则**：
   - "Business travel"分类可能包含:
        - "Business travel"
        - "business air travel by staff"
        - "business air travel by employee"
        - "air travel of our Publishing Group executives"
   - "Waste generated in operations"分类可能包含以下几种描述
        - "Waste from operations"
        - "Waste generated in operations"
        - "wastewater discharge"
        - "sewage treatment"
        - "Sewage processing"
        - "paper waste"
        - "disposed waste paper from landfill"
        - "waste disposal at landfills"
        - "wastepaper disposal"
   - "Upstream leased assets"分类可能包含:
        - "Upstream leased assets"
        - "Upstream emissions"
   - "Downstream leased assets"分类可能包含:
        - "Downstream leased assets"
        - "Downstream emissions"
   - "Upstream transportation and distribution"分类可能包含:
        - "Upstream transportation and distribution"
        - "Upstream transportation & distribution"
   - "Downstream transportation and distribution"分类可能包含:
        - "Downstream transportation and distribution"
        - "Downstream transportation & distribution"
   - 对于其他9个类别，须使用与列表中几乎相同的术语
   - "transportation and distribution"的Upstream和Downstream不明确时, 重复匹配两个类别, 两个类别emission同时置为0
   - 如果描述不够明确或与标准类别有差异，则不要包含该类别

6. **格式要求**：
   - 返回CategoryResult对象的列表
   - 每个CategoryResult应包含：
     * category: 标准化的类别名称
     * emission: 排放数值字符串、"N/A"、"-"或空字符串

7. **以下内容与上述的分类相似，不能提取**
   - traveling by transportation and hotel stays与Business travel类似，不能提取
   - Business trip与Business travel类似，不能提取
   - GHG emissions include emissions from electricity, natural gas and other consumption by tenants与Fuel- and energy-related activities无关，不能提取
   - scope 3 emissions includes fuel consumption for the transportation of sludge by third parties, indirect emissions from air travel by employees and nitrous oxide released from effluen，不应归属于任意一个分类，不能提取

## 期望输出示例：
```json
{
  "items": [
    {
      "text": "Purchased goods and services",
      "category": "Purchased goods and services",
      "emission": "69,605"
    },
    {
      "text": "Processing of sold products",
      "category": "Processing of sold products",
      "emission": "N/A"
    },
    {
      "text": "Upstream leased assets",
      "category": "Upstream leased assets",
      "emission": ""
    }
  ]
}

重要说明：
- 只处理明确报告排放数据的内容块
- 完全忽略叙述性、解释性或战略性文本中提到的类别
- 如果一个类别在排放数据块中出现但没有数值，使用"-"
- 对排放数据中未提及的类别，不要包含在结果中
- 如果没有类别具有特定的数值值，请返回一个空项目列表：{"items": []}
- 如无排放数据也可以，同一个类别，优先采纳有排放数据的内容，如都无排放数据，优先采纳index与其余有排放数据的答案相近的内容
- 如有明确的类别，无排放数值，emission置为-
- 如果类别在排放数据表中出现但标记为"No"或"不适用"，仍然要包含在结果中
- emission字段可以是具体数值、"N/A"、"-"或空字符串
- 严格匹配类别名称，不要基于相似概念进行推断
- 如果排放类别和排放数值在同一表格的不同行，则text的内容为相关的表格行内容
- 对与最终的结果需要严格按照第7项的规则进行检查
"""

P_E7_NEG_SYLLABUS = MatchMulti.compile(
    r"On-balance\s*sheet\s*financed\s*emissions",
    operator=any,
)
P_NEGLECT_CATEGORY_MAP = {
    "Purchased goods and services": MatchMulti.compile(
        r"^(•\s)?(water|paper) consumption$",
        r"^Other indirect GHG emissions",
        r"^Scope 3$",
        operator=any,
    ),
    "Capital goods": MatchMulti.compile(
        r"^Scope 3$",
        operator=any,
    ),
    "Waste generated in operations": MatchMulti.compile(
        r"^(•\s)?(water|paper) consumption$",
        "^Contractual car fuel usage$",
        "^tenants’ energy consumption$",
        "^fresh water processing$",
        "^emission from water usage and processing$",
        "^paper and water used in office and warehouses$",
        r"^Hazardous waste$",
        r"^waste$",
        operator=any,
    ),
    "End-of-life treatment of sold products": MatchMulti.compile(r"Freshwater and sewage processing", operator=any),
    "Use of sold products": MatchMulti.compile(
        r"Use of products$",
        r"Products used$",
        operator=any,
    ),
}

P_SCOPE3_EMISSIONS = PositionPattern.compile(
    r"emissions",
    r"[\d+,\.]+ tonnes of CO2e",
)

R_CAT_PREFIX = r"(?:cat(?:egory)?\s+\d+\s*[:：]?\s*)?"

P_CATEGORY_MAPPING = {
    category.value: MatchMulti.compile(rf"^{R_CAT_PREFIX}{category.value}$", operator=any)
    for category in CategoryTypeEnum
}
P_CATEGORY_MAPPING.update(
    {
        CategoryTypeEnum.category1.value: MatchMulti.compile(
            rf"^{R_CAT_PREFIX}{CategoryTypeEnum.category1.value}$",
            rf"^{R_CAT_PREFIX}Purchased goods and services-paper$",
            operator=any,
        ),
        CategoryTypeEnum.category5.value: MatchMulti.compile(
            rf"^{R_CAT_PREFIX}{CategoryTypeEnum.category5.value}$",
            rf"^{R_CAT_PREFIX}Waste from operation$",
            rf"^{R_CAT_PREFIX}Waste generated in operations$",
            rf"^{R_CAT_PREFIX}wastewater discharge$",
            rf"^{R_CAT_PREFIX}sewage treatment$",
            rf"^{R_CAT_PREFIX}Sewage processing$",
            rf"^{R_CAT_PREFIX}paper waste$",
            rf"^{R_CAT_PREFIX}disposed waste paper from landfill$",
            rf"^{R_CAT_PREFIX}waste disposal at landfills$",
            rf"^{R_CAT_PREFIX}wastepaper disposal$",
            operator=any,
        ),
        CategoryTypeEnum.category3.value: MatchMulti.compile(
            rf"^{R_CAT_PREFIX}{CategoryTypeEnum.category3.value}$",
            r"Fuel- and energy[—–-]?\s?related activities.*\bnot included in scope 1 or scope 2",
            rf"^{R_CAT_PREFIX}Fuel[—–\-\s]+and Energy[—–\-\s]Related Activities$",
            operator=any,
        ),
        CategoryTypeEnum.category11.value: MatchMulti.compile(
            rf"^{R_CAT_PREFIX}{CategoryTypeEnum.category11.value}$",
            rf"^{R_CAT_PREFIX}Use of Sold Product$",
            operator=any,
        ),
    }
)

P_E8_E9_TABLE_TITLE = MatchMulti.compile("Scope 3 (carbon )?Emissions?.*(Methodology|Mapping)", operator=any)
P_E8_COL_HEADER = MatchMulti.compile("Scope 3 category", operator=any)
P_E9_COL_HEADER = MatchMulti.compile("Tonnes? of CO2", "Scope 3 Carbon Emissions", operator=any)
P_E9_VAL = MatchMulti.compile(r"([1-9][0-9]{0,2}(?:,[0-9]{3}){0,6})", operator=any)
P_E9_TOTAL = MatchMulti.compile(r"^Total Scope 3.*emission", operator=any)
P_E9_PERCENT = MatchMulti.compile(rf"^[<>]?\s*\d+(\.\d+)?\s*{R_PERCENT}$", operator=any)

CATEGORY_KEY = "1"


class CategoryResult(BaseResponseModel):
    category: CategoryTypeEnum
    emission: str | None = Field(description="emission for every category")

    @property
    def key(self):
        return CATEGORY_KEY


class CategoryResults(PydanticBaseModel):
    items: List[CategoryResult]
    reasoning: str | None = None


E89_HELPER_MATERIALS = [
    {
        "question": [
            {
                "index": 161,
                "element_sequence": 1,
                "text": "Scope 3 includes indirect emissions, such as indirect GHG emissions from the Company’s upstream/downstream product transportation, employee travel and employee commuting, indirect GHG emissions from the use of the Company’s products and other indirect GHG emissions, and indirect GHG emissions from the use of products/services used in the production/management of plantation plants.",
            }
        ],
        "answer": [
            {
                "items": [
                    {
                        "category": "Upstream transportation and distribution",
                        "emission": "-",
                    },
                    {"category": "Business travel", "emission": "-"},
                    {"category": "Employee commuting", "emission": "-"},
                    {
                        "category": "Downstream transportation and distribution",
                        "emission": "-",
                    },
                ]
            }
        ],
    },
    {
        "question": [
            {
                "index": 161,
                "element_sequence": 1,
                "text": "Scope 3 emissions are the indirect emissions that arise from value chain activities, including the procurement of goods and services, business travel and tenants’ energy consumption.",
            }
        ],
        "answer": [
            {
                "items": [
                    {
                        "category": "Purchased goods and services",
                        "emission": "-",
                    },
                    {
                        "category": "Business travel",
                        "emission": "-",
                    },
                ]
            }
        ],
    },
]


def filter_category(categories):
    results = []
    for category in categories:
        logger.debug(f"{category.category=}, {category.text=}")
        if p_pattern := P_NEGLECT_CATEGORY_MAP.get(category.category):
            if p_pattern.search(category.text):
                continue
            else:
                results.append(category)
        else:
            results.append(category)

    return results


class PolicyEsgE89(PolicyEsgBaseModel):
    TIMEOUT = get_config("ai.openai.timeout") * 2

    def train(self, dataset, **kwargs):
        pass

    @property
    def prompt(self):
        return self.get_config("prompt")

    @property
    def summary_prompt(self):
        return self.get_config("summary_prompt")

    @property
    def e9_schema(self):
        return self.schema.parent.children_schema(PolicyEsgRules.E9.value)

    def answer_schema(self, rule):
        if rule == PolicyEsgRules.E8.value:
            return self.schema
        return self.e9_schema

    @staticmethod
    def valid_e9_category_data(category):
        if not (category.emission and category.emission != "-"):
            return False
        if Scope3Categories.phrase_to_enum(category.category) in (
            Scope3Categories.category4,
            Scope3Categories.category9,
        ):
            text = category.text.lower()
            _up = "up" in text
            _down = "down" in text
            if _up is _down:
                logger.info(f"filtered E9 category {text=}")
                return False
        return True

    @staticmethod
    def gen_default_category_schemas():
        e8_category_schemas = {
            category: ESGCategorySchema(data=[], enum="No", category=category) for category in E8_CATEGORIES
        }
        e9_category_schemas = {
            category: ESGCategorySchema(data=[], enum="No", category=category) for category in E9_CATEGORIES
        }
        return {PolicyEsgRules.E8.value: e8_category_schemas, PolicyEsgRules.E9.value: e9_category_schemas}

    def gen_default_answer(self, default_category_schemas):
        e8_nd_result = self.create_result(
            [],
            schema=self.schema,
            value=AnswerValueEnum.ND,
            meta={"categories": [i.model_dump() for i in default_category_schemas[PolicyEsgRules.E8.value].values()]},
        )
        e9_nd_result = self.create_result(
            [],
            schema=self.e9_schema,
            value=AnswerValueEnum.ND,
            meta={"categories": [i.model_dump() for i in default_category_schemas[PolicyEsgRules.E9.value].values()]},
        )
        return [e8_nd_result, e9_nd_result]

    @staticmethod
    def is_scope3_emissions_table(table: ParsedTable) -> bool:
        """
        第一列为分类且最后一行为总排放量，后面某列为排放量，则认为是 Scope3 排放量表格
        """
        if len(table.cols) < 1 and len(table.cols[0]) < 1:
            return False
        if not P_E8_COL_HEADER.search(table.col_header_texts[table.cols[0][0].colidx]):
            return False
        if not P_E9_TOTAL.search(table.cols[0][-1].clean_text):
            return False
        for cells in table.cols[1:]:
            if not P_E9_COL_HEADER.search(cells[0].clean_text):
                continue
            for cell in cells[1:]:
                if P_E9_VAL.search(cell.text):
                    return True
        return False

    def create_table_result(self, elements, default_category_schemas):
        """
        处理 e8 e9 披露在表格中的情况
        """
        ret = []
        e8_categories = defaultdict(list)
        e9_categories = defaultdict(list)
        table_indexes_set = {element["index"] for element in elements if element["class"] == "TABLE"}
        for idx in table_indexes_set:
            _, element = self.pdfinsight.find_element_by_index(idx)
            table = parse_table(element, tabletype=TableType.TUPLE, pdfinsight_reader=self.pdfinsight)
            if not P_E8_E9_TABLE_TITLE.search(table.title.title_text) and not self.is_scope3_emissions_table(table):
                continue
            total_emission_cells = []
            cat_cell_map = {}
            for col in table.cols:
                if not P_E8_COL_HEADER.search(table.col_header_texts[col[0].colidx]):
                    continue
                for cat_cell in col:
                    if P_E9_TOTAL.search(cat_cell.clean_text):
                        if total_emission := self.find_emission_cell(table, cat_cell):
                            total_emission_cells.extend([cat_cell, total_emission])
                        continue
                    if cat_cell.dummy:
                        continue
                    for cat, cat_pattern in P_CATEGORY_MAPPING.items():
                        if cat_pattern.search(cat_cell.clean_text):
                            cat_cell_map[cat] = cat_cell
            for cat, cat_cell in cat_cell_map.items():
                category_answer = TableCellsResult(element, [cat_cell], display_text=cat_cell.clean_text)
                e8_categories[Scope3Categories.display_text(cat)].append(
                    (element["index"], cat_cell.rowidx, "-", category_answer)
                )
                if emissions_cell := self.find_emission_cell(table, cat_cell):
                    e9_cells = [cat_cell, emissions_cell]
                    if P_E9_PERCENT.search(emissions_cell.clean_text) and total_emission_cells:
                        e9_cells.extend(total_emission_cells)
                    e9_answer = TableCellsResult(element, e9_cells, display_text=emissions_cell.text)
                    # 如果数量披露的是百分数且有合计行数据，把合计内容添加到答案中
                    e9_categories[Scope3Categories.display_emission(cat, "")].append(
                        (element["index"], cat_cell.rowidx, emissions_cell.text, e9_answer)
                    )

        if e8_categories and e9_categories:
            ret.append(self.build_esg_result(PolicyEsgRules.E8.value, e8_categories, default_category_schemas))
            ret.append(self.build_esg_result(PolicyEsgRules.E9.value, e9_categories, default_category_schemas))
        return ret

    def find_emission_cell(self, table: ParsedTable, cat_cell: ParsedTableCell):
        for cell in table.rows[cat_cell.rowidx]:
            header_text = "".join(hearder_cell.clean_text for hearder_cell in cell.col_header_cells)
            if (
                P_E9_COL_HEADER.search(header_text)
                and self.check_content_by_report_year(header_text, self.report_year)
                and P_E9_VAL.search(cell.text)
            ):
                return cell
        return None

    def predict_schema_answer(self, elements):
        default_category_schemas = self.gen_default_category_schemas()
        default_answers = self.gen_default_answer(default_category_schemas)
        if not self.predictor.prophet.metadata.get("e7_normal_data"):
            logger.info("no e7_normal_data, set E8 E9 to ND")
            return default_answers
        results = []
        embedding_elements = self.predictor.prophet.metadata[self.schema.name]
        additional_elements = self.get_e7_elements()
        embedding_elements += additional_elements
        embedding_elements = self.filter_elements_by_current_report_year(embedding_elements)
        if table_res := self.create_table_result(embedding_elements, default_category_schemas):
            return table_res

        data_prompt, embedding_element_map = prepare_scope3_data_prompt(embedding_elements, self.pdfinsight)
        messages = [
            {"role": "system", "content": self.prompt},
            {"role": "user", "content": json.dumps(data_prompt, ensure_ascii=False)},
        ]
        response_model: ResponseModels = self.predict_results_by_llm(
            messages, ResponseModels, elements_mapping=embedding_element_map
        )
        if not response_model or not response_model.items or response_model.enum == "No Disclosure":
            return default_answers
        process_ids = set()
        # fid: 112782 先从E7答案表格中提取下
        additional_items = []
        if e := additional_elements and additional_elements[0]:
            rsp = ResponseModel(
                index=e["index"],
                element_sequence=e["element_sequence"],
                reasoning="E7 answer",
                enum=AnswerValueEnum.COMPLY.value,
            )
            additional_items.append(rsp)
        e8_categories = defaultdict(list)
        e9_categories = defaultdict(list)
        items = classify_esg_results(response_model.items)
        for item in chain(additional_items, items):
            if hasattr(item, "enum") and item.enum in ("No Disclosure", None):
                continue
            process_id = f"{item.index}_{item.element_sequence}"
            if process_id in process_ids:
                continue
            process_ids.add(process_id)
            embedding_element = embedding_element_map.get(process_id)
            if not embedding_element:
                continue
            logger.info(f"get_categories_by_llm for {item.index=}, {item.element_sequence=}")
            categories = self.get_categories_by_llm(embedding_element["text"])
            categories = filter_category(categories)
            logger.info(f"llm_res: {categories=}")
            if not categories:
                continue
            _, element = self.pdfinsight.find_element_by_index(item.index)
            if not element:
                continue

            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7378#note_739361
            # 子句或表格的某行内容，同时披露了多个分类，只有一个排放量，e9不统计
            invalid_e9_category = (
                item.element_sequence != 0
                and len(categories) > 1
                and len({category.emission for category in categories}) == 1
            )

            for category_result in categories:
                category_answer = self.gen_category_result(item, element, category_result, embedding_element)
                if not category_answer:
                    continue
                category_answer = category_answer[0]
                e8_categories[Scope3Categories.display_text(category_result.category)].append(
                    (item.index, item.element_sequence, category_result.emission, category_answer)
                )
                if invalid_e9_category:
                    continue
                if not self.valid_e9_category_data(category_result):
                    below_elements = self.pdfinsight.find_elements_near_by(item.index, amount=2)
                    new_elements = [element]
                    for idx, ele in enumerate(below_elements):
                        if is_paragraph_elt(ele) and P_SCOPE3_EMISSIONS.search(clean_txt(ele["text"])):
                            new_elements.extend(below_elements[: idx + 1])
                            break
                    else:
                        continue
                    new_categories = self.get_categories_by_llm(" ".join(ele["text"] for ele in new_elements))
                    if not (new_categories := filter_category(new_categories)):
                        continue
                    for _category_result in [c for c in new_categories if self.valid_e9_category_data(c)]:
                        page_box = PdfinsightSyllabus.elements_outline(new_elements)
                        category_answer = OutlineResult(
                            page_box=page_box, element=element, origin_elements=new_elements
                        )
                        break

                # 同一表格内有多个分类，对于e9需要拆分不同分类的区域
                if (
                    (p_category := P_CATEGORY_MAPPING.get(category_result.category))
                    and len(categories) > 1
                    and category_answer.element["class"] == "TABLE"
                    and item.element_sequence == 0
                ):
                    p_invalid_category = MatchMulti.compile(
                        *[p_value for name, p_value in P_CATEGORY_MAPPING.items() if name != category_result.category],
                        operator=any,
                    )
                    table = parse_table(element, tabletype=TableType.TUPLE, pdfinsight_reader=self.pdfinsight)
                    cells = []
                    for row_cells in table.rows:
                        text = clean_txt(" ".join(cell.text for cell in row_cells))
                        if p_category.search(text):
                            cells.extend(row_cells)
                            continue
                        if not cells:
                            continue
                        if p_invalid_category.search(text):
                            break
                        cells.extend(row_cells)
                    if cells:
                        category_answer = TableCellsResult(element, cells, display_text=category_answer.display_text)

                e9_categories[
                    Scope3Categories.display_emission(category_result.category, category_result.emission)
                ].append((item.index, item.element_sequence, category_result.emission, category_answer))

        results.append(self.build_esg_result(PolicyEsgRules.E8.value, e8_categories, default_category_schemas))
        results.append(self.build_esg_result(PolicyEsgRules.E9.value, e9_categories, default_category_schemas))
        return results

    def build_esg_result(self, esg_rule, categories, default_category_schemas):
        answer_results = []
        for cat, answers in categories.items():
            _result = default_category_schemas[esg_rule][cat]
            if not answers:
                continue
            _result.enum = "Yes"
            non_zero_element_sequence_index = {
                index for index, element_sequence, emission, answer in answers if element_sequence != 0
            }
            added = set()
            for index, element_sequence, _, answer in answers:
                if element_sequence == 0 and index in non_zero_element_sequence_index:  # fid=112776
                    continue
                if (index, element_sequence) in added:
                    continue
                added.add((index, element_sequence))
                answer_results.append(answer)
                _answer = answer.to_answer()
                data = AnswerData(
                    **{
                        "boxes": _answer["boxes"],
                        "text": "".join(i["text"] for i in _answer["boxes"]) or answer.text,
                        # "emission": emission,
                    }
                )
                _result.data.append(data)

        value = AnswerValueEnum.COMPLY.value if categories else AnswerValueEnum.ND.value
        return self.create_result(
            answer_results,
            schema=self.answer_schema(esg_rule),
            value=value,
            meta={"categories": [i.model_dump() for i in default_category_schemas[esg_rule].values()]},
        )

    def gen_category_result(self, item, element, category, embedding_element):
        display_text = f"{Scope3Categories.display_text(category.category)}_{category.emission}"

        if ElementClassifier.is_table(element):
            table = parse_table(element, tabletype=TableType.TUPLE, pdfinsight_reader=self.pdfinsight)
            table_rows_map = dict(enumerate(table.rows, start=1))
            element_sequence = item.element_sequence
            if element_sequence == 0 and (text := clean_txt(category.text)):
                for col in table.cols:
                    for r_no, cell in enumerate(col, start=1):
                        if text in cell.clean_text:
                            element_sequence = r_no
                            break
                    if element_sequence != 0:
                        break
            if element_sequence == 0:
                return [TableResult(element, parsed_table=table, display_text=display_text)]

            cells = table_rows_map[element_sequence]
            if not cells:
                return []
            # origin_rows = [cell["text"] for cell in element["origin_cells"].values()]
            # if not all(cell.text in origin_rows for cell in cells):  # fid=103410 Cat15
            #     return []
            return [TableCellsResult(element, cells, display_text=display_text)]
        elif ElementClassifier.is_chart(element):
            page_boxes = PdfinsightSyllabus.elements_outline(
                [element],
                pdfinsight=self.pdfinsight,
                ignore_column_position=True,
            )
            page_box = page_boxes[0]
            page_box["text"] = item.text
            return [OutlineResult(page_box=page_boxes, element=element, origin_elements=[element], text=item.text)]

        else:
            sub_embedding_elements = split_element(element)
            dst_chars = sub_embedding_elements[embedding_element["text"]]["chars"]
            if not dst_chars:
                return []
            return [CharResult(element, dst_chars, display_text=display_text)]

    def get_e7_answer_elements(self):
        # http://100.64.0.105:55647/#/hkex/esg-report-checking/report-review/306791?fileId=71525&schemaId=2&rule=E7-Scope%203%20emissions&delist=0
        # http://100.64.0.105:55647/#/hkex/esg-report-checking/report-review/339134?fileId=103421&schemaId=2&rule=E9-Scope%203%20emissions%20data%20by%20categories&delist=0
        # 从E7 的答案获取cat
        fake_element = {}
        predictor = self.predictors.get(PolicyEsgRules.E7.value)
        if len(predictor.answer_groups) == 1:
            predictor_result = self.get_common_predictor_results(get_first_value(predictor.answer_groups))
            print(predictor_result)
        return fake_element

    def get_e7_elements(self):
        results = []
        processed_idx = set()
        predictor = self.predictors.get(PolicyEsgRules.E7.value)
        if len(predictor.answer_groups) == 1:
            predictor_result = self.get_common_predictor_results(get_first_value(predictor.answer_groups))
            elements = self.get_elements_from_answer_result(predictor_result)
            try:
                parseed_cells_mapping = defaultdict(list)
                for cell in predictor_result[0].element_results[0].parsed_cells:
                    parseed_cells_mapping[cell.table.index].append(cell)
            except Exception:  # todo 待调整 处理全部表格和E7答案的关系
                logger.warning("E7-Scope 3 emissions parsed table cells error")
                parseed_cells_mapping = {}
            for element in elements:
                if element["index"] in processed_idx:
                    continue
                if element["class"] == "TABLE":
                    syllabuses = self.pdfinsight.syllabus_reader.find_by_elt_index(element["index"])
                    if any(P_E7_NEG_SYLLABUS.search(syll.get("title") or "") for syll in syllabuses[::-1]):
                        continue

                    cells = parseed_cells_mapping.get(element["index"]) or []
                    if cells:
                        fake_element = copy(element)
                        fake_element["cells"] = {cell.indexstr: cell.raw_cell for cell in cells}
                        fake_element["origin_cells"] = {cell.indexstr: cell.raw_cell for cell in cells}
                        fake_element["text"] = PdfinsightTable(fake_element).markdown
                        fake_element["keep_text"] = True
                        results.append(fake_element)
                    table = parse_table(element, tabletype=TableType.TUPLE, pdfinsight_reader=self.pdfinsight)
                    results.extend(table.footnotes)
                    processed_idx.add(element["index"])
                    for note in parse_footnotes_again(self.pdfinsight, element):
                        if element["index"] in processed_idx:
                            continue
                        results.append(note)
                        processed_idx.add(note["index"])

                    for item in table.elements_above[:1]:
                        results.append(item)
                        processed_idx.add(item["index"])

        for note in results:
            note["element_sequence"] = 0
        return results

    def get_categories_by_llm(self, text):
        helper_prompt = gen_helper_prompt(E89_HELPER_MATERIALS)
        messages = [{"role": "system", "content": SUMMARY_PROMPT}]
        if helper_prompt:
            messages.extend(helper_prompt)
        messages.append({"role": "user", "content": text})
        response_model: CategoryResults = self.predict_results_by_llm(
            messages, CategoryResults, elements_mapping={CATEGORY_KEY: {"text": text}}
        )
        if not response_model:
            return []
        return response_model.items
