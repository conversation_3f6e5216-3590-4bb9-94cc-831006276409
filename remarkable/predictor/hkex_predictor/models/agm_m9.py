import re

from remarkable.common.common import get_date_by_offset_months
from remarkable.common.constants import PDFInsightClass<PERSON>num, TableType
from remarkable.common.pattern import <PERSON><PERSON><PERSON><PERSON>, PatternCollection
from remarkable.converter.utils import date_from_text
from remarkable.pdfinsight.parser import parse_table
from remarkable.predictor.common_pattern import DATE_EN_PATTERN
from remarkable.predictor.models.base_model import BaseModel
from remarkable.predictor.models.para_match import ParaMatch
from remarkable.predictor.models.table_kv import KeyValueTable

R_DEFINITIONS = MatchMulti.compile("^DEFINITIONS$", operator=any)
SINCE_DATE = re.compile(
    # rf"(since|during)(?P<start>.*?)\bto.*Latest\s*Practicable\s*Date",
    rf"(since|during).*?(?P<start>{DATE_EN_PATTERN}).*?\bto.*Latest\s*Practicable\s*Date",
    re.I | re.X,
)


class AGMM9(ParaMatch):
    """
    文档中没有明确提到six months，有 since 某日到 Latest Practicable Date的描述
    """

    def predict_schema_answer(self, elements):
        answer = super().predict_schema_answer(elements)
        common_answers = BaseModel.get_common_predictor_results(answer)
        if not common_answers:
            return []
        # 提取 Latest Practicable Date
        practicable_date_val = self.find_definitions_value(r"Latest\s*Practicable Date")
        if not practicable_date_val:
            return []
        practicable_date = date_from_text(practicable_date_val)
        if not practicable_date:
            return []
        cutoff_date = get_date_by_offset_months(practicable_date, -6, fmt="%d %b %Y")
        for common_answer in common_answers:
            matched_text = SINCE_DATE.search(common_answer.text)
            if not matched_text:
                continue
            start_date = date_from_text(matched_text.group("start"))
            if start_date and start_date <= cutoff_date:
                return answer
        return []

    def find_definitions_value(self, key):
        for syllabus in self.pdfinsight.syllabus_reader.syllabuses:
            if not R_DEFINITIONS.search(syllabus["title"]):
                continue
            elements = self.get_candidate_elements_by_range(
                syllabus["range"], aim_types=PDFInsightClassEnum.TABLE.value
            )
            for element in elements:
                table = parse_table(element, tabletype=TableType.KV.value, pdfinsight_reader=self.pdfinsight)
                kv_pairs = KeyValueTable.parse_kv_pairs(table)
                for pair in kv_pairs:
                    key_cell = pair[0]
                    if PatternCollection(key, flags=re.I).nexts(key_cell.clean_text):
                        return pair[1].clean_text
                if element["page"] > 10:
                    return None
        return None
