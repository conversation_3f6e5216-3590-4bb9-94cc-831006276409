import json
import logging
from collections import defaultdict
from itertools import chain
from operator import itemgetter
from typing import List

from pydantic import BaseModel as PydanticBaseModel

from remarkable.common.constants import AnswerValueEnum, TableType
from remarkable.common.util import split_element
from remarkable.pdfinsight.parser import parse_table
from remarkable.predictor.eltype import ElementClassifier
from remarkable.predictor.hkex_predictor.models.policy_esg_base_model import PolicyEsgBaseModel, ResponseModel
from remarkable.predictor.hkex_predictor.models.policy_esg_e1 import gen_helper_prompt
from remarkable.predictor.schema_answer import CharResult, OutlineResult, TableCellsResult, TableResult

logger = logging.getLogger(__name__)
PROMPT = """你是一个专业的ESG报告鉴证评估专家。你的任务是判断一组JSON格式的文本片段是否证明了ESG报告获得了独立第三方验证。

输入数据格式说明：
[
    {
        "index": 整数唯一标识,
        "element_sequence": "文本在段落中的位置",
        "text": "文本内容字符串"
        "enum": "Comply/ND",
    },
    ...
]

关键验证标准：
1. 文本中必须明确提及独立验证或第三方验证
2. 验证必须针对ESG报告全部、部分内容或ESG data
3. 需要指出验证机构或验证标准
4. 未明确验证机构或验证结论，但满足以下任一条件也可判定为Comply
 - 指向独立鉴证报告（如"详见独立鉴证报告"）
 - 说明验证范围（如"关键ESG数据已通过第三方验证"）
 - 提及未来验证计划（如"下一年度将扩大第三方验证范围"）
5. 如果只明确披露针对部分数据或关键数据进行担保，则不需要提取

以下内容跟第三方验证内容很像，但是不是第三方独立验证的内容，不能提取
- third party consultant。 原因是独立第三方顾问，没有第三方独立验证esg报告
- third-party professional to assist with conducting。 原因：只有第三方人员协助，没有验证
- third-party professionals to regularly monitor our environmental performance。 原因：第三方是为了监控环境数据，而不是为了验证esg报告
- third-party independent auditors to verify the Group’s accounts。 原因：第三方是为了审核集团账目，而不是为了验证esg报告
- ensure comprehensive accounting and review of product carbon footprints, which have been verified through third party assurance 原因：第三方是为了审核碳足迹，而不是为了验证esg报告
- whether the target and the methodology for setting the target has been validated by a third party. 原因：只是说明了是否经第三方验证，而不是第三方验证了esg报告
- we hired a third-party professional organization to conduct an independent verification of our LME Red Flag Assessment Report. 原因：只是说明了对LME进行了第三方验证，而不是第三方验证了esg报告
- engaged independent third-party institutions to provide consultation services. 原因：聘请第三方提供咨询服务，而不是为了验证esg报告
- employ third party to audit the report in due time. 原因：聘请第三方进行审核服务，而不是已经对报告进行了验证

评估步骤：
1. 检查每个JSON对象的文本片段
2. 分析是否满足独立验证的关键要求
3. 判断枚举值：
   - Comply：明确获得独立验证或第三方验证
   - ND：未明确说明验证情况

严格要求：
- 仅当证据极其明确时才给出结论
- 如无法确定，返回空结果
- 不得主观臆断或编造内容

输出格式：
```json
{
    "index": 保持与输入的数据一致,
    "element_sequence": "保持与输入的数据一致",
    "reasoning": "详细推理过程(使用简体中文)"
}

"""


E2_ASSURANCE_HELPER_MATERIALS = [
    {
        "question": [
            {
                "index": 161,
                "element_sequence": 0,
                "text": "We seek third party assurance on key sustainability performance data points. We are also working with a third party to validate the robustness of additional data with a view to expanding the scope of our assured data in future reporting cycles.",
            }
        ],
        "answer": [
            {
                "items": [
                    {
                        "index": 161,
                        "element_sequence": 0,
                        "enum": "Comply",
                        "reasoning": "The text explicitly states that the company seeks third-party assurance on key sustainability data points and is working to expand the scope of assured data. This indicates that at least some of the ESG data has been independently verified, and there are plans to increase the verified data in the future.",
                    }
                ],
            }
        ],
    },
]


class ResponseModels(PydanticBaseModel):
    items: List[ResponseModel] = None


def classify_esg_results(resp_items: list[ResponseModel]):
    """
    同一元素块同时存在postion为0和非0的答案都为Comply时，保留非0，
    """
    group_items = defaultdict(list)
    for item in resp_items:
        if item.enum == AnswerValueEnum.COMPLY.value:
            group_items[item.index].append(item)
    for items in group_items.values():
        if len(items) == 1:
            continue
        for item in items:
            if item.element_sequence == 0:
                resp_items.remove(item)
                break

    return resp_items


class PolicyEsgE2(PolicyEsgBaseModel):
    def train(self, dataset, **kwargs):
        pass

    def predict_schema_answer(self, elements):
        results = []
        embedding_elements = self.predictor.prophet.metadata[self.schema.name]
        embedding_elements.sort(key=itemgetter("index"))
        data_prompt = self.prepare_prompt(embedding_elements)
        embedding_element_map = {f"{i['index']}_{i['element_sequence']}": i for i in data_prompt}
        messages = [
            {"role": "system", "content": PROMPT},
            *gen_helper_prompt(E2_ASSURANCE_HELPER_MATERIALS),
            {"role": "user", "content": json.dumps(data_prompt, ensure_ascii=False)},
        ]
        response_model: ResponseModels = self.predict_results_by_llm(
            messages, ResponseModels, elements_mapping=embedding_element_map
        )

        if not response_model:
            return []

        items = classify_esg_results(response_model.items)
        for item in sorted(items, key=lambda x: (x.index, x.element_sequence)):
            if item.enum != AnswerValueEnum.COMPLY.value:
                continue

            embedding_element = embedding_element_map.get(f"{item.index}_{item.element_sequence}")
            if not embedding_element:
                logger.warning(f"{item.index}_{item.element_sequence} not found")
                continue
            text = embedding_element["text"]
            logging.debug(f"llm_res: {text}")
            logging.debug(f"llm_res: {item.reasoning}")

            _, element = self.pdfinsight.find_element_by_index(item.index)
            if not element:
                continue
            if ElementClassifier.is_table(element):
                if item.element_sequence == 0:
                    answer = self.create_result(
                        [TableResult(element)], schema=self.schema, value=AnswerValueEnum.COMPLY
                    )
                else:
                    table = parse_table(element, tabletype=TableType.TUPLE, pdfinsight_reader=self.pdfinsight)
                    table_rows_map = dict(enumerate(table.rows, start=1))
                    table_rows_map[0] = list(chain(*table.rows))

                    cells = table_rows_map[item.element_sequence]
                    if not cells:
                        continue
                    # origin_rows = [cell["text"] for cell in element["origin_cells"].values()]
                    # if not all(cell.text in origin_rows for cell in cells):
                    #     continue
                    answer = self.create_result(
                        [TableCellsResult(element, cells)],
                        schema=self.schema,
                        value=AnswerValueEnum.COMPLY.value,
                    )
            elif ElementClassifier.is_paragraph(element):
                sub_embedding_elements = split_element(element)
                dst_chars = sub_embedding_elements[embedding_element["text"]]["chars"]
                if not dst_chars:
                    continue
                answer = self.create_result(
                    [CharResult(element, dst_chars)],
                    schema=self.schema,
                    value=AnswerValueEnum.COMPLY.value,
                )
            else:
                page_box = [
                    {
                        "page": element["page"],
                        "outline": element["outline"],
                        "text": element.get("text", ""),
                        "elements": [element],
                    }
                ]
                answer = self.create_result(
                    [OutlineResult(page_box=page_box, element=element, origin_elements=[element])],
                    schema=self.schema,
                    value=AnswerValueEnum.COMPLY.value,
                )

            results.append(answer)
        return results

    def prepare_prompt(self, elements):
        ret = []
        table_elements = self.get_table_elements(elements)
        ret.extend(table_elements)
        para_like_elements = self.get_para_like_elements(elements)
        ret.extend(para_like_elements)
        for item in ret:
            logger.debug(f"{item['index']=}, {item['element_sequence']=}, {item['text']=}")
        return ret

    def get_para_like_elements(self, embedding_elements):
        ret = []
        for embedding_element in embedding_elements:
            element_type, element = self.pdfinsight.find_element_by_index(embedding_element["index"])
            if not element:
                continue
            if not ElementClassifier.like_paragraph(element):
                continue
            ret.append(
                {
                    "index": embedding_element["index"],
                    "text": embedding_element["text"],
                    "element_sequence": embedding_element["element_sequence"],
                }
            )

        return ret

    def get_table_elements(self, embedding_elements):
        ret = []
        for embedding_element in embedding_elements:
            element_type, element = self.pdfinsight.find_element_by_index(embedding_element["index"])
            if not element:
                continue
            if not ElementClassifier.is_table(element):
                continue
            ret.append(
                {
                    "index": embedding_element["index"],
                    "text": embedding_element.get("text", ""),
                    "element_sequence": embedding_element["element_sequence"],
                }
            )
        return ret
