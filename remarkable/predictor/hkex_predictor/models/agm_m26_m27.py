import re
from typing import List, Tuple

from remarkable.common.common import clean_director_name, rotate_values
from remarkable.common.common_pattern import R_MIDDLE_DASHES
from remarkable.common.constants import Answer<PERSON><PERSON>ue<PERSON>num, TableType
from remarkable.common.pattern import <PERSON><PERSON>ult<PERSON>, PatternCollection
from remarkable.common.util import clean_txt, split_paragraph
from remarkable.optools.table_util import TableUtil
from remarkable.pdfinsight.parser import parse_table
from remarkable.predictor.default_predictor.utils import is_table_result
from remarkable.predictor.models.base_model import BaseModel
from remarkable.predictor.schema_answer import Paragraph<PERSON><PERSON>ult, TableCellsResult, TableResult


def prepare_syllabus_section_elements(pdfinsight, syllabus_pattern: PatternCollection = None):
    elements = []
    for page_eles in pdfinsight.page_element_dict.values():
        if any(syllabus_pattern.nexts(clean_txt(ele.data.get("text") or "")) for ele in page_eles[:2]):
            for ele in page_eles:
                _, element = pdfinsight.find_element_by_index(ele.data["index"])
                elements.append(element)
    return elements


def unique_list(list_obj):
    return list({i.get("index"): i for i in list_obj}.values())


class AGMM26(BaseModel):
    """
    issue: # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/5973
    """

    P_NAME_PREFIX = PatternCollection([r"(Mr|Ms|Dr|Prof|Mdm|Mass|Mrs)\."])

    P_KEYWORDS = PatternCollection(
        [
            r"lengths? of (their )?tenure",
            r"As at the Latest Practicable Date[^.]+tenures[^.]+follows",
        ],
        re.I,
    )

    P_TENURE_DESC = PatternCollection([r"appointed as (an )?(ined|independent non-executive Director)"], re.I)
    P_TENURE_LENGTH = PatternCollection(
        [r"length of tenure .*?Latest Practicable Date was (more than )?.{1,10} years"], re.I
    )

    P_TENURE_SUMMARY = PatternCollection(
        [
            r"(ined|independent non-executive directors) .*? more than .{1,10} years .*? length of tenure are shown below"
        ],
        re.I,
    )

    P_TENURE_LENGTH_OF_SUMMARY = PatternCollection(
        [r"(ined|independent non-executive directors) for .{1,10} years"], re.I
    )

    P_SYLLABUS = PatternCollection(
        r"LETTER FROM THE BOARD",
        re.I,
    )

    def train(self, dataset, **kwargs):
        pass

    def predict_schema_answer(self, elements):
        """
        flag self.predictor.prophet.metadata["notice_elements"]

        """
        director = self.predictor.prophet.metadata.get("director")
        ans_value: AnswerValueEnum = (
            AnswerValueEnum.PS if (director and director.get("all_ined_gt_9")) else AnswerValueEnum.ND
        )
        results = []
        if ans_value == AnswerValueEnum.ND:
            return results
        p_director_names = self.build_director_name_patterns(director.get("ined"))
        if not p_director_names:
            return results
        ele_results = []
        paragraphs = []
        elements += prepare_syllabus_section_elements(self.pdfinsight, self.P_SYLLABUS)
        for element in sorted(unique_list(elements), key=lambda x: x["index"]):
            if not p_director_names:
                break
            if element["class"] == "TABLE":
                if not self.P_KEYWORDS.nexts(clean_txt(element["title"])):
                    table = parse_table(element, tabletype=TableType.TUPLE.value, pdfinsight_reader=self.pdfinsight)
                    if not any(self.P_KEYWORDS.nexts(clean_txt(ele["text"])) for ele in table.elements_above):
                        continue
                ele_results.extend(self.build_result_by_table(element, p_director_names))
            if element["class"] == "PARAGRAPH":
                paragraphs.append(element)
                ele_results.extend(self.build_result_by_paragraph(element, p_director_names))
        if p_director_names:
            summary_results = self.build_result_by_summary_elements(paragraphs, p_director_names)
            if p_director_names:
                ans_value = AnswerValueEnum.ND
            ele_results.extend(summary_results)

        if (
            all(is_table_result(result) for result in ele_results)
            and len({result.element["index"] for result in ele_results}) == 1
        ):
            table = ele_results[0].parsed_table
            if len(table.rows) - 1 <= len(ele_results):
                return [
                    self.create_result(
                        [TableResult(ele_results[0].element, parsed_table=table)], schema=self.schema, value=ans_value
                    )
                ]

        return [self.create_result([result], schema=self.schema, value=ans_value) for result in ele_results]

    def build_result_by_table(self, element, director_names: List[Tuple[PatternCollection, MatchMulti]]):
        ele_results = []
        row_cells, _ = TableUtil.group_cells(element["cells"])
        table = parse_table(element, tabletype=TableType.TUPLE.value, pdfinsight_reader=self.pdfinsight)
        for cells in table.rows:
            row_texts = [self.P_NAME_PREFIX.sub("", clean_txt(cell.text)).strip() for cell in cells]
            for t_patterns in reversed(director_names):
                p_name, m_name = t_patterns
                if p_name.nexts("".join(row_texts)) or m_name.search("".join(row_texts)):
                    director_names.remove(t_patterns)
                    ele_results.append(TableCellsResult(element, cells))
                    break
        return ele_results

    def build_result_by_paragraph(self, element, director_names: List[Tuple[PatternCollection, MatchMulti]]):
        ele_results = []
        element_text = element.get("text") or ""
        if not self.P_TENURE_DESC.nexts(clean_txt(element_text)):
            return ele_results
        for sub_text in split_paragraph(element_text):
            start = element_text.index(sub_text)
            end = start + len(sub_text)
            sub_text = clean_txt(self.P_NAME_PREFIX.sub("", sub_text).strip())
            if self.P_TENURE_DESC.nexts(sub_text):
                ele_results.append(ParagraphResult(element, element["chars"][start:end]))
                continue
            if not self.P_TENURE_LENGTH.nexts(sub_text):
                continue
            match = False
            for t_patterns in reversed(director_names):
                p_name, m_name = t_patterns
                if p_name.nexts(sub_text) or m_name.search(sub_text):
                    match = True
                    director_names.remove(t_patterns)
            if not match:
                continue
            ele_results.append(ParagraphResult(element, element["chars"][start:end]))
        return ele_results

    def build_result_by_summary_elements(
        self, elements: List[dict], director_names: List[Tuple[PatternCollection, MatchMulti]]
    ):
        """
        从综述段落及后续的子句中定位INED信息
        """
        ele_results = []
        summary_para = None
        for element in sorted(elements, key=lambda x: x["index"]):
            text = clean_txt(element["text"])
            if not summary_para:
                if self.P_TENURE_SUMMARY.nexts(text):
                    ele_results.append(ParagraphResult(element, element["chars"]))
                    summary_para = element
                continue
            if not self.P_TENURE_LENGTH_OF_SUMMARY.nexts(text):
                continue
            for t_patterns in reversed(director_names):
                p_name, m_name = t_patterns
                if not p_name.nexts(text) and not m_name.search(text):
                    continue
                ele_results.append(ParagraphResult(element, element["chars"]))
                director_names.remove(t_patterns)
                break
        return ele_results

    @classmethod
    def build_director_name_patterns(cls, directors) -> List[Tuple[PatternCollection, MatchMulti]]:
        director_names = []
        if not directors:
            return director_names
        for director in directors:
            d_names = [re.escape(director.chinese_name)]
            en_names = clean_director_name(director.english_name).split()
            d_names.extend(rotate_values(en_names, key=rf"[{R_MIDDLE_DASHES},\s]{{0,2}}"))
            director_names.append((PatternCollection(d_names, re.I), MatchMulti.compile(*en_names, operator=all)))
        return director_names


class AGMM27(BaseModel):
    P_APPOINT_RULES = PatternCollection(
        [
            r"(?:appointment|appoint) .*? as an (?:new )?(?:ined|independent non-executive director)",
        ],
        re.I,
    )

    P_PROCESS_INED_RULES = PatternCollection(
        [
            r"process of identifying\s?a new (?:ined|independent non-executive director)",
        ],
        re.I,
    )

    P_KEYWORDS = PatternCollection(
        [
            r"(?:appoint|identify) a new (?:ined|independent non-executive director)",
            P_APPOINT_RULES,
            P_PROCESS_INED_RULES,
        ],
        re.I,
    )

    P_SYLLABUS = PatternCollection(
        r"LETTER FROM THE BOARD",
        re.I,
    )

    def train(self, dataset, **kwargs):
        pass

    def predict_schema_answer(self, elements):
        """
        flag self.predictor.prophet.metadata["notice_elements"]

        """
        director = self.predictor.prophet.metadata.get("director")
        ans_value: AnswerValueEnum = (
            AnswerValueEnum.PS if (director and director.get("all_ined_gt_9")) else AnswerValueEnum.ND
        )
        results = []
        if ans_value == AnswerValueEnum.ND:
            return results
        match_results = []
        elements += prepare_syllabus_section_elements(self.pdfinsight, self.P_SYLLABUS)
        for element in sorted(unique_list(elements), key=lambda x: x["index"]):
            if element["class"] != "PARAGRAPH":
                continue
            element_text = element.get("text") or ""
            for sub_text in split_paragraph(element_text):
                if self.P_KEYWORDS.nexts(clean_txt(sub_text)):
                    start = element_text.index(sub_text)
                    end = start + len(sub_text)
                    match_results.append(ParagraphResult(element, element["chars"][start:end]))
        if not match_results:
            return results
        for result in match_results:
            if self.P_APPOINT_RULES.nexts(clean_txt(result.text)):
                results.append(self.create_result([result], schema=self.schema, value=ans_value))
                return results
        for result in match_results:
            if self.P_PROCESS_INED_RULES.nexts(clean_txt(result.text)):
                results.append(
                    self.create_result(
                        [ParagraphResult(result.element, result.element["chars"])], schema=self.schema, value=ans_value
                    )
                )
                return results

        return results
