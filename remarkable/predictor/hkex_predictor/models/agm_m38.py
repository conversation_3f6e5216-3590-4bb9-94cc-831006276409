import re

from remarkable.common.constants import AnswerValueEnum, TableType
from remarkable.common.pattern import MATCH_NEVER, MatchMulti, PatternCollection
from remarkable.common.util import clean_txt, index_in_space_string, split_paragraph
from remarkable.pdfinsight.parser import parse_table
from remarkable.predictor.hkex_predictor.models import AGMM35
from remarkable.predictor.schema_answer import CharResult, TableResult


class AGMM38(AGMM35):
    """
    1. 提取每个待选董事相关 ps 或 ns 相关的描述
    2. 判断枚举
        NS: 所有董事都为否定描述则为NS
        PS: 所有董事都有披露且有肯定描述
        ND: 任意一个董事没有披露

    """

    filter_elements_by_target = False

    P_NAME = PatternCollection(r"\b(Mr|Ms|Mdm|Dr|Prof|Madam)(\.|\s)\s*[A-Za-z]+\b")
    P_DISCLOSED_ABOVE = MatchMulti.compile(r"^Save as disclosed above", operator=any)

    @property
    def title_patterns(self):
        return PatternCollection(self.config.get("title_patterns"), flags=self.flags)

    @property
    def neglect_item_pattern(self, col=None):
        regs = self.get_config("neglect_item_pattern")
        if regs:
            return PatternCollection(regs, flags=self.flags)
        return PatternCollection(MATCH_NEVER, flags=self.flags)

    def predict_schema_answer(self, elements):
        answers = []
        re_elect_directors = self.predictor.prophet.metadata.get("re_elect_directors")
        value = AnswerValueEnum.NS.value
        if not re_elect_directors:
            return answers
        all_ns_answers = []
        values = []

        for idx, re_elect_director in enumerate(re_elect_directors):
            ps_answers = []
            ns_answers = []
            for element in self.get_candidate_elements_by_range(re_elect_director["range"], need_score=False):
                if element["class"] != "PARAGRAPH":
                    # TODO 处理在表格描述的情况
                    if element["class"] == "TABLE":
                        table = parse_table(element, tabletype=TableType.TUPLE, pdfinsight_reader=self.pdfinsight)
                        if table and self.title_patterns.nexts(table.title.text):
                            ps_answers.extend([TableResult(element, parsed_table=table)])
                    continue
                # 最后一个可能包含了 总体描述所有董事的情况
                all_ns_answer = []
                if idx == len(re_elect_directors) - 1:
                    # 先按分句子提取
                    all_ns_answer = self.create_content_result(element, self.all_ns_pattern)
                    # 分句提取不到 按整体判断
                    if not all_ns_answer:
                        all_ns_answer = self.create_all_ns_answer(element, self.all_ns_pattern)
                    if all_ns_answer:
                        all_ns_answers.extend(all_ns_answer)
                ps_answers.extend(self.create_content_result(element, self.ps_pattern))
                if not all_ns_answer:
                    ns_answers.extend(self.create_content_result(element, self.ns_pattern))
                if not ns_answers and not all_ns_answer:
                    ns_answers.extend(self.create_spilit_ns_content(element, self.item_separator))
            if not ps_answers and not ns_answers:
                values.append(AnswerValueEnum.ND.value)
            elif ps_answers:
                values.append(AnswerValueEnum.PS.value)
            else:
                values.append(AnswerValueEnum.NS.value)
            if ps_answers + ns_answers:
                answers.append(
                    {self.schema.name: [self.create_result(ps_answers + ns_answers, column=self.schema.name)]}
                )
        if not answers:
            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6164#note_664594
            answers, values = self.predict_answers_from_crude_elements(re_elect_directors, elements)
        if not all_ns_answers:
            # 前面提取不到 总体描述ns的情况 再从初步定位剩下的范围内找
            for element in elements:
                if re_elect_directors[0]["range"][0] < element["index"] < re_elect_directors[-1]["range"][1]:
                    continue
                if not element["class"] == "PARAGRAPH":
                    continue
                if left_ns_answer := self.create_content_result(element, self.all_ns_pattern):
                    all_ns_answers.extend(left_ns_answer)
                elif left_ns_answer := self.create_all_ns_answer(element, self.all_ns_pattern):
                    all_ns_answers.extend(left_ns_answer)
            # 以save as above 开头的一段话中，披露了其他董事没有任何关联关系的描述
            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6163#note_664579
            for element in elements:
                if not element["class"] == "PARAGRAPH":
                    continue
                element_text = element.get("text", "")
                if self.P_DISCLOSED_ABOVE.search(element_text):
                    director_names = [
                        self.P_NAME.nexts(director["name"][0]).group()
                        if self.P_NAME.nexts(director["name"][0])
                        else director["name"][0]
                        for director in re_elect_directors
                    ]
                    name_patterns = [MatchMulti.compile(re.escape(name), operator=any) for name in director_names]
                    if all(pattern.search(element_text) for pattern in name_patterns):
                        all_ns_answers.extend(self.create_content_result(element, self.ns_pattern))
                    elif any(pattern.search(element_text) for pattern in name_patterns):
                        values.append(AnswerValueEnum.NS.value)
                        ns_answers = self.create_content_result(element, self.ns_pattern)
                        answers.append({self.schema.name: [self.create_result(ns_answers, column=self.schema.name)]})

        if all_ns_answers:
            # 提取总体描述了NS的情况
            answers.append({self.schema.name: [self.create_result(all_ns_answers, column=self.schema.name)]})
            value = AnswerValueEnum.PS.value if AnswerValueEnum.PS.value in values else AnswerValueEnum.NS.value
        else:
            if AnswerValueEnum.ND in values:
                value = AnswerValueEnum.ND.value
            elif AnswerValueEnum.PS in values:
                value = AnswerValueEnum.PS.value
        element_indices = set()
        for common_result in self.get_common_predictor_results(answers):
            common_result.update_answer_value(value)
            for element_result in common_result.element_results[:]:
                if isinstance(element_result, CharResult):
                    element_result_index = (
                        f"{element_result.element['index']}_{element_result.start}_{element_result.end}"
                    )
                else:
                    element_result_index = element_result.element["index"]
                if element_result_index in element_indices:
                    common_result.element_results.remove(element_result)
                    break
                element_indices.add(element_result_index)

        return answers

    def create_spilit_ns_content(self, element, separator):
        # 段落拆成每个子项再和第一句拼接起来后匹配
        # http://100.64.0.105:55647/#/project/remark/324779?treeId=51075&fileId=89085&schemaId=33&projectId=51075&schemaKey=M38-interests%20in%20shares%20of%20the%20issuer
        clean_text = clean_txt(element["text"])
        ret = []
        all_chars = element.get("chars") or []
        items = []
        end = 0
        for sentence, (sen_start, _) in split_paragraph(clean_text, need_pos=True):
            inner_start = 0
            for matched in separator.finditer(sentence):
                start = sen_start + inner_start
                end = sen_start + matched.start()
                item_text = clean_text[start:end]
                if self.neglect_item_pattern.nexts(item_text):
                    continue
                items.append((start, end))
                inner_start = matched.end()
        items.append((end, len(clean_text)))
        if len(items) < 3:
            return ret
        pre_sen = clean_text[items[0][0] : items[0][1]]
        pre_sp_start, pre_sp_end = index_in_space_string(element["text"], (items[0][0], items[0][1]))
        for item_start, item_end in items[1:]:
            combine_sen = pre_sen + clean_text[item_start:item_end]
            if self.ns_pattern.nexts(combine_sen):
                sp_start, sp_end = index_in_space_string(element["text"], (item_start, item_end))
                ret.append(CharResult(element, all_chars[pre_sp_start:pre_sp_end] + all_chars[sp_start:sp_end]))
        return ret

    def predict_answers_from_crude_elements(self, directors, elements):
        answers = []
        values = []
        for director in directors:
            ps_answers = []
            ns_answers = []
            exists_element_indicies = set()
            director_name = re.split(r"[,，]|(?<!\.)\s+", director["name"][0])[0]
            director_pattern = re.compile(re.escape(director_name), re.I)
            for element in elements:
                if element["index"] in exists_element_indicies:
                    continue
                if element["class"] == "TABLE":
                    table = parse_table(element, tabletype=TableType.TUPLE, pdfinsight_reader=self.pdfinsight)
                    if (
                        table
                        and self.title_patterns.nexts(table.title.text)
                        and any(director_pattern.search(cell.clean_text) for cell in table.raw_cell_map.values())
                    ):
                        ps_answers.extend([TableResult(element, parsed_table=table)])
                        exists_element_indicies.add(element["index"])
                if element["class"] == "PARAGRAPH":
                    if director_pattern.search(element["text"]):
                        if ps_answer := self.create_content_result(element, self.ps_pattern):
                            ps_answers.extend(ps_answer)
                            exists_element_indicies.add(element["index"])
                        elif ns_answer := (
                            self.create_content_result(element, self.ns_pattern)
                            or self.create_spilit_ns_content(element, self.item_separator)
                        ):
                            ns_answers.extend(ns_answer)
                            exists_element_indicies.add(element["index"])

            if not ps_answers and not ns_answers:
                values.append(AnswerValueEnum.ND.value)
            elif ps_answers:
                values.append(AnswerValueEnum.PS.value)
            else:
                values.append(AnswerValueEnum.NS.value)
            if ps_answers + ns_answers:
                answers.append(
                    {self.schema.name: [self.create_result(ps_answers + ns_answers, column=self.schema.name)]}
                )
        return answers, values
