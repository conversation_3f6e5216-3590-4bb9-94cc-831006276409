import json
import logging
import re
from functools import cached_property
from itertools import chain
from typing import List, Tuple

from pdfparser.imgtools.ocraug.faded_h_stroke import defaultdict
from pydantic import BaseModel as PydanticBaseModel
from pydantic import Field

from remarkable.common.common import get_first_value
from remarkable.common.common_pattern import P_C_FOLLOW_PREFIX
from remarkable.common.constants import AnswerValueEnum, PolicyEsgRules, TableType
from remarkable.common.pattern import MatchMulti, PatternCollection
from remarkable.common.util import box_in_box, clean_txt
from remarkable.db import embedding_pw_db
from remarkable.models.embedding import Embedding
from remarkable.pdfinsight.parser import parse_table
from remarkable.pdfinsight.reader import PdfinsightSyllabus
from remarkable.pdfinsight.reader_table import PdfinsightTable
from remarkable.predictor.eltype import ElementClassifier
from remarkable.predictor.hkex_predictor.model_util import find_as_follow_paras
from remarkable.predictor.hkex_predictor.models.policy_esg_base_model import PolicyEsgBaseModel, ResponseModel
from remarkable.predictor.hkex_predictor.models.policy_esg_e1 import (
    gen_helper_prompt,
    get_para_like_elements,
    get_table_elements,
)
from remarkable.predictor.hkex_predictor.models.policy_esg_e2 import classify_esg_results
from remarkable.predictor.models.base_model import REPORT_YEAR_REG
from remarkable.predictor.schema_answer import (
    AnswerResult,
    FinalResult,
    OutlineResult,
    ParagraphResult,
    TableCellsResult,
    TableResult,
)

logger = logging.getLogger(__name__)

# 侧重于披露对scope1&2数据独立验证
ASSURANCE_SCOPE_PROMPT = """你是一个专业的ESG报告审核评估专家。你的任务是判断一组JSON格式的文本片段是否对scope1和scope2这两项指标数据进行披露。

输入数据格式说明：
[
    {
        "index": 整数唯一标识,
        "element_sequence": "文本在段落中的位置",
        "text": "文本内容字符串"
    },
    ...
]

关键验证标准：
1. 必须明确提及scope1、scope2或温室气体的排放
2. scope1和scope2的描述可以合并，如scope1-2或scope1&2
3. scope1和scope2的描述如果分条披露，则必须找到多条内容
4. 必须明确披露scope1、scope2、scope1&2或scopeI、scope II这些关键词
5. 如果没有提及Scope，则必须包含"排放数据"、"温室气体数据"、"碳数据"等泛称
6. 明确验证动作词（如"验证/核查/保证"）
7. data前不能出现specified、performance、financial、selected、accuracy、raw、some key等限定词汇
8. scope1和scope2的等价描述为温室气体直接排放量、温室气体间接排放量

评估步骤：
1. 检查每个JSON对象的文本片段
2. 分析是否满足关键验证的要求
3. 判断枚举值：
   - Comply：明确获得独立第三方验证
   - ND：未明确说明验证情况

评估条件：
   - 任意一段内容提出温室气体排放相关数据未经独立第三方审计机构验证或核对，均视为ND

严格要求：
- 仅当证据极其明确时才给出结论
- 如无法确定，返回空结果
- 不得主观臆断或编造内容

输出格式：
```json
{
    "index": 匹配文本的唯一标识,
    "enum": "Comply/ND",
    "element_sequence": "文本在段落中的位置",
    "reasoning": "详细推理过程"
}
"""

# 侧重于披露对scope1&2数据独立验证
ASSURANCE_PROMPT = """请严格按以下步骤处理ESG文档并结构化输出：

输入数据格式说明：
[
    {
        "index": 整数唯一标识,
        "element_sequence": "文本在段落中的位置",
        "text": "文本内容字符串"
        "has_hyperlink": "是否存在链接,true为存在，false为不存在"
    },
    ...
]

分析过程：
1. 在给出所有的内容中，分别提取对数据进行独立验证和具体数据获取指引的内容
2. 对数据进行独立验证应满足以下条件：
   - has_hyperlink必须为false
   - 明确验证动作词（如"验证/核查/保证"）
   - 如果没有提及Scope，则必须包含"排放数据"、"温室气体数据"、"碳数据"等泛称，如果没有则不应提取
   - 提及"关键数据"、"特定数据"、"关键定量数据"经过验证
3. 对数据披露的检查应满足以下条件：
   - has_hyperlink必须为true
   - 需要明确披露报告、数据经过独立验证或包含具体数据链接（超链接/访问路径/具体章节引用说明）
   - 如果披露的内容与独立验证数据无关，则不应提取

严格要求：
- 仅当证据极其明确时才给出结论
- 如无法确定，返回空结果
- 不得主观臆断或编造内容
- 最终的结果中必须同时包含独立验证及数据获取指引的内容，缺少任意一个则都视为ND

注意：
 - 文本中的the data , this report 等泛称指代为ESG报告中的独立验证报告的数据
 - 必须有详细的推理过程及原因
"""

# 侧重于披露对全部或部分数据进行独立验证
REVIEW_DATA_PROMPT = """你是一个专业的ESG报告鉴证评估专家。你的任务是判断一组JSON格式的文本片段是否对报告中的数据进行了验证。

输入数据格式说明：
[
    {
        "index": 整数唯一标识,
        "element_sequence": "文本在段落中的位置",
        "text": 文本内容字符串
    },
    ...
]

关键验证标准：
- 文本中必须披露验证动作词（如"验证/核查/保证/审核/审查"）
- 验证必须针对ESG报告全部或部分数据，必须出现the data或all data等对于数据的描述
- data前不能出现specified、performance、financial、selected、accuracy、raw、some key等限定词汇
- 必须出现与数据是经过了验证、验证数据或报告由某个机构独立验证等相同或类似的描述，如果未出现则视为ND
- 如果存在小标题披露了验证数据，且后续的正文披露了核对数据的范围，则都视为Comply
- 排除analyze/分析、assess/评估、procedure/程序、inquiry/询问、commissioned/委托等非直接验证动作
- 验证描述必须体现客观检查行为（如交叉核对、抽样测试、第三方确认等），而非主观分析、询问
- 对报告进行验证的过程和依据不需要提取，视为ND

以下内容和对数据进行独立验证有一些差异，但是属于独立验证的内容，需要提取
-  during the assurance process, nothing has come to our attention that causes us to believe that data and information stated in the Reporting Organization’s ESG Report is not correctly presented or with omission in any material respects。

需要排除的内容(如果提取的主要原因与以下的内容相似，则需要排除):
- Our responsibility is to express an opinion on the text, data, graphs and statements within the scope of verification
- verification work performed

评估步骤：
1. 检查每个JSON对象的文本片段
2. 分析是否满足独立验证的关键要求
3. 判断枚举值：
   - Comply：明确对数据进行验证
   - ND：未明确说明验证情况

严格要求：
- 如未出现the data或all data等描述, 则不能提取，如出现，需在reasoning中说明

- 仅当证据极其明确时才给出结论
- 如无法确定，返回空结果
- 不得主观臆断或编造内容

输出格式：
```json
{
    "index": 保持与输入的数据一致,
    "element_sequence": "保持与输入的数据一致",
    "enum": "Comply/ND",
    "reasoning": "详细推理过程（使用中文描述）"
}

"""

# 侧重于披露对scope1&2的数据
SCOPE_PROMPT = """你是一个专业的ESG报告审核评估专家。你的任务是判断一组JSON格式的文本片段是否对scope1和scope2这两项指标数据进行披露。

输入数据格式说明：
[
    {
        "index": 整数唯一标识,
        "element_sequence": "文本在段落中的位置",
        "text": "文本内容字符串"
    },
    ...
]

关键验证标准：
1. scope1和scope2必须披露对应的排放数值
2. scope1和scope2的描述可以合并，如scope1-2或scope1&2或scopeI、scope II等
3. scope1和scope2的描述如果分条披露，则必须找到多条内容

评估步骤：
1. 检查每个JSON对象的文本片段
2. 分析是否满足关键验证的要求
3. 判断枚举值：
   - Comply：明确获得独立第三方验证
   - ND：未明确说明验证情况

严格要求：
- 仅当证据极其明确时才给出结论
- 如无法确定，返回空结果
- 不得主观臆断或编造内容

输出格式：
```json
{
    "index": 匹配文本的唯一标识,
    "enum": "Comply/ND",
    "element_sequence": "文本在段落中的位置",
    "reasoning": "详细推理过程"
}

"""

# 对独立验证的段落检查report_year是否正确
REPORT_YEAR_PROMPT = """你是一个专业的ESG报告审核评估专家。给出的所有内容都是披露数据或报告经过独立第三方验证，
你的任务是判断这些文本片段中所披露经过验证的报告所属年份与给出的`report_year`是否一致：

输入数据格式说明：
[
    {
        "index": 整数唯一标识,
        "element_sequence": "文本在段落中的位置",
        "text": "文本内容字符串"
        "report_year": "报告年份，与text中的报告年份进行比较"
    },
    ...
]

评估步骤：
    1. 从给出的每个文本片段中提取报告经过独立验证的所属年份
    2. 如果没有年份或者年份与`report_year`一致，视为Comply，否则视为ND

严格要求：
- 仅当证据极其明确时才给出结论
- 如无法确定，返回空结果
- 不得主观臆断或编造内容

输出格式：
```json
{
    "index": 匹配文本的唯一标识,
    "enum": "Comply/ND",
    "element_sequence": "文本在段落中的位置",
    "reasoning": "详细推理过程"
}

注意：
 - 文本中的the data , this report 等泛称指代为ESG报告中的独立验证报告的数据
 - 必须有详细的推理过程及原因
"""

# https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6344#note_690121
P_SCOPE = PatternCollection(r"\bScope(?!\sof)", re.I)
P_SPECIAL_QUERY = MatchMulti.compile(
    r"assurance report can (was|are|were|be) found on our website",
    operator=any,
)

P_FOLLOW_DESC = MatchMulti.compile(
    r"as follow.*?[:：]$",
    r"bell?ow.*?[:：]$",
    r"The objectives are to[:：]$",
    operator=any,
)

P_GHG_AS_FOLLOW = MatchMulti.compile(
    r"(?:greenhouse gas|GHG) emissions [^.]+ as follow.*?[:：]$",
    operator=any,
)

P_SPECIFIED_DATA = PatternCollection(
    r"\bspecified\b[^.]+data\b",
    re.I,
)

P_URL = PatternCollection(
    [
        r"[\(（]\s*https?://[^\s]+",
        r"[\(（]\s*www\.[^\s]+",
        r"[^\s]+\.(?:com|org|cn|hk)\s*[\)）]",
    ],
    re.I,
)

P_INVALID_ELEMENT = MatchMulti.compile(
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7471#note_742766
    r"Assessing whether[\w\s]+data",
    # 忽略自行核算的温室气体数据
    r"(?:greenhouse gas|GHG) emissions [^.]+ was self-accounted",
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7699#note_761028
    r"engaging an external independent assurer to ensure that the information and data presented in this report",
    r"This report has been independently assured by the British Standards Institution with respect to the extent of its coverage",
    operator=any,
)

P_HKQAA_ASSURANCE = MatchMulti.compile(
    r"^(?:Hong Kong Quality Assurance Agency|HKQAA[’'‘s]{0,2}).*?performed a limited assurance engagement",
    operator=any,
)

P_HKQAA_RESPONSIBILITY = MatchMulti.compile(
    r"^(?:Hong Kong Quality Assurance Agency|HKQAA[’'‘s]{0,2}).*?responsibility is to express an assurance conclusion",
    operator=any,
)


class ResponseModels(PydanticBaseModel):
    items: List[ResponseModel] = Field(default_factory=list)


class IndependentVerification(PydanticBaseModel):
    items: List[ResponseModel] = Field(default_factory=list)
    reasoning: str | None = None


class PolicyEsgE4(PolicyEsgBaseModel):
    def train(self, dataset, **kwargs):
        pass

    def supplement_info(self, elements):
        ret = []
        table_elements = get_table_elements(elements, self.pdfinsight)
        ret.extend(table_elements)
        para_like_elements = get_para_like_elements(elements, self.pdfinsight)
        ret.extend(para_like_elements)
        unique_data = {}
        for item in ret:
            if not item["text"]:
                continue
            key = (item["index"], item["element_sequence"])
            if key not in unique_data:
                unique_data[key] = item

        return list(unique_data.values())

    def prepare_assurance_elements(self):
        assurance_elements = self.prepare_e2_elements
        assurance_elements.extend(self.prepare_e3_elements)
        assurance_elements.sort(key=lambda x: x["index"])
        return assurance_elements

    @cached_property
    def prepare_e2_elements(self):
        elements = []
        valid_element_idxes = []
        predictor_e2 = self.predictors.get(PolicyEsgRules.E2)
        if len(predictor_e2.answer_groups) == 1:
            for predictor_result in self.get_common_predictor_results(get_first_value(predictor_e2.answer_groups)):
                valid_element_idxes.extend(
                    ele["index"] for res in predictor_result.element_results for ele in res.elements
                )
        for index in set(valid_element_idxes):
            _, ele = self.pdfinsight.find_element_by_index(index)
            elements.append(ele)

        return elements

    @cached_property
    def prepare_e3_elements(self):
        elements, valid_element_idxes = [], []
        predictor = self.predictors.get(PolicyEsgRules.E3)
        if len(predictor.answer_groups) == 1:
            element_pages = []
            for predictor_result in self.get_common_predictor_results(get_first_value(predictor.answer_groups)):
                element_pages.extend(ele["page"] for res in predictor_result.element_results for ele in res.elements)
            for page in set(element_pages):
                valid_element_idxes.extend(item["index"] for item in self.pdfinsight.find_elements_by_page(page))
        for index in set(valid_element_idxes):
            _, ele = self.pdfinsight.find_element_by_index(index)
            if self.pdfinsight.is_chinese_elt(ele):
                continue
            elements.append(ele)

        return [ele for ele in elements if "text" not in ele or not P_INVALID_ELEMENT.search(ele["text"])]

    def prepare_scope_elements(self):
        embedding_elements = self.predictor.prophet.metadata[self.schema.name]
        scope_elements = self.supplement_info(embedding_elements["scope"])
        scope_elements.sort(key=lambda x: x["index"])
        return scope_elements

    def process_items(self, items):
        answers = []
        has_hyperlink = False
        for item in items:
            _, element = self.pdfinsight.find_element_by_index(item.index)
            if not element:
                continue
            has_hyperlink = has_hyperlink or self.has_hyperlink(element)
            answers.extend(self.build_answer(element, element_sequence=item.element_sequence))
        return answers, has_hyperlink

    def build_answer(self, element, element_sequence=0) -> list[AnswerResult]:
        answers = []
        if ElementClassifier.is_table(element):
            if element_sequence == 0:
                answers.append(TableResult(element))
            else:
                table = parse_table(element, tabletype=TableType.TUPLE, pdfinsight_reader=self.pdfinsight)
                table_rows_map = dict(enumerate(table.rows, start=1))
                table_rows_map[0] = list(chain(*table.rows))

                cells = table_rows_map[element_sequence]
                if not cells:
                    return answers
                # origin_rows = [cell["text"] for cell in element["origin_cells"].values()]
                # if not all(cell.text in origin_rows for cell in cells):
                #     return answers
                answers.append(TableCellsResult(element, cells))
        elif ElementClassifier.is_chart(element):
            page_box = PdfinsightSyllabus.elements_outline(
                [element],
                pdfinsight=self.pdfinsight,
                ignore_column_position=True,
            )
            answers.append(OutlineResult(page_box=page_box, element=element, origin_elements=[element]))
        elif element.get("chars"):
            answers.append(ParagraphResult(element, element["chars"]))
        return answers

    @property
    def nd_answers(self):
        return [self.create_result([], schema=self.schema, value=AnswerValueEnum.ND)]

    def predict_by_llm(self, elements, prompt, response_format, helper_materials=None):
        messages = [
            {"role": "system", "content": prompt},
        ]
        if helper_materials:
            messages.extend(gen_helper_prompt(helper_materials))
        messages.append({"role": "user", "content": json.dumps(elements, ensure_ascii=False)})
        embedding_element_map = {f"{i['index']}_{i['element_sequence']}": i for i in elements}
        return self.predict_results_by_llm(messages, response_format, elements_mapping=embedding_element_map)

    def prepare_elements_with_verify_data(self, elements):
        elements = self.prepare_prompt(elements, with_page=True)
        e2_elements_mapping = {}
        for ele in self.prepare_e2_elements:
            e2_elements_mapping[ele["page"]] = (
                ele["index"]
                if not e2_elements_mapping.get(ele["page"])
                else min(ele["index"], e2_elements_mapping[ele["page"]])
            )

        new_elements = []
        for ele in elements:
            if (min_idx := e2_elements_mapping.get(ele["page"])) and ele["index"] >= min_idx:
                new_elements.append(ele)
        return new_elements

    def predict_with_verify_data(self, elements):
        elements = self.prepare_elements_with_verify_data(elements)
        if not elements:
            return []
        resp: ResponseModels = self.predict_by_llm(elements, ASSURANCE_SCOPE_PROMPT, ResponseModels)
        if not resp:
            return []
        logger.info(f"llm_res: {[i.index for i in resp.items]}")
        # 明确披露Scope1&2的独立验证
        resp_items = [item for item in resp.items if item.enum == AnswerValueEnum.COMPLY.value] if resp.items else []
        if resp_items:
            for item in resp_items:
                logging.debug(f"llm_res: {item.text}")
                logging.debug(f"llm_res: {item.reasoning}")
            answers, _ = self.process_items([item for item in resp.items if item.enum == AnswerValueEnum.COMPLY.value])
            result = self.create_result(answers, schema=self.schema, value=AnswerValueEnum.COMPLY)
            return [result]
        return []

    def predict_schema_answer(self, elements):
        # 1. 是否有明确的scope 1 & 2 的独立验证
        assurance_elements = self.prepare_assurance_elements()
        if not assurance_elements:
            return self.nd_answers
        elements += assurance_elements
        elements = sorted({i.get("index"): i for i in elements}.values(), key=lambda x: x["index"])

        if results := self.predict_with_verify_data(elements):
            if P_GHG_AS_FOLLOW.search(results[0].text):
                scope1_2_results, _ = self.check_scope1_2_answer()
                results.extend([self.create_result(scope1_2_results, schema=self.schema, value=AnswerValueEnum.COMPLY)])
            return results

        return self.find_query_data_answers(elements) or self.find_verify_data_answers() or self.nd_answers

    def find_query_data_answers(self, elements):
        prompt_elements = []
        for ele in self.prepare_prompt(elements):
            _, element = self.pdfinsight.find_element_by_index(ele["index"])
            ele["has_hyperlink"] = bool(self.has_hyperlink(element))
            prompt_elements.append(ele)
        if not prompt_elements:
            return []
        resp: IndependentVerification = self.predict_by_llm(prompt_elements, ASSURANCE_PROMPT, IndependentVerification)
        if not resp:
            return []
        resp_items = [item for item in resp.items if item.enum == AnswerValueEnum.COMPLY.value] if resp.items else []

        logger.info(f"llm_res: {resp.reasoning}")
        logger.info(f"llm_res: {[i.index for i in resp.items]}")

        if resp_items:
            for item in resp_items:
                logging.debug(f"llm_res: {item.text}")
                logging.debug(f"llm_res: {item.reasoning}")
            answers, has_hyperlink = self.process_items(resp_items)
            special_query = self.is_special_query(resp_items)
            if answers and (has_hyperlink or special_query):
                return [self.create_result(answers, schema=self.schema, value=AnswerValueEnum.QUERY)]
        return []

    def find_verify_data_answers(self):
        elements = self.prepare_e3_elements
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6344#note_690117
        invalid_elements = []
        for ele in elements:
            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7492#note_745380
            if P_SPECIFIED_DATA.nexts(clean_txt(ele.get("text") or "")):
                if syll := self.pdfinsight.get_nearest_syllabus(ele):
                    invalid_elements.extend(range(*syll["range"]))
                    continue
                invalid_elements.append(ele["index"])
        elements = [ele for ele in elements if ele["index"] not in invalid_elements]
        if not elements:
            return []

        results, scope1_2_elements = self.check_scope1_2_answer()
        element_idxes = [ele["index"] for ele in scope1_2_elements]
        elements = [ele for ele in elements if ele["index"] not in element_idxes]

        match_elements = []
        # 针对HKQAA的验证报告，直接提取独立验证的段落及目标所属段落
        for idx, ele in enumerate(elements):
            if P_HKQAA_ASSURANCE.search(ele.get("text") or ""):
                for element in elements[idx + 1 :]:
                    if P_HKQAA_RESPONSIBILITY.search(element.get("text") or ""):
                        match_elements = [(None, ele), (None, element)]
                        break
            if match_elements:
                break
        if not match_elements:
            resp: ResponseModels = self.predict_by_llm(
                self.prepare_prompt(elements), REVIEW_DATA_PROMPT, ResponseModels
            )
            if not resp:
                return []
            resp_items = (
                [item for item in resp.items if item.enum == AnswerValueEnum.COMPLY.value] if resp.items else []
            )
            for item in resp_items:
                logging.debug(f"llm_res: {item.text}")
                logging.debug(f"llm_res: {item.reasoning}")
            resp_items = self.filter_results_by_report_year(resp_items)
            if not resp_items:
                return []

            match_elements = [self.pdfinsight.find_element_by_index(item.index) for item in resp_items]
        answer_group = []
        for _, element in sorted(match_elements, key=lambda x: x[-1]["index"]):
            if not element:
                continue
            if P_FOLLOW_DESC.search(element.get("text") or ""):
                f_elements = find_as_follow_paras(self.pdfinsight, element, P_C_FOLLOW_PREFIX)
                element_idxes.extend(ele["index"] for ele in f_elements)
                page_box = PdfinsightSyllabus.elements_outline(
                    f_elements, pdfinsight=self.pdfinsight, ignore_column_position=True
                )
                results.append(OutlineResult(page_box=page_box, element=f_elements[0], origin_elements=f_elements))
            else:
                answer_group.append(element)
        if answer_group:
            page_box = PdfinsightSyllabus.elements_outline(
                answer_group, pdfinsight=self.pdfinsight, ignore_column_position=True
            )
            results.append(OutlineResult(page_box=page_box, element=answer_group[0], origin_elements=answer_group))

        return [self.create_result(results, schema=self.schema, value=AnswerValueEnum.COMPLY)] if results else []

    def filter_results_by_report_year(self, resp_items: list[ResponseModel]):
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7491#note_745708
        new_results, prompt_elements = [], []
        resp_mapping = {f"{item.index}:{item.element_sequence}": item for item in resp_items}
        for item in resp_items:
            if REPORT_YEAR_REG.search(item.text):
                prompt_elements.append(
                    {
                        "text": item.text,
                        "index": item.index,
                        "element_sequence": item.element_sequence,
                        "report_year": self.report_year,
                    }
                )
            else:
                new_results.append(item)
        if not prompt_elements:
            return resp_items
        resp: ResponseModels = self.predict_by_llm(prompt_elements, REPORT_YEAR_PROMPT, ResponseModels)
        if not resp:
            return self.nd_answers
        return [
            resp_mapping[f"{item.index}:{item.element_sequence}"]
            for item in resp.items
            if item.enum == AnswerValueEnum.COMPLY and f"{item.index}:{item.element_sequence}" in resp_mapping
        ] + new_results

    def check_scope1_2_answer(self) -> Tuple[list[AnswerResult], list[dict]]:
        results, scope1_2_elements = [], []
        scope_results = defaultdict(list)
        scope_elements = self.prepare_scope_elements()
        scope1_2_indices = {item["index"] for item in scope_elements}
        for scope1_2_answer in self.predictor.prophet.metadata[self.schema.name].get("scope1_2_answer") or []:
            for item in scope1_2_answer.data:
                ele_idxes = []
                for box in item.get("boxes", []):
                    outline = (
                        box["box"]["box_left"],
                        box["box"]["box_top"],
                        box["box"]["box_right"],
                        box["box"]["box_bottom"],
                    )
                    for _, ele in self.pdfinsight.find_elements_by_outline(box["page"], outline):
                        ele_idxes.append(ele["index"])
                        if ele["index"] not in scope1_2_indices:
                            scope1_2_indices.add(ele["index"])
                            scope1_2_elements.append(ele)
                res = FinalResult(item)
                if any(res.text == prev_res.text for prev_res in results if prev_res.text):
                    continue
                scope_results[tuple(set(ele_idxes))].append(res)
                results.append(res)
        scope_elements.extend([{"index": ele["index"], "element_sequence": 0} for ele in scope1_2_elements])
        resp: ResponseModels = self.predict_by_llm(self.prepare_prompt(scope_elements), SCOPE_PROMPT, ResponseModels)
        if not resp:
            return results, scope1_2_elements
        resp_items = classify_esg_results(resp.items)
        if not resp_items:
            return results, scope1_2_elements
        if resp_items and (resp_items := [item for item in resp_items if item.enum == AnswerValueEnum.COMPLY.value]):
            results = []
            scope1_2_elements = []
            for item in resp_items:
                _, element = self.pdfinsight.find_element_by_index(item.index)
                scope1_2_elements.append(element)
                for ele_idxes in scope_results.keys():
                    if item.index in ele_idxes:
                        results.extend(scope_results.pop(ele_idxes, []))
                        break
                else:
                    new_results, _ = self.process_items([item])
                    results.extend(new_results)

        return results, scope1_2_elements

    def has_hyperlink(self, element) -> str:
        hyperlinks = self.predictor.prophet.metadata["hyperlinks"]
        hyperlinks_by_page = hyperlinks.get(element["page"])
        url = ""
        if res := P_URL.nexts(element.get("text") or ""):
            url = res.group()
        if not hyperlinks_by_page:
            return url or ""
        for hyperlink in hyperlinks_by_page:
            if box_in_box(hyperlink["box"], element["outline"]):
                return hyperlink["url"]
        return url or ""

    def is_special_query(self, items) -> bool:
        # https://jura6-esg.paodingai.com/#/hkex/esg-report-checking/report-review/250749?fileId=68581&schemaId=1&rule=E4-Independent%20Assurance%20on%20scope%201%20and%20scope%202%20GHG%20emissions
        result = False
        for item in items:
            _, element = self.pdfinsight.find_element_by_index(item.index)
            if not element:
                continue
            if ElementClassifier.like_paragraph(element):
                # 目前仅处理段落
                if P_SPECIAL_QUERY.search(clean_txt(element.get("text") or "")):
                    return True
        return result

    def prepare_prompt(self, elements, with_page=False):
        ret = []
        for org_ele in elements:
            ele_type, element = self.pdfinsight.find_element_by_index(org_ele["index"])
            element_sequence = org_ele["element_sequence"] if isinstance(org_ele.get("element_sequence"), int) else 0
            if ele_type == "TABLE":
                ele = {
                    "index": element["index"],
                    "element_sequence": element_sequence,
                    "text": PdfinsightTable.to_markdown(element),
                }
            elif ele_type == "PARAGRAPH" and len(element["text"].split()) > 4:
                ele = {"index": element["index"], "element_sequence": element_sequence, "text": element["text"]}
            elif ElementClassifier.is_chart(element):
                with embedding_pw_db.allow_sync():
                    if embedding_ele := Embedding.get_or_none(
                        Embedding.file_id == self.predictor.file.id,
                        Embedding.index == element["index"],
                        Embedding.element_sequence == 0,
                    ):
                        ele = {"index": element["index"], "element_sequence": 0, "text": embedding_ele.text}
            else:
                continue
            if with_page:
                ele["page"] = element["page"]
            ret.append(ele)
        return ret


if __name__ == "__main__":
    print(PolicyEsgRules.phrase_to_enum("T6-Source of scenarios").label)
