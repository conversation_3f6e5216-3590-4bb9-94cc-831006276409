import re
from copy import deepcopy

from remarkable.common.common_pattern import P_MIDDLE_DASH, R_MIDDLE_DASHES, R_PERIOD
from remarkable.common.pattern import <PERSON><PERSON><PERSON><PERSON>
from remarkable.common.util import split_paragraph
from remarkable.predictor.common_pattern import R_PERCENT_STRICT
from remarkable.predictor.hkex_predictor.models.poll_shares_issued import R_SHARE_CLASS
from remarkable.predictor.models.base_model import BaseModel
from remarkable.predictor.schema_answer import <PERSON><PERSON><PERSON><PERSON><PERSON>
from remarkable.predictor.utils import make_pattern

P_VALUE = re.compile(rf"\d{{1,2}}{R_PERCENT_STRICT}|(twenty|ten) per centum [(（]\d{1, 2}[%％][）)]", re.I)
P_SHARES_CLASS = re.compile(rf"(?<!suc){R_SHARE_CLASS}[\s{R_MIDDLE_DASHES}]*?shares", re.I)
P_TOTAL_CLASS = re.compile(
    r"the (aggregate|total|number|issued).*?\bshares?|the total\b.*?\bshares? capital|the shares?.*?\bin issue",
    re.I,
)
P_TOTAL = re.compile(r"aggregate|total", re.I)
P_PERIOD = re.compile(
    rf"({R_PERIOD}|(?<!\.0\d[(（]\d[)）])[;；](?!LR\d{{2}}))\s*|(?<!paragraph )(?<!paragraphs )[(（][\da-zA-Z]+[）)](?! of)(?! above)"
)
P_EXCLUDING = re.compile(r"\b(excluding|i\.e\.)(?P<content>.+?)([）)]|one basis)")

P_CHAPTER_NOTICE = MatchMulti.compile(
    r"notice of\b.*?\b(AGM|ANNUAL GENERAL MEETING)\b",
    operator=any,
)


class AGMSharesIssued(BaseModel):
    @property
    def paragraph_pattern(self, col=None):
        regs = self.get_config("paragraph_pattern", column=col)
        return make_pattern(regs, flags=self.flags)

    @property
    def neglect_pattern(self, col=None):
        regs = self.get_config("neglect_pattern", column=col)
        return make_pattern(regs, flags=self.flags)

    @staticmethod
    def is_exist_class(_class, exist_classes):
        if _class in exist_classes:
            return True
        # 都是total，保留第一个
        if any(P_TOTAL.search(c) for c in exist_classes) and P_TOTAL.search(_class):
            return True
        if P_TOTAL.search(_class) and (matched := P_SHARES_CLASS.search(_class)):
            share_class = P_MIDDLE_DASH.sub(" ", matched.group())
            if any(share_class == c for c in exist_classes):
                return True
        return False

    @staticmethod
    def range_contains(range1, range2):
        """range2是否完全包含range1"""
        start1, end1 = range1
        start2, end2 = range2
        return start2 <= start1 and end2 >= end1

    def excluding_share_class(self, element):
        share_classes = []
        for matched in P_EXCLUDING.finditer(element["text"]):
            for match in P_SHARES_CLASS.finditer(matched.group()):
                share_classes.append(match.group())
            for match in P_TOTAL_CLASS.finditer(matched.group()):
                share_classes.append(match.group())
        return share_classes

    def predict_schema_answer(self, elements):
        ret = []
        for element in elements:
            for sentence, sen_pos in split_paragraph(
                element["text"], need_pos=True, need_clean=False, separator=P_PERIOD
            ):
                if self.neglect_pattern.nexts(sentence):
                    continue
                share_matched = self.paragraph_pattern.nexts(sentence)
                if not share_matched:
                    continue
                sen_start = sen_pos[0]
                share_start = sen_start + share_matched.span()[0]
                share_end = sen_start + share_matched.span()[1]
                shares_values = self.get_value(element, share_matched.group(), (share_start, share_end))
                shares_clz = self.get_share_class(element, share_matched.group(), (share_start, share_end))
                if shares_clz and shares_values:
                    # 配对class和value
                    if len(shares_values) == 1:
                        # 只有一个value，是所有class共享的
                        pair = zip(shares_clz, [shares_values[0]] * len(shares_clz))
                    else:
                        # value应该在class之前
                        pair = []
                        used_values = []
                        for clz in shares_clz:
                            for value in shares_values:
                                if value in used_values:
                                    continue
                                if value.element_results[0].end < clz.element_results[0].start:
                                    pair.append((clz, value))
                                    used_values.append(value)
                                    break

                    for clz, shares_value in pair:
                        answer_group = {
                            "Relevant share class": [clz],
                            "Value": [deepcopy(shares_value)],
                        }
                        ret.append(answer_group)

        return self.filter_answers_by_chapter(ret)

    def get_value(self, element, sentence, sen_position):
        exists_position = set()
        ret = []
        for matched in P_VALUE.finditer(sentence):
            if element_result := self.build_result(element, matched, sen_position, exists_position, "Value"):
                ret.append(element_result)
        return ret

    def get_share_class(self, element, sentence, sen_position):
        """
        先提取各类别的share class，再提取一次total share class
        """

        ret = []
        exists_position = set()
        excluding_share_classes = self.excluding_share_class(element)
        for matched in P_SHARES_CLASS.finditer(sentence):
            if matched.group() in excluding_share_classes:
                continue
            if element_result := self.build_result(
                element, matched, sen_position, exists_position, "Relevant share class"
            ):
                ret.append(element_result)
        for matched in P_TOTAL_CLASS.finditer(sentence):
            if matched.group() in excluding_share_classes:
                continue
            if element_result := self.build_result(
                element, matched, sen_position, exists_position, "Relevant share class"
            ):
                ret.append(element_result)
        return ret

    def filter_answers_by_chapter(self, answer_results):
        has_notice_result = False
        for answer in answer_results:
            relative_element = answer["Relevant share class"][0].relative_elements[0]
            elements = self.pdfinsight.find_elements_by_page(relative_element["page"])
            first_element = elements[0]
            if P_CHAPTER_NOTICE.search(first_element["text"]):
                has_notice_result = True
                break
        if has_notice_result:
            for answer in answer_results[:]:
                relative_element = answer["Relevant share class"][0].relative_elements[0]
                elements = self.pdfinsight.find_elements_by_page(relative_element["page"])
                first_element = elements[0]
                if not P_CHAPTER_NOTICE.search(first_element["text"]):
                    answer_results.remove(answer)
        exist_classes = set()
        for result in answer_results[:]:
            share_class = result["Relevant share class"][0]
            text_class = share_class.text.lower()
            if self.is_exist_class(text_class, exist_classes):
                answer_results.remove(result)
                continue
            exist_classes.add(text_class)
        return answer_results

    def build_result(self, element, matched, sen_position, exist_positions, column):
        start, end = matched.span()
        start += sen_position[0]
        end += sen_position[0]
        if not any(self.range_contains(pos, (start, end)) for pos in exist_positions):
            exist_positions.add((start, end))
            return self.create_result(
                [CharResult(element=element, chars=element["chars"][start:end], start=start, end=end)],
                column=column,
            )
