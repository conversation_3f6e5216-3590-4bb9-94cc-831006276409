import logging
from typing import List, Literal

from pydantic import BaseModel as PydanticBaseModel

from remarkable.common.util import mm_notify
from remarkable.config import get_config
from remarkable.predictor.models.base_model import BaseModel
from remarkable.services.chatgpt import OpenAIClient

logger = logging.getLogger(__name__)


class BaseResponseModel(PydanticBaseModel):
    _text: str = ""

    @property
    def text(self):
        return self._text

    @text.setter
    def text(self, value):
        self._text = value


class ResponseModel(BaseResponseModel):
    index: int | None = None
    element_sequence: int | None = None
    reasoning: str | None = None  # llm总结和理由
    enum: Literal["Comply", "No Disclosure", None] = None

    @property
    def key(self):
        return f"{self.index}_{self.element_sequence}"


class ResponseModels(PydanticBaseModel):
    items: List[ResponseModel] = None
    enum: Literal["Comply", "No Disclosure", None] = None


class PolicyEsgBaseModel(BaseModel):
    TIMEOUT = get_config("ai.openai.timeout") or 10

    def predict_results_by_llm(self, prompt, response_format, elements_mapping: dict = None):
        try:
            llm_res = OpenAIClient().send_message(
                prompt,
                options={"timeout": self.TIMEOUT},
                response_format=response_format,
            )
        except Exception as e:
            logger.info("==" * 20)
            logger.error(prompt)
            mm_notify(
                f"{e=}, failed to predict for fid:{self.predictor.prophet.metadata['file'].id}, rule: {self.schema.name}",
                error=True,
            )
            return None
        response_model: response_format = llm_res.parsed

        if elements_mapping and hasattr(response_model, "items"):
            for item in response_model.items:
                item.text = elements_mapping.get(item.key, {}).get("text") or ""

        return response_model
