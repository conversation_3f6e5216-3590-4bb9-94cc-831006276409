import re
from typing import Dict, List

from remarkable.common.constants import AnswerValueEnum, PDFInsightClassEnum, TableType
from remarkable.common.pattern import MatchMulti, NeglectPattern, PatternCollection
from remarkable.pdfinsight.parser import parse_table
from remarkable.predictor.hkex_predictor.schemas.pattern import reg_words
from remarkable.predictor.models.special_cells import SpecialCells
from remarkable.predictor.schema_answer import ParagraphResult, PredictorResult, TableCellsResult

R_NO = r"[(（]?\d{1,2}[)）]?([(（][A-H][)）])?"


class PollResolutionPass(SpecialCells):
    COLUMN_1 = "general mandate resolution"
    COLUMN_2 = "pass or not"

    P_RESOLUTION = NeglectPattern.compile(
        match=MatchMulti.compile(
            r"(general\s?(and (an )?unconditional )?mandate.*issu(e|ance).*(share|securit)|Issue\s*Mandate)",
            r"mandate.*issu(e|ance).*(share|securit)",
            operator=any,
        ),
        unmatch=r"((re)?purchas(e|ing|ed)|(buy|bought)(-|\s)?backs?)",
    )

    P_ALL_PASSED = MatchMulti.compile(
        r"\b(each|all).*resolutions.*(passed|approved)",
        r"(each|all)\s*resolutions\s*were\s*approved",
        rf"above\s*resolutions\s*{reg_words(0, 3)}passed",
        r"approved the following resolution.*follows\s*[:：]$",
        operator=any,
    )
    P_PART_PASSED = MatchMulti.compile("resolutions.*(passed|approved)", operator=any)
    P_RESOLUTION_NO = re.compile(r"^(?P<no>\d+)\.?$")
    P_PASS_TO_NO = PatternCollection(
        [
            rf"resolutions\s((nos?|number(ed)?)\.?\s?)?(?P<start>{R_NO})\s?(to|-)\s?((nos?|number(ed)?)\.?\s?)?(?P<end>{R_NO})",
        ],
        re.I,
    )

    def predict_schema_answer(self, elements) -> List[Dict[str, List[PredictorResult]]]:
        # 从表格中取到 reolution 行
        ret = []
        resolution_row = []
        for element in elements:
            table = parse_table(element, tabletype=TableType.TUPLE, pdfinsight_reader=self.pdfinsight)
            for row in table.rows:
                for cell in row:
                    if self.P_RESOLUTION.search(cell.clean_text):
                        resolution_row = row
                        resolution_ans = self.create_result(
                            [TableCellsResult(element=element, cells=[row[0], cell], display_text=cell.clean_text)],
                            column=self.COLUMN_1,
                            value=AnswerValueEnum.PS.value,
                        )
                        ret.append({self.COLUMN_1: [resolution_ans]})
                        break
                if resolution_row:
                    break
            if resolution_row:
                if pass_ans := self.create_pass_result(table, resolution_row):
                    ret.append({self.COLUMN_2: [pass_ans]})
                else:
                    ret.append(
                        {
                            self.COLUMN_2: [
                                self.create_result([], column=self.COLUMN_2, value=AnswerValueEnum.ND_CAP.value)
                            ]
                        }
                    )
                break
        if not ret:
            ret.append(
                {
                    self.COLUMN_1: [self.create_result([], column=self.COLUMN_1, value=AnswerValueEnum.ND.value)],
                    self.COLUMN_2: [self.create_result([], column=self.COLUMN_2, value=AnswerValueEnum.ND_CAP.value)],
                }
            )

        return ret

    def create_pass_result(self, table, resolution_row):
        # 表格下方的段落＞表格上方的段落＞文档前面段落
        below_range = [table.index, self.pdfinsight.max_index]
        for below_para in self.get_candidate_elements_by_range(
            below_range, aim_types=PDFInsightClassEnum.PARAGRAPH.value
        ):
            if self.P_ALL_PASSED.search(below_para["text"]):
                return self.create_result(
                    [ParagraphResult(below_para, below_para.get("chars", []))],
                    column=self.COLUMN_2,
                    value=AnswerValueEnum.Yes.value,
                )
            if self.check_resolution_pass(table, resolution_row, below_para):
                return self.create_result(
                    [ParagraphResult(below_para, below_para.get("chars", []))],
                    column=self.COLUMN_2,
                    value=AnswerValueEnum.Yes.value,
                )
        above_paras = self.get_candidate_elements_by_range(
            [0, table.index], aim_types=PDFInsightClassEnum.PARAGRAPH.value
        )
        above_paras.sort(key=lambda x: x["index"], reverse=True)
        for above_para in above_paras:
            if self.P_ALL_PASSED.search(above_para["text"]):
                return self.create_result(
                    [ParagraphResult(above_para, above_para.get("chars", []))],
                    column=self.COLUMN_2,
                    value=AnswerValueEnum.Yes.value,
                )
        return []

    def check_resolution_pass(self, table, resulution_row, para):
        if not self.P_PART_PASSED.search(para["text"]):
            return False
        # 找到 当前 resolution 的编号
        resulution_no = resulution_row[0].clean_text.strip()
        if no_matched := self.P_RESOLUTION_NO.search(resulution_no):
            resulution_no = int(no_matched.group("no"))
        # 判断当前的 reolution 是否在描述的范围内
        for matched in self.P_PASS_TO_NO.finditer(para["text"]):
            start, end = matched.group("start"), matched.group("end")
            if start.isdigit() and end.isdigit() and (int(start) <= resulution_no <= int(end)):
                return True
        return False


if __name__ == "__main__":
    paras = [
        "resolutions no. 1 to 2, 4 to 12,"
        "Resolutions (1) to (6)"
        "Resolutions no. 1 to no. 7"
        "resolutions nos. 1 to 7"
        "resolutions numbered 1 - 8"
        "Resolutions no. 1 to no. 8"
        "Resolutions no.1 to no.5(C)"
        "As more than 50% of the votes were cast in favour of resolutions number 1 to 6,"
    ]
    for p in paras:
        for match in PollResolutionPass.P_PASS_TO_NO.finditer(p):
            print(match.group("start"), match.group("end"))
