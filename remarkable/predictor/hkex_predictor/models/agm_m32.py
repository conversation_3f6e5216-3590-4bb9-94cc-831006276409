import re

from remarkable.common.common import clean_director_name, rotate_values
from remarkable.common.common_pattern import R_MIDDLE_DASHES
from remarkable.common.constants import AnswerValueEnum
from remarkable.common.pattern import MatchMulti, PatternCollection
from remarkable.common.util import clean_txt, split_paragraph
from remarkable.predictor.hkex_predictor.schemas.pattern import R_APPELLATION, R_INED
from remarkable.predictor.models.base_model import BaseModel
from remarkable.predictor.schema_answer import Char<PERSON><PERSON><PERSON>, ParagraphResult
from remarkable.services.agm import DirectorCVMatcher


class AGMM32(BaseModel):
    """
    1.确定两次AGM时间范围内是否有委任新的INED，如果没有直接返回ND
    2.如果有，在文中找出所有上述INED的描述
    3.如果所有INED都有描述，则PS，否则NS
    """

    P_KEYWORD = MatchMulti.compile(
        MatchMulti.compile(
            r"\b(confirm|review|assessed|determine|meet(s|ted)?\b)",
            r"\bindependen(ce|t)\b(?!(?:\s*(?:non[- ]?executive|executive))?\s*directors?)",
            # r"rules? (3\.13|3\.14|5\.09)",
            operator=all,
        ),
        # 82297
        rf"consider.*?{R_APPELLATION}[A-Za-z\s]*?independen(ce|t)\b(?!(?:\s*(?:non[- ]?executive|executive))?\s*directors?)",
        r"continue\s*to\s*be\s*independent",  # 81870
        operator=any,
    )

    P_EACH_INED = MatchMulti.compile(
        rf"(each\s*of|all\s*(?:of)?)\s*(the)?\s*{R_INED}",
        # stock code: 3866  report date: 2024-05-10
        rf"each\s*candidate\s*for\s*(the)?\s*{R_INED}",
        rf"(?:retiring|Re-elect(ing|ed))\s*{R_INED}",  # 81870
        # code: 1130 year 2024
        rf"\b(reviewed|assessed)\s*the\s*annual\s*written\s*confirmation\s*of\s*independence\s*of\s*the\s*{R_INED}",
        operator=any,
    )

    # 没有明确说明被选举的独立非执行董事的独立性，而是描述了提名委员的审查过程，非结论性确认
    P_INVALID_ANSWER = MatchMulti.compile(
        r"nomination\s*committee"
        r"considered|assessed|reviewed",
        r"Nomination\s*(?:Policy|Principles)",
        r"^(?!.*\b(confirm|review|assessed|determine|meet(s|ted)?\b)[\w\s]+independen(ce|t)).*$",
        operator=all,
    )

    def find_eleted_directors(self):
        director = self.predictor.prophet.metadata.get("director")
        ined_companies: list = director and director.get("ined_companies")
        directors = [
            director["director"]
            for director in [director for director in ined_companies if (director and director.get("director"))]
            if director["director"].capacity
            in ("Independent Non Executive Director - A/F", "Independent Non Executive Director")
        ]
        if not directors:
            return []
        director_matcher = DirectorCVMatcher(self.pdfinsight)
        director_matcher.extract()
        director_matcher.filter_elements_data_by_director(directors)
        self.filter_director_mapping = director_matcher.filter_director_mapping
        return director_matcher.filter_director_data

    def predict_schema_answer(self, elements):
        self.filter_director_mapping = {}
        new_directors = self.predictor.prophet.metadata.get("new_director")
        new_ineds = [
            director
            for director in new_directors
            if director.capacity in ("Independent Non Executive Director - A/F", "Independent Non Executive Director")
        ]
        new_ineds = set(new_ineds + self.find_eleted_directors())
        found_mapping = {director.english_name: False for director in new_ineds}

        results = []
        if not new_ineds:
            return results
        name_patterns = self.build_director_name_patterns(new_ineds)
        if not name_patterns:
            return results
        value = AnswerValueEnum.PS
        for element in elements:
            if element["class"] == "PARAGRAPH" and not self.P_INVALID_ANSWER.search(element["text"]):
                name_element = self.find_director_name(element, name_patterns, found_mapping)
                for sub_content, (start, end) in split_paragraph(element["text"], need_pos=True):
                    if not self.P_KEYWORD.search(sub_content):
                        continue
                    if name_element:
                        results.append(CharResult(element, element["chars"][start:end]))
                    elif self.P_EACH_INED.search(element["text"]):
                        results.append(ParagraphResult(element, element["chars"]))
                    break
        for director_eles in self.filter_director_mapping.values():
            eles_indexes = [ele["index"] for ele in director_eles]
            if any(answer.element["index"] in eles_indexes for answer in results):
                continue
            for ele in director_eles:
                if ele in elements or ele["class"] != "PARAGRAPH" or not self.P_INVALID_ANSWER.search(ele["text"]):
                    continue
                for sub_content, (start, end) in split_paragraph(ele["text"], need_pos=True):
                    if not self.P_KEYWORD.search(sub_content):
                        continue
                    results.append(CharResult(ele, ele["chars"][start:end]))
                    break
        for director in new_ineds:
            p_name = PatternCollection(
                self.rotate_names(clean_director_name(director.english_name).split()),
                re.I,
            )
            for answer in results:
                text = clean_txt(answer.element["text"])
                if p_name.nexts(text) or self.P_EACH_INED.search(text):
                    break
            else:
                value = AnswerValueEnum.ND
        if not results:
            return []
        results = sorted(results, key=lambda x: x.element["index"])
        return [self.create_result([result], schema=self.schema, value=value) for result in results]

    @classmethod
    def build_director_name_patterns(cls, directors):
        director_names = []
        if not directors:
            return director_names
        for director in directors:
            d_names = [re.escape(director.chinese_name)]
            en_names = clean_director_name(director.english_name).split()
            d_names.extend(cls.rotate_names(en_names))
            director_names.append(
                (director.english_name, PatternCollection(d_names, re.I), MatchMulti.compile(*en_names, operator=all))
            )
        return director_names

    @staticmethod
    def rotate_names(names):
        rotated_lists = rotate_values(names, key=rf"[{R_MIDDLE_DASHES},\s]{{0,2}}")
        rotated_lists.extend([rf"{R_APPELLATION}\s*{names[0]}", rf"{R_APPELLATION}\s*{names[-1]}"])
        return rotated_lists

    @staticmethod
    def find_director_name(element, name_patterns, found_mapping):
        for english_name, d_names, en_names in name_patterns:
            if found_mapping[english_name]:
                continue
            if d_names.nexts(element["text"]) or en_names.search(element["text"]):
                found_mapping[english_name] = True
                return ParagraphResult(element, element["chars"])
