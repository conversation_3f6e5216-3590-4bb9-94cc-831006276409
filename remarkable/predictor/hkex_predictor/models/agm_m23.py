from remarkable.common.constants import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TableType
from remarkable.common.pattern import Pat<PERSON><PERSON>ollection, PositionPattern
from remarkable.common.util import clean_txt
from remarkable.optools.table_util import TableUtil
from remarkable.pdfinsight.parser import parse_table
from remarkable.predictor.models.base_model import BaseModel
from remarkable.predictor.schema_answer import Para<PERSON><PERSON><PERSON>ult, TableCellsResult


class AGMM23(BaseModel):
    """
    https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6546
    """

    P_APPENDIX = PatternCollection(
        [
            r"MEMORANDUM AND ARTICLES OF ASSOCIATION",
            r"(?:DETAILS OF|PROPOSED) AMENDMENTS? TO THE (?:MEMORANDUM|(BUY|BYE)-LAWS)",
            r"APPENDIX[^.]*?PARTICULARS OF PROPOSED AMENDMENTS?",
            # 87307
            r"THE EXISTING (BUY|BYE)-LAWS",
        ],
    )

    P_SPECIAL_RIGHTS = PositionPattern.compile(
        r"\b(special\s*)?rights\b",
        r"\b(varied|abrogated|modified)\b",
        r"(?:at\s*least|\bnot\s*less\s*than)\s*(?:three\s*-\s*fourths|3\s*/\s*4|2\s*/\s*3|two\s*-\s*thirds)\b",
    )

    def train(self, dataset, **kwargs):
        pass

    def get_syllabus_section_paragraphs(self):
        elements = []
        for page_eles in self.pdfinsight.page_element_dict.values():
            if any(self.P_APPENDIX.nexts(clean_txt(ele.data.get("text") or "")) for ele in page_eles[:2]):
                for ele in page_eles:
                    _, element = self.pdfinsight.find_element_by_index(ele.data["index"])
                    elements.append(element)
        return elements

    def predict_schema_answer(self, _):
        results = []
        for element in sorted(self.get_syllabus_section_paragraphs(), key=lambda x: x["index"]):
            if element["class"] == "TABLE":
                results = self.build_result_by_table(element)
            if element["class"] == "PARAGRAPH":
                results = self.build_result_by_paragraph(element)
            if results:
                break
        return [self.create_result([result], schema=self.schema, value=AnswerValueEnum.PS) for result in results]

    def build_result_by_table(self, element):
        row_cells, _ = TableUtil.group_cells(element["cells"])
        table = parse_table(element, tabletype=TableType.TUPLE.value, pdfinsight_reader=self.pdfinsight)
        for cells in table.rows:
            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6546#note_695856
            for cell in cells:
                if self.P_SPECIAL_RIGHTS.search(cell.text):
                    return [TableCellsResult(element, [cell])]

            row_texts = [clean_txt(cell.text).strip() for cell in cells]
            if self.P_SPECIAL_RIGHTS.search("".join(row_texts)):
                return [TableCellsResult(element, cells)]
        return []

    def build_result_by_paragraph(self, element):
        element_text = element.get("text") or ""
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7025#note_716756
        # for sub_text, (start, end) in split_paragraph(element_text, need_pos=True):
        #     if self.P_SPECIAL_RIGHTS.search(sub_text):
        #         return [ParagraphResult(element, element["chars"][start:end])]
        # else:
        if self.P_SPECIAL_RIGHTS.search(clean_txt(element_text)):
            return [ParagraphResult(element, element["chars"])]
        return []
