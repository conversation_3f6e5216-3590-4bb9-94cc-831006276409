import re

from remarkable.common.common import is_para_elt
from remarkable.common.common_pattern import R_CHAPTER_PREFIX, R_DR_CHAPTER_TITLES, R_MIDDLE_DASHES
from remarkable.common.constants import AnswerValueEnum, PDFInsightClassEnum
from remarkable.common.pattern import (
    MatchMulti,
    NeglectPattern,
    PatternCollection,
    PositionPattern,
)
from remarkable.common.util import clean_txt, split_paragraph
from remarkable.pdfinsight.reader_util import find_real_syllabus
from remarkable.predictor.common_pattern import R_DATES
from remarkable.predictor.hkex_predictor.schemas.pattern import (
    P_NOTE_CHAPTER,
    P_SAVE_AS,
    P_SAVE_AS_IN_REPORT,
    R_NOT,
    R_SAVE_AS,
)
from remarkable.predictor.models.base_model import BaseModel
from remarkable.predictor.schema_answer import Char<PERSON><PERSON>ult, ParagraphResult
from remarkable.predictor.utils import make_pattern

R_FALL_UNDER = r"(falls?|fell)\s*(under|withi?n?)\s*(the\s*)?(definition|scope)s?"
R_FULLY_EXEMPT = rf"(?<!not )(?<!including )\bfully[{R_MIDDLE_DASHES}\s]exempt(?!(ion|ed)?\s+if\s)(ion|ed)?"

P_C7_FOLLOW_PREFIX = MatchMulti.compile(
    r"^[•]",  # {R_MIDDLE_DASHES}
    r"^[(（]?(?i:\d{1,2}|[a-hj-u])[)）.]",
    r"^[(（][IVX]{1,4}[)）]",
    operator=any,
    flag=0,
)
# R_TRANSACTIONS = r"(?<!other )(?<!connected )(?<!any )transactions"
R_TRANSACTIONS = r"\b(th[eo]se|the|this|which)\s*transactions?(?!\s*between|among)"
R_CCT = r"\b(continuing\s*connected\s*(party\s*)?transaction[（(]?s?[）)]?)\b"
R_CT = r"(\bcontinuing\s*)?\b(connected\s*(party\s*)?transaction[（(]?s?[）)]?)\b"
R_RPT = r"\brelated\s*party\s*transaction[（(]?s?[）)]?"
R_C7_BE = rf"\b((are|were)\s(?!not?\b)|as\s+|also\s+|(?<!not )(constituting|constitute[sd]?)\s+(?!no\b)|(?<!not ){R_FALL_UNDER})"
R_IS_CT = rf"{R_C7_BE}(\S+\s+){{0,3}}(the\s+)?[\"“”]?{R_CT}"
P_IS_CT = MatchMulti.compile(R_IS_CT, operator=any)
P_RPT_IS_CT = PositionPattern.compile(rf"{R_RPT}|{R_TRANSACTIONS}", R_IS_CT)
P_IS_NOT_CT = MatchMulti.compile(
    rf"\s(not\s*{R_FALL_UNDER}\s*(\S+\s+){{,2}}{R_CT})",
    rf"\snot\s*(regard|consider)(ed|ing)?\s*as\s*{R_CT}",
    # http://************:55647/#/project/remark/265279?treeId=14095&fileId=70270&schemaId=18&projectId=17&schemaKey=C7.1
    rf"\b(constitute[sd]?|constituting)\s+no\s+{R_CT}",
    PositionPattern.compile(
        rf"{R_NOT}(?!other)",
        rf"\b(constitute[sd]?|constituting|regard(ed|ing)?\s*as)\s+(\S+\s){{0,3}}[\"“”]?{R_CT}",
    ),
    PositionPattern.compile(rf"{R_NOT}(?!other)", R_FALL_UNDER, R_CT),
    PositionPattern.compile(
        rf"{R_RPT}|{R_TRANSACTIONS}",
        rf"{R_NOT}(?!other)[^.]*?\s(were|are|was|is)\s*(\S+\s){{0,3}}[\"“”]?{R_CT}",
    ),
    PositionPattern.compile(
        R_NOT,
        rf"{R_RPT}|{R_TRANSACTIONS}",
        rf"\s(were|are|was|is|as\s+|constituting|constitute[sd]?)\s*(\S+\s){{0,3}}[\"“”]?{R_CT}",
    ),
    operator=any,
)
P_RPT_IS_NOT_CT = MatchMulti.compile(rf"{R_RPT}|{R_TRANSACTIONS}", P_IS_NOT_CT, operator=all)
# http://************:55647/#/project/remark/265849?treeId=10898&fileId=70384&schemaId=18&projectId=17&schemaKey=C7.1 index=768
P_C7_NO_CT_SKIP = MatchMulti.compile(r"\b(opine|consider)", P_RPT_IS_NOT_CT, operator=any)
# https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6643#note_692993
P_C7_NO_CT = MatchMulti.compile(
    PositionPattern.compile(r"\bnot\s+enter(s|ed|ing)?\s*into", R_CT),
    NeglectPattern.compile(
        match=PositionPattern.compile(rf"{R_NOT}", r"(?<!continuing )(?<!one.off )connected\s*(party\s*)?transaction"),
        unmatch=P_C7_NO_CT_SKIP,
    ),
    operator=any,
)
P_CT_ALL_EXEMPT = NeglectPattern.compile(
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6586#note_697572
    match=PositionPattern.compile(r"transactions", R_FALL_UNDER, R_CT, R_FULLY_EXEMPT),
    unmatch=R_NOT,
)
P_C7_NO_OTHER_CT = PositionPattern.compile(R_NOT, rf"other\s*{R_CT}")
# http://************:55647/#/project/remark/294540?treeId=13545&fileId=70790&schemaId=18&projectId=17&schemaKey=C7.1
P_RPT_IS_NS = MatchMulti.compile(
    PositionPattern.compile(
        rf"{R_RPT}|{R_TRANSACTIONS}", r"either\s*exempt", r"\sor\s*not\s*constitut|\b(were|are|was|is)\s*not\b", R_CT
    ),
    operator=any,
)
P_EXEMPT = MatchMulti.compile(
    # http://************:55647/#/project/remark/268237?treeId=37674&fileId=70439&schemaId=29&projectId=17&schemaKey=C7.1 index=708
    NeglectPattern.compile(match=rf"(?<!not ){R_FULLY_EXEMPT}", unmatch=rf"other\s*(related|transaction)|{R_SAVE_AS}"),
    NeglectPattern.compile(
        match=PositionPattern.compile(
            rf"(?<!\bnot)(?<!\bbut)\sexempt(s|ed|ion)?\s*from\b|{R_FULLY_EXEMPT}|\snot\s*subject\s*to\s",
            r"\b(Chapter|Rules?)\s*(20|14A)\b|\brequirements",
        ),
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6643#note_691318
        unmatch=r"remuneration\s*of\s*the\s*directors|\bother\s*(related|transaction)|\b(is|was|were|are)\s*subject\s*to\s",
    ),
    MatchMulti.compile(rf"(?<!include )(?<!including ){R_FULLY_EXEMPT}", R_CT, operator=all),
    PositionPattern.compile(R_NOT, r"\bnon-exempt", R_CT),
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6885#note_717172
    PositionPattern.compile(r"waiver\s*from", r"\b(Chapter|Rules?)\s*(20|14A)\b"),
    P_RPT_IS_NS,
    operator=any,
)
# https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6643#note_692122
# http://************:55647/#/project/remark/295460?treeId=12218&fileId=70908&schemaId=18&projectId=17&schemaKey=C7.1 index=739
# http://************:55647/#/project/remark/293863?treeId=7418&fileId=70691&schemaId=18&projectId=17&schemaKey=C7.1 index=373
P_SUBJECT_TO = MatchMulti.compile(
    r"\bbut\s*subject\s*to\s",
    PositionPattern.compile(r"(?<!not )\bsubject(s|ed|ing)?\s*to\s", r"\sbut\b", r"\sexempt"),
    operator=any,
)

P_C7_CHAPTER_CT = MatchMulti.compile(
    R_CT,
    # 要匹配这种： CONNECTED/RELATED PARTY TRANSACTIONS
    r"\bc\s*o\s*n\s*n\s*e\s*c\s*t\s*e\s*d.+transactions?",
    #  http://************:55647/#/project/remark/264969?treeId=4297&fileId=70208&schemaId=18&projectId=17&schemaKey=C7.2
    # r"connected\s*persons",
    rf"{R_CHAPTER_PREFIX}loan\s*agreements?$",
    # https://jura6-lir.paodingai.com/#/hkex/annual-report-checking/report-review/341314?fileId=104190&schemaId=5&rule=C7.1&delist=0
    r"Structured\s*contracts",
    operator=any,
)
R_RPT_CHAPTER_TITLES = r"\brelated.+?party.+?(transaction|disclosure)s?"
P_C7_CHAPTER_RPT = MatchMulti.compile(R_RPT_CHAPTER_TITLES, operator=any)

P_RPT = re.compile(R_RPT, re.I)


class C7BaseModel(BaseModel):
    P_SAVE_AS = MatchMulti.compile(
        R_SAVE_AS,
        # https://jura6-lir.paodingai.com/#/hkex/annual-report-checking/report-review/306958?fileId=71585&schemaId=5&rule=C7.1&delist=0
        r"^Also\s*[,，]",  # 此外
        operator=any,
    )

    P_SAVE_AS_ABOVE = MatchMulti.compile(r"\babove\b", operator=any)

    P_CT_RPT = MatchMulti.compile(
        r"CONNECTED.*?TRANSACTIONS?",
        r"RELATED.*?PARTY.*?TRANSACTIONS?",
        operator=any,
    )

    P_RPT = NeglectPattern.compile(match=r"RELATED.*?PARTY.*?TRANSACTIONS?", unmatch=r"including")

    P_CCT = MatchMulti.compile(
        R_CCT,
        operator=any,
    )

    P_CT = MatchMulti.compile(
        r"C\s*O\s*N\s*N\s*E\s*C\s*T\s*E\s*D.*?TRANSACTIONS?",
        operator=any,
    )

    P_EXEMPT = MatchMulti.compile(
        PositionPattern.compile(r"\sexempt(ed|ing|ion)? from|not subject to", r"\b(reporting|disclosure)\b"),
        r"(chapter|rule) (20|14A)",
        r"(under|pursuant to|\bin\b).*?listing rules?",
        operator=all,
    )

    P_FULLY_EXEMPT = MatchMulti.compile(
        r"fully? exempt(ion|ed)?",
        operator=all,
    )

    P_AS_EXEMPT = PositionPattern.compile(
        r"\bas\b",
        r"exempted connected transaction",
        r"\b(under|pursuant to|in)\b",
        r"(chapter|rule) (20|14A)",
        r"listing rules",
    )

    P_RPT_IS_CT = NeglectPattern.compile(
        match=MatchMulti.compile(
            r"related party transactions?.*?\b(is|are|was|were)\b.*?connected transactions?",
            r"related party transactions?.*?\bconstitute.*?connected transactions?",
            PositionPattern.compile(
                r"related party transactions",
                R_FALL_UNDER,
                r"connected transaction",
            ),
            operator=any,
        ),
        unmatch=R_NOT,
    )

    P_RPT_NOT_CT = MatchMulti.compile(
        rf"(related party transactions?|particulars of transactions with related).*?{R_NOT}.*?connected transactions?",
        rf"{R_NOT}.*?related party transactions?.*?constitute(s|d|ing)?.*?connected transactions?",
        operator=any,
    )

    P_NONE_RPT_FALL_CT = MatchMulti.compile(
        rf"{R_NOT}",
        PositionPattern.compile(
            r"related party transaction",
            R_FALL_UNDER,
            r"connected transaction",
        ),
        operator=all,
    )

    P_INCLUDE_CT = MatchMulti.compile(r"(include|is) connected transaction", operator=any)

    P_IIT = MatchMulti.compile(r"interests? in (transaction|contract)s?", operator=any)

    def train(self, dataset, **kwargs):
        pass

    def find_paragraphs_by_pattern(self, syllabuses: list[dict], **kwargs):
        paras = []
        paragraph_pattern = self.get_config("paragraph_pattern")
        neglect_pattern = self.get_config("neglect_pattern")
        for syllabus in syllabuses:
            elements = self.get_candidate_elements_by_range(
                syllabus["range"], aim_types={PDFInsightClassEnum.PARAGRAPH.value}
            )
            for ele in elements:
                if kwargs.get("element_type") == "para" and self.pdfinsight.syllabus_reader.is_syllabus_elt(ele):
                    continue
                if kwargs.get("element_type") == "syllabus" and not self.pdfinsight.syllabus_reader.is_syllabus_elt(
                    ele
                ):
                    continue
                clean_element_text = clean_txt(ele.get("text", ""), remove_blank=self.remove_blank)
                if neglect_pattern and neglect_pattern.search(clean_element_text):
                    continue
                if paragraph_pattern.search(clean_element_text):
                    paras.append(ele)
        return paras

    def ele_under_syl(self, ele, under_pattern):
        syllabus_titles = [syllabus["title"] for syllabus in self.pdfinsight.find_syllabuses_by_index(ele["index"])]
        if any(under_pattern.search(title) for title in syllabus_titles):
            return True
        return False

    def paragraph_pattern(self, col=None):
        regs = self.get_config("paragraph_pattern", column=col)
        return make_pattern(regs, flags=self.flags)


class C7ND(C7BaseModel):
    P_CLEAR_CT = NeglectPattern.compile(
        match=MatchMulti.compile(
            r"constitute(s|d|ing)?.*?connected transactions?",
            PositionPattern.compile(
                R_FALL_UNDER,
                r"connected transaction",
            ),
            operator=any,
        ),
        unmatch=R_NOT,
    )

    def train(self, dataset, **kwargs):
        pass

    def predict_schema_answer(self, elements):
        """
        从全文找到所有‘connected transaction ’描述
        全文只有RPT不是CT的否定描述，标注ND
        存在RPT是CT的描述，但在PRT标题下无明确CT描述，标注ND
        """
        # c7.1-c7.5的nd条件相同，如果c7.1提取出nd答案了，后续rules就不必再提取了，复用c7.1的
        predictor = self.predictors.get("C7.1")
        if predictor and (answers := self.get_common_predictor_results(predictor.prophet.answer)):
            return self.modify_result_schema(answers)

        if not any(self.P_CT_RPT.search(item["title"]) for item in self.pdfinsight.syllabus_dict.values()):
            return [self.create_result([], schema=self.schema, value=AnswerValueEnum.ND.value)]

        paragraph_pattern = self.get_config("paragraph_pattern")
        under_pattern = self.get_config("under_pattern")
        paras = [
            para
            for para in self.pdfinsight.find_paragraphs_by_pattern([paragraph_pattern], remove_blank=False)
            if self.ele_under_syl(para, self.P_CT_RPT)
        ]
        if paras:
            if sentences := [
                sen
                for para in paras
                for sen in split_paragraph(para["text"])
                if sen
                and paragraph_pattern.search(sen)
                # and not self.P_SAVE_AS.search(sen)
                and not (self.P_EXEMPT.search(paras[0]["text"]) or self.P_FULLY_EXEMPT.search(paras[0]["text"]))
            ]:
                if sentences and all(self.P_RPT_NOT_CT.search(text) for text in sentences):
                    return [self.create_result([], schema=self.schema, value=AnswerValueEnum.ND.value)]
                if any(self.P_RPT_IS_CT.search(text) for text in sentences):
                    under_sentences = [
                        sen
                        for para in paras
                        if self.ele_under_syl(para, under_pattern)
                        for sen in split_paragraph(para["text"])
                    ]
                    if under_sentences and not any(self.P_CLEAR_CT.search(sen) for sen in under_sentences):
                        # RPT标题下无明确CT的描述
                        return [self.create_result([], schema=self.schema, value=AnswerValueEnum.ND.value)]
        return []


class TransactionPurpose(C7BaseModel):
    def train(self, dataset, **kwargs):
        pass

    def predict_schema_answer(self, elements):
        """
        涉及到CCT是contractual arrangement， 如果有专门的reason标题就框标题下的内容
        """
        # 提取CT章节下的reason标题下的第一个段落
        dr_sylls = self.pdfinsight.find_sylls_by_pattern(
            [MatchMulti.compile(r"connected.*?transactions?", operator=any)],
        )
        if not dr_sylls:
            return []
        if syll_paras := self.find_paragraphs_by_pattern(dr_sylls, element_type="syllabus"):
            paras = self.pdfinsight.find_elements_near_by(index=syll_paras[0]["index"], step=1, aim_types=["PARAGRAPH"])
            if paras:
                answer_results = [ParagraphResult(paras[0], paras[0]["chars"])]
                return [self.create_result(answer_results, schema=self.schema, value=self.enum)]
        return []


class CTIncompleteDisclosure(C7BaseModel):
    """
    该文档中存在明确的CT标题，该标题下披露--“相关非豁免的CT/CCT 披露在下方段落和 note40-PRT中”, 需要提取标题下方和 note40 中的相关内容
    判断为ND 的原因是NOTE 40 中属于CCT 的RPT 没有披露规则所需的内容，导致披露不完整
    https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6105#note_652171
    """

    P_CERTAIN_CT = MatchMulti.compile(r"certain non-exempt", C7BaseModel.P_CT, operator=all)
    P_DISCLOSED_CERTAIN_RPT = PatternCollection(
        [
            r"certain related party transactions? as disclosed in note (?P<note_chapter>\d+)",
        ],
        flags=re.I,
    )
    P_BELOW_TRANSACTION = PositionPattern.compile(r"further details?", r"transactions? are set out below")

    P_ON_DATES = MatchMulti.compile(
        *[rf"(on|dated) {date}" for date in R_DATES], *[rf"from {date}.*?to {date}" for date in R_DATES], operator=any
    )
    P_ENTER_INTO = MatchMulti.compile(
        r"enter(ed|ing)? into|\bsign(s|ed|ing)?\b|\brenew(s|ed|ing)?\b|\brevise",
        operator=any,
    )
    P_ENTER_INTO_ON_DATE = MatchMulti.compile(P_ENTER_INTO, P_ON_DATES, operator=all)

    P_AGREEMENT = MatchMulti.compile(r"\bagreements?$", operator=any)

    def predict_schema_answer(self, elements):
        ct_sylls = self.pdfinsight.find_sylls_by_pattern(
            [MatchMulti.compile(self.P_CT, operator=any)],
        )
        certain_ct_flag = False
        disclosed_certain_rpt_flag = False
        below_transaction_flag = False
        note_chapter = None
        for syllabus in ct_sylls:
            paras = self.get_candidate_elements_by_range(
                syllabus["range"], aim_types={PDFInsightClassEnum.PARAGRAPH.value}
            )
            for para in paras:
                if not certain_ct_flag and self.P_CERTAIN_CT.search(para["text"]):
                    certain_ct_flag = True
                if not below_transaction_flag and self.P_BELOW_TRANSACTION.search(para["text"]):
                    below_transaction_flag = True
                if not note_chapter:
                    if matched := list(self.P_DISCLOSED_CERTAIN_RPT.search(para["text"])):
                        note_chapter = matched[0].group("note_chapter")
                if certain_ct_flag and below_transaction_flag and note_chapter:
                    break

        if note_chapter and (
            note_syllabus := self.pdfinsight.find_sylls_by_pattern(
                [MatchMulti.compile(r"^Notes?", operator=any), MatchMulti.compile(rf"^{note_chapter}", operator=any)],
            )
        ):
            paras = [
                para
                for syllabus in note_syllabus
                for para in self.get_candidate_elements_by_range(
                    syllabus["range"], aim_types={PDFInsightClassEnum.PARAGRAPH.value}
                )
            ]
            if not any(self.P_ENTER_INTO.search(para["text"]) for para in paras):
                disclosed_certain_rpt_flag = True

        if certain_ct_flag and disclosed_certain_rpt_flag and below_transaction_flag:
            agreement_sylls = self.pdfinsight.find_sylls_by_pattern(
                [
                    MatchMulti.compile(*R_DR_CHAPTER_TITLES, operator=any),
                    MatchMulti.compile(self.P_AGREEMENT, operator=any),
                ],
            )
            nd_paras = []
            for syllabus in agreement_sylls:
                paras = self.get_candidate_elements_by_range(
                    syllabus["range"], aim_types={PDFInsightClassEnum.PARAGRAPH.value}
                )
                paragraph_pattern = self.paragraph_pattern()
                nd_paras.extend([para for para in paras if paragraph_pattern.search(para["text"])])
            if nd_paras:
                answer_results = [ParagraphResult(para, para["chars"]) for para in nd_paras]
                return [self.create_result(answer_results, schema=self.schema, value=AnswerValueEnum.ND.value)]
        return []


class C7NS(BaseModel):
    """
    C7的准确NS，放在所有模型最前面做判断
    """

    def check_rpt(self, elements, p_rpt_is_not_ct):
        rpt_disclosed_chapter, rpt_ns_result = set(), None
        rpt_is_ct, rpt_is_not_ct = [False] * 2
        for element in elements:
            chars = element["chars"]
            for sentence, (start, end) in split_paragraph(element["text"], need_pos=True):
                sentence = clean_txt(sentence)
                if P_SAVE_AS.search(sentence):
                    continue
                if not rpt_disclosed_chapter:
                    rpt_disclosed_chapter = BaseModel.extract_disclosed_headers(sentence)
                if not rpt_ns_result and P_RPT_IS_NS.search(sentence):
                    rpt_ns_result = CharResult(element, chars[start:end])
                if not rpt_is_not_ct:
                    rpt_is_not_ct = p_rpt_is_not_ct.search(sentence)
                if not rpt_is_ct:
                    rpt_is_ct = P_RPT_IS_CT.search(sentence)
                if rpt_is_ct and not rpt_ns_result and P_EXEMPT.search(sentence):
                    rpt_ns_result = CharResult(element, chars[start:end])
            if rpt_is_not_ct or rpt_ns_result:
                break
        return rpt_is_not_ct, rpt_disclosed_chapter, rpt_ns_result

    def predict_schema_answer(self, elements):
        ct_elements, rpt_elements = [], []
        has_rpt, has_children = False, False
        # Step 1. 找出非NOTE章节下的CT和RPT章节
        for syllabus in self.pdfinsight_syllabus.syllabuses:
            real_syllabus = find_real_syllabus(self.pdfinsight, syllabus)
            if P_NOTE_CHAPTER.search(self.pdfinsight.get_root_syllabus_title(real_syllabus, is_syllabus=True)):
                # 不取NOTE章节下内容
                continue
            if P_C7_CHAPTER_CT.search(real_syllabus["title"]):
                has_children = bool(real_syllabus["children"])
                has_rpt = "related" in real_syllabus["title"].lower()
                if not ct_elements:
                    ct_elements.extend(
                        self.get_candidate_elements_by_range(
                            real_syllabus["range"], aim_types={PDFInsightClassEnum.PARAGRAPH.value}
                        )
                    )
                else:
                    # 多个CT章节，则先走PS
                    return []
            elif not rpt_elements and P_C7_CHAPTER_RPT.search(real_syllabus["title"]):
                rpt_elements.extend(
                    self.get_candidate_elements_by_range(
                        real_syllabus["range"], aim_types={PDFInsightClassEnum.PARAGRAPH.value}
                    )
                )
        # Step 2. 找no CT或CT被豁免的描述
        has_ct, has_rpt = bool(ct_elements), has_rpt or bool(rpt_elements)
        if not has_ct and not has_rpt:
            return []
        no_ct_result, exempt_ct_result, ct_disclosed_chapter = [None] * 3
        count_ct_elements = len(ct_elements)
        ct_elements = ct_elements if count_ct_elements < 3 else [*ct_elements[:2], *ct_elements[-2:]]
        for element in ct_elements:
            for sentence, (start, end) in split_paragraph(element["text"], need_pos=True):
                sentence = clean_txt(sentence)
                if P_SUBJECT_TO.search(sentence):
                    return []
                if P_CT_ALL_EXEMPT.search(sentence):
                    # 有这种全豁免描述，直接NS,不考虑save as
                    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6586#note_697572
                    return [
                        self.create_result(
                            [CharResult(element, element["chars"][start:end])],
                            schema=self.schema,
                            value=AnswerValueEnum.NS.value,
                        )
                    ]
                # 有save as描述，不再提取NS
                if P_SAVE_AS.search(sentence) and not (count_ct_elements == 1 and P_SAVE_AS_IN_REPORT.search(sentence)):
                    if not ct_disclosed_chapter:
                        ct_disclosed_chapter = BaseModel.extract_disclosed_headers(sentence)
                    if not ct_disclosed_chapter:
                        return []
                if P_C7_NO_OTHER_CT.search(sentence) and count_ct_elements > 1:
                    return []
                if P_C7_NO_CT.search(sentence):
                    no_ct_result = CharResult(element, element["chars"][start:end])
                    break
                if P_EXEMPT.search(sentence):
                    exempt_ct_result = CharResult(element, element["chars"][start:end])
                    break
            if no_ct_result:
                break
        # 没有完全否定描述时，有子章节或有序号则先判断PS http://************:55647/#/project/remark/294512?treeId=3847&fileId=70786&schemaId=18&projectId=17&schemaKey=C7.1
        if not no_ct_result and (
            has_children or any(P_C7_FOLLOW_PREFIX.search(e["text"]) for e in ct_elements if is_para_elt(e))
        ):
            return []
        # CT章节下判断RPT必须带关键词RPT
        no_ct_result = no_ct_result or exempt_ct_result
        rpt_is_not_ct, _, rpt_ns_result = self.check_rpt(ct_elements, P_RPT_IS_NOT_CT)
        if has_ct and not no_ct_result:
            return []
        if not has_rpt:
            if ct_disclosed_chapter:
                # 提及的save as 不是RPT章节，则先匹PS
                return []
            return [self.create_result([no_ct_result], schema=self.schema, value=AnswerValueEnum.NS.value)]
        # Step 3. 找RPT不是CT或者RPT被豁免的描述
        rpt_disclosed_chapter = None
        if rpt_elements:
            rpt_is_not_ct, rpt_disclosed_chapter, rpt_ns_result = self.check_rpt(rpt_elements[:2], P_IS_NOT_CT)
        # 未明确说明RPT不是CT，则先判断PS
        if not rpt_is_not_ct and len(rpt_elements) > 2:
            return []
        # CT和RPT中都提及了详情章节，但是在rpt中说详情章节是全部被豁免的
        if ct_disclosed_chapter and ct_disclosed_chapter != rpt_disclosed_chapter:
            return []
        if rpt_is_not_ct:
            if no_ct_result:
                return [self.create_result([no_ct_result], schema=self.schema, value=AnswerValueEnum.NS.value)]
            return []
        if rpt_ns_result:
            if has_ct:
                if no_ct_result.element["index"] == rpt_ns_result.element["index"]:
                    results = [no_ct_result]
                else:
                    results = [no_ct_result, rpt_ns_result]
                return [self.create_result(results, schema=self.schema, value=AnswerValueEnum.NS.value)]
            # http://************:55647/#/project/remark/268160?treeId=8733&fileId=70362&schemaId=29&projectId=17&schemaKey=
            return [self.create_result([rpt_ns_result], schema=self.schema, value=AnswerValueEnum.NS.value)]
        return []
