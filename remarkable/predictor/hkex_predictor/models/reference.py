from copy import copy
from typing import Dict, List, Union

from remarkable.common.common import get_first_key, get_first_value
from remarkable.common.constants import AnswerValueEnum
from remarkable.common.pattern import PatternCollection
from remarkable.predictor.dataset import DatasetItem
from remarkable.predictor.models.base_model import BaseModel
from remarkable.predictor.mold_schema import SchemaItem
from remarkable.predictor.schema_answer import PredictorR<PERSON>ult
from remarkable.predictor.share_group_utils import is_elem_in_group


class Reference(BaseModel):
    def __init__(self, options: Dict, schema: SchemaItem, predictor=None):
        super().__init__(options, schema, predictor=predictor)
        assert self.from_paths

    @property
    def from_paths(self) -> List[str]:
        from_path = self.get_config("from_path")
        if isinstance(from_path, str):
            return [from_path]
        return from_path

    @property
    def from_answer_value(self) -> list | AnswerValueEnum | None:
        """
        指定from_path预测结果的枚举值
        """
        if value := self.get_config("from_answer_value"):
            if isinstance(value, list):
                return value
            return [value]
        return []

    @property
    def model_ids(self) -> List[str] | None:
        """
        只要指定index的model的结果
        """
        return self.get_config("model_ids")

    @property
    def share_type(self):
        """无需配置，由`GroupBased`动态设置"""
        share_type = self.config.get("share_type")
        if share_type and share_type not in ["option", "award"]:
            raise ValueError(f"share_type should be option or award, but got {share_type}")
        return share_type

    @property
    def share_group(self):
        """无需配置，由`GroupBased`动态设置"""
        return self.get_config("share_group")

    @property
    def correct_group_keys(self) -> set[str]:
        """无需配置，由`GroupBased`动态设置"""
        return self.get_config("correct_group_keys")

    @property
    def incorrect_group_keys(self) -> set[str]:
        """无需配置，由`GroupBased`动态设置"""
        return self.get_config("incorrect_group_keys")

    @property
    def only_reference_crude_elements(self):
        return self.get_config("only_reference_crude_elements", False)

    @property
    def enum_pattern(self):
        """
        基于关联规则的结果， 用enum_pattern过滤结果
        """
        return PatternCollection(self.get_config("enum_pattern"), flags=self.flags)

    @property
    def filter_results_function(self) -> Union[callable, None]:
        """
        基于关联规则的结果元素块， 用函数filter_results_function对元素块结果做二次处理
        """
        return self.get_config("filter_results_function")

    def train(self, dataset: List[DatasetItem], **kwargs):
        pass

    def extract_feature(self, elements, answer):
        pass

    def predict_schema_answer(self, elements):
        predictor = self.predictors.get(self.from_paths[0])
        if not predictor:
            return []

        if not predictor.models and len(self.from_paths) > 1:
            # https://100.64.0.105:55647/#/project/remark/296327?treeId=3670&fileId=71016&schemaId=18&projectId=17&schemaKey=C1
            if sub_predictors := [p for p in predictor.sub_predictors if p.schema_name == self.from_paths[1]]:
                predictor = sub_predictors[0]
            else:
                return []

        from_results = []
        # TODO 考虑关联顺序，如果被关联的字段后执行，就不能直接取已有答案
        if not self.model_ids and not self.correct_group_keys and len(predictor.answer_groups) == 1:
            # 未配置model_ids时，直接从answer_groups中取答案
            for predictor_result in self.get_common_predictor_results(get_first_value(predictor.answer_groups)):
                new_result = copy(predictor_result)
                new_result.schema = self.schema
                from_results.append(new_result)
        else:
            models = self.get_models(predictor, self.from_paths)
            if not models:
                return []

            crude_elements = predictor.get_candidate_elements(self.from_paths)
            # 考虑非初步定位的元素
            if not self.only_reference_crude_elements:
                crude_elements += [
                    elem for elem in elements if elem["index"] not in (e["index"] for e in crude_elements)
                ]

            if self.share_type:
                crude_elements = [
                    elem
                    for elem in crude_elements
                    if is_elem_in_group(
                        elem,
                        self.pdfinsight,
                        self.share_type,
                        self.share_group,
                        self.correct_group_keys,
                        self.incorrect_group_keys,
                    )
                ]
            for model in models:
                res_list = model.predict(crude_elements)
                if not res_list:
                    continue
                from_results.extend([copy(res) for res in self.get_common_predictor_results(res_list)])
                # 如果基准规则的答案不在指定的model_ids中，直接返回空
                if self.model_ids and (not model.model_id or model.model_id not in self.model_ids):
                    return []
                if self.predictor.pick_answer_strategy == "single":
                    break
            # 考虑后处理函数
            post_process_func = predictor.config.get("post_process")
            if from_results and callable(post_process_func):
                year_start, year_end = None, None
                if hasattr(predictor.prophet, "metadata") and (metadata := predictor.prophet.metadata):
                    year_start, year_end = metadata.get("year_start"), metadata.get("year_end")
                from_results = post_process_func(
                    from_results, predictor=predictor, year_end=year_end, year_start=year_start
                )
        results = []
        for answer_result in from_results:
            # 配置了from_answer_value时，仅当预测结果的枚举值==from_answer_value时，取预测结果，否则丢弃
            if self.from_answer_value and answer_result.answer_value not in self.from_answer_value:
                continue
            if not self.with_element_box:
                answer_result.element_results = []
                # 不需要框的时候，取一个答案就可以了，多了没用
                results.append(answer_result)
                break
            results.append(answer_result)
        if not results:
            return []
        # 配置了enum_pattern时，使用enum_pattern过滤结果
        if self.enum_pattern and not self.enum_pattern.nexts(self.get_text_from_answer_result(results)):
            return []
        # 配置了filter_results_function时，使用filter_results_function过滤结果
        if callable(self.filter_results_function):
            results = self.filter_results_function(self.pdfinsight, results)
            if not isinstance(results, list) or (results and not isinstance(results[0], PredictorResult)):
                raise ValueError(r"The return type of `filter_results_function` must be `list[PredictorResult]`")
        results = self.modify_result_schema(results)
        return results

    def get_models(self, predictor, from_paths):
        """
        考虑models为GroupBased的场景
        """
        models = super().get_models(predictor, from_paths)
        # 校验model_id配置
        if self.model_ids:
            has_id_models = [model for model in models if model.model_id and model.model_id in self.model_ids]
            ref_model_ids = {model.model_id for model in has_id_models}
            if not set(self.model_ids).issubset(ref_model_ids):
                raise Exception(
                    f"{self.from_paths} lack models whose `model_id` are: {set(self.model_ids) - ref_model_ids}"
                )
            if len(self.model_ids) != len(has_id_models):
                raise Exception(f"{self.from_paths} has duplicate `model_id` in models!")
        return models

    def modify_result_schema(self, results):
        from_values = {r.answer_value for r in results}
        for result in results:
            result.schema = self.schema
            from_value = None
            if len(self.from_answer_value) == 1:
                from_value = self.from_answer_value[0]
            elif len(from_values) == 1:
                from_value = get_first_key(from_values)
            if value := (self.enum or from_value):
                result.answer_value = value
            elif not result.answer_value:
                result.answer_value = self.predictor.predict_value(result)
        return results
