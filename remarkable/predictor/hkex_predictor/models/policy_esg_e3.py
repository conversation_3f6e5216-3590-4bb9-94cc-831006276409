import json
import logging
import re
from operator import itemgetter
from typing import List, Literal

from pdfparser.imgtools.ocraug.faded_h_stroke import defaultdict
from pydantic import BaseModel as PydanticBaseModel

from remarkable.common.common import get_first_value, get_keys
from remarkable.common.constants import AnswerValueEnum, PolicyEsgRules, TableType
from remarkable.common.pattern import MatchMulti, PatternCollection
from remarkable.common.util import clean_txt
from remarkable.pdfinsight.parser import parse_table
from remarkable.pdfinsight.reader import PdfinsightSyllabus
from remarkable.predictor.hkex_predictor.models.policy_esg_base_model import BaseResponseModel, PolicyEsgBaseModel
from remarkable.predictor.schema_answer import OutlineResult, PageResult

logger = logging.getLogger(__name__)
PROMPT = """你是一个专业的ESG报告鉴证评估专家。你的任务是判断一组JSON格式的文本片段是否证明了ESG报告涉及独立验证的水平、范围及所采用过程的概括性描述。

输入数据格式说明：
[
    {
        "page": 整数唯一标识,
        "text": 文本内容字符串
    },
    ...
]

关键验证标准：
- 文本中必须明确提及独立验证或第三方验证
- 验证必须针对ESG报告全部或部分内容
- 需要指出验证机构或验证标准
- 需要指出独立验证的水平、范围及所采用过程
- 如果明确说明验证的细则见报告的某部分也算合规

评估步骤：
1. 检查每个JSON对象的文本片段
2. 分析是否满足独立验证的关键要求
3. 判断枚举值：
   - Comply：明确获得独立第三方验证
   - ND：未明确说明验证情况

严格要求：
- 仅当证据极其明确时才给出结论
- 如无法确定，返回空结果
- 不得主观臆断或编造内容

输出格式：
```json
{
    "page": 保持与输入的数据一致,
    "enum": "Comply/ND",
    "reasoning": "详细推理过程"
}

"""


class ResponseModel(BaseResponseModel):
    page: int | None = None
    reasoning: str | None = None
    enum: Literal["Comply", "No Disclosure", None] = None

    @property
    def key(self):
        return self.page


class ResponseModels(PydanticBaseModel):
    items: List[ResponseModel] = None


class PolicyEsgE3(PolicyEsgBaseModel):
    P_VALID_SYLLABUS = MatchMulti.compile(
        r"assurance (?:report|statement)",
        r"verification (statement|opinion)",
        r"Assurance relating to ESG data",
        r"^(Report )?Verification$",
        r"^Independent Auditor[’'‘s]{0,2} Report$",  # 01913 2024
        operator=any,
    )

    P_BREAK_PARA = MatchMulti.compile(
        r"^ASSURANCE OPINION$",
        # r"^(Basis\s*for\s*)?Conclusions?$",
        r"^(?:\b\w+\b[\s,.']*){0,4}\bConclusions?$",
        r"^ESG review.*Environmental$",
        operator=any,
    )

    P_KPMG_SENTENCE = PatternCollection(
        "^(KPMG$|毕马威)",
        re.I,
    )

    P_KPMG_BREAK_PARA = MatchMulti.compile(
        r"^(?:\b\w+\b[\s,.']*){0,15}\bConclusions?.?$",
        *P_BREAK_PARA.patterns,
        operator=any,
    )

    P_CONTENT_PAGE = PatternCollection(
        [
            r"([^\d])+(?P<page>[\d]+)$",
            r"^(?P<page>[\d]+)([^\d])+$",
        ]
    )

    P_ABOUT_THIS_REPORT = MatchMulti.compile(r"^About\s*this\s*report$", operator=any)

    P_VALID_REPORT_CHILD_SYLLABUS = MatchMulti.compile(
        r"^Assurance$",
        r"^(Report )?Verification$",
        operator=any,
    )

    P_SHORT_SYLLABUS = MatchMulti.compile(
        r"^(?:\b\w+\b[\s,.']*){1,5}$",
        operator=any,
    )

    P_SYLLABUS_EXTRA_CHARS = PatternCollection(r"['’‘]s|s['’‘]")

    def train(self, dataset, **kwargs):
        pass

    def predict_schema_answer(self, elements):
        results = []
        elements = self.find_elements_from_content() or []
        if not elements:
            predictor = self.predictors.get(PolicyEsgRules.E2)
            if len(predictor.answer_groups) == 1:
                element_pages = []
                for predictor_result in self.get_common_predictor_results(get_first_value(predictor.answer_groups)):
                    element_pages.extend(res.element["page"] for res in predictor_result.element_results)
                for page in set(element_pages):
                    elements.extend(self.pdfinsight.find_elements_by_page(page))
        if not elements:
            return []
        group_elements = self.filter_valid_syllabus_elements(elements)
        data_prompt = self.prepare_prompt(group_elements)
        embedding_element_map = {i["page"]: i for i in data_prompt}
        messages = [
            {
                "role": "system",
                "content": PROMPT,
            },
            {"role": "user", "content": json.dumps(data_prompt, ensure_ascii=False)},
        ]
        response_model: ResponseModels = self.predict_results_by_llm(
            messages, ResponseModels, elements_mapping=embedding_element_map
        )
        if not response_model:
            return []
        logger.info(f"llm_res: {response_model}")
        try:
            results = self.process_answers(response_model, group_elements)
        except Exception as e:
            logger.exception(e)
        if results:
            return results
        for item in sorted(response_model.items, key=lambda x: x.page):
            if item.enum == "No Disclosure" or not (page_elements := group_elements.get(item.page)):
                continue
            page_box = PdfinsightSyllabus.elements_outline(page_elements)
            results.append(
                self.create_result(
                    [OutlineResult(page_box=page_box, element=page_elements[0], origin_elements=page_elements)],
                    column=self.schema.name,
                    value=AnswerValueEnum.COMPLY.value,
                )
            )
        return results

    def process_answers(self, response_model: ResponseModels, group_elements: dict):
        # https://jura6-esg.paodingai.com/#/hkex/esg-report-checking/report-review/379587?fileId=112990&schemaId=2&rule=E4-Independent%20Assurance%20on%20scope%201%20and%20scope%202%20GHG%20emissions&delist=0
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6975
        # 十分特殊的披露形式
        if self.predictor.prophet.metadata["stock_code"] != "09987":
            return []
        current_page = None
        elements = []
        for item in response_model.items:
            if "Independent  Practitioner's  Limited  Assurance  Report" in item.text:
                current_page = item.page
                break

        if current_page:
            for item in response_model.items:
                elements.extend(group_elements.get(item.page))
            current_page_elements = self.pdfinsight.find_elements_by_page(current_page)
            left = min([ele["outline"][0] for ele in current_page_elements])
            top = min([ele["outline"][1] for ele in current_page_elements])
            right = max([ele["outline"][2] for ele in current_page_elements])
            bottom = max([ele["outline"][3] for ele in current_page_elements])
            answer_results = self.create_result(
                [PageResult(page=current_page, outline=[left, top, right, bottom], elements=elements)],
                column=self.schema.name,
                value=AnswerValueEnum.COMPLY.value,
            )
            return [answer_results]
        return []

    def find_elements_from_content(self):
        elements = []
        for page, page_eles in self.pdfinsight.page_element_dict.items():
            if not self.pdfinsight.is_catalog_page(page):
                continue
            contexts = []
            for item in page_eles:
                ele_type, element = self.pdfinsight.find_element_by_index(item.data["index"])
                if ele_type == "TABLE":
                    table = parse_table(element, tabletype=TableType.ROW, pdfinsight_reader=self.pdfinsight)
                    for row in table.rows:
                        contexts.append(" ".join(clean_txt(i.text) for i in row))
                elif ele_type == "PARAGRAPH":
                    contexts.append(clean_txt(element["text"]))
            for text in contexts:
                if self.P_VALID_SYLLABUS.search(text) and (res := self.P_CONTENT_PAGE.nexts(text)):
                    page = int(res.group("page")) or -1
                    elements.extend(self.pdfinsight.find_elements_by_page(self.pdfinsight.content_pages.get(page)))
                    break
            break
        valid_pages = []
        for syll in self.pdfinsight.find_sylls_by_pattern([self.P_VALID_SYLLABUS]):
            _, syll_ele = self.pdfinsight.find_element_by_index(syll["element"])
            for index in range(*syll["range"]):
                _, ele = self.pdfinsight.find_element_by_index(index)
                valid_pages.append(ele["page"])

            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7370#note_738728
            # syllabus章节范围提取不准确，取该页之后的每页的前五个元素块，存在相同的段落，则为同一章节，否则不包含
            syll_ele_text = self.P_SYLLABUS_EXTRA_CHARS.sub("", clean_txt(syll_ele.get("text") or "").lower())
            for page in range(syll_ele["page"] + 1, syll_ele["page"] + 10):
                if not (page_eles := self.pdfinsight.page_element_dict.get(page)):
                    continue
                for ele in page_eles[:5]:
                    _, fix_ele = self.pdfinsight.fixed_element_dict.get(ele.data.get("index"))
                    if (
                        fix_ele
                        and self.P_SYLLABUS_EXTRA_CHARS.sub("", clean_txt(fix_ele.get("text") or "").lower())
                        == syll_ele_text
                    ):
                        valid_pages.append(fix_ele["page"])
                        break
        if not valid_pages:
            return []
        for page in range(min(valid_pages), max(valid_pages) + 1):
            elements.extend(self.pdfinsight.find_elements_by_page(page))

        return sorted(self.unique_list(elements), key=itemgetter("index"))

    @staticmethod
    def unique_list(list_obj):
        elements_dict = {}
        elements_idxes = []
        for ele in sorted(list_obj, key=itemgetter("index")):
            if ele["index"] in elements_idxes:
                continue
            elements_dict[ele["index"]] = ele
            elements_idxes.extend(get_keys(ele, ["page_merged_paragraph", "paragraph_indices"], default=[ele["index"]]))
        return list(elements_dict.values())

    @classmethod
    def filter_valid_syllabus_elements(cls, elements):
        ret = defaultdict(list)
        elements.sort(key=itemgetter("index"))

        is_kpmg_report = any(cls.P_KPMG_SENTENCE.nexts(clean_txt(ele.get("text") or "")) for ele in elements)

        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7466#note_743407
        ele_pages = {ele["page"] for ele in elements}
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7370#note_732095
        for idx, ele in enumerate(reversed(elements), start=1):
            if ((is_kpmg_report and cls.P_KPMG_BREAK_PARA) or cls.P_BREAK_PARA).search(
                ele.get("text") or ele.get("title") or ""
            ):
                if ele["page"] == max(ele_pages):
                    elements = elements[:-idx]
                break

        for ele in elements:
            ret[ele["page"]].append(ele)

        # code 01288 year 2024
        if all(len(eles) < 5 for eles in ret.values()):
            for ele in elements:
                ret[ele["page"]].append(ele)

        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7370#note_736522
        # about this report类型的答案，如果存在指定的syllabus，则只返回该syllabus所对应的元素块
        for page, eles in ret.items():
            idx1 = cls.find_elements_by_pattern(eles[:5], cls.P_ABOUT_THIS_REPORT)
            if idx1 is None:
                break
            idx2 = cls.find_elements_by_pattern(eles[idx1 + 1 :], cls.P_VALID_REPORT_CHILD_SYLLABUS)
            if idx2 is None:
                break
            # idx2实际位置需要基于idx1向右偏移
            idx2 += idx1 + 1
            idx3 = cls.find_elements_by_pattern(eles[idx2 + 1 :], cls.P_SHORT_SYLLABUS)
            if idx3 is None or not eles[idx2 : idx2 + idx3 + 1]:
                break
            return {page: eles[idx2 : idx2 + idx3 + 1]}

        return ret

    @staticmethod
    def find_elements_by_pattern(elements, p_pattern: MatchMulti) -> int | None:
        for idx, ele in enumerate(elements):
            if p_pattern.search(clean_txt(ele.get("text") or "")):
                return idx
        return None

    def prepare_prompt(self, group_elements):
        ret = []
        for page, elements in group_elements.items():
            contexts = []
            for element in elements:
                ele_type, element = self.pdfinsight.find_element_by_index(element["index"])
                if ele_type == "TABLE":
                    table = parse_table(element, tabletype=TableType.ROW, pdfinsight_reader=self.pdfinsight)
                    for row in table.rows:
                        contexts.append("\n".join(i.text for i in row))
                elif ele_type == "PARAGRAPH":
                    contexts.append(element["text"])
            ret.append(
                {
                    "page": page,
                    "text": "\n".join(contexts),
                }
            )
        return ret
