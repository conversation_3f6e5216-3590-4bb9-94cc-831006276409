import re
from itertools import groupby

from remarkable.common.constants import PDFInsightClass<PERSON>num, TableType
from remarkable.common.pattern import <PERSON><PERSON><PERSON><PERSON>, PatternCollection
from remarkable.pdfinsight.parser import parse_table
from remarkable.predictor.models.syllabus_elt_v2 import SyllabusEltV2
from remarkable.predictor.models.table_kv import KeyValueTable

R_CONTENTS = MatchMulti.compile("^CONTENTS$", operator=any)
P_M25_SYLLABUS = PatternCollection(
    [
        r"APPENDIX.*(PARTICULAR|DETAIL|RESOLUTION|PROPOSED).*AMENDMENTS?\s*TO",
    ],
    flags=re.I,
)


class AGMM25(SyllabusEltV2):
    """ "
    处理 M25- syllabus 识别不正确的问题
    将每页前几个元素合并，且于后与
    """

    def predict_schema_answer(self, elements):
        answer_results = []
        pages = []
        for page, title in self.pdfinsight.page_chapter_from_first_para.items():
            if P_M25_SYLLABUS.nexts(title):
                pages.append(page)
        for _, value in groupby(enumerate(pages), lambda x: x[1] - x[0]):
            values = list(value)
            first, last = values[0][1], values[-1][1]
            merged_range = [
                self.pdfinsight.page_dict[first].element_index_range[0],
                self.pdfinsight.page_dict[last].element_index_range[1],
            ]
            aim_syl = {"range": merged_range, "children": []}
            if answer_result := self.create_syl_outline_result(aim_syl, "Full terms of the proposed amendments"):
                answer_results.append(answer_result)
        return answer_results

    def find_syllabus_in_contents(self):
        for syllabus in self.pdfinsight.syllabus_reader.syllabuses:
            if not R_CONTENTS.search(syllabus["title"]):
                continue
            elements = self.get_candidate_elements_by_range(
                syllabus["range"], aim_types=PDFInsightClassEnum.TABLE.value
            )
            for element in elements:
                table = parse_table(element, tabletype=TableType.KV.value, pdfinsight_reader=self.pdfinsight)
                kv_pairs = KeyValueTable.parse_kv_pairs(table)
                for pair in kv_pairs:
                    key_cell = pair[0]
                    if P_M25_SYLLABUS.nexts(key_cell.clean_text):
                        return pair
                if element["page"] > 10:
                    return None
        return None
