import json
import logging
import re
from collections import defaultdict
from itertools import chain
from operator import itemgetter

from remarkable.common.constants import AnswerValueEnum, TableType
from remarkable.common.pattern import PatternCollection
from remarkable.common.util import clean_txt, split_element
from remarkable.pdfinsight.parser import parse_table
from remarkable.pdfinsight.reader import PdfinsightSyllabus
from remarkable.pdfinsight.reader_table import PdfinsightTable
from remarkable.predictor.eltype import ElementClassifier
from remarkable.predictor.hkex_predictor.models.policy_esg_base_model import PolicyEsgBaseModel, ResponseModels
from remarkable.predictor.schema_answer import CharResult, OutlineResult, ParagraphResult, TableCellsResult

logger = logging.getLogger(__name__)


PROMPT = """
您的任务是分析ESG（环境、社会和治理）报告，以确定编制或撰写报告时是否引用或采用ISSB（International Sustainability Standards Board）或IFRS (International Financial Reporting Sustainability) 标准。

在分析内容时，寻找以下关键点：
1.  References to ISSB/International Sustainability Standards Board standards
2.  Reference to the IFRS S1
3.  Reference to the IFRS S2
4.  Reference to the IFRS 1 and 2
5.  Mentions of the report being written or prepared in accordance with ISSB standards
6.  Statements about improving performance based on ISSB/IFRS S2 standards

需要忽略的关键短语：
1. SASB

分析过程：
1. 仔细阅读所提供的ESG报告内容
2. 识别任何提及 ISSB/IFRS 或相关术语
3. 报告内容明确声明引用、参考或采用 ISSB/IFRS
4. 寻找部分采用的证据（例如，仅针对与气候相关的披露）

关键验证标准，满足以下任意一点即可：
1. 必须明确说明报告或ESG的内容引用、采用、采纳、参考或依据ISSB或IFRS来编制或提高performance
2. 报告参考ISSB而编写
3. 报告参考国际财务报告可持续披露准则第2号或气候相关披露（IFRS S2）而撰写

评估步骤：
1. 检查每个JSON对象的文本片段
2. 分析是否满足关键验证标准
3. 判断枚举值：
   - Comply：明确披露了报告或ESG报告的编制或者披露参考了 ISSB/IFRS，或者以 ISSB/IFRS 为依据
   - ND：未明确说明

严格要求：
- 必须严格按照分析过程进行分析
- 仅当证据极其明确时才给出结论
- 如无法确定，返回空结果
- 不得主观臆断或编造内容
- 不满足要求的内容不应返回

以下内容与基于ISSB编制的报告内容相似。但是未按ISSB编制或公开的内容，不能提取；
- With reference to the IFRS Sustainability Disclosure Standards, climate-related risks are divided into physical and transition risks. 原因：风险分类是根据ISSB进行的，而不是ESG报告是根据ISSB编制或披露的。
- create a high-quality global baseline for sustainability-related disclosures. 原因：创建一个高质量的全球基线，而不是ESG报告是根据ISSB编制或披露的。
- develop globally accepted sustainability disclosure standards. 原因：制定全球公认的可持续发展披露标准，而不是ESG报告是根据ISSB编制或披露的。
- We regularly assess our compliance with the ISSB IFRS S2 Climate-related Disclosures (“IFRS S2”). 原因：它通过定期核查来描述是否遵循了IFRS S2中的内容，而不是说明报告的编制依据是IFRS S2。
- IFRS S1 General Requirements for the Disclosure of Sustainability-related Financial Information Content Index. 原因：单纯的标准内容索引，无采纳声明。
- which closely mirrors the International Sustainability Standards Board (the “ISSB”) Standards. 原因：他们参考的是港交所更新后的reporting code，是与ISSB相似，但是没有参考ISSB。
- The policy was formulated with reference to the applicable regulatory requirements, (the “ISSB”) Standards. 原因：The policy参考了ISSB,而不是编写这个报告参考了ISSB的规则。
- Method. In addition to AA1000AS v3, the standards, principles, and initiatives referenced by SynTao GF included: ISSB. 原因：SynTao GF是第三方验证机构，这个第三方验证机构采用的验证标准参考了IFRS，而不是披露的这个ESG报告的编写依据。
- These metrics and targets are developed in accordance with standards including the HKEX's Environmental, Social and Governance Reporting Code and IFRS S2/TCFD. 原因: metrics and targets 是根据 IFRS 制定的，而不是ESG报告是根据IFRS编制或披露的。
- With reference to IFRS S2 — Climate-related Disclosures issued by the International Sustainability Standards Board (ISSB), the Company identifies various risks of climate change and formulates targeted responses, thereby minimizing the negative impacts of climate change on its operations. 原因：公司根据 IFRS S2 — Climate-related Disclosures 识别了风险和制定了措施，而不是本报告是根据IFRS编制或披露的。
- With reference to the “International Financial Reporting Standards S2 — Climate-related Disclosures” (《國際財務報告可持續披露準則第2號—氣候相關披露》) issued by the International Sustainability Standards Board (“ISSB”), we identify and evaluate climate change risk while facilitating decarbonization across the entire value chain. 原因：描述了根据 International Financial Reporting Standards S2 — Climate-related Disclosures 评估气候变化风险 而不是本报告是根据IFRS编制或披露的。

输出格式：
```json
{
    "index": 保持与输入的数据一致,
    "element_sequence": "保持与输入的数据一致",
    "reasoning": "详细推理过程，使用中文描述，并返回满足要求的原文内容"
    "enum": "枚举值，Comply/ND",
}
"""

E1_HELPER_MATERIALS = [
    {
        "question": [
            {
                "index": 1618,
                "element_sequence": 2,
                "text": "She became a director of the Board of the Value Reporting Foundation (“VRF”) formed by the merger of the IIRC with the Sustainability Accounting Standards Board, serving in this position until the VRF consolidated into the IFRS Foundation to support the formation of the International Sustainability Standards Board.",
            }
        ],
        "answer": [
            {
                "items": [],
                "enum": "No Disclosure",
            }
        ],
    },
    {
        "question": [
            {
                "index": 593,
                "element_sequence": 2,
                "text": "This enhancement will navigate the forthcoming HKEX ESG Reporting Code requirements, which is developed based on the Standards issued by the International Sustainability Standards Board.",
            }
        ],
        "answer": [
            {
                "items": [],
                "enum": "No Disclosure",
            }
        ],
    },
    {
        "question": [
            {
                "index": 1692,
                "element_sequence": 1,
                "text": " Furthermore, HKEx published an amendment to climate-related information disclosure requirements under the ESG framework of the Listing Rules in April 2024 to require all issuers to prepare their ESG reports in compliance with IFRS S1 General Requirements for Disclosure of Sustainability-related Financial Information (IFRS S1) and IFRS S2 Climate-related Disclosures (IFRS S2) published by the International Sustainability Standards Board (ISSB) of the IFRS Foundation.",
            }
        ],
        "answer": [
            {
                "items": [],
                "enum": "No Disclosure",
            }
        ],
    },
    {
        "question": [
            {
                "index": 363,
                "element_sequence": 2,
                "text": "During the Reporting Period, the Stock Exchange enhanced its climate-related disclosure requirements to align with IFRS S1 General Requirements for Disclosure of Sustainability-related Financial Information (“IFRS S1”) and the IFRS S2 Climate-related Disclosures (“IFRS S2”) published by the IFRS Foundation’s International Sustainability Standards Board (“ISSB”), incorporating these regulations as Part D of the ESG Reporting Code (“ESG Code”) (effective from 2025).",
            }
        ],
        "answer": [
            {
                "items": [],
                "enum": "No Disclosure",
            }
        ],
    },
    {
        "question": [
            {
                "index": 246,
                "element_sequence": 1,
                "text": "The report mentions it is preparing for climate-related disclosures in accordance with HKEX's ESG Reporting Code, closely mirroring the ISSB Standards, which qualifies it for compliance.",
            }
        ],
        "answer": [
            {
                "items": [],
                "enum": "No Disclosure",
            }
        ],
    },
    {
        "question": [
            {
                "index": 397,
                "element_sequence": 1,
                "text": "The policy was formulated with reference to the applicable regulatory requirements, the Basel Committee on Banking Supervision (BCBS)'s Principles for the Efective Management and Supervision of Climate-Related Financial Risks, the International Sustainability Standards Board (ISSB)'s sustainability-related disclosure standards, the International Finance Corporation (IFC)'s Performance Standards on Environmental and Social Sustainability, the Equator Principles, and the advanced practices of international peers.",
            }
        ],
        "answer": [
            {
                "items": [],
                "enum": "No Disclosure",
            }
        ],
    },
    {
        "question": [
            {
                "index": 4267,
                "element_sequence": 2,
                "text": "We have incorporated a content index based on the International Sustainability Standards Board’s (“ISSB”) IFRS S2 on Climate-related Disclosures published in June 2023, which is adopted by the HKEX ESG Code.",
            }
        ],
        "answer": [
            {
                "items": [],
                "enum": "No Disclosure",
            }
        ],
    },
]


P_ISSB = PatternCollection(
    [
        r"\b(ISSB|International Sustainability Standards Board)\b",
        r"\bIFRS S[12]\b",
    ],
    re.I,
)
P_ISSB_COLON = PatternCollection([r"[\b\d](?P<dst>ISSB[：:].*)"], re.I)

P_PRECISE_DESC = PatternCollection(
    [
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6341#note_720906
        r"preparing\s*this\s*report[^.]*?(reference\s*to|referenced).*(ISSB|IFRS|International\s*Sustainability\s*Standards\s*Board)",
    ],
    flags=re.I,
)

P_INVALID_DESC = PatternCollection(
    [
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6341#note_709366
        r"(reference|referring)\s*to\s*the[^.]*?recommendations[^.,]*?(ISSB|IFRS|International\s*Sustainability\s*Standards\s*Board)",
    ],
    flags=re.I,
)


class PolicyEsgE1(PolicyEsgBaseModel):
    def train(self, dataset, **kwargs):
        pass

    def predict_schema_answer(self, elements):
        all_embedding_elements: dict = self.predictor.prophet.metadata[self.schema.name]
        for key, embedding_elements in all_embedding_elements.items():
            logger.info(f"process_elements: {key}: {len(embedding_elements)}")
            results = self.process_elements(embedding_elements)
            if results:
                return results
        return []

    def process_elements(self, embedding_elements):
        results = []
        embedding_elements = [item for item in embedding_elements if not P_INVALID_DESC.nexts(item["text"])]
        embedding_elements.sort(key=itemgetter("index"))
        data_prompt = prepare_data_prompt(embedding_elements, self.pdfinsight)
        if not data_prompt:
            return []
        embedding_element_map = {f"{i['index']}_{i['element_sequence']}": i for i in data_prompt}
        helper_prompt = gen_helper_prompt(E1_HELPER_MATERIALS)
        messages = [{"role": "system", "content": PROMPT}]
        if helper_prompt:
            messages.extend(helper_prompt)
        messages.append({"role": "user", "content": json.dumps(data_prompt, ensure_ascii=False)})
        response_model: ResponseModels = self.predict_results_by_llm(
            messages, ResponseModels, elements_mapping=embedding_element_map
        )
        if not response_model:
            return []
        logger.info(f"llm res: {response_model.enum}")
        if not response_model.items:
            return []
        for item in response_model.items:
            if item.enum != "Comply":
                continue
            embedding_element = embedding_element_map.get(f"{item.index}_{item.element_sequence}")
            if not embedding_element:
                continue

            _, element = self.pdfinsight.find_element_by_index(embedding_element["index"])
            if not element:
                continue
            if ElementClassifier.is_table(element):
                table = parse_table(element, tabletype=TableType.TUPLE, pdfinsight_reader=self.pdfinsight)
                table_rows_map = dict(enumerate(table.rows, start=1))
                table_rows_map[0] = list(chain(*table.rows))
                cells = [
                    cell
                    for cell in table_rows_map.get(embedding_element["element_sequence"]) or []
                    if P_ISSB.nexts(clean_txt(cell.text))
                ]
                # fid: 71375
                if not cells or (len(table_rows_map) > 1 and len(cells) == 1 and len(cells[0].clean_text.split()) < 5):
                    continue
                # origin_rows = [cell["text"] for cell in element["origin_cells"].values()]
                # if not all(cell.text in origin_rows for cell in cells):
                #     continue
                answer = self.create_result(
                    [TableCellsResult(element, cells)],
                    schema=self.schema,
                    value=AnswerValueEnum.COMPLY.value,
                )
            elif ElementClassifier.is_chart(element):
                page_box = PdfinsightSyllabus.elements_outline(
                    [element],
                    pdfinsight=self.pdfinsight,
                    ignore_column_position=True,
                )
                answer = self.create_result(
                    [OutlineResult(page_box=page_box, element=element, origin_elements=[element], text=item.text)],
                    schema=self.schema,
                    value=AnswerValueEnum.COMPLY.value,
                )
            else:
                sub_embedding_elements = split_element(element)
                matcher = P_ISSB_COLON.nexts(clean_txt(element["text"]))
                if matcher:
                    dst_chars = self.get_dst_chars_from_matcher(matcher, element)
                else:
                    dst_chars = sub_embedding_elements[embedding_element["text"]]["chars"]
                if not dst_chars:
                    continue
                if len(dst_chars) == len(element["text"]):
                    answer = self.create_result(
                        [ParagraphResult(element, dst_chars)],
                        schema=self.schema,
                        value=AnswerValueEnum.COMPLY.value,
                    )
                else:
                    answer = self.create_result(
                        [CharResult(element, dst_chars)],
                        schema=self.schema,
                        value=AnswerValueEnum.COMPLY.value,
                    )
            results.append(answer)

        return [res for res in results if P_PRECISE_DESC.nexts(clean_txt(res.text))] or results


def prepare_data_prompt(elements, pdfinsight):
    ret = []
    table_elements = get_table_elements(elements, pdfinsight)
    ret.extend(table_elements)
    para_like_elements = get_para_like_elements(elements, pdfinsight)
    ret.extend(para_like_elements)
    unique_data = {}
    unique_data_by_element = defaultdict(set)
    for item in ret:
        key = (item["index"], item["element_sequence"])
        if key not in unique_data and item["text"] not in unique_data_by_element[item["index"]]:
            unique_data[key] = item
            unique_data_by_element[item["index"]].add(item["text"])

    ret = list(unique_data.values())
    for item in ret:
        logger.debug(
            f"page: {item['page']}, index: {item['index']}, position: {item['element_sequence']}, {item['text']=}"
        )
        item.pop("page", None)
    return ret


def gen_helper_prompt(helper_materials) -> list[dict]:
    result = []
    for item in helper_materials:
        result.append({"role": "user", "content": json.dumps(item["question"], ensure_ascii=False)})
        result.append({"role": "assistant", "content": json.dumps(item["answer"], ensure_ascii=False)})
    return result


def get_para_like_elements(embedding_elements, pdfinsight):
    ret = []
    for embedding_element in embedding_elements:
        element_index = embedding_element.get("element_index", embedding_element.get("index"))
        element_type, element = pdfinsight.find_element_by_index(element_index)
        if not element:
            continue
        if not ElementClassifier.like_paragraph(element):
            continue
        if "text" not in embedding_element:
            continue
        ret.append(
            {
                "index": element_index,
                "text": embedding_element["text"],
                "element_sequence": embedding_element["element_sequence"],
                "page": element["page"],
            }
        )

    return ret


def get_table_elements(embedding_elements, pdfinsight):
    ret = []
    for embedding_element in embedding_elements:
        element_index = embedding_element.get("element_index", embedding_element.get("index"))
        element_type, element = pdfinsight.find_element_by_index(element_index)
        if not element:
            continue
        if not ElementClassifier.is_table(element):
            continue
        element_sequence = embedding_element.get("element_sequence", 0)
        if element_sequence != 0:
            table_text = embedding_element["text"]
        elif embedding_element.get("keep_text"):
            table_text = embedding_element["text"]
        else:
            table_text = PdfinsightTable.to_markdown(element, need_merged_table=True)

        ret.append(
            {
                "index": element_index,
                "text": table_text,
                "element_sequence": element_sequence,
                "page": element["page"],
            }
        )
    return ret
