import re
from typing import Dict

from remarkable.common.constants import TableType
from remarkable.pdfinsight.parser import parse_table
from remarkable.predictor.models.base_model import TableModel
from remarkable.predictor.schema_answer import TableCellsResult

P_SHARE_CLASS = re.compile(r"(?<![A-Z]\s)class of share", re.I)
P_SHARE_TYPE = re.compile(r"(type|(?<![A-Z]\s)class) of share", re.I)
P_OTHER_TYPE = re.compile(r"other type|not applicable", re.I)
P_TYPE_DESCRIPTION = re.compile(r"description", re.I)
P_BALANCE = re.compile(r"Balance at close of the month", re.I)

P_PREFERENCE_SHARES = re.compile(r"\b(preference|preferred) shares", re.I)


class MRSharesIssued(TableModel):
    TOTAL_ISSUED_SHARES = "Total number of issued shares"
    P_ISSUED_SHARES = re.compile(r"T?otal number of issued shares", re.I)
    P_TREASURY_SHARES = re.compile(r"Number of treasury shares", re.I)

    @property
    def after_2024_06(self):
        if ended_date := self.predictor.prophet.metadata.get("ended_date"):
            return ended_date >= "2024-06-30"
        return False

    @property
    def predictor_name(self):
        if paths := self.predictor.config.get("path", []):
            return paths[0]

    def is_target_element(self, element: Dict) -> bool:
        if element["class"] != "TABLE":
            return False
        cells_map = element["cells"]
        for cell_idx, cell in element["cells"].items():
            cell_row = int(cell_idx.split("_")[0])
            if P_SHARE_CLASS.search(cell["text"]):
                if (right_cell := cells_map.get(f"{cell_row}_{cell['right']}")) and (
                    P_PREFERENCE_SHARES.search(right_cell["text"])
                ):
                    return False
                break
        return True

    @staticmethod
    def group_table_cells(table):
        cells_map = table.cell_map
        share_class_rows = set()
        for cell in cells_map.values():
            if P_SHARE_CLASS.search(cell.text):
                share_class_rows.add(cell.rowidx)
        if len(share_class_rows) <= 1:
            return [cells_map]
        share_class_rows = sorted(share_class_rows)
        share_class_rows.append(table.height)
        grouped_cells = []
        for cell_range in zip(share_class_rows[:-1], share_class_rows[1:]):
            grouped_cells.append(
                {cell.indexstr: cell for cell in cells_map.values() if cell_range[0] <= cell.rowidx < cell_range[1]}
            )
        return grouped_cells

    def predict_schema_answer(self, elements):
        ret = []
        for element in elements:
            if not self.is_target_element(element):
                continue
            table = parse_table(element, tabletype=TableType.TUPLE, pdfinsight_reader=self.pdfinsight)
            # 多张表识别为一张表，需要根据每张表开始的行号将表的单元格分开，放到多个组里
            cells_group = self.group_table_cells(table)
            for cells in cells_group:
                shares_class = self.get_class(element, cells)
                shares_value = self.get_value(element, cells)
                if shares_class and shares_value:
                    answer_group = {
                        "Relevant share class": shares_class,
                        "Value": shares_value,
                    }
                    ret.append(answer_group)
        return ret

    def get_value(self, tbl_element, cells):
        target_rowidx = None
        target_colidx = None
        target_cell = None
        P_SHARES = self.P_ISSUED_SHARES if self.predictor_name == self.TOTAL_ISSUED_SHARES else self.P_TREASURY_SHARES
        for cell in cells.values():
            if P_BALANCE.search(cell.text):
                target_rowidx = cell.rowidx
            if P_SHARES.search(cell.text):
                target_colidx = cell.colidx
            if target_rowidx and target_colidx:
                break
        if target_rowidx and target_colidx:
            target_cell = cells.get(f"{target_rowidx}_{target_colidx}")
        elif self.predictor_name == self.TOTAL_ISSUED_SHARES:
            for cell in cells.values():
                if not cell.dummy and P_BALANCE.search(cell.text):
                    if right_cell := cells.get(f"{cell.rowidx}_{cell.colidx + cell.width}"):
                        target_cell = right_cell
                        break
        if target_cell:
            return [
                self.create_result(
                    [TableCellsResult(element=tbl_element, cells=[target_cell], display_text=target_cell.text)],
                    column="Value",
                )
            ]
        return []

    def get_class(self, tbl_element, cells):
        target_cell = None
        for cell in cells.values():
            if not cell.dummy and P_SHARE_TYPE.search(cell.text) and cell.colidx != 0:
                cell_right, cell_bottom = cell.colidx + cell.width, cell.rowidx + cell.height
                right_cell = cells.get(f"{cell.rowidx}_{cell_right}")
                bottom_cell = cells.get(f"{cell_bottom}_{cell.colidx}")
                if right_cell and not P_OTHER_TYPE.search(right_cell.text):
                    target_cell = right_cell
                    break
                elif (
                    right_cell
                    and P_OTHER_TYPE.search(right_cell.text)
                    and bottom_cell
                    and P_TYPE_DESCRIPTION.search(bottom_cell.text)
                ):
                    if (bottom_right_cell := cells.get(f"{cell_bottom}_{cell_right}")) and bottom_right_cell.text:
                        target_cell = bottom_right_cell
                    else:
                        target_cell = right_cell
                    break
        if target_cell:
            return [
                self.create_result(
                    [TableCellsResult(element=tbl_element, cells=[target_cell], display_text=target_cell.text)],
                    column="Relevant share class",
                )
            ]
        return []
