import logging
import re
from collections import Counter, defaultdict
from functools import cached_property
from itertools import chain
from operator import itemgetter
from typing import Dict, List

from remarkable.common.common import is_para_elt, is_table_elt
from remarkable.common.common_pattern import (
    P_NOTES,
    R_ASSETS,
    R_CHAPTER_PREFIX,
    R_CN_SPACE,
    R_CN_SPACE_COLON,
    R_CURRENT_ASSETS,
    R_MIDDLE_DASH,
    R_MIDDLE_DASHES,
    R_NON_CURRENT_ASSETS,
    R_TOTAL_ASSETS,
)
from remarkable.common.constants import DocType, PDFInsightClassEnum, TableType
from remarkable.common.pattern import Match<PERSON>ult<PERSON>, NeglectPattern, PatternCollection
from remarkable.common.protocol import SearchPatternLike
from remarkable.common.util import P_NON_EN, clean_txt
from remarkable.pdfinsight.parser import (
    P_SUBTOTAL_ROW_HEADER,
    R_CELL_CURRENCY,
    <PERSON>rse<PERSON><PERSON><PERSON>,
    Parsed<PERSON><PERSON><PERSON><PERSON>,
    ParsedT<PERSON><PERSON>egion,
    TableSubTitle,
    cell_data_patterns,
    parse_table,
)
from remarkable.pdfinsight.reader_util import find_table_title
from remarkable.predictor.common_pattern import R_EN_MONTH, R_PERCENT
from remarkable.predictor.dataset import DatasetItem
from remarkable.predictor.hkex_predictor.models.financial_result import COLUMNS_PATTERNS
from remarkable.predictor.hkex_predictor.schemas.ratio_check_schema import TOTAL_ASSETS_PATTERN
from remarkable.predictor.models.table_tuple import TupleTable
from remarkable.predictor.schema_answer import CellCharResult, CharResult, OutlineResult, TableCellsResult

TIME_PERIOD = "Time Period"
CURRENCY = "Currency"
UNIT = "Unit"
ACCOUNT = "Account"
VALUE = "Value"

THREE_MONTH_PATTERN = re.compile(r"Three month", re.I)

# 流动资产
P_CURRENT_ASSETS = MatchMulti.compile(rf"^{R_CURRENT_ASSETS}$", operator=any)
# 非流动资产
# non-current: http://100.64.0.105:55647/#/project/remark/408382?treeId=37981&fileId=113463&schemaId=29&projectId=17
P_NON_CURRENT_ASSETS = MatchMulti.compile(rf"^{R_NON_CURRENT_ASSETS}$", operator=any)
# 总资产
P_TOTAL_ASSETS = MatchMulti.compile(rf"^{R_TOTAL_ASSETS}$", operator=any)
# 当没有CA,NCA，也没有total assets时，取assets
# stock_code=00966, year=2024, mid=29, index=2315
P_ASSETS = MatchMulti.compile(rf"^{R_ASSETS}$", operator=any)
P_VALID_TOTAL_ASSETS = MatchMulti.compile(*TOTAL_ASSETS_PATTERN, operator=any)
R_BS_TBL_TITLES = [
    rf"({R_CHAPTER_PREFIX}|^\d*[^a-z]*)(audited\s*|the\s*)?Consolidated.*financial\s*position",
    rf"({R_CHAPTER_PREFIX}|^\d*[^a-z]*)(audited\s*|the\s*)?Consolidated.*Balance",
]
# 都找不到后，再尝试找其他的表名，后续新遇到的都添加到这里
P_MAYBE_BS_TBL_TITLES = PatternCollection(
    [
        rf"({R_CHAPTER_PREFIX}|^\d*[^a-z]*)condensed\s*Consolidated.*financial\s*position",
        rf"({R_CHAPTER_PREFIX}|^\d*[^a-z]*)condensed\s*Consolidated.*Balance",
        rf"({R_CHAPTER_PREFIX}|^\d*[^a-z]*)(audited\s*|the\s*|condensed\s*)?statement.*financial\s*position",
        rf"({R_CHAPTER_PREFIX}|^\d*[^a-z]*)(audited\s*|the\s*|condensed\s*)?statement.*Balance",
        rf"({R_CHAPTER_PREFIX}|^\d*[^a-z]*)financial\s*position[^a-z]*$",
        rf"({R_CHAPTER_PREFIX}|^\d*[^a-z]*)Balance\s*(sheet)?[^a-z]*$",
    ],
    flags=re.I,
)
# audited: http://100.64.0.105:55647/#/project/remark/411619?treeId=4498&fileId=113827&schemaId=29&projectId=17 stock=2009 year=2024
R_BS_TBL_CHAPTERS = [
    # http://100.64.0.105:55647/#/project/remark/406293?treeId=8837&fileId=113228&schemaId=29&projectId=17
    # http://100.64.0.105:55647/#/project/remark/409821?treeId=25010&fileId=113625&schemaId=18&projectId=17&schemaKey=C2.1.1
    NeglectPattern.compile(match=r"financial\s*(Statement|report)s?[\s\u4e00-\u9fa5\d]*$", unmatch=r"note|analysis"),
    # stock_code=06199, year=2024, mid=29 http://100.64.0.105:55647/#/project/remark/383853?treeId=37622&fileId=113005&schemaId=29&projectId=17
    r"financial\s*statements\s*and\s*notes",
]
# 都找不到后，再尝试找特殊的章节，后续新遇到的都添加到这里
P_MAYBE_BS_TBL_CHAPTERS = MatchMulti.compile(
    # http://100.64.0.105:55647/#/project/remark/383871?treeId=8333&fileId=113007&schemaId=29&projectId=17
    r"financial\s*summary",
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7556#note_748503
    r"^Impairment of inventories$",
    operator=any,
)
# 资产负债表所属章节
P_BS_TBL_CHAPTER = MatchMulti.compile(*R_BS_TBL_CHAPTERS, operator=any)
P_STRICT_NOTE_CHAPTER = MatchMulti.compile(
    rf"{R_CHAPTER_PREFIX}{R_CN_SPACE}Note.*(CONSOLIDATED|FINANCIAL).*?", operator=any
)

NON_PATTERN = PatternCollection(r"\bnon", re.I)

impairment_black_feature = [
    r"decrease of impairment loss",
    r"depreciation|amorti[sz]ation",
    r"(before|after) credit loss",
    r"credit loss expenses (after|before)",
    r"over[-\s]provision",
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/660#note_172026
    r"provision.*?(for|of).*?(services?|litigation|performance)",
    r"Cost of inventor ies recognised as expenses",
    r"from.*?operations",
    r"Rental income from investment properties",
]

# 需要排除的重大投资科目
R_SIGNI_INVEST_NEG_SUBJECTS = [
    # other than: stock_code=00358, year=2024, mid=29, page=192
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7431#note_741885
    r"\b(debt|loan|associate|joint\s*venture|derivative|forward)",
    r"\b(subsidiar(y|ies)|propert)",
    r"^interest",
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6172#note_731363
    r"resale\s*agreement",
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6172#note_731413
    r"equity\s*method",
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6172#note_733145
    r"receivable",
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6172#note_733696
    r"supply\s*chain",
]
INVALID_COLUMNS_PATTERNS = {
    TIME_PERIOD: {
        "default": PatternCollection(
            [
                r"(HK|RMB|\$|€).*?0\s?00",
                R_CELL_CURRENCY,
                NeglectPattern.compile(match=r"^[\d,()]*$", unmatch=r"^20\d{2}$"),
                r"Operating (costs|income)",
                r"\d+\)\s?\(",
                r"未經?[審审]核",
                # 过滤只有月份的合并单元格 https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6172#note_738206 stock=00336,year=2024
                # http://100.64.0.105:55647/#/project/remark/412052?projectId=17&treeId=37955&fileId=113876&schemaId=29 stock=9698, year=2024
                rf"^((as\s*|at\s*|of\s*){{,2}}\d{{1,2}}(st|nd|rd|th)?\s*{R_EN_MONTH}[,，]?(\s*[于於][\d\s]+月[\d\s]+日)?\s*){{1,2}}$",
                rf"^((as\s*|at\s*|of\s*){{,2}}{R_EN_MONTH}\s*\d{{1,2}}(st|nd|rd|th)?[,，]?(\s*[于於][\d\s]+月[\d\s]+日)?\s*){{1,2}}$",
            ]
            + COLUMNS_PATTERNS[CURRENCY].patterns,
            flags=re.I,
        ),
    },
    CURRENCY: {
        "default": PatternCollection(
            [
                r"(?P<dst>assets|August|causes)",
                r"(?P<dst>policyholders’)",
                r"HKFRS",
                # 换行字符被识别为货币符： stock_code=01086, year=2024, mid=29
                r"\s[a-z]\s",
            ],
            flags=re.I,
        ),
    },
    UNIT: {
        "default": PatternCollection(
            [
                r"\d+%",
            ],
            flags=re.I,
        ),
    },
    ACCOUNT: {
        "Total Assets": PatternCollection(
            [
                r"Bank balances and cash",
                r"profit or loss",
                r"plant and equipment",
                r"Net current assets",
                r"Current liabilities",
                r"Capital and reserves",
            ],
            flags=re.I,
        ),
        "Significant Investment": PatternCollection(
            [
                *TOTAL_ASSETS_PATTERN,
                *R_SIGNI_INVEST_NEG_SUBJECTS,
            ],
            flags=re.I,
        ),
    },
    VALUE: {
        "Total Assets": PatternCollection(
            [
                r"(?<!ASSETS AND )LIABILITIES$",
                r"Attributable.*?to",
                r"EQUITY$",
                r"Unaudit|未經?[審审]核",
            ],
            flags=re.I,
        ),
        "Significant Investment": PatternCollection(
            [
                *TOTAL_ASSETS_PATTERN,
                *R_SIGNI_INVEST_NEG_SUBJECTS,
            ],
            flags=re.I,
        ),
    },
}

ACCOUNT_PATTERN = {
    "Total Assets": {
        "pattern": PatternCollection(
            [
                rf"^{R_NON_CURRENT_ASSETS}$",
                rf"^{R_CURRENT_ASSETS}$",
                rf"^{R_TOTAL_ASSETS}$",
                rf"^{R_ASSETS}$",
            ],
            flags=re.I,
        ),
        "invalid_pattern": PatternCollection(
            [
                r"Other",
                r"of (non-)?current assets",
            ],
            flags=re.I,
        ),
        "stop_pattern": PatternCollection(
            [
                rf"{R_TOTAL_ASSETS}{R_CN_SPACE_COLON}$",
                r"^Contract\s*?liabilities$",
            ],
            flags=re.I,
        ),
    },
}

CELL_DATA_PATTERN = PatternCollection(
    [
        cell_data_patterns.patterns,
        re.compile(rf"[(（]\d+(\.\d+)?{R_PERCENT}?[)）]"),
    ]
)

INVALID_CELL_DATA_PATTERN = PatternCollection(
    [
        r"20\d\d",
        r"Notes?",
        r"company|parent",
        r"Restated",
        r"\d+\s*?million",  # file id revenue 42224
        r"^\($",  # file id revenue 42224
        r"(Un)?audit(ed)?|未經?[審审]核",
    ],
    re.I,
)

SPECIAL_TIME_PATTERNS = PatternCollection(
    [
        r"20\d\d",
    ]
)

NUMBER_PATTERN = PatternCollection(r"\d")
DOT_PATTERN = PatternCollection(r"\.")
COMMA_PATTERN = PatternCollection(r"[,，]")
MINUS_PATTERN = PatternCollection(rf"^{R_MIDDLE_DASH}")


COMMON_FEATURE_BLACK_LIST = {
    "row_headers": [
        r"__regex__^D_date",
    ],
}

TABLE_ABOVE_BREAK_PATTERN = PatternCollection(
    [
        r"^The board (\(the “Board”\))? of Directors",  # 该描述一般意味着上方不太可能出现单位和币种
        r"^The Board is pleased to announce",
    ],
    re.I,
)


def get_pattern_collection(pattern_config, col=None, schema_name=None):
    pattern_collection = None
    if col and schema_name:
        col_pattern_collection = pattern_config.get(col, {})
        pattern_collection = col_pattern_collection.get(schema_name) or col_pattern_collection.get("default")
    elif col:
        pattern_collection = pattern_config.get(col)
    elif schema_name:
        pattern_collection = pattern_config.get(schema_name)

    if pattern_collection and not isinstance(pattern_collection, PatternCollection):
        pattern_collection = PatternCollection(pattern_collection)

    return pattern_collection


def match_subtitle_and_header(header: ParsedTableCell, patterns: Dict[str, List[str]]) -> bool:
    if not patterns:
        return False
    for subtitle, pattern_list in patterns.items():
        subtitle_p = PatternCollection(subtitle, re.I)
        patterns_p = PatternCollection(pattern_list, re.I)
        clean_subtitle = clean_txt(header.subtitle) if header.subtitle else ""
        if subtitle_p.nexts(clean_subtitle) and patterns_p.nexts(header.clean_text):
            return True
    return False


class SignificantInvestmentRatio(TupleTable):
    @property
    def aim_types(self):
        # 仅提取table
        return {PDFInsightClassEnum.TABLE.value}

    @property
    def answer_counts(self):
        return self.get_config("answer_counts")

    @property
    def multi_elements(self):
        return self.get_config("multi_elements", False)

    @property
    def filter_later_elements(self):
        return self.get_config("filter_later_elements", False)

    @property
    def table_title_pattern(self):
        pattern = self.get_config("table_title_pattern", [])
        return PatternCollection(pattern, flags=re.I) if pattern else None

    @property
    def neglect_title_pattern(self):
        pattern = self.get_config("neglect_title_pattern", [])
        return PatternCollection(pattern, flags=re.I) if pattern else None

    @property
    def neglect_title_above_pattern(self):
        pattern = self.get_config("neglect_title_above_pattern", [])
        return PatternCollection(pattern, flags=re.I) if pattern else None

    @property
    def syllabus_pattern(self):
        pattern = self.get_config("syllabus_pattern", [])
        return PatternCollection(pattern, flags=re.I) if pattern else None

    @property
    def neglect_syllabus_pattern(self):
        pattern = self.get_config("neglect_syllabus_pattern", [])
        return PatternCollection(pattern, flags=re.I) if pattern else None

    @property
    def col_black_feature(self):
        # https://www1.hkexnews.hk/listedco/listconews/sehk/2025/0422/2025042201685.pdf code=1202 year=2024
        # company: https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6172#note_719253
        # the company: stock=02607 year=2024
        # 1RWHV: Notes被错误识别后的乱码
        pattern = [
            rf"^{R_CN_SPACE}(notes?|section)\b",
            # 列名为竖排版：https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7556#note_748495
            r"^(\s*s)?\s*e\s*t\s*o\s*n\s*$",
            r"company|parent",
            r"^1RWHV$",
            rf"(?<!\d){R_PERCENT}",
            # stock=00152, year=2024, page=126
            r"reference",
        ] + self.get_config("col_black_feature", [])
        return PatternCollection(pattern, flags=re.I) if pattern else None

    @property
    def full_match(self):
        """
        规则与训练特征完全匹配即 pattern==feature
        """
        return self.get_config("full_match", False)

    @property
    def need_significant(self):
        return self.get_config("need_significant", False)

    @property
    def only_inject_features(self):
        return self.get_config("only_inject_features", False)

    @property
    def is_total_assets(self):
        return self.schema.parent.name == "Total Assets"

    @cached_property
    def parsed_tables(self):
        return {}

    @cached_property
    def total_assets_patterns(self):
        return defaultdict(list)

    def get_parsed_table(self, element):
        index = element["index"]
        if index in self.parsed_tables:
            return self.parsed_tables[index]
        parsed_table = parse_table(element, tabletype=TableType.TUPLE.value, pdfinsight_reader=self.pdfinsight)
        self.parsed_tables[index] = parsed_table
        return parsed_table

    def train(self, dataset: List[DatasetItem], **kwargs):
        model_data = {}
        for col, col_path in self.columns_with_fullpath():
            if col not in ["Value"]:
                continue
            for item in dataset:
                for node in self.find_answer_nodes(item, col_path):
                    if node.data is None:
                        continue
                    features = self.extract_feature(item.data["elements"], node.data)
                    model_data.setdefault(col, Counter()).update(features)
        self.model_data = model_data

    def add_common_feature_black_list(self):
        common_feature_black_list = COMMON_FEATURE_BLACK_LIST.get(self.feature_from, [])
        self.config["feature_black_list"] = self.config.get("feature_black_list", []) + common_feature_black_list

    @staticmethod
    def guess_sub_total_text(account_cell: ParsedTableCell, value_cell: ParsedTableCell):
        if account_cell.sub_total_headers:
            return [item.clean_text for item in account_cell.sub_total_headers]
        if value_cell.subtitle_cells:
            return [c.clean_text for c in value_cell.subtitle_cells]
        if account_cell.subtitle_cells:
            return [c.clean_text for c in account_cell.subtitle_cells]
        return ""

    @staticmethod
    def convert_text(texts: list[str]):
        """
        TODO 之前仅判断了Non-current Assets，其余都是Current Assets，这里增加Total Assets
        """
        for text in texts:
            if NON_PATTERN.nexts(text):
                return "Non-current Assets"
            elif P_CURRENT_ASSETS.search(text):
                return "Current Assets"
            elif P_TOTAL_ASSETS.search(text):
                return "Total Assets"
        logging.info(f"invalid sub_total: {','.join(texts)}")
        return ""

    @staticmethod
    def remove_non_en_chars(header_text):
        """
        列名中的`流動資産`等词无法被识别，这里删除再做匹配
        """
        return P_NON_EN.sub("", header_text).strip()

    def add_title2account(self, answer_results):
        ret = []
        for answer_result in answer_results:
            account_item = answer_result.get("Account")
            value_item = answer_result.get("Value")
            if not (account_item and value_item):
                ret.append(answer_result)
                continue
            account_cell = account_item[0].element_results[0].parsed_cells[0]
            value_cell = value_item[0].element_results[0].parsed_cells[0]
            if value_cell.sub_total_headers:
                sub_total_texts = [item.clean_text for item in value_cell.sub_total_headers]
            else:
                sub_total_texts = self.guess_sub_total_text(account_cell, value_cell)
            sub_total_text = self.convert_text(sub_total_texts)
            element = account_item[0].element_results[0].element
            element_results = TableCellsResult(element, [account_cell])
            account_answer = self.create_result(
                [element_results],
                column="Account",
                meta={"sub_total": sub_total_text},
            )
            answer_result["Account"] = [account_answer]
            ret.append(answer_result)
        return ret

    def filter_elements(self, elements, check_syllabus=True):
        """
        覆写TableModel.filter_elements方法，避免多余的过滤
        """
        return elements

    def predict_schema_answer(self, elements):
        # 添加公共的feature黑名单
        self.add_common_feature_black_list()
        elements = self.collect_bs_table_elements()
        if elements:
            elements = self.filter_continued_tbl_elements(elements)
        answer_results = self.collect_schema_answer(elements)
        # TODO 不确定meta信息的作用，暂时注释
        # if self.need_significant:
        #     answer_results = self.add_title2account(answer_results)
        return answer_results

    def collect_schema_answer(self, elements):  # 只有Value字段依赖table_tuple的结果
        if not elements:
            return []
        ret = []
        self.columns = ["Value"]
        schema_name = self.schema.parent.name
        elt_page = elements[0]["page"]
        for element in elements:
            # 提取到多个元素块时，只返回第一个及第一个相邻页的元素块
            if element["page"] not in (elt_page, elt_page + 1):
                break
            answer_results = super().predict_schema_answer([element])
            value_answers = self.get_value_answer(answer_results, schema_name)
            if not value_answers:
                continue
            all_accounts, element_ret = set(), []
            existed_accounts = []
            for value_answer in value_answers:
                time_answers = (
                    self.get_other_answer(value_answer, TIME_PERIOD, schema_name)
                    or self.get_other_answer(value_answer, TIME_PERIOD, schema_name, from_header_header=True)
                    or self.get_time_answer_from_table_above(value_answer, TIME_PERIOD, schema_name)
                    or self.get_default_time_answer(value_answer, TIME_PERIOD, schema_name)
                )
                if not time_answers:
                    continue
                currency_answer = self.get_other_answer(value_answer, CURRENCY, schema_name)
                if not currency_answer:
                    currency_answer = self.get_answer_from_table_above(value_answer, CURRENCY, schema_name)
                    if not currency_answer:
                        currency_answer = self.get_other_answer(
                            value_answer, CURRENCY, schema_name, from_header_header=True
                        )
                unit_answer = self.get_other_answer(value_answer, UNIT, schema_name)
                if not unit_answer:
                    unit_answer = self.get_answer_from_table_above(value_answer, UNIT, schema_name)
                    if not unit_answer:
                        unit_answer = self.get_other_answer(value_answer, UNIT, schema_name, from_header_header=True)
                account_answer = self.get_other_answer(value_answer, ACCOUNT, schema_name, from_col_header=False)
                if not account_answer:
                    continue
                # 如果已经取到某个年份的科目，则后续列不再重复提取
                # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6172#note_739320
                # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6172#note_738206
                # http://100.64.0.105:55647/#/project/remark/406917?projectId=17&treeId=13662&fileId=113298&schemaId=29 stock=1065, year=2024
                # http://100.64.0.105:55647/#/project/remark/418470?projectId=17&treeId=38075&fileId=114592&schemaId=29 stock=9961, year=2024
                accounts, account_rows = (
                    [ans.text for ans in account_answer],
                    [ans.meta["cell"][0] for ans in account_answer],
                )
                time_periods = [ans.text for ans in time_answers]
                if [accounts, account_rows, time_periods] in existed_accounts:
                    continue
                existed_accounts.append([accounts, account_rows, time_periods])
                all_accounts.update(clean_txt(ans.text, remove_cn_text=True) for ans in account_answer)
                answer_group = {
                    "Time Period": time_answers,
                    "Currency": currency_answer,
                    "Unit": unit_answer,
                    "Account": account_answer,
                    "Value": [value_answer],
                }
                element_ret.append(answer_group)
            # rule=Total Assets时，必须有total assets或者同时有current assets + non-current assets，否则不提取
            if not self.is_total_assets or self.is_valid_total_assets(element["index"], all_accounts):
                ret.extend(element_ret)
            # 确保取到total assets后，不再向下找 （TODO 考虑未正确识别的跨页连续表格
            if not self.multi_elements and ret:
                break

        return ret

    def is_valid_total_assets(self, table_index, all_accounts):
        """
        提取到的总计必须与表格中内容对应
        https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6172#note_730854
        """
        for pattern in self.total_assets_patterns[table_index]:
            if not any(pattern.search(self.remove_non_en_chars(a)) for a in all_accounts):
                return False
        return True

    def get_value_answer(self, answer_results, schema_name):
        ret = []
        answer_row_indices = sorted(
            {
                r.element_results[0].parsed_cells[0].rowidx
                for r in chain.from_iterable(
                    v for answer_result in answer_results for k, v in answer_result.items() if k == "Value"
                )
            }
        )
        for answer_result in answer_results:
            for key, items in answer_result.items():
                for item in items:
                    if key == "Value" and self.is_valid_value(item, schema_name, answer_row_indices):
                        if not self.answer_counts:
                            ret.append(item)
                        else:
                            if len(ret) < self.answer_counts:
                                ret.append(item)
                            else:
                                break
        return ret

    @staticmethod
    def is_fixed_cell(cell: ParsedTableCell):
        return cell.text != cell.fixed_text

    def get_cell_answer(self, cell: ParsedTableCell, matcher: re.Match, col: str, element: dict):
        if not (value := matcher.groupdict().get("dst")):
            return None
        span = matcher.span()
        if self.is_fixed_cell(cell):
            # 识别错误的表头，直接取cell的chars
            dst_chars = cell.raw_cell["chars"]
        else:
            dst_chars = self.get_chars(cell.text, value, cell.raw_cell["chars"], span)
        return self.create_result(
            [CellCharResult(element, dst_chars, [cell], display_text=value)],
            column=col,
            meta={"cell": [int(i) for i in cell.raw_cell["index"].split("_")]},
        )

    def is_invalid_header(self, col, cell_text, invalid_patterns):
        if not invalid_patterns:
            return False
        if col == TIME_PERIOD:
            if SPECIAL_TIME_PATTERNS.nexts(cell_text):
                return False
            if self.is_invalid_time(cell_text):
                return True
        return invalid_patterns.nexts(cell_text)

    def get_other_answer(self, value_answer, col, schema_name, from_col_header=True, from_header_header=False):
        ret = []
        patterns = get_pattern_collection(COLUMNS_PATTERNS, col=col)
        invalid_patterns = get_pattern_collection(INVALID_COLUMNS_PATTERNS, col=col, schema_name=schema_name)
        answer_cell = value_answer.element_results[0].parsed_cells[0]
        element = answer_cell.table.element
        if col == ACCOUNT:
            headers = self.get_possible_headers_for_account(answer_cell, schema_name)
        else:
            # 如果是续表，找续表的element： http://100.64.0.105:55647/#/project/remark/418044?treeId=37993&fileId=114544&schemaId=29&projectId=17
            if real_element := self.get_continue_table_element(answer_cell):
                element = real_element
            headers = self.get_possible_headers_for_other(
                col, schema_name, answer_cell, from_col_header, from_header_header
            )
        for header_cell in headers:
            cell_txt = header_cell.clean_text
            if col == TIME_PERIOD and self.doc_type in [DocType.Q3, DocType.INTERIM, DocType.FINAL]:
                possibles = [c.clean_text for c in header_cell.col_header_cells] + [cell_txt]
                if any(THREE_MONTH_PATTERN.search(t) for t in possibles):
                    return []
            if self.is_invalid_header(col, cell_txt, invalid_patterns):
                # Account场景下任意一个header匹配到黑名单，则丢弃答案：https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6172#note_732181
                if col == ACCOUNT:
                    return []
                continue
            if not (matcher := patterns.nexts(cell_txt)):
                continue
            answer = self.get_cell_answer(header_cell, matcher, col, element)
            if not answer:
                answer = self.create_result(
                    [TableCellsResult(element, [header_cell], display_text=header_cell.fixed_text)],
                    column=col,
                    meta={"cell": [int(i) for i in header_cell.raw_cell["index"].split("_")]},
                )
            ret.append(answer)
        return ret

    def find_elements_above(self, element):
        table = self.get_parsed_table(element)
        above_elements = []
        above_indices = [e["index"] for e in table.elements_above]
        # 不确定elements_above会被哪些内容过滤，这里追加所有页面开始~表格之间的元素块，确保能找到单位币种
        for index in range(self.pdfinsight.page_dict[table.element["page"]].element_index_range[0], table.index):
            if index in above_indices:
                continue
            _, above_elt = self.pdfinsight.find_element_by_index(index)
            if not is_para_elt(above_elt):
                continue
            above_elements.append(above_elt)
        above_elements.extend(table.elements_above)
        return above_elements

    def get_time_answer_from_table_above(self, value_answer, col, schema_name):
        """
        日期列名被识别为段落： https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7556#note_749687
        函数作用：使用数据单元格的左右边界，取对应的日期列名
        stock=06182, year=2025
        stock=02066, year=2024
        """
        answer_cell = value_answer.element_results[0].parsed_cells[0]
        if real_element := self.get_continue_table_element(answer_cell):
            # 若存在续表，优先在续表所在页面找
            above_elements = self.find_elements_above(real_element)
        else:
            above_elements = self.find_elements_above(answer_cell.table.element)
        if not above_elements:
            return []
        date_element = sorted(above_elements, key=itemgetter("index"), reverse=True)[0]
        indices, date_text = self.get_date_text_by_cell_bounds(answer_cell, date_element)
        if not indices:
            return []
        start, end = min(indices), max(indices) + 1
        patterns = get_pattern_collection(COLUMNS_PATTERNS, col=col)
        invalid_patterns = get_pattern_collection(INVALID_COLUMNS_PATTERNS, col=col, schema_name=schema_name)
        if invalid_patterns.nexts(date_text) or not patterns.nexts(date_text):
            return []
        return [
            self.create_result(
                [
                    CharResult(
                        date_element, date_element["chars"][start:end], start=start, end=end, display_text=date_text
                    )
                ],
                column=col,
            )
        ]

    @staticmethod
    def get_date_text_by_cell_bounds(answer_cell, date_element):
        # 这里必须获取cell的边框边界，不能使用font_box
        cell_left, _, cell_right, _ = answer_cell.raw_cell["box"]
        idx_and_chars = [
            (i, c["text"])
            for i, c in enumerate(date_element["chars"])
            if c["font_box"][0] >= cell_left - 1 and c["font_box"][2] <= cell_right + 1
        ]
        return [i[0] for i in idx_and_chars], "".join([i[1] for i in idx_and_chars]).strip()

    def get_default_time_answer(self, value_answer, col, schema_name):
        """
        取不到time_answer时，若当前仅有2个有效数据列，则认为第一列为日期列
        https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7556#note_748495
        https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7556#note_767995
        """
        answer_cell = value_answer.element_results[0].parsed_cells[0]
        table = answer_cell.table
        if len(table.col_header) - table.row_header_end - 1 != 2:
            return []
        data_headers = {}
        invalid_patterns = get_pattern_collection(INVALID_COLUMNS_PATTERNS, col=col, schema_name=schema_name)
        for header_cells in table.col_header:
            col_index = header_cells[0].colidx
            if col_index < table.row_header_end:
                continue
            if any(self.col_black_feature.nexts(c.clean_text) for c in header_cells):
                continue
            if cells := [
                cell
                for cell in header_cells
                if cell.clean_text and not cell.dummy and not invalid_patterns.nexts(cell.clean_text)
            ]:
                data_headers[col_index] = cells
        answer_col = answer_cell.colidx
        if not data_headers and table.index > 1:
            return self._get_time_answer_by_outline(table, answer_col, col)
        # 仅有两列数据列时，默认第一列为当年数据，第二列为上一年数据
        if len(data_headers) != 2 or answer_col not in data_headers:
            return []
        result_cells = data_headers[answer_cell.colidx]
        year = self.report_year if answer_col == min(data_headers) else str(int(self.report_year) - 1)
        return [self.create_result([TableCellsResult(table.element, result_cells, display_text=year)], column=col)]

    def _get_time_answer_by_outline(self, table, answer_col, col):
        time_headers = [cells for cells in table.col_header[-2:] if answer_col == cells[0].colidx]
        if not time_headers:
            return []
        # 列名有背景色，且距离数据行有一定距离（这里设置为2倍表格行高）时，识别不到表头：https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7556#note_767995
        prev_element = self.pdfinsight.find_element_by_index(table.index - 1)[1]
        elt_bottom = prev_element["outline"][-1]
        _, cell_top, _, cell_bottom = table.col_header[0][0].outline
        cell_height = cell_bottom - cell_top
        if cell_top - elt_bottom < 5 * cell_height:
            return []
        year = str(int(self.report_year) - 1) if answer_col == table.col_header[-1][0].colidx else self.report_year
        left, _, right, _ = time_headers[0][0].outline
        # 在最近元素块和表格第一行之间构造一个outline
        page_box = [
            {
                "page": table.page,
                "outline": [left, cell_top - 2 * cell_height, right, cell_top - 5 * cell_height],
                "text": year,
            }
        ]
        return [self.create_result([OutlineResult(page_box)], column=col)]

    def get_answer_from_table_above(self, value_answer, col, schema_name):
        ret = []
        answer_cell = value_answer.element_results[0].parsed_cells[0]
        above_elements = []
        if real_element := self.get_continue_table_element(answer_cell):
            # 续表的单位，要先尝试在续表所在页面找： http://100.64.0.105:55647/#/project/remark/418044?treeId=37993&fileId=114544&schemaId=29&projectId=17
            above_elements.extend(self.find_elements_above(real_element))
        above_elements.extend(self.find_elements_above(answer_cell.table.element))
        patterns = get_pattern_collection(COLUMNS_PATTERNS, col=col)
        invalid_patterns = get_pattern_collection(INVALID_COLUMNS_PATTERNS, col=col, schema_name=schema_name)
        for element in sorted(above_elements, key=itemgetter("index"), reverse=True):
            # # 资产负债表仅在当前页找unit
            # if element["page"] != table_element["page"]:
            #     break
            if is_para_elt(element):
                element_text = clean_txt(element["text"])
                if TABLE_ABOVE_BREAK_PATTERN.nexts(element_text):
                    break
                # 太长的段落里有可能会出现比较多的金额的描述 跟表格里的单位币种没有关系 51425
                if len(element_text) > 300:
                    continue
                if invalid_patterns and invalid_patterns.nexts(element_text):
                    continue
                matcher = patterns.nexts(element_text)
                if not matcher or not (value := matcher.groupdict().get("dst")):
                    continue
                span = matcher.span()
                dst_chars = self.get_chars(element["text"], value, element["chars"], span)
                answer = self.create_result([CharResult(element, dst_chars)], column=col)
                ret.append(answer)
                break
            # 忽略大表格 认为单位币种的表格只有1-2行
            if not is_table_elt(element) or len(element["cells"]) > 2:
                continue
            table = self.get_parsed_table(element)
            for row in table.rows:
                for cell in row:
                    cell_txt = cell.clean_text
                    if invalid_patterns and invalid_patterns.nexts(cell_txt):
                        continue
                    if not (matcher := patterns.nexts(cell_txt)):
                        continue
                    if answer := self.get_cell_answer(cell, matcher, col, element):
                        ret.append(answer)
        return ret

    def get_possible_headers_for_other(self, col, schema_name, answer_cell, from_col_header, from_header_header):
        headers = self.cell_col_headers(answer_cell) if from_col_header else self.cell_row_headers(answer_cell)
        headers = [header for header in headers if header.clean_text != ""]
        patterns = get_pattern_collection(COLUMNS_PATTERNS, col=col)
        if from_col_header:
            headers = [header for header in headers if header.colidx == answer_cell.colidx]
        if from_header_header:  # from header row_header
            header_headers = []
            for header in headers:
                for item in header.row_header_cells:
                    matcher = patterns.nexts(item.clean_text)
                    if not matcher:
                        continue
                    header_headers.append(item)
            # 添加header下一行第一个单元格 51420
            if not header_headers and headers:
                current_table = headers[0].table
                header_rowidx = headers[0].rowidx
                if len(current_table.rows) > header_rowidx + 1:
                    header_headers.append(current_table.rows[header_rowidx + 1][0])
                if len(current_table.rows) > header_rowidx + 2:
                    header_headers.append(current_table.rows[header_rowidx + 2][0])
            if not header_headers:  # 48871
                current_table = answer_cell.table
                # 表格仅有一年的数据
                # https://jura.paodingai.com/#/hkex/result-announcement/report-review/155214?fileId=40373&page=5 stock_code=00022, year=2020, type=Final
                header_headers = [cell for cell in current_table.rows[0][1:3] if cell.clean_text]
            headers += header_headers
        if len({header.text for header in headers}) == 1:
            range_end = 2 if col not in [CURRENCY, UNIT] else 3
            for row in range(headers[0].rowidx + 1, headers[0].rowidx + range_end + 1):
                try:
                    header_next_cell = answer_cell.table.rows[row][answer_cell.colidx]
                except IndexError:
                    logging.info("not found header for irregular table")
                    continue
                else:
                    headers.append(header_next_cell)
        if not headers:
            # 先从上面往下找 找不到再从下往上尝试
            range_end = 2 if col not in [CURRENCY, UNIT] else 3
            for row in range(0, range_end + 1):
                try:
                    header_next_cell = answer_cell.table.cols[answer_cell.colidx][row]
                except IndexError:
                    logging.info("not found header for irregular table")
                    continue
                else:
                    matcher = patterns.nexts(header_next_cell.clean_text)
                    if not matcher:
                        continue
                    headers.append(header_next_cell)
        headers = self.filter_heads(headers, col)
        if not headers:
            # 从answer_cell开始下到上尝试获取
            header_map = {}
            for cell in answer_cell.table.cols[answer_cell.colidx][: answer_cell.rowidx][::-1]:
                clean_text = cell.clean_text
                if SPECIAL_TIME_PATTERNS.nexts(clean_text) and not header_map.get("time"):
                    header_map["time"] = cell
                    continue
                time_matcher = COLUMNS_PATTERNS[TIME_PERIOD].nexts(clean_text)
                invalid_time_patterns = get_pattern_collection(INVALID_COLUMNS_PATTERNS, TIME_PERIOD, schema_name)
                invalid_time_matcher = invalid_time_patterns.nexts(clean_text)
                if (not invalid_time_matcher and time_matcher) and not header_map.get("time"):
                    header_map["time"] = cell
                    break  # 时间一般在最上方 找到时间之后就跳出
                currency_matcher = COLUMNS_PATTERNS[CURRENCY].nexts(clean_text)
                if currency_matcher and not header_map.get("currency"):
                    header_map["currency"] = cell
                    continue
                unit_matcher = COLUMNS_PATTERNS[UNIT].nexts(clean_text)
                if unit_matcher and not header_map.get("unit"):
                    header_map["unit"] = cell
                    continue
            headers = list(header_map.values())
        ret = []
        exists_headers = set()
        for header in headers:
            if header.clean_text not in exists_headers:
                ret.append(header)
                exists_headers.add(header.clean_text)
        return ret

    @staticmethod
    def filter_heads(headers, col):
        if col != TIME_PERIOD:  # 目前仅time_period字段作此处理 根据需要其他字段也可
            return headers
        ret = []
        for header in headers:
            clean_header_text = header.clean_text
            matcher = COLUMNS_PATTERNS[TIME_PERIOD].nexts(clean_header_text)
            invalid_matcher = INVALID_COLUMNS_PATTERNS[TIME_PERIOD]["default"].nexts(clean_header_text)
            if col == TIME_PERIOD and SPECIAL_TIME_PATTERNS.nexts(clean_header_text):
                invalid_matcher = False
            if matcher and not invalid_matcher:
                ret.append(header)
                # break
        return ret

    @cached_property
    def other_subtitles(self):
        return defaultdict(list)

    def get_possible_headers_for_account(self, answer_cell, schema_name):
        table, table_index = answer_cell.table, answer_cell.table.index
        if any(answer_cell.rowidx == t.row_index for t in self.other_subtitles[table_index]):
            # http://100.64.0.105:55647/#/project/remark/407891?treeId=40856&fileId=113408&schemaId=18&projectId=17&schemaKey=C2.1.1 未识别到的subtitle
            return []
        if self.strict_sub_total_as_feature and self.is_one_item_sub_total(answer_cell):
            # subtitle下只有一个科目，且subtitle没有总计，subtitle取唯一科目的值
            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6172#note_719014
            # stock_code=00133 year=2024 mid=29
            possible_headers = table.row_header[answer_cell.rowidx - 1]
        else:
            possible_headers = self.cell_row_headers(answer_cell)
            # http://100.64.0.105:55647/#/project/remark/407891?treeId=40856&fileId=113408&schemaId=18&projectId=17&schemaKey=C2.1.1 未识别到的subtitle
            num_value = ParsedTableRegion.cell_num(answer_cell.text)
            if len(possible_headers) == 1 and num_value > 0:
                header_cell = possible_headers[0]
                rows = table.rows
                if header_cell.text:
                    left_border = ParsedTableCell.chars_left_border(header_cell.raw_cell)
                    children_rows, total = [], 0
                    for offset in range(1, 10):
                        ridx = answer_cell.rowidx + offset
                        if ridx == len(table.rows):
                            break
                        left = ParsedTableCell.chars_left_border(rows[ridx][0].raw_cell)
                        if (
                            rows[ridx][0].text.startswith(tuple(R_MIDDLE_DASHES))
                            or left - left_border > 1.2 * table.average_char_width
                        ):
                            children_rows.append(ridx)
                            total += ParsedTableRegion.cell_num(rows[ridx][answer_cell.colidx].text)
                    if total == num_value:
                        self.other_subtitles[table_index].append(
                            TableSubTitle(
                                header_cell.rowidx,
                                header_cell.colidx,
                                left_border,
                                header_cell.text,
                                children_rows=children_rows,
                            )
                        )
                        return []
        ret = []
        for cell in possible_headers:
            if not cell.clean_text:
                continue
            ret.append(cell)
            # http://100.64.0.105:55647/#/project/remark/265279?treeId=14095&fileId=70270&schemaId=18&projectId=17&schemaKey=C2.1.1 有多个subtitle时，取最近的subtitle
            if not self.is_total_assets:
                # 给单个科目追加所属的subtitle
                if len(cell.subtitle_cells) > 1:
                    # 小标题包含and时，可能有黑名单关键词： http://100.64.0.105:55647/#/project/remark/410044?treeId=4667&fileId=113650&schemaId=29&projectId=17
                    if " and " not in cell.subtitle_cells[0].text.lower():
                        ret.append(cell.subtitle_cells[0])
                elif subtitles := self.other_subtitles[table_index]:
                    # http://100.64.0.105:55647/#/project/remark/407891?treeId=40856&fileId=113408&schemaId=18&projectId=17&schemaKey=C2.1.1 未识别到的subtitle
                    for subtitle in subtitles:
                        # 不能取current assets等subtitle
                        if any(
                            p.search(subtitle.title) for p in {P_TOTAL_ASSETS, P_CURRENT_ASSETS, P_NON_CURRENT_ASSETS}
                        ):
                            continue
                        if cell.rowidx in subtitle.children_rows:
                            ret.append(table.rows[subtitle.row_index][subtitle.col_index])
                            break
        if ret:
            return sorted(ret, key=lambda x: (x.rowidx, x.colidx))
        patterns = ACCOUNT_PATTERN.get(schema_name)
        if not patterns:
            return []
        valid_pattern = patterns["pattern"]
        invalid_pattern = patterns["invalid_pattern"]
        stop_pattern = patterns["stop_pattern"]
        for col in table.cols:
            for cell in col[: answer_cell.rowidx][::-1][:15]:  # :15 忽略离单元格太远的header，只取15个
                if not cell:
                    continue
                if stop_pattern.nexts(cell.clean_text):
                    break
                if invalid_pattern and invalid_pattern.nexts(cell.clean_text):
                    continue
                if valid_pattern.nexts(cell.clean_text):
                    ret.append(cell)
                if ret:
                    break
            if ret:
                break
        if not ret and answer_cell.sub_total_headers:
            ret = [i for i in answer_cell.sub_total_headers if i.clean_text]
        return ret

    def is_valid_value(self, answer, schema_name, answer_row_indices):
        """
        依次提取资产总额的列名+行名，判断是否为需要的属性
        """
        answer_cell = answer.element_results[0].parsed_cells[0]
        # 数据列在第0列，row header在第一列，这种表格，排除第一列
        # http://100.64.0.105:55647/#/project/remark/416952?treeId=9968&fileId=114422&schemaId=29&projectId=17&page=126 stock=00152, year=2024
        # http://100.64.0.105:55647/#/project/remark/417955?treeId=5769&fileId=114534&schemaId=29&projectId=17&page=130 stock=0001, year=2024
        if answer_cell.colidx == 0:
            return False
        answer_row = answer_cell.rowidx
        table = answer_cell.table
        # 出现了第二个标题列，说明是双排表格，则第二个标题列之后的数据都丢弃
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6172#note_737713
        # http://100.64.0.105:55647/#/project/remark/410673?projectId=17&treeId=9369&fileId=113721&schemaId=29&page=228 stock=00177, year=2024
        second_title_col = 0
        for idx, col in enumerate(table.cols[table.row_header_end :], start=table.row_header_end):
            # 跳过附注列
            if P_NOTES.search(table.col_header_texts[idx]):
                continue
            if any(P_SUBTOTAL_ROW_HEADER.nexts(c.text) for c in col if not c.is_header):
                second_title_col = idx
        if 0 < second_title_col <= answer_cell.colidx:
            return False
        if self.col_black_feature:
            # 这里取单元格所属列名，来判断黑名单属性
            headers = [cell for cell in self.cell_col_headers(answer_cell)]  # noqa
            if headers and not headers[0].clean_text:  # header的text为空, 可能是跨页表格导致，尝试取表格第一行的数据
                first_cell = table.rows[0][answer_cell.colidx]
                if first_cell.clean_text:
                    headers = [first_cell]
            headers.extend(self.cell_row_headers(answer_cell))
            # 这里取单元格的上一个单元格，来判断黑名单属性（可能是%）
            if answer_row - 1 > 0:
                cell_above = table.rows[answer_row - 1][answer_cell.colidx]
                headers.append(cell_above)
            for header in headers:
                invalid_col_matchers = self.col_black_feature.nexts(header.clean_text)
                if invalid_col_matchers:
                    return False
        # 多个疑似合计行，取最后一个： https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6172#note_730831
        sub_total_header = answer_cell.sub_total_headers
        if self.is_total_assets and sub_total_header:
            max_total_row = answer_row
            for ridx in answer_row_indices:
                if ridx <= answer_row:
                    continue
                next_cell = table.rows[ridx][answer_cell.colidx]
                if next_cell.sub_total_headers != sub_total_header:
                    break
                max_total_row = ridx
            # 在当前行的下方，还有sub_total_headers和当前行一样的行，说明当前行不是真实的合计行
            if answer_row < max_total_row:
                return False

        text = clean_txt(
            answer_cell.table.row_header_texts[answer_cell.rowidx] or answer_cell.subtitle or "", remove_cn_text=True
        )
        invalid_pattern = get_pattern_collection(INVALID_COLUMNS_PATTERNS, col=VALUE, schema_name=schema_name)
        if invalid_pattern and text and invalid_pattern.nexts(text):
            return False
        clean_answer_cell_text = answer_cell.clean_text
        if invalid_pattern and invalid_pattern.nexts(clean_answer_cell_text):
            return False
        if INVALID_CELL_DATA_PATTERN.nexts(clean_answer_cell_text):
            return False
        if CELL_DATA_PATTERN.nexts(clean_answer_cell_text):
            return True
        if COLUMNS_PATTERNS[TIME_PERIOD].nexts(clean_answer_cell_text):
            return False
        if COLUMNS_PATTERNS[CURRENCY].nexts(clean_answer_cell_text):
            return False
        if COLUMNS_PATTERNS[UNIT].nexts(clean_answer_cell_text):
            return False
        return True

    @staticmethod
    def is_invalid_time(cell_text):
        #  同时出现 数字 , . 的金额类描述 不是时间
        number_matcher = NUMBER_PATTERN.nexts(cell_text)
        dot_matcher = DOT_PATTERN.nexts(cell_text)
        minus_matcher = MINUS_PATTERN.nexts(cell_text)
        comma_matcher = COMMA_PATTERN.nexts(cell_text)
        # 有可能没有小数点 有可能有负号
        return number_matcher and dot_matcher and (minus_matcher or comma_matcher)

    def collect_bs_table_elements(self):
        # 先用严格的表名/章节查找
        if table_elements := self.find_bs_table_elements(self.table_title_pattern, P_BS_TBL_CHAPTER):
            return table_elements
        # 找不到再用模糊的表名/章节查找
        if table_elements := self.find_bs_table_elements(P_MAYBE_BS_TBL_TITLES, P_MAYBE_BS_TBL_CHAPTERS):
            return table_elements
        # if not table_elements:
        #     # 5. 使用行名中是否有total assets/current assets/non-current assets来找表格
        #     # http://100.64.0.105:55647/#/project/remark/405806?treeId=3359&fileId=113173&schemaId=29&projectId=17
        #     return self.find_target_elements_by_row_header(title_pattern, limit_end)
        return []

    def find_bs_table_elements(self, title_pattern: PatternCollection, chapter_pattern: SearchPatternLike):
        note_chapter_start = self.pdfinsight.get_root_syllabus_range(P_STRICT_NOTE_CHAPTER)[0]
        limit_end = 0
        if note_chapter_start > 0:
            root_range = self.pdfinsight.get_root_syllabus_range(chapter_pattern)
            limit_end = max(root_range[1], note_chapter_start)
        # 1.根据目录Content页码范围找候选元素块
        table_elements = self.find_target_elements_by_contents(title_pattern, limit_end)
        if not table_elements:
            # 2. 从目录中的财务报表父章节（consolidated financial statements）找表格，此时不限制表名，不限制note章节范围
            # http://100.64.0.105:55647/#/project/remark/383853?treeId=37622&fileId=113005&schemaId=29&projectId=17
            table_elements = self.find_target_elements_by_content_chapter(chapter_pattern, title_pattern)
        if not table_elements:
            # 3. 找资产负债表所属章节下的表格，限制表格标题
            table_elements = self.find_target_elements_by_syllabus(chapter_pattern, title_pattern, limit_end, True)
        if not table_elements:
            # 4. 找资产负债表所属章节下的表格，不限制表格标题
            table_elements = self.find_target_elements_by_syllabus(chapter_pattern, title_pattern, limit_end, False)
            # 仅取前两个表格： https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7556#note_748503
            if len(table_elements) > 2:
                return table_elements[:2]
        if not table_elements:
            # 5. 使用所有表格
            table_elements = self.filter_by_title_and_header(self.get_all_tables(), title_pattern, limit_end)
        return table_elements

    def find_target_elements_by_contents(self, title_pattern: PatternCollection, limit_end: int):
        """
        根据目录找资产负债表
        """
        elements = []
        existed_indices = set()
        for title, index_range in self.pdfinsight.root_chapter_ranges_from_contents.items():
            if self.neglect_title_pattern.nexts(title) or not title_pattern.nexts(title):
                continue
            elements.extend(
                self.get_candidate_elements_by_range(
                    index_range, aim_types=self.aim_types, exclude_indices=existed_indices, include_start=True
                )
            )
            existed_indices.update(i for i in range(*index_range))
        # 通过目录找到的一般比较准确，这里不再用标题过滤
        return self.filter_by_title_and_header(
            elements, title_pattern, limit_end, check_title=False, check_neg_title=False, strict=False
        )

    def find_target_elements_by_content_chapter(
        self, chapter_pattern: SearchPatternLike, title_pattern: PatternCollection
    ):
        """
        根据consolidated financial statement章节找资产负债表元素块
        """
        elements = []
        existed_indices = set()
        for title, index_range in self.pdfinsight.root_chapter_ranges_from_contents.items():
            if not chapter_pattern.search(title):
                continue
            # 避免范围太广，只取前6张表格
            elements.extend(
                self.get_candidate_elements_by_range(
                    index_range,
                    aim_types=self.aim_types,
                    exclude_indices=existed_indices,
                    include_start=True,
                )
            )
            existed_indices.update(e["index"] for e in elements)
        return self.filter_by_title_and_header(elements, title_pattern, 0, check_title=False, strict=False)

    def find_target_elements_by_syllabus(
        self, chapter_pattern: SearchPatternLike, title_pattern: PatternCollection, limit_end: int, check_title: bool
    ):
        """
        根据章节找资产负债表元素块
        """
        elements = []
        existed_indices = set()
        for syllabus in self.pdfinsight.find_sylls_by_pattern([chapter_pattern]):
            # http://100.64.0.105:55647/#/project/remark/409821?treeId=25010&fileId=113625&schemaId=18&projectId=17&schemaKey=C2.1.1
            if syllabus["parent"] != -1:
                continue
            elements.extend(
                self.get_candidate_elements_by_range(
                    syllabus["range"], aim_types=self.aim_types, exclude_indices=existed_indices, include_start=True
                )
            )
            existed_indices.update(i for i in range(*syllabus["range"]))
        if not check_title:
            # 不校验表名时，仅取前10张表
            elements = elements[:10]
        return self.filter_by_title_and_header(
            elements, title_pattern, limit_end, check_title=check_title, strict=False
        )

    def filter_by_title_and_header(
        self, elements, title_pattern, limit_end: int, *, check_title=True, check_neg_title=True, strict=True
    ):
        """
        根据表名 + total assets等关键词定位资产负债表
        """
        if limit_end > 0:
            # 过滤notes章节之后的元素块
            elements = [e for e in elements if e["index"] < limit_end]
        if not elements:
            return []
        table_elements = []
        title_matched_indices = set()
        for element in elements:
            table = self.get_parsed_table(element)
            # 过滤黑名单表名
            if (
                not check_title
                and check_neg_title
                and not self.is_valid_table_title(table, element, title_pattern, only_check_neglect=True)
            ):
                continue
            if self.is_valid_table_title(table, element, title_pattern):
                title_matched_indices.add(element["index"])
            elif check_title:
                continue
            # 列名/行名中必须包含总资产
            if self.has_total_assets_in_table(table, strict=strict):
                table_elements.append(element)
        if check_title or len(table_elements) <= 1:
            return table_elements
        # 不校验表名时，可能提取到多张表
        if title_matched_indices:
            return [e for e in table_elements if e["index"] in title_matched_indices]
        # 如果配置了章节等正则，这里做一次过滤
        return self.base_filter_elements(table_elements)

    def is_valid_table_title(
        self, table: ParsedTable, element: dict, title_pattern: PatternCollection, only_check_neglect=False
    ):
        table_titles = []
        if table_title := find_table_title(self.pdfinsight, element["index"]):
            table_titles.append(table_title)
        elif syllabus := self.pdfinsight.get_nearest_syllabus(element):
            if syllabus["title"] not in table_titles:
                table_titles.append(syllabus["title"])
        table_titles = table_titles or table.possible_titles
        # 排除黑名单表名
        if self.neglect_title_pattern and any(self.neglect_title_pattern.nexts(title) for title in table_titles):
            return False
        if only_check_neglect:
            return True
        # 根据标题过滤
        if any(title_pattern.nexts(title) for title in table_titles):
            return True
        return False

    def has_total_assets_in_table(self, table: ParsedTable, strict=True):
        """
        判断行名中是否包含总资产信息
        """
        if self.feature_from == "row_headers":
            header_texts = table.row_header_texts.values()
        else:
            header_texts = table.col_header_texts.values()
        current_patterns, non_current_patterns = [], []
        has_assets = False
        for text in header_texts:
            text = self.remove_non_en_chars(text)
            if not text:
                continue
            if P_NON_CURRENT_ASSETS.search(text):
                non_current_patterns.append(P_NON_CURRENT_ASSETS)
                continue
            if P_CURRENT_ASSETS.search(text):
                current_patterns.append(P_CURRENT_ASSETS)
                continue
            if not has_assets and P_ASSETS.search(text):
                has_assets = True
            # 有科目“total assets”
            if P_TOTAL_ASSETS.search(text):
                self.total_assets_patterns[table.index].append(P_TOTAL_ASSETS)
                return True
        # 有“current assets”或者“non-current assets”，组合所有可能的正则
        for patterns in [current_patterns, non_current_patterns]:
            if not patterns:
                continue
            if len(patterns) == 1:
                self.total_assets_patterns[table.index].extend(patterns)
            else:
                self.total_assets_patterns[table.index].append(MatchMulti.compile(*patterns, operator=any))
        if self.total_assets_patterns[table.index]:
            return True
        if not has_assets:
            return []
        # 全局只有一个subtitle且subtitle为assets，则认为是总资产
        # http://100.64.0.105:55647/#/project/remark/383880?treeId=12441&fileId=113008&schemaId=29&projectId=17
        if not strict:
            self.total_assets_patterns[table.index].append(P_ASSETS)
            return True
        return False
