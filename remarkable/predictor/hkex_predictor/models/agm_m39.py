import re

from remarkable.common.common_pattern import R_CURRENCY, R_MIDDLE_DASHES
from remarkable.common.constants import AnswerValueEnum
from remarkable.common.pattern import Match<PERSON><PERSON><PERSON>, PatternCollection
from remarkable.common.util import clean_txt, split_paragraph
from remarkable.predictor.hkex_predictor.schemas.pattern import P_APPELLATION, R_APPELLATION
from remarkable.predictor.models.para_match import ParaMatch
from remarkable.predictor.schema_answer import AnswerResult, ParagraphResult
from remarkable.services.agm import P_APPENDIX, R_INED, DirectorCVMatcher


class AGMM39(ParaMatch):
    """
    三个子项：薪酬、薪酬依据、薪酬中被服务合同所涵盖
    PS: 每位董事的薪酬、薪酬依据、薪酬中被服务合同所涵盖 都被披露
    ND：任一一位董事未被披露或未披露董事
    NS：仅涉及子项1、3；有明确否定描述或金额为0
    """

    COLUMN_1 = "Amount of emoluments"
    COLUMN_2 = "Basis of determination"
    COLUMN_3 = "Emoluments under service contract."

    R_DIRECTOR_FEE = r"[dD]irector(?:[’'‘s]{2})?\s*(fees?|remuneration|allowance)"

    R_DIRECTOR_EMOLUMENT = rf"(?:[Rr]emuneration|[Ee]moluments?|{R_DIRECTOR_FEE}|fees?|salar(y|ies)|allowance)"

    R_CURRENCY_EXT = rf"({R_CURRENCY}|(?:Y=\d))"

    R_REMUNERATION_ORG = r"(?:board|remuneration\s*committee|Company)"

    P_TOTAL_FEE = PatternCollection(
        [rf"total\s*{R_DIRECTOR_EMOLUMENT}", r"amounted\s*to"],
        re.I,
    )

    P_COLUMN3_KEYS = PatternCollection(
        [
            rf"([Pp]ursuant|entered\s*into|According)[^.]*?service\s*(?:contract|agreement).*?{R_DIRECTOR_EMOLUMENT}",
            rf"remuneration\s*of\s*{R_CURRENCY_EXT}[\d,.]+\s*annually",
            rf"({R_APPELLATION}|[Rr]eceives?).*?annual\s*remuneration\s*(is|of)\s*{R_CURRENCY_EXT}[\d,.]+",
            rf"Under\s*the.*?appointment.*?(?:(?<!no)|(?<!not))\sentitled\s*to[^.]*?{R_CURRENCY_EXT}",  # 79371
        ],
    )

    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6165#note_683300
    P_COLUMN3_VALID_KEYS = PatternCollection(
        [
            r"entitled|per\s*(?:annu(?:al|m)|month|year)",
            rf"receive.*?{R_DIRECTOR_EMOLUMENT}",
            rf"(?:annu(?:al|m)|monthly)\s*{R_DIRECTOR_EMOLUMENT}",
        ],
        re.I,
    )

    P_COLUMN3_ANNUAL = PatternCollection(r"per\s*(?:annu(?:al|m)|month|year)", re.I)

    P_COLUMN3_INVALID_KEYS = PatternCollection(
        [
            r"\bentitled\s*to\s*receive.*?shares?\b",
            r"\bservice\s*(?:contract|agreement).+(?:initial|fix(?:ed)?)\s*term\b",
        ],
        re.I,
    )

    P_COLUMN3_ND_INVALID_KEYS = PatternCollection(
        r"apart\s*from\s*the\s*aforesaid\s*allowance\s*and\s*subsidy",
        re.I,
    )

    P_COLUMN2_KEYS = PatternCollection(
        [
            r"basis\s*of\s*determining",
            rf"determined.*?(?:recommendation\s*of\s*the)?\s*{R_REMUNERATION_ORG}",
        ],
        re.I,
    )

    P_COLUMN2_RULES = MatchMulti.compile(
        R_DIRECTOR_EMOLUMENT,
        MatchMulti.compile(
            r"determined\s*(?:by\s*the\s*Board|on)|based\s*on|according\s*to",  # 88649  # 82151
            r"(?:taking|taken|took)\s*into\s*account",
            r"\bwas\s*determined",
            r"determined\s*in\s*accordance\s*with",
            rf"discretion\s*of\s*the\s*{R_REMUNERATION_ORG}",
            # r"(?:review|fix|determined?|adjust|approved)(?:ed)?.*?(?:(?:with|by)\s*reference|pursuant)\s*to",  # 88649  # 82151
            r"(?:(?:with|by)\s*reference|pursuant)\s*to",  # 88649  # 82151
            rf"entitled\s*to.*?(?:(?:(?:with|by)\s*reference|pursuant)\s*to|discretion\s*of\s*the\s*{R_REMUNERATION_ORG})",
            operator=any,
        ),
        operator=all,
    )

    P_AMOUNT = PatternCollection(rf"{R_CURRENCY_EXT}(?P<amount>[\d][,\d]*(?:\.[\d]+)?)")

    P_SUPPLEMENT_REMUNERATION = PatternCollection(
        [
            r"entitled\s*to\s*[^.]*?bonus.*?determined\s*by",
            P_TOTAL_FEE,
        ],
        re.I,
    )

    P_REMUNERATION = PatternCollection(
        [
            rf"For\s*the\s*financial\s*year[^.]*?emoluments\s*comprised\s*{R_DIRECTOR_FEE}",
            rf"total\s*{R_DIRECTOR_EMOLUMENT}.*?(?:approximately|was)\s*{R_CURRENCY_EXT}[\d,.]+",
            rf"{R_APPELLATION}.*?entitled\s*to[^.]*?{R_CURRENCY_EXT}[\d,.]+",
            rf"{R_DIRECTOR_EMOLUMENT}.*?{R_CURRENCY_EXT}[\d,.]+",
            rf"[Rr]eceived?\s*(the|a)\s*(?:amounts|{R_DIRECTOR_FEE}|fee)\s*of(?:\s*approximately)?\s*{R_CURRENCY_EXT}[\d,.]+",
            r"entitled\s*to\s*a\s*bonus.*?determined\s*by\s*reference\s*to",
            *P_SUPPLEMENT_REMUNERATION.patterns,
            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6165#note_665276  4.3
            rf"[Rr]eceived?\s*(the|a)?\s*(?:amounts|{R_DIRECTOR_FEE}|fee)?{R_CURRENCY_EXT}[\d,.]+.*?comprising.*?(salary|commission|bonus)",
        ],
        # re.I, # 货币类型不能忽略大小写
    )

    P_NO_REMUNERATION = PatternCollection(
        [
            rf"not\s*(entitled\s*to|receive)?\s*.*?{R_DIRECTOR_EMOLUMENT}",
            r"no\s*director(?:’s)?\s*fee\s*payable",
        ],
        re.I,
    )

    P_COLUMN1_WAIVED = PatternCollection(rf"waived?[^.]*{R_DIRECTOR_EMOLUMENT}", re.I)

    P_NO_SERVICE_CONTRACT = PatternCollection(
        [
            r"not\s*(?:have\s*a|enter(ed)?\s*into)[^.]*?\s*service\s*(?:contract|agreement)",
            r"no\s*service\s*(?:contract|agreement)",
            rf"not\s*entitled\s*to\s*receive[^.]*?(?:any\s*(?:remuneration|emoluments?|fixed))\s*(annual\s*)?\s*(as\s*)?{R_DIRECTOR_FEE}",
            rf"not\s*entitled\s*to\s*any\s*(?:remuneration|emoluments?)\s*as\s*{R_DIRECTOR_FEE}",  # 71056
            r"not\s*receive\s*any\s*(?:remuneration|emoluments?)\s*from\s*the\s*(group|company)",
            *P_NO_REMUNERATION.patterns,
        ],
        re.I,
    )

    P_SYLLABUS = PatternCollection(
        [r"LETTER\s*FROM\s*THE\s*(?:BOARD|CHAIRMAN)", *P_APPENDIX.patterns],
        re.I,
    )

    P_COLUMN2_SUMMARIZE_KEYS = MatchMulti.compile(
        r"^The\s*emoluments?\s*of\s*the\s*directors\s*are\s*determined\s*with\s*reference\s*to[^.]+(duties|responsibilities|performance)",
        rf"The\s*{R_DIRECTOR_FEE}\s*are\s*proposed\s*by\s*the\s*Board\s*and\s*approved\s*by",
        rf"entitled\s*to\s*a?\s*{R_DIRECTOR_FEE}\s*which\s*is\s*determined\s*by\s*the\s*Board",
        MatchMulti.compile(
            rf"{R_DIRECTOR_EMOLUMENT}\s*of\s*the\s*directors",
            r"determined[^.]*?with\s*reference\s*to|(?:by|with)\s*reference\s*to",
            operator=all,
        ),
        operator=any,
    )

    P_ALL_DIRECTORS = PatternCollection(
        [
            r"the\s*Retiring\s*Directors",
            r"All\s*the\s*Directors",
        ],
        re.I,
    )

    DIRECTOR_POSITION_MAPPING = {
        "independent non-executive directors": R_INED,
        "non-executive directors": rf"non[{R_MIDDLE_DASHES}\s]executive\s*directors?",
        "executive directors": r"executive\s*directors?",
    }

    BASE_DISCLOSURE_DIRECTOR_POSITION_PATTERNS = [
        r"(?:each|all)\s*(of\s*)?(the\s*)?{director_type}",
        rf"{{director_type}}\s*{R_DIRECTOR_EMOLUMENT}.*?{R_CURRENCY_EXT}[\d,.]+",
    ]

    def default_answer(self):
        results = []
        for column in (self.COLUMN_1, self.COLUMN_2, self.COLUMN_3):
            results.extend(self.build_results_by_column([], column, AnswerValueEnum.ND))
        return results

    def predict_schema_answer(self, elements):
        re_elect_directors = self.predictor.prophet.metadata.get("re_elect_directors")
        if not re_elect_directors:
            return self.default_answer()
        column1_ps_results, column1_ns_results = [], []
        column2_ps_results, column2_ns_results = [], []
        column3_ps_results, column3_ns_results = [], []
        column1_value, column2_value, column3_value = None, None, None
        for director in re_elect_directors:
            c1_ps_results, c1_ns_results = [], []
            c2_ps_results, c2_ns_results = [], []
            c3_ps_results, c3_ns_results = [], []
            for element in reversed(self.get_candidate_elements_by_range(director["range"], need_score=False)):
                if element["class"] != "PARAGRAPH":
                    continue
                if amount_results := self.create_content_result(element, self.P_REMUNERATION):
                    # 按优先级，仅取一条薪酬描述
                    match_results = []
                    for p_amount in self.P_REMUNERATION._pattern_objects:
                        for item in amount_results:
                            if p_amount.search(item.text):
                                match_results.append(item)
                                break
                        if not match_results:
                            continue
                        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6165  3.5
                        for item in amount_results:
                            if item in match_results:
                                continue
                            if self.P_SUPPLEMENT_REMUNERATION.nexts(item.text):
                                match_results.append(item)
                        if match_results:
                            break
                    c1_ps_results.extend(match_results or amount_results)
                    c2_ps_results.extend(self.filter_column2_results(amount_results))
                elif res := self.create_content_result(element, self.P_NO_REMUNERATION):
                    c1_ns_results.extend(res)
                    if c2_res := self.filter_column2_results(res):
                        c2_ps_results.extend(c2_res)
                    else:
                        c2_ns_results.extend(res)
                # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6165#note_683998
                if res := self.create_content_result(element, self.P_COLUMN1_WAIVED):
                    c1_ns_results.extend(res)
                    c2_ns_results.extend(res)
                    c1_ps_results = []
                if not c2_ns_results and not c2_ps_results:
                    c2_ps_results.extend(self.build_column2_results([element]))
                if not c2_ps_results:
                    if self.P_COLUMN2_SUMMARIZE_KEYS.search(clean_txt(element.get("text"))):
                        c2_ps_results.append(ParagraphResult(element, element["chars"]))
                if c3_ns_results:
                    continue

                # 优先提取明确说明在服务合同下，薪酬为多少
                if column3_res := self.create_content_result(element, self.P_COLUMN3_KEYS):
                    if res := self.filter_column3_results(column3_res):
                        c3_ps_results.extend(res)
                        continue
                # 薪酬明确表面为每年、每月等关键词
                elif res := [ans for ans in amount_results if self.P_COLUMN3_ANNUAL.nexts(ans.text)]:
                    c3_ps_results.extend(res)
                    continue
                # 明确说明符合NS的case
                elif res := [
                    ans
                    for ans in self.create_content_result(element, self.P_NO_SERVICE_CONTRACT)
                    if not self.P_COLUMN3_ND_INVALID_KEYS.nexts(ans.text)
                ]:
                    c3_ns_results.extend(res)
                    continue

                if not c3_ps_results:
                    # 以上都无，则从薪酬中取特定关键词，匹配上也可以提取
                    c3_ps_results.extend(
                        self.filter_column3_results(amount_results, valid_pattern=self.P_COLUMN3_VALID_KEYS)
                    )

            if not c1_ps_results and not c1_ns_results:
                column1_value = AnswerValueEnum.ND
            if not c2_ps_results and not c2_ns_results:
                column2_value = AnswerValueEnum.ND
            if not c3_ns_results and not c3_ps_results:
                column3_value = AnswerValueEnum.ND

            column1_ps_results.extend(c1_ps_results)
            column1_ns_results.extend(c1_ns_results)
            column2_ps_results.extend(c2_ps_results)
            column2_ns_results.extend(c2_ns_results)
            column3_ps_results.extend(c3_ps_results)
            column3_ns_results.extend(c3_ns_results)

        if not column2_ps_results:
            # http://100.64.0.105:55647/#/project/remark/317056?projectId=36&treeId=49189&fileId=81361&schemaId=33
            for ele in self.get_syllabus_section_paragraphs():
                if self.P_COLUMN2_SUMMARIZE_KEYS.search(clean_txt(ele.get("text"))):
                    column2_ps_results.append(ParagraphResult(ele, ele["chars"]))
                    column2_value = None
        if column1_value == AnswerValueEnum.ND and (
            self.is_all_directors(column1_ps_results or column1_ns_results)
            or (column1_ps_results and self.check_all_disclosure_by_position(column1_ps_results + column1_ns_results))
        ):
            column1_value = None

        if column2_value == AnswerValueEnum.ND and (
            self.is_all_directors(column2_ps_results or column2_ns_results)
            or (column2_ps_results and self.check_all_disclosure_by_position(column2_ps_results + column2_ns_results))
        ):
            column2_value = None

        if self.is_all_directors(column3_ns_results):
            column3_ps_results = []
            column3_value = None
        elif column3_value == AnswerValueEnum.ND and (
            self.is_all_directors(column3_ps_results)
            or (column3_ps_results and self.check_all_disclosure_by_position(column3_ps_results + column3_ns_results))
        ):
            column3_value = None

        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6194#note_661262
        value1 = AnswerValueEnum.PS if column1_ps_results else AnswerValueEnum.NS
        value2 = AnswerValueEnum.PS if column2_ps_results else AnswerValueEnum.NS
        value3 = AnswerValueEnum.PS if column3_ps_results else AnswerValueEnum.NS

        answers_1 = self.build_results_by_column(
            column1_ps_results + column1_ns_results, self.COLUMN_1, column1_value or value1
        )
        answers_3 = self.build_results_by_column(
            column3_ps_results + column3_ns_results, self.COLUMN_3, column3_value or value3
        )
        answers_2 = self.build_results_by_column(
            column2_ps_results + column2_ns_results, self.COLUMN_2, column2_value or value2
        )
        return answers_1 + answers_2 + answers_3

    def is_all_directors(self, results: list[AnswerResult]) -> bool:
        if not results:
            return False
        director_count = len(self.predictor.prophet.metadata.get("re_elect_directors"))
        director_prefix_count = [len(P_APPELLATION.findall(item.text)) for item in results]
        if sum(director_prefix_count) == director_count or director_count in director_prefix_count:
            return True
        if (
            len(results) == 1
            and self.P_ALL_DIRECTORS.nexts(results[0].text)
            and not P_APPELLATION.search(results[0].text)
        ):
            return True

        return False

    def check_all_disclosure_by_position(self, results: list[AnswerResult]) -> bool:
        director = self.predictor.prophet.metadata.get("director")
        directors: list = director and director.get("directors")
        if not directors:
            return False
        director_matcher = DirectorCVMatcher(self.pdfinsight)
        director_matcher.extract()
        director_matcher.filter_elements_data_by_director(directors)

        director_types = set()
        for director in director_matcher.filter_director_data:
            for position_type in self.DIRECTOR_POSITION_MAPPING.values():
                if PatternCollection(rf"^{position_type}", re.I).nexts(clean_txt(director.capacity)):
                    director_types.add(position_type)
                    break

        for answer in results:
            text = answer.text
            if hasattr(answer, "element"):
                text = answer.element["text"]
            for p_type in self.DIRECTOR_POSITION_MAPPING.values():
                if p_type in director_types:
                    if MatchMulti.compile(
                        rf"(?:each|all)\s*(of\s*)?(the\s*)?{p_type}",
                        self.R_DIRECTOR_EMOLUMENT,
                        operator=all,
                    ).search(text):
                        director_types.remove(p_type)
                        break
            else:
                return False
        return not director_types

    def get_syllabus_section_paragraphs(self):
        elements = []
        for page_eles in self.pdfinsight.page_element_dict.values():
            if any(self.P_SYLLABUS.nexts(clean_txt(ele.data.get("text") or "")) for ele in page_eles[:2]):
                elements.extend([ele.data for ele in page_eles if ele.data.get("class") == "PARAGRAPH"])
        return elements

    def filter_column1_results(self, results: list[AnswerResult]) -> list[AnswerResult]:
        """
        子项1找该董事当年的所有薪酬，取最大值，暂未遇到不同单位的场景
        """
        if len(results) < 2:
            return results[:1]
        for item in results:
            if self.P_TOTAL_FEE.nexts(item.text):
                return [item]
        return results

    def filter_column2_results(self, results: list[AnswerResult]) -> list[ParagraphResult]:
        elements = [item.element for item in results if hasattr(item, "element")]
        return self.build_column2_results(elements)

    def build_column2_results(self, elements: list[dict]) -> list[ParagraphResult]:
        for element in elements:
            for sub_text, (start, end) in split_paragraph(element.get("text") or "", need_pos=True):
                if self.P_COLUMN2_RULES.search(clean_txt(sub_text)):
                    return [ParagraphResult(element, element["chars"][start:end])]
        for p_key in self.P_COLUMN2_KEYS._pattern_objects:
            for element in elements:
                for sub_text, (start, end) in split_paragraph(element.get("text") or "", need_pos=True):
                    if p_key.search(clean_txt(sub_text)):
                        return [ParagraphResult(element, element["chars"][start:end])]
        return []

    def filter_column3_results(
        self, results: list[AnswerResult], valid_pattern: PatternCollection = None
    ) -> list[AnswerResult]:
        for item in results:
            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6165#note_664929
            if (valid_pattern or self.P_COLUMN3_KEYS).nexts(item.text) and not self.P_COLUMN3_INVALID_KEYS.nexts(
                item.text
            ):
                return [item]
        return []

    def build_results_by_column(self, results: list[AnswerResult], column: str, value: AnswerValueEnum):
        results = sorted(results, key=lambda x: x.element["index"])
        answers = [{column: [self.create_result(results, column=column)]}]
        for common_result in self.get_common_predictor_results(answers):
            common_result.update_answer_value(value)
        return answers
