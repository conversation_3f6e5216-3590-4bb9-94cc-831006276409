import re

from remarkable.common.pattern import <PERSON><PERSON><PERSON><PERSON>, Pat<PERSON><PERSON>ollection, PositionPattern
from remarkable.common.util import clean_txt
from remarkable.pdfinsight.reader import PdfinsightSyllabus
from remarkable.predictor.hkex_predictor.schemas.pattern import R_DURING_THE_YEAR
from remarkable.predictor.models.kmeans_classification import KmeansClassification
from remarkable.predictor.schema_answer import OutlineResult

R_CCT = r"\b(continuing\s*connected\s*(related\s*)?transaction|CCT\b)"
R_C5_CT = r"\b(connected\s*(related\s*)?transaction|CT\b)"

R_GROUP_PREFIX = r"((the group|Company)|there)"

# http://100.64.0.105:55647/#/project/remark/234409?treeId=7337&fileId=66690&schemaId=18&projectId=7337&schemaKey=C5
R_C5_NOT = r"(has|had|have|did|was|were|are) (not|no)\b(?!\swaived)"

R_PREFIX = rf"{R_DURING_THE_YEAR}|{R_GROUP_PREFIX}"
R_SAVE_AS = r"(^save as|other than|except for)"
P_SAVE_AS = MatchMulti.compile(r"^save as", r"other than", r"except for", operator=any)


P_NS = MatchMulti.compile(
    PositionPattern.compile(R_PREFIX, R_C5_NOT, R_CCT),
    PositionPattern.compile(R_SAVE_AS, R_C5_NOT, R_CCT),
    operator=any,
)
P_NS_WITHOUT_CONTINUE = PositionPattern.compile(R_PREFIX, R_C5_NOT, R_C5_CT)

P_PS_IN_SINGLE_PARA = PositionPattern.compile(
    r"\(i\)",
    r"\(ii\)",
    r"\(iii\)",
    # r"\(iv\)",
)

P_PS_CONFIRMED = MatchMulti.compile(
    r"auditor has confirmed in a (letter|report) (in accordance|to the Board)",
    operator=any,
)

P_PS = PatternCollection(
    [
        P_PS_IN_SINGLE_PARA,
        r"\(a\)\(i\) above",
        r"unqualified (their )?(letter|report)",
        rf"report on the {R_CCT}",
        P_PS_CONFIRMED,
    ],
    re.I,
)

P_C5_START_PATTERN = PatternCollection(
    [
        r"^\d+\.(?!\d)",
        r"^[•‧]",
        r"^\([iv]",
        r"^[-—－–]",
        r"^\([abcdefg]\)",
        r"^[abcdefg]\.[\s\b]",
    ],
    re.I,
)


P_C5_START_ONE_PATTERN = PatternCollection(
    [
        r"^\(?[ia1]\b",
        r"^\(?[ia1]\.",
    ],
    re.I,
)


class C5(KmeansClassification):
    def predict_schema_answer(self, elements):
        parent_answer_results = super().predict_schema_answer(elements)
        if not parent_answer_results:
            return []
        elements = self.get_elements_from_answer_result(parent_answer_results)
        if self.need_supplement(elements):
            # 元素块数量大于1，说明答案有多个段落
            elements = self.supplemental_elements(elements)
            if len(elements) > 1:  # 元素块大于1 说明是有具体的`相关监管的 (1)-(4)要求`的要求
                page_box = PdfinsightSyllabus.elements_outline(elements)
                element_results = [OutlineResult(page_box=page_box, element=elements[0], origin_elements=elements)]
                answer_result = self.create_result(element_results, column=self.schema.name)
                return [answer_result]
        element = elements[0]
        if element["class"] != "PARAGRAPH":
            return []
        clean_text = clean_txt(element["text"])
        if P_PS.nexts(clean_text):
            return parent_answer_results
        for sentence in clean_text.split("."):
            if P_NS.search(sentence):
                return parent_answer_results
        return []

    @staticmethod
    def need_supplement(elements):
        if len(elements) > 1:
            return True
        element = elements[0]
        if P_C5_START_PATTERN.nexts(clean_txt(element.get("text", ""))):
            return True
        return False

    def supplemental_elements(self, elements):
        """
        按照分数kmeans分组之后 有可能会遗漏段落 这里往下找段落做补充
        http://100.64.0.105:55647/#/project/remark/233059?treeId=37999&fileId=66465&schemaId=18&projectId=17&schemaKey=C5
        """
        all_elements_idx = [element["index"] for element in elements]
        start_element = elements[0]
        clean_text = clean_txt(start_element.get("text", ""))
        if P_C5_START_ONE_PATTERN.nexts(clean_text) or P_C5_START_PATTERN.nexts(clean_text):
            start_index = start_element["index"]
        else:
            start_index = start_element["index"] - 4  # 最多往前找四个
        for index in range(start_index, start_element["index"] + 16):
            if index in all_elements_idx:
                continue
            ele_type, element = self.pdfinsight.find_element_by_index(index)
            if not element:
                continue
            if (
                index > start_element["index"]
                and element.get("index")
                and element["index"] in self.pdfinsight_syllabus.elt_syllabus_dict
            ):
                #  遍历到开始元素块之后 若遇到目录 则停止
                break
            if ele_type == "TABLE":
                break
            if ele_type != "PARAGRAPH":
                continue
            clean_text = clean_txt(element.get("text", ""))
            if not clean_text:
                continue
            if P_C5_START_PATTERN.nexts(clean_text):
                elements.append(element)
        elements.sort(key=lambda x: x["index"])
        first_element = elements[0]
        # 添加分点描述的上一段 一般是综述性的描述
        if P_C5_START_PATTERN.nexts(clean_txt(first_element.get("text", ""))):
            ele_type, previous_elements = self.pdfinsight.find_element_by_index(first_element["index"] - 1)
            if previous_elements and ele_type == "PARAGRAPH" and previous_elements["text"].endswith((":", "：")):
                elements.insert(0, previous_elements)

        # 去除最后一个可能得综述性描述
        if len(elements) >= 2:
            last_element = elements[-1]
            penultimate_element = elements[-2]
            if P_SAVE_AS.search(clean_txt(last_element.get("text", ""))) and P_C5_START_PATTERN.nexts(
                clean_txt(penultimate_element.get("text", ""))
            ):
                elements = elements[:-1]
        return elements
