from remarkable.common.constants import AnswerV<PERSON>ue<PERSON>num, TableType
from remarkable.common.pattern import PatternCollection
from remarkable.common.protocol import SearchPatternLike
from remarkable.common.util import box_in_box, clean_txt
from remarkable.pdfinsight.parser import ParsedTable, ParsedTableCell, parse_table
from remarkable.pdfinsight.reader_util import find_table_title
from remarkable.predictor.models.base_model import TableModel
from remarkable.predictor.schema_answer import TableCellsResult


class NDDRRepurchase(TableModel):
    """
    https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/5749
    提取披露易中NDDR -> Repurchase report中对应数据
    """

    @property
    def enum(self):
        return AnswerValueEnum.PS.value

    @property
    def force_use_all_elements(self):
        return True

    @property
    def skip_continued_table(self):
        """
        不能跳过续表
        """
        return False

    @property
    def keyword_pattern(self):
        """
        指定关键词正则，表名或者表格中的单元格必须匹配该正则
        """
        return self.get_config("keyword_pattern")

    @property
    def col_pattern(self):
        """
        列正则
        """
        return self.get_config("col_pattern")

    @property
    def col_index(self):
        """
        列号，支持负数
        """
        return self.get_config("col_index")

    @property
    def row_pattern(self) -> SearchPatternLike:
        """
        行正则
        """
        return self.get_config("row_pattern")

    @property
    def start_row_pattern(self) -> SearchPatternLike:
        """
        限制行的终止正则
        """
        return self.get_config("start_row_pattern")

    @property
    def stop_row_pattern(self) -> SearchPatternLike:
        """
        限制行的终止正则
        """
        return self.get_config("stop_row_pattern")

    @property
    def cell_pattern(self):
        """
        单元格正则
        """
        return self.get_config("cell_pattern")

    @property
    def only_value(self):
        """
        是否只需要值，不需要列头/行头
        """
        return self.get_config("only_value")

    @property
    def find_kw_in_prev(self):
        """
        若当前表格找不到关键词，是否需要找上一个相邻表格
        """
        return self.get_config("find_kw_in_prev")

    def train(self, dataset, **kwargs):
        pass

    def predict_schema_answer(self, elements):
        answers = []
        prev_kw_col = None
        all_elements, all_tables = {e["index"]: e for e in elements}, {}
        for element in elements:
            elt_index, elt_page = element["index"], element["page"]
            table = parse_table(element, tabletype=TableType.TUPLE, pdfinsight_reader=self.pdfinsight)
            all_tables[elt_index] = table
            # 表名或单元格必须能够匹配关键词正则
            if self.keyword_pattern:
                found_kw = self._find_keyword(elt_index, table)
                if not found_kw and self.find_kw_in_prev:
                    # 考虑跨页表格： http://************:55647/#/project/remark/325204?treeId=51203&fileId=89509&schemaId=35&projectId=40
                    for i in range(1, 10):
                        prev_index = elt_index - i
                        if prev_element := all_elements.get(prev_index):
                            # 必须跨页
                            if prev_element["page"] >= elt_page:
                                break
                            prev_table = all_tables.get(prev_index) or parse_table(
                                prev_element, tabletype=TableType.TUPLE, pdfinsight_reader=self.pdfinsight
                            )
                            # 跨页表格列数不太一致：
                            # http://************:55647/#/project/remark/325181?treeId=51223&fileId=89486&schemaId=35&projectId=40&schemaKey=
                            # http://************:55647/#/project/remark/339396?projectId=40&treeId=51184&fileId=103672&schemaId=35
                            found_kw = self._find_keyword(prev_index, prev_table)
                            break
                if not found_kw:
                    continue
            if self.row_pattern and (self.col_pattern or self.col_index is not None):
                result_cells = self.find_by_row_and_col(table, prev_kw_col)
            elif self.row_pattern:
                if self.multi:
                    result_cells = self.find_by_rows(table)
                else:
                    result_cells = self.find_by_row(table)
            elif self.col_pattern:
                result_cells = self.find_by_col(table)
            elif self.cell_pattern:
                result_cells = self.find_by_cell(table)
            else:
                raise ValueError("'row_pattern' or 'col_pattern' or 'cell_pattern' is required.")
            # 按page+box过滤，避免取重复
            # http://************:55647/#/project/remark/325206?treeId=51200&fileId=89511&schemaId=35&projectId=40
            # http://************:55647/#/project/remark/325692?treeId=51201&fileId=89997&schemaId=35&projectId=40
            result_cells = [
                c for c in result_cells if c.page == element["page"] and box_in_box(c.outline, element["outline"])
            ]
            if not result_cells:
                continue
            cell_results = [TableCellsResult(element=element, cells=result_cells)]
            answers.append(self.create_result(cell_results, schema=self.schema, value=self.enum))
        return answers

    def _find_keyword(self, index: int, table):
        title = find_table_title(self.pdfinsight, index)
        if title and self.keyword_pattern.search(title):
            return True
        if any(self.keyword_pattern.search(clean_txt(cell.text)) for row in table.rows for cell in row):
            return True
        return False

    def find_by_cell(self, table: ParsedTable):
        for row in table.rows:
            for cell in row:
                if self.cell_pattern.search(clean_txt(cell.text)):
                    return [cell]
        return []

    def find_by_rows(self, table: ParsedTable):
        """
        仅配置了row_pattern且multi=True, 取row_pattern匹中的所有单元格
        同时遍历每行进行匹配，直到stop_row_pattern命中为止
        注意：row_pattern要配置的比较全面，截至哪些单元格能匹配到，就取哪些单元格
        """
        found_start, last_col_index = False, -1
        all_cells = []
        for row in table.rows:
            row_text = "\t".join(clean_txt(c.text) for c in row)
            if not found_start and self.start_row_pattern.search(row_text):
                found_start = True
                continue
            if not found_start:
                continue
            if last_col_index < 0:
                texts, cells = [], []
                for i, cell in enumerate(row):
                    cell_text = clean_txt(cell.text)
                    if cell_text in texts:
                        continue
                    texts.append(cell_text)
                    cells.append(cell)
                    if self.row_pattern.search("\t".join(texts)):
                        last_col_index = i + 1
                        all_cells.extend(cells)
                        break
                continue
            if last_col_index < 0:
                continue
            if self.stop_row_pattern.search(row_text):
                break
            # start_row_pattern和end_row_pattern之间的内容都要
            texts, cells = set(), []
            for cell in row:
                cell_text = clean_txt(cell.text)
                if cell_text in texts:
                    continue
                texts.add(cell_text)
                cells.append(cell)
            all_cells.extend(cells)
        return all_cells

    def find_by_row(self, table: ParsedTable):
        """
        仅配置了row_pattern，取row_pattern匹中的单元格及之后的所有行单元格(需要同时匹配value_pattern)
        注意： 仅匹一行
        """
        for row in table.rows:
            cells, other_cells = self._find_row_or_col_cells(row, self.row_pattern, greedy=True)
            if cells:
                return cells + other_cells
        return []

    def find_by_col(self, table: ParsedTable):
        """
        仅配置了col_pattern，取col_pattern匹中的单元格之后的1个列单元格
        注意： 仅匹一列
        特例：trading date被拆分http://************:55647/#/project/remark/325870?treeId=51176&fileId=90176&schemaId=35&projectId=40
        """
        for col in table.cols:
            cells, other_cells = self._find_row_or_col_cells(col, self.col_pattern)
            if cells:
                return cells + other_cells
        return []

    def find_by_row_and_col(self, table: ParsedTable, prev_kw_col: int | None):
        row_headers, matched_rows, matched_cols = [], [], []
        for row in table.rows:
            # row_headers可能匹配到多个单元格，全部都要提
            # http://************:55647/#/project/remark/325452?treeId=51260&fileId=89757&schemaId=35&projectId=40
            row_headers, other_cells = self._find_row_or_col_cells(row, self.row_pattern, greedy=True)
            if row_headers:
                matched_rows = row_headers + other_cells
                break
        else:
            return []
        if self.col_index is not None:
            kw_col_index = prev_kw_col if prev_kw_col else self.col_index
            matched_cols = table.cols[kw_col_index]
        else:
            for col in table.cols:
                for i, cell in enumerate(col):
                    if self.col_pattern.search(clean_txt(cell.text)):
                        matched_cols = col[i + 1 :]
        if not matched_cols:
            return []
        if not (result_cells := set(matched_rows) & set(matched_cols)):
            return []
        return [*row_headers, *result_cells]

    def _find_row_or_col_cells(
        self, row_or_col: list[ParsedTableCell], pattern: PatternCollection | SearchPatternLike, greedy: bool = False
    ) -> tuple[list[ParsedTableCell], list[ParsedTableCell]]:
        """
        greedy=True: 贪婪模式，匹配正则后，从匹中的单元格取到行末或者列末；否则仅取匹配到正则的单元格
        """
        count = len(row_or_col)
        for i, cell in enumerate(row_or_col):
            if not cell.text.strip():
                continue
            cells = [cell]
            for off in range(1, count - i + 1):
                if pattern.search(" ".join(clean_txt(c.text) for c in cells)):
                    start_index = i + off
                    if self.only_value and len(cells) == 1:
                        value_cell = row_or_col[start_index]
                        if not any(value_cell.text == c.text for c in cells):
                            cells.append(row_or_col[start_index])
                        start_index += 1
                    other_cells = []
                    if greedy:
                        for rest_cell in row_or_col[start_index:]:
                            # 剩余单元格可能存在合并单元格，用文本做排除
                            # http://************:55647/#/project/remark/339396?projectId=40&treeId=51184&fileId=103672&schemaId=35
                            if not any(rest_cell.text == c.text for c in cells + other_cells):
                                other_cells.append(rest_cell)
                    if self.only_value:
                        return cells[1:], other_cells
                    return cells, other_cells
                if i + off >= count:
                    break
                next_cell = row_or_col[i + off]
                if any(next_cell.text == c.text for c in cells):
                    continue
                cells.append(next_cell)
        return [], []
