from __future__ import annotations

from collections import Counter
from dataclasses import dataclass, field
from typing import Iterable, List, Literal

from remarkable.common.common_pattern import PAGE_PATTERN
from remarkable.common.protocol import SearchPatternLike
from remarkable.pdfinsight.interdoc_reader import Bind<PERSON><PERSON><PERSON><PERSON><PERSON>, Interdoc<PERSON>eader, Para
from remarkable.predictor.hkex_predictor.models.yoda_layer.base import BaseLocator
from remarkable.predictor.hkex_predictor.models.yoda_layer.filter import <PERSON><PERSON><PERSON>er, OrFilter
from remarkable.predictor.hkex_predictor.models.yoda_layer.form import Answer, Pos, Range, TableForm
from remarkable.predictor.hkex_predictor.models.yoda_layer.generics import E
from remarkable.predictor.hkex_predictor.models.yoda_layer.iterator import ElementIterator


@dataclass
class ParaBetweenPageRange(BaseLocator):
    """
    在指定的页码范围内查找匹配的段落
    """

    pattern: SearchPatternLike

    def __call__(self, interdoc: InterdocReader, pos: Pos) -> Answer:
        page_range = Range(pos.pages[0], pos.pages[-1])
        offset = self.get_offset(interdoc, page_range.start, page_range.end)
        start, end = page_range.start - offset, page_range.end - offset
        paras = [
            para
            for para in ElementIterator[Para](
                interdoc,
                start=0,
                filter=lambda x: min(x.page_info) >= start and x.type == "PARAGRAPH",
                stop=lambda x: max(x.page_info) > end,
            )
            if self.pattern.search(para.text)
        ]
        return Answer(para_indices=[child.index for para in paras for child in para.children])

    @staticmethod
    def get_offset(interdoc: InterdocReader, start: int, end: int) -> int:
        counter = Counter()
        for serial in range(start - 1, end + 1):
            # 年报中 有类似的描述 `page 76 to 204`，年报实际只有184页
            # http://100.64.0.105:55647/#/project/remark/251585?projectId=17&treeId=5730&fileId=68647&schemaId=28
            if serial >= len(interdoc.pages):
                break
            page_doc = interdoc.pages[serial]
            possible_paras: List[Para] = sorted(
                para
                for para in page_doc.paragraphs + page_doc.page_footers + page_doc.page_headers
                if len(para.children) == 1
            )
            for para in reversed(possible_paras[-2:]):
                if len(para.text) > 200:  # 太长的段落跳过
                    continue
                if not (matcher := PAGE_PATTERN.nexts(para.text)):
                    continue
                if not (page := matcher.groupdict().get("dst", None)):
                    continue
                if len(page) > 3:
                    continue
                counter[int(page) - serial] += 1
        return (counter.most_common(1) or [(0, 0)])[0][0]


@dataclass
class ElemFromEdge(BaseLocator, BindTypeMixin[E]):
    """给定一个index列表, 从两侧向外查找"""

    step: Literal[-1, 1] = 1
    limit: int = 10
    filter: BaseFilter = field(default_factory=OrFilter)

    def next_element(self, interdoc: InterdocReader, pos: Pos) -> Iterable[E]:
        start = pos.start if self.step == -1 else pos.end
        limit = self.limit
        for elem in ElementIterator[self.bind_type](
            interdoc,
            start=start + self.step,
            step=self.step,
        ):
            limit -= 1
            yield elem
            if limit <= 0:
                break

    def __call__(self, interdoc: InterdocReader, pos: Pos) -> Answer:
        if not pos.ranges:
            return Answer()
        elements = []
        for element in self.next_element(interdoc, pos):
            if self.filter(interdoc, element):
                elements.append(element)
        return Answer(
            para_indices=[element.index for element in elements if element.type == "PARAGRAPH"],
            tables=[TableForm(table_index=element.index) for element in elements if element.type == "TABLE"],
        )
