from __future__ import annotations

import re
from dataclasses import dataclass, field
from itertools import chain
from re import Pattern
from typing import Callable, Iterable, List, Literal, Type, Union

from typing_extensions import Self

from remarkable.common.common_pattern import P_SEN_SEPARATOR
from remarkable.common.constants import TableCellsResultType
from remarkable.common.pattern import MATCH_ALWAYS, MatchMulti, PatternCollection
from remarkable.common.protocol import (
    CompleteSearchPattern,
    SearchPatternLike,
)
from remarkable.common.util import clean_txt, pairwise
from remarkable.pdfinsight import clear_syl_title
from remarkable.pdfinsight.interdoc_reader import BindType<PERSON><PERSON>in, ColPara, InterdocReader, Para, RowPara, Table
from remarkable.predictor.hkex_predictor.models.yoda_layer.base import (
    BaseLocator,
    ConditionBase,
    ElementLocator,
    FilterTableMixin,
    StopAtPos,
)
from remarkable.predictor.hkex_predictor.models.yoda_layer.filter import (
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>er,
    <PERSON><PERSON><PERSON><PERSON>,
    ParaTextFilter,
)
from remarkable.predictor.hkex_predictor.models.yoda_layer.form import (
    Answer,
    <PERSON><PERSON>,
    PosAdapter,
    <PERSON>,
    <PERSON><PERSON>ce,
    TableForm,
)
from remarkable.predictor.hkex_predictor.models.yoda_layer.generics import E
from remarkable.predictor.hkex_predictor.models.yoda_layer.iterator import ElementIterator
from remarkable.predictor.hkex_predictor.models.yoda_layer.rule.special import ElemFromEdge


@dataclass
class DefaultLocator(BaseLocator):
    def __call__(self, interdoc: InterdocReader, pos: Pos) -> Answer:
        return Answer()

    def __bool__(self):
        return False


@dataclass
class PageRangePara(BaseLocator):
    """
    通过正则提取到段落中的页码范围, pattern必须包含两个要捕获的组
    """

    pattern: PatternCollection
    dest: BaseLocator
    include_self: bool = True

    def __call__(self, interdoc: InterdocReader, pos: Pos) -> Answer:
        answer = Answer()
        for para in ElementIterator[Para](
            interdoc,
            start=pos.start,
            filter=lambda x: x.type == "PARAGRAPH",
        ):
            if matched := self.pattern.nexts(para.text):
                page_start = int(matched.group(1))
                if matched.lastindex == 1:
                    page_end = page_start
                else:
                    page_end = int(matched.group(2))
                if self.include_self:
                    answer.update(Answer(para_indices=[child.index for child in para.children]))
                answer.update(self.dest.__call__(interdoc, Pos(pages=[page_start, *range(page_start + 1, page_end)])))
                return answer
        return answer


@dataclass
class SentenceFromPos(BaseLocator):
    """通过正则提取段落中的一句话"""

    pattern: CompleteSearchPattern
    skip_pattern: SearchPatternLike = field(default_factory=MatchMulti.never)
    dest: BaseLocator = field(default_factory=DefaultLocator)

    def __call__(self, interdoc: InterdocReader, pos: Pos) -> Answer:
        answer = Answer()
        for para in ElementIterator[Para](interdoc, start=pos.start):
            if self.skip_pattern.search(para.text):
                continue
            if matched := self.pattern.search(para.text):
                if matched.groupdict():
                    for key in matched.groupdict().keys():
                        c_start, c_end = matched.span(key)
                        answer.sentences.append(Sentence(para_index=para.index, start=c_start, end=c_end))
                else:
                    answer.sentences = [Sentence(para_index=para.index, start=matched.start(), end=matched.end())]
                break
        return answer


@dataclass
class OtherChapterPara(BaseLocator):
    """通过正则定位另一个章节，从另一个章节找段落"""

    pattern: PatternCollection
    find_pattern: Pattern
    skip_pattern: SearchPatternLike = field(default_factory=MatchMulti.never)
    dest: BaseLocator = field(default_factory=DefaultLocator)

    INVALID_CHAPTERS = ["issuance of new convertible bonds"]

    def __call__(self, interdoc: InterdocReader, pos: Pos) -> Answer:
        answer = Answer()
        chapters = []
        for para in ElementIterator[Para](interdoc, start=pos.start):
            if self.skip_pattern.search(para.text):
                continue
            if matched := self.pattern.nexts(para.text):
                chapters = self.find_pattern.findall(matched.group("other_chapters"))
                break
        chapter_pattern = PatternCollection(
            patterns=[
                re.compile(rf"{re.escape(chapter)}")
                for chapter in chapters
                if chapter.lower() not in self.INVALID_CHAPTERS
            ]
        )
        nodes = [c for c in interdoc.chapters if chapter_pattern.nexts(c.title)]
        for node in nodes:
            if new_answer := self.dest.__call__(interdoc, Pos(ranges=[Range(start=node.start, end=node.end)])):
                answer.update(new_answer)
                break

        return answer


@dataclass
class SentenceFromSplitPara(BaseLocator):
    """
    通过separator（默认为分号或者英文句号）将段落切分为多个句子，再取出能够匹配pattern的句子
    注意：传进来的pos，仅取start作为段落的index，仅针对一个段落做处理

    Args:
        ordered_patterns: 按照patterns的顺序，依次匹配各个段落中的句子
        skip_pattern: 匹配则不取
        separator: 用来分割段落的正则
        multi: 是否匹配多个句子
        index: 当multi为True时，可以按索引取结果中的值，例如：-1；multi为False相当于index=0
        remove_blank: 是否去除空格匹配
    """

    ordered_patterns: List[SearchPatternLike]
    skip_pattern: SearchPatternLike = field(default_factory=MatchMulti.never)
    separator: Pattern = P_SEN_SEPARATOR
    multi: bool = False
    index: int | None = None
    remove_blank: bool = False
    adapter: PosAdapter = field(default_factory=PosAdapter)

    def __call__(self, interdoc: InterdocReader, pos: Pos) -> Answer:
        # 只取一个段落
        para = interdoc.find_by_index(pos.start)
        if not para:
            return Answer()
        answers = []
        for pattern in self.ordered_patterns:
            start = 0
            for matched in chain(self.separator.finditer(para.text), (None,)):  # type: ignore
                end, matched_len = (matched.start(), len(matched.group())) if matched else (len(para.text), 0)
                sentence = clean_txt(para.text[start : end + matched_len], remove_blank=self.remove_blank)
                if (sentence_matched := pattern.search(sentence)) and not self.skip_pattern.search(sentence):
                    if isinstance(sentence_matched, re.Match) and sentence_matched.groupdict():
                        for key in sentence_matched.groupdict().keys():
                            c_start, c_end = sentence_matched.span(key)
                            answers.append(
                                Answer(
                                    sentences=[
                                        Sentence(para_index=para.index, start=start + c_start, end=start + c_end)
                                    ]
                                )
                            )
                    else:
                        if start == 0 and end == len(para.text) - matched_len:
                            # 如果匹配了整段，直接取段落，以确保框线
                            answers.append(Answer(para_indices=[para.index]))
                            break
                        answers.append(
                            Answer(sentences=[Sentence(para_index=para.index, start=start, end=end + matched_len)])
                        )
                    if answers and not self.multi:
                        return Answer.union(answers)
                if matched:
                    start = matched.end()
            # 任意一个正则匹配到结果，则停止匹配
            if answers:
                break
        # 一个段落中提取出多个sentence，sentences加在一起就是整段
        if len(answers) > 1:
            # answer_text_count = sum(item.sentences[0].end - item.sentences[0].start for item in answers)
            # if answer_text_count == len(para.text):
            #     return Answer(para_indices=[para.index])
            new_answers = []
            global_left = None
            left_changed = False
            for left, right in pairwise(answers):
                left = global_left or left
                if left.sentences[0].end == right.sentences[0].start:
                    merged_answer = Answer(
                        sentences=[
                            Sentence(para_index=para.index, start=left.sentences[0].start, end=right.sentences[0].end)
                        ]
                    )
                    if new_answers:
                        new_answers.pop()
                    new_answers.append(merged_answer)
                    global_left = merged_answer
                    left_changed = True
                else:
                    if not left_changed:
                        new_answers.append(left)
                    new_answers.append(right)
                    global_left = None
            answers = new_answers

        ret_answer = Answer.union(
            answers[self.index : (self.index + 1 if self.index is not None and self.index + 1 else None)]
        )
        ret_answer.positions.append(self.adapter(interdoc, ret_answer))
        return ret_answer


@dataclass
class ForwardParaMatch(BaseLocator):
    size: int

    def __call__(self, interdoc: InterdocReader, pos: Pos) -> Answer:
        """根据开始的index, 向前找self.size长度的段落, 要求找到的段落和开始的元素块的第一部分在同一栏"""
        if self.size == 0:
            return Answer()

        paras = []
        for para in ElementIterator[Para](
            interdoc,
            start=pos.start - 1,
            step=-1,
            filter=lambda x: x.type == "PARAGRAPH",
            stop=StopAtPos(interdoc, pos),
        ):
            if len(paras) >= self.size:
                break
            paras.append(para)
        return Answer(para_indices=[child.index for para in paras for child in para.children])


@dataclass
class ContinuedParaFromPos(BaseLocator):
    """
    在章节之后找到连续段落

    Attributes:
        stop: 匹配不到则停止向后找
        forward: 找到连续段落后, 添加前面size为self.start的段落
        include_stop_para: 是否包含stop匹配到的para
    """

    pattern: SearchPatternLike
    forward: BaseLocator = field(default_factory=lambda: ForwardParaMatch(0))
    start: int = 1
    limit: int = 1
    skip: SearchPatternLike = field(default_factory=MatchMulti.never)
    stop: SearchPatternLike = field(default_factory=MatchMulti.never)
    include_stop_para: bool = False

    def __call__(self, interdoc: InterdocReader, pos: Pos) -> Answer:
        continued_paras = []
        limit = self.limit
        for para in ElementIterator[Para](
            interdoc,
            start=pos.start + self.start,
            filter=lambda x: x.type == "PARAGRAPH",
            stop=StopAtPos(interdoc, pos),
        ):
            limit -= 1
            if self.stop.search(para.text):
                if self.include_stop_para:
                    continued_paras.append(para)
                break
            if self.skip.search(para.text):
                continue
            if self.pattern.search(para.text):
                continued_paras.append(para)
                continue
            if limit < 0 or continued_paras:  # 已找到连续段落或者已经达到查找的最大长度
                break
        if not continued_paras:
            return Answer()

        return self.forward.__call__(
            interdoc,
            Pos.from_range(continued_paras[0].index, interdoc.max_index),
        ) + Answer(para_indices=[child.index for para in continued_paras for child in para.children])


@dataclass
class ContinuedElem(BaseLocator, BindTypeMixin[E]):
    """
    在章节之后找到连续的多个元素块

    Attributes:
        filter: 匹配元素块
        skip: 匹配到则跳过
        stop: 匹配到则停止
    """

    filter: BaseFilter = field(default_factory=AndFilter)
    skip: BaseFilter = field(default_factory=OrFilter)
    stop: BaseFilter = field(default_factory=OrFilter)
    start: int = 1
    limit: int = 1
    size: int = 1

    dest: BaseLocator = field(default_factory=ElemFromEdge[Union[Para, Table]])

    def __call__(self, interdoc: InterdocReader, pos: Pos) -> Answer:
        continued_elms = []
        limit = self.limit
        for elem in ElementIterator[self.bind_type](
            interdoc,
            start=pos.start + self.start,
            filter=lambda x: x.type not in ("PAGE_HEADER", "PAGE_FOOTER"),
            stop=StopAtPos(interdoc, pos),
        ):
            limit -= 1
            if elem.index > pos.start + self.start and self.stop(interdoc, elem):
                break
            if self.skip(interdoc, elem):
                continue
            if self.filter(interdoc, elem):
                continued_elms.append(elem)
                continue
            if limit < 0 or continued_elms:  # 已找到连续段落或者已经达到查找的最大长度
                break
        if len(continued_elms) < self.size:
            return Answer()

        answer = Answer(
            para_indices=[
                child.index for elem in continued_elms if elem.type == "PARAGRAPH" for child in elem.children
            ],
            tables=[
                TableForm(table_index=child.index)
                for elem in continued_elms
                if elem.type == "TABLE"
                for child in elem.children
            ],
        )
        return answer + self.dest.__call__(interdoc, Pos.from_range(continued_elms[0].index, continued_elms[-1].index))


@dataclass
class TableFromPos(BaseLocator, FilterTableMixin):
    start: int = 1
    limit: int = 10
    size: int = 1
    filter: BaseFilter = field(default_factory=AndFilter)
    step: int = 1
    stop: Type[ConditionBase] = StopAtPos
    types: tuple[Literal["row", "col"], ...] = ()
    adapter: PosAdapter = field(default_factory=PosAdapter)

    def get_tpara_types(self) -> list[type[RowPara | ColPara]]:
        types = []
        for typ in self.types:
            if typ == "row":
                types.append(RowPara)
            elif typ == "col":
                types.append(ColPara)
        return types

    def __call__(self, interdoc: InterdocReader, pos: Pos) -> Answer:
        limit = self.limit
        tables = []
        for table in ElementIterator[Table](
            interdoc,
            start=pos.start + self.start,
            stop=self.stop(interdoc, Pos.from_range(pos.start + self.start, pos.end)),
            step=self.step,
        ):
            limit -= 1
            if self.filter(interdoc, table):
                tables.append(table)
            if len(tables) >= self.size:
                break
            if limit < 0:
                break
        answer = Answer()
        for table in tables:
            if cells := self.filter_table(interdoc, table):
                answer.table_cells.extend(cells)
            else:
                answer.tables.append(TableForm(table_index=table.index))
        answer.positions.append(self.adapter(interdoc, answer))
        return answer

    @classmethod
    def from_bothway(cls, **kwargs) -> BaseLocator:
        from remarkable.predictor.hkex_predictor.models.yoda_layer.operator import Operator

        return Operator.any(
            TableFromPos(**kwargs, step=-1),
            TableFromPos(**kwargs, step=1),
        )


@dataclass
class TableCellsFromPos(BaseLocator):
    cell_pattern: SearchPatternLike
    result_type: TableCellsResultType
    operator: Callable[[Iterable], bool] = any
    multi: bool = False
    start: int = 1
    limit: int = 10
    size: int = 1
    filter: BaseFilter = field(default_factory=AndFilter)

    def __call__(self, interdoc: InterdocReader, pos: Pos) -> Answer:
        limit = self.limit
        tables = []
        for table in ElementIterator[Table](
            interdoc,
            start=pos.start + self.start,
            stop=StopAtPos(interdoc, pos),
        ):
            limit -= 1
            if self.filter(interdoc, table):
                if self.result_type in [TableCellsResultType.ROWS.value, TableCellsResultType.COLS.value]:
                    attr = "rows" if TableCellsResultType.ROWS.value == self.result_type else "cols"
                    indices = []
                    for index, row_or_col in enumerate(getattr(table, attr)):
                        if self.operator(self.cell_pattern.search(cell.text) for cell in row_or_col):
                            indices.append(index)
                            if not self.multi:
                                break
                    if indices:
                        tables.append(TableForm(table_index=table.index, **{attr: indices}))
                elif self.result_type == TableCellsResultType.CELLS.value:
                    cells = []
                    for position, cell in table.cells.items():
                        if self.cell_pattern.search(cell.text):
                            cells.append(position)
                            if not self.multi:
                                break
                    if cells:
                        tables.append(TableForm(table_index=table.index, cells=cells))
                else:
                    tables.append(TableForm(table_index=table.index))
            if len(tables) >= self.size or limit < 0 or not self.multi and tables:
                break
        return Answer(tables=tables)


class ParaLocator(ElementLocator[Para]):
    @classmethod
    def from_pattern(
        cls,
        p_filter: SearchPatternLike = MATCH_ALWAYS,
        p_skip: SearchPatternLike = field(default_factory=MatchMulti.never),
        p_stop: SearchPatternLike = field(default_factory=MatchMulti.never),
        **kwargs,
    ) -> Self:
        return cls(filter=ParaTextFilter(p_filter), skip=ParaTextFilter(p_skip), stop=ParaTextFilter(p_stop), **kwargs)


@dataclass
class ParaFromPos(BaseLocator):
    """
    在指定的位置之后找到段落, 如果定义了dest, 则将找到的多个段落中的每一个作为dest查找的开始位置

    Attributes:
        start: 可以为负数, 表示给指定的位置加上偏移量
        skip_pattern: 匹配则跳过
        only_dest_answers: 是否只要dest的结果
    """

    pattern: SearchPatternLike = MATCH_ALWAYS
    start: int = 1
    limit: int = 10
    size: int = 1
    step: int = 1
    dest: BaseLocator = field(default_factory=DefaultLocator)
    only_dest_answers: bool = True
    skip_pattern: SearchPatternLike = field(default_factory=MatchMulti.never)
    adapter: PosAdapter = field(default_factory=PosAdapter)

    def __call__(self, interdoc: InterdocReader, pos: Pos) -> Answer:
        limit = self.limit
        paras: list[Para] = []
        for para in ElementIterator[Para](
            interdoc,
            start=pos.start + self.start,
            filter=lambda x: x.type == "PARAGRAPH",
            stop=StopAtPos(interdoc, pos),
            step=self.step,
        ):
            if self.skip_pattern and self.skip_pattern.search(para.text):
                continue
            limit -= 1
            if self.pattern.search(para.text):
                paras.append(para)
            if len(paras) >= self.size:
                break
            if limit < 0:
                break
        if not paras:
            return Answer()
        answers = (
            []
            if self.only_dest_answers and self.dest
            else [Answer(para_indices=[child.index for para in paras for child in para.children])]
        )
        for para in paras:
            answers.append(self.dest.__call__(interdoc, Pos.from_range(para.index, end=pos.end)))
        answer = Answer.union(answers)
        answer.positions.append(self.adapter(interdoc, answer))
        return answer


@dataclass
class MiddleElement(BaseLocator, BindTypeMixin[E]):
    """
    匹配指定两个para之间的内容, 同时找到start和stop才返回
    start 开始标志
    stop 结束标志
    filter 匹配中间元素
    skip 匹配跳过元素
    include_between 是否包含中间元素
    unmatch_count 可以不匹配次数
    """

    start: BaseFilter = field(default_factory=OrFilter)
    stop: BaseFilter = field(default_factory=OrFilter)
    filter: BaseFilter = field(default_factory=AndFilter)
    skip: BaseFilter = field(default_factory=OrFilter)
    include_between: bool = True
    limit: int = 100
    unmatch_count: int = 1

    def __call__(self, interdoc: InterdocReader, pos: Pos) -> Answer:
        between_paras = []
        limit = self.limit
        unmatch_count = self.unmatch_count
        start_idx = None
        stop_idx = None
        for elem in ElementIterator[self.bind_type](
            interdoc,
            start=pos.start,
            filter=lambda x: x.type not in ("PAGE_HEADER", "PAGE_FOOTER"),
            stop=StopAtPos(interdoc, pos),
        ):
            limit -= 1
            if start_idx:
                if self.stop(interdoc, elem):
                    stop_idx = elem.index
                    break
                if self.skip(interdoc, elem):
                    continue
                if self.filter(interdoc, elem):
                    if self.include_between:
                        between_paras.append(elem)
                    continue
                unmatch_count -= 1
                if unmatch_count < 0:
                    break
            else:
                if self.start(interdoc, elem):
                    start_idx = elem.index
                if limit < 0:
                    break
                continue
        if start_idx and stop_idx:
            result = [interdoc.find_by_index(start_idx), *between_paras, interdoc.find_by_index(stop_idx)]
        else:
            return Answer()
        return Answer(para_indices=[child.index for para in result for child in para.children])


@dataclass
class SyllabusLocator(BaseLocator):
    """
    根据段落匹配到最小章节，取该章节的所有段落
    """

    pattern: SearchPatternLike = field(default_factory=MatchMulti.never)
    skip_pattern: SearchPatternLike = field(default_factory=MatchMulti.never)

    def __call__(self, interdoc: InterdocReader, pos: Pos) -> Answer:
        elem = interdoc.find_by_index(pos.start)
        if not elem:
            return Answer()
        chapter_node = None
        for node in interdoc.lazy_find_chapters(elem.index):
            title = clear_syl_title(node.title, remove_bound_num=True, remove_prefix=True)
            if self.skip_pattern.search(title):
                return Answer()
            if self.pattern.search(title):
                chapter_node = node
        if chapter_node:
            return Answer(para_indices=list(range(chapter_node.start + 1, chapter_node.end)))
        return Answer()
