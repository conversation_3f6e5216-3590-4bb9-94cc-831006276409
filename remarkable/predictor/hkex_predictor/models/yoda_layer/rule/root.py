from __future__ import annotations

import re
from copy import deepcopy
from dataclasses import dataclass, field, fields
from enum import IntEnum
from operator import itemgetter
from typing import Any, Callable, ClassVar, Iterable, List, Tuple, Type, TypeVar

from interdoc import protocol
from interdoc.chapter import ChapterNode

from remarkable.common.common import is_table_elt
from remarkable.common.common_pattern import (
    R_CG_CHAPTER_TITLES,
    R_CI_CHAPTER_TITLES,
    R_DR_CHAPTER_TITLES,
    R_ESG_CHAPTER_TITLES,
    R_NOTES_CHAPTER_TITLES,
    R_OI_CHAPTER_TITLES,
)
from remarkable.common.pattern import (
    MATCH_ALWAYS,
    MATCH_NEVER,
    MatchMulti,
)
from remarkable.common.protocol import SearchPatternLike
from remarkable.common.util import clean_txt
from remarkable.pdfinsight.interdoc_reader import BindTypeMixin, InterdocReader, Para, Table
from remarkable.predictor.hkex_predictor.models.yoda_layer.base import (
    DEFAULT_OPT,
    BaseLocator,
    FilterTableMixin,
    Option,
)
from remarkable.predictor.hkex_predictor.models.yoda_layer.filter import AnswerFilter, BaseFilter
from remarkable.predictor.hkex_predictor.models.yoda_layer.form import (
    Answer,
    Pos,
    ReferenceForm,
    ScoreElement,
    TableForm,
)
from remarkable.predictor.hkex_predictor.models.yoda_layer.generics import E, is_instance_of
from remarkable.predictor.hkex_predictor.models.yoda_layer.iterator import ElementIterator
from remarkable.predictor.hkex_predictor.models.yoda_layer.rule.by_pos import DefaultLocator
from remarkable.predictor.hkex_predictor.pattern import (
    R_NC_CHAPTER_TITLES,
    R_RC_CHAPTER_TITLES,
)

TLocator = TypeVar("TLocator", bound="_ChapterLocator")


class StrictMode(IntEnum):
    """
    严格模式

    >>> assert StrictMode.ROOT.bit_eq(0b11) and StrictMode.ROOT.bit_eq(0b10)
    >>> assert not StrictMode.ROOT.bit_eq(0b01)
    >>> assert StrictMode.CHAPTER.bit_eq(0b11) and StrictMode.CHAPTER.bit_eq(0b01)
    >>> assert not StrictMode.CHAPTER.bit_eq(0b10)
    """

    CHAPTER = 0b01  # 限制查找范围必须在章节和下一个章节之间
    ROOT = 0b10  # 一级目录必须存在, 比如CG,CI等章节

    def bit_eq(self, value: object) -> bool:
        if isinstance(value, StrictMode):
            value = value.value
        elif not isinstance(value, int):
            return False
        return (self.value & value) == self.value


class ChapterStopMixin:
    chapter_pattern: ClassVar[SearchPatternLike]
    min_page_range: ClassVar[int] = 10

    def get_start_chapter(self, interdoc: InterdocReader) -> ChapterNode | None:
        for node, next_node in zip(interdoc.root.children[:-1], interdoc.root.children[1:]):
            if not self.chapter_pattern.search(node.title):
                continue
            if node.title == next_node.title:
                continue
            if node.page + self.min_page_range <= next_node.page:
                return node
        return None


@dataclass
class _ChapterLocator(BaseLocator, ChapterStopMixin):
    """
    Attributes:
        pattern: 匹配章节的正则
        min_page: 从第几页开始查找
        strict: 是否限制只能在找到的章节和下一个章节之间查找
    """

    pattern: SearchPatternLike

    dest: BaseLocator = field(default_factory=DefaultLocator)
    min_page: int = 0
    strict: int = 0b00
    all: bool = False

    def with_dest(self, dest: BaseLocator) -> "_ChapterLocator":
        locator = deepcopy(self)
        locator.dest = dest
        return locator

    def __call__(self, interdoc: InterdocReader, pos: Pos) -> Answer:
        start_chapter = self.get_start_chapter(interdoc)
        if StrictMode.ROOT.bit_eq(self.strict) and not start_chapter:
            return Answer()
        end = None if start_chapter is None else start_chapter.end
        answer = Answer()
        for node in (start_chapter or interdoc.root).follow_nodes:
            if node.page < self.min_page:
                continue
            if (end or node.end) < node.start:
                break
            if not self.pattern.search(node.title):
                continue
            pos = Pos.from_range(node.start, end)
            if StrictMode.CHAPTER.bit_eq(self.strict) and (next_chapter := next(node.follow_nodes, None)):
                pos.ranges[0].end = min(node.end, next_chapter.start)
            answer.positions.append(pos)
            answer.update(self.dest.__call__(interdoc, pos))
            if answer and not self.all:
                break
        return answer

    def as_locator(self, type_: Type[TLocator]) -> TLocator:
        return type_(**{field.name: deepcopy(getattr(self, field.name)) for field in fields(self)})


class _RootChapterLocator:
    """
    定位一级章节，若在interdoc.root.children中没匹配到，或匹配不正确，遍历所有页面尝试定位
    """

    chapter_pattern: ClassVar[SearchPatternLike]
    remove_blank: bool

    def __match_first_para(self, interdoc: InterdocReader, page: protocol.PageDoc):
        """
        判断章节正则是否能匹配页面的第一个段落，如果第一个段落不符且文本长度<正则长度，尝试向后多匹配几个段落
        """
        if page_first_title := interdoc.page_chapter_from_first_para.get(page.page):
            return self.chapter_pattern.search(page_first_title)
        return False
        # # 先判断页眉是否匹配标题正则
        # if any(
        #     self.chapter_pattern.search(clean_txt(header.text, remove_blank=self.remove_blank))
        #     for header in page.page_headers
        # ):
        #     return True
        # if not page.paragraphs or len(page.paragraphs[0].page_info) > 1:
        #     # 跳过空白页和跨页段落
        #     return False
        # paras = []
        # separator = "" if self.remove_blank else " "
        # # 这里考虑标题被拆成了多个段落
        # # 适配文档：https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/3168#note_410269
        # for para in page.paragraphs:
        #     # 考虑到许多文档页眉被识别为段落，这里取前3个段落，尝试匹配
        #     if len(paras) > 3:
        #         break
        #     cleaned_text = clean_txt(para.text, remove_blank=self.remove_blank)
        #     paras.append(cleaned_text)
        #     if self.chapter_pattern.search(cleaned_text) or self.chapter_pattern.search(separator.join(paras)):
        #         return True
        # return self.chapter_pattern.search(separator.join(paras))

    def loc_root_chapter(self, interdoc) -> Pos | None:
        """
        定位一级目录，尽可能准确地获取一级目录index范围
        """
        root_chapter_pos = None
        # 先从interdoc识别的目录中获取
        chapter = next(
            (
                node
                for node in interdoc.root.children
                if self.chapter_pattern.search(clean_txt(node.title, remove_blank=self.remove_blank))
            ),
            None,
        )
        # 如果标题被识别成多个，取下一个，直到范围不是1
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/3168#note_457200
        count_loop = 0
        while chapter and chapter.end - chapter.start <= 1:
            count_loop += 1
            if count_loop > 2:
                break
            chapter = next(chapter.follow_nodes)
        # 判断章节是否在所在页码的前3个段落，不是则说明识别错误，丢弃不用
        if chapter and any(para.index == chapter.element for para in interdoc.pages[chapter.page].paragraphs[:3]):
            # 遍历所有章节页码，判断第一个元素是否满足章节正则
            if pages := [page for page in interdoc.pages if self.__match_first_para(interdoc, page) if page.paragraphs]:
                first_para, last_para = pages[0].paragraphs[0].children[0], pages[-1].paragraphs[-1].children[-1]
                root_chapter_pos = Pos.from_range(
                    min(chapter.start, first_para.index), max(chapter.end, last_para.index)
                )
            else:
                root_chapter_pos = Pos.from_range(chapter.element, chapter.end)
        if not root_chapter_pos:
            # interdoc未能识别，直接遍历所有页码匹配第一个段落元素
            if pages := [page for page in interdoc.pages if self.__match_first_para(interdoc, page) if page.paragraphs]:
                first_para, last_para = pages[0].paragraphs[0].children[0], pages[-1].paragraphs[-1].children[-1]
                root_chapter_pos = Pos.from_range(first_para.index, last_para.index)
        return root_chapter_pos


@dataclass
class _CascadeChapterLocator(BaseLocator, _RootChapterLocator):
    """
    适用于文档目录识别不好的场景，根据章节正则，自上而下匹配目标章节，章节全部匹配上后，返回章节的开始索引，及一级目录的结束页码
    注意：由于会用段落去匹配章节标题，chapter_patterns中的正则建议带上起止符^$
    Attributes:
        chapter_patterns: 多级目录章节的正则，注意：
                          ①第一个正则必须为二级目录，一级目录配置在RootChapterLocator
                          ②必须按顺序自上往下
                          ③正则尽可能带^及$
        remove_blank: 是否全文去掉空白符后进行匹配
        only_chapter: 是否仅在interdoc识别到的目录中做匹配
    # TODO: unitest
    """

    chapter_patterns: List[SearchPatternLike] | None = None
    dest: BaseLocator = field(default_factory=DefaultLocator)
    remove_blank: bool = False
    only_chapter: bool = True
    strict: bool = False

    def with_dest(self, dest: BaseLocator) -> "_CascadeChapterLocator":
        locator = deepcopy(self)
        locator.dest = dest
        return locator

    def loc_follow_chapters(self, interdoc: InterdocReader) -> Tuple[int, Pos]:
        """
        遍历所有章节，依次匹配chapter_patterns中的正则
        """
        # 先找到一级目录
        root_chapter_pos = self.loc_root_chapter(interdoc)
        if not root_chapter_pos:
            return 0, Pos()
        if not self.chapter_patterns:
            return 0, root_chapter_pos
        # 记录未匹配章节正则在self.chapter_patterns中的索引
        unmatched_index = 0
        matched_nodes = []
        # 考虑章节未识别的情况，先遍历所有章节，记录所有能匹配到的节点
        for node in interdoc.root.follow_nodes:
            if node.element > root_chapter_pos.end or unmatched_index == len(self.chapter_patterns):
                break
            if node.element < root_chapter_pos.start:
                continue
            # 只找每个章节第一次出现的位置
            if self.chapter_patterns[unmatched_index].search(clean_txt(node.title, remove_blank=self.remove_blank)):
                unmatched_index += 1
                matched_nodes.append(node)
        if unmatched_index == len(self.chapter_patterns):
            end = matched_nodes[-1].end if self.strict else root_chapter_pos.end
            return unmatched_index, Pos.from_range(matched_nodes[-1].start, end)
        if self.strict:
            # 严格模式，章节没找到则返回空pos
            return 0, Pos()
        # 没找到二级目录
        if not matched_nodes:
            return unmatched_index, root_chapter_pos
        # 如果三级及以后目录没匹配到，返回匹配到的最后一个目录
        end = matched_nodes[-1].end if self.strict else root_chapter_pos.end
        return unmatched_index, Pos.from_range(matched_nodes[-1].start, end)

    def __call__(self, interdoc: InterdocReader, pos: Pos) -> Answer:
        """
        基于章节匹配结果，遍历接下来的所有段落，尝试匹配
        """
        unmatched_index, located_pos = self.loc_follow_chapters(interdoc)
        if not located_pos:
            # 这里表示一级目录没匹配到，直接返回
            return Answer()
        if not self.chapter_patterns:
            return self.dest.__call__(interdoc, located_pos)
        if not self.only_chapter and unmatched_index < len(self.chapter_patterns):
            # 章节信息获取不完整，尝试遍历接下来的段落做匹配
            for para in ElementIterator[Para](
                interdoc, start=located_pos.start, filter=lambda x: x.type == "PARAGRAPH"
            ):
                if para.index > located_pos.end or unmatched_index == len(self.chapter_patterns):
                    break
                if para.index < located_pos.start:
                    continue
                if self.chapter_patterns[unmatched_index].search(clean_txt(para.text, remove_blank=self.remove_blank)):
                    unmatched_index += 1
                    located_pos.ranges[0].start = para.index
        if unmatched_index != len(self.chapter_patterns):
            return Answer()
        return self.dest.__call__(interdoc, located_pos)


@dataclass
class Reference(BaseLocator):
    path: List[str] | str
    from_answer_value: str = ""

    def __call__(self, interdoc: InterdocReader, pos: Pos) -> Answer:
        paths = [self.path] if isinstance(self.path, str) else self.path
        return Answer(references=[ReferenceForm(path=paths, from_answer_value=self.from_answer_value)])

    def link_next(self, *locators: BaseLocator, option: Option = DEFAULT_OPT) -> BaseLocator:
        raise NotImplementedError


@dataclass
class CGChapterLocator(_ChapterLocator):
    """在Corporate Governance Report的章节之后查找"""

    chapter_pattern: ClassVar[SearchPatternLike] = MatchMulti.compile(
        r"corporate\s*governance\s*(report|practices)",
        r"corporate\s*governance\s*overview\s*statement",
        r"section\s*\d+\s*corporate\s*governance",
        *R_CG_CHAPTER_TITLES,
        operator=any,
    )


@dataclass
class RDChapterLocator(_ChapterLocator):
    """在Report of Directors的章节之后查找"""

    min_page_range = 1  # 66727 章节识别问题，把每页的页码识别成为章节，所以每个章节只有一页

    chapter_pattern: ClassVar[SearchPatternLike] = MatchMulti.compile(
        MatchMulti.compile(*R_DR_CHAPTER_TITLES, operator=any),
        operator=any,
    )


@dataclass
class OIChapterLocator(_ChapterLocator):
    """在Other Information的章节之后查找"""

    chapter_pattern: ClassVar[SearchPatternLike] = MatchMulti.compile(*R_OI_CHAPTER_TITLES, operator=any)
    min_page_range = 3


@dataclass
class CIChapterLocator(_ChapterLocator):
    """在Corporate Information的章节之后查找"""

    chapter_pattern: ClassVar[SearchPatternLike] = MatchMulti.compile(*R_CI_CHAPTER_TITLES, operator=any)
    min_page_range = 1


@dataclass
class RCChapterLocator(_ChapterLocator):
    """在Remuneration Committee的章节之后查找"""

    chapter_pattern: ClassVar[SearchPatternLike] = MatchMulti.compile(*R_RC_CHAPTER_TITLES, operator=any)


@dataclass
class NCChapterLocator(_ChapterLocator):
    """在 Nomination Committee 的章节之后查找"""

    chapter_pattern: ClassVar[SearchPatternLike] = MatchMulti.compile(*R_NC_CHAPTER_TITLES, operator=any)


@dataclass
class ACChapterLocator(_ChapterLocator):
    """在Audit Committee Report的章节之后查找"""

    chapter_pattern: ClassVar[SearchPatternLike] = re.compile(r"^audit\s*committee\s*report$", re.I)
    min_page_range = 0


@dataclass
class DRChapterLocator(_ChapterLocator):
    """在Directors' Report章节之后查找"""

    chapter_pattern: ClassVar[SearchPatternLike] = MatchMulti.compile(*R_DR_CHAPTER_TITLES, operator=any)


@dataclass
class MDAChapterLocator(_ChapterLocator):
    """在Management Discussion and Analysis的章节之后查找"""

    chapter_pattern: ClassVar[SearchPatternLike] = re.compile(r"^management\s*discussion\s*and\s*analysis$", re.I)


@dataclass
class NoteChapterLocator(_ChapterLocator):
    """Notes to the Consolidated Financial Statements(綜合財務報表附註)章节之后查找"""

    chapter_pattern: ClassVar[SearchPatternLike] = MatchMulti.compile(
        *R_NOTES_CHAPTER_TITLES,
        operator=any,
    )


@dataclass
class CGCascadeChapterLocator(_CascadeChapterLocator):
    """在Corporate Governance Report的章节之后查找"""

    chapter_pattern: ClassVar[SearchPatternLike] = MatchMulti.compile(*R_CG_CHAPTER_TITLES, operator=any)


@dataclass
class ESGCascadeChapterLocator(_CascadeChapterLocator):
    """在ENVIRONMENTAL, SOCIAL AND GOVERNANCE REPORT的章节之后查找"""

    chapter_pattern: ClassVar[SearchPatternLike] = MatchMulti.compile(*R_ESG_CHAPTER_TITLES, operator=any)


@dataclass
class DRCascadeChapterLocator(_CascadeChapterLocator):
    """在Directors' Report章节之后查找"""

    chapter_pattern: ClassVar[SearchPatternLike] = MatchMulti.compile(*R_DR_CHAPTER_TITLES, operator=any)


@dataclass
class NotesCascadeChapterLocator(_CascadeChapterLocator):
    """在Notes章节之后查找"""

    chapter_pattern: ClassVar[SearchPatternLike] = MatchMulti.compile(*R_NOTES_CHAPTER_TITLES, operator=any)


@dataclass
class ScoreLocator(BaseLocator):
    dest: BaseLocator

    def __call__(self, interdoc: InterdocReader, pos: Pos):
        if not pos.elements:
            return Answer()
        return self.dest.__call__(
            interdoc, Pos.from_range(start=pos.elements[0]["index"], end=max(interdoc.__element_map__))
        )


@dataclass
class ScoreStartParaLocator(BaseLocator):
    pattern: SearchPatternLike
    dest: BaseLocator
    index: int = 0

    def __call__(self, interdoc: InterdocReader, pos: Pos) -> Answer:
        if len(pos.elements) != 1:
            return Answer()
        element = pos.elements[self.index]
        if not (element["class"] == "PARAGRAPH" and self.pattern.search(element["text"])):
            return Answer()
        answer = Answer(para_indices=[element["index"]])
        if self.dest:
            dest_answer = self.dest.__call__(interdoc, Pos.from_range(element["index"], interdoc.max_index))
            if not dest_answer:
                return Answer()
            answer.update(dest_answer)
        return answer


@dataclass
class ScoreParaFilter(BaseLocator):
    """
    要用多个元素，在yoda_layer这一层，必须配置 "multi_elements": True，否则只会取第一个预测结果
    """

    pattern: SearchPatternLike = MATCH_ALWAYS
    skip_pattern: SearchPatternLike = field(default_factory=MatchMulti.never)

    def __call__(self, interdoc: InterdocReader, pos: Pos) -> Answer:
        para_indices = [
            elem["index"]
            for elem in pos.elements
            if elem["class"] == "PARAGRAPH"
            and self.pattern.search(elem["text"])
            and not self.skip_pattern.search(elem["text"])
        ]
        return Answer(para_indices=para_indices)


@dataclass
class ScoreDestFilter(BaseLocator):
    """
    在score_filter模型基础上，指定dest提取需要的段落
    注意：在yoda_layer这一层，必须配置 "multi_elements": True，否则只会取到一个段落
    Args:
        multi: 是否取多个结果
        pattern: 匹配上则取该element
        skip_pattern: 匹配上则不取该element
        aim_type: 过滤元素块类型
    """

    multi_elements: ClassVar[bool] = True

    dest: BaseLocator
    multi: bool = False
    pattern: SearchPatternLike = MATCH_ALWAYS
    skip_pattern: SearchPatternLike = field(default_factory=MatchMulti.never)
    aim_type: str = "PARAGRAPH"

    def __call__(self, interdoc: InterdocReader, pos: Pos) -> Answer:
        answer = Answer()
        for element in pos.elements:
            if element["class"] != self.aim_type:
                continue
            text = clean_txt(element.get("text") or "")
            if not text or self.skip_pattern.search(text) or not self.pattern.search(text):
                continue
            answer += self.dest.__call__(interdoc, Pos.from_range(element["index"], element["index"] + 1))
            if answer and not self.multi:
                return answer
        return answer


@dataclass
class ScoreStartDestFilter(BaseLocator):
    """
    在初步定位答案下方找元素块
    注意：在yoda_layer这一层，必须配置 "multi_elements": True，否则只会取到一个段落
    Args:
        dest: 匹配到初步定位元素块之后，后面要处理的逻辑
        multi: 是否取多个结果
        pattern: 匹配上则取该element
        skip_pattern: 匹配上则不取该element
        end_offset: 从初步定位元素块向后找N个元素块
        include_start: 是否包含开始元素块
    """

    multi_elements: ClassVar[bool] = True

    dest: BaseLocator
    multi: bool = False
    pattern: SearchPatternLike = MATCH_ALWAYS
    skip_pattern: SearchPatternLike = field(default_factory=MatchMulti.never)
    end_offset: int = 0
    include_start: bool = False

    def __call__(self, interdoc: InterdocReader, pos: Pos) -> Answer:
        answer = Answer()
        for element in pos.elements:
            elem_idx = element["index"]
            text = clean_txt(element.get("text") or "")
            if not text or self.skip_pattern.search(text) or not self.pattern.search(text):
                continue
            if cur_answer := self.dest.__call__(interdoc, Pos.from_range(elem_idx, elem_idx + self.end_offset + 1)):
                if self.include_start and not any(elem_idx in sen.para_index for sen in cur_answer.sentences):
                    cur_answer = Answer(para_indices=[elem_idx]) + cur_answer
                answer += cur_answer
                if not self.multi:
                    break
        return answer


@dataclass
class BaseScoreFilter(BaseLocator, BindTypeMixin[E], FilterTableMixin):
    """在指定的段落之后查找"""

    multi_elements: ClassVar[bool] = True

    filter: BaseFilter

    def next(self, interdoc, pos) -> Iterable[E]:
        skip_indices = set()
        for index in map(itemgetter("index"), pos.elements):
            elem = interdoc.find_by_index(index)
            if elem in skip_indices:
                continue
            skip_indices.add(elem)
            yield elem

    def assemble(self, interdoc, elements):
        answer = Answer()
        for element in elements:
            if isinstance(element, Para):
                answer.para_indices.append(element.index)
            if isinstance(element, Table):
                regions = self.filter_table(interdoc, element)
                if not regions:
                    answer.append(TableForm(table_index=element.index))
                answer.table_cells.extend(regions)
        return answer


@dataclass
class ScoreElemFilter(BaseScoreFilter[E]):
    multi_elements: ClassVar[bool] = True

    filter: BaseFilter
    size: int = 1

    def __call__(self, interdoc: InterdocReader, pos: Pos) -> Answer:
        elements = []
        for element in self.next(interdoc, pos):
            if not self.filter(interdoc, element):
                continue
            elements.append(element)
            if len(elements) >= self.size:
                break
        return self.assemble(interdoc, elements)


@dataclass
class ScoreContinuedElem(BaseScoreFilter[E]):
    """取初步定位答案的前几个相同类型的元素块, 遇到不匹配的元素则停止"""

    multi_elements: ClassVar[bool] = True

    filter: BaseFilter

    def __call__(self, interdoc: InterdocReader, pos: Pos) -> Answer:
        elements = []
        for element in self.next(interdoc, pos):
            if not self.filter(interdoc, element):
                break
            elements.append(element)
        return self.assemble(interdoc, elements)


@dataclass
class ScoreLensFilter(BaseLocator):
    length: int

    def __call__(self, interdoc: InterdocReader, pos: Pos) -> Answer:
        if len(pos.elements) != self.length:
            return Answer()
        return Answer(
            para_indices=[elem["index"] for elem in pos.elements if elem["class"] == "PARAGRAPH"],
            tables=[TableForm(table_index=elem["index"]) for elem in pos.elements if elem["class"] == "TABLE"],
        )


@dataclass
class ScoreFirstTableLocator(BaseLocator):
    """strict为False时,yoda_layer的multi_elements需要设置为True"""

    dest: BaseLocator
    strict: bool = True  # 是否要求第一个元素必须是table

    def __call__(self, interdoc: InterdocReader, pos: Pos) -> Answer:
        if not pos.elements:
            return Answer()
        if self.strict and is_table_elt(pos.elements[0]):
            table = pos.elements[0]
        elif not self.strict and (tables := [element for element in pos.elements if is_table_elt(element)]):
            table = tables[0]
        else:
            return Answer()

        return self.dest.__call__(interdoc, Pos.from_range(table["index"], interdoc.max_index))


@dataclass
class ScoreFirstElementFilter(BaseLocator, BindTypeMixin[E], FilterTableMixin):
    multi_elements: ClassVar[bool] = True

    filter: BaseFilter
    strict: bool = False  # 是否要求第一个元素必须是table

    def __call__(self, interdoc: InterdocReader, pos: Pos) -> Answer:
        if not pos.elements:
            return Answer()
        if self.strict:
            elem = interdoc.find_by_index(pos.elements[0]["index"])
            if not is_instance_of(elem, self.bind_type):
                return Answer()
        else:
            elems = [
                elem
                for index in map(itemgetter("index"), pos.elements)
                if (elem := interdoc.find_by_index(index)) and is_instance_of(elem, self.bind_type)
            ]
            if not elems:
                return Answer()
            elem = elems[0]
        if not self.filter(interdoc, elem):
            return Answer()
        if isinstance(elem, Para):
            return Answer(para_indices=[elem.index])
        if isinstance(elem, Table):
            regions = self.filter_table(interdoc, elem)
            if not regions:
                return Answer(tables=[TableForm(table_index=elem.index)])
            return Answer(table_cells=regions)
        return Answer()


def sort_by_score(element: ScoreElement):
    return element["score"]


def sort_by_index(element: ScoreElement):
    return element["index"]


@dataclass
class ScoreAnswerLocator(BaseLocator):
    filter: AnswerFilter
    neglect_pattern: SearchPatternLike = MATCH_NEVER
    sorter: Callable[[ScoreElement], Any] = sort_by_score

    def __call__(self, interdoc: InterdocReader, pos: Pos) -> Answer:
        filter_ = deepcopy(self.filter)  # filter会重复执行多次, 每次使用前需要deepcopy,防止answer传递到其他地方
        pos.elements.sort(key=self.sorter)

        answer = filter_.answer
        for elem_ in pos.elements:
            elem = interdoc.find_by_index(elem_["index"])
            if filter_(interdoc, elem):
                if isinstance(elem, Para) and self.neglect_pattern.search(clean_txt(elem.text)):
                    continue
                if isinstance(elem, Table):
                    answer.tables.append(TableForm(table_index=elem_["index"]))
                elif isinstance(elem, Para):
                    answer.para_indices.append(elem_["index"])
        return answer
