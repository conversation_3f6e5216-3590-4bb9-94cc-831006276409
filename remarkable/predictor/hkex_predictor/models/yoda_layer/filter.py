# todo 统一处理关于类型的判断
from __future__ import annotations

from dataclasses import dataclass, field
from typing import Li<PERSON>, <PERSON><PERSON>, final

from typing_extensions import Self

from remarkable.common.common_pattern import P_CONTINUED
from remarkable.common.constants import PDFInsightClassEnum
from remarkable.common.pattern import MATCH_ALWAYS, MATCH_NEVER, MatchMulti
from remarkable.common.protocol import SearchPatternLike
from remarkable.common.util import clean_txt
from remarkable.pdfinsight.interdoc_reader import Cell, Element, InterdocReader, Para, Table, TPara
from remarkable.predictor.hkex_predictor.models.yoda_layer.form import Answer
from remarkable.predictor.hkex_predictor.models.yoda_layer.iterator import ElementIterator


@dataclass
class BaseFilter:
    def __call__(self, interdoc: InterdocReader, elem: Element | TPara | Cell) -> bool:
        raise NotImplementedError

    def __invert__(self):
        return NotFilter(self)

    def __or__(self, other):
        return OrFilter((self, other))

    def or_(self, *others: BaseFilter):
        return OrFilter((self, *others))

    def __and__(self, other):
        return AndFilter((self, other))

    def and_(self, *others: BaseFilter):
        return AndFilter((self, *others))


@dataclass
class NotFilter(BaseFilter):
    filter: BaseFilter

    def __call__(self, interdoc: InterdocReader, elem: Element) -> bool:
        return not self.filter.__call__(interdoc, elem)


@dataclass
class AndFilter(BaseFilter):
    filters: Tuple[BaseFilter, ...] = ()

    @classmethod
    def from_filters(cls, *filters) -> Self:
        return cls(filters)

    def __and__(self, other: BaseFilter) -> Self:
        return AndFilter(self.filters + (other,))

    def __call__(self, interdoc: InterdocReader, elem: Element) -> bool:
        return all(filter_.__call__(interdoc, elem) for filter_ in self.filters)


@dataclass
class OrFilter(BaseFilter):
    filters: Tuple[BaseFilter, ...] = ()

    @classmethod
    def from_filters(cls, *filters) -> Self:
        return cls(filters)

    def __or__(self, other: BaseFilter) -> Self:
        return OrFilter(self.filters + (other,))

    def __call__(self, interdoc: InterdocReader, elem: Element) -> bool:
        return any(filter_.__call__(interdoc, elem) for filter_ in self.filters)


@dataclass
class ParaFilter(BaseFilter):
    @final
    def __call__(self, interdoc: InterdocReader, elem: Element) -> bool:
        return isinstance(elem, Para) and self.__filter__(interdoc, elem)

    def __filter__(self, interdoc: InterdocReader, elem: Element) -> bool:
        raise NotImplementedError

    def __invert__(self):
        return NotFilter(self) & ParaFilter()


@dataclass
class TableFilter(BaseFilter):
    @final
    def __call__(self, interdoc: InterdocReader, elem: Table) -> bool:
        return isinstance(elem, Table) and self.__filter__(interdoc, elem)

    def __filter__(self, interdoc: InterdocReader, elem: Table) -> bool:
        return True

    def __invert__(self):
        return NotFilter(self) & TableFilter()


@dataclass
class TParaFilter(BaseFilter):
    @final
    def __call__(self, interdoc: InterdocReader, elem: TPara) -> bool:
        return isinstance(elem, TPara) and self.__filter__(interdoc, elem)

    def __filter__(self, interdoc: InterdocReader, elem: TPara) -> bool:
        return True

    def __invert__(self):
        return NotFilter(self) & TParaFilter()

    def as_table_filter(self, *types: Literal["row", "col"]):
        return OrFilter.from_filters(TableAsParaFilter(types, self), self)


@dataclass
class CellFilter(BaseFilter):
    @final
    def __call__(self, interdoc: InterdocReader, elem: Cell) -> bool:
        return isinstance(elem, Cell) and self.__filter__(interdoc, elem)

    def __filter__(self, interdoc: InterdocReader, elem: Cell) -> bool:
        return True

    def __invert__(self):
        return NotFilter(self) & CellFilter()


@dataclass
class ParaTextFilter(ParaFilter):
    pattern: SearchPatternLike = MATCH_ALWAYS

    def __filter__(self, interdoc: InterdocReader, elem: Element) -> bool:
        return self.pattern.search(elem.text)


@dataclass
class ChapterFilter(ParaFilter):
    """
    判断当前元素是否为满足pattern/skip_pattern的章节
    Args:
        pattern: 章节标题必须满足的正则
        skip_pattern: 章节标题若满足这个正则，结果为False
    """

    pattern: SearchPatternLike = MATCH_ALWAYS
    skip_pattern: SearchPatternLike = field(default_factory=MatchMulti.never)

    def __filter__(self, interdoc: InterdocReader, elem: Element) -> bool:
        return (
            (elem.index in interdoc.chapter_by_elem or elem.text in interdoc.chapter_titles)
            and self.pattern.search(elem.text)
            and not self.skip_pattern.search(elem.text)
        )


@dataclass
class ElemChapterFilter(BaseFilter):
    """
    判断当前元素所属章节是否满足条件
    Args:
        pattern: 指定的章节，元素在这个章节则返回True
        skip_pattern: 需要跳过的章节，元素在这个章节则返回False
    """

    pattern: SearchPatternLike = MATCH_ALWAYS
    skip_pattern: SearchPatternLike = field(default_factory=MatchMulti.never)

    @final
    def __call__(self, interdoc: InterdocReader, elem: Element) -> bool:
        return self.__filter__(interdoc, elem)

    def __filter__(self, interdoc: InterdocReader, elem: Element) -> bool:
        matched = False
        for node in interdoc.lazy_find_chapters(elem.index):
            title = clean_txt(node.title)
            if self.skip_pattern.search(title):
                return False
            if self.pattern.search(title):
                matched = True
        return matched

    def __invert__(self):
        return NotFilter(self) & ElemChapterFilter()


@dataclass
class InvalidParaFilter(ParaFilter):
    """
    判断元素块类型是否不为PARAGRAPH，或以CONTINUED结尾，或为一级章节标题
    这种元素块在找表格的表名时可以跳过
    Args:
        skip_pattern: 指定元素块不做过滤
    """

    skip_pattern: SearchPatternLike = MATCH_NEVER

    def __filter__(self, interdoc: InterdocReader, elem: Element) -> bool:
        if elem.type != PDFInsightClassEnum.PARAGRAPH.value:
            return True
        text = clean_txt(elem.text, remove_cn_text=True)
        if not text:
            return True
        if self.skip_pattern.search(elem.text):
            return False
        if P_CONTINUED.search(elem.text):
            return True
        if clean_txt(elem.text, remove_cn_text=True) in interdoc.chapter_titles:
            return True
        return False


@dataclass
class AdjacentElemFilter(BaseFilter):
    """
    根据相邻的元素块判断当前元素块是否匹配

    Attributes:
        offset: 相邻元素块的偏移量, 允许负数
        filter: 判断相邻元素块是否匹配的函数
        stop: 满足即止
        skip: 满足skip的元素不计入limit
    """

    offset: int
    filter: BaseFilter
    stop: BaseFilter = field(default_factory=OrFilter)
    skip: BaseFilter = field(default_factory=OrFilter)

    def __call__(self, interdoc: InterdocReader, elem: Element) -> bool:
        return self.__filter__(interdoc, elem)

    def __filter__(self, interdoc: InterdocReader, elem: Element) -> bool:
        limit = abs(self.offset)
        step = 1 if self.offset > 0 else -1
        for elm in ElementIterator[Element](interdoc, start=elem.index + step, step=step):
            if self.skip(interdoc, elm):
                continue
            limit -= 1
            if self.stop(interdoc, elm):
                break
            if self.filter(interdoc, elm):
                return True
            if limit <= 0:
                break
        return False


@dataclass
class ParaLensFilter(ParaFilter):
    ge: int = 0
    le: int = 100_000

    def __filter__(self, interdoc: InterdocReader, elem: Element) -> bool:
        return self.ge <= len(elem.text) <= self.le


@dataclass
class TableRowColFilter(TableFilter):
    pattern: SearchPatternLike
    limit: int = 10
    type: Literal["row", "col"] = "row"

    def __filter__(self, interdoc: InterdocReader, elem: Table) -> bool:
        limit = self.limit
        for para in elem.by_row if self.type == "row" else elem.by_col:
            limit -= 1
            if self.pattern.search(para.text):
                return True
            if limit <= 0:
                break
        return False


@dataclass
class TableTextFilter(TableFilter):
    pattern: SearchPatternLike
    limit: int = 10

    def __filter__(self, interdoc: InterdocReader, elem: Table) -> bool:
        table_text = "\n".join(row.text for row in elem.by_row)
        if self.pattern.search(table_text):
            return True
        return False


@dataclass
class TableRowColValueFilter(TableFilter):
    pattern: SearchPatternLike
    value_pattern: SearchPatternLike
    limit: int = 10
    type: Literal["row", "col"] = "row"

    def __filter__(self, interdoc: InterdocReader, elem: Table) -> bool:
        limit = self.limit
        for para in elem.by_row if self.type == "row" else elem.by_col:
            limit -= 1
            if items := para.text.split("\t"):
                # 拿到第一个不为空的row作为title
                for i, item in enumerate(items):
                    if not item:
                        continue
                    if self.pattern.search(item) and self.value_pattern.search("\t".join(items[i + 1 :])):
                        return True
            if limit <= 0:
                break
        return False


@dataclass
class TableAsParaFilter(TableFilter):
    types: tuple[str, ...] = ("row",)
    filter: TParaFilter = field(default_factory=TParaFilter)

    def __filter__(self, interdoc: InterdocReader, elem: Table) -> bool:
        return any(self.filter(interdoc, para) for type_ in self.types for para in getattr(elem, f"by_{type_}"))


@dataclass
class AnswerFilter(BaseFilter):
    """基于answer对象的过滤基类, answer对象在具体的locator中被不断更新"""

    answer: Answer = field(init=False, default_factory=Answer)


@dataclass
class SameChapterFilter(AnswerFilter):
    def __call__(self, interdoc: InterdocReader, elem: Element) -> bool:
        if not self.answer.para_indices:
            return True
        for prev_elem in ElementIterator[Para](interdoc, start=elem.index, step=-1):
            if chapter := interdoc.chapter_by_elem.get(prev_elem.index):
                return all(para_index in chapter for para_index in self.answer.para_indices) and all(
                    table.table_index in chapter for table in self.answer.tables
                )
        return False


@dataclass
class TParaTextFilter(TParaFilter):
    pattern: SearchPatternLike = MATCH_ALWAYS

    def __filter__(self, interdoc: InterdocReader, elem: TPara) -> bool:
        return self.pattern.search(elem.text)


@dataclass
class CellTextFilter(CellFilter):
    pattern: SearchPatternLike = MATCH_ALWAYS

    def __filter__(self, interdoc: InterdocReader, elem: Cell) -> bool:
        return self.pattern.search(elem.text)
