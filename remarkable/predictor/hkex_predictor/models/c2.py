import logging
import re

from remarkable.common.common import is_para_elt, is_table_elt
from remarkable.common.common_pattern import R_CHAPTER_PREFIX, R_CHAPTER_SUFFIX, R_MIDDLE_DASH
from remarkable.common.constants import AnswerValueEnum, PDFInsightClassEnum
from remarkable.common.pattern import MatchMulti, NeglectPattern, PatternCollection, PositionPattern
from remarkable.common.util import clean_txt, split_paragraph
from remarkable.pdfinsight.reader_util import find_real_syllabus
from remarkable.predictor.common_pattern import R_PERCENT_STRICT
from remarkable.predictor.hkex_predictor.model_util import is_note_element
from remarkable.predictor.hkex_predictor.schemas.pattern import R_NOT, R_SAVE_AS
from remarkable.predictor.models.base_model import BaseModel
from remarkable.predictor.schema_answer import CharR<PERSON>ult, FinalResult, ParagraphResult

R_STRICT_SIGNIFICANT_INVESTMENT = r"(?<!for )(major|significant|material)\s*investments?(?!s?\s*plan)"
# http://100.64.0.105:55647/#/project/remark/409265?treeId=12891&fileId=113561&schemaId=18&projectId=17&schemaKey=C2.1.1
R_SIGNIFICANT_INVESTMENT = r"(major|significant|material)\s*(equity\s*|securities\s*)?investments?(?!s?\s*plan)"
# no significant equity investment: http://100.64.0.105:55647/#/project/remark/408342?treeId=37960&fileId=113459&schemaId=18&projectId=17&schemaKey=C2.1.1
P_EQUITY_SIGNIFICANT_INVESTMENT = MatchMulti.compile(
    rf"(major|significant|material)\s*(non{R_MIDDLE_DASH})?equity\s*investments?(?!s?\s*plan)", operator=any
)
# # code：00291 year:2024
R_SIGNIFICANT_INVESTMENT_CHAPTER = r"\b({}|{}|{})\s*{}s?".format(
    r"\s*".join("major"),
    r"\s*".join("significant"),
    r"\s*".join("material"),
    r"\s*".join("investment"),
)
# http://100.64.0.105:55647/#/project/remark/406169?treeId=45517&fileId=113214&schemaId=18&projectId=45517&schemaKey=C2.5
P_SIGNIFICANT_INVESTMENT_CHAPTER = NeglectPattern.compile(
    match=MatchMulti.compile(
        R_SIGNIFICANT_INVESTMENT_CHAPTER,
        R_SIGNIFICANT_INVESTMENT,
        # stock=00662 year=2024 page=13
        rf"investment.*more\s*than\s*5{R_PERCENT_STRICT}",
        rf"more\s*than\s*5{R_PERCENT_STRICT}.*investment",
        operator=any,
    ),
    # http://100.64.0.105:55647/#/project/remark/410324?treeId=12154&fileId=113681&schemaId=18&projectId=17&schemaKey=C2.1.1&page=379 index=6710
    # http://100.64.0.105:55647/#/project/remark/410315?treeId=11828&fileId=113680&schemaId=18&projectId=17&schemaKey=C2.4&page=17 index=318
    unmatch=rf"{R_CHAPTER_PREFIX}(future|associate|subsidiar)",
)
# 比较严格的NS章节正则
P_C2_STRICT_NS_CHAPTER = MatchMulti.compile(
    rf"{R_CHAPTER_PREFIX}{R_SIGNIFICANT_INVESTMENT_CHAPTER}(\s*h[oe]ld(ing)?s?)?((and|or|[,，])|{R_CHAPTER_SUFFIX})",
    operator=any,
)
P_C2_NO_PLAN = PatternCollection(
    [
        rf"{R_NOT}(any\s*)?(major|significant|material)\s*(equity\s*|securities\s*)?investments?\s*plan",
        rf"{R_NOT}(any\s*)?(future|plans?)\s*.{{,20}}{R_SIGNIFICANT_INVESTMENT}",
    ],
    flags=re.I,
)
# 其他可能的NS章节
# http://100.64.0.105:55647/#/project/remark/409265?treeId=12891&fileId=113561&schemaId=18&projectId=17&schemaKey=C2.1.1
# http://100.64.0.105:55647/#/project/remark/411879?treeId=6352&fileId=113857&schemaId=18&projectId=17&schemaKey=C2.1.1
# http://100.64.0.105:55647/#/project/remark/411879?treeId=6352&fileId=113857&schemaId=18&projectId=17&schemaKey=C2.1.1
P_OTHER_NS_CHAPTER = MatchMulti.compile(
    rf"{R_CHAPTER_PREFIX}analysis\s*([a-z]+\s*)?investments?$",
    P_EQUITY_SIGNIFICANT_INVESTMENT,
    # http://100.64.0.105:55647/#/project/remark/420333?treeId=21418&fileId=114803&schemaId=18&projectId=17&schemaKey=C2.1.1
    rf"{R_CHAPTER_PREFIX}Investments\s*Held{R_CHAPTER_SUFFIX}",
    # http://100.64.0.105:55647/#/project/remark/417076?treeId=44487&fileId=114436&schemaId=18&projectId=17&schemaKey=C2.4
    rf"{R_CHAPTER_PREFIX}(Significant|major|material)\s*Acquisition",
    operator=any,
)
# additional investment: https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7197#note_735961
P_C2_NS_NEGLECT = MatchMulti.compile(
    # http://100.64.0.105:55647/#/project/remark/415342?treeId=5796&fileId=114242&schemaId=18&projectId=17&schemaKey=C2.3
    NeglectPattern.compile(
        match=PositionPattern.compile(R_NOT, r"future|plan"),
        # http://100.64.0.105:55647/#/project/remark/407057?treeId=37677&fileId=113314&schemaId=18&projectId=17&schemaKey=C2.1.1
        unmatch=PositionPattern.compile(R_NOT, R_SIGNIFICANT_INVESTMENT, r"plan"),
    ),
    # http://100.64.0.105:55647/#/project/remark/406490?treeId=13801&fileId=113250&schemaId=18&projectId=17&schemaKey=C2.1.1&page=61 index=554
    PositionPattern.compile(R_NOT, R_SIGNIFICANT_INVESTMENT, rf"\s([6-9]|[1-9]\d)(\.\d+)?{R_PERCENT_STRICT}"),
    # http://100.64.0.105:55647/#/project/remark/418531?treeId=30959&fileId=114599&schemaId=18&projectId=17&schemaKey=C2.1.1&page=23 index=259
    PositionPattern.compile(R_NOT, r"carrying\s*value|change", r"investment"),
    # http://100.64.0.105:55647/#/project/remark/266234?treeId=11828&fileId=70461&schemaId=18&projectId=17&schemaKey=C2.1.1
    # 冲突： http://100.64.0.105:55647/#/project/remark/417578?treeId=7543&fileId=114492&schemaId=18&projectId=17&schemaKey=C2.1.1
    # r"authorised\s*by",
    operator=any,
)
P_C2_NO_OTHER_INVESTMENT = PositionPattern.compile(R_NOT, rf"(other|additional)\s*{R_SIGNIFICANT_INVESTMENT}")
# 明确的NS句子
# http://100.64.0.105:55647/#/project/remark/407057?treeId=37677&fileId=113314&schemaId=18&projectId=17&schemaKey=C2.1.1
P_C2_NS = MatchMulti.compile(
    rf"{R_NOT}{R_STRICT_SIGNIFICANT_INVESTMENT}",
    rf"{R_NOT}(have\s*)?any\s*{R_STRICT_SIGNIFICANT_INVESTMENT}",
    operator=any,
)
P_NO_SIGNIFICANT_INVESTMENT = MatchMulti.compile(
    PositionPattern.compile(R_NOT, R_SIGNIFICANT_INVESTMENT),
    PositionPattern.compile(R_NOT, r"\sany\s*investments?[.\s]*$"),
    # http://100.64.0.105:55647/#/project/remark/295578?treeId=5016&fileId=70923&schemaId=18&projectId=17&schemaKey=C2.7
    # http://100.64.0.105:55647/#/project/remark/265504?treeId=38029&fileId=70315&schemaId=18&projectId=17&schemaKey=C2.7
    PositionPattern.compile(R_NOT, r"(?<!other )investment", rf"\s5{R_PERCENT_STRICT}"),
    operator=any,
)
P_NON_EQUITY_CHAPTER = re.compile(
    rf"(?<!\bfor )(major|significant|material)\s*non{R_MIDDLE_DASH}equity\s*investment(?!s?\s*plan)", re.I
)
P_NO_NON_EQUITY_INVESTMENT = MatchMulti.compile(
    PositionPattern.compile(
        R_NOT, rf"(?<!\bfor )(major|significant|material)\s*non{R_MIDDLE_DASH}equity\s*investment(?!s?\s*plan)"
    ),
    operator=any,
)
R_TOTAL_ASSETS = r"\b((total|gross)\s*(consolidated\s*)?assets)"
# 不超过5%的投资，认为没有重大投资，NS
P_NS_NO_MORE_THAN_5_PERCENT = MatchMulti.compile(
    # http://100.64.0.105:55647/#/project/remark/419198?treeId=12597&fileId=114675&schemaId=18&projectId=17&schemaKey=C2.6
    PositionPattern.compile(
        R_NOT,
        r"investment|individual\s*project",
        rf"(exceeds?|more\s*than|constituting|constitute[sd]?|reache?d?)\s+5(\.0+)?{R_PERCENT_STRICT}",
        R_TOTAL_ASSETS,
    ),
    # http://100.64.0.105:55647/#/project/remark/411099?treeId=9500&fileId=113769&schemaId=18&projectId=17&schemaKey=C2.6
    PositionPattern.compile(
        R_NOT, r"investment", rf"with\s*a\s*value\s*of\s*5(\.0+)?{R_PERCENT_STRICT}", R_TOTAL_ASSETS
    ),
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7436#note_762748
    PositionPattern.compile(
        r"\blargest\s*(investment|exposure\s*of\s*(a\s*)?single)",
        r"\s[0-4](\.\d+)?[%％]\s|(?<!not )less\s*than\s*5(\.0)?[%％]\s",
        r"\bof\b",
        R_TOTAL_ASSETS,
    ),
    operator=any,
)
P_INVESTMENT_RATIO = PatternCollection(
    [
        rf"\s(?P<percent>\d{{1,2}}(\.\d+)?)[%％]\s*\sof\s.*?{R_TOTAL_ASSETS}",
    ],
    flags=re.I,
)
P_C2_NS_SAVE_AS = MatchMulti.compile(
    # save as在NS描述之后，也认为是NS： http://100.64.0.105:55647/#/project/remark/417578?treeId=7543&fileId=114492&schemaId=18&projectId=17&schemaKey=C2.1.1
    NeglectPattern.compile(
        match=R_SAVE_AS,
        unmatch=PositionPattern.compile(R_NOT, R_SIGNIFICANT_INVESTMENT, rf"[,，](\s*and)?\s+{R_SAVE_AS}"),
    ),
    # http://100.64.0.105:55647/#/project/remark/412632?treeId=7144&fileId=113940&schemaId=18&projectId=17&schemaKey=C2.1.1
    MatchMulti.compile(rf"{R_SIGNIFICANT_INVESTMENT}s?", r"follow|below", r"[：:]$", operator=all),
    operator=any,
)


def is_significant_investment_chapter(title):
    """
    章节标题去掉空格后做匹配
    """
    return P_SIGNIFICANT_INVESTMENT_CHAPTER.search(title)


class C2NS(BaseModel):
    def train(self, dataset, **kwargs):
        pass

    def predict_schema_answer(self, elements):
        metadata = self.predictor.prophet.metadata
        if metadata.get("ratio4") is None:
            logging.error(f"attribute ratio4 is missed, file: {self.predictor.file.id}")
        # 所有投资比例<5%，认为没有重大投资，NS
        if all(formula["ratio"] < 0.05 for formula in metadata["ratio4"].formulas):
            answers = []
            for formula in metadata["ratio4"].formulas:
                answer_boxes = []
                for section in formula["sections"]:
                    for item in section["items"]:
                        for box in item.values():
                            if not box or not isinstance(box, dict):
                                # ignore date_var and from attr
                                continue
                            answer_boxes.append({"page": box["page"], "box": box["box"], "text": box["text"]})
                answers.append(FinalResult({"boxes": answer_boxes, "handleType": "wireframe"}))
            answer_result = self.create_result(answers, schema=self.schema, value=AnswerValueEnum.NS.value)
            return [answer_result]
        # 按照正则从严格到宽松的顺序找NS语句
        candidate_elements = []
        for i, p_syllabus in enumerate([P_C2_STRICT_NS_CHAPTER, P_OTHER_NS_CHAPTER, P_SIGNIFICANT_INVESTMENT_CHAPTER]):
            elements, result = self.find_ns_by_syllabus(p_syllabus, metadata, is_strict_chapter=i == 0)
            if result:
                return result
            candidate_elements.extend(elements)
            # 前两个章节正则中的任意一个已经找到，则不再继续向下找
            if elements and i > 1:
                break
        # 追加候选元素块，找不超过5%的段落
        indices = {e["index"] for e in candidate_elements}
        candidate_elements.extend(e for e in metadata.get("c2_candidate_elements", []) if e["index"] not in indices)
        return self.find_no_more_than_5_percent_investment(candidate_elements)

    @staticmethod
    def is_ns_sentence(sentence, check_other=False, pattern=P_NO_SIGNIFICANT_INVESTMENT):
        # 有save as等描述，不可判定为NS
        if P_C2_NS_SAVE_AS.search(sentence):
            return False
        if P_C2_NS.search(sentence):
            # 明确的NS描述
            return True
        # http://100.64.0.105:55647/#/project/remark/295311?treeId=3571&fileId=70889&schemaId=18&projectId=17&schemaKey=C2.1.1
        if check_other and P_C2_NO_OTHER_INVESTMENT.search(sentence):
            return False
        if P_C2_NS_NEGLECT.search(sentence):
            # 排除no plan等描述
            return False
        new_sentence = P_C2_NO_PLAN.sub("", sentence)
        return pattern.search(new_sentence)

    def get_ns_element_result(self, element, check_other=False, ns_pattern=None):
        origin_text, chars = element["text"], element["chars"]
        ns_pattern = ns_pattern or P_NO_SIGNIFICANT_INVESTMENT
        for sentence, pos in split_paragraph(origin_text, need_pos=True):
            sentence = clean_txt(sentence)
            # if P_C2_NS_SAVE_AS.search(sentence):
            #     return None
            if not self.is_ns_sentence(sentence, check_other=check_other, pattern=ns_pattern):
                continue
            start, end = pos
            if start == 0 and end == len(origin_text):
                return ParagraphResult(element, chars)
            return CharResult(element, chars[start:end])
        return None

    def is_ns_equity(self, element):
        """
        有no significant equity investment描述时，如果有non-significant equity investment章节，则该章节下也必须有否定描述
        https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7431#note_739616
        """
        syllabuses = self.pdfinsight.find_syllabuses_by_index(element["index"])
        if not syllabuses:
            return True, []
        cur_syllabus = syllabuses[-1]
        if not P_EQUITY_SIGNIFICANT_INVESTMENT.search(cur_syllabus["title"]):
            return True, []
        next_syllabus = self.pdfinsight_syllabus.syllabus_dict.get(cur_syllabus["index"] + 1)
        if not next_syllabus:
            return True, []
        if not P_NON_EQUITY_CHAPTER.search(next_syllabus["title"]):
            return True, []
        _, next_element = self.pdfinsight.find_element_by_index(next_syllabus["range"][0] + 1)
        if ns_result := self.get_ns_element_result(
            next_element, check_other=True, ns_pattern=P_NO_NON_EQUITY_INVESTMENT
        ):
            return True, [ns_result]
        return False, []

    def find_ns_by_syllabus(self, p_syllabus, metadata, is_strict_chapter=False):
        all_elements = []
        # 在significant investment章节下，寻找“没有重大投资”的描述，如果找到，则认为没有重大投资，NS
        for syllabus in self.pdfinsight_syllabus.syllabuses:
            if not p_syllabus.search(syllabus["title"]):
                continue
            syllabus = find_real_syllabus(self.pdfinsight, syllabus)
            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6684#note_692543
            # 错误识别为page_footer: http://100.64.0.105:55647/#/project/remark/405228?treeId=7535&fileId=113105&schemaId=18&projectId=17&schemaKey=C2.1.1
            syllabus_elements = self.get_candidate_elements_by_range(
                syllabus["range"],
                aim_types={PDFInsightClassEnum.PARAGRAPH.value, PDFInsightClassEnum.PAGE_FOOTER.value},
                need_score=False,
            )
            if not syllabus_elements:
                continue
            all_elements.extend(syllabus_elements)
            # save as的描述在第二句，则不判断save as
            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7315#note_735822
            # http://100.64.0.105:55647/#/project/remark/406169?treeId=45517&fileId=113214&schemaId=18&projectId=17&schemaKey=C2.3
            # http://100.64.0.105:55647/#/project/remark/406607?treeId=20844&fileId=113263&schemaId=18&projectId=17&schemaKey=C2.3
            first_element = syllabus_elements[0]
            sentences = list(split_paragraph(first_element["text"], need_pos=True))
            first_sentence, (start, end) = sentences[0]
            if self.is_ns_sentence(first_sentence):
                is_ns, non_equity_result = self.is_ns_equity(first_element)
                if not is_ns:
                    return all_elements, []
                return all_elements, [
                    self.create_result(
                        [CharResult(first_element, first_element["chars"][start:end]), *non_equity_result],
                        schema=self.schema,
                        value=AnswerValueEnum.NS.value,
                    )
                ]
            # 严格的重大投资章节下有表格或者有子章节，则认为不应该为NS
            if is_strict_chapter and (syllabus["children"] or any(is_table_elt(e) for e in syllabus_elements)):
                return all_elements, []
            for element in syllabus_elements:
                element_result = self.get_ns_element_result(element, check_other=len(syllabus_elements) > 1)
                if not element_result:
                    continue
                is_ns, non_equity_result = self.is_ns_equity(element)
                if not is_ns:
                    return all_elements, []
                return all_elements, [
                    self.create_result(
                        [element_result, *non_equity_result], schema=self.schema, value=AnswerValueEnum.NS.value
                    )
                ]
        # TODO 识别问题： http://100.64.0.105:55647/#/project/remark/293850?treeId=7899&fileId=70689&schemaId=18&projectId=7899&schemaKey=C2.7
        return all_elements, []

    def find_no_more_than_5_percent_investment(self, candidate_elements):
        # 在年报中披露了最大投资的占比比例为小于 5% 的数据，但是没有明确的否定描述，披露 应该是 NS， 合规应该是 C
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6682
        char_results = []
        for element in candidate_elements:
            # http://100.64.0.105:55647/#/project/remark/265839?treeId=3584&fileId=70382&schemaId=18&projectId=17&schemaKey=C2.1.1 index=185
            if (
                not is_para_elt(element)
                or P_C2_NS_SAVE_AS.search(element["text"])
                or is_note_element(self.pdfinsight, element)
            ):
                continue
            for sentence, (start, end) in split_paragraph(element["text"], need_pos=True):
                if P_NS_NO_MORE_THAN_5_PERCENT.search(clean_txt(sentence)):
                    char_results.append(CharResult(element, chars=element["chars"][start:end]))
                    continue
                if matched := P_INVESTMENT_RATIO.nexts(clean_txt(sentence)):
                    percent = matched.group("percent")
                    if float(percent) > 5.0:
                        # 任意一个比例>5%都不能是NS
                        return []
        if not char_results:
            return []
        return [self.create_result(char_results, schema=self.schema, value=AnswerValueEnum.NS.value)]
