import json
import logging
import re

from remarkable.common.constants import AnswerValueEnum
from remarkable.common.pattern import PatternCollection
from remarkable.common.util import clean_txt
from remarkable.pdfinsight.reader import PdfinsightSyllabus
from remarkable.predictor.hkex_predictor.models.policy_esg_base_model import PolicyEsgBaseModel, ResponseModels
from remarkable.predictor.hkex_predictor.models.policy_esg_e1 import prepare_data_prompt
from remarkable.predictor.schema_answer import OutlineResult

logger = logging.getLogger(__name__)
PROMPT = """你是一位专业的ESG数据分析助手。我需要你从ESG文档中提取关于Scope 3温室气体排放的相关内容。

## 提取目标
从文档中识别并提取与Scope 3排放相关的数据，包括：
- Scope 3各个排放类型的具体排放量
- Scope 3的合计排放量

## 关键词参考
- scope 3 greenhouse emissions
- scope 3 GHG emissions
- scope 3: other indirect GHG emissions
- scope 3 emissions inventory
- 价值链排放
- 间接排放

## 分类标准
请将提取结果分为以下两类：
1. **Comply**：文档披露了Scope 3各个排放类型的排放量或合计排放量
   - 若同时披露了Scope 3合计排放量和各类别排放量，请全部提取
   - 若仅披露了各类别排放量或仅有合计排放量，只提取已披露内容
   - 若仅披露total排放量，提取该合计值
   - 排放量是0或者NA或者insignificant或者immaterial类似的描述，认为是Comply

2. **No Disclosure (ND)**：
    - 文档未披露Scope 3各个排放类型的排放量或合计排放量
    - `– 或 / 或 not disclosed`类似的描述
"""


def prepare_scope3_data_prompt(embedding_elements, pdfinsight):
    if not embedding_elements:
        return [], {}
    embedding_elements.sort(key=lambda x: x.get("element_index", x.get("index")))
    data_prompt = prepare_data_prompt(embedding_elements, pdfinsight)
    embedding_element_map = {f"{i['index']}_{i['element_sequence']}": i for i in data_prompt}
    return data_prompt, embedding_element_map


class PolicyEsgE7(PolicyEsgBaseModel):
    def train(self, dataset, **kwargs):
        pass

    @property
    def regs(self):
        return PatternCollection(self.get_config("regs", []), re.I)

    @property
    def neglect_regs(self):
        return PatternCollection(self.get_config("neglect_regs", []), re.I)

    def predict_schema_answer(self, elements):
        result = self.process_shape(elements)
        if not result:
            result = self.process_by_llm()
        return result

    def process_by_llm(self):
        embedding_elements = self.predictor.prophet.metadata[self.schema.name]
        data_prompt, embedding_element_map = prepare_scope3_data_prompt(embedding_elements, self.pdfinsight)
        messages = [
            {
                "role": "system",
                "content": PROMPT,
            },
            {"role": "user", "content": json.dumps(data_prompt, ensure_ascii=False)},
        ]
        response_model: ResponseModels = self.predict_results_by_llm(
            messages, ResponseModels, elements_mapping=embedding_element_map
        )
        if not response_model:
            return []
        logger.info(f"llm_res: {response_model.enum}")
        if not response_model.items:
            return []
        if response_model.enum == "No Disclosure":
            return []
        element_results = []
        processed_elements = set()
        for item in response_model.items:
            embedding_element = embedding_element_map.get(f"{item.index}_{item.element_sequence}")
            if not embedding_element:
                continue
            if item.index in processed_elements:
                continue
            _, element = self.pdfinsight.find_element_by_index(embedding_element["index"])
            if not element:
                continue
            page_box = PdfinsightSyllabus.elements_outline(
                [element],
                pdfinsight=self.pdfinsight,
                ignore_column_position=True,
            )
            text = clean_txt(embedding_element.get("text"), remove_blank=self.remove_blank)
            element_results.append(
                OutlineResult(page_box=page_box, text=text, element=element, origin_elements=[element])
            )
            processed_elements.add(item.index)
        if not element_results:
            return []
        answer_result = self.create_result(
            element_results,
            column=self.predictor.schema.name,
            value=AnswerValueEnum.COMPLY,
        )
        return [answer_result]

    def process_shape(self, elements):
        element_results = []
        for element in elements:
            if element["class"] not in {"IMAGE", "SHAPE", "INFOGRAPHIC"}:
                continue
            shape_title = element.get("title") or ""
            shape_title = clean_txt(shape_title, remove_blank=self.remove_blank)
            if self.neglect_regs and self.neglect_regs.nexts(shape_title):
                continue
            if not (self.regs and self.regs.nexts(shape_title)):
                continue
            page_box = PdfinsightSyllabus.elements_outline([element], self.pdfinsight, ignore_column_position=True)
            if not page_box:
                continue
            if page_box[-1]["text"] == "":
                page_box[-1]["text"] = shape_title
            element_results.append(
                OutlineResult(page_box=page_box, text=shape_title, element=element, origin_elements=elements)
            )
        if not element_results:
            return []
        answer_result = self.create_result(
            element_results,
            column=self.predictor.schema.name,
            value=AnswerValueEnum.COMPLY,
        )
        return [answer_result]
