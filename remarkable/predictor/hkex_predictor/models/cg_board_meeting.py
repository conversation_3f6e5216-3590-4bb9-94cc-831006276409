import re
from typing import List

from remarkable.common.constants import TableType
from remarkable.common.pattern import Match<PERSON><PERSON><PERSON>, PatternCollection
from remarkable.common.util import clean_txt
from remarkable.pdfinsight.parser import parse_table
from remarkable.predictor.dataset import DatasetItem
from remarkable.predictor.models.base_model import BaseModel
from remarkable.predictor.schema_answer import TableResult

P_ATTEND_MEETING = MatchMulti.compile(
    r"members|meeting",
    "attended",
    operator=all,
)

P_BOARD = PatternCollection(
    ["Board", "董事會|董事会"],
    flags=re.I,
)

P_COMMITTEE = MatchMulti.compile(
    "Audit Committee",
    "Nomination Committee",
    "Remuneration Committee",
    "Corporate Governance Committee",
    "審核委員會|审核委员会",
    "提名委員會|提名委员会",
    "薪酬委員會|薪酬委员会",
    "企業管治委員會|企业管治委员会",
    operator=any,
)


class CGBoardMeeting(BaseModel):
    def train(self, dataset: List[DatasetItem], **kwargs):
        pass

    def extract_feature(self, elements, answer):
        pass

    def predict_schema_answer(self, elements):
        answer_results = []
        for element in elements:
            if element["class"] != "TABLE":
                # 段落的情况遇到了再补充
                continue
            table = parse_table(element, tabletype=TableType.TUPLE.value, pdfinsight_reader=self.pdfinsight)
            if len(table.rows) < 2 or len(table.cols) < 2:
                continue
            # 第一行 匹配 attended meeting memebers等关键词
            first_row_text = clean_txt("".join([cell.text for cell in table.rows[0]]))
            first_col_text = clean_txt("".join([cell.text for cell in table.cols[0]]))
            if not P_ATTEND_MEETING.search(first_row_text) or not P_ATTEND_MEETING.search(first_col_text):
                continue
            # 第二行 董事会 和 其他几个委员会中的任意一个
            second_row_text = clean_txt("".join([cell.text for cell in table.rows[1]]))
            second_col_text = clean_txt("".join([cell.text for cell in table.cols[1]]))
            if not P_BOARD.nexts(second_row_text) or not P_BOARD.nexts(second_col_text):
                continue
            if not P_COMMITTEE.search(second_row_text) or not P_COMMITTEE.search(second_col_text):
                continue
            answer_result = self.create_result([TableResult(element, parsed_table=table)], column=self.schema.name)
            answer_results.append(answer_result)

        return answer_results
