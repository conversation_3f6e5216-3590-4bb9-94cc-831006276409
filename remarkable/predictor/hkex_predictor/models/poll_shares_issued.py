import re

from remarkable.common.pattern import PatternCollection
from remarkable.common.util import clean_txt, split_paragraph
from remarkable.predictor.common_pattern import R_PERCENT_STRICT
from remarkable.predictor.dataset import DatasetItem
from remarkable.predictor.hkex_predictor.pattern import R_BE
from remarkable.predictor.hkex_predictor.schemas.pattern import R_SHARE_CLASS
from remarkable.predictor.models.base_model import BaseModel
from remarkable.predictor.schema_answer import CellCharResult, CharResult

# 注意不忽略大小写
P_SHARE_CLASS = PatternCollection(
    [
        rf"\s(?P<share_class>{R_SHARE_CLASS})\s*(?i:shares)\b",
        r"^\s*(?P<share_class>(?i:shares))\b",
        r"\bof\s*(the\s*)?(?P<share_class>(issued|ordinary)\s*(?i:shares?))\b",
        r"\b(?P<share_class>existing.*?issued shares?( capital)?)\b",
        r"(?P<share_class>aggregate\s*nominal\s*amount\s*of.*?share)",
        r"\bof\s*(the\s*)?(?P<share_class>share.*?in\s*issue)",
    ]
)
R_NUM = r"([(（][a-z\d][)）]\s)"
R_NON_ISSUE = r"(?!\s?((shares\s)?repurchase|were\streasury|shares\swere\streasury))"


class PollSharesIssued(BaseModel):
    CLASS_REG_MAP = {
        "Percentage": PatternCollection(
            [
                rf"(^|\s)(?P<value>\d{{1,2}}{R_PERCENT_STRICT})\s*of\s*the\s*(?P<class>{R_SHARE_CLASS}\s*shares)",
                rf"(^|\s)(?P<value>\d{{1,2}}{R_PERCENT_STRICT})\s*of\s(?P<class>the\s(total|aggregate)\snumber\sof\s(the\s*)?shares?.*?in\sissue)",
            ],
            re.I,
        ),
        "Total number of issued shares": PatternCollection(
            [
                # total 之后有明确的类型需要取具体的类型
                rf"total.*?Shares[^\d.]+{R_BE}\s(?P<value>[\d,，]+)\s(class\s)?(?P<class>{R_SHARE_CLASS}\s*(shares)?)",
                # total
                rf"the\s(?P<class>total.*?Shares)[^\d.]+{R_BE}\s(?P<value>[\d,，]+)",
                rf"the\snumber\sof\s(?P<class>issued\sShares)[^\d.,]+{R_BE}\s(?P<value>[\d,，]+)",
                r"company\s*(has|have|had)\s(?P<value>[\d,，]+)\s*(?P<class>shares?\sin\sissue)",
                # by class
                rf"(including|compris(ing|es?)|consisting\sof)\s{R_NUM}?(?P<value>[\d,，]+)\s((shares?\s)?{R_BE}\s)?(class\s)?(?P<class>{R_SHARE_CLASS}\s*(shares)?)\b{R_NON_ISSUE}",
                rf"(which|and)\s{R_NUM}?(?P<value>[\d,，]+)\s((shares?\s)?{R_BE}\s)?(class\s)?(?P<class>{R_SHARE_CLASS}\s*(shares)?)\b{R_NON_ISSUE}",
            ],
            re.I,
        ),
        "Number of treasury shares": PatternCollection(
            [
                rf"(?P<value>[\d,，]+)\s*(?P<class>{R_SHARE_CLASS}\s*Shares)\s*{R_BE}\s*treasury\s*share",
                rf"(?P<value>[\d,，]+)\s*(?P<class>{R_SHARE_CLASS}\s*Shares)\s*[^\d.,]+\bas treasury share",
            ],
            re.I,
        ),
    }

    @property
    def models(self):
        return self.get_config("models") or []

    def train(self, dataset: list[DatasetItem], **kwargs):
        pass

    def predict(self, elements, sort_by_score=True):
        ret = []
        for model_config in self.models:
            model = self.predictor.create_model(model_config, self.schema)
            if model.name == "special_cells":
                elements = self.base_filter_elements(self.get_special_elements("TABLE"))
            else:
                elements = self.base_filter_elements(self.get_special_elements("PARAGRAPH"))
            model_answers = model.predict_schema_answer(elements)
            ret.extend(self.collect_schema_answer(model_answers))
        return ret

    def collect_schema_answer(self, model_answers):
        self.columns = ["Value"]
        # schema_name = self.schema.parent.name
        value_answers = self.get_common_predictor_results(self.get_value_answer(model_answers))
        if not value_answers:
            return []
        ret = []
        for value_answer in value_answers:
            answer_group = self.get_share_class_answer(value_answer)
            ret.extend(answer_group)
        return ret

    def get_value_answer(self, answer_results):
        ret = []
        for answer_result in answer_results:
            if isinstance(answer_result, dict):
                for key, items in answer_result.items():
                    for item in items:
                        if key == "Value":
                            ret.append(item)
            else:
                return [self.create_result(answer_result.element_results, column="Value")]
        return ret

    def get_share_class_answer(self, value_answer):
        # elt_result = value_answer.element_results[0]
        answer_group = []
        for elt_result in value_answer.element_results:
            group = []
            if isinstance(elt_result, CellCharResult):
                raw_cell = elt_result.parsed_cells[0].raw_cell
                split_ans = self.split_class_answer(elt_result, raw_cell["text"])
                group = [
                    {
                        "Value": [
                            self.create_result(
                                [
                                    CellCharResult(
                                        element=elt_result.element,
                                        chars=raw_cell["chars"][i[1]["start"] : i[1]["end"]],
                                        cells=elt_result.parsed_cells,
                                        display_text=i[1]["text"],
                                        start=i[1]["start"],
                                        end=i[1]["end"],
                                    )
                                ],
                                column="Value",
                            )
                        ],
                        "Relevant share class": [
                            self.create_result(
                                [
                                    CellCharResult(
                                        element=elt_result.element,
                                        chars=raw_cell["chars"][i[0]["start"] : i[0]["end"]],
                                        cells=elt_result.parsed_cells,
                                        display_text=i[0]["text"],
                                        start=i[0]["start"],
                                        end=i[0]["end"],
                                    )
                                ],
                                column="Relevant share class",
                            )
                        ],
                    }
                    for i in split_ans
                ]
            elif isinstance(elt_result, CharResult):
                sen_range = None
                for _, pos in split_paragraph(elt_result.element["text"], need_pos=True):
                    if elt_result.start >= pos[0] and elt_result.end <= pos[1]:
                        sen_range = pos
                split_ans = self.split_class_answer(elt_result, elt_result.element["text"], sen_range=sen_range)

                group = [
                    {
                        "Relevant share class": [
                            self.create_result(
                                [
                                    CharResult(
                                        element=elt_result.element,
                                        chars=elt_result.element["chars"][i[0]["start"] : i[0]["end"]],
                                        display_text=i[0]["text"],
                                        start=i[0]["start"],
                                        end=i[0]["end"],
                                    )
                                ],
                                column="Relevant share class",
                            )
                        ],
                        "Value": [
                            self.create_result(
                                [
                                    CharResult(
                                        element=elt_result.element,
                                        chars=elt_result.element["chars"][i[1]["start"] : i[1]["end"]],
                                        display_text=i[1]["text"],
                                        start=i[1]["start"],
                                        end=i[1]["end"],
                                    )
                                ],
                                column="Value",
                            )
                        ],
                    }
                    for i in split_ans
                ]
            answer_group.extend(group)
        return answer_group

    @classmethod
    def get_display_text(cls, elt_result, origin_text):
        start, end = elt_result.start, elt_result.end
        # words_before = origin_text[:start]
        text_after = origin_text[end:]
        class_mat = []
        for matched in P_SHARE_CLASS.finditer(text_after):
            # if matched := P_SHARE_CLASS.nexts(text_after):
            class_mat.append((clean_txt(matched.group("share_class")), [end + i for i in matched.span("share_class")]))
        return class_mat or [("Total", (start, end))]

    def split_class_answer(self, elt_result, text, sen_range=None):
        res = []
        if value_class_reg := self.CLASS_REG_MAP.get(self.schema.parent.name):
            found_value = set()
            for per_matched in value_class_reg.finditer(text):
                if sen_range and (per_matched.start() < sen_range[0] or per_matched.end() > sen_range[1]):
                    continue
                if (per_matched.start("value"), per_matched.end("value")) in found_value:
                    continue
                res.append(
                    (
                        {
                            "text": per_matched.group("class"),
                            "start": per_matched.start("class"),
                            "end": per_matched.end("class"),
                        },
                        {
                            "text": per_matched.group("value"),
                            "start": per_matched.start("value"),
                            "end": per_matched.end("value"),
                        },
                    )
                )
                found_value.add((per_matched.start("value"), per_matched.end("value")))
        if not res:
            for display_text, pos in self.get_display_text(elt_result, text):
                # display_text, pos = self.get_display_text(elt_result, text)
                res.append(
                    (
                        {
                            "text": display_text,
                            "start": pos[0],
                            "end": pos[1],
                        },
                        {
                            "text": elt_result.text,
                            "start": elt_result.start,
                            "end": elt_result.end,
                        },
                    )
                )

        return res
