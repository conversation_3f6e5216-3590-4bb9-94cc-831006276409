from typing import List

from remarkable.common.constants import AnswerValueEnum
from remarkable.common.pattern import NeglectPattern
from remarkable.predictor.dataset import DatasetItem
from remarkable.predictor.models.base_model import BaseModel
from remarkable.predictor.schema_answer import OutlineResult

P_ND_B81 = NeglectPattern.compile(
    match=r"Directors’ and Chief Executives’ Interest",
    unmatch=r"award|option",
)


class NoShare(BaseModel):
    """
    针对规则B74~B97
    metadata["share_info"]["no_option_element_idx"] >=0 或 metadata["share_info"]["no_award_element_idx"] >=0 时，直接生成答案
    """

    @property
    def with_element_box(self):
        """
        是否需要框选元素块，默认不框选
        """
        return self.get_config("with_element_box", True)

    @property
    def need_award_option_word(self):
        """
        是否需要披露award option 关键词
        """
        return self.get_config("need_award_option_word", False)

    def train(self, dataset: List[DatasetItem], **kwargs):
        pass

    @property
    def share_type(self):
        share_type = self.get_config("share_type")
        if not share_type and self.predictor.models:
            share_type = self.predictor.models[0].config.get("share_type")
        if not share_type:
            raise ValueError("`share_type` must be configured in model `group_based` or `no_share`!")
        if share_type not in ["option", "award"]:
            raise ValueError(f"`share_type` should be `option` or `award`, but got `{share_type}`")
        return share_type

    def predict_schema_answer(self, elements):
        metadata = self.predictor.prophet.metadata
        if not metadata or not metadata.get("share_info"):
            return []
        ns_element_idx = metadata["share_info"][f"no_{self.share_type}_element_idx"]
        if ns_element_idx < 0:
            return []
        _, element = self.pdfinsight.find_element_by_index(ns_element_idx)
        if not element:
            return []
        if not self.with_element_box:
            return [self.create_result([], schema=self.schema, value=self.enum or AnswerValueEnum.NS.value)]
        if self.need_award_option_word and P_ND_B81.search(element.get("text", "")):
            return []
        page_box = self.pdfinsight_syllabus.elements_outline([element])
        if not page_box:
            return []
        return [
            self.create_result(
                [OutlineResult(page_box=page_box, element=element, origin_elements=[element])],
                schema=self.schema,
                value=self.enum or AnswerValueEnum.NS.value,
            )
        ]
