import re

from remarkable.common.common_pattern import P_SEN_SEPARATOR
from remarkable.common.pattern import (
    MatchMulti,
    NeglectPattern,
    PositionPattern,
    SplitBeforeMatch,
)
from remarkable.common.util import clean_txt
from remarkable.predictor.hkex_predictor.schemas.cg_schema.cg_a import (
    R_LIST_A_C_AS_FOLLOWS,
    R_LIST_A_C_COMPLIED,
    R_LIST_A_C_DEVIATE,
    R_TUPLE_A_C_CHAIRMAN_IS_CEO,
)
from remarkable.predictor.hkex_predictor.schemas.cg_schema.cg_ac import (
    P_AC_NO_MEETING,
    R_E_DI_3,
)
from remarkable.predictor.hkex_predictor.schemas.cg_schema.cg_b import P_INED_INDEPENDENCE_RULE, R_HOLD_POSITION
from remarkable.predictor.hkex_predictor.schemas.cg_schema.cg_f import R_FA_COMPLY, R_SECRETARY
from remarkable.predictor.hkex_predictor.schemas.cg_schema.cg_i import (
    P_IB_NOTE,
    P_NOAUDIT_SERVICE_INCLUDE,
    P_NOAUDIT_SERVICES,
    R_IB_NA,
    R_NOAUDIT,
)
from remarkable.predictor.hkex_predictor.schemas.cg_schema.cg_j import (
    P_ACHIEVED,
    P_J_B_ALREADY_COMPLIANCE,
    P_J_B_GENDER_PLANS,
    P_J_B_MEASURE,
    P_J_B_NO_PLANS,
    P_J_B_OBJECTIVES,
    R_ACHIEVED,
    R_J_B_OBJECTIVES,
)
from remarkable.predictor.hkex_predictor.schemas.cg_schema.common_models import (
    P_EB_COMPLY,
    P_NAME,
    R_CHAIR,
    R_EXECUTIVE,
)
from remarkable.predictor.hkex_predictor.schemas.pattern import (
    R_APPELLATION,
    R_INED,
    R_NOT_SEGREGATED_CEO_CHAIRMAN,
    reg_words,
)
from remarkable.predictor.predictor import JudgeByRegex
from remarkable.predictor.utils import GPTDictator

R_EN_NUM = (
    "zero",
    "one",
    "two",
    "three",
    "four",
    "five",
    "six",
    "seven",
    "eight",
    "nine",
    "ten",
)


class CGEnumChecker(JudgeByRegex):
    from remarkable.predictor.hkex_predictor.schemas.pattern import R_NULL_VALUE

    col_patterns = {
        "A(a)-Explanation of application of code principles": {
            "No Disclosure": [r"listed on the New York Stock Exchange"],
            "Comply": [r".*"],
        },
        "A(b)-Compliance with CPs": {
            "Comply": [r".*"],
        },
        "A(c)-Deviation from Code Provisions": {
            "Not Applicable": [
                # r"fully\s*complied\s*with",
                NeglectPattern.compile(
                    match=MatchMulti.compile(*R_LIST_A_C_COMPLIED, operator=any),
                    unmatch=MatchMulti.compile(
                        *R_LIST_A_C_DEVIATE,
                        *[as_follow + ".+" for as_follow in R_LIST_A_C_AS_FOLLOWS],
                        *R_TUPLE_A_C_CHAIRMAN_IS_CEO,
                        *R_NOT_SEGREGATED_CEO_CHAIRMAN,
                        operator=any,
                    ),
                ),
                # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/3735#note_443328
                MatchMulti.compile(r"complied\s*with", r"\bno\s*(material\s*)?deviation", operator=all),
                MatchMulti.compile(
                    r"complied.*?to the extent applicable and permissible",
                    r"complied with (the provisions? (of)?\s?)?(\s?contained in )?the (CG|Corporate\s*Governance) Code\.$",
                    r"^There is no material deviations?",
                    r"did not have any significant deviation",
                    operator=any,
                ),
            ],
            # "Comply": [
            # CEO和chairman不是同一个人
            # MatchMulti.compile(
            #     *R_NOT_SEGREGATED_CEO_CHAIRMAN,
            #     operator=any,
            # ),
            #     NeglectPattern.compile(
            #         MatchMulti.compile(*R_LIST_A_C_DEVIATE, operator=any),
            #         MatchMulti.compile(*R_LIST_A_C_ALL_COMPILED, operator=any),
            #     ),
            #     r"deviate|deviation|save\s*for|except for|C.2.1",
            #     r"not\s*separated\s*the\s*roles",
            # ],
            "Comply": [r".+"],
        },
        "B(a)-Board Composition by category of directors": {
            "Comply": MatchMulti.compile(rf"{R_CHAIR}|{R_HOLD_POSITION}", R_EXECUTIVE, P_NAME, operator=all),
            "No Disclosure": [r".*"],
        },
        "B(b)-No. of board meetings": {
            "Comply": [r".*"],
        },
        "B(c)-Attendance of directors at board and general meetings": {
            "Comply": [
                MatchMulti.compile(
                    "board",
                    r"audit",
                    r"nomination",
                    r"remuneration",
                    r"annual",
                    r"risk management",
                    r"committee",
                    operator=all,
                ),
                MatchMulti.compile(
                    r"\bno\s*agm\b",
                    r"board\s*meeting",
                    operator=all,
                ),
                MatchMulti.compile("board", "general", "director", "meeting", operator=all),
            ],
        },
        "B(d)-Number of board or committee meetings attended": {
            "Comply": [r".*"],
        },
        "B(e)-Statement of responsibilities of board and management": {
            "Comply": [r".*"],
        },
        "B(f)-Details of non-compliance with rules 3.10 and 3.10A and remedial steps": {
            "Comply": [
                # re.compile(r"\s*".join(["(?:" + "|".join(R_EN_NUM[:3]) + ")", R_INED])),
                MatchMulti.compile(
                    r"in non-compliance with the requirements under Rule 5.05\(1\)",
                    r"details of non-compliance of the Listing Rules",
                    r"failed to comply with the requirements of Rules",
                    operator=any,
                ),
                PositionPattern.compile(
                    r"fail(ed)? to meet",
                    r"\bactions?\b",
                    r"(comply|complied) with",
                ),
            ],
            "Not Applicable": [
                re.compile(
                    r"\s*".join(["(?:" + "|".join(R_EN_NUM[3:]) + ")", "(are)?", R_INED]),
                    re.I,
                ),
                MatchMulti.compile(
                    R_INED,
                    r"least\sthree",
                    operator=all,
                ),
                MatchMulti.compile(
                    r"compli",
                    r"\b3.10\b",
                    r"\b3.10A\b",
                    operator=all,
                ),
                MatchMulti.compile(
                    r"compli",
                    r"3.10\(1\)\s*and\s*\(2\)",
                    operator=all,
                    flag=0,
                ),
                re.compile(rf"no\s*{R_INED}", re.I),
            ],
        },
        "B(g)-Why INED is independent if Rule 3.13 is not complied": {
            "Comply": [
                SplitBeforeMatch.compile(
                    MatchMulti.compile(
                        rf"(\bno\b|\bnot\b|without)\s*{reg_words(0, 5)}(compli|fulfill|\bmee?t\b)",
                        P_INED_INDEPENDENCE_RULE,
                        operator=all,
                    ),
                    separator=P_SEN_SEPARATOR,
                    operator=any,
                ),
            ],
            "Not Applicable": [r".*"],
        },
        "B(h)-Relationship between board members and between chairman and CEO": {
            "Comply": [r".*"],
        },
        "B(i)-Directors' compliance with C.1.4": {
            "Comply": [r".*"],
        },
        "C-Identity of chairman and CEO": {
            "Comply": [
                re.compile(
                    r"(?:(?:Chairlady|Chairwoman|Chairman|chairmen|Chief Executive(\sOfficer)?|Managing Director|general "
                    r"manager|G?CEO).*){2}",
                    re.I,
                )
            ],
        },
        # "D-Term of appointment of NED": GPTDictator(
        #     ps=AnswerValueEnum.ND,
        #     ns=AnswerValueEnum.PS,
        #     nd=AnswerValueEnum.NA,
        #     rule="没有披露Non-executive Directors任期的相关内容，或者仅披露董事几年轮换一次。",
        # ),
        "D-Term of appointment of NED": {
            "Comply": [r".*"],
            # "No Disclosure": [MatchMulti.compile(r"None of", r"without", r"\bnot?\b", operator=any)],
        },
        "RC-E(a)-Role and function": {
            "Comply": [r".*"],
        },
        "RC-E(b)-Composition": {
            "Comply": P_EB_COMPLY,
            "No Disclosure": [r".*"],
        },
        "RC-E(c)-Number of meetings and record of attendance": {
            "Comply": [r".*"],
        },
        "E(d)(ii)-1-Policy for remuneration of executive directors": {
            "Comply": [r".*"],
        },
        "E(d)(ii)-2-Assessing performance of executive directors": {
            "Comply": [r".*"],
        },
        "E(d)(ii)-3-Approving terms of directors' service contracts": {
            "Comply": [r".*"],
        },
        "E(d)(ii)-4-Reviewing and or approving share scheme matters": {
            "Not Applicable": MatchMulti.compile(
                r"\b(no|neither|nor)\s*((material\s*)?matter|share|meeting)", operator=any
            ),
            "Comply": [r".*"],
        },
        "E(d)(ii)-5-Disclose which of the models in E.1.2(c) adopted": {
            "Comply": [r".*"],
        },
        "NC-E(a)-Role and function": {
            "Comply": [r".*"],
        },
        "NC-E(b)-Composition": {
            "Comply": P_EB_COMPLY,
            "No Disclosure": [r".*"],
        },
        "NC-E(c)-Number of meetings and record of attendance": {
            "Comply": [r".*"],
        },
        "E(d)(iii)-Policy for directors nomination": {
            "Comply": [r".*"],
        },
        "AC-E(a)-Role and function": {
            "Comply": [r".*"],
        },
        "AC-E(b)-Composition": {
            "Comply": P_EB_COMPLY,
            "No Disclosure": [r".*"],
        },
        "AC-E(c)-Number of meetings and record of attendance": {
            "Comply": [r".*"],
        },
        "E(d)(i)-Review of financial reports": {
            "Comply": NeglectPattern(match=re.compile(r".+"), unmatch=P_AC_NO_MEETING),
            "No Disclosure": [r".*"],
        },
        "E(d)(i)-1-Review of risk management and internal control system": {
            "Comply": [r".*"],
        },
        "E(d)(i)-2-Effectiveness of internal audit function": {
            "Comply": [r".*"],
        },
        "E(d)(i)-3-Non-compliance with Rule 3.21 and remedial steps": {
            "Comply": R_E_DI_3,
            "Not Applicable": [r".*"],
        },
        "RiC-E(a)-Role and function": {
            "Comply": [r".*"],
        },
        "RiC-E(b)-Composition": {
            "Comply": P_EB_COMPLY,
            "Not Applicable": [r".*"],
        },
        "RiC-E(c)-Number of meetings and record of attendance": {
            "Comply": ["r.+"],
        },
        "E(d)(iv)-1-How responsibility met in review of risk management and internal control": {
            "Comply": [r".*"],
        },
        "E(d)(iv)-2-Effectiveness of issuer's internal audit function": {
            "Comply": [r".*"],
        },
        "CG-E(a)-Role and function": {
            "Comply": [r".*"],
        },
        "CG-E(b)-Composition": {
            "Comply": MatchMulti.compile(rf"{R_CHAIR}|{R_HOLD_POSITION}", R_EXECUTIVE, P_NAME, operator=all),
            "Not Applicable": [r".*"],
        },
        "CG-E(c)-Number of meetings and record of attendance": {
            "Comply": [r".*"],
        },
        "E(d)(v)-1-Determine issuer's CG policy": {
            "Comply": [r".*"],
        },
        "E(d)(v)-2-Duties performed by the board or the committee under CP A.2.1": {
            "Comply": [r".*"],
        },
        "F(a)-Primary contact person if external service provider is engaged": {
            "Comply": [
                MatchMulti.compile(
                    *R_FA_COMPLY,
                    operator=any,
                ),
            ],
            "Not Applicable": [
                NeglectPattern.compile(
                    match=MatchMulti.compile(
                        r"brief biographical detail".replace(" ", r"\s*"),
                        r"employee\s*of\s*(the)?\s*company",
                        rf"appointed\s*as\s*the\s*{R_SECRETARY}",
                        MatchMulti.compile(
                            rf"{R_SECRETARY}\s*of",
                            R_APPELLATION,
                            operator=all,
                        ),
                        rf"the\s*{R_SECRETARY}\s*{R_APPELLATION}",
                        operator=any,
                    ),
                    unmatch=MatchMulti.compile(
                        r"primary\s*contact|external|joint", r"(main|principal) contact person", operator=any
                    ),
                )
            ],
        },
        "F(b)-Details of non-compliance with rule 3.29": {
            "Not Applicable": [r".*"],
            # "Comply": [r".*"],
        },
        "G(a)-Adoption of model code": {
            "Comply": [r".*"],
        },
        "G(b)-Directors' compliance with model code": {
            "Comply": [r".*"],
        },
        "G(c)-Details of non-compliance and remedial steps": {
            "Not Applicable": [r".*"],
            # "Comply": [r".*"],
        },
        "H(a)-Whether issuer has internal audit function": {
            "Comply": [r".*"],
        },
        "H(b)-Frequency of review and the period covered": {
            # "No Disclosure": [
            #     NeglectPattern.compile(
            #         r"\b(often|regular(ly)?|from\s*time\s*to\s*time)\b",
            #         r"annual\s*review|annually|every\s*year|yearly|\d+\s*times|every\s*\w{1,6}\s*(?:months|days)|once\s*a\s*year|assess",
            #     ),
            # ],
            "Comply": [r".*"],
        },
        "H(c)-Whether issuer considers them effective and adequate": {
            "Comply": [r".*"],
        },
        "I(a)Analysis of remuneration of audit and non-audit services": {
            "Comply": [MatchMulti.compile(r"non-audit", r"non-auditing", r"non audit services", operator=any)],
        },
        "I(b)-Details of nature of significant non-audit services and fees paid": {
            "Not Applicable": [
                NeglectPattern.compile(
                    match=MatchMulti.compile(
                        r"no\s*(remuneration|fees\s*were\s*paid)\s*for\s*non-audit\s*services",
                        r"(non-audit services?|非(審核|核數)(服務|費用)|non-audit\s*fees?|professional services|Other services)[\|\s]?[-—－–]",
                        r"(non-audit services?|非(審核|核數)(服務|費用)|non-audit\s*fees?|professional services|Other services)\s(0|Nil無?)",
                        r"no\s*non-audit\s*service\s*was\s*rendered",
                        r"nil\s*for\s*non-audit\s*services",
                        r"no\s*other\s*service\s*provided",
                        r"no\s*non-audit\s*service\s*assignment\s*provided",
                        rf"tax\s*filing\s*services\){R_NULL_VALUE}",
                        r"not\s*provide\s*any\s*other\s*non-audit\s*services",
                        r"not\s*engaged\s*in\s*any\s*non-audit\s*services",
                        r"no\s*non-audit\s*service\s*fees",
                        rf"non-audit\s*services\s*provided .*?{R_NULL_VALUE}",
                        rf"non\s*audit\s*servicesHK\$\s*{R_NULL_VALUE}",
                        r"none\s*of.*?provision\s*of\s*non-audit\s*services",
                        r"did\s*not\s*provide\s*any\s*non-audit\s*services",
                        r"no\s*non-?audit\s*services?",
                        rf"{R_NOAUDIT}\s*[:：]\s*{R_NULL_VALUE}",
                        MatchMulti.compile(
                            *R_IB_NA,
                            operator=any,
                        ),
                        operator=any,
                    ),
                    unmatch=MatchMulti.compile(
                        P_NOAUDIT_SERVICES,
                        P_NOAUDIT_SERVICE_INCLUDE,
                        P_IB_NOTE,
                        operator=any,
                    ),
                )
            ],
            "Comply": [
                MatchMulti.compile(
                    # 非审计服务
                    MatchMulti.compile(r"non-audit", r"non-auditing", r"non audit services", operator=any),
                    # 非审计服务包含明细
                    SplitBeforeMatch.compile(
                        MatchMulti.compile(P_NOAUDIT_SERVICES, P_NOAUDIT_SERVICE_INCLUDE, operator=any),
                        separator=P_SEN_SEPARATOR,
                    ),
                    operator=all,
                ),
                MatchMulti.compile(
                    # 审计服务
                    MatchMulti.compile(r"Audit services", operator=any),
                    # 非审计服务的具体内容
                    MatchMulti.compile(
                        r"Taxation services",
                        r"Review of continuing connected transactions",
                        operator=any,
                    ),
                    operator=all,
                ),
            ],
            "No Disclosure": [r".*"],
        },
        "J(a)-Board diversity policy": {
            "Comply": [r".*"],
        },
        "J(b)-Numerical targets and timelines and measures adopted": {
            "Comply": [
                # NeglectPattern.compile(
                #     MatchMulti.compile(P_J_B_GENDER_PLANS, P_J_B_OBJECTIVES, operator=any), P_ACHIEVED
                # ),
                P_J_B_MEASURE,
                NeglectPattern.compile(
                    match=P_J_B_GENDER_PLANS,
                    unmatch=P_J_B_ALREADY_COMPLIANCE,
                ),
                # 已满足如果在后面，则为NA，否则为Comply
                NeglectPattern(
                    match=P_J_B_OBJECTIVES,
                    unmatch=MatchMulti.compile(
                        PositionPattern.compile(
                            r"|".join(R_J_B_OBJECTIVES),
                            r"|".join(R_ACHIEVED),
                            ignore=r"[\r\n]",
                        ),
                        P_J_B_ALREADY_COMPLIANCE,
                        operator=any,
                    ),
                ),
            ],
            "Not Applicable": [P_ACHIEVED, P_J_B_NO_PLANS],
        },
        "J(c)-Gender ratios in the workforce and gender diversity objectives": {
            # 在提取的过程中严格限制，提取到文本之后应该都是Comply
            "Comply": [r".*"],
            # "Comply": MatchMulti.compile(
            #     R_STAFF, MatchMulti.compile(*R_J_C_GENDER_DATA, operator=any), P_J_C_TARGETS, operator=any
            # ),
            # "No Disclosure": [r".*"],
        },
        "K(a)-How to convene EGM": {
            "No Disclosure": [
                r"not obliged by the Companies Act to call shareholders’ annual general meetings",
            ],
            "Comply": [r".*"],
        },
        "K(b)-Enquiry Procedure and contact details": {
            "Comply": [r".*"],
        },
        "K(c)-Procedure to put forward proposal in shareholders' meetings": {
            "Comply": [r".*"],
        },
        "L(a)-Significant changes in constitutional documents": {
            "Comply": [
                r"amendment",
                r"\sno.*?change",
            ],
        },
        "L(b)-Shareholders' communication policy": {
            "Comply": [r".*"],
        },
        "L(c)-Annual review on effectiveness of communication policy": {
            "Comply": [r".*"],
        },
    }

    def predict(self, predictor_result, schema, metadata=None):
        if (pattern := self.col_patterns.get(schema.name)) and isinstance(pattern, GPTDictator):
            return pattern.judge(clean_txt(predictor_result.text, remove_blank=self.remove_blank))
        return super().predict(predictor_result, schema, metadata)
