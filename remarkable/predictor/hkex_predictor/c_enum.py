from remarkable.common.constants import AnswerValueEnum
from remarkable.common.pattern import <PERSON><PERSON><PERSON><PERSON>, PatternCollection
from remarkable.common.util import clean_txt
from remarkable.predictor.hkex_predictor.models.c5 import P_NS, P_NS_WITHOUT_CONTINUE, P_PS, P_PS_CONFIRMED, R_CCT
from remarkable.predictor.models.base_model import BaseModel
from remarkable.predictor.predictor import EnumPredictor


class RuleCChecker(EnumPredictor):
    col_patterns = {
        "CCT Confirmed with Auditors": {
            AnswerValueEnum.PS.value: [P_PS],
            AnswerValueEnum.ND.value: [
                MatchMulti.compile(rf"related party transactions.*did not {R_CCT}", operator=any),
            ],
            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/3621#note_435643
            AnswerValueEnum.NS.value: [
                MatchMulti.compile(P_NS, P_NS_WITHOUT_CONTINUE, operator=any)
                # NeglectPattern.compile(
                #     match=MatchMulti.compile(P_NS, P_NS_WITHOUT_CONTINUE, operator=any),
                #     unmatch=P_SAVE_AS,
                # )
            ],
        },
    }

    def __init__(self, remove_blank=False):
        self.remove_blank = remove_blank

    def predict(self, predictor_result, schema, metadata=None):
        schema_name = schema.parent.name if schema.name == "Content" else schema.name
        col_patterns = self.col_patterns.get(schema_name, {})
        if not col_patterns:
            return AnswerValueEnum.ND.value
        elements = BaseModel.get_elements_from_answer_result([predictor_result])
        enums = []
        for element in elements:
            if element["class"] != "PARAGRAPH":
                continue
            clean_text = clean_txt(element["text"])
            for col, patterns in col_patterns.items():
                pattern = PatternCollection(patterns)
                for sentence in clean_text.split("."):
                    if not sentence:
                        continue
                    if pattern.nexts(sentence):
                        enums.append(col)
        if enums:
            all_element_text = "\n".join([element.get("text") for element in elements])
            if AnswerValueEnum.PS.value in enums and P_PS_CONFIRMED.search(all_element_text):
                return AnswerValueEnum.PS.value
            return enums[0]
        return AnswerValueEnum.PS.value  # 默认返回PS
