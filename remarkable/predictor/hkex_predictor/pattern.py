from __future__ import annotations

import re

from remarkable.common.common_pattern import (
    R_CI_CHAPTER_TITLES,
    R_ESG_CHAPTER_TITLES,
    R_MIDDLE_DASHES,
)
from remarkable.common.pattern import PatternCollection
from remarkable.predictor.common_pattern import R_EN_MONTH

R_FOLLOW = r"follow\w+.?$|below.?$|following\s*table|were$|[:：]$|table\s*below"
R_PARA_START = r"^([•]|\([a-z]\)|\(?\d|\((i{,3}|iv|vi{0,3}|ix|xi?)\))"

R_BE = r"(be|been|is|are|was|were)"
R_SIM_MONTH = r"(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|S[ae]p|Oct|Nov|Dec)"
R_EN_DAY = r"\b\d{1,2}(st|nd|rd|th)?"
# R_MONTH = rf"({R_SIM_MONTH})[a-z]{{0,6}}"
R_DAY = rf"\b({R_EN_DAY}[-/\s]*{R_EN_MONTH}[,，.]?[-/\s]*(\d{{4}}|\d{{2}})|{R_EN_MONTH}[-/\s]*{R_EN_DAY}[,，.]?[-/\s]*(\d{{4}}|\d{{2}}))\b"
R_NUM_DAYS = [
    r"\b\d{1,2}[-/.]\d{1,2}[-/.]20\d{2}\b",
    r"\b20\d{2}[-/.]\d{1,2}[-/.]\d{1,2}\b",
    r"\d{4}\s*年\s*\d{1,2}\s*月\s*\d{1,2}\s*日",
]
R_DATES_FOR_EXTRACT = [
    rf"(\s|^)(?P<day>[1-9]|[1-2]\d|3[01])(st|nd|rd|th)?\s*(?P<mon>{R_SIM_MONTH})[a-z]{{0,6}}[,，]?\s*(?P<year>\d{{4}})\b",
    rf"\b(?P<mon>{R_SIM_MONTH})[a-z]{{0,6}}\s*(?P<day>[1-9]|[1-2]\d|3[01])(st|nd|rd|th)?[,，]?\s*(?P<year>\d{{4}})\b",
]
R_MONTH_FOR_EXTRACT = rf"\b(?P<mon>{R_SIM_MONTH})[a-z]{{0,6}}[,，]?\s*(?P<year>\d{{4}})\b"

R_RC_CHAPTER_TITLES = [
    r"^Remuneration\s?Committee\s?Report",
    r"^Remuneration\s?Committee$",
    r"remuneration and monitoring committee,",
]

R_NC_CHAPTER_TITLES = [
    r"Nomination\s*Committee",
    r"Nomination and Remuneration Committee",
]


# # http://************:55647/#/project/remark/?treeId=2753&fileId=68577&schemaId=15&projectId=17&schemaKey=B76 这个文件的share信息在RC章节
# P_SHARE_ROOT_CHAPTERS = PatternCollection(
#     [*R_DR_CHAPTER_TITLES, *R_NOTES_CHAPTER_TITLES, *R_OI_CHAPTER_TITLES, *R_RC_CHAPTER_TITLES], flags=re.I
# )

# list rule 提取分组关键词时，需要忽略的章节正则
# http://************:55647/#/project/remark/251242?treeId=8654&fileId=68663&schemaId=15&projectId=17&schemaKey=B93.2
P_LIST_RULE_IGNORE_CHAPTERS = PatternCollection(
    [
        *R_CI_CHAPTER_TITLES,
        # 个别情况下要取CG章节：https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/4696#note_527216
        # *R_CG_CHAPTER_TITLES,
        *R_ESG_CHAPTER_TITLES,
        # http://************:55647/#/project/remark/231822?treeId=38106&fileId=66259&schemaId=15&projectId=17&schemaKey=B82
        # r"SUMMARY\s*OF\s*SIGNIFICANT\s*ACCOUNTING\s*POLICIES",
        r"MANAGEMENT\s*DISCUSSION\s*AND\s*ANALYSIS",
        r"RELATIONSHIP",
        r"report\s*of\s*the\s*supervisory\s*committee",
    ],
    flags=re.I,
)
# 挂钩协议章节
P_EQUITY_CHAPTER = re.compile(rf"equity[{R_MIDDLE_DASHES}\s]+linked|agreement|acquire\s*share|debenture", re.I)

# ESG相关
ESG_INDEX_PATTERN = PatternCollection(
    [
        r"Content Index",
        r"guide index",
        r"esg index",
        r"guide general disclosure",
        r"general disclosure",
        r"Operating Practices",
        r"esg indicators",
        r"Subject Areas and Aspects",
        r"主要範疇和層面",
        r"Aspects 層面",
        r"Reporting Guide",
        r"^indicators$",
        r"^Indicator description$",
        r"^Aspects?",
        r"Main Scope, Aspect, KPI",
        r"ABOUT THE REPORT",
        r"Compliance List",
        r"Scope A\. Environment",
        r"^(KPI|page)$",
        r"^subject area$",
        r"^Indicator\s?Content",
        r"Mandatory Disclosure Requirements",
        r"Relevant\s?Disclosure\s?Index",
        r"Index Content",
        r"Mandatory Disclosure\s?強制披露規定",
        r"主要領域、層面、一般披露及關鍵績效指標",
        r"General Disclosure and KPIs",
    ],
    re.I,
)
ESG_INDEX_TITLE_PATTERN = PatternCollection(
    [
        r"Indicator description",
        r"ESG REPORTING GUIDE CONTENT INDEX",
        r"ENVIRONMENTAL TARGETS AND PERFORMANCE",
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/998#note_242283
        r"total of approximately.*?total approximately.*?total participants of approximately",
        r"^CONTENTS$",  # 目录相关
        r"ESG Content Index|環境、社會及管治內容索引",
        r"Content Index",
        r"ESG REPORTING GUIDE INDEX",
        r"General Disclosures and KPIsReferences",
        r"INDEX OF HKEX ESG REPORTING GUIDE",
        r"GRI Standards Index",
        r"Indicator.*?(Chapter|Disclosure).*?Page",
    ],
    re.I,
)
