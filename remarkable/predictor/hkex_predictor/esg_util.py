import re
from dataclasses import dataclass
from typing import Dict, List

from remarkable.common.constants import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TableType
from remarkable.common.pattern import PatternCollection
from remarkable.common.util import clean_txt
from remarkable.pdfinsight.parser import ParsedTable, ParsedTableCell, parse_table
from remarkable.predictor.common_pattern import (
    P_ESG_DIRECT_ANSWER,
    P_ESG_RULE,
    P_ESG_RULE_DESC,
    P_ESG_RULE_PAGE,
    P_ESG_RULE_SYLLABUS,
    P_NOTE,
    P_PAGE,
)
from remarkable.predictor.hkex_predictor.pattern import ESG_INDEX_PATTERN, ESG_INDEX_TITLE_PATTERN

rule_map = {
    "A1 policy": "A1 policies - emissions",
    "A1 law": "law compliance - emissions",
    "A1.1": "KPI A1.1 - emission type and data",
    "A1.2 scope 1": "KPI A1.2 part 1 - Scope 1",
    "A1.2 scope 2": "KPI A1.2 part 2 - Scope 2",
    "A1.2 scope 3": "KPI A1.2 part 3 - Scope 3",
    "A1.3": "KPI A1.3 - hazardous waste",
    "A1.4": "KPI A1.4 - non-hazardous waste",
    "A1.5": "KPI A1.5 - emission target",
    "A1.6 part 1": "KPI A1.6 part 1 - waste handling",
    "A1.6 part 2": "KPI A1.6 part 2 - waste reduction target",
    "A2 policy": "A2 policies - use of resources",
    "A2.1": "KPI A2.1 - energy consumption",
    "A2.2": "KPI A2.2 - water consumption",
    "A2.3": "KPI A2.3 - energy efficiency targets",
    "A2.4 part 1": "KPI A2.4 part 1 - water sourcing",
    "A2.4 part 2": "KPI A2.4 part 2 - water efficiency targets",
    "A2.5": "KPI A2.5 - packaging material",
    "A3 policy": "A3 policies - environment and natural resources",
    "A3.1": "KPI A3.1 - impact on environment and natural resources",
    "A4 policy": "A4 policies - climate-related issues",
    "A4.1": "KPI A4.1 - climate-related issues & impact",
    "B1 policy": "B1 policies - employment",
    "B1 law": "B1 law compliance - employment",
    "B1.1": "KPI B1.1 - workforce by types",
    "B1.2": "KPI B1.2 - employee turnover by types",
    "B2 policy": "B2 policies - health and safety",
    "B2 law": "B2 law compliance - health and safety",
    "B2.1": "KPI B2.1 - work-related fatalities",
    "B2.2": "KPI B2.2 - work injury lost days",
    "B2.3": "KPI B2.3 - health and safety measures",
    "B3 policy": "B3 policies - development and training",
    "B3.1": "KPI B3.1 - percentage of employees trained",
    "B3.2": "KPI B3.2 - training hours completed",
    "B4 policy": "B4 policies - labour standards",
    "B4 law": "B4 law compliance - labour standards",
    "B4.1": "KPI B4.1 - review measures to avoid child & forced labour",
    "B4.2": "KPI B4.2 - steps to avoid child & forced labour",
    "B5 policy": "B5 policies - supply chain",
    "B5.1": "KPI B5.1 - number of suppliers",
    "B5.2": "KPI B5.2 - suppliers engagement",
    "B5.3": "KPI B5.3 - supply chain ESG risks identification",
    "B5.4": "KPI B5.4 - practice to promote environmentally preferable products",
    "B6 policy": "B6 policies - product responsibility",
    "B6 law": "B6 law compliance - product responsibility",
    "B6.1": "KPI B6.1 - products recall",
    "B6.2": "KPI B6.2 - products related complaints",
    "B6.3": "KPI B6.3 - IP rights protection",
    "B6.4": "KPI B6.4 - quality assurance process",
    "B6.5": "KPI B6.5 - consumer data protection",
    "B7 policy": "B7 policies - anti-corruption",
    "B7 law": "B7 law compliance - anti-corruption",
    "B7.1": "KPI B7.1 - legal cases on corruption",
    "B7.2": "KPI B7.2 - preventive measures & whistle-blowing procedures",
    "B7.3": "KPI B7.3 - anti-corruption training",
    "B8 policy": "B8 policies - community investment",
    "B8.1": "KPI B8.1 - community investment focus",
    "B8.2": "KPI B8.2 - resources contributed",
}

REVERED_MAP = {
    "A1 policies - emissions": "A1 policy",
    "law compliance - emissions": "A1 law",
    "KPI A1.1 - emission type and data": "A1.1",
    "KPI A1.2 part 1 - Scope 1": "A1.2",
    "KPI A1.2 part 2 - Scope 2": "A1.2",
    "KPI A1.2 part 3 - Scope 3": "A1.2",
    "KPI A1.3 - hazardous waste": "A1.3",
    "KPI A1.4 - non-hazardous waste": "A1.4",
    "KPI A1.5 - emission target": "A1.5",
    "KPI A1.6 part 1 - waste handling": "A1.6",
    "KPI A1.6 part 2 - waste reduction target": "A1.6",
    "A2 policies - use of resources": "A2 policy",
    "KPI A2.1 - energy consumption": "A2.1",
    "KPI A2.2 - water consumption": "A2.2",
    "KPI A2.3 - energy efficiency targets": "A2.3",
    "KPI A2.4 part 1 - water sourcing": "A2.4",
    "KPI A2.4 part 2 - water efficiency targets": "A2.4",
    "KPI A2.5 - packaging material": "A2.5",
    "A3 policies - environment and natural resources": "A3 policy",
    "KPI A3.1 - impact on environment and natural resources": "A3.1",
    "A4 policies - climate-related issues": "A4 policy",
    "KPI A4.1 - climate-related issues & impact": "A4.1",
    "B1 policies - employment": "B1 policy",
    "B1 law compliance - employment": "B1 law",
    "KPI B1.1 - workforce by types": "B1.1",
    "KPI B1.2 - employee turnover by types": "B1.2",
    "B2 policies - health and safety": "B2 policy",
    "B2 law compliance - health and safety": "B2 law",
    "KPI B2.1 - work-related fatalities": "B2.1",
    "KPI B2.2 - work injury lost days": "B2.2",
    "KPI B2.3 - health and safety measures": "B2.3",
    "B3 policies - development and training": "B3 policy",
    "KPI B3.1 - percentage of employees trained": "B3.1",
    "KPI B3.2 - training hours completed": "B3.2",
    "B4 policies - labour standards": "B4 policy",
    "B4 law compliance - labour standards": "B4 law",
    "KPI B4.1 - review measures to avoid child & forced labour": "B4.1",
    "KPI B4.2 - steps to avoid child & forced labour": "B4.2",
    "B5 policies - supply chain": "B5 policy",
    "KPI B5.1 - number of suppliers": "B5.1",
    "KPI B5.2 - suppliers engagement": "B5.2",
    "KPI B5.3 - supply chain ESG risks identification": "B5.3",
    "KPI B5.4 - practice to promote environmentally preferable products": "B5.4",
    "B6 policies - product responsibility": "B6 policy",
    "B6 law compliance - product responsibility": "B6 law",
    "KPI B6.1 - products recall": "B6.1",
    "KPI B6.2 - products related complaints": "B6.2",
    "KPI B6.3 - IP rights protection": "B6.3",
    "KPI B6.4 - quality assurance process": "B6.4",
    "KPI B6.5 - consumer data protection": "B6.5",
    "B7 policies - anti-corruption": "B7 policy",
    "B7 law compliance - anti-corruption": "B7 law",
    "KPI B7.1 - legal cases on corruption": "B7.1",
    "KPI B7.2 - preventive measures & whistle-blowing procedures": "B7.2",
    "KPI B7.3 - anti-corruption training": "B7.3",
    "B8 policies - community investment": "B8 policy",
    "KPI B8.1 - community investment focus": "B8.1",
    "KPI B8.2 - resources contributed": "B8.2",
}

P_NEED_JUDGE_AGAIN = PatternCollection(
    [
        r"N/A",
        r"not available",
        r"Not applicable",
    ],
    re.I,
)


@dataclass
class EsgRuleInfo:
    name: str
    rule_cell: ParsedTableCell | None = None
    desc_cell: ParsedTableCell | None = None
    syllabus_cell: ParsedTableCell | None = None
    page_cell: ParsedTableCell | None = None
    direct_answer_cell: ParsedTableCell | None = None
    foot_notes: List[Dict] | None = None

    def __repr__(self):
        return f"{self.name}: {self.rule_cell.text}"

    def need_ignore_index_table_answer(self) -> bool:
        """
        如果有page_cell 或者 syllabus_cess 则返回True
        如果 direct_answer_cell 有值则返回False
        :return:
        """
        return bool(self.page_cell or self.syllabus_cell)

    def explain_cell(self):
        # 忽略page_cell中的N/A
        if self.direct_answer_cell:
            return self.direct_answer_cell
        if self.syllabus_cell:
            if P_ESG_DIRECT_ANSWER.nexts(clean_txt(self.syllabus_cell.text)):
                return self.syllabus_cell
        return None


def is_index_table(
    table: ParsedTable,
    addition_table_cell_pattern: PatternCollection | None = None,
    addition_table_title_pattern: PatternCollection | None = None,
) -> bool:
    """
    判断是否是Index表格
    :param table:
    :return:
    """
    element = table.element
    table_titles = [element["title"] or ""]
    if table.title:
        table_titles.append(table.title.title_text)
    if any(ESG_INDEX_TITLE_PATTERN.nexts(i) for i in table_titles):
        return True
    if addition_table_title_pattern and any(addition_table_title_pattern.nexts(i) for i in table_titles):
        return True
    for row in table.rows[:2]:
        if any(ESG_INDEX_PATTERN.nexts(clean_txt(cell.text)) for cell in row):
            return True
        if addition_table_cell_pattern and any(addition_table_cell_pattern.nexts(clean_txt(cell.text)) for cell in row):
            return True
    return False


def judge_explain_again(matcher, predictor_result):
    if not P_NEED_JUDGE_AGAIN.nexts(matcher.re.pattern):
        return AnswerValueEnum.EXPLAIN.value
    for ele in predictor_result.relative_elements:
        if ele.get("class") == "TABLE":
            table = parse_table(ele, tabletype=TableType.TUPLE)
            if res := parse_from_table(table):
                return res
    return AnswerValueEnum.EXPLAIN.value


def parse_from_table(table):
    dataline_idx = [i for i, tag in enumerate(table.row_tags) if tag == "dataline"]
    na_count, num_count = 0, 0
    for idx, row in enumerate(table.rows):
        if idx not in dataline_idx:
            continue
        has_na = any(P_NEED_JUDGE_AGAIN.nexts(clean_txt(cell.text)) for cell in row)
        has_num = any(re.search(r"\d+", clean_txt(cell.text)) for cell in row)
        if has_num:
            num_count += 1
        if has_na:
            na_count += 1
        if has_na and has_num:
            return AnswerValueEnum.COMPLY.value
    if num_count > 0 and num_count / (num_count + na_count) > 0.8:
        return AnswerValueEnum.COMPLY.value
    return AnswerValueEnum.EXPLAIN.value


def get_all_index_table(pdfinsight):
    index_tables = []
    for index in pdfinsight.table_dict:
        ele_type, element = pdfinsight.find_element_by_index(index)
        if ele_type != "TABLE":
            continue
        table = parse_table(element, tabletype=TableType.TUPLE, pdfinsight_reader=pdfinsight)
        if is_index_table(table):
            index_tables.append(table)
    return index_tables


def get_esg_rule_info(pdfinsight):
    index_tables = get_all_index_table(pdfinsight)
    esg_rules = {}
    for table in index_tables:
        for row in table.rows:
            rule = None
            desc_cell = None
            syllabus_cell = None
            page_cell = None
            direct_answer_cell = None
            rule_cell = None
            notes_elements = []
            for cell in row[:3]:
                if matcher := P_ESG_RULE.nexts(clean_txt(cell.text)):
                    rule = matcher.group()
                    rule_cell = cell
                    break
            if not rule_cell:
                continue
            for cell in row[rule_cell.colidx + 1 :]:
                clean_cell_text = clean_txt(cell.text)
                for possible_header in table.cols[cell.colidx][:3]:
                    possible_header_text = clean_txt(clean_txt(possible_header.text))
                    if P_ESG_RULE_DESC.nexts(possible_header_text):
                        desc_cell = cell
                        break
                    if P_ESG_RULE_SYLLABUS.nexts(possible_header_text):
                        syllabus_cell = cell
                        if P_PAGE.nexts(clean_cell_text):
                            # 类似于 Relevant Sections in the Report 的表头下面描述是页码
                            # 01181|2021
                            syllabus_cell = None
                            page_cell = cell
                        break
                    if P_ESG_RULE_PAGE.nexts(possible_header_text) and P_PAGE.nexts(clean_cell_text):
                        page_cell = cell
                        break
                    if P_ESG_DIRECT_ANSWER.nexts(clean_cell_text):
                        if rule.startswith("A"):
                            direct_answer_cell = cell
                        break
                if P_NOTE.nexts(clean_cell_text):
                    notes_elements = table.footnotes

            if not syllabus_cell and not page_cell:
                syllabus_cell = find_default_syllabus(row, rule_cell)
            esg_rule = EsgRuleInfo(
                name=rule,
                rule_cell=rule_cell,
                desc_cell=desc_cell,
                syllabus_cell=syllabus_cell,
                page_cell=page_cell,
                direct_answer_cell=direct_answer_cell,
                foot_notes=notes_elements,
            )
            esg_rules[rule] = esg_rule

    return esg_rules


def find_default_syllabus(row, rule_cell):
    # 倒序寻找 找到第一个最有可能的章节单元格
    for cell in row[rule_cell.colidx + 1 :][::-1]:
        if not cell.text:
            continue
        if cell.text.count("\n") > 8:
            continue
        if P_ESG_DIRECT_ANSWER.nexts(cell.text):
            continue
        return cell
    return None
