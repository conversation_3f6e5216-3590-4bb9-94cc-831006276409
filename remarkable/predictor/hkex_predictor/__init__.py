# -*- coding: utf-8 -*-
import json
import logging
import re

from remarkable.common.common import JURA21_COLS, is_paragraph_elt, is_table_elt
from remarkable.common.constants import AnswerValueEnum, CGEnum, ESGEnum, PolicyEsgRules, QuarterReportEnum, SchemaName
from remarkable.common.pattern import PatternCollection
from remarkable.common.util import clean_txt
from remarkable.data.answer_tools import copy_special_answer
from remarkable.models.mold import special_mold
from remarkable.models.new_mold import NewMold
from remarkable.predictor.base_prophet import BaseProphet
from remarkable.predictor.hkex_predictor.agm_enum import AGMResultEnumChecker
from remarkable.predictor.hkex_predictor.c_enum import <PERSON><PERSON>he<PERSON>
from remarkable.predictor.hkex_predictor.cg_enum import CGEnumChecker
from remarkable.predictor.hkex_predictor.esg_util import judge_explain_again
from remarkable.predictor.hkex_predictor.list_rule_enum import ListRuleEnumChecker
from remarkable.predictor.hkex_predictor.lrs_enum import LRsEnumChecker
from remarkable.predictor.hkex_predictor.poll_result_enum import <PERSON><PERSON><PERSON>ult<PERSON><PERSON><PERSON>he<PERSON>
from remarkable.predictor.mold_schema import SchemaItem
from remarkable.predictor.predictor import JudgeByRegex
from remarkable.predictor.schema_answer import PredictorResult
from remarkable.predictor.utils import GPTDictator

logger = logging.getLogger(__name__)

NS_PATTERN = r"(no|not)(?![tfe])"
asset_account_has_liabilities = PatternCollection(
    [
        r"^(non-)?Current assets and liabilities$",  # 42827
    ],
    re.I,
)

Y_ND_COLS = [
    "MDR 13 i - board oversight",
    "MDR 13 ii - board management approach",
    "MDR 13 iii - board progress review",
    "MDR 14 part 1 - materiality application",
    "MDR 14 part 2 - quantitative application",
    "MDR 14 part 3 - consistency application",
    "MDR 15 - reporting boundary",
]

P_DISCLOSURE18_SEGMENT = PatternCollection(r"segment", re.I)


class MyDict(dict):
    def get(self, key, default=None):
        if key in self:
            return self[key]
        default = {"Negative Statement": [NS_PATTERN], "No Disclosure": [r"^\s*$"], "Disclosure": [r".*"]}
        self[key] = default
        return default


class DisclosureEnum(JudgeByRegex):
    col_patterns = {
        "Modified opinion": {
            "Disclosure": [
                re.compile(
                    r"modified|disclaimer of opinion|qualified opinion|adverse|emphasis of matter|material uncertainty on going concern",
                    re.I | re.X,
                )
            ],
            "No Disclosure": [r"^\s*$"],
        },
        "Potential fraud internal control breakdown": {
            "Disclosure": [
                re.compile(
                    r"frauds|forensic investigation|loss in accounting records|internal control deficiencies",
                    re.I | re.X,
                )
            ],
            "No Disclosure": [r"^\s*$"],
        },
        "Potential Legal Issue": {
            "Disclosure": GPTDictator(
                rule="To assist our consideration regarding any trading halt implication/delay in publication of potential inside information, AI would red-flag the relevant location in the results announcement that indicates potential legal issues by performing key word search, e.g. contingent liabilities, litigations/legal proceedings, court order, etc."
            ),
            # 'Disclosure': [
            #     re.compile(r'litigations?|legal|proceedings?|court|claim|petition|summon|write', re.I | re.X)
            # ],
            # 'No Disclosure': [r'^\s*$'],
            "priority_to_disclose": True,
        },
        "Reviewed by AC": {
            "Disclosure": [r"auditor|review|agree|audited|reviewed|non-executive|non-independent"],
            "Negative Statement": [NS_PATTERN],
            "No Disclosure": [r"^\s*$"],
            "priority_to_disclose": True,
            "priority_ns_pattern": [r"not review"],
        },
        "Statement of profit or loss": {
            "Disclosure": [
                re.compile(
                    r"(CONSO[LI]IDAT[EI]D|STATEMENTS?|PR[O0][HF][TI]T|[IL][O0]SS|OTHER|OTTTDR|OTIDR|OTHD|INCOME|COMPR[EM]]HENSIVE)",
                    re.I | re.X,
                ),
                re.compile(r"(CONSO[li]IDAT[EI]D)? statements? O[fi] pr[o0][fi]it [o0][rf] l[o0]ss", re.I | re.X),
                re.compile(r"(CONSO[li]IDAT[EI]D)? STATEMENTs? O[fi] COMPREHENSIVE INCOME", re.I | re.X),
                re.compile(r"(CONSO[li]IDAT[EI]D)? STATEMENTs? O[fi] PROFIT AND LOSS AND OTHER", re.I | re.X),
                re.compile(
                    r"(CONSO[li]IDAT[EID]D)? STATEMI?ENTs? O[fi] PRO[HF][TI]T [o0][rf] [il][o0]ss AND (OTTTDR|OTIDR|OTHD)",
                    re.I | re.X,
                ),
                re.compile(
                    r"(CONSO[li]IDAT[EI]D)? statements? O[fi] pro[fi]it [o0][rf] l[o0]ss and other (compr[em]]hensive incom[de])?",
                    re.I | re.X,
                ),
                re.compile(r"(CONSO[li]IDAT[EI]D)? STATEMENTs? O[fi] PROFIT", re.I | re.X),
                re.compile(r"(CONSO[LI]IDAT[EI]D)? (STAEMN|STATMDNT) OF COM(PREHN|PRIHDN)SIVE INCOM[DE]", re.I | re.X),
                re.compile(r"INTERIM CONSOLIDATED STATEMENTs? O[rf] INCOME", re.I | re.X),
                re.compile(r"Interim Condes Conslidate Staemn of Profit or Los", re.I | re.X),
                re.compile(r"ProfitorLossandOtherComprehensiveIncome", re.I | re.X),
            ],
            "No Disclosure": [r"^\s*$"],
        },
        "Statement of financial position": {
            "Disclosure": [
                re.compile(r"[FI]inancial Position|Balance Sheet", re.I | re.X),
                re.compile(r"CONDENSED CONSOLIDATED STATEMENT OF [FI]INANCIAL", re.I | re.X),
                re.compile(r"CONSOLIDAT[ED]D STATEMENTO[FE] [FI]INANCIA[IL] POSITION", re.I | re.X),
                re.compile(r"CONSOL[IT]DAT[ED]D STATEMENT O[FE] [FI][IT]NANC[IT]A[IL] POS[IT]{3}ON", re.I | re.X),
                re.compile(r"CONSOL[IT]DAT[ED]D STATEMENT O[FE]", re.I | re.X),
                re.compile(r"[FI][IT]NANC[IT]A[IL] POS[IT]{3}ON", re.I | re.X),
                re.compile(r"staemn", re.I | re.X),
            ],
            "No Disclosure": [r"^\s*$"],
        },
        "Revenue in PL": {
            "Disclosure": [re.compile(r"Revenue|turnover|sales|net sales|segment", re.I | re.X)],
            "No Disclosure": [r"^\s*$"],
        },
        "Note of revenue": {
            "Disclosure": [re.compile(r"Revenue|turnover|sales|net sales|segment", re.I | re.X)],
            "Negative Statement": [NS_PATTERN],
            "No Disclosure": [r"^\s*$"],
            "priority_to_disclose": True,
        },
        "Note of taxation": {
            "Disclosure": [
                re.compile(r"Tax|Taxation", re.I | re.X),
            ],
            "Negative Statement": [rf"{NS_PATTERN}"],
            "No Disclosure": [r"^\s*$"],
            "enum_from_table": True,
            "priority_to_disclose": True,
            "priority_ns_pattern": [
                r"no provision",
                r"no estimated",
            ],
        },
        "Note of EPS": {
            "Disclosure": [re.compile(r"loss|earnings|no (diluted|dilutive)", re.I | re.X)],
            "Negative Statement": [[rf"{NS_PATTERN}|\b[-—－]\b"]],
            "No Disclosure": [r"^\s*$"],
            "enum_from_table": True,
            "priority_to_disclose": True,
        },
        "Note of dividend": {
            "Disclosure": [re.compile(r"dividend|distribution", re.I | re.X)],
            "Negative Statement": [[rf"{NS_PATTERN}|\b[-—－]\b|Neither"]],
            "No Disclosure": [r"^\s*$"],
            "priority_ns_pattern": [
                r"no (to)? interim",
                r"no (to)? dividend",
                r"not (to)? recommend",
                r"not (to)? declare",
                r"no final dividend",
            ],
        },
        "Purchase Sale or Redemption": {
            "Disclosure": [
                re.compile(r"purchase|redemption|sale|dealing|buyback|listed securities", re.I | re.X),
                re.compile(r"other than|save as|save for|except|US\$[\d.]+", re.I | re.X),
            ],
            "Negative Statement": [re.compile(r"(no|not)\b(?![tfe])|either.*?nor", re.I | re.X)],
            "No Disclosure": [r"^\s*$"],
            "neglect_ns_pattern": [
                re.compile(r"other than|save as|save for|except|US\$[\d.]+", re.I | re.X),
            ],
        },
        "CG Code": {
            "Disclosure": [re.compile(r"Appendix 14(MB)|Appendix15(GEM)|Corporate Governance", re.I | re.X)],
            "No Disclosure": [r"^\s*$"],
        },
        "Significant changes in accounting policies": {
            "Negative Statement": [r"\b(not?|unlikely)\b"],  # 优先判NS
            "No Disclosure": [r"^\s*$"],
            "Disclosure": [r".*"],
            # 'Disclosure': [r'adopt|adoption|revised|effect|impact|amend|amendment'],
            "neglect_ns_pattern": [
                re.compile(r"affects|economic", re.I | re.X),
            ],
        },
        "Prior period adjustment": {
            "Disclosure": [re.compile(r"prior year adjustment|prior period adjustment", re.I | re.X)],
            "No Disclosure": [r"^\s*$"],
        },
        "Discontinued operation": {
            "Disclosure": [re.compile(r"discontinued operation", re.I | re.X)],
            "No Disclosure": [r"^\s*$"],
        },
        "Incorporated Place (Hong Kong)": {
            "Disclosure": [re.compile(r"Hong.*?Kong|HK", re.I)],
            "No Disclosure": [r"^\s*$"],
            "Negative Statement": [re.compile(r".*")],
            "priority_to_disclose": True,
        },
        "Section 436(3) of HKCO": {
            "Disclosure": [re.compile(r"section 436", re.I | re.X)],
            "No Disclosure": [r"^\s*$"],
        },
        "Money lending Indent trading or Proprietary securities trading ": {
            "Disclosure": [
                re.compile(r"money lending|indent trading|loan financing|back-to-back", re.I | re.X),
                re.compile(r"securities.*?(trading|investment)", re.I | re.X),
                re.compile(r"Lending business", re.I | re.X),
                re.compile(r"investment.*?securities", re.I | re.X),
                re.compile(r"provision of finance and investments", re.I | re.X),
                re.compile(r"reported to the executive", re.I | re.X),
                re.compile(r"proprietary trading", re.I | re.X),
                re.compile(r"trading of securities", re.I | re.X),
                re.compile(r"treasury activity", re.I | re.X),
            ],
            "No Disclosure": [r"^\s*$"],
        },
        "Comment on segment": {  # 18.1
            "No Disclosure": [r"^\s*$"],
            "Disclosure": [r".*"],
        },
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/592#note_160622
        "Multiple Business Segments": {  # 18.2 原18.1
            "Negative Statement": [
                re.compile(r"Casual.*?operat\w+", re.I | re.X),  # 08232|2022
                re.compile(r"(Only)? (One|single) (One|single)? (Business|operating|reportable)? Segment", re.I | re.X),
                re.compile(r"one operating and reportable segment", re.I | re.X),
                re.compile(r"only entity-wide discussions", re.I | re.X),
                re.compile(r"(One|single)\s?(\w+\s){1,5}segment", re.I | re.X),
            ],
            "Disclosure": [
                re.compile(r"segment|product|service|investment|business|provision", re.I | re.X),
                re.compile(r"trading|work|project|operation|Region|leasing|development|develop|sale", re.I | re.X),
                re.compile(r"Retail|venue", re.I | re.X),
                re.compile(r"revenue analysis by contract type", re.I | re.X),
            ],
            "No Disclosure": [r"^\s*$"],
        },
        "Audited or agreed with the auditors": {
            "Disclosure": GPTDictator(
                rule="The listed issuer's financial statements for the financial year must be agreed with the auditors and contained audited figures."
            ),
            # 'Negative Statement': [NS_PATTERN],
            # 'No Disclosure': [r'^\s*$'],
            "priority_to_disclose": True,
            # 'priority_ns_pattern': [
            #     r'not review',
            #     r'UNAUDITED',
            # ],
        },
    }

    def predict(self, predictor_result, schema, metadata=None):
        default_result = AnswerValueEnum.ND.value  # 默认返回ND
        clean_text_with_blank = clean_txt(predictor_result.text)
        clean_text = clean_txt(predictor_result.text, remove_blank=self.remove_blank)

        col_patterns = self.col_patterns.get(schema.name, {})
        if not col_patterns:
            return default_result
        enum_from_table = col_patterns.get("enum_from_table")
        if enum_from_table:
            table_text = ""
            for element_result in predictor_result.element_results:
                if not hasattr(element_result, "origin_elements"):
                    break
                for element in element_result.origin_elements:
                    if is_paragraph_elt(element, strict=True):
                        table_text += element["text"] + "\n"
                    if is_table_elt(element):
                        for cell in element["cells"].values():
                            table_text += f"{clean_txt(cell['text'])} "
            if table_text:
                clean_text_with_blank = table_text
                clean_text = clean_txt(table_text, remove_blank=self.remove_blank)

        # 优先在原文中判断是否是NS
        priority_ns_pattern = col_patterns.get("priority_ns_pattern")
        if priority_ns_pattern and any(
            (
                PatternCollection(priority_ns_pattern, re.I).nexts(clean_text_with_blank),
                PatternCollection(priority_ns_pattern, re.I | re.X).nexts(clean_text),
            )
        ):
            return AnswerValueEnum.NS.value
        ns_pattern = col_patterns.get(AnswerValueEnum.NS.value)
        priority_to_disclose = col_patterns.get("priority_to_disclose")
        if not priority_to_disclose and ns_pattern and PatternCollection(ns_pattern, re.I).nexts(clean_text_with_blank):
            neglect_ns_pattern = col_patterns.get("neglect_ns_pattern")
            if not neglect_ns_pattern:
                return AnswerValueEnum.NS.value
            neglect_ns_matcher = PatternCollection(neglect_ns_pattern).nexts(clean_text)
            if not neglect_ns_matcher:
                return AnswerValueEnum.NS.value

        for col, patterns in col_patterns.items():
            if col not in (AnswerValueEnum.NS.value, AnswerValueEnum.ND.value, AnswerValueEnum.D.value):
                continue
            if isinstance(patterns, GPTDictator):
                return patterns.judge(clean_text_with_blank)
            if PatternCollection(patterns).nexts(clean_text_with_blank) or PatternCollection(patterns).nexts(
                clean_text
            ):
                return col
        return default_result


class RatioEnum(JudgeByRegex):
    col_patterns = {
        "Material Impairment": {
            "Disclosure": [r".*"],
            "No Disclosure": [r"^\s*$"],
        },
    }


class ArEsgEnum:
    common_explain_patterns = [
        # 由于业务需要 而不披露 属于E, 这个常量下添加类似的描述
        r"Due to (our|the) business nature",
        r"Due to (our|the) operation characteristics",
        r"not material to (our|the) group",
        r"not considered.*?material issue",
        r"not emit significant",
        r"not a material issue",
        r"not identified as material issues",
        r"N/A",  # 可能会误判
    ]
    comply_priority_cols = [
        "KPI A1.6 part 1 - waste handling",
        "KPI A1.6 part 2 - waste reduction target",
        "KPI B6.5 - consumer data protection",
    ] + Y_ND_COLS
    not_need_common_explain_cols = [
        "KPI A1.4 - non-hazardous waste",
        "KPI A2.4 part 1 - water sourcing",
        "KPI A2.4 part 2 - water efficiency targets",
        "KPI B2.2 - work injury lost days",
        "KPI A3.1 - impact on environment and natural resources",
        "B5 policies - supply chain",
        "KPI B5.3 - supply chain ESG risks identification",
        "KPI B6.5 - consumer data protection",
        "KPI B7.1 - legal cases on corruption",
        "KPI B3.1 - percentage of employees trained",
    ]
    col_patterns = {
        "MDR 13 i - board oversight": {},
        "MDR 13 ii - board management approach": {},
        "MDR 13 iii - board progress review": {},
        "MDR 14 part 1 - materiality application": {},
        "MDR 14 part 2 - quantitative application": {},
        "MDR 14 part 3 - consistency application": {},
        "MDR 15 - reporting boundary": {},
        "A1 policies - emissions": {
            "Comply": [
                # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/840#note_225798
                r"(impact|emission).*?c(?:o|a)me.*?from",
            ],
            "Explain": [
                r"^As mentioned under",
                r"KPIs?.*?(below|follow)",
                r"due to.*?nature",
                r"not.*?impact",
                r"not involve|not engage|not disclosed|not generate|no generation|not produce|not quantify",
                r"not own.*?emissions",
                r"not applicable",
                r"do not emit significant",
                # r'(?<!not )limit',
            ],
        },
        "A1 law compliance - emissions": {
            "Explain": [
                r"not applicable",
                r"no relevant laws",
            ],
        },
        "KPI A1.1 - emission type and data": {
            "Explain": [
                r"no material to the",
                r"do not emit significant",
                r"not applicable",
                r"not? (directly )?(involve|engage|disclosed|generate|generation|produce)",
                r"limited|not quantify",
                r"no material.*?during.*?year",
            ],
        },
        "KPI A1.2 part 1 - Scope 1": {
            "Explain": [
                r"not applicable",
                r"not to compile",
                r"not generate any.*?direct emissions",
                r"not consume resources",
                r"has chosen not to disclose",
            ],
            "No Disclosure": [r"greenhouse gas emission from energy indirect emissions \(Scope 2\)"],
            "Comply": [
                r".*",
            ],
        },
        "KPI A1.2 part 2 - Scope 2": {
            "Explain": [
                r"not applicable",
            ],
            "Comply": [
                r"The (GHG|greenhouse gas) emission data is set out in the table below",
            ],
        },
        # "KPI A1.2 part 3 - Scope 3": {
        #     'Explain': [
        #         r'not applicable',
        #     ],
        # },
        "KPI A1.3 - hazardous waste": {
            "Comply": [
                r"not generate hazardous waste.*?nil",
                r"The Group generates no hazardous waste in its operation",
            ],
            "Explain": [
                r"Due to the business nature.*?not (directly )?generate",
                r"not applicable",
                r"limited",
                r"(not|minimal) quanti(fy|ties)",
                r"No (?:material discharge of)? hazardous waste",
                r"not applicable",
                r"insignificant",
            ],
        },
        "KPI A1.4 - non-hazardous waste": {
            "Comply": [
                r"on-hazardous waste generated.*?tonnes",
            ],
            "Explain": [
                r"not applicable",
                r"(not|minimal) quanti(fy|ties)",
                r"not? ((directly|been) )?(involve|engage|disclosed)",
                r"insignificant",
                r"non-hazardous wastes are currently negligible",
                r"expand its data.*?non-hazardous waste.*?future",
                r"no KPI are identified and disclosed",
                r"waste are trivial",
                r"For non-hazardous.*?handling by qualified agencies",
                r"amount.*?was relatively limited",
            ],
        },
        "KPI A1.5 - emission target": {
            "Comply": [
                r"reduce exhaust gas emission",
                r"have set a long-term target.*?reducing.*?emission",
            ],
            "Explain": [
                r"not involve|not engage|not disclosed|not generate|no generation|not produce|limited|not quantify",
                r"not undertake",
                r"not applicable",
                r"not.*?be.*?material issue",
                r"issue is not material",
            ],
        },
        "KPI A1.6 part 1 - waste handling": {
            "Comply": [
                r"(adopted|take).*?measures.*?to reduce",
                # r'waste',
                r"dispose|recycle|reuse|recove",
                r"striving.*?qualified agencies",
                r"following:",
                r"dispose of",
            ],
            "Explain": [
                r"not involve|not engage|not disclosed|not generate|no generation|not produce|not quantify",
                r"not undertake",
                r"(?P<content>immaterial|insignificant|continue)",
                r"not applicable",
                r"this information and reduction target shall be disclosed in the next reporting year",
                r"not set target",
                r"trivial",
            ],
        },
        "KPI A1.6 part 2 - waste reduction target": {
            "Comply": [
                r"(adopted|take).*?measures.*?to reduce",
                r"measures.*?reduc",
                r"waste reduction",
                r"following.*?:",
                r"dispose of",
                r"in order to|waste reduction",
                r"after being cleaned",
            ],
            "Explain": [
                r"not involve|not engage|not disclosed|not generate|no generation|not produce|not quantify",
                r"not undertake",
                r"immaterial|insignificant",
                r"not applicable",
                r"not have a reduction policy",
                r"this information and reduction target shall be disclosed in the next reporting year",
                r"not set.*?target",
            ],
        },
        "A2 policies - use of resources": {},
        "KPI A2.1 - energy consumption": {
            "Explain": [
                r"not material",
                r"not available",
            ]
        },
        "KPI A2.2 - water consumption": {
            "Comply": [
                r"\d+ cubic metres",
            ],
            "Explain": [
                r"not? ((directly|been) )?(involve|engage|disclosed|generate|generation|produce|significant)",
                r"no suppliers|not material|not disclosed|N/A",
                r"no separate meter",
                r"not available",
                r"not consume",
                r"due to.*?nature",
                r"not feasible",
                r"no individual water consumption data",
                r"insignificant",
                r"not possess information",
                r"not have details of",
            ],
        },
        "KPI A2.3 - energy efficiency targets": {
            "Explain": [
                r"water consumption.*?not available",
                r"not available",
            ],
            "Comply": [
                r"reduction target",
            ],
        },
        "KPI A2.4 part 1 - water sourcing": {
            "Explain": [
                r"not relevant",
                r"not applicable",
                # r'water consumption.*?not available',
                r"consumption figures are not available",  # 60838
            ],
            "Comply": [
                r"not encounter|not deal with|not face|no issue",
                r"not have any issues",
                r"(?P<content>not have (any)? problems.*?water resources)",
            ],
        },
        "KPI A2.4 part 2 - water efficiency targets": {
            "Explain": [
                r"not deal with|not face",
                r"water consumption.*?not available",
                r"(?P<content>not have problems.*?water resources)",
                r"not placed emphasis",
                r"cannot control what water saving measures",
                r"reached an optimal point",
                r"not applicable",
                r"water consumption.*?insignificant",
            ],
            # 'Comply': [],
        },
        "KPI A2.5 - packaging material": {
            "Comply": [
                r"pack(?:ag)?ing material(?:(?!year|date).)*?(?=\b(?:is|was|are)\b.*?\d+)",
            ],
            "Explain": [
                r"pack(?:ag)?.*?not material",
                r"not available",
                r"not consume",
                r"due to.*?nature",
                r"immaterial",
                r"Not?\s.*?Packaging materia",
                r"shall be disclosed in the next reporting year",
                r"without consumption.*?packaging material",
                r"packaging materials? did not constitute material",
                r"not identified as material",
            ],
        },
        "A3 policies - environment and natural resources": {
            "Explain": [
                r"Not Applicable",
                r"Due to the nature of (the|our) business",
                r"not have any direct and considerable impacts",
            ],
        },
        "KPI A3.1 - impact on environment and natural resources": {
            "Explain": [
                r"Not Applicable",
                r"Due to the nature of (the|our) business",
                r"not have any direct and considerable impacts",
            ],
        },
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/813#note_214274
        "A4 policies - climate-related issues": {
            "Explain": [
                "not direct impact|not disclosed|of little significance|not affect",
                "not produce|not applicable|non-material|not disclosed",
                "no significant",
            ],
        },
        "KPI A4.1 - climate-related issues & impact": {
            "Comply": [
                r"extreme weather|typhoon|climate change",
                r"climate related risk",
            ],
            "Explain": [
                "not direct impact|not disclosed|of little significance|not affect",
                "not produce|not applicable|non-material|not disclosed",
                "no significant",
            ],
        },
        "B1 policies - employment": {},
        "B1 law compliance - employment": {
            # 'Explain': [],  # explain 不常见 所有comply 也就不配置了 会走默认的逻辑
            # 'Comply': [],
            # 'No Disclosure': [],
        },
        "KPI B1.1 - workforce by types": {
            "Explain": [r"Not Applicable"],
        },
        "KPI B1.2 - employee turnover by types": {
            "Explain": [
                r"Not Applicable",
                r"commercially sensitive",
            ],
        },
        "B2 policies - health and safety": {},
        "B2 law compliance - health and safety": {
            # 'Explain': [],  # explain 不常见 所有comply 也就不配置了 会走默认的逻辑
            # 'Comply': [],
            # 'No Disclosure': [],
        },
        "KPI B2.1 - work-related fatalities": {
            "Explain": [r"Not Applicable"],
        },
        "KPI B2.2 - work injury lost days": {
            "Explain": [r"Not Applicable"],
        },
        "KPI B2.3 - health and safety measures": {
            "Explain": [r"Not Applicable"],
        },
        "B3 policies - development and training": {},
        "KPI B3.1 - percentage of employees trained": {
            "Explain": [
                r"Not Applicable",
                r"not arrange",
            ],
        },
        "KPI B3.2 - training hours completed": {
            "Explain": [r"Not Applicable"],
        },
        "B4 policies - labour standards": {
            "Explain": [r"Not Applicable"],
        },
        "B4 law compliance - labour standards": {
            "Explain": [r"Not Applicable"],
        },
        "KPI B4.1 - review measures to avoid child & forced labour": {
            "Explain": [r"Not Applicable"],
        },
        "KPI B4.2 - steps to avoid child & forced labour": {
            "Explain": [r"Not Applicable"],
        },
        "B5 policies - supply chain": {
            "Explain": [r"Not Applicable", r"Due to the nature of (the|our) business"],
        },
        "KPI B5.1 - number of suppliers": {
            "Explain": [
                r"no suppliers|not material|not disclosed|N/A",
                r"no major suppli",
                r"immaterial",
            ],
        },
        "KPI B5.2 - suppliers engagement": {
            "Explain": [r"no suppliers|not material|not disclosed|N/A"],
        },
        "KPI B5.3 - supply chain ESG risks identification": {
            "Explain": [
                r"no suppliers|not material",
                r"not the key material matters",
                r"Due to the nature of our business",
                r"Due to the business nature",
            ],
        },
        "KPI B5.4 - practice to promote environmentally preferable products": {
            "Explain": [r"no suppliers|not material|not disclosed|N/A", r"Due to the nature of (the|our) business"],
        },
        "B6 policies - product responsibility": {
            "Explain": [r"Not Applicable"],
        },
        "B6 law compliance - product responsibility": {
            "Explain": [r"Not Applicable"],
        },
        "KPI B6.1 - products recall": {
            "Comply": [r"no\s.*recall", r"0"],
            "Explain": [
                r"Not Applicable|not subject",
                r"provide.*?(education|leasing|financial)",
                r"no tangible products",
                # r'(?<!no\s).*?recall',
                r"no relevant quality assurance process and recall procedure",
            ],
        },
        "KPI B6.2 - products related complaints": {
            "Explain": [r"Not Applicable", r"not.*?products.*?recalls"],
        },
        "KPI B6.3 - IP rights protection": {
            "Explain": [r"Not Applicable"],
        },
        "KPI B6.4 - quality assurance process": {
            "Explain": [r"Not Applicable"],
        },
        "KPI B6.5 - consumer data protection": {
            "Explain": [r"Not Applicable"],
        },
        "B7 policies - anti-corruption": {
            "Explain": [r"Not Applicable"],
        },
        "B7 law compliance - anti-corruption": {
            "Explain": [r"Not Applicable"],
        },
        "KPI B7.1 - legal cases on corruption": {
            "Explain": [r"Not Applicable"],
        },
        "KPI B7.2 - preventive measures & whistle-blowing procedures": {
            "Explain": [r"Not Applicable"],
        },
        "KPI B7.3 - anti-corruption training": {
            "Explain": [
                r"Not Applicable",
                # r'will consider'
                r"will consider to offer anti-corruption trainings",
                r"No anti-corruption on training",
                r"no training hours",
            ],
        },
        "B8 policies - community investment": {},
        "KPI B8.1 - community investment focus": {
            "Explain": [r"Not Applicable"],
        },
        "KPI B8.2 - resources contributed": {
            "Explain": [
                r"Not Applicable",
                r"temporarily suspended",
                r"did not participate",
            ],
        },
    }

    def __init__(self, remove_blank=False):
        self.remove_blank = remove_blank

    def predict(self, predictor_result: PredictorResult, schema: SchemaItem, metadata: dict = None) -> str | None:
        if schema.name == "KPI A1.2 part 3 - Scope 3":
            return None
        clean_text = clean_txt(predictor_result.text, remove_blank=self.remove_blank)
        col_patterns = self.col_patterns.get(schema.name, {})

        explain_matcher = PatternCollection(self.common_explain_patterns, re.I).nexts(clean_text)
        if "E" in schema.type and explain_matcher and schema.name not in self.not_need_common_explain_cols:
            enum_value = judge_explain_again(explain_matcher, predictor_result)
            return enum_value
        if col_patterns:
            for enum_value, patterns in col_patterns.items():
                # if enum_value == 'Explain' and schema.name not in self.not_need_common_explain_cols:
                #     patterns.extend(EXPLAIN_PATTERN)
                matched = PatternCollection(patterns, re.I).nexts(clean_text)
                if matched:
                    logger.debug(matched.re.pattern)
                    logger.debug(clean_text)
                    logger.debug(f"{schema.name}: {enum_value}")
                    if enum_value == "Explain":
                        enum_value = judge_explain_again(matched, predictor_result)
                    return enum_value

        if not clean_text:
            return AnswerValueEnum.ND.value

        for ele in predictor_result.relative_elements:
            if ele.get("class") == "TABLE":
                # 表中无关键字，但表头可能会有
                # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/860#note_218200
                fake_result = PredictorResult([], text=ele["title"])
                return self.predict(fake_result, schema)
        return AnswerValueEnum.COMPLY.value


class EsgEnum:
    """Enum for Jura4 ESG Report"""

    common_explain_patterns = [
        # 由于业务需要 而不披露 属于E, 这个常量下添加类似的描述
        r"Due to (our|the) business nature",
        r"Due to (our|the) operation characteristics",
        r"be disclosed in the future",
        r"not material (to the group|in our operation)",
        r"not considered.*?material issue",
        r"not emit significant",
        r"not a material (issue|topic)",
        r"not identified as material issues",
        r"N/A",  # 可能会误判
        r"insig-?nificant",  # 可能会误判
    ]
    comply_priority_cols = [
        "KPI A1.6 part 1 - waste handling",
        "KPI A1.6 part 2 - waste reduction target",
        "KPI B6.5 - consumer data protection",
    ] + Y_ND_COLS
    not_need_common_explain_cols = [
        "KPI B6.2 - products related complaints",
    ]
    col_patterns = {
        "MDR 13 i - board oversight": {},
        "MDR 13 ii - board management approach": {},
        "MDR 13 iii - board progress review": {},
        "MDR 14 part 1 - materiality application": {},
        "MDR 14 part 2 - quantitative application": {},
        "MDR 14 part 3 - consistency application": {},
        "MDR 15 - reporting boundary": {},
        "A1 policies - emissions": {
            "Comply": [
                # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/840#note_225798
                r"(impact|emission)(?:(?!\.).)*?c(?:o|a)me.*?from",
            ],
            "Explain": [
                r"^As mentioned under",
                r"KPIs?.*?(below|follow)",
                r"due to.*?nature",
                r"not.*?impact",
                r"not involve|not engage|not disclosed|not generate|no generation|not produce|not quantify",
                r"not own.*?emissions",
                r"not applicable",
                # r'(?<!not )limit',
            ],
        },
        "A1 law compliance - emissions": {
            "Explain": [
                r"not applicable",
                r"no relevant laws",
            ],
        },
        "KPI A1.1 - emission type and data": {
            "Explain": [
                r"not applicable",
                r"not? (directly )?(involve|engage|disclosed|generate|generation|produce)",
                r"limited|not quantify",
                r"no material.*?during.*?year",
            ],
        },
        "KPI A1.2 part 1 - Scope 1": {
            "Explain": [
                r"not applicable",
                r"not to compile",
                r"not generate any.*?direct emissions",
                r"not consume resources",
                r"not discharge exhaust gas",
            ],
            "No Disclosure": [r"greenhouse gas emission from energy indirect emissions \(Scope 2\)"],
            "Comply": [
                r".*",
            ],
        },
        "KPI A1.2 part 2 - Scope 2": {
            "Explain": [
                r"not applicable",
            ],
            "Comply": [
                r"The (GHG|greenhouse gas) emission data is set out in the table below",
            ],
        },
        "KPI A1.3 - hazardous waste": {
            "Comply": [
                r"not generate hazardous waste.*?nil",
                r"The Group generates no hazardous waste in its operation",
            ],
            "Explain": [
                r"Due to the business nature.*?not (directly )?generate",
                r"no significant hazardous waste generated",
                r"not applicable",
                r"not generate",
                r"limited",
                r"(not|minimal) quanti(fy|ties)",
                r"No hazardous waste",
                r"not applicable",
                r"insignificant",
                r"hazardous and non-hazardous waste is not material",
                r"did not generate any hazardous waste",
                r"he Group.*?operations do not involve hazardous waste",
            ],
        },
        "KPI A1.4 - non-hazardous waste": {
            "Comply": [
                r"on-hazardous waste generated.*?tonnes",
            ],
            "Explain": [
                r"not applicable",
                r"(not|minimal) quanti(fy|ties)",
                r"not? ((directly|been) )?(involve|engage|disclosed)",
                r"insignificant",
                r"non-hazardous wastes are currently negligible",
                r"expand its data.*?non-hazardous waste.*?future",
                r"no KPI are identified and disclosed",
                r"waste are trivial",
                r"For non-hazardous.*?handling by qualified agencies",
                r"provided by the property management companies",
                r"Non-hazardous wastes are generally.*?property management office",
                r"amount.*?was relatively limited",
            ],
        },
        "KPI A1.5 - emission target": {
            "Comply": [
                r"reduce exhaust gas emission",
                r"have set a long-term target.*?reducing.*?emission",
            ],
            "Explain": [
                r"not involve|not engage|not disclosed|not generate|no generation|not produce|limited|not quantify",
                r"not undertake",
                r"not applicable",
                r"unrealistic to set emission target",
            ],
        },
        "KPI A1.6 part 1 - waste handling": {
            "Comply": [
                r"(adopted|take).*?measures.*?to reduce",
                # r'waste',
                r"dispose|recycle|reuse|recove",
                r"striving.*?qualified agencies",
                r"following:",
                r"dispose of",
            ],
            "Explain": [
                r"not involve|not engage|not disclosed|not generate|no generation|not produce|not quantify",
                r"not undertake",
                r"(?P<content>immaterial|insignificant|continue)",
                r"not applicable",
                r"this information and reduction target shall be disclosed in the next reporting year",
                r"not set target",
                r"trivial",
            ],
        },
        "KPI A1.6 part 2 - waste reduction target": {
            "Comply": [
                r"(adopted|take).*?measures.*?to reduce",
                r"measures.*?reduc",
                r"following.*?:",
                r"dispose of",
                r"in order to",
                r"after being cleaned",
            ],
            "Explain": [
                r"not involve|not engage|not disclosed|not generate|no generation|not produce|not quantify",
                r"not undertake",
                r"immaterial|insignificant",
                r"not applicable",
                r"not have a reduction policy",
                r"this information and reduction target shall be disclosed in the next reporting year",
                r"not.*?set.*?target",
                r"does not have.*?target",
            ],
        },
        "A2 policies - use of resources": {},
        "KPI A2.1 - energy consumption": {
            "Explain": [
                r"not material",
                r"not available",
            ]
        },
        "KPI A2.2 - water consumption": {
            "Comply": [
                r"\d+ cubic metres",
            ],
            "Explain": [
                r"not? ((directly|been) )?(involve|engage|disclosed|generate|generation|produce|significant)",
                r"no suppliers|not material|not disclosed|N/A",
                r"no separate meter",
                r"not available",
                r"not consume",
                r"due to.*?nature",
                r"not feasible",
                r"no individual water consumption data",
                r"insignificant",
                r"not possess information",
                r"not have details of",
            ],
        },
        "KPI A2.3 - energy efficiency targets": {
            "Explain": [
                r"water consumption.*?not available",
                r"not available",
                r"not currently set.*?target",
            ],
            "Comply": [
                r"reduction target",
            ],
        },
        "KPI A2.4 part 1 - water sourcing": {
            "Explain": [
                r"not relevant",
                r"not identified",
                r"not applicable",
                r"consumption figures are not available",  # 60838
                r"use of water is not material",
            ],
            "Comply": [
                r"not encounter|not deal with|not face|no issue",
                r"not have any issues",
                r"(?P<content>not have (any)? problems.*?water resources)",
            ],
        },
        "KPI A2.4 part 2 - water efficiency targets": {
            "Explain": [
                r"not deal with",
                r"not identified",
                r"water consumption.*?not available",
                r"(?P<content>not have problems.*?water resources)",
                r"not placed emphasis",
                r"cannot control what water saving measures",
                r"reached an optimal point",
                r"not applicable",
                r"not be available",
                r"water consumption.*?insignificant",
                r"not considered a material aspect",
                r"not.*?set.*?water.*?target",
                r"No.*?target is set",
                r"involve no significant use of water resources",
                r"No issues in sourcing water",
                r"No policies nor initiatives have been formulated",
                r"No formal policy on energy or water use efficiency has been established",
            ],
            # 'Comply': [],
        },
        "KPI A2.5 - packaging material": {
            "Explain": [
                r"pack(?:ag)?.*?not material",
                r"not available",
                r"not applicable",
                r"not consume",
                r"due to.*?nature",
                r"immaterial",
                r"insig-?nificant",
                r"Not?\s.*?Packaging materia",
                r"shall be disclosed in the next reporting year",
                r"without consumption.*?packaging material",
                r"packaging materials? did not constitute material",
                r"is not subject to material disclosure",
                r"kg0",  # 特例 需要判断表格中当前年份的数值 todo
                r"not identified as material",
            ],
            "Comply": [
                r"pack(?:ag)?ing material(?:(?!year|date).)*?(?=\b(?:is|was|are)\b.*?\d+)",
            ],
        },
        "A3 policies - environment and natural resources": {},
        "KPI A3.1 - impact on environment and natural resources": {
            "Explain": [r"Not Applicable"],
        },
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/813#note_214274
        "A4 policies - climate-related issues": {
            "Explain": [
                "not direct impact|not disclosed|of little significance|not affect",
                "not produce|not applicable|non-material|not disclosed",
                "no significant",
                r"potential impacts of climate change on our Group",
            ],
        },
        "KPI A4.1 - climate-related issues & impact": {
            "Explain": [
                "not direct impact|not disclosed|of little significance|not affect",
                "not produce|not applicable|non-material|not disclosed",
                "no significant",
                r"potential impacts of climate change on our Group",
            ],
            "Comply": [
                r"extreme weather|typhoon|climate change",
                r"climate related risk",
            ],
        },
        "B1 policies - employment": {},
        "B1 law compliance - employment": {
            # 'Explain': [],  # explain 不常见 所有comply 也就不配置了 会走默认的逻辑
            # 'Comply': [],
            # 'No Disclosure': [],
        },
        "KPI B1.1 - workforce by types": {
            "Explain": [r"Not Applicable"],
        },
        "KPI B1.2 - employee turnover by types": {
            "Explain": [
                r"Not Applicable",
                r"commercially sensitive",
                r"Pending setting up",
            ],
        },
        "B2 policies - health and safety": {},
        "B2 law compliance - health and safety": {
            # 'Explain': [],  # explain 不常见 所有comply 也就不配置了 会走默认的逻辑
            # 'Comply': [],
            # 'No Disclosure': [],
        },
        "KPI B2.1 - work-related fatalities": {
            "Explain": [r"Not Applicable"],
        },
        "KPI B2.2 - work injury lost days": {
            "Explain": [r"Not Applicable"],
        },
        "KPI B2.3 - health and safety measures": {
            "Explain": [r"Not Applicable"],
        },
        "B3 policies - development and training": {},
        "KPI B3.1 - percentage of employees trained": {
            "Comply": [
                r"%\d+",
                r"\d+%",
            ],
            "Explain": [
                r"Not Applicable",
                r"not arrange",
            ],
        },
        "KPI B3.2 - training hours completed": {
            "Explain": [r"Not Applicable"],
        },
        "B4 policies - labour standards": {
            "Explain": [r"Not Applicable"],
        },
        "B4 law compliance - labour standards": {
            "Explain": [r"Not Applicable"],
        },
        "KPI B4.1 - review measures to avoid child & forced labour": {
            "Explain": [r"Not Applicable"],
        },
        "KPI B4.2 - steps to avoid child & forced labour": {
            "Explain": [r"Not Applicable"],
        },
        "B5 policies - supply chain": {
            "Explain": [
                r"Not Applicable",
                r"not considered to have significant",
                r"not a relevant and material",
                r"aspect to be irrelevant",
            ],
        },
        "KPI B5.1 - number of suppliers": {
            "Explain": [
                r"no suppliers|not material|not disclosed|N/A",
                r"no major suppli",
                r"immaterial",
                r"did not have major supplier",
            ],
        },
        "KPI B5.2 - suppliers engagement": {
            "Explain": [r"no suppliers|not material|not disclosed|N/A"],
        },
        "KPI B5.3 - supply chain ESG risks identification": {
            "Explain": [
                r"no suppliers|not material",
                r"not the key material matters",
            ],
        },
        "KPI B5.4 - practice to promote environmentally preferable products": {
            "Explain": [r"no suppliers|not material|not disclosed|N/A"],
        },
        "B6 policies - product responsibility": {
            "Explain": [r"Not Applicable"],
        },
        "B6 law compliance - product responsibility": {
            "Explain": [r"Not Applicable"],
        },
        "KPI B6.1 - products recall": {
            "Comply": [r"no\s(?:(?!\.).)*recall", r"0"],
            "Explain": [
                r"not involve",
                r"不適用|不适用",
                r"Not( an)? Applicable|not subject",
                r"provide.*?(education|leasing|financial)",
                r"no tangible products",
                r"not( a)? material",
                # r'(?<!no\s).*?recall',
                r"no relevant quality assurance process and recall procedure",
            ],
        },
        "KPI B6.2 - products related complaints": {
            "Explain": [r"Not Applicable", r"not.*?products.*?recalls", "n/a"],
            "Comply": [r"\d+(?:(?!\.).)*complaints? (were|are|was|is) received"],
        },
        "KPI B6.3 - IP rights protection": {
            "Explain": [
                r"Not Applicable",
                "Not material",
            ],
        },
        "KPI B6.4 - quality assurance process": {
            "Explain": [
                r"not (an? )?(applicable|been disclosed|considered)",
                r"(?<![a-zA-Z])N/?A(?![a-zA-Z])",
                r"不適用",
                r"be disclosed in the future",
                r"not involve(?:(?!(\.)).)*recall",
                r"Quality assurance(?:(?!(\.)).)*(don\'t|not) apply",
            ],
        },
        "KPI B6.5 - consumer data protection": {
            "Explain": [r"Not Applicable"],
        },
        "B7 policies - anti-corruption": {
            "Explain": [r"Not Applicable"],
        },
        "B7 law compliance - anti-corruption": {
            "Explain": [r"Not Applicable"],
        },
        "KPI B7.1 - legal cases on corruption": {
            "Explain": [r"Not Applicable"],
        },
        "KPI B7.2 - preventive measures & whistle-blowing procedures": {
            "Explain": [r"Not Applicable"],
        },
        "KPI B7.3 - anti-corruption training": {
            "Explain": [
                r"Not Applicable",
                # r'will consider'
                r"will consider to offer anti-corruption trainings",
                r"No anti-corruption on training",
                r"no training hours",
                r"not provide anti-corruption training",
                r"no anti-corruption training was provided",
            ],
        },
        "B8 policies - community investment": {},
        "KPI B8.1 - community investment focus": {
            "Explain": [r"Not Applicable", r"not(?:(?!\.).)*participate in(?:(?!\.).)*due to"],
        },
        "KPI B8.2 - resources contributed": {
            "Explain": [
                r"Not Applicable",
                r"not involve",
                r"temporarily suspended",
                r"did not participate",
                r"not? (?:(?!\.).)* participate in (?:(?!\.).)* due to",
            ],
        },
    }

    def __init__(self, remove_blank=False):
        self.remove_blank = remove_blank

    def predict(self, predictor_result: PredictorResult, schema: SchemaItem, metadata: dict = None) -> str | None:
        if schema.name == "KPI A1.2 part 3 - Scope 3":
            return None
        clean_text = clean_txt(predictor_result.text, remove_blank=self.remove_blank)
        col_patterns = self.col_patterns.get(schema.name, {})

        explain_matcher = PatternCollection(self.common_explain_patterns, re.I).nexts(clean_text)
        if "E" in schema.type and explain_matcher and schema.name not in self.not_need_common_explain_cols:
            enum_value = judge_explain_again(explain_matcher, predictor_result)
            return enum_value
        if col_patterns:
            for enum_value, patterns in col_patterns.items():
                # if enum_value == 'Explain' and schema.name not in self.not_need_common_explain_cols:
                #     patterns.extend(EXPLAIN_PATTERN)
                matched = PatternCollection(patterns, re.I).nexts(clean_text)
                if matched:
                    logger.debug(matched.re.pattern)
                    logger.debug(clean_text)
                    logger.debug(f"{schema.name}: {enum_value}")
                    if enum_value == "Explain":
                        enum_value = judge_explain_again(matched, predictor_result)
                    return enum_value

        if not clean_text:
            return AnswerValueEnum.ND.value

        for ele in predictor_result.relative_elements:
            if ele.get("class") == "TABLE":
                # 表中无关键字，但表头可能会有
                # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/860#note_218200
                fake_result = PredictorResult([], text=ele["title"])
                return self.predict(fake_result, schema)
        return AnswerValueEnum.COMPLY.value


enum_selector = {
    "Jura 3.0 Disclosure Checking": DisclosureEnum(remove_blank=True),
    "Jura 3.0 Ratio Checking": RatioEnum(),
    "Jura4 Annual Report ESG": ArEsgEnum(),
    "Jura4 ESG Report": EsgEnum(),
    "Jura5 CG Report": CGEnumChecker(),
    "Jura 2.0 Listing Rules": ListRuleEnumChecker(),
    "LRs": LRsEnumChecker(),
    "Jura 2.0 Additional Rules": RuleCChecker(),
    "POLL": PollResultEnumChecker(),
    "AGM": AGMResultEnumChecker(),
}

esg_need_guaranteed_answer_cols = [
    # 'MDR 14 part 2 - quantitative application',
    # 'A1 policies - emissions',
    # 'A2 policies - use of resources',
    # 'A3 policies - environment and natural resources',  # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/1034#note_251817
    "A4 policies - climate-related issues",
    "B1 policies - employment",
    "B2 policies - health and safety",
    "B3 policies - development and training",
    "B4 policies - labour standards",
    # 'B5 policies - supply chain',
    # 'B6 policies - product responsibility',
    "B7 policies - anti-corruption",
    "B8 policies - community investment",
]

answer_in_single_element_cols = [
    "MDR 13 iii - board progress review",
    "MDR 14 part 1 - materiality application",
    "MDR 14 part 2 - quantitative application",
    "MDR 14 part 3 - consistency application",
    "MDR 15 - reporting boundary",
    "A1 law compliance - emissions",
    "B1 law compliance - employment",
    "B2 law compliance - health and safety",
    "B4 law compliance - labour standards",
    "B6 law compliance - product responsibility",
    "B7 law compliance - anti-corruption",
    "KPI A1.3 - hazardous waste",
    "KPI A1.4 - non-hazardous waste",
    "KPI A2.4 part 1 - water sourcing",
    "KPI B1.1 - workforce by types",
    "KPI B1.2 - employee turnover by types",
    "KPI B2.1 - work-related fatalities",
    "KPI B2.2 - work injury lost days",
    "KPI B3.1 - percentage of employees trained",
    "KPI B3.2 - training hours completed",
    "KPI B4.1 - review measures to avoid child & forced labour",
    "KPI B4.2 - steps to avoid child & forced labour",
    "KPI B5.1 - number of suppliers",
    "KPI B6.3 - IP rights protection",
    "KPI B7.3 - anti-corruption training",
    "KPI B8.1 - community investment focus",
]

DEFAULT_NA_LIST = [
    "CG-E(a)-Role and function",
    "CG-E(b)-Composition",
    "CG-E(c)-Number of meetings and record of attendance",
    "E(d)(v)-1-Determine issuer's CG policy",
    "E(d)(v)-2-Duties performed by the board or the committee under CP A.2.1",
    "E(d)(iv)-1-How responsibility met in review of risk management and internal control",
    "E(d)(iv)-2-Effectiveness of issuer's internal audit function",
    "E(d)(i)-3-Non-compliance with Rule 3.21 and remedial steps",
    "E(d)(v)-2-Duties performed by the board or the committee under CP A.2.1",
    "RiC-E(c)-Number of meetings and record of attendance",
]

LRS_DEFAULT_ND_LIST = ["A34", "A35", "A36", "A37", "丨H38-41"]


class Prophet(BaseProphet):
    def __init__(self, prophet_config: dict, mold: NewMold, version_id: int = 0):
        super().__init__(prophet_config, mold, version_id=version_id)
        self.need_missing_crude_answer = mold.id in (special_mold.esg_id, special_mold.ar_esg_id)
        self.need_guaranteed_answer = {
            "Jura4 Annual Report ESG": esg_need_guaranteed_answer_cols,
            "Jura4 ESG Report": esg_need_guaranteed_answer_cols,
        }
        self.answer_in_single_element_cols = {
            "Jura4 Annual Report ESG": answer_in_single_element_cols,
            "Jura4 ESG Report": answer_in_single_element_cols,
        }
        self.ignore_missing_crude_answer_models = {
            "esg_quantitative",
            "after_row_match",
            "esg_16_part_2",
        }

    def parse_enum_value(self, predictor_result, schema, metadata):
        if self.enum_predictor:
            return self.enum_predictor.predict(predictor_result, schema, metadata)
        return None

    def gen_mock_answer(self, enum_value, type_value, schema, *, label=None, key=None):
        """
        :param enum_value: 枚举值
        :param type_value: 枚举值的类型
        :param schema: schema 字段
        :param label: schema 结构中的 label
        :param key: answer 中的 key
        """
        key = key or f'["{self.root_schema.name}:0","{schema}:0"]'
        label = label or schema
        mock_answer = {
            "key": key,
            "data": [],
            "value": [enum_value] if enum_value else [],
            "schema": {
                "data": {
                    "label": label,
                    "required": False,
                    "multi": True,
                    "type": type_value,
                    "words": "",
                    "description": "",
                },
            },
            "meta": {},
        }
        return mock_answer

    def get_enum_predictor(self):
        enum_predictor = enum_selector.get(self._mold.name) or enum_selector.get(clean_txt(self._mold.name))
        return enum_predictor

    def post_process(self, preset_answer, **kwargs):
        match self.root_schema.name:
            case SchemaName.RATIO.value:
                self.add_default_answer_ratio(preset_answer)
                self.remove_assets_answer(preset_answer)
            case SchemaName.DISCLOSURE.value:
                # self.modify_7_2_answer(preset_answer)
                self.modify_18_answer(preset_answer)
                self.remove_empty_box(preset_answer)
            case SchemaName.AR_ESG.value | SchemaName.ESG.value:
                self.add_empty_answer_for_esg(preset_answer)
                self.fix_enum_answer(preset_answer)
            case SchemaName.CG.value:
                self.fix_enum_answer_for_cg(preset_answer)
            case SchemaName.A.value:
                copy_special_answer(preset_answer, special_mold.v1_id, manual=False)
            case SchemaName.B.value:
                copy_special_answer(preset_answer, special_mold.v2_id, manual=False)
            case SchemaName.C.value:
                self.add_default_answer_for_c(preset_answer)
            case SchemaName.POLICY_AR.value | SchemaName.POLICY_ESG.value:
                self.fix_policy_answer(preset_answer)

        return preset_answer

    @staticmethod
    def get_answer_schemas(preset_answer):
        has_answer_schemas = []
        for item in preset_answer["userAnswer"]["items"]:
            item_key = json.loads(item["key"])
            schema = item_key[1].split(":")[0]
            has_answer_schemas.append(schema)
        return has_answer_schemas

    def add_default_answer_for_jura21_b(self, preset_answer):
        has_answer_schemas = self.get_answer_schemas(preset_answer)
        all_schemas = self.root_schema.data["orders"]
        need_add_rules = [i for i in all_schemas if (i not in has_answer_schemas and i in JURA21_COLS + ("丨H83-92",))]
        for rule in need_add_rules:
            schema = self.root_schema.children_schema(rule)
            mock_answer_items = []
            if schema.orders:
                for order in schema.orders:
                    sub_schema = schema.children_schema(order)
                    key = f'["{self.root_schema.name}:0","{rule}:0","{order}:0"]'
                    param = {
                        "enum_value": AnswerValueEnum.ND.value,
                        "type_value": sub_schema.type,
                        "label": order,
                        "schema": schema,
                        "key": key,
                    }
                    mock_answer_items.append(param)
            else:
                # '丨H83-92'
                key = f'["{self.root_schema.name}:0","{rule}:0"]'
                param = {
                    "enum_value": "ND",
                    "type_value": schema.type,
                    "label": rule,
                    "schema": schema,
                    "key": key,
                }
                mock_answer_items.append(param)
            for mock_answer_item in mock_answer_items:
                mock_answer = self.gen_mock_answer(**mock_answer_item)
                preset_answer["userAnswer"]["items"].append(mock_answer)

    def add_default_answer_for_c(self, preset_answer):
        if len(preset_answer["userAnswer"]["items"]) > 0:
            return
        type_value = "PS/NS/ND"
        value = AnswerValueEnum.ND.value
        schema = ""
        key = f'["{self.root_schema.name}:0","C5:0","CCT Confirmed with Auditors:0"]'
        mock_answer = self.gen_mock_answer(value, type_value, schema, label="CCT Confirmed with Auditors", key=key)
        preset_answer["userAnswer"]["items"].append(mock_answer)

    @staticmethod
    def fix_enum_answer_for_cg(preset_answer):
        for item in preset_answer["userAnswer"]["items"]:
            answer_value = item["value"]
            if isinstance(answer_value, list) and AnswerValueEnum.COMPLY.value in answer_value:
                item["value"] = AnswerValueEnum.COMPLY.value

    def add_default_answer_for_cg(self, preset_answer):
        has_answer_schemas = []
        for item in preset_answer["userAnswer"]["items"]:
            item_key = json.loads(item["key"])
            schema = item_key[-1].split(":")[0]
            has_answer_schemas.append(schema)

        all_schemas = self.root_schema.data["orders"]
        need_add_schemas = [i for i in all_schemas if i not in has_answer_schemas]
        type_value = "C,ND,N/A"
        for schema in need_add_schemas:
            value = CGEnum.ND.value
            if schema in DEFAULT_NA_LIST:
                # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/2687
                value = CGEnum.NA.value
            mock_answer = self.gen_mock_answer(value, type_value, schema)
            preset_answer["userAnswer"]["items"].append(mock_answer)

    def add_default_answer_ratio(self, preset_answer):
        need_add_schemas = ["Material Impairment"]
        type_value = "D/NS/ND"
        for schema in need_add_schemas:
            for item in preset_answer["userAnswer"]["items"]:
                if schema in item["key"]:
                    break
            else:
                mock_answer = self.gen_mock_answer(QuarterReportEnum.ND.value, type_value, schema)
                preset_answer["userAnswer"]["items"].append(mock_answer)

    def add_empty_answer_for_esg(self, preset_answer):
        has_answer_schemas = []
        for item in preset_answer["userAnswer"]["items"]:
            item_key = json.loads(item["key"])
            schema = item_key[-1].split(":")[0]
            has_answer_schemas.append(schema)

        all_schemas = self.root_schema.data["orders"]
        need_add_schemas = [i for i in all_schemas if i not in has_answer_schemas]
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/792#note_210650
        if "KPI A1.2 part 3 - Scope 3" in need_add_schemas:
            need_add_schemas.remove("KPI A1.2 part 3 - Scope 3")
            mock_answer = self.gen_mock_answer(None, "E/Y/ND", "KPI A1.2 part 3 - Scope 3")
            preset_answer["userAnswer"]["items"].append(mock_answer)
        for schema in need_add_schemas:
            type_value = "E/Y/ND"
            if schema in Y_ND_COLS:
                type_value = "Y/ND"
            mock_answer = self.gen_mock_answer(ESGEnum.ND.value, type_value, schema)
            preset_answer["userAnswer"]["items"].append(mock_answer)

    @staticmethod
    def fix_enum_answer(preset_answer):
        for item in preset_answer["userAnswer"]["items"]:
            answer_value = item["value"]
            if isinstance(answer_value, list):
                if len(answer_value) == 1:
                    item["value"] = answer_value[0]
                else:
                    col = item["schema"]["data"]["label"]
                    if col in ArEsgEnum.comply_priority_cols:
                        item["value"] = AnswerValueEnum.COMPLY.value
                    elif col != "KPI A1.2 part 3 - Scope 3":
                        item["value"] = AnswerValueEnum.EXPLAIN.value

    @staticmethod
    def remove_assets_answer(preset_answer):
        # Total Assets 有可能会提取到两个value 分到同一组 在这里删除其中位置靠上的那个
        # derivative financial instruments要在current assets中才算short term investments 在这里删除其中位置靠上的那个
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/620#note_161792
        schemas = [
            "Total Assets",
            # 'Short Term Investments',
        ]
        has_liabilities = False
        for item in preset_answer["userAnswer"]["items"]:
            schema_name = json.loads(item["key"])[1].split(":")[0]
            if schema_name not in schemas:
                continue
            schema_label = item["schema"]["data"]["label"]
            if schema_label == "Account":
                account = item["data"][0]["boxes"][0]["text"]
                if asset_account_has_liabilities.nexts(account):
                    has_liabilities = True

            if len(item["data"]) > 1:
                if schema_label == "Value":
                    # 按照box_bottom排序
                    boxes = sorted(item["data"], key=lambda x: x["boxes"][0]["box"]["box_bottom"])
                    if len(boxes) < 4:
                        if has_liabilities:
                            item["data"] = boxes[:1]
                        else:
                            item["data"] = boxes[1:2]  # Short Term Investments有可能会提取到三个答案 这里取第二个
                    else:
                        item["data"] = boxes[-1:]
                else:
                    answers = set()
                    for box_info in item["data"]:
                        for box in box_info["boxes"]:
                            answers.add(box["text"])
                    if len(answers) == 1:
                        item["data"] = item["data"][:1]

        return preset_answer

    @staticmethod
    def modify_7_2_answer(preset_answer):
        """
        7.1为ND时,7.2的答案设置为ND
        """
        enum_value = None
        items = preset_answer["userAnswer"]["items"]
        for item in items:
            label = item["schema"]["data"]["label"]
            if label == "Revenue in PL":
                enum_value = item["value"]
            if (
                enum_value
                and enum_value == AnswerValueEnum.ND.value
                and label == "Note of revenue"
                and item["value"] != AnswerValueEnum.ND.value
            ):
                item["data"] = []
                item["value"] = AnswerValueEnum.ND.value
                break
        return preset_answer

    @staticmethod
    def modify_18_answer(preset_answer):
        """
        partial_text可能预测出多个枚举值
        18_2 只要有D的答案 就认为所有的答案的枚举值都是D

        修改18_1的答案
        18_2是ND 且 18_1 没有segment信息时 18_1修改成ND 不能提取 business review 章节的内容
        """
        items = preset_answer["userAnswer"]["items"]
        enum_values = []
        for item in items:
            label = item["schema"]["data"]["label"]
            if label == "Multiple Business Segments":
                enum_value = item["value"]
                if isinstance(enum_value, list):
                    enum_values.extend(enum_value)
                else:
                    enum_values.append(enum_value)
        if AnswerValueEnum.D.value in enum_values:
            for item in items:
                label = item["schema"]["data"]["label"]
                if label == "Multiple Business Segments":
                    item["value"] = AnswerValueEnum.D.value

        if AnswerValueEnum.ND.value in enum_values:
            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/1109#note_262699
            for item in items:
                label = item["schema"]["data"]["label"]
                if label == "Comment on segment":
                    answers = set()
                    for box_info in item["data"]:
                        for box in box_info["boxes"]:
                            answers.add(box["text"])
                    if not any(P_DISCLOSURE18_SEGMENT.nexts(clean_txt(i)) for i in answers):
                        item["value"] = AnswerValueEnum.ND.value
                        item["data"] = []
        return preset_answer

    @staticmethod
    def remove_empty_box(preset_answer):
        items = preset_answer["userAnswer"]["items"]
        for item in items:
            datas = item["data"]
            has_del_empty_answer = False
            for data in datas:
                if len(data["boxes"]) == 0:
                    datas.remove(data)
                    has_del_empty_answer = True
            if has_del_empty_answer:
                if isinstance(item["value"], list) and len(item["value"]) > 1:
                    item["value"].remove(AnswerValueEnum.ND.value)
            if isinstance(item["value"], list) and len(item["value"]) == 1:
                item["value"] = item["value"][0]
            # Disclosure19 isclosure18.2 pick_answer_strategy 设置为TRUE 有可能会有多个枚举值 优先取 D
            # Disclosure18.1 special_cells  有可能会有多个枚举值 优先取 D
            if item["schema"]["data"]["label"] in [
                "Audited or agreed with the auditors",
                "Summary of segmental information",
                "Comment on segment",
            ]:
                if isinstance(item["value"], list) and len(item["value"]) == 2:
                    item["value"] = AnswerValueEnum.D.value
        return preset_answer

    def fix_policy_answer(self, preset_answer):
        schema_map = {
            "E2-Adoption of Independent Assurance": [
                "E3-Details of Independent Assurance",
                "E4-Independent Assurance on scope 1 and scope 2 GHG emission",
            ],
            "E5-Scenario analysis": ["E6-Source of scenarios"],
            "E7-Scope 3 emissions": [
                "E8-Categories of scope 3 emissions",
                "E9-Scope 3 emissions data by categories",
            ],
        }

        # for E7
        has_answer_schemas = []
        for item in preset_answer["userAnswer"]["items"]:
            item_key = json.loads(item["key"])
            schema = item_key[-1].split(":")[0]
            has_answer_schemas.append(schema)

        all_schemas = self.root_schema.data["orders"]
        need_add_schemas = [i for i in all_schemas if i not in has_answer_schemas]
        for schema in need_add_schemas:
            mock_answer = self.gen_mock_answer(ESGEnum.ND.value, "Y/ND", schema)
            preset_answer["userAnswer"]["items"].append(mock_answer)

        reversed_schema_map = {}
        for k, v in schema_map.items():
            for i in v:
                reversed_schema_map[i] = k
        depends_enum = {}
        for item in preset_answer["userAnswer"]["items"]:
            label = item["schema"]["data"]["label"]
            if label in schema_map:
                if item["value"] == AnswerValueEnum.ND.value or AnswerValueEnum.ND.value in item["value"]:
                    depends_enum[label] = item["value"]

        for item in preset_answer["userAnswer"]["items"]:
            label = item["schema"]["data"]["label"]
            if label in reversed_schema_map and reversed_schema_map[label] in depends_enum:
                logger.info(f"set {label} answer to ND")
                item["data"] = []
                item["value"] = AnswerValueEnum.ND.value
                if label in (PolicyEsgRules.E6.value, PolicyEsgRules.E8.value, PolicyEsgRules.E9.value):
                    categories = item.get("meta", {}).get("categories") or []
                    for category in categories:
                        category["enum"] = "No"
                        category["data"] = []
                    item["meta"] = {"categories": categories}
        return preset_answer
