# -*- coding: utf-8 -*-
import glob
import json
import logging
import pickle
import re
import time
from collections import Counter, defaultdict
from copy import copy, deepcopy
from difflib import Sequence<PERSON><PERSON><PERSON>
from inspect import signature
from itertools import chain, groupby
from typing import Dict, List

from remarkable.common.constants import AnswerV<PERSON>ueEnum, CGEnum, SchemaName, TableType
from remarkable.common.exceptions import ConfigurationError
from remarkable.common.pattern import PatternCollection
from remarkable.common.util import clean_txt, group_cells, import_class_by_path
from remarkable.config import get_config
from remarkable.pdfinsight import clear_syl_title
from remarkable.pdfinsight.parser import parse_table
from remarkable.pdfinsight.reader import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, PdfinsightSyllabus
from remarkable.plugins.predict.common import get_element_candidates
from remarkable.predictor.dataset import DatasetItem
from remarkable.predictor.hkex_predictor.esg_util import REVERED_MAP, is_index_table
from remarkable.predictor.hkex_predictor.models.agm_shares_issued import AGMSharesIssued
from remarkable.predictor.hkex_predictor.models.mr_shares_issued import MRSharesIssued
from remarkable.predictor.hkex_predictor.models.poll_resolution_pass import PollResolutionPass
from remarkable.predictor.hkex_predictor.models.poll_shares_issued import PollSharesIssued
from remarkable.predictor.hkex_predictor.models.reference import Reference
from remarkable.predictor.hkex_predictor.models.total_assets import SignificantInvestmentRatio
from remarkable.predictor.models.auto import AutoModel
from remarkable.predictor.models.base_model import (
    BaseModel,
    find_currency_element_result,
    find_unit_by_element_result,
)
from remarkable.predictor.models.chapter import Chapter
from remarkable.predictor.models.col_match import ColMatch
from remarkable.predictor.models.collect_elements_based import ElementsCollectorBased
from remarkable.predictor.models.empty_answer import EmptyAnswer
from remarkable.predictor.models.enum_value import EnumValue
from remarkable.predictor.models.fixed_position import FixedPosition
from remarkable.predictor.models.fund_raising_group import FundRaisingGroup
from remarkable.predictor.models.group_based import GroupBased
from remarkable.predictor.models.kmeans_classification import KmeansClassification
from remarkable.predictor.models.middle_paras import MiddleParas
from remarkable.predictor.models.middle_rows import MiddleRows
from remarkable.predictor.models.multi_models import MultiModels
from remarkable.predictor.models.near_paras import NearParas
from remarkable.predictor.models.para_match import ParaMatch
from remarkable.predictor.models.partial_text import PartialText
from remarkable.predictor.models.poll_result_common import PollResultCommon
from remarkable.predictor.models.regex_pattern import RegexPattern
from remarkable.predictor.models.relation_entity import RelationEntity
from remarkable.predictor.models.row_match import RowMatch
from remarkable.predictor.models.score_filter import ScoreFilter
from remarkable.predictor.models.shape_text import ShapeText
from remarkable.predictor.models.shape_titles import ShapeTitle
from remarkable.predictor.models.shapes import Shapes
from remarkable.predictor.models.special_cells import SpecialCells
from remarkable.predictor.models.syllabus_based import SyllabusBased
from remarkable.predictor.models.syllabus_elt import SyllabusElt
from remarkable.predictor.models.syllabus_elt_v2 import SyllabusEltV2
from remarkable.predictor.models.table_ai import AITable
from remarkable.predictor.models.table_column_content import TableColumnContent
from remarkable.predictor.models.table_footnote import TableFootNote
from remarkable.predictor.models.table_kv import KeyValueTable
from remarkable.predictor.models.table_row import TableRow
from remarkable.predictor.models.table_titles import TableTitle
from remarkable.predictor.models.table_tuple import TupleTable
from remarkable.predictor.models.table_tuple_select import TupleTableSelect
from remarkable.predictor.models.twice_para_match import TwiceParaMatch
from remarkable.predictor.models.twice_row_match import TwiceRowMatch
from remarkable.predictor.models.use_of_proceeds_group import UseOfProceedsGroup
from remarkable.predictor.schema_answer import (
    OutlineResult,
    PredictorResult,
    PredictorResultGroup,
)
from remarkable.predictor.utils import SafeFileName, calc_distance, classify_by_kmeans

logger = logging.getLogger(__name__)


predictor_models = {
    "auto": AutoModel,
    "partial_text": PartialText,
    "empty": EmptyAnswer,
    "regex_pattern": RegexPattern,
    "score_filter": ScoreFilter,
    "table_column_content": TableColumnContent,
    "segment_info": EmptyAnswer,
    "table_tuple_select": TupleTableSelect,
    "enum_value": EnumValue,
    "syllabus_elt": SyllabusElt,
    "syllabus_elt_v2": SyllabusEltV2,
    "para_match": ParaMatch,
    "twice_para_match": TwiceParaMatch,
    "twice_row_match": TwiceRowMatch,
    "relation_entity": RelationEntity,
    "table_row": TableRow,
    "table_kv": KeyValueTable,
    "table_tuple": TupleTable,
    "table_ai": AITable,
    "syllabus_based": SyllabusBased,
    "chapter": Chapter,
    "middle_paras": MiddleParas,
    "row_match": RowMatch,
    "special_cells": SpecialCells,
    "table_title": TableTitle,
    "kmeans_classification": KmeansClassification,
    "elements_collector_based": ElementsCollectorBased,
    "shape_title": ShapeTitle,
    "shapes": Shapes,
    "shape_text": ShapeText,
    "middle_rows": MiddleRows,
    "table_footnote": TableFootNote,
    "col_match": ColMatch,
    "reference": Reference,
    "group_based": GroupBased,
    "multi_models": MultiModels,
    "near_paras": NearParas,
    "fund_raising_group": FundRaisingGroup,
    "use_of_proceeds_group": UseOfProceedsGroup,
    "fixed_position": FixedPosition,
    "poll_result_common": PollResultCommon,
    "significant_investment_ratio": SignificantInvestmentRatio,
    "poll_shares_issued": PollSharesIssued,
    "mr_shares_issued": MRSharesIssued,
    "agm_shares_issued": AGMSharesIssued,
    "poll_resolution_pass": PollResolutionPass,
}


class EnumPredictor:
    def predict(self, predictor_result, schema, metadata=None):
        raise NotImplementedError


class JudgeByRegex(EnumPredictor):
    col_patterns = {}
    multi_answer_col_patterns = {}

    def __init__(self, remove_blank=False):
        self.remove_blank = remove_blank

    def predict(self, predictor_result, schema, metadata=None):
        clean_text = clean_txt(predictor_result.text, remove_blank=self.remove_blank)
        schema_name = schema.parent.name if schema.name == "Content" else schema.name
        col_patterns = self.col_patterns.get(schema_name, {})
        if not col_patterns:
            return AnswerValueEnum.ND.value  # 默认返回ND
        for col, patterns in col_patterns.items():
            if PatternCollection(patterns).nexts(clean_text):
                return col
        return list(col_patterns.keys())[-1]


def is_similar_content(content_a, content_b):
    if content_a is None or content_b is None:
        return content_a is None and content_b is None

    if content_a in content_b or content_b in content_a:
        return True
    return SequenceMatcher(None, content_a, content_b).ratio() > 0.4


class BasePredictor:
    def __init__(self, schema, config, prophet=None, primary_key=None, columns=None):
        self.schema = schema
        self.config = config
        self.prophet = prophet
        self.model_data = {}
        self.primary_key = primary_key
        self.columns = columns or list(set([self.schema.name] + (self.primary_key or [])))

        models = self.create_models()
        self.models = models["schema_models"]
        self.primary_models = models["primary_models"]

    @property
    def leaf(self):
        # NOTE: amount 字段在预测时视为叶子节点
        return self.schema.is_leaf or self.schema.is_amount or self.config.get("fake_leaf")

    @property
    def model_options(self):
        if self.config.get("models"):
            model_options = self.config.get("models")
        else:
            model_options = [self.config.get("model")] if self.config.get("model") else []
        return model_options

    def train(self, **kwargs):
        raise NotImplementedError

    def predict(self):
        raise NotImplementedError

    def negotiate(
        self, schema_answers: List[Dict[str, List[PredictorResult]]]
    ) -> List[Dict[str, List[PredictorResult]]]:
        raise NotImplementedError

    def load_dataset(self):
        raise NotImplementedError

    def create_model(self, model_option, schema):
        """优先加载通用 model, 然后再加载各自特例 model"""
        model_class = predictor_models.get(model_option["name"])
        if not model_class:
            package = f"remarkable.predictor.{get_config('prophet.package_name')}.models.model_config"
            # TODO: fix possible circular import issue
            # https://mm.paodingai.com/cheftin/pl/3mimjxrp3fbytdn63u9bnjyx8c
            models = import_class_by_path(package) or {}
            model_class = models.get(model_option["name"])
        if not model_class:
            raise ConfigurationError(f"No model found: {model_option['name']}")
        return model_class(model_option, schema, predictor=self)

    def create_models(self):
        models = {"primary_models": [], "schema_models": []}
        for option in self.model_options:
            is_primary = option.get("is_primary", False)
            if is_primary:
                schema = self.prophet.mold_schema.find_schema_by_path(option["schema_path"])
                model = self.create_model(option, schema)
                models["primary_models"].append(model)
            else:
                model = self.create_model(option, self.schema)
                models["schema_models"].append(model)

        return models


class SchemaPredictor(BasePredictor):
    def __init__(self, schema, config, prophet=None, parent=None, columns=None):
        super().__init__(
            schema, config, prophet=prophet, primary_key=parent.sub_primary_key if parent else [], columns=columns
        )

        self.tree_answers = defaultdict(list)
        self.answer_groups = {}
        self.answer_share_group = []
        self.answer_no_group = []
        self.parent = parent

        self.sub_predictors = self.create_sub_predictors()
        if post_process_func := self.config.get("post_process"):
            if not callable(post_process_func):
                raise ValueError("post_process must be a callable function")
            sig = signature(post_process_func)
            required_params_count = len(sig.parameters) - sum(p.default is not p.empty for p in sig.parameters.values())
            if required_params_count != 2:
                raise ValueError(
                    f"post_process function must have 2 required parameters, but {required_params_count} found"
                )

    def __str__(self):
        return f"SchemaPredictor<{self.schema_name}>"

    @property
    def root_config(self):
        return self.prophet.predictor_config

    @property
    def sub_primary_key(self):
        return self.config.get("sub_primary_key", [])

    @property
    def is_share_column(self):
        """是否共享字段，两种情况：
        1. 没有配置主键
        2. 配置 share_column = True
        """
        return self.config.get("share_column", False) or (not self.primary_key)

    @property
    def prophet_answer(self):
        return self.prophet.answer

    @property
    def pdfinsight(self) -> PdfinsightReader:
        return self.prophet.reader

    @property
    def interdoc(self):
        return self.prophet.interdoc

    @property
    def doc_type(self):
        return self.prophet.metadata["doc_type"]

    @property
    def pdf_path(self):
        return self.prophet.metadata["pdf_path"]

    @property
    def file(self):
        return self.prophet.metadata["file"]

    @property
    def schema_level(self):
        return self.schema.level

    @property
    def schema_name(self):
        return self.schema.name

    @property
    def model_data_path(self):
        filename = SafeFileName.escape("_".join(self.config["path"]))
        return self.prophet.model_data_dir.joinpath(f"{filename}.pkl")

    @property
    def merge_answers(self):
        result = self.prophet.merge_schema_answers
        if self.config.get("merge_answers") is not None:
            result = self.prophet.merge_schema_answers
        return result

    @property
    def depends(self) -> List[str]:
        return self.prophet.depends.get(self.schema_name, [])

    @property
    def pick_answer_strategy(self):
        """Strategy to pick predicted answers by models

        :return: pick strategy: single, all
        :rtype: str
        """
        return self.config.get("pick_answer_strategy", "single")

    @property
    def strict_group(self):
        """Strict comparison when getting group name"""
        return self.config.get("strict_group", False)

    @property
    def unit_depend(self):
        return self.config.get("unit_depend", {})

    def train(self, **kwargs):
        logger.info(f"training model for schema: {json.loads(self.schema.path_key)}")

        if self.leaf:
            dataset = self.load_dataset()
            start = time.time()
            models = self.models + self.primary_models
            for model in models:
                model.train(dataset, **kwargs)
                model.print_model()

            logger.info(f"finish training schema {self.schema.path_key}, cost {int(time.time() - start)}s")
        else:
            for predictor in self.sub_predictors:
                predictor.train(**kwargs)

        self.dump_model_data()

    def load_dataset(self) -> List[DatasetItem]:
        dataset = []
        dataset_dir = self.prophet.dataset_dir

        if dataset_dir.exists():
            for pkl in glob.glob(f"{dataset_dir}/*.pkl"):
                with open(pkl, "rb") as dataset_fp:
                    dataset.append(pickle.load(dataset_fp))
        else:
            logger.warning(f"can't find dataset for schema: {json.loads(self.schema.path_key)}")

        return dataset

    def load_model_data(self):
        if self.model_data or not self.config:
            return
        if not self.model_data_path.exists():
            logger.debug(f"can't find model features: {self.model_data_path}")
        else:
            with open(self.model_data_path, "rb") as model_fp:
                self.model_data = pickle.load(model_fp)

    def dump_model_data(self):
        if self.config:
            model_data = {model.name: model.model_data for model in self.models}
            if self.primary_models:
                primary_model_data = {model.name: model.model_data for model in self.primary_models}
                for model_name in primary_model_data:
                    m_data = primary_model_data[model_name]
                    if model_name not in model_data:
                        model_data[model_name] = m_data
                    else:
                        model_data[model_name].update(m_data)
            with open(self.model_data_path, "wb") as model_fp:
                pickle.dump(model_data, model_fp)

        for sub_predictor in self.sub_predictors:
            sub_predictor.dump_model_data()

    def build_primary_key_str(self, dict_answer: Dict[str, List[PredictorResult]]):
        primary_key_values = []
        for key in self.primary_key:
            results = dict_answer.get(key, [])
            for result in results:  # PredictorResult
                if result.primary_key:
                    primary_key_values.extend(result.primary_key)
                else:
                    for element_result in result.element_results:  # ElementResult
                        primary_key_values.append(element_result.text.strip())
        subtotal_column = self.config.get("primary_key_include_subtotal")
        if subtotal_column:
            results = dict_answer.get(subtotal_column)
            if results:
                answer_cell = results[0].element_results[0].parsed_cells[0]
                for sub_total in answer_cell.sub_total_headers:
                    primary_key_values.append(sub_total.text)
        return clean_txt("|".join(primary_key_values))

    def predict_groups(self, known_group_names: List[str] = None) -> Dict[str, List[PredictorResult]]:
        schema_answers = None

        for group_name in known_group_names or []:
            if group_name not in self.answer_groups:
                self.answer_groups[group_name] = []

        # 1. 使用模型进行预测
        if self.models:
            self.load_model_data()
        elements = self.get_candidate_elements()
        schema_answers = self.predict_answer_from_models(elements)

        if schema_answers:
            schema_answers = self.negotiate(schema_answers)
            self.add_predicted_results(schema_answers)

        # 2. 处理未分组的情况 answer_no_group
        if self.answer_no_group:  # TODO self.answer_no_group 永远都是空[]
            if not self.primary_key:
                answer_group = self.answer_groups.setdefault("", [])
                for predictor_result in self.answer_no_group:
                    self.add_predicted_results(
                        [{predictor_result.schema.path_key[-1]: [predictor_result]}], selected_group=answer_group
                    )
            elif self.answer_groups:
                for predictor_result in self.answer_no_group:
                    group_name = self._guess_group_name(predictor_result)
                    if group_name is None:
                        continue
                    answer_group = self.select_answer_group(group_name)
                    if answer_group is not None:
                        self.add_predicted_results(
                            [{predictor_result.schema.path_key[-1]: [predictor_result]}], selected_group=answer_group
                        )

        # 3. 处理共享字段的情况 answer_share_group
        if not self.answer_groups:
            if self.answer_share_group:
                # 只有共享字段 或 不分组 的情况
                self.answer_groups[""] = deepcopy(self.answer_share_group)
        else:
            # 把 answer_share_group 更新到每一组答案中
            for group in self.answer_groups.values():
                group.extend(deepcopy(self.answer_share_group))

        # 4. 修正 answer_groups，并给答案添加 group index
        self.answer_groups = {key: self.revise_group_answer(group) for key, group in self.answer_groups.items()}
        # 5. TODO: 按原表格顺序(从左至右从上到下)对分组进行排序
        return self.answer_groups

    def predict(self):
        groups = self.predict_groups()
        return self.distribute_answers(list(groups.values()))

    @staticmethod
    def revise_group_answer(group: List[PredictorResult], method="merge") -> List[PredictorResult]:
        """修正 group 答案：
        1. 保证 group 中每个叶子节点只有一份 PredictorResult（否则会生成多个相同 path 的叶子节点）
           可能来源有: 本 predictor 输出、sub predictor 补充、share 字段合并
           修正方法："merge" or "overwrite"
        """
        group_dict = {}
        for item in group:
            key = "|".join(item.key_path)
            if key in group_dict:
                if method == "merge":
                    group_dict[key].merge(item)
                elif method == "overwrite":
                    group_dict[key] = item
                else:
                    raise ValueError("undefined revise method %s" % method)
            else:
                group_dict[key] = item
        return list(group_dict.values())

    def predict_value(self, result: PredictorResult):
        if not isinstance(result, PredictorResult):
            raise TypeError("result should be a instance of PredictorResult")
        return self.prophet.parse_value(result)

    def distribute_answers(self, groups: List[List[PredictorResult]]):
        """将嵌套的答案转为平铺的 Dict[path_key, List[PredictorResult]]"""
        tree_answer = defaultdict(list)
        for group_index, group_items in enumerate(groups):
            for item in group_items:
                item.push_group_index(group_index)
                if isinstance(item, PredictorResultGroup):
                    # 非叶子节点
                    for path_key, sub_items in self.distribute_answers(item.groups).items():
                        tree_answer[path_key].extend(sub_items)
                else:
                    # 叶子节点
                    tree_answer[item.key_path_str].append(item)
        return tree_answer

    def build_path_key(self, answer_data, index):
        path_key = []
        for schema_name in answer_data["key"]:
            if schema_name == self.schema_name:
                path_key.append(f"{schema_name}:{index}")
            else:
                path_key.append(f"{schema_name}:0")

        return json.dumps(path_key, ensure_ascii=False)

    def _guess_group_from_element(self, element, element_text=None):
        if element["class"] == "TABLE":
            cells_by_row, _ = group_cells(element["cells"])
            for cells in cells_by_row.values():
                for cell in cells.values():
                    content = clean_txt(cell.get("text", ""))
                    group_names = [i for i in self.answer_groups if clean_txt(i) in content]
                    if group_names:
                        return group_names[0]
            return None
        if element_text is None:
            element_text = element.get("text")

        group_names = [i for i in self.answer_groups if clean_txt(i) in element_text]
        if not group_names:
            return None

        return group_names[0]

    def _guess_group_from_context_elements(self, predictor_result):
        group_option = self.config.get("group")
        result_element = predictor_result.relative_elements[0]
        lookup_strategy = group_option.get("lookup_strategy", "lookahead")
        range_num = group_option.get("range_num", 5)
        if lookup_strategy == "lookahead":
            start = max(result_element["index"] - range_num, 0)
            element_range = range(result_element["index"] - 1, start, -1)
        else:
            if lookup_strategy == "lookbehind":
                start = result_element["index"] + 1
            else:
                start = max(result_element["index"] - 10, 0)
            max_index = self.pdfinsight.max_index
            end = min(start + range_num, max_index)
            element_range = range(end, start, -1)

        for i in element_range:
            if i == result_element["index"]:
                continue
            try:
                elt_type, current_element = self.pdfinsight.find_element_by_index(i)
            except IndexError:
                break
            if current_element is None:
                break

            if elt_type != "PARAGRAPH":
                continue

            group_name = self._guess_group_from_element(current_element)
            if group_name:
                return group_name

        return None

    def _guess_from_syllabuses(self, predictor_result):
        result_element = predictor_result.relative_elements[0]
        group_names = self.answer_groups.keys()
        syllabuses = self.pdfinsight.get_parent_titles(result_element.get("syllabus"))
        if not syllabuses:
            return None

        group_name = None
        for syllabus in syllabuses[::-1]:
            matches = [
                name for name in group_names if clear_syl_title(name) in clear_syl_title(syllabus.get("title", ""))
            ]
            if matches:
                group_name = matches[0]
                break

        return group_name

    def _get_group_metas_base_element(self, result_element):
        name_distances = []
        for name, answer_results in self.answer_groups.items():
            for answer_result in answer_results:
                if answer_result.schema.name in self.primary_key:
                    group_element = answer_result.element_results[0].element
                    distance = calc_distance(result_element, group_element)
                    name_distances.append((name, distance, group_element))

        name_distances.sort(key=lambda x: abs(x[1]))
        return name_distances

    def _guess_group_name(self, predictor_result):
        # group配置参考：{"group": {"lookup_strategy": "lookahead"}} , 可选值：lookahead, lookbehind, both
        group_option = self.config.get("group")
        if not group_option:
            return None
        if not predictor_result.element_results:
            return None

        result_element = predictor_result.element_results[0].element
        group_name = self._guess_group_from_element(result_element)
        if group_name is not None:
            return group_name

        group_name = self._guess_from_syllabuses(predictor_result)
        if group_name is not None:
            return group_name

        group_name = self._guess_group_from_context_elements(predictor_result)
        if group_name is not None:
            return group_name

        # result_element = predictor_result.relative_elements[0]
        # metas = self._get_group_metas_base_element(result_element)

        # lookup_strategy = group_option['lookup_strategy']
        # if lookup_strategy == 'lookahead':
        #     name_distance = min([i for i in metas if i[1] >= 0], key=lambda x: x[1])
        # elif lookup_strategy == 'lookbehind':
        #     name_distance = max([i for i in metas if i[1] <= 0], key=lambda x: x[1])
        # else:
        #     name_distance = min(metas, key=lambda x: abs(x[1]))

        # return name_distance[0]
        return None

    def select_answer_group(self, primary_key):
        if primary_key in self.answer_groups:
            return self.answer_groups[primary_key]

        for key, group in self.answer_groups.items():
            # FOR: `中原冶炼厂60.98%股权` and `中原冶炼厂`
            is_share_group = key == ""
            if is_share_group:
                continue
            if self.strict_group:
                if key == primary_key:
                    return group
            elif key in primary_key or primary_key in key:
                return group

        return None

    def add_predicted_results(self, schema_answers: List[Dict[str, List[PredictorResult]]], selected_group=None):
        """把模型输出 PredictorResult 按照 primary_key 添加到相应的分组中"""
        if not schema_answers:
            return

        for group in schema_answers:
            is_new_group = None
            primary_key_value = self.build_primary_key_str(group)
            if selected_group is not None:
                answer_group = selected_group
            elif self.is_share_column:
                answer_group = self.answer_share_group
            elif primary_key_value:
                answer_group = self.select_answer_group(primary_key_value)
                if answer_group is None:
                    answer_group = self.answer_groups.setdefault(primary_key_value, [])
                    is_new_group = True
                else:
                    is_new_group = False
            else:
                answer_group = self.answer_no_group

            for col, items in group.items():
                if is_new_group is False and col in self.primary_key:
                    # 不重复添加主键
                    continue
                for item in items:
                    answer_result = self.build_schema_answer(item)
                    answer_result.primary_key = primary_key_value
                    answer_group.append(answer_result)

    def predict_answer_from_models(self, elements) -> List[Dict[str, List[PredictorResult]]]:
        answers = []
        primary_answers = []
        if not self.leaf:
            groups = defaultdict(list)
            for sub in self.sub_predictors:
                for dep in sub.depends:
                    # 填充依赖的答案，仅支持同级
                    for item in chain(*groups.values()):
                        if dep == item.schema.name:
                            sub.find_entry_predictor().tree_answers[item.key_path_str].append(item)
                res = sub.predict_groups(known_group_names=groups.keys())
                for group_name, items in res.items():
                    groups[group_name].extend(items)
            answers = [
                {self.schema_name: [PredictorResultGroup([group], schema=self.schema)]} for group in groups.values()
            ]
        elif self.config.get("auto_select", False):
            for element in elements:
                if not (model := self.select_model(element)):
                    continue
                if model.base_all_elements:
                    candidate_elements = elements
                else:
                    candidate_elements = [element]
                model_answers = model.predict(candidate_elements)
                valid_answers = [self.is_valid(i) for i in model_answers]
                answers.extend(valid_answers)
        else:
            logger.info(f"preset answer for: {self.schema.parent.name=}, {self.schema_name=}")
            need_ignore_index_table_answer = None
            if "ESG" in self.prophet.root_schema.name:
                if (esg_rule_name := REVERED_MAP.get(self.schema_name)) and (
                    esg_rule := self.prophet.metadata["esg_rules"].get(esg_rule_name)
                ):
                    need_ignore_index_table_answer = esg_rule.need_ignore_index_table_answer()
            for index, model in enumerate(self.models, start=1):
                if need_ignore_index_table_answer is not None and hasattr(model, "ignore_index_table"):
                    model.ignore_index_table = need_ignore_index_table_answer
                model_answers = model.predict(elements)
                if not (valid_answers := [i for i in model_answers if self.is_valid(i)]):
                    continue
                if logger.level == logging.DEBUG:
                    if ans_values := {
                        ans.answer_value
                        for ans in BaseModel.get_common_predictor_results(model_answers)
                        if ans.answer_value
                    }:
                        logger.debug(", ".join(ans_values))
                    logger.debug(f"predict the order of the answers: ==={index}===, model name: ==={model.name}====")
                answers.extend(valid_answers)
                if self.pick_answer_strategy == "single":
                    break
            if self.pick_answer_strategy == "all":
                answers = self.filter_duplicate_elements(answers)
            answers = self.post_process(answers)
            answers = self.add_default_answer(answers)

        answers = self._unify_output_from_models(answers)
        answers = self.add_missing_crude_answer(answers, elements)

        # 因目前不支持把 primary models 和 schema models 预测出来的答案重新分配
        # 故 primary models 不应该和期望产出多个 groups 的模型(如 table_row)一起使用
        if not self.is_share_column and self.primary_models:
            for model in self.primary_models:
                # primary model should use custom predict method
                model_answers = model.predict(elements, answers)
                valid_answers = [i for i in model_answers if self.is_valid(i)]
                primary_answers.extend(valid_answers)
                if self.pick_answer_strategy == "single" and valid_answers:
                    break

        if primary_answers:
            primary_answers = self._unify_output_from_models(primary_answers)
            for answer in answers:
                answer.update(primary_answers[0])

        return answers

    def add_default_answer(self, answers):
        # todo 梳理所有模型的返回  修改此处的逻辑 组合字段有一个子项预测出来，其余没有预测出来时会有问题
        if answers:
            return answers
        answers = []
        parent_schema = self.schema.parent
        if parent_schema.name in (
            SchemaName.AR_ESG.value,
            SchemaName.ESG.value,
            SchemaName.POLICY_AR.value,
            SchemaName.POLICY_ESG.value,
        ):
            # ESG 有特殊的补充默认答案的逻辑
            # Policy ESG E8 E9 有特殊逻辑
            return []
        enum_value = self.config.get("default_enum_value", self.schema.last_enum)
        if parent_schema.name == SchemaName.CG:
            # CG 除去配置default_enum_value的字段 默认是ND
            enum_value = self.config.get("default_enum_value", CGEnum.ND.value)
        # 处理有多个子项的情况，某个子项有答案，需要给另外一个子项添加默认值
        if self.config.get("fake_leaf"):
            group_answer = []
            for children in self.schema.children:
                children_enum = self.config.get("default_enum_value", children.last_enum)
                result = PredictorResult([], schema=children, value=children_enum)
                group_answer.append(result)
            answers.append(PredictorResultGroup([group_answer], schema=self.schema))
        elif self.leaf:
            result = PredictorResult([], schema=self.schema, value=enum_value)
            answers.append(result)
        return answers

    def post_process(self, answers):
        if post_process_func := self.config.get("post_process"):
            year_start, year_end = None, None
            if hasattr(self.prophet, "metadata") and (metadata := self.prophet.metadata):
                year_start, year_end = metadata.get("year_start"), metadata.get("year_end")
            answers = post_process_func(answers, predictor=self, year_end=year_end, year_start=year_start)
        return answers

    @classmethod
    def filter_duplicate_elements(cls, answers):
        answer_results = BaseModel.get_common_predictor_results(answers)
        if not answer_results or len(answer_results) == len(answer_results[0].element_results) == 1:
            return answers
        result = []
        answers_dict: Dict[tuple, PredictorResult] = {}
        for answer in answer_results:
            indices = tuple({i["index"] for i in BaseModel.get_elements_from_answer_result([answer])})
            if indices in answers_dict and answer.text != answers_dict[indices].text:
                # 相同段落下不同内容，不去重 http://100.64.0.105:55647/#/project/remark/265273?treeId=7102&fileId=70269&schemaId=15&projectId=17&schemaKey=B5
                return answers
            answers_dict[indices] = answer
        # TODO: 结果可能是同一个段落的多个句子
        # http://100.64.0.105:55647/#/project/remark/232917?treeId=23392&fileId=66441&schemaId=15&projectId=17&schemaKey=%E4%B8%A8H83-92
        if len(answers_dict) == 1 and len({ans.text for ans in answer_results}) > 1:
            return answers

        # 1. 任意一组答案是别人的子集，则丢弃
        removed_indices = set()
        for indices, answer in answers_dict.items():
            for idx in answers_dict:
                if indices == idx:
                    continue
                if set(indices).issubset(set(idx)):
                    removed_indices.add(indices)
                    break
            else:
                result.append(answer)

        # 2. 答案之间部分元素块有重复，删除重复的元素块
        if not (
            dup_indices := {
                idx for idx, count in Counter([i for ans in answers_dict for i in ans]).items() if count > 1
            }
        ):
            return result

        existed_indices = set()
        new_result = []
        continuous_indices = {
            idxs for idxs in answers_dict if len(idxs) > 1 and sorted(idxs) == list(range(min(idxs), max(idxs) + 1))
        }
        # 按照连续元素块 + 每组最小index顺序排序
        for indices, answer in sorted(
            answers_dict.items(), key=lambda x: (x[0] not in continuous_indices, min(x[0], default=0))
        ):
            # 跳过已删除的
            if indices in removed_indices:
                continue
            # 没有重复元素的直接取
            if not set(indices) & dup_indices:
                new_result.append(answer)
                continue
            # 连续元素块或未遍历到的直接取
            if not existed_indices & set(indices):
                existed_indices.update(indices)
                new_result.append(answer)
                continue
            # 否则删除重复元素块
            if answer := cls.remove_duplicate_answers(answer, existed_indices):
                new_result.append(answer)
        return new_result

    @staticmethod
    def remove_duplicate_answers(
        answer: dict[str, list[PredictorResult]] | PredictorResult, exclude_indices
    ) -> dict[str, list[PredictorResult]] | PredictorResult:
        """
        若answer.element_results下元素块的index都在exclude_indices中，从answer中删除该元素块对应的AnswerResult
        """
        # http://100.64.0.105:55647/#/project/remark/?treeId=42572&fileId=67313&schemaId=28&projectId=17&schemaKey=A(c)

        def filter_element_results(ans):
            return [res for res in ans.element_results if res.element["index"] not in exclude_indices]

        if isinstance(answer, dict):
            new_answers = {}
            for key, items in answer.items():
                new_items = []
                for item in items:
                    if not (element_results := filter_element_results(item)):
                        continue
                    item.element_results = element_results
                    new_items.append(item)
                if not new_items:
                    continue
                new_answers[key] = new_items
            return new_answers
        if isinstance(answer, PredictorResult):
            if not (element_results := filter_element_results(answer)):
                return None
            answer.element_results = element_results
        return answer

    def add_missing_crude_answer(self, answers, elements):
        if not self.prophet.need_missing_crude_answer:  # 配置开关
            return answers
        if not self.models or not elements:
            return answers
        first_model = self.models[0]
        high_score_elements = classify_by_kmeans(elements)
        # 排除高分答案中的index表格
        high_score_elements = self.filter_invalid_elements(high_score_elements)
        if not answers:
            valid_element = self.valid_high_score_element(elements, high_score_elements)
            if not valid_element:
                return answers
            page_box = PdfinsightSyllabus.elements_outline([valid_element])
            if not page_box:
                return answers
            logger.debug(
                f"add_missing_crude_answer: schema_name : {self.schema_name}, crude score: {valid_element['score']}"
            )
            element_results = [OutlineResult(page_box=page_box, element=valid_element, origin_elements=[valid_element])]
            answer_result = first_model.create_result(element_results, column=self.schema.name)
            return [{self.schema.name: [answer_result]}]
        if not high_score_elements:
            return answers
        answer_models, ignore_missing_crude_answer = self.judge_answer_model(answers)
        if ignore_missing_crude_answer:
            return answers
        if answer_models.intersection(self.prophet.ignore_missing_crude_answer_models):
            # 答案仅包含index表格里的框，有大概率不在初步定位中，此时不补充默认答案
            return answers

        # 使用所有的初步定位的元素块跟答案进行比较 而不是仅用高分元素块
        all_crude_element_idx = [item["index"] for item in elements]
        answer_elements = []
        for answer in answers:
            for items in answer.values():
                answer_elements.extend(first_model.get_elements_from_answer_result(items))
        answer_element_idx = [item["index"] for item in answer_elements]
        if set(answer_element_idx).intersection(set(all_crude_element_idx)):
            return answers
        has_multi_answers = any(
            model.get("multi") or model.get("multi_elements")
            for model in self.config.get("models", [])
            if model.get("name") in answer_models
        )
        if has_multi_answers:
            crude_answer_elements = high_score_elements
        else:
            crude_answer_elements = high_score_elements[:1]
        page_box = PdfinsightSyllabus.elements_outline(crude_answer_elements)
        if not page_box:
            return answers
        logger.debug(f"add_missing_crude_answer: schema_name : {self.schema_name}")
        element_results = [
            OutlineResult(page_box=page_box, element=crude_answer_elements[0], origin_elements=crude_answer_elements)
        ]
        answer_result = first_model.create_result(element_results, column=self.schema.name)
        for items in answers[0].values():
            items.insert(0, answer_result)
            break

        return answers

    def valid_high_score_element(self, elements, high_score_elements):
        special_cols = self.prophet.need_guaranteed_answer.get(self.schema.root_name, [])
        if elements[0]["score"] >= 0.8 or self.schema_name in special_cols:
            # 分数特别高的和policy相关的字段 肯定会有一个答案 基本不会出现ND
            return elements[0]
        answer_in_single_element_cols = self.prophet.answer_in_single_element_cols.get(self.schema.root_name, [])
        if len(high_score_elements) == 1:  # fixme: 这个判断可以考虑去掉
            if self.schema.name in answer_in_single_element_cols:
                if high_score_elements[0]["score"] > 0.7:
                    return high_score_elements[0]
            else:
                if high_score_elements[0]["score"] > 0.01:
                    # fixme: 没有加分数限制之前 分数小于0.01占比73% 0.01-0.1之间是18% 分数大于0.1的占比8%
                    #  等到再需要优化ND相关时 可以将分数从0.01提高至0.1再看 数据来源于标注环境
                    return high_score_elements[0]
        return None

    @staticmethod
    def judge_answer_model(answers):
        rets = set()
        ignore_missing_crude_answer = False
        for answer in answers:
            for items in answer.values():
                for item in items:
                    rets.add(item.answer_model)
                    if item.ignore_missing_crude_answer:
                        ignore_missing_crude_answer = True
        return rets, ignore_missing_crude_answer

    def is_valid_para_elements(self, element: dict) -> bool:
        neglect_para_missing_crude_regs = PatternCollection(
            self.config.get("neglect_para_missing_crude_regs", []), re.I
        )
        clean_element_text = clean_txt(element["text"])
        return not neglect_para_missing_crude_regs.nexts(clean_element_text)

    def filter_invalid_elements(self, elements):
        rets = []
        neglect_cell_regs = PatternCollection(self.config.get("neglect_table_cell_missing_crude_regs", []), re.I)
        neglect_title_regs = PatternCollection(self.config.get("neglect_table_title_missing_crude_regs", []), re.I)
        for element in elements:
            if element["class"] == "PARAGRAPH":
                if self.is_valid_para_elements(element):
                    rets.append(element)
                continue
            if element["class"] != "TABLE":
                rets.append(element)
                continue
            table = parse_table(element, tabletype=TableType.TUPLE.value, pdfinsight_reader=self.pdfinsight)
            if is_index_table(table, neglect_cell_regs, neglect_title_regs):
                continue
            rets.append(element)
        return rets

    @staticmethod
    def _unify_output_from_models(answers):
        # 转换：兼容 [PredictorResult] 格式的模型输出
        return [{i.schema.name: [i]} if isinstance(i, PredictorResult) else i for i in answers]

    @staticmethod
    def is_valid(answer):
        return True

    def select_model(self, element):
        grouped_models = self.group_models()
        element_type = element["class"].lower()
        models = grouped_models.get(element_type)
        if models:
            return self.select_model_base_features(element_type, models)
        return None

    @staticmethod
    def select_model_base_features(element_type, models):
        if element_type == "paragraph":
            return models[0]
        if element_type == "table":
            return models[0]
        return None

    def group_models(self):
        grouped_models = defaultdict(list)
        for element_type, items in groupby(self.models, key=lambda x: x.target_element):
            for model in items:
                grouped_models[element_type].append(model)

        return grouped_models

    @staticmethod
    def merge_answer_data(answers):
        first = answers[0]
        for item in answers[1:]:
            first.merge(item)

        return first

    def get_candidate_elements(self, key_path: List[str] = None) -> list:
        # 某一节点没有配置模型，path是不存在的，所以需要get方法取
        crude_answer_path = key_path or self.config.get("crude_answer_path") or self.config.get("path") or []
        if crude_answer_path and crude_answer_path[-1] == "PARENT_SUBSTITUE":
            crude_answer_path.pop(-1)
        candidates = get_element_candidates(
            self.prophet.crude_answer,
            crude_answer_path,
            priors=self.config.get("element_candidate_priors", []),
            limit=self.config.get("element_candidate_count", 10),
        )
        candidate_elements = []
        location_threshold = self.config.get("location_threshold") or 0
        for item in candidates:
            _, ele = self.pdfinsight.find_element_by_index(item["element_index"])
            if not ele:
                continue
            if not ele or item["score"] < location_threshold:
                continue
            ele = copy(ele)
            ele["score"] = item["score"]
            if self.config.get("anchor_regs") and not self.match_anchor(ele):
                continue
            candidate_elements.append(ele)
        return candidate_elements

    def match_anchor(self, elt):
        near_by = {"step": -1, "amount": 3, "aim_types": ["PARAGRAPH"]}
        prev_elts = self.pdfinsight.find_elements_near_by(elt["index"], **near_by)
        anchor_pattern = PatternCollection(self.config.get("anchor_regs", []))
        if any(anchor_pattern.search("".join([clean_txt(i["text"]) for i in prev_elts[::-1]]))):
            return True
        return False

    def find_sub_predictor(self, schema_name):
        predictors = [i for i in self.sub_predictors if i.schema_name == schema_name]
        return predictors[0]

    def find_child_schema(self, schema_name):
        for column in self.schema.children:
            if column.name == schema_name:
                return column
            for item in column.children:
                if item.name == schema_name:
                    return item
        return None

    def get_depending_answers(self) -> List[PredictorResult]:
        answers = []
        entry_predictor = self.find_entry_predictor()
        for schema_name in self.depends:
            path = self.schema.path[:-1] + [schema_name]
            answer = entry_predictor.get_sub_answer(json.dumps(path, ensure_ascii=False))
            answers.extend(answer)
        return answers

    def find_entry_predictor(self):
        if self.parent.schema_level == 2:
            return self.parent
        return self.parent.find_entry_predictor()

    def get_sub_answer(self, path_key):
        return self.tree_answers[path_key]

    def get_enum_values(self, schema_type):
        return self.prophet.mold_schema.get_enum_values(schema_type)

    @staticmethod
    def build_schema_answer(answer_result):
        return answer_result

    def negotiate(
        self, schema_answers: List[Dict[str, List[PredictorResult]]]
    ) -> List[Dict[str, List[PredictorResult]]]:
        """模型输出答案处理：
        1. 多组合并
        2. 数量字段加单位、币种
        """
        # 多组合并
        if self.merge_answers:
            for group in schema_answers:
                for column, items in group.items():
                    if len(items) > 1:
                        group[column] = [self.merge_answer_data(items)]

        # 补充单位
        for group in schema_answers:
            for column, items in group.items():
                column_schema_path = self.schema.sibling_path(column)
                column_schema = self.schema.mold_schema.find_schema_by_path(column_schema_path[1:])
                if column_schema.is_amount:
                    group[column] = [self.predict_amount_column(item) for item in items]

        for col, unit_col in self.unit_depend.items():
            for answer_result in schema_answers:
                predictor_result = answer_result.get(col)
                if not predictor_result:
                    continue
                units = []
                for result in predictor_result:
                    for i in result.element_results:
                        unit = find_unit_by_element_result(i)
                        if unit:
                            units.append(unit)
                schema = self.parent.find_child_schema(unit_col)
                if units and schema:
                    answer_result[unit_col] = [PredictorResult([u for u in units if u], schema=schema)]

        return schema_answers

    def predict_amount_column(self, item: PredictorResult):
        """把数量答案 扩充为 数量+单位+币种 二级字段"""
        element_results = item.element_results
        column_answers = []
        for child_schema in item.schema.children:
            if child_schema.name in ["金额", "数值"]:
                column_answers.append(PredictorResult(element_results, schema=child_schema, meta=item.meta))
            elif child_schema.name == "单位":
                units = [find_unit_by_element_result(result) for result in element_results]
                column_answers.append(PredictorResult([u for u in units if u], schema=child_schema))
            elif child_schema.name == "币种":
                currencies = [find_currency_element_result(self.pdfinsight, result) for result in element_results]
                column_answers.append(PredictorResult([c for c in currencies if c], schema=child_schema))
        return PredictorResultGroup([column_answers], schema=item.schema, element_results=element_results)

    def create_sub_predictors(self):
        if self.leaf:
            return []
        sub_predictors = [
            self.create_sub_predictor(item, primary_key=self.config.get("sub_primary_key"))
            for item in self.schema.children
            if self._is_configured_schema(item)
        ]
        children_predictor = self.create_children_predictor()
        if children_predictor:
            sub_predictors.insert(0, children_predictor)

        # NOTE: children_predictor 只是选了第一个字段名，可能对排序有问题
        high_priority_names = (self.sub_primary_key or [])[:]
        for sub_predictor in sub_predictors:
            if sub_predictor.depends:
                high_priority_names.extend(sub_predictor.depends)
        sub_predictors.sort(key=lambda x: 0 if x.schema_name in high_priority_names else 100)
        return sub_predictors

    def create_children_predictor(self):
        if self.leaf:
            return None
        all_children_names = [
            child.name
            for child in self.schema.children
            if not self._is_configured_schema(child) or child.name in self.sub_primary_key
        ]
        if not all_children_names:
            return None
        children_deputy_name = self.sub_primary_key[0] if self.sub_primary_key else all_children_names[0]
        children_deputy_schema = next(
            (child for child in self.schema.children if child.name == children_deputy_name), None
        )
        children_deputy_config = deepcopy(self.config)
        children_deputy_config.pop("sub_primary_key", None)
        children_deputy_config["path"] = self.config["path"] + ["PARENT_SUBSTITUE"]
        return SchemaPredictor(
            children_deputy_schema,
            children_deputy_config,
            prophet=self.prophet,
            parent=self,
            columns=all_children_names,
        )

    def _is_configured_schema(self, item):
        schemas_path = [json.loads(path_key) for path_key in self.root_config]
        return any(item.name in schema_path for schema_path in schemas_path)

    def create_sub_predictor(self, schema, primary_key):
        predictor_config = self.root_config.get(schema.path_key, {"path": schema.path[:]})
        return SchemaPredictor(schema, predictor_config, prophet=self.prophet, parent=self)
