from remarkable.common.exceptions import ConfigurationError
from remarkable.predictor.base_prophet import BaseProphet
from remarkable.predictor.mold_schema import MoldSchema
from remarkable.predictor.schema_answer import CellCharResult, CharResult, TableCellsResult, TableResult


def is_table_result(element_result):
    return isinstance(element_result, (CellCharResult, TableCellsResult, TableResult))


def is_sentence_result(result):
    return isinstance(result, CharResult) and result.start is not None and result.end


def collect_predictable_items(schema_item):
    def _collect(item, result):
        if item.is_leaf:
            result.append(item)
        else:
            for child in item.children:
                _collect(child, result)

    predictable_items = []
    _collect(schema_item, predictable_items)

    return predictable_items


def choose_model(schema_item, predictors):
    default_model = {"name": "score_filter"}
    config = next((i for i in predictors if i["path"] == schema_item.path[1:]), None)
    if config:
        return {"name": config["model"]}
    else:
        return default_model


def create_predictor_options(mold, predictors):
    predictor_options = []
    predictors = predictors or mold.predictors or []
    mold_schema = MoldSchema(mold.data)
    is_leaf_map = get_child_schema(mold_schema.root_schema)
    for item in predictors:
        model_option = item.get("model", None) or item.get("models", None)
        item_models = []
        if model_option:
            if isinstance(model_option, str):
                item_models.append({"name": model_option})
            elif isinstance(model_option, list):
                item_models = model_option[:]
            else:
                raise ConfigurationError("Predictor config should be instance of str or list")

        item_config = {"path": item["path"], "models": item_models}
        item_info = is_leaf_map.get("_".join(item["path"]), {})
        if not item_info.get("is_leaf", True) and "table_row" in [i["name"] for i in item_models]:
            item_config["sub_primary_key"] = item_info["children"]
        predictor_options.append(item_config)

    return predictor_options


def get_child_schema(schema_item):
    def _get_child_schema(_schema_item, result):
        if _schema_item.children:
            for children in _schema_item.children:
                if children.is_leaf:
                    result["_".join(children.path[1:])] = {"is_leaf": True}
                else:
                    result["_".join(children.path[1:])] = {"is_leaf": False, "children": list(children.schema.keys())}

                _get_child_schema(children, result)

    ret = {}
    if schema_item.is_leaf:
        ret["_".join(schema_item.path[1:])] = {"is_leaf": True}
        return ret
    _get_child_schema(schema_item, ret)
    return ret


def load_prophet_config(mold, predictors=None):
    predictor_options = create_predictor_options(mold, predictors)

    return {
        "depends": {},
        "predictor_options": predictor_options,
    }


class DefaultProphet(BaseProphet):
    def parse_enum_value(self, predictor_result, schema, metadata):
        pass

    def get_enum_predictor(self):
        return


def make_prophet_instance(prophet_config, mold, version_id):
    return DefaultProphet(prophet_config, mold, version_id)
