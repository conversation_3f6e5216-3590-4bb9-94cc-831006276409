# -*- coding: utf-8 -*-
import json
from functools import cached_property
from typing import Dict, List, Union

from typing_extensions import Self


class MoldSchema:
    basic_types = ["文本", "数字", "日期"]

    def __init__(self, schema_data):
        self.schema_data = schema_data
        self._schemas = self.schema_data["schemas"]

        self.schemas = {schema["name"]: schema for schema in self._schemas}
        self.schema_types = self.schema_data["schema_types"]

        root_schema_data = self._schemas[0]
        self.root_schema = SchemaItem(root_schema_data, [root_schema_data["name"]], self)

    @property
    def enum_types(self):
        return [s["label"] for s in self.schema_types]

    @classmethod
    def create(cls, schema_data):
        return MoldSchema(schema_data)

    def is_enum(self, schema_type):
        return schema_type in self.enum_types

    def is_leaf(self, schema_type):
        return schema_type in self.basic_types or self.is_enum(schema_type)

    def get_enum_values(self, schema_type):
        enum_type = self._get_enum_type(schema_type)
        if enum_type is None:
            return None
        return [value["name"] for value in enum_type["values"]]

    def find_schema_by_path(self, schema_path: Union[str, list], *, is_comp_key=False):
        if isinstance(schema_path, str):
            schema_path = [i.split(":")[0] for i in json.loads(schema_path)][1:]

        current_schema = self.root_schema
        index = 0
        while current_schema.children and index < len(schema_path):
            schema_name = schema_path[index]
            schema = next(i for i in current_schema.children if i.name == schema_name)
            current_schema = schema
            index += 1
        if is_comp_key and current_schema.children:
            # 补全合规字段的schema
            data = current_schema.children[0].data
            data["type"] = current_schema.name
            return SchemaItem(data, current_schema.path, self, current_schema.parent)
        return current_schema

    def is_enum_schema(self, schema):
        enum_schema_labels = [i["label"] for i in self.schema_types]
        return schema.type in enum_schema_labels

    def _get_enum_type(self, schema_type):
        schema_types = (i for i in self.schema_types if i["label"] == schema_type)
        return next(schema_types, None)

    def all_rules(self) -> List[str]:
        # 返回所有的一级字段
        return self.root_schema.orders


class SchemaItem:
    def __init__(self, data: Dict, path: List[str], mold_schema: MoldSchema = None, parent: "SchemaItem" = None):
        self.data = data
        self.path = path
        self.mold_schema = mold_schema
        self.parent = parent
        self.children = [self.create_child(name) for name in self.orders] if self.orders else []
        self.children_idx = {name: i for i, name in enumerate(self.orders)} if self.orders else []

    @property
    def name(self):
        return self.path[-1]

    @property
    def root_name(self):
        return self.path[0]

    @property
    def orders(self):
        return self.data.get("orders", [])

    @property
    def schema(self):
        return self.data.get("schema", {})

    @property
    def type(self):
        return self.data.get("type")

    @property
    def level(self):
        return len(self.path)

    @property
    def is_leaf(self):
        return self.mold_schema.is_leaf(self.type)

    @property
    def is_enum(self):
        return self.mold_schema.is_enum(self.type)

    @cached_property
    def is_amount(self):
        sub_schema = {c.name for c in self.children}
        if "单位" in sub_schema and any(i in sub_schema for i in ["数值", "金额"]):
            return True
        return False

    @cached_property
    def path_key(self):
        return json.dumps(self.path, ensure_ascii=False)

    @cached_property
    def last_enum(self) -> str | None:
        if not self.is_enum:
            return None
        return self.mold_schema.get_enum_values(self.type)[-1]

    @cached_property
    def enums(self) -> str | None:
        if not self.is_enum:
            return []
        return self.mold_schema.get_enum_values(self.type)

    def sibling_path(self, column: str) -> List[str]:
        return sibling_path(self.path, column)

    def children_schema(self, column: str) -> Self:
        return self.children[self.children_idx[column]]

    def create_child(self, name):
        child_path = self.path + [name]
        schema_type = self.schema[name]["type"]
        schemas = self.mold_schema.schemas
        if schema_type in schemas:
            return SchemaItem(schemas[schema_type], child_path, self.mold_schema, parent=self)
        return SchemaItem(self.schema[name], child_path, self.mold_schema, parent=self)

    def to_answer_data(self):
        data = {
            "type": self.data.get("type"),
            "label": self.name,
            "words": self.data.get("words", ""),
            "multi": self.data.get("multi"),
            "required": self.data.get("required"),
            "description": self.data.get("description"),
        }
        return {"data": data}

    def __str__(self):
        return f"SchemaItem<{self.name}>"


def sibling_path(path: List[str], column: str) -> List[str]:
    if not path:
        return [column]
    return path[:-1] + [column]
