import gzip
import json
import logging
import os.path
import pickle
import re
import zipfile
from copy import deepcopy
from dataclasses import asdict
from functools import partial
from importlib import import_module
from multiprocessing import cpu_count

import openpyxl

from remarkable.common.common import (
    B1_B10_COLS,
    JURA21_A_COLS,
    JURA21_B_COLS,
    JURA21_C_COLS,
    <PERSON><PERSON><PERSON>,
    get_keys,
    is_paragraph_elt,
    is_table_elt,
)
from remarkable.common.constants import QuestionStatus, TableType
from remarkable.common.multiprocess import run_by_batch, run_in_multiprocess
from remarkable.common.pattern import PatternCollection
from remarkable.common.util import clean_txt
from remarkable.config import get_config
from remarkable.db import loop_wrapper, pw_db
from remarkable.models.answer import Answer
from remarkable.models.hkex_file_meta import HKEXFileMeta
from remarkable.models.mold import special_mold
from remarkable.models.new_file import NewFile
from remarkable.models.new_mold import NewMold
from remarkable.models.new_question import NewQuestion
from remarkable.optools.export_mark_answers import all_null
from remarkable.optools.stat_scriber_answer_master import StatScriberAnswer
from remarkable.optools.stat_util import Url
from remarkable.pdfinsight.parser import parse_table
from remarkable.pdfinsight.reader import PdfinsightReader, PdfinsightReaderBase
from remarkable.predictor.utils import get_jura21_helper_and_ai_schemes
from remarkable.rule.inspector import inspect_rules
from remarkable.services.question import (
    ctx_debug_share_group,
    ctx_only_jura21,
    ctx_predict_crude_elements,
    merge_answers,
    replace_answer_item,
)

P_WILL_BE = re.compile(r"Non-connected|(will|shall|may)\s*(be\s*)?(allot|issu)", re.I)


async def create_tasks(mold, start, end, file_full_label=False):
    """
    file_full_label 为True时，仅仅load应标尽标的答案 判断应标尽标的方法因schema不同可能会不同
    """
    files = await NewFile.list_by_range(mold.id, start, end, only_label=True)
    tasks = []
    for file in files:
        question = await NewQuestion.find_by_fid_mid(file.id, mold.id)
        if not question:
            continue
        if not question.answer or not file or not file.pdfinsight_path():
            logging.warning(f"No answer found, skip qid: {question.id}")
            continue
        answer_data = await question.collect_answers()
        merge_answer = merge_answers(answer_data)

        if not merge_answer:
            continue
        if file_full_label and mold.id == special_mold.v3r_id and all_null(merge_answer):
            logging.warning(f"Answer should be marked inextenso, skip qid: {question.id}")
            continue
        task = {
            # 'mold_type': mold.mold_type,
            "fid": file.id,
            "qid": question.id,
            "answer": merge_answer,
            "pdfinsight_path": file.pdfinsight_path(abs_path=True),
            "pdf_contents": file.pdf_contents,
        }
        tasks.append(task)

    return tasks


async def prepare_prophet_dataset(mold, start, end, prophet=None, file_full_label=False, pipe_ver=None):
    from remarkable.predictor.base_prophet import prepare_dataset

    workers = get_config("prompter.workers", cpu_count() // 2)
    tasks = await create_tasks(mold, start, end, file_full_label)
    if not prophet:
        prophet = create_predictor_prophet(mold, pipe_ver=pipe_ver)
    run_in_multiprocess(partial(prepare_dataset, prophet.dataset_dir), tasks, workers=workers)
    return [task["fid"] for task in tasks]


@loop_wrapper()
async def _test_load_schema_dataset(mold_id, schema_name, file_id):
    mold = await NewMold.find_by_id(mold_id)
    file = await NewFile.find_by_id(file_id)
    question = await NewQuestion.find_by_fid_mid(file.id, mold.id, fields=(NewQuestion.id,))
    prophet = create_predictor_prophet(mold)

    dataset_path = prophet.dataset_dir.joinpath(schema_name, f"{question.id}.pkl")
    dataset_data = None
    if dataset_path:
        with open(dataset_path, "rb") as dataset_file:
            dataset_data = pickle.load(dataset_file)

    print(dataset_data)


@loop_wrapper()
async def dump_dataset(mold_id, start=0, end=0, file_full_label=False, pipe_ver=None):
    mold = await NewMold.find_by_id(mold_id)
    await prepare_prophet_dataset(mold, start, end, file_full_label=file_full_label, pipe_ver=pipe_ver)


@loop_wrapper()
async def train_answer_data(mold_id, special_rules=None, pipe_ver=None):
    mold = await NewMold.find_by_id(mold_id)
    prophet = create_predictor_prophet(mold, special_rules=special_rules, pipe_ver=pipe_ver)
    prophet.run_train()


def get_special_rules(only_jura21, mold):
    if get_config("ONLY_B1_B10") and mold == special_mold.v2_id:
        return B1_B10_COLS
    if only_jura21:
        match mold:
            case special_mold.v1_id:
                return JURA21_A_COLS + ("丨H38-41",)
            case special_mold.v2_id:
                return JURA21_B_COLS + ("丨H83-92",)
            case special_mold.v3_id:
                return JURA21_C_COLS
    return None


@loop_wrapper()
async def predict_mold_answer(
    mold_id,
    start=None,
    end=None,
    special_rules=None,
    skip_no_answer=False,
    file_ids=None,
    exclude_ids=None,
    only_jura21=False,
    predict_crude_elements=True,
    debug_share_group=False,
):
    from remarkable.predictor.predict import predict_answer

    files = await NewFile.list_by_range(
        mold_id, start, end, only_label=skip_no_answer, file_ids=file_ids, exclude_ids=exclude_ids
    )
    for file in files:
        question = await NewQuestion.find_by_fid_mid(file.id, mold_id)
        if not question or not file.pdfinsight_path():
            continue

        if not special_rules:
            special_rules = get_special_rules(only_jura21, question.mold)

        if skip_no_answer:
            if question.status == QuestionStatus.TODO:
                logging.warning(f"Question should be marked done, skip {file.id=}, {question.id=}")
                continue
            if special_rules:
                rules_str = special_rules if isinstance(special_rules, str) else ",".join(special_rules)
                rule_has_answer = False
                # skip_no_answer精确到具体的规则
                for answer in await Answer.get_answers_by_qid(question.id):
                    if rule_has_answer:
                        break
                    for item in get_keys(answer.data, ["userAnswer", "items"]):
                        if not item.get("value") and not item.get("data"):
                            continue
                        if rules_str in ",".join(key.split(":")[0] for key in json.loads(item["key"])) or (
                            isinstance(special_rules, list)
                            and set(special_rules) & {key.split(":")[0] for key in json.loads(item["key"])[1:]}
                        ):
                            rule_has_answer = True
                            break
                if not rule_has_answer:
                    continue
        # 设置上下文变量only_jura21/predict_crude_elements/debug_share_group
        ctx_only_jura21.set(only_jura21)
        ctx_predict_crude_elements.set(predict_crude_elements)
        ctx_debug_share_group.set(debug_share_group)

        preset_answer = await predict_answer(file, question, special_rules=special_rules)
        if special_rules:
            origin_preset_answer = question.preset_answer
            preset_answer = replace_answer_item(origin_preset_answer, preset_answer, special_rules)
        question.preset_answer = preset_answer
        await question.set_answer()
        await pw_db.update(question)


def create_predictor_prophet(mold, model_version=None, special_rules=None, pipe_ver=None, **kwargs):
    version_id = model_version if model_version else 0
    predictors = model_version.predictors if model_version else mold.predictors
    try:
        utils_module = import_module("remarkable.predictor")
        prophet_config = utils_module.load_prophet_config(mold)
    except ModuleNotFoundError:
        utils_module = import_module("remarkable.predictor.default_predictor.utils")
        prophet_config = utils_module.load_prophet_config(mold, predictors=predictors)
    if not special_rules:
        special_rules = get_special_rules(kwargs.get("only_jura21"), mold.id)
    if special_rules:
        if not isinstance(special_rules, (list, tuple)):
            special_rules = [special_rules]
        special_config = []
        for config in prophet_config["predictor_options"]:
            if any(path in special_rules for path in config["path"]):
                special_config.append(config)
        prophet_config["predictor_options"] = special_config
    instance_v2 = utils_module.make_prophet_instance(prophet_config, mold, version_id)
    if pipe_ver == "2.0" or mold.pipe_ver == "2.0":
        return instance_v2

    from remarkable.predictor.base_prophet import PredictorV1ProphetAdapter

    kwargs["special_rules"] = special_rules
    return PredictorV1ProphetAdapter(mold, version_id, new_prophet=instance_v2, **kwargs)


@loop_wrapper()
async def inspect_rule(fid, mid, answer_type="label", only_jura21=False, special_rules=None):
    file = await NewFile.find_by_id(fid)
    question = await NewQuestion.find_by_fid_mid(fid, mid)
    # 设置上下文变量only_jura21
    ctx_only_jura21.set(only_jura21)
    if question:
        logging.info(f"inspect rules for file: {fid}, question: {question.id}")
        await inspect_rules(file, question, answer_type, special_rules)
        question = await NewQuestion.find_by_fid_mid(fid, mid)
        await question.set_answer()


@loop_wrapper()
async def stat(
    mold_id,
    from_id,
    to_id,
    skip_reg=None,
    host=None,
    orderby=None,
    strict=True,
    only_adjudge_enum=False,
):
    await StatScriberAnswer(
        headnum=5,
        threshold=None,
        from_id=from_id,
        to_id=to_id,
        mold=mold_id,
        host=host,
        skip_reg=skip_reg,
        orderby=orderby,
        strict=strict,
        only_adjudge_enum=only_adjudge_enum,
    ).stat_preset_answer()


def find_next_paragraph(pdfinsight, index, step=1):
    next_elements = pdfinsight.find_elements_near_by(index=index, step=step, steprange=5, aim_types=["PARAGRAPH"])
    next_element = next_elements[0] if next_elements else None
    return next_element


def get_pattern_collection(pattern_collection):
    if pattern_collection and not isinstance(pattern_collection, PatternCollection):
        pattern_collection = PatternCollection(pattern_collection)

    return pattern_collection


class AnswerInspector:
    def __init__(
        self,
        mid,
        start_id,
        stop_id,
        only_label,
        schema_word,
        p_content=None,
        p_first=None,
        p_last=None,
        p_prev=None,
        p_next=None,
        p_table_title=None,
    ):
        self.mid = mid
        self.start_id = start_id
        self.stop_id = stop_id
        self.only_label = only_label
        self.schema_word = schema_word
        self.p_content = p_content
        self.p_first = p_first
        self.p_last = p_last
        self.p_prev = p_prev
        self.p_next = p_next
        self.p_table_title = p_table_title

    def init_pattern(self):
        self.p_content = get_pattern_collection(self.p_content)
        self.p_first = get_pattern_collection(self.p_first)
        self.p_last = get_pattern_collection(self.p_last)
        self.p_prev = get_pattern_collection(self.p_prev)
        self.p_next = get_pattern_collection(self.p_next)
        self.p_table_title = get_pattern_collection(self.p_table_title)

    def print_next_paragraph(self, reader, answer_elements_index):
        if answer_elements_index:
            element = find_next_paragraph(reader, max(answer_elements_index))
            if element:
                print("next_element:")
                next_text = clean_txt(element.get("text", ""))
                if self.is_match(next_text, self.p_next):
                    print("matched")
                else:
                    print(next_text)
            else:
                print("cannot find next_element!")

    def print_prev_paragraph(self, reader, answer_elements_index):
        if answer_elements_index:
            element = find_next_paragraph(reader, min(answer_elements_index), step=-1)
            if element:
                print("prev_element:")
                next_text = element.get("text", "")
                if self.is_match(next_text, self.p_prev):
                    print("matched")
                else:
                    print(next_text)
            else:
                print("cannot find prev_element!")

    def print_table_title(self, reader, answer_elements):
        for _, element in answer_elements.items():
            if not element or element["class"] != "TABLE":
                continue

            table = parse_table(element, tabletype=TableType.TUPLE, pdfinsight_reader=reader)

            print("table_title:")
            table_title = table.title.title_text if table.title else element["title"]
            matched = self.is_match(clean_txt(table_title), self.p_table_title)

            if matched:
                print("matched")
            else:
                print(f"not match for page: {element['page']} ,table_title: {table_title}")

    @classmethod
    def is_match(cls, text, pattern):
        match = pattern.nexts(clean_txt(text, remove_blank=False))
        return match

    async def inspect_answer(self):
        files = await NewFile.list_by_range(self.mid, self.start_id, self.stop_id, only_label=self.only_label)
        rows = []
        # for res in run_by_batch(self.process, files, workers=1, batch_size=100):
        for res in run_by_batch(
            self.process_predict_answer,
            [(await NewQuestion.find_by_fid_mid(file.id, self.mid), file.id, file.tree_id) for file in files],
            workers=4,
            batch_size=500,
        ):
            for items in res:
                if not items:
                    continue
                rows.extend(items)

        # write rows to excel
        workbook = openpyxl.Workbook()
        worksheet = workbook.active
        worksheet.append(["answer", "syllabus", "url"])
        for row in rows:
            worksheet.append(row)
        workbook.save("label_answer.xlsx")

    async def process(self, file: NewFile):
        # if file.id not in ():
        #     return []
        data = []
        question = await NewQuestion.find_by_fid_mid(file.id, self.mid)
        print("\n")
        url = Url(file.id, self.mid, question.id, file.tree_id, schema_key=self.schema_word)
        print(url)
        reader = PdfinsightReader(file.pdfinsight_path(abs_path=True), pdf_contents=file.pdf_contents)
        answer = await question.get_user_merged_answer()
        if not answer:
            print("not answer")
            return data

        for item in answer["userAnswer"]["items"]:
            item_key = item["key"]
            if isinstance(self.schema_word, list) and any(x not in item_key for x in self.schema_word):
                continue
            if isinstance(self.schema_word, str) and self.schema_word not in item_key:
                continue
            # if not item.get('manual'):
            #     continue

            answer_elements = {}

            for data_item in item["data"]:
                boxes = data_item["boxes"]
                answer_texts = []
                answer_elements_text = []
                syllabus_text = set()
                for box_info in boxes:
                    outline = box_info["box"]
                    page = box_info["page"]
                    answer_texts.append(clean_txt(box_info["text"]))
                    item_syllabus_text = []
                    for _type, elt in reader.find_elements_by_outline(page, list(outline.values())):
                        if not elt:
                            logging.warning(f"No elt, fid:{file.id}, page:{page}, outline:{outline}")
                            continue
                        if elt["class"] == "TABLE":
                            answer_elements_text.append(box_info["text"])
                        else:
                            answer_elements_text.append(clean_txt(elt.get("text", "")))

                        answer_elements[elt["index"]] = elt
                        syllabuses = reader.find_syllabuses_by_index(elt["index"])
                        for i in syllabuses:
                            syllabus_text.add(i["title"])
                            item_syllabus_text.append(i["title"])
                    print(item_syllabus_text)
                data.append(
                    [
                        "".join([x for x in answer_elements_text if x]),
                        "".join([x for x in syllabus_text if x]),
                        str(url),
                    ]
                )
                if not answer_texts:
                    continue
                print("-" * 30)
                text = "\n".join(answer_elements_text)
                if self.p_content:
                    # print("content:")
                    if self.is_match(text, self.p_content):
                        pass
                        # print("matched")
                        # print(item["value"])
                        # print(text)
                    else:
                        # print(url)
                        print(text)

                if not answer_elements_text:
                    continue
                if self.p_first:
                    print("first_text:")
                    if self.is_match(answer_elements_text[0], self.p_first):
                        print("matched")
                    else:
                        print(answer_elements_text[0])
                if self.p_last:
                    print("last_text:")
                    if self.is_match(answer_elements_text[-1], self.p_last):
                        print("matched")
                    else:
                        print(answer_elements_text[-1])
                if self.p_prev:
                    self.print_prev_paragraph(reader, answer_elements.keys())
                if self.p_next:
                    self.print_next_paragraph(reader, answer_elements.keys())
                if self.p_table_title:
                    self.print_table_title(reader, answer_elements)
                print("+" * 30)

            return data

    def process_predict_answer(self, question: NewQuestion, file_id: int, tree_id: int):
        if not question:
            return
        answer = question.preset_answer
        if not answer:
            print("not answer")
            return
        for item in answer["userAnswer"]["items"]:
            item_key = item["key"]
            if isinstance(self.schema_word, list) and any(x not in item_key for x in self.schema_word):
                continue
            if isinstance(self.schema_word, str) and self.schema_word not in item_key:
                continue
            if item.get("value") != "No":
                continue
            for data_item in item["data"]:
                boxes = data_item["boxes"]
                answer_texts = []
                for box_info in boxes:
                    # outline, page = box_info["box"], box_info["page"]
                    answer_texts.append(clean_txt(box_info["text"]))
                if not answer_texts:
                    continue
                text = " ".join(answer_texts)
                if P_WILL_BE.search(text):
                    print("#+" * 40)
                    url = Url(file_id, self.mid, question.id, tree_id, schema_key=self.schema_word)
                    print(url)
                    print(text)
                    print("#+" * 40)
                    print("\n")

            return


@loop_wrapper()
async def inspect_answer(mid, start_id, stop_id, schema_word, only_label):
    p_table_title = PatternCollection(
        [".*"],
        flags=re.I,
    )
    if isinstance(schema_word, list):
        schema_word = schema_word[0]
    answer_inspector = AnswerInspector(
        mid,
        start_id,
        stop_id,
        only_label,
        schema_word,
        p_content=p_table_title,
    )
    await answer_inspector.inspect_answer()


@loop_wrapper()
async def prophet_config_assist(mid):
    """
    辅助生成prophet_config
    :param mid:
    :return:
    """
    template = {
        "path": None,
        "models": [
            {
                "name": "score_filter",
                "threshold": 0.618,
            }
        ],
    }
    predictor_options = []
    mold = await NewMold.find_by_id(mid)
    schema = Schema(mold.data)
    for item in schema.leaf_iter():
        option = deepcopy(template)
        option["path"] = item[1:]
        predictor_options.append(option)

    with open("prophet_config.json", "w", encoding="utf-8") as file_obj:
        json.dump(predictor_options, file_obj, ensure_ascii=False)


def gene_thin_interdoc_data(
    pdfinsight: PdfinsightReader, table_indices: list | None = None, p_tables_chapter: PatternCollection | None = None
):
    """
    提取简化版interdoc数据
    table相关参数若没配置，则不会提取table
    """
    new_paras = []
    for paragraph in pdfinsight.data["paragraphs"]:
        new_paragraph = {
            k: v
            for k, v in paragraph.items()
            if k
            in {
                "index",
                "page",
                "class",
                "type",
                "text",
                "syllabus",
                "outline",
                "fragment",
                "page_merged_paragraph",
                "position",
            }
        }
        new_paragraph["chars"] = [
            {"style": char["style"], "text": char["text"], "box": char["box"], "font_box": char["font_box"]}
            for char in paragraph["chars"]
        ]
        new_paras.append(new_paragraph)

    thin_data = {
        "syllabuses": deepcopy(pdfinsight.data["syllabuses"]),
        "paragraphs": new_paras,
        "styles": pdfinsight.data["styles"],
        "pages": pdfinsight.data["pages"],
        "images": [
            {k: v for k, v in img.items() if k in ("index", "page", "type", "class", "outline")}
            for img in pdfinsight.data.get("images", [])
        ],
        "shapes": [
            {k: v for k, v in shape.items() if k in ("index", "page", "type", "class", "outline")}
            for shape in pdfinsight.data.get("shapes", [])
        ],
    }
    # 注意：如果要考虑所有table，cells中的chars会导致文件压缩比很大
    # 所以这里限制根据条件取需要的table
    # if table_indices or p_tables_chapter:
    tables, tbl_indices = [], []
    for table in pdfinsight.data["tables"]:
        if table_indices and table["index"] not in table_indices:
            tables.append({k: v for k, v in table.items() if k in ("index", "page", "type", "class", "outline")})
            continue
        syllabuses = pdfinsight.find_syllabuses_by_index(table["index"])
        if not any(p_tables_chapter.nexts(syll["title"]) for syll in syllabuses):
            continue
        new_table = deepcopy(table)
        for key in set(new_table) - {
            "index",
            "class",
            "title",
            "merged",
            "cells",
            "page",
            "chars",
            "syllabus",
            "outline",
            "page_merged_table",
        }:
            new_table.pop(key)
        for cell in new_table["cells"].values():
            for key in {"styles", "styles_diff"}:
                cell.pop(key)
        tbl_indices.append(new_table["index"])
        tables.append(new_table)
    if tables:
        print("table_indices: ", tbl_indices)
        thin_data["tables"] = tables
    return thin_data


@loop_wrapper()
async def zip_files_for_test_share_group(
    mold_id,
    start=None,
    end=None,
    file_ids=None,
    table_indices: list | None = None,
    p_tables_chapter: PatternCollection | None = None,
    adoption_indices: dict[int, set[int]] = None,
    # 如果是重新生成zip包，是否重新生成ai预测结果
    overwrite_ai_schemes: bool = False,
):
    """
    将pdfinsight及初步预测结果简化后，压缩到{file.id}.zip中
    用于生成tests/list_rule_group/test_group_info.py所需的测试文件
    """
    from remarkable.predictor.share_group_gpt import get_scheme_results

    files = await NewFile.list_by_range(mold_id, start, end, file_ids=file_ids)
    count = len(files)
    for i, file in enumerate(files, start=1):
        logging.info(f"------{file.id}------")
        question = await NewQuestion.find_by_fid_mid(file.id, mold_id, fields=(NewQuestion.crude_answer,))
        if not question or not file.pdfinsight_path():
            continue
        pdfinsight = PdfinsightReader.from_path(file.pdfinsight_path(abs_path=True))
        interdoc_data = gene_thin_interdoc_data(pdfinsight, table_indices, p_tables_chapter)
        crude_elements = await get_jura21_helper_and_ai_schemes(pdfinsight, file, as_dict=True)
        zip_file = f"data/tests/list_rule_group/{file.id}.zip"
        if not overwrite_ai_schemes and os.path.isfile(zip_file):
            with zipfile.ZipFile(zip_file, "r") as zip_ref:
                existed_elements = json.loads(zip_ref.read("crude_elements.json"))
                crude_elements["ai_schemes"] = existed_elements["ai_schemes"]
        elif adoption_indices:
            # GPT预测adoption date特别费时，在这里预测，存到zip中
            ai_schemes = crude_elements.get("ai_schemes") or {}
            for index in adoption_indices.get(file.id) or []:
                if index in ai_schemes:
                    continue
                _, elem = pdfinsight.find_element_by_index(index)
                if schemes := get_scheme_results(index, clean_txt(elem["text"], remove_cn_text=True)):
                    ai_schemes[index] = [asdict(scheme) for scheme in schemes]
            if ai_schemes:
                crude_elements["ai_schemes"] = ai_schemes
        if file_meta := await HKEXFileMeta.find_by_fid(file.id):
            crude_elements["report_year"] = file_meta.report_year
            crude_elements["company"] = file_meta.name
        logging.info(f"[{i}/{count}] Compress to file: %s", os.path.abspath(zip_file))
        with zipfile.ZipFile(zip_file, "w", compression=zipfile.ZIP_DEFLATED) as zip_fp:
            zip_fp.writestr("crude_elements.json", json.dumps(crude_elements, ensure_ascii=False))
            zip_fp.writestr("simple_interdoc.json", json.dumps(interdoc_data))
            zip_fp.writestr("pdf_contents.json", json.dumps(file.pdf_contents))


@loop_wrapper()
async def zip_file_for_test_chapter_fixer(mold_id: int, file_ids: list, start=None, end=None):
    """
    将pdfinsight及初步预测结果简化后，压缩到{file.id}.zip中
    用于生成tests/list_rule_group/test_group_info.py所需的测试文件
    """
    files = await NewFile.list_by_range(mold_id, start, end, file_ids=file_ids)
    count = len(files)
    for i, file in enumerate(files, start=1):
        if not file.pdfinsight_path():
            continue
        pdfinsight = PdfinsightReaderBase.from_path(file.pdfinsight_path(abs_path=True))
        new_paras, new_tables = [], []
        # 前3页取页面全部元素块，第3页之后仅取段落
        for page, page_paras in pdfinsight.page_element_dict.items():
            for item in page_paras:
                element = item.data
                if page > 3 and not is_paragraph_elt(element):
                    if is_table_elt(element):
                        mock_cells = {"0_0": {"text": "mock"}}
                        new_paras.append(
                            {"cells": mock_cells, "merged": []}
                            | {
                                k: v
                                for k, v in element.items()
                                if k in {"index", "page", "class", "type", "outline", "position"}
                            }
                        )
                    else:
                        new_paras.append(
                            {
                                k: v
                                for k, v in element.items()
                                if k in {"index", "page", "class", "type", "outline", "position"}
                            }
                        )
                    continue
                if is_paragraph_elt(element):
                    new_paras.append(
                        {
                            k: v
                            for k, v in element.items()
                            if k in {"index", "page", "class", "type", "outline", "text", "chars", "position"}
                        }
                    )
                elif is_table_elt(element):
                    new_tables.append(
                        {
                            k: v
                            for k, v in element.items()
                            if k in {"index", "page", "class", "title", "cells", "merged"}
                        }
                    )
                else:
                    new_paras.append(
                        {
                            k: v
                            for k, v in element.items()
                            if k in {"index", "page", "class", "type", "outline", "position"}
                        }
                    )
        thin_data = {
            "syllabuses": deepcopy(pdfinsight.data["syllabuses"]),
            "paragraphs": new_paras,
            "tables": new_tables,
            "styles": pdfinsight.data["styles"],
            "pages": pdfinsight.data["pages"],
        }
        zip_file = f"data/tests/test_chapter_fixer/{file.id}.zip"
        logging.info(f"[{i}/{count}] write to file: %s", os.path.abspath(zip_file))
        with zipfile.ZipFile(zip_file, "w", compression=zipfile.ZIP_DEFLATED) as zip_fp:
            zip_fp.writestr("pdfinsight.json", json.dumps(thin_data, ensure_ascii=False))
            zip_fp.writestr("pdf_contents.json", json.dumps(file.pdf_contents, ensure_ascii=False))


@loop_wrapper()
async def zip_tbl_rows_for_test_section_rows(mold_id: int, file_tbl_ids: dict):
    """
    将指定表格元素块转parse_table后，取table.rows压缩到{file.id}_{table_index}.pkl.zip中
    用于生成tests/test_find_all_section_rows.py所需的测试文件
    """
    files = await NewFile.list_by_range(mold_id, file_ids=list(file_tbl_ids))
    count = len(files)
    for i, file in enumerate(files, start=1):
        if not file.pdfinsight_path():
            continue
        pdfinsight = PdfinsightReader.from_path(file.pdfinsight_path(abs_path=True))
        target_file_prefix = f"data/tests/section_rows/{file.id}"
        for tbl_index in file_tbl_ids[file.id]:
            element = pdfinsight.find_element_by_index(tbl_index)[1]
            table = parse_table(element, tabletype=TableType.TUPLE, pdfinsight_reader=pdfinsight)
            file_name = f"{target_file_prefix}_{tbl_index}.pkl.zip"
            with open(file_name, "wb") as f:
                f.write(gzip.compress(pickle.dumps(table.rows)))
        logging.info(f"[{i}/{count}] write to file: %s", os.path.abspath(target_file_prefix))


@loop_wrapper()
async def zip_test_file_by_pages(file_pages: dict[int, list]):
    """
    将pdfinsight指定页码的所有元素块压缩到{file.id}.zip中
    用于生成tests/test_pdfinsight_reader.py所需的测试文件
    file_pages配置格式： {66236: [1-5, 2-10]}
    """
    zip_path = "data/tests/test_pdfinsight_reader"
    if not os.path.isdir(zip_path):
        os.mkdir(zip_path)
    files = await NewFile.list_by_range(file_ids=list(file_pages))
    count = len(files)
    for i, file in enumerate(files, start=1):
        if not file.pdfinsight_path():
            continue
        pdfinsight = PdfinsightReaderBase.from_path(file.pdfinsight_path(abs_path=True))
        new_paras, new_tables = [], []
        page_ranges = [[int(p) for p in page.split("-")] for page in file_pages[file.id]]
        # 前3页取页面全部元素块，第3页之后仅取段落
        for page, page_paras in pdfinsight.page_element_dict.items():
            for item in page_paras:
                element = item.data
                if not any(rng[0] <= page <= rng[1] for rng in page_ranges):
                    continue
                if is_paragraph_elt(element):
                    new_paras.append(element)
                elif is_table_elt(element):
                    new_tables.append(element)
                else:
                    new_paras.append(
                        {
                            k: v
                            for k, v in element.items()
                            if k in {"index", "page", "class", "type", "outline", "position"}
                        }
                    )
        thin_data = {
            "syllabuses": deepcopy(pdfinsight.data["syllabuses"]),
            "paragraphs": new_paras,
            "tables": new_tables,
            "styles": pdfinsight.data["styles"],
            "pages": pdfinsight.data["pages"],
        }
        zip_file = f"{zip_path}/{file.id}.zip"
        logging.info(f"[{i}/{count}] write to file: %s", os.path.abspath(zip_file))
        with zipfile.ZipFile(zip_file, "w", compression=zipfile.ZIP_DEFLATED) as zip_fp:
            zip_fp.writestr("pdfinsight.json", json.dumps(thin_data, ensure_ascii=False))
            # zip_fp.writestr("pdf_contents.json", json.dumps(file.pdf_contents, ensure_ascii=False))


def main(mid, from_id, to_id=None, special_rules=None, only_jura21: bool = False):
    for name in (
        "remarkable.common.pattern",
        "remarkable.predictor.models.base_model",
        "remarkable.plugins.predict.predictor",
        "remarkable.predictor.models.posneg",
    ):
        logging.getLogger(name).setLevel(logging.DEBUG)
    to_id = to_id or from_id
    # dump_dataset(mid, from_id, to_id)
    # train_answer_data(mid, special_rules=special_rules)
    predict_mold_answer(mid, from_id, to_id, special_rules=special_rules, only_jura21=only_jura21)
    # inspect_rule(from_id, mid, answer_type='preset')
    # inspect_answer(mid, from_id, to_id, special_rules, True)
    # stat(mid, from_id, to_id)


if __name__ == "__main__":
    # main(15, 80856, special_rules=None)
    # zip_file_for_test_chapter_fixer(
    #     15,
    #     file_ids=[
    #         66818,
    #         66624,
    #         88698,
    #         67609,
    #         66635,
    #         66691,
    #         66645,
    #         68760,
    #         66669,
    #         83666,
    #         83437,
    #         79882,
    #         83589,
    #         83654,
    #         90196,
    #         83431,
    #         86364,
    #     ],
    # )
    # --------------------------------------------------------
    # from remarkable.optools.export_prompt_for_llm import P_SCHEME_TITLE
    #
    # zip_files_for_test_share_group(
    #     15,
    #     file_ids=[
    #         66213,
    #         66219,
    #         66236,
    #         66263,
    #         66322,
    #         66430,
    #         66435,
    #         66449,
    #         66477,
    #         66483,
    #         66487,
    #         66488,
    #         66552,
    #         66615,
    #         68516,
    #         68591,
    #         68646,
    #         68663,
    #         68664,
    #         68665,
    #         68744,
    #         68795,
    #         68823,
    #         70136,
    #         70159,
    #         70162,
    #         70179,
    #     ],
    #     p_tables_chapter=PatternCollection([*P_SCHEME_TITLE.patterns, r"INTERESTS\s*IN\s*SUBSIDIARIES"], flags=re.I),
    #     adoption_indices={
    #         66219: {1790, 1809},
    #         68823: {1006},
    #     },
    # )
    # --------------------------------------------------------
    # zip_tbl_rows_for_test_section_rows(15, {70541: [2484, 2530]})
    # --------------------------------------------------------
    zip_test_file_by_pages(
        {
            66236: ["29-32", "85-107"],
            66449: ["101-109"],
            66818: ["85-90"],
            68744: ["39-45", "47-51", "71-76", "79-83"],
            114544: ["54-57", "115-130"],
            114184: ["116-135"],
            113257: ["125-146", "185-190"],
            112806: ["37-41", "92-95", "131-144", "155-158", "168-177", "195-201", "248-251"],
            74567: ["0-1"],
        },
    )
