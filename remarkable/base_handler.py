import asyncio
import datetime
import email.utils
import functools
import http
import importlib
import json
import logging
import mimetypes
import os
import re
import urllib.parse
import warnings
from concurrent.futures import ThreadPoolExecutor
from enum import IntEnum, unique
from pathlib import Path
from typing import Any, Awaitable, Callable, Union

import tornado.web
from tornado import httputil, iostream
from tornado.escape import utf8

from remarkable.common.constants import API_PREFIX_V1, PageModule, UserRole, all_permissions
from remarkable.common.exceptions import CustomError
from remarkable.common.pattern import PatternCollection
from remarkable.common.util import QueryHelper, need_secure_cookie
from remarkable.config import get_config
from remarkable.db import pw_db
from remarkable.models.user import AdminUser
from remarkable.security.authtoken import _generate_token, generate_timestamp
from remarkable.session import SessionMixin

EXPIRE_PROMPT = "The session has expired, please login again"
error_msg_p = re.compile(r"\"([\w\s]{2,}?.*?)\"")


@unique
class AuthError(IntEnum):
    NO_ERROR = 0
    NO_USER = 1
    NO_PERMISSION = 2


class route:
    HANDLERS = []

    def __init__(self, router_url, prefix=API_PREFIX_V1):
        self.router_url = router_url
        self.prefix = prefix

    def __call__(self, clz):
        url = "{}{}".format(self.prefix, self.router_url)
        self.__class__.HANDLERS.append((url, clz))
        return clz

    @classmethod
    def init_handlers(cls):
        logging.info("loading web handlers")
        for handler in ("user", "data", "meta"):
            importlib.import_module(f"remarkable.{handler}.handlers")

        logging.info("loading plugins")
        for plugin in get_config("web.plugins", []):
            logging.info(f"\t- loading plugin: {plugin}")
            try:
                mod = importlib.import_module(f"remarkable.plugins.{plugin}")
            except ModuleNotFoundError as exp:
                logging.warning(f"{exp}")
            else:
                getattr(mod, "init", lambda: logging.warning('No "init" func found'))()

        logging.info("handler list:")
        for url, clz in cls.HANDLERS:
            clz.url = url
            logging.info(url)
        return cls.HANDLERS


class BaseHandler(tornado.web.RequestHandler, SessionMixin):
    executor = ThreadPoolExecutor(10)

    def __init__(self, application, request, **kwargs):
        super(BaseHandler, self).__init__(application, request, **kwargs)
        self.user_data = None

    def set_etag_header(self):
        pass

    def check_etag_header(self):
        return False

    @classmethod
    def run_in_executor(cls, func: Callable, *args: Any) -> Awaitable:
        """Runs a function in a ``concurrent.futures.Executor``. If
        ``executor`` is ``None``, the IO loop's default executor will be used.

        In general, using ``run_in_executor``
        when *calling* a blocking method is recommended instead of using
        this decorator when *defining* a method.
        """
        return asyncio.get_event_loop().run_in_executor(cls.executor, func, *args)

    def is_first_fetch_file(self):
        range_header = self.request.headers.get("Range")
        if range_header:
            # As per RFC 2616 14.16, if an invalid Range header is specified,
            # the request will be treated as if the header didn't exist.
            request_range = httputil._parse_request_range(range_header)
            if request_range:
                start, *_ = request_range
                return not start
        return True

    async def prepare(self):
        if self.request.method.lower() in {"post", "put", "delete"}:
            self.check_xsrf()

        if user := await AdminUser.find_by_id(int(self.session.user_id)):
            self.current_user: AdminUser = user
            if (
                doc_type := (self.get_argument("dt", None) or self.get_argument("doc_type", None))
            ) and doc_type not in user.accessible_module_abbrs:
                self.error(
                    _(f"You have no access to this page module: {PageModule.abbr_to_enum(doc_type).label}"),
                    http.HTTPStatus.FORBIDDEN,
                )
                await self.finish()

    def check_xsrf(self):
        if not self.request.path.startswith("/api/v"):
            return None
        if not get_config("web.xsrf_cookies", False):
            return None
        for route_ext in get_config("web.trust_routes", []):
            if re.search(rf"^/api/v\d+{route_ext}$", self.request.path):
                logging.debug("Skip xsrf check: %s", self.request.path)
                return None
        url = self.request.full_url()
        query = urllib.parse.urlparse(url).query
        params = urllib.parse.parse_qs(query) if query else {}
        if all([v for k, v in params.items() if k in ("_token", "_timestamp")] or [None]):
            logging.debug("Skip xsrf check: %s", self.request.path)
            return None
        try:
            Auth.referer_check(self)
            super(BaseHandler, self).check_xsrf_cookie()
        except tornado.web.HTTPError:
            return Auth.custom_error(self, http.HTTPStatus.UNAUTHORIZED, EXPIRE_PROMPT)
        return None

    @property
    def origin_host(self):
        scheme = self.request.headers.get("X-Scheme") or "http"
        origin_host = self.request.headers.get("Host") or self.request.host
        origin_host = scheme + "://" + origin_host
        return origin_host

    def redirect(self, url, permanent=False, status=None):
        if self._headers_written:
            raise Exception("Cannot redirect after headers have been written")
        if status is None:
            status = 301 if permanent else 302
        else:
            assert isinstance(status, int) and 300 <= status <= 399
        subpath = get_config("web.redirect_subpath")
        if subpath:
            prefix = urllib.parse.urljoin(self.origin_host, subpath.lstrip("/"))
            prefix = prefix.rstrip("/")
            if url.startswith("/"):
                url = prefix + url
            elif url.startswith(("http", "https")):
                pass
            else:
                url = prefix + "/" + url
        self.set_status(status)
        self.set_header("Location", utf8(url))
        self.finish()

    def set_default_headers(self):
        # avoid Clickjacking: https://www.ietf.org/rfc/rfc7034.txt
        self.set_header("X-Frame-Options", "DENY")
        self.set_header("Server", "*")

    def data_received(self, chunk):
        pass

    def write_error(self, status_code, **kwargs):
        ex = kwargs.get("exc_info")[1]
        if status_code == http.HTTPStatus.FORBIDDEN:
            # TODO: 临时解决方案 403 -> 401
            self.error(EXPIRE_PROMPT, status_code=http.HTTPStatus.UNAUTHORIZED)
        elif isinstance(ex, CustomError):
            self.error(ex.msg, status_code=ex.resp_status_code)
        elif status_code == http.HTTPStatus.UNPROCESSABLE_ENTITY and "exc_info" in kwargs:
            etype, exc, traceback = kwargs["exc_info"]
            if hasattr(exc, "messages"):
                if getattr(exc, "headers", None):
                    for name, val in exc.headers.items():
                        self.set_header(name, val)
                if isinstance(exc.messages, dict):
                    msg = ""
                    for exc_msg in exc.messages.values():
                        matches = error_msg_p.findall(json.dumps(exc_msg))
                        if matches:
                            msg = ": ".join(matches[:2])
                            break
                    self.error(msg or json.dumps(exc.messages), errors=exc.messages)
                else:
                    self.error(message="Invalid request payload", errors=exc.messages)
        else:
            super(BaseHandler, self).write_error(status_code, **kwargs)

    @staticmethod
    def get_user_from_subsys_uinfo(uinfo, user_type, max_age_days):
        uinfo = urllib.parse.unquote(uinfo)
        uinfo_list = uinfo.split("|")
        user_dict = {}
        for uinfo_ in uinfo_list:
            key, val = uinfo_.split(":")
            user_dict[key] = val

        src = "uid:{}|typ:{}|grp:{}".format(user_dict["uid"], user_dict["typ"], user_dict["grp"])
        tm_data = user_dict["tm"]
        if generate_timestamp() - int(tm_data) > max_age_days * 86400:
            logging.error("token expired")
            return False

        new_tk = _generate_token(
            src,
            get_config("app.auth.roster.app_id"),
            get_config("app.auth.roster.secret_key"),
            timestamp=user_dict["tm"],
        )
        if new_tk != user_dict["tk"]:
            logging.error("validate not pass: %s != %s", new_tk, user_dict["tk"])
            return None

        return user_type(int(user_dict["uid"]), user_dict["typ"], "", "", user_dict["grp"])

    def is_admin(self):
        all_perm = sorted(all_permissions())
        user_perm = sorted([perm["perm"] for perm in self.current_user.permission if "prj_filter" not in perm])
        return user_perm == all_perm or self.current_user.role == UserRole.BUSINESS_ADMIN

    def have_permission(self, required_permission):
        if not self.current_user:
            return AuthError.NO_USER

        if not isinstance(required_permission, list):
            required_permission = [required_permission]
        for perm in required_permission:
            have_perm = False
            for user_perm in self.current_user.permission:
                if perm == user_perm["perm"]:
                    have_perm = True
                    break
            if perm.upper() == UserRole(self.current_user.role).name:
                have_perm = True

            if not have_perm:
                return AuthError.NO_PERMISSION
        return AuthError.NO_ERROR

    def have_roster_permission(self, permission):
        if not self.current_user or not self.current_user.roster_cn:
            logging.error("user or user.roster_cn is none")
            return AuthError.NO_USER

        logging.info(self.current_user)
        perm_list = [perm.upper() for perm in self.current_user.roster_permission.split(",")]
        if permission in perm_list:
            return AuthError.NO_ERROR

        logging.error("user has no permission")
        return AuthError.NO_PERMISSION

    def get_json_body(self):
        """Return the body of the request as JSON data.
        建议使用 [webargs](https://webargs.readthedocs.io/en/latest/index.html) 接收&验证请求参数
        """
        warnings.warn(
            "The 'get_json_body' method is deprecated, use 'webargs' instead", DeprecationWarning, stacklevel=2
        )
        if not self.request.body:
            return None
        # Do we need to call body.decode('utf-8') here?
        body = self.request.body.strip().decode("utf-8")
        try:
            model = json.loads(body)
        except Exception:
            logging.debug("Bad JSON: %r", body)
            logging.error("Couldn't parse JSON", exc_info=True)
            raise tornado.web.HTTPError(400, "Invalid JSON in body of request") from Exception
        return model

    def send_json(self, data, **kwargs):
        self.set_header("Content-Type", "application/json")
        self.write(json.dumps(data))

    def data(self, data, binary=None, handshake=False, *, raw=False):
        if not raw:
            # 默认组装返回数据
            data = {"status": "ok", "data": data}
        self.send_json(data, binary=binary, handshake=handshake)

    def error(self, message, status_code=200, errors=None, binary=None, handshake=False):
        self.set_status(status_code)
        self.send_json(
            {"status": "error", "message": message, "errors": errors or {}}, binary=binary, handshake=handshake
        )

    def on_finish(self):
        # TODO: commit dbsession?
        self.session_refresh()

    async def export(
        self,
        abs_path: Union[str, bytes, Path],
        file_name: str | None = None,
        content_type: str = "application/octet-stream",
    ):
        def set_content_length(this, path, req_range):
            size = os.path.getsize(path)
            if req_range:
                start, end = req_range
                if (start is not None and start >= size) or end == 0:
                    # As per RFC 2616 14.35.1, a range is not satisfiable only: if
                    # the first requested byte is equal to or greater than the
                    # content, or when a suffix with length 0 is specified
                    this.set_status(416)  # Range Not Satisfiable
                    this.set_header("Content-Type", "text/plain")
                    this.set_header("Content-Range", "bytes */%s" % (size,))
                    return start, end
                if start is not None and start < 0:
                    start += size
                if end is not None and end > size:
                    # Clients sometimes blindly use a large range to limit their
                    # download size; cap the endpoint at the actual file size.
                    end = size
                # Note: only return HTTP 206 if less than the entire range has been
                # requested. Not only is this semantically correct, but Chrome
                # refuses to play audio if it gets an HTTP 206 in response to
                # ``Range: bytes=0-``.
                if size != (end or size) - (start or 0):
                    this.set_status(206)  # Partial Content
                    this.set_header("Content-Range", httputil._get_content_range(start, end, size))
            else:
                start = end = None

            if start is not None and end is not None:
                length = end - start
            elif end is not None:
                length = end
            elif start is not None:
                length = size - start
            else:
                length = size
            this.set_header("Content-Length", length)
            return start, end

        def get_content_type(path):
            mime_type, encoding = mimetypes.guess_type(path)
            # per RFC 6713, use the appropriate type for a gzip compressed file
            if encoding == "gzip":
                return "application/gzip"
            # As of 2015-07-21 there is no bzip2 encoding defined at
            # http://www.iana.org/assignments/media-types/media-types.xhtml
            # So for that (and any other encoding), use octet-stream.
            if mime_type is not None:
                return mime_type
            # if mime_type not detected, use application/octet-stream
            return "application/octet-stream"

        def should_return_304(this):
            if this.check_etag_header():
                return True

            # Check the If-Modified-Since, and don't send the result if the
            # content has not been modified
            ims_value = this.request.headers.get("If-Modified-Since")
            if ims_value is not None:
                date_tuple = email.utils.parsedate(ims_value)
                if date_tuple is not None:
                    if_since = datetime.datetime(*date_tuple[:6])
                    assert this.modified is not None
                    if if_since >= this.modified:
                        return True

            return False

        if isinstance(abs_path, bytes):
            if not content_type:
                content_type = "application/octet-stream"
            self.set_header("Content-Type", content_type)
            if file_name:
                file_name = urllib.parse.quote(file_name)
                self.set_header("Content-Disposition", f"attachment; filename={file_name}")
            return await self.finish(abs_path)
        if not os.path.exists(abs_path):
            raise CustomError(_("File not found"), resp_status_code=http.HTTPStatus.NOT_FOUND)
        if not file_name:
            file_name = os.path.basename(abs_path)
        file_name = urllib.parse.quote(file_name)
        self.set_header("Content-Disposition", f"attachment; filename={file_name}")
        if not content_type:
            content_type = get_content_type(abs_path)
        self.set_header("Content-Type", content_type)

        self.modified = datetime.datetime.utcfromtimestamp(os.path.getmtime(abs_path))
        self.set_header("Last-Modified", self.modified)
        cache_time = tornado.web.StaticFileHandler.CACHE_MAX_AGE if "v" in self.request.arguments else 0
        if cache_time > 0:
            self.set_header(
                "Expires",
                datetime.datetime.utcnow() + datetime.timedelta(seconds=cache_time),
            )
            self.set_header("Cache-Control", "max-age=" + str(cache_time))
        if should_return_304(self):
            self.set_status(304)
            return None

        request_range = None
        if get_config("web.support_http_partial_content", True):
            self.set_header("Accept-Ranges", "bytes")
            range_header = self.request.headers.get("Range")
            if range_header:
                # As per RFC 2616 14.16, if an invalid Range header is specified,
                # the request will be treated as if the header didn't exist.
                request_range = httputil._parse_request_range(range_header)
        start, end = set_content_length(self, abs_path, request_range)

        if self.request.method == "GET":
            from remarkable.common.storage import (
                LocalStorage,
            )

            async for chunk in LocalStorage.chunk_read(abs_path, start, end):
                try:
                    self.write(chunk)
                except iostream.StreamClosedError:
                    return None
        else:
            assert self.request.method == "HEAD"

    def clear_all_cookies(self, **kwargs: Any) -> None:
        kwargs.update(
            {
                "httponly": True,
                "secure": need_secure_cookie,
                "samesite": "none" if need_secure_cookie else "lax",
            }
        )
        for name in self.cookies:
            if not name.endswith("_session_id"):
                self.clear_cookie(name, **kwargs)


class Custom404Handler(BaseHandler):
    """自定义错误页面"""

    url = ""
    # TODO: 用户正常访问期间被踢出，有些接口拿不到合法的id会报404，需要换成401
    invalid_path_pattern = PatternCollection(
        [
            r"null/quarter_reports$",  # /api/v1/plugins/hkex/stocks//years/null/quarter_reports
        ]
    )

    def prepare(self):
        if self.invalid_path_pattern.nexts(self.request.path):
            self.error(_(EXPIRE_PROMPT), http.HTTPStatus.FORBIDDEN)
        else:
            self.error(_("Undefined routing request"), http.HTTPStatus.NOT_FOUND)
        self.finish()

    def check_xsrf_cookie(self):
        # POSTs to an ErrorHandler don't actually have side effects,
        # so we don't need to check the xsrf token.  This allows POSTs
        # to the wrong url to return a 404 instead of 403.
        pass


class DbQueryHandler(BaseHandler):
    async def querydata(self, query, columns, params=None):
        return await QueryHelper.query_data(query, columns, params)

    async def pagedata_from_request(self, request, query, columns, orderby="", params=None):
        return await QueryHelper.pagedata_from_request(request, query, columns, orderby, params)


class Auth:
    referer_prefix = f"{get_config('web.scheme')}://{get_config('web.domain').split(':')[0]}"

    def __init__(self, required_permission):
        self.required_permission = required_permission

    @classmethod
    def referer_check(cls, this):
        referer = this.request.headers.get("referer")
        if not referer or not referer.startswith(cls.referer_prefix):
            logging.error("Referer check failed: %s", this.request.path)
            raise tornado.web.HTTPError(http.HTTPStatus.FORBIDDEN, "Referer check failed")

    def auth_check(self, this):
        for route_ext in get_config("web.trust_routes", []):
            if re.search(rf"^/api/v\d+{route_ext}$", this.request.path):
                logging.debug("Skip auth check: %s", this.request.path)
                return AuthError.NO_ERROR
        if this.current_user is None:
            # logging.error('user is none')
            return AuthError.NO_USER
        else:
            return this.have_permission(self.required_permission)

    @staticmethod
    def is_ws(this):
        return getattr(this, "_status_code", None) == http.HTTPStatus.SWITCHING_PROTOCOLS and hasattr(
            this, "ws_connection"
        )

    @staticmethod
    def custom_error(this, status_code, reason):
        if Auth.is_ws(this):  # deal with websocket connections
            this.write_message({"stage": reason})
            this.close()
        else:
            this.error(_(reason), status_code=status_code)
            this.finish()
        raise tornado.web.HTTPError(status_code, reason=reason)

    def __call__(self, method):
        @functools.wraps(method)
        def wrapper(this, *args, **kwargs):
            check_ret = self.auth_check(this)
            if check_ret == AuthError.NO_PERMISSION:
                return self.custom_error(this, http.HTTPStatus.FORBIDDEN, "You have no permission")
            if check_ret == AuthError.NO_USER:
                this.session_clear()
                if not self.is_ws(this):
                    this.clear_all_cookies()
                return self.custom_error(
                    this, http.HTTPStatus.UNAUTHORIZED, "The session has expired, please login again"
                )

            return method(this, *args, **kwargs)

        return wrapper


def peewee_transaction_wrapper(method):
    @functools.wraps(method)
    async def wrapper(*args, **kwargs):
        async with pw_db.atomic():
            return await method(*args, **kwargs)

    return wrapper
