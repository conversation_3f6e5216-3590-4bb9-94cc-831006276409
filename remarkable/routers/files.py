import gzip
import http
import logging
import pickle

import celery
from fastapi import APIRouter, Depends, HTTPException, Request
from speedy.middleware.i18n import translate
from speedy.pai_response import PaiStreamingResponse
from typing_extensions import Annotated

from remarkable.common.constants import INVALID_STOCK_CODE_MSG
from remarkable.db import pw_db
from remarkable.dependencies import get_current_user, model_with_perm
from remarkable.models.new_file import NewFile
from remarkable.models.new_mold import NewMold
from remarkable.models.new_question import NewQuestion
from remarkable.predictor.mold_schema import MoldSchema
from remarkable.services.file import Ratio4Result, SyncBakery, get_ratio4_by_file

router = APIRouter(prefix="/files", tags=["files"])
logger = logging.getLogger(__name__)


@router.post("/sync")
async def sync_files(request: Request):
    bakery: SyncBakery = pickle.loads(await request.body())
    if bakery.fid == -1:
        # 仅更新PDFinsight数据
        for row in bakery.rows[:]:
            if isinstance(row, NewFile):
                file = await NewFile.get_first_one(NewFile.hash == row.hash)
                if not file:
                    raise HTTPException(status_code=http.HTTPStatus.NOT_FOUND, detail="File not exists")
                bakery.fid = file.id
                file.pdfinsight = row.pdfinsight
                bakery.rows = [file]
                break
    if bakery.files and bakery.rows:
        from remarkable.worker.tasks import make_question, recruit_crud_answer

        await bakery.loads()
        bakery.rows = []
        bakery.files = {}
        celery.chain(recruit_crud_answer.si(bakery.fid, bakery.mid), make_question.si(bakery.fid, bakery.mid))()
        data = gzip.compress(pickle.dumps(bakery))
    else:
        data = await bakery.dumps()

    if not data:
        raise HTTPException(status_code=http.HTTPStatus.NOT_FOUND, detail="File not exists")

    return PaiStreamingResponse(
        data, chunk_size=5242880, headers={"X-Content-Length": f"{len(data)}"}, media_type="application/octet-stream"
    )


@router.get("/{fid:int}/ratio4", dependencies=[Depends(get_current_user)])
async def get_ratio4_result(
    file: Annotated[
        NewFile,
        model_with_perm(
            NewFile,
            fields=("id",),
            alias="fid",
        ),
    ],
):
    """
    for jura21_helper mold_id=29
    """
    ratio4_result: Ratio4Result = await get_ratio4_by_file(file.id)
    if ratio4_result.reason == "no_question":
        raise HTTPException(status_code=http.HTTPStatus.NOT_FOUND, detail=translate("Question not exists"))
    if ratio4_result.reason == "no_meta":
        raise HTTPException(status_code=http.HTTPStatus.NOT_FOUND, detail=translate(INVALID_STOCK_CODE_MSG))

    return {"data": ratio4_result.formulas}


@router.get("/{fid:int}/rules", dependencies=[Depends(get_current_user)])
async def get_file_rules(
    file: Annotated[NewFile, model_with_perm(NewFile, fields=("id", "mold_list"), alias="fid")],
):
    data = {}
    molds = await pw_db.execute(NewMold.select().where(NewMold.id.in_(file.mold_list)))
    mid_qid_map = dict(
        await pw_db.execute(
            NewQuestion.select(NewQuestion.mold, NewQuestion.id).where(NewQuestion.fid == file.id).tuples()
        )
    )
    for mold in molds:
        qid = mid_qid_map[mold.id]
        mold_schema = MoldSchema(mold.data)
        data.update({rule: qid for rule in mold_schema.all_rules()})

    return data
