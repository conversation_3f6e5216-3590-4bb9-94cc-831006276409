import http
import os
import urllib
from datetime import datetime
from functools import reduce
from typing import List

import peewee
from fastapi import API<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, HTTPException
from pydantic import BaseModel, field_validator
from speedy.peewee_plus.orm import TRUE
from starlette.requests import Request
from typing_extensions import Annotated

from remarkable.common.constants import DocType, PollGMLRules
from remarkable.common.util import DateTimeUtil, clean_txt, gen_juraurl
from remarkable.config import get_config
from remarkable.db import pw_db
from remarkable.models.diagnosis import Diagnosis
from remarkable.models.file_esg_xref import FileESGxREF
from remarkable.models.hkex_file import HKEXFile
from remarkable.models.hkex_file_meta import HKEXFileMeta
from remarkable.models.hkex_stock import HKEXCompaniesInfo
from remarkable.models.mold import special_mold
from remarkable.models.new_file import NewFile
from remarkable.models.new_question import NewQuestion
from remarkable.models.poll_gml import PollGML
from remarkable.models.poll_meta import POLLMeta
from remarkable.models.rule_reference import RuleReference
from remarkable.plugins.hkex.jura_v3_handlers import calc_non_compliance_rules
from remarkable.schemas.external import HKEXNewsResult, MandateLimitResult, TimeRange

router = APIRouter(prefix="/external", tags=["external"])


@router.get("/avt/documents", response_model=List[HKEXNewsResult])
async def get_histories(request: Request, time_range: Annotated[TimeRange, Depends(TimeRange)]):
    """
    from_date 和 to_date 应该符合 ISO 8601 标准, 如下

    "2023-11-22T00:00:00"
    """
    results = []
    rule_reference_map = await RuleReference.find_qr_rule_reference_map()
    rule_label_map = special_mold._id_map.get(special_mold.v3dr_id, {}).get("rule_label_map", {})

    qr_query = prepare_qr_query(time_range.from_date, time_range.to_date)
    # include DocType.AR, DocType.AGM, DocType.POLL
    ar_query = prepare_ar_like_query(time_range.from_date, time_range.to_date)
    esg_query = prepare_esg_query(time_range.from_date, time_range.to_date)
    query = qr_query.union_all(esg_query).union_all(ar_query)

    cte = query.select().cte("temp_query")
    query = query.with_cte(cte).order_by(cte.c.updated_utc.desc()).dicts()

    for item in await pw_db.execute(query):
        is_annual_report = False
        is_esg_report = False
        flag_info = calc_non_compliance_rules(item["stat_res"], item["doc_type"], rule_label_map, rule_reference_map)
        disclosure_non_critical_flags = [i["main_alias"] for i in flag_info["disclosure_check"]["non_critical_flags"]]
        ratio_non_critical_flags = [clean_txt(i) for i in flag_info["ratio_check"]]
        if item["doc_type"] == DocType.ESG:
            is_esg_report = True
        elif item["doc_type"] == DocType.AR:
            if item["mold_id"] in special_mold.esg_mids:
                is_esg_report = True
            else:
                is_annual_report = True
        hkex_news_result = {
            "hkex_file_id": os.path.basename(item.get("hkexurl", "")).split(".")[0],
            "non_critical_flags": disclosure_non_critical_flags + ratio_non_critical_flags,
            "critical_flags": [i["main_alias"] for i in flag_info["disclosure_check"]["critical_flags"]],
            "jura_url": f"{
                gen_juraurl(
                    item['qid'],
                    item['fid'],
                    is_annual_report=is_annual_report,
                    is_esg_report=is_esg_report,
                    mid=item['mold_id'],
                    doc_type=item['doc_type'],
                )
            }&_from=gekko",
            "release_time": DateTimeUtil.timestamp2str(item["release_date"], pattern="%Y-%m-%dT%H:%M:%S"),
            "modify_time": DateTimeUtil.timestamp2str(item["updated_utc"], pattern="%Y-%m-%dT%H:%M:%S"),
        }
        results.append(hkex_news_result)

    await Diagnosis.create(
        admin_user=1,
        url=request.url,
        method=request.method,
        request_header=dict(request.headers),
        request_body="",
        response_header={},
        response_body="",
        status_code=http.HTTPStatus.OK,
        error_type="GEKKO_API",
        extra={"results": results},
    )
    return results


def prepare_qr_query(from_date, to_date):
    new_question = NewQuestion.alias()
    cte_cond = [new_question.mold.in_(special_mold.qr_mids), new_question.deleted_utc == 0]
    if from_date:
        cte_cond.append(new_question.updated_utc >= datetime.timestamp(from_date))
    if to_date:
        cte_cond.append(new_question.updated_utc <= datetime.timestamp(to_date))
    window = peewee.Window(partition_by=[new_question.fid], order_by=[new_question.updated_utc.desc()])
    cte = (
        new_question.select(
            new_question.id,
            new_question.fid,
            new_question.mold,
            new_question.deleted_utc,
            new_question.updated_utc,
            peewee.fn.ROW_NUMBER().over(window).alias("rank"),
        )
        .filter(*cte_cond)
        .window(window)
        .cte("temp_q")
    )
    query = (
        HKEXFileMeta.select(
            HKEXFileMeta.fid,
            HKEXFileMeta.qid,  # NOTE 这里没有用question.id 是因为disclose和ratio 的url都需要 ratio的mid
            cte.c.updated_utc,
            cte.c.mold.alias("mold_id"),
            HKEXFileMeta.stock_code,
            HKEXFileMeta.stat_res,
            HKEXFile.headline,
            HKEXFile.url.alias("hkexurl"),
            HKEXFile.release_time.alias("release_date"),
            HKEXFileMeta.doc_type,
        )
        .join(HKEXFile, on=((HKEXFileMeta.fid == HKEXFile.fid) & (HKEXFile.type.in_(DocType.qr_types()))))
        .join(NewFile, on=(HKEXFileMeta.fid == NewFile.id))
        .join(cte, on=(HKEXFileMeta.fid == cte.c.fid) & (cte.c.rank == 1))
    )
    cond = [
        NewFile.deleted_utc == 0,
        HKEXFileMeta.doc_type.in_([DocType.Q1, DocType.INTERIM, DocType.Q3, DocType.FINAL]),
    ]
    query = query.join(HKEXCompaniesInfo, on=(HKEXFileMeta.stock_code == HKEXCompaniesInfo.stock_code))
    order_by = cte.c.updated_utc.desc()
    query = query.with_cte(cte).dicts().where(*cond).order_by(order_by)
    return query


def prepare_ar_like_query(from_date, to_date):
    new_question = NewQuestion.alias()
    ar_like_mids = special_mold.ar_mids + [special_mold.v6_agm_id, special_mold.v6_poll_id]
    cte_cond = [new_question.mold.in_(ar_like_mids), new_question.deleted_utc == 0]
    if from_date:
        cte_cond.append(new_question.updated_utc >= datetime.timestamp(from_date))
    if to_date:
        cte_cond.append(new_question.updated_utc <= datetime.timestamp(to_date))
    window = peewee.Window(partition_by=[new_question.fid], order_by=[new_question.updated_utc.desc()])
    cte = (
        new_question.select(
            new_question.id,
            new_question.fid,
            new_question.mold,
            new_question.deleted_utc,
            new_question.updated_utc,
            peewee.fn.ROW_NUMBER().over(window).alias("rank"),
        )
        .filter(*cte_cond)
        .window(window)
        .cte("temp_q")
    )
    query = (
        HKEXFileMeta.select(
            HKEXFileMeta.fid,
            cte.c.id.alias("qid"),
            cte.c.updated_utc,
            cte.c.mold.alias("mold_id"),
            HKEXFileMeta.stock_code,
            HKEXFileMeta.stat_res,
            HKEXFile.headline,
            HKEXFile.url.alias("hkexurl"),
            HKEXFile.release_time.alias("release_date"),
            HKEXFileMeta.doc_type,
        )
        .join(HKEXFile, on=((HKEXFileMeta.fid == HKEXFile.fid) & (HKEXFile.type.in_(["ar", "AGM", "POLL"]))))
        .join(NewFile, on=(HKEXFileMeta.fid == NewFile.id))
        .join(cte, on=((HKEXFileMeta.fid == cte.c.fid) & (cte.c.rank == 1)))
    )
    cond = [NewFile.deleted_utc == 0, HKEXFileMeta.doc_type.in_([DocType.AR, DocType.AGM, DocType.POLL])]
    query = query.join(HKEXCompaniesInfo, on=(HKEXFileMeta.stock_code == HKEXCompaniesInfo.stock_code))
    order_by = cte.c.updated_utc.desc()
    query = query.with_cte(cte).dicts().where(*cond).order_by(order_by)
    return query


def prepare_esg_query(from_date, to_date):
    new_question = NewQuestion.alias()
    cte_cond = [new_question.mold.in_(special_mold.esg_mids), new_question.deleted_utc == 0]
    if from_date:
        cte_cond.append(new_question.updated_utc >= datetime.timestamp(from_date))
    if to_date:
        cte_cond.append(new_question.updated_utc <= datetime.timestamp(to_date))
    cte = (
        new_question.select(
            new_question.id,
            new_question.fid,
            new_question.mold,
            new_question.deleted_utc,
            new_question.updated_utc,
        )
        .filter(*cte_cond)
        .cte("temp_q")
    )
    query = (
        HKEXFileMeta.select(
            HKEXFileMeta.fid,
            cte.c.id.alias("qid"),
            cte.c.updated_utc,
            cte.c.mold.alias("mold_id"),
            HKEXFileMeta.stock_code,
            HKEXFileMeta.stat_res,
            HKEXFile.headline,
            HKEXFile.url.alias("hkexurl"),
            HKEXFile.release_time.alias("release_date"),
            HKEXFileMeta.doc_type,
        )
        .join(HKEXFile, on=((HKEXFileMeta.fid == HKEXFile.fid) & (HKEXFile.type.in_(DocType.qr_types() + ["ar"]))))
        .join(NewFile, on=(HKEXFileMeta.fid == NewFile.id))
        .join(FileESGxREF, on=(FileESGxREF.fid == NewFile.id))
        .join(cte, on=(HKEXFileMeta.fid == cte.c.fid))
    )
    cond = [NewFile.deleted_utc == 0, FileESGxREF.activated, HKEXFileMeta.doc_type.in_([DocType.AR, DocType.ESG])]
    query = query.join(HKEXCompaniesInfo, on=(HKEXFileMeta.stock_code == HKEXCompaniesInfo.stock_code))
    order_by = cte.c.updated_utc.desc()
    query = query.with_cte(cte).dicts().where(*cond).order_by(order_by)
    return query


class CommonHeaders(BaseModel):
    access_token: str

    @field_validator("access_token")
    def verify_simple_token(cls, value):
        simple_token = get_config("app.simple_token", None)
        if not simple_token:
            return value
        if value != simple_token:
            raise HTTPException(status_code=http.HTTPStatus.UNAUTHORIZED, detail="Invalid token")
        return value


@router.get("/mandate_limit", response_model=List[MandateLimitResult])
async def get_mandate_limit(
    request: Request,
    _: Annotated[CommonHeaders, Header()],
    time_range: Annotated[TimeRange, Depends(TimeRange)],
    stock_code: str | None = None,
):
    """
    from_date 和 to_date 应该符合 ISO 8601 标准, 如下

    "2023-11-22T00:00:00"
    """

    results = []
    poll_file = HKEXFile.alias("poll_file")
    mr_file = HKEXFile.alias("mr_file")
    agm_file = HKEXFile.alias("agm_file")
    cond = TRUE
    if time_range.from_date:
        cond &= PollGML.updated_utc >= datetime.timestamp(time_range.from_date)
    if time_range.to_date:
        cond &= PollGML.updated_utc <= datetime.timestamp(time_range.to_date)
    if stock_code:
        cond &= POLLMeta.stock_code == stock_code
    query = (
        (
            PollGML.select(
                PollGML.fid,
                PollGML.qid,
                PollGML.updated_utc,
                PollGML.predict_gml,
                PollGML.manual_gml,
                POLLMeta.stock_code,
                poll_file.release_time,
                poll_file.url.alias("poll_url"),
                mr_file.url.alias("mr_url"),
                agm_file.url.alias("agm_url"),
            )
            .join(POLLMeta, on=(POLLMeta.poll_fid == PollGML.fid))
            .join(
                poll_file,
                on=(POLLMeta.poll_fid == poll_file.fid),
            )
            .join(mr_file, join_type=peewee.JOIN.LEFT_OUTER, on=(POLLMeta.mr_fid == mr_file.fid), include_deleted=True)
            .join(
                agm_file, join_type=peewee.JOIN.LEFT_OUTER, on=(POLLMeta.agm_fid == agm_file.fid), include_deleted=True
            )
        )
        .where(cond)
        .dicts()
    )
    poll_gmls = await pw_db.execute(query)
    # 有人工答案优先取人工
    url_prefix = (
        f"{get_config('web.scheme', 'http')}://{get_config('web.domain')}/#/hkex/agm-poll-results/report-review/"
    )
    url_suffix = f"&schemaId=34&rule={urllib.parse.quote('General Mandate Limit')}&_from=gekko"
    for item in poll_gmls:
        whether_passed = get_gml_value(item["predict_gml"], item["manual_gml"], [PollGMLRules.RP, "enum"])
        if gml_formula := get_gml_value(item["predict_gml"], item["manual_gml"], [PollGMLRules.GML, "formula"]):
            try:
                mandate_limit = f"{gml_formula.split('=')[1].strip().replace(',', '')}"
            except ValueError:
                mandate_limit = "0.00"
        else:
            mandate_limit = None
        passed_date = get_gml_value(item["predict_gml"], item["manual_gml"], [PollGMLRules.AD, "passed_date", "value"])
        results.append(
            MandateLimitResult(
                stock_code=item["stock_code"],
                poll_result_release_time=DateTimeUtil.timestamp2str(item["release_time"], pattern="%Y-%m-%dT%H:%M:%S"),
                jura_url=f"{url_prefix}{item['qid']}?fileId={item['fid']}{url_suffix}",
                poll_result_id=os.path.basename(item.get("poll_url") or "").split(".")[0],
                agm_id=os.path.basename(item.get("mr_url") or "").split(".")[0],
                monthly_return_id=os.path.basename(item.get("agm_url") or "").split(".")[0],
                general_mandate_limit=mandate_limit,
                whether_passed=whether_passed,
                passed_date=passed_date,
                modify_time=DateTimeUtil.timestamp2str(item["updated_utc"], pattern="%Y-%m-%dT%H:%M:%S"),
            ).model_dump()
        )
    await Diagnosis.create(
        admin_user=1,
        url=request.url,
        method=request.method,
        request_header=dict(request.headers),
        request_body="",
        response_header={},
        response_body="",
        status_code=http.HTTPStatus.OK,
        error_type="mandate_limit",
        extra={"results": results},
    )
    return results


def get_gml_value(predict: dict, manual: dict, path: list[str], default=None):
    try:
        ret = reduce(lambda d, key: d.get(key), path, manual)
        if not ret:
            try:
                ret = reduce(lambda d, key: d.get(key), path, predict)
            except (TypeError, AttributeError):
                ret = default
    except (TypeError, AttributeError):
        try:
            ret = reduce(lambda d, key: d.get(key), path, predict)
        except (TypeError, AttributeError):
            ret = default
    return ret
