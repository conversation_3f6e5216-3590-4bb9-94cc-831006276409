import datetime
import http
import re
from collections import defaultdict
from itertools import chain
from typing import Any, Literal, Self
from urllib.parse import quote

from fastapi import API<PERSON><PERSON><PERSON>, Depends, HTTPException
from peewee import fn
from pydantic import BaseModel, ConfigDict, Field, field_validator, model_validator
from starlette.requests import Request
from typing_extensions import Annotated

from remarkable import logger
from remarkable.common.common import (
    FUNDRAISING_COLS,
    JURA21_COLS,
    EmptyAnswer,
    normalize_enum_value,
)
from remarkable.common.constants import DEFAULT_REPORT_YEAR, DocType, HKEXARStatus, PolicyEsgRules
from remarkable.common.util import DateTimeUtil, fix_aid, gen_juraurl
from remarkable.config import get_config
from remarkable.converter.utils import quantize_decimal
from remarkable.data.answer_tools import load_key_path
from remarkable.db import pw_db
from remarkable.dependencies import get_current_user, validate_optional_year, validate_stock_code
from remarkable.models.file_esg_xref import FileESGxREF
from remarkable.models.hkex_file import HKEXFile
from remarkable.models.hkex_file_meta import HKEXFileMeta
from remarkable.models.hkex_stock import HKEXCompaniesInfo
from remarkable.models.mold import special_mold
from remarkable.models.new_file import NewFile
from remarkable.models.new_mold import NewMold
from remarkable.models.new_question import NewQuestion
from remarkable.models.rule_reference import RuleReference
from remarkable.models.user import AdminUser
from remarkable.plugins.hkex.handlers_util import RuleAnswerHandlerUtil
from remarkable.plugins.hkex.utils import MoldAnswer
from remarkable.schemas import TypeStripStr
from remarkable.schemas.esg import SpecialESGAnswerSchema
from remarkable.services.ar import get_target_clz, sync_ar_answer_result

router = APIRouter(
    prefix="/external_api",
    tags=["external"],
    responses={
        http.HTTPStatus.BAD_REQUEST: {"description": "Bad request"},
        http.HTTPStatus.NOT_FOUND: {"description": "Item not found"},
    },
)


class _BaseModel(BaseModel):
    model_config = ConfigDict(from_attributes=True, str_strip_whitespace=True)


class DocInfo(_BaseModel):
    """
    {
       "stock_code": "08451",
        "company_name": "SUNLIGHT (1977) HOLDINGS LIMITED",
        "team": 12,
        "headline": "Circulars - [General Mandate / Explanatory Statement for Repurchase of Shares / Re-election or Appointment of Director subject to Shareholders' Approval]",
        "title": "(1) PROPOSAL FOR GENERAL MANDATES TO ISSUE AND REPURCHASE SHARES, (2) PROPOSAL FOR RE-ELECTION OF DIRECTORS, AND (3) NOTICE OF ANNUAL GENERAL MEETING.pdf",
        "year": "2024",
        "hkex_url": "https://www1.hkexnews.hk/listedco/listconews/gem/2024/1230/2024123001283.pdf",
        "jura_url": "http://************:8000/#/hkex/agm-circular-checking/report-review/339595?fileId=103871&schemaId=33&rule=M2-total number and description of the shares which the issuer proposes to purchase",
        "release_time": "2024/12/30 19:21:00"
        "flagged": "Yes"
    }
    """

    stock_code: str = Field(description="Stock Code, e.g. 00001")
    company_name: str = Field(description="Company Name, e.g. CK HUTCHISON HOLDINGS LIMITED")
    team: int = Field(description="Team, e.g. 27")
    headline: str = Field(description="Headline, e.g. Annual Report")
    title: str = Field(description="Title, e.g. 2024-08-30T22-46_08321_TAI-KAM-HOLDINGS-LIMITED_ANNUAL-REPORT-2024.pdf")
    report_year: str = Field(serialization_alias="year", description="Report Year, e.g. 2024")
    hkex_url: str = Field(
        description="HKEX URL, e.g. https://www1.hkexnews.hk/listedco/listconews/sehk/2024/0428/2024042800001.pdf",
    )
    jura_url: str = Field(description="Jura URL, e.g. https://jura.test.paodingai.com/xxx")
    release_time: str = Field(description="Release Time, e.g. 2024/09/04 00:00:00")
    flagged: str | None = Field(description="Flagged(Just for Annual Report Result), e.g. Yes/No", default=None)


class Box(_BaseModel):
    box_left: float = Field(description="Left position of the box")
    box_top: float = Field(description="Top position of the box")
    box_right: float = Field(description="Right position of the box")
    box_bottom: float = Field(description="Bottom position of the box")


class BoxItem(_BaseModel):
    page: int = Field(description="Page number(1-based)")
    box: Box = Field(description="Box position")
    text: str = Field(default="", description="Text content")

    def model_post_init(self, __context: Any) -> None:
        self.page += 1


class BoxItemWithScore(BoxItem):
    score: str = Field(
        description="A value (range 0.0-1.0) representing the predicted preference strength or relevance score"
    )

    @field_validator("score", mode="before")
    def validate_score(cls, v: str | float) -> str:
        # keep 4 decimal places
        return quantize_decimal(v, decimal_places=4)


class BoxData(_BaseModel):
    boxes: list[BoxItem] = Field(default_factory=list, description="List of boxes")


class _CommonKey(_BaseModel):
    category: str | None = Field(
        default=None, description="Category, e.g. 'IPCC', 'IEA'. Only available for ESG rules: T6/8/9"
    )
    group_name: str | None = Field(
        default=None,
        description="Group Name, e.g. 'Issue of Shares 1'. Only available for the 'Fundraising Event Type' rules",
    )
    key: str | None = Field(default=None, description="Key, only available for the rule with sub-rules")
    value: str = Field(default="", description="Enum value")
    has_sub_rules: bool = Field(default=True, description="Whether has sub-rules", exclude=True)

    def model_post_init(self, __context: Any) -> None:
        if not self.group_name or self.group_name.lower() == "none":
            # NOTE: group_name is None or "None" means no group name
            self.group_name = None
        self.value = normalize_enum_value(self.value)

    @model_validator(mode="after")
    def check_sub_rules(self) -> Self:
        if not self.has_sub_rules:
            # NOTE: has_sub_rules is False means no sub-rules
            self.key = None
        return self

    @field_validator("key")
    def validate_key(cls, v: str | None) -> str | None:
        """两种情况是不带key的:
        1. A rules 大部分
        2. B/C/D 默认只有一个子项且为`Content`
        """
        kp = load_key_path(v)
        return None if (kp[-1].level == 1 or kp[-1].level == 2 and kp[-1].name == "Content") else kp[-1].name


class AILocationItem(_CommonKey):
    paragraph_data: list[BoxItemWithScore] = Field(default_factory=list, description="List of paragraph data")
    precise_data: list[BoxData] = Field(default_factory=list, description="List of precise data")

    @field_validator("paragraph_data", mode="before")
    def validate_paragraph_data(cls, items):
        rows = []
        for item in items:
            if not item:
                continue
            if "box" not in item and "outline" in item:
                outline = item.pop("outline")
                item["box"] = {
                    "box_left": outline[0],
                    "box_top": outline[1],
                    "box_right": outline[2],
                    "box_bottom": outline[3],
                }
            if "element_text" in item:
                item["text"] = item.pop("element_text")
            rows.append(item)
        return rows


class ManualLocationItem(_CommonKey):
    data: list[BoxData] = Field(default_factory=list, description="List of data")


class _CommonAssessment(_BaseModel):
    compliance_assessment: str = Field(default="", description="Compliance Assessment")

    def model_post_init(self, __context: Any) -> None:
        self.compliance_assessment = normalize_enum_value(self.compliance_assessment)


class AISuggestion(_CommonAssessment):
    disclosure_location: list[AILocationItem] = Field(default_factory=list)


class ManualAdjustment(_CommonAssessment):
    disclosure_location: list[ManualLocationItem] = Field(default_factory=list)


class ESGAIItems(_BaseModel):
    disclosure_classification: str = Field(default="", description="Disclosure Classification")
    items: list[AILocationItem] = Field(default_factory=list)


class ESGManualItems(_BaseModel):
    disclosure_classification: str = Field(default="", description="Disclosure Classification")
    items: list[ManualLocationItem] = Field(default_factory=list)


class ESGAISuggestion(_CommonAssessment):
    disclosure_location: ESGAIItems


class ESGManualAdjustment(_CommonAssessment):
    disclosure_location: ESGManualItems


class RuleItem(_BaseModel):
    mb_no: str = Field(serialization_alias="Main Board Rule Number", description="Main Board Rule Number")
    gem_no: str = Field(serialization_alias="GEM Board Rule Number", description="GEM Board Rule Number")
    description: str = Field(serialization_alias="Main Board Rule Details", description="Main Board Rule Details")
    gem_description: str = Field(serialization_alias="GEM Board Rule Details", description="GEM Board Rule Details")
    manual_adjustment: ManualAdjustment = Field(default_factory=ManualAdjustment)
    ai_suggestion: AISuggestion = Field(default_factory=AISuggestion)
    order: int = Field(default=0, exclude=True)

    def model_post_init(self, __context: Any) -> None:
        self.gem_description = self.gem_description or self.description

    @staticmethod
    def convert_to_crude_key(key: str) -> str:
        """
        Convert key to crude key
        :param key: '["POLL:0","P1-shares to attend and vote:0","Content:0"]'
        :return: "P1-shares to attend and vote-Content"
        """
        return "-".join(k.name for k in load_key_path(key)[1:])

    @model_validator(mode="before")
    @classmethod
    def validate(cls, data: dict[str, Any]) -> dict[str, Any]:
        if "result" not in data:
            return data

        result = data.pop("result")
        crude_answer = data.pop("crude_answer")
        clz = get_target_clz(result["mold_id"])
        result_ins = clz(**result)
        data["manual_adjustment"] = {
            "disclosure_location": list(chain(*result_ins.manual_disclosure_answer.values())),
            "compliance_assessment": result_ins.manual_compliance_value,
        }
        data["ai_suggestion"] = {
            "disclosure_location": [
                AILocationItem(
                    key=answer["key"],
                    value=answer["value"],
                    precise_data=answer["data"],
                    paragraph_data=crude_answer.get(cls.convert_to_crude_key(answer["key"]), [])[
                        : get_config("prompter.top_n")
                    ],
                )
                for key, answers in result_ins.ai_disclosure_answer.items()
                for answer in answers
            ],
            "compliance_assessment": result_ins.ai_compliance_value,
        }
        return data


class ESGRuleItem(_BaseModel):
    mb_no: str = Field(serialization_alias="Main Board Rule Number", description="Main Board Rule Number")
    gem_no: str = Field(serialization_alias="GEM Board Rule Number", description="GEM Board Rule Number")
    description: str = Field(serialization_alias="Main Board Rule Details", description="Main Board Rule Details")
    gem_description: str = Field(serialization_alias="GEM Board Rule Details", description="GEM Board Rule Details")
    manual_adjustment: ESGManualAdjustment = Field(default_factory=ManualAdjustment)
    ai_suggestion: ESGAISuggestion = Field(default_factory=AISuggestion)
    order: int = Field(default=0, exclude=True)

    def model_post_init(self, __context: Any) -> None:
        self.gem_description = self.gem_description or self.description

    @staticmethod
    def convert_to_crude_key(key: str) -> str:
        """
        Convert key to crude key
        :param key: '["POLL:0","P1-shares to attend and vote:0","Content:0"]'
        :return: "P1-shares to attend and vote-Content"
        """
        return "-".join(k.name for k in load_key_path(key)[1:])


class RuleData(BaseModel):
    doc_info: DocInfo = Field(description="Document Information")
    data: dict[str, RuleItem] = Field(description="Disclosure Data")

    @model_validator(mode="after")
    def validate(self):
        self.data = dict(sorted(self.data.items(), key=lambda x: x[1].order))
        return self


class ESGRuleData(BaseModel):
    doc_info: DocInfo = Field(description="Document Information")
    data: dict[str, ESGRuleItem] = Field(description="Disclosure Data")

    @model_validator(mode="after")
    def validate(self):
        self.data = dict(sorted(self.data.items(), key=lambda x: x[1].order))
        return self


@router.get(
    "/agm_result",
    summary="Get AGM Result",
    response_model=list[RuleData],
    response_model_by_alias=True,
    response_model_exclude_none=True,
)
@router.get(
    "/poll_result",
    summary="Get Poll Result",
    response_model=list[RuleData],
    response_model_by_alias=True,
    response_model_exclude_none=True,
)
async def get_result(
    request: Request,
    stock_code: Annotated[TypeStripStr, validate_stock_code(alias="stockcode")],
    year: Annotated[TypeStripStr | None, validate_optional_year()],
    user: AdminUser = Depends(get_current_user),
):
    cond = HKEXFileMeta.stock_code == stock_code
    if year:
        cond &= HKEXFileMeta.report_year == year

    if request.url.path.endswith("agm_result"):
        mid = special_mold.v6_agm_id
        cond &= HKEXFileMeta.doc_type == DocType.AGM
    else:
        mid = special_mold.v6_poll_id
        cond &= HKEXFileMeta.doc_type == DocType.POLL

    if rows := await _prepare_data(cond, mid):
        return rows

    stmt = NewQuestion.select().join(HKEXFileMeta, on=(NewQuestion.fid == HKEXFileMeta.fid)).where(cond)
    for question in await pw_db.execute(stmt):
        await sync_ar_answer_result(question.id, question=question)

    if rows := await _prepare_data(cond, mid):
        return rows
    raise HTTPException(status_code=http.HTTPStatus.NOT_FOUND, detail="No valid data found")


async def _prepare_data(cond, mid):
    clz = get_target_clz(mid)
    stmt = (
        HKEXFileMeta.select(
            HKEXFileMeta.stock_code,
            HKEXFileMeta.report_year,
            HKEXCompaniesInfo.company_name_en.alias("company_name"),
            HKEXCompaniesInfo.team_id.alias("team"),
            HKEXFile.headline,
            HKEXFile.name.alias("title"),
            HKEXFile.url.alias("hkex_url"),
            NewQuestion.crude_answer,
            fn.TO_CHAR(HKEXFileMeta.published.cast("timestamp"), "YYYY/MM/DD HH24:MI:SS").alias("release_time"),
            # 拼接 jura_url
            # AGM: https://jura.test.paodingai.com/#/hkex/agm-circular-checking/report-review/306050?fileId=96762&schemaId=33&rule=M2-xxx
            # Poll: https://jura.test.paodingai.com/#/hkex/agm-poll-results/report-review/306064?fileId=96775&schemaId=34&rule=P1-yyy
            fn.CONCAT(
                f"{get_config('web.scheme', 'http')}://{get_config('web.domain')}/#/hkex/",
                "agm-circular-checking" if mid == special_mold.v6_agm_id else "agm-poll-results",
                "/report-review/",
                NewQuestion.id,
                "?fileId=",
                HKEXFileMeta.fid,
                "&schemaId=",
                NewQuestion.mold,
                "&rule=",
                quote(
                    "M2-total number and description of the shares which the issuer proposes to purchase"
                    if mid == special_mold.v6_agm_id
                    else "P1-shares to attend and vote"
                ),
            ).alias("jura_url"),
            fn.TO_JSON(
                fn.JSONB_OBJECT_AGG(
                    fn.SPLIT_PART(RuleReference.rule, "-", 1),
                    fn.JSONB_BUILD_OBJECT(
                        "mb_no",
                        RuleReference.main_alias,
                        "gem_no",
                        RuleReference.gem_alias,
                        "description",
                        RuleReference.rule_description,
                        "gem_description",
                        RuleReference.gem_description,
                        "result",
                        clz,
                        "order",
                        RuleReference.order,
                    ),
                )
            ).alias("meta"),
        )
        .join(HKEXFile, on=(HKEXFileMeta.fid == HKEXFile.fid))
        .join(HKEXCompaniesInfo, on=(HKEXFileMeta.stock_code == HKEXCompaniesInfo.stock_code))
        .join(NewQuestion, on=((HKEXFileMeta.fid == NewQuestion.fid) & (NewQuestion.mold == mid)))
        .join(clz, on=(clz.file_id == NewQuestion.fid))
        .join(RuleReference, on=(RuleReference.id == clz.rule_reference_id))
        .where(cond)
        .group_by(
            HKEXFileMeta.stock_code,
            HKEXFileMeta.report_year,
            HKEXCompaniesInfo.company_name_en,
            HKEXCompaniesInfo.team_id,
            HKEXFile.headline,
            HKEXFile.name,
            HKEXFile.url,
            HKEXFileMeta.published,
            NewQuestion.id,
            HKEXFileMeta.fid,
            NewQuestion.mold,
        )
        .order_by(HKEXFileMeta.published.desc())
        .namedtuples()
    )
    rows = []
    for row in await pw_db.execute(stmt):
        for key in row.meta:
            row.meta[key]["crude_answer"] = row.crude_answer or defaultdict(list)
        rows.append(
            RuleData(
                doc_info=DocInfo(
                    stock_code=row.stock_code,
                    company_name=row.company_name,
                    team=row.team,
                    report_year=row.report_year,
                    headline=row.headline,
                    title=row.title,
                    hkex_url=row.hkex_url,
                    jura_url=row.jura_url,
                    release_time=row.release_time,
                ),
                data=row.meta,
            )
        )
    return rows


@router.get("/latest_fy", dependencies=[Depends(get_current_user)])
async def get_latest_fy() -> int:
    latest_fy = await pw_db.scalar(
        HKEXFileMeta.select(HKEXFileMeta.report_year.cast("int"))
        .where(HKEXFileMeta.doc_type == DocType.AR)
        .order_by(HKEXFileMeta.report_year.desc())
        .limit(1)
    )
    return latest_fy or datetime.datetime.now().year


@router.get(
    "/annual_report",
    summary="Get Annual Report Result",
    response_model=list[RuleData],
    response_model_by_alias=True,
    response_model_exclude_none=True,
)
async def get_annual_report(
    stock_code: Annotated[TypeStripStr, validate_stock_code(alias="stockcode")],
    year: Annotated[TypeStripStr | None, validate_optional_year()],
    user: AdminUser = Depends(get_current_user),
):
    rule_references = await RuleReference.find_abcd_rule_references()
    rule_reference_map = {rule_reference.rule: rule_reference for rule_reference in rule_references}

    all_mold_instances = {
        m.id: m for m in await pw_db.execute(NewMold.select().where(NewMold.id.in_(special_mold.ar_mids)))
    }
    results = []
    for item in await pw_db.execute(prepare_ar_query(stock_code, year)):
        results.append(
            {
                "doc_info": {
                    "stock_code": item.stock_code,
                    "company_name": item.company_name,
                    "team": item.team,
                    "headline": item.headline,
                    "title": item.title,
                    "report_year": item.report_year,
                    "hkex_url": item.hkexurl or "",
                    "jura_url": gen_juraurl(item.qid, item.fid, is_annual_report=True),
                    "release_time": DateTimeUtil.timestamp2str(item.release_date),
                    "year_end": item.year_end,
                    "flagged": HKEXARStatus.convert2status(item.ar_status)[0],
                },
                "data": await prepare_ar_result(item.fid, rule_reference_map, all_mold_instances),
            }
        )
    if results:
        return results
    raise HTTPException(status_code=http.HTTPStatus.NOT_FOUND, detail="No valid data found")


async def prepare_ar_result(
    fid: int,
    rule_reference_map: dict[str, RuleReference],
    all_mold_instances: dict[int, NewMold],
):
    results = {}
    mid_rules_map = {
        special_mold.v1_id: [rule for rule in rule_reference_map if rule.startswith("A")],
        special_mold.v2_id: [rule for rule in rule_reference_map if rule.startswith("B")],
        special_mold.v3_id: [rule for rule in rule_reference_map if rule.startswith("C")],
        special_mold.v3d_id: [rule for rule in rule_reference_map if rule.startswith("D")],
    }
    rule_result = await parser_answer(fid, all_mold_instances, mid_rules_map)
    for rule, refer in rule_reference_map.items():
        if rule in ("B39", "B40") or rule not in rule_result:
            continue
        results[rule] = {
            "mb_no": refer.main_alias,
            "description": re.sub("\n", "", refer.rule_description),
            "gem_no": refer.gem_alias,
            "gem_description": re.sub("\n", "", refer.gem_description),
            **rule_result[rule],
        }
    return results


async def prepare_esg_rules_answer(
    question: NewQuestion, rules: list[str]
) -> dict[str, dict[Literal["ai_suggestion", "manual_suggestion"], ESGAISuggestion | ESGManualAdjustment]]:
    result = {}
    user_mold_answer = MoldAnswer(question.answer)
    preset_mold_answer = MoldAnswer(question.preset_answer)
    top_n = get_config("prompter.top_n") or 5
    question.crude_answer = question.crude_answer or {}
    for rule in rules:
        try:
            user_answer = user_mold_answer.get_rule_answer(rule)
            preset_answer = preset_mold_answer.get_rule_answer(rule)
        except RuntimeError:
            logger.warning(f"Failed to get answer for {rule=}")
            continue
        has_sub_rules = len(preset_mold_answer.schema.root_rules_map.get(rule) or []) > 1

        if rule in {PolicyEsgRules.E6, PolicyEsgRules.E8, PolicyEsgRules.E9}:
            special_user_answer = SpecialESGAnswerSchema.build_from_answer_items(
                *user_answer["answer"], rule=rule, only_special_ui=True
            )
            special_ai_answer = SpecialESGAnswerSchema.build_from_answer_items(*preset_answer["answer"], rule=rule)
            result[rule] = {
                "manual_adjustment": {
                    "disclosure_location": {
                        "disclosure_classification": special_user_answer.value,
                        "items": [
                            {
                                "category": cat.category,
                                "data": cat.data,
                                "value": cat.enum,
                                "has_sub_rules": has_sub_rules,
                            }
                            for cat in special_user_answer.categories
                        ],
                    },
                    "compliance_assessment": "",
                },
                "ai_suggestion": {
                    "compliance_assessment": "",
                    "disclosure_location": {
                        "disclosure_classification": special_ai_answer.value,
                        "items": [
                            AILocationItem(
                                category=cat.category,
                                value=cat.enum,
                                precise_data=cat.data,  # noqa
                                paragraph_data=(
                                    question.crude_answer.get(fix_aid(rule, special_mold.policy_esg_id, cat.category))
                                    or []
                                )[:top_n],
                                has_sub_rules=has_sub_rules,
                            )
                            for cat in special_ai_answer.categories
                        ],
                    },
                },
            }
        else:
            for answer in preset_answer["answer"]:
                answer["crude_answer"] = (
                    question.crude_answer.get("-".join(p.name for p in load_key_path(answer["key"])[1:])) or []
                )[:top_n]

            ai_disclosure_classification = preset_answer["answer"][0]["value"] if preset_answer["answer"] else ""
            manual_items = [a | {"has_sub_rules": has_sub_rules} for a in user_answer["answer"]]
            manual_disclosure_classification = manual_items[0]["value"] if manual_items else ""

            result[rule] = {
                "manual_adjustment": {
                    "compliance_assessment": "",
                    "disclosure_location": {
                        "disclosure_classification": manual_disclosure_classification,
                        "items": manual_items,
                    },
                },
                "ai_suggestion": {
                    "compliance_assessment": "",
                    "disclosure_location": {
                        "disclosure_classification": ai_disclosure_classification,
                        "items": [
                            AILocationItem(
                                key=answer["key"],
                                value=answer["value"],
                                # 存在group_id说明是人工答案，不需要paragraph_data: https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7514#note_745427
                                paragraph_data=[] if answer.get("group_id") else answer.pop("crude_answer"),
                                precise_data=answer["data"],
                                has_sub_rules=has_sub_rules,
                                group_name=answer.get("group_name") or (answer.get("meta") or {}).get("group_name"),
                            )
                            for answer in preset_answer["answer"]
                        ],
                    },
                },
            }
    return result


async def prepare_rules_answer(
    question: NewQuestion, rules: list[str]
) -> dict[str, dict[Literal["ai_suggestion", "manual_suggestion"], AISuggestion | ManualAdjustment]]:
    result = {}
    user_mold_answer = MoldAnswer(question.answer)
    preset_mold_answer = MoldAnswer(question.preset_answer)
    top_n = get_config("prompter.top_n") or 5
    question.crude_answer = question.crude_answer or {}
    for rule in rules:
        is_multi_group = rule in FUNDRAISING_COLS
        try:
            user_answer = user_mold_answer.get_rule_answer(rule, multi=is_multi_group)
            preset_answer = preset_mold_answer.get_rule_answer(rule, multi=is_multi_group)
        except RuntimeError:
            # TODO: 为啥会缺答案, 先跳过
            logger.warning(f"Failed to get answer for {rule=}")
            continue
        if get_config("hide_jura21") and rule in JURA21_COLS:
            for result in preset_answer["answer"]:
                result["data"] = []
                result["value"] = ""
            preset_answer["rule_result"]["value"] = ""

        has_sub_rules = len(preset_mold_answer.schema.root_rules_map.get(rule) or []) > 1

        if is_multi_group:
            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7514
            # 先补充manual_group_answer表中的数据组成结构：[{"group_name": "name1", "items": [{"key": "path1", ...}, {"key": "path2", ...}]}]
            all_answer = {"user_answer": user_answer, "preset_answer": preset_answer}
            await RuleAnswerHandlerUtil.build_b1_b10_answer(question.id, all_answer, rule)
            # 再拍平结构为：[{"group_name": "name1", "key": "path1", ...}, {"group_name": "name1", "key": "path2", ...}]
            RuleAnswerHandlerUtil.flatten_answer(preset_answer)
            RuleAnswerHandlerUtil.flatten_answer(user_answer)
        for answer in preset_answer["answer"]:
            answer["crude_answer"] = (
                question.crude_answer.get("-".join(p.name for p in load_key_path(answer["key"])[1:])) or []
            )[:top_n]

        result[rule] = {
            "manual_adjustment": {
                "disclosure_location": [a | {"has_sub_rules": has_sub_rules} for a in user_answer["answer"]],
                "compliance_assessment": user_answer["rule_result"]["value"],
            },
            "ai_suggestion": {
                "disclosure_location": [
                    AILocationItem(
                        key=answer["key"],
                        value=answer["value"],
                        # 存在group_id说明是人工答案，不需要paragraph_data: https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7514#note_745427
                        paragraph_data=[] if answer.get("group_id") else answer.pop("crude_answer"),
                        precise_data=answer["data"],
                        has_sub_rules=has_sub_rules,
                        group_name=answer.get("group_name") or (answer.get("meta") or {}).get("group_name"),
                    )
                    for answer in preset_answer["answer"]
                ],
                "compliance_assessment": preset_answer["rule_result"]["value"],
            },
        }
    return result


async def parser_answer(
    fid: int,
    all_mold_instances: dict[int, NewMold],
    mold_rules_map: dict[int, list[str]],
) -> dict[str, dict[Literal["ai_suggestion", "manual_suggestion"], AISuggestion | ManualAdjustment]]:
    result = {}
    for question in await pw_db.execute(
        NewQuestion.select()
        .where(NewQuestion.fid == fid, NewQuestion.mold.in_(list(mold_rules_map)))
        .order_by(NewQuestion.mold)
    ):
        mold_instance = all_mold_instances[question.mold]
        empty_answer = EmptyAnswer(mold_instance)

        if not question.preset_answer:
            question.preset_answer = empty_answer.answer
            logger.warning(f"preset_answer is empty, {question.id}, {question.fid=}, {question.mold=}")
        if not question.answer:
            answer = await question.set_answer()
            question.answer = answer or empty_answer.answer

        question.answer = empty_answer.merge(question.answer, special_ui_only=True)
        question.preset_answer = empty_answer.merge(question.preset_answer)
        rules = mold_rules_map[question.mold]
        if question.mold in special_mold.esg_mids_with_policy:
            result.update(await prepare_esg_rules_answer(question, rules))
        else:
            result.update(await prepare_rules_answer(question, rules))
    return result


def prepare_ar_query(stock_code: str, year: str = None):
    cond = (HKEXFileMeta.doc_type == DocType.AR.value) & (HKEXFileMeta.stock_code == stock_code)
    if year:
        cond &= HKEXFileMeta.report_year == year

    cte = (
        HKEXFileMeta.select(
            HKEXFileMeta.fid,
            fn.ROW_NUMBER()
            .over(
                partition_by=[HKEXFileMeta.report_year],
                order_by=[HKEXFileMeta.published.desc()],
            )
            .alias("rank"),
        )
        .where(cond)
        .cte("cte")
    )

    stmt = (
        HKEXFileMeta.select(
            HKEXFileMeta.fid,
            NewQuestion.id.alias("qid"),
            NewFile.mold.alias("mold_id"),
            HKEXFileMeta.stock_code,
            HKEXFileMeta.name.alias("company_name"),
            HKEXFile.name.alias("title"),
            HKEXFile.headline,
            HKEXFile.url.alias("hkexurl"),
            HKEXFile.release_time.alias("release_date"),
            HKEXFileMeta.doc_type,
            HKEXFileMeta.stat_res,
            HKEXFileMeta.report_year,
            HKEXFileMeta.year_end,
            HKEXCompaniesInfo.team_id.alias("team"),
            NewQuestion.ar_status,
        )
        .join(HKEXFile, on=(HKEXFileMeta.fid == HKEXFile.fid))
        .join(NewFile, on=(HKEXFileMeta.fid == NewFile.id))
        .join(HKEXCompaniesInfo, on=(HKEXFileMeta.stock_code == HKEXCompaniesInfo.stock_code))
        .join(NewQuestion, on=((HKEXFileMeta.fid == NewQuestion.fid) & (NewQuestion.mold == special_mold.v1_id)))
        .join(cte, on=(cte.c.fid == HKEXFileMeta.fid))
        .where(cte.c.rank == 1)  # remove duplicate records
        .order_by(HKEXFileMeta.report_year.cast("integer").desc())
        .with_cte(cte)
    )
    return stmt.namedtuples()


@router.get(
    "/esg_result",
    summary="Get ESG Result",
    response_model=list[ESGRuleData],
    response_model_by_alias=True,
    response_model_exclude_none=True,
)
async def get_esg_report(
    stock_code: Annotated[TypeStripStr, validate_stock_code(alias="stockcode")],
    year: Annotated[TypeStripStr | None, validate_optional_year()],
    user: AdminUser = Depends(get_current_user),
):
    jura4_rules = await RuleReference.find_esg_rule_refer_map()
    jura6_rules = await RuleReference.find_policy_esg_rule_refer_map()
    all_mold_instances = {
        m.id: m for m in await pw_db.execute(NewMold.select().where(NewMold.id.in_(special_mold.esg_mids_with_policy)))
    }
    result = []
    for item in await pw_db.execute(prepare_esg_query(stock_code, year)):
        qids = await pw_db.scalars(
            NewQuestion.select(NewQuestion.id)
            .where(NewQuestion.fid == item.fid, NewQuestion.mold.in_(special_mold.esg_mids_with_policy))
            .order_by(NewQuestion.mold)
        )
        result.append(
            {
                "doc_info": DocInfo(
                    stock_code=item.stock_code,
                    company_name=item.company_name,
                    team=item.team,
                    headline=item.headline,
                    title=item.title,
                    report_year=item.report_year,
                    hkex_url=item.hkex_url,
                    jura_url=gen_juraurl(qids[0], item.fid, is_esg_report=True, mid=special_mold.ar_esg_id),
                    release_time=item.release_time,
                ),
                "data": await prepare_esg_result(item.fid, all_mold_instances, jura4_rules, jura6_rules),
            }
        )
    if result:
        return result
    raise HTTPException(status_code=http.HTTPStatus.NOT_FOUND, detail="No valid data found")


async def prepare_esg_result(
    fid: int,
    all_mold_instances: dict[int, NewMold],
    jura4_rules: dict[str, RuleReference],
    jura6_rules: dict[str, RuleReference],
):
    result = {}
    mid_rules_map = {
        special_mold.ar_esg_id: list(jura4_rules),
        special_mold.esg_id: list(jura4_rules),
        special_mold.policy_ar_id: list(jura6_rules),
        special_mold.policy_esg_id: list(jura6_rules),
    }
    rule_result = await parser_answer(fid, all_mold_instances, mid_rules_map)
    for rule, refer in chain(jura4_rules.items(), jura6_rules.items()):
        if rule not in rule_result:
            continue
        result[refer.main_alias] = {
            "mb_no": refer.main_alias,
            "description": re.sub("\n", "", refer.rule_description),
            "gem_no": refer.gem_alias,
            "gem_description": re.sub("\n", "", refer.gem_description),
            **rule_result[rule],
        }
    return result


def prepare_esg_query(stock_code: str, report_year: str = None):
    cond = (
        (HKEXFileMeta.stock_code == stock_code)
        & (HKEXFileMeta.doc_type.in_((DocType.AR.value, DocType.ESG.value)))
        & (HKEXFileMeta.report_year >= DEFAULT_REPORT_YEAR)
    )
    if report_year:
        cond &= HKEXFileMeta.report_year == report_year
    cte = (
        HKEXFileMeta.select(
            HKEXFileMeta.fid,
            fn.ROW_NUMBER()
            .over(
                partition_by=[HKEXFileMeta.report_year, HKEXFileMeta.doc_type],
                order_by=[HKEXFileMeta.published.desc()],
            )
            .alias("rank"),
        )
        .join(FileESGxREF, on=((FileESGxREF.fid == HKEXFileMeta.fid) & FileESGxREF.activated))
        .where(cond)
        .cte("cte")
    )
    stmt = (
        HKEXFileMeta.select(
            HKEXFileMeta.fid,
            HKEXFileMeta.stock_code,
            HKEXFile.name.alias("title"),
            HKEXFile.headline,
            HKEXFile.url.alias("hkex_url"),
            HKEXFileMeta.report_year,
            HKEXCompaniesInfo.company_name_en.alias("company_name"),
            HKEXCompaniesInfo.team_id.alias("team"),
            fn.TO_CHAR(HKEXFileMeta.published.cast("timestamp"), "YYYY/MM/DD HH24:MI:SS").alias("release_time"),
        )
        .join(HKEXFile, on=(HKEXFileMeta.fid == HKEXFile.fid))
        .join(HKEXCompaniesInfo, on=(HKEXFileMeta.stock_code == HKEXCompaniesInfo.stock_code))
        .join(cte, on=(cte.c.fid == HKEXFileMeta.fid))
        .where(cte.c.rank == 1)
        .order_by(HKEXFileMeta.report_year.cast("integer").desc())
        .with_cte(cte)
        .namedtuples()
    )
    return stmt
