import http
import logging
from collections import defaultdict
from typing import Annotated, Literal

from fastapi import APIRouter, Body, Depends, HTTPException, Query
from peewee import Ordering
from speedy.peewee_plus import orm

from remarkable.common.constants import DocType, PollGMLRules
from remarkable.common.util import compact_dumps
from remarkable.config import get_config
from remarkable.db import pw_db
from remarkable.dependencies import (
    CommonDep,
    StockYear,
    get_current_user,
    model_with_perm,
    order_by_query,
    validate_rule_query,
    validate_stock_code,
    validate_stock_code_and_year,
)
from remarkable.models.hkex_file import HKEXFile
from remarkable.models.hkex_file_meta import HKEXFileMeta
from remarkable.models.mold import special_mold
from remarkable.models.new_file import NewFile
from remarkable.models.new_history import HistoryMeta, NewHistory
from remarkable.models.new_mold import NewMold
from remarkable.models.new_question import NewQuestion
from remarkable.models.poll_gml import PollGML
from remarkable.models.poll_meta import POLLMeta
from remarkable.models.rule_reference import RuleReference
from remarkable.models.user import AdminUser
from remarkable.optools.stat_util import Url
from remarkable.plugins.hkex.utils import AdjustmentInfo, compare_label_answer, gen_adjustment_info
from remarkable.predictor.mold_schema import MoldSchema
from remarkable.schemas import PaginateSchema
from remarkable.schemas.agm import DocFlowQuerySchema, DocFlowSchema
from remarkable.schemas.poll import (
    GetPollMetaSchema,
    PollFileMetaSchema,
    PollGMLRuleSchema,
    PollGMLSubRuleSchema,
    PollMetaSchema,
)
from remarkable.schemas.stat_by_issuer import ComplianceReport
from remarkable.schemas.stat_by_rule import ComplianceStatModel
from remarkable.services.agm import prepare_doc_flows_pagination_data, prepare_docs_query, stat_by_issuer, stat_by_rule
from remarkable.services.question import calc_key_hash, replace_answer_item
from remarkable.services.rule_manage import get_action_by_rule

router = APIRouter(prefix="/poll", tags=["poll"])
logger = logging.getLogger(__name__)


@router.get("/doc_flows", response_model=PaginateSchema[DocFlowSchema], dependencies=[Depends(get_current_user)])
async def get_doc_flows(
    commons: CommonDep,
    doc_query: Annotated[DocFlowQuerySchema, Depends(DocFlowQuerySchema)],
    user: AdminUser = Depends(get_current_user),
):
    """
    `calendar_years`和`teams`是多选，`teams`是团队ID，`calendar_years`是年份，以逗号分隔
    """
    # await NewHistory.save_operation_history_by_user(
    #     qid=None,
    #     current_user=user,
    #     action=HistoryAction.LOAD_PAGE,
    #     meta=HistoryMeta(system_function=["AGM Poll Results home page"]),
    # )
    return await prepare_doc_flows_pagination_data(DocType.POLL, commons, doc_query)


@router.get("/docs", response_model=list[DocFlowSchema], dependencies=[Depends(get_current_user)])
async def get_docs(
    order_by: Annotated[list[Ordering], order_by_query(HKEXFileMeta, default="published")],
    stock_year: StockYear = Depends(validate_stock_code_and_year),
):
    """
    `stock_code`和`calendar_year`全传，按发布时间升序返回指定code和year的所有 POLL 报告记录，否则返回最新的一份。
    """
    query = prepare_docs_query(stock_year, DocType.POLL, order_by)
    if rows := await HKEXFileMeta.manager().execute(query.namedtuples()):
        return rows
    raise HTTPException(status_code=http.HTTPStatus.NOT_FOUND, detail="No valid POLL report found")


@router.get("/stat_by_rule", response_model=ComplianceStatModel, dependencies=[Depends(get_current_user)])
async def get_stat_by_rule(
    refer: Annotated[
        RuleReference,
        validate_rule_query(
            doc_type="poll",
            default_rule="P1-shares to attend and vote",
        ),
    ],
):
    return await stat_by_rule(DocType.POLL, refer)


@router.get("/stat_by_issuer", response_model=ComplianceReport, dependencies=[Depends(get_current_user)])
async def get_stat_by_issuer(stock_code: Annotated[str, validate_stock_code()]):
    return await stat_by_issuer(DocType.POLL, stock_code)


@router.get("/poll_meta", response_model=PollMetaSchema, dependencies=[Depends(get_current_user)])
async def get_poll_meta(query_params: Annotated[GetPollMetaSchema, Depends(GetPollMetaSchema)]):
    mid = query_params.mold_id
    if mid in (special_mold.v6_poll_id, special_mold.v6_1_poll_id):
        query_field = POLLMeta.poll_fid
    elif mid == special_mold.v6_1_mr_id:
        query_field = POLLMeta.mr_fid
    elif mid in (special_mold.v6_agm_id, special_mold.v6_1_agm_id):
        query_field = POLLMeta.agm_fid
    else:
        raise HTTPException(status_code=http.HTTPStatus.NOT_FOUND, detail="Invalid mold id")
    poll_meta = await pw_db.first(POLLMeta.select().where(query_field == query_params.file_id))
    if not poll_meta:
        raise HTTPException(status_code=http.HTTPStatus.NOT_FOUND, detail="No valid poll meta found")
    file_urls = await gen_file_meta(poll_meta)
    return {
        "stock_code": poll_meta.stock_code,
        "poll": file_urls.get("poll", {}),
        "agm": file_urls.get("agm", {}),
        "mr": file_urls.get("mr", {}),
    }


@router.get("/gml/{qid:int}/answer", response_model=PollGMLRuleSchema)
async def get_gml_answer(
    qid,
    question: Annotated[
        NewQuestion,
        model_with_perm(NewQuestion, fields=("id", "fid"), alias="qid"),
    ],
):
    poll_gml = await pw_db.first(PollGML.select().where(PollGML.qid == qid))
    if not poll_gml:
        return {}

    return {"ai": poll_gml.predict_gml, "manual": poll_gml.manual_gml}


@router.put("/gml/{qid:int}/answer")
async def update_gml_answer(
    qid,
    rule: Literal[PollGMLRules.GML, PollGMLRules.RP, PollGMLRules.AD],
    question: Annotated[
        NewQuestion,
        model_with_perm(NewQuestion, fields=("id", "fid"), alias="qid"),
    ],
    manual_ans: Annotated[PollGMLSubRuleSchema, Body],
    user: AdminUser = Depends(get_current_user),
):
    # 保存到 poll_gml
    rule_ans = manual_ans.model_dump(mode="json", by_alias=True).get(rule)
    old_ans = None
    if poll_gml := await pw_db.first(PollGML.select().where(PollGML.qid == qid)):
        if poll_gml.manual_gml:
            old_ans = poll_gml.manual_gml.get(rule)
            poll_gml.manual_gml[rule] = rule_ans
        else:
            poll_gml.manual_gml = {rule: rule_ans}
        await pw_db.update(poll_gml, only=["manual_gml"])
    else:
        await pw_db.create(PollGML, qid=qid, fid=question.fid, manual_gml={rule: rule_ans})

    poll_meta = await pw_db.first(POLLMeta.select().where(POLLMeta.poll_fid == question.fid))
    poll_question = await NewQuestion.find_by_fid_mid(question.fid, special_mold.v6_1_poll_id)
    ans_map = {"poll": defaultdict(list), "mr": defaultdict(list), "agm": defaultdict(list)}
    poll_schema = MoldSchema((await NewMold.find_by_id(special_mold.v6_1_poll_id)).data)
    mr_schema = MoldSchema((await NewMold.find_by_id(special_mold.v6_1_mr_id)).data)
    agm_schema = MoldSchema((await NewMold.find_by_id(special_mold.v6_1_agm_id)).data)
    special_rules = []
    if rule == PollGMLRules.GML and manual_ans.gml:
        special_rules = [PollGMLRules.TOTAL_SHARE, PollGMLRules.TREASURY_SHARE, PollGMLRules.PERCENTAGE]
        if total := manual_ans.gml.total:
            group_gml_ans(total, poll_meta, ans_map, poll_schema, mr_schema, agm_schema)
        if by_class := manual_ans.gml.classes:
            for by_class_item in by_class:
                group_gml_ans(by_class_item, poll_meta, ans_map, poll_schema, mr_schema, agm_schema)
    elif rule == PollGMLRules.RP and manual_ans.resolution_passed:
        special_rules = [PollGMLRules.RESOLUTION_PASSED]
        ans_map["poll"][PollGMLRules.PASSED_DATE].append(
            [
                (
                    poll_schema.find_schema_by_path([PollGMLRules.RESOLUTION_PASSED, PollGMLRules.RESOLUTION]),
                    manual_ans.resolution_passed.resolution,
                ),
                (
                    poll_schema.find_schema_by_path([PollGMLRules.RESOLUTION_PASSED, PollGMLRules.PASSED]),
                    manual_ans.resolution_passed.pass_or_not,
                ),
            ]
        )
    elif rule == PollGMLRules.AD and manual_ans.approval_date:
        special_rules = [PollGMLRules.PASSED_DATE]
        ad_ans = manual_ans.approval_date.passed_date
        ad_ans.value = manual_ans.approval_date.enum
        ans_map["poll"][PollGMLRules.PASSED_DATE].append(
            [(poll_schema.find_schema_by_path([PollGMLRules.PASSED_DATE]), manual_ans.approval_date.passed_date)]
        )
    if poll_ans := ans_map["poll"]:
        poll_asnwer = gen_anwer(poll_ans, user)
        poll_question.answer = replace_answer_item(poll_question.answer, poll_asnwer, special_rules)
        await pw_db.update(poll_question, only=["answer"])
    if mr_ans := ans_map["mr"]:
        mr_question = await NewQuestion.find_by_fid_mid(poll_meta.mr_fid, special_mold.v6_1_mr_id)
        mr_answer = gen_anwer(mr_ans, user)
        mr_question.answer = replace_answer_item(mr_question.answer, mr_answer, special_rules)
        await pw_db.update(mr_question, only=["answer"])
    if agm_ans := ans_map["agm"]:
        agm_question = await NewQuestion.find_by_fid_mid(poll_meta.agm_fid, special_mold.v6_1_agm_id)
        agm_answer = gen_anwer(agm_ans, user)
        agm_question.answer = replace_answer_item(agm_question.answer, agm_answer, special_rules)
        await pw_db.update(agm_question, only=["answer"])

    adjustment_infos = gen_gml_history(rule, old_ans or {}, rule_ans or {})
    file_meta = await HKEXFileMeta.find_by_fid(question.fid)
    rule_reference = await RuleReference.find_by_rule(PollGMLRules.GML_TITLE_CASE)
    action = await get_action_by_rule(PollGMLRules.GML_TITLE_CASE, question.fid, special_mold.v6_poll_id, None)
    for adjustment_info in adjustment_infos:
        adjustment = adjustment_info.adjustment
        if not adjustment:
            continue
        meta = {
            "rule": PollGMLRules.GML_TITLE_CASE,
            "stock_code": poll_meta.stock_code,
            "year": file_meta.report_year,
            "main_alias": rule_reference.main_alias,
            "rule_subtab": adjustment_info.rule_subtab,
            "adjustment": adjustment,
        }
        if adjustment_info.box_change:
            meta["box_change"] = adjustment_info.box_change
        await NewHistory.save_operation_history_by_user(
            qid=question.id, current_user=user, action=action, meta=HistoryMeta(**meta)
        )


def gen_anwer(poll_ans: list[str:list], user):
    items_ans = []
    for _, ans_list in poll_ans.items():
        for idx, item_list in enumerate(ans_list):
            for field_ans in item_list:
                field_schema, field_ans = field_ans[0], field_ans[1]
                path_with_index = compact_dumps([f"{name}:{idx}" for name, idx in zip(field_schema.path, [0, idx, 0])])
                item = {
                    "key": path_with_index,
                    "schema": field_schema.to_answer_data(),
                    "score": -1,
                    "data": [i.model_dump() for i in field_ans.data] if field_ans else [],
                    "value": field_ans.value if field_ans else None,
                    "marker": {"id": user.id, "name": user.name, "others": []},
                    "md5": calc_key_hash(path_with_index),
                    "special_ui": True,
                    "manual": True,
                    "meta": {},
                }
                items_ans.append(item)
    return {"userAnswer": {"items": items_ans}}


def group_gml_ans(gml_single_ans, poll_meta, ans_map, poll_schema: MoldSchema, mr_schema, agm_schema):
    # 定义字段处理配置：字段名 -> (规则键, 非poll_fid的目标映射)
    FIELD_CONFIG = {
        "total_shares": (PollGMLRules.TOTAL_SHARE, {"mr_fid": "mr"}),
        "treasury_shares": (PollGMLRules.TREASURY_SHARE, {"mr_fid": "mr"}),
        "percentage": (PollGMLRules.PERCENTAGE, {"agm_fid": "agm"}),
    }

    # 统一处理所有字段
    for field_name, (rule_key, non_poll_targets) in FIELD_CONFIG.items():
        field_value = getattr(gml_single_ans, field_name)
        if field_value is None:
            continue

        # 确定目标存储位置（poll/mr/agm）
        if field_value.fid == poll_meta.poll_fid:
            target = "poll"
        else:
            # 查找匹配的非poll目标
            target = None
            for fid_attr, target_key in non_poll_targets.items():
                if field_value.fid == getattr(poll_meta, fid_attr):
                    target = target_key
                    break
            if target is None:
                continue  # 没有匹配的目标位置

        # 获取字段对应的schema路径
        value_schema = poll_schema.find_schema_by_path([rule_key, PollGMLRules.VALUE])
        class_schema = poll_schema.find_schema_by_path([rule_key, PollGMLRules.SHARE_CLASS])

        # 构建条目并添加到结果映射
        entry = [(value_schema, field_value), (class_schema, gml_single_ans.related_class)]
        ans_map[target][rule_key].append(entry)


@router.get("/gml/{qid:int}/prompt")
async def get_gml_prompt(
    question: Annotated[
        NewQuestion,
        model_with_perm(NewQuestion, fields=("id", "fid"), alias="qid"),
    ],
    rule: Literal[PollGMLRules.GML, PollGMLRules.RP, PollGMLRules.AD],
    sub_rule: Literal[
        PollGMLRules.TOTAL_SHARE,
        PollGMLRules.TREASURY_SHARE,
        PollGMLRules.PERCENTAGE,
        "General mandate resolution",
        "Yes or No",
    ]
    | None = None,
    size: int = Query(default=get_config("prompter.top_n", 5), gt=0),
):
    poll_q = NewQuestion.alias("poll_q")
    mr_q = NewQuestion.alias("mr_q")
    agm_q = NewQuestion.alias("agm_q")
    query = (
        POLLMeta.select(
            POLLMeta.poll_fid,
            POLLMeta.mr_fid,
            POLLMeta.agm_fid,
            poll_q.crude_answer.alias("poll_crude"),
            mr_q.crude_answer.alias("mr_crude"),
            agm_q.crude_answer.alias("agm_crude"),
        )
        .join(poll_q, on=((poll_q.fid == POLLMeta.poll_fid) & (poll_q.mold == special_mold.v6_1_poll_id)))
        .join(mr_q, on=((mr_q.fid == POLLMeta.mr_fid) & (mr_q.mold == special_mold.v6_1_mr_id)))
        .join(agm_q, on=((agm_q.fid == POLLMeta.agm_fid) & (agm_q.mold == special_mold.v6_1_agm_id)))
        .where(POLLMeta.poll_fid == question.fid)
    ).dicts()
    curde_ans = await pw_db.first(query)
    crude_key = gml_crude_key(rule, sub_rule)
    poll_crude = curde_ans.get("poll_crude").get(crude_key, [])[:size]
    data = {"AGM Poll Results": gen_crude_res(curde_ans.get("poll_fid"), poll_crude)}
    if rule == PollGMLRules.GML:
        if sub_rule in (PollGMLRules.TOTAL_SHARE, PollGMLRules.TREASURY_SHARE):
            data["Monthly Return"] = gen_crude_res(
                curde_ans.get("mr_fid"), curde_ans.get("mr_crude").get(crude_key, [])
            )[:size]
        elif sub_rule == PollGMLRules.PERCENTAGE:
            data["AGM Circular"] = gen_crude_res(
                curde_ans.get("agm_fid"), curde_ans.get("agm_crude").get(crude_key, [])
            )[:size]
    return data


def gml_crude_key(rule: str, sub_rule: str):
    if rule == PollGMLRules.GML:
        return f"{sub_rule}-{PollGMLRules.VALUE}"
    elif rule == PollGMLRules.RP:
        if sub_rule == "General mandate resolution":
            return f"{PollGMLRules.RESOLUTION_PASSED}-{PollGMLRules.RESOLUTION}"
        elif sub_rule == "Yes or No":
            return f"{PollGMLRules.RESOLUTION_PASSED}-{PollGMLRules.PASSED}"
    elif rule == PollGMLRules.AD:
        return PollGMLRules.PASSED_DATE
    return f"{rule}-{sub_rule}"


def gen_crude_res(fid: int, crude_list: list):
    return [
        {
            "fid": fid,
            "score": item["score"],
            "text": item.get("element_text") or item["text"],
            "page": item["page"],
            "outlines": [item["outline"]],
        }
        for item in crude_list
    ]


async def gen_file_meta(poll_meta):
    result = {}
    file_map = {
        "poll": poll_meta.poll_fid,
        "agm": poll_meta.agm_fid,
        "mr": poll_meta.mr_fid,
    }
    file_type_mid_map = {
        "poll": special_mold.v6_1_poll_id,
        "agm": special_mold.v6_1_agm_id,
        "mr": special_mold.v6_1_mr_id,
    }

    conditions = []
    for file_type, fid in file_map.items():
        if not fid:
            continue
        mid = file_type_mid_map[file_type]
        conditions.append((fid, mid, file_type))

    if not conditions:
        return result

    query_conditions = []
    for fid, mid, _ in conditions:
        query_conditions.append((NewQuestion.fid == fid) & (NewQuestion.mold == mid))

    query = (
        NewQuestion.select(
            NewQuestion.id.alias("qid"),
            NewFile.tree_id,
            NewQuestion.fid,
            NewQuestion.mold,
            HKEXFileMeta.published,
            HKEXFileMeta.report_year,
            HKEXFile.release_time,
        )
        .join(NewFile, on=(NewFile.id == NewQuestion.fid))
        .join(HKEXFileMeta, on=(NewQuestion.fid == HKEXFileMeta.fid))
        .join(HKEXFile, on=(NewQuestion.fid == HKEXFile.fid))
        .where(orm.or_(*query_conditions))
        .dicts()
    )

    rows = await pw_db.execute(query)

    for row in rows:
        for fid, mid, file_type in conditions:
            if row["fid"] == fid and row["mold"] == mid:
                row["url"] = str(Url(fid, mid, row["qid"], row["tree_id"], schema_key=""))
                result[file_type] = PollFileMetaSchema(**row)

    return result


def gen_gml_history(
    sub_rule: Literal[PollGMLRules.GML, PollGMLRules.RP, PollGMLRules.AD], old_ans, current_ans
) -> list[AdjustmentInfo]:
    rule = PollGMLRules.GML_TITLE_CASE
    ret = []
    # 根据修改的字段生成正确的历史记录
    if sub_rule == PollGMLRules.GML:
        if enum_adj := gen_adjustment_info(
            rule,
            sub_rule,
            old_ans.get("enum"),
            current_ans.get("enum"),
            f'Disclosure Location from "<{old_ans.get("enum")}>" to "<{current_ans.get("enum")}>"',
        ):
            ret.append(enum_adj)
        if calc_type := gen_adjustment_info(
            rule,
            sub_rule,
            old_ans.get("calc_type"),
            current_ans.get("calc_type"),
            f'Calculation Type from "<{old_ans.get("calc_type")}>" to "<{current_ans.get("calc_type")}>"',
        ):
            ret.append(calc_type)
        old_limit = get_limit_by_formula(old_ans.get("formula", ""))
        new_limit = get_limit_by_formula(current_ans.get("formula", ""))
        if formual_adj := gen_adjustment_info(
            rule,
            sub_rule,
            old_limit,
            new_limit,
            rf'Added General mandate limit from "<{old_limit}>" to "<{new_limit}>"',
        ):
            ret.append(formual_adj)
        # 比较 Total
        if total_adjs := compare_gml_item(
            "Total",
            "Total",
            {
                "old": (old_ans.get("total") or {}),
                "current": (current_ans.get("total") or {}),
            },
        ):
            ret.extend(total_adjs)
        # 比较 Class
        by_class_map = defaultdict(dict)
        for class_item in old_ans.get("classes") or []:
            if uuid := class_item.get("uuid"):
                by_class_map[uuid]["old"] = class_item
        for class_item in current_ans.get("classes") or []:
            if uuid := class_item.get("uuid"):
                by_class_map[uuid]["current"] = class_item
        for _, ans_pair in by_class_map.items():
            old_name = ans_pair.get("old", {}).get(PollGMLRules.SHARE_CLASS, {}).get("text")
            current_name = ans_pair.get("current", {}).get(PollGMLRules.SHARE_CLASS, {}).get("text")
            if item_adjs := compare_gml_item(old_name, current_name, ans_pair):
                ret.extend(item_adjs)

    elif sub_rule == PollGMLRules.RP:
        if enum_adj := gen_adjustment_info(
            rule,
            sub_rule,
            old_ans.get("enum"),
            current_ans.get("enum"),
            f'Disclosure Location from "<{old_ans.get("enum")}>" to "<{current_ans.get("enum")}>"',
        ):
            ret.append(enum_adj)
        if resolution_adj := gen_tagged_adjustment(
            rule,
            rule,
            PollGMLRules.RP_RESOLUTION,
            old_ans.get("resolution"),
            current_ans.get("resolution"),
        ):
            ret.append(resolution_adj)
        if passed_adj := gen_tagged_adjustment(
            rule,
            rule,
            PollGMLRules.PASSED,
            old_ans.get("pass_or_not"),
            current_ans.get("pass_or_not"),
        ):
            ret.append(passed_adj)
    elif sub_rule == PollGMLRules.AD:
        if enum_adj := gen_adjustment_info(
            rule,
            sub_rule,
            old_ans.get("enum"),
            current_ans.get("enum"),
            f'Disclosure Location from "<{old_ans.get("enum")}>" to "<{current_ans.get("enum")}>"',
        ):
            ret.append(enum_adj)
        if tagged_adj := gen_tagged_adjustment(
            rule, sub_rule, None, old_ans.get("passed_date"), current_ans.get("passed_date")
        ):
            ret.append(tagged_adj)
    return ret


def get_limit_by_formula(formula: str):
    parts = formula.split("=", 1)  # 最多分割一次
    limit = parts[1].strip() if len(parts) > 1 else None
    return limit


def compare_gml_item(old_name, current_name, ans_pair):
    ret = []
    # 修改
    if ans_pair.get("old") and ans_pair.get("current"):
        if name_adj := gen_adjustment_info(
            PollGMLRules.GML_TITLE_CASE,
            PollGMLRules.GML,
            old_name,
            current_name,
            f'Class Type Name from "<{old_name}>" to "<{current_name}>"',
        ):
            ret.append(name_adj)
        # 比较字段
        if field_adj := compare_gml_field(current_name, ans_pair.get("old", {}), ans_pair.get("current", {})):
            ret.extend(field_adj)
    # 新增
    if not ans_pair.get("old") and ans_pair.get("current"):
        ret.append(AdjustmentInfo(PollGMLRules.GML_TITLE_CASE, PollGMLRules.GML, f'Added "<{current_name}>"', {}))
        # 比较字段
        if field_adj := compare_gml_field(current_name, ans_pair.get("old", {}), ans_pair.get("current")):
            ret.extend(field_adj)
    # 删除
    if ans_pair.get("old") and not ans_pair.get("current"):
        ret.append(
            AdjustmentInfo(
                PollGMLRules.GML_TITLE_CASE, PollGMLRules.GML, f'Removed "<{old_name}>" (including all attributes)', {}
            )
        )
    return ret


def compare_gml_field(name, old_ans, current_ans):
    ret = []
    # Number of treasury shares, Total number of issued shares, Percentage
    for field in [PollGMLRules.TOTAL_SHARE, PollGMLRules.TREASURY_SHARE, PollGMLRules.PERCENTAGE]:
        if not current_ans.get(field):
            desc = f'Removed "<{name}>" {field}'
        else:
            desc = f'"<{name}>" {field} from "<{old_ans.get(field, {}).get("text")}>" to "<{current_ans.get(field, {}).get("text")}>"'
        if field_anj := gen_adjustment_info(
            PollGMLRules.GML_TITLE_CASE,
            PollGMLRules.GML,
            old_ans.get(field, {}).get("text"),
            current_ans.get(field, {}).get("text"),
            desc,
        ):
            ret.append(field_anj)
    return ret


def gen_tagged_adjustment(rule, sub_rule, field, old_ans, current_ans):
    old_boxes, current_boxes = compare_label_answer(
        old_ans.get("data") if old_ans else {}, current_ans.get("data") if current_ans else {}
    )
    prefix = f"<{field}> " if field else ""
    adjustment_desc = (
        f"{prefix}Manually delete the tagged information"
        if (old_boxes and not current_boxes)
        else f"{prefix}Manual Tagged Information"
    )
    return gen_adjustment_info(rule, sub_rule, old_boxes, current_boxes, f"{adjustment_desc}", is_box=True)
