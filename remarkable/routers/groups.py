import http
import logging
from collections import defaultdict
from copy import copy
from typing import List, Literal

import peewee
from fastapi import APIRouter, Body, Depends, HTTPException, Query
from speedy.middleware.i18n import translate
from speedy.peewee_plus import orm
from typing_extensions import Annotated
from utensils.util import generate_timestamp

from remarkable.answer.node import AnswerItem
from remarkable.common.common import EmptyAnswer
from remarkable.common.constants import CRUD, HistoryAction, PolicyEsgRules, PollGMLRules, RuleOperation
from remarkable.common.pagination import AsyncPagination
from remarkable.db import pw_db
from remarkable.dependencies import (
    CommonDep,
    get_business_admin,
    get_current_user,
    model_with_perm,
    pw_transaction,
)
from remarkable.models.mold import special_mold
from remarkable.models.new_file import NewFile
from remarkable.models.new_history import HistoryMeta, NewHistory
from remarkable.models.new_mold import NewMold
from remarkable.models.new_question import NewQuestionWithFile
from remarkable.models.poll_gml import PollGML
from remarkable.models.rule_group import GroupRuleRef, RuleGroup
from remarkable.models.rule_reference import RuleReference
from remarkable.models.rule_search_history import RuleSearchHistory
from remarkable.models.user import AdminUser
from remarkable.plugins.hkex.utils import <PERSON><PERSON><PERSON><PERSON><PERSON>
from remarkable.schemas import PaginateSchema, TRuleType
from remarkable.schemas.esg import SpecialESGAnswerSchema
from remarkable.schemas.rule import ExtraRuleGroupSchema, RuleGroupSchema, RuleSchema
from remarkable.services.ar import get_target_clz, sync_ar_answer_result

router = APIRouter(prefix="/groups", tags=["groups"])
logger = logging.getLogger(__name__)


@router.get("", response_model=List[ExtraRuleGroupSchema], dependencies=[Depends(get_current_user)])
async def get_groups(
    rule_type: TRuleType = None,
    only_enabled: bool = False,
    file_id: int | None = Query(None, description="文件ID，用于获取文件关联的规则分组"),
):
    """Get all groups with rules

    如果指定了 `rule_type`，返回指定类型的 `group.children`，否则返回所有 `groups`（长度目前为6，分别对应年报、季报、ESG 报告、CG 报告、AGM 报告、POLL 报告）

    如果指定了 `file_id`，那么 `rule_type` 必传，同时返回值结构将变为 `ExtraRuleGroupSchema`

    """
    if file_id and not rule_type:
        raise HTTPException(
            status_code=http.HTTPStatus.BAD_REQUEST, detail='"rule_type" is required when "file_id" is given'
        )
    groups = await RuleGroup.get_all(rule_type=rule_type, only_enabled=only_enabled)
    trees = RuleGroup.build_trees(groups, schema=ExtraRuleGroupSchema if file_id else RuleGroupSchema)
    trees = [t for t in trees if t.level == 0]
    trees = trees if len(trees) > 1 else trees[0].children
    if file_id is None:
        return trees

    mold_ids = special_mold.ar_mids
    # TODO helper question 的 answer 存在ruleA的answer 原因还未定位到
    # http://100.64.0.105:55647/#/hkex/annual-report-checking/report-review/258878?fileId=89236&schemaId=5&rule=B82
    match rule_type:
        case "qr":
            mold_ids = special_mold.qr_mids
        case "esg":
            mold_ids = special_mold.esg_mids_with_policy
        case "cg":
            mold_ids = [special_mold.v5_id]
        case "agm":
            mold_ids = [special_mold.v6_agm_id]
        case "poll":
            mold_ids = [special_mold.v6_poll_id]

    rule_answers = defaultdict(dict)
    if rule_type in {"ar", "agm", "poll"}:
        ar_like_class = get_target_clz(mold_ids[0])
        cached_mids = await pw_db.scalars(
            ar_like_class.select(ar_like_class.mold_id).distinct().where(ar_like_class.file_id == file_id)
        )
        if missed_mids := set(mold_ids) - set(cached_mids):
            logger.warning(f"No cached answers for {missed_mids=}, {file_id=}, syncing...")
            for question in await pw_db.execute(
                NewQuestionWithFile.select().where(
                    NewQuestionWithFile.file == file_id, NewQuestionWithFile.mold.in_(missed_mids)
                )
            ):
                await sync_ar_answer_result(question.id, question=question)

        for row in await pw_db.execute(
            ar_like_class.select(
                ar_like_class.compliance_answer_key,
                ar_like_class.compliance_value,
                ar_like_class.ai_compliance_answer["value"].alias("ai_compliance_value"),
                peewee.fn.COALESCE(
                    ar_like_class.ai_disclosure_answer[ar_like_class.compliance_answer_key][0]["value"],
                    ar_like_class.ai_disclosure_answer[peewee.fn.CONCAT(ar_like_class.compliance_answer_key, ".1")][0][
                        "value"
                    ],
                    peewee.Value(""),
                ).alias("ai_disclosure_value"),
                peewee.fn.COALESCE(
                    ar_like_class.manual_disclosure_answer[ar_like_class.compliance_answer_key][0]["value"],
                    ar_like_class.manual_disclosure_answer[peewee.fn.CONCAT(ar_like_class.compliance_answer_key, ".1")][
                        0
                    ]["value"],
                    peewee.Value(""),
                ).alias("manual_disclosure_value"),
            )
            .where(ar_like_class.file_id == file_id)
            .order_by(ar_like_class.id.desc())
            .namedtuples()
        ):
            rule_answers.setdefault(
                row.compliance_answer_key,
                {
                    "disclosure_value": row.manual_disclosure_value or row.ai_disclosure_value,
                    "compliance_value": row.compliance_value or row.ai_compliance_value,
                },
            )
        if rule_type == "poll":
            query = PollGML.select(PollGML.manual_gml, PollGML.predict_gml).where(PollGML.fid == file_id)
            poll_gml = await pw_db.first(query)
            rule_answers[PollGMLRules.GML] = {"disclosure_value": "", "compliance_value": ""}
            if poll_gml:
                manual_gml = poll_gml.manual_gml if poll_gml.manual_gml else {}
                predict_gml = poll_gml.predict_gml if poll_gml.predict_gml else {}

                resolution_passed = ""
                if manual_gml and PollGMLRules.RP in manual_gml:
                    resolution_passed = manual_gml[PollGMLRules.RP].get("enum", "")
                if not resolution_passed and predict_gml and PollGMLRules.RP in predict_gml:
                    resolution_passed = predict_gml[PollGMLRules.RP].get("enum", "")
                rule_answers[PollGMLRules.GML_TITLE_CASE]["compliance_value"] = resolution_passed
    else:
        # TODO: Optimize the performance of the rest of the rule types(qr, esg, cg)
        file = await NewFile.get_by_id(
            file_id,
            NewQuestionWithFile.select(
                NewQuestionWithFile.id,
                NewQuestionWithFile.file,
                NewQuestionWithFile.mold,
                NewQuestionWithFile.answer,
                NewQuestionWithFile.preset_answer,
            ).where(NewQuestionWithFile.mold.in_(mold_ids)),
        )
        if not file:
            raise HTTPException(status_code=http.HTTPStatus.NOT_FOUND, detail=f"File({file_id}) not found")
        for question in file.questions:
            empty_answer = None
            if not question.answer or not question.preset_answer:
                mold = await NewMold.get_by_id(question.mold)
                empty_answer = EmptyAnswer(mold).answer
            answer = MoldAnswer(question.answer or empty_answer)
            preset_answer = MoldAnswer(question.preset_answer or empty_answer)
            for rule in answer.schema.root_rules_map:
                if rule.startswith("丨"):
                    continue
                ans = answer.get_rule_answer(rule)
                preset_ans = preset_answer.get_rule_answer(rule)
                if rule_type == "esg" and rule in {PolicyEsgRules.E6, PolicyEsgRules.E8, PolicyEsgRules.E9}:
                    rule_answers[rule].setdefault("disclosure_value", "")
                    if any(i.get("special_ui") for i in ans["answer"]):
                        # 标注界面标注的答案会合并到 answer 表中，所以这里需要根据 special_ui 过滤下
                        rule_answers[rule]["disclosure_value"] = SpecialESGAnswerSchema.build_from_answer_items(
                            *ans["answer"], rule=rule
                        ).value
                    if not rule_answers[rule]["disclosure_value"]:
                        rule_answers[rule]["disclosure_value"] = SpecialESGAnswerSchema.build_from_answer_items(
                            *preset_ans["answer"], rule=rule
                        ).value
                else:
                    rule_answers[rule].setdefault(
                        "disclosure_value",
                        AnswerItem(**ans["answer"][0]).enum
                        if ans["answer"] and ans["answer"][0].get("special_ui")
                        else "",
                    )
                    if not rule_answers[rule]["disclosure_value"]:
                        rule_answers[rule]["disclosure_value"] = (
                            AnswerItem(**preset_ans["answer"][0]).enum if preset_ans["answer"] else ""
                        )
                label_compliance_value = ans["rule_result"]["value"] if ans["rule_result"].get("special_ui") else ""
                rule_answers[rule].setdefault("compliance_value", label_compliance_value)
                if not rule_answers[rule]["compliance_value"]:
                    rule_answers[rule]["compliance_value"] = preset_ans["rule_result"]["value"]

    for tree in trees:
        tree.update_rule_label(rule_type, rule_answers)
    return trees


@router.get("/{group_id:int}/children", response_model=PaginateSchema[RuleGroupSchema])
async def get_children(
    group: Annotated[RuleGroup, model_with_perm(RuleGroup, alias="group_id")],
    commons: CommonDep,
    user: AdminUser = Depends(get_current_user),
):
    query = (
        RuleGroup.select()
        .where(RuleGroup.parent_id == group.id)
        .order_by(RuleGroup.level, RuleGroup.order, RuleGroup.created_utc, RuleGroup.id)
    )

    conflict_actions = list(range(HistoryAction.CREATE_GROUP, HistoryAction.DELETE_GROUP + 1))
    if not await NewHistory.manager().exists(
        NewHistory.select().where(
            NewHistory.action.in_(conflict_actions), NewHistory.action_time >= (generate_timestamp() - 1)
        )
    ):
        # 避免在冲突事件发生时的重复记录
        # TODO: 前端增加特殊处理，区分正常访问事件和冲突事件
        await NewHistory.save_operation_history_by_user(
            None,
            user,
            HistoryAction.LOAD_PAGE,
            meta=HistoryMeta(system_function=["rule management"]),
        )
    return await AsyncPagination(query, commons.page, commons.page_size).data(
        GroupRuleRef.select(),
        RuleReference.select(),
        no_marshal=True,
    )


@router.get("/{group_id:int}", response_model=RuleGroupSchema, summary="获取指定分组信息/别组引用")
async def get_group(group: Annotated[RuleGroup, model_with_perm(RuleGroup, alias="group_id")]):
    """可用于编辑分组信息时，从他组引用"""
    groups = RuleGroup.build_trees(await RuleGroup.get_all(group_id=group.id))
    if not groups:
        raise HTTPException(status_code=http.HTTPStatus.NOT_FOUND, detail=f"Group({group.id}) not found")
    return groups[0]


@router.get("/search", response_model=PaginateSchema[RuleGroupSchema])
async def search_group(
    rule_type: TRuleType,
    commons: CommonDep,
    by: Literal["name", "description", "group"] = None,
    user: AdminUser = Depends(get_current_user),
):
    """Get special group and rule"""
    cond = ~(RuleReference.rule.startswith("ratio"))
    if rule_type == "ar":
        cond &= (RuleReference.rule.regexp(r"^[A-D]\d+\.?\d?$")) & (RuleReference.batch.not_in(["jura4", "jura2.1"]))
        order_by = (RuleReference.rule,)
    elif rule_type == "qr":
        cond &= (RuleReference.rule.startswith("Disclosure")) & (RuleReference.batch != "jura4")
        order_by = (RuleReference.order,)
    elif rule_type == "esg":
        cond &= RuleReference.batch == "jura4"
        order_by = (RuleReference.order,)
    else:
        cond &= RuleReference.batch == "jura2.1"
        order_by = (RuleReference.id,)

    if not by:
        cond &= orm.or_(RuleReference.rule.contains(commons.q), RuleReference.rule_description.contains(commons.q))
    elif by != "group":
        cond_column = RuleReference.rule if by == "name" else RuleReference.rule_description
        cond &= orm.or_(cond_column.contains(commons.q))

    rule_query = RuleReference.select(RuleReference.id).where(cond).order_by(*order_by)
    rule_ids = list(await pw_db.execute(rule_query))

    group_name = commons.q if by == "group" else None
    groups = await RuleGroup.get_all(rule_type=rule_type, name=group_name)
    parent_ids = [group.id for group in groups]
    if not parent_ids:
        return []
    parent = RuleGroup.select().where(RuleGroup.parent_id.in_(parent_ids)).cte("base", recursive=True)
    child = RuleGroup.alias("child")
    recursive = child.select().join(parent, on=(child.parent_id == parent.c.id))
    cte = parent.union_all(recursive)
    subquery = cte.select_from(cte.c.id, cte.c.parent_id, cte.c.user_id).alias("jq")
    exist_query = peewee.fn.EXISTS(subquery.select().where(cte.c.id == RuleGroup.id))

    rule_group_cond = orm.or_(exist_query, RuleGroup.id.in_(parent_ids))
    rule_group_cond &= RuleGroup.is_enabled
    rule_group_cond &= GroupRuleRef.rulereference_id.in_(rule_ids)
    if not by:
        rule_group_cond |= RuleGroup.name.contains(commons.q)

    query = (
        RuleGroup.select()
        .join(GroupRuleRef, on=(RuleGroup.id == GroupRuleRef.rulegroup_id))
        .group_by(RuleGroup)
        .having(peewee.fn.COUNT(GroupRuleRef.id) > 0)
        .where(rule_group_cond)
        .order_by(RuleGroup.id)
    )
    groups = await AsyncPagination(query, commons.page, commons.page_size).data(
        GroupRuleRef.select().where(GroupRuleRef.rulereference_id.in_(rule_ids)),
        fields="id,created_utc,updated_utc,deleted_utc,name,description,parent_id,user_id,level,rules",
    )

    data = {
        "user_id": user.id,
        "rule_type": rule_type,
        "search_by": by or "all",  # NOTE: search_by为null时不会触发唯一性约束 Postgres 15 才支持
        "text": commons.q,
        "updated_utc": generate_timestamp(),
    }
    await RuleSearchHistory.insert_or_update(
        conflict_target=[
            RuleSearchHistory.user_id,
            RuleSearchHistory.rule_type,
            RuleSearchHistory.search_by,
            RuleSearchHistory.text,
        ],
        **data,
    )

    return groups


@router.get(
    "/{group_id:int}/rules", response_model=PaginateSchema[RuleSchema], summary="Get rules(or search by condition)"
)
async def get_rules(
    group: Annotated[RuleGroup, model_with_perm(RuleGroup, alias="group_id")],
    commons: CommonDep,
    by: Literal["MLR", "GLR"] = "MLR",
):
    groups = await RuleGroup.get_all(group_id=group.id)
    if not groups:
        raise HTTPException(status_code=http.HTTPStatus.NOT_FOUND, detail=f"Group({group.id}) not found")

    sub_query = GroupRuleRef.select(GroupRuleRef.rulereference_id.distinct()).where(
        GroupRuleRef.rulegroup_id.in_([g.id for g in groups])
    )
    cond = RuleReference.id.in_(sub_query)
    if commons.q:
        if by == "MLR":
            cond &= orm.or_(
                RuleReference.main_alias.contains(commons.q),
                # RuleReference.main_rule.contains(commons.q),
                # RuleReference.rule.contains(commons.q),
            )
        else:
            cond &= orm.or_(
                RuleReference.gem_alias.contains(commons.q),
                # RuleReference.gem_rule.contains(commons.q),
                # RuleReference.rule.contains(commons.q),
            )
    fields = copy(RuleReference._meta.sorted_fields)

    subquery = (
        RuleReference.select(
            RuleReference.rule, (peewee.fn.COUNT(RuleReference.rule) > 0).alias("data"), include_deleted=True
        )
        .where(RuleReference.operation == RuleOperation.NEW.value)
        .group_by(RuleReference.rule)
        .alias("subquery")
    )
    fields.append(subquery.c.data.alias("already_new"))

    special_field = peewee.Case(
        None, ((RuleReference.operation == RuleOperation.REPEALED.value, "Repealed"),), "Active"
    )
    fields.append(special_field.alias("status"))
    query = (
        RuleReference.select(*fields)
        .join(subquery, on=(RuleReference.rule == subquery.c.rule), join_type=peewee.JOIN.LEFT_OUTER)
        .where(cond)
        .order_by(RuleReference.order)
        .dicts()
    )
    return await AsyncPagination(query, commons.page, commons.page_size).data(no_marshal=True)


@router.post(
    "/{group_id:int}/rules",
    response_model=RuleGroupSchema,
    dependencies=[
        Depends(get_business_admin),
        Depends(pw_transaction),
    ],
)
async def add_rules(
    group: Annotated[RuleGroup, model_with_perm(RuleGroup, alias="group_id", lock=True)],
    rule_ids: Annotated[List[int], Query(..., description="要添加到本组的规则ID列表")],
):
    rules = list(await pw_db.execute(RuleReference.select().where(RuleReference.id.in_(rule_ids))))
    if not rules:
        raise HTTPException(status_code=http.HTTPStatus.BAD_REQUEST, detail=f"No rules found for group: {group.id}")

    await pw_db.execute(GroupRuleRef.delete().where(GroupRuleRef.rulegroup_id == group.id))
    with pw_db.allow_sync():
        group.rules.add(rules)
    groups = RuleGroup.build_trees(await RuleGroup.get_all(group_id=group.id))
    return groups[0]


@router.delete(
    "/{group_id:int}/rules",
    response_model=RuleGroupSchema,
    summary="从分组中移除规则",
    dependencies=[Depends(get_business_admin)],
)
async def remove_rules(
    group: Annotated[RuleGroup, model_with_perm(RuleGroup, alias="group_id")],
    rule_ids: Annotated[List[int], Query(..., description="要删除的规则ID列表")],
):
    """Remove rules from a group"""
    await pw_db.execute(
        GroupRuleRef.delete().where(GroupRuleRef.rulegroup_id == group.id, GroupRuleRef.rulereference_id.in_(rule_ids))
    )
    groups = RuleGroup.build_trees(await RuleGroup.get_all(group_id=group.id))
    return groups[0]


@router.post("", response_model=RuleGroupSchema)
async def create_group(group: Annotated[RuleGroupSchema, Body], user: AdminUser = Depends(get_business_admin)):
    """Create a group"""
    if group.parent_id == 0:
        raise HTTPException(
            status_code=http.HTTPStatus.BAD_REQUEST,
            detail=translate(
                "Invalid parent group id: ",
                group.parent_id,
                " detected, please check.",
                sep='"',
            ),
        )

    parent_group = await RuleGroup.get_by_id(group.parent_id)
    if not parent_group:
        raise HTTPException(
            status_code=http.HTTPStatus.BAD_REQUEST, detail=f"Parent group({group.parent_id}) not found"
        )
    group.user_id = user.id
    group.level = parent_group.level + 1
    # 新创建的分组默认是禁用状态
    group.is_enabled = False
    async with pw_db.atomic():
        await create_group_recursively(group)

    tree = RuleGroup.build_trees(await RuleGroup.get_all(group_id=group.id))[0]
    ancestors = await RuleGroup.get_ancestors(group.id)
    await NewHistory.save_operation_history_by_user(
        None,
        user,
        HistoryAction.CREATE_GROUP,
        meta={"group_name": group.name, "report_type": ancestors[0].name},
    )
    return tree


async def create_group_recursively(group: RuleGroupSchema) -> RuleGroupSchema:
    group.self_check()

    if await pw_db.first(
        RuleGroup.select().where(RuleGroup.name == group.name, RuleGroup.parent_id == group.parent_id)
    ):
        raise HTTPException(
            status_code=http.HTTPStatus.BAD_REQUEST,
            detail=translate(
                "Duplicate group name: ",
                group.name,
                " detected, please check.",
                sep='"',
            ),
        )

    new_group = await RuleGroup.insert_or_update(
        conflict_target=[RuleGroup.name, RuleGroup.parent_id],
        **group.model_dump(exclude={"id", "created_utc", "updated_utc", "deleted_utc", "rules", "children"}),
    )
    if new_group.deleted_utc:
        new_group.deleted_utc = 0
        new_group.user_id = group.user_id
        await pw_db.update(new_group, only=["deleted_utc", "user_id"])

    await pw_db.execute(GroupRuleRef.delete().where(GroupRuleRef.rulegroup_id == new_group.id))
    if rule_ids := [r.id for r in group.rules]:
        if rules := list(
            await pw_db.execute(
                RuleReference.select()
                .where(RuleReference.id.in_(rule_ids))
                .order_by(peewee.fn.array_position(peewee.Value(rule_ids, unpack=False), RuleReference.id))
            )
        ):
            with pw_db.allow_sync():
                new_group.rules.add(rules)
    for idx, child in enumerate(group.children):
        child.parent_id = new_group.id
        child.level = new_group.level + 1
        # 子分组必须是启用状态才能在Report Review界面显示
        child.is_enabled = True
        child.order = idx
        sub_group = await create_group_recursively(child)
        child.id = sub_group.id

    group.id = new_group.id
    return group


@router.put("/{group_id:int}", response_model=RuleGroupSchema)
async def update_group(
    group: Annotated[RuleGroup, model_with_perm(RuleGroup, alias="group_id", action=CRUD.U)],
    group_data: Annotated[RuleGroupSchema, Body],
    user: AdminUser = Depends(get_business_admin),
):
    """Update a group"""
    parent_group = await RuleGroup.get_by_id(group_data.parent_id)
    if not parent_group:
        raise HTTPException(
            status_code=http.HTTPStatus.BAD_REQUEST, detail=f"Parent group({group_data.parent_id}) not found"
        )
    if group_data.is_enabled and not group.is_enabled:
        enabled_group_count = await pw_db.scalar(
            RuleGroup.select(peewee.fn.COUNT(RuleGroup.id)).where(
                RuleGroup.parent_id == group.parent_id, RuleGroup.is_enabled
            )
        )
        max_count = 3
        if enabled_group_count >= max_count:
            raise HTTPException(
                status_code=http.HTTPStatus.BAD_REQUEST,
                detail=f"The count of enabled groups must not exceed {max_count}",
            )

    group_data.level = parent_group.level + 1

    if group_data.is_enabled != group.is_enabled and not group_data.children:
        # 如果分组下没有子分组，那么可以直接修改启用状态
        group.is_enabled = group_data.is_enabled
        group.level = group_data.level
        await pw_db.update(group, only=["is_enabled", "level"])
        await NewHistory.save_operation_history_by_user(
            None,
            user,
            HistoryAction.ENABLE_GROUP if group_data.is_enabled else HistoryAction.DISABLE_GROUP,
            meta={"group_name": group.name, "report_type": parent_group.name},
        )
    else:
        try:
            async with pw_db.atomic():
                sub_group_changed = await update_group_recursively(user, group_data, group, parent_group.name)
        except peewee.IntegrityError:
            raise HTTPException(
                status_code=http.HTTPStatus.BAD_REQUEST,
                detail=translate("Duplicate group name detected, please check."),
            ) from None
        if sub_group_changed:
            await NewHistory.save_operation_history_by_user(
                None,
                user,
                HistoryAction.EDITE_SUB_GROUP,
                meta={"group_name": group.name, "report_type": parent_group.name},
            )
    return RuleGroup.build_trees(await RuleGroup.get_all(group_id=group.id))[0]


async def update_group_recursively(
    user: AdminUser,
    group_schema: RuleGroupSchema,
    group: RuleGroup,
    report_type: str,
    sub_group_changed: bool = False,
) -> bool:
    group_schema.self_check()

    # update self
    if group.name != group_schema.name:
        await NewHistory.save_operation_history_by_user(
            None,
            user,
            HistoryAction.EDITE_GROUP_NAME,
            meta={"group_name": group.name, "new_group_name": group_schema.name, "report_type": report_type},
        )
    group.name = group_schema.name

    if group.description != group_schema.description:
        await NewHistory.save_operation_history_by_user(
            None,
            user,
            HistoryAction.EDITE_GROUP_DESC,
            meta={
                "group_name": group.name,
                "report_type": report_type,
                "old_description": group.description,
                "new_description": group_schema.description,
            },
        )
    group.description = group_schema.description

    group.parent_id = group_schema.parent_id
    group.is_enabled = group_schema.is_enabled
    group.level = group_schema.level
    await pw_db.update(group, only=["name", "description", "parent_id", "is_enabled", "level"])

    # update rules
    rids_in_db = await pw_db.scalars(
        GroupRuleRef.select(GroupRuleRef.rulereference_id)
        .where(GroupRuleRef.rulegroup_id == group.id)
        .order_by(GroupRuleRef.id)
    )
    await pw_db.execute(GroupRuleRef.delete().where(GroupRuleRef.rulegroup_id == group.id))
    if rule_ids := [r.id for r in group_schema.rules]:
        if rule_ids != rids_in_db:
            sub_group_changed = True
        if rules := list(
            await pw_db.execute(
                RuleReference.select()
                .where(RuleReference.id.in_(rule_ids))
                .order_by(peewee.fn.array_position(peewee.Value(rule_ids, unpack=False), RuleReference.id))
            )
        ):
            with pw_db.allow_sync():
                group.rules.add(rules)

    # update children
    group_ids_in_db = await pw_db.scalars(RuleGroup.select(RuleGroup.id).where(RuleGroup.parent_id == group.id))
    for idx, child in enumerate(group_schema.children):
        child.parent_id = group.id
        child.level = group.level + 1
        child.order = idx
        if sub_group := await RuleGroup.get_first_one(
            ((RuleGroup.name == child.name) & (RuleGroup.parent_id == group.id))
        ):
            if sub_group.name != child.name:
                sub_group_changed = True
        else:
            sub_group_changed = True
        sub_group = await RuleGroup.insert_or_update(
            conflict_target=[RuleGroup.name, RuleGroup.parent_id],
            **child.model_dump(exclude={"id", "created_utc", "updated_utc", "deleted_utc", "rules", "children"}),
        )
        if sub_group.id in group_ids_in_db:
            group_ids_in_db.remove(sub_group.id)
        child.id = sub_group.id
        sub_group_changed = await update_group_recursively(user, child, sub_group, report_type, sub_group_changed)

    # delete children(if any)
    if group_ids_in_db:
        await delete_group_by_id(*group_ids_in_db)
        sub_group_changed = True
    return sub_group_changed


@router.delete("/{group_id:int}", response_model=int, summary="删除分组（包括子组），返回受影响的数据行数")
async def delete_group(
    group: Annotated[RuleGroup, model_with_perm(RuleGroup, alias="group_id")],
    user: AdminUser = Depends(get_business_admin),
):
    if group.is_enabled:
        raise HTTPException(
            status_code=http.HTTPStatus.BAD_REQUEST,
            detail=translate("Can't delete a enabled group, please disable it first"),
        )

    ancestors = await RuleGroup.get_ancestors(group.id)
    await NewHistory.save_operation_history_by_user(
        None,
        user,
        HistoryAction.DELETE_GROUP,
        meta={"group_name": group.name, "report_type": ancestors[0].name},
    )
    return await delete_group_by_id(group.id)


async def delete_group_by_id(*group_id: int) -> int:
    count = 0
    groups = await RuleGroup.get_groups(group_id, GroupRuleRef.select(), RuleReference.select(), include_self=True)
    group_ids = []
    relation_ids = []
    for group in groups:
        if group.parent_id == 0:
            raise HTTPException(status_code=http.HTTPStatus.BAD_REQUEST, detail="Can't delete root group")
        group_ids.append(group.id)
        for row in group.rulegrouprulereferencethrough_set:
            relation_ids.append(row.id)
    async with pw_db.atomic():
        count += await pw_db.execute(RuleGroup.delete().where(RuleGroup.id.in_(group_ids)))
        count += await pw_db.execute(GroupRuleRef.delete().where(GroupRuleRef.id.in_(relation_ids)))
    return count
