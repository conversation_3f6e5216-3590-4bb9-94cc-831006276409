# fmt: off
import logging

from remarkable.common.constants import PolicyEsgRules, AGMRules
from remarkable.common.hkex_util import parse_year_end_date
from remarkable.devtools.task_prompter import run_prompt_element
from remarkable.models.hkex_file import HKEXFile
from remarkable.predictor.helpers import predict_mold_answer, inspect_rule, inspect_answer

# from interdoc.loader import CommonLoader
# from interdoc.struct import Interdoc
from remarkable.common.constants import PolicyEsgRules
from remarkable.common.constants import PolicyEsgRules
from remarkable.devtools.task_dev import stat_accuracy
from remarkable.devtools.task_dev import stat_accuracy
from remarkable.services.embedding import create_and_save_embeddings, summary_chart, encode_image


# from remarkable.prompter.embedding import prompt_from_pre_train_embedding

#
# def judge_image():
#     pdf_path = "/Users/<USER>/PycharmProjects/scriber_hkex/data/files/36/d5fd00af21457e6ff87770bd4dd388"
#     res = pdf.PDF.is_image_pdf(pdf_path)
#     print(res)
#
def freeze_import():
    from_id = 0
    mid= 5
    predict_mold_answer(from_id, mid)
    inspect_rule(from_id, mid, answer_type='preset')
    inspect_answer(mid, 1, 1000000, None, True)
    create_and_save_embeddings(70211)
    print(PolicyEsgRules.E4)
    res = summary_chart(base64_image=encode_image(''), image_title="test")
    from invoke import Context
    stat_accuracy(Context(), "/Users/<USER>/PycharmProjects/scriber_hkex/data/files/36/d5fd00af21457e6ff87770bd4dd388", 1)
    run_prompt_element()


#
#
# async def sync_if_not_exist(fid):
#     file = await NewFile.find_by_id(fid)
#     if not file:
#         await fetch_and_sync(fid)
#
# async def task_predictor_preset_answer():
#     await run_preset_answer(70560, 268579, only_jura21=False, preset_path=None)
#
# async def esg_fy_demo(fid):
#     hkex_file = await pw_db.first(HKEXFile.select().where(HKEXFile.fid == fid))
#     await parse_esg_meta(hkex_file)
#
#
# def time_wrapper(func):
#     def wrapper(*args, **kwargs):
#         start = time.time()
#         result = func(*args, **kwargs)
#         end = time.time()
#         print(f"Function {func.__name__} took {end - start} seconds")
#         return result
#
#     return wrapper
# @time_wrapper
# def pages_count_diff(data):
#     """
#     :param data: pdf data
#     :return: page count
#     """
#     parser = PDFParser(BytesIO(data))
#     doc = PDFDocument(parser)
#     parser.set_document(doc)
#     pages = resolve1(doc.catalog["Pages"])
#     return pages.get("Count", 0)
#
# @time_wrapper
# def new_pdfparse_func(data):
#     return get_page_num(data)
#
#
# def gen_stat():
#     from invoke import Context
#     # stat_accuracy_for_jura21(Context(), "/Users/<USER>/PycharmProjects/scriber_hkex/lir_uat_s_r_32")
#     # stat_accuracy_for_cg(Context(), "/Users/<USER>/PycharmProjects/scriber_hkex/poll_uat_s_fid_20",34)
#     # stat_accuracy(Context(), "/Users/<USER>/PycharmProjects/scriber_hkex/poll_uat_s_fid_20",34)
#     stat_accuracy(Context(), "/Users/<USER>/PycharmProjects/scriber_hkex/data/hkex/dev_fids_files/agm_final_s_fid_20",33)
#     # stat_accuracy_for_policy(Context(), "/Users/<USER>/PycharmProjects/scriber_hkex/policy_uat_s_r_30")
#     # stat_accuracy_for_policy(Context(), "/Users/<USER>/PycharmProjects/scriber_hkex/data/hkex/dev_fids_files/policy_label_export_s_r")
async def set_qr_meta(fid):
    hkex_file = await HKEXFile.get_first_one(cond= (HKEXFile.fid==fid))
    year_end = parse_year_end_date(hkex_file.to_dict(), pdfinsight_path=None)
    print(year_end)


def run_prompt(fid, qid, mid):
    import asyncio
    asyncio.run(run_prompt_element(fid, qid, mid))

def main():
    for name in (
        "remarkable.common.pattern",
        "remarkable.predictor.models.base_model",
        "remarkable.predictor.models.policy_esg_e89",
        "remarkable.predictor.models.policy_esg_e1",
        "remarkable.predictor.models.policy_esg_e2",
        "remarkable.predictor.models.policy_esg_e3",
        "remarkable.predictor.models.policy_esg_e4",
        "remarkable.predictor.predictor",
        "remarkable.predictor.predictor",
        "remarkable.predictor.group_utils",
        "remarkable.predictor.hkex_predictor.schemas.c_rule_schema",
        "remarkable.predictor.hkex_predictor.schemas.cg_schema",
        "remarkable.predictor.hkex_predictor.share_group",
        "remarkable.predictor.hkex_predictor.share_group",
        "remarkable.rule.inspector",
        "remarkable.plugins.predict.predictor",
    ):
        logging.getLogger(name).setLevel(logging.DEBUG)
    special_rules = None
    # mid= 31
    # to_id = from_id = 71316
    # mid= 32
    # to_id = from_id = 112914
    # special_rules = [PolicyEsgRules.E1.value]
    # special_rules = [PolicyEsgRules.E2.value,PolicyEsgRules.E3.value]
    # special_rules = [PolicyEsgRules.E2.value,PolicyEsgRules.E3.value,PolicyEsgRules.E4.value]
    # special_rules = [PolicyEsgRules.E5.value, PolicyEsgRules.E6.value]
    # special_rules = [PolicyEsgRules.E7.value,PolicyEsgRules.E8.value,PolicyEsgRules.E9.value]
    # special_rules = ["C2.1.1"]
    # special_rules = ["B11"]
    # special_rules = ["Whether the resolution has been passed"]
    # predict_mold_answer(mid, from_id, to_id, special_rules=special_rules, only_jura21=False)
    mid= 15
    to_id = from_id = 93638  # you lead ined  但是在DR章节
    # to_id = from_id = 174582
    # to_id = from_id = 133791

    # special_rules = [AGMRules.M33,AGMRules.M35]
    predict_mold_answer(mid, from_id, to_id, special_rules=special_rules, only_jura21=False)
    # run_prompt(fid=167219, qid=401223, mid=mid)

    # mid= 15
    # to_id = from_id = 158366
    # special_rules = ["B38"]
    # to_id = from_id = 157228
    # special_rules = ["B1"]

    # mid= 18
    # to_id = from_id = 171456
    # special_rules = ["C2.1.1"]
    # predict_mold_answer(mid, from_id, to_id, special_rules=special_rules, only_jura21=False)
    # inspect_rule(from_id, mid, answer_type='preset', only_jura21=False, special_rules=special_rules)
    # inspect_rule(from_id, mid, answer_type='preset', only_jura21=False)

if __name__ == "__main__":
    main()
