import re
from datetime import datetime
from typing import Any

from fastapi import HTTPException
from pydantic import BaseModel, ConfigDict, Field, field_validator, model_validator

SIMPLE_DATE_FORMAT = "%b %d %Y %H:%M:%S"


class HKEXNewsResult(BaseModel):
    hkex_file_id: str
    non_critical_flags: list[str]
    critical_flags: list[str]
    jura_url: str
    release_time: str
    modify_time: str


class TimeRange(BaseModel):
    from_date: Any = None
    to_date: Any = None

    @field_validator("from_date", "to_date", mode="before")
    def check_date(cls, v, field):
        if v is None:
            return v
        try:
            return datetime.fromisoformat(v)
        except ValueError as e:
            raise HTTPException(status_code=400, detail=f"{field.field_name} must be isoformat") from e

    @model_validator(mode="after")
    def check(self):
        if self.from_date and self.to_date and self.to_date < self.from_date:
            raise HTTPException(status_code=400, detail="to_date must be after from_date")
        return self


_FLEX_STR = "flexString1=HKEX_APP flexString1Label=HKEX Application Unified Identifier"
_P_TIMESTAMP = re.compile(r"=(\d{13})")


class CEFHistoryEvent(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    created_utc: str
    event_id: str
    event: str

    @staticmethod
    def _convert_timestamps(text: str) -> str:
        def replace_timestamp(match):
            timestamp = int(match.group(1)) / 1000  # Convert milliseconds to seconds
            return f"={datetime.fromtimestamp(timestamp).strftime(SIMPLE_DATE_FORMAT)}"

        return _P_TIMESTAMP.sub(replace_timestamp, text)

    def model_post_init(self, __context: Any) -> None:
        self.event = self._convert_timestamps(self.event)
        if "outcome=" not in self.event:
            self.event = f"{self.event} outcome=success"
        if not self.event.endswith(_FLEX_STR):
            self.event = f"{self.event} {_FLEX_STR}"


class CEFHistorySchema(BaseModel):
    format: str = "cef"
    version: str = "1.0"
    timestamp: str
    count: int
    events: list[CEFHistoryEvent]


class MandateLimitResult(BaseModel):
    stock_code: str = Field(description="Stock code of the company")
    poll_result_release_time: str = Field(description="Release time of the poll result")
    jura_url: str = Field(description="URL to the Jura platform for viewing the poll result")
    poll_result_id: str = Field(description="Poll result report ID on the hkexnews")
    agm_id: str = Field(description="Agm report ID on the hkexnews")
    monthly_return_id: str = Field(description="Monthly return report ID on the hkexnews")
    general_mandate_limit: str | None = Field(description="General mandate limit value")
    whether_passed: str | None = Field(description="Whether the mandate was passed")
    passed_date: str | None = Field(description="Date when the mandate was passed")
    modify_time: str = Field(description="Last modification time")
