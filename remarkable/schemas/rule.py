import datetime
import http
from collections import Counter
from typing import Any, Dict, List, Literal

from fastapi import HTTPException
from pydantic import BaseModel, Field, field_validator, model_validator
from speedy.middleware.i18n import translate

from remarkable.common.common import JURA21_COLS
from remarkable.common.constants import CNAEnum, HistoryAction, PollGMLRules, RuleOperation
from remarkable.config import get_config
from remarkable.models.new_history import NewHistory
from remarkable.schemas import BaseORM, TRuleType


class RuleSchema(BaseORM):
    rule: str
    main_rule: str = Field(..., description="MLR")
    main_alias: str = Field(..., description="MLR Alias")
    rule_description: str = Field(..., description="MLR Description")
    gem_rule: str = Field(..., description="GLR")
    gem_alias: str = Field(..., description="GLR Alias")
    gem_description: str = Field(..., description="GLR Description")
    batch: str | None = None
    order: int | None = None
    activated_at: int | None = Field(default=None, description="Date Effective")
    operation: int | None = Field(default=None, description="Operation")
    status: str | None = None
    already_new: bool | None = None

    @field_validator("already_new")
    def check_already_new(cls, value: bool | None) -> bool:
        return value or False


class DescriptionHistorySchema(BaseModel):
    activated_at: int
    updated_utc: int = Field(..., description="Use `activated_at` instead")
    description: str
    operation_enum: int
    operation: str = Field(default="")
    title: str

    def model_post_init(self, __context) -> None:
        self.operation = RuleOperation(self.operation_enum).name


class HistoryAndReviewSchema(BaseModel):
    review: DescriptionHistorySchema
    history: List[DescriptionHistorySchema]


class ExtraRuleSchema(RuleSchema):
    disclosure_value: str = Field(default="", description="Disclosure Value")
    compliance_value: str = Field(default="", description="Compliance Value")
    color: Literal["green", "red"] = Field(default="green", description="Color")
    label: str = Field(default="C", description="Label")

    def update_label(
        self,
        rule_type: TRuleType,
        values: Dict[Literal["disclosure_value", "compliance_value"], str],
    ):
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/2372
        self.disclosure_value = (values.get("disclosure_value") or self.disclosure_value).lower()
        self.compliance_value = (values.get("compliance_value") or self.compliance_value).lower()

        if rule_type == "ar" and not self.compliance_value and self.disclosure_value in ("yes", "no", "n/a"):
            self.label = values.get("disclosure_value")
            if self.disclosure_value == "no":
                self.color = "red"
            return

        if (rule_type in {"ar", "agm", "poll"} and not self.compliance_value) or (
            rule_type in {"esg", "cg"} and not self.disclosure_value
        ):
            self.label = ""
            return

        if rule_type in {"ar", "agm", "poll"}:
            if "non-compliance" in self.compliance_value:
                self.color = "red"
                self.label = "NC"
            if self.rule == PollGMLRules.GML_TITLE_CASE:
                label_map = {"yes": "Y", "no": "N", "no disclosure": "ND"}
                self.label = label_map[self.compliance_value]
                self.color = "green" if self.label == "Y" else "red"

        elif rule_type == "esg" and self.disclosure_value and self.disclosure_value != "comply":
            self.color = "red"
            match self.disclosure_value:
                case "explain":
                    self.label = "E"
                case "no disclosure":
                    self.label = "ND"
                case "query":
                    self.label = "Q"
        elif rule_type == "cg":
            if self.disclosure_value == CNAEnum.C.phrase.lower():
                self.label = CNAEnum.C.name
            elif self.disclosure_value == CNAEnum.ND.phrase.lower():
                self.label = CNAEnum.ND.name
                self.color = "red"
            else:
                self.color = "red"
                self.label = CNAEnum.NA.label


class _RuleGroupSchema(BaseORM):
    name: str = Field(min_length=1)
    description: str = ""
    parent_id: int | None = Field(
        default=0, description="父节点ID，本组子节点中的`parent_id`可不传，会自动填充为本组ID"
    )
    user_id: int | None = 1
    level: int | None = 0
    is_enabled: bool = True
    order: int = 0

    def model_post_init(self, __context) -> None:
        self.name = self.name[:255]
        if self.parent_id == 0 and not self.description:
            if self.name == "Annual Report":
                self.description = "ar"
            elif self.name == "Result":
                self.description = "qr"
            elif self.name == "ESG Report":
                self.description = "esg"
            elif self.name == "CG Report":
                self.description = "cg"


class RuleGroupSchema(_RuleGroupSchema):
    rules: List[RuleSchema] = Field(default_factory=list)
    children: List["RuleGroupSchema"] = Field(
        default_factory=list,
        description='self reference of "RuleGroupSchema"',
        examples=[
            [
                {
                    "id": 1,
                    "name": "child1",
                    "description": "child1 description",
                },
                {
                    "id": 2,
                    "name": "child2",
                    "description": "child2 description",
                },
            ]
        ],
    )

    def flatten(self):
        yield self
        for child in self.children:
            # 保证子节点的parent_id和level正确
            assert self.id is not None
            child.parent_id = self.id
            child.level = self.level + 1
            yield from child.flatten()

    def self_check(self):
        if self.name.lower() == "all":
            raise HTTPException(
                status_code=http.HTTPStatus.BAD_REQUEST,
                detail=translate(
                    "Duplicate group name: ",
                    self.name,
                    " detected, please check.",
                    sep='"',
                ),
            )

        counter = Counter()
        for child in self.children:
            counter[child.name] += 1

        if duplicated := ", ".join(name for name, count in counter.items() if count > 1):
            raise HTTPException(
                status_code=http.HTTPStatus.BAD_REQUEST,
                detail=f'Duplicate sub-group name found: "{duplicated}".',
            )


class ExtraRuleGroupSchema(_RuleGroupSchema):
    rules: List[ExtraRuleSchema] = Field(default_factory=list)
    children: List["ExtraRuleGroupSchema"] = Field(
        default_factory=list,
        description='self reference of "ExtraRuleGroupSchema"',
        examples=[
            [
                {
                    "id": 1,
                    "name": "child1",
                    "description": "child1 description",
                },
                {
                    "id": 2,
                    "name": "child2",
                    "description": "child2 description",
                },
            ]
        ],
    )

    def update_rule_label(
        self,
        rule_type: TRuleType,
        rule_answers: Dict[str, Dict[Literal["disclosure_value", "compliance_value"], str]],
    ):
        for rule in self.rules:
            rule.update_label(rule_type, rule_answers.get(rule.rule, {}))
            if get_config("hide_jura21") and rule.rule in JURA21_COLS:
                rule.label = ""
                rule.color = "green"
        for child in self.children:
            child.update_rule_label(rule_type, rule_answers)


class RuleSearchSchema(BaseORM):
    user_id: int
    rule_type: str
    search_by: str | None = None
    text: str


class OperateSchema(BaseModel):
    rule: str
    main_alias: str
    at: int
    by: str
    operation: str

    @model_validator(mode="before")
    @classmethod
    def revise_history(cls, history: NewHistory | Dict[str, Any]) -> Dict[str, Any]:
        if isinstance(history, dict):
            return history

        ins = cls(
            rule="N/A",
            main_alias="N/A",
            at=history.action_time,
            by=history.user_name,
            operation="",
        )
        if history.action == HistoryAction.UPDATE_RULE:
            ins.operation += "Modified "
            for col, value in history.meta.items():
                if not isinstance(value, list):
                    continue
                from_value, to_value = value
                if col == "rule":
                    ins.rule = from_value
                    ins.main_alias = from_value
                    continue
                if col == "activated_at":
                    from_value = datetime.datetime.fromtimestamp(from_value).strftime("%Y-%m-%d")
                    to_value = datetime.datetime.fromtimestamp(to_value).strftime("%Y-%m-%d")
                if col == "operation":
                    from_value = RuleOperation(from_value).name
                    to_value = RuleOperation(to_value).name
                ins.operation += f'"{RuleSchema.model_fields[col].description}" from "{from_value}" to "{to_value}";\n'
            ins.operation = ins.operation.rstrip(";\n")
            # TODO: 其他操作（增/删）暂不支持
        return ins.model_dump()
