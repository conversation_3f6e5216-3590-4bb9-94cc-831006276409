from typing import Literal
from uuid import uuid4

from pydantic import BaseModel, <PERSON>

from remarkable.common.constants import AnswerValueEnum, GMLCalcType, PollGMLRules
from remarkable.schemas import TypeStripStr
from remarkable.schemas.answer import AnswerData


class PollFileMetaSchema(BaseModel):
    fid: int | None = Field(description="fid", default=None)
    url: TypeStripStr | None = Field(description="url", default=None)
    published: str | None = Field(description="published", default=None)
    release_time: int | None = Field(description="release_time", default=None)
    report_year: str | None = Field(description="report year", default=None)


class PollMetaSchema(BaseModel):
    stock_code: TypeStripStr | None = Field(description="Stock Code", default=None)
    poll: PollFileMetaSchema | None = Field(description="AGM Poll Results", default=None)
    mr: PollFileMetaSchema | None = Field(description="Monthly Returns", default=None)
    agm: PollFileMetaSchema | None = Field(description="AGM Circular", default=None)


class GetPollMetaSchema(BaseModel):
    file_id: int
    mold_id: int


class PollGMLFieldAnswerSchema(BaseModel):
    fid: int = Field(description="File ID")
    qid: int = Field(description="Question ID")
    text: str = Field(description="Answer text")
    value: int | float | str = Field(description="Answer value")
    data: list[AnswerData] = Field(default_factory=list, description="Box Data")


class PollGMLTypeAnswerSchema(BaseModel):
    uuid: str = Field(description="uuid", default_factory=lambda: str(uuid4()))
    related_class: PollGMLFieldAnswerSchema = Field(description="Related share class", alias="Relevant share class")
    total_shares: PollGMLFieldAnswerSchema = Field(
        description="Total number of issued shares", alias="Total number of issued shares"
    )
    treasury_shares: PollGMLFieldAnswerSchema | None = Field(
        description="Number of treasury shares", alias="Number of treasury shares", default=None
    )
    percentage: PollGMLFieldAnswerSchema | None = Field(description="Percentage", alias="Percentage", default=None)


class PollGMLAnswerSchema(BaseModel):
    total: PollGMLTypeAnswerSchema | None = Field(description="By Total Answer", default=None)
    classes: list[PollGMLTypeAnswerSchema] | None = Field(default_factory=list, description="By Class Answer")
    formula: str | None = Field(description="Formula", default="")
    enum: Literal[AnswerValueEnum.ND, AnswerValueEnum.PS] = Field(description="Enum", default=AnswerValueEnum.ND.value)
    calc_type: Literal[GMLCalcType.BY_TOTAl, GMLCalcType.BY_CLASS] = Field(
        description="Calculation Type", default=GMLCalcType.BY_TOTAl.value
    )


class PollResolutionSchema(BaseModel):
    resolution: PollGMLFieldAnswerSchema | None = Field(description="General mandate resolution", default=None)
    pass_or_not: PollGMLFieldAnswerSchema | None = Field(description="Yes or No", default=None)
    enum: Literal[AnswerValueEnum.ND, AnswerValueEnum.Yes, AnswerValueEnum.No, ""] = Field(
        description="Enum", default=""
    )


class PollPassedDateSchema(BaseModel):
    enum: Literal[AnswerValueEnum.ND, AnswerValueEnum.PS, ""] = Field(description="Enum", default="")
    passed_date: PollGMLFieldAnswerSchema | None = Field(description="Passed Date", default=None)


class PollGMLSubRuleSchema(BaseModel):
    gml: PollGMLAnswerSchema | None = Field(
        description="General mandate limit", alias="General mandate limit", default=None
    )
    resolution_passed: PollResolutionSchema | None = Field(
        description=PollGMLRules.RP, alias=PollGMLRules.RP, default=None
    )
    approval_date: PollPassedDateSchema | None = Field(description="Approval date", alias="Approval date", default=None)


class PollGMLRuleSchema(BaseModel):
    ai: PollGMLSubRuleSchema | None = Field(description="Ai answer", default=None)
    manual: PollGMLSubRuleSchema | None = Field(description="Manual answer", default=None)
