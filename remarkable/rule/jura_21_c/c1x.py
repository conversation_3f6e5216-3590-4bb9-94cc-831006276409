import json
import logging
import os
from dataclasses import dataclass
from datetime import datetime

from remarkable.common.common import get_keys
from remarkable.common.constants import COMP<PERSON><PERSON><PERSON><PERSON>, NON_COMPLIANCE, AnswerValueEnum, DocType
from remarkable.common.util import mm_notify
from remarkable.db import pw_db
from remarkable.models.hkex_company import HKEXCompany
from remarkable.models.hkex_file_meta import HKEXFileMeta
from remarkable.models.mold import special_mold
from remarkable.models.new_question import NewQuestion, QuestionPrevFundRaising
from remarkable.plugins.hkex.handlers_util import RuleAnswerHandlerUtil
from remarkable.rule.inspector import Rule
from remarkable.rule.utils import MultiGroupDisclosureMeta, get_disclosure_value

logger = logging.getLogger(__name__)

ENABLE_MM_NOTIFY = os.environ.get("ENV") == "hkex"


@dataclass
class PreviousYearResult:
    b9_enum: dict
    c1_2_1_enum: dict
    b9_is_nc: bool | None
    c1_2_1_is_nc: bool | None


class C1XRule(Rule):
    """
    C1.1.1, C1.2.1, C1.3合规判断依据：
    1. 若上年年报的B9+C1.2.1为ND/NS： C
    2. 若上年年报的B9+C1.2.1为PS：任意子项为ND -> NC, 否则C

    https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/5745

    2025.6合规逻辑变更： https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7432
    • 若上年【B9+C1.2.1-子项1】均是NS： C
    • 若上年【B9+C1.2.1-子项1】均是ND或为NS/ND，且任一合规为ND-NC: ND-NC; 其余情况C
    • 若上年【B9+C1.2.1-子项1】任一个是PS: 任意分组是ND-NC; 其余情况C
    """

    def __init__(self):
        super().__init__(["C1.1.1", "C1.2.1", "C1.3"])

    @staticmethod
    def get_prev_event_answer_value(preset_answer, rule, sub_name=None) -> dict:
        result = {}
        for item in get_keys(preset_answer, ["userAnswer", "items"]) or []:
            key_paths = [i.split(":")[0] for i in json.loads(item["key"])]
            if key_paths[1] != rule or (sub_name and sub_name != key_paths[2]):
                continue
            value = item.get("value")
            if not value or not item.get("meta") or "event_id" not in item["meta"]:
                break
            event_id = item["meta"]["event_id"]
            result[event_id] = value
        return result

    @staticmethod
    def get_prev_compliance_value(preset_answer, rule):
        for item in get_keys(preset_answer, ["rule_result", "items"]) or []:
            if rule != [i.split(":")[0] for i in json.loads(item["key"])][1]:
                continue
            return item.get("value")
        return None

    @classmethod
    def get_prev_year_answer_value(
        cls, fid, prev_fid, mid, rule, stock_code: str | None, sub_name=None
    ) -> tuple[dict | None, bool | None]:
        with pw_db.allow_sync():
            # 存储在临时表中的B9/C1.2.1答案
            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6309
            # TODO 所有2024年年报预测完成之后，可以删除下方5行代码
            prev_fund_question = (
                QuestionPrevFundRaising.select()
                .where(QuestionPrevFundRaising.fid == prev_fid, QuestionPrevFundRaising.mold_id == mid)
                .first()
            )
            # 上年年报的答案
            prev_question = NewQuestion.get_or_none(((NewQuestion.fid == prev_fid) & (NewQuestion.mold == mid)))
            if not (prev_fund_question or prev_question):
                # logger.error(f"No question from the previous year: {stock_code=}, {fid=}, {prev_fid=}, {mid=}")
                if ENABLE_MM_NOTIFY:
                    mm_notify(
                        f"No question from the previous year: {stock_code=}, {fid=}, {prev_fid=}, {mid=}", error=True
                    )
                return None, None
        result, prev_compliance = {}, None
        # 先从上年年报中找有效的分组答案(
        if (
            prev_question
            and prev_question.preset_answer
            and RuleAnswerHandlerUtil.answer_has_group_name(
                rule, get_keys(prev_question.preset_answer, ["userAnswer", "items"], [])
            )
        ):
            result = cls.get_prev_event_answer_value(prev_question.preset_answer, rule, sub_name)
            prev_compliance = cls.get_prev_compliance_value(prev_question.preset_answer, rule)
        # TODO 所有2024年年报预测完成之后，可以删除下方5行代码
        # 年报中没有有效的分组数据，再从prev_fund_question.preset_answer中找答案
        if not result and prev_fund_question and prev_fund_question.preset_answer:
            result = cls.get_prev_event_answer_value(prev_fund_question.preset_answer, rule, sub_name)
            prev_compliance = cls.get_prev_compliance_value(prev_fund_question.preset_answer, rule)
            logger.debug(
                f"use data from the table 'question_prev_fund_raising': id={prev_fund_question.id}, {prev_fid=}"
            )
        if not result:
            # logger.error(f"No valid {rule} answer from the previous year: {stock_code=}, {fid=}, {prev_fid=}, {mid=}")
            if ENABLE_MM_NOTIFY:
                mm_notify(
                    f"No valid {rule} answer from the previous year: {stock_code=}, {fid=}, {prev_fid=}, {mid=}",
                    error=True,
                )
            return None, None
        # 注意：这里判断NC时，要保证上一年至少有一个分组（即：上年不为No Event）
        return result, any(result) and prev_compliance == NON_COMPLIANCE

    @classmethod
    def get_answer_of_previous_year(cls, file_id, stock_code: str | None) -> PreviousYearResult | None:
        file_metas = HKEXFileMeta.find_previous_year_metas(file_id, DocType.AR.value)
        if not file_metas:
            if ENABLE_MM_NOTIFY:
                mm_notify(f"No report file from the previous year: {stock_code=}, fid={file_id}", error=True)
            return None
        # 若有多份,取预测时最新已发布的那份
        prev_fid = file_metas[0].fid
        # 取上年年报B9+C1.2.1子项1答案
        b9_result, b9_is_nc = cls.get_prev_year_answer_value(file_id, prev_fid, special_mold.v2_id, "B9", stock_code)
        c1_2_1_result, c1_2_1_is_nc = cls.get_prev_year_answer_value(
            file_id, prev_fid, special_mold.v3_id, "C1.2.1", stock_code, sub_name="Unutilized proceeds"
        )
        if b9_result is None and c1_2_1_result is None:
            return None
        return PreviousYearResult(b9_result or {}, c1_2_1_result or {}, b9_is_nc, c1_2_1_is_nc)

    def check(self, rule_name, question, mold_answer, pdfinsight, used_later_answer, stock_info):
        if not (stock_code := stock_info.stock if stock_info and stock_info.stock else ""):
            return COMPLIANCE
        # 当年刚上市的年报，没有往年年报，直接返回合规: https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6891#note_714782
        year_start, _ = stock_info.financial_start_end
        ipo_date = HKEXCompany.get_listing_date_by_stock(stock_code)
        if year_start and ipo_date and datetime.fromtimestamp(ipo_date).date() >= year_start:
            return COMPLIANCE
        # 找上年年报
        prev_result = self.get_answer_of_previous_year(question.fid, stock_code)
        if prev_result is None:
            # 取不到往年报告或答案，默认给C
            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6309
            return COMPLIANCE
        # B9+C1.2.1都是NS，则合规
        all_prev_enum = prev_result.b9_enum | prev_result.c1_2_1_enum
        if all_prev_enum and all(v == AnswerValueEnum.NS.value for v in all_prev_enum.values()):
            return COMPLIANCE
        found_events = set()
        # B9+C1.2.1中存在PS
        prev_has_ps = any(self.is_ps(v) for v in all_prev_enum.values())
        multi_group_disclosure = MultiGroupDisclosureMeta(question, mold_answer, rule_name)
        for third_word in multi_group_disclosure.third_words:
            for disclosure_meta in multi_group_disclosure.disclosure_metas[third_word]:
                event_id = disclosure_meta.get("meta", {}).get("event_id")
                if event_id != "":
                    found_events.add(event_id)
                # 往年的B9+C1.2.1存在PS或合规为NC，但当年为NO EVENT，则返回NC
                if event_id == "" and (prev_has_ps or prev_result.b9_is_nc or prev_result.c1_2_1_is_nc):
                    return NON_COMPLIANCE
                if event_id == "" or event_id not in all_prev_enum:
                    # TODO: 这种一般是当年或者往年分组提取错误
                    continue
                is_nd = get_disclosure_value(disclosure_meta) == AnswerValueEnum.ND
                if not is_nd:
                    continue
                prev_value = all_prev_enum[event_id]
                prev_is_nc = prev_result.b9_is_nc if event_id in prev_result.b9_enum else prev_result.c1_2_1_is_nc
                # 往年对应分组为PS，或往年不合规，则当年ND-NC
                if self.is_ps(prev_value) or prev_is_nc:
                    return NON_COMPLIANCE
        # 若往年B9存在PS/ND-NC的event，在当年没有找到对应event，则返回NC
        not_found_events = {e for e in all_prev_enum if e and e not in found_events}
        if any(
            event_id in not_found_events and (self.is_ps(value) or self.is_nd_nc(value, prev_result.b9_is_nc))
            for event_id, value in prev_result.b9_enum.items()
        ):
            return NON_COMPLIANCE
        # 若往年C1.2.1存在PS/ND-NC的event，在当年没有找到对应event，则返回NC
        if any(
            event_id in not_found_events and (self.is_ps(value) or self.is_nd_nc(value, prev_result.c1_2_1_is_nc))
            for event_id, value in prev_result.c1_2_1_enum.items()
        ):
            return NON_COMPLIANCE
        return COMPLIANCE

    @staticmethod
    def is_ps(value):
        return value == AnswerValueEnum.PS.value

    @staticmethod
    def is_nd_nc(value, prev_is_nc):
        return value == AnswerValueEnum.ND.value and prev_is_nc
