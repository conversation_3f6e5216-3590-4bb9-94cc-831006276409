import re
from itertools import chain

from remarkable.common.common import get_first_value
from remarkable.common.common_pattern import P_CELL_NIL
from remarkable.common.constants import COMPLIANC<PERSON>, NON_COMPLIANCE, AnswerValueEnum
from remarkable.models.nddr_repurchase_report import NDDRRepurchaseReport, NDDRTreasuryShare
from remarkable.models.new_question import NewQuestion
from remarkable.pdfinsight.reader import Pd<PERSON>sightReader
from remarkable.plugins.hkex.utils import MoldAnswer
from remarkable.predictor.hkex_predictor.schemas.pattern import gen_regex_by_price
from remarkable.rule.inspector import Rule, StockInfo
from remarkable.rule.utils import DisclosureMeta


class C1Rule(Rule):
    """
    C1合规判断
    https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6014
    外部文档示例：
    http://100.64.0.10:4082/#/project/inspect/72385?projectId=282&treeId=792&fileId=56231&schemaId=258&task_type=extract

    #### 2025.6 更新合规逻辑： https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6014
    情况1--所有子项都是NS时：
    所有子项都是NS+当年没有NDDR--C
    所有子项都是NS+当年虽有NDDR但其中没有所需表格--C
    所有子项都是NS+当年NDDR中repurchase数据为0 且Treasury share数据为0--C

    情况2--子项1&2&3均为PS 时：
    子项1&2&3均为PS+子项1&2的提取内容与NDDR内容一致+子项4&5均为ND/NS+NDDR 中Treasury share数据为0—C
    子项1&2&3均为PS+子项1&2的提取内容与NDDR内容一致+子项4&5均为PS +子项 4和NDDR 中Treasury share数据一致-C

    其余情况NC
    """

    def __init__(self):
        super(C1Rule, self).__init__(["C1"])

    def check(
        self,
        rule_name: str,
        question: NewQuestion,
        mold_answer: MoldAnswer,
        pdfinsight: PdfinsightReader,
        used_later_answer: dict,
        stock_info: StockInfo,
    ):
        disclosure_meta = DisclosureMeta(question, mold_answer, rule_name)
        disclosure_results = disclosure_meta.disclosure_results
        disclosure_contents = disclosure_meta.disclosure_contents
        if not stock_info.file_meta:
            raise Exception(f"No file meta found: qid={question.id}, fid={question.fid}, rule={rule_name}")
        stock_code = stock_info.stock
        year_start, year_end = stock_info.financial_year

        # 1. 子项4 & 5 不满足条件，返回NC
        name4 = "Number of treasury share held"
        value4, value5 = disclosure_results[name4], disclosure_results["Intended use"]
        if not self.check_treasury_shares(disclosure_contents[name4], value4, value5, stock_code, year_start, year_end):
            return NON_COMPLIANCE

        name1, name2, name3 = "Aggregate price", "The exchangs/arrangement", "Purchase by issuer/subsidiary"
        value1, value2, value3 = disclosure_results[name1], disclosure_results[name2], disclosure_results[name3]
        aggregate_prices = NDDRRepurchaseReport.aggregate_price_of_year(stock_code, year_start, year_end)
        # 2. 子项1&2&3全部为NS且NDDR且没有repurchase或repurchase为0
        if value1 == value2 == value3 == AnswerValueEnum.NS.value:
            if not aggregate_prices or all(price == 0 for _, price in aggregate_prices):
                return COMPLIANCE
            return NON_COMPLIANCE
        # 3. 存在repurchase时，子项1&2&任意一个不为PS，返回NC
        if not value1 == value2 == value3 == AnswerValueEnum.PS.value:
            return NON_COMPLIANCE
        # 4. 子项2（交易所）必须与外部文档一致
        contents1, contents2 = disclosure_contents[name1], disclosure_contents[name2]
        for exchange in NDDRRepurchaseReport.get_exchanges(stock_code, year_start, year_end):
            exchange = exchange.replace("On ", "on ")
            # NNDR中存的是on the Exchange，年报中一般披露on the Stock Exchange
            if not any(
                exchange in text or exchange in text.replace("the Stock Exchange", "the Exchange") for text in contents2
            ):
                return NON_COMPLIANCE
        # 5. 子项1（总价）必须与外部文档内容一致
        tbl_meta = disclosure_meta.disclosure_metas[name1].get("meta") or {}
        if self.check_aggregate_price(contents1, aggregate_prices, tbl_meta, stock_code, year_start, year_end):
            return COMPLIANCE
        return NON_COMPLIANCE

    @staticmethod
    def check_treasury_shares(contents4, value4, value5, stock_code, year_start, year_end):
        """
        对比子项4与NDDR中的treasury shares
        """
        treasury_shares = NDDRTreasuryShare.get_treasury_shares(stock_code, year_start, year_end)
        if treasury_shares > 0:
            # 子项4&5合规：均为PS且子项4与NDDR中Treasury share数值一致
            shares_num = f"{treasury_shares:,.0f}"
            if value4 == value5 == AnswerValueEnum.PS.value and any(shares_num in text for text in contents4):
                return True
        else:
            # 子项4&5合规：均为NS/ND且Treasury share为0 -- C
            return {value4, value5}.issubset({AnswerValueEnum.ND.value, AnswerValueEnum.NS.value})
        return False

    @classmethod
    def check_aggregate_price(cls, contents1, aggregate_prices, tbl_meta, stock_code, year_start, year_end):
        """
        对比子项1与NDDR中的aggregate price
        """
        cell_values, header = None, None
        # 1. 对比总价
        for currency, aggregate_price in aggregate_prices:
            price_regex = gen_regex_by_price(f"{aggregate_price}", unit=rf"({currency}?\$?.000\t.*|{currency}?\$?)")
            for content in contents1:
                if "\t" in content:
                    cell_values = content.split("\t")
                    p_currency = cls.gen_currency_regex(currency)
                    header = get_first_value(tbl_meta["col_headers"])
                    if not p_currency.search(header):
                        continue
                    if cls.is_price_equal(cell_values[-1], aggregate_price, header):
                        return True
                else:
                    if price_regex.search(content):
                        return True
        # 2. 如果总价不一致，对比每月数据
        by_month = NDDRRepurchaseReport.aggregate_price_of_months(stock_code, year_start, year_end)
        if cell_values and by_month:
            months = {
                "January",
                "February",
                "March",
                "April",
                "May",
                "June",
                "July",
                "August",
                "September",
                "October",
                "November",
                "December",
            }
            # meta存库后，整型的key会被转为字符串型，这里转回去
            row_headers = {int(k): v for k, v in tbl_meta["row_headers"].items()}
            offset_idx = len(row_headers) - len(cell_values)
            # 按月匹配，任意一个月匹配不到，则返回NC
            currency_dict = {}
            other_months = months - set(row_headers.values())
            if len(other_months) == 12:
                return NON_COMPLIANCE
            for num_or_month, value in chain(enumerate(cell_values), ((month, "0") for month in other_months)):
                if num_or_month in tbl_meta.get("header_rows") or []:
                    continue
                month = row_headers[num_or_month + offset_idx] if isinstance(num_or_month, int) else num_or_month
                if month not in months:
                    continue
                matched_currencies = set()
                for currency, price in by_month.get(month, [(None, 0.0)]):
                    if currency is None:
                        matched_currencies.add(None)
                    elif currency not in matched_currencies:
                        if currency in currency_dict:
                            p_currency = currency_dict[currency]
                        else:
                            p_currency = cls.gen_currency_regex(currency)
                            currency_dict[currency] = p_currency
                        if not p_currency.search(header):
                            continue
                        matched_currencies.add(currency)
                    if not cls.is_price_equal(value, price, header):
                        return False
                if not matched_currencies:
                    return False
        return True

    @staticmethod
    def gen_currency_regex(currency: str):
        return re.compile(rf"(^|\W){currency[:-1]}[{currency[-1]}$]+($|[^a-z])")

    @staticmethod
    def is_price_equal(ar_price: str, db_price: float, header: str):
        # 表格数据需要转换million/thousand等单位再做比较
        if "million" in header or "thousand" in header or header.count("000") == 2:
            expected_value = db_price / 1000000
        elif "000" in header:
            expected_value = db_price / 1000
        else:
            expected_value = db_price
        if P_CELL_NIL.search(ar_price) and not db_price:
            return True
        if ar_price in {f"{expected_value:.{i}f}" for i in range(3)} | {f"{expected_value:,.{i}f}" for i in range(3)}:
            return True
        return False
