from remarkable.common.constants import COMPL<PERSON>NC<PERSON>, NON_COMPLIANCE, AnswerValueEnum
from remarkable.common.pattern import NeglectPattern
from remarkable.pdfinsight.reader import PdfinsightReader
from remarkable.predictor.hkex_predictor.models.c5 import R_C5_CT
from remarkable.rule.inspector import Rule
from remarkable.rule.jura_21_c.c5 import R_CT_SKIP, has_keywords_in_doc
from remarkable.rule.utils import DisclosureMeta

P_CT = NeglectPattern.compile(match=R_C5_CT, unmatch=R_CT_SKIP)


def has_ct_in_doc(pdfinsight: PdfinsightReader):
    """
    全文检索：是否存在关键词CT/connected transaction
    """
    if has_keywords_in_doc(pdfinsight, P_CT):
        return True
    return False


class C7Rule(Rule):
    """
    PS/NS --> C
    ND+ 文档不存在CT/CCT --> C
    ND + 文档存在CT/CCT --> NC
    https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/5998

    2025/2/18需求变更：https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/5998#note_676790
    """

    def __init__(self):
        super().__init__(["C7.1", "C7.2", "C7.3", "C7.5"])

    @classmethod
    def any_c7_is_not_nd(cls, question, mold_answer, exclude_rule: str = None):
        for rule in {"C7.1", "C7.2", "C7.3", "C7.4.1", "C7.5"}:
            if exclude_rule and rule == exclude_rule:
                continue
            disclosure_meta = DisclosureMeta(question, mold_answer, rule)
            disclosure_results = disclosure_meta.disclosure_results
            if rule == "C7.4.1":
                for third_word in disclosure_meta.third_words:
                    if disclosure_results[third_word] != AnswerValueEnum.ND:
                        return True
            else:
                if disclosure_results[disclosure_meta.third_word] != AnswerValueEnum.ND:
                    return True
        return False

    def check(self, rule_name, question, mold_answer, pdfinsight, used_later_answer, stock_info):
        disclosure_meta = DisclosureMeta(question, mold_answer, rule_name)
        disclosure_result = disclosure_meta.disclosure_results[disclosure_meta.third_word]
        if disclosure_result == AnswerValueEnum.ND:
            if not has_ct_in_doc(pdfinsight):
                return COMPLIANCE
            # 需求变更： 除了当前之外的其余C7任一为PS，则不合规
            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/5998#note_674314
            if self.any_c7_is_not_nd(question, mold_answer, exclude_rule=rule_name):
                return NON_COMPLIANCE
        return COMPLIANCE


class C74Rule(Rule):
    """
    文档存在CT/CCT: 任一子项ND-NC;
    其余情况-C

    https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/5998#note_658040

    2025/2/18需求变更：https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/5998#note_676790
    """

    def __init__(self):
        super().__init__(["C7.4.1"])

    def check(self, rule_name, question, mold_answer, pdfinsight, used_later_answer, stock_info):
        disclosure_meta = DisclosureMeta(question, mold_answer, rule_name)
        disclosure_results = disclosure_meta.disclosure_results
        doc_has_cct = has_ct_in_doc(pdfinsight)
        for third_word in disclosure_meta.third_words:
            if disclosure_results[third_word] == AnswerValueEnum.ND:
                if not doc_has_cct:
                    continue
                # 需求变更： C7任一一项为PS，则不合规
                # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/5998#note_674314
                if C7Rule.any_c7_is_not_nd(question, mold_answer):
                    return NON_COMPLIANCE
        return COMPLIANCE
