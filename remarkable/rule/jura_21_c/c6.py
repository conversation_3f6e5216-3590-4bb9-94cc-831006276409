import re
from itertools import chain

from remarkable.common.common_pattern import R_CG_CHAPTER_TITLES
from remarkable.common.constants import COMPLIANCE, NON_COMPLIANCE, AnswerValueEnum, TableType
from remarkable.common.pattern import MatchMulti
from remarkable.pdfinsight.parser import parse_table
from remarkable.rule.inspector import Rule
from remarkable.rule.jura_21_c.c5 import has_keywords_in_doc
from remarkable.rule.utils import DisclosureMeta


class C61Rule(Rule):
    """
    （notes 表格） PS/NS-C
    ND:若 remuneration相关的表格中没有出现 bonus 关键词: ND-C
    若 remuneration相关的表格中有出现 bonus 关键词: ND-NC

    https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/5998
    """

    def __init__(self):
        super().__init__(["C6.1"])

    def check(self, rule_name, question, mold_answer, pdfinsight, used_later_answer, stock_info):
        disclosure_meta = DisclosureMeta(question, mold_answer, rule_name)
        disclosure_result = disclosure_meta.disclosure_results[disclosure_meta.third_word]
        disclosure_content = disclosure_meta.disclosure_contents[disclosure_meta.third_word]
        if disclosure_result != AnswerValueEnum.ND:
            return COMPLIANCE
        # ND场景下，表格中有bonus关键词 --> NC
        if any("bonus" in content.lower() for content in disclosure_content):
            return NON_COMPLIANCE
        if self.has_bonus(pdfinsight):
            return NON_COMPLIANCE
        return COMPLIANCE

    @staticmethod
    def has_bonus(pdfinsight):
        # 在remuneration章节下找带有bonus的表格
        syllabuses = pdfinsight.find_sylls_by_pattern(
            [
                re.compile(r"directors.*?(remuneration|emolument)|emoluments? of director", re.I),
            ]
        )
        for syllabus in syllabuses:
            tables = pdfinsight.find_elements_by_index_range(syllabus["element"], start=0, end=10, etype="TABLE")
            for table in tables:
                parsed_table = parse_table(table, tabletype=TableType.TUPLE, pdfinsight_reader=pdfinsight)
                if any(
                    "bonus" in text
                    for text in chain(parsed_table.col_header_texts.values(), parsed_table.row_header_texts.values())
                ):
                    return True
        return False


class C62Rule(Rule):
    """
    （全文检索）

    PS/NS-C

    ND:
    若存在join or upon joining + director出现在同一句内: ND-NC;

    其余ND-C


    https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/5998
    """

    P_JOIN_DIRECTOR = MatchMulti.compile(r"\b(join\b|upon\s*joining)", r"\sdirector\b", operator=all)
    P_SKIP_CHAPTER = MatchMulti.compile(*R_CG_CHAPTER_TITLES, operator=any)

    def __init__(self):
        super().__init__(["C6.2"])

    def check(self, rule_name, question, mold_answer, pdfinsight, used_later_answer, stock_info):
        disclosure_meta = DisclosureMeta(question, mold_answer, rule_name)
        disclosure_result = disclosure_meta.disclosure_results[disclosure_meta.third_word]
        if disclosure_result != AnswerValueEnum.ND:
            return COMPLIANCE
        # ND场景下，非CG章节下存在join or upon joining + director出现在同一句内 --> NC
        # https://mm.paodingai.com/cheftin/pl/kngsx9zrg3nm8qgwan471akncc
        if has_keywords_in_doc(pdfinsight, self.P_JOIN_DIRECTOR, p_skip_chapter=self.P_SKIP_CHAPTER):
            return NON_COMPLIANCE
        return COMPLIANCE


class C63Rule(Rule):
    """
    （全文检索）

    PS/NS-C

    ND:若存在compensation for loss of office + director出现在同一句内: ND-NC;

    其余ND-C

    https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/5998
    """

    P_LOSS_DIRECTOR = MatchMulti.compile(r"compensation\s*for\s*loss\s*of\s*office", r"\sdirectors?\b", operator=all)

    def __init__(self):
        super().__init__(["C6.3"])

    def check(self, rule_name, question, mold_answer, pdfinsight, used_later_answer, stock_info):
        disclosure_meta = DisclosureMeta(question, mold_answer, rule_name)
        disclosure_result = disclosure_meta.disclosure_results[disclosure_meta.third_word]
        # disclosure_content = disclosure_meta.disclosure_contents[disclosure_meta.third_word]
        if disclosure_result != AnswerValueEnum.ND:
            return COMPLIANCE
        # ND场景下，存在compensation for loss of office + director出现在同一句内 --> NC
        if has_keywords_in_doc(pdfinsight, self.P_LOSS_DIRECTOR):
            return NON_COMPLIANCE
        return COMPLIANCE
