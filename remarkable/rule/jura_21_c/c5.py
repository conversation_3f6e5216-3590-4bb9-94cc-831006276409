from remarkable.common.common import is_para_elt
from remarkable.common.common_pattern import R_MIDDLE_DASH
from remarkable.common.constants import CO<PERSON><PERSON><PERSON><PERSON><PERSON>, NON_COMPLIANCE
from remarkable.common.pattern import NeglectPattern
from remarkable.common.protocol import SearchPatternLike
from remarkable.common.util import split_paragraph
from remarkable.pdfinsight.reader import PdfinsightReader
from remarkable.predictor.hkex_predictor.models.c5 import R_CCT
from remarkable.predictor.hkex_predictor.schemas.pattern import R_NOT
from remarkable.rule.hkex_rules import ND
from remarkable.rule.inspector import Rule
from remarkable.rule.utils import DisclosureMeta

R_CT_SKIP = rf"{R_NOT}|\b(were|are|was|is|fully{R_MIDDLE_DASH}?)\s*exempted"
P_CCT = NeglectPattern.compile(match=R_CCT, unmatch=R_CT_SKIP)


def has_keywords_in_doc(
    pdfinsight: PdfinsightReader, p_keywords: SearchPatternLike, p_skip_chapter: SearchPatternLike = None
):
    for element in pdfinsight.elements_iter(is_para_elt):
        # 跳过标题: https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6408#note_672560
        if element["index"] in pdfinsight.syllabus_reader.elt_syllabus_dict:
            continue
        if any(p_keywords.search(s) for s in split_paragraph(element["text"])):
            if p_skip_chapter:
                # 跳过指定的root章节： https://mm.paodingai.com/cheftin/pl/kngsx9zrg3nm8qgwan471akncc
                root_title = pdfinsight.page_chapter_from_first_para.get(element["page"])
                if not root_title and (syllabuses := pdfinsight.find_syllabuses_by_index(element["index"])):
                    root_title = syllabuses[0]["title"]
                if not root_title or p_skip_chapter.search(root_title):
                    continue
            return True
    return False


class C5Rule(Rule):
    """
    PS/NS ---> C
    ND + 文中没有CCT ---> C
    ND + 文中有CCT --> NC
    https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/3859#note_451859
    """

    def __init__(self):
        super().__init__(["C5"])

    def check(self, rule_name, question, mold_answer, pdfinsight, used_later_answer, stock_info):
        disclosure_meta = DisclosureMeta(question, mold_answer, rule_name)
        disclosure_result = disclosure_meta.disclosure_results[disclosure_meta.third_word]
        if disclosure_result == ND:
            if has_keywords_in_doc(pdfinsight, P_CCT):
                return NON_COMPLIANCE
        return COMPLIANCE
