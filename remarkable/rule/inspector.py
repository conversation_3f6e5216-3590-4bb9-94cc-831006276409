import asyncio
import importlib
import logging
import traceback
from copy import deepcopy
from functools import cached_property
from typing import Any, Dict, List, Literal

from pydantic import BaseModel

from remarkable.answer.node import AnswerItem
from remarkable.common.common import (
    BELONG_BANK_STOCKS,
    BELONG_INSURANCE_STOCKS,
    HAS_SHARE_OPTION_STOCKS,
    JURA21_A_COLS,
    get_financial_year,
    get_financial_year_dates,
    has_share_options_2018,
)
from remarkable.common.constants import FundRaisingRuleEnum
from remarkable.common.util import compact_dumps, fail2mm_sync, mm_notify
from remarkable.config import get_config
from remarkable.converter import class_bakery
from remarkable.db import pw_db
from remarkable.models.addition_data import AdditionData
from remarkable.models.hkex_file_meta import HKEXFileMeta
from remarkable.models.hkex_stock import HKEXStock
from remarkable.models.mold import special_mold
from remarkable.models.new_file import NewFile
from remarkable.models.new_mold import NewMold
from remarkable.models.new_question import NewQuestion
from remarkable.pdfinsight.reader import Pd<PERSON><PERSON><PERSON>eader
from remarkable.plugins.fileapi.predict import SCHEMA_ENUMERATE_TYPE
from remarkable.plugins.hkex.utils import <PERSON>ld<PERSON>nswer
from remarkable.rule.utils import DisclosureMeta
from remarkable.services.agm import DirectorCVMatcher
from remarkable.services.director import B99AdditionData, find_ined_director, find_new_director_for_b99
from remarkable.services.question import ctx_only_jura21, replace_answer_item

data_dir = get_config("web.data_dir")
logger = logging.getLogger(__name__)
# addition_data表中，某一列的值为以下，则认为为空
ADDITION_NO_VALUE = {"", "not mentioned"}


class Inspector:
    def __init__(self, rules, mold):
        """
        rules: {
            "schema1_name": [Rul1, Rule2],
            "default": [Rul1],
        }
        """
        self.rules = rules
        self.mold = mold
        self.root_schema_name = mold.data["schemas"][0]["name"]
        self.schema_dict = {schema["name"]: schema for schema in mold.data["schemas"]}

    def sort_by(self, item):
        raise NotImplementedError()

    @fail2mm_sync(tags=("inspect_rule",))
    def gen_rule_result(
        self, file: NewFile, question: NewQuestion, answer_type: str, metadata=None, special_rules=None
    ):
        def build_schema(schema_info):
            data = {
                "type": schema_info.get("type"),
                "label": schema_info.get("name"),
                "words": schema_info.get("words", ""),
                "multi": schema_info.get("multi"),
                "required": schema_info.get("required"),
            }
            if data["label"] == self.root_schema_name:  # 根结点没有这两项
                del data["multi"]
                del data["required"]
            return {"data": data}

        def build_col(schema_info, parent_path, index_l):
            schema = build_schema(schema_info)
            path_l = deepcopy(parent_path)
            path_l.append(schema_info["name"])
            col = {
                "schema": schema,
                "score": -1,
                "data": [],
                "key": compact_dumps([f"{path}:{idx}" for path, idx in zip(path_l, index_l)]),
            }
            return col

        pdfinsight = None
        if pdfinsight_path := file.pdfinsight_path(abs_path=True):
            pdfinsight = PdfinsightReader(pdfinsight_path, pdf_contents=file.pdf_contents)
        check_methods = (
            self.rules[self.root_schema_name] if self.root_schema_name in self.rules else self.rules.get("default", [])
        )
        # 根据 special_rules 过滤 Rule.name 匹配的Rule
        if special_rules:
            check_methods = [c for c in check_methods if set(c.name).intersection(set(special_rules))]
        if answer_type == "label":
            mold_answer = MoldAnswer(question.answer)
        else:
            mold_answer = MoldAnswer(question.preset_answer)
        rule_results = []
        used_later_answer = {}
        metadata = metadata or {}
        metadata = {**metadata, **self.group_re_elect_directors(metadata.get("director_info"), pdfinsight)}
        for check_method in check_methods:
            # 删除参数use_addition_data https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/5531#note_662338
            results = check_method.get_results(
                question, mold_answer, pdfinsight, used_later_answer, metadata=metadata, special_rules=special_rules
            )
            for checked_result in results:
                rule_name = checked_result.rule_name
                col_attributes = deepcopy(self.schema_dict[self.root_schema_name]["schema"][rule_name])
                col_attributes.update({"name": rule_name})
                rule_result = build_col(col_attributes, [self.root_schema_name], index_l=("0", "0"))
                rule_result["data"] = []
                rule_result["value"] = checked_result.result
                rule_result["misc"] = {}
                # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/5531#note_662338
                # B1-B10旧版数据需要addition_data（存于question表），重新跑的B1-B10不再需要这部分数据
                # B99 合规需要 new_director 相关数据
                rule_result["addition_data"] = checked_result.addition_data
                rule_results.append(rule_result)
        return rule_results

    @staticmethod
    def group_re_elect_directors(director_info: dict, pdfinsight) -> dict:
        if not (directors := (director_info or {}).get("directors")):
            return {"re_elect_directors": []}
        director_matcher = DirectorCVMatcher(pdfinsight)
        director_matcher.extract()
        director_matcher.filter_elements_data_by_director(directors)
        return {"re_elect_directors": director_matcher.filter_director_data}


def get_crude_key(second_rule, schema):
    crude_key = second_rule
    for schema_key, schema_info in schema[0]["schema"].items():
        if schema_key == second_rule and schema_info.get("type") not in SCHEMA_ENUMERATE_TYPE:
            for item in schema:
                if item["name"] == schema_info.get("type"):
                    crude_key = "-".join((schema_key, item["orders"][0]))
                    break
            break
    return crude_key


class StockInfo:
    def __init__(self, question, share_info: dict | None = None):
        self.file_meta = self.get_file_meta(question.fid)
        self.stock = self.file_meta.stock_code
        self.report_year = self.file_meta.report_year
        self.financial_year = get_financial_year(self.file_meta.fid, self.file_meta.year_end, "%d %b %Y")
        self.have_share_option = self.stock in HAS_SHARE_OPTION_STOCKS.get(self.report_year, has_share_options_2018)
        self.is_belong_bank = self.stock in BELONG_BANK_STOCKS
        self.is_belong_bank_insurance = self.stock in BELONG_INSURANCE_STOCKS + BELONG_BANK_STOCKS
        self.issue_place = self.get_issue_place(self.stock)
        # 删除参数use_addition_data https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/5531#note_662338
        # self.use_addition_data = use_addition_data
        self.share_info = share_info or {}

    @cached_property
    def financial_start_end(self):
        if not self.file_meta or not self.file_meta.year_end:
            return None, None
        return get_financial_year_dates(self.file_meta.fid, self.file_meta.year_end, "%d %b %Y")

    @cached_property
    def addition_data(self) -> dict[str, AdditionData] | None:
        result = {}
        year_start, year_end = self.financial_year
        for row in AdditionData.sync_find_by_stock_code(self.stock):
            if row.event_id in result:
                continue
            # B1-B7 仅需要当年内完成发行的数据
            if not row.is_completed_in_the_year(year_start, year_end):
                continue
            result[row.event_id] = row
        return result

    @staticmethod
    def no_addition_or_lapsed_is_yes(addition_row: AdditionData | None, column_name: str):
        def format_value(value):
            return "" if value is None else value.strip().lower()

        if not addition_row or addition_row.lapsed.lower() == "yes":
            return True
        rule_value = getattr(addition_row, column_name.lower())
        # B3为int
        if column_name == FundRaisingRuleEnum.B3.value:
            return not rule_value
        rule_value = format_value(rule_value)
        if rule_value in ADDITION_NO_VALUE:
            if column_name == FundRaisingRuleEnum.B4.value:
                rule_value = addition_row.exercise_price
            if column_name == FundRaisingRuleEnum.B5.value:
                rule_value = addition_row.fund_raised
            rule_value = format_value(rule_value)
        # https://jura-uat.paodingai.com/#/hkex/annual-report-checking/report-review/258003?fileId=88995&schemaId=5&rule=B1&delist=0
        return rule_value in ADDITION_NO_VALUE

    @staticmethod
    def get_file_meta(fid):
        """
        是否有股份期权
        """
        return HKEXFileMeta.sync_find_by_fid(fid, include_deleted=True)

    @staticmethod
    def get_issue_place(stock_code):
        """
        返回股票的发行地
        """
        hkex_stock = HKEXStock.get_issue_from_stock(stock_code)
        if not hkex_stock:
            return None
        return hkex_stock.issue_place


class CheckedResult(BaseModel):
    rule_name: str
    result: Literal["compliance", "potential non-compliance"]
    addition_data: dict[Literal["new_directors"], Any] | None = None


class Rule:
    def __init__(self, name):
        self.name = name
        self.metadata = None

    def check(self, rule_name, question, mold_answer, pdfinsight, used_later_answer, stock_info):
        """检查答案是否合规"""
        raise NotImplementedError()

    @classmethod
    def get_third_result_by_number(
        cls, question: NewQuestion, mold_answer: MoldAnswer, rule_name: str, number: int = 1
    ) -> str:
        # 返回 rule_name 预测答案, number：子项序号
        disclosure_meta = DisclosureMeta(question, mold_answer, rule_name)
        third_word = disclosure_meta.third_words[number - 1]
        return disclosure_meta.disclosure_results[third_word]

    def get_results(
        self, question, mold_answer, pdfinsight, used_later_answer, metadata: dict = None, special_rules: list = None
    ) -> List[CheckedResult]:
        from remarkable.predictor.utils import get_crude_elements, get_share_info

        self.metadata = metadata or {}
        results = []
        crude_elements = {
            share_type: get_crude_elements(question, pdfinsight, share_type) for share_type in ("option", "award")
        }
        share_info = get_share_info(pdfinsight, crude_elements)
        # 删除参数use_addition_data https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/5531#note_662338
        stock_info = StockInfo(question, share_info)
        for rule_name in self.name:
            if special_rules and rule_name not in special_rules:
                continue
            try:
                result = self.check(rule_name, question, mold_answer, pdfinsight, used_later_answer, stock_info)
            except:  # noqa
                args_str = (
                    f"mid={question.mold}, fid={question.fid}, qid={question.id}, rule={rule_name}, "
                    f"stock_code={stock_info.stock}, \n{used_later_answer=}, \n{metadata=}"
                )
                logger.error(f"{self.__class__.__name__}.check() error: {args_str}\n{traceback.format_exc()}")
                msg = (
                    f"- FUNCTION: `{self.__class__.__name__}.check()`\n"
                    f"- ARGS:\n```shell\n{args_str}\n```\n"
                    f"- TRACEBACK:\n```shell\n{traceback.format_exc()}\n```\n"
                )
                mm_notify(msg, error=True)
                continue
            if not result:
                logger.info(f"inspect result not exist, rule: {rule_name}, file: {question.fid}")
                continue
            addition_data = None
            if rule_name == "B99" and result == "potential non-compliance":
                new_directors: B99AdditionData = self.metadata.get("new_directors")
                addition_data = {"new_directors": new_directors.model_dump()}
            results.append(CheckedResult(rule_name=rule_name, result=result, addition_data=addition_data))
            logger.debug(f"check rule for {rule_name}, {result}")
        return results


class AnswerInspectorFactory:
    CLASSNAME_OVER_CONFIG = None
    CLASS = None

    @classmethod
    def get_class_fullname(cls, schema_name):
        _config = cls.CLASSNAME_OVER_CONFIG or get_config("web.classes.answer_inspector")
        if isinstance(_config, dict):
            return _config.get(schema_name)
        return _config

    @classmethod
    def get_class(cls, schema_name):
        if not cls.CLASS:
            class_fullname = cls.get_class_fullname(schema_name)
            if not class_fullname:
                return None
            last_dot_index = class_fullname.rindex(".")
            mod_name, clazz_name = class_fullname[:last_dot_index], class_fullname[last_dot_index + 1 :]
            module = importlib.import_module(mod_name)
            clazz = getattr(module, clazz_name)
            cls.CLASS = clazz
        return cls.CLASS

    @classmethod
    def create(cls, mold, *args, **kwargs):
        clazz = cls.get_class(mold.name)
        if not clazz:
            return None
        return clazz(mold, *args, **kwargs)


async def inspect_rules(file, question, answer_type: Literal["preset", "label"] = "preset", special_rules=None):
    answer = question.preset_answer if answer_type == "preset" else question.answer
    if question.mold:
        mold = await NewMold.find_by_id(question.mold)
        if mold.id in (special_mold.v3r_id, special_mold.v6_1_poll_id):  # HARDCODE
            clazz = class_bakery.get_class(mold.name, class_bakery)
            if not clazz:
                return logger.warning(f'No class found for schema: {mold.id} "{mold.name}"')
            return await asyncio.get_event_loop().create_task(clazz.run_pipe(question, mold))

        if not answer:
            return None
        inspector = AnswerInspectorFactory.create(mold, only_jura21=ctx_only_jura21.get())
        if not inspector:
            logger.info(f"No inspector found for mold: {mold.id} {mold.name}")
            return None
        metadata = {"director_info": {}, "directors": []}
        if mold.id == special_mold.v6_agm_id and (file_meta := StockInfo.get_file_meta(file.id)):
            director_info = await find_ined_director(file.id, stock_code=file_meta.stock_code)
            metadata = {"director_info": director_info}
        if mold.id == special_mold.v2_id and (file_meta := StockInfo.get_file_meta(file.id)):
            new_directors = await find_new_director_for_b99(file_meta)
            metadata["new_directors"] = new_directors
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/5531#note_662338
        # rule_results = inspector.gen_rule_result(file, question, answer_type, use_addition_data, metadata)
        rule_results = inspector.gen_rule_result(file, question, answer_type, metadata, special_rules=special_rules)
        rule_results.sort(key=inspector.sort_by)
        if question.mold == special_mold.v1_id:
            # NOTE: JURA2.1 Rule A 特殊规则
            # 因为已经有客户的标注标注答案保存到 preset_answer.userAnswer 中
            # 这里将rule_result的答案转换成 preset_answer.userAnswer 的格式 更新到 preset_answer.userAnswer
            new_preset_answer = rule_result_to_preset_answer(rule_results)
            origin_preset_answer = question.preset_answer
            if special_rules:
                special_a_cols = [f"({i})" for i in special_rules if i in JURA21_A_COLS]
            else:
                special_a_cols = [f"({i})" for i in JURA21_A_COLS]
            question.preset_answer = replace_answer_item(origin_preset_answer, new_preset_answer, special_a_cols)
        else:
            # 其余schema的合规答案则都应该保存到 rule_result 中
            if not hasattr(question, "preset_answer") or not question.preset_answer:
                question.preset_answer = {"rule_result": {"items": rule_results}}
            else:
                new_rule_results = replace_rule_result(question.preset_answer.get("rule_result", {}), rule_results)
                new_rule_results.sort(key=inspector.sort_by)
                question.preset_answer["rule_result"] = {"items": new_rule_results}
        await pw_db.update(question)
        # # 同步合规结果到 xx_result表（例如：ar_result）
        # # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6468#note_678645
        # await sync_answer_result(question.id)
    logger.info(f"run_inspect_rule for file: {file.id}, question: {question.id}, mold: {question.mold} done!")

    return None


def replace_rule_result(origin_answer: Dict[str, dict], new_answer: List[dict]) -> List[dict]:
    if not origin_answer.get("items"):
        return new_answer
    new_answer_cols = []
    for item in new_answer:
        answer_item = AnswerItem(**item)
        new_answer_cols.append(answer_item.first_word)
    logger.info(f"replace_rule_result, {len(new_answer_cols)=}")

    need_remain_items = []
    for origin_item in origin_answer["items"]:
        answer_item = AnswerItem(**origin_item)
        if answer_item.first_word not in new_answer_cols:
            need_remain_items.append(origin_item)
    return need_remain_items + new_answer


def rule_result_to_preset_answer(rule_results):
    res = []
    for rule_result in rule_results:
        schema = rule_result["schema"]
        rule = schema["data"]["label"]
        schema["data"]["label"] = f"({rule})"
        item = {
            "schema": schema,
            "score": -1,
            "data": [],
            "key": compact_dumps(["LRs:0", f"({rule}):0"]),
            "value": rule_result["value"],
        }
        res.append(item)
    return {"userAnswer": {"items": res}}
