import decimal
import logging
import re
from collections import defaultdict
from copy import deepcopy
from difflib import Se<PERSON><PERSON><PERSON><PERSON>

from remarkable.common.common import get_first_key, get_keys
from remarkable.common.common_pattern import R_CURRENCY
from remarkable.common.constants import COMPLIANC<PERSON>, NON_COMPLIANCE, FundRaisingRuleEnum
from remarkable.common.pattern import <PERSON><PERSON><PERSON><PERSON>, PatternCollection
from remarkable.common.util import P_BLANKS, P_CHNS, P_WHITE, clean_txt, split_paragraph
from remarkable.config import get_config, project_root
from remarkable.models.addition_data import AdditionData
from remarkable.models.mold import special_mold
from remarkable.pdfinsight.reader_util import P_CHINESE
from remarkable.predictor.common_pattern import P_EN_MONTH
from remarkable.predictor.hkex_predictor.schemas.list_rule_schema.list_rule_b6 import P_B6_NOT_LESS_THAN_SIX
from remarkable.predictor.hkex_predictor.schemas.pattern import R_APPELLATION, gen_regex_by_num, gen_regex_by_price
from remarkable.rule.inspector import ADDITION_NO_VALUE, Inspector, Rule, StockInfo
from remarkable.rule.jura_21_b.b98_b99 import B98Rule, B99Rule
from remarkable.rule.utils import (
    DisclosureMeta,
    MultiGroupDisclosureMeta,
    find_subclasses,
    get_disclosure_content,
    get_disclosure_value,
)

logger = logging.getLogger(__name__)

NS = "Negative Statement"
PS = "Positive Statement"
ND = "No Disclosure"

director = r"DIRECTORS?"
executive_director = r"Executive\s*Directors?:?"
management_contract = r"MANAGEMENT\s*CONTRACTS?"
SIX_TEXT = r"(six |\(?6\)? ){1,2}"
B6_REG_TEXT = rf"not? (less|fewer) than {SIX_TEXT}(independent )?(placees?|professional)|at (less|fewer) {SIX_TEXT}|independent third part(y|ies)"
R_PRICE_NUM = r"(([1-9][\d,]*|0)\.\d+|[1-9][0-9]{0,2}(,[0-9]{3}){0,5}|\d{3,})"

PATTERNS = {
    FundRaisingRuleEnum.B1: re.compile(r"The\s?Directors.*?(raise\s?(funds|capital)|Rights\s?Issue).*"),
    "Issue reason": re.compile(
        r"The Directors.*?Placing.*|Placing|gross fund raised|"
        r"Acquisition|replenish|Placd|purpose|the\s?placement\s?of\s?shares|"
        r"fund\s?raising|maintain\s?adequate\s?funds|convertible\s?bonds|"
        r"Subscription\s?Shares|financing\s?working\s?capital|"
        r"strengthen\s?the\s?financial|the\s?warrants\s?could\s?be\s?issued",
        re.X | re.I,
    ),
    FundRaisingRuleEnum.B6: re.compile(B6_REG_TEXT, re.I),
    "B35": re.compile(r"(?P<dst>\d+\.?\d+)(%|per)?"),
    "B50": re.compile(r"(?P<dst>\d+\.?\d+)(%|per)?"),
    "B22": [re.compile(r"\$?\s?(RMB)?(?P<dst>[\d,\.]*)(?<![,\.])")],
    "B14": re.compile(r"hk|Hong\s*Kong", re.I),
    "B17": [
        MatchMulti.compile(
            r"bonus(es)?|(pension\s*or\s*contribution)", r"wages?|salar(y|ies)|all?owances?|benefit", operator=all
        ),
    ],
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/1140#note_260613
    # 相当于B17，NC判定规则更加严格
    "B18": [
        MatchMulti.compile(
            r"bonus(es)?|(pension\s*or\s*contribution)", r"wages?|salar(y|ies)|all?owances?|benefit", operator=all
        ),
    ],
    "B19": [
        re.compile(r"bonus(es)?\s*(&|and)\s*Pension\s*or\s*contribution", re.X | re.I),
        re.compile(r"salar(y|ies).*?bonus(es)?", re.X | re.I),
    ],
    "B37": [
        re.compile(
            rf"^{director}$|^{director}\s*AND\s*{director}’\s*SERVICE\s*CONTRACTS$|{executive_director}|"
            rf"details\s*of\s*the\s*Directors\s*of\s*the\s*Company\s*were\s*as\s*follows",
            re.X | re.I,
        )
    ],
    "B38": [
        re.compile(r"Business\s*?Review", re.I),
    ],
    "B41": [re.compile(rf"^PERMITTED\s*INDEMNITY(\s*PROVISIONS?$)?|{management_contract}", re.X | re.I)],
    "B42": [re.compile(rf"{management_contract}", re.X | re.I)],
    "B43": {
        "Management contract": [
            re.compile(
                rf"^DIRECTORS$|^DIRECTORS\s*AND\s*DIRECTORS’\s*SERVICE\s*CONTRACTS$|"
                rf"^Permitted\s*indemnity\s*provisions$|{management_contract}|{executive_director}",
                re.X | re.I,
            )
        ],
        "Director list": [re.compile(rf"{management_contract}|{executive_director}|{director}", re.X | re.I)],
    },
    "B44": [
        re.compile(
            rf"^(PRINCIPAL\s*ACTIVITIES\s*AND\s*)?BUSINESS?\s*REVIEW(\s*AND\s*PERFORMANCE)?$|{management_contract}|"
            rf"During\s*the\s*reporting\s*period.*quality\s*management.*",
            re.X | re.I,
        )
    ],
    "syllabus": [
        re.compile(r"""(?=.*(director'?s?|board))(?=.*(report(ing|er)?|statement))""", re.I),
        # NOTE: 特例 file ID 5835 5769 6216
        re.compile(r"""(BASIC\s?)?Information\s?on\s?(THE\s?)?Directors""", re.I),
        re.compile(r"""BIOGRAPHICAL\s?DETAILS\s?OF\s?DIRECTORS""", re.I),
    ],
    "B63": re.compile(r"granted|exercised|lapsed|cancelled"),
}

schema_additional_map = {
    "Issue reason": "Reasons",
    "Class of equity securities": "Class of equity",
    "Number of issued": "Number issued",
    "Issue price": "Issued price",
    "Net price": "Net price",
    "Names of allottes": "Name of allotters",
    "Market price": "Market price",
    "Detailed breakdown and description": "Process of subscription",
}

R_LINE_BREAK = r"([\r\n\t]+|\.\s+|\s*[(（](\w|[IVX]{1,4})[）)]\s*)"
P_LINE_BREAK = re.compile(R_LINE_BREAK, re.I)


def get_event_id(disclosure_meta):
    return get_keys(disclosure_meta, ["meta", "event_id"])


def get_addition_row(disclosure_meta: dict, addition_data: dict[str, AdditionData]) -> AdditionData | None:
    if event_id := get_event_id(disclosure_meta):
        return addition_data.get(event_id)
    return None


def is_ipo(disclosure_meta: dict) -> bool:
    """
    判断当前分组是否为IPO
    """
    from remarkable.predictor.models.fund_raising_group import FundRaisingGroup

    return (
        get_event_id(disclosure_meta) == FundRaisingGroup.DEFAULT_EVENT_ID_MAP[FundRaisingGroup.DEFAULT_IPO_EVENT_TYPE]
    )


def is_b1_b10_nd_c(disclosure_meta: dict):
    """
    特殊描述（满足： P_B1_B10_MUST_ND）的分组不影响合规
    """
    return get_keys(disclosure_meta, ["meta", "nd_c"])


def is_compliance_ipo(disclosure_result: str) -> bool:
    """
    IPO没有gekko数据，所以披露即合规
    https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6234#note_661466
    """
    return disclosure_result != ND


class B1Rule(Rule):
    """
    使用gekko数据
        NS/ND 且 EXCEL 中未检索到数据 —— C
        NS/ND + EXCEL column `lapsed` 所有相关 fundraising 为 YES —— C
        PS + EXCEL 中检索到的内容与年报内容比对分析且设定阈值一致 —— C
        其他情况 NC

    没有gekko数据
        ND —— NC
        其他情况C
    """

    # 常见reason
    P_COMMON_REASONS = [
        re.compile(r"invest", re.I),  # 投资
        re.compile(r"general\s*working|working\s*capital", re.I),
        re.compile(r"repayment", re.I),
        re.compile(r"redemption", re.I),
        re.compile(r"\b(in)?debt\b", re.I),
        re.compile(r"rais(e|ing)\s*additional\s*(capital|funds)|financing", re.I),  # 融资
    ]
    P_PARA_SEPARATOR = re.compile(
        rf"[;；,，]\s|{R_LINE_BREAK}|"  # 分号或逗号或标题序号
        r"(?<!\bMadam)(?<!\b(Prof|Miss))(?<!\b(Mr|Ms|Dr|Co|no))(?<!\bLtd)\.(\s|$)|"  # 句号
        r"\s+(for|to|and|as|or)\s+(ensure|support|enhance|increase|enlarge)?",  # 关键词
        re.I,
    )
    P_IGNORE_SUB_SENTENCE = PatternCollection(
        [
            r"issued|issue\s*of\b|placing|approximately",
            r"entered\s*into",
        ],
        flags=re.I,
    )
    P_IGNORE_WORDS = re.compile(
        r"\b(of\s*)?(the\s*|its\s*)?(group|company)(['’‘]s)?\b|^\s*[a-z]{1,3}\s+|\s+[a-z]{1,3}\s*$", re.I
    )

    def __init__(self):
        super().__init__([FundRaisingRuleEnum.B1.value])

    def check(self, rule_name, question, mold_answer, pdfinsight, used_later_answer, stock_info: StockInfo):
        multi_group_disclosure = MultiGroupDisclosureMeta(question, mold_answer, rule_name)
        third_word = multi_group_disclosure.third_word
        for disclosure_meta in multi_group_disclosure.disclosure_metas[third_word]:
            disclosure_result = get_disclosure_value(disclosure_meta)
            disclosure_contents = get_disclosure_content(disclosure_meta)
            if is_ipo(disclosure_meta):
                if is_compliance_ipo(disclosure_result):
                    continue
                return NON_COMPLIANCE
            if is_b1_b10_nd_c(disclosure_meta):
                continue
            addition_row = get_addition_row(disclosure_meta, stock_info.addition_data)
            if disclosure_result in {NS, ND} and stock_info.no_addition_or_lapsed_is_yes(addition_row, rule_name):
                continue
            if disclosure_result == PS and self.check_b1_ps_result(disclosure_contents, addition_row):
                continue
            # 任意一组答案与外部Excel不符，则不合规
            return NON_COMPLIANCE
        return COMPLIANCE

    @staticmethod
    def diff_ratio(sentence1, sentence2):
        """
        计算两个句子之间相似度
        注意：这里的分母用两者中较短的那个长度
        """
        if not sentence1 or not sentence2:
            return 0.0
        matcher = SequenceMatcher(None, sentence1, sentence2)
        return sum(block[-1] for block in matcher.get_matching_blocks()) / min(len(sentence1), len(sentence2))

    @classmethod
    def check_b1_ps_result(cls, disclosure_contents: list[str], addition_row: AdditionData):
        """
        提取B1答案与gekko外部数据做比较
        TODO https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/5322#note_589122
        """
        # 没有外部数据，披露即合规
        if not addition_row:
            return True
        # not mentioned: http://************:55647/#/hkex/annual-report-checking/report-review/261757?fileId=69566&schemaId=5&rule=B1&delist=0
        if not disclosure_contents or not addition_row.b1 or addition_row.b1.lower() == "not mentioned":
            return False
        addition_content = addition_row.b1.lower()
        disclosure_content = P_CHINESE.sub("", " ".join(d.strip() for d in disclosure_contents))
        if not disclosure_content:
            return False
        if cls.is_same_reason(disclosure_content, addition_content):
            return True
        for addition in split_paragraph(addition_content, P_LINE_BREAK, need_separator=False):
            if not addition or len(addition) < 10 or len(addition.split()) < 2:
                continue
            # 1. 相似度>=20%，注意：这里的分母用两者中较短的那个长度
            if cls.diff_ratio(addition, disclosure_content) >= 0.2:
                return True
        # 2. 根据关键词切割发行目的，并保证切割后的目的存在于外部excel中
        if reasons := cls.split_reasons(disclosure_content):
            if (
                sum(reason in addition_content or cls.diff_ratio(reason, addition_content) >= 0.2 for reason in reasons)
                / len(reasons)
                >= 0.2
            ):
                return True
        return False

    @classmethod
    def is_same_reason(cls, disclosure_content, addition_content):
        """
        遍历已知reason，两者都同时包含则认为合规
        TODO https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/5467#note_605906
        """
        is_same = False
        for p_reason in cls.P_COMMON_REASONS:
            matched_disclosure = bool(p_reason.search(disclosure_content))
            matched_addition = bool(p_reason.search(addition_content))
            if matched_disclosure != matched_addition:
                return False
            if matched_disclosure:
                is_same = True
        return is_same

    @classmethod
    def split_reasons(cls, paragraph) -> set[str]:
        results = set()
        for reason in split_paragraph(paragraph, cls.P_PARA_SEPARATOR, need_separator=False):
            reason = cls.P_IGNORE_WORDS.sub("", reason.lower()).strip()
            if not reason or len(reason.split()) < 2:
                continue
            if cls.P_IGNORE_SUB_SENTENCE.nexts(reason):
                continue
            # TODO 尝试找动词 expand/promote/enhance/increase/repay/enlarge/reduce/decrease/decline/reduction
            #  /decrease/expansion/strengthen/promote/advance/reinforce/consolidate
            results.add(reason)
        return results


class B2Rule(Rule):
    """
    B2合规于2025.6月变更： https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7352
    合规规则：
      - 存在gekko数据且lapsed=No时，各种Event Type的合规逻辑均为ND-NC，其余情况C
      - 不存在gekko数据时，ND-C
    https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7352#note_751194
    """

    def __init__(self):
        super().__init__([FundRaisingRuleEnum.B2.value])

    def check(self, rule_name, question, mold_answer, pdfinsight, used_later_answer, stock_info):
        multi_group_disclosure = MultiGroupDisclosureMeta(question, mold_answer, rule_name)
        third_word = multi_group_disclosure.third_word
        for disclosure_meta in multi_group_disclosure.disclosure_metas[third_word]:
            disclosure_result = get_disclosure_value(disclosure_meta)
            if is_ipo(disclosure_meta):
                # IPO场景下，未披露即不合规
                if is_compliance_ipo(disclosure_result):
                    continue
                return NON_COMPLIANCE
            if is_b1_b10_nd_c(disclosure_meta):
                continue
            # 使用gekko数据
            addition_row = get_addition_row(disclosure_meta, stock_info.addition_data)
            if disclosure_result == ND:
                # 没有gekko数据或gekko数据中lapsed为yes，认为合规
                if not addition_row or addition_row.lapsed.lower() == "yes":
                    continue
                # 未披露即不合规
                return NON_COMPLIANCE
        return COMPLIANCE


class B3ToB10Rule(Rule):
    """
    B8-B10合规说明： https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/5744
    B2合规于2025.6月变更： https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7352
    使用gekko数据
        NS/ND：EXCEL中未检索到数据 —— C
        PS:
            addition_data中无对应记录或规则为B2/B8/B9/B10  —— C
            B2: 当年存在 issue of warrants 时，ND-NC; 其余情况C; 当年不存在issue of warrants 时，默认合规
            B3: 提取到的值与addition_data.b3完全一致 —— C
            B4: 提取到的值与addition_data.b4或addition_data.exercise_price一致 —— C
            B5: 提取到的值与addition_data.b7或addition_data.fund_raised一致 —— C
            B7: 提取到的值与addition_data.b7完全一致 —— C
        其他情况 NC
    """

    P_PRICE = re.compile(
        rf"(^|\s)(?P<unit>{R_CURRENCY})\s*(?P<price>{R_PRICE_NUM})\s*((?P<million>million)|$|[^\w.])", re.I
    )
    P_AMOUNT = re.compile(rf"(^|\s)(?P<amount>{R_PRICE_NUM})\s*((?P<million>million)|$|[^\w.])", re.I)

    def __init__(self):
        super().__init__(
            [
                FundRaisingRuleEnum.B3.value,
                FundRaisingRuleEnum.B4.value,
                FundRaisingRuleEnum.B5.value,
                FundRaisingRuleEnum.B7.value,
                FundRaisingRuleEnum.B8.value,
                FundRaisingRuleEnum.B9.value,
                FundRaisingRuleEnum.B10.value,
            ]
        )
        self.threshold = 0.5

    def check(self, rule_name, question, mold_answer, pdfinsight, used_later_answer, stock_info):
        multi_group_disclosure = MultiGroupDisclosureMeta(question, mold_answer, rule_name)
        third_word = multi_group_disclosure.third_word
        for disclosure_meta in multi_group_disclosure.disclosure_metas[third_word]:
            disclosure_result = get_disclosure_value(disclosure_meta)
            disclosure_contents = get_disclosure_content(disclosure_meta)
            if is_ipo(disclosure_meta):
                # IPO场景下，B6/B7直接合规 https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6240#note_665112
                if rule_name == FundRaisingRuleEnum.B7.value or is_compliance_ipo(disclosure_result):
                    continue
                return NON_COMPLIANCE
            if is_b1_b10_nd_c(disclosure_meta):
                continue
            # 使用gekko数据
            addition_row = get_addition_row(disclosure_meta, stock_info.addition_data)
            if disclosure_result in {NS, ND} and stock_info.no_addition_or_lapsed_is_yes(addition_row, rule_name):
                continue
            if disclosure_result == PS and self.check_b2_to_b10_ps_result(disclosure_contents, addition_row, rule_name):
                continue
            # B8-B10 有外部数据的情况下，披露即合规
            if disclosure_result == NS and rule_name in {
                FundRaisingRuleEnum.B8,
                FundRaisingRuleEnum.B9,
                FundRaisingRuleEnum.B10,
            }:
                continue
            return NON_COMPLIANCE
        return COMPLIANCE

    @classmethod
    def extract_amount(cls, disclosure_contents: list[str]) -> set[float]:
        result = set()
        for content in disclosure_contents:
            if P_CHNS.search(content):
                # 不处理包含中文的段落
                continue
            content = clean_txt(content)
            total_amount = 0
            for p_amount in cls.P_AMOUNT.finditer(content):
                amount, million = float(p_amount.group("amount").replace(",", "")), p_amount.group("million")
                # 找到数字前的两个单词，任意一个为月份则跳过
                if any(P_EN_MONTH.search(word) for word in content[: p_amount.start()].split()[-2:]):
                    continue
                # 找到数字后的单词，为月份则跳过
                if (suffix := content[p_amount.end() :]) and P_EN_MONTH.search(suffix.split(maxsplit=1)[0]):
                    continue
                if million:
                    total_amount += amount * 1000000
                total_amount += amount
            result.add(round(total_amount, 2))
        return result

    @classmethod
    def extract_total_price(cls, disclosure_contents: list[str]) -> set[tuple[str, float]]:
        result = set()
        for content in disclosure_contents:
            if P_CHNS.search(content):
                # 不处理包含中文的段落
                continue
            content = clean_txt(content)
            total_price = defaultdict(float)
            for p_price in cls.P_PRICE.finditer(content):
                # 不要每股价格
                if (suffix := content[p_price.end() :]) and suffix.split(maxsplit=1) == "per":
                    continue
                price = float(p_price.group("price").replace(",", ""))
                unit, million = p_price.group("unit")[:2], p_price.group("million")
                if million:
                    price *= 1000000
                elif price < 10:
                    # 忽略小于10元的价格
                    continue
                total_price[unit] += price
            for unit, price in total_price.items():
                result.add((unit, round(price, 2)))
        return result

    @classmethod
    def check_b2_to_b10_ps_result(
        cls, disclosure_contents: list[str], addition_row: AdditionData | str | None, rule_name: str
    ):
        if not addition_row or rule_name in {
            FundRaisingRuleEnum.B2,
            FundRaisingRuleEnum.B8,
            FundRaisingRuleEnum.B9,
            FundRaisingRuleEnum.B10,
        }:
            # 没有外部数据或者规则为B2/B8/B9/B10，披露即合规
            return True
        # 提取B3/B4/B5/B7答案与gekko外部数据做比较
        if isinstance(addition_row, AdditionData):
            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6238#note_665115
            # 取消event type为CB的判定，没有gekko没有B4时，就用exercise price来判定B4是否合规
            if rule_name == FundRaisingRuleEnum.B4:
                # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/956#note_621042
                addition_content = addition_row.b4 or addition_row.exercise_price
            else:
                addition_content = getattr(addition_row, rule_name.lower())
        else:
            addition_content = addition_row
        if not addition_content:
            return False
        addition_content = str(addition_content)
        disclosure_content = " ".join(disclosure_contents).lower().strip().replace("\n", "\t")
        # B3为数量，B4/B5/B7都是钱数
        if addition_content.lower() in disclosure_content:
            return True
        if rule_name == FundRaisingRuleEnum.B3 and addition_content.isdigit():
            p_b3 = gen_regex_by_num(addition_content)
            if p_b3.search(disclosure_content):
                return True
            # 同一个段落中的数量求和做对比
            if expected_set := cls.extract_amount([addition_content]):
                expect_b3 = get_first_key(expected_set)
                b3_values = cls.extract_amount(disclosure_contents)
                b3_values.add(sum(b3_values))
                if any(expect_b3 == v for v in b3_values):
                    return True
            return False
        if rule_name in {FundRaisingRuleEnum.B4, FundRaisingRuleEnum.B7}:
            p_price = gen_regex_by_price(addition_content)
            return bool(p_price.search(disclosure_content))
        if rule_name == FundRaisingRuleEnum.B5:
            p_price = gen_regex_by_price(addition_content)
            if p_price.search(disclosure_content):
                return True
            # B5也可以用总净值做判断
            p_fund = gen_regex_by_price(addition_row.fund_raised)
            if p_fund.search(disclosure_content):
                return True
            # 同一个段落中的总净值数量求和做对比
            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6414#note_673366
            if expected_set := cls.extract_total_price([addition_row.fund_raised]):
                unit, expected_b5 = get_first_key(expected_set)
                b5_values = cls.extract_total_price(disclosure_contents)
                # 分币种 加总
                group_b5_values = defaultdict(list)
                for unit, price in b5_values:
                    group_b5_values[unit].append(price)
                sum_by_unit = {(k, round(sum(v), 2)) for k, v in group_b5_values.items()}
                b5_values.update(sum_by_unit)
                return any((unit, round(expected_b5, 2)) == v for v in b5_values)
        return False


class B6Rule(Rule):
    """
    使用gekko数据
        NS/ND 且 EXCEL中未检索到数据 —— C
        NS/ND＋EXCEL column AT 所有相关 fundraising 为 YES —— C
        EXCEL中 D列（Involved Entities Note）内容 是否包含（not less than six placees/allotees）相关的描述
            3.1  PS + 包含 + Excel中D列与年报内容比对分析且设定阈值一致 —— C
            3.2  PS + 不包含 + 名称与Involved Entities English列一致 —— C
        其他情况 NC

    没有gekko数据
        ND —— NC
        其他情况C
    """

    P_B6_SEPARATOR = re.compile(r"([,，](?!\d)|\s+to\s+|\s+and\s+|\.$)", re.I)
    P_B6_SUBSCRIBER = re.compile(rf"^{R_APPELLATION}.+$|^([A-Z][-.a-zA-Z]*(\s+|$)|[(（][^(（]+[)）])+$")

    def __init__(self):
        super(B6Rule, self).__init__([FundRaisingRuleEnum.B6.value])
        self.threshold = 0.5

    def check(self, rule_name, question, mold_answer, pdfinsight, used_later_answer, stock_info):
        multi_group_disclosure = MultiGroupDisclosureMeta(question, mold_answer, rule_name)
        third_word = multi_group_disclosure.third_word
        for disclosure_meta in multi_group_disclosure.disclosure_metas[third_word]:
            disclosure_result = get_disclosure_value(disclosure_meta)
            disclosure_content = get_disclosure_content(disclosure_meta)
            if is_ipo(disclosure_meta):
                # IPO场景下，B6/B7直接合规 https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6240#note_665112
                if rule_name == FundRaisingRuleEnum.B6.value or is_compliance_ipo(disclosure_result):
                    continue
                return NON_COMPLIANCE
            if is_b1_b10_nd_c(disclosure_meta):
                continue
            # 使用gekko数据
            addition_row = get_addition_row(disclosure_meta, stock_info.addition_data)
            no_addition = (
                not addition_row
                or addition_row.lapsed.lower() == "yes"
                or not (
                    addition_row.b6_first
                    and addition_row.b6_first.strip().lower() not in ADDITION_NO_VALUE
                    or addition_row.b6_second
                    and addition_row.b6_second.strip().lower() not in ADDITION_NO_VALUE
                )
            )
            if disclosure_result in {NS, ND} and no_addition:
                continue
            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6931
            if addition_row.issue_type in {r"open offer", "rights issue"}:
                continue
            if disclosure_result == PS and self.check_b6_ps_result(disclosure_content, addition_row):
                continue
            return NON_COMPLIANCE
        return COMPLIANCE

    @staticmethod
    def match_b6_name(disclosure_content, addition_data):
        lower_content = disclosure_content.lower()
        for subscriber in addition_data.split("\n"):
            # 公司名称/董事名取前三个单词
            if " ".join(subscriber.split()[:3]).lower() in lower_content:
                return True
            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7318#note_733133
            if B1Rule.diff_ratio(subscriber, addition_data) >= 0.5:
                return True
        return False

    @classmethod
    def check_b6_ps_result(cls, disclosure_contents: list[str], addition_row):
        """
        EXCEL中 D列（Involved Entities Note）内容 是否包含（not less than six placees/allotees）相关的描述
          3.1  PS + 包含 + Excel中D列与年报内容比对分析且设定阈值一致 —— C
          3.2  PS + 不包含 + 名称List中任意一个在PS段落中能找到 —— C
         New: April 3, 2024
         如果人数不少于6人：在Involved Entities Note和年报中都必须包含相关描述，例如not less than 6或9 subscribers等
         如果人数少于6人：在年报中必须有具体人名，且这些人名在Involved Entities Note中应该都能找到，如果找不到，就在Involved Entities English中找
        """
        # 没有外部数据，披露即合规
        if not addition_row:
            return True
        if not disclosure_contents:
            return False
        b6_first, b6_second = addition_row.b6_first, addition_row.b6_second
        if not b6_first and not b6_second:
            return False
        disclosure_content = " ".join(disclosure_contents)
        if b6_first:
            # 都包含not less than six
            if P_B6_NOT_LESS_THAN_SIX.search(b6_first):
                return P_B6_NOT_LESS_THAN_SIX.search(disclosure_content)
            if cls.match_b6_name(disclosure_content, b6_first):
                return True
            # 相似度>=0.5
            if B1Rule.diff_ratio(b6_first, disclosure_content) >= 0.5:
                return True
        if b6_second and cls.match_b6_name(disclosure_content, b6_second):
            return True
        if not (names := cls.extract_subscribers(disclosure_content)):
            return False
        b6_first_no_blank = P_WHITE.sub("", b6_first)
        if b6_first_no_blank and all(P_WHITE.sub("", name) in b6_first_no_blank for name in names):
            return True
        if not b6_second:
            return False
        return names == set(P_BLANKS.split(b6_second))

    @classmethod
    def extract_subscribers(cls, disclosure_content):
        names = set()
        for name in cls.P_B6_SEPARATOR.split(disclosure_content):
            name = name.strip() if name else ""
            if name and cls.P_B6_SUBSCRIBER.match(name):
                names.add(name)
        return names


class B11EtcRule(Rule):
    """
    "根据披露结果判断
    PS/NS----C
    ND---NC"
    """

    def __init__(self):
        super(B11EtcRule, self).__init__(["B11", "B13", "B16", "B49", "B54", "B56", "B59"])

    def check(self, rule_name, question, mold_answer, pdfinsight, used_later_answer, stock_info):
        disclosure_meta = DisclosureMeta(question, mold_answer, rule_name)
        third_word = disclosure_meta.third_word
        disclosure_results = disclosure_meta.disclosure_results
        disclosure_result = disclosure_results[third_word]
        result = NON_COMPLIANCE
        if disclosure_result in (NS, PS):
            result = COMPLIANCE
        return result


class B12Rule(Rule):
    """
    根据披露结果判断
    1到4 全部PS/ 1到4全部NS----C
    others---NC
    """

    def __init__(self):
        super(B12Rule, self).__init__(["B12"])

    def check(self, rule_name, question, mold_answer, pdfinsight, used_later_answer, stock_info):
        result = NON_COMPLIANCE
        disclosure_meta = DisclosureMeta(question, mold_answer, rule_name)
        disclosure_results = disclosure_meta.disclosure_results
        if len(set(disclosure_results.values())) == 1 and set(disclosure_results.values()).pop() in (PS, NS):
            result = COMPLIANCE
        return result


class B14Rule(Rule):
    """
    1PS/NS ----C  Pre-emptive right
    1ND +2PS（包含关键词HK、HongKong）= C  Incorporated place
    """

    def __init__(self):
        super(B14Rule, self).__init__(["B14"])

    def check(self, rule_name, question, mold_answer, pdfinsight, used_later_answer, stock_info):
        disclosure_meta = DisclosureMeta(question, mold_answer, rule_name)
        disclosure_results = disclosure_meta.disclosure_results
        disclosure_contents = disclosure_meta.disclosure_contents
        third_words = disclosure_meta.third_words
        pre_emptive_right, incorporated_place = third_words
        pre_emptive_right_result = disclosure_results[pre_emptive_right]
        incorporated_place_result = disclosure_results[incorporated_place]
        result = NON_COMPLIANCE
        if pre_emptive_right_result in (NS, PS):
            result = COMPLIANCE
        elif incorporated_place_result == PS:
            patterns = PATTERNS[rule_name]
            ar_issue_place = disclosure_contents[incorporated_place]
            if any((patterns.search(issue_place) for issue_place in ar_issue_place)):
                result = COMPLIANCE
        return result


class B15Rule(Rule):
    """
    1.15.1 或15.2 为PS -> C; 15.1 或15.2 为NS -> C
    2.如果年报类别属于银行业 - 默认C
    """

    def __init__(self):
        super(B15Rule, self).__init__(["B15"])

    def check(self, rule_name, question, mold_answer, pdfinsight, used_later_answer, stock_info):
        result = NON_COMPLIANCE
        disclosure_meta = DisclosureMeta(question, mold_answer, rule_name)
        disclosure_results = disclosure_meta.disclosure_results
        bank_loans_overdrafts, other_borrowings, _ = disclosure_meta.third_words
        bank_loans_overdrafts_result = disclosure_results[bank_loans_overdrafts]
        other_borrowings_result = disclosure_results[other_borrowings]
        used_later_answer["B15_1"] = bank_loans_overdrafts_result
        used_later_answer["B15_2"] = other_borrowings_result
        belong_bank = stock_info.is_belong_bank
        if belong_bank or any(
            (
                bank_loans_overdrafts_result == PS,
                other_borrowings_result == PS,
                bank_loans_overdrafts_result == NS,
                other_borrowings_result == NS,
            )
        ):
            result = COMPLIANCE
        return result


class B17EtcRule(Rule):
    """
    PS----C
    https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/701#note_188103
    PS + (初步定位的表格中、表头中含有salar(y|ies) & bonus类似的字符) ----NC
    PS + (初步定位的表格中、表头中含有salar(y|ies) & (Pension or contribution)) - NC
    NS----C
    ND---NC
    """

    def __init__(self):
        super(B17EtcRule, self).__init__(["B17", "B18"])

    def check(self, rule_name, question, mold_answer, pdfinsight, used_later_answer, stock_info):
        result = NON_COMPLIANCE
        disclosure_meta = DisclosureMeta(question, mold_answer, rule_name)
        disclosure_results = disclosure_meta.disclosure_results
        third_word = disclosure_meta.third_word
        disclosure_result = disclosure_results[third_word]
        crude_answers = []
        if question.crude_answer:
            if items := question.crude_answer.get(f"{rule_name}-{third_word}"):
                crude_answers = items[:1]
            else:
                logger.warning(f"crude_answer not found for {rule_name}")
        if disclosure_result == NS:
            result = COMPLIANCE
        elif disclosure_result == PS:
            result = COMPLIANCE
            if is_match_pattern(pdfinsight, rule_name, disclosure_meta):
                # 表头中含有指定字符
                result = NON_COMPLIANCE
            if header_match_pattern(rule_name, pdfinsight, crude_answers):
                # 初步定位的表格表头中含有指定字符
                result = NON_COMPLIANCE
        elif disclosure_result == ND:
            result = NON_COMPLIANCE
        return result


def header_match_pattern(rule_name, pdfinsight, crude_answers):
    outlines = []
    if not crude_answers:
        return False
    for answer in crude_answers:
        outlines.append(
            {
                "page": answer["page"],
                "outline": answer["outline"],
            }
        )
    all_headers = set()
    elts = get_elements(pdfinsight, outlines)
    for elt_type, elt in elts:
        if elt_type != "TABLE":
            continue
        for cell_idx, cell in elt["cells"].items():
            if cell_idx.startswith("0") or cell_idx.endswith("0"):
                if not cell.get("text"):
                    continue
                all_headers.add(cell.get("text"))
    if not all_headers:
        return False
    pattern = PATTERNS[rule_name]
    for header in all_headers:
        header = clean_txt(header)
        if any(_pattern.search(header) for _pattern in pattern):
            return True
    return False


def is_match_pattern(pdfinsight, rule_name, disclosure_meta):
    def get_all_titles(_box_infos):
        for box_info in _box_infos.get("boxes"):
            box = box_info["box"]
            outline = (box["box_left"], box["box_top"], box["box_right"], box["box_bottom"])
            page = box_info["page"]
            elt_type, elt = pdfinsight.find_element_by_outline(page, outline)
            if elt_type == "TABLE":
                try:
                    aim_cell_idx = pdfinsight.find_cell_idx_by_outline(elt, outline, page)
                except ZeroDivisionError:
                    continue
                if aim_cell_idx:
                    if not elt["cells"].get(aim_cell_idx, {}).get("dummy"):
                        row, col = aim_cell_idx.split("_")
                        for cell_idx in (f"{row}_0", f"0_{col}"):
                            yield elt["cells"].get(cell_idx, {}).get("text", "")

    pattern = PATTERNS[rule_name]
    for disclosure_info in disclosure_meta.disclosure_metas.values():
        for box_infos in disclosure_info.get("data"):
            for title in get_all_titles(box_infos):
                if not title:
                    continue
                title = clean_txt(title)
                if any(_pattern.search(title) for _pattern in pattern):
                    return True
    return False


class B19Rule(Rule):
    """
    PS/NS/ND----C
    PS + (表头中含有salar(y|ies) & bounes类似的字符) ----NC
    PS + (表头中含有salar(y|ies) & (Pension or contribution)) - NC
    """

    def __init__(self):
        super(B19Rule, self).__init__(["B19"])

    def check(self, rule_name, question, mold_answer, pdfinsight, used_later_answer, stock_info):
        result = COMPLIANCE
        disclosure_meta = DisclosureMeta(question, mold_answer, rule_name)
        disclosure_results = disclosure_meta.disclosure_results
        third_word = disclosure_meta.third_word
        disclosure_result = disclosure_results[third_word]
        if disclosure_result == PS and is_match_pattern(pdfinsight, rule_name, disclosure_meta):
            result = NON_COMPLIANCE
        return result


class B32EtcRule(Rule):
    """
    根据披露结果判断
    PS----C
    NS/ND---NC"
    """

    def __init__(self):
        super(B32EtcRule, self).__init__(["B32", "B34", "B36"])

    def check(self, rule_name, question, mold_answer, pdfinsight, used_later_answer, stock_info):
        result = NON_COMPLIANCE
        disclosure_meta = DisclosureMeta(question, mold_answer, rule_name)
        disclosure_results = disclosure_meta.disclosure_results
        third_word = disclosure_meta.third_word
        disclosure_result = disclosure_results[third_word]
        if disclosure_result == PS:
            result = COMPLIANCE
        return result


class B20B21Rule(Rule):
    """
    根据披露结果判断
    PS - C
    NS - C
    ND -NC
    """

    def __init__(self):
        super(B20B21Rule, self).__init__(["B20", "B21"])

    def check(self, rule_name, question, mold_answer, pdfinsight, used_later_answer, stock_info):
        result = NON_COMPLIANCE
        disclosure_meta = DisclosureMeta(question, mold_answer, rule_name)
        disclosure_results = disclosure_meta.disclosure_results
        third_word = disclosure_meta.third_word
        disclosure_result = disclosure_results[third_word]
        if disclosure_result in (PS, NS):
            result = COMPLIANCE
        return result


class B22Rule(Rule):
    """
    根据披露结果判断
    1 PS & 2 中的数字都是5的倍数 ---C
    1 PS & 2 中的数字不是5的倍数 -- NC
    1 NS/ND --- NC
    "Table, Upper limit"
    """

    def __init__(self):
        super(B22Rule, self).__init__(["B22"])

    def check(self, rule_name, question, mold_answer, pdfinsight, used_later_answer, stock_info):
        result = NON_COMPLIANCE
        disclosure_meta = DisclosureMeta(question, mold_answer, rule_name)
        disclosure_results = disclosure_meta.disclosure_results
        disclosure_contents = disclosure_meta.disclosure_contents
        third_words = disclosure_meta.third_words
        table, upper_limit = third_words
        first_col_result = disclosure_results[table]
        second_col_content = ""
        if disclosure_contents[upper_limit]:
            second_col_content = disclosure_contents[upper_limit][0]
        if first_col_result == PS:
            upper_limits = get_amount(second_col_content)
            for upper_limit_content in upper_limits:
                upper_limit_content = re.sub(r"\$|,", "", upper_limit_content)
                try:
                    upper_limit_content = float(upper_limit_content)
                except ValueError:
                    upper_limit_content = 0
                if not (upper_limit_content and self.is_multiple_of_5(upper_limit_content)):
                    result = NON_COMPLIANCE
                    break
            else:
                result = COMPLIANCE
        return result

    @staticmethod
    def is_multiple_of_5(n):
        return n % 5 == 0 or n % 5 * 10 % 5 == 0


class B23Rule(Rule):
    """
    情况一：23.1PS/NS时，直接判断为C,无需看B24-B31
    情况二：23.1ND+B24-B31任一个非ND,判断为C
    其他情况均不合规
    系统目前规则有点乱,因为B23.2所有文档默认ND, 所以可以不考虑
    https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/1060#note_253698
    """

    def __init__(self):
        super(B23Rule, self).__init__(["B23"])

    def check(self, rule_name, question, mold_answer, pdfinsight, used_later_answer, stock_info):
        result = NON_COMPLIANCE
        disclosure_meta = DisclosureMeta(question, mold_answer, rule_name)
        disclosure_results = disclosure_meta.disclosure_results
        third_words = disclosure_meta.third_words
        first_sub_col, second_sub_col = third_words
        first_sub_col_result = disclosure_results[first_sub_col]
        second_sub_col_result = disclosure_results[second_sub_col]
        used_later_answer["23_1"] = first_sub_col_result
        used_later_answer["23_2"] = second_sub_col_result
        b24_b31_result = self.get_b24_b31_disclosure(question, mold_answer, used_later_answer)
        if (
            first_sub_col_result in (PS, NS)
            or first_sub_col_result == ND
            and any(result != ND for result in b24_b31_result)
        ):
            result = COMPLIANCE
        return result

    @staticmethod
    def get_b24_b31_disclosure(question, mold_answer, used_later_answer):
        b24_b31_result = []
        for i in range(24, 32):
            rule_name = f"B{str(i)}"
            disclosure_meta = DisclosureMeta(question, mold_answer, rule_name)
            disclosure_results = disclosure_meta.disclosure_results
            b24_b31_result.extend([disclosure_results[i] for i in disclosure_meta.third_words])
        used_later_answer["b24_b31_result"] = b24_b31_result
        return b24_b31_result


class B24ToB31Rule(Rule):
    """
    情况一：PS时，直接判断为C,无需看B23
    情况二：NS/ND+B23.1PS/NS时,判断为C
    其他情况均不合规
    https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/1060#note_253698
    """

    def __init__(self):
        super().__init__(["B24", "B25", "B26", "B27", "B28", "B29", "B30", "B31"])

    def check(self, rule_name, question, mold_answer, pdfinsight, used_later_answer, stock_info):
        result = NON_COMPLIANCE
        disclosure_meta = DisclosureMeta(question, mold_answer, rule_name)
        disclosure_results = disclosure_meta.disclosure_results
        third_word = disclosure_meta.third_word
        first_sub_col_result = disclosure_results[third_word]
        disclosure_result_231 = used_later_answer.get("23_1", "")
        if first_sub_col_result == PS or disclosure_result_231 in (PS, NS):
            result = COMPLIANCE
        return result


class B35Rule(Rule):
    """
    根据披露结果判断
    1.PS----C
    1.NS/ND & 2.PS （百分比小于等于50%）-- C
    1 ND + 2 ND ->C
    others -- NC
    Controlling interest 可能提取多个值，取最大的进行比较
    """

    def __init__(self):
        super(B35Rule, self).__init__(["B35"])

    def check(self, rule_name, question, mold_answer, pdfinsight, used_later_answer, stock_info):
        result = NON_COMPLIANCE
        disclosure_meta = DisclosureMeta(question, mold_answer, rule_name)
        disclosure_results = disclosure_meta.disclosure_results
        third_words = disclosure_meta.third_words
        content, control_interest = third_words
        content_result = disclosure_results[content]
        control_interest_result = disclosure_results[control_interest]
        if any(
            (
                content_result == PS,
                self.control_interest_gt_50(disclosure_meta),
                content_result == ND and control_interest_result == ND,
            )
        ):
            result = COMPLIANCE
        return result

    @staticmethod
    def control_interest_gt_50(disclosure_meta):
        pattern = PATTERNS["B35"]
        disclosure_results = disclosure_meta.disclosure_results
        disclosure_contents = disclosure_meta.disclosure_contents
        third_words = disclosure_meta.third_words
        content, control_interest = third_words
        content_result = disclosure_results[content]
        control_interest_result = disclosure_results[control_interest]
        disclosure_content = disclosure_contents[control_interest]
        if content_result in (NS, ND) and control_interest_result == PS:
            return compare_num_percentage(control_interest, disclosure_content, 50, pattern, greater=False)
        return False


class B37EtcRule(Rule):
    """
    "根据披露结果判断
    PS+关键词or属于固定章节（目录模型判断）——C"
    """

    def __init__(self):
        super(B37EtcRule, self).__init__(["B37", "B38", "B44"])

    def check(self, rule_name, question, mold_answer, pdfinsight, used_later_answer, stock_info):
        result = NON_COMPLIANCE
        disclosure_meta = DisclosureMeta(question, mold_answer, rule_name)
        disclosure_results = disclosure_meta.disclosure_results
        third_word = disclosure_meta.third_word
        third_word_result = disclosure_results[third_word]
        if third_word_result == PS and get_match_result(mold_answer, rule_name, pdfinsight, third_word):
            result = COMPLIANCE
        return result


class B42Rule(Rule):
    """
    根据披露结果判断
    PS|NS + 内容在“director report” -> C
    其余的是NC
    https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/420#note_107603
    """

    def __init__(self):
        super(B42Rule, self).__init__(["B41", "B42"])

    def check(self, rule_name, question, mold_answer, pdfinsight, used_later_answer, stock_info):
        result = NON_COMPLIANCE
        disclosure_meta = DisclosureMeta(question, mold_answer, rule_name)
        disclosure_results = disclosure_meta.disclosure_results
        third_word = disclosure_meta.third_word
        third_word_result = disclosure_results[third_word]
        if third_word_result in (PS, NS) and get_match_result(mold_answer, rule_name, pdfinsight, third_word):
            result = COMPLIANCE
        return result


class B43Rule(Rule):
    """
    根据披露结果判断
    43.1 PS / NS + 43.1 属于固定章节（目录模型判断）——C
    判定合规时，去掉对43.3 的披露判断
    """

    def __init__(self):
        super(B43Rule, self).__init__(["B43"])

    def check(self, rule_name, question, mold_answer, pdfinsight, used_later_answer, stock_info):
        result = NON_COMPLIANCE
        disclosure_meta = DisclosureMeta(question, mold_answer, rule_name)
        disclosure_results = disclosure_meta.disclosure_results
        third_words = disclosure_meta.third_words
        management, service, director_list = tuple(third_words)
        management_result = disclosure_results[management]
        if management_result in (PS, NS) and get_match_result(mold_answer, rule_name, pdfinsight, management):
            result = COMPLIANCE
        return result


class B50Rule(Rule):
    """
    根据披露结果判断
    PS/NS----C
    ND + B46 PS ---C
    如果年报属于银行业或保险业，该条直接默认 -- C
    剩余 -- NC
    """

    def __init__(self):
        super(B50Rule, self).__init__(["B50"])

    def check(self, rule_name, question, mold_answer, pdfinsight, used_later_answer, stock_info):
        result = NON_COMPLIANCE
        disclosure_meta = DisclosureMeta(question, mold_answer, rule_name)
        disclosure_results = disclosure_meta.disclosure_results
        third_word = disclosure_meta.third_word
        third_word_result = disclosure_results[third_word]
        used_later_answer[rule_name] = third_word_result
        belong_bank = stock_info.is_belong_bank_insurance
        if any(
            (
                belong_bank,
                third_word_result in (PS, NS),
                third_word_result == ND and content_gt_num("B46", question, mold_answer, 30, pdfinsight),
            )
        ):
            result = COMPLIANCE
        return result


class B51Rule(Rule):
    """
    根据披露结果判断
    PS/NS----C
    ND + B48 PS ---C
    剩余 -- NC
    """

    def __init__(self):
        super(B51Rule, self).__init__(["B51"])

    def check(self, rule_name, question, mold_answer, pdfinsight, used_later_answer, stock_info):
        result = NON_COMPLIANCE
        disclosure_meta = DisclosureMeta(question, mold_answer, rule_name)
        disclosure_results = disclosure_meta.disclosure_results
        third_word = disclosure_meta.third_word
        third_word_result = disclosure_results[third_word]
        used_later_answer[rule_name] = third_word_result
        if any(
            (
                third_word_result in (PS, NS),
                third_word_result == ND and content_gt_num("B48", question, mold_answer, 30, pdfinsight),
            )
        ):
            result = COMPLIANCE
        return result


class B5253Rule(Rule):
    """
    PS/NS----C
    B15.1 或B15.2 是PS 或NS + B52是ND -> C
    ND---NC
    """

    def __init__(self):
        super(B5253Rule, self).__init__(["B52", "B53"])

    def check(self, rule_name, question, mold_answer, pdfinsight, used_later_answer, stock_info):
        disclosure_meta = DisclosureMeta(question, mold_answer, rule_name)
        third_word = disclosure_meta.third_word
        disclosure_results = disclosure_meta.disclosure_results
        disclosure_result = disclosure_results[third_word]
        result = NON_COMPLIANCE
        b15_1_result = used_later_answer["B15_1"]
        b15_2_result = used_later_answer["B15_2"]
        if any(
            (
                disclosure_result in (NS, PS),
                (b15_1_result in (NS, PS) or b15_2_result in (NS, PS)) and disclosure_result == ND,
            )
        ):
            result = COMPLIANCE
        return result


class B47B48Rule(Rule):
    """
    https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/701#note_187935
    根据披露结果判断
    PS/NS----C
    ND + B51 NS ---C
    如果年报属于银行业或保险业，该条直接默认 -- C
    OTHERS -- NC
    """

    def __init__(self):
        super(B47B48Rule, self).__init__(["B47", "B48"])

    def check(self, rule_name, question, mold_answer, pdfinsight, used_later_answer, stock_info):
        # NOTE: hard code for hkex annual report
        if stock_info.stock == "00388" and stock_info.report_year == "2019":
            return COMPLIANCE
        result = NON_COMPLIANCE
        disclosure_meta = DisclosureMeta(question, mold_answer, rule_name)
        disclosure_results = disclosure_meta.disclosure_results
        third_word = disclosure_meta.third_word
        third_word_result = disclosure_results[third_word]
        belong_bank = stock_info.is_belong_bank_insurance
        if any(
            (belong_bank, third_word_result in (PS, NS), third_word_result == ND and used_later_answer.get("B51") == NS)
        ):
            result = COMPLIANCE
        return result


class B45B46Rule(Rule):
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/1088
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/1126
    """
    PS/NS----C
    ND + B50 NS ---C
    如果年报属于银行业或保险业，该条直接默认 -- C
    OTHERS -- NC
    """

    def __init__(self):
        super(B45B46Rule, self).__init__(["B45", "B46"])

    def check(self, rule_name, question, mold_answer, pdfinsight, used_later_answer, stock_info):
        result = NON_COMPLIANCE
        disclosure_meta = DisclosureMeta(question, mold_answer, rule_name)
        disclosure_results = disclosure_meta.disclosure_results
        third_word = disclosure_meta.third_word
        third_word_result = disclosure_results[third_word]
        belong_bank = stock_info.is_belong_bank_insurance
        if any(
            (belong_bank, third_word_result in (PS, NS), third_word_result == ND and used_later_answer.get("B50") == NS)
        ):
            result = COMPLIANCE
        return result


class B58Rule(Rule):
    """
    "根据披露结果判断
    1PS & 2 PS/NS----C
    1NS & 2 NS/ND --C
    others---NC"
    "Summary of segmental information
    Detail of segmental information"
    """

    def __init__(self):
        super(B58Rule, self).__init__(["B58"])

    def check(self, rule_name, question, mold_answer, pdfinsight, used_later_answer, stock_info):
        result = NON_COMPLIANCE
        disclosure_meta = DisclosureMeta(question, mold_answer, rule_name)
        disclosure_results = disclosure_meta.disclosure_results
        third_words = disclosure_meta.third_words
        summary, detail = tuple(third_words)
        summary_result = disclosure_results[summary]
        detail_result = disclosure_results[detail]
        if any(
            (summary_result == PS and detail_result in (PS, NS), summary_result == NS and detail_result in (NS, ND))
        ):
            result = COMPLIANCE
        return result


class B55EtcRule(Rule):
    """
    不做处理 默认合规
    B39 B40 不返回结果
    B57 规则 PS/NS----C ND---NC 披露模型需要优化 现阶段将 ND -》C
    新增B41 B42 B43
    """

    def __init__(self):
        super(B55EtcRule, self).__init__(
            ["B20", "B21", "B33", "B41", "B42", "B43", "B55", "B57", "B60", "B61", "B62", "B69", "B70"]
        )

    def check(self, rule_name, question, mold_answer, pdfinsight, used_later_answer, stock_info):
        return COMPLIANCE


class B63Rule(Rule):
    """
    B63的7个子项全部PS + B63.1 PS ---> C
    B63的7个子项全部ND + do not have share option，此时不论B63.1是PS/NS/ND ---> C
    B63的7个子项任意一个NS + 63的第一个子项（Number of options）包含grated, exercised, lapsed or cancelled + have share option，
        此时不论B63.1是PS/NS/ND ---> C
    B63的7个子项任意一个NS + 63的第一个子项（Number of options） 不包含grated, exercised, lapsed or cancelled + do not have share
        option ，此时不论B63.1是PS/NS/ND ---> C
    其余 ---> NC
    https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/3859#note_460126
    """

    def __init__(self):
        super(B63Rule, self).__init__(["B63", "B63.1"])

    def check(self, rule_name, question, mold_answer, pdfinsight, used_later_answer, stock_info):
        disclosure_meta = DisclosureMeta(question, mold_answer, rule_name)
        disclosure_results = disclosure_meta.disclosure_results
        disclosure_meta_b63_1 = DisclosureMeta(question, mold_answer, "B63.1")
        b63_1_disclosure_result = disclosure_meta_b63_1.disclosure_results[disclosure_meta_b63_1.third_word]
        used_later_answer["B63.1"] = b63_1_disclosure_result
        has_share_option = (share_info := stock_info.share_info) and share_info["has_share_option"]
        all_is_ps = all((result == PS for result in disclosure_results.values()))
        all_is_nd = all((result == ND for result in disclosure_results.values()))
        one_is_ns = any((result == NS for result in disclosure_results.values()))
        result = COMPLIANCE
        if all_is_ps and b63_1_disclosure_result == PS:
            pass  # 全部PS + B63.1 PS ---> C
        elif all_is_nd and not has_share_option:
            pass  # 全部ND + not have option ---> C
        elif one_is_ns:  # 任意一个NS
            b63_1_contents = disclosure_meta.disclosure_contents[disclosure_meta.third_word]
            contains_keywords = False
            for content in b63_1_contents:
                if PATTERNS["B63"].search(content):
                    contains_keywords = True
                    break
            used_later_answer["B63_has_keyword"] = contains_keywords
            if (contains_keywords and has_share_option) or (not contains_keywords and not has_share_option):
                # 包含关键字 + have option / 不包含关键字 + not have option ---> C
                pass
            else:  # 其余 ---> NC
                result = NON_COMPLIANCE
        else:  # 其余 ---> NC
            result = NON_COMPLIANCE
        return result


class B64Rule(Rule):
    """
    有披露share option scheme + B64.8/B64.9 任意一个是NS + B64.1 PS + (除B64.6其余是ND) 或者 （除去B64.3 B64.4 B64.6 其余是NS）
        ---> NC
    # B64.1-5, B64.7, B64.8, B64.9 全部PS  + have share option ---> C
    # B64.1-5, B64.7, B64.8, B64.9 全部NS  ---> C
    # B64.1 ND + 64.1 = 0 代入判定公式，公式成立 -- C
    # (B64.1-5, B64.7, B64.8, B64.9 全部ND
    #     或 63的第一个子项（Number of options）NS
    #     或 B64.1-5, B64.7, B64.8, B64.9中有一个NS) + do not have share option
    #     ---> C
    其余情况 ---> C
    https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/3859#note_460516
    """

    def __init__(self):
        super(B64Rule, self).__init__(["B64"])

    def check(self, rule_name, question, mold_answer, pdfinsight, used_later_answer, stock_info):
        disclosure_meta = DisclosureMeta(question, mold_answer, rule_name)
        disclosure_results = disclosure_meta.disclosure_results
        b641_result = disclosure_results[disclosure_meta.third_word]
        b64_8_result = self.get_third_result_by_number(question, mold_answer, rule_name, number=8)
        b64_9_result = self.get_third_result_by_number(question, mold_answer, rule_name, number=9)
        share_option_flag = (share_info := stock_info.share_info) and share_info["has_share_option"]
        # 除B64.6其余是ND
        excluding_b64_6_nd = all(
            disclosure_results[disclosure_meta.third_words[number - 1]] == ND for number in (2, 3, 4, 5, 7)
        )
        # 除去B64.3 B64.4 B64.6 其余是NS
        excluding_b64_3_4_6_ns = all(
            disclosure_results[disclosure_meta.third_words[number - 1]] == NS for number in (2, 5, 7)
        )
        if (
            share_option_flag
            and any(result == NS for result in (b64_8_result, b64_9_result))
            and b641_result == PS
            and (excluding_b64_6_nd or excluding_b64_3_4_6_ns)
        ):
            return NON_COMPLIANCE
        # B65 会用到 formula_texts 的结果
        self.get_formula_value(question, mold_answer, used_later_answer)
        # b647_result = self.get_third_result_by_number(question, mold_answer, rule_name, sub_index=7)
        # b631_result = self.get_third_result_by_number(question, mold_answer, "B63")
        # part_is_ps, part_is_ns, one_is_ns, part_is_nd = self.get_third_result(
        #     disclosure_results, disclosure_meta.third_words
        # )
        # b647_9_result_list = [b647_result, b648_result, b649_result]
        # if any(
        #     (
        #         part_is_ps and all(result == PS for result in b647_9_result_list) and share_option_flag,
        #         part_is_ns and all(result == NS for result in b647_9_result_list),
        #         b641_result == ND and formula_result(used_later_answer, "b64_1"),
        #         any(
        #             (
        #                 part_is_nd and all(result == ND for result in b647_9_result_list),
        #                 b631_result == NS,
        #                 (one_is_ns or any(result == NS for result in b647_9_result_list)),
        #             )
        #         )
        #         and not share_option_flag,
        #     )
        # ):
        #     return COMPLIANCE
        return COMPLIANCE

    @staticmethod
    def get_third_result(disclosure_results, third_words):
        part_is_ps = all(disclosure_results[third_word] == PS for third_word in third_words[:5])
        part_is_ns = all(disclosure_results[third_word] == NS for third_word in third_words[:5])
        one_is_ns = any(disclosure_results[third_word] == NS for third_word in third_words[:5])
        part_is_nd = all(disclosure_results[third_word] == ND for third_word in third_words[:5])
        return part_is_ps, part_is_ns, one_is_ns, part_is_nd

    @staticmethod
    def get_formula_value(question, mold_answer, used_later_answer):
        """
        63.6 + 64.1 - 65.1 - 66.1 - 67.1 = 63.7
        将公式中每个子项的值保存到used_later_answer['formula_texts']中
        """
        disclosure_meta = DisclosureMeta(question, mold_answer, "B63")
        disclosure_contents = disclosure_meta.disclosure_contents
        third_words = disclosure_meta.third_words
        b63_6_content = disclosure_contents[third_words[5]][0] if disclosure_contents[third_words[5]] else ""
        b63_7_content = disclosure_contents[third_words[6]][0] if disclosure_contents[third_words[6]] else ""

        disclosure_meta = DisclosureMeta(question, mold_answer, "B64")
        disclosure_contents = disclosure_meta.disclosure_contents
        third_words = disclosure_meta.third_words
        b64_1_content = disclosure_contents[third_words[0]][0] if disclosure_contents[third_words[0]] else ""

        disclosure_meta = DisclosureMeta(question, mold_answer, "B65")
        disclosure_contents = disclosure_meta.disclosure_contents
        third_words = disclosure_meta.third_words
        b65_1_content = disclosure_contents[third_words[0]][0] if disclosure_contents[third_words[0]] else ""

        disclosure_meta = DisclosureMeta(question, mold_answer, "B66")
        disclosure_contents = disclosure_meta.disclosure_contents
        third_words = disclosure_meta.third_words
        b66_1_content = disclosure_contents[third_words[0]][0] if disclosure_contents[third_words[0]] else ""

        disclosure_meta = DisclosureMeta(question, mold_answer, "B67")
        disclosure_contents = disclosure_meta.disclosure_contents
        third_words = disclosure_meta.third_words

        b67_1_content = disclosure_contents[third_words[0]][0] if disclosure_contents[third_words[0]] else ""
        formula_texts = {
            "b63_6": b63_6_content,
            "b63_7": b63_7_content,
            "b64_1": b64_1_content,
            "b65_1": b65_1_content,
            "b66_1": b66_1_content,
            "b67_1": b67_1_content,
        }
        used_later_answer["formula_texts"] = formula_texts
        return used_later_answer


class B65Rule(Rule):
    """
    1、2、3 全部PS、结合code（have share option ） —— C
    1 、2、3 全部NS --C  all is ns
    1 ND + 65.1 = 0 代入判定公式，公式成立 -- C
    全部ND或63.1NS或65.1-65.3中有一个NS + 结合code（do not have share option ）—— C
    """

    def __init__(self):
        super(B65Rule, self).__init__(["B65"])

    def check(self, rule_name, question, mold_answer, pdfinsight, used_later_answer, stock_info):
        result = NON_COMPLIANCE
        disclosure_meta = DisclosureMeta(question, mold_answer, rule_name)
        disclosure_results = disclosure_meta.disclosure_results
        third_words = disclosure_meta.third_words
        b65_1, b65_2, b65_3 = tuple(third_words)
        b65_1_result = disclosure_results[b65_1]
        all_is_ps = all((result == PS for result in disclosure_results.values()))
        all_is_ns = all((result == NS for result in disclosure_results.values()))
        all_is_nd = all((result == ND for result in disclosure_results.values()))
        one_is_ns = any((result == NS for result in disclosure_results.values()))
        share_option_flag = stock_info.have_share_option
        if any(
            (
                all_is_ps and share_option_flag,
                all_is_ns,
                b65_1_result == ND and formula_result(used_later_answer, "b65_1"),
                (all_is_nd or used_later_answer["B63.1"] == NS or one_is_ns) and not share_option_flag,
            )
        ):
            result = COMPLIANCE
        return result


class B66Rule(Rule):
    """
    1，2全部PS、结合code（have share option ） ——C
    1 NS + 2 NS --C
    全部ND或63.1NS或66.1-66.2中有一个NS + 结合code（do not have share option ）——C
    1 ND + 2 NS/PS -- C
    """

    def __init__(self):
        super(B66Rule, self).__init__(["B66"])

    def check(self, rule_name, question, mold_answer, pdfinsight, used_later_answer, stock_info):
        result = NON_COMPLIANCE
        disclosure_meta = DisclosureMeta(question, mold_answer, rule_name)
        disclosure_results = disclosure_meta.disclosure_results
        third_words = disclosure_meta.third_words
        b66_1, b66_2 = third_words
        b66_1_result = disclosure_results[b66_1]
        b66_2_result = disclosure_results[b66_2]
        all_is_ps = all((result == PS for result in disclosure_results.values()))
        all_is_ns = all((result == NS for result in disclosure_results.values()))
        all_is_nd = all((result == ND for result in disclosure_results.values()))
        one_is_ns = any((result == NS for result in disclosure_results.values()))
        share_option_flag = stock_info.have_share_option
        if any(
            (
                all_is_ps and share_option_flag,
                all_is_ns,
                (all_is_nd or used_later_answer["B63.1"] == NS or one_is_ns) and not share_option_flag,
                b66_1_result == ND and b66_2_result != ND,
            )
        ):
            result = COMPLIANCE
        return result


class B67Rule(Rule):
    """
    1 全部PS、结合code（have share option ） ——C
    1 NS - C
    1 ND -- C
    1 ND或63.1NS，结合code（do not have share option ）——C
    """

    def __init__(self):
        super(B67Rule, self).__init__(["B67"])

    def check(self, rule_name, question, mold_answer, pdfinsight, used_later_answer, stock_info):
        result = NON_COMPLIANCE
        disclosure_meta = DisclosureMeta(question, mold_answer, rule_name)
        disclosure_results = disclosure_meta.disclosure_results
        b67_1 = disclosure_meta.third_word
        b67_1_result = disclosure_results[b67_1]
        share_option_flag = stock_info.have_share_option
        if any(
            (
                b67_1_result == PS and share_option_flag,
                b67_1_result in (NS, ND),
                (b67_1_result == ND or used_later_answer["B63.1"] == NS) and not share_option_flag,
            )
        ):
            result = COMPLIANCE
        return result


class B68Rule(Rule):
    """
    PS->C ; NS->C
    ND或63.1NS、结合code（do not have share option ）——C

    """

    def __init__(self):
        super(B68Rule, self).__init__(["B68"])

    def check(self, rule_name, question, mold_answer, pdfinsight, used_later_answer, stock_info):
        result = NON_COMPLIANCE
        disclosure_meta = DisclosureMeta(question, mold_answer, rule_name)
        disclosure_results = disclosure_meta.disclosure_results
        third_word = disclosure_meta.third_word
        third_word_result = disclosure_results[third_word]
        share_option_flag = stock_info.have_share_option
        if any(
            (
                third_word_result in (PS, NS),
                (third_word_result == ND or used_later_answer["B63.1"] == NS) and not share_option_flag,
            )
        ):
            result = COMPLIANCE
        return result


class B71EtcRule(Rule):
    """
    PS->C NS->C
    ND或63.1NS、不包含`grated, exercised, lapsed or cancelled`、结合code（do not have share option ）——C
    """

    def __init__(self):
        super(B71EtcRule, self).__init__(["B71", "B72"])

    def check(self, rule_name, question, mold_answer, pdfinsight, used_later_answer, stock_info):
        result = NON_COMPLIANCE
        disclosure_meta = DisclosureMeta(question, mold_answer, rule_name)
        disclosure_results = disclosure_meta.disclosure_results
        third_word = disclosure_meta.third_word
        third_word_result = disclosure_results[third_word]
        share_option_flag = stock_info.have_share_option
        if any(
            (
                third_word_result in (PS, NS),
                (third_word_result == ND or used_later_answer["B63.1"] == NS) and not share_option_flag,
            )
        ):
            result = COMPLIANCE
        return result


def formula_result(used_later_answer, zero_col):
    """
    B64-B67 判定公式
    beginning + grant - exercise - cancel - lapse = ending
    即63.6 + 64.1 - 65.1 - 66.1 - 67.1 = 63.7
    """
    formula_texts = deepcopy(used_later_answer["formula_texts"])
    if any((i == "" for i in formula_texts.values())):
        return False
    formula_texts[zero_col] = 0
    b63_6 = formula_texts["b63_6"]
    b64_1 = formula_texts["b64_1"]
    b65_1 = formula_texts["b65_1"]
    b66_1 = formula_texts["b66_1"]
    b67_1 = formula_texts["b67_1"]
    b63_7 = formula_texts["b63_7"]
    return b63_6 + b64_1 - b65_1 - b66_1 - b67_1 == b63_7


def content_gt_num(rule_name, question, mold_answer, num, pdfinsight):
    disclosure_meta = DisclosureMeta(question, mold_answer, rule_name)
    disclosure_contents = disclosure_meta.disclosure_contents
    percentage_col = disclosure_meta.third_word
    if disclosure_meta.disclosure_results[percentage_col] == PS:
        return True
    outline_dict = get_outlines(mold_answer, rule_name)
    elts = get_elements(pdfinsight, outline_dict[percentage_col])
    percentages = [elt["text"] for _, elt in elts if elt.get("text")]
    # 上一步会过滤表格中的百分比 将提取的百分比添加到percentages
    percentages.extend(disclosure_contents[percentage_col])
    return compare_num_percentage(rule_name, percentages, num, PATTERNS["B50"])


def compare_num_percentage(rule_name, disclosure_contents, num, extract_num_pattern, greater=True):
    disclosure_contents = set(disclosure_contents)
    if not any((extract_num_pattern.search(disclosure_content) for disclosure_content in disclosure_contents)):
        return False
    ar_percentages = []
    for disclosure_content in disclosure_contents:
        ar_percentages.extend(extract_all_percentage(disclosure_content, extract_num_pattern))
    convert_percentage = []
    for ar_percentage in ar_percentages:
        try:
            ar_percentage = float(ar_percentage)
            if ar_percentage > 100:
                continue
        except ValueError:
            logger.info("%s，%s convert percentage to float failed", rule_name, ar_percentage)
        else:
            convert_percentage.append(ar_percentage)
    if not convert_percentage:
        return False
    convert_percentage = sorted(convert_percentage)
    if greater:
        return convert_percentage[-1] >= num
    else:
        return convert_percentage[-1] <= num


def extract_all_percentage(content, extract_num_pattern):
    ret = []
    for i in extract_num_pattern.finditer(content):
        if i.group():
            ret.append(i.group("dst"))
    return ret


def get_match_result(mold_answer, rule_name, pdfinsight, third_word):
    outline_dict = get_outlines(mold_answer, rule_name)
    elts = get_elements(pdfinsight, outline_dict[third_word])
    if get_config("web.use_syllabus_inspect", False):
        return match_syllabus(elts, pdfinsight) or match_keyword(elts, pdfinsight, rule_name, third_word)
    return match_keyword(elts, pdfinsight, rule_name, third_word)


def match_keyword(elts, pdfinsight, rule_name, third_word):
    prev_elts = {}
    for _, elt in elts:
        elt_index = elt["index"]
        prev_elts[elt["index"]] = elt
        _prev_elts = get_prev_texts(pdfinsight, elt_index)
        for _elt in _prev_elts:
            if _elt["index"] not in prev_elts and _elt["class"] == "PARAGRAPH":
                prev_elts[_elt["index"]] = _elt
    patterns = PATTERNS[rule_name]
    if not isinstance(patterns, list):
        patterns = PATTERNS[rule_name][third_word]
    for elt in prev_elts.values():
        if any((pattern.search(elt.get("text", "")) for pattern in patterns)):
            return True
    return False


def match_syllabus(elts, pdfinsight):
    patterns = PATTERNS["syllabus"]
    syllabus_ids = {elt["syllabus"] for _, elt in elts if elt.get("syllabus")}
    syllabus_titles = []
    for syllabus_id in syllabus_ids:
        syllabus_titles.extend(get_titles_from_pdfindight(pdfinsight, syllabus_id))
    for pattern in patterns:
        if any((pattern.search(syllabus_title) for syllabus_title in set(syllabus_titles))):
            return True
    return False


def get_prev_texts(pdfinsight, index):
    prev_elts = pdfinsight.find_elements_near_by(index, step=-1, amount=3)
    return prev_elts


def get_elements(pdfinsight, outlines):
    elements = []
    elt_indexes = set()
    for outline in outlines:
        elts = pdfinsight.find_elements_by_outline(outline["page"], outline["outline"])
        for elt_type, elt in elts:
            if elt["index"] not in elt_indexes:
                elements.append((elt_type, elt))
                elt_indexes.add(elt["index"])
    return elements


def get_outlines(mold_answer, rule_name):
    preset_items = mold_answer.get_rule_answer(rule_name)
    disclosure_metas = {item["schema"]["data"]["label"]: item for item in preset_items["answer"]}
    outline_dict = {}
    for schema_label, disclosure_result in disclosure_metas.items():
        outlines = []
        disclosure_datas = disclosure_result.get("data", [])
        for data in disclosure_datas:
            for box_info in data.get("boxes", []):
                outlines.append({"page": box_info.get("page"), "outline": box_info.get("box")})
        outline_dict[schema_label] = outlines
    return outline_dict


def get_titles_from_pdfindight(pdfinsight, syllabus_id):
    syllabus_dict = pdfinsight.syllabus_dict
    syllabus_ids = []
    get_syllabus_id(pdfinsight, syllabus_id, syllabus_ids)
    syllabus_ids.insert(0, syllabus_id)

    syllabus_titles = [syllabus_dict.get(syllabus_id, {}).get("title", "") for syllabus_id in syllabus_ids]
    return syllabus_titles


def get_syllabus_id(pdfinsight, _syllabus_id, _syllabus_ids):
    item = pdfinsight.syllabus_dict.get(_syllabus_id, {})
    parent_id = item.get("parent", -1)
    if parent_id == -1:
        return _syllabus_ids
    else:
        _syllabus_ids.append(parent_id)
        return get_syllabus_id(pdfinsight, parent_id, _syllabus_ids)


def is_additional_null(additional_docs, third_word):
    """
    返回 是否 外部文档未提取到内容/不存在
    """
    if not additional_docs:  # 外部文档不存在
        return True
    additional_word = schema_additional_map.get(third_word)
    if not additional_word:
        return False
    if any(
        additional_doc["result"].get(additional_word, {}).get("records", []) for additional_doc in additional_docs
    ):  # 外部文档提取到内容
        return False
    return True


def compare_additional_content(disclosure_contents, additional_docs, third_word, threshold):
    additional_contents = extract_additional_doc(additional_docs, third_word)
    for additional_content in additional_contents:
        if not additional_content:
            continue
        for disclosure_content in disclosure_contents:
            if not disclosure_content:
                continue
            ratio = SequenceMatcher(lambda x: re.search(r"\s+", x), additional_content, disclosure_content).ratio()
            if ratio > threshold:
                return True
            pattern = PATTERNS.get(third_word, None)
            if pattern and pattern.search(disclosure_content) and pattern.search(additional_content):
                return True
    return False


def compare_addition_data(
    disclosure_contents: list[str], addition_row: AdditionData | None, rule_name: str, threshold: float
):
    # 提取答案与gekko外部数据做比较
    if not addition_row:
        return False
    if isinstance(addition_row, AdditionData):
        addition_content = getattr(addition_row, rule_name.lower())
    else:
        addition_content = addition_row
    if " ".join(disclosure_contents).lower() in str(addition_content).lower():
        return True
    for disclosure_content in disclosure_contents:
        if not disclosure_content:
            continue
        ratio = SequenceMatcher(lambda x: re.search(r"\s+", x), str(addition_content), disclosure_content).ratio()
        if ratio > threshold:
            return True
    return False


def compare_consistent_additional(disclosure_contents, additional_docs, third_word, is_num=False):
    additional_contents = extract_additional_doc(additional_docs, third_word)
    for additional_content in additional_contents:
        for disclosure_content in disclosure_contents:
            if is_num:
                # 利用B22的规则
                additional_amount = [decimal.Decimal(amount) for amount in get_amount(additional_content)]
                disclosure_amount = [decimal.Decimal(amount) for amount in get_amount(disclosure_content)]
                if len(set(additional_amount + disclosure_amount)) < len(additional_amount + disclosure_amount):
                    return True
            else:
                additional_content = convert_amount(additional_content.lower())
                if isinstance(additional_content, list):  # 外部问题提取到的金额为范围 id 7059
                    res = amount_range(additional_content, disclosure_content)
                    if res is not None:
                        return res
                disclosure_content = convert_amount(disclosure_content.lower())
                if isinstance(disclosure_content, list):
                    res = amount_range(disclosure_content, additional_content)
                    if res is not None:
                        return res
                if any(
                    (
                        additional_content in disclosure_content,
                        disclosure_content in additional_content,
                        SequenceMatcher(None, additional_content, disclosure_content).ratio() > 0.8,
                    )
                ):
                    return True
    return False


def amount_range(additional_content, disclosure_content):
    less_amount = float(additional_content[0])
    more_amount = float(additional_content[1])
    disclosure_amounts = get_amount(disclosure_content.lower())
    disclosure_amount = float(disclosure_amounts[0])
    if less_amount < disclosure_amount < more_amount:
        return True
    return None


def convert_amount(amount_content):
    """
    将金额中的million转换为千分位数字
    """
    if "million" not in amount_content:
        return amount_content
    amounts = get_amount(amount_content)
    if not amounts:
        return amount_content
    if len(amounts) == 2:
        return amounts
    amount_ori = amounts[0]
    amount = format(float(amount_ori) * 1000000, ",")
    amount_content = amount_content.replace(amount_ori, amount)
    amount_content = amount_content.replace("million", "")
    return amount_content


def extract_additional_doc(additional_docs, third_word):
    # todo 补充B63-72的信息
    contents = []
    additional_key = schema_additional_map.get(third_word)
    if not additional_key:
        return contents
    for additional_doc in additional_docs:
        additional_result = additional_doc["result"].get(additional_key, {})
        for header in additional_result.get("headers", []):
            contents.extend([record[header] for record in additional_result["records"]])
    return contents


def ns_nd_and_no_additional(disclosure_result, additional_docs, third_word):
    """
    NS/ND & 外部文档未提取到内容/不存在 ——C
    """
    return disclosure_result in (NS, ND) and is_additional_null(additional_docs, third_word)


def ps_and_consistent_additional(disclosure_result, disclosure_contents, additional_docs, third_word, is_num):
    """
    PS 外部文档提取内容与年报内容  数字一致——C
    """
    if not additional_docs:
        return False
    return disclosure_result == PS and compare_consistent_additional(
        disclosure_contents[third_word], additional_docs, third_word, is_num
    )


def judge_disclosure(disclosure_result):
    """
    根据披露结果判断
    PS/NS----C
    ND---NC
    """
    if disclosure_result in (NS, PS):
        return COMPLIANCE
    elif disclosure_result == ND:
        return NON_COMPLIANCE
    return None


def get_amount(second_col_content):
    """
    从年报或者外部文档中提取金额
    """
    patterns = PATTERNS["B22"]
    ret = []
    for pattern in patterns:
        if pattern.search(second_col_content):
            for item in pattern.finditer(second_col_content):
                chars = second_col_content[item.start("dst") : item.end("dst")]
                if not chars:
                    continue
                ret.append(re.sub(r"[^\d.]", "", chars))
    return ret


def match_pattern(disclosure_contents, rule_name):
    return any((PATTERNS[rule_name].search(disclosure_content) for disclosure_content in disclosure_contents))


class HkexInspector(Inspector):
    inspector_map = {
        "Jura 2.0 Listing Rules": [
            B1Rule(),
            B2Rule(),
            B3ToB10Rule(),
            B6Rule(),
            B11EtcRule(),
            B12Rule(),
            B14Rule(),
            B15Rule(),
            B17EtcRule(),
            B19Rule(),
            B32EtcRule(),
            B22Rule(),
            B23Rule(),
            B24ToB31Rule(),
            B35Rule(),
            B37EtcRule(),
            B55EtcRule(),
            # B42Rule(),
            # B43Rule(),
            B50Rule(),
            B51Rule(),
            B5253Rule(),
            B45B46Rule(),
            B47B48Rule(),
            B58Rule(),
            B63Rule(),
            B64Rule(),
            B65Rule(),
            B66Rule(),
            B67Rule(),
            B68Rule(),
            B71EtcRule(),
        ],
        "LRs": [],
        "Jura 2.0 Additional Rules": [],
    }
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/3859
    # 获取 jura_21_a 目录下所有文件中所有继承于Rule的子类
    jura_21_a_path = f"{project_root}/remarkable/rule/jura_21_a"
    subclasses = find_subclasses(jura_21_a_path, Rule)
    for subclass in subclasses:
        inspector_map["LRs"].append(subclass())

    # 获取 jura_21_b 目录下所有文件中所有继承于Rule的子类
    jura_21_b_path = f"{project_root}/remarkable/rule/jura_21_b"
    subclasses = find_subclasses(jura_21_b_path, Rule)
    for subclass in subclasses:
        inspector_map["Jura 2.0 Listing Rules"].append(subclass())

    # 获取 jura_21_c 目录下所有文件中所有继承于Rule的子类
    jura_21_c_path = f"{project_root}/remarkable/rule/jura_21_c"
    subclasses = find_subclasses(jura_21_c_path, Rule)
    for subclass in subclasses:
        inspector_map["Jura 2.0 Additional Rules"].append(subclass())

    def __init__(self, mold, **kwargs):
        if mold.id == special_mold.v2_id:
            if kwargs.get("only_jura21") is True:
                jura_21_rule_b_rules = [B63Rule(), B64Rule()]
                jura_21_b_path = f"{project_root}/remarkable/rule/jura_21_b"
                subclasses = find_subclasses(jura_21_b_path, Rule)
                for subclass in subclasses:
                    jura_21_rule_b_rules.append(subclass())
                self.inspector_map["Jura 2.0 Listing Rules"] = jura_21_rule_b_rules
            if get_config("ONLY_B1_B10"):
                self.inspector_map["Jura 2.0 Listing Rules"] = [B1Rule(), B2Rule(), B3ToB10Rule(), B6Rule()]
            if get_config("ONLY_B98_B99"):
                self.inspector_map["Jura 2.0 Listing Rules"] = [B98Rule(), B99Rule()]

        super().__init__(self.inspector_map, mold)

    def sort_by(self, item):
        label_num = item["schema"]["data"]["label"][1:]
        nums = label_num.split(".")
        if len(nums) > 2:
            # 处理C1.1.1/C1.2.1
            label_num = f"{nums[0]}.{''.join(nums[1:])}"
        return float(label_num)


class POLLInspector(HkexInspector):
    inspector_map = {"POLL": []}
    # 获取 jura6_poll 目录下所有文件中所有继承于Rule的子类
    jura6_poll_path = f"{project_root}/remarkable/rule/jura_6_poll"
    subclasses = find_subclasses(jura6_poll_path, Rule)
    for subclass in subclasses:
        inspector_map["POLL"].append(subclass())

    def sort_by(self, item):
        return float(item["schema"]["data"]["label"][1:2])


class AGMInspector(HkexInspector):
    inspector_map = {"AGM": []}
    # 获取 jura6_agm 目录下所有文件中所有继承于Rule的子类
    jura6_agm_path = f"{project_root}/remarkable/rule/jura_6_agm"
    subclasses = find_subclasses(jura6_agm_path, Rule)
    for subclass in subclasses:
        inspector_map["AGM"].append(subclass())

    def sort_by(self, item):
        label = item["schema"]["data"]["label"]
        return float(label[1 : label.index("-")])
