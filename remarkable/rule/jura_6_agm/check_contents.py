from remarkable.common.constants import <PERSON><PERSON><PERSON><PERSON>NC<PERSON>, NON_COMPLIANCE, TableType
from remarkable.common.pattern import MatchMulti
from remarkable.common.util import clean_txt
from remarkable.pdfinsight.parser import parse_table
from remarkable.pdfinsight.reader import PdfinsightReader

P_DEFINITIONS = MatchMulti.compile("^DEFINITIONS$", operator=any)


def check_contents_by_rule(
    pdfinsight: PdfinsightReader,
    p_sentence_rule: MatchMulti,
):
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6175#note_659200
    for page in range(pdfinsight.max_page + 1):
        for ele in pdfinsight.find_elements_by_page(page):
            # todo 修改 find_elements_by_page 方法 和  单元测试 test_chapter_fixer
            _, ele = pdfinsight.find_element_by_index(ele["index"])
            if not ele:
                continue
            if ele["class"] == "PARAGRAPH" and p_sentence_rule.search(ele.get("text") or ""):
                return NON_COMPLIANCE, ele
            elif ele["class"] == "TABLE":
                table = parse_table(ele, tabletype=TableType.TUPLE.value, pdfinsight_reader=pdfinsight)
                # 先匹配前两个元素块
                first_two_elements = table.elements_above[:2]
                if any(P_DEFINITIONS.search(clean_txt(i.get("text", ""))) for i in first_two_elements):
                    continue
                for cells in table.rows:
                    if p_sentence_rule.search("".join(clean_txt(cell.text).strip() for cell in cells)):
                        return NON_COMPLIANCE, ele
    return COMPLIANCE, None
