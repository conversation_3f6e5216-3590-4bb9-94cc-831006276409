from remarkable.common.constants import COMPLIANCE
from remarkable.common.pattern import MatchMulti
from remarkable.rule.hkex_rules import ND
from remarkable.rule.inspector import Rule
from remarkable.rule.jura_6_agm.check_contents import check_contents_by_rule
from remarkable.rule.utils import DisclosureMeta


# https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6175
class AGM2Rule(Rule):
    P_SENTENCE_RULE = MatchMulti.compile(
        # 明确的回购的标题
        MatchMulti.compile(
            r"repurchase\s*(general\s*)?mandate",
            r"purchase\s*mandate",
            r"buy-?back\s*mandate",
            operator=any,
        ),
        # 通过语义理解的回购
        MatchMulti.compile(
            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7086
            r"Share Repurchase Rules.*consideration of the proposal.*Repurchase Resolution",
            operator=any,
        ),
        operator=any,
    )

    def __init__(self):
        super().__init__(
            [
                "M2-total number and description of the shares which the issuer proposes to purchase",
                "M3-reasons for the proposed purchase of shares",
                "M4-source of funds for making the proposed purchase",
                "M5-material adverse impact on the working capital or gearing position",
                "M6-close associates of the directors with a present intention to sell shares to the issuer",
                "M7-directors will exercise the power to make purchases in accordance with rules and laws",
                "M8-consequences of any purchases under the Takeovers Code",
                "M9-purchases made in the previous six months",
                "M10-whether core connected persons of the issuer have notified their intention to sell shares to the issuer",
                "M11-highest and lowest prices of shares during the previous twelve months",
                "M12-unusual features",
                "M13-whether issuer intends to cancel the repurchased shares or hold them as treasury shares",
                "M14-purchase mandate",
                "M15-relevant period for purchase mandate",
                "M17-extension to general mandate",
            ]
        )

    def check(self, rule_name, question, mold_answer, pdfinsight, used_later_answer, stock_info):
        disclosure_meta = DisclosureMeta(question, mold_answer, rule_name)
        disclosure_result = disclosure_meta.disclosure_results[disclosure_meta.third_word]
        if disclosure_result != ND:
            return COMPLIANCE
        if res := used_later_answer.get("AGM_M2"):
            return res
        res, _ = check_contents_by_rule(pdfinsight, self.P_SENTENCE_RULE)
        used_later_answer["AGM_M2"] = res
        return res
