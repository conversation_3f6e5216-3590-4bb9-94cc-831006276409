from remarkable.common.constants import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, NON_COMPLIANCE
from remarkable.common.pattern import NeglectPattern
from remarkable.rule.hkex_rules import ND
from remarkable.rule.inspector import Rule
from remarkable.rule.jura_6_agm.check_contents import check_contents_by_rule
from remarkable.rule.utils import DisclosureMeta


# https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6175
class AGM16Rule(Rule):
    P_SENTENCE_RULE = NeglectPattern.compile(
        unmatch=r"issu(e|ance)\s*(of\s)?debt",
        match=r"(issue|general)\s*mandate",
    )

    def __init__(self):
        super().__init__(
            [
                "M16-issue mandate",
                "M18-relevant period for issue mandate",
            ]
        )

    def check(self, rule_name, question, mold_answer, pdfinsight, used_later_answer, stock_info):
        disclosure_meta = DisclosureMeta(question, mold_answer, rule_name)
        disclosure_result = disclosure_meta.disclosure_results[disclosure_meta.third_word]
        if disclosure_result != ND:
            return COMPLIANCE
        if res := used_later_answer.get("AGM_M16"):
            return res
        res, _ = check_contents_by_rule(pdfinsight, self.P_SENTENCE_RULE)
        used_later_answer["AGM_M16"] = res
        return res


class AGM19Rule(Rule):
    def __init__(self):
        super().__init__(
            [
                "M19-appoint an auditor to hold office",
                "M20-the appointment, removal and remuneration of auditors",
            ]
        )

    def check(self, rule_name, question, mold_answer, pdfinsight, used_later_answer, stock_info):
        disclosure_meta = DisclosureMeta(question, mold_answer, rule_name)
        disclosure_result = disclosure_meta.disclosure_results[disclosure_meta.third_word]
        if disclosure_result == ND:
            return NON_COMPLIANCE
        return COMPLIANCE
