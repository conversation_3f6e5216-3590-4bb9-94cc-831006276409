from remarkable.common.constants import COMPLIANC<PERSON>, NON_COMPLIANCE, TableType
from remarkable.common.pattern import MatchMulti
from remarkable.common.util import clean_txt
from remarkable.pdfinsight.parser import parse_table
from remarkable.rule.hkex_disclosure_rules import ND
from remarkable.rule.inspector import Rule
from remarkable.rule.utils import DisclosureMeta


class AGM23Rule(Rule):
    def __init__(self):
        super().__init__(
            [
                "M23-a super-majority vote required to approve a change to those rights",
            ]
        )

    def check(self, rule_name, question, mold_answer, pdfinsight, used_later_answer, stock_info):
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6175#note_659220
        return COMPLIANCE


class AGM24_25Rule(Rule):
    """
    两个子项均不是ND时-C
    只有一个子项ND时- NC;
    两个子项均为ND时，分为如下两种情况：
    情况 1-文档中不存在Amendments/amended + articles /association/memorandum/bye-laws相关内容，ND-C
    情况 2-文档中存在Amendments/amended + articles /association/memorandum/bye-laws相关内容，ND-NC
    """

    P_SENTENCE_RULE = MatchMulti.compile(
        r"Amendments|amended", "articles|association|memorandum|bye-laws", operator=all
    )

    P_COMPLIANCE_RULES = MatchMulti.compile(r"(Amendments|amended).*?[\)）]", operator=any)

    P_INVALID_SYLLABUS = MatchMulti.compile(
        r"WORK REPORT OF THE SUPERVISORY COMMITTEE",
        "^DEFINITIONS?$",
        "Relevant Authorisation",  # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7003#note_716029
        operator=any,
    )

    P_CONTENTS_RULE = MatchMulti.compile(
        MatchMulti.compile(
            r"amendments?|amended",
            r"articles|memorandum|association|constitution|bye-laws",
            operator=all,
        ),
        MatchMulti.compile(
            r"\b(adoption|adopt|adopted)",
            r"\bnew",
            r"articles|memorandum|association|constitution|bye-laws",
            operator=all,
        ),
        operator=any,
    )

    def __init__(self):
        super().__init__(
            [
                "M24-a super-majority vote required to approve changes to constitutional documents",
                "M25-13.51(1)",
            ]
        )

    def check(self, rule_name, question, mold_answer, pdfinsight, used_later_answer, stock_info):
        disclosure_meta = DisclosureMeta(question, mold_answer, rule_name)
        disclosure_results = [disclosure_meta.disclosure_results[word] for word in disclosure_meta.third_words]
        if all(res != ND for res in disclosure_results):
            return COMPLIANCE
        if self.check_disclosure_association(pdfinsight):
            return NON_COMPLIANCE
        # if all(res == ND for res in disclosure_results):
        #     res, ele = check_contents_by_rule(pdfinsight, self.P_SENTENCE_RULE)
        #     # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6569#note_695819
        #     if res == NON_COMPLIANCE and self.check_compliance_by_syllabus(pdfinsight, ele):
        #         return COMPLIANCE
        #     # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6284#note_666556
        #     if res == NON_COMPLIANCE and ele and ele.get("class") == "PARAGRAPH":
        #         if any(self.P_COMPLIANCE_RULES.search(sub_text) for sub_text in split_paragraph(ele["text"])):
        #             return COMPLIANCE
        #     return res
        return COMPLIANCE

    @classmethod
    def check_compliance_by_syllabus(cls, pdfinsight, element):
        return any(
            cls.P_INVALID_SYLLABUS.search(clean_txt(syll["title"]))
            for syll in pdfinsight.find_syllabuses_by_index(element["index"])
        ) or cls.P_INVALID_SYLLABUS.search(pdfinsight.page_chapter_from_first_para.get(element["page"]) or "")

    @classmethod
    def check_disclosure_association(cls, pdfinsight):
        """
        检查"封面"和"目录 contents"页是否存在相关内容
        """
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7141#note_736149
        for page, page_eles in pdfinsight.page_element_dict.items():
            if page != 0 and not pdfinsight.is_catalog_page(page):
                continue
            for item in page_eles:
                ele_type, element = pdfinsight.find_element_by_index(item.data["index"])
                if ele_type == "TABLE":
                    table = parse_table(element, tabletype=TableType.ROW, pdfinsight_reader=pdfinsight)
                    for row in table.rows:
                        if cls.P_CONTENTS_RULE.search(" ".join(clean_txt(i.text) for i in row)):
                            return True
                elif ele_type == "PARAGRAPH":
                    if cls.P_CONTENTS_RULE.search(clean_txt(element["text"])):
                        return True
            if page != 0:
                break
        return False
