from remarkable.common.common_pattern import P_SEN_SEPARATOR
from remarkable.common.constants import COMP<PERSON><PERSON>NC<PERSON>, NON_COMPLIANCE
from remarkable.common.pattern import <PERSON><PERSON>ulti, SplitBeforeMatch
from remarkable.rule.hkex_rules import ND, NS, PS
from remarkable.rule.inspector import Rule
from remarkable.rule.utils import DisclosureMeta

R_GREATER_THAN_12 = r"\b(1[2-9]|[2-9]\d|[1-9]\d{2,})\b"
P_MAX_12MOS = [
    SplitBeforeMatch(
        MatchMulti.compile(
            "be vested", f"(year|anniversary|{R_GREATER_THAN_12}months).*?(grant date|date of grant)", operator=any
        ),
        separator=P_SEN_SEPARATOR,
        operator=any,
    ),
]

P_MIN_12MOS = [
    SplitBeforeMatch(
        MatchMulti.compile("(were|are|been) vested", operator=all),
        separator=P_SEN_SEPARATOR,
        operator=any,
    ),
]


def get_is_max_12mos(content: str) -> bool:
    # 判断归属期大于等于12个月
    is_max_12mos = False
    if content:
        if any(pattern.search(content) for pattern in P_MIN_12MOS):
            is_max_12mos = False
        elif any(pattern.search(content) for pattern in P_MAX_12MOS):
            is_max_12mos = True
    return is_max_12mos


class B951Rule(Rule):
    """
    B95.1
    NS/PS---> C
    ND + B64子项1 NS→ C
    ND + B64子项1 PS + B64子项3大于等于12个月 + B64子项8 PS→ C
    其余情况 ---> NC
    https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/3859#note_459770
    """

    def __init__(self):
        super().__init__(["B95.1"])

    def check(self, rule_name, question, mold_answer, pdfinsight, used_later_answer, stock_info):
        disclosure_result = self.get_third_result_by_number(question, mold_answer, rule_name)
        if disclosure_result in (NS, PS):
            return COMPLIANCE
        if disclosure_result == ND:
            b64_1_result = self.get_third_result_by_number(question, mold_answer, "B64")
            if b64_1_result == NS:
                return COMPLIANCE
            b64_meta = DisclosureMeta(question, mold_answer, "B64")
            b64_3_contents = b64_meta.disclosure_contents[b64_meta.third_words[2]]
            b64_8_result = self.get_third_result_by_number(question, mold_answer, "B64", number=8)
            if b64_1_result == PS and b64_8_result == PS and get_is_max_12mos("\n".join(b64_3_contents)):
                return COMPLIANCE
        return NON_COMPLIANCE


class B952Rule(Rule):
    """
    B95.2
    NS/PS---> C
    ND + B75子项1 NS→ C
    ND + B75子项1 PS + B75子项3大于等于12个月 + B75子项6 PS→ C
    其余情况 ---> NC
    https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/3859#note_459770
    """

    def __init__(self):
        super().__init__(["B95.2"])

    def check(self, rule_name, question, mold_answer, pdfinsight, used_later_answer, stock_info):
        disclosure_result = self.get_third_result_by_number(question, mold_answer, rule_name)
        if disclosure_result in (NS, PS):
            return COMPLIANCE
        if disclosure_result == ND:
            b75_1_result = self.get_third_result_by_number(question, mold_answer, "B75")
            if b75_1_result == NS:
                return COMPLIANCE
            b75_6_result = self.get_third_result_by_number(question, mold_answer, "B75", number=6)
            b75_meta = DisclosureMeta(question, mold_answer, "B75")
            b75_3_contents = b75_meta.disclosure_contents[b75_meta.third_words[2]]
            if b75_1_result == PS and b75_6_result == PS and get_is_max_12mos("\n".join(b75_3_contents)):
                return COMPLIANCE
        return NON_COMPLIANCE
