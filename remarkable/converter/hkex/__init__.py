import logging
import re
from collections import defaultdict
from copy import deepcopy
from datetime import datetime
from decimal import Decimal
from functools import reduce
from itertools import groupby
from pathlib import Path
from types import FunctionType
from typing import Any, Callable, Dict, List, Union
from weakref import WeakValueDictionary

import attr
from dateutil.relativedelta import relativedelta

from remarkable.answer.node import AnswerItem, AnswerNode
from remarkable.common.common import EmptyAnswer
from remarkable.common.common_pattern import (
    R_CN_SPACE,
    R_CURRENT_ASSETS,
    R_MIDDLE_DASH,
    R_NON_CURRENT_ASSETS,
    R_TOTAL_ASSETS,
)
from remarkable.common.constants import AnswerValueEnum, DocType, PollGMLRules, RatioAnswerType
from remarkable.common.pattern import MatchMulti, PatternCollection
from remarkable.common.util import clean_txt, format_amount
from remarkable.config import get_config
from remarkable.converter import BaseConverter, ConverterMaker, pack
from remarkable.converter.utils import (
    <PERSON>N_NUM_MAP,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON>anager,
    date_from_text,
    is_similar,
    safe_eval,
)
from remarkable.db import pw_db
from remarkable.models.exchange_rate import ExchangeRate
from remarkable.models.hkex_file_meta import DelistedFileMeta, HKEXFileMeta
from remarkable.models.mold import special_mold
from remarkable.models.new_mold import NewMold
from remarkable.models.new_question import NewQuestion
from remarkable.models.poll_gml import PollGML
from remarkable.models.poll_meta import POLLMeta
from remarkable.models.rule_reference import RuleReference
from remarkable.models.special_answer import NewSpecialAnswer
from remarkable.schemas.poll import (
    PollGMLFieldAnswerSchema,
    PollGMLSubRuleSchema,
    PollGMLTypeAnswerSchema,
    PollPassedDateSchema,
    PollResolutionSchema,
)
from remarkable.spiders.exchange_rate_hkd import ExchangeRageHKDApi

logger = logging.getLogger(__name__)
# 过滤中文内容及xxx month ended ...之类只保留日期数字类型描述
date_sub_p = PatternCollection(
    [r"(\d{1,3})(,\d{3})+\.?\d+|\d{,3}\.\d+", r"[^\da-zA-Z]+", r".*end(ed)?", r"^\s*As\s*at", r"(un)?audit(ed)?"],
    flags=re.IGNORECASE,
)
time_period_p = PatternCollection(
    [r"(Current) Period", r"(Current) Year", r"Corr. Period in (Prior) Year", r"(Prior) Year", "Last Reported Value"]
)
DESCRIPTION_PREFIX = r"Red\;Flag\;when\;"
P_COMPLEX_DATE_TWO_DATE = PatternCollection(
    [
        r"Period from.*?to\s?"
        r"(?P<day>\d+)\s*"
        r"(?P<month>(?:January|February|March|April|May|June|July|August|September|October|November|December))\s*"
        r"(?P<year>20\d{2})\s*",  # 'Period from 1 October 2020 to 31 March 2022'
    ],
    re.I,
)
# 未识别到数字年份，用中文年份 https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6172#note_715398
P_CN_DATE = PatternCollection(r"(?P<year>[二贰][零0oO〇].{2})年($|[\sa-z])", re.I)

MONTHS_TEXT = (
    "January|February|March|April|May|June|July|August|September|October"
    "|November|December|Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec"
)
P_MATCH_DATE = re.compile(
    r"on\s*(?P<day>\d+)\s*" rf"(?P<month>(?:{MONTHS_TEXT}))\s*" r"(?P<year>20\d{2})\s*",
    re.I,
)

P_COMPLEX_DATE = PatternCollection(
    [
        r"^(?:\s*For\s*the\s*)?(?:Three|Six|Nine)\s*months\s*ended\s*"
        r"(?P<day>\d+)\s*"
        r"(?P<month>(?:January|February|March|April|May|June|July|August|September|October|November|December))\s*"
        r"截至.*?日止.*?個月\s*"
        r"(?P<year>20\d{2})\s*",  # Three months ended 31 September 截至三月三十一日止共三個月 2021 二零二一年
        r"^(?:\s*For\s*the\s*)?(?:Three|Six|Nine)?(?:\s*months\s*ended)?\s*"
        r"(?P<day>\d+)\s*"
        r"(?P<month>(?:January|February|March|April|May|June|July|August|September|October|November|December))\s*"
        r"(?P<year>20\d{2})\s*",  # Six months ended 30 September 2021,
        r"A[ts]\s*?(?P<day>[0-3]?\d+)\.(?P<month>[0-1]?\d+)\.(?P<year>20\d{2})\b",  # At 31.3.2020
        r"[在於于](?P<year>20\d{2})\s*?年\s*?(?P<month>[0-1]?\d)\s*?月\s*?(?P<day>[0-3]?\d+)",  # 於2020年3月31日
        r"(?P<day>[0-1]?\d+)\s*?"
        r"(?P<month>(?:January|February|March|April|May|June|July|August|September|October|November|December))\s*?"
        r"(?:\w*)\s*?(?P<year>20\d{2})",  # 31 October Group 2020
        r"(?P<day>[0-3]?\d)\.(?P<month>[0-1]?\d)\.(?P<year>20\d{2})",  # 30.9.2021
        r"(?P<year>20\d{2})\.(?P<month>[0-1]?\d)\.(?P<day>[0-3]?\d)",  # 2021.9.30
    ],
    re.S | re.I,
)
P_COMPLEX_DATE_QUARTER = PatternCollection(
    [
        r"(?P<year>\d{4})年?\s*第?(?P<quarter>[一二三四1234]+)季度",
        r"(?P<year>\d{4})年?\s*Q(?P<quarter>[1234])",
        r"\b(?P<year>2\d)\s*Q(?P<quarter>[1-4])\b",  # 21Q3
        r"(?P<quarter>\bQ[1-4])\s*(?P<year>2\d)\b",  # Q3 21
        # First three quarters of 2021
        r"(?P<quarter>(?:one|two|three|four)|[1-4]|(?:first|second|third|fourth))\s*quarters?.*?(?P<year>\d{4})",
        r"(?P<quarter>January- March)\s*(?P<year>2\d+)",  # January-March 2021
    ],
    re.S | re.I,
)

QUARTER_MAPPING = {
    "1": (3, 31),
    "2": (6, 30),
    "3": (9, 30),
    "4": (12, 31),
    "一": (3, 31),
    "二": (6, 30),
    "三": (9, 30),
    "四": (12, 31),
    "One": (3, 31),
    "Two": (6, 30),
    "Three": (9, 30),
    "Four": (12, 31),
    "First": (3, 31),
    "Second": (6, 30),
    "Third": (9, 30),
    "Fourth": (12, 31),
    "January- march": (3, 31),
}

P_COMPLEX_DATE_MONTH = PatternCollection(
    [
        r"(?P<minus>Three|Six|Nine|Twelve)\s*months\s*ended\s*"
        r"(?P<day>\d{,2})\s*"
        r"(?P<month>(?:January|February|March|April|May|June|July|August|September|October|November|December))\s*"
        r"(\(?\w+\)?\s)?(?P<year>\d{4})?",  # Six/Twelve months ended 31 December 2021
        # Nine months ended 30 September 截至九月三十日止九個月 2021 二零二一年
        # Six months ended 31 October (unaudited) 2021
    ],
    re.S | re.I,
)

P_COMPLEX_DATE_MONTH_YEAR = PatternCollection(
    [
        r"(?P<month>[0-2]?\d)(M)(?P<year>2\d)",  # 9M20
        r"(?P<year>2\d)(M)(?P<month>[0-2]?\d)",  # 20M9
    ]
)

MONTHS_MAP = {
    "January": 1,
    "February": 2,
    "March": 3,
    "April": 4,
    "May": 5,
    "June": 6,
    "July": 7,
    "August": 8,
    "September": 9,
    "October": 10,
    "November": 11,
    "December": 12,
    "Three": 3,  # Q3
    "Six": 6,  # Interim
    "Nine": 9,  # Q1
    "Twelve": 0,  # Final
}

P_TOTAL_ASSETS_OTHER = PatternCollection([r"assets\s*(cla46915sified\s*(as\s*)?)?held\s*for\s*sale"], re.IGNORECASE)

P_TOTAL_ASSETS = PatternCollection([R_TOTAL_ASSETS, r"^assets$"], re.IGNORECASE)

P_NON_CURRENT_ASSETS = PatternCollection([R_NON_CURRENT_ASSETS], re.IGNORECASE)

P_CURRENT_ASSERT = PatternCollection([R_CURRENT_ASSETS], re.IGNORECASE)

DUMMY_PAGE_INDEX = -1

P_NET = PatternCollection([r"net"], re.IGNORECASE)
P_OPTION_NET = PatternCollection([r"\|?[—–-]"])
P_OPTION_NET_BLACK = PatternCollection([r"Write-off"], re.I)
P_CONTINUED = PatternCollection(r"Continuing operation|Discontinued operation", re.I)
# https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/1043#note_254587
P_SPECIAL_SUB_TOTAL = PatternCollection(r"(Continuing operation|Discontinued operation)s?[:：]\|", re.I)

P_SUMMARY_IMPAIRMENT = PatternCollection(r"trade receivabl(es)?, bond receivable", re.I)
P_OPTION_IMPAIRMENT = PatternCollection(r"in respect of (trade|bond) receivabl(es)?", re.I)

P_CURRENT_DATE = PatternCollection(
    [
        rf"(ending|Closing)\s*balance{R_CN_SPACE}$",
        r"\b(in|during)\s*(this|the|current).*?(period|year)",
        # https://www1.hkexnews.hk/listedco/listconews/sehk/2025/0422/2025042201685.pdf code=1202, year=2024, schema=29 page=69
        r"^current\s*(period|year)",
        # stock_code=01513, year=2024, mid=29
        # stock_code=00568, year=2024, page=119
        r"\b(end|close?)(ing)?\s*of\s*(this|the|current).*?(period|year)",
    ],
    re.I,
)

P_LAST_DATE = PatternCollection(
    [
        r"(ending|Closing)\s*balance\s*for\s*preceding\s*period",
        # stock_code=01202, year=2024, mid=29: https://www1.hkexnews.hk/listedco/listconews/sehk/2025/0422/2025042201685.pdf
        # stock_code=0187, year=2024
        rf"(beginn?ing|Openn?ing)\s*balance{R_CN_SPACE}$",
        # stock_code=09977, year=2024, page=117
        r"(last|previous|Preceding)\s*(period|year)",
        # stock_code=01513, year=2024, mid=29
        # stock_code=00568, year=2024, page=119
        r"(beginn?ing|Openn?ing)\s*of\s*(the|this|current)\s*(period|year)",
    ],
    re.I,
)


def attr2dict(obj):
    def _filter(key, val):
        if key.name.startswith("_") or key.name.endswith("_func"):
            return False
        if getattr(obj, "RENDER_WHITELIST", None) and key.name in obj.RENDER_WHITELIST:
            return True
        if getattr(obj, "RENDER_BLACKLIST", None) and key.name in obj.RENDER_BLACKLIST:
            return False
        if isinstance(val, (FunctionType, datetime)):
            return False
        return True

    return attr.asdict(obj, filter=_filter)


def get_date_var(doc_type: int, current_year=False) -> str | None:
    date_var = {
        DocType.Q1.value: "Current Period",
        DocType.INTERIM.value: "Current Period",
        DocType.Q3.value: "Current Period",
        DocType.FINAL.value: "Current Year",
    }.get(doc_type)
    pre_date_var = {
        DocType.Q1.value: "Corr. Period in Prior Year",
        DocType.INTERIM.value: "Corr. Period in Prior Year",
        DocType.Q3.value: "Corr. Period in Prior Year",
        DocType.FINAL.value: "Prior Year",
    }.get(doc_type)
    return date_var if current_year else pre_date_var


@attr.s(slots=True)
class ExtInfo:
    # 需要更多额外数据可以继续加
    meta: HKEXFileMeta = attr.ib()
    ratio_type: str = attr.ib(default="")


@attr.s(slots=True)
class Box:
    box_left: float = attr.ib()
    box_top: float = attr.ib()
    box_right: float = attr.ib()
    box_bottom: float = attr.ib()

    def __add__(self, other: "Box"):
        if other.is_dummy:
            return self
        return Box(
            min(self.box_left, other.box_left),
            min(self.box_top, other.box_top),
            max(self.box_right, other.box_right),
            max(self.box_bottom, other.box_bottom),
        )

    @staticmethod
    def dummy_box():
        return Box(-1, -1, -1, -1)

    @property
    def is_dummy(self):
        return all(getattr(self, f.name) < 0 for f in attr.fields(Box))


@attr.s(slots=True)
class BoxItem:
    box: Box = attr.ib(converter=lambda x: pack(x or Box.dummy_box(), Box))
    page: int = attr.ib()
    text: str = attr.ib()
    value_: Any = attr.ib(default=None)
    fid: int = attr.ib(default=None)  # FIXME: remove default
    meta: dict = attr.ib(default=attr.Factory(dict))

    def __attrs_post_init__(self):
        if self.box.is_dummy and self.page != DUMMY_PAGE_INDEX:
            self.page = DUMMY_PAGE_INDEX

    def __eq__(self, other: "BoxItem"):
        return self.box == other.box and self.text == other.text and self.fid == other.fid

    def __add__(self, other: "BoxItem"):
        if self == other or other.is_dummy:
            return BoxItem(self.box, self.page, self.text, fid=other.fid)
        if self.fid == other.fid:
            box = self.box + other.box
            text = f"{self.text}\n{other.text}"
        else:
            box = other.box
            text = other.text
        return BoxItem(box, self.page, text, fid=other.fid)

    @staticmethod
    def load_from_answer_item(item: AnswerItem, attr_name: str | None = None, fid: int | None = None):
        """多框合并成一个框"""
        if not item.data:
            return BoxItem.dummy_box_item(fid=fid)
        offset_map = {
            "time_period": [0, 2],  # 时间段取前两个框
            # 科目可能包含了父标题，取所有框: http://100.64.0.105:55647/#/project/remark/379639?projectId=17&treeId=5374&fileId=112999&schemaId=29
            "account": [0, None],
            None: [0, None],  # 未指定属性名，取所有
        }
        start, end = offset_map.get(attr_name) or [0, 1]
        box_item = reduce(
            lambda x, y: x + y,
            [BoxItem(**box, fid=fid) for data in item.data[start:end] for box in data.get("boxes", [])],
        )
        box_item.meta = item.meta
        return box_item

    @staticmethod
    def dummy_box_item(text="", fid=None):
        return BoxItem(Box.dummy_box(), DUMMY_PAGE_INDEX, text, fid=fid)

    @property
    def is_dummy(self):
        return self.page == DUMMY_PAGE_INDEX and self.box.is_dummy and not self.text


@attr.s(slots=True)
class DataItem:
    time_period: BoxItem = attr.ib(converter=lambda x: pack(x, BoxItem))
    currency: BoxItem = attr.ib(converter=lambda x: pack(x, BoxItem))
    unit: BoxItem = attr.ib(converter=lambda x: pack(x, BoxItem))
    account: BoxItem = attr.ib(converter=lambda x: pack(x, BoxItem))
    value: BoxItem = attr.ib(converter=lambda x: pack(x, BoxItem))

    date_var = attr.ib(default=None)
    from_attr: str = attr.ib(default="")

    IGNORE_FIELD = {"date_var", "from_attr"}
    RENDER_BLACKLIST = ("from_attr",)

    def __str__(self):
        return "|".join(
            f"{a.name}:{getattr(self, a.name).text or None}"
            for a in attr.fields(self.__class__)
            if a.name not in self.IGNORE_FIELD
        )

    def __add__(self, other):
        if other.is_dummy:
            return self
        return DataItem(
            self.time_period + other.time_period,
            self.currency + other.currency,
            self.unit + other.unit,
            self.account + other.account,
            self.value + other.value,
        )

    def export_source_answer(self):
        return {
            "Time Period": self.time_period.text,
            "Currency": self.currency.text,
            "Unit": self.unit.text,
            "Account": self.account.text,
            "Value": self.value.text,
        }

    @staticmethod
    def dummy_data_item(fid=None):
        return DataItem(
            *(
                BoxItem.dummy_box_item(fid=fid)
                for attr_ in attr.fields(DataItem)
                if attr_.name not in DataItem.IGNORE_FIELD
            )
        )

    @property
    def is_dummy(self):
        return all(
            getattr(self, i.name).is_dummy for i in attr.fields(self.__class__) if i.name not in self.IGNORE_FIELD
        )

    @classmethod
    def parse_node(cls, node: AnswerNode, ext_info: ExtInfo) -> "DataItem":
        if node.name == "Material Impairment":
            dummy_data = cls.dummy_data_item(fid=ext_info.meta.fid)
            dummy_data.account = BoxItem.load_from_answer_item(node.data, fid=ext_info.meta.fid)
            if not dummy_data.account.text:
                dummy_data.account.text = node.data.value
            # TODO: D/NS枚举值如何处理?
            return dummy_data

        if node.isleaf():
            raise ValueError
        fields = attr.fields(cls)
        data = cls(
            *(cls.get_box_item(node, f, fid=ext_info.meta.fid) for f in fields if f.name not in cls.IGNORE_FIELD)
        )
        cls.revise_attrs(data, ext_info)
        return data

    @classmethod
    def revise_attrs(cls, data: "DataItem", ext_info: ExtInfo):
        for field in attr.fields(cls):
            if field.name in cls.IGNORE_FIELD:
                continue
            func_name = f"revise_{field.name}"
            method = getattr(
                cls,
                func_name,
                lambda *args, **kwargs: logger.debug(f'{cls.__name__}: "{func_name}" func not found'),  # noqa
            )
            method(data, ext_info)

    @classmethod
    def get_box_item(cls, node: AnswerNode, attr_, fid=None):
        path = " ".join(a.capitalize() for a in attr_.name.split("_"))
        sub_node = node.get(path, None)
        if sub_node is None:
            raise ValueError
        if not sub_node:
            logger.debug(f'Missing answer for "{node.fullpath}-{path}"')
            return BoxItem.dummy_box_item(fid=fid)
        for node_ in sub_node.values():
            if not node_.isleaf():
                raise ValueError
            return BoxItem.load_from_answer_item(node_.data, attr_.name, fid=fid)

    @staticmethod
    def revise_time_period(item: "DataItem", ext_info: ExtInfo):
        if item.time_period.is_dummy:
            return
        date_text = clean_txt(item.time_period.text)
        while True:
            matched = P_COMPLEX_DATE_MONTH.nexts(date_text)
            if matched:
                date = date_from_text(
                    f"{matched.group('day')} {matched.group('month')} {matched.group('year') or ext_info.meta.report_year}"
                )
                minus = MONTHS_MAP.get(matched.group("minus").capitalize(), 12)
                date_text = (date - relativedelta(months=minus)).strftime("%d %B %Y")
                break

            matched = P_COMPLEX_DATE_TWO_DATE.nexts(date_text)
            if matched and matched.group("day") and matched.group("year") and matched.group("month"):
                date_text = "{} {} {}".format(matched.group("day"), matched.group("month"), matched.group("year"))
                break

            matched = P_COMPLEX_DATE.nexts(date_text)
            if matched and matched.group("day") and matched.group("year") and matched.group("month"):
                date_text = "{} {} {}".format(matched.group("day"), matched.group("month"), matched.group("year"))
                break

            matched = P_COMPLEX_DATE_QUARTER.nexts(date_text)
            if matched and matched.group("year") and matched.group("quarter"):
                year_str = matched.group("year")
                if len(year_str) == 2:
                    year_str = f"20{year_str}"
                date_text = "{} {} {}".format(*QUARTER_MAPPING.get(matched.group("quarter").capitalize()), year_str)
                break
            # Amount in this reporting period
            if P_CURRENT_DATE.nexts(date_text):
                date_text = f"{ext_info.meta.year_end}"
                break
            # Amount in last reporting period
            if P_LAST_DATE.nexts(date_text):
                date_text = f"{ext_info.meta.year_end.replace(ext_info.meta.report_year, str(int(ext_info.meta.report_year) - 1))}"
                break

            matched = P_COMPLEX_DATE_MONTH_YEAR.nexts(date_text)
            if matched:
                # 9M21 -> 2021 9th, 1
                date_text = P_COMPLEX_DATE_MONTH_YEAR.sub(
                    f"20{matched.group('year')} {matched.group('month')}th, 1", date_text
                )
                break

            # 未取到英文年份，尝试取中文年份 TODO 考虑中文年月日、季
            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6172#note_715398
            # stock_code=00436, year=2024, mid=29
            if matched := P_CN_DATE.nexts(date_text):
                date_text = "".join(str(CN_NUM_MAP.get(n, n)) for n in matched.group("year"))
            break

        date_str = clean_txt(date_sub_p.sub(" ", date_text)).strip()
        try:
            year_end = date_from_text(date_str)
        except:  # noqa
            logger.error(f'Failed to parse date "{date_str}"')
        else:
            if year_end:
                item.time_period.value_ = year_end

    @staticmethod
    def revise_currency(item: "DataItem", ext_info: ExtInfo):
        pass
        # item.currency.value_ = re.sub(r'\d', '', clean_txt(item.currency.text))

    @staticmethod
    def revise_unit(item: "DataItem", ext_info: ExtInfo):
        if not item.unit.is_dummy:
            _, value = UnitManager.get_unit_from_str(clean_txt(item.unit.text, remove_blank=True))
            item.unit.value_ = value
        else:
            item.unit.value_ = UnitManager.DEFAULT_VALUE

    @staticmethod
    def revise_account(item: "DataItem", ext_info: ExtInfo):
        # 去掉科目描述中的尾注 or 前缀
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/637#note_166224
        replace_map = {
            r"^Net\s*?(impairment)": r"\1",  # Net impairment -> impairment
            r"\bon\s*?(financial)": r"of \1",  # on financial -> of financial
            r"\(note.*\)": "",
            r"^Including[:：]\s*": "",
            r"(\w+)ies(\b)": r"\1y\2",  # ies -> y
            r"(\w+)es(\b)": r"\1\2",  # 去掉es
            "FVTOCI": "fair value through other comprehensive income",
            "FVOCI": "fair value through other comprehensive income",
            "FVTPL": "fair value through profit or loss",
            "FVPL": "fair value through profit or loss",
        }
        if not item.is_dummy and ext_info.ratio_type in ("ratio2", "ratio5"):
            # ratio2的account会有sub_title前缀
            text, *sub_titles = clean_txt(item.account.text).split("|", maxsplit=1)[::-1]
            for pattern, value in replace_map.items():
                if re.search(pattern, text, re.I):
                    text = clean_txt(re.sub(pattern, value, text, flags=re.I))
                    # break
            item.account.value_ = "|".join(sub_titles + [text])

        elif ext_info.ratio_type in ("ratio6",):
            item.account.value_ = clean_txt(item.account.text)

    @staticmethod
    def revise_value(item: "DataItem", ext_info: ExtInfo):
        # 多组数据(大多是提取分组错误), 按`\n`拆分取第一项参与计算
        clear_text = clean_txt(item.value.text.strip().split("\n", 1)[0])
        sign = -1 if re.match(r"^\(.*\)$|^\-", clear_text) else 1  # 带括号为负
        value_str = re.sub(r"[^\d.]", "", clear_text)  # 只留数字及小数点
        if value_str:
            try:
                item.value.value_ = item.unit.value_ * float(value_str) * sign
            except Exception as exp:
                logger.error(exp)
                item.value.value_ = 0.0
        else:
            item.value.value_ = 0.0


@attr.s(slots=True)
class SectionItem:
    RENDER_BLACKLIST = ("attrs_", "label_", "period_")

    label: str = attr.ib()
    label_: str = attr.ib(default="")
    period_: str = attr.ib(default="")
    # TODO: 去重
    items: List[DataItem] = attr.ib(default=attr.Factory(list), converter=lambda x: [pack(i, DataItem) for i in x])
    date_: str = attr.ib(default="")
    attrs_: List[Union[str, tuple]] = attr.ib(default=attr.Factory(list))
    value_: float = attr.ib(default=0.0)
    operator_: str = attr.ib(default="")
    visible: bool = attr.ib(default=True)  # 是否显示
    is_manual: bool = attr.ib(default=False)
    revise_func: Callable = attr.ib(default=lambda *args, **kwargs: None)
    rm_dup_func: Callable = attr.ib(default=lambda *args, **kwargs: None)
    calc_func: Callable = attr.ib(default=lambda *args, **kwargs: None)
    sub_labels: List[dict] = attr.ib(default=attr.Factory(list))  # ratio label来自子section
    date_var: str = attr.ib(default=None)
    scope: Dict = attr.ib(default=None)

    def __attrs_post_init__(self):
        if self.scope:
            self.scope = {**self.scope}
        if self.label_ and self.period_:
            self.label = f"{self.label_} {self.period_}"

    def sum(self, is_abs=False):
        self.value_ = sum(abs(i.value.value_) if is_abs else i.value.value_ for i in self.items)
        return self.value_

    def sub(self):
        if self.items:
            self.value_ = self.items[0].value.value_
            for item in self.items[1:]:
                if item.value.value_ is not None:
                    self.value_ -= item.value.value_
        return self.value_

    def __add__(self, other):
        if not isinstance(other, SectionItem):
            raise ValueError
        if not self.items:
            self.items = other.items
        else:
            for self_item, other_item in zip(
                self.items,
                other.items,
            ):
                self_item = self_item + other_item
        self.is_manual = True  # 有合并操作说明答案来自用户直接标注
        return self

    @property
    def args_count(self):
        return self.operator_.count("{}")


@attr.s(slots=True)
class FormulaItem:
    RENDER_BLACKLIST = ("is_ai",)

    label: str = attr.ib(default="Formula 1")
    name: str = attr.ib(default="")
    order: int = attr.ib(default=1)
    ratio: float = attr.ib(default=0.0)
    ratio_str: str = attr.ib(default="-")
    formula_info: str = attr.ib(default="")
    sections: List[SectionItem] = attr.ib(
        default=attr.Factory(list), converter=lambda x: [pack(i, SectionItem) for i in x]
    )
    red_flag: bool = attr.ib(default=False)  # 是否标红
    flag_cond: str = attr.ib(default="")
    revise_func: Callable = attr.ib(default=lambda *args, **kwargs: None)
    unit_info: Dict = attr.ib(default=None)
    is_ai: bool | None = attr.ib(default=None)

    def __attrs_post_init__(self):
        if self.unit_info:
            self.unit_info = {**self.unit_info}

    def convert_currency(self, financial_period_end, ratio_currency):
        if self.unit_info:
            date = date_from_text(financial_period_end)

            currency = self.currency or ratio_currency or "HK$"

            currency_str = CurrencyManager.get_currency_for_db(currency)
            if currency_str != CurrencyManager.get_currency_for_db(self.unit_info["target"]):
                rate = ExchangeRate.get_exchange_rate_by_date(date, currency_str)
                if not rate:
                    logger.error(f"miss currency exchange rate for {currency_str} {date}")
                    return self.ratio

                self.unit_info.update(
                    {
                        "link": ExchangeRageHKDApi.PAGE_FOR_USER,
                        "date": date.strftime("%Y-%m-%d"),
                        "rate": rate,
                    }
                )
                decimal_places = self.calc_decimal_places(self.ratio, rate)
                ratio = (Decimal(str(self.ratio)) * Decimal(str(rate))).quantize(Decimal("0." + "0" * decimal_places))
                ratio_str = format_amount(ratio)
                self.formula_info += BaseChecker.desc2latex(f"=HK${ratio_str}")
                return ratio

        return self.ratio

    @staticmethod
    def calc_decimal_places(*nums):
        """calculate the number of decimal places"""
        dec_places = []
        for num in nums:
            if num is None:
                continue
            if isinstance(num, str):
                num = num.replace(",", "")
            if isinstance(num, (int, float)):
                num = Decimal(str(num))
            if isinstance(num, Decimal):
                dec_places.append(abs(num.as_tuple().exponent))
        return max(dec_places) if dec_places else 0

    def __add__(self, other):
        """这里的加法实际做的是覆盖&合并的操作: self-模板结构, other-用户数据,
        即把用户数据填充到预定义公式结构中, 所以是有方向的, 不支持交换律"""
        if not isinstance(other, FormulaItem) or other.label != self.label:
            raise ValueError
        other_sections = {s.label: s for s in other.sections}
        for sec in self.sections:
            sec = sec + other_sections.get(sec.label, sec)
        return self

    @property
    def currency(self):
        currency = ""
        for section in self.sections:
            if not section.scope:  # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/618#note_167791
                for item in section.items:
                    if item.currency.text and "%" not in item.currency.text:
                        currency = currency or item.currency.text
        return currency

    @property
    def unit(self):
        unit = None
        for section in self.sections:
            if not section.scope:
                for item in section.items:
                    unit = unit or item.unit.value_
        return unit or UnitManager.DEFAULT_VALUE


@attr.s(slots=True)
class RatioData:
    label: str = attr.ib()
    description: str = attr.ib()
    ai: List[FormulaItem] = attr.ib(default=attr.Factory(list), converter=lambda x: [pack(i, FormulaItem) for i in x])
    manual: List[FormulaItem] = attr.ib(
        default=attr.Factory(list), converter=lambda x: [pack(i, FormulaItem) for i in x]
    )

    @classmethod
    async def get_ratio_results(
        cls, ext_info, question, mold, special_answers, checker, answer_type, ratio, scope_info
    ):
        formulas = [
            {"id": i.id, **i.data} for i in special_answers if i.answer_type.startswith(f"hkex_{answer_type}_{ratio}")
        ]
        if not formulas and checker.name != "base":
            answer = EmptyAnswer(mold).merge(question.preset_answer if answer_type == "ai" else None)
            checker = checker(
                RatioConverter.load_answer_node(answer), ext_info, answer_type=answer_type, scope_info=scope_info
            )
            formulas = await checker.create_formulas(question.id, answer_type)
        cls.split_section_label(formulas)
        return formulas

    @classmethod
    def split_section_label(cls, formulas):
        # 拆分section label，仅供前端展示
        for formula in formulas:
            for section in formula["sections"]:
                match = time_period_p.nexts(section["label"])
                if match:
                    section["label_"] = section["label"][: match.start()].strip()
                    section["period_"] = match.group()
                else:
                    section["label_"] = section["label"]
                    section["period_"] = ""

    @classmethod
    async def collect_ratio_results(cls, qid: int, delist: bool = False):
        question = await NewQuestion.find_by_id(qid)
        file_meta_class = DelistedFileMeta if delist else HKEXFileMeta
        file_meta = await pw_db.first(file_meta_class.select().where(file_meta_class.fid == question.fid))
        ext_info = ExtInfo(file_meta)
        detail = deepcopy(get_config("ratio_check"))

        special_answers = await pw_db.execute(
            NewSpecialAnswer.select().filter(NewSpecialAnswer.qid == qid, NewSpecialAnswer.deleted_utc == 0)
        )
        special_answers = sorted(special_answers, key=lambda x: (x.data.get("order") or 0, x.id))
        mold = await NewMold.find_by_id(question.mold)
        scope_info = await RatioConverter.load_scope_info(ext_info)
        definitions = await RuleReference.find_descriptions(file_meta.stock_code, r"^ratio*")
        for ratio, data in detail.items():
            checker = RatioConverter.get_ratio_checker(ratio)

            data["ai"] = checker.order_sections(
                await cls.get_ratio_results(ext_info, question, mold, special_answers, checker, "ai", ratio, scope_info)
            )
            # 拼接描述信息
            data["description"] = checker.render_description(data, ext_info)
            data["definition"] = definitions[ratio]
            data["manual"] = checker.order_sections(
                await cls.get_ratio_results(
                    ext_info, question, mold, special_answers, checker, "manual", ratio, scope_info
                )
            )
            data["visible"] = special_mold.is_needed(ratio, ext_info.meta.doc_type)
            data["brief_summary"] = {"reason": None, **checker.gen_brief_summary(data["ai"], data["manual"])}
            data["ai_ext_docs"] = await cls.get_ext_docs(file_meta, data["ai"], ratio)
            data["ai_currency_info"] = checker.get_ai_currency_info(data["ai"])
            data["manual_currency_info"] = checker.get_manual_currency_info(ratio, special_answers)
        return {"orders": RatioAnswerType.orders(), "detail": detail}

    @classmethod
    async def get_ext_docs(cls, file_meta, formulas, ratio) -> List[dict] | None:
        """
        https://mm.paodingai.com/cheftin/pl/di5inigexpre386b34hjmi53rh
        ext_docs: None, 不展示 additional documents 按钮
        ext_docs: [], additional documents 按钮置灰, hover 提示
        ext_docs: 非空, 正常展示
        """
        ext_docs = None
        if ratio == "ratio3" and any(f["red_flag"] for f in formulas):
            ext_docs = []
            for idx, meta in enumerate(
                await pw_db.execute(
                    HKEXFileMeta.select(HKEXFileMeta.fid)
                    .filter(
                        HKEXFileMeta.stock_code == file_meta.stock_code,
                        HKEXFileMeta.report_year == file_meta.report_year,
                        HKEXFileMeta.doc_type == file_meta.doc_type + 10,
                    )
                    .order_by(HKEXFileMeta.fid.desc())
                )
            ):
                ext_docs.append(
                    {
                        "file_name": f"Document {idx + 1}",
                        "file_id": meta.fid,
                    }
                )
        return ext_docs


class RatioConverter(BaseConverter, ConverterMaker):
    checkers = WeakValueDictionary()

    @classmethod
    async def load_scope_info(cls, ext_info: ExtInfo):
        """
        :param ext_info:
        :return:  {
            "Xi-1": {
                "fid": 1,
                "_meta": ExtInfo,
                "_question": NewQuestion,
                "doc_type_name": 'Final',
                "year": 2020,
            },
            "Yi": {
                "doc_type": 12,
                "year": 2020,
            },
        }
        """
        cls._load_checkers()

        # find all supported scope
        scope_mapping = {}
        for clazz in cls.checkers.values():
            if not clazz.SCOPE_MAPPING:
                continue

            for item in clazz.SCOPE_MAPPING.values():
                assert item["name"] in clazz.SUPPORTED_SCOPES

                year_delta, doc_type = clazz.SUPPORTED_SCOPES[item["name"]]
                scope_mapping[item["name"]] = {"doc_type": doc_type, "year_delta": year_delta}

        if scope_mapping:
            file_metas, questions = await cls._find_file_meta_by_ext_info(ext_info, scope_mapping)

            await cls.update_scope_mapping(file_metas, questions, scope_mapping)
        return scope_mapping

    @classmethod
    async def update_scope_mapping(cls, file_metas, questions, scope_mapping):
        for file_meta in file_metas:
            meta = ExtInfo(file_meta)
            current_question = None
            for question in questions:
                if question.fid == meta.meta.fid:
                    current_question = question
                    break
            for _, info in scope_mapping.items():
                if meta.meta.doc_type == info["doc_type"] and int(meta.meta.report_year) == info["year"]:
                    info["fid"] = meta.meta.fid
                    info["qid"] = meta.meta.qid
                    info["_meta"] = meta
                    info["_question"] = current_question
                    if current_question:
                        info["_answer"] = cls.load_answer_node(current_question.preset_answer)
                    break

    @classmethod
    async def _find_file_meta_by_ext_info(cls, ext_info, scope_mapping):
        cond = None
        for name, info in scope_mapping.items():
            year = int(ext_info.meta.report_year) + info["year_delta"]
            sub_cond = (HKEXFileMeta.doc_type == info["doc_type"]) & (HKEXFileMeta.report_year == str(year))
            if cond:
                cond |= sub_cond
            else:
                cond = sub_cond
            scope_mapping[name] = {
                "year": year,
                "doc_type_name": DocType.status_anno_map()[info["doc_type"]],
                "doc_type": info["doc_type"],
            }
        cond &= HKEXFileMeta.stock_code == ext_info.meta.stock_code
        file_metas = await pw_db.execute(HKEXFileMeta.select().filter(cond).order_by(HKEXFileMeta.published.asc()))
        questions = await pw_db.execute(
            NewQuestion.select().filter(NewQuestion.id.in_([item.qid for item in file_metas]))
        )
        return file_metas, questions

    def convert(self, ext_info: ExtInfo, scope_info: Dict, answer_type: str) -> Dict[str, List[FormulaItem]]:
        self._load_checkers()
        results = {}
        for ratio, cls in self.checkers.items():
            if not self.answer_node:
                continue
            try:
                results[ratio] = cls(self.answer_node, ext_info, scope_info, answer_type).check() or []
            except NotImplementedError:
                logger.warning(f"{self._sub_checker_name(ratio)} not implemented yet.")
        return results

    @classmethod
    def _load_checkers(cls):
        for ratio in RatioAnswerType.orders():
            cls.checkers[ratio] = cls.get_ratio_checker(ratio)

    @staticmethod
    def _sub_checker_name(ratio: str):
        return f"{ratio.capitalize()}Checker"

    @classmethod
    def get_ratio_checker(cls, ratio):
        if not cls.checkers.get(ratio):
            clz = cls.load_converter(__package__, Path(__file__).parent, cls._sub_checker_name(ratio))
            cls.checkers[ratio] = BaseChecker if clz.__name__ == "NullConverter" else clz
            if not hasattr(cls.checkers[ratio], "formulas"):
                raise NotImplementedError("Empty Formula detected")
            if cls.checkers[ratio].name == "base":
                logger.warning(f'"{cls._sub_checker_name(ratio)}" not implemented yet.')
        return cls.checkers[ratio]

    @classmethod
    async def run_pipe(cls, question: NewQuestion, mold: NewMold):
        file_meta = await pw_db.first(HKEXFileMeta.select().where(HKEXFileMeta.fid == question.fid))
        if not file_meta:
            raise ValueError(f"Meta info not found: fid {question.fid}, qid {question.id}")

        ext_info = ExtInfo(file_meta)

        scope_info = await cls.load_scope_info(ext_info)

        formulas = []
        for answer_type, answer in (("manual", None), ("ai", question.preset_answer)):
            answer = EmptyAnswer(mold).merge(answer)
            formulas.extend(
                [
                    (question.id, f"{RatioAnswerType.prefix(answer_type)}{ratio}", attr2dict(item))
                    for ratio, items in cls(answer).convert(ext_info, scope_info, answer_type).items()
                    for item in items
                ]
            )
        if not formulas:
            logging.warning(f"No answer saved for question: {question.id}, please check your checking pipe")
            return

        fields = [NewSpecialAnswer.qid, NewSpecialAnswer.answer_type, NewSpecialAnswer.data]
        async with pw_db.atomic():
            existed_answers = {
                f"{a.answer_type}|{a.data.get('label')}": a
                for a in await pw_db.execute(
                    NewSpecialAnswer.select().filter(
                        NewSpecialAnswer.qid == question.id,
                        NewSpecialAnswer.answer_type.startswith(RatioAnswerType.prefix("manual")),
                    )
                )
            }

            max_order_mapping = defaultdict(int)
            for item in existed_answers.values():
                order = item.data.get("order") or 0
                if order > max_order_mapping[item.answer_type]:
                    max_order_mapping[item.answer_type] = order

            new_formulas = []
            for _id, answer_type, formula in formulas:
                label = f"{answer_type}|{formula.get('label')}"
                if label not in existed_answers:
                    if answer_type.startswith(RatioAnswerType.prefix("manual")) and answer_type in max_order_mapping:
                        max_order_mapping[answer_type] += 1
                        formula["order"] = max_order_mapping[answer_type]
                    new_formulas.append([_id, answer_type, formula])

            await pw_db.execute(
                NewSpecialAnswer.delete().where(
                    NewSpecialAnswer.qid == question.id,
                    NewSpecialAnswer.answer_type.startswith(RatioAnswerType.prefix("ai")),
                )
            )
            await pw_db.execute(NewSpecialAnswer.insert_many(new_formulas, fields=fields))
        await BaseChecker.save_summary_result(question.id)


class BaseChecker:
    name = "base"
    formulas: List[Union[dict, FormulaItem]] = []

    SUPPORTED_SCOPES = {
        "Xi-1": (-1, DocType.FINAL),
        "Yi": (0, DocType.INTERIM),
    }
    SCOPE_MAPPING = None
    IGNORE_INVALID_RESULT = False
    P_COMPARE_OPERATOR = re.compile(r"[><≥≤]")

    def __init__(
        self,
        answer_node: AnswerNode | None = None,
        ext_info: ExtInfo | None = None,
        scope_info: Dict | None = None,
        answer_type: str | None = None,
    ):
        self.answer_type = answer_type
        self.answer_node = answer_node
        if ext_info:
            ext_info.ratio_type = self.name
        self.ext_info = ext_info
        self.scope_info = scope_info
        self.formulas = [FormulaItem(**f) for f in self.get_formulas()]

    def get_formulas(self):
        return self.__class__.formulas[::]

    @classmethod
    def render_description(cls, result, ext_info):  # noqa
        return (
            "$"
            + DESCRIPTION_PREFIX
            + r"\;OR\;".join(
                cls.desc2latex(f"{cls._gen_formula_string(formula, is_value=False)} {formula['flag_cond']}")
                for formula in (result["ai"][:-1] if cls.name == "ratio2" else result["ai"])
            )
            + "$"
        )

    @classmethod
    def desc2latex(cls, desc: str):
        if desc.startswith("$") and desc.endswith("$"):
            return desc
        # 负数加括号
        desc = re.sub(r"(\-(?:\d{1,3},?)+\.?\d+)(\s*\))", r"(\1)\2", desc)
        # 特殊字符转义
        desc = re.sub(r"([{}\$])", r"\\text{\\\1}", desc)
        # 百分比
        desc = re.sub(r"(\d+)\%", r"\1\\%", desc)
        if cls.P_COMPARE_OPERATOR.search(desc):
            words = re.split(r"\s+", desc)
            cond = " ".join(words[:-2])
            result = " ".join(words[-2:]).replace("≥", "\\geq ").replace("≤", "\\leq ")
        else:
            cond = desc
            result = ""
        # " Current Year" -> "_{Current Year}"
        cond = re.sub(
            r"\s*(Corr\. Period in Prior Year|Current Period|Current Year|Prior Year|Last Reported Value)",
            r"_{\1}",
            cond,
        )
        division_sign_p = re.compile(r"\s*/\s*")
        if division_sign_p.search(cond):
            divisor, dividend = division_sign_p.split(cond)
            cond = f"\\frac{{{divisor}}}{{{dividend}}}"
        return f"{cond}{' ' + result if result else ''}".replace(" ", r"\;").replace("│", "|")

    @classmethod
    def has_valid_formula(cls, manual_formulas):
        """人工提交任意有效条目都算有效"""
        for formula in (pack(f, FormulaItem) for f in manual_formulas):
            for section in formula.sections:
                for item in section.items:
                    if not item.is_dummy:
                        return True
        return False

    @classmethod
    def gen_brief_summary(cls, ai_formulas, manual_formulas):
        ret = []

        formulas = ai_formulas
        if cls.has_valid_formula(manual_formulas):
            formulas = manual_formulas

        for formula in formulas:
            ratio_str = formula["ratio_str"]
            if "=" in ratio_str:
                ratio_str, *_ = ratio_str.split("=")

            ret.append({"label": formula["label"], "ratio_str": ratio_str, "red_flag": formula["red_flag"]})

        return {"type": "flag", "description": None, "summary": ret}

    @classmethod
    async def save_summary_result(cls, qid):
        meta = await pw_db.first(
            HKEXFileMeta.select()
            .join(NewQuestion, on=(NewQuestion.fid == HKEXFileMeta.fid))
            .filter(NewQuestion.id == qid)
        )
        if not meta:
            logger.error("no meta for question: %s", qid)
            return

        result = {}
        answers = defaultdict(dict)
        for answer in await pw_db.execute(
            NewSpecialAnswer.select().filter(NewSpecialAnswer.qid == qid, NewSpecialAnswer.deleted_utc == 0)
        ):
            *_, answer_type, ratio_name = answer.answer_type.split("_")
            answers[ratio_name].setdefault(answer_type, []).append(answer.data)
        for ratio_name, data in answers.items():
            checker = RatioConverter.get_ratio_checker(ratio_name)
            if checker and data.get("ai") and data.get("manual"):
                brief_summary = checker.gen_brief_summary(data["ai"], data["manual"])
                result[ratio_name] = "false" if any(s.get("red_flag") for s in brief_summary["summary"]) else "true"
            else:
                logger.warning(f"no checker or ai/manual answers for ratio: {ratio_name}")
        # TODO: meta.stat_res 为啥会为 None？
        if any(((meta.stat_res or {}).get(key) != value) for key, value in result.items()):
            meta.stat_res = {**(meta.stat_res or {}), **result}
            await pw_db.update(meta)
            logger.info(f"save_summary_result for question: {qid=}, {meta.fid=}")

    @classmethod
    def order_sections(cls, formulas, instantiate=False):
        return formulas

    async def create_formulas(self, qid, answer_type):
        fields = [NewSpecialAnswer.qid, NewSpecialAnswer.answer_type, NewSpecialAnswer.data]
        values = [(qid, RatioAnswerType.prefix(answer_type) + self.name, attr2dict(f)) for f in self.check()]
        first_pk = await pw_db.execute(NewSpecialAnswer.insert_many(values, fields=fields))
        await self.save_summary_result(qid)
        return [
            {"id": pk, **value[-1]}
            for pk, value in zip(
                range(first_pk, first_pk + len(values)),
                values,
            )
        ]

    @classmethod
    def fix_ai_formulas_for_manual_template(cls, ai_formulas, ext_info):  # noqa
        for formula in ai_formulas:
            formula["id"] = None
        return ai_formulas

    @classmethod
    def _get_valid_currency(cls, formulas):
        for formula in formulas:
            for section in formula["sections"]:
                if not section.get("scope"):
                    for item in section["items"]:
                        if item["currency"].get("text"):
                            return item["currency"]
        return None

    @classmethod
    def _get_valid_unit(cls, formulas):
        for formula in formulas:
            for section in formula["sections"]:
                if not section.get("scope"):
                    for item in section["items"]:
                        if item["unit"].get("text"):
                            return item["unit"]
        return None

    @classmethod
    def get_ai_currency_info(cls, ai_formulas):
        currency = cls._get_valid_currency(ai_formulas) or attr2dict(BoxItem.dummy_box_item(""))
        unit = cls._get_valid_unit(ai_formulas) or attr2dict(BoxItem.dummy_box_item(""))

        return {"currency": currency, "unit": unit}

    @classmethod
    def get_manual_currency_info(cls, ratio, special_answers):
        data = [item for item in special_answers if item.answer_type.startswith(f"hkex_currency_{ratio}")]
        if data:
            data.sort(key=lambda x: x.updated_utc, reverse=True)
            return data[0].data
        return {"currency": attr2dict(BoxItem.dummy_box_item("")), "unit": attr2dict(BoxItem.dummy_box_item(""))}

    @classmethod
    def add_currency_info(cls, formulas, currency_info):
        if (
            not currency_info
            or not currency_info.get("unit")
            or not currency_info.get("currency")
            or (not currency_info["unit"].get("text") and not currency_info["currency"].get("text"))
        ):
            return False

        for formula in formulas:
            for section in formula["sections"]:
                for item in section["items"]:
                    item["unit"] = currency_info["unit"]
                    item["currency"] = currency_info["currency"]

        return True

    async def update_formulas(self, formulas, qid, answer_type, manual_currency_info, brief_summary):
        self.add_currency_info(formulas, manual_currency_info)

        base_formulas = {f.label: f for f in self.check()}

        new_formulas = [
            (formula.pop("id"), base_formulas[formula["label"]] + FormulaItem(**formula)) for formula in formulas
        ]

        self.formulas = [item[1] for item in new_formulas]

        await pw_db.execute(
            NewSpecialAnswer.delete().where(
                NewSpecialAnswer.answer_type == f"hkex_manual_{self.name}", NewSpecialAnswer.qid == qid
            )
        )

        formulas = []
        async with pw_db.atomic():
            index = 1
            for _, formula in zip(
                new_formulas,
                map(attr2dict, self.check()),
            ):
                formula["order"] = index
                answer = await pw_db.create(
                    NewSpecialAnswer,
                    qid=qid,
                    data=formula,
                    answer_type=RatioAnswerType.prefix(answer_type) + self.name,
                )
                formulas.append({"id": answer.id, **formula})
                index += 1
        await self.save_summary_result(qid)
        return formulas

    @classmethod
    def _get_currency_unit_from_all_formulas(cls, formulas):
        unit = None
        currency = ""
        for formula in formulas:
            unit = unit or formula.unit
            currency = currency or formula.currency
        return currency, unit

    def check(self) -> List[FormulaItem]:
        formulas = self._init_formulas(self.formulas)
        res = []

        # 从提取结果里找到公式要的section.items
        for formula in formulas:
            for section_item in formula.sections:
                self.collect_items(section_item)

        # 从section.items里找到一个可用的 币种和单位
        ratio_currency, ratio_unit = self._get_currency_unit_from_all_formulas(formulas)

        # 对于当前公式没有单位的，取其他公式的
        self._fix_empty_unit(formulas, ratio_unit)

        # 开始计算
        for formula in formulas:
            for section_item in formula.sections:
                self._update_section_info(section_item)
            if self.update_formula_info(formula, ratio_currency):
                res.append(formula)

        # 后处理
        return self.end_of_check(res)

    @classmethod
    def _fix_empty_unit(cls, formulas, ratio_unit):
        for formula in formulas:
            for section in formula.sections:
                for item in section.items:
                    if (
                        ratio_unit
                        and item.unit.is_dummy
                        and ratio_unit != UnitManager.DEFAULT_VALUE
                        and item.value.value_
                    ):
                        item.value.value_ *= ratio_unit

    @classmethod
    def end_of_check(cls, formulas):
        for formula in formulas:
            for section in formula.sections:
                if not section.scope:
                    continue
                for key in list(section.scope.keys()):
                    if not key.startswith("_"):
                        continue
                    section.scope.pop(key)

        return formulas

    def bind_scope(self, section: SectionItem):
        if section.scope and self.scope_info:
            scope_info = self.scope_info.get(section.scope.get("name")) or None
            if scope_info:
                section.scope.update({**scope_info})
                if "_meta" in scope_info:
                    return scope_info["_meta"], scope_info.get("_answer")
        return None, None

    def collect_items(self, section: SectionItem):
        ext_info, answer_node = self.bind_scope(section)
        ext_info = ext_info or self.ext_info

        miss_scope_file = False
        if section.scope and not ext_info:
            section.items = []
            miss_scope_file = True

        answer_node = answer_node or self.answer_node
        if self.answer_type == "manual":
            answer_node = self.answer_node

        if answer_node:
            # 从提取答案中获取数据
            if not miss_scope_file:
                for path in section.attrs_:
                    for node in answer_node.get(path, defaultdict(dict)).values():
                        data_item = DataItem.parse_node(node, ext_info)
                        data_item.from_attr = path
                        section.items.append(data_item)

            # 按币种分组, 取条目最多的一组币种
            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/620#note_170305
            items = defaultdict(list)
            for item in section.items:
                if item.is_dummy:
                    continue
                # 一个正经的单位中间应该不会出现空格 这个clean_txt的时候去除所有的空格
                currency = clean_txt(item.currency.value_ or item.currency.text or "", remove_blank=True)
                items[currency].append(item)
            sorted_items = sorted(items.values(), key=len, reverse=True)
            if sorted_items:
                section.items = sorted_items[0]

            if self.name in ("ratio2", "ratio5") and len(section.attrs_) > 1:
                # 特殊提取逻辑, 具体见
                # https://mm.paodingai.com/cheftin/pl/3ydfwg1a638qzpdwd6fkjidxjh
                items = {}
                for item in section.items:
                    if item.is_dummy:
                        continue
                    if (
                        self.name == "ratio2"
                        and item.from_attr == "Segment Notes-Impairment"
                        and any("impairment" in f"{v.account.value_ or v.account.text}".lower() for v in items.values())
                    ):
                        # PL有提取到impairment，notes有提取到impairment，直接 segment 的全部过滤掉
                        # https://mm.paodingai.com/cheftin/pl/h9mbjuyasi8bdgj47wjirg98ie
                        # logger.debug(f'{self.name}: {item.account.value_ or item.account.text} is already in items')
                        continue
                    items.setdefault(
                        (
                            f"{item.time_period.value_ or item.time_period.text}|"
                            f"{item.account.value_ or item.account.text}|"
                            f"{item.value.value_ or item.value.text}"
                        ).lower(),
                        item,
                    )
                section.items = list(items.values())
        else:
            # 先拿 后 revise
            if not section.visible:
                # 从可见答案中填充非可见答案
                other_sections = {
                    sec.label: sec
                    for formula in self.formulas
                    for sec in formula.sections
                    if sec.label == section.label and sec.visible
                }
                section.items = other_sections.get(section.label, section).items

            # 从用户提交的答案中获取数据
            if not miss_scope_file:
                for item in section.items:
                    item.revise_attrs(item, ext_info)

    @classmethod
    def _gen_formula_string(cls, formula: Union[FormulaItem, dict], is_value=True):
        formula = pack(formula, FormulaItem)
        values = []
        for section in formula.sections:
            if not section.sub_labels:
                value = (
                    (section.value_ if section.value_ else 0)
                    if is_value
                    else (f"{section.label} {section.date_var}" if section.date_var else section.label)
                )
                values.append(value)
            else:
                values_ = [
                    (data_item.value.value_ if data_item.value.value_ else 0)
                    if is_value
                    else ((data_item.account.text or section.label) + " " + data_item.date_var)
                    for data_item in section.items
                ]
                values.extend(values_)

        format_str = "".join(s.operator_ for s in formula.sections)
        return (
            cls.desc2latex(format_str.format(*(format_amount(v) for v in values)))
            if is_value
            else format_str.format(*values)
        )

    @classmethod
    def _calc_formula_ratio(cls, formula_item):
        values = []
        for section in formula_item.sections:
            if not section.sub_labels:
                values.append(section.value_)
                continue

            values += [item.value.value_ for item in section.items]

        expr = "".join(s.operator_ for s in formula_item.sections).format(*values)
        result = safe_eval(expr)
        logger.debug(f"expr:{expr}. result: {result}")

        return result

    @classmethod
    def _is_invalid_result(cls, formula):
        for section in formula.sections:
            if not section.items:
                return True
            for item in section.items:
                if item.is_dummy:
                    return True
        return False

    def update_formula_info(self, formula: FormulaItem, ratio_currency: str):
        for section in formula.sections:
            if section.sub_labels and len(section.items) != section.args_count:
                return None

        formula.formula_info = self._gen_formula_string(formula)
        try:
            formula.ratio = self._calc_formula_ratio(formula)
        except ZeroDivisionError:
            formula.formula_info = "-"
            logger.warning(f"Float division by zero: {attr2dict(formula.sections[-1])}")
        except SyntaxError as exp:
            formula.formula_info = "-"
            logger.warning(f"{attr2dict(formula.sections[-1])}: {exp}")
        else:
            if "%" in formula.flag_cond:
                formula.ratio_str = f"{formula.ratio * 100:.2f}%"
            else:
                currency = formula.currency or ratio_currency
                formula.ratio_str = f"{currency}{format_amount(formula.ratio)}"
            formula.formula_info += self.desc2latex(f"={formula.ratio_str}")
            if (
                self._is_invalid_result(formula)
                and self.P_COMPARE_OPERATOR.search(formula.flag_cond)
                and self.IGNORE_INVALID_RESULT
                and len(formula.sections) <= 2
            ):
                formula.ratio_str = "-"
                formula.ratio = 0
                formula.red_flag = False

            elif formula.flag_cond:
                currency_info = CurrencyManager().convert_currency_str(formula.flag_cond)
                if currency_info:
                    _, _, flat_cond = currency_info
                    real_ratio = formula.convert_currency(self.ext_info.meta.year_end, ratio_currency)
                    formula.red_flag = safe_eval(f"{real_ratio} {flat_cond}")
                else:
                    formula.red_flag = safe_eval(f"{formula.ratio} {formula.flag_cond.replace('%', '/100')}")
            if formula.red_flag:
                formula.formula_info += self.desc2latex(formula.flag_cond)
            formula.formula_info = "$" + formula.formula_info + "$"

        if all(not sec.items for sec in formula.sections):
            formula.formula_info = ""

        formula.revise_func(formula)
        return formula

    def _update_section_info(self, section: SectionItem):
        # 按需提取所需字段结果
        section.revise_func(section, self.ext_info)
        # 去重
        section.rm_dup_func(section)
        # 计算
        section.calc_func(section)

    def _init_formulas(self, formulas: List[Union[dict, FormulaItem]] | None = None):
        revise_func = getattr(self, "revise_formula", lambda *args, **kwargs: None)
        if formulas is None:
            formulas = self.get_formulas()
        return [
            FormulaItem(**{"revise_func": revise_func, **(attr.asdict(f) if isinstance(f, FormulaItem) else f)})
            for f in formulas
        ]


def group_items_by_date(items: List[DataItem], ext_info: ExtInfo) -> Dict[datetime, List[DataItem]]:
    def which_date(item):
        # Final按照年份来分组
        return (
            item.time_period.value_.year
            if ext_info.meta.doc_type in (DocType.FINAL.value,) and item.time_period.value_
            else item.time_period.value_
        )

    group_by_year = defaultdict(list)
    items.sort(key=lambda x: str(which_date(x)))
    for date, items_ in groupby(items, key=which_date):
        if date is None:
            continue
        group_by_year[date].extend(items_)
    return group_by_year


def group_items_by_account(items: List[DataItem]) -> Dict[datetime, List[DataItem]]:
    group_by_account = defaultdict(list)
    items.sort(key=lambda x: str(x.account.value_))
    for account, items_ in groupby(items, key=lambda x: x.account.value_):
        if account is None:
            continue
        group_by_account[account].extend(items_)
    return group_by_account


def remove_total_assets_duplicate_items(section: SectionItem):
    if not section.items:
        return None

    def get_last_item(items):
        if not items:
            return []
        return items[-1:]

    if section.is_manual:
        # 人工提交答案不做去重操作
        return None

    total_rules = {
        "non_current_assert": P_NON_CURRENT_ASSETS,
        "total_items": P_TOTAL_ASSETS,
        "other_items": P_TOTAL_ASSETS_OTHER,
        "current_assert": P_CURRENT_ASSERT,
    }
    results = {key: [] for key in total_rules}
    for item in section.items:
        for key, regex in total_rules.items():
            text = clean_txt(item.account.value_ or item.account.text, remove_cn_text=True).strip()
            if regex.nexts(text):
                results[key].append(item)
                break

    if results.get("total_items"):
        section.items = get_last_item(results["total_items"]) + get_last_item(results["other_items"])

    elif results.get("current_assert") or results.get("non_current_assert"):
        section.items = (
            get_last_item(results["current_assert"])
            + get_last_item(results["non_current_assert"])
            + get_last_item(results["other_items"])
        )

    elif results.get("other_items"):
        section.items = get_last_item(results["other_items"])
    else:
        total_items = [
            i for i in section.items if (i.account.value_ or clean_txt(i.account.text)).lower().startswith("total")
        ]
        section.items = total_items

    return section.items


def remove_impairment_duplicate_items(section: SectionItem):
    if section.is_manual:
        return
    items = remove_duplicate_items(section)
    # ratio2 中特殊的去重逻辑就依次加到这里
    items = remove_same_account(items)
    items = remove_net_in_pl(items)
    items = remove_receivable_items_ratio2(items)
    section.items = items
    return


def remove_same_account(items):
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/1043#note_254587
    # https://mm.paodingai.com/cheftin/pl/e3ncnmhce3ggprjnj97g1c3ibr
    replace_map = {
        r"(impairment\s)on(\sproperty)": r"\1of\2",  # impairment on property --> impairment of property
        r"(deposit)s": r"\1",  # deposits --> deposit
    }

    mapping = {}
    for item in items:
        date = ""
        if item.time_period.value_:
            date = item.time_period.value_.strftime("%Y-%m-%d")
        key = (
            f"{(item.account.value_ or item.account.text).lower()} "
            f"at {date} = {abs(item.value.value_) or item.value.text}"
        )
        key = P_SPECIAL_SUB_TOTAL.sub("", key)
        for pattern, value in replace_map.items():
            if re.search(pattern, key, re.I):
                key = clean_txt(re.sub(pattern, value, key, flags=re.I))
        if any(is_similar(key, k) for k in mapping):
            continue
        mapping[key] = item

    return list(mapping.values())


def remove_net_in_pl(items):
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/692#note_182458
    # 如果PL的相关科目出现net，notes中有关于这个科目的分项描述时，提取notes中的分项，不提取PL的net值
    pl_items = [item for item in items if item.from_attr == "PL-Impairment"]
    note_items = [item for item in items if item.from_attr != "PL-Impairment"]
    pl_has_net_desc = any(P_NET.nexts(i.account.value_ or i.account.text) for i in pl_items)
    note_has_option = any(P_OPTION_NET.nexts(i.account.value_ or i.account.text) for i in note_items)

    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/1043#note_252292
    neglect_note_has_option = any(P_OPTION_NET_BLACK.nexts(i.account.value_ or i.account.text) for i in note_items)
    note_has_option = note_has_option and not neglect_note_has_option

    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/1043#note_253313
    pl_has_continue = any(P_CONTINUED.nexts(i.account.value_ or i.account.text) for i in pl_items)
    note_has_continue = any(P_CONTINUED.nexts(i.account.value_ or i.account.text) for i in note_items)
    if pl_has_continue or note_has_continue:
        return items

    if pl_has_net_desc and note_has_option:
        return note_items
    return items


def remove_receivable_items_ratio2(items):
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/692#note_180680
    # 科目trade receivables, bond receivable 包含 in respect of (trade|bond) receivables 的情况
    has_summary_desc = any(P_SUMMARY_IMPAIRMENT.nexts(i.account.value_ or i.account.text) for i in items)
    if not has_summary_desc:
        return items
    mapping = {}
    for item in items:
        account = item.account.value_ or item.account.text
        if P_OPTION_IMPAIRMENT.nexts(account):
            continue
        mapping[account] = item
    return list(mapping.values())


def remove_significant_duplicate_items(section: SectionItem):
    if section.is_manual:
        return None
    if len(section.items) == 1:
        return section.items
    if len(section.items) == 2:
        currencies = {item.currency.text for item in section.items}
        if len(currencies) == 2:
            # total asset 取的是最后一个  详见代码   get_last_item(results["total_items"]))
            # 这里取靠右的最后一个
            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6172#note_687412
            first_item = section.items[0]
            second_item = section.items[-1]
            if first_item.currency.box.box_right > second_item.currency.box.box_right:
                section.items = [first_item]
            else:
                section.items = [second_item]
    return section.items


def remove_duplicate_items(section: SectionItem):
    if section.is_manual:
        # 人工提交答案不做去重操作
        return None

    mapping = {}
    for item in section.items:
        date = ""
        if item.time_period.value_:
            date = item.time_period.value_.strftime("%Y-%m-%d")
        key = (
            f"{(item.account.value_ or item.account.text).lower()} "
            f"at {date} = {abs(item.value.value_) or item.value.text}"
        )
        if any(is_similar(key, k) for k in mapping):
            continue
        mapping[key] = item

    section.items = list(mapping.values())
    return section.items


def revise_section(
    section: SectionItem, ext_info: ExtInfo, current_year=True, has_date_var=False, select_date: str | None = None
):
    if all(item.is_dummy for item in section.items):
        section.items = []
    from remarkable.predictor.hkex_predictor.models.impairment import judge_answer

    if not section.is_manual and section.items and any(not item.time_period.is_dummy for item in section.items):
        # 按日期重组 items 的条件:
        # 1. AI提取类(人工答案直出, 不做筛选)
        # 2. 必须要有具体的实际意义的日期内容
        items = group_items_by_date(section.items, ext_info)
        if items:
            date = max(items) if current_year else min(items)
            section.items = items[date]

        else:
            logger.error(f"No FY found: {section.label} {section.attrs_}")

        if ext_info.ratio_type == "ratio2" and section.label == "Impairment":
            valid_items = []
            for item in section.items:
                if judge_answer(item.account.value_.split("|"), [item.value.text]):
                    item.account.text = item.account.text.split("|")[-1]
                    valid_items.append(item)
            section.items = valid_items

    date_var = select_date
    if section.scope:
        if ext_info.meta.doc_type in (DocType.Q1.value, DocType.Q3.value):
            date_var = "Last Reported Value"  # Xi-1/Yi(Q1/3) -> "Last Reported Value"
        elif section.scope["name"] == "Xi-1":
            date_var = get_date_var(DocType.FINAL.value)
        elif section.scope["name"] == "Yi":
            date_var = get_date_var(DocType.INTERIM.value, current_year=True)
        else:
            raise Exception(f"Unsupported scope: {section.scope}")

    elif not select_date:
        date_var = get_date_var(ext_info.meta.doc_type, current_year)

    if not has_date_var:
        if date_var and not section.label.endswith(date_var):
            section.label = " ".join((section.label, date_var))
    else:
        section.date_var = date_var
        for item in section.items:
            item.date_var = date_var


def get_empty_items(ext_info, section):
    res = []
    for sub_info in section.sub_labels:
        data_item = DataItem.dummy_data_item(fid=ext_info.meta.fid)
        if sub_info.get("select_date"):
            data_item.date_var = sub_info["select_date"]
        else:
            data_item.date_var = get_date_var(ext_info.meta.doc_type, sub_info["type"] == "last")
        res.append(data_item)
    return res


def revise_group_section(section: SectionItem, ext_info: ExtInfo):
    empty_items = get_empty_items(ext_info, section)
    if all(item.is_dummy for item in section.items):
        section.items = empty_items

    if not section.sub_labels or section.is_manual:
        return

    if not all(not item.time_period.is_dummy for item in section.items):
        section.items = empty_items
        return

    temp_items = []
    account_groups = group_items_by_account(section.items)
    if account_groups:
        for sub_info in section.sub_labels:
            if sub_info["name"] in account_groups:
                date_groups = group_items_by_date(account_groups[sub_info["name"]], ext_info)

                if date_groups:
                    if sub_info["type"] == "first":
                        data_item = date_groups[min(date_groups)][0]
                        temp_items.append(data_item)

                        if sub_info.get("select_date"):
                            data_item.date_var = sub_info["select_date"]
                        else:
                            data_item.date_var = get_date_var(ext_info.meta.doc_type, False)
                    elif sub_info["type"] == "last":
                        data_item = date_groups[max(date_groups)][0]
                        temp_items.append(data_item)
                        data_item.date_var = get_date_var(ext_info.meta.doc_type, True)
        if temp_items:
            section.items = temp_items
    if not temp_items:
        section.items = empty_items
    if len(temp_items) != len(section.sub_labels):
        section.items = empty_items


class SignificantInvestmentConverter(RatioConverter):
    checkers = {}

    def convert(self, ext_info: ExtInfo, scope_info: Dict, answer_type: str) -> Dict[str, List[FormulaItem]]:
        self._load_checkers()
        results = {}
        for ratio, cls in self.checkers.items():
            if not self.answer_node:
                continue
            try:
                results[ratio] = cls(self.answer_node, ext_info, scope_info, answer_type).check() or []
            except NotImplementedError:
                logger.warning(f"{self._sub_checker_name(ratio)} not implemented yet.")
        return results

    @classmethod
    def _load_checkers(cls):
        from remarkable.converter.hkex.ratio4_conv import (
            IndividualSignificantInvestmentChecker,
        )

        # cls.checkers["sum_ratio4"] = SignificantInvestmentChecker
        cls.checkers["ratio4"] = IndividualSignificantInvestmentChecker


class PollGMLConverter(BaseConverter, ConverterMaker):
    P_TOTAL_SHARE_CLASS = re.compile(r"(total|^issued\s*shares?$)", re.I)
    SHAREE_CLASS_MAP = {
        "H": MatchMulti.compile(r"(HK|Hong\sKong|H)\s*Shares?", r"^(Class)?(HK|Hong\sKong|H)$", operator=any),
        "A": MatchMulti.compile(r"(A|RMB)\s*Shares?", r"^(Class)?(A|RMB)$", operator=any),
        "B": MatchMulti.compile(r"(B)\s*Shares?", r"^(Class)?B$", operator=any),
        "Domestic Unlisted": MatchMulti.compile(r"Domestic\sUnlisted\s*Shares?", r"^Domestic\sUnlisted$", operator=any),
        "Domestic": MatchMulti.compile(r"domestics?\s*Shares?", r"^domestics$", operator=any),
        "Unlisted": MatchMulti.compile(
            rf"(Un|Non){R_MIDDLE_DASH}?listed\s*Shares?", rf"^(Un|Non){R_MIDDLE_DASH}?listed$", operator=any
        ),
    }

    @classmethod
    async def run_pipe(cls, question: NewQuestion, mold: NewMold):
        if not question.preset_answer:
            logger.warning(f"question {question.id} no preset answer")
        poll_question = await NewQuestion.find_by_fid_mid(question.fid, special_mold.v6_poll_id)
        poll_meta = await pw_db.first(POLLMeta.select().where(POLLMeta.poll_fid == question.fid))
        mr_question = await NewQuestion.find_by_fid_mid(poll_meta.mr_fid, special_mold.v6_1_mr_id)
        agm_question = await NewQuestion.find_by_fid_mid(poll_meta.agm_fid, special_mold.v6_1_agm_id)

        poll_group_ans = cls.load_answer_node(question.preset_answer).to_dict()
        # 按 class 将 poll 或 mr 答案分成 total 和 classes
        ans_by_class = {"classes": []}
        if total_shares := poll_group_ans.get(PollGMLRules.TOTAL_SHARE):
            cls.find_total(ans_by_class, total_shares, poll_question)
        elif mr_question:
            mr_group_ans = cls.load_answer_node(mr_question.preset_answer).to_dict()
            if mr_total_shares := mr_group_ans.get(PollGMLRules.TOTAL_SHARE):
                cls.find_total(ans_by_class, mr_total_shares, mr_question)

        # 把 treasury shares 和 percentage 分配 total share 下面各组答案中
        if treasury_shares := poll_group_ans.get(PollGMLRules.TREASURY_SHARE):
            cls.set_other_field_to_total(treasury_shares, PollGMLRules.TREASURY_SHARE, ans_by_class, poll_question)
        elif mr_question:
            mr_group_ans = cls.load_answer_node(mr_question.preset_answer).to_dict()
            cls.set_other_field_to_total(
                mr_group_ans.get(PollGMLRules.TREASURY_SHARE, []),
                PollGMLRules.TREASURY_SHARE,
                ans_by_class,
                mr_question,
            )

        if percentages := poll_group_ans.get(PollGMLRules.PERCENTAGE):
            cls.set_other_field_to_total(percentages, PollGMLRules.PERCENTAGE, ans_by_class, poll_question)
        elif agm_question:
            if agm_answer_node := cls.load_answer_node(agm_question.preset_answer):
                agm_group_ans = agm_answer_node.to_dict()
                cls.set_other_field_to_total(
                    agm_group_ans.get(PollGMLRules.PERCENTAGE, []), PollGMLRules.PERCENTAGE, ans_by_class, agm_question
                )
        else:
            logger.warning(f"Poll file {question.fid} has not percentage answer")

        # 确定如何计算公式有 total 取 total 计算，没有 total 用 classes 计算
        if ans_by_class.get("total") and ans_by_class["total"].get(PollGMLRules.PERCENTAGE):
            formula, limit_value = cls.calc_limit(poll_question, [ans_by_class["total"]])
            ans_by_class["enum"] = AnswerValueEnum.PS.value if limit_value > 0 else AnswerValueEnum.ND.value
            ans_by_class["formula"] = formula
        elif ans_by_class.get("classes"):
            formula, limit_value = cls.calc_limit(poll_question, ans_by_class["classes"])
            ans_by_class["enum"] = AnswerValueEnum.PS.value if limit_value > 0 else AnswerValueEnum.ND.value
            ans_by_class["formula"] = formula
        else:
            ans_by_class["enum"] = AnswerValueEnum.ND.value
            ans_by_class["formula"] = ""
            ans_by_class.setdefault("total", cls.empty_total_answer(poll_question))

        predict_gml = PollGMLSubRuleSchema(**{PollGMLRules.GML: ans_by_class})
        predict_gml.approval_date = PollPassedDateSchema()
        predict_gml.resolution_passed = PollResolutionSchema()
        if resolution_ans := poll_group_ans.get(PollGMLRules.RESOLUTION_PASSED):
            if resolution_ans[0].get(PollGMLRules.RESOLUTION):
                predict_gml.resolution_passed.resolution = cls.answer_to_schema(
                    resolution_ans[0], PollGMLRules.RESOLUTION, poll_question
                )
            if resolution_ans[0].get(PollGMLRules.PASSED):
                predict_gml.resolution_passed.pass_or_not = cls.answer_to_schema(
                    resolution_ans[0], PollGMLRules.PASSED, poll_question
                )
                if resolution_ans[0][PollGMLRules.PASSED].enum in (AnswerValueEnum.Yes.value, AnswerValueEnum.No.value):
                    predict_gml.resolution_passed.enum = resolution_ans[0][PollGMLRules.PASSED].enum
        if passed_date_ans := poll_group_ans.get(PollGMLRules.PASSED_DATE):
            predict_gml.approval_date.passed_date = cls.answer_to_schema(
                poll_group_ans, PollGMLRules.PASSED_DATE, poll_question
            )
            if passed_date := passed_date_ans.meta.get("passed_date"):
                predict_gml.approval_date.passed_date.text = passed_date
                predict_gml.approval_date.passed_date.value = passed_date
            predict_gml.approval_date.enum = passed_date_ans.enum

        result_data = {
            "fid": question.fid,
            "qid": poll_question.id,
            "predict_gml": predict_gml.model_dump(by_alias=True),
        }
        # 结果存到 poll_gml
        await PollGML.insert_or_update(conflict_target=[PollGML.qid], **result_data)

    @classmethod
    def empty_total_answer(cls, question):
        empty_ans = PollGMLFieldAnswerSchema(qid=question.id, fid=question.fid, text="", value="")
        return {
            PollGMLRules.TOTAL_SHARE: empty_ans,
            PollGMLRules.SHARE_CLASS: empty_ans,
            PollGMLRules.TREASURY_SHARE: empty_ans,
            PollGMLRules.PERCENTAGE: empty_ans,
        }

    @classmethod
    def set_other_field_to_total(cls, ans_list, ans_field, ans_by_class, question):
        class_ans_map = {}
        for percentage in ans_list:
            share_class_text = (
                percentage[PollGMLRules.SHARE_CLASS].plain_text if percentage[PollGMLRules.SHARE_CLASS] else ""
            )
            if cls.P_TOTAL_SHARE_CLASS.search(share_class_text):
                class_ans_map["Total"] = cls.answer_to_schema(percentage, PollGMLRules.VALUE, question)
            elif share_class := cls.extract_share_class(share_class_text):
                class_ans_map[share_class] = cls.answer_to_schema(percentage, PollGMLRules.VALUE, question)
            elif len(ans_list) == 1:
                # 只有一组且匹配不到任何的 Class 认为是 Total
                class_ans_map["Total"] = cls.answer_to_schema(percentage, PollGMLRules.VALUE, question)

        if len(class_ans_map) == 1 and "Total" in class_ans_map:
            # 把 percentage/treasury 分配到每个 total_share 中
            if ans_by_class.get("total"):
                ans_by_class["total"][ans_field] = class_ans_map["Total"]
            for share_class in ans_by_class["classes"]:
                share_class[ans_field] = class_ans_map["Total"]
        else:
            for k, v in class_ans_map.items():
                for share_class in ans_by_class["classes"]:
                    if cls.is_same_share_class(k, share_class[PollGMLRules.SHARE_CLASS].text):
                        share_class[ans_field] = v

    @classmethod
    def calc_limit(cls, poll_question, share_values):
        values, formulas = [], []
        for share_value in share_values:
            total_shares = share_value.get(PollGMLRules.TOTAL_SHARE)
            percentage = share_value.get(PollGMLRules.PERCENTAGE)
            if not percentage or not total_shares:
                continue
            share_value.setdefault(
                PollGMLRules.TREASURY_SHARE,
                PollGMLFieldAnswerSchema(
                    **{
                        "qid": poll_question.id,
                        "fid": poll_question.fid,
                        "data": [],
                        "text": "0",
                        "value": 0,
                    }
                ),
            )
            treasury_shares = share_value[PollGMLRules.TREASURY_SHARE]
            limit_value = (total_shares.value - treasury_shares.value) * percentage.value
            values.append(limit_value)
            formulas.append(rf"({total_shares.text} - {treasury_shares.text}) * {percentage.text}")
        if values and formulas:
            sum_value = sum(values)
            return f"{' + '.join(formulas)} = {sum_value:,.2f}", sum_value
        else:
            return "", 0

    @classmethod
    def find_total(cls, ans_by_class, total_shares, question):
        for ans_item in total_shares:
            share_class = ans_item["Relevant share class"]
            total_ans = {
                PollGMLRules.TOTAL_SHARE: cls.answer_to_schema(ans_item, PollGMLRules.VALUE, question),
                PollGMLRules.SHARE_CLASS: cls.answer_to_schema(ans_item, PollGMLRules.SHARE_CLASS, question),
            }
            # 原始答案转成schema
            if cls.P_TOTAL_SHARE_CLASS.search(share_class.plain_text):
                ans_by_class["total"] = total_ans
            elif cls.extract_share_class(share_class.plain_text):
                ans_by_class["classes"].append(total_ans)
            elif len(total_shares) == 1:
                ans_by_class["total"] = total_ans

    @classmethod
    def answer_to_schema(cls, answer, field, question):
        text = value = AnswerItem.text_only(answer[field])
        if field == PollGMLRules.VALUE:
            text, value = cls.str_to_number(text)
        return PollGMLFieldAnswerSchema(
            **{
                "qid": question.id,
                "fid": question.fid,
                "data": answer[field].data,
                "text": text,
                "value": value,
            }
        )

    @classmethod
    def extract_share_class(cls, text):
        for class_name, class_pattern in cls.SHAREE_CLASS_MAP.items():
            if class_pattern.search(text):
                return class_name
        return None

    @classmethod
    def is_same_share_class(cls, class1, class2):
        for class_name, class_pattern in cls.SHAREE_CLASS_MAP.items():
            if class_pattern.search(class1):
                class1 = class_name
            if class_pattern.search(class2):
                class2 = class_name
        return class1 == class2

    @classmethod
    def str_to_number(cls, text):
        s = text.strip()
        if not s:
            return s, 0
        is_percent = False
        if s.endswith("%"):
            is_percent = True
            s = s[:-1].strip()
        s = re.sub("[,，]", "", s)
        try:
            if "." in s:
                num = float(s)
            else:
                num = int(s)
        except ValueError:
            return "0", 0
        if is_percent:
            return f"{num}%", num / 100.0
        return f"{num:,}", num
