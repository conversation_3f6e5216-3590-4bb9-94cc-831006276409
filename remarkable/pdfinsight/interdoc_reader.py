from __future__ import annotations

from collections import defaultdict
from dataclasses import dataclass, field
from functools import cached_property
from operator import attrgetter
from typing import (
    Any,
    ClassVar,
    Dict,
    Generic,
    Iterable,
    Iterator,
    List,
    Literal,
    Protocol,
    Tuple,
    Type,
    TypeAlias,
    TypeVar,
    Union,
    get_args,
    runtime_checkable,
)

from interdoc import protocol
from interdoc.base import ALL_TYPES, Box, Char, Position
from interdoc.chapter import ChapterNode, RootChapter
from interdoc.collection import Interdoc
from interdoc.collection import PageDoc as _PageDoc
from interdoc.converter import Converter
from interdoc.element import Cell, Graphic, Para
from interdoc.element import Table as _Table
from interdoc.loader import CommonLoader
from interdoc.proxy import AutoProxy

from remarkable.common.constants import PDFInsightClassEnum

__all__ = [
    "Para",
    "TPara",
    "RowPara",
    "ColPara",
    "Cell",
    "Table",
    "Graphic",
    "Element",
    "InterdocReader",
    "BindTypeMixin",
]

from remarkable.pdfinsight.reader_util import fill_middle_page_title, page_first_title

T = TypeVar("T")


def get_union_args(tp: Any) -> Tuple[Type[Any], ...]:
    if hasattr(tp, "__origin__") and tp.__origin__ is Union:
        return tuple(arg for t in get_args(tp) for arg in get_union_args(t))
    return (tp,)


@runtime_checkable
class GenericLike(Protocol[T]):
    __orig_class__: Any
    __orig_bases__: Any


def get_bind_types(obj: GenericLike[T]) -> Tuple[Type[T], ...]:
    return get_args(obj.__orig_class__)


class BindTypeMixin(Generic[T]):
    @cached_property
    def bind_type(self: GenericLike[T]) -> Type[T]:
        try:
            return get_bind_types(self)[0]
        except AttributeError:
            for cls in self.__orig_bases__:
                return get_args(cls)[0]

    @cached_property
    def types(self) -> Tuple[Type, ...]:
        return get_union_args(self.bind_type)


@dataclass
class TPara:
    type: ClassVar[Literal["row", "col", "cell"]]
    index: int
    serial: int
    cells: list[Cell]
    merged_cells: list[Cell]

    @cached_property
    def text(self) -> str:
        if len(self.cells) == 1:
            return self.cells[0].text
        return "\t".join(cell.text for cell in self.cells)

    @cached_property
    def chars(self) -> tuple[Char, ...]:
        if len(self.cells) == 1:
            return self.cells[0].chars
        return tuple(char for cell in self.cells for child_cell in cell.children for char in child_cell.chars)

    @cached_property
    def page_info(self) -> Dict[int, tuple[Box, ...]]:
        if len(self.cells) == 1:
            return self.cells[0].page_info
        page_info: Dict[int, List[Box]] = defaultdict(list)
        for cell in self.cells:
            for child_cell in cell.children:
                page_info[child_cell.page].append(child_cell.box)

        for page, (first_box, *other_boxes) in page_info.items():
            boxes = []
            for box in other_boxes:
                if first_box.in_same_col(box):
                    first_box = merge_box(first_box, box)
                else:
                    first_box = box
                    boxes.append(first_box)
            if not boxes or boxes[-1] != first_box:
                boxes.append(first_box)
            page_info[page] = boxes

        return {page: tuple(boxes) for page, boxes in page_info.items()}

    @property
    def real_cells(self):
        for cell in self.cells:
            if self.type == "row" and cell.row != self.serial:
                continue
            if self.type == "col" and cell.col != self.serial:
                continue
            yield cell


class RowPara(TPara):
    type: ClassVar[str] = "row"


class ColPara(TPara):
    type: ClassVar[str] = "col"


CP = TypeVar("CP", bound=TPara)


@dataclass
class TableIterator(BindTypeMixin[CP]):
    table: _Table

    @cached_property
    def rows(self):
        new_rows = [list(self.table.rows[r]) for r in range(self.table.row_count)]
        merged_cells_by_row = defaultdict(list)
        for (row, col), *subs in self.table.merged:
            merged_cells_by_row[row].append(self.table.cells[Position(row, col)])
            for sub_row, _ in subs:
                merged_cells_by_row[sub_row].append(self.table.cells[Position(row, col)])
                if sub_row == row:
                    continue
                new_rows[sub_row].append(self.table.cells[Position(row, col)])

        return [
            RowPara(
                index=self.table.index,
                serial=i,
                cells=sorted(row, key=attrgetter("col")),
                merged_cells=merged_cells_by_row[i],
            )
            for i, row in enumerate(new_rows)
        ]

    @cached_property
    def cols(self):
        new_cols = [set(self.table.cols[c]) for c in range(self.table.col_count)]
        merged_cells_by_col = defaultdict(list)
        for (row, col), *subs in self.table.merged:
            merged_cells_by_col[col].append(self.table.cells[Position(row, col)])
            for _, sub_col in subs:
                merged_cells_by_col[sub_col].append(self.table.cells[Position(row, col)])
                if sub_col == col:
                    continue
                new_cols[sub_col].add(self.table.cells[Position(row, col)])

        return [
            ColPara(
                index=self.table.index,
                serial=i,
                cells=sorted(col, key=attrgetter("row")),
                merged_cells=merged_cells_by_col[i],
            )
            for i, col in enumerate(new_cols)
        ]

    def __iter__(self) -> Iterator[CP]:
        if self.bind_type is RowPara:
            return iter(self.rows)
        elif self.bind_type is ColPara:
            return iter(self.cols)
        return iter(())


class Table(AutoProxy[_Table], protocol.Table, BindTypeMixin[CP]):
    @cached_property
    def by_row(self):
        return TableIterator[RowPara](self.schema)

    @cached_property
    def by_col(self):
        return TableIterator[ColPara](self.schema)


@dataclass
class PageDoc(AutoProxy[_PageDoc], protocol.PageDoc):
    tables: List[Table] = field(default_factory=list)


Element: TypeAlias = Table | Para | Graphic


@dataclass
class InterdocReader(AutoProxy[Interdoc], protocol.Interdoc):
    pages: List[PageDoc] = field(default_factory=list)
    tables: List[Table] = field(default_factory=list)

    def __post_init__(self):
        """
        修复：个别文档页眉页脚的类型是PARAGRAPH
        http://100.64.0.105:55647/#/project/remark/244333?treeId=5616&fileId=67283&schemaId=28&projectId=43974&schemaKey=L(b)-Shareholders'%20communication%20policy
        """
        for paragraph in self.schema.page_headers:
            paragraph.type = PDFInsightClassEnum.PAGE_HEADER.value
        for paragraph in self.schema.page_footers:
            paragraph.type = PDFInsightClassEnum.PAGE_FOOTER.value

        # 需要覆盖interdoc库中定义的元素块类型时, 需要手动重新填充对应的元素块
        self.tables.extend(Table(table) for table in self.schema.tables)
        self.pages.extend(PageDoc(page) for page in self.schema.pages)
        for page in self.pages:
            page.tables.clear()
        for table in self.tables:
            for page in table.page_info:
                self.pages[page].tables.append(table)

    @classmethod
    def from_path(cls, path: str) -> "InterdocReader":
        from interdoc.schema import InterdocSchema

        origin = CommonLoader(path).load()
        converted = Converter(origin).__call__()
        return cls(InterdocSchema.model_validate(converted).root)

    @cached_property
    def root(self) -> RootChapter:
        root = RootChapter()
        chapter_by_index: Dict[int, ChapterNode] = {
            chapter.index: ChapterNode(schema=chapter, parent=root) for chapter in self.chapters
        }  # type:ignore
        for chapter in self.chapters:
            if chapter.parent_id == -1:
                root.add_child(chapter_by_index[chapter.index])
            else:
                chapter_by_index[chapter.index].parent = chapter_by_index[chapter.parent_id]
            root.__index_map__[chapter.index] = chapter_by_index[chapter.index]

        return root

    @cached_property
    def __element_map__(self) -> Dict[int, Element]:
        return {
            child.index: element
            for type_ in ALL_TYPES
            for element in getattr(self, type_)
            for child in element.children
        }

    @cached_property
    def chapter_by_elem(self) -> Dict[int, ChapterNode]:
        return {chapter.element: chapter for chapter in self.root.follow_nodes}

    @cached_property
    def chapter_titles(self) -> set[str]:
        return {chapter.title for chapter in self.root.follow_nodes}

    @cached_property
    def page_chapter_from_first_para(self) -> dict[int, str]:
        page_first = {}
        for page in self.pages:
            if not page.paragraphs or len(page.paragraphs[0].page_info) > 1:
                continue
            if fake_title := page_first_title(page.page_headers + page.paragraphs):
                page_first[page.page] = fake_title
        # 补充相邻页的标题
        fill_middle_page_title([page.page for page in self.pages], page_first)
        return page_first

    @cached_property
    def max_index(self):
        return max(self.__element_map__)

    def find_by_indices(self, *indices: int) -> List[Element]:
        return sorted(self.__element_map__[index] for index in set(indices) if index in self.__element_map__)

    def lazy_find_by_indices(self, *indices: int) -> Iterable[Element]:
        for index in sorted(set(indices)):
            if element := self.__element_map__.get(index):
                yield element

    def find_by_index(self, index: int) -> Element | None:
        return self.__element_map__.get(index)

    def lazy_find_chapters(self, index: int):
        """获取元素块的章节"""
        for node in self.root.follow_nodes:
            if node.start > index:
                break
            if node.start <= index < node.end:
                yield node


def merge_box(*boxes: Box) -> Box:
    lefts, tops, rights, bottoms = [], [], [], []
    for box in boxes:
        lefts.append(box.left)
        tops.append(box.top)
        rights.append(box.right)
        bottoms.append(box.bottom)
    return Box(min(lefts), min(tops), max(rights), max(bottoms))
