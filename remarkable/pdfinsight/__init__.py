import re
from collections import OrderedDict
from difflib import SequenceMatcher
from functools import lru_cache
from typing import List, Pattern, Union

from remarkable.common.common_pattern import P_CHAPTER_PREFIX, R_CONTINUED, R_MIDDLE_DASHES
from remarkable.common.util import clean_txt, is_in_range

P_CHAPTER_CLEAR = re.compile(r"[()*+]")
# 一些文件的章节标题提取到了页码，可能在前也可能在后，需要处理一下
# https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/3168#note_493677
# TODO 后续视情况考虑1.1. 1.2 类似场景
P_CHAPTER_BORDER_NUM = re.compile(
    rf"\s*([{R_MIDDLE_DASHES}(（]\s*{R_CONTINUED})?(?<!\d)\d{{1,3}}\s*$|^\s*\d{{1,3}}(?![{R_MIDDLE_DASHES}\d])\s*", re.I
)


@lru_cache()
def clear_syl_title(title, *, remove_cn_text=False, remove_blank=False, remove_bound_num=False, remove_prefix=False):
    """TODO: 英文年报章节特征待总结"""
    title = clean_txt(title, remove_blank=remove_blank, remove_cn_text=remove_cn_text)
    if remove_bound_num:
        title = P_CHAPTER_BORDER_NUM.sub("", title).strip()
    if remove_prefix:
        title = P_CHAPTER_PREFIX.sub("", title).strip()
    title = P_CHAPTER_CLEAR.sub("", title).strip()
    return title


class Syllabus:
    def __init__(self, syllabuses):
        self.syllabuses = syllabuses
        self.syllabus_dict = {syl["index"]: syl for syl in syllabuses}
        self.elt_syllabus_dict = {syl["element"]: syl for syl in syllabuses if "element" in syl}

    def is_syllabus_elt(self, element):
        return element.get("index") in self.elt_syllabus_dict

    def find_by_elt_index(self, index):
        """Unique element index"""
        sylls = [s for s in self.syllabus_dict.values() if is_in_range(index, s["range"])]
        return sorted(sylls, key=lambda s: s["index"])

    def full_syll_path(self, syll):
        sylls = []
        cursor = syll
        while cursor:
            sylls.insert(0, cursor)
            cursor = self.syllabus_dict.get(cursor["parent"])
        return sylls

    def get_root_syllabus(self, syllabus):
        parent = self.syllabus_dict.get(syllabus["parent"])
        if not parent:
            return syllabus
        return self.get_root_syllabus(parent)

    def find_by_index(self, index):
        syll = self.syllabus_dict.get(index)
        return self.full_syll_path(syll)

    def find_by_pattern(
        self,
        patterns: List[Union[Pattern, str]],
        candidates: List[int] | None = None,
        order_by="index",
        reverse=False,
        clean_func=None,
    ):
        """
        patterns 支持 正则、字符串 两种模式：
        正则：正则匹配
        字符串：相似度超过 60%
        """

        def match(pattern: Union[Pattern, str], name: str):
            if isinstance(pattern, re.Pattern):
                return pattern.search(name)
            if isinstance(pattern, str):
                return SequenceMatcher(None, pattern, name).ratio() > 0.6
            return False

        if not patterns:
            return []
        res = []
        head_p, *tails_p = patterns
        if not candidates:
            syllabuses = list(self.syllabus_dict.values())
        else:
            syllabuses = [self.syllabus_dict[i] for i in candidates if i in self.syllabus_dict]
        for syllabus in sorted(syllabuses, key=lambda x: x[order_by], reverse=reverse):
            cleaned_title = clean_func(syllabus["title"]) if clean_func else clean_txt(syllabus["title"])
            if not match(head_p, cleaned_title):
                continue
            if not tails_p:
                # 只有一个pattern取当前章节
                res.append(syllabus)
            elif not syllabus["children"]:
                continue
            else:
                # 多个pattern说明是按父子关系严格匹配, 取后续子章节
                tails = self.find_by_pattern(tails_p, candidates=syllabus["children"], clean_func=clean_func)
                if tails:
                    res.extend(tails)
        return res

    def find_by_clear_title(self, title, order_by="index", reverse=False, multi=False, equal_mode=False):
        res = []
        for syl in sorted(self.syllabus_dict.values(), key=lambda x: x[order_by], reverse=reverse):
            condition = (
                clear_syl_title(syl["title"]) == title
                if equal_mode
                else re.search(title, clear_syl_title(syl["title"]))
            )
            if condition:
                res.append(syl)
                if not multi:
                    break
        return res

    def find_sylls_by_name(self, names, candidates=None):
        res = []
        name = names[0]
        if not candidates:
            candidates = sorted(self.syllabus_dict.keys())
        for syll_idx in candidates:
            syll = self.syllabus_dict[syll_idx]
            if name == syll["title"]:
                res.append(syll)
                if len(names) > 1:
                    tails = self.find_sylls_by_name(names[1:], candidates=syll["children"])
                    if not tails:
                        return []
                    res.extend(tails)
        return res

    @staticmethod
    def syl_outline(syllabus, pdfinsight, include_title=False):
        """
        获取章节外框
        """
        elements = OrderedDict()
        start, end = syllabus["range"]
        if include_title:  # 是否包含章节标题
            elt_type, elt = pdfinsight.find_element_by_index(start)
            if elt_type == "PARAGRAPH":
                elements.setdefault(elt["page"], []).append(elt)
        element_indexes = set()
        for idx in range(start + 1, end):
            elt_type, elt = pdfinsight.find_element_by_index(idx)
            if elt and elt_type not in ["PAGE_HEADER", "PAGE_FOOTER"] and elt["index"] not in element_indexes:
                elements.setdefault(elt["page"], []).append(elt)
                if elt.get("page_merged_paragraph"):
                    for elt_index in elt["page_merged_paragraph"]["paragraph_indices"]:
                        element_indexes.add(elt_index)
                else:
                    element_indexes.add(elt["index"])
        page_box = []
        for page, elts in elements.items():
            outline = [
                min(e["outline"][0] for e in elts),
                min(e["outline"][1] for e in elts),
                max(e["outline"][2] for e in elts),
                max(e["outline"][3] for e in elts),
            ]
            page_box.append(
                {
                    "page": page,
                    "outline": outline,
                    "text": "\n".join([e.get("text") for e in elts if e.get("text")]),  # 添加完整的段落text
                    "elements": elts,
                }
            )
            for elt in elts:
                page_merged_paragraph = elt.get("page_merged_paragraph", {})
                if page_merged_paragraph:
                    page_box.append(
                        {
                            "page": page_merged_paragraph["page"],
                            "outline": page_merged_paragraph["outline"],
                            "text": "",  # 上面已经添加完整的段落text 这里不再添加
                            "elements": [elt],
                        }
                    )
        return page_box

    @staticmethod
    def is_valid_syllabus(syllabus_id):
        return not (syllabus_id is None or syllabus_id == -1)
