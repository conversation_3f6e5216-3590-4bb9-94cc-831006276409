import logging
import re
from collections import Counter, OrderedDict, defaultdict
from dataclasses import dataclass, field
from datetime import datetime
from functools import cached_property
from itertools import groupby
from typing import Dict, List, Match, Tuple, Union

import numpy as np

from remarkable.common.common import get_style_of_raw_cell, is_para_elt, page_merged_table_indices
from remarkable.common.common_pattern import (
    P_MIDDLE_DASH,
    P_NOTES,
    P_ONLY_NOTES,
    R_ASSETS,
    R_CN_SPACE,
    R_CN_SPACE_COLON,
    R_CURRENCY,
    R_CURRENT_ASSETS,
    R_MIDDLE_DASH,
    R_MIDDLE_DASHES,
    R_NON_CURRENT_ASSETS,
    R_TOTAL_ASSETS,
)
from remarkable.common.constants import PDFInsightClassEnum, TableType
from remarkable.common.element_util import get_avg_width, outline_left_of_last_line
from remarkable.common.pattern import PatternCollection
from remarkable.common.util import (
    clean_txt,
    has_chinese_chars,
    has_english_chars,
    index_in_space_string,
    kmeans,
    remove_chinese_chars,
)
from remarkable.converter.utils import CN_NUM_MAP
from remarkable.pdfinsight.reader import PdfinsightReader
from remarkable.pdfinsight.reader_table import PdfinsightTable, PdfinsightTableCell
from remarkable.pdfinsight.reader_util import R_INVALID_TBL_TITLES
from remarkable.plugins.predict.common import is_table_elt, normalize_words
from remarkable.predictor.common_pattern import (
    CURRENCY,
    DATE_EN_PATTERN,
    DATE_PATTERN,
    PC_ONLY_DATE,
    R_EN_MONTH,
    R_PERCENT,
    UNIT_PATTERN,
)
from remarkable.predictor.schema_answer import (
    CharResult,
    ElementResult,
    TitleCharResult,
)

sub_option_pattern = PatternCollection([r"^[—–-]"])

# hkex schema 26 and 24 后续schema的表格title可能需要修改这个
TABLE_TITLE_PATTERNS = PatternCollection(
    [
        r"CONSOLIDATED|PROFIT|LOSS|GAIN|TAX|REVENUE|EXPENSE|INCOME|SEGMENT|TRADE|ITEM|POSITION|OPERATING|COSTS",
        r"Consolidated|Profit|Loss|Gain|Tax|Revenue|Expense|Income|Segment|Trade|Item|Position|Operating|Costs",
        r"other\s?comprehe?nsive income",
        r"^C ondes conslidate staemn ofprofit$",
        r"Other revenue and other net \(loss\)/income",  # 49110
        r"ESG|REPORT|GUIDE|INDEX|CONTENT",  # esg
        r"turnover",  # esg
        r"hazardous",  # esg
        r"TABLE OF KEY",  # esg
        r"Overview",  # esg
        r"ENVIRONMENTAL KEY|PERFORMANCE|INDICATORS",  # esg
        r"category|percentage|employee|train|KPI",  # esg
        r"Indicators",  # esg
        r"MATERIALITY ASSESSMENT",  # esg
        r"Employment",  # esg
        r"Workforce",  # esg
        r"Appendix|GRI|Content Index",  # esg gri
    ]
)

INVALID_TABLE_TITLE = PatternCollection(R_INVALID_TBL_TITLES, flags=re.I)
TITLE_NEGLECT_PATTERNS = PatternCollection(
    [
        *R_INVALID_TBL_TITLES,
        r"^Hong Kong",
        r"^[(]?Expressed in Hong Kong dollars",
        r"^\([\w]\)\s",
        r"as follows[:：]$",
        r"^as at\s\d+",
    ],
    re.I,
)
P_AS_FOLLOW = re.compile(r"[:：]$", re.I)

TOTAL_ASSETS_PATTERN = [
    # 中文在后
    *[rf"^{r}{R_CN_SPACE_COLON}$" for r in [R_NON_CURRENT_ASSETS, R_CURRENT_ASSETS, R_TOTAL_ASSETS, R_ASSETS]],
    # 中文在前
    *[rf"^{R_CN_SPACE}{r}[:：\s]*$" for r in [R_NON_CURRENT_ASSETS, R_CURRENT_ASSETS, R_TOTAL_ASSETS, R_ASSETS]],
]
P_TOTAL_ASSETS = PatternCollection(TOTAL_ASSETS_PATTERN, flags=re.I)
# 资产负债表中常见的subtitle行，例如: current assets, current liabilities 等
R_SUB_TITLES = [
    r"(ASSETS?|EQUITY|LIABILITY|LIABILITIES|REVENUES?)",
    r"current\s*(assets?|liabilit(y|ies))",
    rf"non[{R_MIDDLE_DASHES}\s]?cu\s*rr?ent",
    rf"non[{R_MIDDLE_DASHES}\s]?cu\s*rr?ent\s*(assets?|liabilit(y|ies))",
    r"capital\s*and\s*(reserve|deficit)s?",
    r"(EQUITY|assets) AND LIABILIT(Y|IES)",
    r"revenue from principal",
    r"(depreciation|amorti[sz]ation|Impairment loss)",
    r"Provision for warranty",
    r"Inventory written downs to net realisable value",
    r"Loss for the period attributable",
    r"Continuing operation",
    r"Discontinued operation",
]
P_COMMON_SUBTITLES = PatternCollection(
    [
        # 中文在后
        *[rf"^{t}{R_CN_SPACE_COLON}$" for t in R_SUB_TITLES],
        # 中文在前
        *[rf"^{R_CN_SPACE}{t}[:：\s]*$" for t in R_SUB_TITLES],
        # 序号开头：stock=03369, year=2024, index=2033
        r"^[IV]{1,4}[.]\s*[a-z]",
    ],
    flags=re.I,
)


P_SUB_TOTAL = PatternCollection(
    [
        *TOTAL_ASSETS_PATTERN,
        # 中文在后
        *[rf"^{t}{R_CN_SPACE_COLON}$" for t in R_SUB_TITLES],
        # 中文在前
        *[rf"^{R_CN_SPACE}{t}[:：\s]*$" for t in R_SUB_TITLES],
        rf"^{R_CN_SPACE}Credit loss allowance on",
        rf"^{R_CN_SPACE}Attributable to",
    ],
    flags=re.I,
)

P_INVALID_SUBTOTAL_HEADER = PatternCollection([rf"^notes?{R_CN_SPACE_COLON}$", rf"^{R_CN_SPACE}notes?\s*$"], flags=re.I)
P_SUBTOTAL_ROW_HEADER = PatternCollection(
    [
        rf"^{R_CN_SPACE}Total\s*(assets?|current|non[{R_MIDDLE_DASHES}\s]?current|equity|liabilit(y|ies)|capital|REVENUE)",
        # net描述太多，不能模糊匹配，这里限制已知的三个总净值
        rf"^{R_CN_SPACE}net\s*(current\s*|non[{R_MIDDLE_DASHES}\s]?current\s*)?assets{R_CN_SPACE_COLON}$",
    ],
    re.I,
)

table_unit_patterns = PatternCollection([rf"单位[:：]{CURRENCY}?(?P<dst>.*?({UNIT_PATTERN}))"])
cell_data_patterns = PatternCollection(
    [
        re.compile(rf"^\d+(\.\d+)?{R_PERCENT}?$"),
        re.compile(rf"{R_MIDDLE_DASH}?\d+(,\d+)+(\.\d+)?{R_PERCENT}?"),
        re.compile(rf"^{R_MIDDLE_DASH}$"),
        re.compile(rf"不[低高]于\s?\d+(\.\d+)?{R_PERCENT}?$"),
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6918#note_709564
        re.compile(rf"^\d+\n[(（]\d+(\.\d+)?{R_PERCENT}"),  # 投票比例
    ]
)

date_header_patterns = PatternCollection([r"^(19|20)\d{2}$"])

header_cell_unit_patterns = PatternCollection(
    [rf"[（\(](?P<dst>{UNIT_PATTERN})[\)）]", rf"[（（\(]?(?P<dst>{R_PERCENT})[\)）]?"]
)
data_cell_unit_patterns = PatternCollection(
    [rf"[（\(]?(?P<dst>{UNIT_PATTERN})[\)）]?", rf"[（（\(]?(?P<dst>{R_PERCENT})[\)）]?"]
)
percent_unit_patterns = PatternCollection([rf"[（\(]?(?P<dst>{R_PERCENT})[\)）]?"])
date_patterns = re.compile(DATE_PATTERN)
date_en_patterns = re.compile(DATE_EN_PATTERN, re.I | re.X)
serial_number_pattern = re.compile("序号")
# 列名中包含序号或货币单位，说明该列不是行头列
R_CELL_CURRENCY = (
    rf"[（(]?({R_CURRENCY}|[$¥€￡])\s*(m|[bm]illion|.?[0oO]{{3}}|.?[0oO]{{3}}.?[0oO]{{3}})[)）]?{R_CN_SPACE}$"
)
P_INVALID_HEADER_COL = PatternCollection([R_CELL_CURRENCY, r"序号"], flags=re.I)
# 第一列非行头的情况 包含 序号 1 和 金额表述 92,355
non_first_col_header = PatternCollection(
    [
        r"^[\d,]*$",
    ],
    flags=re.I,
)

invalid_feature_pattern = PatternCollection(["^半?年[度末]$"])
special_split_pattern = re.compile(r"\n/|/\n|\n年$")
FOOTNOTE_START_PATTERN = PatternCollection(
    [
        r"^[\[(（]?(?P<num>[1-9]\d?|[a-z]|[ivx]{1,4})[\])）:：.].{20,}",
        r"^(?P<num>Notes?\s*([1-9]\d?|[a-z])?)[.:：\s]*",
        r"^(?P<num>[\^#*@]+)",
    ],
    re.I,
)
P_START_FIRST = re.compile(r"^notes?|[\^#*@]+|[1AI]", re.I)
FOOTNOTE_AS_FOLLOW_PATTERN = re.compile(r"(follow|below).*?[:：]$", re.I)
P_HEADER_UNIT = re.compile(rf"^{R_CELL_CURRENCY}", re.I)
P_HEADER_MONTH_DAY = re.compile(rf"\s(\d{{1,2}}\s+{R_EN_MONTH}|{R_EN_MONTH}\s+\d{{1,2}})[,，]?\s*$", re.I)
P_HEADER_YEAR = re.compile(r"^\d{4}$")
# 年份和unit识别差，这里做强制转换： https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/3168#note_727374
BAD_HEADER_REPLACE_MAP = {
    "50%¶": "RMB'",
    "1RWHV": "Notes",
    "\x13": "0",
    "\x14": "1",
    "\x15": "2",
    "\x16": "3",
    "\x17": "4",
    "\x18": "5",
    "\x19": "6",
    # 下方三个没遇到，不太确定
    # r"\x20": "7",
    # r"\x21": "8",
    # r"\x22": "9"
}
P_NUM_CELL_DATA = re.compile(rf"^{R_MIDDLE_DASH}$|^[{R_MIDDLE_DASHES}+]?\d[\d,]*(\.\d+)?$")
P_CELL_FOOTNOTE = re.compile(
    r"[(（\[{【\s](Note\s*[(（]?)?([\^*#@]|\d+|[a-z]|[ivx]{1,4})[）)]?[】}\]）)]\s*$|\s+[(（]note[）)]$", re.I
)


logger = logging.getLogger(__name__)


@dataclass
class TableSubTitle:
    row_index: int
    col_index: int
    left_border: float
    title: str
    is_all_merged: bool = False
    children_rows: list[int] = field(default_factory=list)


class ParsedTable:
    pdfinsight_table: PdfinsightTable
    elements_above: List[Dict]
    footnote: List[Dict]
    tabletype: int
    unit: "ElementResult"
    title: "ElementResult"
    raw_rows: List[List[PdfinsightTableCell]]
    height: int
    row_tags: List[str]
    regions: List["ParsedTableRegion"]

    def __init__(
        self,
        pdfinsight_table: PdfinsightTable,
        elements_above: List[Dict] = None,
        footnotes: List[Dict] = None,
        tabletype=None,
        width_from_all_rows=False,
        *,
        extra_title_patterns: List[str] = None,
        pdf_md5: str = None,
        merged_header_boxes: dict[int, list] | None = None,
    ):  # raw data
        self.pdfinsight_table = pdfinsight_table
        self.pdf_md5 = pdf_md5
        self.merged_header_boxes = merged_header_boxes
        # NOTE: ordring: bottom to top
        self.elements_above = sorted(elements_above, key=lambda e: e["index"], reverse=True) if elements_above else []
        self.footnotes: list[dict] = footnotes if footnotes else []
        self.tabletype = self.parse_shape(tabletype)  # 如果tabletype为None, 依赖 self.rows
        self.raw_rows = self.pdfinsight_table.sorted_rows()
        # self.normalize_table_headers()
        self.height = len(self.raw_rows)

        # parsed results
        self.unit = self.parse_unit()
        self.title: TitleCharResult = self.parse_title(extra_title_patterns)
        self.cols_counter = self.find_cols_counter()
        self.row_tags = self.parse_row_tags()
        self.regions = self.parse_regions(self.row_tags, width_from_all_rows=width_from_all_rows)  # 依赖 self.tabletype

    def __repr__(self):
        return f"page:{self.element['page']}, index:{self.element['index']}"

    @property
    def element(self) -> Dict:
        return self.pdfinsight_table.element

    @property
    def index(self):
        return self.element["index"]

    @property
    def page(self):
        return self.element["page"]

    @cached_property
    def rows(self) -> List[List["ParsedTableCell"]]:  # 依赖 self.regions  # TODO
        return [row for region in self.regions for row in region.rows]

    @cached_property
    def cols(self) -> List[List["ParsedTableCell"]]:
        ret = defaultdict(list)
        for row in self.rows:
            for cell in row:
                ret[cell.colidx].append(cell)
        return list(ret.values())

    @cached_property
    def body(self) -> List["ParsedTableCell"]:
        return [cell for row in self.rows for cell in row if not cell.is_header]

    @cached_property
    def header(self) -> List["ParsedTableCell"]:
        return [cell for row in self.rows for cell in row if cell.is_header and cell.page == self.element["page"]]

    @cached_property
    def row_indices_of_header(self) -> set[int]:
        """
        列名所在行
        """
        return {c.rowidx for col in self.cols for c in col if c.is_col_header}

    @cached_property
    def col_indices_of_header(self) -> set[int]:
        """
        行名所在列
        """
        return {r.colidx for row in self.rows for r in row if r.is_row_header}

    @staticmethod
    def get_header_texts(header_cells, attr) -> dict[int, str]:
        header_map = {}
        for cells in header_cells:
            texts = []
            for cell in cells:
                text = clean_txt(cell.text)
                if text not in texts:
                    texts.append(text)
            header_map[getattr(cells[0], attr)] = "\t".join(texts)
        return header_map

    @cached_property
    def row_header(self) -> list[list]:
        # 先找出最大的列号（行标题可能是多列）
        col_indices = set()
        for row in self.rows:
            col_indices.update(c.colidx for c in row if c.is_row_header)
        # 所有<=最大列号的单元格，都认为是标题
        max_col = max(max(col_indices) + 1, self.row_header_end)
        row_headers = []
        for row in self.rows:
            row_headers.append([cell for cell in row if self.row_header_start <= cell.colidx < max_col])
        return row_headers

    @cached_property
    def row_header_texts(self) -> dict[int, str]:
        return self.get_header_texts(self.row_header, "rowidx")

    @cached_property
    def data_row_header(self) -> list[list]:
        """
        找出数据行的行头，处理步骤：
        1. 排除列名行
        2. 排除合并表格的列名行
        例如： http://************:55647/#/project/remark/407508?treeId=7885&fileId=113365&schemaId=18&projectId=17&schemaKey=C2.4&page=184 index=2168,2176
        """
        # 找出币种所在行
        currency_row_indices = set()
        for row in self.rows:
            if all(not c.clean_text or P_HEADER_UNIT.search(c.no_cn_text) for c in row):
                currency_row_indices.add(row[0].rowidx)
                break
        data_row_headers = []
        for header_cells in self.row_header:
            if any(c.is_col_header for c in header_cells) or header_cells[0].rowidx in currency_row_indices:
                continue
            if any(c.raw_cell["box"] in self.merged_header_boxes.get(c.page, []) for c in header_cells):
                continue
            data_row_headers.append(header_cells)
        return data_row_headers

    @cached_property
    def col_header(self) -> list[list]:
        # 先找出最大的行号（列标题可能是多行）
        # http://************:55647/#/project/remark/266374?treeId=2530&fileId=70489&schemaId=18&projectId=2530&schemaKey=C1&page=110 index=1325
        page = self.element["page"]
        row_indices = set()
        for col in self.cols:
            row_indices.update(c.rowidx for c in col if c.is_col_header and c.page == page)
        check_page = True
        if not row_indices:
            # 考虑跨页表格只在第一页有列名
            # https://mm.paodingai.com/cheftin/pl/9xadb6xbz7ypfxxkfg9ggat6sr
            # http://************:55647/#/project/remark/257766?treeId=12192&fileId=68768&schemaId=15&projectId=17&schemaKey=B74&page=18 元素块193
            check_page = False
            for col in self.cols:
                row_indices.update(c.rowidx for c in col if c.is_col_header)
        # 所有<=最大行号的单元格，都认为是标题
        max_row = max(row_indices)
        col_headers = []
        for col in self.cols:
            col_headers.append([c for c in col if c.rowidx <= max_row and (not check_page or c.page == page)])
        return col_headers

    @cached_property
    def col_header_texts(self) -> dict[int, str]:
        return self.get_header_texts(self.col_header, "colidx")

    @cached_property
    def all_subtitles(self) -> dict[int, TableSubTitle]:
        return {}

    @staticmethod
    def get_merged_cells(rows) -> list[list[tuple]]:
        merged_cells = []
        for row in rows:
            for cell in row:
                if cell.dummy or not (cell.width > 1 or cell.height > 1):
                    continue
                row_idx, col_idx = cell.rowidx, cell.colidx
                merged = []
                for row_offset in range(0, cell.height):
                    for col_offset in range(0, cell.width):
                        merged.append((row_idx + row_offset, col_idx + col_offset))
                merged_cells.append(merged)
        return merged_cells

    @staticmethod
    def get_merged_headers(rows) -> tuple[list[list], list[list]]:
        merged_rows, merged_cols = [], []
        for row in rows:
            for cell in row:
                if not cell.is_header:
                    continue
                if cell.dummy or (cell.rowidx == cell.colidx == 0) or (cell.is_row_header and cell.is_col_header):
                    continue
                if cell.is_col_header and cell.width > 1:
                    merged_cols.append([cell.colidx + o for o in range(0, cell.width)])
                if cell.is_row_header and cell.height > 1:
                    merged_rows.append([cell.rowidx + o for o in range(0, cell.height)])
        return merged_rows, merged_cols

    @cached_property
    def merged_headers(self) -> tuple[list[list], list[list]]:
        """
        列头/行头中的合并行及合并列
        """
        return self.get_merged_headers(self.rows)

    @cached_property
    def merged_cells(self) -> list[list[tuple]]:
        """
        列头/行头中按行合并的列
        """
        return self.get_merged_cells(self.rows)

    @cached_property
    def possible_titles(self) -> List[str]:
        titles = []
        if title := clean_txt(self.pdfinsight_table.element.get("title") or ""):
            titles.append(title)
        if self.title and (title := clean_txt(self.title.text)) and title not in titles:
            titles.append(title)
        if (
            self.elements_above
            and (title := clean_txt(self.elements_above[0].get("text") or ""))
            and title not in titles
        ):
            titles.append(title)
        return titles

    @cached_property
    def average_char_width(self) -> float:
        """
        字符平均宽度 取倒数第一行有数据的数据行字符的平均宽度
        """
        first_data_row_chars = []
        for ridx in range(self.height - 1, -1, -1):
            row = self.raw_rows[ridx]
            for cell in row:
                first_data_row_chars.extend(cell["chars"])
            if first_data_row_chars:
                break
        return get_avg_width(first_data_row_chars)

    @cached_property
    def min_max_of_left_indents(self):
        row_first_chars_left = []
        for row_idx in range(self.height):
            cell = self.raw_rows[row_idx][self.row_header_start]
            if not clean_txt(cell["text"], remove_blank=True):
                continue
            row_first_chars_left.append(ParsedTableCell.chars_left_border(cell))
        return min(row_first_chars_left, default=0), max(row_first_chars_left, default=0)

    @cached_property
    def indented_tag_for_rows(self) -> List[bool]:
        """
        字符左边距平均宽度 取第1个列名列（不应定是第一列）数据行字符的平均左边距
        """
        min_indent, max_indent = self.min_max_of_left_indents
        if (min_indent == max_indent == 0) or (max_indent - min_indent < self.average_char_width):
            # 最大值减去最小值小于一个字符的宽度 认为所有行都不是缩进行
            return [False] * self.height
        all_row_first_chars_left = [
            ParsedTableCell.chars_left_border(self.raw_rows[i][self.row_header_start]) for i in range(self.height)
        ]
        # 按左边距排序
        border_items = sorted([(left, idx) for idx, left in enumerate(all_row_first_chars_left)], key=lambda x: x[0])
        kmeans_input = [[border] for border, _ in border_items]
        kmeans_result = kmeans(np.asarray(kmeans_input))
        cluster_items = []
        for (_, idx), cluster in zip(border_items, kmeans_result):
            cluster_items.append((idx, cluster))
        cluster_items.sort(key=lambda x: x[0])  # 以表格行号排序
        ret = [bool(x[1]) for x in cluster_items]
        return ret

    @cached_property
    def raw_cell_map(self) -> dict[str, "ParsedTableCell"]:
        return {cell.raw_cell["index"]: cell for row in self.rows for cell in row}

    @cached_property
    def cell_map(self) -> dict[str, "ParsedTableCell"]:
        return {cell.indexstr: cell for row in self.rows for cell in row}

    @cached_property
    def note_indices(self) -> set[int]:
        return {note["index"] for note in self.footnotes}

    def cell(self, ridx, cidx) -> "ParsedTableCell":
        return self.rows[ridx][cidx]

    def parse_title(self, extra_patterns) -> TitleCharResult:
        title_idx = None
        title_element = None
        for idx, ele in enumerate(self.elements_above):
            if self.is_title(ele, extra_patterns):
                title_idx = idx
                title_element = ele
                break
        else:
            for idx, elt in enumerate(self.elements_above):
                if elt["class"] == "PARAGRAPH":
                    title_idx = idx
                    title_element = elt
                    break
        if title_element is None:
            title_text = ""
            if self.element["title"] is not None:
                title_text = self.element["title"] or ""
            fake_element = {
                "index": self.element["index"],
                "text": title_text,
                "chars": [],
            }
            return TitleCharResult(fake_element, [], title_text=title_text)
        title_text = title_element["text"]
        if len(self.elements_above) > title_idx + 1:
            previous_element = self.elements_above[title_idx + 1]
            if previous_element["class"] == "PARAGRAPH":
                matcher = TABLE_TITLE_PATTERNS.nexts(previous_element["text"])
                if not matcher and previous_element["text"].count(" ") > 10:
                    matcher = TABLE_TITLE_PATTERNS.nexts(clean_txt(previous_element["text"], remove_blank=True))
                if matcher and len(previous_element["text"]) < 100:
                    title_text = previous_element["text"] + title_text
        return TitleCharResult(title_element, title_element.get("chars"), title_text=title_text)

    @staticmethod
    def is_title(ele, extra_patterns: List[str]) -> bool:
        if ele["class"] != "PARAGRAPH":
            return False

        match = TABLE_TITLE_PATTERNS.nexts(ele["text"], extra_patterns=extra_patterns)
        if not match and ele["text"].count(" ") > 10:
            match = TABLE_TITLE_PATTERNS.nexts(clean_txt(ele["text"], remove_blank=True), extra_patterns=extra_patterns)
        if match:
            return True

        if TITLE_NEGLECT_PATTERNS.nexts(ele["text"]):
            return False

        if not P_AS_FOLLOW.search(ele["text"]) and len(ele["text"].split()) > 20:
            # 单词数量过多的一般是误识别的title
            return False

        return True

    def parse_unit(self):
        """
        TODO 英文文档下，这里提不到任何信息
        """

        def find_unit(ele) -> Match | None:
            if not is_para_elt(ele):
                return None
            match = table_unit_patterns.nexts(ele["text"])
            return match

        for ele in self.elements_above:
            match = find_unit(ele)
            if not match:
                continue
            unit_slice = slice(*match.span("dst"))

            return CharResult(ele, ele["chars"][unit_slice])
        return None

    def is_all_cn(self, raw_cells, start_col):
        """
        是否所有单元格都为空或不包含中文
        """
        merged_row = set()
        # 找出存在合并列的行（合并列必须包含start），则找出包含标题列的
        # http://************:55647/#/project/remark/295663?treeId=12941&fileId=70933&schemaId=29&projectId=17&page=75 index=1407
        for merged in self.element["merged"]:
            row_dict = defaultdict(list)
            for row, col in merged:
                row_dict[row].append(col)
            for row, cols in row_dict.items():
                if len(cols) > 1 and start_col in cols:
                    merged_row.add(row)
        # 判断是否全部为中文时，需要跳过存在合并列的行，以及空白单元格
        return all(
            not c.get("text")
            or int(c["index"].split("_")[0]) in merged_row
            or (has_chinese_chars(c) and not has_english_chars(c))
            for c in raw_cells
        )

    @cached_property
    def row_header_end(self) -> int:
        end_col = self.row_header_start + 1
        next_col_cells = [r[end_col] for r in self.raw_rows if len(r) > end_col and r[end_col].get("text")]
        if (
            next_col_cells
            and self.is_all_cn(next_col_cells, self.row_header_start)
            and not P_NOTES.search(next_col_cells[0]["text"])
        ):
            # 第一列的列名为英文且第二列的列名为中文，则第二列也要作为列头
            # http://************:55647/#/project/remark/405806?treeId=3359&fileId=113173&schemaId=29&projectId=17 index=1457 stock=02289, year=2024
            return end_col + 1
        return end_col

    def merged_row_header_indices(self):
        merged_col_indices = set()
        for merged in self.element["merged"]:
            cols = [c for r, c in merged if r == 0]
            if len(cols) > 1 and max(cols) < 3:
                merged_col_indices.update(cols)
        return merged_col_indices

    @cached_property
    def row_header_start(self) -> int:
        """
        https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/648#note_168703
        """
        min_columns = 3
        count_col = min(len(r) for r in self.raw_rows)
        # http://************:55647/#/project/remark/293661?projectId=17&treeId=4881&fileId=70660&schemaId=18&page=143 element_index=1590
        if count_col < min_columns:
            return 0
        merged_col_indices = self.merged_row_header_indices()
        # 仅找前三列
        start_col = 0
        for col_idx in range(min_columns):
            col_cells = [r[col_idx] for r in self.raw_rows]
            if any(P_SUB_TOTAL.nexts(c["text"]) or P_SUBTOTAL_ROW_HEADER.nexts(c["text"]) for c in col_cells):
                return col_idx
            if any(P_INVALID_HEADER_COL.nexts(c["text"]) for c in col_cells[:4]):
                # 第一列前几行中有货币单位，说明第一列为数据列，取第二列做行头
                # http://************:55647/#/project/remark/416952?treeId=9968&fileId=114422&schemaId=29&projectId=17&page=126 index=1479, stock=00152, year=2024
                # http://************:55647/#/project/remark/417955?treeId=5769&fileId=114534&schemaId=29&projectId=17&page=128 index=1537, stock=00001, year=2024
                start_col += 1
                continue
            next_col = col_idx + 1
            if next_col >= count_col:
                return 0
            if self.is_all_cn(col_cells, col_idx) and any(
                has_english_chars(r[next_col]) and not has_chinese_chars(r[next_col]) for r in self.raw_rows
            ):
                # 第一列的列名为中文且第二列的列名为英文，则第二列之前作为行头
                # http://************:55647/#/project/remark/405806?treeId=3359&fileId=113173&schemaId=29&projectId=17 index=1457 stock=02289, year=2024
                return next_col
            if col_idx in merged_col_indices:
                continue
            return col_idx
        return 0

    def parse_regions(self, tags, width_from_all_rows=False) -> List["ParsedTableRegion"]:
        """用 header 划分区域
        ---
        header
        header
        dataline
        dataline
        ---
        header
        ...
        ---


        ---
        subtitile
        header
        dataline
        dataline
        ---
        header
        ...
        ---

        """
        regions = []
        region = None
        meet_header = "none"  # none -> in -> out -> (start new region) none
        for ridx, tag in enumerate(self.row_tags):
            if ridx == 0 or (tag == "header" and meet_header == "out"):
                # start a new region
                region = [ridx, ridx]
                regions.append(region)
                meet_header = "in" if tag == "header" else "none"
            else:
                region[1] = ridx
                if tag == "header":
                    meet_header = "in"
                elif meet_header == "in":
                    meet_header = "out"

        # 调整 subtitle：紧挨着 header 的 subtitle 应该属于下一个 region
        for rgidx in range(1, len(regions)):
            lastregion = regions[rgidx - 1]
            region = regions[rgidx]
            for ridx in range(region[0] - 1, lastregion[0], -1):
                if tags[ridx] == "subtitle":
                    lastregion[1] -= 1
                    region[0] -= 1
                else:
                    break

        parsed_regions = []
        for num, (start, end) in enumerate(regions):
            parsed_region = ParsedTableRegion(
                self,
                start,
                end,
                num,
                row_headers_last=parsed_regions[-1].raw_header_rows if parsed_regions else None,
                width_from_all_rows=width_from_all_rows,
            )
            parsed_regions.append(parsed_region)
        return parsed_regions

    def is_same_indent(self, left, next_left):
        """
        缩进是否相近
        """
        return abs(next_left - left) < self.average_char_width * 1.2

    @staticmethod
    def is_all_merged_row(row):
        # 所有单元格的宽度全部相同，且==列宽，认为和是合并行
        widths = [c["right"] - c["left"] for c in row]
        return len(set(widths)) == 1 and widths[0] == len(row)

    @staticmethod
    def is_all_upper(cell_text):
        if en_chars := {c for c in cell_text if c.isalpha()}:
            return all(c.isupper() for c in en_chars)
        return False

    def is_subtitle(self, row_idx: int):
        """
        TODO unittest
        """
        # 第一行和最后一行，不会是subtitle
        if row_idx == len(self.raw_rows) - 1:
            return False
        row = self.raw_rows[row_idx][self.row_header_start :]
        # TODO: 还要确认是来自同一个单元格的 dummy
        valid_cells = [
            c for c in row if not c.get("dummy") and clean_txt(c["text"], remove_blank=True, remove_cn_text=True)
        ]
        if not valid_cells:
            return False
        next_cell = self.raw_rows[row_idx + 1][self.row_header_start]
        next_text = next_cell["text"]
        # 1. 必须有且仅有1-2个有效单元格文本
        # stock=01988,year=2024,mid=29,page=219,rowidx=9
        count_valid_cells = len(valid_cells)
        if count_valid_cells > 2:  # not next_text.startswith(tuple(R_MIDDLE_DASHES)) and
            return False
        first_cell = valid_cells[0]
        left_border = ParsedTableCell.chars_left_border(first_cell)
        col_index, cell_text = int(first_cell["index"].split("_")[1]), first_cell["text"]
        # 2. 合并行就是subtitle
        if self.is_all_merged_row(row):
            self.all_subtitles[row_idx] = TableSubTitle(row_idx, col_index, left_border, cell_text, is_all_merged=True)
            return True
        # 非合并行时，标题单元格需要是第一个 TODO 后续若遇到不在第一个的场景，这里再处理
        if first_cell["index"] != row[0]["index"]:
            return False
        if not clean_txt(next_text, remove_blank=True):
            # 下一行的第一个单元格为空， 说明下一行可能为小计行，当前行可能不是subtitle
            return False
        # 3. 用正则判断  http://************:55647/#/project/remark/267706?treeId=5071&fileId=69908&schemaId=29&projectId=17
        # 序号开头：stock=03369, year=2024, index=2033
        if P_COMMON_SUBTITLES.nexts(clean_txt(" ".join(c["text"] for c in valid_cells))):
            self.all_subtitles[row_idx] = TableSubTitle(row_idx, col_index, left_border, cell_text)
            return True
        # 不满足上一步正则的第一行，一般是列标题行
        if row_idx == 0:
            return False
        # 当前行有英文，下一行只有中文，则属于合并行未正确识别
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6172#note_733145 rowidx=9/19 stock=2382, year=2024
        if has_english_chars(first_cell) and not has_english_chars(next_cell) and has_chinese_chars(next_cell):
            return False
        # 4. 有缩进时，按缩进判断
        if any(self.indented_tag_for_rows):
            # stock_code=01513, year=2024, page_index=223, mid=29, row_idx=7
            # stock_code=01697, year=2024, page_index=165, mid=29, element_index=1563
            next_left_border = ParsedTableCell.chars_left_border(next_cell)
            # 与下行缩进超过平均字符宽度，认为存在缩进
            if not self.is_same_indent(next_left_border, left_border) and next_left_border > left_border:
                self.all_subtitles[row_idx] = TableSubTitle(row_idx, col_index, left_border, cell_text)
                return True
            # 如果有效数据为2，且没有缩进，认为不是subtitle
            # stock_code=00966, year=2024, mid=29, element_index=2315
            if count_valid_cells > 1:
                return False
        # 5. 缩进无法判断时，按照样式判断（TODO 也可以按照一些常见汇总的正则）
        font_name, weight, italic = get_style_of_raw_cell(first_cell)
        if not font_name:
            # 没有英文字母，或者提取不到样式
            return False
        next_font_name, next_weight, next_italic = get_style_of_raw_cell(next_cell)
        if (
            (weight == "bold" and next_weight and weight != next_weight)
            or (italic and not next_italic)
            or (next_font_name and font_name != next_font_name)
        ):
            self.all_subtitles[row_idx] = TableSubTitle(row_idx, col_index, left_border, cell_text)
            return True
        # 6. 用上下行大小写字母是否不同判断
        # http://************:55647/#/project/remark/419563?treeId=8735&fileId=114716&schemaId=29&projectId=17
        if self.is_all_upper(cell_text) and any(c.isalpha() for c in next_text) and not self.is_all_upper(next_text):
            self.all_subtitles[row_idx] = TableSubTitle(row_idx, col_index, left_border, cell_text)
            return True
        return False

    def parse_row_tags(self) -> List[str | None]:
        row_tags = [None] * self.height

        def must_be_dataline(row: List[PdfinsightTableCell]):
            data_cell, counts = 0, 0
            for cell in row:
                if not cell["text"] or cell.get("dummy"):
                    continue
                if cell_data_patterns.nexts(cell["text"].strip()):
                    data_cell += 1
                counts += 1
            if not counts:  # TODO 可能有问题 如果一行数据 全是上一行的dummy 那么此时counts为0 但是应该是dataline
                return False
            return data_cell / counts >= 0.5

        def filter_subtitle(subtitle_indexes):
            ret = []
            # 若存在连续的sub_title 则只取第一个
            for _, idx in groupby(enumerate(subtitle_indexes), lambda x: x[1] - x[0]):
                subtitle_group = [k for _, k in idx]
                ret.append(subtitle_group[0])
                # total assets/current assets/non-current assets及它们的下一行不能被过滤
                # http://************:55647/#/project/remark/409592?treeId=42540&fileId=113599&schemaId=29&projectId=17
                for i, row_idx in enumerate(subtitle_group[1:], start=1):
                    if any(
                        P_COMMON_SUBTITLES.nexts(self.all_subtitles[r].title) for r in (row_idx, subtitle_group[i - 1])
                    ):
                        ret.append(row_idx)
            # 从all_subtitles中排除连续的subtitle
            for k in set(self.all_subtitles) - set(ret):
                self.all_subtitles.pop(k)
            return ret

        def row_range(row: List[PdfinsightTableCell]) -> Tuple[int, int]:
            row_start = min(c["top"] for c in row)
            row_end = max(c["bottom"] for c in row)
            return row_start, row_end

        # 肯定为 dataline 的
        dataline_idx = [ridx for ridx in range(self.height) if must_be_dataline(self.raw_rows[ridx])]

        # 第一行为年份 2017 2018 类似的描述时 从dataline_idx中去除第一行
        if 0 in dataline_idx:
            first_row = self.raw_rows[0]
            cells = [cell for cell in first_row if date_header_patterns.nexts(clean_txt(cell["text"]))]
            if len(cells) / len(first_row) >= 0.5:
                dataline_idx.remove(0)

        # 推断 subtitle, 2025.3.18： 排除第1行，因为一般不是subtitle
        if subtitle_indices := [ridx for ridx in range(self.height) if self.is_subtitle(ridx)]:
            subtitle_indices = filter_subtitle(subtitle_indices)

        # 在 subtitle 划分的每个区域找 header
        header_idx = []
        for i, j in zip([0] + subtitle_indices, subtitle_indices + [self.height]):
            if i == j:
                continue
            header_idx.extend(self.find_header_idx(i, j, subtitle_indices, dataline_idx))
        # https://jura.paodingai.com/#/hkex/result-announcement/report-review/165300?fileId=50110: stock_code=00285, year=2021, Q3
        header_idx = [idx for idx in header_idx if idx != self.height - 1]  # 排除最后一行

        # TODO: pdfinsight bug 段落识别成了表格的一行 file_id: 1211
        # if subtitle_idx and subtitle_idx[0] != 0:
        #     subtitle_idx.insert(0, 0)

        # 对每一行进行判定: header, dataline, subtitle
        for ridx in range(self.height):
            if row_tags[ridx]:
                continue
            row_tag = "dataline"
            row = self.raw_rows[ridx]
            if ridx in subtitle_indices:
                row_tag = "subtitle"
            elif ridx in header_idx:
                row_tag = "header"
            # header所在区域中，任意一行是header，则认为所有行都是header
            # http://************:55647/#/hkex/annual-report-checking/report-review/260712?fileId=69357&schemaId=18&rule=C1&delist=0 index=1651
            is_header_row = any(i in header_idx for i in range(*row_range(row)))
            for i in range(*row_range(row)):
                if i < self.height:
                    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6585#note_691628
                    # 如果row_range中任意一个行号被识别为header，则row_tag直接使用"header"
                    row_tags[i] = "header" if is_header_row else row_tag
        return row_tags

    def parse_shape(self, table_type: int | None = None) -> int:
        if table_type is not None:
            return table_type
        if self.is_table_kv():
            return TableType.KV.value
        # todo 添加判断表格为table_row table_tuple的方法
        return self.tabletype

    def is_table_kv(self):
        # 表格每一行均有两列且未合并
        return all((len(row) == 2 and all((not cell.dummy for cell in row)) for row in self.rows))

    def find_cells_by_outline(self, page, outline):
        cell_indexes = self.pdfinsight_table.find_cellidx_list_by_outline(page, outline)
        return [self.rows[ridx][cidx] for ridx, cidx in cell_indexes]

    def find_cells_by_text(self, text):
        cells = [cell for row in self.rows for cell in row if clean_txt(cell.text) == clean_txt(text)]
        return cells

    @staticmethod
    def calculate_axis_length(cells, axis="row"):
        """计算给定行/列的独立单元格数目(合并单元格视为一个独立单元格,只记一次数)"""
        if axis not in ("row", "col"):
            raise ValueError(f'axis must be "row" or "col", not "{axis}"')
        length = 0
        for cell in cells:
            if axis == "row":
                if cell["right"] - cell["left"] == 1 or cell["left"] == int(cell["index"].split("_")[1]):
                    length = length + 1
            if axis == "col":
                if cell["bottom"] - cell["top"] == 1 or cell["top"] == int(cell["index"].split("_")[0]):
                    length = length + 1
        return length

    def find_cols_counter(self):
        cols = Counter()
        for row in self.raw_rows:
            cols.update([self.calculate_axis_length(row, axis="row")])
        return cols

    def find_header_idx(self, start_index, end_index, subtitle_idx, dataline_idx):
        """寻找列头行的index
        - 统计出现最多的列数认为是 数据列数
        - 取第一行满足 数据列数 的行认为是头部
        """
        header_idx = set()
        max_header_idx = start_index
        find_max_header_idx = False
        for row_len, count in self.cols_counter.most_common():
            if count < 2:
                break
            for idx, row in enumerate(self.raw_rows[start_index:end_index], start=start_index):
                # 第一个行独立单元格数目达到最大的行即可认为是列头行
                if self.calculate_axis_length(row, axis="row") == row_len:
                    max_header_idx = idx
                    find_max_header_idx = True
                    break
            if find_max_header_idx:
                break
        for i in range(start_index, max_header_idx + 1):
            if i not in subtitle_idx and i not in dataline_idx:
                header_idx.add(i)

        if not header_idx:
            return header_idx
        # 补充条件: 如果上一行是header，当前行不是header且当前行内容为货币单位（例如：RMB'000）或上行+下行能构成完整的年份
        # http://************:55647/#/project/remark/340995?projectId=17&treeId=5005&fileId=104149&schemaId=18&rule=C1.1.1 index=179
        max_header_idx = max(header_idx)
        if max_header_idx < self.height - 2:
            row, next_row = self.raw_rows[max_header_idx], self.raw_rows[max_header_idx + 1]
            # 处理下述情况，列头被识别成两行
            #  | Approximate | used as at 30 June|
            #  | HK$ million | 2024              |
            if any(P_HEADER_UNIT.search(clean_txt(r.get("text") or "", remove_cn_text=True)) for r in next_row) or any(
                P_HEADER_MONTH_DAY.search(row[i].get("text") or "") and P_HEADER_YEAR.search(r.get("text") or "")
                for i, r in enumerate(next_row)
            ):
                header_idx.add(max_header_idx + 1)
        return header_idx

    # def normalize_table_headers(self):
    #     """
    #     主要是对年份信息做处理
    #         最大年-largest_year_minus_0
    #         第二大年-largest_year_minus_1
    #         第三大年-largest_year_minus_2
    #         ...
    #     """
    #     # largest_year = self.find_largest_year()
    #     # if largest_year:
    #     for row in self.raw_rows:
    #         for cell in row:
    #             # cell['normalized_text'] = self.normalize_cell_text(cell['text'], largest_year)
    #             # 使用hkex的泛化方法
    #             cell["normalized_text"] = normalize_words(cell["text"])

    def find_largest_year(self):
        years = set()
        for row in self.raw_rows:
            for cell in row:
                largest_year = self.find_largest_year_in_text(cell["text"])
                if largest_year:
                    years.add(largest_year)
                    cell["_largest_year"] = largest_year  # 标记该cell, 认为可泛化
        return max(years) if years else 0

    def find_largest_year_in_text(self, text):
        text = clean_txt(text)
        years = set()
        for match in date_en_patterns.finditer(text):
            text = text[: match.span()[0]] + text[match.span()[1] :]  # 去掉已经被匹配过的内容
            years.add(self.revise_year_str(match.group()))
        return max(years) if years else 0

    def normalize_cell_text(self, text, largest_year):
        ret = OrderedDict()
        if text.count("/") == 1 and not special_split_pattern.search(text):
            for sub_text in re.compile(r"/").split(clean_txt(text)):
                self.normalize_sub_text(sub_text, largest_year, ret)
        else:
            for sub_text in special_split_pattern.split(text):
                sub_text = clean_txt(sub_text)
                self.normalize_sub_text(sub_text, largest_year, ret)
        return "".join(list(ret.keys()))

    @staticmethod
    def normalize_sub_text(sub_text, largest_year, ret):
        if not sub_text or len(sub_text) == 1:
            return
        if invalid_feature_pattern.nexts(sub_text):
            return
        for match in date_patterns.finditer(sub_text):
            match_year = ParsedTable.revise_year_str(match.group())
            if match_year:
                sub_text = date_patterns.sub(f"largest_year_minus_{largest_year - match_year}", sub_text)
        ret.update({sub_text: None})

    @classmethod
    def revise_year_str(cls, text):
        text = "".join(CN_NUM_MAP.get(t, t) for t in text)
        match = re.search(r"\d{4}", text)
        return int(match.group()) if match and cls.is_valid_year(int(match.group())) else 0

    @classmethod
    def is_valid_year(cls, year: int):
        # 简单验证一下提取年度的有效性(1990~2030)
        return -30 <= year - datetime.utcnow().year <= 10


class ParsedTableRegion:
    table: ParsedTable
    start: int
    end: int
    num: int
    width: int
    height: int
    raw_rows: List[List[PdfinsightTableCell]]
    raw_header_rows: List[List[PdfinsightTableCell]]
    rows: List[List["ParsedTableCell"]]
    row_header_list: List[List["ParsedTableCell"]]
    col_header_list: List[List["ParsedTableCell"]]

    def __init__(
        self,
        table: ParsedTable,
        start: int,
        end: int,
        num: int,
        row_headers_last: List[List[PdfinsightTableCell]] = None,
        width_from_all_rows=False,
    ):
        # raw data
        self.table = table
        self.start, self.end = start, end
        self.num = num
        self.raw_rows = table.raw_rows[self.start : self.end + 1]
        self.row_tags = table.row_tags[self.start : self.end + 1]
        self.height = len(self.raw_rows)
        self.width = max(len(row) for row in self.raw_rows) if width_from_all_rows else len(self.raw_rows[0])

        # parsed results
        self.rows: List[List[ParsedTableCell]] = [[] for rowidx in range(self.height)]
        self.row_header_list: List[List[ParsedTableCell]] = [[] for rowidx in range(self.height)]
        self.col_header_list: List[List[ParsedTableCell]] = [[] for colidx in range(self.width)]
        self.parse(row_headers_last)
        self.fix_headers()

    def __repr__(self):
        return f"start: {self.start}, end:{self.end}"

    @cached_property
    def subtitle_parents(self):
        return {}

    def parse(self, row_headers_last):
        # TODO: TABLETYPE_COL will have no row headers (but this is not aways true)
        header_rows = [row for row, tag in zip(self.raw_rows, self.row_tags) if tag == "header"]
        col_header_from_first = False
        if self.table.tabletype != TableType.KV.value and not header_rows:
            if row_headers_last:
                # 本 region 没有 header，就取上一个 region 的 header
                header_rows = row_headers_last
            else:
                # 再没有只好取第一行
                header_rows = [self.raw_rows[0]]
                col_header_from_first = True
                # logging.warning("can't find region row headers, use the first row")
                # logging.warning('|'.join({cell['text'] for cell in self.raw_rows[0]}))

        if self.table.tabletype == TableType.ROW.value:
            row_header_end = 1
        else:
            row_header_end = max(row[0]["right"] - row[0]["left"] for row in header_rows) if header_rows else 1
            row_header_end = self.table.row_header_end or row_header_end

        for rowidx in range(self.height):
            # if row_tag == "subtitle":
            #     cells = sorted({c["text"] for c in raw_row})
            #     subtitle = "".join(cells)
            #     subtitle_idxes = [(rowidx, i) for i, cell in enumerate(raw_row) if clean_txt(cell["text"])]
            # 上述代码取的是距离当前行最近的subtitle，不合理，遇到多级的subtitle，会取错，例如:
            # https://www1.hkexnews.hk/listedco/listconews/sehk/2025/0423/2025042301478.pdf P222, page_index=223, row_idx=7
            subtitles, subtitle_idxes = [], []
            if cur_subtitles := self.find_subtitles(rowidx):
                subtitles = [st.title for st in cur_subtitles]
                subtitle_idxes = [(st.row_index, st.col_index) for st in cur_subtitles]
            raw_row, row_tag = self.raw_rows[rowidx], self.row_tags[rowidx]
            for colidx in range(self.width):
                try:
                    raw_cell = raw_row[colidx]
                except IndexError:
                    # TODO  http://************:55647/#/project/remark/355489?fileid=112806&projectId=17&treeId=8011&fileId=112806&schemaId=29&pag=42
                    # 直接取colidx会indexerror，但实际上按照raw_cell["index"]取，可以对上
                    logger.error(
                        f"merge table columns is not equal, page: {self.table.element['page']}, "
                        f"pdf_md5:{self.table.pdf_md5}, index: {self.table.element['index']}, title : {self.table.element['title']}"
                    )
                    # raise Exception(
                    #     f"merge table columns is not equal, page: {self.table.element['page']}, "
                    #     f"index: {self.table.element['index']}, title : {self.table.element['title']}"
                    # )
                    continue
                dummy = raw_cell.get("dummy", False)
                merge_to = self.table.pdfinsight_table.cell_merged_to(rowidx + self.start, colidx) if dummy else None
                is_col_header = row_tag == "header" or (col_header_from_first and rowidx == 0)
                is_row_header = self.table.row_header_start <= colidx < row_header_end

                # create cell
                cell = ParsedTableCell(
                    rowidx + self.start,
                    colidx,
                    raw_cell,
                    self,
                    dummy=dummy,
                    is_header=is_col_header or is_row_header,
                    is_col_header=is_col_header,
                    is_row_header=is_row_header,
                    col_header_cells=self.col_header_list[colidx],
                    row_header_cells=self.row_header_list[rowidx],
                    merge_to=merge_to,
                )
                # TODO 需要考虑多级属性的情况
                cell.subtitle = subtitles[0] if subtitles else ""
                cell.subtitle_idxes = subtitle_idxes
                cell.col_header_cells = self.col_header_list[colidx]
                cell.row_header_cells = self.row_header_list[rowidx]
                if is_col_header:
                    if not (cell.dummy and cell.text in [c.text for c in self.col_header_list[colidx]]):
                        self.col_header_list[colidx].append(cell)
                if is_row_header:
                    if not (cell.dummy and cell.text in [c.text for c in self.row_header_list[rowidx]]):
                        matcher = non_first_col_header.nexts(clean_txt(cell.text))
                        if not matcher:
                            self.row_header_list[rowidx].append(cell)
                self.rows[rowidx].append(cell)
                cell.parse_unit()

        self.raw_header_rows = header_rows

    @staticmethod
    def cell_num(cell_text) -> float:
        text = P_MIDDLE_DASH.sub("", cell_text).replace(",", "")
        if not text:
            return 0
        if text.replace(".", "").isdigit():
            return float(text)
        return 0

    def use_prev_subtitle(self, row_index, prev_subtitle, cur_subtitle):
        """
        当前subtitle的上一个subtitle是non-current assets，且当前total行的下一行是current assets，则取前一个subtitle
        http://************:55647/#/project/remark/379356?treeId=10143&fileId=112970&schemaId=29&projectId=17
        http://************:55647/#/project/remark/416305?treeId=7862&fileId=114350&schemaId=29&projectId=17&page=102
        """
        if not prev_subtitle:
            return False
        if not P_COMMON_SUBTITLES.nexts(prev_subtitle.title):
            return False
        # 当前是最后一行
        if row_index >= len(self.rows) - 1:
            return True
        # 下一行是指定的subtitle
        next_subtitle = self.table.all_subtitles.get(row_index + 1)
        if next_subtitle and P_COMMON_SUBTITLES.nexts(next_subtitle.title):
            return True
        # 下一行是total行
        next_text = self.raw_rows[row_index + 1][self.table.row_header_start]["text"]
        if P_COMMON_SUBTITLES.nexts(next_text) or P_SUBTOTAL_ROW_HEADER.nexts(next_text):
            return True
        # 上一行是current assets/non-current assets行并且当前行没有行头
        # http://************:55647/#/project/remark/267335?treeId=7862&fileId=69537&schemaId=29&projectId=17&page=108 index=1742 综合财务状况表
        prev_subtitle = self.table.all_subtitles.get(cur_subtitle.row_index - 1)
        if (
            prev_subtitle
            and P_COMMON_SUBTITLES.nexts(prev_subtitle.title)
            and not (
                "".join(
                    c.get("text") or ""
                    for c in self.raw_rows[row_index][self.table.row_header_start : self.table.row_header_end]
                )
            )
        ):
            return True
        return False

    def find_subtitles(self, row_index) -> list[TableSubTitle]:
        valid_cells = [
            c
            for c in self.raw_rows[row_index][self.table.row_header_start :]
            if clean_txt(c["text"], remove_blank=True)
        ]
        if not valid_cells:
            # 空行不提取subtitle
            return []
        # Total assets, total current assets等行不提取subtitle:
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6172#note_729156
        # http://************:55647/#/project/remark/416491?projectId=17&treeId=37553&fileId=114371&schemaId=29&page=349
        if P_COMMON_SUBTITLES.nexts(valid_cells[0]["text"]):
            return []
        subtitles = []
        # min_left, _ = self.table.min_max_of_left_indents
        cur_border = ParsedTableCell.chars_left_border(valid_cells[0])
        cur_col = int(valid_cells[0]["index"].split("_")[-1])
        count_header_cols = max({len(r) for r in self.row_header_list}, default=1)
        data_col = None
        if data_cells := [c for c in valid_cells if P_NUM_CELL_DATA.search(c["text"])]:
            data_col = int(data_cells[-1]["index"].split("_")[-1])
        for idx in sorted(self.table.all_subtitles, reverse=True):
            if idx >= row_index:
                continue
            subtitle = self.table.all_subtitles[idx]
            # 居中合并的subtitle直接取
            if subtitle.is_all_merged or P_COMMON_SUBTITLES.nexts(subtitle.title):
                subtitles.append(subtitle)
                break
            # 这里需要确保行名只有一列，如果行名有多列，暂时不处理（因为：如果subtitle在第一列，而子行的行名在第二列，下方逻辑就会有问题）
            if (
                data_col
                and count_header_cols == self.table.row_header_end
                and cur_col != subtitle.col_index
                and row_index > 2
            ):
                # 当前subtitle的上一个subtitle是non-current assets，且当前total行的下一行是current assets，则取前一个subtitle
                # http://************:55647/#/project/remark/379356?treeId=10143&fileId=112970&schemaId=29&projectId=17
                prev_subtitle = self.table.all_subtitles.get(subtitle.row_index - 1)
                if self.use_prev_subtitle(row_index, prev_subtitle, subtitle):
                    subtitles.append(prev_subtitle)
                    break
                cur_num = self.cell_num(self.raw_rows[row_index][data_col]["text"])
                sub_total_rows, total_num = set(), 0
                for idx in range(subtitle.row_index + 1, row_index):
                    # 如果是小计行(没有行名），跳过  TODO 行名带total/including
                    if not any(self.raw_rows[idx][c]["text"] for c in range(cur_col)):
                        sub_total_rows.add(idx)
                        continue
                    total_num += self.cell_num(self.raw_rows[idx][data_col]["text"])
                # subtitle ~ 当前行之间的数据之和，小于当前行数字，则继续向回找
                # http://************:55647/#/project/remark/267538?projectId=17&treeId=12441&fileId=69740&schemaId=29&page=137 table_index=2465,rowidx=28
                # http://************:55647/#/project/remark/406118?treeId=10413&fileId=113208&schemaId=29&projectId=17&page=66 table_index=955,rowidx=23
                if total_num == cur_num:
                    # 当前subtitle已经找到自己的合计，则跳过
                    # http://************:55647/#/project/remark/379356?treeId=10143&fileId=112970&schemaId=29&projectId=17
                    if any(
                        (subtitle.row_index, subtitle.col_index) in self.rows[i][0].subtitle_idxes
                        for i in sub_total_rows
                    ):
                        continue
                    subtitles.append(subtitle)
                    break
                if total_num < cur_num:
                    continue
            # 缩进相同时，判断是否相邻
            if self.table.is_same_indent(subtitle.left_border, cur_border):
                # 与subtitle相邻，或与subtitle下的第一行缩进一致，才是正确的层级
                child_border = ParsedTableCell.chars_left_border(
                    self.raw_rows[subtitle.row_index + 1][self.table.row_header_start]
                )
                if row_index == subtitle.row_index + 1 or self.table.is_same_indent(child_border, cur_border):
                    subtitles.append(subtitle)
                    break
                continue
            if cur_border > subtitle.left_border:
                subtitles.append(subtitle)
                break
        if subtitles:
            # 1. 存subtitle的父子关系
            if row_index in set(self.table.all_subtitles) - set(self.subtitle_parents):
                self.subtitle_parents[row_index] = subtitles[0].row_index
            # 2. 当前行不是小计行(小计行没有行头，绝对不可以添加父级subtitle)时，添加父级subtitle
            # stock=00966, year=2024, mid=29, index=2315,rowidx=8
            if valid_cells[0]["index"].endswith("_0"):
                cur_subtitle = subtitles[0]
                while cur_subtitle.row_index in self.subtitle_parents:
                    parent_row_idx = self.subtitle_parents[cur_subtitle.row_index]
                    cur_subtitle = self.table.all_subtitles[parent_row_idx]
                    subtitles.append(cur_subtitle)
            return subtitles
        if row_index in self.table.all_subtitles:
            return [self.table.all_subtitles[row_index]]
        return []

    def fix_headers(self):
        """针对一些情况对 header 解析特殊处理

        case 1: | AAA | 占比 | BBB | 占比 |
        处理：如有重复的 col header，把前一格 col header 也带上
        """
        col_headers = ["_".join([c.text for c in headers]) for headers in self.col_header_list]
        col_header_slots = defaultdict(list)
        for col_idx, val in enumerate(col_headers):
            col_header_slots[val].append(col_idx)
        for key, slot in col_header_slots.items():
            if key == "":
                continue
            if len(slot) <= 1:
                continue
            for col_idx in slot:
                if col_idx == 0:
                    continue
                # 修正 col_header_list
                self.col_header_list[col_idx] = self.col_header_list[col_idx - 1] + self.col_header_list[col_idx]
                # 更新 cell.col_header_cells
                for row in self.rows:
                    if len(row) <= col_idx:
                        continue
                    row[col_idx].col_header_cells = self.col_header_list[col_idx]


class ParsedTableCell:
    rowidx: int
    colidx: int
    raw_cell: PdfinsightTableCell
    text: str
    width: int
    height: int
    col_header_cells: List["ParsedTableCell"]
    row_header_cells: List["ParsedTableCell"]
    unit: "ElementResult"
    dummy: bool
    is_header: bool
    is_row_header: bool
    is_col_header: bool
    subtitle: str
    subtitle_idxes: List[Tuple]
    region: ParsedTableRegion
    merge_to: Tuple[int, int]

    def __init__(
        self,
        rowidx,
        colidx,
        cell,
        region,
        dummy=False,
        is_header=False,
        is_col_header=False,
        is_row_header=False,
        col_header_cells=None,
        row_header_cells=None,
        merge_to=None,
    ):
        self.rowidx = rowidx
        self.colidx = colidx
        self.raw_cell = cell
        self.text = cell["text"]
        self.width = cell["right"] - cell["left"]
        self.height = cell["bottom"] - cell["top"]
        self.dummy = dummy
        self.is_header = is_header
        self.is_col_header = is_col_header
        self.is_row_header = is_row_header
        self.region = region
        self.col_header_cells = col_header_cells or []
        self.row_header_cells = row_header_cells or []
        self.merge_to = merge_to

        self.parse_unit()

    def __str__(self):
        return self.text

    def __repr__(self):
        return self.text

    @cached_property
    def normalized_text(self):
        return normalize_words(self.text) or self.text

    @cached_property
    def no_cn_normalized_text(self):
        return normalize_words(self.no_cn_text) or self.no_cn_text

    @cached_property
    def clean_text(self):
        return clean_txt(self.fixed_text)

    @cached_property
    def no_cn_text(self):
        return remove_chinese_chars(self.raw_cell)

    @cached_property
    def text_no_superscript(self):
        """
        单元格文本，不带脚注序号
        """
        text = clean_txt("".join(char["text"] for char in self.raw_cell["chars"] if char.get("tag") != "superscript"))
        return P_CELL_FOOTNOTE.sub("", text)

    @cached_property
    def fixed_text(self):
        """
        针对列名中的识别乱码做修复: https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/3168#note_727374
        已知：
        "\x15\x13\x15\x17": "2024"
        "\x15\x13\x15\x16": "2023"
        "50%¶\x13\x13\x13": "RMB'000"
        """
        # 若存在超过8个\n，认为是竖排版，清除所有\n  TODO 后续按需要按照竖版处理
        if self.text.count("\n") > 8:
            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7556#note_748495
            fixed_text = self.text.replace("\n", "")
        else:
            fixed_text = self.text
        if self.is_col_header and self.text:
            for src, dst in BAD_HEADER_REPLACE_MAP.items():
                fixed_text = fixed_text.replace(src, dst)
        return fixed_text

    @property
    def headers(self) -> List["ParsedTableCell"]:
        return self.row_header_cells + self.col_header_cells

    @cached_property
    def subtitle_cells(self) -> List["ParsedTableCell"]:
        ret = []
        for row_idx, col_idx in self.subtitle_idxes:
            ret.append(self.table.rows[row_idx][col_idx])
        return ret

    @cached_property
    def sub_total_headers(self) -> List["ParsedTableCell"]:
        """
        | subtotal |   |
        |     A   | 10 |
        |    B    | 20 |
        |         | 30 |
        30对应的单元格的 sub_total_headers 是 subtotal
        类似于上面30的单元格有可能有三个特征 左边的空白（row_headers） 上面的时间（col_headers）以及 subtotal

        43512
        | subtotal/subtitle |   |
        | invalid_subtotal|     |
        |     A           | 10 |
        |    B            | 20 |
        |                 | 30 |

        42512
        | invalid_subtotal/subtitle|     |
        | subtotal         |   |
        |     A           | 10 |
        |    B            | 20 |
        |                 | 30 |
        stock=00966,year=2024
        | invalid_subtotal/subtitle|     |
        | subtotal         |    |
        | A                | 100|
        | B                |    |
        |    B-1           | 10 |
        |    B-2           | 20 |
        |                  | 30 |
        """

        def is_valid_cell(_cell):
            cells = []
            for item in _cell.table.rows[_cell.rowidx]:
                if item.is_header:
                    # 特殊表格 没有非流动资产的小类
                    if P_TOTAL_ASSETS.nexts(clean_txt(item.text)):  # 51510
                        return True
                    continue
                for col_header in item.col_header_cells:
                    if P_INVALID_SUBTOTAL_HEADER.nexts(clean_txt(col_header.text)):
                        break
                else:
                    cells.append(item)
            return all(not i.text for i in cells)

        if any(self.text == h.text for h in self.headers):  # 忽略跨页的表头
            return []

        # indented_in_above_rows = False
        ignore_sub_title = False
        # 当前行的行头，就是total assets，说明subtotal就是自己，不能向回找
        # http://************:55647/#/project/remark/379657?treeId=15729&fileId=113001&schemaId=29&projectId=17 stock=436, year=2024
        if self.row_header_cells and P_SUBTOTAL_ROW_HEADER.nexts(
            "\t".join(c.clean_text for c in self.row_header_cells)
        ):
            return self.row_header_cells
        ret = []
        for cell in self.table.cols[self.colidx][: self.rowidx][::-1]:
            # 在row_header_cells 和 其subtitle_cells 中优先选择匹配正则的cell
            # 这里的正则 SUB_TOTAL_PATTERN 在其他的schema可能需要修改  目前只适用于 schema 24 ratio check
            if all(not c.text for c in cell.row_header_cells):  # 如果该单元格上面的单元格没有行头, 忽略
                continue
            if any(P_SUBTOTAL_ROW_HEADER.nexts(clean_txt(c.text)) for c in cell.row_header_cells):
                ignore_sub_title = True
                break

            if self.is_indented_region_bottom(cell) and (
                not self.subtitle_idxes or not cell.subtitle_idxes or cell.subtitle_idxes[0] == self.subtitle_idxes[0]
            ):
                # fixme: 这里调用这个方法并没用体现出是最后一行 上面的方法是从下往上遍历 如果是缩进块的最后一行， 那么第一次循环就会
                # fixme: 跳出， 没有机会遍历到最上面的sub total 比如 (non-)Current assets
                # TODO: 可以尝试对已经拿到的数据做加总是否=当前行的总计值来判断这里是否要break
                # or cell.is_indented_row():  # 该行是一个缩进块的最后一行,跳出
                # indented_in_above_rows = True
                break
            if is_valid_cell(cell):
                possible_subtotal = []
                if cell.row_header_cells:
                    possible_subtotal.extend(cell.row_header_cells)
                    # 这里不能直接追加subtitle的父subtitle，因为subtotal行不一定是父subtitle的total
                    # if cell.row_header_cells[0].subtitle_idxes:
                    #     possible_subtotal.extend(cell.row_header_cells[0].subtitle_cells)
                    all_rows = {c.rowidx for c in possible_subtotal}
                    for subtitle_cell in self.subtitle_cells:
                        if subtitle_cell.rowidx in all_rows:
                            continue
                        possible_subtotal.append(subtitle_cell)
                for subtotal in possible_subtotal:
                    if P_SUB_TOTAL.nexts(subtotal.no_cn_text):
                        ret.append(subtotal)
                        break
            if ret:
                break
            if self.subtitle_idxes and cell.rowidx == max(r for r, _ in self.subtitle_idxes):
                # 找到最近的subtitle就终止，不能过度向上找
                # stock_code=00966, year=2024, mid=29, index=2315, self.rowidx=8
                break
        if not ret:
            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6172#note_734930 stock=09688, year=2024
            # http://************:55647/#/project/remark/414431?treeId=6822&fileId=114141&schemaId=29&projectId=17&page=51 stock=01038, year=2024 index=10800
            # 向前找到某个total终止，则向下找最近的total
            for cell in self.table.cols[self.colidx][self.rowidx + 1 :]:
                if all(not c.text for c in cell.row_header_cells):
                    continue
                header_text = clean_txt("\t".join(c.clean_text for c in cell.row_header_cells), remove_cn_text=True)
                if not P_SUB_TOTAL.nexts(header_text):
                    continue
                # 这里必须以total开头，才能取
                first_word = header_text.split(maxsplit=1)[0].lower()
                if first_word == "total":
                    ret.extend(cell.row_header_cells)
                # 找到任意一个合计行，则终止向下找
                break
        if not ret and not ignore_sub_title:
            ret.extend(self.subtitle_cells)
        return ret

    @property
    def outline(self) -> Tuple[float, float, float, float]:
        return self.raw_cell["box"]

    @staticmethod
    def chars_left_border(raw_cell: dict) -> float:
        skip_chars = ["-", "—", "–", " "]
        for char in raw_cell["chars"]:
            if not char["text"].strip() or char["text"] in skip_chars:
                continue
            return char["font_box"][0]
        return raw_cell["box"][0]

    @property
    def page(self) -> int:
        return self.raw_cell["page"]

    @property
    def indexstr(self) -> str:
        return self.raw_cell["index"]

    @property
    def table(self) -> ParsedTable:
        return self.region.table

    @property
    def original(self) -> PdfinsightTableCell:
        if not self.dummy:
            return self
        if self.merge_to:
            return self.table.cell(*self.merge_to)
        return None

    def parse_unit(self):
        def get_unit_slice(match_res, text):
            match_text = match_res.group()
            dst_value = match_res.group("dst")
            offset = match_text.index(dst_value)
            _start = match_res.span()[0] + offset
            _end = _start + len(dst_value)
            _start, _end = index_in_space_string(text, (_start, _end))
            return slice(*(_start, _end))

        unit = None
        if self.is_header:
            matches = list(header_cell_unit_patterns.finditer(clean_txt(self.text)))
            if matches:
                unit_slice = get_unit_slice(matches[-1], self.text)
                unit = CharResult(self.table.element, self.raw_cell["chars"][unit_slice])
        else:
            matches = list(data_cell_unit_patterns.finditer(self.text))
            if matches:
                unit_slice = get_unit_slice(matches[-1], self.text)
                unit = CharResult(self.table.element, self.raw_cell["chars"][unit_slice])

            if not unit:
                for inherited_unit in [cell.unit for cell in self.col_header_cells + self.row_header_cells] + [
                    self.region.table.unit
                ]:
                    if inherited_unit:
                        unit = inherited_unit
                        break
        self.unit = unit

    def is_indented_region_bottom(self, cell):
        """
        该行是否是缩进的块的最后一行
        """
        # 最后一行
        if cell.rowidx + 1 >= len(self.table.cols):
            return True
        below_cell = self.table.cols[cell.colidx][cell.rowidx + 1]
        if not (cell.row_header_cells and below_cell.row_header_cells):  # 无法判断
            return False

        row_header_chars_left = self.chars_left_border(cell.row_header_cells[0].raw_cell)
        below_header_chars_left = self.chars_left_border(below_cell.row_header_cells[0].raw_cell)

        row_header_chars = []
        for header_cell in cell.row_header_cells:
            row_header_chars.extend(header_cell.raw_cell["chars"])
        average_char_width = get_avg_width(row_header_chars)  # 字符的平均宽度

        if row_header_chars_left - below_header_chars_left > average_char_width:
            return True
        return False

    def is_indented_row(self):
        """
        此单元格所在行是否是一个缩进行
        """
        left_margin_cell = self.table.cell(self.rowidx, 0)
        if sub_option_pattern.nexts(clean_txt(left_margin_cell.text)):
            return True
        return self.table.indented_tag_for_rows[self.rowidx]


def parse_footnotes(pdfinsight: PdfinsightReader, table_element: dict, is_shape=False):
    footnotes = []
    next_elem_index = table_element["index"]
    current_page = table_element["page"]
    # 空白页没有元素块 element_dict的keys里可能会缺失某一页
    while True:
        next_elem_index += 1
        try:
            ele_type, elem = pdfinsight.find_element_by_index(next_elem_index)
        except IndexError:
            break
        if not elem:
            # Note: 如果遍历到文件最后，这里的element会一直是None,会死循环
            continue
        if elem["page"] > current_page + 1:  # 有可能跨页
            break
        if ele_type == "FOOTNOTE":
            footnotes.append(elem)
        if ele_type != "FOOTNOTE":
            break

    if footnotes:
        return footnotes

    return parse_footnotes_again(pdfinsight, table_element, is_shape=is_shape)


def parse_footnotes_again(pdfinsight: PdfinsightReader, table_element: dict, is_shape=False):
    """
    TODO 根据单元格中的Note序号来取下方的footnotes
    TODO 考虑跨页连续表格的脚注在每个表格后面
    """
    if not is_shape and len(table_elements := pdfinsight.continuous_tables(table_element, only_after=True)) > 1:
        # 先找未正确识别的跨页连续表格
        last_tbl_element = table_elements[-1]
        next_elem_index = last_tbl_element["index"]
        current_page = last_tbl_element["page"]
        table_left, _, table_right, _ = last_tbl_element.get("outline") or [None, None, None, None]
    else:
        # 跨页连续表格的脚注在最后一个表格的后面
        # http://************:55647/#/project/remark/233958?treeId=5370&fileId=66615&schemaId=15&projectId=17&schemaKey=B77&page=88
        merged_indices = page_merged_table_indices(table_element)
        if len(merged_indices) > 1:
            next_elem_index = max(int(idx) for idx in merged_indices)
            _, ele = pdfinsight.find_element_by_index(next_elem_index)
            current_page = ele["page"]
            table_left, _, table_right, _ = ele.get("outline") or [None, None, None, None]
        else:
            next_elem_index = table_element["index"]
            current_page = table_element["page"]
            table_left, _, table_right, _ = table_element.get("outline") or [None, None, None, None]
    # Note可能不是挨着表格，先在表格附近的10个元素块附近找Notes:
    # http://************:55647/#/project/remark/416471?treeId=7054&fileId=114369&schemaId=18&projectId=17&schemaKey=C2.1.1&page=151 tbl_index=1678
    before_elements = []
    footnotes = []
    for i in range(1, 10):
        next_index = next_elem_index + i
        try:
            ele_type, elem = pdfinsight.find_element_by_index(next_index)
        except IndexError:
            break
        if not elem or is_table_elt(elem) or pdfinsight.syllabus_reader.is_syllabus_elt(elem):
            break
        if not pdfinsight.is_skip_element(elem):
            before_elements.append(elem)
        if is_para_elt(elem) and P_ONLY_NOTES.search(elem["text"]):
            next_elem_index = next_index
            footnotes.extend(before_elements)
            break

    last_text = None
    found_start = False
    note_line_left, base_last_line_left = 0, 0
    base_fontsize = None
    skipped_one = False
    offset = 0
    while True:
        next_elem_index += 1
        try:
            ele_type, elem = pdfinsight.find_element_by_index(next_elem_index)
        except IndexError:
            break
        if not elem:
            continue
        # 脚注最多三页
        if elem["page"] > current_page + 3 + offset:
            break
        if ele_type == PDFInsightClassEnum.TABLE.value and footnotes:
            if last_text and FOOTNOTE_AS_FOLLOW_PATTERN.search(last_text):
                # 脚注中带了表格
                # http://************:55647/#/project/remark/233958?treeId=5370&fileId=66615&schemaId=15&projectId=17&schemaKey=B77&page=90
                # 多个表格在一起，最后才是脚注
                # http://************:55647/#/project/remark/234432?treeId=37922&fileId=66694&schemaId=15&projectId=17&schemaKey=B76
                if not footnotes:
                    offset = elem["page"] - current_page
                continue
            break
        if pdfinsight.is_skip_element(elem, outline_left=table_left, outline_right=table_right):
            continue
        if ele_type not in {PDFInsightClassEnum.PARAGRAPH.value, PDFInsightClassEnum.FOOTNOTE.value}:
            break
        text = clean_txt(elem.get("text") or "", remove_cn_text=True)
        # http://************:55647/#/project/remark/258043?treeId=3543&fileId=68823&schemaId=15&projectId=17&schemaKey=B74&page=185
        # http://************:55647/#/project/remark/266418?treeId=17250&fileId=70498&page=151 每个页面都带了for the year end xxx
        if PC_ONLY_DATE.nexts(text):
            # 页面开始的章节下方带了日期（即页面的第二个元素块），这种日期需要跳过
            # http://************:55647/#/project/remark/265624?treeId=9782&fileId=70339&schemaId=18&projectId=9782&schemaKey=C2.2.1 index=1540
            page_elements = [
                e
                for e in pdfinsight.page_element_dict.get(elem["page"]) or []
                if e.data["class"] != PDFInsightClassEnum.PAGE_HEADER.value
            ]
            if len(page_elements) > 1 and next_elem_index <= page_elements[1].index:
                continue
            break
        last_text = text
        if syllabus := pdfinsight.syllabus_reader.elt_syllabus_dict.get(next_elem_index):
            if syllabus["parent"] == -1:
                continue
            # 段落被错误识别为章节 https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/3168#note_547222
            if not syllabus["title"].endswith((".", ";", "；", ":", "：")):
                # 遇到不是一级章节的章节则终止
                break
        element_left, last_line_left = elem["outline"][0], outline_left_of_last_line(elem)
        if P_NOTES.search(text):
            if not found_start:
                found_start = True
            footnotes.append(elem)
            note_line_left = element_left
            base_last_line_left = last_line_left
            base_fontsize = pdfinsight.get_font_size(elem["chars"])
            continue
        if matched := FOOTNOTE_START_PATTERN.nexts(text):
            if not footnotes and not P_START_FIRST.search(matched.group("num")):
                # 没取到任何脚注时，第一个脚注序号必须为起始序号，例如序号1，i，a或者note xx
                # http://************:55647/#/project/remark/264738?treeId=10611&fileId=70162&schemaId=15&projectId=17&schemaKey=B74
                break
            note_line_left = element_left
            base_last_line_left = last_line_left
            if not base_fontsize:
                base_fontsize = pdfinsight.get_font_size(elem["chars"])
            footnotes.append(elem)
        elif found_start or len(footnotes) > 0:
            if found_start and len(footnotes) == 1 and P_ONLY_NOTES.search(footnotes[0]["text"]):
                # notes下方没有序号，直接取下方第一个段落
                footnotes.append(elem)
                base_fontsize = pdfinsight.get_font_size(elem["chars"])
                note_line_left = element_left
                base_last_line_left = last_line_left
                continue
            fontsize = pdfinsight.get_font_size(elem["chars"])
            # 字体比notes行字体大，则终止查找
            # http://************:55647/#/project/remark/268322?treeId=10008&fileId=70524&schemaId=18&projectId=17&schemaKey=C2.7&page=9 tbl_index=164
            # http://************:55647/#/project/remark/265914?treeId=22819&fileId=70397&schemaId=18&projectId=17&schemaKey=C2.7&page=13 tbl_index=187
            if fontsize and base_fontsize and base_fontsize < fontsize:
                break
            # 按照缩进取： 当前元素块左边界与最近的小序号最后一行的缩进相同，或>最近小序号最后一行的缩进
            # http://************:55647/#/project/remark/257848?treeId=14793&fileId=68784&schemaId=15&projectId=17&schemaKey=B74&page=30 table_index=284
            # http://************:55647/#/project/remark/268890?treeId=3137&fileId=70604&schemaId=18&projectId=17&schemaKey=C2.1.1&page=129 table_index=1889
            # http://************:55647/#/project/remark/296224?treeId=37978&fileId=71003&schemaId=18&projectId=17&schemaKey=C2.1.1&page=223 table_index=2562
            if element_left - note_line_left > 15 and (
                element_left > base_last_line_left or abs(element_left - base_last_line_left) < 1
            ):
                footnotes.append(elem)
            else:
                break
        else:
            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/4147#note_485214
            # 表格下方有一段话，之后才开始notes
            if not skipped_one:
                skipped_one = True
                continue
            break
    return footnotes


def parse_table(
    element: Union[PdfinsightTable, dict],
    tabletype=None,
    elements_above=None,
    pdfinsight_reader=None,
    width_from_all_rows=False,  # ParsedTableRegion.width 是否取自表格宽度最长的一行的宽度 默认false, 即取第一行的宽度
    *,
    extra_title_patterns: List[str] = None,
) -> ParsedTable:
    if isinstance(element, dict):
        element = PdfinsightTable(element)
    footnotes = []
    pdf_md5 = None
    # 存在合并表格时，后续表格的列名也会被合并，且容易被当作数据行误判，这里存储被合并表格的列名对应的page+box，后续按需取用
    merged_header_boxes = {}
    if pdfinsight_reader:
        pdf_md5 = "".join(pdfinsight_reader.path.split("/")[-2:])
        if elements_above is None:
            elements_above = pdfinsight_reader.find_elements_near_by(
                element.index,
                amount=30,
                step=-1,
                aim_types=["PARAGRAPH", "SYLLABUS"],
                neg_patterns=INVALID_TABLE_TITLE,
                stop_at_syllabus=True,
            )
        footnotes = parse_footnotes(pdfinsight_reader, element.element)
        for index in page_merged_table_indices(element.data):
            if index == element.index:
                continue
            _, next_element = pdfinsight_reader.find_element_by_index(index)
            next_table = ParsedTable(
                PdfinsightTable(next_element),
                tabletype=tabletype,
                elements_above=elements_above,
                footnotes=footnotes,
                width_from_all_rows=width_from_all_rows,
                extra_title_patterns=extra_title_patterns,
                pdf_md5=pdf_md5,
            )
            page = next_element["page"]
            for row in next_table.rows:
                if not row[0].is_col_header:
                    break
                if page in merged_header_boxes:
                    merged_header_boxes[page].extend(c.raw_cell["box"] for c in row)
                else:
                    merged_header_boxes[page] = [c.raw_cell["box"] for c in row]

    return ParsedTable(
        element,
        tabletype=tabletype,
        elements_above=elements_above,
        footnotes=footnotes,
        width_from_all_rows=width_from_all_rows,
        extra_title_patterns=extra_title_patterns,
        pdf_md5=pdf_md5,
        merged_header_boxes=merged_header_boxes,
    )
