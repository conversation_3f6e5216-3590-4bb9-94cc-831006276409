import logging
import re
from collections import Counter, defaultdict
from copy import deepcopy
from dataclasses import dataclass
from difflib import SequenceMatcher
from functools import cached_property
from typing import Any, Iterable, Self, Union

from interdoc.base import Box
from interdoc.chapter import ChapterNode

from remarkable.common.common_pattern import P_CHAPTER_PREFIX, P_CONTINUED, P_FOLLOW_PREFIX, R_MIDDLE_DASH
from remarkable.common.constants import PDFInsightClassEnum
from remarkable.common.element_util import FontStyle, calc_line_spacing, get_avg_height, is_continue_line
from remarkable.common.pattern import PatternCollection
from remarkable.common.util import P_CHNS, P_SPECIAL_PRIORI, P_WHITE
from remarkable.pdfinsight.reader_util import (
    P_BLANK,
    P_CONTENTS,
    P_ENSURE_ROMAN,
    P_LOWER_CASE,
    P_NOT_WORD,
    UnifiedElement,
    UnifiedParaElement,
    clean_title,
    fill_middle_page_title,
    get_prefix_format,
    get_prefix_num,
    is_same_title,
    is_sentence_like,
    is_valid_title,
    page_first_title,
    prefix_words,
)

logger = logging.getLogger(__name__)


@dataclass
class ChapterRange:
    pages: list[int]
    start_page: int
    start: int
    end: int


@dataclass
class UnifiedChapterBase:
    """
    统一pdfinsight和interdoc的章节信息
    """

    chapter: dict | ChapterNode

    def __hash__(self):
        return hash(self.index)

    def __bool__(self):
        return bool(self.chapter)

    def __str__(self):
        return (
            f"index={self.index}[{self.element_index}], is_root={self.is_root}, page={self.page}, "
            f"level={self.level}, title={self.title}, range=[{self.start}, {self.end}]"
        )

    @property
    def index(self):
        return self.chapter.index if self.is_interdoc else self.chapter["index"]

    @cached_property
    def is_interdoc(self) -> bool:
        """
        判断当前chapter是否为interdoc类型。
        :return: True表示是interdoc类型，False表示不是。
        """
        return isinstance(self.chapter, ChapterNode)

    @property
    def element_index(self) -> int:
        return self.chapter.element if self.is_interdoc else self.chapter["element"]

    @property
    def title(self):
        return self.chapter.title if self.is_interdoc else self.chapter["title"]

    @property
    def page(self):
        return self.chapter.page if self.is_interdoc else self.chapter["dest"]["page_index"]

    @property
    def level(self):
        return self.chapter.level if self.is_interdoc else self.chapter["level"]

    @property
    def start(self) -> int:
        return self.chapter.start if self.is_interdoc else self.chapter["range"][0]

    @property
    def end(self) -> int:
        return self.chapter.end if self.is_interdoc else self.chapter["range"][1]

    @property
    def is_root(self):
        return self.parent_index == -1

    @property
    def has_parent(self):
        return self.parent_index and self.parent_index != -1

    @property
    def parent_index(self) -> int:
        return self.chapter.parent_id if self.is_interdoc else self.chapter["parent"]

    @property
    def children_indices(self) -> list[int]:
        if self.is_interdoc:
            # pass
            return []
            # return [node.index for node in self.chapter_map.values() if node.parent_id == self.index]
        return self.chapter.get("children") or []

    def set_index(self, index):
        self.__set_value("index", index)

    def set_page(self, page):
        if self.is_interdoc:
            self.chapter.page = page
        else:
            self.chapter["dest"]["page_index"] = page

    def set_box(self, box: Box | list):
        if self.is_interdoc:
            self.chapter.box = box
        else:
            self.chapter["dest"]["box"] = box

    def set_start(self, start: int):
        if self.is_interdoc:
            self.chapter.start = start
        else:
            self.chapter["range"][0] = start

    def set_end(self, end: int):
        if self.is_interdoc:
            self.chapter.end = end
        else:
            self.chapter["range"][1] = end

    def set_level(self, level: int):
        self.__set_value("level", level)

    def set_title(self, title: str):
        self.__set_value("title", title)
        # logger.debug(f"reset title: {self}")

    def set_element_index(self, index: int):
        self.__set_value("element", index)
        # logger.debug(f"reset element index: {self}")

    def set_parent(self, parent_index: int | None):
        self.__set_value("parent", parent_index)

    def set_children(self, children_indices: list[int]):
        if self.is_interdoc:
            return
        self.__set_value("children", children_indices)

    def add_children(self, children_indices: list[int]):
        children_set, cur_children_set = set(children_indices), set(self.children_indices)
        if self.is_interdoc or children_set.issubset(cur_children_set):
            return
        self.__set_value("children", sorted(cur_children_set | children_set))

    def __set_value(self, attr, value):
        if self.is_interdoc:
            setattr(self.chapter, attr, value)
        else:
            self.chapter[attr] = value


class UnifiedChapter(UnifiedChapterBase):
    def __init__(
        self,
        chapter: dict | ChapterNode,
        reader: Union["PdfinsightReader", "InterdocReader"],  # noqa
    ):
        super().__init__(chapter)
        self.chapter = chapter
        self.reader = reader

    @cached_property
    def added_space_title(self):
        return UnifiedParaElement(self.element.element, self.reader).added_space_text

    @cached_property
    def cleaned_title(self):
        # 注意： title的名称可能和added_space_title对应的元素块不是同一个
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/3168#note_583455
        text = (
            self.added_space_title
            if self.start == self.element_index
            and len(P_NOT_WORD.sub("", self.title)) <= len(P_NOT_WORD.sub("", self.added_space_title))
            and not P_SPECIAL_PRIORI.search(self.title)
            else self.title
        )
        return clean_title(text, remove_bound_num=self.is_root)

    @cached_property
    def no_prefix_title(self):
        return P_CHAPTER_PREFIX.sub("", self.cleaned_title).strip()

    @cached_property
    def element(self) -> UnifiedElement | None:  # noqa
        return find_element(self.reader, self.element_index, self.is_interdoc)

    @cached_property
    def border_left(self) -> float:
        return self.chapter.box.left if self.is_interdoc else self.chapter["dest"]["box"][0]

    @cached_property
    def border_left_without_prefix(self) -> float | None:
        return self.element.border_left_without_prefix

    @cached_property
    def font_style(self) -> FontStyle | None:
        """
        TODO interdoc拿不到chars和styles
        """
        return UnifiedParaElement(self.element.element, self.reader).font_style

    @cached_property
    def prefix_words(self) -> str:
        """
        标题中的序号，不包含括号等格式， 例如: 1, a, iii等
        """
        return prefix_words(self.added_space_title)

    @cached_property
    def prefix_format(self):
        """
        标题前缀格式，例如(A)
        """
        return (
            get_prefix_format(self.cleaned_title) or UnifiedParaElement(self.element.element, self.reader).prefix_format
        )

    def prefix_num(self, is_roman: bool = False) -> int:
        """
        标题中的序号
        """
        return get_prefix_num(self.cleaned_title, is_roman) or get_prefix_num(self.added_space_title, is_roman)

    @cached_property
    def is_upper(self):
        return not P_LOWER_CASE.search(self.no_prefix_title) or not P_LOWER_CASE.search(self.element.no_prefix_title)

    @cached_property
    def is_sentence_like(self):
        return is_sentence_like(self)

    @cached_property
    def is_valid_title(self) -> bool:
        """
        是否为合法的目录
        """
        if not self.element.is_paragraph:
            return False
        return is_valid_title(self)

    def is_valid(self, body_font_style: FontStyle, strict=True):
        """
        TODO: 是否要保留所有有prefix的章节，即strict默认为Fasle
        """
        if not self.is_valid_title:
            return False
        # 全大写或者加粗的目录，认为是正确目录 66818 element_index=1700
        if self.is_upper or (self.font_style and self.font_style.bold):
            return True
        if not strict and self.prefix_words:
            return True
        if self.is_sentence_like:
            if body_font_style and self.font_style.is_normal_style(body_font_style):
                return False
            return bool(self.prefix_format)
        return True

    def is_same_style(self, other: Self | UnifiedParaElement):
        if not self.font_style or not other.font_style:
            return False
        if self.is_root:
            return self.font_style.without_color == other.font_style.without_color
        return self.font_style == other.font_style

    def is_same_border(self, other: Self | UnifiedElement):
        return self.border_left_without_prefix == other.border_left_without_prefix


class ChapterFixer:
    P_ONE_LETTER_TITLE = re.compile(r"(\w\s){3,}", re.I)
    P_COVER = re.compile(r"\b(COVER|FRONT)\s*$", re.I)
    # 满足以下正则，则不合并
    P_NOT_MERGE = PatternCollection(
        [
            rf"([.:：;；]|{R_MIDDLE_DASH})$",
            rf"^(independent\s*)?(non{R_MIDDLE_DASH})?executive\s*directors?[:：]?\s*$",
            r"^(chair[a-z]+|I?NEDs?|ceo)\s*$",
            r"^(Chief\s*)?Executive\s*Officer\s*$",
        ],
        flags=re.I,
    )

    def __init__(self, reader: Union["PdfinsightReader", "InterdocReader"], chapters: Iterable[dict | ChapterNode]):  # noqa
        self.reader = reader
        self.chapters = [deepcopy(chapter) for chapter in chapters]
        self.added_indices = set()
        self._build_chapter_map()

    @cached_property
    def is_interdoc(self) -> bool:
        return not isinstance(self.chapters[0], dict)

    @cached_property
    def page_of_contents(self):
        for page, pager in self.reader.page_dict.items():
            if pager.is_catalog:
                return page
        return 0

    @cached_property
    def body_font_style(self):
        """
        取出正文的样式
        """
        if self.is_interdoc:
            return None
        for page, elements in self._page_paragraphs.items():
            if page < self.page_of_contents + 10 or len(elements) < 6:
                continue
            for element in elements:
                if (
                    element.index in self.elt_chapter_map
                    or not element.is_multi_line
                    or not element.cleaned_title.endswith(".")
                    or len(element.text.split()) < 200
                ):
                    continue
                return UnifiedParaElement(element.element, self.reader).font_style
        return None

    def get_chapters(self):
        return self.chapters

    def _find_element(self, index: int) -> UnifiedElement | None:
        return find_element(self.reader, index, self.is_interdoc)

    def _get_chapter(self, index: int) -> UnifiedChapter:
        return self.chapter_map.get(index)

    def _build_chapter_map(self):
        self.root_chapters, self.chapter_map, self.elt_chapter_map = [], {}, {}
        for chapter in self.chapters:
            chapter = UnifiedChapter(chapter, self.reader)
            self.chapter_map[chapter.index] = chapter
            self.elt_chapter_map[chapter.element_index] = chapter
            if chapter.is_root:
                self.root_chapters.append(chapter)

    def fix_child_chapters(self):
        """
        修复子章节，如果有变化，则需要重新遍历
        """
        last_start_index, start_index = -1, 0
        while last_start_index != start_index:
            last_start_index = start_index
            for index, chapter in sorted(self.chapter_map.items(), key=lambda x: x[0]):
                if not chapter.children_indices or index < start_index:
                    continue
                # 存在新增或删除章节，则重新遍历所有章节
                if self._fix_children(chapter):
                    start_index = index + 1
                    break

    def _fix_children(self, chapter: UnifiedChapter) -> bool:
        """
        遍历当前章节下的所有段落，根据已有子章节的样式+前缀样式，找出正确的子章节，错误的子章节设置parent为None
        返回True 表示有新增或者删除章节
        """
        added_before = len(self.added_indices)
        is_roman = any(P_ENSURE_ROMAN.search(self.chapter_map[idx].prefix_words) for idx in chapter.children_indices)
        # 找出子章节的序号和字体样式
        has_multi_para, child_style = self._find_child_style(chapter, is_roman)
        if not child_style:
            return False
        if (
            len(chapter.children_indices) == 1
            and self.body_font_style
            and child_style[1].fontsize <= self.body_font_style.fontsize
            and child_style[1].fontcolor == self.body_font_style.fontcolor
        ):
            if not has_multi_para:
                # 所有段落都是一句话，说明子章节识别错误
                self._remove_invalid_indices(chapter.children_indices)
                chapter.set_children([])
                return True
            # 正文样式情况复杂，不做修正
            return False
        # 找出子章节中样式相同的子章节
        same_style_children = [
            self.chapter_map[idx]
            for idx in chapter.children_indices
            if self._get_child_style(self.chapter_map[idx], is_roman) == child_style
        ]
        if not same_style_children:
            chapter.set_children([])
            return False
        same_style_nums = [n for n in [chapter.prefix_num(is_roman) for chapter in same_style_children] if n > 0]
        same_style_elt_indices = [child.element_index for child in same_style_children]
        if not same_style_nums or len(same_style_nums) != len(same_style_children):
            new_children = self._find_children_by_range(
                chapter, child_style, is_roman, same_style_elt_indices, chapter.start
            )
            if abs(len(new_children) - len(chapter.children_indices)) > 3:
                # 没有前缀时，提取到的子章节过多或过少，可能是提取错误，不处理
                return False
        else:
            new_children = self._find_children_by_prefix_num(
                chapter, child_style, is_roman, same_style_elt_indices, same_style_nums
            )
        old_children = [self.chapter_map[idx] for idx in chapter.children_indices]
        new_child_indices = []
        for i, new_child in enumerate(new_children):
            if new_child.index in new_child_indices:
                continue
            if new_child.parent_index != chapter.index:
                new_child.set_parent(chapter.index)
            new_child_indices.append(new_child.index)
            if i < len(new_children) - 1:
                if new_child.end != new_children[i + 1].start:
                    new_child.set_end(new_children[i + 1].start)
            elif new_child.end != chapter.end:
                new_child.set_end(chapter.end)
            if new_child.parent_index != chapter.index:
                self._remove_from_parent(new_child)
                new_child.set_parent(chapter.index)
        chapter.set_children(new_child_indices)
        for child in old_children:
            if child.index in new_child_indices:
                continue
            child.set_parent(None)
        return added_before != len(self.added_indices)

    @staticmethod
    def _get_child_style(
        chapter_or_element: UnifiedChapter | UnifiedParaElement, is_roman: bool
    ) -> tuple[str | None, FontStyle | None]:
        """
        只处理有前缀的子章节，无前缀的章节情况复杂，不处理
        """
        if chapter_or_element.prefix_format:
            if is_roman or P_ENSURE_ROMAN.search(chapter_or_element.prefix_words):
                prefix_fmt = chapter_or_element.prefix_format.replace("a", "i").replace("A", "I")
            else:
                prefix_fmt = chapter_or_element.prefix_format.replace("i", "a").replace("I", "A")
            return prefix_fmt, chapter_or_element.font_style.size_and_color
        return None, None

    def _find_child_style(self, chapter: UnifiedChapter, is_roman: bool):
        child_styles = {self._get_child_style(self.chapter_map[idx], is_roman) for idx in chapter.children_indices}
        if any(style[0] is None for style in child_styles):
            return None, None
        has_multi_line_para = False
        child_style = None
        for index in range(chapter.start + 1, chapter.end):
            element = self._find_element(index)
            if not element.is_paragraph:
                continue
            if element.is_multi_line:
                if child_style:
                    return True, child_style
                has_multi_line_para = True
            para_element = UnifiedParaElement(element.element, self.reader)
            if not (para_element.prefix_format or para_element.font_style) or not para_element.is_valid_title:
                continue
            if not child_style:
                para_style = self._get_child_style(para_element, is_roman)
                # 第一个出现的样式或第一个出现的已有子章节的样式，作为实际子章节的样式
                if para_style in child_styles or (
                    para_element.prefix_format and not para_element.is_sentence_like and index == chapter.start + 1
                ):
                    child_style = para_style
        return has_multi_line_para, child_style

    def _find_children_by_prefix_num(
        self,
        chapter: UnifiedChapter,
        child_style: tuple[str, FontStyle, bool],
        is_roman: bool,
        same_style_elt_indices: list[int],
        same_style_nums: list[int],
    ):
        new_children = []
        last_prefix_num = 0
        if len(same_style_nums) > 1:
            for i, prefix_num in enumerate(same_style_nums, start=1):
                if prefix_num != last_prefix_num + 1:
                    start = new_children[-1].start if new_children else chapter.start
                    end = chapter.end if i - 1 == len(same_style_elt_indices) else same_style_elt_indices[i - 1]
                    new_children.extend(
                        self._find_children_by_range(
                            chapter,
                            child_style,
                            is_roman,
                            start=start,
                            end=end,
                            prefix_num_start=last_prefix_num + 1,
                            prefix_num_end=prefix_num,
                        )
                    )
                new_children.append(self.elt_chapter_map[same_style_elt_indices[i - 1]])
                last_prefix_num = prefix_num
        new_children.extend(
            self._find_children_by_range(
                chapter,
                child_style,
                is_roman,
                start=new_children[-1].start if new_children else chapter.start,
                prefix_num_start=last_prefix_num + 1,
            )
        )
        return new_children

    def _find_children_by_range(
        self,
        chapter: UnifiedChapter,
        child_style: tuple[str, FontStyle, bool],
        is_roman: bool,
        same_style_elt_indices: list[int] = (),
        start: int = None,
        end: int = None,
        prefix_num_start: int = None,
        prefix_num_end: int = None,
    ) -> list[UnifiedChapter]:
        """
        遍历章节下所有段落，找出符合child_style样式的章节
        """
        new_children = []
        end = end or chapter.end
        for index in range((start or chapter.start) + 1, end):
            if index > end:
                break
            if index in same_style_elt_indices:
                new_children.append(self.elt_chapter_map[index])
                continue
            element = self._find_element(index)
            if not element or not element.is_paragraph:
                continue
            para_element = UnifiedParaElement(element.element, self.reader)
            if not para_element.is_valid_title:
                continue
            if not (para_element.prefix_format or para_element.font_style):
                continue
            para_prefix, para_style = self._get_child_style(para_element, is_roman)
            if para_style != child_style[1] or not (chapter.level == 1 or para_prefix == child_style[0]):
                continue
            if new_children and index == new_children[-1].element_index + 1:
                # 存在相邻目录，说明取错
                return []
            if is_roman and not set(para_element.prefix_words.lower()).issubset({"i", "v", "x"}):
                continue
            prefix_num = get_prefix_num(para_element.added_space_text, is_roman)
            if chapter.level == 1 and para_prefix != child_style[0] and para_prefix.endswith(".1"):
                # http://100.64.0.105:55647/#/project/remark/234414?treeId=38065&fileId=66691&page=105 index=1306 子章节1的兄弟章节是2.1
                prefix_num = int(para_prefix.split(".")[0])
            if prefix_num_start and (
                prefix_num < prefix_num_start or not new_children and prefix_num != prefix_num_start
            ):
                # 没找到开始序号，说明取错
                return []
            if prefix_num_end and prefix_num > prefix_num_end:
                break
            if para_element.index in self.elt_chapter_map:
                real_child = self.elt_chapter_map[para_element.index]
            else:
                real_child = self._add_new_chapter(
                    para_element,
                    page=para_element.page,
                    start=index,
                    end=end,
                    level=chapter.level + 1,
                    parent_index=chapter.index,
                    title=para_element.cleaned_title,
                )
                if not real_child:
                    continue
            new_children.append(real_child)
        return new_children

    def fix_levels(self):
        """
        修正目录层级
        """
        for chapter in self.chapter_map.values():
            if chapter.is_root:
                continue
            if chapter.parent_index is None and chapter.page < min(c.page for c in self.root_chapters):
                continue
            if chapter.parent_index is None or chapter.parent_index not in self.chapter_map:
                raise Exception(f"unknown parent chapter: {chapter}")
            parent = self.chapter_map[chapter.parent_index]
            if chapter.level != parent.level + 1:
                chapter.set_level(parent.level + 1)

    def _is_valid_neighbor(self, chapter, element_, last_element_):
        if not (
            element_
            and element_.is_paragraph
            and element_.page == chapter.page
            and element_.position == chapter.element.position
        ):
            return False
        if P_CHNS.search(element_.text):
            return False
        para_elt = UnifiedParaElement(element_.element, self.reader)
        if not para_elt.font_style or para_elt.is_sentence_like:
            return False
        # 样式必须相同
        if not chapter.is_same_style(para_elt):
            return False
        # 行距不能超过1.5倍行高
        avg_height = get_avg_height(element_.chars)
        if calc_line_spacing(element_.element, last_element_.element) > 1.5 * avg_height:
            return False
        # 非一级目录
        if not chapter.is_root:
            # 左侧边差异不能太大
            if (
                last_element_.border_left_without_prefix
                and element_.border_left_without_prefix
                and abs(element_.border_left_without_prefix - last_element_.border_left_without_prefix) > 2
            ):
                return False
            first_elt, second_elt = (
                (last_element_, element_) if element_.index > last_element_.index else (element_, last_element_)
            )
            # # 上一行右侧边+比下一行右侧边小太多，则不取
            # if outline_right_of_first_line(second_elt.element) - first_elt.border_right_of_last_line > 10:
            #     return False
            if not self.is_interdoc and (
                chapter.font_style.bold and chapter.font_style.italic or chapter.font_style.fontsize > 10
            ):
                if is_continue_line(first_elt.text.split()[-1].lower(), second_elt.text.split(maxsplit=1)[0].lower()):
                    return True
            # 上一行与右侧边相距过远，则不取
            pos = tuple(chapter.element.position)
            if pos in self._page_left_right.get(chapter.page, {}):
                _, right_border = self._page_left_right[chapter.page].get(pos) or (0.0, 0.0)
                space_index = second_elt.text.find(" ")
                if space_index == -1:
                    # 向下找时，第二行只有1个单词，可以合并
                    return element_.index > last_element_.index
                # 上行 + 下行的第一个单词宽度 < 右边界，不合并
                first_word_chars = second_elt.chars[: space_index + 1]
                words_width = max(char["box"][2] for char in first_word_chars) - min(
                    char["box"][0] for char in first_word_chars
                )
                if first_elt.border_right_of_last_line + words_width < right_border:
                    return False
        return True

    def _find_same_style_elements(self, chapter: UnifiedChapter) -> list[UnifiedElement]:
        """
        在目录附近找样式相同且index连续的目录
        """
        if not chapter.element:
            return []
        neighbor_elements = [chapter.element]
        no_prev, no_next = P_CHAPTER_PREFIX.search(chapter.cleaned_title), P_CONTINUED.search(chapter.title)
        if no_prev and no_next or self.P_NOT_MERGE.nexts(chapter.cleaned_title):
            return neighbor_elements
        prev_indices = [] if no_prev else [chapter.element_index - offset for offset in range(1, 3)]
        next_indices = [] if no_next else [chapter.element_index + offset for offset in range(1, 3)]
        last_element = chapter.element

        # 向前找三个元素块
        for prev_index in prev_indices:
            element = self._find_element(prev_index)
            # 在当前目录前边，且以continue结尾，不合并
            if P_CONTINUED.search(element.cleaned_title):
                break
            if not self._is_valid_neighbor(chapter, element, last_element):
                break
            neighbor_elements.append(element)
            last_element = element

        # 向后找三个元素块
        last_element = chapter.element
        for next_index in next_indices:
            element = self._find_element(next_index)
            # 在当前目录后边，且以序号开头，不合并
            if P_CHAPTER_PREFIX.search(element.added_space_text) or P_FOLLOW_PREFIX.search(element.added_space_text):
                break
            if not self._is_valid_neighbor(chapter, element, last_element):
                break
            neighbor_elements.append(element)
            last_element = element
        return sorted(neighbor_elements, key=lambda x: x.index)

    def _merge_same_style_elements(
        self, chapter: UnifiedChapter, same_style_elements: list[UnifiedElement], max_end: int
    ) -> set[int]:
        """
        合并章节附近样式相同的元素块，一起作为标题
        """
        if len(same_style_elements) < 2:
            if chapter.title != chapter.cleaned_title:
                chapter.set_title(chapter.cleaned_title)
            return set()
        invalid_indices, child_indices = set(), set(chapter.children_indices)
        for element in same_style_elements:
            if element.index not in self.elt_chapter_map or element.index == chapter.element_index:
                continue
            elt_chapter = self.elt_chapter_map[element.index]
            if elt_chapter.end > max_end:
                max_end = elt_chapter.end
            if elt_chapter.index == chapter.index or elt_chapter.title == chapter.title:
                continue
            child_indices.update(elt_chapter.children_indices)
            invalid_indices.add(elt_chapter.index)
        first_index = same_style_elements[0].index
        if first_index != chapter.element_index:
            chapter.set_start(first_index)
            chapter.set_element_index(first_index)
            chapter.set_box(same_style_elements[0].box)
        chapter.set_end(max_end)
        # 修正标题
        chapter.set_title(
            " ".join(clean_title(e.added_space_text, remove_bound_num=chapter.is_root) for e in same_style_elements)
        )
        child_indices = sorted(child_indices - invalid_indices)
        # 修正子目录及子目录所属父目录
        for child_index in child_indices:
            self.chapter_map[child_index].set_parent(chapter.index)
        # 修正子目录
        chapter.set_children(child_indices)
        return invalid_indices

    def _fix_root_titles(self):
        invalid_indices = set()
        for chapter in self.root_chapters:
            if not chapter.is_root or chapter.index in invalid_indices:
                continue
            same_style_elements = self._find_same_style_elements(chapter)
            max_end = chapter.end
            invalid_indices.update(self._merge_same_style_elements(chapter, same_style_elements, max_end))
            # 判断合并后的目录名称是否合法
            if not chapter.is_valid(self.body_font_style):
                continue
            if chapter.page <= self.page_of_contents:
                # 提取到的章节page为目录页的page
                # http://100.64.0.105:55647/#/hkex/annual-report-checking/report-review/248483?fileId=68203&schemaId=5&rule=B1&page=3
                continue
            child_indices = []
            # 遍历当前页面的一级目录，丢弃多余目录，取目录范围中的最大end作为当前目录的end
            for next_chapter in self._page_chapters[chapter.page]:
                if not next_chapter.is_root or next_chapter.index in invalid_indices:
                    continue
                if next_chapter.index == chapter.index or next_chapter.title == chapter.title:
                    continue
                if next_chapter.end > chapter.end:
                    max_end = next_chapter.end
                child_indices.extend(next_chapter.children_indices)
                if next_chapter.is_same_style(chapter):
                    invalid_indices.add(next_chapter.index)
                else:
                    next_chapter.set_parent(None)
            chapter.add_children(child_indices)
            chapter.set_end(max_end)
        self._remove_invalid_indices(invalid_indices)

    def fix_titles(self):
        """
        标题被识别为多个: https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/3168#note_457200
        """
        self._fix_root_titles()
        invalid_indices = set()
        for chapter in self.chapter_map.values():
            if chapter.is_root or chapter.index in invalid_indices:
                continue
            same_style_elements = self._find_same_style_elements(chapter)
            if len(same_style_elements) < 2 and not chapter.is_valid(self.body_font_style):
                invalid_indices.add(chapter.index)
                continue
            if len(same_style_elements) < 2 or len(same_style_elements) > 3:
                if not chapter.is_valid(self.body_font_style):
                    invalid_indices.add(chapter.index)
                elif chapter.cleaned_title != chapter.title:
                    chapter.set_title(chapter.cleaned_title)
                continue
            max_end = chapter.end
            invalid_indices.update(self._merge_same_style_elements(chapter, same_style_elements, max_end))

        self._remove_invalid_indices(invalid_indices)

    def _get_same_title_range(
        self, title: str, start: int, end: int, right_chapters: dict[str, ChapterRange]
    ) -> tuple[str | None, ChapterRange | None]:
        if title in right_chapters:
            return title, right_chapters[title]
        for right_title, right_range in right_chapters.items():
            if (
                right_range.start == start
                and (right_range.end and abs(right_range.end - end) < 3 or self._is_similar(title, right_title))
            ) or is_same_title(right_title, title):
                return right_title, right_range
        return None, None

    @staticmethod
    def _is_similar(title1, title2):
        title1, title2 = P_NOT_WORD.sub("", title1), P_NOT_WORD.sub("", title2)
        if title1 in title2 or title2 in title1:
            return True
        return SequenceMatcher(None, title1, title2).ratio() > 0.8

    def _remove_duplicate_root_chapters(self):
        """
        合并一级目录中的重复章节
        http://100.64.0.105:55647/#/hkex/annual-report-checking/report-review/262267?fileId=69668&schemaId=5&rule=B1&delist=0
        # P136目录标题被识别为单个字符，例如： T S O F C H A N G E S I N E Q U I T Y
        http://100.64.0.105:55647/#/project/remark/257728?treeId=21876&fileId=68760&schemaId=15&projectId=17&schemaKey=B73
        """
        root_chapter_dict, duplicate_chapter_map = {}, {}
        valid_titles = {
            idx: P_WHITE.sub("", c.cleaned_title).upper()
            for idx, c in self.chapter_map.items()
            if c.is_root and not self.P_ONE_LETTER_TITLE.search(c.cleaned_title)
        }
        for chapter in self.root_chapters:
            title = P_WHITE.sub("", chapter.cleaned_title).upper()
            is_duplicate = False
            if chapter.index not in valid_titles:
                # 被识别成单个字符的目录，例如： T S O F C H A N G E S I N E Q U I T Y
                for valid_idx, valid_title in valid_titles.items():
                    if valid_title.endswith(title):
                        duplicate_chapter_map[chapter.index] = valid_idx
                        is_duplicate = True
                        break
            if is_duplicate:
                continue
            for root_title in root_chapter_dict:
                if is_same_title(root_title, title):
                    logger.warning(f"duplicate root chapter：{chapter}")
                    root_chapter_dict[root_title].set_end(chapter.end)
                    duplicate_chapter_map[chapter.index] = root_chapter_dict[root_title].index
                    break
            else:
                root_chapter_dict[title] = chapter
        # 校正重复目录的子目录
        for duplicate_idx in duplicate_chapter_map:
            self._reset_parent_of_children(self.chapter_map[duplicate_idx])
        self._remove_invalid_indices(duplicate_chapter_map)
        return {chapter.cleaned_title.upper(): chapter for chapter in root_chapter_dict.values()}

    def _generate_new_chapter_index(self, element_index: int):
        """
        找出指定元素块之后的第一个目录的index，作为新增章节的index，后面所有的章节index+1
        """
        from_chapter = None
        for elt_index, chapter in self.elt_chapter_map.items():
            if elt_index > element_index:
                from_chapter = chapter
                break
        if element_index > max(self.elt_chapter_map):
            return max(self.chapter_map) + 1
        if from_chapter.index > 1 and (from_chapter.index - 1) not in self.chapter_map:
            # index-1对应章节可能已经被删除，直接使用被删除的index
            return from_chapter.index - 1
        from_index = from_chapter.index
        self.added_indices.add(from_index)
        for chapter in self.chapter_map.values():
            if chapter.index >= from_index:
                chapter.set_index(chapter.index + 1)
            if chapter.has_parent and chapter.parent_index >= from_index:
                chapter.set_parent(chapter.parent_index + 1)
            chapter.set_children([idx + 1 if idx >= from_index else idx for idx in chapter.children_indices])
        return from_index

    def _add_new_chapter(
        self,
        start_element: UnifiedElement,
        *,
        page: int,
        start: int,
        end: int,
        level: int,
        parent_index: int,
        title: str = None,
    ) -> UnifiedChapter | None:
        if self.is_interdoc:
            # TODO
            return None
        else:
            syllabus = {
                "index": 0,
                "element": start_element.index,
                "level": level,
                "parent": parent_index,
                "children": [],
                "title": title or start_element.cleaned_title,
                "dest": {"box": start_element.box, "page_index": page},
                "range": [start, end],
                "class": "SYLLABUS",
            }
            new_chapter = UnifiedChapter(syllabus, self.reader)
            if not new_chapter.is_valid(self.body_font_style, strict=False):
                return None
        # 生成一个新的一级目录
        from_index = self._generate_new_chapter_index(start)
        new_chapter.set_index(from_index)
        self.chapters.insert(len([idx for idx in self.chapter_map if idx < from_index]), new_chapter.chapter)
        self._build_chapter_map()
        # 给范围在新增目录范围内的章节，设置parent为None，在fix_unknown_parent_chapters()中，会重新设置parent
        for chapter in self.chapter_map.values():
            if chapter.index <= from_index:
                #  新增章节之前的章节，若子章节中存在index>from_index的章节，则移除父子关系
                if wrong_child_indices := [
                    idx
                    for idx in chapter.children_indices
                    if idx > from_index and self.chapter_map[idx].level > new_chapter.level
                ]:
                    chapter.set_children([idx for idx in chapter.children_indices if idx not in wrong_child_indices])
                continue
            if start < chapter.start < end:
                chapter.set_parent(None)
                self._remove_from_parent(chapter)
        return new_chapter

    def _add_root_chapters(self, content_chapters: dict[str, ChapterRange], found_titles: set[str]):
        """
        找出存在于contents，但一级章节中不存在的章节
        http://100.64.0.105:55647/#/project/remark/235176?treeId=5614&fileId=66818&page=52 index=481+482
        """
        for title, chapter_rng in content_chapters.items():
            if title in found_titles or chapter_rng.start_page not in self._page_paragraphs:
                continue
            same_style_elements = []
            last_size = 0
            start_element = None
            # 在当前页面找字号一致的段落
            for element in self._page_paragraphs[chapter_rng.start_page]:
                if first_chapter := self.elt_chapter_map.get(element.index):
                    if not first_chapter.is_root and (
                        first_chapter.start == chapter_rng.start or is_same_title(first_chapter.title, title)
                    ):
                        start_element = element
                        break
                    continue
                if same_style_elements and not element.cleaned_title:
                    break
                element = UnifiedParaElement(element.element, self.reader)
                if last_size == 0 or last_size == element.font_style.fontsize:
                    same_style_elements.append(element)
                elif same_style_elements:
                    break
            if not start_element and same_style_elements:
                for i, element in enumerate(same_style_elements):
                    if start_element:
                        break
                    for j in range(i + 1, len(same_style_elements) + 1):
                        if is_same_title(title, " ".join(e.cleaned_title for e in same_style_elements[i:j])):
                            start_element = element
                            break
            if not start_element:
                # 可能财报，文档是横向，无法识别到标题
                # http://100.64.0.105:55647/#/project/remark/234282?treeId=13571&fileId=66669&page=118
                start_element = self._find_element(chapter_rng.start)
                if not start_element:
                    raise Exception(f"root chapter in contents not found: {title}")
            if (chapter := self.elt_chapter_map.get(start_element.index)) and chapter.has_parent:
                self._remove_from_parent(chapter)
                chapter.set_parent(-1)
                chapter.set_title(title)
                chapter.set_start(chapter_rng.start)
                chapter.set_element_index(chapter_rng.start)
                chapter.set_end(chapter_rng.end)
            else:
                # 增加一个新的一级章节
                self._add_new_chapter(
                    start_element,
                    page=chapter_rng.start_page,
                    start=chapter_rng.start,
                    end=chapter_rng.end,
                    level=1,
                    parent_index=-1,
                    title=title,
                )

    def fix_root_chapters(self):
        """
        一级章节目录修正
        TODO 校验一级目录之间是否连续
        """
        # 先合并名称相同的一级目录
        root_chapter_dict = self._remove_duplicate_root_chapters()

        content_chapters, page_chapters = self._chapter_range_from_contents, self._chapter_range_from_first_para
        if not content_chapters and not page_chapters:
            return
        not_found_chapters, found_titles = [], set()
        for title, chapter in root_chapter_dict.items():
            right_title, right_range = None, None
            if content_chapters:
                # 严格遵循目录提取到的章节
                right_title, right_range = self._get_same_title_range(
                    title, chapter.start, chapter.end, content_chapters
                )
                if not right_title:
                    if chapter.is_valid(self.body_font_style):
                        not_found_chapters.append(chapter)
                    continue
                found_titles.add(right_title)
                if right_title != title:
                    chapter.set_title(right_title)
            elif page_chapters:
                right_title, right_range = self._get_same_title_range(title, chapter.start, chapter.end, page_chapters)
            if not right_title:
                continue
            pages, start_page, start, end = (
                right_range.pages,
                right_range.start_page,
                right_range.start,
                right_range.end,
            )
            if not content_chapters and chapter.page not in pages:
                logger.warning(f"root chapter page not in range: {chapter} not in {pages}")
                continue
            if content_chapters:
                # 有目录，严格按照目录范围设置章节范围
                if chapter.page != start_page:
                    chapter.set_page(start_page)
                if start is not None and chapter.start != start:
                    chapter.set_start(start)
                    # 设置元素块index，可能为table
                    chapter.set_element_index(start)
                if end is not None and chapter.end != end:
                    chapter.set_end(end)
            else:
                if start is not None and chapter.page != start_page and chapter.start > start:
                    chapter.set_page(start_page)
                    chapter.set_start(start)
                    # 设置元素块index，可能为table
                    chapter.set_element_index(start)
                if end is not None and chapter.end != max(pages) + 1 and chapter.end < end:
                    chapter.set_end(end)
        if content_chapters:
            # 20%的root章节都没找到
            if (len(root_chapter_dict) - len(not_found_chapters)) / len(content_chapters) < 0.8:
                logger.warning(f"too many chapters not found: {not_found_chapters}")
                return
            # 找到不在目录中的章节，添加
            self._add_root_chapters(content_chapters, found_titles)

        invalid_roots = set()
        for chapter in not_found_chapters:
            if len(self._page_paragraphs[chapter.page]) == 1:
                # root章节不在目录中，且所在页面只有一个段落，则删除该目录
                invalid_roots.add(chapter.index)
                self._reset_parent_of_children(chapter)
                continue
            # root章节不在目录中，认为是非法目录，先给父级设置为None
            logger.warning(f"wrong root chapter: {chapter}")
            chapter.set_parent(None)
        if invalid_roots:
            self._remove_invalid_indices(invalid_roots)
        else:
            self._build_chapter_map()

    def _get_parent_chapters(self, chapter: UnifiedChapterBase) -> list[UnifiedChapterBase]:
        return sorted(
            [c for c in self.chapter_map.values() if c.start < chapter.start < c.end and chapter.end <= c.end],
            key=lambda x: x.index,
        )

    def _set_parent_of_chapter(self, chapter, parent_index: int):
        parent_chapter = self.chapter_map[parent_index]
        parent_chapter.add_children([chapter.index])
        logger.debug(f"---- set parent of chapter: {chapter} \nto {parent_chapter}")
        chapter.set_parent(parent_index)
        chapter.set_level(parent_chapter.level + 1)
        self._build_chapter_map()

    def fix_unknown_parent_chapters(self):
        invalid_chapter_indices = set()
        for chapter in self.chapter_map.values():
            if chapter.is_root or (chapter.parent_index is not None and chapter.parent_index in self.chapter_map):
                continue
            if not (root_chapter := [c for c in self.root_chapters if c.start < chapter.start < c.end]):
                if chapter.page < min(c.page for c in self.root_chapters):
                    # http://100.64.0.105:55647/#/project/remark/232965?projectId=17&treeId=4451&fileId=66449 第一章之前的页面被误识别为章节
                    invalid_chapter_indices.add(chapter.index)
                    continue
                else:
                    raise Exception(f"no root chapter: {chapter}")
            children_indices = root_chapter[0].children_indices
            if not children_indices:
                self._set_parent_of_chapter(chapter, root_chapter[0].index)
                continue
            last_parent_index = root_chapter[0].index
            found = False
            while children_indices:
                last_index, prefix_formats, next_children_indices = None, [], []
                # 遍历子章节，判断样式+前缀序号
                for child_idx in sorted(children_indices):
                    child_chapter = self.chapter_map[child_idx]
                    if chapter.start > child_chapter.start:
                        last_index, next_children_indices = child_idx, child_chapter.children_indices
                    # 如果当前章节index=子章节index+1，说明子章节是当前章节的父章节
                    if child_chapter.element_index + 1 == chapter.index:
                        self._set_parent_of_chapter(chapter, child_idx)
                        found = True
                        break
                    most_prefix_format = Counter(prefix_formats).most_common()[0][0] if prefix_formats else None
                    # 样式相同，则说明子章节是当前章节的兄弟章节，这里由于会遇到章节错误识别的情况，所以校验下前缀
                    if (
                        child_chapter.has_parent
                        and chapter.is_same_style(child_chapter)
                        and chapter.prefix_format == child_chapter.prefix_format
                        and (not most_prefix_format or most_prefix_format == child_chapter.prefix_format)
                    ):
                        self._set_parent_of_chapter(chapter, child_chapter.parent_index)
                        found = True
                        break
                    prefix_formats.append(child_chapter.prefix_format)
                if found:
                    break
                if last_index is None:
                    self._set_parent_of_chapter(chapter, last_parent_index)
                    break
                if not next_children_indices:
                    self._set_parent_of_chapter(chapter, last_index)
                    break
                last_parent_index = last_index
                children_indices = next_children_indices
        self._remove_invalid_indices(invalid_chapter_indices)

    def remove_invalid_chapters(self):
        """
        句子、表头、日期被识别为目录，删除这类目录
        """
        self._remove_invalid_root_chapters()
        for chapter in self.chapter_map.values():
            if chapter.is_valid(self.body_font_style):
                continue
            logger.debug(f"invalid chapter: {chapter}")
            self._remove_invalid_chapter(chapter)

    def _remove_invalid_root_chapters(self):
        """
        页面的第一个章节标记被识别为目录
        https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/3168#note_571108
        """
        # 先按照title去重统计章节个数
        page_first_chapters = defaultdict(list)
        content_chapters = self._chapter_range_from_contents or self._chapter_range_from_first_para
        for paras in self._page_paragraphs.values():
            index = paras[0].index
            if index in self.elt_chapter_map:
                title = clean_title(self.elt_chapter_map[index].title, remove_bound_num=True)
                page_first_chapters[title].append(self.elt_chapter_map[index])
        # 遍历目录，找出错误章节
        root_elements = {root.element_index for root in self.root_chapters}
        invalid_chapters = []
        for title, chapters in page_first_chapters.items():
            if len(chapters) < 2:
                continue
            # 名称在一级目录名称中，则是识别错误的章节
            if title in content_chapters:
                invalid_chapters.extend(c for c in chapters if c.element_index not in root_elements)
                continue
            # 名称不在，但超过一半的页码上都有该章节，也是识别错误的章节
            for rng in content_chapters.values():
                if (
                    len(chapters) > 3
                    and {c.page for c in chapters}.issubset(set(rng.pages))
                    and len(chapters) - 1 > len(rng.pages) / 2 - 1
                ):
                    invalid_chapters.extend(c for c in chapters if c.element_index not in root_elements)
        # 删除误识别的章节
        for chapter in invalid_chapters:
            self._remove_invalid_chapter(chapter)

    def _remove_invalid_chapter(self, chapter):
        self._remove_from_parent(chapter)
        # 将子章节的parent设置为None
        self._reset_parent_of_children(chapter)
        self.chapters.remove(chapter.chapter)
        # 每删除一个记录，需要重新构建目录树
        self._build_chapter_map()

    @cached_property
    def _chapter_range_from_first_para(self) -> dict[str, ChapterRange]:
        """
        判断文档每页的第一个元素块，是不是一级章节标题
        """
        page_first, page_ranges = {}, {}
        for page, elements in self._page_elements_iter(
            {PDFInsightClassEnum.PAGE_HEADER.value, PDFInsightClassEnum.PARAGRAPH.value}
        ):
            indices = [get_index(elt) for elt in elements]
            max_index = max(indices) + 1
            if page not in page_ranges:
                page_ranges[page] = [min(indices), max_index]
            elif max_index > page_ranges[page][1]:
                page_ranges[page][1] = max_index
            if fake_title := page_first_title(elements):
                page_first[page] = fake_title
        fill_middle_page_title(page_ranges, page_first)
        chapter_pages = defaultdict(list)
        for page, title in page_first.items():
            chapter_pages[title].append(page)
        chapter_ranges = {}
        for title, pages in chapter_pages.items():
            start_page = min(pages)
            chapter_ranges[title] = ChapterRange(
                pages, start_page, page_ranges[start_page][0], page_ranges[max(pages)][1]
            )
        return chapter_ranges

    @cached_property
    def _chapter_range_from_contents(self) -> dict[str, ChapterRange]:
        """
        step1: 先尝试取pdfparser解析到的目录
        step2: 若取不到，再尝试根据目录提取正确的一级目录页码及index范围
        """
        contents = {}
        # 1. 读取pdfparser解析到的目录
        for catalog in self.reader.pdf_contents:
            page_index, parent, title = catalog["dest"]["page_index"], catalog["parent"], catalog["title"].upper()
            if (
                self.P_COVER.search(title)
                or P_CONTENTS.nexts(title)
                or self.reader.is_catalog_page(page_index)
                or parent != -1
                or page_index < 0
            ):
                continue
            contents[page_index] = title
        if len(contents) < 10:
            # 2. 读取页面目录中的内容
            contents = self.chapter_from_contents
        if len(contents) < 10:
            return {}
        pages = sorted(contents)
        chapter_ranges = {}
        max_page = max(p.page for p in self.reader.pages) if self.is_interdoc else max(self.reader.page_dict)
        for i, page in enumerate(pages):
            first_index = self._page_first_index(page)
            if i < len(pages) - 1:
                next_page = pages[i + 1]
                next_index = self._page_first_index(next_page)
            else:
                next_index = self.reader.max_index + 1
                next_page = max_page + 1
            chapter_ranges[contents[page]] = ChapterRange(list(range(page, next_page)), page, first_index, next_index)
        return chapter_ranges

    @cached_property
    def chapter_from_contents(self) -> dict[int, str]:
        """
        提取目录中的标题作为一级目录标题
        """
        if self.is_interdoc:
            return {}
        return self.reader.find_chapter_from_contents()

    @cached_property
    def _page_paragraphs(self) -> dict[int, list[UnifiedElement]]:
        page_paras = {}
        for page, paras in self._page_elements_iter(PDFInsightClassEnum.PARAGRAPH.value):
            if paras:
                page_paras[page] = [UnifiedElement(para) for para in paras]
        return page_paras

    @cached_property
    def _page_headers(self) -> dict[int, list[UnifiedElement]]:
        page_headers = {}
        for page, headers in self._page_elements_iter(PDFInsightClassEnum.PAGE_HEADER.value):
            if headers:
                page_headers[page] = [UnifiedElement(header) for header in headers]
        return page_headers

    @cached_property
    def _page_tables(self) -> dict[int, list[UnifiedElement]]:
        page_tables = {}
        for page, tables in self._page_elements_iter(PDFInsightClassEnum.TABLE.value):
            if tables:
                page_tables[page] = [UnifiedElement(table) for table in tables]
        return page_tables

    @cached_property
    def _page_left_right(self) -> dict[int, dict[(int, int), list[float]]]:
        """
        获取每个页面的分栏列宽
        """
        if self.is_interdoc:
            # TODO
            return {}
        return self.reader.page_left_right

    def _page_elements_iter(self, element_types: set[str] = None):
        pages = self.reader.pages if self.is_interdoc else self.reader.page_dict.values()
        for page in pages:
            if self.is_interdoc:
                # if page.paragraphs and len(page.paragraphs[0].page_info) > 1:
                #     continue
                elements = []
                if not element_types:
                    elements = page.page_headers + page.paragraphs + page.tables + page.images + page.page_footers
                for type_ in element_types:
                    if type_ == PDFInsightClassEnum.PAGE_HEADER.value:
                        elements.extend(page.page_headers)
                    if type_ == PDFInsightClassEnum.PARAGRAPH.value:
                        elements.extend(page.paragraphs)
                    if type_ == PDFInsightClassEnum.TABLE.value:
                        elements.extend(page.tables)
                    if type_ == PDFInsightClassEnum.PAGE_FOOTER.value:
                        elements.extend(page.page_footers)
            else:
                elements = [e for e in page.origin_elements if not element_types or e["class"] in element_types]
            if not elements:
                continue
            elements = sorted(elements, key=lambda x: x.index if self.is_interdoc else x["index"])
            yield page.page, elements

    def _page_first_index(self, page):
        """
        取页面的第一个元素块，优先找段落
        """
        if page in self._page_paragraphs:
            idx = 0
            if len(self._page_paragraphs[page]) > 1:
                # 页眉被识别为段落
                # http://100.64.0.105:55647/#/project/remark/248744?treeId=1080&fileId=83431
                if self._page_paragraphs[page][0].cleaned_title == self._page_paragraphs[page][1].cleaned_title:
                    idx = 1
            return self._page_paragraphs[page][idx].index
        if page in self._page_headers:
            return self._page_headers[page][0].index
        if page in self._page_tables:
            return self._page_tables[page][0].index
        return None

    @cached_property
    def _page_chapters(self) -> dict[int, list[UnifiedChapter]]:
        """
        取每个页面的所有章节
        """
        page_chapter_dict = defaultdict(list)
        for chapter in self.chapter_map.values():
            page_chapter_dict[chapter.page].append(chapter)
        return page_chapter_dict

    def _prev_element_chapter(self, chapter: UnifiedChapter):
        """
        找章节前面元素块所属的章节
        # TODO 考虑元素块的缩进
        """
        index = chapter.element_index
        for i in range(1, 11):
            index -= i
            if not (element := self._find_element(index)):
                continue
            if not element.is_paragraph:
                continue
            if (prev_chapter := self._find_element_chapter_by_index(index)) and prev_chapter.is_valid(
                self.body_font_style
            ):
                if chapter.is_same_style(prev_chapter):
                    # 兄弟章节
                    if prev_chapter.has_parent:
                        return self._get_chapter(prev_chapter.parent_index)
                    return None
                while prev_chapter.has_parent:
                    prev_chapter = self._get_chapter(prev_chapter.parent_index)
                    if chapter.is_same_style(prev_chapter):
                        return self._get_chapter(prev_chapter.parent_index)
        return None

    def _find_element_chapter_by_index(self, index):
        if chapters := [s for s in self.chapter_map.values() if s.start <= index < s.end]:
            return sorted(chapters, key=lambda s: s.index)[-1]
        return None

    def _remove_from_parent(self, chapter: UnifiedChapter):
        if not chapter.has_parent:
            return
        if parent := self._get_chapter(chapter.parent_index):
            parent.set_children([i for i in parent.children_indices if i != chapter.index])

    def _reset_parent_of_children(self, chapter):
        for idx in chapter.children_indices:
            self.chapter_map[idx].set_parent(None)

    def _add_children_to_parent(self, parent_index, indices: list[int]):
        if parent := self._get_chapter(parent_index):
            parent.set_children(sorted(set(parent.children_indices + indices)))

    def _remove_invalid_indices(self, invalid_indices: set[int] | list[int] | dict[int, Any]):
        if not invalid_indices:
            return
        new_chapters = []
        for idx, chapter in self.chapter_map.items():
            if idx in invalid_indices:
                continue
            if chapter.parent_index in invalid_indices:
                chapter.set_parent(None)
            if any(idx in invalid_indices for idx in chapter.children_indices):
                chapter.set_children([i for i in chapter.children_indices if i not in invalid_indices])
            new_chapters.append(chapter.chapter)
        self.chapters = new_chapters
        self._build_chapter_map()


def get_index(chapter: dict | ChapterNode) -> int:
    return chapter.index if isinstance(chapter, ChapterNode) else chapter["index"]


def calc_fixed_index(orig_index: int, added_indices: set[int] = None):
    """
    因新增章节，索引会变化，计算变化后的章节index
    """
    if added_indices:
        fixed_index = orig_index + len({i for i in added_indices if i <= orig_index}) if added_indices else orig_index
        return fixed_index + len({i for i in added_indices if orig_index < i <= fixed_index})
    return orig_index


def find_element(
    reader: Union["PdfinsightReader", "InterdocReader"],  # noqa
    index: int,
    is_interdoc: bool,  # noqa
) -> UnifiedElement | None:
    try:
        if is_interdoc:
            element = reader.find_by_index(index)
        else:
            element = reader.element_dict[index]
        return UnifiedElement(element)
    except (IndexError, KeyError):
        return None


def debug_chapters_diff(
    orig_chapters: Iterable[dict | ChapterNode],
    fixed_chapters: Iterable[dict | ChapterNode],
    added_indices: set[int] = None,
):
    """
    打印修正前后差异
    """
    mapping = {get_index(c): UnifiedChapterBase(c) for c in orig_chapters}
    fixed_mapping = {get_index(c): UnifiedChapterBase(c) for c in fixed_chapters}
    for idx in added_indices:
        logger.debug(f"added chapter: {fixed_mapping[idx]}")
    removed_chapters = []
    for idx, orig_chapter in mapping.items():
        fixed_index = calc_fixed_index(idx, added_indices)
        if fixed_index in added_indices:
            continue
        if fixed_index not in fixed_mapping:
            removed_chapters.append(orig_chapter)
            continue
        fixed_chapter = fixed_mapping[fixed_index]
        if orig_chapter == fixed_chapter:
            continue
        changed = []
        for attr in ["start", "end", "page", "element_index", "level", "title"]:
            orig_value, new_value = getattr(orig_chapter, attr), getattr(fixed_chapter, attr)
            if attr == "title":
                orig_value = P_BLANK.sub(" ", orig_value).strip()
            if orig_value != new_value:
                changed.append(f"`{attr}`: {orig_value} -> {new_value}")
        # 对比父章节元素块
        orig_parent_elt = (
            mapping[orig_chapter.parent_index].element_index
            if orig_chapter.parent_index in mapping
            else orig_chapter.parent_index
        )
        new_parent_elt = (
            fixed_mapping[fixed_chapter.parent_index].element_index
            if fixed_chapter.parent_index in fixed_mapping
            else fixed_chapter.parent_index
        )
        if orig_parent_elt != new_parent_elt:
            changed.append(f"`parent_element`: {orig_parent_elt} -> {new_parent_elt}")
        # 对比子章节元素块
        orig_children_elt = [mapping[idx].element_index for idx in orig_chapter.children_indices]
        new_children_elt = [fixed_mapping[idx].element_index for idx in fixed_chapter.children_indices]
        if orig_children_elt != new_children_elt:
            changed.append(f"`children_elements`: {orig_children_elt} -> {new_children_elt}")
        if not changed:
            continue
        logger.debug(
            f"index={orig_chapter.index}[{orig_chapter.element_index}],is_root={orig_chapter.is_root},"
            + f"page={orig_chapter.page} ===> {'; '.join(changed)}"
        )
    for chapter in removed_chapters:
        logger.debug(f"removed chapter: {chapter}")


def fix_chapters(reader: Union["PdfinsightReader", "InterdocReader"], orig_chapters: Iterable[dict | ChapterNode]):  # noqa
    fixer = ChapterFixer(reader, orig_chapters)
    # 修正章节标题
    fixer.fix_titles()
    # 删除无效章节
    fixer.remove_invalid_chapters()
    # 修正一级章节
    # TODO 这个放在remove_invalid_chapters()之前
    fixer.fix_root_chapters()
    # 修正子章节 TODO 未经验证
    # fixer.fix_child_chapters()
    # 根据范围重新设置parent=None的目录的父章节和level
    fixer.fix_unknown_parent_chapters()
    # 修正level
    fixer.fix_levels()
    fixed_chapters = fixer.get_chapters()
    fixed_mapping = {get_index(c): UnifiedChapterBase(c) for c in fixed_chapters}
    # 更新元素块的目录，考虑到一些element的目录不正确，也一并纠正
    for element in reader.elements_iter():
        syllabus = element.get("syllabus")
        syllabus_index = None
        if chapters := sorted(
            [s for s in fixed_mapping.values() if s.start <= element["index"] < s.end], key=lambda s: s.start
        ):
            syllabus_index = chapters[-1].index
        if syllabus_index != syllabus:
            element["syllabus"] = syllabus_index
    debug_chapters_diff(orig_chapters, fixed_chapters, fixer.added_indices)
    return fixed_chapters
