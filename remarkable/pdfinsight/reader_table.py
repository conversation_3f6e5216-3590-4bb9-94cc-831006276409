import re
from copy import deepcopy
from functools import cached_property

from remarkable.common.util import clean_txt
from remarkable.pdfinsight.reader import PdfinsightReader


class PdfinsightTableCell(dict):
    pass


class PdfinsightTable:
    def __init__(self, data):
        self.data = data
        self.element = data
        self.merged = {}
        self._merged_mapping = None
        self.rowcount, self.colcount = 0, 0
        for key, cell in self.data["cells"].items():
            rowidx, colidx = [int(k) for k in key.split("_")]
            self.rowcount = max(rowidx + 1, self.rowcount)
            self.colcount = max(colidx + 1, self.colcount)
            for _colidx in range(cell.get("left"), cell.get("right")):
                for _rowidx in range(cell.get("top"), cell.get("bottom")):
                    if not (_rowidx == rowidx and _colidx == colidx):
                        self.merged["%s_%s" % (_rowidx, _colidx)] = key
        self._cells = None

    def sorted_rows(self, cells=None):
        return [
            [cell for cidx, cell in sorted(cell_dict.items(), key=lambda c: c[0])]
            for ridx, cell_dict in sorted(self.cells.items() if cells is None else cells.items(), key=lambda r: r[0])
        ]

    @cached_property
    def cells(self):
        if self._cells is None:
            self._cells = {}
            for idx, cell in self.data["cells"].items():
                cell["index"] = idx
                ridx, cidx = idx.split("_")
                _row = self._cells.setdefault(int(ridx), {})
                _row[int(cidx)] = cell
        return self._cells

    @property
    def index(self):
        return self.data["index"]

    @property
    def size(self):
        return self.rowcount, self.colcount

    def cell(self, row, col):
        key = "%s_%s" % (row, col)
        if key in self.merged:
            return self.data["cells"][self.merged[key]], True
        if key in self.data["cells"]:
            return self.data["cells"][key], False
        return None, None

    def row(self, idx):
        for i in range(0, self.colcount):
            yield self.cell(idx, i)

    def row_text(self, idx):
        cells = []
        for i in range(0, self.colcount):
            cell, virtual = self.cell(idx, i)
            if virtual:
                continue
            cells.append(cell)
        return re.sub(r"[\r\t\n]+", " ", " | ".join([c["text"] for c in cells]))

    @cached_property
    def rows(self):
        for i in range(0, self.rowcount):
            yield i, self.row(i)

    @classmethod
    def group_cells(cls, cells):
        cells = deepcopy(cells)
        cells_by_row = {}
        cells_by_col = {}
        for idx, cell in cells.items():
            row, col = idx.split("_")
            cells_by_col.setdefault(col, {})[row] = cell
            cells_by_row.setdefault(row, {})[col] = cell
        return cells_by_row, cells_by_col

    @classmethod
    def locate_cell_from_header(cls, tbl, pattern, from_row=False, from_col=False):
        """
        从表头中查找关键字，作为锚点
        """
        res = None
        cells_by_row, cells_by_col = cls.group_cells(tbl["cells"])
        if from_row:
            for col, cell in cells_by_row.get("0", {}).items():  # 第一行
                if pattern.search(clean_txt(cell["text"])):
                    res = "_".join(map(str, [0, col]))
        if from_col:
            for row, cell in cells_by_col.get("0", {}).items():  # 第一列
                if pattern.search(clean_txt(cell["text"])):
                    res = "_".join(map(str, [row, 0]))
        return res

    def find_cellidx_list_by_outline(self, page, outline):
        cells = []
        for ridx, row in self.cells.items():
            for cidx, cell in row.items():
                if cell["page"] == page and PdfinsightReader.overlap_percent(cell["box"], outline) > 0.618:
                    cells.append((ridx, cidx))
        return cells

    def find_first_cellidx_list_by_outline(self, page, outline):
        cells = self.find_cellidx_list_by_outline(page, outline)
        return cells[0] if cells else None

    def cell_merged_to(self, cidx, ridx) -> tuple[int, int]:
        if self._merged_mapping is None:
            self._merged_mapping = {}
            for item in self.merged:
                for cell in item[1:]:
                    self._merged_mapping[tuple(cell)] = tuple(item[0])
        return self._merged_mapping.get((cidx, ridx))

    def is_merged_cell(self, cell):
        for merge_group in self.element["merged"]:
            merge_group = ["_".join(map(str, x)) for x in merge_group]
            if cell["index"] in merge_group:
                return True
        return False

    def is_combo_table(self):
        return self.data.get("combo_table_idx") is not None

    def is_combo_sub_table(self):
        return len((self.element.get("page_merged_table") or {}).get("cells_idx", {})) > 1 and not self.is_combo_table()

    @cached_property
    def markdown(self):
        return self.to_markdown(self.element)

    @staticmethod
    def cells_rowcount(cells):
        return max((int(ck.split("_")[0]) for ck in cells), default=0) + 1

    @classmethod
    def to_markdown(cls, element, need_merged_table=True):
        """Convert table to markdown format"""
        is_combo = element.get("combo_table_idx") is not None
        cells = element["cells"] if is_combo else element.get("origin_cells", element["cells"])

        def _get_merged_cell_dict(table):
            merged_cell_dict = {}
            for merged in table["merged"]:
                for cell_id in merged[1:]:
                    merged_cell_dict.setdefault(tuple(merged[0]), []).append(tuple(cell_id))
            return merged_cell_dict

        def _get_cell_text_dict(table):
            merged_cell_dict = _get_merged_cell_dict(table)
            cell_dict = {}
            for cell_id, cell in list(cells.items()):
                x, y = [int(_x) for _x in cell_id.split("_")]
                cell_text = cell["text"]
                cell_dict.setdefault((x, y), cell_text)
                # copy text info to merged cell
                if not need_merged_table:
                    cell_text = ""
                for sub_cell_id in merged_cell_dict.get((x, y), []):
                    cell_dict.setdefault(sub_cell_id, cell_text)
            return cell_dict

        first_row_idx = min(int(i.split("_")[0]) for i in cells)
        last_row_idx = max(int(i.split("_")[0]) for i in cells)
        cell_texts = _get_cell_text_dict(element)
        n_col = len(element["grid"]["columns"]) + 1
        md_table = ""
        header_line = "|" + "-|" * n_col + "\n"
        for i in range(first_row_idx, last_row_idx + 1):
            md_table += "|"
            for j in range(n_col):
                md_table += cell_texts.get((i, j), "").replace("\n", "") + "|"
            md_table += "\n"
            if i == 0:
                md_table += header_line
        return md_table
