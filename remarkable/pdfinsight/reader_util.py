import logging
import re
from collections import defaultdict
from dataclasses import dataclass
from functools import cached_property
from itertools import chain
from typing import Iterable, Union

import numpy as np
from interdoc.base import Box
from interdoc.element import Child<PERSON>ell, Element, Para
from sklearn.cluster import KMeans

from remarkable.common.common import get_keys, is_para_elt, is_table_elt, roman_to_int
from remarkable.common.common_pattern import (
    ALL_CHAPTER_TITLES,
    P_ALL_CHAPTER_TITLES,
    P_CHAPTER_PREFIX,
    P_CONSOLIDATED_STATEMENT,
    P_CONTENTS,
    P_CONTINUED,
    R_CN_SPACE,
    R_MIDDLE_DASH,
    R_MIDDLE_DASHES,
    R_SELECTED,
)
from remarkable.common.constants import PDFInsightClassEnum
from remarkable.common.element_util import (
    FontStyle,
    first_space_index,
    max_col_of_tbl_element,
    outline_right_of_last_line,
)
from remarkable.common.pattern import PatternCollection
from remarkable.common.util import P_CHNS, clean_txt, is_in_range
from remarkable.pdfinsight import P_CHAPTER_BORDER_NUM, clear_syl_title
from remarkable.predictor.common_pattern import R_ANY_EN_DATE, R_DATES, R_EN_MONTH

logger = logging.getLogger(__name__)

P_BLANK = re.compile(r"\s+")
P_NOT_WORD = re.compile(r"\W+")
P_REPEAT_WORDS = re.compile(r"(.)\1+")
# 处理这种问题： 中英文识别到了一起，删除中文后，标题前缀后缀相同，删除后缀
# http://************:55647/#/project/remark/257728?treeId=21876&fileId=68760&page=162 元素块2605
P_INVALID_END = re.compile(
    r"^\s*(?P<prefix>[\[(（]?(\d{1,3}\b|[a-zA-Z]|[IVXivx]{1,4})[-—－–.）)\s\]]|(?<!\d)\d{1,2}(\.\d{1,2})+)\s*(?P<title>.+?)(?P=prefix)\s*$"
)
# 一级章节标题一般以字母数字结尾（这里不考虑括号）
P_TITLE_END = re.compile(r"[a-z0-9]\s*$", re.I)
P_STOP_TITLE = PatternCollection([r"[^\w]\s*$", P_CONTINUED.pattern], flags=re.I)
P_INVALID_ROOT_TITLE = PatternCollection(
    [
        r"\b(Co|ltd|etc|inc|corp)\.|\sl\s*i\s*m\s*i\s*t\s*e\s*d(\s+\d+)?$|a\s*n\s*n\s*u\s*a\s*l\s*r\s*e\s*p\s*o\s*r\s*t",
        r"([^,，]+[,，]){3,}",
        r"^\d+$",
        r"/$",
        r"[:：；;]",
    ],
    flags=re.I,
)
R_SPECIAL_PREFIX = r"[•▶◆■★→]"
P_ROOT_CHAPTER_PREFX = re.compile(rf"^{R_SPECIAL_PREFIX}?(SECTION|ITEM|CHAPTER|PART)?", re.I)
# P_CONTENTS = re.compile(r"^(table\s*of\s*)?contents$", re.I)
P_PAGE_NUM = re.compile(
    rf"^\s*\d{{1,3}}(?!{R_MIDDLE_DASH}[^\d])({R_MIDDLE_DASH}\d{{1,3}})?(?!\d)\s*|\s*(?<!\d)\d{{1,3}}({R_MIDDLE_DASH}\d{{1,3}})?\s*$",
    re.MULTILINE,
)
P_PAGE_NUM_END = re.compile(rf"\s*(?<!\d)\d{{1,3}}({R_MIDDLE_DASH}\d{{1,3}})?\s*$", re.MULTILINE)
# 识别的页码之间有空格，需要替换
# http://************:55647/#/hkex/annual-report-checking/report-review/248483?fileId=68203&schemaId=15&rule=B1
P_SPACE_NUM = re.compile(r"(?P<num1>\d{1,3})[ \t]+(?P<num2>\d{1,3})([ \t]+(?P<num3>\d{1,3}))?")
P_WORDS = re.compile(r"[a-z]+", re.I)
P_INVALID_CONTENT = re.compile(r"^(page[(（]?s?[）)]?|contents)$|annual\s*report|about\s*us\b", re.I)
P_CHINESE = re.compile(r"[^\s0-9a-z）)”.]?[\u4e00-\u9fa5�流][^a-z]*[\u4e00-\u9fa5�流][^\s0-9a-z(（“]?", re.I)

P_LOWER_CASE = re.compile(r"[a-z]")
P_ENSURE_ROMAN = re.compile(r"[IVX]{2,4}", re.I)
R_PREFIX = rf"^(\s*(?P<start>{R_SPECIAL_PREFIX}|[(（]|SECTION|ITEM|CHAPTER|PART)\s*)?"
R_SUFFIX = r"(?P<end>[）).\s]).+$"
P_CHAPTER_PREFIX_EXTRACTOR = PatternCollection(
    [
        r"^[(（]?(?P<num1>[1-9]\d?)\.(?P<num2>[1-9]\d?)\.(?P<num>[1-9]\d?)(?!\d)[\s.)）]",
        r"^[(（]?(?P<num1>[1-9]\d?)\.(?P<num>[1-9]\d?)(?!\d)[\s.)）]",
        r"^[(（]?(?P<num>[1-9]\d?)(?P<word1>\.?[A-Z])[\s.)）]",
        r"^[(（]?(?P<num>[1-9]\d?)(?!\d)[\s.)）]",
        r"^[(（]?(?P<word>[a-z])[\s.)）]",
        r"^[(（]?(?P<roman>[ivx]{1,4})[\s.)）]",
    ],
    flags=re.I,
)
P_CAPITAL_WORD = re.compile(r"^(\W|[0-9A-Z])")
# 满足该正则，不是目录标题
P_INVALID_TITLE = PatternCollection(
    [
        # 纯数字
        rf"^[{R_MIDDLE_DASHES},，、./\d\s%％]+$",
        # 纯日期
        *[rf"^(as\s*(at|of))?\s*{dt}\s*$" for dt in R_DATES],
        # continued
        r"[(（]\s*(continued?|cont['‘’]d)\s*[）)]",
        rf"{R_MIDDLE_DASH}\s*(continued?|cont['‘’]d)",
        # 脚注
        r"^Notes?\s*([1-9]\d?|[a-z])?\s*[.:：]",
    ],
    flags=re.I,
)
# 满足该正则，可能不是目录标题，需要结合目录样式判断
P_SEN_TITLE = PatternCollection(
    [
        # 包含分号
        r"[;；]",
        # -或：结尾
        rf"[{R_MIDDLE_DASHES}:：]$",
        # 句号结尾
        r"(?<!\bLTD)(?<!\bCO)(?<!\binc)\.$",
        # 日期
        *R_DATES,
        # 人名开始
        r"\b(Mr|Ms|Mdm|Dr|Prof|Madam)(\.|\s)",
        # 括号中包含了chairman等
        rf"([(（]|(?<!non){R_MIDDLE_DASH})\s*[-a-z\s]*(chair((wo)?man|lady)|chief|executive\s*director|ceo|president)\b",
    ],
    flags=re.I,
)
TITLE_STYLE_MAP = {
    # 数字.数字序号
    re.compile(rf"{R_PREFIX}(?P<num>([1-9]\d?\.){{1,3}})[1-9]\d?(?!\d){R_SUFFIX}"): r"\g<start>\g<num>1\g<end>",
    # 数字序号
    re.compile(rf"{R_PREFIX}\d{{1,2}}(\.[A-Z])?{R_SUFFIX}"): r"\g<start>1\g<end>",
    # 大写罗马序号
    re.compile(rf"{R_PREFIX}[IVX]{{1,4}}{R_SUFFIX}"): r"\g<start>I\g<end>",
    # 小写罗马序号
    re.compile(rf"{R_PREFIX}[ivx]{{1,4}}{R_SUFFIX}"): r"\g<start>i\g<end>",
    # 大写英文序号
    re.compile(rf"{R_PREFIX}[A-Z]{R_SUFFIX}"): r"\g<start>A\g<end>",
    # 小写英文序号
    re.compile(rf"{R_PREFIX}[a-z]{R_SUFFIX}"): r"\g<start>a\g<end>",
    # 其它序号
    re.compile(rf"^\s*(?P<start>{R_SPECIAL_PREFIX}|{R_MIDDLE_DASH}).+$", re.I): r"\g<start>",
}
R_TABLE_UNITS = [
    r"^([(（]?[單单]位[:：]\s*)?(units?|currenc(y|ies))[:：]",
    r"^([(（]?[編编]制[單单]位[:：]\s*)?[(（]?prepared\s*by[:：]",
    # http://************:55647/#/project/remark/258933?projectId=17&treeId=8881&fileId=69001&schemaId=18 index=2726
    # http://************:55647/#/project/remark/405869?projectId=17&treeId=6392&fileId=113179&schemaId=29 index=1699
    # http://************:55647/#/project/remark/418044?treeId=37993&fileId=114544&schemaId=29&projectId=17&page=117 index=1832
    rf"^(as\s*(at|of)|for\s*the\s*year\s*ended)?\s*({R_ANY_EN_DATE}\s*)?[(（]?{R_CN_SPACE}[(（]?(Expressed|(all\s*)?amounts)\s*in\s",
]
R_INVALID_TBL_TITLES = [
    r"^notes?\s?[:：]",
    # 匹配三组以内的长度不超过3的识别异常单词，例如： aa bbb c
    # 表格所在页面侧边栏被错误识别为Paragraph导致获取的表格名称只有几个字母
    # 见第168页：http://************:55647/#/project/remark/?treeId=8837&fileId=67293&schemaId=28&projectId=43974&schemaKey=E(d)(i)-Review%20of%20financial%20reports
    r"^\s*\S{1,3}(\s+\S{1,3}){0,2}\s*$",
    P_CONTINUED,
    *ALL_CHAPTER_TITLES,
    rf"^({R_EN_MONTH}\s+)?20\d{{2}}$",
    rf"^{R_ANY_EN_DATE}$",
    rf"^(as\s*(at|of)|for\s*the\s*year\s*ended)\s*{R_ANY_EN_DATE}(\s*and\s*({R_EN_MONTH}\s*)?\d{{4}})?$",
    r"(continued?|cont['‘’]d|續|续).{,3}\s*[)）]?\s*\d*$",
    # 适配https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/3168#note_433123
    r"^(No\.|number)\s*of\s*meetings\s*(h[eo]ld/)?attend",
]
P_INVALID_TBL_TITLES = PatternCollection(
    [
        *R_INVALID_TBL_TITLES,
        *R_TABLE_UNITS,
        # http://************:55647/#/project/remark/405869?projectId=17&treeId=6392&fileId=113179&schemaId=29 index=1699
        r"^[(（][^）)]+[)）]$",
        # http://************:55647/#/project/remark/416995?treeId=28543&fileId=114427&schemaId=18&projectId=17&schemaKey=C2.1.1&page=68 index=681
        rf"^{R_SELECTED}?\s*Applicable\s*{R_SELECTED}?(Not\s*Applicable|N/A)",
    ],
    flags=re.I,
)


def is_merged_elt(element):
    """
    是否为合并元素块的非第一个元素块
    """
    if page_merged := get_keys(element, ["page_merged_paragraph", "paragraph_indices"]):
        return element["index"] != page_merged[0]
    return False


def get_real_parent_title(element_index: int, root_chapters: list[dict]):
    for chapter in root_chapters:
        if (fake_title := chapter["fake_title"]) and is_in_range(element_index, chapter["range"]):
            return fake_title
    return None


def fill_middle_page_title(pages: Iterable[int], page_first: dict[int, str]):
    """
    如果前一页和后一页都有标题，则取前一页的标题

    http://************:55647/#/project/remark/245358?treeId=38119&fileId=66258&schemaId=28&projectId=17&schemaKey=E(d)(iv)-1&page=30
    http://************:55647/#/project/remark/244825?treeId=2700&fileId=66791&schemaId=28&projectId=17&schemaKey=B(a)&page=92
    """
    for page in pages:
        if page in page_first:
            continue
        prev_page_title = page_first.get(page - 1)
        if prev_page_title and page_first.get(page + 1):
            page_first[page] = prev_page_title


def page_first_title(elements: list[dict | Para], pdfinsight=None) -> str:
    """
    遍历页面前5个元素块，如果元素块可能是一级目录标题，且不在识别到的目录范围内，则认为这个页面所有元素块，都属于这个目录
    同时考虑目录换行的场景：
    https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/3168#note_410269
    https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/3168#note_457200
    TODO 应用P_ROOT_CHAPTER_PREFX 判断标题格式
    """
    if not elements:
        return ""
    final_title = ""
    paras = []
    is_interdoc = isinstance(elements[0], Para)
    style, last_style, last_index = None, None, None
    for i, element in enumerate(elements[:4]):
        index = element.index if is_interdoc else element["index"]
        if pdfinsight:
            # 若能找到follow lines，不需要判断root正则
            style = pdfinsight.get_font_style(element["chars"])
            if next_elements := pdfinsight.find_follow_lines(element):
                final_title = " ".join(e["text"] for e in [element, *next_elements])
                break
        elem_text = clean_txt(element.text if is_interdoc else element["text"], remove_cn_text=True)
        if not elem_text:
            last_style = style
            continue
        if P_CONTINUED.search(elem_text):
            # 去掉continue的标题能匹配上root正则
            if (title := P_CONTINUED.sub("", elem_text)) and P_ALL_CHAPTER_TITLES.nexts(title.strip()):
                final_title = title
            # or elem_type == PDFInsightClassEnum.PARAGRAPH.value and not P_TITLE_END.search(elem_text)
            break
        # http://************:55647/#/project/remark/355489?fileid=112806&projectId=17&treeId=8011&fileId=112806&schemaId=29&page=202 index=2494, 目录下方是table
        # 目录下不提取章节
        if P_CONTENTS.nexts(elem_text):
            break
        if title_in_header := (i == 0 and "|" in elem_text):
            elem_text = elem_text.split("|")[1].strip()
        if P_INVALID_ROOT_TITLE.nexts(elem_text):
            if title_in_header:
                break
            paras = []
            last_style = style
            continue
        elem_text = clear_syl_title(elem_text, remove_bound_num=True)
        # http://************:55647/#/project/remark/245280?treeId=5589&fileId=66336&schemaId=28&projectId=17&schemaKey=E(d)(ii)-1&page=22 大章节标题带了continued
        if P_ALL_CHAPTER_TITLES.nexts(elem_text):
            return elem_text.upper()
        if title_in_header:
            fake_title = elem_text
        else:
            elem_type = element.type if is_interdoc else element["class"]
            # 样式不同则清空paras，以当前元素块为第一个
            if (
                elem_type != PDFInsightClassEnum.PARAGRAPH.value
                or (last_style and style != last_style)
                or (last_index and index != last_index + 1)
            ):
                paras = []
            elif len(paras) > 3:
                paras.pop(0)
            # 考虑这种目录： https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/3168#note_457200
            paras.append(elem_text)
            fake_title = " ".join(paras)
        last_style, last_index = style, index
        if P_ALL_CHAPTER_TITLES.nexts(fake_title):
            # 如果一个目录标题换了三行，第一行+第二行已经识别到，需要覆盖
            final_title = fake_title
        if title_in_header:
            break
    if not final_title or P_INVALID_ROOT_TITLE.nexts(final_title):
        return ""
    return final_title.upper()


@dataclass
class UnifiedCell:
    cell: dict | ChildCell
    row: int
    col: int
    is_interdoc: bool = False

    @property
    def chars(self):
        if self.is_interdoc:
            return []
        return self.cell["chars"]

    @cached_property
    def chars_only_word(self):
        return [c for c in self.chars if not P_NOT_WORD.search(c["text"])]

    @cached_property
    def text(self) -> str:
        return self.cell.text if self.is_interdoc else self.cell["text"]

    @cached_property
    def no_cn_text(self) -> str:
        return P_CHNS.sub("", P_CHINESE.sub(" ", self.text)).strip()

    @cached_property
    def clean_text(self) -> str:
        return clean_title(self.text)

    @property
    def left_border(self):
        if self.is_interdoc:
            return self.cell.box.left
        return self.cell["box"][0]

    @property
    def right_border(self):
        if self.is_interdoc:
            return self.cell.box.right
        return self.cell["box"][2]

    @property
    def text_left_border(self):
        """
        单元格中文字的左边距
        """
        if self.is_interdoc:
            return None
        return self.chars[0]["box"][0] if self.chars else None

    @property
    def text_right_border(self):
        """
        单元格中文字的右边距
        """
        if self.is_interdoc:
            return None
        return self.chars[-1]["box"][2] if self.chars else None


@dataclass
class UnifiedElement:
    element: Union[dict, "Element"]  # noqa

    @cached_property
    def is_interdoc(self):
        return not isinstance(self.element, dict)

    @property
    def index(self):
        return self.element.index if self.is_interdoc else self.element["index"]

    @property
    def text(self):
        text = self.element.text if self.is_interdoc else self.element.get("text")
        return text or ""

    @cached_property
    def cleaned_title(self):
        return clean_title(self.added_space_text) if self.text else ""

    @cached_property
    def no_prefix_title(self):
        return P_CHAPTER_PREFIX.sub("", self.cleaned_title).strip() if self.cleaned_title else ""

    @property
    def box(self) -> Box | list:
        if self.is_interdoc:
            return self.element.box
        return self.element.get("outline")

    @property
    def chars(self):
        return [] if self.is_interdoc else self.element.get("chars") or []

    @property
    def page(self):
        return self.element.page if self.is_interdoc else self.element["page"]

    @property
    def position(self):
        return self.element.position if self.is_interdoc else self.element.get("position") or [0, 0]

    @property
    def element_type(self):
        return self.element.type if self.is_interdoc else self.element["class"]

    @property
    def is_multi_line(self):
        if self.is_interdoc:
            return False
        return self.element.get("type") == "PARAGRAPH_2"

    @property
    def is_paragraph(self):
        return self.element_type == PDFInsightClassEnum.PARAGRAPH.value

    @property
    def is_table(self):
        return self.element_type == PDFInsightClassEnum.TABLE.value

    @property
    def border_left(self):
        return self.element.box.left if self.is_interdoc else self.element["outline"][0]

    @property
    def border_right(self):
        return self.element.box.right if self.is_interdoc else self.element["outline"][2]

    @property
    def border_bottom(self):
        return self.element.box.top if self.is_interdoc else self.element["outline"][-1]

    @property
    def border_bottom_first_line(self):
        """
        取多行段落中，第一行文字的bottom
        """
        if not self.is_paragraph:
            return self.border_bottom
        if self.is_multi_line and self.chars:
            return min(char["box"][-1] for char in self.chars)
        return self.border_bottom

    @property
    def border_right_of_last_line(self):
        """
        取多行段落中，最后一行文字的最右侧
        """
        if not self.is_paragraph:
            return self.border_bottom
        return outline_right_of_last_line(self.element)

    @cached_property
    def border_left_without_prefix(self) -> float | None:
        if not P_CHAPTER_PREFIX.search(self.added_space_text) or not self.is_paragraph:
            return self.border_left
        if self.is_interdoc:
            return None
        if matched := P_CHAPTER_PREFIX.search(self.added_space_text):
            position = matched.end()
            if position < len(self.chars):
                return self.chars[matched.end()]["box"][0]
            return None
        return self.border_left

    @cached_property
    def added_space_text(self):
        """
        https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/3168#note_494834
        序号和标题之间有空格，但被识别丢了，根据字符之间距离，补上空格
        添加空格依据：找出标题中最宽的字符，若前缀和标题之间宽度>该字符宽度，则在对应位置添加空格
        # TODO 如果max_word_width太宽，考虑： 向上取整后去掉最大值和最小值，再求最大值
        """
        if not self.is_paragraph or not self.chars or not self.text:
            return ""
        space_index = first_space_index(self.element)
        if space_index == 0:
            return self.text
        return clean_title(self.text[:space_index] + " " + self.text[space_index:])


@dataclass
class UnifiedTableElement(UnifiedElement):
    @cached_property
    def tbl_cells(self) -> list[UnifiedCell]:
        if not self.is_table:
            return []
        cells = []
        if self.is_interdoc:
            for cell in self.element.cells:
                cell = UnifiedCell(cell=cell, row=cell.row, col=cell.col, is_interdoc=self.is_interdoc)
                cells.append(cell)
        else:
            tbl_cells = self.element.get("origin_cells") or self.element.get("cells") or []
            for index_, cell in sorted(
                tbl_cells.items(), key=lambda x: (int(x[0].split("_")[0]), int(x[0].split("_")[1]))
            ):
                row, col = map(int, index_.split("_"))
                cell = UnifiedCell(cell, row=int(row), col=int(col), is_interdoc=self.is_interdoc)
                cells.append(cell)
        return cells

    @cached_property
    def tbl_first_cell(self) -> UnifiedCell | None:
        if not self.is_table:
            return None
        cells = [cell for cell in self.tbl_cells if cell.row == cell.col == 0]
        return cells[0] if cells else None

    def is_merged_cell(self, cell: UnifiedCell):
        if self.is_interdoc:
            return False
        # http://************:55647/#/project/remark/234282?treeId=13571&fileId=66669&schemaId=15&projectId=13571&schemaKey=B73
        # 合并单元格会有重复内容
        return any([cell.row, cell.col] in merged[1:] for merged in self.element.get("merged") or [])


class UnifiedParaElement(UnifiedElement):
    def __init__(self, element, reader):
        super().__init__(element)
        self.reader = reader

    # @staticmethod
    # def is_prep_word(word:str):
    #     return len(word) < 4 or word.lower() in {"with", "into", "then", "about", "between", "through", "during", "before", "after"}

    @cached_property
    def chars_only_word(self):
        return [c for c in self.chars if not P_NOT_WORD.search(c["text"])]

    @cached_property
    def is_upper(self):
        return not P_LOWER_CASE.search(self.no_prefix_title)

    @cached_property
    def font_style(self) -> FontStyle | None:
        """
        TODO interdoc拿不到chars和styles
        """
        if self.is_interdoc:
            return None
        return self.reader.get_font_style(self.chars_only_word)

    @cached_property
    def prefix_format(self) -> str:
        """
        标题前缀格式，例如(A)
        """
        return get_prefix_format(self.added_space_text)

    @cached_property
    def prefix_words(self) -> str:
        return prefix_words(self.added_space_text)

    def prefix_num(self, is_roman: bool = False) -> int | None:
        return get_prefix_num(self.added_space_text, is_roman)

    @cached_property
    def is_valid_title(self) -> bool:
        if not self.is_paragraph:
            return False
        return is_valid_title(self)

    @cached_property
    def is_sentence_like(self) -> bool:
        return is_sentence_like(self)


def prefix_words(title: str) -> str:
    """
    标题中的序号，不包含括号等格式， 例如: 1, a, iii等
    """
    if matched := P_CHAPTER_PREFIX_EXTRACTOR.nexts(title):
        group = matched.groupdict()
        if value := (group.get("word") or group.get("roman")):
            return value
        if num := group.get("num"):
            num1, num2, word1 = group.get("num1"), group.get("num2"), group.get("word1")
            if num1 and num2:
                return f"{num1}.{num2}.{num}"
            if num1:
                return f"{num1}.{num}"
            if word1:
                return f"{num}{word1}"
            return num
    return ""


def is_sentence_like(chapter_like: Union["UnifiedChapter", "UnifiedParaElement"]) -> bool:  # noqa
    """
    判断是否像句子，而非标题
    """
    # 全大写或加粗，则不认为是句子
    if chapter_like.is_upper or (chapter_like.font_style and chapter_like.font_style.bold):
        return False
    return P_SEN_TITLE.nexts(chapter_like.cleaned_title)


def is_surround_title(title):
    # 括号不对称，则是无效目录
    if title.count("(") + title.count("（") != title.count(")") + title.count("）"):
        return True
    # 前后都是括号
    if title.startswith(("(", "（")) and title.endswith((")", "）")):
        return True
    # 前后都是引号
    quotes = ('"', "'", "‘", "’", "“", "”")
    if title.startswith(quotes) and title.endswith(quotes):
        return True
    return False


def is_valid_title(chapter_like: Union["UnifiedChapter", "UnifiedParaElement"]):  # noqa
    # 删除中文后只有序号和单个字符
    if len(chapter_like.no_prefix_title) < 2:
        return False
    # 句子长度小于4
    if len(chapter_like.cleaned_title.replace(" ", "")) < 4:
        return False
    # 以-开头
    if chapter_like.cleaned_title.startswith(tuple(R_MIDDLE_DASHES)):
        return False
    # 句号结尾且包含至少两个句子+过多的逗号
    if (
        chapter_like.cleaned_title.endswith(".")
        and chapter_like.cleaned_title.count(".") > 1
        and (chapter_like.cleaned_title.count(",") + chapter_like.cleaned_title.count("，")) > 5
    ):
        return False
    if isinstance(chapter_like, UnifiedParaElement):
        if is_surround_title(chapter_like.no_prefix_title):
            return False
    else:
        # 括号或者引号不对称或者前后都是括号或者引号，则是无效目录
        no_pre_text_of_elt = chapter_like.element.no_prefix_title
        if is_surround_title(chapter_like.no_prefix_title) or (
            len(no_pre_text_of_elt) > len(chapter_like.no_prefix_title) and is_surround_title(no_pre_text_of_elt)
        ):
            return False
    if P_INVALID_TITLE.nexts(chapter_like.cleaned_title):
        return False
    return True


def clean_title(title: str, remove_bound_num=False):
    # http://************:55647/#/hkex/annual-report-checking/report-review/257764?fileId=68768&schemaId=5&rule=B1&delist=0&page=48 index=484
    title = title.replace("", "fi")
    title = P_CHINESE.sub(" ", title).strip()
    title = P_BLANK.sub(" ", title)
    title = P_CHNS.sub("", title).strip()
    has_words = P_WORDS.search(title)
    if remove_bound_num and has_words:
        title = P_CHAPTER_BORDER_NUM.sub("", title).strip()
    # 处理这种问题： 中英文识别到了一起，删除中文后，标题前缀后缀相同，删除后缀
    # http://************:55647/#/hkex/annual-report-checking/report-review/257727?fileId=68760&schemaId=5&rule=B1&page=162 元素块2605
    if has_words and P_INVALID_END.search(title):
        title = P_INVALID_END.sub(r"\g<prefix> \g<title>", title).strip()
    return title


def get_prefix_format(text):
    """
    标题前缀格式，例如(A)
    """
    for regex, repl in TITLE_STYLE_MAP.items():
        if not regex.search(text):
            continue
        return regex.sub(repl, text).replace("（", "(").replace("）", ")").strip().replace(" ", "")
    return None


def get_prefix_num(text: str, is_roman: bool = False):
    if not (matched := P_CHAPTER_PREFIX_EXTRACTOR.nexts(text)):
        # 无法识别的样式
        return 0
    group = matched.groupdict()
    word1, num, word, roman = group.get("word1"), group.get("num"), group.get("word"), group.get("roman")
    if is_roman and word and word.lower() in {"i", "v", "x", "l"}:
        # 这种场景当作罗马数字来处理
        roman = word
        word = None
    if word1:
        return ord(word1.replace(".", "").lower()) - 96
    if num:
        return int(num)
    if word:
        return ord(word.lower()) - 96
    if roman:
        return roman_to_int(roman.upper())
    return 0


def format_title(title: str):
    return P_NOT_WORD.sub("", P_REPEAT_WORDS.sub(r"\1", title).replace("&", "AND")).upper()


def is_same_title(title1, title2):
    return format_title(title1) == format_title(title2)


def is_title_in_page(page_texts: list[str], title: str) -> bool:
    # 取当前页面内容的前4项文本
    if not page_texts:
        return False
    page_texts = [para for para in page_texts if any(t in para for t in title.split())]
    if not page_texts:
        return False
    for i in range(len(page_texts)):
        for j in range(i + 1, len(page_texts) + 1):
            merged_para = " ".join(page_texts[i:j])
            if is_same_title(title, merged_para):
                return True
    return False


@dataclass
class LastRow:
    found_page: bool = False
    last_style: FontStyle | None = None
    last_is_page_end: bool = False
    last_border: float = 0


def get_contents(reader, elements: list[dict | Element], found: bool = False) -> tuple[bool, dict[int, str]]:
    """
    读目录内容，目录一般是一个表格，但也可能是表格+段落混合，其中还可能带子目录
    1. 多个目录在同一个单元格： http://************:55647/#/project/remark/234012?treeId=38073&fileId=66624
    2. 有二级目录： http://************:55647/#/project/remark/248758?treeId=3976&fileId=83437
                  http://************:55647/#/project/remark/250577?projectId=17&treeId=18843&fileId=68552&schemaId=15
    3. 标题在表格中： http://************:55647/#/project/remark/249165?treeId=4817&fileId=83666
    4. 两列： http://************:55647/#/project/remark/257077?treeId=12192&fileId=88698
    5. 右对齐: http://************:55647/#/project/remark/234282?treeId=13571&fileId=66669
    6. 一个标题多行： http://************:55647/#/project/remark/245358?treeId=38119&fileId=66258
    TODO 获取目录父子关系
    TODO 单元测试
    """
    all_texts, left_borders = [], []
    last_row = LastRow(found_page=False, last_style=None, last_is_page_end=False, last_border=0)
    for element_ in elements:
        element = (
            UnifiedTableElement(element_) if UnifiedElement(element_).is_table else UnifiedParaElement(element_, reader)
        )
        if not found:
            if element.is_paragraph:
                if element.cleaned_title and P_CONTENTS.nexts(element.cleaned_title):
                    found = True
                    continue
            elif element.is_table:
                if P_CONTENTS.nexts(element.tbl_first_cell.clean_text):
                    found = True
            else:
                continue
        if not found:
            continue
        if element.is_paragraph:
            if not (elem_text := element.cleaned_title) or P_INVALID_CONTENT.search(elem_text):
                continue
            if not last_row.found_page:
                if P_PAGE_NUM.search(elem_text):
                    last_row.found_page = True
                else:
                    continue
            row_style = element.font_style
            if (
                last_row.last_style
                and not P_PAGE_NUM.search(elem_text)
                and (last_row.last_is_page_end or last_row.last_style != row_style)
            ):
                # 当前行没有页码时：若样式与上一行不同，或上一行以页码结束，则认为当前行是无效的行（一般是汇总financial statements)
                # http://************:55647/#/project/remark/250577?projectId=17&treeId=18843&fileId=68552&schemaId=15
                # http://************:55647/#/project/remark/243508?treeId=6940&fileId=80183&schemaId=15&projectId=6940&schemaKey=B71
                last_row.last_style = None
                last_row.last_is_page_end = False
                continue
            all_texts.append(elem_text)
            border = element.border_left
            if P_PAGE_NUM.search(elem_text) or last_row.last_border == 0:
                left_borders.append(border)
                last_row.last_border = border
            else:
                left_borders.append(last_row.last_border)
            last_row.last_style = row_style
            last_row.last_is_page_end = bool(P_PAGE_NUM_END.search(elem_text))
        elif element.is_table:
            group_by_row = defaultdict(list)
            for cell in element.tbl_cells:
                if not cell.clean_text or P_INVALID_CONTENT.search(cell.clean_text) or element.is_merged_cell(cell):
                    continue
                group_by_row[cell.row].append(cell)
            texts, borders, last_row = concat_multi_rows(reader, group_by_row.values(), last_row)
            all_texts.extend(texts)
            left_borders.extend(borders)
        else:
            continue
    if not all_texts:
        return found, {}
    if not left_borders:
        return found, split_contents(all_texts)
    # 所有左边距离之间误差小于5, 则认为没有二级目录
    if all(abs(left_borders[i + 1] - left_borders[i]) < 5 for i in range(len(left_borders) - 1)):
        return found, split_contents(all_texts)
    # 使用K-Means算法进行聚类, 找出左边距与其他不同的记录
    data_array = np.array(left_borders).reshape(-1, 1)
    kmeans_left = KMeans(n_clusters=2, random_state=0, n_init="auto").fit(data_array)
    labels = kmeans_left.labels_
    if not any(labels) or not 0 < abs(sum([label - 1 for label in labels])) / sum(labels) < 0.4:
        # 0占比不合理，则认为没有二级目录
        return found, split_contents(all_texts)
    # http://************:55647/#/project/remark/249149?treeId=18843&fileId=83654 财务报表像一级目录，实际是二级目录
    return found, split_contents([t for i, t in enumerate(all_texts) if labels[i] > 0])


def split_contents(texts: list[str]) -> dict[int, str]:
    """
    将所有提取到的目录文本串起来，再用页码分割
    注意： 识别的页码之间可能有空格，需要替换
    http://************:55647/#/project/remark/9015?treeId=1080&fileId=83431&page=4
    """
    text = P_SPACE_NUM.sub(r"\g<num1>\g<num2>\g<num3>", "\n".join(texts))
    start = 0
    titles, pages = [], []
    for matched in chain(P_PAGE_NUM.finditer(text), (None,)):  # type: ignore
        end = matched.start() if matched else len(text)
        if title := clean_title(text[start:end]):
            titles.append(title)
        if matched:
            pages.append(int(matched.group().strip().split("-")[0]))
            start = matched.end()
    if len(set(pages)) != len(titles):
        # 页码重复或者和目录不是一对一，则当作目录提取错误
        logger.debug(f"Get contents failed:\n{pages}\n{titles}")
        return {}
    return {pages[i]: titles[i].upper() for i in range(len(titles))}


def concat_multi_rows(
    reader, row_cells: list[list[UnifiedCell]], last_row: LastRow
) -> tuple[list[str], list[float], LastRow | None]:
    """
    http://************:55647/#/project/remark/257077?treeId=12192&fileId=88698
    目录可能有多列，且列数不确定，需要根据页码切分列
    | 02 | 目录名称1        | 33 目录名称4 |
    | 10 | 目录名称2        | 40 目录名称5 |
    |    | 目录名称2的另一半 | 41 目录名称6 |
    """
    page_cell_cols, multi_col_cells = [], []
    for cells in row_cells:
        if not last_row.found_page:
            if any(P_PAGE_NUM.search(cell.text) for cell in cells):
                last_row.found_page = True
            else:
                continue
        if not page_cell_cols:
            page_cell_cols = [cell.col for cell in cells if P_PAGE_NUM.search(cell.text)]
        row_style = reader.get_font_style(chain.from_iterable(c.chars_only_word for c in cells))
        if (
            last_row.last_style
            and not any(P_PAGE_NUM.search(cell.text) for cell in cells)
            and (last_row.last_is_page_end or last_row.last_style != row_style)
        ):
            # 当前行没有页码时：若样式与上一行不同，或上一行以页码结束，则认为当前行是无效的行（一般是汇总financial statements)
            # http://************:55647/#/project/remark/250577?projectId=17&treeId=18843&fileId=68552&schemaId=15
            # http://************:55647/#/project/remark/243508?treeId=6940&fileId=80183&schemaId=15&projectId=6940&schemaKey=B71
            last_row.last_style = None
            last_row.last_is_page_end = False
            continue
        row = []
        last_is_page = bool(P_PAGE_NUM_END.search(cells[-1].clean_text))
        if len(page_cell_cols) <= 1:
            row.append(cells)
        else:
            if page_cell_cols[0] > 0:
                row.append(cells[: page_cell_cols[0] + 1] if last_is_page else cells[: page_cell_cols[0]])
            for i in range(len(page_cell_cols) - 1):
                if last_is_page:
                    merge_cells = cells[page_cell_cols[i] + 1 : page_cell_cols[i + 1] + 1]
                else:
                    merge_cells = cells[page_cell_cols[i] : page_cell_cols[i + 1]]
                row.append(merge_cells)
            if page_cell_cols[0] == 0:
                row.append(cells[page_cell_cols[-1] :])
        last_row.last_style = row_style
        last_row.last_is_page_end = last_is_page
        multi_col_cells.append(row)
    if not multi_col_cells:
        return [], [], last_row
    all_texts, left_borders = [], []
    count_cols = len(multi_col_cells[0])
    if count_cols > 2:
        # 不处理三列目录的场景
        return [], [], last_row
    for i in range(count_cols):
        for cells in [row[i] for row in multi_col_cells]:
            row_texts = []
            for cell in cells:
                if P_PAGE_NUM.search(cell.text):
                    row_texts.append(cell.no_cn_text)
                    continue
                # 识别到的多行内容单元格中，可能出现样式不同的行
                # http://************:55647/#/hkex/annual-report-checking/report-review/340377?fileId=104071&schemaId=5&rule=B1&delist=0
                if len(lines := cell.text.split("\n")) > 1:
                    first_row_style = reader.get_font_style(cell.chars[: len(lines[0])])
                    if reader.get_font_style(cell.chars[len(cell.text) - len(lines[-1]) :]) == first_row_style:
                        row_texts.append(cell.no_cn_text)
                    else:
                        row_texts.append(P_CHNS.sub("", " ".join(lines[:-1])))
                else:
                    row_texts.append(cell.no_cn_text)
                    continue

            row_text = "\n".join(row_texts)
            all_texts.append(row_text)
            if count_cols != 1:
                # 两栏目录的场景，不考虑左边距
                continue
            if P_PAGE_NUM.search(row_text) or last_row.last_border == 0:
                border = cells[0].left_border
                left_borders.append(border)
                last_row.last_border = border
            else:
                left_borders.append(last_row.last_border)
    return all_texts, left_borders, last_row


def find_consolidate_statement_element(pdfinsight_reader, page, table_element):
    table_index = table_element["index"]
    left, top, right, bottom = table_element["outline"]
    para_elements = [e for e in pdfinsight_reader.page_dict[page].elements if e["index"] < table_index]
    valid_elements, invalid_indices = [], set()
    if len(pdfinsight_reader.page_columns.get(table_element["page"]) or []) > 1:
        # 优先找表格正上方的段落
        for para_elt in pdfinsight_reader.page_dict[page].elements:
            if para_elt["index"] >= table_index or is_table_elt(para_elt):
                break
            if not is_para_elt(para_elt):
                continue
            elt_left, _, elt_right, elt_bottom = para_elt["outline"]
            if not P_CONSOLIDATED_STATEMENT.search(clean_txt(para_elt["text"], remove_cn_text=True)):
                continue
            # 表格上方的段落，更可能是表名
            if (
                elt_bottom < top
                and (abs(left - elt_left) < 2 or elt_left > left)
                and (abs(right - elt_right) < 2 or elt_right < right)
            ):
                valid_elements.append(para_elt)
            # 表格左侧/右侧的段落
            if para_elt["outline"][0] > right or para_elt["outline"][2] < left:
                invalid_indices.add(para_elt["index"])
    if len(invalid_indices) > 1:
        # 如果左侧/右侧有太多段落，且都能匹配P_CONSOLIDATED_STATEMENT，则跳过这些段落
        # http://************:55647/#/project/remark/266808?treeId=11703&fileId=69010&schemaId=29&projectId=17&page=233 index=3809
        para_elements = [e for e in para_elements if e["index"] not in invalid_indices]
    if len(valid_elements) == 1:
        # 右栏的元素块index更小，导致找错： https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/3168#note_741150
        return valid_elements[0]
    for element in para_elements[::-1]:
        if is_table_elt(element):
            break
        if P_CONSOLIDATED_STATEMENT.search(element.get("text") or ""):
            return element
    return None


def find_table_title(pdfinsight_reader, table_index: int, return_element=False) -> str | dict | None:
    """
    根据表格index向回找第一个段落作为表格标题
    """

    elements_above = pdfinsight_reader.find_elements_near_by(
        table_index, amount=30, step=-1, aim_types=["PARAGRAPH", "SYLLABUS"], neg_patterns=P_INVALID_TBL_TITLES
    )
    # 如果是合并财务报表，直接取报表名
    # http://************:55647/#/project/remark/379228?projectId=17&treeId=3370&fileId=156870&schemaId=29
    _, table_element = pdfinsight_reader.find_element_by_index(table_index)
    if title_element := find_consolidate_statement_element(pdfinsight_reader, table_element["page"], table_element):
        return title_element if return_element else title_element["text"]
    # 如果未识别到合并财务报表的表名，或合并财务报表的标题在侧栏，需要用目录页码来识别
    for title, index_range in pdfinsight_reader.root_chapter_ranges_from_contents.items():
        if P_CONSOLIDATED_STATEMENT.search(title) and is_in_range(table_index, index_range):
            _, start_element = pdfinsight_reader.find_element_by_index(index_range[0])
            # 注意这里可能返回None，因为识别不到标题元素块
            # http://************:55647/#/project/remark/383052?projectId=17&treeId=37668&fileId=157765&schemaId=29&page=87
            if title_element := find_consolidate_statement_element(
                pdfinsight_reader, start_element["page"], table_element
            ):
                return title_element if return_element else title_element["text"]
            return None if return_element else title
    title_element = None
    for element in elements_above:
        if pdfinsight_reader.is_skip_element(
            element,
            aim_types={PDFInsightClassEnum.PARAGRAPH.value},
            skip_chinese=True,
            skip_tbl_unit=True,
            skip_merged=True,
        ):
            continue
        if syllabus := pdfinsight_reader.syllabus_reader.elt_syllabus_dict.get(element["index"]):
            if (
                syllabus["parent"] == -1
                and clear_syl_title(syllabus["title"]) in pdfinsight_reader.page_chapter_from_first_para.values()
            ):
                continue
            # 遇到章节标题，则把章节标题作为表名
            title_element = element
            break
        title_element = element
        break
    if return_element:
        return title_element
    return clean_txt(title_element["text"], remove_cn_text=True) if title_element else ""


"""
该脚本是由王旭提供的用于修正syllabuses的脚本，详情点链接查看
https://mm.paodingai.com/cheftin/pl/1f9o6cx7qprxpqmdqi5kr1a8eo
"""


def make_syllabuses_range(syllabuses, data):
    valid_dest = False
    for syl in syllabuses:
        if syl["dest"]["page_index"] and syl["dest"]["page_index"] >= 0:
            valid_dest = True
            break
    if valid_dest:
        make_syllabuses_range_by_dest(syllabuses, data)
    else:
        make_syllabuses_range_by_element(syllabuses, data)


def make_syllabuses_range_by_dest(syllabuses, data):
    elements = sorted(
        data.get("paragraphs", []) + data.get("page_headers", []) + data.get("tables", []), key=lambda x: x["index"]
    )
    ele_idx = 0
    current = {}
    for syllabus in syllabuses:
        dest = syllabus["dest"]
        level = syllabus["level"]
        if dest["page_index"] < 0 or dest["box"] is None:
            syllabus["range"] = [-1, -1]
            continue
        for idx, element in enumerate(elements[ele_idx:]):
            if (element["page"] == dest["page_index"] and element["outline"][3] > dest["box"][1]) or element[
                "page"
            ] > dest["page_index"]:
                syl_element = syllabus.get("element")
                if syl_element and (
                    abs(syl_element - element["index"]) < 5 or is_multi_column_page(data, element["page"])
                ):
                    start = syl_element
                else:
                    start = element["index"]
                syllabus["range"] = [start]
                for lvl, syl in list(current.items()):
                    if lvl >= level:
                        if len(syl["range"]) == 1:
                            syl["range"].append(start)
                        del current[lvl]
                ele_idx += idx
                break
        if "range" not in syllabus:
            syllabus["range"] = [-1, -1]
        current[level] = syllabus

    for syl in current.values():
        if len(syl["range"]) == 1:
            syl["range"].append(elements[-1]["index"] + 1)


def is_multi_column_page(data, page_idx):
    for idx in [int(page_idx), str(page_idx)]:
        page = data.get("pages", {}).get(idx, {})
        if not page:
            continue
        column = page.get("column")
        return column and len(column["grid"]["columns"]) > 0
    return False


def make_syllabuses_range_by_element(syllabuses, data):
    elements = sorted(
        data.get("paragraphs", []) + data.get("page_headers", []) + data.get("tables", []), key=lambda x: x["index"]
    )
    ele_idx = 0
    current = {}
    for syllabus in syllabuses:
        level = syllabus["level"]
        if not syllabus.get("element") or syllabus["element"] < 0:
            syllabus["range"] = [-1, -1]
            continue
        for idx, element in enumerate(elements[ele_idx:]):
            if element["index"] >= syllabus["element"]:
                syllabus["range"] = [element["index"]]
                for lvl, syl in list(current.items()):
                    if lvl >= level:
                        if len(syl["range"]) == 1:
                            syl["range"].append(element["index"])
                        del current[lvl]
                ele_idx += idx
                break
        if "range" not in syllabus:
            syllabus["range"] = [-1, -1]
        current[level] = syllabus

    for syl in current.values():
        if len(syl["range"]) == 1:
            syl["range"].append(elements[-1]["index"] + 1)


def get_first_row_texts_of_table(element) -> list[str]:
    """
    取表格第一行的文本，如果有origin_cells则优先取
    """
    cells = element.get("origin_cells") or element["cells"]
    max_col = max_col_of_tbl_element(element)
    sorted_cells = sorted(cells.items(), key=lambda x: tuple(int(i) for i in x[0].split("_")))
    header_cells = [cell for key, cell in sorted_cells if key.startswith("0_") and cell.get("text")]
    # 第一行存在横向合并单元格且前两行存在纵向合并单元格，则取第二行一起做为标题行
    if (
        len(header_cells) < max_col
        and element.get("merged")
        and any(all(c[0] == 0 for c in cells) for cells in element["merged"])
        and any({0, 1}.issubset({c[0] for c in cells}) for cells in element["merged"])
    ):
        header_cells.extend(cell for key, cell in sorted_cells if key.startswith("1_"))
    results = []
    for cell in header_cells:
        # 列名包含continued： http://************:55647/#/project/remark/406553?treeId=5815&fileId=113257&schemaId=18&projectId=5815&schemaKey=C2.1.1&page=131 index=1060
        if cell_text := clean_txt(P_CONTINUED.sub("", cell.get("text") or "")):
            if cell_text not in results:
                results.append(cell_text)
    return results


def get_syllabus_style(pdfinsight, syllabus):
    """
    计算章节所属元素块的样式
    """
    _, element = pdfinsight.find_element_by_index(syllabus.get("element") or syllabus["range"][0])
    if not is_para_elt(element) or not element.get("chars"):
        return None
    return pdfinsight.get_font_style(element["chars"])


def find_real_syllabus(pdfinsight, syllabus: dict):
    # http://************:55647/#/project/remark/266163?treeId=13884&fileId=70447&schemaId=15&projectId=17&schemaKey=B1
    page = syllabus["dest"]["page_index"]
    real_syllabus = syllabus
    start, end = syllabus["range"]
    ranges = [start, end]
    syllabus_style = get_syllabus_style(pdfinsight, syllabus)
    if not syllabus_style:
        return syllabus
    if syllabus["parent"] == -1 and (title := pdfinsight.page_chapter_from_first_para.get(page)):
        child_style = None
        if children := syllabus["children"]:
            child_style = get_syllabus_style(pdfinsight, pdfinsight.syllabus_reader.syllabus_dict.get(children[0]))
        first_page = min([p for p, t in pdfinsight.page_chapter_from_first_para.items() if t == title])
        if first_page == page:
            # 如果是一级目录的开始页，说明章节正确
            return syllabus
        prev_index, prev_syllabus = syllabus["index"], syllabus
        while prev_index > 1:
            prev_index -= 1
            prev_syllabus = pdfinsight.syllabus_dict.get(prev_index)
            # 跨了21页： http://************:55647/#/project/remark/260714?treeId=11891&fileId=69357&schemaId=18&projectId=17&schemaKey=C7.1
            if not prev_syllabus or page - prev_syllabus["dest"]["page_index"] > 25:
                return syllabus
            if prev_syllabus["parent"] == -1:
                # 必须去掉章节名称中的页码
                # http://************:55647/#/project/remark/260714?treeId=11891&fileId=69357&schemaId=18&projectId=17&schemaKey=C7.1
                if (
                    prev_syllabus["dest"]["page_index"] in pdfinsight.page_chapter_from_first_para
                    and title == clear_syl_title(prev_syllabus["title"], remove_bound_num=True).upper()
                ):
                    ranges.extend(prev_syllabus["range"])
                    # 必须是一级目录
                    continue
            # 找到的上一页的章节，样式与当前章节样式相同，或与当前章节的子章节样式相同，说明情况复杂，暂不处理
            # https://jura6-lir.paodingai.com/#/hkex/annual-report-checking/report-review/258057?fileId=68826&schemaId=5&rule=C7.4.1 index=703
            cur_style = get_syllabus_style(pdfinsight, prev_syllabus)
            if syllabus_style in [cur_style, child_style]:
                return syllabus
            ranges.extend(prev_syllabus["range"])
            # 页面第一个root名称被误识别为章节，则向前找最近的一个目录作为正式目录，但是范围要更新
            real_syllabus = prev_syllabus | {"range": [min(ranges), max(ranges)]}
            break
    # 所属元素块是合并行，取合并后的范围+标题
    _, syllabus_element = pdfinsight.find_element_by_index(syllabus["element"])
    if merged := syllabus_element.get("page_merged_paragraph"):
        merged_text, merged_indices = merged.get("text") or "", merged.get("paragraph_indices") or []
        first_syllabus, ranges = None, []
        for idx in merged_indices:
            if idx == syllabus_element["index"]:
                if not first_syllabus:
                    first_syllabus = real_syllabus
                ranges.extend(real_syllabus["range"])
            elif syllabus_ := pdfinsight.syllabus_reader.elt_syllabus_dict.get(idx):
                if not first_syllabus:
                    first_syllabus = syllabus_
                ranges.extend(syllabus_["range"])
        if ranges:
            real_syllabus = first_syllabus | {"title": merged_text, "range": [min(ranges), max(ranges)]}
    elif end - start < 3:
        # start与element不同，应取element的index
        element_index = syllabus.get("element") or start
        page = get_keys(syllabus, ["dest", "page_index"])
        # 多个相邻的end-start<2的章节元素块，做合并
        for i in range(1, 4):
            next_index = element_index + i
            # 相邻元素块必须是章节
            if not (next_syllabus := pdfinsight.syllabus_reader.elt_syllabus_dict.get(next_index)):
                break
            # 必须在同一页
            if page != get_keys(next_syllabus, ["dest", "page_index"]):
                break
            try:
                _, next_element = pdfinsight.find_element_by_index(next_index)
            except IndexError:
                break
            # 样式必须相同
            if pdfinsight.get_font_style(next_element["chars"]) != syllabus_style:
                # TODO 样式不同时，视情况接下来的章节都是当前章节的子章节？
                break
            ranges.extend(next_syllabus["range"])
            real_syllabus = next_syllabus | {
                "title": f"{real_syllabus['title']} {next_syllabus['title']}",
                "range": [min(ranges), max(ranges)],
            }
            if next_syllabus["range"][1] - next_syllabus["range"][0] < 2:
                continue
            break
    return real_syllabus
