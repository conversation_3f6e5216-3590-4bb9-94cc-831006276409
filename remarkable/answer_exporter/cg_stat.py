import logging
import tempfile
from dataclasses import dataclass
from datetime import datetime
from pathlib import Path

import openpyxl
from peewee import JO<PERSON>, fn

from remarkable.answer.node import AnswerItem
from remarkable.common.constants import DocType
from remarkable.common.util import standard_stock
from remarkable.config import project_root
from remarkable.db import pw_db
from remarkable.models import is_empty
from remarkable.models.answer import Answer
from remarkable.models.hkex_file_meta import HKEXFileMeta
from remarkable.models.mold import special_mold
from remarkable.models.new_file import NewFile
from remarkable.models.new_mold import NewMold
from remarkable.models.new_question import NewQuestion
from remarkable.optools.fm_upload import FMUploader
from remarkable.optools.stat_util import AnswerPrepare, Url
from remarkable.pdfinsight.reader import PdfinsightReader

DATE_FMT = "%Y%m%d"
logger = logging.getLogger(__name__)


@dataclass
class CgStat:
    stock: str
    year: str
    mid: int
    rule_refer: dict
    need_label_items: list

    async def stat_accuracy_info(self):
        cond = HKEXFileMeta.stock_code == standard_stock(self.stock)
        cond &= HKEXFileMeta.report_year == self.year
        cond &= HKEXFileMeta.doc_type == DocType.AR.value
        query = HKEXFileMeta.select().where(cond)
        hkex_file_meta = await pw_db.first(query)

        if not hkex_file_meta or not hkex_file_meta.fid:
            logger.info(f"{self.stock=} {self.year=} not found")
            return None
        data = await self.prepare_data(hkex_file_meta.fid)
        if not data:
            return {}
        _, _, qid, answer, preset_answer = data[0]
        stat_result = await self.get_stat_results(qid, hkex_file_meta)
        if not stat_result:
            return {}
        stat_data = self.organization_data(answer, preset_answer, stat_result)
        return {
            "fid": hkex_file_meta.fid,
            "stock": self.stock,
            "result": stat_data,
        }

    async def prepare_data(self, file_id: int, project_id: int = None, date_start=None, date_end=None):
        cte = Answer.select(
            Answer.qid,
            Answer.updated_utc,
            fn.RANK().over(partition_by=[Answer.qid], order_by=[Answer.created_utc.desc()]).alias("rank"),
        ).cte("tmp_ans")
        cond = (
            (NewQuestion.mold == special_mold.v5_id)
            # & (NewQuestion.status >= QuestionStatus.DOING)  # 无论是否答题完毕 只要有question.answer 即可
            & (~is_empty(NewQuestion.answer))
            & (NewFile.deleted_utc == 0)
            & ((cte.c.rank == 1) | (cte.c.rank.is_null()))  # 取最新提交的答案时间戳
        )
        if file_id:
            cond &= NewQuestion.fid == file_id
        if date_start:
            cond &= cte.c.updated_utc >= datetime.strptime(date_start, DATE_FMT).timestamp()
        if date_end:
            cond &= cte.c.updated_utc <= datetime.strptime(date_end, DATE_FMT).timestamp()
        if project_id:
            cond &= NewFile.pid == project_id
        query = (
            NewQuestion.select(
                NewFile.id.alias("fid"), NewFile.name, NewQuestion.id, NewQuestion.answer, NewQuestion.preset_answer
            )
            .join(NewFile, on=(NewFile.id == NewQuestion.fid))
            .join(cte, join_type=JOIN.LEFT_OUTER, on=(cte.c.qid == NewQuestion.id))
            .where(cond)
            .order_by(NewFile.id)
            .with_cte(cte)
            .tuples()
        )
        return list(await pw_db.execute(query))

    def organization_data(self, answer, preset_answer, stat_result):
        schema_orders = {x: i for i, x in enumerate(answer["schema"]["schemas"][0]["orders"])}
        answer_items = [
            AnswerItem(**item)
            for item in answer["userAnswer"]["items"]
            if item.get("manual") is True or item.get("special_ui") is True
        ]
        answer_items = sorted(answer_items, key=lambda x: schema_orders.get(x.schema["data"]["label"], -1))
        answer_items_map = {item.schema["data"]["label"]: item for item in answer_items}

        preset_items_map = {
            item["schema"]["data"]["label"]: AnswerItem(**item) for item in preset_answer["userAnswer"]["items"]
        }
        ret = {}
        for label in schema_orders:
            ai_disclosure_classification, manual_disclosure_classification = None, None
            ai_tag_nt, manual_tag_nt = "NT", "NT"

            preset_answer_item = preset_items_map.get(label)
            if preset_answer_item:
                ai_disclosure_classification = preset_answer_item.enum
                at_tag_content = preset_answer_item.simple_text(preset_answer_item, enum=False)
                ai_tag_nt = "T" if at_tag_content else "NT"

            answer_item = answer_items_map.get(label)
            if answer_item and (answer_item.manual is True or answer_item.marker["name"] != "admin"):  # 预测的答案不要
                manual_disclosure_classification = answer_item.enum
                manual_tag_content = answer_item.simple_text(answer_item, enum=False)
                manual_tag_nt = "T" if manual_tag_content else "NT"

            ai_location_result = stat_result.get(label, {}).get(
                "result", "N"
            )  # 默认为N, 当没有标注答案时该字段结果为空
            ret[label] = {
                "AI Classification": self.convert_enum(ai_disclosure_classification),
                "Manual Classification": self.convert_enum(manual_disclosure_classification),
                "AI Tagging": ai_tag_nt,
                "Manual Tagging": manual_tag_nt,
                "AI Location Result": ai_location_result,
            }
        return ret

    async def get_stat_results(self, qid, hkex_file_meta):
        ret = {}
        mold = await NewMold.get_mold_by_qid(qid)
        orders = mold.data["schemas"][0]["orders"]
        question = await NewQuestion.find_by_id(qid)
        file = await NewFile.find_by_id(question.fid)
        preset_answer = AnswerPrepare.group_by_aid(question.preset_answer.get("userAnswer", {}))
        answer_instance = await question.get_user_merged_answer()
        standard_answer = AnswerPrepare.group_by_aid(answer_instance.get("userAnswer", {}))
        correct_counts = 0
        for key in orders:
            ai_answer = preset_answer.get(key)
            ai_answer = AnswerItem(**ai_answer[0])
            manual_answer = standard_answer.get(key)
            if not manual_answer:
                logger.error(f"manual_answer not found : {key=}, {question.fid=}, {hkex_file_meta.stock_code=}")
                url = Url(
                    question.fid,
                    special_mold.v5_id,
                    qid,
                    file.tree_id,
                    key,
                    scheme="https",
                    domain="jura-uat.paodingai.com",
                    custom_ui=True,
                    need_split=False,
                )
                logger.error(url)
                main_alais = self.rule_refer[key].main_alias
                self.need_label_items.append([hkex_file_meta.stock_code, main_alais, str(url)])
                continue
            manual_answer = AnswerItem(**manual_answer[0])
            if not manual_answer.enum:
                logger.error(f"manual enum not found : {key=}, {question.fid=}, {hkex_file_meta.stock_code=}")
                url = Url(
                    question.fid,
                    special_mold.v5_id,
                    qid,
                    file.tree_id,
                    key,
                    scheme="https",
                    domain="jura-uat.paodingai.com",
                    custom_ui=True,
                    need_split=False,
                )
                logger.error(url)
                main_alais = self.rule_refer[key].main_alias
                self.need_label_items.append([hkex_file_meta.stock_code, main_alais, str(url)])

            enum_result = ai_answer.enum.lower() == manual_answer.enum.lower() and ai_answer.enum != ""

            box_result = False
            ai_data = ai_answer.data if ai_answer else []
            manual_data = manual_answer.data if manual_answer else []
            if enum_result and not manual_data:
                box_result = True
            manual_boxes, ai_boxes = [], []
            for item in manual_data:
                manual_boxes.extend(item["boxes"])
            for item in ai_data:
                ai_boxes.extend(item["boxes"])
            for manual_box in manual_boxes:
                if any(self.box_same(manual_box["box"], ai_box["box"]) for ai_box in ai_boxes):
                    box_result = True
                    break
            result = enum_result and box_result
            if result:
                correct_counts += 1
            ret[key] = {
                "enum": "Y" if enum_result else "N",
                "box": "Y" if box_result else "N",
                "result": "Y" if result else "N",
            }

        return ret

    @staticmethod
    def box_same(manual_box, ai_box):
        percent = PdfinsightReader.overlap_percent(manual_box, ai_box, base="min")
        return percent >= 0.9

    @staticmethod
    def convert_enum(value):
        value = value.title() if value else ""
        if value == "Comply":
            return "C"
        if value == "No Disclosure":
            return "ND"
        if value == "Not Applicable":
            return "NA"
        return value

    @classmethod
    def stuff_data(cls, answers):
        template_path = Path(project_root) / "data/hkex/JURA5_CG_UAT_Round_1_Precision_Recall_Calculation_Template.xlsx"
        workbook = openpyxl.load_workbook(template_path)
        worksheet = workbook["RAW-Classification-60"]
        for col_idx in range(2, worksheet.max_column + 1):
            if col_idx - 2 > len(answers) - 1:
                break
            answer = answers[col_idx - 2]  # answer是数组 从0开始
            worksheet.cell(row=2, column=col_idx).value = answer["stock"]
            worksheet.cell(row=3, column=col_idx).value = answer["fid"]
            for row_idx, item in zip(range(4, 66), answer["result"].values()):
                worksheet.cell(row=row_idx, column=col_idx).value = item["AI Classification"]

            worksheet.cell(row=67, column=col_idx).value = answer["stock"]
            worksheet.cell(row=68, column=col_idx).value = answer["fid"]
            for row_idx, item in zip(range(69, 131), answer["result"].values()):
                worksheet.cell(row=row_idx, column=col_idx).value = item["Manual Classification"]

        worksheet = workbook["RAW-Location-60"]
        for col_idx in range(2, worksheet.max_column + 1):
            if col_idx - 2 > len(answers) - 1:
                break
            answer = answers[col_idx - 2]  # answer是数组 从0开始
            worksheet.cell(row=2, column=col_idx).value = answer["stock"]
            for row_idx, item in zip(range(3, 65), answer["result"].values()):
                worksheet.cell(row=row_idx, column=col_idx).value = item["AI Tagging"]

            worksheet.cell(row=66, column=col_idx).value = answer["stock"]
            for row_idx, item in zip(range(67, 131), answer["result"].values()):
                worksheet.cell(row=row_idx, column=col_idx).value = item["Manual Tagging"]

            worksheet.cell(row=130, column=col_idx).value = answer["stock"]
            for row_idx, item in zip(range(131, 193), answer["result"].values()):
                worksheet.cell(row=row_idx, column=col_idx).value = item["AI Location Result"]

        with tempfile.NamedTemporaryFile(
            prefix="JURA5_CG_UAT_Round_1_Precision_Recall_Calculation_", suffix=".xlsx"
        ) as tmp_fp:
            excel_path = tmp_fp.name
            workbook.save(excel_path)
            logger.info(f"run completed, {excel_path}")
            logger.info("upload to fm.paodingai.com ...")
            FMUploader().upload(Path(excel_path))
