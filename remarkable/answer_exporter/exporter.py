import asyncio
import json
import re
from collections import defaultdict

from remarkable.common.util import box_in_box, clean_txt
from remarkable.models.new_file import NewFile
from remarkable.models.new_question import NewQuestion
from remarkable.pdfinsight.reader import <PERSON>d<PERSON><PERSON><PERSON><PERSON>er
from remarkable.plugins.hkex.utils import <PERSON><PERSON><PERSON><PERSON><PERSON>


def get_rules():
    from remarkable.plugins.predict.config.hkex_listing_rule import predictors

    result = []
    for i in predictors:
        result.append(i["path"])

    return result


def get_box_related_elements(box, reader):
    page = box["page"]
    box = box["box"]
    outline = (box["box_left"], box["box_top"], box["box_right"], box["box_bottom"])
    elements = []
    for _, element in reader.find_elements_by_outline(page, outline):
        if element["class"] != "PARAGRAPH":
            continue

        elements.append(element)

    return elements


def collect_elements(item_elements_data):
    answer_elements = []
    for elements in item_elements_data:
        for element in elements:
            answer_elements.append(element)

    return answer_elements


def is_valid_span(boxes, element):
    min_word_count = 3

    item_text = clean_txt(" ".join(b["text"] for b in boxes))
    element_text = element["text"]
    is_valid = item_text in element_text and len(re.split(r"\s+", item_text)) >= min_word_count
    return is_valid


def get_boxes_text(boxes):
    return clean_txt(" ".join(i["text"] for i in boxes))


def collect_elements_data(boxes, reader):
    label_elements = []
    element_map = {}
    for box in boxes:
        box_elements = get_box_related_elements(box, reader)
        for box_element in box_elements:
            if box_element["index"] in element_map:
                continue
            element_map[box_element["index"]] = box_element
            label_elements.append(box_element)

    return label_elements


def collect_potential_records(boxes, reader, element_map, item):
    element_boxes_map = defaultdict(list)
    for box in boxes:
        box_related_elements = get_box_related_elements(box, reader)
        elements_index = ",".join([str(i["index"]) for i in box_related_elements])
        if elements_index:
            element_boxes_map[elements_index].append(box)

    valid = True
    potential_records = []
    for key in element_boxes_map:
        elements = [element_map[int(element_id)] for element_id in key.split(",")]
        if len(elements) == 1:
            r_boxes = [
                box_data for box_data in element_boxes_map[key] if box_in_box(box_data["box"], elements[0]["outline"])
            ]
            boxes_text = clean_txt(" ".join(b["text"] for b in element_boxes_map[key]))
            if elements[0]["text"] in boxes_text:
                potential_records.append({"texts": [elements[0]["text"]], "elements": [{"page": elements[0]["page"]}]})
            elif not is_valid_span(r_boxes, elements[0]):
                valid = False
                print("{:-^80}".format("log start"))
                print(item["key"])
                print("{:*^80}".format("box data"))
                print(
                    len(element_boxes_map[key]),
                    [(i["page"], i["text"][:80], i["text"][-20:]) for i in element_boxes_map[key]],
                )
                print(len(r_boxes), [(i["page"], i["text"][:80], i["text"][-20:]) for i in r_boxes])
                print(boxes_text, "\n")
                print(elements[0]["text"])
                print("{:-^80}".format("log end"))
                print()
                print()
                break
            potential_records.append({"texts": [get_boxes_text(boxes)], "elements": [{"page": elements[0]["page"]}]})
        else:
            potential_records.append(
                {"texts": [i["text"] for i in elements], "elements": [{"page": i["page"]} for i in elements]}
            )

    return valid, potential_records


def find_related_records(item, reader):
    item_data = item.get("data", [])
    records = []
    for label_data in item_data:
        boxes = label_data.get("boxes", [])
        label_elements = collect_elements_data(boxes, reader)
        element_map = {e["index"]: e for e in label_elements}
        if not label_elements:
            continue

        if len(label_elements) == 1:
            item_element = label_elements[0]
            if is_valid_span(boxes, item_element):
                records.append({"texts": [get_boxes_text(boxes)], "elements": [{"page": item_element["page"]}]})
        else:
            valid, potential_records = collect_potential_records(boxes, reader, element_map, item)

            if valid:
                records.extend(potential_records)
    return records


def get_para_answer_items(answer, item_rules, reader, fid):
    print("get_para_answer_items", fid)
    valid_data = []
    valid_values = ["positive statement", "negative statement"]
    for rule in item_rules:
        rule_name = rule[0]
        rule_answer = answer.get_rule_answer(rule_name)
        for item in rule_answer["answer"]:
            if not item.get("manual"):
                # 去掉非人工答案
                continue
            item_key = json.loads(item["key"])
            is_current_rule_answer = not item_key[-1].startswith(f"{rule[1]}:")
            ps_or_ns = item.get("value", "").lower() not in valid_values
            if is_current_rule_answer or ps_or_ns:
                continue

            for record in find_related_records(item, reader):
                valid_data.append(
                    {
                        "rule": rule,
                        "elements": record["elements"],
                        "value": item["value"],
                        "texts": record["texts"],
                    }
                )

    return valid_data


def export_para_answer_items(queue, fid, mold_id, item_rules):
    async def _run():
        question = await NewQuestion.find_by_fid_mid(mold_id=mold_id, fid=fid)
        if question is None:
            return
        file = await NewFile.find_by_id(question.fid)
        mold_answer = MoldAnswer(question.answer)
        pdfinsight_reader = PdfinsightReader(file.pdfinsight_path(abs_path=True))

        result = []
        for i in get_para_answer_items(mold_answer, item_rules, pdfinsight_reader, fid):
            elements = i.pop("elements")
            element_pages = ",".join([str(j["page"]) for j in elements])
            i["trace_id"] = f"{file.id}##{'-'.join(i['rule'])}##{element_pages}"
            result.append(i)

        print(f"export fid {fid} done!")
        queue.put(result)

    asyncio.run(_run())
