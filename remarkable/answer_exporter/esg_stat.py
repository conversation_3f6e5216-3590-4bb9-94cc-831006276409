from datetime import datetime

from peewee import <PERSON><PERSON><PERSON>, fn

from remarkable.answer.node import AnswerItem
from remarkable.db import pw_db
from remarkable.models import is_empty
from remarkable.models.answer import Answer
from remarkable.models.hkex_file_meta import HKEXFileMeta
from remarkable.models.mold import special_mold
from remarkable.models.new_file import NewFile
from remarkable.models.new_mold import NewMold
from remarkable.models.new_question import NewQuestion
from remarkable.optools.stat_util import AnswerPrepare
from remarkable.pdfinsight.reader import PdfinsightReader


class EsgStat:
    date_fmt = "%Y%m%d"

    def __init__(self, fid):
        self.fid = fid

    async def stock_code(self):
        hkex_file_meta = await HKEXFileMeta.find_by_fid(self.fid)
        return hkex_file_meta.stock_code if hkex_file_meta else ""

    async def stat_accuracy_info(self):
        data = await self.prepare_data(self.fid)
        if not data:
            return {}
        _, name, qid, answer, preset_answer = data[0]
        stat_result = await self.get_stat_results(qid, name)
        result = self.organization_data(answer, preset_answer, stat_result)
        return result

    async def prepare_data(self, file_id: int, project_id: int = None, date_start=None, date_end=None):
        cte = Answer.select(
            Answer.qid,
            Answer.updated_utc,
            fn.RANK().over(partition_by=[Answer.qid], order_by=[Answer.created_utc.desc()]).alias("rank"),
        ).cte("tmp_ans")
        cond = (
            (NewQuestion.mold.in_(special_mold.esg_mids_with_policy))
            # & (NewQuestion.status >= QuestionStatus.DOING)  # 无论是否答题完毕 只要有question.answer 即可
            & (~is_empty(NewQuestion.answer))
            & (NewFile.deleted_utc == 0)
            & ((cte.c.rank == 1) | (cte.c.rank.is_null()))  # 取最新提交的答案时间戳
        )
        if file_id:
            cond &= NewQuestion.fid == file_id
        if date_start:
            cond &= cte.c.updated_utc >= datetime.strptime(date_start, self.date_fmt).timestamp()
        if date_end:
            cond &= cte.c.updated_utc <= datetime.strptime(date_end, self.date_fmt).timestamp()
        if project_id:
            cond &= NewFile.pid == project_id
        query = (
            NewQuestion.select(
                NewFile.id.alias("fid"), NewFile.name, NewQuestion.id, NewQuestion.answer, NewQuestion.preset_answer
            )
            .join(NewFile, on=(NewFile.id == NewQuestion.fid))
            .join(cte, join_type=JOIN.LEFT_OUTER, on=(cte.c.qid == NewQuestion.id))
            .where(cond)
            .order_by(NewFile.id)
            .with_cte(cte)
            .tuples()
        )
        return list(await pw_db.execute(query))

    def organization_data(self, answer, preset_answer, stat_result):
        schema_orders = {x: i for i, x in enumerate(answer["schema"]["schemas"][0]["orders"])}
        answer_items = [
            AnswerItem(**item)
            for item in answer["userAnswer"]["items"]
            if item.get("manual") is True or item.get("special_ui") is True
        ]
        answer_items = sorted(answer_items, key=lambda x: schema_orders.get(x.schema["data"]["label"], -1))
        answer_items_map = {item.schema["data"]["label"]: item for item in answer_items}

        preset_items_map = {
            item["schema"]["data"]["label"]: AnswerItem(**item) for item in preset_answer["userAnswer"]["items"]
        }
        ret = {}
        for label in schema_orders:
            ai_disclosure_classification, manual_disclosure_classification = None, None
            ai_tag_nt, manual_tag_nt = "NT", "NT"

            preset_answer_item = preset_items_map.get(label)
            if preset_answer_item:
                ai_disclosure_classification = preset_answer_item.value
                at_tag_content = preset_answer_item.simple_text(preset_answer_item, enum=False)
                ai_tag_nt = "T" if at_tag_content else "NT"

            answer_item = answer_items_map.get(label)
            if answer_item and (answer_item.manual is True or answer_item.marker["name"] != "admin"):  # 预测的答案不要
                manual_disclosure_classification = answer_item.value
                manual_tag_content = answer_item.simple_text(answer_item, enum=False)
                manual_tag_nt = "T" if manual_tag_content else "NT"

            ai_location_result = stat_result.get(label, {}).get("result")
            ret[label] = {
                "AI Classification": self.convert_enum(ai_disclosure_classification),
                "Manual Classification": self.convert_enum(manual_disclosure_classification),
                "AI Tagging": ai_tag_nt,
                "Manual Tagging": manual_tag_nt,
                "AI Location Result": ai_location_result,
            }
        return ret

    async def get_stat_results(self, qid, name):
        ret = {}
        mold = await NewMold.get_mold_by_qid(qid)
        orders = mold.data["schemas"][0]["orders"]
        question = await NewQuestion.find_by_id(qid)
        preset_answer = AnswerPrepare.group_by_aid(question.preset_answer.get("userAnswer", {}))
        answer_instance = await question.get_user_merged_answer()
        standard_answer = AnswerPrepare.group_by_aid(answer_instance.get("userAnswer", {}))
        correct_counts = 0
        for key in orders:
            ai_answer = preset_answer.get(key)
            manual_answer = standard_answer.get(key)
            ai_enum_value = ai_answer[0].get("value", "") if ai_answer else ""
            ai_enum_value = ai_enum_value.lower() if ai_enum_value else ""
            manual_enum_value = manual_answer[0].get("value", "") if manual_answer else ""
            manual_enum_value = manual_enum_value.lower() if manual_enum_value else ""
            enum_result = ai_enum_value == manual_enum_value and ai_enum_value != ""
            if key == "KPI A1.2 part 3 - Scope 3":
                enum_result = True
            box_result = False
            ai_data = ai_answer[0].get("data", []) if ai_answer else []
            manual_data = manual_answer[0].get("data", []) if manual_answer else []
            if enum_result and not manual_data:
                box_result = True
            manual_boxes = []
            ai_boxes = []
            for item in manual_data:
                manual_boxes.extend(item["boxes"])
            for item in ai_data:
                ai_boxes.extend(item["boxes"])
            for manual_box in manual_boxes:
                if any(self.box_same(manual_box["box"], ai_box["box"]) for ai_box in ai_boxes):
                    box_result = True
                    break
            if key == "KPI A1.2 part 3 - Scope 3" and not manual_data and ai_data:
                enum_result = False
            result = enum_result and box_result
            if result:
                correct_counts += 1
            ret[key] = {
                "enum": "Y" if enum_result else "N",
                "box": "Y" if box_result else "N",
                "result": "Y" if result else "N",
            }

        accuracy = correct_counts / len(orders) * 100
        ret["all"] = [f"accuracy for file: {question.fid}, name: {name}", f"{accuracy:.2f}%"]
        # print(','.join(ret['all']))
        return ret

    @staticmethod
    def box_same(manual_box, ai_box):
        percent = PdfinsightReader.overlap_percent(manual_box, ai_box, base="min")
        return percent >= 0.9

    @staticmethod
    def convert_enum(value):
        value = value.title() if value else ""
        if value == "Comply":
            return "C"
        if value == "No Disclosure":
            return "ND"
        if value == "Explain":
            return "E"
        return value
