import logging
import tempfile
from collections import Counter, defaultdict
from dataclasses import dataclass
from datetime import datetime
from pathlib import Path

import openpyxl
from peewee import JOIN, fn

from remarkable.answer.node import AnswerItem
from remarkable.common.common import JURA21_A_COLS, JURA21_B_COLS, JUR<PERSON>21_C_COLS
from remarkable.common.util import box_in_box, standard_stock
from remarkable.config import project_root
from remarkable.db import pw_db
from remarkable.models import is_empty
from remarkable.models.answer import Answer
from remarkable.models.hkex_file_meta import HKEXFileMeta
from remarkable.models.mold import special_mold
from remarkable.models.new_file import NewFile
from remarkable.models.new_mold import NewMold
from remarkable.models.new_question import NewQuestion
from remarkable.optools.fm_upload import FMUploader
from remarkable.optools.stat_util import AnswerPrepare, Url
from remarkable.pdfinsight.reader import PdfinsightReader

DATE_FMT = "%Y%m%d"
logger = logging.getLogger(__name__)


@dataclass
class CommonStat:
    stock: str
    fid: str
    mid: int
    rule_refer: dict
    need_label_items: list

    async def stat_accuracy_info(self):
        cond = HKEXFileMeta.stock_code == standard_stock(self.stock)
        cond &= HKEXFileMeta.fid == int(self.fid)
        query = HKEXFileMeta.select().where(cond)
        hkex_file_meta = await pw_db.first(query)

        if not hkex_file_meta or not hkex_file_meta.fid:
            logger.info(f"{self.stock=} {self.fid=} not found")
            return None
        datas = await self.prepare_data(hkex_file_meta.fid)
        if not datas:
            return {}
        ret = {
            "fid": hkex_file_meta.fid,
            "stock": self.stock,
        }
        for mid, data in datas.items():
            stat_result = await self.get_stat_results(data["id"], hkex_file_meta, mid)
            stat_data = self.organization_data(data["answer"], data["preset_answer"], stat_result, mid)
            ret[mid] = stat_data
        return ret

    async def prepare_data(self, file_id: int, project_id: int = None, date_start=None, date_end=None):
        cte = Answer.select(
            Answer.qid,
            Answer.updated_utc,
            fn.RANK().over(partition_by=[Answer.qid], order_by=[Answer.created_utc.desc()]).alias("rank"),
        ).cte("tmp_ans")
        cond = (
            (NewQuestion.mold == self.mid)
            & (~is_empty(NewQuestion.answer))
            & (NewFile.deleted_utc == 0)
            & ((cte.c.rank == 1) | (cte.c.rank.is_null()))  # 取最新提交的答案时间戳
        )
        if file_id:
            cond &= NewQuestion.fid == file_id
        if date_start:
            cond &= cte.c.updated_utc >= datetime.strptime(date_start, DATE_FMT).timestamp()
        if date_end:
            cond &= cte.c.updated_utc <= datetime.strptime(date_end, DATE_FMT).timestamp()
        if project_id:
            cond &= NewFile.pid == project_id
        query = (
            NewQuestion.select(
                NewFile.id.alias("fid"),
                NewFile.name,
                NewQuestion.id,
                NewQuestion.answer,
                NewQuestion.preset_answer,
                NewQuestion.mold,
            )
            .join(NewFile, on=(NewFile.id == NewQuestion.fid))
            .join(cte, join_type=JOIN.LEFT_OUTER, on=(cte.c.qid == NewQuestion.id))
            .where(cond)
            .order_by(NewFile.id)
            .with_cte(cte)
            .dicts()
        )
        result = {item["mold"]: item for item in await pw_db.execute(query)}
        return result

    def organization_predict_answer(self, answer, preset_answer, schema_orders):
        # 整理标注答案的格式
        answer_items = [
            AnswerItem(**item)
            for item in answer["userAnswer"]["items"]
            if item.get("manual") is True or item.get("special_ui") is True
        ]
        answer_items = sorted(answer_items, key=lambda x: schema_orders.get(x.schema["data"]["label"], -1))
        answer_items_map = defaultdict(lambda: defaultdict(list))
        for item in answer_items:
            answer_items_map[item.first_word][item.schema["data"]["label"]].append(item)

        # 整理预测答案的格式
        preset_answer_items = [AnswerItem(**item) for item in preset_answer["userAnswer"]["items"]]
        preset_items_map = defaultdict(lambda: defaultdict(list))
        for item in preset_answer_items:
            preset_items_map[item.first_word][item.schema["data"]["label"]].append(item)

        return answer_items_map, preset_items_map

    def organization_inspect_answer(self, label_answer, preset_answer, schema_orders, mid):
        # 整理标注答案的格式
        answer_items = [
            AnswerItem(**item)
            for item in label_answer["rule_result"]["items"]
            if item.get("manual") is True or item.get("special_ui") is True
        ]
        if mid == special_mold.v1_id:
            # rule A 带括号的情况
            with_bracket_items = [
                AnswerItem(**item) for item in label_answer["userAnswer"]["items"] if "(" in item["key"]
            ]
            answer_items += with_bracket_items
        answer_items = sorted(answer_items, key=lambda x: schema_orders.get(x.schema["data"]["label"], -1))
        answer_inspect_map = {}
        for item in answer_items:
            # rule = item.first_word.replace("(", "").replace(")", "")
            answer_inspect_map[item.first_word] = self.convert_enum(item.value)

        # 整理预测答案的格式
        preset_inspect_map = {}
        if mid == special_mold.v1_id:
            preset_answer_items = [
                AnswerItem(**item)
                for item in preset_answer["userAnswer"]["items"]
                if item["schema"]["data"]["label"].startswith("(")
            ]
            for item in preset_answer_items:
                preset_inspect_map[item.first_word.replace("(", "").replace(")", "")] = self.convert_enum(item.value)
        else:
            preset_answer_items = [AnswerItem(**item) for item in preset_answer["rule_result"]["items"]]
            for item in preset_answer_items:
                preset_inspect_map[item.first_word] = self.convert_enum(item.value)

        return answer_inspect_map, preset_inspect_map

    def organization_data(self, answer, preset_answer, stat_result, mid):
        order_map = {item["name"]: item["orders"] for item in answer["schema"]["schemas"]}
        schema_orders = {x: i for i, x in enumerate(answer["schema"]["schemas"][0]["orders"])}

        answer_items_map, preset_items_map = self.organization_predict_answer(answer, preset_answer, schema_orders)
        answer_inspect_map, preset_inspect_map = self.organization_inspect_answer(
            answer, preset_answer, schema_orders, mid
        )

        ret = defaultdict(dict)
        for rule in schema_orders:
            match mid:
                case 5:
                    if rule not in JURA21_A_COLS:
                        continue
                    if rule in ("A42",):  # noai
                        continue
                case 15:
                    if rule not in JURA21_B_COLS:
                        continue
                case 18:
                    if rule not in JURA21_C_COLS:
                        continue
            all_preset_answer_item = preset_items_map.get(rule, {})
            all_label_answer_item = answer_items_map.get(rule, {})  # 有可能没有标注
            for sub_rule in order_map[rule]:
                ai_disclosure_classification, manual_disclosure_classification = None, None
                ai_tag_nt, manual_tag_nt = "NT", "NT"

                preset_answer_items = all_preset_answer_item.get(sub_rule, [])
                label_answer_items = all_label_answer_item.get(sub_rule, [])  # 有可能没有标注
                label_answer_items = [i for i in label_answer_items if i.manual is True or i.marker["name"] != "admin"]

                # jura2.1的模版中没有用到  AI Location Result 的数据

                # NOTE: 分组时按照时间排序，这里取预测答案的倒数一组
                # remarkable/common/common.py:853 相同 key_path 在这里会只留下最后一个
                if preset_answer_items:
                    preset_answer_item = preset_answer_items[-1]
                    ai_disclosure_classification = preset_answer_item.enum
                    at_tag_content = preset_answer_item.simple_text(preset_answer_item, enum=False)
                    ai_tag_nt = "T" if at_tag_content else "NT"

                if label_answer_items:
                    label_answer_item = label_answer_items[0]
                    manual_disclosure_classification = label_answer_item.enum
                    manual_tag_content = label_answer_item.simple_text(label_answer_item, enum=False)
                    manual_tag_nt = "T" if manual_tag_content else "NT"

                ret[rule][sub_rule] = {
                    "AI Classification": self.convert_enum(ai_disclosure_classification),
                    "Manual Classification": self.convert_enum(manual_disclosure_classification),
                    "AI Tagging": ai_tag_nt,
                    "Manual Tagging": manual_tag_nt,
                    "AI Location Result": stat_result.get(rule, {}).get(sub_rule, {}).get("result"),
                    "AI Compliance": preset_inspect_map.get(rule, ""),
                    "Manual Compliance": answer_inspect_map.get(rule, ""),
                }

            # NOTE: 有可能有多组答案 取出现最多的
            ai_class_result = Counter([i["AI Classification"] for i in ret[rule].values()]).most_common(1)[0][0]
            manual_class_result = Counter([i["Manual Classification"] for i in ret[rule].values()]).most_common(1)[0][0]

            ret[rule]["ALL"] = {
                "AI Classification": ai_class_result,
                "Manual Classification": manual_class_result,
                "AI Tagging": "T" if all(i["AI Tagging"] == "T" for i in ret[rule].values()) else "NT",
                "Manual Tagging": "T" if all(i["Manual Tagging"] == "T" for i in ret[rule].values()) else "NT",
                "AI Location Result": "Y" if all(i["AI Location Result"] == "Y" for i in ret[rule].values()) else "N",
                "AI Compliance": preset_inspect_map.get(rule, ""),
                "Manual Compliance": answer_inspect_map.get(rule, ""),
            }

        return ret

    async def get_stat_results(self, qid, hkex_file_meta, mid):
        ret = defaultdict(dict)
        mold = await NewMold.get_mold_by_qid(qid)
        rules = mold.data["schemas"][0]["orders"]
        order_map = {item["name"]: item["orders"] for item in mold.data["schemas"]}
        question = await NewQuestion.find_by_id(qid)
        file = await NewFile.find_by_id(question.fid)
        pdfinsight = PdfinsightReader(file.pdfinsight_path(abs_path=True))
        preset_answer = AnswerPrepare.group_by_aid(question.preset_answer.get("userAnswer", {}))
        answer_instance = await question.get_user_merged_answer()
        standard_answer = AnswerPrepare.group_by_aid(answer_instance.get("userAnswer", {}))
        correct_counts = 0
        for rule in rules:
            for order in order_map[rule]:
                key = f"{rule}-{order}"
                ai_answer = preset_answer.get(key)
                if not ai_answer:
                    continue
                # TODO 需要判断多组，目前只判断了一组
                ai_answer = AnswerItem(**ai_answer[0])
                manual_answer = standard_answer.get(key)
                if not manual_answer:
                    url = Url(
                        question.fid,
                        special_mold.v1_id,
                        qid,
                        file.tree_id,
                        rule,
                        scheme="https",
                        domain="jura6-lir.paodingai.com",
                        custom_ui=True,
                        need_split=False,
                    )
                    main_alais = self.rule_refer[rule].main_alias
                    logger.error(
                        f"manual_answer not found : {main_alais=}, {key=}, {question.fid=}, {hkex_file_meta.stock_code=}"
                    )
                    logger.error(url)
                    self.need_label_items.append([hkex_file_meta.stock_code, main_alais, rule, order, url])
                    continue

                manual_answer = AnswerItem(**manual_answer[0])
                enum_result = ai_answer.enum.lower() == manual_answer.enum.lower() and ai_answer.enum != ""

                box_result = False
                ai_data = ai_answer.data if ai_answer else []
                manual_data = manual_answer.data if manual_answer else []
                if enum_result and not manual_data:
                    box_result = True
                manual_boxes, ai_boxes = [], []
                for item in manual_data:
                    manual_boxes.extend(item["boxes"])
                for item in ai_data:
                    ai_boxes.extend(item["boxes"])
                for manual_box in manual_boxes:
                    if any(
                        manual_box["page"] == ai_box["page"] and self.box_same(pdfinsight, file.id, manual_box, ai_box)
                        for ai_box in ai_boxes
                    ):
                        box_result = True
                        break
                result = enum_result and box_result
                if result:
                    correct_counts += 1
                ret[rule][order] = {
                    "enum": "Y" if enum_result else "N",
                    "box": "Y" if box_result else "N",
                    "result": "Y" if box_result else "N",
                }

        return ret

    @staticmethod
    def box_same(pdfinsight, fid, manual_box, ai_box):
        percent = pdfinsight.overlap_percent(manual_box["box"], ai_box["box"], base="min")

        _, manual_element = pdfinsight.find_element_by_outline(manual_box["page"], manual_box["box"])
        _, ai_element = pdfinsight.find_element_by_outline(ai_box["page"], ai_box["box"])
        try:
            res = (
                percent >= 0.9
                or box_in_box(manual_box["box"], ai_box["box"])
                or box_in_box(ai_box["box"], manual_box["box"])
            )
        except Exception:
            print(1)
        if ai_element and manual_element:
            return res or manual_element["index"] == ai_element["index"]
        logger.info(f"{fid=} {manual_box['page']=} {ai_box['page']=}")
        return res

    @staticmethod
    def convert_enum(value):
        value = value.title() if value else ""
        match value:
            case "Disclosure":
                return "PS"
            case "Positive Statement":
                return "PS"
            case "Negative Statement":
                return "NS"
            case "No Disclosure":
                return "ND"
            case "Compliance":
                return "C"
            case "Potential Non-Compliance":
                return "NC"
        return value

    @classmethod
    def stuff_data(cls, answers, alias_rule_map, mid):
        # todo 自动计算
        misc_map = {
            33: {
                "template_name": "JURA_6_UAT_Results_AGM_Template.xlsx",
                "result_name": "JURA6_AGM_UAT_Testing_Result",
                "disclosure_idx": 68,
                "compliance_idx": 61,
                "classification_name": "Raw Data - Classification",
                "location_name": "Raw Data - Location",
                "compliance_name": "Raw Data - Compliance",
            },
            34: {
                "template_name": "JURA_6_UAT_Results_Poll_Template.xlsx",
                "result_name": "JURA6_POLL_UAT_Testing_Result",
                "disclosure_idx": 11,
                "compliance_idx": 10,
                "classification_name": "Raw Data - Classification",
                "location_name": "Raw Data - Location",
                "compliance_name": "Raw Data - Compliance",
            },
        }
        if mid not in misc_map:
            logger.info(f"{mid=} not found in misc_map")
            return
        template_path = Path(project_root) / "data/hkex/stat_template" / misc_map[mid]["template_name"]
        workbook = openpyxl.load_workbook(template_path)
        cls.write_rows(
            workbook,
            mid,
            answers,
            alias_rule_map,
            misc_map[mid]["disclosure_idx"],
            misc_map[mid]["classification_name"],
            "classification",
        )
        cls.write_rows(
            workbook,
            mid,
            answers,
            alias_rule_map,
            misc_map[mid]["disclosure_idx"],
            misc_map[mid]["location_name"],
            "tag",
        )
        cls.write_rows(
            workbook,
            mid,
            answers,
            alias_rule_map,
            misc_map[mid]["compliance_idx"],
            misc_map[mid]["compliance_name"],
            "compliance",
        )

        today = datetime.now().strftime("%Y%m%d")
        with tempfile.NamedTemporaryFile(prefix=f"{misc_map[mid]['result_name']}_{today}_", suffix=".xlsx") as tmp_fp:
            excel_path = tmp_fp.name
            workbook.save(excel_path)
            logger.info(f"run completed, {excel_path}")
            logger.info("upload to fm.paodingai.com ...")
            FMUploader().upload(Path(excel_path))

    @classmethod
    def write_rows(cls, workbook, mid, answers, alias_rule_map, classification_row_end, sheet_name, answer_type):
        answer_type_map = {
            "classification": ("AI Classification", "Manual Classification"),
            "compliance": ("AI Compliance", "Manual Compliance"),
            "tag": ("AI Tagging", "Manual Tagging", "AI Location Result"),
        }
        worksheet = workbook[sheet_name]
        for col_idx in range(3, worksheet.max_column + 1000):
            if col_idx - 3 > len(answers) - 1:
                break
            answer = answers[col_idx - 3]  # answer是数组 从0开始
            worksheet.cell(row=2, column=col_idx).value = answer["stock"]
            for row_idx in range(3, classification_row_end):
                alias = worksheet.cell(row=row_idx, column=2).value
                value = cls.find_answer_by_alias(answer, alias_rule_map, alias, answer_type_map[answer_type][0], mid)
                worksheet.cell(row=row_idx, column=col_idx).value = value

            worksheet.cell(row=classification_row_end + 1, column=col_idx).value = answer["stock"]
            for row_idx in range(classification_row_end + 2, classification_row_end * 2 - 1):
                alias = worksheet.cell(row=row_idx, column=2).value
                value = cls.find_answer_by_alias(answer, alias_rule_map, alias, answer_type_map[answer_type][1], mid)
                worksheet.cell(row=row_idx, column=col_idx).value = value

            if answer_type == "tag":
                worksheet.cell(row=classification_row_end * 2, column=col_idx).value = answer["stock"]
                for row_idx in range(classification_row_end * 2 + 1, classification_row_end * 3 - 2):
                    alias = worksheet.cell(row=row_idx, column=2).value
                    value = cls.find_answer_by_alias(
                        answer, alias_rule_map, alias, answer_type_map[answer_type][2], mid
                    )
                    worksheet.cell(row=row_idx, column=col_idx).value = value

    @staticmethod
    def find_answer_by_alias(answer, alias_rule_map, alias, answer_type, mid):
        alias = str(alias)
        rule = alias_rule_map.get(alias)
        sub_rule = ""
        if not rule:
            if alias and "-" in alias:
                alias, sub_rule = alias.rsplit("-", maxsplit=1)
                rule = alias_rule_map.get(alias)
            else:
                return ""
        if sub_rule:
            result = answer.get(mid).get(rule, "")[sub_rule][answer_type]
        else:
            result = answer.get(mid).get(rule, "")["ALL"][answer_type]
        return result
