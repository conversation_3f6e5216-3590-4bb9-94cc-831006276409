import logging
import tempfile
from collections import defaultdict
from datetime import datetime
from pathlib import Path

import openpyxl
from pydantic import BaseModel

from remarkable.answer.node import AnswerItem
from remarkable.common.constants import PolicyEsgRules
from remarkable.common.util import box_in_box
from remarkable.config import project_root
from remarkable.db import pw_db
from remarkable.models import is_empty
from remarkable.models.file_esg_xref import FileESGxREF
from remarkable.models.hkex_file_meta import HKEXFileMeta
from remarkable.models.mold import special_mold
from remarkable.models.new_file import NewFile
from remarkable.models.new_mold import NewMold
from remarkable.models.new_question import NewQuestion
from remarkable.optools.fm_upload import FMUploader
from remarkable.optools.stat_util import AnswerPrepare
from remarkable.pdfinsight.reader import PdfinsightReader

logger = logging.getLogger(__name__)


class AnswerItemLike(BaseModel):
    enum: str
    text: str
    special_ui: bool

    def simple_text(self, item, enum):
        return self.text


class PolicyEsgStat:
    def __init__(self, stock_code, report_year, rule_refer):
        self.stock_code = stock_code
        self.report_year = report_year
        self.rule_refer = rule_refer

    async def stat_accuracy_info(self):
        data = await self.prepare_data()
        if not data:
            return {}
        stat_result = await self.get_stat_results(data["id"])
        result = self.organization_data(data["answer"], data["preset_answer"], stat_result)
        result["stock"] = self.stock_code
        return result

    async def prepare_data(self):
        cond = (
            (NewQuestion.mold.in_((special_mold.policy_ar_id, special_mold.policy_esg_id)))
            & (~is_empty(NewQuestion.answer))
            & (NewFile.deleted_utc == 0)
            & (FileESGxREF.activated)
            & (HKEXFileMeta.stock_code == self.stock_code)
            & (HKEXFileMeta.report_year == self.report_year)
        )
        query = (
            NewQuestion.select(
                NewFile.id.alias("fid"),
                NewFile.name,
                NewQuestion.id,
                NewQuestion.answer,
                NewQuestion.preset_answer,
                NewQuestion.mold,
            )
            .join(NewFile, on=(NewFile.id == NewQuestion.fid))
            .join(HKEXFileMeta, on=(HKEXFileMeta.fid == NewQuestion.fid))
            .join(FileESGxREF, on=(FileESGxREF.fid == NewQuestion.fid))
            .where(cond)
            .order_by(NewFile.id)
            .dicts()
        )
        return await pw_db.first(query)

    @staticmethod
    def filter_answer_items(answer_items):
        result = []
        for item in answer_items:
            answer_ins = AnswerItem(**item)
            if answer_ins.first_word in {PolicyEsgRules.E6, PolicyEsgRules.E8, PolicyEsgRules.E9}:
                if item.get("special_ui") and all(i["special_ui"] for i in item["meta"].get("categories")):
                    result.append(answer_ins)
            else:
                if item.get("special_ui"):
                    # and item.get("marker", {}).get("name", "").startswith("esg"):
                    result.append(answer_ins)
        return result

    def organization_data(self, answer, preset_answer, stat_results):
        schema_orders = {x: i for i, x in enumerate(answer["schema"]["schemas"][0]["orders"])}
        answer_items = self.filter_answer_items(answer["userAnswer"]["items"])
        answer_items = sorted(answer_items, key=lambda x: schema_orders.get(x.schema["data"]["label"], -1))

        answer_items_map = {item.schema["data"]["label"]: item for item in answer_items}
        answer_items_map = self.add_category_answer(answer_items_map, special_ui=True)

        preset_items_map = {
            item["schema"]["data"]["label"]: AnswerItem(**item) for item in preset_answer["userAnswer"]["items"]
        }
        preset_items_map = self.add_category_answer(preset_items_map, special_ui=False)
        ret = {}
        for rule, stat_result in stat_results.items():
            ai_disclosure_classification, manual_disclosure_classification = None, None
            ai_tag_nt, manual_tag_nt = "NT", "NT"

            preset_answer_item = preset_items_map.get(rule)
            if preset_answer_item:
                ai_disclosure_classification = preset_answer_item.enum
                at_tag_content = preset_answer_item.simple_text(preset_answer_item, enum=False)
                ai_tag_nt = "T" if at_tag_content else "NT"

            answer_item = answer_items_map.get(rule)
            if answer_item:
                manual_disclosure_classification = answer_item.enum
                manual_tag_content = answer_item.simple_text(answer_item, enum=False)
                manual_tag_nt = "T" if manual_tag_content else "NT"

            ai_location_result = stat_result.get("result")
            ret[rule] = {
                "AI Classification": self.convert_enum(ai_disclosure_classification, rule, ai=True),
                "Manual Classification": self.convert_enum(manual_disclosure_classification, rule),
                "AI Tagging": ai_tag_nt,
                "Manual Tagging": manual_tag_nt,
                "AI Location Result": ai_location_result,
            }
        return ret

    @staticmethod
    def add_category_answer(answer_items_map, special_ui=False):
        result = {}
        for rule, data in answer_items_map.items():
            if rule in {PolicyEsgRules.E6, PolicyEsgRules.E8, PolicyEsgRules.E9}:
                if not data.meta:
                    continue
                categories = data.meta.get("categories", [])
                categories = {i["category"]: i for i in categories}
                for sub_rule, category in categories.items():
                    simple_text = "".join(box["text"] for i in category["data"] for box in i["boxes"])

                    answer_item = AnswerItemLike(enum=category["enum"], text=simple_text, special_ui=special_ui)
                    result[f"{rule}-{sub_rule}"] = answer_item
            else:
                result[rule] = data

        return result

    def merge_box(self, *boxes):
        lefts, tops, rights, bottoms = [], [], [], []
        for box in boxes:
            box = box[0]
            lefts.append(box["box_left"])
            tops.append(box["box_top"])
            rights.append(box["box_right"])
            bottoms.append(box["box_bottom"])
        return [min(lefts), min(tops), max(rights), max(bottoms)]

    async def get_stat_results(self, qid):
        ret = defaultdict(dict)
        mold = await NewMold.get_mold_by_qid(qid)
        rules = mold.data["schemas"][0]["orders"]
        question = await NewQuestion.find_by_id(qid)
        file = await NewFile.find_by_id(question.fid)
        pdfinsight = PdfinsightReader.from_path(file.pdfinsight_path(abs_path=True))
        preset_answer = AnswerPrepare.group_by_aid(question.preset_answer.get("userAnswer", {}))
        answer_instance = question.answer  # 只需要review 界面标注的数据
        standard_answer = AnswerPrepare.group_by_aid(answer_instance.get("userAnswer", {}))
        for rule in rules:
            if rule in {PolicyEsgRules.E6, PolicyEsgRules.E8, PolicyEsgRules.E9}:
                continue
            ai_answer = preset_answer.get(rule)
            if not ai_answer:
                continue
            ai_answer = AnswerItem(**ai_answer[0])
            manual_answer = standard_answer.get(rule)
            if not manual_answer or not manual_answer[0].get("special_ui"):
                logger.info(f"<{rule}> manual answer is empty, {file.id=}")
                ret[rule] = {
                    "enum": "",
                    "box": "",
                    "result": "",
                }
                continue

            manual_answer = AnswerItem(**manual_answer[0])

            # compute box_result
            enum_result = ai_answer.enum.lower() == manual_answer.enum.lower() and ai_answer.enum != ""

            # compute box_result
            box_result = False
            ai_data = ai_answer.data if ai_answer else []
            manual_data = manual_answer.data if manual_answer else []
            if enum_result and not manual_data:
                box_result = True
            manual_boxes, ai_boxes = [], []
            for item in manual_data:
                manual_boxes.extend(item["boxes"])
            for item in ai_data:
                ai_boxes.extend(item["boxes"])
            for manual_box in manual_boxes:
                if any(
                    manual_box["page"] == ai_box["page"] and self.box_same(pdfinsight, file.id, manual_box, ai_box)
                    for ai_box in ai_boxes
                ):
                    box_result = True
                    break
            # compute result
            result = enum_result and box_result
            ret[rule] = {
                "enum": "Y" if enum_result else "N",
                "box": "Y" if box_result else "N",
                "result": "Y" if result else "N",
            }

        for rule in rules:
            if rule not in {PolicyEsgRules.E6, PolicyEsgRules.E8, PolicyEsgRules.E9}:
                continue
            ai_answer = preset_answer.get(rule)
            if not ai_answer:
                continue
            ai_answer = AnswerItem(**ai_answer[0])
            ai_categories = ai_answer.meta.get("categories", [])
            ai_categories = {i["category"]: i for i in ai_categories}

            manual_answer = standard_answer.get(rule)
            if (
                not manual_answer
                or not AnswerItem(**manual_answer[0]).meta
                or not all(i["special_ui"] for i in manual_answer[0]["meta"].get("categories"))
            ):
                logger.info(f"<{rule}> manual answer is empty, {file.id=}")
                if not ai_categories:
                    if rule == PolicyEsgRules.E6.value:
                        ai_categories = ["IPCC", "IEA", "NGFS"]
                for sub_rule in ai_categories:
                    ret[f"{rule}-{sub_rule}"] = {
                        "enum": "",
                        "box": "",
                        "result": "",
                    }
                continue
            manual_answer = AnswerItem(**manual_answer[0])
            manual_categories = manual_answer.meta.get("categories", [])
            manual_categories = {i["category"]: i for i in manual_categories}
            for sub_rule, manual_category in manual_categories.items():
                ai_category = ai_categories.get(sub_rule, {})
                enum_result = ai_category.get("enum") == manual_category.get("enum")
                box_result = False
                ai_boxes = ai_category.get("data", [])
                manual_boxes = manual_category.get("data", [])
                if enum_result and not manual_boxes:
                    box_result = True
                else:
                    manual_boxes = [i["boxes"] for i in manual_boxes]
                    ai_boxes = [i["boxes"] for i in ai_boxes]
                    for manual_box in manual_boxes:
                        _manual_box = self.merge_box([i["box"] for i in manual_box])
                        new_manual_box = {
                            "page": manual_box[0]["page"],
                            "box": _manual_box,
                        }
                        try:
                            if any(
                                manual_box[0]["page"] == ai_box[0]["page"]
                                and self.box_same(
                                    pdfinsight,
                                    file.id,
                                    new_manual_box,
                                    {"page": ai_box[0]["page"], "box": self.merge_box([i["box"] for i in ai_box])},
                                )
                                for ai_box in ai_boxes
                            ):
                                box_result = True
                                break
                        except Exception as e:
                            print(file.id)
                            raise e
                result = enum_result and box_result
                ret[f"{rule}-{sub_rule}"] = {
                    "enum": "Y" if enum_result else "N",
                    "box": "Y" if box_result else "N",
                    "result": "Y" if result else "N",
                }
        return ret

    @staticmethod
    def box_same(pdfinsight, fid, manual_box, ai_box):
        percent = pdfinsight.overlap_percent(manual_box["box"], ai_box["box"], base="min")

        _, manual_element = pdfinsight.find_element_by_outline(manual_box["page"], manual_box["box"])
        _, ai_element = pdfinsight.find_element_by_outline(ai_box["page"], ai_box["box"])
        res = False
        try:
            res = (
                percent >= 0.9
                or box_in_box(manual_box["box"], ai_box["box"])
                or box_in_box(ai_box["box"], manual_box["box"])
            )
        except Exception as e:
            logger.exception(f"{fid=} {e=}")
            raise
        if ai_element and manual_element:
            return res or manual_element["index"] == ai_element["index"]
        logger.info(f"{fid=} {manual_box['page']=} {ai_box['page']=}")
        return res

    @staticmethod
    def convert_enum(value, rule, ai=False):
        default_value = ""
        if ai and any(i in rule for i in ("E6", "E8", "E9")):
            default_value = "N"
        value = value.title() if value else default_value
        if value == "Comply":
            return "C"
        if value == "No Disclosure":
            return "ND"
        if value == "Explain":
            return "E"
        if value == "Yes":
            return "Y"
        if value == "No":
            return "N"
        if value == "Query":
            return "Q"
        return value

    @classmethod
    def stuff_data(cls, answers, alias_rule_map):
        mid = 31
        misc_map = {
            31: {
                "template_name": "JURA6_AGM_UAT_Round_1_Precision_Recall_Calculation_Template.xlsx",
                "result_name": "JURA6_AGM_UAT_Testing_Result",
                "disclosure_idx": 42,
                "compliance_idx": 61,
            },
        }

        template_path = (
            Path(project_root)
            / "data/hkex/stat_template"
            / "JURA6_POLICYESG_UAT_Round_1_Precision_Recall_Calculation_Template.xlsx"
        )
        workbook = openpyxl.load_workbook(template_path)
        cls.write_rows(
            workbook,
            answers,
            alias_rule_map,
            misc_map[mid]["disclosure_idx"],
            "RAW-Classification",
            "classification",
        )
        cls.write_rows(
            workbook,
            answers,
            alias_rule_map,
            misc_map[mid]["disclosure_idx"],
            "RAW-Location",
            "tag",
        )

        today = datetime.now().strftime("%Y%m%d")
        with tempfile.NamedTemporaryFile(
            prefix=f"JURA6_POLICY_ESG_UAT_Round_1_Precision_Recall_{today}_", suffix=".xlsx"
        ) as tmp_fp:
            excel_path = tmp_fp.name
            workbook.save(excel_path)
            logger.info(f"run completed, {excel_path}")
            logger.info("upload to fm.paodingai.com ...")
            FMUploader().upload(Path(excel_path))

    @classmethod
    def write_rows(cls, workbook, answers, alias_rule_map, classification_row_end, sheet_name, answer_type):
        answer_type_map = {
            "classification": ("AI Classification", "Manual Classification"),
            "compliance": ("AI Compliance", "Manual Compliance"),
            "tag": ("AI Tagging", "Manual Tagging", "AI Location Result"),
        }
        worksheet = workbook[sheet_name]
        for col_idx in range(3, worksheet.max_column + 1000):  # 文件数量不确定
            if col_idx - 3 > len(answers) - 1:
                break
            answer = answers[col_idx - 3]  # answer是数组 从0开始
            if not answer.get("stock"):
                continue
            worksheet.cell(row=2, column=col_idx).value = answer["stock"]
            for row_idx in range(3, classification_row_end):
                alias = worksheet.cell(row=row_idx, column=2).value
                value = cls.find_answer_by_alias(
                    answer, alias_rule_map, alias, answer_type_map[answer_type][0], answer["stock"]
                )
                worksheet.cell(row=row_idx, column=col_idx).value = value

            worksheet.cell(row=classification_row_end + 1, column=col_idx).value = answer["stock"]
            for row_idx in range(classification_row_end + 2, classification_row_end * 2 - 1):
                alias = worksheet.cell(row=row_idx, column=2).value
                value = cls.find_answer_by_alias(
                    answer, alias_rule_map, alias, answer_type_map[answer_type][1], answer["stock"]
                )
                worksheet.cell(row=row_idx, column=col_idx).value = value

            if answer_type == "tag":
                worksheet.cell(row=classification_row_end * 2, column=col_idx).value = answer["stock"]
                for row_idx in range(classification_row_end * 2 + 1, classification_row_end * 3 - 2):
                    alias = worksheet.cell(row=row_idx, column=2).value
                    value = cls.find_answer_by_alias(
                        answer, alias_rule_map, alias, answer_type_map[answer_type][2], answer["stock"]
                    )
                    worksheet.cell(row=row_idx, column=col_idx).value = value

    @staticmethod
    def find_answer_by_alias(answer, alias_rule_map, alias, answer_type, stock):
        try:
            reversed_alias_rule_map = {v.main_alias: k for k, v in alias_rule_map.items()}
            rule = reversed_alias_rule_map.get(alias)
            if not rule:
                rule = "E" + alias[1:]
            result = answer.get(rule, "")[answer_type]
            return result
        except Exception:
            logger.exception(f"KeyError: {alias} not found in reversed_alias_rule_map, {stock=}")
            return ""
