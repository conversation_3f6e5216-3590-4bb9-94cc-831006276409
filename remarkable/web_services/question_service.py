import http
import logging

from dateutil.relativedelta import relativedelta
from fastapi import HTTPException
from peewee import Case, IntegrityError, fn

from remarkable.common.common import (
    B1_B10_COLS,
    FUNDRAISING_COLS,
    get_financial_year_dates,
    get_keys,
)
from remarkable.common.constants import BadRequestMessage, DocType, HistoryAction
from remarkable.common.pagination import AsyncPagination
from remarkable.db import pw_db
from remarkable.dependencies import CommonDep
from remarkable.models.addition_data import AdditionData
from remarkable.models.hkex_file_meta import HKEXFileMeta
from remarkable.models.manual_group_answer import ManualGroupAnswer
from remarkable.models.new_history import HistoryMeta, NewHistory
from remarkable.models.new_question import NewQuestion
from remarkable.models.rule_reference import RuleReference
from remarkable.models.user import AdminUser
from remarkable.plugins.hkex.handlers_util import RuleAnswerHandlerUtil
from remarkable.plugins.hkex.utils import MoldAnswer
from remarkable.predictor.models.fund_raising_group import R_IGNORE_B1_REASON
from remarkable.schemas.answer import AdditionDataQuerySchema, ManualGroupBody

logger = logging.getLogger(__name__)


def _calc_group_num(question: NewQuestion, group_name: str, ref_rules: list[str]):
    """
    根据前端提供的event type（这里就是参数group_name）给自定义分组指定名称
    规则：event type + 补位序号，例如：Issue of Shares 4
    https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6069
    """
    group_nums = ManualGroupAnswer.get_group_nums(question.id, group_name, ref_rules)
    mold_answer = MoldAnswer(question.preset_answer)
    for rule in ref_rules:
        for item in mold_answer.get_rule_answer(rule, multi=True)["answer"]:
            if (rule_group_name := get_keys(item, ["meta", "group_name"])) and rule_group_name.lower().startswith(
                group_name.lower()
            ):
                group_nums.append(int(rule_group_name.split()[-1]))
    if not group_nums:
        return 1
    group_nums = sorted(group_nums)
    # 这里做序号补位
    for i, num in enumerate(group_nums, start=1):
        if num != i:
            return i
    return max(group_nums) + 1


def get_ref_rules(rule: str):
    """
    创建人工分组时，B1-B10统一创建，C1.1.1/C1.2.1/C1.3统一创建
    https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6069#note_652133
    """
    if rule in B1_B10_COLS:
        return B1_B10_COLS
    return [r for r in FUNDRAISING_COLS if r not in B1_B10_COLS]


async def get_main_alias(rule):
    rule_reference = await RuleReference.find_by_rule(rule)
    if not rule_reference or not rule_reference.main_alias:
        raise HTTPException(
            status_code=http.HTTPStatus.BAD_REQUEST,
            detail=f"Main alias of the rule '{rule}' was not found!",
        )
    return rule_reference.main_alias


async def create_manual_group(
    question: NewQuestion,
    body: ManualGroupBody,
    user: AdminUser,
    rule: str,
):
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/5531#note_609407
    if RuleAnswerHandlerUtil.is_predicting(question):
        raise HTTPException(status_code=http.HTTPStatus.BAD_REQUEST, detail=BadRequestMessage.DOC_IS_PARSING)
    # 1. 给group_name添加唯一序号
    ref_rules = get_ref_rules(rule)
    group_num = _calc_group_num(question, body.group_name, ref_rules)
    group_name_with_num = f"{body.group_name} {group_num}"
    logger.info(f"group_name={group_name_with_num}")

    # 2. 写入数据
    data = [
        {
            "qid": question.id,
            "user_id": user.id,
            "group_name": body.group_name,
            "group_num": group_num,
            "rule": r,
            "from_copy": r != rule,
        }
        for r in ref_rules
    ]
    try:
        await pw_db.execute(ManualGroupAnswer.insert_many(data))
    except IntegrityError as err:
        logger.error(f"Failed to create manual group answer. \n{body=}\n{data=}")
        raise HTTPException(status_code=http.HTTPStatus.BAD_REQUEST, detail=BadRequestMessage.CAN_NOT_SUBMIT) from err

    # 3. 记录操作日志
    meta = {
        "rules": ref_rules,
        "rule_subtab": "",
        "main_alias": await get_main_alias(rule),
        "data": {"group_name": group_name_with_num},
        "adjustment": f'Added Event Type "<{group_name_with_num}>"',
    }
    if file_meta := await HKEXFileMeta.find_by_fid(question.fid):
        meta.update({"stock_code": file_meta.stock_code, "year": file_meta.report_year})
    await NewHistory.save_operation_history(
        qid=question.id,
        uid=user.id,
        action=HistoryAction.CREATE_FUNDRAISING_EVENT_TYPE,
        user_name=user.name,
        meta=HistoryMeta(**meta),
    )
    return {
        "data": len(data),
        "message": f'The Fundraising Event Type "{group_name_with_num}" has been created successfully.',
    }


async def delete_manual_group(
    question: NewQuestion,
    manual_group_id: int,
    user: AdminUser,
    rule: str,
):
    if not (manual_group := await ManualGroupAnswer.find_by_id(manual_group_id)):
        raise HTTPException(
            status_code=http.HTTPStatus.CONFLICT,
            detail="The Fundraising Event Type has been deleted and the page will be reloaded.",
        )
    group_name, group_num = manual_group.group_name, manual_group.group_num
    group_name_with_num = manual_group.group_name_with_num
    ref_rules = get_ref_rules(rule)
    # 删除当前question下所有group_name与manual_group.group_name相同的数据
    if deleted_rows := await ManualGroupAnswer.get_by_qid_and_group_name(
        question.id, group_name, ref_rules, group_num, lock=True, to_dict=True
    ):
        await pw_db.execute(
            ManualGroupAnswer.delete().where(
                ManualGroupAnswer.qid == question.id,
                ManualGroupAnswer.group_name == group_name,
                ManualGroupAnswer.rule.in_(ref_rules),
                ManualGroupAnswer.group_num == group_num,
            )
        )
        # 记录操作日志
        meta = {
            "rules": ref_rules,
            "rule_subtab": "",
            "main_alias": await get_main_alias(rule),
            "data": deleted_rows,
            "adjustment": f'Removed Event Type "<{group_name_with_num}>"',
        }
        if file_meta := await HKEXFileMeta.find_by_fid(question.fid):
            meta.update({"stock_code": file_meta.stock_code, "year": file_meta.report_year})

        await NewHistory.save_operation_history(
            qid=question.id,
            uid=user.id,
            action=HistoryAction.DELETE_FUNDRAISING_EVENT_TYPE,
            user_name=user.name,
            meta=HistoryMeta(**meta),
        )
    else:
        raise HTTPException(
            status_code=http.HTTPStatus.NOT_FOUND, detail=f'Fundraising Event Type "{group_name_with_num}" not found.'
        )
    return {
        "data": len(deleted_rows),
        "message": f'The Fundraising Event Type "{group_name_with_num}" has been deleted successfully.',
    }


async def get_addition_data_(
    question: NewQuestion, rule: str, query_params: AdditionDataQuerySchema, commons: CommonDep, current_user: AdminUser
):
    """
    https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/5455
    """

    def _add_months_to_date(date_, month_offset):
        return date_ + relativedelta(months=month_offset)

    async def _count_in_the_year_rows(stock_code_, event_ids_):
        return await pw_db.count(
            AdditionData.select(1)
            .where(
                AdditionData.stock_code == stock_code_,
                AdditionData.event_id.in_(event_ids_),
                fn.Lower(AdditionData.b1).not_in(AdditionData.IGNORE_REASONS),
                fn.Lower(AdditionData.b2).not_in(AdditionData.IGNORE_EVENT_TYPES),
                # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6240#note_661521
                ~AdditionData.b1.iregexp(R_IGNORE_B1_REASON),
            )
            .group_by(AdditionData.event_id)
        )

    async def _save_view_record():
        meta = {
            "rule": rule,
            "main_alias": main_alias,
            "stock_code": stock_code,
            "year": financial_year,
            "doc_type": "Annual Report",
        }
        await NewHistory.save_operation_history_by_user(
            qid=question.id,
            current_user=current_user,
            action=HistoryAction.VIEW_FUNDRAISING_ACTIVITY,
            meta=HistoryMeta(**meta),
        )

    stock_code, financial_year, month_offset = (
        query_params.stock_code,
        query_params.financial_year,
        query_params.month_offset,
    )

    main_alias = await get_main_alias(rule)
    if not (file_meta := await HKEXFileMeta.get_first_by_stock_code(stock_code, DocType.AR, financial_year)):
        logger.error(
            f"No records in hkex_file_meta: qid={question.id}, fid={question.fid}, stock_code={file_meta.stock_code if file_meta else ''}"
        )
        # 记录查看日志
        await _save_view_record()
        raise HTTPException(status_code=http.HTTPStatus.BAD_REQUEST, detail=BadRequestMessage.AR_IS_CRAWLING)
    # 确定财年范围
    if not file_meta.year_end:
        logger.error(
            f"No year_end: qid={question.id}, fid={question.fid}, stock_code={file_meta.stock_code if file_meta else ''}"
        )
        # 记录查看日志
        await _save_view_record()
        raise HTTPException(status_code=http.HTTPStatus.BAD_REQUEST, detail=BadRequestMessage.YEAR_END_NOT_AVAILABLE)
    year_start, year_end = get_financial_year_dates(file_meta.fid, file_meta.year_end, "%d %b %Y")
    pre_year_start, after_year_end = year_start, year_end
    if month_offset < 0:
        pre_year_start = _add_months_to_date(year_start, month_offset)
    elif month_offset > 0:
        after_year_end = _add_months_to_date(year_end, month_offset)

    # 1. 查询每个event_id的最大report_date
    sub_query = AdditionData.select(
        AdditionData.event_id, fn.max(fn.TO_DATE(AdditionData.last_report_date, "YYYY-MM-DD")).alias("max_date")
    ).group_by(AdditionData.event_id)
    # 2. 过滤查询范围内及财年范围内的event_ids
    event_ids, fy_event_ids = [], []
    for row in await pw_db.execute(sub_query):
        if pre_year_start <= row.max_date <= after_year_end:
            event_ids.append(row.event_id)
        if year_start <= row.max_date <= year_end:
            fy_event_ids.append(row.event_id)
    # 3. 查询需要显示的数据
    if not event_ids:
        res = {"page": 1, "size": 20, "total": 0, "count": 0, "items": []}
    else:
        group_by_fields = [
            AdditionData.event_id,
            AdditionData.report_dates,
            AdditionData.first_report_time,
            AdditionData.last_report_date,
            AdditionData.b1,
            AdditionData.b2,
            AdditionData.b3,
            AdditionData.b4,
            AdditionData.b6_first,
            AdditionData.b6_second,
            AdditionData.b7,
            AdditionData.b8,
            AdditionData.fund_raised,
            AdditionData.lapsed,
            AdditionData.report_link,
            AdditionData.shares_class,
            AdditionData.issue_method,
            AdditionData.exercise_price,
        ]
        rank = fn.ROW_NUMBER().over(partition_by=group_by_fields, order_by=[AdditionData.id.desc()]).alias("rank")
        case_in_the_year = Case(
            None,
            (
                (fn.TO_DATE(AdditionData.last_report_date, "YYYY-MM-DD") < year_start, False),
                (fn.TO_DATE(AdditionData.last_report_date, "YYYY-MM-DD") > year_end, False),
                (fn.Lower(AdditionData.b1).in_(AdditionData.IGNORE_REASONS), False),
                (fn.Lower(AdditionData.b2).in_(AdditionData.IGNORE_EVENT_TYPES), False),
                # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6240#note_661521
                (AdditionData.b1.iregexp(R_IGNORE_B1_REASON), False),
            ),
            default=True,
        ).alias("is_in_the_year")
        cte = (
            AdditionData.select(rank, *group_by_fields, case_in_the_year).where(
                AdditionData.stock_code == stock_code, AdditionData.event_id.in_(event_ids)
            )
        ).cte("tmp_cte")
        query = (
            cte.select_from(
                *[
                    getattr(cte.c, f.name).alias(AdditionData.FIELD_MAPPING.get(f.name, f.name))
                    for f in group_by_fields
                ],
                cte.c.is_in_the_year,
            )
            .where(cte.c.rank == 1)
            .order_by(cte.c.is_in_the_year.desc(), cte.c.event_id.asc())
            .with_cte(cte)
        )
        res = await AsyncPagination(query, commons.page, commons.page_size).data(
            fields=[AdditionData.FIELD_MAPPING.get(a.name, a.name) for a in group_by_fields] + ["is_in_the_year"],
        )
        for item in res["items"]:
            # 字符串转列表
            for key in {"Involved Entities English", "Report Link"}:
                item[key] = AdditionData.to_list(item[key])
        res["count"] = await _count_in_the_year_rows(stock_code, fy_event_ids) if fy_event_ids else 0
    res["latest_update"] = await AdditionData.latest_update()
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6468#note_677188
    res["year_end"] = year_end.strftime("%Y-%m-%d")
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6468#note_677927
    res["ar_years"] = await pw_db.scalars(
        HKEXFileMeta.select(HKEXFileMeta.report_year)
        .where(HKEXFileMeta.stock_code == stock_code, HKEXFileMeta.doc_type == DocType.AR)
        .order_by(HKEXFileMeta.report_year.desc())
    )
    # 记录查看日志
    await _save_view_record()
    return res
