import base64
import logging
import re
from contextlib import asynccontextmanager
from typing import Any

import httpx
import tornado.httpserver
import tornado.ioloop
import tornado.options
import tornado.web
from fastapi import Fast<PERSON><PERSON>
from fastapi.exceptions import HTTPException
from fastapi.openapi.utils import get_openapi
from speedy.middleware.encrypt import EncryptMiddleware
from speedy.middleware.i18n import translate
from speedy.web_api import PaoDing<PERSON><PERSON>
from starlette.requests import Request
from starlette.responses import Response
from starlette.status import HTTP_404_NOT_FOUND

from remarkable.base_handler import Custom404<PERSON>andler, route
from remarkable.common.constants import API_PREFIX_V1, API_PREFIX_V2
from remarkable.common.util import compact_dumps, need_secure_cookie
from remarkable.config import get_config
from remarkable.db import async_rdb, pw_db
from remarkable.middleware.header import header_middleware
from remarkable.middleware.pai_wsgi import TornadoMiddleware
from remarkable.middleware.session import SessionMiddleware
from remarkable.middleware.web_logging import logging_middleware
from remarkable.models.new_history import <PERSON><PERSON><PERSON><PERSON>ory, Event
from remarkable.routers import (
    agm,
    audit_trail,
    cg,
    debug,
    esg,
    ext,
    ext_api_v1,
    external,
    files,
    groups,
    nddr,
    poll,
    questions,
    report_review,
    rule_search_history,
    rules,
    saml,
)
from remarkable.security.package_encrypt import AES256GCMEncryptor

logger = logging.getLogger(__name__)


class HandleDelegate(tornado.web._HandlerDelegate):
    def execute(self):
        if not self.application.settings.get("compiled_template_cache", True):
            with tornado.web.RequestHandler._template_loader_lock:
                for loader in tornado.web.RequestHandler._template_loaders.values():
                    loader.reset()
        if not self.application.settings.get("static_hash_cache", True):
            tornado.web.StaticFileHandler.reset()

        self.handler = self.handler_class(self.application, self.request, **self.handler_kwargs)
        transforms = [t(self.request) for t in self.application.transforms]

        return self.handler._execute(transforms, *self.path_args, **self.path_kwargs)


class Application(tornado.web.Application):
    def __init__(self):
        logger.info("starting remarkable")
        handlers = route.init_handlers()
        settings = {
            "autoreload": get_config("client.autoreload", False),  # 与gunicorn启动模式冲突，默认不应该启用
            "default_handler_class": Custom404Handler,
            "compiled_template_cache": False,
            "template_path": "templates",
            "serve_traceback": get_config("debug", True),
            "xsrf_cookie_kwargs": {
                "httponly": True,
                "secure": need_secure_cookie,
                "samesite": "none" if need_secure_cookie else "lax",
            },
            "cookie_secret": get_config("app.cookie_secret"),
        }
        tornado.web.Application.__init__(self, handlers, debug=get_config("debug", True), **settings)

    def log_request(self, handler):
        pass


class TornadoApplication(Application):
    def get_handler_delegate(
        self,
        request,
        target_class,
        target_kwargs=None,
        path_args=None,
        path_kwargs=None,
    ):
        return HandleDelegate(self, request, target_class, target_kwargs, path_args, path_kwargs)


@asynccontextmanager
async def lifespan(app: FastAPI):
    async with async_rdb() as rdb:
        # app.state is instance of `fastapi.datastructures.State` which it base is from `starlette.datastructures.State`
        app.state.aio_redis = rdb  # noqa
        event_id = "siemcop:106"
        event = Event()
        event.update_common("IT Security Measures have been started", event_id)
        event.set_field("act", "Started")
        event.set_field("outcome", "success")
        try:
            await pw_db.create(CEFHistory, event_id=event_id, event=event.build_cef())
        except Exception as exp:
            logger.error(f"create cef history failed: {exp}")
        yield
        event = Event()
        event.update_common("IT Security Measures have been stopped", event_id)
        event.set_field("act", "Stopped")
        event.set_field("outcome", "success")
        await pw_db.create(CEFHistory, event_id=event_id, event=event.build_cef())


def create_app():
    app = PaoDingAPI(get_config("speedy"), docs_url=f"{API_PREFIX_V2}/docs", lifespan=lifespan)

    def custom_openapi() -> dict[str, Any]:
        """仅包含部分接口的 OpenAPI docs，用于生成外部 API 文档
        1. 开发接口 xxx
        2. 生成 OpenAPI 文档（仅包含xxx接口）
        3. 将`/api/v2/docs/openapi.json`导入 https://editor-next.swagger.io
        4. 生成&校对外部 API 文档
        """
        target_paths = ("/external_api/agm_result", "/external_api/poll_result")
        if not app.openapi_schema:
            app.openapi_schema = get_openapi(
                title=app.title,
                version=app.version,
                openapi_version=app.openapi_version,
                summary=app.summary,
                description=app.description,
                terms_of_service=app.terms_of_service,
                contact=app.contact,
                license_info=app.license_info,
                routes=[r for r in app.routes if r.path in target_paths],
                webhooks=app.webhooks.routes,
                tags=app.openapi_tags,
                servers=app.servers,
                separate_input_output_schemas=app.separate_input_output_schemas,
            )
        return app.openapi_schema

    @app.exception_handler(HTTPException)
    async def http_exception_handler(request: Request, exc: HTTPException):
        media_type = "application/json"
        content = compact_dumps(
            {"status": "error", "message": translate(exc.detail), "errors": {}},
            allow_nan=False,
        ).encode("utf-8")
        if (ins := request.app.middleware_stack.app) and isinstance(ins, EncryptMiddleware):
            media_type = "application/binary-json"
            content = ins.encryptor.encrypt(content)
        return Response(content, status_code=exc.status_code, media_type=media_type)

    app.middleware("http")(logging_middleware)
    app.middleware("http")(header_middleware)
    app.add_middleware(SessionMiddleware)
    for router in (
        groups.router,
        saml.router,
        rules.router,
        rule_search_history.router,
        report_review.router,
        questions.router,
        external.router,
        files.router,
        cg.router,
        debug.router,
        audit_trail.router,
        agm.router,
        poll.router,
        esg.router,
        nddr.router,
    ):
        app.include_router(router, prefix=API_PREFIX_V2)
    app.include_router(ext_api_v1.router, prefix=API_PREFIX_V1)
    app.include_router(ext.router)
    tornado_ins = TornadoMiddleware(TornadoApplication())
    app.mount(API_PREFIX_V1, tornado_ins)
    app.mount("/external_api", tornado_ins)

    if binary_key := get_config("web.binary_key"):
        app.enable_last_encrypt_middleware(
            encryptor=AES256GCMEncryptor(binary_key),
            x_binary_key=base64.b64encode(
                AES256GCMEncryptor(get_config("web.share_key", "hkx85vMOBSM7M7W")).encrypt(binary_key)
            ).decode(),
            encrypt_list=get_config("web.encrypted_response_routes", []),
            decrypt_list=get_config("web.encrypted_request_routes", []),
            skip_list=[
                "/external/.*",
                *get_config("web.encrypted_skip_routes", []),
                *[re.compile(rf"{i}") for i in get_config("web.encrypted_skip_routes_patterns", [])],
            ],
            enc_error_rsp=True,
        )

    @app.get("/{path:path}", include_in_schema=False)
    async def front_proxy(path: str, response: Response):
        """
        转发目标服务器前端资源到本地开发环境，方便本地开发调试
        需要确保最后载入本接口
        """
        if path.startswith("api/v"):
            response.status_code = HTTP_404_NOT_FOUND
            return response
        backend = get_config("web.debug_frontend_upstream")
        if not backend:
            backend = f"http://{get_config('web.domain')}"
        backend_url = f"{backend.rstrip('/')}/{path.lstrip('/')}"

        async with httpx.AsyncClient(verify=False) as client:
            proxy = await client.get(backend_url, headers={"User-Agent": "Chrome"})
        response.body = proxy.content
        response.status_code = proxy.status_code
        response.headers["content-type"] = proxy.headers["content-type"]
        if path:
            response.headers["cache-control"] = "max-age=36000"
        return response

    # 按需放开以生成订制 OpenAPI 文档
    # app.openapi = custom_openapi

    # logger.info("FastAPI Routes:")
    # for route in app.routes:
    #     if hasattr(route, 'methods') and hasattr(route, 'path'):
    #         methods = ', '.join(route.methods) if route.methods else 'ALL'
    #         logger.info(f"  {methods:<8} {route.path}")
    #     elif hasattr(route, 'path'):
    #         logger.info(f"  {'MOUNT':<8} {route.path}")

    return app
