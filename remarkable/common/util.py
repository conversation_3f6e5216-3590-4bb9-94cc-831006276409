import collections
import functools
import io
import json
import logging
import os
import pickle
import random
import re
import shutil
import ssl
import subprocess
import tempfile
import traceback
import urllib
import zipfile
from collections import OrderedDict
from contextlib import asynccontextmanager, suppress
from copy import copy, deepcopy
from datetime import datetime, timed<PERSON>ta
from importlib import import_module
from itertools import chain, groupby, tee
from pathlib import Path
from typing import Any, AsyncGenerator, Dict, List, Literal, Tuple, Type, TypeVar, Union
from urllib.parse import parse_qs, urlencode, urlparse, urlunparse

import fitz
import httpx
import nltk
import numpy as np
import redis_lock
import requests
import tornado.web
from celery.result import AsyncResult
from httpx import URL
from msgspec.json import decode
from nltk import SnowballStemmer
from nltk.corpus import wordnet
from numpy import average
from pdfminer.pdfdocument import PDFDocument
from pdfminer.pdfparser import PDFParser
from pdfminer.pdftypes import resolve1
from pdfparser.pdftools.pdf_element import (
    extract_paras_by_outline,
)
from pdfparser.pdftools.pdf_util import PDFUtil

from remarkable.common.common_pattern import P_PERIOD_SEPARATOR, P_YYYY_MM_DD, R_MIDDLE_DASHES
from remarkable.common.constants import (
    DEFAULT_REPORT_YEAR,
    HKEX_TEXT2EMBEDDING_FILE_PATH,
    USER_AGENT,
    AnswerType,
    DocType,
    HistoryAction,
    PolicyEsgRules,
)
from remarkable.common.exceptions import DownloadError, ShellCmdError
from remarkable.common.rectangle import Rectangle
from remarkable.config import get_config, project_root
from remarkable.db import init_rdb, pw_db
from remarkable.models.mold import special_mold
from remarkable.models.user import AdminUser
from remarkable.security import authtoken
from remarkable.services.chatgpt import OpenAIClient
from remarkable.util import md5json

T = TypeVar("T")
logger = logging.getLogger(__name__)


DATE_PATTERN = re.compile(r"(\d+)年度?(\d+月份?)?(\d+日)?")
need_secure_cookie = get_config("web.scheme", "http").lower() == "https"
DIGIT_PATTERN = re.compile(r"^\(?[\d,.]+\)?$")
P_BLANKS = re.compile(r"[\r\t\f\v\n]+")
# 匹配英文单词被识别为一个字母一个空格 http://************:55647/#/project/remark/244285?treeId=44521&fileId=67331&schemaId=28&projectId=17&schemaKey=NC-E(b)&page=120 元素块4582
P_SPECIAL_WORDS = re.compile(r"(?P<prefix>.*)(?P<middle>([A-Za-z]\s+){10,})(?P<suffix>.+)")
P_SPECIAL_PRIORI = re.compile(r"([A-Za-z]\s+){10,}")
REPORT_YEAR_REG = re.compile(r"\d{4}")

P_WHITE = re.compile(r"\s")
P_MORE_THAN_2_BLANKS = re.compile(r"\s{2,}")
P_CHNS = re.compile(
    rf"[(（]「|」[)）]|(^[(（]?([a-z]|[ivx]{{1,4}}|\d{{1,2}})[.\s)）]|[,，：:。(（；;、{R_MIDDLE_DASHES}]*)?[\u4e00-\u9fa5�流「」][,，。：:）)；;、{R_MIDDLE_DASHES}]*"
)
P_NON_EN = re.compile(r"[^a-z\s\-_()（）*.\d'‘’]", re.I)
P_EN = re.compile(r"[a-z]", re.I)
P_CH = re.compile(r"[\u4e00-\u9fa5流]")
P_ESG_E9_SUFFIX = re.compile(r" Data$")


def answer_type_to_history_action_type(answer_type):
    return {
        AnswerType.USER_DO.value: HistoryAction.SUBMIT_ANSWER.value,
        AnswerType.ADMIN_DO_1.value: HistoryAction.SUBMIT_ANSWER.value,
        AnswerType.ADMIN_DO_2.value: HistoryAction.SUBMIT_ANSWER.value,
        AnswerType.ADMIN_VERIFY.value: HistoryAction.ADMIN_VERIFY.value,
        AnswerType.ADMIN_JUDGE.value: HistoryAction.ADMIN_JUDGE.value,
    }[answer_type]


@functools.lru_cache(1000)
def clean_txt(text, remove_blank=False, remove_cn_text=False):
    if remove_cn_text:
        text = P_CHNS.sub("", text)
    # 英文内容换行需要用空格链接,且不能去空格
    text = P_BLANKS.sub(" ", text).strip()
    if P_SPECIAL_PRIORI.search(text) and (matched := P_SPECIAL_WORDS.search(text)):
        # 处理个别单词的字母之间有空格
        prefix, middle, suffix = matched.group("prefix"), matched.group("middle"), matched.group("suffix")
        text = f"{prefix or ''} {P_WHITE.sub('', middle)} {suffix}"
    if remove_blank:
        return P_WHITE.sub("", text)
    return P_MORE_THAN_2_BLANKS.sub(" ", text).strip()


def remove_chinese_chars(element_or_cell: dict):
    if not element_or_cell.get("text"):
        return ""
    text = element_or_cell["text"]
    cleaned_text = clean_txt(text, remove_cn_text=True)
    cn_indices = element_or_cell.get("chinese_chars")
    if not cn_indices or len(clean_txt(text, remove_blank=True)) - len(cleaned_text.replace(" ", "")) >= len(
        cn_indices
    ):
        return cleaned_text
    # http://************:55647/#/project/remark/340831?projectId=17&treeId=4103&fileId=104128&schemaId=29&page=57 tbl_index=1302
    # 为解决乱码问题，根据chinese_chars删除中文
    start = 0
    sub_texts = []
    for sub_text in text.split("\n"):
        if sub_indices := [i for i in cn_indices if start <= i < start + len(sub_text)]:
            min_idx, max_idx = min(sub_indices), max(sub_indices)
            sub_texts.append(sub_text[:min_idx] + sub_text[max_idx + 1 :])
        else:
            sub_texts.append(sub_text)
        start += len(sub_text) + 1
    return clean_txt(" ".join(sub_texts), remove_cn_text=True)


def index_in_space_string(text, rng, remove_blank=False):
    c_text = clean_txt(text, remove_blank)
    step = 2 if re.search(r"\s", c_text) else 1
    start = end = None

    sp_idx = 0
    s_rng, e_rng = rng
    text_len = len(text)
    for idx in range(len(c_text)):
        while sp_idx < text_len:
            if re.search(r"\s" * step, text[sp_idx : sp_idx + step]):
                sp_idx += 1
                continue
            if idx == s_rng:
                start = sp_idx
            if idx == e_rng - 1:
                end = sp_idx + 1
            sp_idx += 1
            break
        if end:
            break
    if None in (start, end):
        start, end = -1, -1
    return start, end


def split_en_words(text: str) -> list[str]:
    return [c for c in re.split(r"\s+", text) if c] if text else []


def group_cells(cells):
    cells = deepcopy(cells)
    _row = {}
    _col = {}
    for idx, cell in cells.items():
        row, col = idx.split("_")
        _col.setdefault(col, {})[row] = cell
        _row.setdefault(row, {})[col] = cell

    cells_by_row = OrderedDict()
    for k in sorted(_row, key=int):
        cells_by_row[k] = OrderedDict()
        for k_1 in sorted(_row[k], key=int):
            cells_by_row[k][k_1] = _row[k][k_1]

    cells_by_col = OrderedDict()
    for k in sorted(_col, key=int):
        cells_by_col[k] = OrderedDict()
        for k_1 in sorted(_col[k], key=int):
            cells_by_col[k][k_1] = _col[k][k_1]
    return cells_by_row, cells_by_col


def fix_zip_filename_encoding(filename):
    # from ZipFile.write documents:
    # There is no official file name encoding for ZIP files.
    # If you have unicode file names, you must convert them to byte strings in
    # your desired encoding before passing them to write(). WinZip interprets
    # all file names as encoded in CP437, also known as DOS Latin.
    _flag = False

    try:
        filename = filename.encode("cp437").decode("utf8")
        _flag = True
    except Exception:
        pass

    try:
        if not _flag:
            filename = filename.encode("cp437").decode("gbk")
            _flag = True
    except Exception:
        pass

    return filename


def filter_tree(tree_id, data, tree_s):
    tree_s.add(tree_id)
    if tree_id in data:
        for sub_tree in data[tree_id]["children"]:
            filter_tree(sub_tree["id"], data, tree_s)


class QueryHelper:
    MAX_PAGE_SIZE = 1000

    @classmethod
    def pack(cls, columns, rows):
        columns = [
            re.split(r"\bas\b\s*", col)[-1] if re.search(r"\bas\b\s*", col) else re.split(r"\.| ", col)[-1]
            for col in columns
        ]
        return [
            dict(
                zip(
                    columns,
                    r,
                )
            )
            for r in rows
        ]

    @classmethod
    async def a_count(cls, query, params=None):
        return len(list(await pw_db.execute(query, params)))

    @classmethod
    async def a_filter_perm(cls, query: str, params: Dict[str, Any]) -> str:
        if any(
            (
                not get_config("client.independent_file_perm", False),  # 是否开启独立文件权限控制
                re.search(r"order\s+by|limit", query.lower()),
                "file." not in query,
                not params,
                params and "uid" not in params,
            )
        ):
            return query

        uid = params["uid"]
        for row in await pw_db.scalar(
            AdminUser.select(AdminUser.permission).where(AdminUser.id == int(uid), AdminUser.deleted_utc == 0)
        ):
            if row["perm"] == "manage_user":  # 该权限可见所有用户文件
                return query
        # 无特殊权限只返回本id所属文件
        return "{} and file.uid = {}".format(query.rstrip(";"), uid)

    @classmethod
    async def query_data(cls, query, columns, params=None):
        query = await cls.a_filter_perm(query, params)
        data = list(await pw_db.execute(query.format(",".join(columns)), params))
        return cls.pack(columns, data)

    @classmethod
    async def page_data(cls, query, columns, orderby="", page=1, size=20, params=None):
        query = await cls.a_filter_perm(query, params)
        total = await cls.a_count(query, params=params)
        sql = (
            " ".join([query.strip(";"), orderby, "offset {}".format((page - 1) * size), "limit {}".format(size)]) + ";"
        )
        items = await cls.query_data(sql, columns, params=params)

        return {"page": page, "size": size, "total": total, "items": items}

    @classmethod
    async def pagedata_from_request(cls, request, query, columns, orderby="", params=None):
        page = int(request.get_argument("page", "1"))
        size = int(request.get_argument("size", "20"))
        if size > cls.MAX_PAGE_SIZE:
            size = cls.MAX_PAGE_SIZE
        return await cls.page_data(query, columns, orderby, page, size, params=params)


class TokenAuth:
    def __init__(self, config_prefix="app"):
        self.config_prefix = config_prefix

        self.app_id = get_config("{}.app_id".format(self.config_prefix))
        self.secret = get_config("{}.secret_key".format(self.config_prefix))
        self.token_expire = get_config("{}.token_expire".format(self.config_prefix), 3600)

    def auth_check(self, this):
        for route_ext in get_config("web.trust_routes", []):
            if re.search(rf"^/api/v\d+{route_ext}$", this.request.path):
                logger.debug(f"Skip token check: {this.request.path}")
                return True
        url = this.request.full_url()
        if "X-Original-Request-URI" in this.request.headers:
            url = this.request.headers["X-Original-Request-URI"]
        return authtoken.validate_url(url, self.app_id, self.secret, self.token_expire) or authtoken.validate_url(
            this.request.uri, self.app_id, self.secret, self.token_expire
        )

    def __call__(self, method):
        @functools.wraps(method)
        def wrapper(this, *args, **kwargs):
            if not self.auth_check(this):
                raise tornado.web.HTTPError(403)
            return method(this, *args, **kwargs)

        return wrapper


class SimpleTokenAuth:
    def __init__(self):
        self.simple_token = get_config("app.simple_token", None)

    def auth_check(self, this):
        if not self.simple_token:
            return True
        return this.request.headers.get("access-token") == self.simple_token

    def __call__(self, method):
        @functools.wraps(method)
        def wrapper(this, *args, **kwargs):
            if not self.auth_check(this):
                raise tornado.web.HTTPError(403)
            return method(this, *args, **kwargs)

        return wrapper


def mm_notify(
    msg,
    error=False,
    force=False,
    tags: tuple | None = None,
    tail: str | None = None,
    channel: str | None = None,
):
    tail = tail or get_config("notification.tail", "")
    channel = channel or get_config("notification.channel", "test-exception")
    mm_hook_url = get_config("notification.mattermost", "https://mm.paodingai.com/hooks/zxg3ncokc3yuxfymyrco7zctta")
    if error:
        logger.error(msg)
    else:
        logger.info(msg)
    if not force and not get_config("notification.switch"):
        return False
    icon = ":x:" if error else ":white_check_mark:"
    data = {
        "text": "👉#Scriber-{}-{}{}{}\n{}\n{}".format(
            os.environ.get("ENV", "dev"),
            os.uname()[1].replace(".", "_"),
            f" {' '.join('#' + t.replace(' ', '').ljust(3, '丨') for t in tags)} " if tags else " ",
            icon,
            msg,
            tail,
        ),
        "channel": channel,
        "username": "Scriber",
        "icon_url": "https://res.cloudinary.com/kdr2/image/upload/"
        "c_crop,g_faces,h_240,w_240/v1454772214/misc/c3p0-001.jpg",
    }
    try:
        rsp = requests.post(mm_hook_url, json=data, timeout=10.0)
        return rsp.status_code // 200 == 1
    except Exception:
        return False


def fail2mm_sync(tags: tuple | None = None):
    def decorate(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception:
                msg = (
                    "- FUNCTION: `{}`\n"
                    "- ARGS:\n```shell\n{}\n```\n"
                    "- KWARGS:\n```shell\n{}\n```\n"
                    "- TRACEBACK:\n```shell\n{}\n```\n"
                ).format(
                    func.__name__,
                    "\n".join(
                        f"{idx}: {str(getattr(arg, 'to_dict', lambda: arg)())[:80]}"  # noqa
                        for idx, arg in enumerate(args, 1)
                    ),
                    "\n".join(f"{k}: {v}" for k, v in kwargs.items()),
                    traceback.format_exc(),
                )
                mm_notify(msg, error=True, tags=tags)
                raise

        return wrapper

    return decorate


def calc_cost_time(start, end):
    """计算处理时间, 如果间隔时间过短或过长, 随机一个4~30min的时间"""
    res = end - start
    res = res if 1800 > res > 240 else random.randint(240, 1800)
    return "{}s".format(res)


class DownloadWrapper:
    def __init__(self, url, http_proxy=None):
        self.url = url
        self.http_proxy = http_proxy
        self.lftp_bin = shutil.which("lftp") or None

    @classmethod
    def cmd(cls, cmd_list, out_path, http_proxy):
        if http_proxy:
            env = dict(os.environ, http_proxy=http_proxy, https_proxy=http_proxy)
        else:
            env = dict(os.environ)
        try:
            ret_code = subprocess.Popen(cmd_list, env=env, encoding="utf-8", stdout=subprocess.DEVNULL).wait()
        except subprocess.CalledProcessError as e:
            raise DownloadError(e) from e

        if ret_code != 0:
            raise DownloadError(f"Error while downloading file: {out_path}, exit status code: {ret_code}")

    def build_in_func(self, dst_path):
        """Use build in func: urllib.request.urlretrieve download file(default)"""
        logging.warning('No "lftp" binary found on your system, will use native python download func.')
        ssl._create_default_https_context = ssl._create_unverified_context
        if self.http_proxy:
            # create the object, assign it to a variable
            proxy = urllib.request.ProxyHandler({"http": self.http_proxy})
            # construct a new opener using your proxy settings
            opener = urllib.request.build_opener(proxy)
            # install the opener on the module-level
            urllib.request.install_opener(opener)
        try:
            urllib.request.urlretrieve(self.url, dst_path)
        except Exception as e:
            raise DownloadError(e) from e

    def run(self):
        """Download file over bad connection network env with lftp"""

        with tempfile.NamedTemporaryFile() as tmp_file:
            if self.lftp_bin:
                self.lftp(tmp_file.name)
            else:
                self.build_in_func(tmp_file.name)
            return tmp_file.read()

    def lftp(self, dst_path):
        arg_list = [
            self.lftp_bin,
            "-e",
            f"""
            set ssl:verify-certificate false;
            set net:idle 10;
            set net:max-retries 3;
            set net:reconnect-interval-base 3;
            set net:reconnect-interval-max 3;
            set http:user-agent '{USER_AGENT}';
            pget -n 10 -c {self.url} -o {dst_path};
            exit""",
        ]
        self.cmd(arg_list, dst_path, self.http_proxy)


def count_pdf_pages(pdf_path: Union[Path, str]) -> int:
    with open(pdf_path, "rb") as file_obj:
        parser = PDFParser(file_obj)
        doc = PDFDocument(parser)
        parser.set_document(doc)
        pages = resolve1(doc.catalog["Pages"])
        return pages.get("Count", 0)


async def stream_download(url: str, save_path: str | Path, chunk_size=io.DEFAULT_BUFFER_SIZE):
    logger.info(f'Download file from "{url}"')
    headers = {"user-agent": USER_AGENT}
    transport = httpx.AsyncHTTPTransport(verify=False, retries=3)
    if isinstance(save_path, str):
        save_path = Path(save_path)
    if not save_path.parent.exists():
        save_path.parent.mkdir(parents=True, exist_ok=True)
    async with httpx.AsyncClient(transport=transport, headers=headers, timeout=httpx.Timeout(timeout=60.0)) as client:
        async with client.stream("GET", url) as rsp:
            rsp.raise_for_status()
            with open(save_path, "wb") as file_obj:
                async for chunk in rsp.aiter_bytes(chunk_size=chunk_size):
                    file_obj.write(chunk)
    logger.info(f'Download complete, file saved to "{save_path}"')


def import_class_by_path(module_path: str) -> Type | None:
    last_dot_idx = module_path.rindex(".")
    module_path, cls_name = module_path[:last_dot_idx], module_path[last_dot_idx + 1 :]
    try:
        module = import_module(module_path)
        return getattr(module, cls_name, None)
    except (ImportError, ModuleNotFoundError) as exp:
        logger.error(f'Import module "{module_path}" failed, error: {exp}')
        return None


def release_lock_keys():
    rdb = init_rdb()
    for key in rdb.scan_iter(match="lock:*"):
        rdb.delete(key)


def run_singleton_task(func: callable, *args, **kwargs) -> Tuple[bool, redis_lock.Lock, Any | AsyncResult]:
    lock_expired = kwargs.pop("lock_expired", random.randint(1800, 3600))
    lock_key = kwargs.pop("lock_key", md5json({"func": func.__name__, "args": args, "kwargs": kwargs}))
    lock = redis_lock.Lock(init_rdb(), lock_key, expire=lock_expired)
    if lock.acquire(blocking=False):
        try:
            return True, lock, (func.delay(*args, **kwargs) if hasattr(func, "delay") else func(*args, **kwargs))
        except Exception as exp:
            logger.exception(exp)
            lock.release()
            return False, lock, None
    logger.warning(f'Someone else has the lock: "{func.__name__}", skip this task.')
    return False, lock, None


def one_shot_task(lock_expired: int = None, lock_key: str = None):
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs) -> Tuple[bool, redis_lock.Lock, Any | AsyncResult]:
            nonlocal lock_expired, lock_key
            if lock_expired is None:
                lock_expired = random.randint(1800, 3600)
            if lock_key is None:
                lock_key = md5json({"func": func.__name__, "args": args, "kwargs": kwargs})

            return run_singleton_task(func, *args, lock_expired=lock_expired, lock_key=lock_key, **kwargs)

        return wrapper

    return decorator


def outline_to_box(outline: Union[List, Tuple]) -> Dict[str, float]:
    box = {
        "box_top": outline[1],
        "box_right": outline[2],
        "box_bottom": outline[3],
        "box_left": outline[0],
    }
    return box


def is_space(string):
    return clean_txt(string) == ""


def box_to_outline(box: Dict[str, float]) -> Tuple[float, float, float, float]:
    outline = (box["box_left"], box["box_top"], box["box_right"], box["box_bottom"])
    return outline


def is_aim_element(elt, aim_types=None, neg_patterns=None):
    aim_types = aim_types or []
    if aim_types and elt.get("class") not in aim_types:
        return False  # todo: text of table
    # 优先取合并后的文本做判断
    text = (elt.get("page_merged_paragraph") or {}).get("text") or elt.get("text") or ""
    if set(aim_types).issubset({"PARAGRAPH", "SYLLABUS"}) and not text:
        return False
    if neg_patterns:
        return not neg_patterns.nexts(text)
    return True


def is_digit_text(text):
    return DIGIT_PATTERN.search(text)


def format_amount(amount, decimal_places=2):
    format_str = "{:,.%sf}" % (decimal_places,)
    return format_str.format(amount).rstrip("0").rstrip(".")


def optimize_outline(chars):
    """根据pdfparser解析结果重新画框，使其贴合文本边缘"""
    if not chars:
        return []

    left, top, right, bottom = None, None, None, None
    char_width = []
    for char in chars:
        box = char["box"]
        char_width.append(box[2] - box[0])
        if left is None or box[0] < left:
            left = box[0]
        if top is None or box[1] < top:
            top = box[1]
        if right is None or box[2] > right:
            right = box[2]
        if bottom is None or box[3] > bottom:
            bottom = box[3]
    margin = average(char_width) / 10
    margin = round(margin, 4)

    return [left - 3 * margin, top - 3 * margin, right + margin, bottom + margin]


def extract_text_by_ocr(pdf_path, page, outline, ocr_name):
    # fixme  RuntimeWarning: coroutine 'Pool._wakeup' was never awaited
    return extract_paras_by_outline(pdf_path, page, outline, ocr_name=ocr_name)


def kmeans(nums, clusters=2):
    """k-means聚类算法
    clusters      - 指定分簇数量
    nums          - ndarray(m, n)，m个样本的数据集，每个样本n个属性值
    将初步定位的结果按照分数分簇
    nums 应该是有序的
    """
    sample_size, attrs = nums.shape  # sample_size：样本数量，attrs：每个样本的属性值个数
    result = np.ones(sample_size, dtype=np.int32)  # sample_size个样本的聚类结果
    cores = [nums[0], nums[-1]]  # 选择第一个和最后一个元素块当做质心

    while True:  # 迭代计算
        square = np.square(np.repeat(nums, clusters, axis=0).reshape(sample_size, clusters, attrs) - cores)
        distance = np.sqrt(
            np.sum(square, axis=2)
        )  # ndarray(sample_size, clusters)，每个样本距离k个质心的距离，共有sample_size行
        index_min = np.argmin(distance, axis=1)  # 每个样本距离最近的质心索引序号

        if (index_min == result).all():  # 如果样本聚类没有改变
            return result  # 则返回聚类结果

        result[:] = index_min  # 重新分类
        for cluster in range(clusters):  # 遍历质心集
            items = nums[result == cluster]  # 找出对应当前质心的子样本集
            cores[cluster] = np.mean(items, axis=0)  # 以子样本集的均值作为当前质心的位置


def gen_juraurl(qid, fid, is_annual_report=False, is_esg_report=False, is_cg_report=False, mid=None, doc_type=None):
    scheme = get_config("web.scheme", "http")
    url_prefix = f"{scheme}://{get_config('web.domain')}"
    if is_annual_report:
        mid = special_mold.v1_id
        url = f"{url_prefix}/#/hkex/annual-report-checking/report-review/{qid}?fileId={fid}&schemaId={mid}&rule=B1"
    elif is_esg_report:
        url = (
            f"{url_prefix}/#/hkex/esg-report-checking/report-review/{qid}?"
            f"fileId={fid}&schemaId={mid}&rule=MDR%2013%20i%20-%20board%20oversight"
        )
    elif is_cg_report:
        url = f"{url_prefix}/#/hkex/cg-report-checking/report-review/{qid}?fileId={fid}&schemaId={mid}&rule=A%28c%29-Deviation%20from%20Code%20Provisions"
    else:
        url = f"{url_prefix}/index.html#/hkex/result-announcement/report-review/{qid}?fileId={fid}"
    if doc_type == DocType.AGM:
        url = f"{url_prefix}/index.html#/hkex/agm-circular-checking/report-review/{qid}?fileId={fid}&schemaId={mid}&rule=M2-total%20number%20and%20description%20of%20the%20shares%20which%20the%20issuer%20proposes%20to%20purchase"
    elif doc_type == DocType.POLL:
        url = f"{url_prefix}/index.html#/hkex/agm-poll-results/report-review/{qid}?fileId={fid}&schemaId={mid}&rule=P1-shares%20to%20attend%20and%20vote"
    return url


class DateTimeUtil:
    @classmethod
    def str2timestamp(cls, time_str: str, pattern="%Y/%m/%d %H:%M:%S") -> int:
        date_time = datetime.strptime(time_str, pattern)
        return int(datetime.timestamp(date_time))

    @classmethod
    def timestamp2str(cls, time_stamp: int, pattern="%Y/%m/%d %H:%M:%S") -> str:
        date_time = datetime.fromtimestamp(time_stamp)
        return datetime.strftime(date_time, pattern)

    @classmethod
    def the_end_time(cls, date: datetime, *, by: Literal["day", "month", "year"] = "day") -> datetime:
        match by:
            case "day":
                return date.replace(hour=23, minute=59, second=59)
            case "month":
                if date.month == 12:
                    return date.replace(day=31, hour=23, minute=59, second=59)
                return date.replace(day=1, month=date.month + 1, hour=0, minute=0, second=0) - timedelta(seconds=1)
            case "year":
                return date.replace(day=31, month=12, hour=23, minute=59, second=59)
            case _:
                raise ValueError(f"Invalid by: {by}")

    @classmethod
    def the_start_time(cls, date: datetime, *, by: Literal["day", "month", "year"] = "day") -> datetime:
        match by:
            case "day":
                return date.replace(hour=0, minute=0, second=0)
            case "month":
                return date.replace(day=1, hour=0, minute=0, second=0)
            case "year":
                return date.replace(day=1, month=1, hour=0, minute=0, second=0)
            case _:
                raise ValueError(f"Invalid by: {by}")


def is_valid_report_year_for_ar(year: str) -> bool:
    if not REPORT_YEAR_REG.search(year):
        return False
    year_now = datetime.now().year
    return DEFAULT_REPORT_YEAR <= int(year) <= year_now + 1


def is_valid_report_year_for_qr(year: str) -> bool:
    if not REPORT_YEAR_REG.search(year):
        return False
    # 年结日靠前的有可能在当年发布下一年份的季报 例子:stock_code 00922 08009
    year_now = datetime.now().year + 1
    return DEFAULT_REPORT_YEAR <= int(year) <= year_now


def chars_in_box_by_center(box, page=None, texts=None, with_white_chars=False):
    chars = []

    def extend_chars(candidate_chars):
        for char in candidate_chars:
            if PDFUtil.is_box_in_box_by_center(char["box"], box):
                chars.append(char)

    if page:
        for text in page.get("texts", []):
            if text["type"] == "LINE":
                continue
            if text["box"][3] < box[1]:
                continue
            if text["box"][1] > box[3]:
                if text["box"][1] > box[3] + max(12, text["box"][3] - text["box"][1]) * 2:
                    break
                continue
            extend_chars(text["chars"])
            if with_white_chars:
                extend_chars(text.get("white_chars", []))
        chars = sorted(chars, key=functools.cmp_to_key(PDFUtil.compare_box_item))
    elif texts:
        extend_chars(texts)
        if not with_white_chars:
            chars = [char for char in chars if not P_WHITE.match(char)]
    return chars


def match_ext(path: Union[Path, str, bytes], *expected: str) -> bool:
    from magika import Magika
    from magika.types.prediction_mode import PredictionMode

    magic = Magika(prediction_mode=PredictionMode.BEST_GUESS)
    if isinstance(path, str):
        path = Path(path)
    result = (magic.identify_bytes(path) if isinstance(path, bytes) else magic.identify_path(path)).output.label
    return any(result.lower() == ext.lstrip(".").lower() for ext in expected)


class NewQueryHelper:
    MAX_PAGE_SIZE = 1000

    @classmethod
    def pack(cls, columns, rows):
        columns = [
            re.split(r"\bas\b\s*", col)[-1] if re.search(r"\bas\b\s*", col) else re.split(r"\.| ", col)[-1]
            for col in columns
        ]
        return [
            dict(
                zip(
                    columns,
                    r,
                )
            )
            for r in rows
        ]

    @classmethod
    async def count(cls, query, params=None, columns=None):
        fields = ",".join(columns) if columns else "count(*)"
        total = await pw_db.count(query.format(fields), params)
        return total

    @classmethod
    async def filter_perm(cls, query, params):
        if any(
            (
                not get_config("client.independent_file_perm", False),  # 是否开启独立文件权限控制
                re.search(r"order\s+by|limit", query.lower()),
                "file." not in query,
                not params,
                params and "uid" not in params,
            )
        ):
            return query

        uid = params["uid"]
        user = await pw_db.first(AdminUser.select(AdminUser.permission).where(AdminUser.id == uid))
        perms = user.permission
        for row in perms:
            if row["perm"] == "manage_user":  # 该权限可见所有用户文件
                return query
        # 无特殊权限只返回本id所属文件
        return "{} and file.uid = {}".format(query.rstrip(";"), uid)

    @classmethod
    async def querydata(cls, query, columns, params=None):
        query = await cls.filter_perm(query, params)
        data = await pw_db.execute(query.format(",".join(columns)), params)
        return cls.pack(columns, data)

    @classmethod
    async def pagedata(cls, query, columns, orderby="", page=1, size=20, params=None):
        query = await cls.filter_perm(query, params)
        total = await cls.count(query, params=params, columns=columns)
        sql = (
            " ".join([query.strip(";"), orderby, "offset {}".format((page - 1) * size), "limit {}".format(size)]) + ";"
        )
        items = await cls.querydata(sql, columns, params=params)

        return {"page": page, "size": size, "total": total, "items": items}

    @classmethod
    def pagedata_from_request(cls, conn, request, query, columns, orderby="", params=None):
        page = int(request.get_argument("page", "1"))
        size = int(request.get_argument("size", "20"))
        if size > cls.MAX_PAGE_SIZE:
            size = cls.MAX_PAGE_SIZE
        return cls.pagedata(conn, query, columns, orderby, page, size, params=params)


def get_pub_date_from_string(string: str) -> datetime | None:
    if match := P_YYYY_MM_DD.nexts(string):
        date_str = match.group(1)
        with suppress(ValueError):
            return datetime.strptime(date_str, "%Y%m%d")
    return None


def convert_label(schema: "HKEXSchema", label: str, force=False):  # noqa
    rule = schema.name_rule_map.get(label, None)
    if force or rule is None:
        return rule
    if not rule or "(" not in rule:
        match = re.search(r"^(?P<rule>D\d+)\.1$", rule)  # HARDCODE: D Rules
        return match.group("rule") if match else None
    rule = rule.replace("(", "")
    rule = rule.replace(")", "")
    return rule


def get_enum_answer(answer_item, judge_manual=False):
    value = answer_item.get("value", None)
    is_manual = answer_item.get("manual", None)
    if value and (judge_manual and is_manual or not judge_manual):
        return value
    data = answer_item.get("data")
    if not data:
        return ""
    return data[0].get("value", "")


def stat_compliance_score(stat_res, rule_count):
    if rule_count == 0:
        return 0
    score_for_this_ar = collections.Counter(stat_res.values()).get("true", 0) / rule_count
    return score_for_this_ar


def standard_stock(code_str):
    if isinstance(code_str, int):
        code_str = str(code_str)
    return code_str.strip().rjust(5, "0")


def is_main_stock(stock_code):
    return not standard_stock(stock_code).startswith("08")


def pairwise(iterable):
    # used to replace itertools.pairwise in py310 for py38
    "[s0, s1, s2, s3] -> [(s0,s1), (s1,s2), (s2, s3)]"
    a, b = tee(iterable)
    next(b, None)
    return zip(a, b)


def is_in_range(index: int, target_range: list | tuple) -> bool:
    return target_range[0] <= index < target_range[1]


def ensure_nltk_data():
    data_dir = Path(project_root) / "data"
    nltk.data.path.append(data_dir)  # 添加自定义路径
    try:
        nltk.data.find("corpora/wordnet.zip")
    except LookupError:
        logger.warning("nltk data not found, downloading...")
        nltk.download("wordnet", download_dir=data_dir, quiet=True)


def expand_query(query: str) -> str:
    ensure_nltk_data()
    stemmer = SnowballStemmer("english")
    words = query.split()
    expanded_words = set()
    for word in words:
        stemmed = stemmer.stem(word)
        expanded_words.update([word, stemmed])
        for syn in wordnet.synsets(word):
            for lemma in syn.lemmas():
                if "'" in lemma.name():
                    continue
                expanded_words.add(lemma.name())
    return " | ".join(expanded_words)


def normalize_outline(outline):
    if isinstance(outline, (tuple, list)):
        return outline
    if isinstance(outline, dict):
        return (outline["box_left"], outline["box_top"], outline["box_right"], outline["box_bottom"])
    raise ValueError("not support input outline")


def box_in_box(element_outline, box_outline):
    """outline:
    (left, top, right, bottom)
    or
    {"box_bottom": bottom, "box_left": left, ...}
    """

    element_outline = normalize_outline(element_outline)
    box_outline = normalize_outline(box_outline)

    x_in_box = box_outline[0] <= (element_outline[2] + element_outline[0]) / 2 <= box_outline[2]
    y_in_box = box_outline[1] <= (element_outline[3] + element_outline[1]) / 2 <= box_outline[3]

    return x_in_box and y_in_box


def subprocess_exec(command, timeout=None):
    logging.info(f'running command: "{command}"')
    with subprocess.Popen(command, stdout=subprocess.PIPE, stderr=subprocess.PIPE, shell=True) as cmd_process:
        out, err = cmd_process.communicate(timeout=timeout)
        if err:
            raise ShellCmdError(err)
        return out


def compact_dumps(data, **kwargs) -> str:
    """json.dumps with compact format"""
    kwargs.update(
        {
            "ensure_ascii": False,
            "separators": (",", ":"),
        }
    )
    return json.dumps(data, **kwargs)


def _merge_url_params(url: str, additional_params: httpx._types.QueryParamTypes) -> str:
    """
    合并URL中已有的查询参数和新的参数

    Args:
        url: 包含查询参数的URL
        additional_params: 要添加的额外参数

    Returns:
        str: 合并后的完整URL
    """
    parsed = urlparse(url)

    # 解析现有的查询参数
    existing_params = parse_qs(parsed.query, keep_blank_values=True)

    # 将现有参数转换为单值字典（parse_qs返回的是列表）
    merged_params = {k: v[0] if v else "" for k, v in existing_params.items()}

    # 处理新参数，支持多种类型
    if isinstance(additional_params, dict):
        for k, v in additional_params.items():
            merged_params[k] = str(v)
    elif hasattr(additional_params, "items"):
        # 处理类似字典的对象
        for k, v in additional_params.items():
            merged_params[k] = str(v)
    elif isinstance(additional_params, (list, tuple)):
        # 处理列表或元组形式的参数
        for item in additional_params:
            if isinstance(item, (list, tuple)) and len(item) == 2:
                k, v = item
                merged_params[str(k)] = str(v)

    # 重新构建URL
    new_query = urlencode(merged_params)
    new_parsed = parsed._replace(query=new_query)

    result = urlunparse(new_parsed)
    # 确保返回字符串类型
    return result if isinstance(result, str) else result.decode("utf-8")


@asynccontextmanager
async def httpx_client(*, verify=False, retries=3, timeout=10.0, **kwargs) -> AsyncGenerator[httpx.AsyncClient, Any]:
    async with httpx.AsyncClient(
        transport=httpx.AsyncHTTPTransport(verify=verify, retries=retries),
        headers={"user-agent": USER_AGENT},
        timeout=timeout,
        **kwargs,
    ) as client:
        # 保存原始的request方法
        original_request = client.request

        async def _request(method: str, url: URL | str, **kw) -> httpx.Response:
            if params := kw.pop("params", {}):
                url = _merge_url_params(str(url), params)
            # 调用原始的request方法，避免无限递归
            return await original_request(method, url, **kw)

        client.request = _request
        yield client


def split_paragraph(
    text: str,
    separator: re.Pattern = P_PERIOD_SEPARATOR,
    *,
    need_separator=True,
    need_clean=True,
    remove_blank=False,
    remove_cn_text=False,
    need_pos=False,
):
    """
    根据指定正则切分段落
    """
    start = 0
    for matched in chain(separator.finditer(text), (None,)):
        end, matched_len = (matched.start(), len(matched.group())) if matched else (len(text), 0)
        sen_end_pos = end + (matched_len if need_separator else 0)
        sentence = text[start:sen_end_pos]
        if need_clean:
            sentence = clean_txt(sentence, remove_blank=remove_blank, remove_cn_text=remove_cn_text)
        if sentence:
            if need_pos:
                yield sentence, (start, sen_end_pos)
            else:
                yield sentence
        if matched:
            start = matched.end()


def split_element(element) -> dict:
    result = {}
    if not element.get("text"):
        return result
    sub_texts = split_paragraph(element["text"], need_clean=False)
    start = 0
    for element_sequence, sub_text in enumerate(sub_texts, start=1):
        sub_element = copy(element)
        end = start + len(sub_text)
        sub_element["text"] = sub_text
        sub_element["chars"] = element["chars"][start:end]
        sub_element["element_sequence"] = element_sequence
        result[sub_text] = sub_element
        start = end
    sub_element = copy(element)
    sub_element["element_sequence"] = 0
    result[element["text"]] = sub_element
    return result


def read_zip_first_file(zip_path, *, msgspec_type: Type[T] | None = None):
    # start = time.time()
    thin_path = zip_path + ".thin"
    if os.path.exists(thin_path):
        zip_path = thin_path
    with zipfile.ZipFile(zip_path, "r") as fzip:
        data = fzip.read(fzip.namelist()[0])
        # logger.info("read %s file cost %.2fs", zip_path, time.time() - start)
        if msgspec_type is None:
            return data.decode("utf-8") if isinstance(data, bytes) else data
        return decode(data, type=msgspec_type)


def get_page_rotation(page: dict) -> float:
    if PDFUtil.is_ocr_page(page):  # noqa
        return page["ret_rotation"]
    return page.get("page_rotation", page["rotate"])


def merge_char_rects(chars, pos_key="char-position"):
    if not chars:
        return {}
    merged_page_rects = {}
    for page, group in groupby(chars, key=lambda x: x.get("page")):
        merged_rects = []
        index = 0
        for item_char in group:
            if not item_char["text"].strip():
                continue
            rect = Rectangle(*item_char[pos_key])
            if not merged_rects:
                merged_rects.append(rect)
                continue
            if rect.y < merged_rects[index].yy:
                merged_rects[index] = merged_rects[index].union(rect)
            elif chars.index(item_char) == 1:
                merged_rect = merged_rects[index].union(rect)
                if merged_rect.x == merged_rects[index].x and merged_rect.xx == rect.xx:
                    merged_rects[index] = merged_rect
                else:
                    index += 1
                    merged_rects.append(rect)
            else:
                index += 1
                merged_rects.append(rect)
        merged_page_rects[page] = merged_rects
    return merged_page_rects


def extract_hyperlinks_from_pdf(pdf_path):
    """
    从 PDF 中提取所有超链接
    返回格式：[{"page": 页码, "url": URL, "box": [x0,y0,x1,y1]}, ...]
    """
    doc = fitz.open(pdf_path)
    result = collections.defaultdict(list)

    for page_num in range(len(doc)):
        page = doc.load_page(page_num)
        links = page.get_links()

        for link in links:
            if link["kind"] == fitz.LINK_URI:
                result[page_num].append(
                    {
                        "url": link["uri"],
                        "box": [link["from"].x0, link["from"].y0, link["from"].x1, link["from"].y1],
                    }
                )

    return result


@functools.lru_cache(1000)
def text2embedding(text: str) -> List[float]:
    embedding_info = {}
    if os.path.exists(HKEX_TEXT2EMBEDDING_FILE_PATH):
        with open(HKEX_TEXT2EMBEDDING_FILE_PATH, "rb") as fp:
            embedding_info = pickle.load(fp)
    if text in embedding_info:
        return embedding_info[text]
    return OpenAIClient().embedding(text)


def normalize_account(name):
    name = clean_txt(name, remove_cn_text=True)
    name = name.lower()

    replace_map = {
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6172#note_731436
        r"\s*[（(][\"“”‘’']?fvt?(pl|oci)[\"“”‘’']?[)）]\s*": "",
        r"^Net\s*?(impairment)": r"\1",  # Net impairment -> impairment
        r"\bon\s*?(financial)": r"of \1",  # on financial -> of financial
        r"\(note.*\)": "",
        r"^Including[:：]\s*": "",
        r"(\w+)ies(\b)": r"\1y\2",  # ies -> y
        r"(\w+)es(\b)": r"\1\2",  # remove es
        "fvtoci": "fair value through other comprehensive income",
        "fvoci": "fair value through other comprehensive income",
        "fvtpl": "fair value through profit or loss",
        "fvpl": "fair value through profit or loss",
        "finance": "financial",
        "profit of loss": "profit or loss",
        r"(^|\s)as(\s|$)": r"\1at\2",
        r"measured\s*at\s+": r"at ",
        r"(^|\s)asset(\s|$)": r"\1assets\2",
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/6172#note_732342
        rf"\s*(non[{R_MIDDLE_DASHES}\s]?)?current\s*": " ",
    }

    for pattern, repl in replace_map.items():
        name = re.sub(pattern, repl, name, flags=re.IGNORECASE)
    # 必须放在最后
    name = name.strip().replace('"', "").replace("(", "").replace(")", "")
    return clean_txt(name)


def fix_aid(aid: str, mid: int, sub_rule: str = "") -> str:
    if not sub_rule:
        return aid
    if mid not in (special_mold.policy_esg_id, special_mold.policy_ar_id):
        return aid
    if aid == PolicyEsgRules.E9:
        sub_rule = P_ESG_E9_SUFFIX.sub("", sub_rule)
    return f"{aid}_{sub_rule}"


def split_chars(chars, interval=100):
    lines = []
    for char in chars:
        if lines:
            # 如果字符的顶部+100还是小于上一个字符的底部，那么可能是分栏了
            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/1135#note_271576
            if (
                char["box"][1] > lines[-1][-1]["box"][3]
                or char["box"][0] > lines[-1][-1]["box"][2] + interval
                or char["box"][1] + interval < lines[-1][-1]["box"][3]
            ):
                lines.append([char])
            else:
                lines[-1].append(char)
        else:
            lines.append([char])
    return lines


def has_english_chars(element_or_cell):
    if element_or_cell.get("english_chars"):
        return True
    if not element_or_cell.get("text"):
        return False
    return P_EN.search(element_or_cell["text"])


def has_chinese_chars(element_or_cell):
    if element_or_cell.get("chinese_chars"):
        return True
    if not element_or_cell.get("text"):
        return False
    return P_CH.search(element_or_cell["text"])
