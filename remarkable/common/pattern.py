from __future__ import annotations

import re
import sys
from dataclasses import dataclass
from typing import Any, Callable, Iterable, List, Match, Pattern, Tu<PERSON>, Union

from attrs import define, field

from remarkable.common.protocol import SearchPatternLike


@define(slots=True)
class PatternCollection:
    patterns: Union[None, str, List[Union[str, Pattern, "PatternCollection"]]] = field(default=None)
    flags: int = field(default=0)
    _pattern_objects: List[Pattern] = field(default=None)

    def __attrs_post_init__(self):
        # 在实例化后立即编译 patterns
        if not self._pattern_objects:
            self._pattern_objects = self._compile(self.patterns, self.flags)

    def __bool__(self):
        return bool(self.patterns)

    def __str__(self):
        return "\n".join(pattern.pattern for pattern in self._pattern_objects)

    def dynamic_patterns(self, patterns=None):
        return self._pattern_objects + self._compile(patterns)

    @classmethod
    def _compile(cls, patterns, flags=0) -> List[Pattern]:
        pattern_list = []
        if patterns is None:
            return pattern_list
        if isinstance(patterns, str):
            pattern_list.append(re.compile(patterns, flags))
        elif isinstance(patterns, cls):
            pattern_list.extend(cls._compile(patterns.patterns, flags))
        elif isinstance(patterns, re.Pattern) or hasattr(patterns, "search"):
            pattern_list.append(patterns)
        elif isinstance(patterns, (tuple, list)):
            for pattern in patterns:
                pattern_list.extend(cls._compile(pattern, flags))
        return pattern_list

    def search(self, text, *, extra_patterns=None):
        for pattern in self.dynamic_patterns(extra_patterns):
            match = pattern.search(text)
            if match:
                yield match

    def nexts(self, text, *, extra_patterns=None):
        patterns = self.dynamic_patterns(extra_patterns)
        if not patterns:
            return None
        return next((match for pattern in patterns for match in [pattern.search(text)] if match), None)

    def match(self, text, *, extra_patterns=None):
        for pattern in self.dynamic_patterns(extra_patterns):
            match = pattern.match(text)
            if match:
                yield match

    def finditer(self, text, *, extra_patterns=None):
        for pattern in self.dynamic_patterns(extra_patterns):
            match = pattern.finditer(text)
            for item in match:
                if item:
                    yield item

    def sub(self, repl, text, *, extra_patterns=None):
        for pattern in self.dynamic_patterns(extra_patterns):
            text = pattern.sub(repl, text)
        return text


def _compile(pattern: Any, flag: int = 0) -> SearchPatternLike:
    if isinstance(pattern, str):
        return re.compile(pattern, flag)
    if isinstance(pattern, SearchPatternLike):
        return pattern
    raise TypeError(f"{pattern} is not a valid pattern")


def get_all_patterns(patterns: SearchPatternLike) -> List[str]:
    all_patterns = []
    for pattern in patterns:
        if isinstance(pattern, re.Pattern):
            all_patterns.append(pattern.pattern)
        else:
            all_patterns.extend(get_all_patterns(pattern.patterns))

    return all_patterns


@dataclass
class SplitBeforeMatch:
    pattern: SearchPatternLike
    separator: Pattern
    operator: Callable[[Iterable], bool] = any

    def __hash__(self):
        return hash((self.pattern, self.separator))

    @classmethod
    def compile(
        cls,
        pattern: str | SearchPatternLike,
        separator: str,
        operator: Callable[[Iterable], bool] = any,
    ) -> "SplitBeforeMatch":
        return cls(_compile(pattern), re.compile(separator), operator)

    def split(self, string: str, pos: int = 0, endpos: int = sys.maxsize) -> Iterable[str]:
        start = 0
        for matched in self.separator.finditer(string, pos, endpos):
            yield string[start : matched.start()]
            start = matched.end()
        yield string[start:]

    def search(self, string: str, pos: int = 0, endpos: int = sys.maxsize) -> bool:
        return self.operator(self.pattern.search(text, pos, endpos) for text in self.split(string, pos, endpos))


@dataclass
class MatchMulti:
    patterns: Tuple[SearchPatternLike, ...]
    operator: Callable[[Iterable], bool]

    def __hash__(self):
        return hash(self.patterns)

    def search(self, string: str, pos: int = 0, endpos: int = sys.maxsize) -> bool:
        return self.operator(pattern.search(string, pos, endpos) for pattern in self.patterns)

    @classmethod
    def compile(
        cls,
        *patterns: SearchPatternLike | str,
        operator: Callable[[Iterable], bool],
        flag: int = re.I,
    ) -> "MatchMulti":
        return cls(tuple(_compile(pattern, flag) for pattern in patterns), operator)

    def union(self, pattern: SearchPatternLike | str, flag: int = re.I) -> "MatchMulti":
        return type(self)((*self.patterns, _compile(pattern, flag)), self.operator)  # type: ignore

    @classmethod
    def never(cls):
        return cls.compile(operator=any)

    def __str__(self):
        return "\n".join(get_all_patterns(self.patterns))


@dataclass
class MatchFirst:
    patterns: Tuple[Pattern, ...]

    def __hash__(self):
        return hash(self.patterns)

    @classmethod
    def compile(
        cls,
        *patterns: Pattern | str,
        flag: int = re.I,
    ) -> "MatchFirst":
        return cls(tuple(re.compile(pattern, flag) for pattern in patterns))

    def search(self, string: str, pos: int = 0, endpos: int = sys.maxsize) -> Match | None:
        for pattern in self.patterns:
            if matched := pattern.search(string, pos, endpos):
                return matched
        return None


@dataclass
class NeglectPattern:
    match: SearchPatternLike
    unmatch: SearchPatternLike

    def __hash__(self):
        return hash((self.match, self.unmatch))

    @classmethod
    def compile(
        cls, *, match: str | SearchPatternLike, unmatch: str | SearchPatternLike, flag: int = re.I
    ) -> "NeglectPattern":
        return cls(_compile(match, flag), _compile(unmatch, flag))

    def search(self, string: str, pos: int = 0, endpos: int = sys.maxsize) -> bool:
        return self.match.search(string, pos, endpos) and not self.unmatch.search(string, pos, endpos)


@dataclass
class PositionPattern:
    """按照顺序依次匹配多个正则, 全部匹配则返回True, 否则返回False"""

    patterns: tuple[Pattern, ...]
    ignore: Pattern

    def __hash__(self):
        return hash((self.patterns, self.ignore))

    def search(self, string: str, pos: int = 0, endpos: int = sys.maxsize) -> bool:
        patterns = iter(self.patterns)
        string = self.ignore.sub("", string)
        while pattern := next(patterns, None):
            start = 0
            try:
                for matched in pattern.finditer(string, pos, endpos):
                    if matched.start() == matched.end():
                        raise StopIteration
                    start = matched.end()
                    break
            except StopIteration:
                continue
            if start == 0:
                return False
            string = string[start:]
        return True

    @classmethod
    def compile(
        cls, *patterns: str | Pattern, flag: int = re.I, ignore: str | Pattern = re.compile("^$")
    ) -> "PositionPattern":
        return cls(tuple(re.compile(pattern, flag) for pattern in patterns), re.compile(ignore))


MATCH_NEVER = MatchMulti.compile(operator=any)
MATCH_ALWAYS = re.compile(".?", re.DOTALL)
