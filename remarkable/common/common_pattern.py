from __future__ import annotations

import re

from remarkable.common.pattern import PatternCollection

esg_outline_p = PatternCollection(
    [
        r"environment.*?social\s*?and\s*?governance\s*?report",
        r"Corporate\s*?Social\s*?Responsibility\s*?Report",
        r"^SOCIAL\s*?AND\s*?GOVERNANCE\s*?REPORT$",  # esg换行
        r"^environment.*?social\s*?and$",  # esg换行
        r"^B\. SOCIAL$",  # social
    ],
    re.I,
)


esg_chapter_p = PatternCollection(
    [
        r"[AB]\d",
        # r'Product Responsibility', # B6等字段
        r"Compliance Management",
        r"Emissions Management",
    ],
    re.I,
)

EXPLAIN_PATTERN = [
    r"not (been )?addressed",
    r"not? ((directly|been) )?(involve|engage|disclosed|significant)",
    # r'not? ((directly|been) )?(generate|generation|produce)', # 不是通用的explain pattern
    # r'not? material',
    # r'N/A',
    r"immaterial|insignificant",
    r"not applicable",
    r"no.*?was undertaken",
]


PAGE_PATTERN = PatternCollection(
    [
        r"^(?P<dst>\d{1,3})$",
        r"^(?P<dst>\d{1,3})\s",
        r"\s(?P<dst>\d{1,3})$",
        r"\s(?P<dst>\d{1,3})\s",
    ]
)


INVALID_FIRST_ELE_PATTERN = PatternCollection(
    [
        r"^Chapter\s?\d",
        r"^A.\s?ENVIRONMENTAL ASPECT$",
        r"^B\.\s?SOCIAL ASPECT\s?$",
        r"^B\.\s?社會層面$",
        r"^\d\.",  # TODO 可能会误判 一般是章节在了一页的第一个元素块
    ]
)


COMMON_TABLE_TITLE_BLACK_LIST = PatternCollection(
    [
        r"20\d\d\s20\d\d",
        r"ENVIRONMENTAL, SOCIAL",
    ]
    + esg_outline_p.patterns,
    re.I,
)

# 日期匹配: 年份在 1900 到 2099 之间，月份为 01 到 12，日期为 01 到 31，仅做粗略匹配。
P_YYYY_MM_DD = PatternCollection(r"((?:19|20)\d{2}(?:0[1-9]|1[0-2])(?:0[1-9]|[12][0-9]|3[01]))")

R_QUOTE = "[\"“”'‘ ’]"
R_MIDDLE_DASHES = r"\-—－–‐"
R_SELECTED = r"[√✔✅☑]"
R_MIDDLE_DASH = rf"[{R_MIDDLE_DASHES}]"
P_MIDDLE_DASH = re.compile(rf"{R_MIDDLE_DASH}")
P_ONLY_MIDDLE_DASH = re.compile(rf"^[{R_MIDDLE_DASHES}\s]*$")
P_CELL_NIL = re.compile(rf"^(0|0.0+|nil|N/A|{R_MIDDLE_DASH}|\s*)$", re.I)
# TODO： `流`这个字，只能用regex库的regex.compile(r'\p{Han}+')来匹配，CJK扩展也匹配不了
# stock_code=01527, year=2024, mid=29, page=88, index=1404
R_CN_SPACE = r"[\s\u4e00-\u9fa5�流]*"
R_CN_SPACE_COLON = r"[:：\s\u4e00-\u9fa5�流]*"
R_CN_SPACE_MIDDLE_DASH = rf"[{R_MIDDLE_DASHES}\s\u4e00-\u9fa5�流]*"
# 资产负债表中的总资产
R_ASSETS = r"assets?"
R_NON_CURRENT_ASSETS = rf"(total\s*)?non[{R_MIDDLE_DASHES}\s]?cu\s*rr?ent(\s*assets?)?"
R_CURRENT_ASSETS = r"(total\s*)?cu\s*rr?ent\s*assets?(\s*and\s*liabilities)?"
R_TOTAL_ASSETS = r"total\s*assets(\s*and\s*liabilities)?"

DIRECTORY_START_PATTERN = PatternCollection(r"^\d+\.\d+.(\d+)?")  # 目录中的段落
P_CONTENTS = PatternCollection(
    [
        r"^(table\s*of\s*)?contents?$",
        r"^content.*?目[录錄]$",
    ],
    re.I,
)  # 目录中的段落
# 表格脚注
R_SPECIAL_PREFIX = r"[•➢➣➤▷⊙●◊►■]"
R_NOTE_PREFIX = rf"[{R_MIDDLE_DASHES}•*#①②③④⑤⑥⑦⑧⑨⑩⑪⑫⑬⑭⑮⑯⑰⑱⑲⑳]"
P_NOTES = re.compile(rf"^{R_CN_SPACE}Notes?(\s*[1-9]\d?|[a-z]|[IV]{1, 4})?[.:：\s]*", re.I)
P_ONLY_NOTES = re.compile(rf"^Notes?{R_CN_SPACE_COLON}$|^{R_CN_SPACE}Notes?$", re.I)
R_PREFIX_NUM = r"([(（]?([a-z]|[iIvVxX]{1,4}|[1-9]\d?(\.[1-9]\d?\b)*)[•)）\s.]+|[1-9](?!\d)|[1-9]\d(?!\d))"
R_FOLLOW_PREFIX = rf"^\s*({R_NOTE_PREFIX}|{R_PREFIX_NUM}|stage\s*\d+\s*{R_MIDDLE_DASH}\s)\s*"
P_FOLLOW_PREFIX = re.compile(R_FOLLOW_PREFIX, re.I | re.M)
P_C_FOLLOW_PREFIX = PatternCollection(R_FOLLOW_PREFIX, flags=re.I)
P_NON_CH_EN = re.compile(r"[^a-z\u4e00-\u9fa5]", re.I)
# TODO unittest
R_CHAPTER_PREFIX = rf"^({R_SPECIAL_PREFIX}?((SECTION|CHAPTER|ITEM|PART|stage)\s+([iIvVxX]{{1,4}}|[1-9]\d{{,2}}\b)\.?)?\s*|{R_PREFIX_NUM})"
P_CHAPTER_PREFIX = re.compile(
    rf"^({R_SPECIAL_PREFIX}|(SECTION|CHAPTER|ITEM|PART)\s+([ivx]{{1,4}}|[1-9]\d{{,2}}\b)\s+|{R_PREFIX_NUM})", re.I
)
R_CONTINUED = r"(continued?|cont['‘’]d|[續续]).{,3}\s*[)）\s\d]*$"
P_CONTINUED = re.compile(
    rf"(?<![:：] )(?<![:：] [{R_MIDDLE_DASHES}（(])(?<![:：])(?<![:：][{R_MIDDLE_DASHES}（(])[{R_MIDDLE_DASHES}（(]?\s*{R_CONTINUED}",
    re.I,
)
P_CONTINUED_WITH_COLON = re.compile(
    rf"[{R_MIDDLE_DASHES}（(\s]+{R_CONTINUED}",
    re.I,
)
R_AS_AT = r"\b(as\s*at|as\s*of|up\s*to|end\s*of|year\s*end(ed|ing)?|as)\b"
# http://100.64.0.105:55647/#/project/remark/245361?treeId=2484&fileId=66255&schemaId=28&projectId=17&schemaKey=E(d)(iii)&page=42
R_CHAPTER_SUFFIX = rf"\s*([{R_MIDDLE_DASHES}(（]\s*{R_CONTINUED}|(?<!\d)\d{{1,3}}\s*)?$"

# 财务报表
# audited: http://100.64.0.105:55647/#/project/remark/411619?treeId=4498&fileId=113827&schemaId=29&projectId=17 stock=2009 year=2024
P_CONSOLIDATED_STATEMENT = re.compile(
    rf"({R_CHAPTER_PREFIX}|^\d*[^a-z]*)(audited\s*|the\s*|condensed\s*)?(consolidated|statement).*(balance|profit\s*or\s*loss|financial\s*position|changes\s*in\s.*equity|cash\s*flow|comprehensive\s*income|income\s*statement)",
    re.I,
)

R_CG_CHAPTER_TITLES = [
    rf"((corporate\s*)?governance\s*(report|PRACTICES?)|企業管治(報告書?|常規)){R_CHAPTER_SUFFIX}",
    rf"(企業管治(報告書?|常規))?\s*Corporate\s*Governance\s*(report|PRACTICES?)?{R_CHAPTER_SUFFIX}",
    rf"(GOVERNANCE\s*|.+?\|\s*)?Corporate\s*Governance\s*(report|PRACTICES?)\s*(企業管治(報告書?|常規))?{R_CHAPTER_SUFFIX}",
    rf"{R_CHAPTER_PREFIX}(report\s*of\s*)?corporate\s*governance{R_CHAPTER_SUFFIX}",
    rf"{R_CHAPTER_PREFIX}企業管治(報告書?|常規){R_CHAPTER_SUFFIX}$",
    rf"{R_CHAPTER_PREFIX}corporate\s*governance\s*overview\s*statement{R_CHAPTER_SUFFIX}",
    # https://jura-uat.paodingai.com/#/hkex/cg-report-checking/report-review/251416?fileId=80006&schemaId=28&rule=A%28c%29-Deviation%20from%20Code%20Provisions&delist=0
    rf"{R_CHAPTER_PREFIX}corporate\s*governance\s*and\s*other\s*information\s*{R_CHAPTER_SUFFIX}",
    # http://100.64.0.105:55647/#/project/remark/245202?treeId=28543&fileId=66414&schemaId=28&projectId=17&schemaKey=E(d)(iv)-1
    rf"{R_CHAPTER_PREFIX}cor?porate\s*governance\s*[（(]Cor?porate\s*Governance\s*Report[)）]{R_CHAPTER_SUFFIX}",
]
R_DR_CHAPTER_TITLES = [
    rf"Directors['‘’]?\s*Report\s*(董事會報告)?{R_CHAPTER_SUFFIX}",
    rf"Report\s*of\s*(the\s*)?(board\s*of\s*)?Directors\s*(董事會報告)?{R_CHAPTER_SUFFIX}",
    rf"{R_CHAPTER_PREFIX}董事會報告{R_CHAPTER_SUFFIX}",
    r"^BOARD\s?REPORT$",
]
R_NOTES_CHAPTER_TITLES = [
    rf"{R_CHAPTER_PREFIX}Notes.*Financial\s*Statements{R_CHAPTER_SUFFIX}",
    rf"{R_CHAPTER_PREFIX}NOTES\s*TO\s*THE\s*CONSOLIDATED.+$",
    rf"{R_CHAPTER_PREFIX}FINANCIAL\S*STATEMENTS\s*and\s*notes{R_CHAPTER_SUFFIX}",
    rf"{R_CHAPTER_PREFIX}(綜合)?財務報表附註{R_CHAPTER_SUFFIX}",
    # 修复标题换行识别问题后，不太会出现以下情况
    # rf"FINANCIAL\s*STATEMENTS(\s*綜合財務報表附註)?{R_CHAPTER_SUFFIX}",
    # rf"{R_CHAPTER_PREFIX}FINANCIAL\s*STATEMENTS",
]
R_ESG_CHAPTER_TITLES = [
    # http://100.64.0.105:55647/#/project/remark/250726?treeId=2753&fileId=68577&schemaId=5&projectId=17
    rf"Environmental[,，]?\s*Social\s*and\s*Governance\s*Report\s*(環境、社會及管治報告)?{R_CHAPTER_SUFFIX}",
    rf"{R_CHAPTER_PREFIX}環境、社會及管治報告{R_CHAPTER_SUFFIX}",
]
R_CI_CHAPTER_TITLES = [
    rf"{R_CHAPTER_PREFIX}corporate\s.*(information|pro.{{,2}}le)\s*((公司|企業)資料)?{R_CHAPTER_SUFFIX}",
    rf"{R_CHAPTER_PREFIX}(公司|企業)(及股東)?資料{R_CHAPTER_SUFFIX}",
]
R_OI_CHAPTER_TITLES = [
    rf"{R_CHAPTER_PREFIX}other\s*information\s*(其他資料)?{R_CHAPTER_SUFFIX}",
    rf"{R_CHAPTER_PREFIX}其他資料{R_CHAPTER_SUFFIX}",
]
R_MDA_CHAPTER_TITLES = [
    # http://100.64.0.105:55647/#/project/remark/265984?treeId=3706&fileId=70411&schemaId=18&projectId=3706&schemaKey=C1.1.1
    # http://100.64.0.105:55647/#/project/remark/294188?treeId=14382&fileId=70739&schemaId=18&projectId=14382&schemaKey=C1.1.1
    rf"{R_CHAPTER_PREFIX}.*?(management[’s]*\s*)?discussion\s*and\s*analysis(\s*of\s*.+)?{R_CHAPTER_SUFFIX}",
]
# 管理层报告
# https://jura-uat.paodingai.com/#/hkex/cg-report-checking/report-review/251295?fileId=80255&schemaId=28&rule=E(d)(iv)-1-How%20responsibility%20met%20in%20review%20of%20risk%20management%20and%20internal%20control
R_MANAGEMENT_TITLES = [
    r"STATEMENT.*MANAGEMENT",
    r"MANAGEMENT.*STATEMENT",
]
R_INED_REPORT_TITLES = [r"Independent\s*Auditor[’'‘]s\s*Report", r"report\s*of\s*(the\s*)?Independent\s*Auditor"]
# 附录
R_APPENDIX_TITLES = [r"^APPENDIX\s*([A-Z]|[IVX]{1,4}|\d+)\s.+$"]

R_LETTER_BOARD_TITLES = [r"^LETTER\s*FROM\s*THE\s*BOARD$"]
# http://100.64.0.105:55647/#/project/remark/259939?treeId=37651&fileId=69202&schemaId=18&projectId=17&schemaKey=C7.1 index=150
R_REVIEW_CCT = [r"^review\s*of\s*continuing\s*connected\s*transactions$"]

# 所有root章节，严格模式匹配
ALL_CHAPTER_TITLES = [
    (r"" if title.startswith((R_CHAPTER_PREFIX, "^")) else R_CHAPTER_PREFIX)
    + title
    + (r"" if title.endswith((R_CHAPTER_SUFFIX, "$")) else R_CHAPTER_SUFFIX)
    for title in [
        *R_CG_CHAPTER_TITLES,
        *R_DR_CHAPTER_TITLES,
        *R_NOTES_CHAPTER_TITLES,
        *R_ESG_CHAPTER_TITLES,
        *R_CI_CHAPTER_TITLES,
        *R_OI_CHAPTER_TITLES,
        *R_MANAGEMENT_TITLES,
        *R_INED_REPORT_TITLES,
        *R_MDA_CHAPTER_TITLES,
        *R_APPENDIX_TITLES,
        *R_LETTER_BOARD_TITLES,
        *R_REVIEW_CCT,
    ]
]
P_ALL_CHAPTER_TITLES = PatternCollection(ALL_CHAPTER_TITLES, flags=re.I)
P_COLLECTION_NOTE_CHAPTER = PatternCollection(R_NOTES_CHAPTER_TITLES, flags=re.I)

# 句号 前后都是大写字母不参与分割
R_PERIOD = r"(?<!\bMadam\b)(?<!\b(Prof|Miss|Corp)\b)(?<!\b(Mr|Ms|Dr|[NnCc][Oo])\b)(?<![\"“”(（][A-Za-z])(?<!\b(?i:ltd|etc|inc|cie|Mrs)\b)(?<![ .][A-Za-z])(?<![(（].)(?<!^.)(?<!^.{2})(?<!^[IVX]{3})(?<!^[IVX]{4})\.($|\s(?![a-z]))"
# 使用SplitBeforeMatch时，匹配段落中的句号或分号
# https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/3168#note_430699 TODO 若LR17.09这种乱码问题解决，把分号前后的否定限制删除
P_SEN_SEPARATOR = re.compile(rf"\s*({R_PERIOD}|(?<!\.0\d[(（]\d[)）])[;；](?!LR\d{{2}}))\s*")
# 匹配段落中的句号
P_PERIOD_SEPARATOR = re.compile(R_PERIOD)

# 货币
R_CURRENCY = r"(?<!['‘’])\b([SC]|HK[DS]?|RMB|Yuan|CNY|VND|GPY|USD?|EUR|JPY|GBP|AUD|NZD|SGD|CHF|CAD|MYR|RUB|ZAR|KRW|AED|SAR|HUF|PLN|DKK|SEK|NOK|TRY|MXN|THB)[$¥€￡]?"
P_NON_WORDS = re.compile(r"[^\w\s]")

# 主要证券交易所
R_EXCHANGE = r"\b(NYSE|NASDAQ|TSX|TSXV|B3|LSE|FWB|SIX|TSE|SSE|SZSE|HKEX|NSE|KRX|ASX)\b"
