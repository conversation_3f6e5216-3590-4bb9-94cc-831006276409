import copy
import re
from collections import defaultdict
from dataclasses import dataclass
from functools import cached_property
from typing import Self

from remarkable.common.common_pattern import (
    ALL_CHAPTER_TITLES,
    INVALID_FIRST_ELE_PATTERN,
    P_CHAPTER_PREFIX,
    P_CONTINUED,
    P_FOLLOW_PREFIX,
    P_NOTES,
    R_MIDDLE_DASH,
    R_MIDDLE_DASHES,
    R_NOTE_PREFIX,
    R_PREFIX_NUM,
    R_SPECIAL_PREFIX,
)
from remarkable.common.constants import PDFInsightClassEnum
from remarkable.common.pattern import PatternCollection
from remarkable.common.util import P_CHNS, clean_txt, group_cells
from remarkable.predictor.eltype import ElementClassifier, ElementType

"""
Independent Non-executive
"""
P_ONLY_CLOSE_PREFIX = re.compile(r"^([a-z]|[ivx]{1,4}|[1-9]\d{0,2})[）)]")
P_PARA_START = PatternCollection(
    [
        r"(?i)^\s*notes?[：:]\s*$",
        r"^(Mr|Ms|Dr|Prof|Mdm|Mass|Mrs)\.",
        rf"(?i)^(Independent\s*)?((Non{R_MIDDLE_DASH})?executive\s*)?directors?$",
        r"^(INEDs|CEO|Chairm[ae]n|executive\s*and\s*chairman|chief\s*executive\s*(\s*officer)?)$",
        P_NOTES,
        rf"^({R_SPECIAL_PREFIX}|{R_NOTE_PREFIX})",
        rf"^(S(?i:ECTION)|C(?i:HAPTER)|I(?i:TEM)|P(?i:ART)|(?i:stage))\s*(?i:{R_PREFIX_NUM})",
        r"^[(（]?([a-zA-Z]|[ivx]{1,4}|[IVX]{1,4}|[1-9]\d{,2}(?!\d))([•)）]|\.\s)",
        r"^[1-9]\d?(\.[1-9]\d?){1,3}\s+",
        r"(?i)https?://",
        # 邮箱
        r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$",
        # http://************:55647/#/project/remark/233167?treeId=4194&fileId=66483&schemaId=15&projectId=17&schemaKey=B77 P334 3583
        r"^None\.$",
        r"(?i)^unit[:：]",
        # http://************:55647/#/project/remark/233191?treeId=10935&fileId=66487&schemaId=15&projectId=17&schemaKey=B77 P11
        rf"^(Fig|Chart|Table)[.\s]*[{R_MIDDLE_DASHES}\d.]+$",
    ]
)
P_TITLE_PREFIX = PatternCollection(
    [r"^[1-9]\d?(\.[1-9]\d?){1,3}\.?$", r"^([a-zA-Z]|[ivx]{1,4}|[IVX]{1,4}|[1-9]\d{,2})\.?$"]
)
# 合并后能匹配上这个正则，认为需要合并，注意这里的正则必须带起止符^$，否则可能误匹配到句子
P_ROOT_CHAPTER_TITLES = PatternCollection(ALL_CHAPTER_TITLES, flags=re.I)
PREP_WORDS = ("and", "or", "&", "of", "at", "to", "by", "for")


@dataclass
class PdfinsightElement:
    pdfinsight_hash: str
    element: dict

    def __hash__(self):
        return hash((self.pdfinsight_hash, self.index))

    def __eq__(self, other):
        if isinstance(other, PdfinsightElement):
            return self.pdfinsight_hash == other.pdfinsight_hash and self.index == other.index
        return False

    @property
    def index(self):
        return self.element["index"]

    @property
    def class_(self):
        return self.element["class"]

    @property
    def type_(self):
        return self.element["type"]

    @property
    def text(self):
        return self.element.get("text") or ""

    @property
    def chars(self):
        return self.element.get("chars") or []

    @property
    def page(self):
        return self.element["page"]

    @property
    def outline(self):
        return self.element.get("outline") or [0.0, 0.0, 0.0, 0.0]

    @property
    def position(self):
        if pos := self.element.get("position"):
            return tuple(pos)
        return None

    @property
    def page_merged_paragraph(self):
        return self.element.get("page_merged_paragraph")

    @property
    def is_paragraph(self):
        return self.class_ == PDFInsightClassEnum.PARAGRAPH.value

    @cached_property
    def is_multi_line(self):
        return is_multi_line(self.element)

    @cached_property
    def first_line_text(self):
        return "".join([c["text"] for c in first_line_chars(self.chars)])

    @cached_property
    def outline_of_first_line(self):
        return outline_of_first_line(self.element)

    @cached_property
    def outline_of_last_line(self):
        return outline_of_last_line(self.element)

    @cached_property
    def left_of_second_line(self):
        return outline_left_of_second_line(self.element)

    @cached_property
    def is_para_start(self):
        """
        判断是否为段落最开始:
        1. 匹配一些特定章节正则
        2. 前6个字符之间有width特别宽的空格或字符(可能空格未识别到）
        """
        if P_PARA_START.nexts(self.text):
            return True
        chars = first_line_chars(self.chars) if self.is_multi_line else self.chars
        avg_width = get_avg_width(chars)
        for i, char in enumerate(chars[1:], start=1):
            if i > 5:
                break
            space_width = box_left(char) - box_right(chars[i - 1])
            if space_width > avg_width * 2:
                return True
            if space_width <= 1:
                continue
            if self.text[i] == " ":
                return P_TITLE_PREFIX.nexts(self.text[:i])
            return False
        return False


@dataclass
class FontStyle:
    """
    pdf识别到的文字样式
    """

    # 样式id
    # style_id: str
    # 字号
    fontsize: int
    # 字体颜色
    fontcolor: int
    # 字体
    fontname: str = None
    # 是否为斜体
    italic: bool = False
    # 是否加粗
    bold: bool = False
    # 是否浅色
    light: bool = False
    # 边框颜色，不准确：http://************:55647/#/project/remark/234077?treeId=37672&fileId=66635 index=1251和1251值相同
    # stroking_color: int
    # # 全部单词大写
    # is_upper: bool = False
    # 背景颜色（可能会有背景图）
    # bg_color: int

    def __hash__(self):
        return hash(
            (
                self.fontname,
                self.fontsize,
                self.fontcolor,
                self.italic,
                self.bold,
                self.light,
            )
        )

    def __eq__(self, other):
        # if self.style_id == other.style_id:
        #     return True
        return hash(self) == hash(other)

    def similarity(self, other):
        if not other or self.fontsize != other.fontsize or self.fontcolor != other.fontcolor or self.bold != other.bold:
            return False
        return self.fontname.lower().split("+")[-1] == other.fontname.lower().split("+")[-1]

    @property
    def without_color(self):
        return self.fontsize, self.fontname, self.italic, self.bold, self.light

    @property
    def size_and_color(self) -> Self:
        return FontStyle(fontsize=self.fontsize, fontcolor=self.fontcolor)

    def is_normal_style(self, body_font_style: Self):
        """
        当前样式是否为正文样式
        """
        if not body_font_style:
            return False
        return self.fontsize == body_font_style.fontsize and self.fontcolor == body_font_style.fontcolor


def is_sentence_end(text: str):
    if not (text := text.strip()):
        return False
    return text[-1] in ".。;；"


def box_left(char, key="font_box"):
    return char[key][0]


def box_right(char, key="font_box"):
    return char[key][2]


def box_top(char, key="font_box"):
    return char[key][1]


def box_bottom(char, key="font_box"):
    return char[key][-1]


def first_line_chars(chars):
    """
    取多行元素块的第一行
    """
    if not chars:
        return []
    end_index = 0
    for i, char in enumerate(chars[1:], start=1):
        if char["box"][0] < chars[i - 1]["box"][0]:
            end_index = i
            break
    # http://************:55647/#/project/remark/233958?treeId=5370&fileId=66615&schemaId=15&projectId=17&schemaKey=B64 元素块3767只有一行，但type是PARAGRAPH_2
    return chars[:end_index] if end_index > 0 else chars


def last_line_chars(chars):
    """
    取多行元素块的最后一行
    """
    if not chars:
        return []
    start_index = 0
    for i, char in enumerate(chars[1:], start=1):
        if char["box"][0] < chars[i - 1]["box"][0]:
            start_index = i
    return chars[start_index:]


def first_line_text(element):
    return "".join([c["text"] for c in first_line_chars(element["chars"])])


def get_avg_width(chars):
    word_chars = [ch for ch in chars if ch["text"]]
    return round(sum(box_right(ch) - box_left(ch) for ch in word_chars) / len(word_chars), 2)


def get_avg_height(chars):
    word_chars = [ch for ch in chars if ch["text"]]
    return round(sum(box_bottom(ch) - box_top(ch) for ch in word_chars) / len(word_chars), 2)


def is_multi_line(element):
    """
    判断元素块是否为多行元素块，type==PARAGRAPH_2不准确
    TODO 旋转页面可能误判
    """
    if not (chars := element.get("chars")) or element["type"] == "PARAGRAPH_1":
        return False
    for i, char in enumerate(chars[1:], start=1):
        if char["box"][0] < chars[i - 1]["box"][0]:
            return True
    return False


def outline_of_chars(chars) -> list[float]:
    if not chars:
        return [0.0, 0.0, 0.0, 0.0]
    # outline可能不准，用char
    return [box_left(chars[0]), min(box_top(c) for c in chars), box_right(chars[-1]), max(box_bottom(c) for c in chars)]


def outline_of_first_line(element) -> list[float] | None:
    """
    取多行段落中，最后一行文字的最右侧
    """
    elt_chars = element["chars"]
    if not elt_chars:
        return [0.0, 0.0, 0.0, 0.0]
    if is_multi_line(element):
        return outline_of_chars(first_line_chars(elt_chars))
    return outline_of_chars(elt_chars)


def outline_of_last_line(element) -> list[float]:
    elt_chars = element["chars"]
    if not elt_chars:
        return [0.0, 0.0, 0.0, 0.0]
    if is_multi_line(element):
        return outline_of_chars(last_line_chars(elt_chars))
    return outline_of_chars(elt_chars)


def outline_left_of_last_line(element) -> float:
    """
    取多行段落中，最后一行文字的最左侧
    """
    return outline_of_last_line(element)[0]


def outline_right_of_last_line(element) -> float:
    """
    取多行段落中，最后一行文字的最右侧
    """
    return outline_of_last_line(element)[2]


def outline_left_of_second_line(element) -> float:
    """
    取多行段落中，第二行文字的最左侧
    """
    if not (chars := element.get("chars")):
        return 0.0
    for i, char in enumerate(chars[1:], start=1):
        if char["box"][0] < chars[i - 1]["box"][0]:
            return box_left(chars[i])
    return 0.0


def calc_line_spacing(first_element, second_element):
    """
    计算行间距
    """
    # index小的元素块为被减元素块
    if first_element["index"] < second_element["index"]:
        real_first, real_second = first_element, second_element
    else:
        real_first, real_second = second_element, first_element
    if not real_first.get("chars") or not real_second.get("chars"):
        return 0.0
    # 第一个元素块取最后一行，求平均bottom
    first_chars = last_line_chars(real_first["chars"]) if is_multi_line(real_first) else real_first["chars"]
    first_bottom = sum(box_bottom(char) for char in first_chars) / len(first_chars)
    # 第二个元素块取第一行，求平均bottom
    second_chars = first_line_chars(real_second["chars"]) if is_multi_line(real_second) else real_second["chars"]
    second_bottom = sum(box_bottom(char) for char in second_chars) / len(second_chars)
    return round(second_bottom - first_bottom, 2)


def outline_right_of_title_prefix(element: dict) -> float:
    """
    如果element包含标题前缀，计算标题前缀最后一个字符的outline右侧，考虑空格
    """
    chars = first_line_chars(element.get("chars")) if is_multi_line(element) else element["chars"]
    if not chars:
        return 0.0
    text = "".join(c["text"] for c in chars)
    if match := (P_CHAPTER_PREFIX.search(text) or P_FOLLOW_PREFIX.search(text)):
        index = match.end()
        if index >= len(chars) or index > 6:
            return 0.0
        return box_right(chars[index])
    return 0.0


def is_unclosed_text(text: str) -> bool:
    """
    判断当前段落中是否有未闭合的括号/引号
    """
    # 考虑章节序号 a)
    count_close = text.count("）") + text.count(")")
    if P_ONLY_CLOSE_PREFIX.search(text):
        count_close -= 1
    if text.count("(") + text.count("（") != count_close:
        return True
    # 识别原因，双引号可能识别有误， 判断双引号是否为双数
    if (text.count("“") + text.count("”")) % 2 != 0:
        return True
    return False


def get_element_styles(element):
    if not element.get("chars"):
        return set()
    return {char["style"] for char in element["chars"]}


def max_col_of_tbl_element(element):
    return max(int(key.split("_")[1]) for key in element["cells"])


def first_space_index(element):
    """
    章节序号中的空格识别丢了，这里根据font_box找出章节序号后的空格所在index
    """
    text = element["text"]
    if not text or not element.get("chars"):
        return 0
    chars = element["chars"]
    no_space_chars = [c for c in chars if c["text"].strip()]
    # http://************:55647/#/project/remark/294275?treeId=6009&fileId=70752&schemaId=18&projectId=17&schemaKey=C2.4&page=229 index=3951
    avg_word_width = sum(box_right(c) - box_left(c) for c in no_space_chars) / len(no_space_chars)
    last_char_right = box_right(chars[0])
    prefix_chars = []
    if first_char := chars[0]["text"].strip():
        prefix_chars.append(first_char)
    for idx, char in enumerate(chars[1:], start=1):
        # 长度超过1且均为英文字符但不是罗马字符，则认为不是章节序号
        # stock=00016，year=2023, page=72, index=810: Mainland Property Business没有序号被识别为Main
        if idx > 5 or len(prefix_chars) > 1 and all(c.isalpha() and c not in ("i", "v", "x") for c in prefix_chars):
            break
        stripped_char = char["text"].strip()
        if len(char["text"]) >= 1 and not stripped_char:
            return 0
        left, _, right, _ = char["font_box"]
        # TODO 这里门限值放的相对较小，后续视情况可调大一点（太小容易误判）
        if stripped_char and left - last_char_right > avg_word_width:
            return idx
        if stripped_char:
            prefix_chars.append(stripped_char.lower())
        last_char_right = right
    return 0


def add_space_to_element(element):
    """
    原始元素块缺少章节序号后的空格，需要补上
    https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/3168#note_494834
    """
    if space_index := first_space_index(element):
        new_elt = copy.copy(element)
        # 造一个空格char
        chars = new_elt["chars"]
        space_char = copy.deepcopy(chars[space_index])
        prev_char = chars[space_index - 1]
        box_left, box_top, _, box_bottom = space_char["box"]
        space_char["text"] = " "
        space_char["box"] = [prev_char["box"][2] + 0.1, box_top, box_left - 0.1, box_bottom]
        font_left, font_top, _, font_bottom = space_char["font_box"]
        space_char["font_box"] = [box_right(prev_char) + 0.1, font_top, font_left - 0.1, font_bottom]
        # 补文本
        new_elt["text"] = new_elt["text"][:space_index] + " " + new_elt["text"][space_index:]
        # 补char
        new_elt["chars"] = chars[:space_index] + [space_char] + chars[space_index:]
        return new_elt
    return None


def element_text(ele):
    text = ""
    ele_type = ElementClassifier.get_type(ele)
    if ElementClassifier.like_paragraph(ele):
        text = ele.get("text", "")
    elif ele_type in (ElementType.TABLE_KV, ElementType.TABLE_ROW, ElementType.TABLE_TUPLE):
        cells_by_row, _ = group_cells(ele["cells"])
        row_texts = []
        for cells in cells_by_row.values():
            row_texts.append("|".join([cell["text"] for cell in cells.values()]))
        text = "\n".join(row_texts)
    return text


def get_page_column(pdfinsight, page, elements) -> tuple[float | None, bool | None]:
    # TODO add class page to pdfinsight reader
    if not pdfinsight:
        return None, None
    has_chinese_ele = []
    for element in elements:
        if element.get("chinese_chars") and len(element.get("text", "")) > 15:  # 防止小标题等干扰判断
            has_chinese_ele.append(element)
    if not has_chinese_ele:
        return None, None
    columns_info = pdfinsight.data["columns"]
    page_column_info = columns_info.get(str(page), {})
    if page_column_info:
        page_outline = page_column_info["outline"]
        column_positions = page_column_info["grid"]["columns"]
        column_position = (
            column_positions[0] if column_positions else None
        )  # 默认取第一个 认为只分成两栏 更多的先不处理
        if column_position:
            column_position = page_outline[0] + column_position  # 根据相对位置计算绝对位置
            is_left_chinese = has_chinese_ele[0]["outline"][0] < column_position
            return column_position, is_left_chinese
    return None, None


def get_page_para_outline(paras, column_position=0, is_left_chinese=False):
    """计算同页连续段落的外框"""
    left = min(e["outline"][0] for e in paras)
    top = min(e["outline"][1] for e in paras)
    right = max(e["outline"][2] for e in paras)
    bottom = max(e["outline"][3] for e in paras)
    if column_position:
        if is_left_chinese:
            left = column_position if left < column_position else left
        else:
            right = column_position if column_position < right else right
    return [left, top, right, bottom]


def is_invalid_syllabus(syllabus_id):
    return syllabus_id is None or syllabus_id == -1


def is_valid_first_ele_in_page(element, pdfinsight):
    page_elements = pdfinsight.find_elements_by_page(element["page"])
    if not page_elements:
        return True
    # 前两个元素块
    idxes = [page_elements[0]["index"]]
    if len(page_elements) > 1:
        idxes.append(page_elements[1]["index"])
    if element["index"] not in idxes:
        return True
    if INVALID_FIRST_ELE_PATTERN.nexts(clean_txt(element.get("text", ""))):
        return False
    return True


def classify_by_page(elements, pdfinsight=None):
    page_elements = defaultdict(list)
    for element in elements:
        page_merged_paragraph = element.get("page_merged_paragraph", {})
        if page_merged_paragraph and page_merged_paragraph.get("page"):
            paragraph_indices = page_merged_paragraph.get("paragraph_indices", [])
            element_index = element["index"]
            if page_merged_paragraph["page"] > element["page"]:
                # 处理跨页段落的下半部分 添加一个fake_element 仅用于计算大框
                below_element_index = min(idx for idx in paragraph_indices if idx > element_index)
                fake_element = {
                    "fragment": True,
                    "position": [0, 0],
                    "index": below_element_index,
                    "type": "fake_element",
                    "class": "PARAGRAPH",
                    "outline": page_merged_paragraph["outline"],
                }
                page_elements[element["page"]].append(element)
                page_elements[page_merged_paragraph["page"]].append(fake_element)
            elif page_merged_paragraph["page"] < element["page"]:
                # 处理跨页段落的上半部分 添加一个fake_element
                above_element_index = max(idx for idx in paragraph_indices if idx < element_index)
                if above_element_index in [i["index"] for i in page_elements[page_merged_paragraph["page"]]]:
                    # 已经添加过 无需重复添加
                    continue
                above_element = {}
                if pdfinsight:
                    try:
                        _, above_element = pdfinsight.find_element_by_index(above_element_index)
                    except IndexError:
                        above_element = None
                    if not above_element:
                        above_element = {}
                if not above_element:
                    element_text = page_merged_paragraph.get("text", "")
                    above_element = {
                        "type": "fake_element",
                        "position": [0, 0],
                        "index": above_element_index,
                        "text": element_text,
                        "class": "PARAGRAPH",
                        "outline": page_merged_paragraph["outline"],
                    }
                if above_element:
                    if prev_above_elements := page_elements[page_merged_paragraph["page"]]:
                        above_element["position"] = prev_above_elements[-1]["position"]
                    page_elements[page_merged_paragraph["page"]].append(above_element)
                # 下面element的文本 在后面会根据 fragment 过滤掉
                page_elements[element["page"]].append(element)
            else:
                # 分栏 左右两侧的需要合并的元素块
                page_elements[element["page"]].append(element)
        else:
            page_elements[element["page"]].append(element)

    page_elements = dict(sorted(page_elements.items(), key=lambda item: item[0]))
    return page_elements


def is_continue_line(text_, next_text_):
    """
    当前行以and/or/of/by/at/to/by等结尾
    或下一行以and/or/of/by/to/by等开始
    """
    if text_ in PREP_WORDS or next_text_ in PREP_WORDS:
        return True
    if next_text_.startswith(tuple(f"{p} " for p in PREP_WORDS)):
        return True
    return text_.endswith(tuple(f" {p}" for p in PREP_WORDS)) and not text_.endswith(
        ("; and", ";and", "；and", "； and")
    )


def is_end_line(
    element: dict,
    next_element: dict,
    merged_text: str,
    page_right: float,
    is_first: bool,
    is_chapter: bool,
    is_page_first: bool,
    is_normal_size: bool,
) -> bool:
    """
    根据当前及下一个元素块判断当前元素块是不是段落最后一行
    返回True表示已经是最后一行，不需要再向下判断
    """

    def is_para_start(elt_):
        """
        判断是否为段落最开始:
        1. 匹配一些特定章节正则
        2. 前6个字符之间有width特别宽的空格或字符(可能空格未识别到）
        """
        if P_PARA_START.nexts(elt_["text"]):
            return True
        is_multi = is_multi_line(elt_)
        chars = first_line_chars(elt_.get("chars")) if is_multi else elt_["chars"]
        avg_width = get_avg_width(chars)
        for i, char in enumerate(chars[1:], start=1):
            if i > 5:
                break
            space_width = box_left(char) - box_right(chars[i - 1])
            if space_width > avg_width * 2:
                return True
            if space_width <= 1:
                continue
            if elt_["text"][i] == " ":
                return P_TITLE_PREFIX.nexts(elt_["text"][:i])
            return False
        return False

    # 1. 空句子或句号结尾或中文或包含continue
    text, chars = element.get("text"), element.get("chars")
    if is_sentence_end(text) or P_CHNS.search(text) or P_CONTINUED.search(text):
        return True
    avg_width = get_avg_width(last_line_chars(chars))
    # 2. 冒号/分号结尾且距离右侧较远，终止
    last_line_right = outline_right_of_last_line(element)
    if text[-1] in ":;：；" and page_right - last_line_right > avg_width * 1.5:
        return True
    # 3. 下个元素块的第一行包含了中文
    next_text, next_chars = next_element["text"], next_element.get("chars")
    if not next_chars or P_CHNS.search(first_line_text(next_element)):
        return True
    # 4. 行距过远(超过1.5倍大号字体或2倍普通字体距离)或下一行是章节的开始
    line_spacing, avg_height = calc_line_spacing(element, next_element), get_avg_height(chars)
    if line_spacing > avg_height * (2 if is_normal_size else 1.5) or is_para_start(next_element):
        return True
    # 5. 第二行包含continued或者合并后的文本可以严格匹配一级章节标题
    if P_CONTINUED.search(next_text) or (is_page_first and P_ROOT_CHAPTER_TITLES.nexts(f"{merged_text} {next_text}")):
        return False
    # 6. 都是单行，章节中包含of/and/or等，直接合并
    # http://************:55647/#/project/remark/306996?projectId=29&treeId=45119&fileId=71356&schemaId=32&page=78
    end_text, next_end_text = text.strip(), next_text.strip()
    is_multi, next_is_multi = is_multi_line(element), is_multi_line(next_element)
    if (
        (is_chapter or not is_normal_size)
        and (not is_multi and not next_is_multi)
        and is_continue_line(end_text.split()[-1].lower(), next_end_text.split(maxsplit=1)[0].lower())
    ):
        return False
    # 7. 右侧边接近或者大于下一行侧边，同时缺少括号、或下一行以句号结尾且首字母是小写
    next_left, _, next_right, _ = outline_of_first_line(next_element)
    if (last_line_right > next_right or abs(last_line_right - next_right) < avg_width) and (
        text[-1] in ",，" or is_unclosed_text(merged_text) or (is_sentence_end(next_text) and next_text[0].islower())
    ):
        return False
    # 8. 当前元素块最后一行的右侧+下一行第一个单词的宽度 < page_right
    space_index = next_text.find(" ")
    first_word_chars = next_chars[: space_index + 1] if space_index != -1 else next_chars
    words_width = max(ch["box"][2] for ch in first_word_chars) - min(ch["box"][0] for ch in first_word_chars)
    if last_line_right + words_width + avg_width < page_right:
        return True
    # 同时，换行后如果只有一个单词，且长度较小同时只有一个大写字母（单词太长可能是未识别到空格）
    # http://************:55647/#/project/remark/233167?treeId=4194&fileId=66483&schemaId=15&projectId=17&schemaKey=B77&page=230 index=2588
    if space_index < 0 and len(next_text) < 20:
        return False
    if is_continue_line(end_text, next_end_text):
        return False
    # 9. 不判断侧边距：章节（是否要判断级别）、大号字体
    if is_chapter or not is_normal_size:
        return False
    # 10. 当前行有缩进，且与下一行的缩进一致，说明不能合并
    base_left = outline_of_first_line(element)[0]
    if is_multi:
        cur_indent = base_left - outline_left_of_second_line(element)
        if cur_indent > 2 * avg_width:
            if next_is_multi:
                next_indent = next_left - outline_left_of_second_line(next_element)
            else:
                next_indent = next_left - base_left
            if abs(cur_indent - next_indent) <= avg_width:
                return True
    # 11. 当前行与下一行左侧边距相差太远
    width_ = 10 * avg_width if is_first else avg_width
    if abs(next_left - base_left) > width_:
        return True
    # 12. 当前行距离右侧边太远
    return page_right - max(box_right(c) for c in chars if c["text"]) > avg_width * 5
