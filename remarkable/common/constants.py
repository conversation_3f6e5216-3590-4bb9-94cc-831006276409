import logging
import re
from collections import named<PERSON>ple
from datetime import datetime
from enum import Enum, EnumMeta, IntEnum, StrEnum, unique
from functools import lru_cache
from pathlib import Path
from typing import List, Tu<PERSON>, Union

from typing_extensions import Self, Type<PERSON>lias

from remarkable.config import get_config, project_root

SCRAPPING_SUMMARY_DIR = Path(get_config("web.data_dir")) / "scrapping_summary"
HKEX_TEXT2EMBEDDING_FILE_PATH = Path(project_root) / "data" / "model" / "hkex_text2embedding.pkl"
INVALID_STOCK_CODE_MSG = "The stock code you entered is not in the scope of Jura."
DELIST_COMPANY_MSG = INVALID_STOCK_CODE_MSG
INVALID_REPORT_YEAR_MSG = "The year you entered is outside the scope of Jura."
INVALID_RULE_OR_SUB_GROUPS = "Invalid rules or sub groups."
DEFAULT_REPORT_YEAR = datetime.now().year - 5
DEFAULT_FUNDRAISING_EVENT_TYPE = "No Event"
API_PREFIX_V1 = "/api/v1"
API_PREFIX_V2 = "/api/v2"
MIN_MODIFY_ANSWER_ACTION = 1020
TIMEZONE_OFFSET = 8 * 60 * 60
logger = logging.getLogger(__name__)


class _EnumMeta(EnumMeta):
    def __new__(metacls, cls, bases, classdict, **kwds):
        enum_cls = super().__new__(metacls, cls, bases, classdict, **kwds)
        enum_cls._phrase2enum_map_ = {}
        enum_cls._label2enum_map_ = {}
        enum_cls._abbr2enum_map_ = {}
        for enum in enum_cls:
            enum_cls._phrase2enum_map_[enum.phrase.lower()] = enum
            enum_cls._label2enum_map_[enum.label.lower()] = enum
            enum_cls._abbr2enum_map_[enum.abbr.lower()] = enum
        return enum_cls

    def __contains__(self, item):
        if item in self._value2member_map_:
            return True
        item = str(item).lower()
        return item in self._phrase2enum_map_ or item in self._label2enum_map_ or item in self._abbr2enum_map_


class EnumMixin:
    _phrase2enum_map_ = None
    _label2enum_map_ = None
    _abbr2enum_map_ = None

    @classmethod
    def phrase_to_enum(cls, phrase: str) -> Self | None:
        return cls._phrase2enum_map_.get((phrase or "").lower())

    @classmethod
    def label_to_enum(cls, label: str) -> Self | None:
        return cls._label2enum_map_.get((label or "").lower())

    @classmethod
    def abbr_to_enum(cls, abbr: str) -> Self | None:
        return cls._abbr2enum_map_.get((abbr or "").lower())

    @classmethod
    def phrase_to_enums(cls, phrase: Union[str, None, List[str]]) -> List[Self]:
        if not phrase:
            return []
        if isinstance(phrase, str):
            phrase = [phrase]

        enums = []
        for p in phrase:
            if enum := cls.phrase_to_enum(p):
                enums.append(enum)
            else:
                logger.warning(f'Invalid phrase: "{p}"')
        return enums

    @classmethod
    def abbreviations(cls) -> Tuple[str, ...]:
        return tuple(i.abbr for i in cls)

    @classmethod
    def labels(cls) -> Tuple[str, ...]:
        return tuple(i.label for i in cls)

    @classmethod
    def phrases(cls) -> Tuple[str, ...]:
        return tuple(i.phrase for i in cls)


class IntEnumMixin(IntEnum):
    def __new__(cls, value, phrase="", label="", abbr=""):
        obj = int.__new__(cls, value)
        obj._value_ = value
        obj.phrase = phrase
        obj.label = label or phrase
        obj.abbr = abbr or phrase
        return obj


class StrEnumMixin(StrEnum):
    def __new__(cls, value, phrase="", label="", abbr=""):
        obj = str.__new__(cls, value)
        obj._value_ = value
        obj.phrase = phrase
        obj.label = label or phrase
        obj.abbr = abbr or phrase
        return obj


class IntEnumBase(EnumMixin, IntEnumMixin, metaclass=_EnumMeta):
    pass


class StrEnumBase(EnumMixin, StrEnumMixin, metaclass=_EnumMeta):
    pass


@unique
class AccuracyType(EnumMixin, IntEnum):
    PROMPT = 1  # 默认，最终提取答案的正确率
    CRUDE = 2  # 位置推荐的正确率


@unique
class AccuracyTest(EnumMixin, IntEnum):
    TRAIN = 1  # 默认，训练集正确率
    TEST = 2  # 测试集正确率


@unique
class AnswerStatus(EnumMixin, IntEnum):
    INVALID = 0
    VALID = 1

    # 用户答题过程中暂存在服务器上的答案，在正式确认前该答案不算作答案。
    # PDF表格标注要求客户端每20s自动提交一次暂存答案给服务端，最后用户手动确认。
    UNFINISHED = 2
    ARCHIVED = 3


@unique
class QuestionStatus(EnumMixin, IntEnum):
    TODO = 0  # 待作
    DOING = 1  # 正在答题
    FINISH = 2  # 答题完毕
    VERIFY = 3  # 已反馈
    DISACCORD = 4  # 答案不一致
    ACCORDANCE = 5  # 答案一致
    VERIFY_CONFIRMED = 6  # 管理员确认了反馈
    STANDARD_CONFIRMED = 10  # 正确答案已确定/冲突已处理


@unique
class AIStatus(EnumMixin, IntEnum):
    NOPREDICT = -1  # 不需要预测
    TODO = 0  # 排队中
    DOING = 1  # 预测中
    FAILED = 2  # 失败
    FINISH = 3  # 完成


@unique
class AnswerResult(EnumMixin, IntEnum):
    NONE = -1
    NOT_REACH_THRESHOLD = 0  # 等待凑足答题人数
    CORRECT = 1  # 正确
    INCORRECT = 2  # 错误
    TOBE_JUDGED = 3  # 自动比较无法判断, 等待管理员判断


@unique
class AnswerType(EnumMixin, IntEnum):
    USER_DO = 1  # 用户创建的普通答案
    ADMIN_DO_1 = 2  # 普通用户答题完毕, 比较答案前, 管理员答题
    ADMIN_VERIFY = 3  # 管理员处理反馈增加的答案
    ADMIN_JUDGE = 4  # 管理员处理冲突增加的答案
    ADMIN_DO_2 = 5  # 题目已经处理完毕(一致或者已经设定标准答案)的情况下, 管理员答题


# TODO: AGM/POLL 描述未最终确定
# 暂定 {"agm": "AGM Circular", "poll": "AGM Poll Results"}
_COMMON_EXPORT_TABLE_TEMPLATE = """
{%- if kwargs is defined -%}
    {%- set excluded_keys = ['use_cache', 'non_compliance', 'list_status'] -%}
    {%- if "team_ids" in kwargs -%} {%- set _ = excluded_keys.append('stock_codes') -%} {%- endif -%}
    {%- set alias_map = {"ar" : "Annual Report", "esg": "ESG Report", "cg": "CG Report", "qr": "Result Announcement", "agm": "AGM Circular", "poll": "AGM Poll Results", "full": "Full Dataset", "summary": "Summary"} -%}
    [
        {%- for key, value in kwargs.items() if key not in excluded_keys -%}
            {%- if not loop.first %} | {% endif -%}
            {%- if key == 'doc_type' -%}
                "{{ key.replace('_', ' ').capitalize() }}": "{{ alias_map[value] }}"
            {%- elif key in ['date_from', 'date_to'] -%}
                "{{ key.replace('_', ' ').capitalize() }}": "{{ value|int|datetime('%Y-%m-%d') }}"
            {%- elif key == 'rules' -%}
                "Rules": "{% if value %}{{ value }}{% else %}ALL{% endif %}"
            {%- elif key == 'qr_types' -%}
                "Report Type(s)": "{{ value }}"
            {%- elif key == 'stock_codes' -%}
                "Issuer": "{{ value }}"
            {%- elif key == 'report_years' -%}
                "Financial Year(s)": "{{ value }}"
            {%- elif key == 'team_ids' -%}
                "Teams": "{{ value }}"
            {%- elif key == 'sub_groups' -%}
                "Sub-Group(s)": "{{ value }}"
            {%- elif key == 'ratio_output_type' -%}
                "Ratio Output Type": "{{ alias_map[value] }}"
            {%- else -%}
                "{{ key.replace('_', ' ').capitalize() }}": "{{ value.capitalize() }}"
            {%- endif -%}
        {%- endfor -%}
    ]
{%- endif -%}
"""


class HistoryAction(IntEnumBase):
    LOGIN = 1
    LOGIN_WRONG_PW = (
        34,
        "Failed",
        "to log into the system(Wrong password)",
        "Login",
    )
    LOGIN_INACTIVE = (
        35,
        "Failed",
        "to log into the system(Inactive user)",
        "Login",
    )
    LOGOUT = 45  # 登出
    OPEN_PDF = 2  # 查看的报告
    SUBMIT_ANSWER = 3
    ADMIN_VERIFY = 4
    ADMIN_JUDGE = 5
    CREATE_USER = (
        6,
        "Created",
        'new user "{{ user_name }}" with user role "{{ role }}"',
        "Created user",
    )
    ENABLE_USER = (
        38,
        "Enabled",
        'user "{{ user_name }}"',
        "Edited user status",
    )
    DISABLE_USER = (
        39,
        "Disabled",
        'user "{{ user_name }}"',
        "Edited user status",
    )
    RESET_USER_PASSWORD = (
        40,
        "Reset",
        'password function for "{{ user_name }}"',
        "Reset password",
    )
    MODIFY_USER_ROLE = (
        41,
        "Edited",
        'user role of "{{ user_name }}" from "{{ from_role }}" to "{{ to_role }}"',
        "Edited user role",
    )
    MODIFY_USER_EMAIL = (
        44,
        "Edited",
        'email of user "{{ user_name }}" from "{{ old_email }}" to "{{ new_email }}"',
        "Edited user email",
    )
    MODIFY_USER = 7
    DELETE_USER = 8
    CREATE_MOLD = 9
    MODIFY_MOLD = 10
    DELETE_MOLD = 11
    CREATE_PROJECT = 12
    MODIFY_PROJECT = 13
    DELETE_PROJECT = 14
    CREATE_TREE = 15
    MODIFY_TREE = 16
    DELETE_TREE = 17
    CREATE_FILE = 18
    MODIFY_FILE = 19
    DELETE_FILE = 20
    CREATE_TAG = 21
    MODIFY_TAG = 22
    DELETE_TAG = 23
    UPLOAD_ZIP = 24
    CREATE_TRAINING_DATA = 25  # 新建`导出训练数据`任务
    EXPORT_TRAINING_DATA = 26  # 导出训练数据
    DELETE_TRAINING_DATA = 27  # 删除`导出训练数据`任务
    TRAINING_SCHEMA = 28  # 训练模型
    UPDATE_COMPANY_INFO = 29  # 更新`hkex_companies_info`表

    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/628#note_166832
    # 这里的枚举值31是一个前缀，实际枚举值为31xxxx，详见：get_update_answer_enum()
    # 这里的模板是abbr，对应/hkex/user/audit-trail页面的`Operation type`列，用法见gen_operation_type()
    # 页面的`Detailed operation`见post_answer_template()
    UPDATE_RULE_ANSWER = (
        31,
        "Updated the rule answer",
        "",
        """
{%- if adjustment is defined and adjustment is not none -%}
    {%- if 'Disclosure' in adjustment -%}
Updated Disclosure Location
    {%- elif 'Compliance' in adjustment -%}
Updated Compliance Assessment
    {%- else -%}
Updated Manual Tagged Information
    {%- endif -%}
{%- else -%}
Updated Manual Tagged Information
{%- endif -%}""",
    )
    # 这里的枚举值32是一个前缀，实际枚举值为32xxxx，详见：get_update_answer_enum()
    # 这里的模板是abbr，对应/hkex/user/audit-trail页面的`Operation type`列，用法见gen_operation_type()
    # 页面的`Detailed operation`见post_answer_template()
    UPDATE_DISCLOSURE = (
        32,
        "Updated the disclosure",
        "",
        """
{%- if adjustment is defined and adjustment is not none -%}
    {%- if 'Disclosure' in adjustment -%}
Updated Disclosure Location
    {%- elif 'Compliance' in adjustment -%}
Updated Compliance/Red Flag Assessment
    {%- else -%}
Updated Manual Tagged Information
    {%- endif -%}
{%- else -%}
Updated Manual Tagged Information
{%- endif -%}""",
    )
    # 这里的枚举值31是一个前缀，实际枚举值为31xxxx，详见：get_update_answer_enum()
    # 这里的模板是abbr，对应/hkex/user/audit-trail页面的`Operation type`列，用法见gen_operation_type()
    # 页面的`Detailed operation`见post_answer_template()
    UPDATE_RATIO = (
        33,
        "Updated the ratio",
        "",
        """
{%- if adjustment is defined and adjustment is not none -%}
    {%- if 'Disclosure' in adjustment -%}
Updated Disclosure Location
    {%- elif 'Compliance' in adjustment -%}
Updated Compliance/Red Flag Assessment
    {%- else -%}
Updated Manual Tagged Information
    {%- endif -%}
{%- else -%}
Updated Manual Tagged Information
{%- endif -%}""",
    )
    # 这里的枚举值3300是一个前缀，实际枚举值为3300xxxx，详见：get_update_answer_enum()
    # 这里的模板是abbr，对应/hkex/user/audit-trail页面的`Operation type`列，用法见gen_operation_type()
    # 页面的`Detailed operation`见post_answer_template()
    UPDATE_AR_ESG = (
        3300,
        "Updated the AR ESG",
        "",
        """
{%- if adjustment is defined and adjustment is not none -%}
    {%- if 'Disclosure' in adjustment -%}
Updated Disclosure Classification
    {%- else -%}
Updated Manual Tagged Information
    {%- endif -%}
{%- else -%}
Updated Manual Tagged Information
{%- endif -%}""",
    )
    # 这里的枚举值3301是一个前缀，实际枚举值为3301xxxx，详见：get_update_answer_enum()
    # 这里的模板是abbr，对应/hkex/user/audit-trail页面的`Operation type`列，用法见gen_operation_type()
    # 页面的`Detailed operation`见post_answer_template()
    UPDATE_ESG = (
        3301,
        "Updated the ESG",
        "",
        """
{%- if adjustment is defined and adjustment is not none -%}
    {%- if 'Disclosure' in adjustment -%}
Updated Disclosure Classification
    {%- else -%}
Updated Manual Tagged Information
    {%- endif -%}
{%- else -%}
Updated Manual Tagged Information
{%- endif -%}""",
    )
    # 这里的枚举值102是一个前缀，实际枚举值为102xxxx，详见：get_update_answer_enum()
    # 这里的模板是abbr，对应/hkex/user/audit-trail页面的`Operation type`列，用法见gen_operation_type()
    # 页面的`Detailed operation`见post_answer_template()
    UPDATE_CG_ANSWER = (
        102,
        "Updated the CG answer",
        "",
        """
{%- if adjustment is defined and adjustment is not none -%}
    {%- if 'Disclosure' in adjustment -%}
Updated Disclosure Classification
    {%- else -%}
Updated Manual Tagged Information
    {%- endif -%}
{%- else -%}
Updated Manual Tagged Information
{%- endif -%}""",
    )
    # 访问页面
    LOAD_PAGE = (
        36,
        "Loaded",
        # NOTE: 多级页面的描述如果仅取最后一级的话，这个 op detail 可能会重复。
        "{{ system_function[-1].replace(' page', '') }} page",
        "Loaded page",
    )
    REVIEWED_RESULT_ANNOUNCEMENT = 42  # 业绩公告详情页
    REVIEWED_ANNUAL_REPORT = 43  # 年报详情页
    REVIEWED_CG_REPORT = 435  # CG详情页
    REVIEWED_ESG_REPORT = 436  # ESG详情页
    REVIEWED_AGM_REPORT = 437  # AGM详情页
    REVIEWED_POLL_REPORT = 438  # AGM POLL结果详情页

    EXPORTED_ACTIVITY_LOG = (
        46,
        "Downloaded",
        "activity log from user management",
        "Downloaded activity log",
    )
    EXPORTED_ACTIVITY_LOG_FAILED = (
        47,
        "Failed to download",
        "activity log from user management",
        "Downloaded activity log",
    )
    EXPORTED_USER_LIST = 48  # 导出用户列表

    EXPORTED_TABLE_RESULT_BY_RULE = (
        49,
        "Exported Table Result by Rule",
        _COMMON_EXPORT_TABLE_TEMPLATE,
        "Exported Table",
    )
    EXPORTED_TABLE_RESULT_BY_ISSUER = (
        50,
        "Exported Table Result by Issuer",
        _COMMON_EXPORT_TABLE_TEMPLATE,
        "Exported Table",
    )

    EXPORTED_TABLE_ANNUAL_BY_RULE = (
        51,
        "Exported Table Result by Rule",
        _COMMON_EXPORT_TABLE_TEMPLATE,
        "Exported Table",
    )
    EXPORTED_TABLE_ANNUAL_BY_ISSUER = (
        52,
        "Exported Table Result by Issuer",
        _COMMON_EXPORT_TABLE_TEMPLATE,
        "Exported Table",
    )
    EXPORTED_NON_COMPLIANT_ISSUERS = (
        53,
        "Exported List of Non-Compliant Issuers",
        _COMMON_EXPORT_TABLE_TEMPLATE,
        "Exported Table",
    )
    EXPORTED_TABLE_ESG_BY_ISSUER = 54
    EXPORTED_ESG_RESULT_BY_RULE = (
        55,
        "Exported Table Result by Rule",
        _COMMON_EXPORT_TABLE_TEMPLATE,
        "Exported Table",
    )
    EXPORTED_CG_RESULT_BY_RULE = (
        56,
        "Exported Table Result by Rule",
        _COMMON_EXPORT_TABLE_TEMPLATE,
        "Exported Table",
    )
    EXPORTED_AGM_RESULT_BY_RULE = (
        57,
        "Exported Table Result by Rule",
        _COMMON_EXPORT_TABLE_TEMPLATE,
        "Exported Table",
    )
    EXPORTED_POLL_RESULT_BY_RULE = (
        58,
        "Exported Table Result by Rule",
        _COMMON_EXPORT_TABLE_TEMPLATE,
        "Exported Table",
    )

    QUOTED_AI_RATIO = 60  # ratio 界面点击引用AI
    CLEARED_MANUAL_RATIO = 61  # ratio 界面点击清除按钮

    TEXT_IN_BOX = 70  # 画框取字

    AUTHENTICATION_SUCCESS = (80, "", "Successfully logged into the system(AD)", "Login")
    AUTHENTICATION_FAILURE = (82, "", "Failed to log into the system(AD)", "Login")

    UPLOAD_AR = (103, "Uploaded Annual Report", '"{{stock_code}}" "{{year}}"', "Uploaded Report")  # 上传年报
    UPLOAD_ESG = (104, "Uploaded ESG Report", '"{{stock_code}}" "{{year}}"', "Uploaded Report")  # 上传ESG报告

    CREATE_GROUP = (105, "Created a new group", '"{{group_name}}" in {{report_type}}', "Created new group")
    EDITE_GROUP_NAME = (
        106,
        "Edited group name",
        'from "{{group_name}}" to "{{new_group_name}}" in {{report_type}}',
        "Edited group name",
    )
    EDITE_GROUP_DESC = (
        107,
        "Edited the description",
        """of group "{{ group_name }}"
{% if old_description is defined and new_description is defined %}
 from "{{ old_description }}" to "{{ new_description }}"
{% endif %}
 in {{ report_type }}""",
        "Edited group description",
    )
    EDITE_SUB_GROUP = (
        108,
        "Edited the sub-grouping",
        'of group "{{group_name}}" in {{report_type}}',
        "Edited sub-grouping",
    )
    ENABLE_GROUP = (109, "Enabled group", '"{{group_name}}" in {{report_type}}', "Enabled group view")
    DISABLE_GROUP = (110, "Disabled group", '"{{group_name}}" in {{report_type}}', "Disabled group view")
    DELETE_GROUP = (111, "Deleted group", '"{{group_name}}" in {{report_type}}', "Deleted group")

    UPDATE_RULE = (
        112,
        "",
        '{{operation}} of rule "{{rule}}"{% if report_type %} in {{report_type}}{% endif %}',
        "Edited rule",
    )
    DELETE_RULE = (
        113,
        "",
        '{{operation}} of rule "{{rule}}"{% if report_type %} in {{report_type}}{% endif %}',
        "Deleted rule",
    )
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/2923#note_378568
    UPDATE_RULE_RECORD = (
        114,
        "",
        """{%- set operation_map = {10 : "init", 20: "new", 30: "amended", 40: "repealed"} -%}
{%- set ns = namespace(output=[]) %}
{%- if activated_at is defined %}
    {%- set _ = ns.output.append('Edited "'~ operation_map[operation] ~'" date of rule "' ~ rule ~ '" from "' ~ activated_at[0]|datetime("%Y-%m-%d") ~ '" to "' ~ activated_at[1]|datetime("%Y-%m-%d") ~ '"') %}
{%- endif %}
{%- if rule_description is defined %}
    {%- set _ = ns.output.append('Modified MLR description of rule "' ~ rule ~ '" from "' ~ rule_description[0] ~ '" to "' ~ rule_description[1] ~ '"') %}
{%- endif %}
{%- if gem_description is defined %}
    {%- set _ = ns.output.append('Modified GLR description of rule "' ~ rule ~ '" from "' ~ gem_description[0] ~ '" to "' ~ gem_description[1] ~ '"') %}
{%- endif %}
{{ ns.output|join("; ") }} in {{ report_type }}""",
        """
{%- set operation_map = {10 : "Init", 20: "New", 30: "Amended", 40: "Repealed"} -%}
{%- if rule_description is defined %}
    Edited MLR rule description
{%- elif gem_description is defined %}
    Edited GLR rule description
{%- elif operation in operation_map %}
    {%- if activated_at is defined %}
        Edited "{{ operation_map[operation] }}"
    {%- else %}
        Added "{{ operation_map[operation] }}"
    {%- endif %}
{%- endif %}""",
    )
    # 新增B1-B7子组
    # 见`IntEnumMixin`，第一个参数为action，第二个参数为phrase，可不配，
    # 第三个参数对应/hkex/user/audit-trail页面的`Detailed operation`
    # 第四个参数对应/hkex/user/audit-trail页面的`Operation type`列
    CREATE_FUNDRAISING_EVENT_TYPE = (
        115,
        "",
        'Modified ["Main Alias": "{{main_alias}}"] {{adjustment}} ["Doc Type": "Annual Report" | "Stock Code": "{{stock_code}}" | "Year": "{{year}}"] ',
        "Created Fundraising Event Type",
    )
    # 删除B1-B7子组
    DELETE_FUNDRAISING_EVENT_TYPE = (
        116,
        "",
        'Modified ["Main Alias": "{{main_alias}}"] {{adjustment}} ["Doc Type": "Annual Report" | "Stock Code": "{{stock_code}}" | "Year": "{{year}}"]',
        "Deleted Fundraising Event Type",
    )
    # 查看fundraising activity页面
    VIEW_FUNDRAISING_ACTIVITY = (
        117,
        "",
        '["Doc Type": "Annual Report" | "Stock Code": "{{stock_code}}" | "Year": "{{year}}"]',
        "Viewed page: Fundraising Activity",
    )
    # TODO: 审计日志渲染模板
    # 额外占据11800-11899，11900-11999枚举值
    UPDATE_AGM_ANSWER = (
        118,
        "Updated the AGM Circular",
        "",
        """
{%- if adjustment is defined and adjustment is not none -%}
    {%- if 'Disclosure' in adjustment -%}
Updated Disclosure Classification
    {%- else -%}
Updated Manual Tagged Information
    {%- endif -%}
{%- else -%}
Updated Manual Tagged Information
{%- endif -%}""",
    )
    UPDATE_POLL_ANSWER = (
        119,
        "Updated the AGM Poll Results",
        "",
        """
{%- if adjustment is defined and adjustment is not none -%}
    {%- if 'Disclosure' in adjustment -%}
Updated Disclosure Classification
    {%- else -%}
Updated Manual Tagged Information
    {%- endif -%}
{%- else -%}
Updated Manual Tagged Information
{%- endif -%}""",
    )
    # 查看director list页面
    VIEW_DIRECTOR_LIST = (
        130,
        "",
        '["Stock Code": "{{stock_code}}" | "Capacity": "{{capacity}}" |"Long Term INED": "{{long_term_ined}}" |"New Director": "{{new_director}}" |"Last Convening Date": "{{last_convening_date}}" |"Current Convening Date": "{{current_convening_date}}"]',
        "Viewed page: Director List",
    )

    @classmethod
    def get_update_answer_enum(
        cls, main_action: int, rule: Union[str, List[str]], *, schema_data: dict | None = None
    ) -> int:
        """
        main_action: UPDATE_RULE_ANSWER
        rule: A10.1
        return: 310650101
        """
        if main_action in {cls.UPDATE_ESG, cls.UPDATE_AR_ESG}:
            from remarkable.common.common import Schema

            if isinstance(rule, str):
                rule = [rule]
            ret = f"{main_action}" + "".join(str(i).rjust(2, "0") for i in Schema(schema_data).path2indexes(rule))
        elif main_action == cls.UPDATE_CG_ANSWER:
            # TODO：这里是否需要迁移，同 AGM/POLL 的处理方式，使用 102 前缀，范围为10200-10299？
            number_in_detail = schema_data["schemas"][0]["orders"].index(rule)
            ret = f"{main_action}{number_in_detail}"
        elif main_action in {cls.UPDATE_AGM_ANSWER, cls.UPDATE_POLL_ANSWER}:
            # 使用前缀118, 119，范围为11800-11899，11900-11999
            if rule == PollGMLRules.GML_TITLE_CASE:
                # mold=34 rule General Mandate Limit 没有在 schema_data 中这个直接指定序号
                number_in_detail = "07"
            else:
                number_in_detail = str(schema_data["schemas"][0]["orders"].index(rule)).zfill(2)
            ret = f"{main_action}{number_in_detail}"
        else:
            number_in_detail = re.sub(r"\D", "", rule)
            ret = f"{main_action}0{ord(rule[0].upper())}0{number_in_detail}"
        return int(ret)

    @staticmethod
    def is_post_answer(history_action):
        return history_action >= MIN_MODIFY_ANSWER_ACTION

    @classmethod
    def modify_answer_action(cls):
        # 必须保证 UPDATE_RATIO 放在最后
        return [
            cls.UPDATE_RULE_ANSWER.value,
            cls.UPDATE_DISCLOSURE.value,
            cls.UPDATE_CG_ANSWER.value,
            cls.UPDATE_AR_ESG.value,
            cls.UPDATE_ESG.value,
            cls.UPDATE_AGM_ANSWER.value,
            cls.UPDATE_POLL_ANSWER.value,
            cls.UPDATE_RATIO.value,
        ]

    @staticmethod
    @lru_cache
    def normal_template() -> dict[int, str]:
        stock_code_and_year = 'stock code = "<stock_code>" | year = "<year>"'
        qr_adjustment_template = f"doc type = Result Announcement | {stock_code_and_year}"
        adjustment_postfix = 'Modified "MainAlias = <main_alias>" | criteria = "<rule_subtab>" <adjustment>'
        qr_adjustment = f"[{qr_adjustment_template} {adjustment_postfix}]"
        return {
            HistoryAction.LOGIN.value: "Successfully logged into the system",
            HistoryAction.LOGOUT.value: "Logged out successfully",
            HistoryAction.LOGIN_WRONG_PW.value: "",
            HistoryAction.LOGIN_INACTIVE.value: "",
            HistoryAction.LOAD_PAGE.value: "",
            HistoryAction.REVIEWED_RESULT_ANNOUNCEMENT.value: f'[report type = "<report_type>" | {stock_code_and_year}]',
            HistoryAction.REVIEWED_ANNUAL_REPORT.value: f"[{stock_code_and_year}]",
            HistoryAction.REVIEWED_CG_REPORT.value: f"[{stock_code_and_year}]",
            HistoryAction.REVIEWED_ESG_REPORT.value: f"[{stock_code_and_year}]",
            HistoryAction.CREATE_USER.value: "",
            HistoryAction.DISABLE_USER.value: "",
            HistoryAction.ENABLE_USER.value: "",
            HistoryAction.MODIFY_USER_ROLE.value: "",
            HistoryAction.RESET_USER_PASSWORD.value: "",
            HistoryAction.MODIFY_USER_EMAIL.value: "",
            HistoryAction.EXPORTED_ACTIVITY_LOG.value: "",
            HistoryAction.EXPORTED_ACTIVITY_LOG_FAILED.value: "",
            HistoryAction.EXPORTED_USER_LIST.value: "Exported user list from user management page",
            # Quarterly Report(qr) exporting
            HistoryAction.EXPORTED_TABLE_RESULT_BY_ISSUER.value: "",
            HistoryAction.EXPORTED_TABLE_RESULT_BY_RULE.value: "",
            # Annual Report(ar) exporting
            HistoryAction.EXPORTED_TABLE_ANNUAL_BY_ISSUER.value: "",
            HistoryAction.EXPORTED_TABLE_ANNUAL_BY_RULE.value: "",
            HistoryAction.EXPORTED_NON_COMPLIANT_ISSUERS.value: "",
            # ESG(esg) exporting
            HistoryAction.EXPORTED_ESG_RESULT_BY_RULE.value: "",
            HistoryAction.EXPORTED_TABLE_ESG_BY_ISSUER.value: "Exported table from ESG Report Result Analysis By Issuer",
            # CG(cg) exporting
            HistoryAction.EXPORTED_CG_RESULT_BY_RULE.value: "Exported table from CG Report Result Analysis By Rule",
            HistoryAction.QUOTED_AI_RATIO.value: qr_adjustment,
            HistoryAction.CLEARED_MANUAL_RATIO.value: qr_adjustment,
            # Upload file
            HistoryAction.UPLOAD_AR.value: "",
            HistoryAction.UPLOAD_ESG.value: "",
            HistoryAction.CREATE_GROUP.value: "",
            HistoryAction.EDITE_GROUP_NAME.value: "",
            HistoryAction.EDITE_GROUP_DESC.value: "",
            HistoryAction.EDITE_SUB_GROUP.value: "",
            HistoryAction.ENABLE_GROUP.value: "",
            HistoryAction.DISABLE_GROUP.value: "",
            HistoryAction.DELETE_GROUP.value: "",
            HistoryAction.UPDATE_RULE.value: "",
            HistoryAction.DELETE_RULE.value: "",
            HistoryAction.UPDATE_RULE_RECORD.value: "",
            HistoryAction.AUTHENTICATION_SUCCESS.value: "",
            HistoryAction.AUTHENTICATION_FAILURE.value: "",
            HistoryAction.CREATE_FUNDRAISING_EVENT_TYPE.value: "",
            HistoryAction.DELETE_FUNDRAISING_EVENT_TYPE.value: "",
            HistoryAction.VIEW_FUNDRAISING_ACTIVITY: "",
        }

    @staticmethod
    @lru_cache
    def post_answer_template() -> dict[int, str]:
        doc_type_name, stock_code_and_year = "doc type", 'stock code = "<stock_code>" | year = "<year>"'
        qr_adjustment_template = (
            f'{doc_type_name} = Result Announcement | report type = "<report_type>" | {stock_code_and_year}'
        )
        modified = 'Modified "MainAlias = <main_alias>" <adjustment>'
        modified_with_criteria = 'Modified "MainAlias = <main_alias>" | criteria = "<rule_subtab>" <adjustment>'
        esg_adjustment = f"[{doc_type_name} = ESG Report | {stock_code_and_year} | {modified}]"
        return {
            HistoryAction.UPDATE_RULE_ANSWER.value: f"[{doc_type_name} = Annual Report | {stock_code_and_year} | {modified_with_criteria}]",
            HistoryAction.UPDATE_DISCLOSURE.value: f"[{qr_adjustment_template} {modified_with_criteria}]",
            HistoryAction.UPDATE_RATIO.value: f"[{qr_adjustment_template} {modified}]",
            HistoryAction.UPDATE_CG_ANSWER.value: f"[{doc_type_name} = CG Report | {stock_code_and_year} | {modified}]",
            HistoryAction.UPDATE_ESG.value: esg_adjustment,
            HistoryAction.UPDATE_AR_ESG.value: esg_adjustment,
            HistoryAction.UPDATE_AGM_ANSWER.value: f"[{doc_type_name} = AGM Circular | {stock_code_and_year} | {modified}]",
            HistoryAction.UPDATE_POLL_ANSWER.value: f"[{doc_type_name} = AGM Poll Results | {stock_code_and_year} | {modified}]",
        }


def all_permissions() -> list[str]:
    return list({**get_config("feature.common_permissions"), **get_config("feature.additional_permissions")})


UserTuple = namedtuple("User", ["id", "name"])
ADMIN = UserTuple(1, "admin")
BASE_PERM = [{"perm": p} for p in get_config("feature.basic_permissions")]
ADMIN_PERM = [{"perm": p} for p in all_permissions()]


@unique
class PDFParseStatus(EnumMixin, IntEnum):
    PENDING = 1  # 排队中
    CACHING = 6  # 缓存&分页
    PARSING = 2  # 解析PDFinsight中
    PAGE_CACHED = 7  # 分页缓存完毕
    COMPLETE = 4
    FAIL = 5
    CANCELLED = 3


@unique
class PDFFlag(EnumMixin, IntEnum):
    NEED_CONVERT = 1
    CONVERTED = 0
    FAILED_CONVERT = -1


# k:v -> 规则:年份跨度
hkex_rule_map = {
    # 'ar': 0,      # 年报(仅测试)
    "A3": 0,  # 年报当年内是否有股票回购
    "A11.3": 0,
    "A11.1": 0,  # 当年是否发行了可转换债券，认证，期权等
    "A11.2": 0,
    "A22": 0,  # 年报当年是否发生了重大收购/出售
    "A23": 0,  # 如果当年有关联交易，那么要满足14a55准则
    "A24.2": 3,  # 陈述关联方交易是否为关联交易
    "A25": 0,  # 独立董事没有达到其规则的要求，但公司仍聘请其为独董的理由
    "A27": 2,  # 过去三年是否更换过审计师
    "A28.1": 2,  # 发行人是否存在业绩承诺
    "A28.2": 2,  # 如果未实现业绩承诺，有多少差额
    "A28.3": 2,  # 如何弥补差额
    "A29.1": 0,  # 股权质押的数量，类别，以及质押的债项款额等
    "A29.2": 0,
    "A29.3": 0,
    "A30": 0,  # 股份认购权的 目的、参与人、发行证券总数及百分比 还有个人可获权益上限
    "A31": 0,
    "A32": 0,
    "A33": 0,
    "B1": 0,
    "B2": 0,
    "B3": 0,
    "B4": 0,
    "B5": 0,
    "B6": 0,
    "B7": 0,
    "B8": 0,
    "B9": 0,
    "B10": 0,
    "B1-10": 0,
    "B63": 0,
    "B63.1": 0,
    "B63.2": 0,
    "B63.3": 0,
    "B63.4": 0,
    "B63.5": 0,
    "B64": 0,
    "B64.1": 0,
    "B64.2": 0,
    "B64.3": 0,
    "B64.4": 0,
    "B64.5": 0,
    "B64.6": 0,
    "B65": 0,
    "B65.1": 0,
    "B65.2": 0,
    "B65.3": 0,
    "B66": 0,
    "B66.1": 0,
    "B66.2": 0,
    "B67": 0,
    "B68": 0,
    "B69": 0,
    "B70": 0,
    "B71": 0,
    "B72": 0,
    "B63-72": 0,
}

ADDITIONAL_FILE_TYPES = [
    "A11.1",
    "A11.2",
    "A22",
    "A23",
    "A24.2",
    "A25",
    "A27",
    "A28.1",
    "A28.2",
    "A28.3",
    "A29.1",
    "A29.2",
    "A30",
    "A31",
    "A32",
    "A33",
    "A3+A11.3",
    # "ar",
    "B1-10",
    "B63-72",
    # "ESG",
    # "Final",
    # "Interim",
    "listing_doc",
    "ProfitWarning",
    # "Q1",
    # "Q3",
]


@unique
class HKEXFileParseStatus(EnumMixin, IntEnum):
    """港交所外部文档解析状态
    大于1024(unix timestamp)表示解析正常"""

    CON_FAILED = -1  # 文档转换失败
    DUPLICATED = -2
    DELETED = -3
    TODO = 0  # 文档正常接收, 待解析
    PLANNING = 1  # 已进入定时解析队列
    PENDING = 201  # 已进入队列等待处理
    EXCLUDE = 202  # 文档在排除列表, 无需解析
    HTTP_ERROR = 502  # 网络问题, 文件下载失败
    ERROR = 500  # 解析过程出错
    SUCCESS = 1024


@unique
class HKEXInfoQueryStatus(EnumMixin, IntEnum):
    """港交所外部文档查询返回状态码"""

    NOT_SUPPORT = -1  # 当前rule暂不支持查找外部文档
    NOT_FOUND = 0  # 当前rule未找到外部文档
    FOUND = 1  # 当前rule找到外部文件


@unique
class HKEXDisclosureType(EnumMixin, IntEnum):
    """港交所披露信息结果分类"""

    DISCLOSURE = 0
    NEGATIVE_STATEMENT = 1
    NO_DISCLOSURE = 2


@unique
class HKEXComplianceType(EnumMixin, IntEnum):
    """港交所合规校验结果分类"""

    COMPLIANCE = 0
    NON_COMPLIANCE = 1


@unique
class JuraVersion(Enum):
    PROJECT_NAME = {
        "v1": "Jura 1.0",
        "v2": "Jura 2.0 new",
        "v2a": "Additional Documents",
        "v3": "Jura 3.0",
    }
    SCHEMA_NAME = {
        "v1": "LRs",
        "v2": "Jura 2.0 Listing Rules",
        "v2a": "Additional Documents",
        "v3": "Jura 3.0 Ratio Checking",
    }

    LINK = 1
    FILE = 2

    @classmethod
    def get(cls, version, typ="project", default=None):
        if typ == "project":
            return cls.PROJECT_NAME.value.get(version, default)
        if typ == "schema":
            return cls.SCHEMA_NAME.value.get(version, default)
        raise ValueError("unkonw type: %s" % typ)


@unique
class HKEXARStatus(EnumMixin, IntEnum):
    """HKEX question flag
    f c n
    0 0 0 = 0 null
    0 0 1 = 1 normal
    0 1 1 = 3 completed
    1 0 1 = 5 flagged
    1 1 1 = 7 both(completed + flagged)
    """

    NULL = 0
    NORMAL = 1
    COMPLETED = 3
    FLAGGED = 5
    BOTH = 7

    @staticmethod
    def convert2enum(status):
        """转换状态列表为枚举值"""
        members = ("flagged", "completed", "normal")
        status = set(status)
        status.add(members[-1])
        status = [i for i in members if i in status]
        status = "".join([i in status and "1" or "0" for i in members])
        return int(status, 2)

    @staticmethod
    def convert2status(enum):
        """
        转换枚举值为状态
        返回flagged, completed的状态
        """
        if enum == 1:
            return "No", "Yes"
        if enum == 3:
            return "No", "Yes"
        if enum == 5:
            return "Yes", "No"
        return "Yes", "Yes"


class ConflictTreatmentType(EnumMixin, IntEnum):
    MANUAL = 1  # 人工处理
    LATEST = 2  # 以最新为准
    MERGED = 3  # 合并答案


class StatType(EnumMixin, IntEnum):
    PRESET_ANSWER = 1
    CRUDE_ANSWER = 2
    RULE_RESULT = 3


@unique
class MarketType(EnumMixin, IntEnum):
    MAIN_BOARD = 0
    GEM = 1


@unique
class DocType(EnumMixin, IntEnum):
    AR = 1
    ESG = 2
    Q1 = 11
    INTERIM = 12
    Q3 = 13
    FINAL = 14

    PROFITWARNING = 20
    Q1_PW = 21
    INTERIM_PW = 22
    Q3_PW = 23
    FINAL_PW = 24

    AGM = 30
    POLL = 31

    MR = 36

    @classmethod
    def status_anno_map(cls):
        return {
            1: "Annual Report",
            2: "ESG",
            11: "Q1",
            12: "Interim",
            13: "Q3",
            14: "Final",
        }

    @classmethod
    def anno_status_map(cls):
        return {v: k for k, v in cls.status_anno_map().items()}

    @classmethod
    def qr_types(cls):
        """Quarterly report types"""
        return [v for t, v in cls.status_anno_map().items() if 10 < t < 20]

    @classmethod
    def qr_values(cls):
        """Quarterly report values"""
        return [t for t in cls.status_anno_map() if 10 < t < 20]


@unique
class RatioAnswerType(EnumMixin, str, Enum):
    AI_RATIO = "hkex_ai_ratio"
    AI_RATIO1 = "hkex_ai_ratio1"
    AI_RATIO2 = "hkex_ai_ratio2"
    AI_RATIO3 = "hkex_ai_ratio3"
    AI_RATIO4 = "hkex_ai_ratio4"
    AI_RATIO5 = "hkex_ai_ratio5"
    AI_RATIO6 = "hkex_ai_ratio6"
    AI_RATIO7 = "hkex_ai_ratio7"

    MANUAL_RATIO = "hkex_manual_ratio"
    MANUAL_RATIO1 = "hkex_manual_ratio1"
    MANUAL_RATIO2 = "hkex_manual_ratio2"
    MANUAL_RATIO3 = "hkex_manual_ratio3"
    MANUAL_RATIO4 = "hkex_manual_ratio4"
    MANUAL_RATIO5 = "hkex_manual_ratio5"
    MANUAL_RATIO6 = "hkex_manual_ratio6"
    MANUAL_RATIO7 = "hkex_manual_ratio7"

    @classmethod
    def orders(cls):
        return [f"{cls.AI_RATIO.value.rsplit('_', maxsplit=1)[-1]}{i}" for i in range(1, 8)]

    @classmethod
    def prefix(cls, answer_type="ai"):
        valid_middle_strs = (
            "ai",
            "manual",
            "currency",
        )
        assert answer_type in valid_middle_strs, f"Not a valid answer type: {answer_type}"
        return f"hkex_{answer_type}_"


class TableType(EnumMixin, IntEnum):
    TUPLE = 1
    ROW = 2
    KV = 3
    COL = 4  # 按列解析

    @classmethod
    def status_anno_map(cls):
        return {
            1: "table_tuple",
            2: "table_row",
            3: "table_kv",
            4: "table_col",
        }


class AnswerValueEnum(EnumMixin, str, Enum):
    NS = "Negative Statement"
    PS = "Positive Statement"
    ND = "No Disclosure"
    D = "Disclosure"
    COMPLY = "Comply"
    EXPLAIN = "Explain"
    NA = "Not Applicable"
    Yes = "Yes"
    No = "No"
    ND_CAP = "ND"
    QUERY = "Query"


class UserStatus(EnumMixin, IntEnum):
    ACTIVE = 1
    INACTIVE = 2


class UserRole(IntEnumBase):
    UNKNOWN = (0, "")
    BUSINESS_ADMIN = (1, "Business Admin")
    GENERAL_USER = (2, "General User")


class PageModule(IntEnumBase):
    AR = (1, "jura1~5", "Annual Report Checking", "ar")
    QR = (3, "jura3", "Result Announcement", "qr")
    ESG = (4, "jura4", "ESG Report Checking", "esg")
    CG = (5, "jura5", "CG Report Checking", "cg")
    AGM = (61, "jura6.1", "AGM Circular Checking", "agm")
    POLL = (62, "jura6.2", "AGM Poll Results", "poll")


class Department(IntEnumBase):
    # Policy and Secretariat Services、Listing Regulation & Enforcement、Accounting Affairs
    OTHER = (0, "Others")
    PSS = (1, "Policy and Secretariat Services")
    LRE = (2, "Listing Regulation & Enforcement")
    AA = (3, "Accounting Affairs")

    @classmethod
    def label_to_enum(cls, label: str) -> Self | None:
        enum = super().label_to_enum(label)
        return cls.OTHER if enum is None else enum

    @classmethod
    @lru_cache
    def calc_default_modules(cls, department: Self | int) -> list[PageModule]:
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/5945
        if isinstance(department, int):
            department = cls(department) if department in cls else cls.OTHER
        match department:
            case cls.PSS:
                # ESG, CG
                return [PageModule.ESG, PageModule.CG]
            case cls.LRE:
                # AR, QR, CG, AGM, POLL
                return [PageModule.AR, PageModule.QR, PageModule.CG, PageModule.AGM, PageModule.POLL]
            case cls.AA:
                # AR, QR
                return [PageModule.AR, PageModule.QR]
        logger.warning(f"No accessible modules for {department}")
        return []


class CRUD(str, EnumMixin, Enum):
    C = "crud:c"
    R = "crud:r"
    U = "crud:u"
    D = "crud:d"


@unique
class UserPlatform(EnumMixin, IntEnum):
    BUILD_IN = 0
    ROSTER = 10
    AZURE = 20


class RuleOperation(EnumMixin, IntEnum):
    INIT = 10
    NEW = 20
    AMENDED = 30
    REPEALED = 40


class SchemaName(EnumMixin, str, Enum):
    A = "LRs"
    B = "Jura 2.0 Listing Rules"
    ADDITIONAL = "Jura 2.0 Additional Rules"
    C = "Jura 2.0 Additional Rules"
    D = "Jura3.0 AR Disclosure Checking"
    DISCLOSURE = "Jura 3.0 Disclosure Checking"
    RATIO = "Jura 3.0 Ratio Checking"
    AR_ESG = "Jura4 Annual Report ESG"
    ESG = "Jura4 ESG Report"
    CG = "Jura5 CG Report"
    POLICY_AR = "Policy-AR-ESG"
    POLICY_ESG = "Policy-ESG"


class CGEnum(EnumMixin, str, Enum):
    C = "Comply"
    ND = "No Disclosure"
    NA = "Not Applicable"


COMPLIANCE = "compliance"
NON_COMPLIANCE = "potential non-compliance"


class LRSEnum(EnumMixin, str, Enum):
    D = "disclosure"
    ND = "no disclosure"
    NS = "negative statement"
    ND_CAP = "ND"
    No = "No"


class ESGEnum(EnumMixin, str, Enum):
    Y = "Comply"
    ND = "No Disclosure"


class QuarterReportEnum(EnumMixin, str, Enum):
    D = "Disclosure"
    NS = "Negative Statement"
    ND = "No Disclosure"


class AnnualReportEnum(EnumMixin, str, Enum):
    PS = "Positive Statement"
    NS = "Negative Statement"
    ND = "No Disclosure"


ALL_SCHEME_ENUM: TypeAlias = Union[CGEnum, ESGEnum, QuarterReportEnum, AnnualReportEnum, AnswerValueEnum]


@unique
class Language(EnumMixin, Enum):
    """
    语言类型
    """

    ZH_CN = "zh_CN"
    EN_US = "en_US"


class PDFInsightClassEnum(EnumMixin, str, Enum):
    """
    元素块类型枚举
    """

    SYLLABUS = "SYLLABUS"
    PARAGRAPH = "PARAGRAPH"
    TABLE = "TABLE"
    PAGE_HEADER = "PAGE_HEADER"
    PAGE_FOOTER = "PAGE_FOOTER"
    SHAPE = "SHAPE"
    IMAGE = "IMAGE"
    FOOTNOTE = "FOOTNOTE"
    INFOGRAPHIC = "INFOGRAPHIC"


class TableCellsResultType(EnumMixin, Enum):
    CELLS = "CELLS"
    ROWS = "ROWS"
    COLS = "COLS"


class CNAEnum(IntEnumBase):
    C = (1, "Comply", "Comply", "Y")
    ND = (2, "No Disclosure", "No Disclosure", "N")
    NA = (3, "Not Applicable", "N/A", "NA")


class PNNEnum(IntEnumBase):
    PS = (1, "Positive Statement", "Positive Statement", "PS")
    NS = (2, "Negative Statement", "Negative Statement", "NS")
    ND = (3, "No Disclosure", "No Disclosure", "ND")


class QuarterEnum(IntEnumBase):
    Q1 = (1, "Q1")
    INTERIM = (2, "Interim")
    Q3 = (3, "Q3")
    FINAL = (4, "Final")


class BadRequestMessage:
    CAN_NOT_SUBMIT = "Unable to submit. Please contact support team for further assistance."
    DOC_IS_PARSING = "The document is processing, please wait a moment."
    DATA_CHANGED = "The data has been updated, please try again."
    AR_IS_CRAWLING = "The annual report file you specified is being crawled, please wait a few minutes."
    YEAR_END_NOT_AVAILABLE = "Year End details are not available. Please contact support team for further assistance."


class FundRaisingRuleEnum(EnumMixin, str, Enum):
    B1 = "B1"
    B2 = "B2"
    B3 = "B3"
    B4 = "B4"
    B5 = "B5"
    B6 = "B6"
    B7 = "B7"
    B8 = "B8"
    B9 = "B9"
    B10 = "B10"
    C1_1_1 = "C1.1.1"
    C1_2_1 = "C1.2.1"
    C1_3 = "C1.3"


USER_AGENT = "Mozilla/5.0 (X11; Linux x86_64) Jura (From P.A.I; Partner)"


class PolicyEsgRules(StrEnumBase):
    E1 = (
        "E1-Reference to ISSB Standards",
        "T1-Reference to ISSB Standards",
        "Reference to ISSB Standards",
        "T1",
    )
    E2 = (
        "E2-Adoption of Independent Assurance",
        "T2-Adoption of Independent Assurance",
        "Adoption of Independent Assurance",
        "T2",
    )
    E3 = (
        "E3-Details of Independent Assurance",
        "T3-Details of Independent Assurance",
        "Details of Independent Assurance",
        "T3",
    )
    E4 = (
        "E4-Independent Assurance on scope 1 and scope 2 GHG emissions",
        "T4-Independent Assurance on scope 1 and scope 2 GHG emissions",
        "Independent Assurance on Scope 1 and Scope 2 GHG Emissions",
        "T4",
    )
    E5 = ("E5-Scenario analysis", "T5-Scenario analysis", "Scenario Analysis", "T5")
    E6 = ("E6-Source of scenarios", "T6-Source of scenarios", "Source of Scenarios", "T6")
    E7 = ("E7-Scope 3 emissions", "T7-Scope 3 emissions", "Scope 3 Emissions", "T7")
    E8 = (
        "E8-Categories of scope 3 emissions",
        "T8-Categories of scope 3 emissions",
        "Categories of Scope 3 Emissions",
        "T8",
    )
    E9 = (
        "E9-Scope 3 emissions data by categories",
        "T9-Scope 3 emissions data by categories",
        "Scope 3 Emissions Data",
        "T9",
    )


class E6Scenario(StrEnumBase):
    IPCC = ("IPCC", "IPCC", "Scenario 1 (IPCC)", "IPCC")
    IEA = ("IEA", "IEA", "Scenario 2 (IEA)", "IEA")
    NGFS = ("NGFS", "NGFS", "Scenario 3 (NGFS)", "NGFS")


class CategoryTypeEnum(EnumMixin, str, Enum):
    category1 = ("Purchased goods and services",)
    category2 = ("Capital goods",)
    category3 = (
        "Fuel- and energy-related activities not included in scope 1 greenhouse gas emissions or scope 2 greenhouse gas emissions",
    )
    category4 = ("Upstream transportation and distribution",)
    category5 = ("Waste generated in operations",)
    category6 = ("Business travel",)
    category7 = ("Employee commuting",)
    category8 = ("Upstream leased assets",)
    category9 = ("Downstream transportation and distribution",)
    category10 = ("Processing of sold products",)
    category11 = ("Use of sold products",)
    category12 = ("End-of-life treatment of sold products",)
    category13 = ("Downstream leased assets",)
    category14 = ("Franchises",)
    category15 = ("Investments",)


class Scope3Categories(IntEnumBase):
    category1 = (1, CategoryTypeEnum.category1.value, "category1", "Cat 1")
    category2 = (2, CategoryTypeEnum.category2.value, "category2", "Cat 2")
    category3 = (3, CategoryTypeEnum.category3.value, "category3", "Cat 3")
    category4 = (4, CategoryTypeEnum.category4.value, "category4", "Cat 4")
    category5 = (5, CategoryTypeEnum.category5.value, "category5", "Cat 5")
    category6 = (6, CategoryTypeEnum.category6.value, "category6", "Cat 6")
    category7 = (7, CategoryTypeEnum.category7.value, "category7", "Cat 7")
    category8 = (8, CategoryTypeEnum.category8.value, "category8", "Cat 8")
    category9 = (9, CategoryTypeEnum.category9.value, "category9", "Cat 9")
    category10 = (10, CategoryTypeEnum.category10.value, "category10", "Cat 10")
    category11 = (11, CategoryTypeEnum.category11.value, "category11", "Cat 11")
    category12 = (12, CategoryTypeEnum.category12.value, "category12", "Cat 12")
    category13 = (13, CategoryTypeEnum.category13.value, "category13", "Cat 13")
    category14 = (14, CategoryTypeEnum.category14.value, "category14", "Cat 14")
    category15 = (15, CategoryTypeEnum.category15.value, "category15", "Cat 15")

    @classmethod
    def display_text(cls, phrase: str) -> str:
        # return f"{cls.phrase_to_enum(phrase).label}_{phrase}"
        return cls.phrase_to_enum(phrase).abbr

    @classmethod
    def display_emission(cls, phrase, emission: str) -> str:
        # return f"{cls.phrase_to_enum(phrase).label}_{emission}"
        return f"{cls.phrase_to_enum(phrase).abbr} Data"

    @classmethod
    def abbr_to_full_label(cls, abbr: str):
        """
        1. Cat 1[ Data] -> Cat 1. Purchased goods and services
        2. IPCC -> Scenario 1 (IPCC)
        """
        if enum := cls.abbr_to_enum(abbr.replace(" Data", "")):
            return f"{enum.abbr}. {enum.phrase}"
        if enum := E6Scenario.abbr_to_enum(abbr):
            return enum.label
        raise ValueError(f"Invalid abbr: {abbr}, must be startswith one of: {cls.abbreviations()}.")


TYPE_NAME_MAP = {
    "ar": "Annual Report",
    "qr": "Result",
    "esg": "ESG Report",
    "cg": "CG Report",
    "agm": "AGM Circular",
    "poll": "AGM Poll Results",
}


class AGMRules:
    M1 = "M1-disclaimer on the front page"
    M2 = "M2-total number and description of the shares which the issuer proposes to purchase"
    M3 = "M3-reasons for the proposed purchase of shares"
    M4 = "M4-source of funds for making the proposed purchase"
    M5 = "M5-material adverse impact on the working capital or gearing position"
    M6 = "M6-close associates of the directors with a present intention to sell shares to the issuer"
    M7 = "M7-directors will exercise the power to make purchases in accordance with rules and laws"
    M8 = "M8-consequences of any purchases under the Takeovers Code"
    M9 = "M9-purchases made in the previous six months"
    M10 = "M10-whether core connected persons of the issuer have notified their intention to sell shares to the issuer"
    M11 = "M11-highest and lowest prices of shares during the previous twelve months"
    M12 = "M12-unusual features"
    M13 = "M13-whether issuer intends to cancel the repurchased shares or hold them as treasury shares"
    M14 = "M14-purchase mandate"
    M15 = "M15-relevant period for purchase mandate"
    M16 = "M16-issue mandate"
    M17 = "M17-extension to general mandate"
    M18 = "M18-relevant period for issue mandate"
    M19 = "M19-appoint an auditor to hold office"
    M20 = "M20-the appointment, removal and remuneration of auditors"
    M21 = "M21-date of AGM"
    M22 = "M22-date of notice"
    M23 = "M23-a super-majority vote required to approve a change to those rights"
    M24 = "M24-a super-majority vote required to approve changes to constitutional documents"
    M25 = "M25-13.51(1)"
    M26 = "M26-length of tenure of each INED when all INED serving more than nine years"
    M27 = "M27-appoint a new INED when all INED serving more than nine years"
    M28 = "M28-AC1.B.3.4(a)"
    M29 = "M29-AC1.B.3.4(b)"
    M30 = "M30-the perspectives, skills and experience of proposed INEDs"
    M31 = "M31-contribution to diversity from proposed INEDs"
    M32 = "M32-confirmation of independence for a new INED"
    M33 = "M33-the full name and age of proposed directors"
    M34 = "M34-positions of proposed directors"
    M35 = "M35-experience including other directorships held in the last three years in public companies and other major appointments and professional qualifications"
    M36 = "M36-length or proposed length of service of proposed directors"
    M37 = "M37-relationships with any directors, senior management or substantial or controlling shareholders of the issuer"
    M38 = "M38-interests in shares of the issuer"
    M39 = "M39-13.74 - 13.51(2)(g)"
    M40 = "M40-any person appointed by the directors to fill a casual vacancy on or as an addition to the board"
    M41 = "M41-AC1.B.2.3"
    M42 = "M42-public sanctions made against directors"
    M43 = "M43-directors adjudged bankrupt or insolvent"
    M44 = "M44-deed of arrangement or the arrangement or composition with creditors"
    M45 = "M45-unsatisfied judgments or court orders against directors"
    M46 = "M46-dissolved or put into liquidation"
    M47 = "M47-any conviction for any offence"
    M48 = "M48-insider dealing"
    M49 = "M49-adjudged by a Court or arbitral body civilly liable for any fraud, breach of duty or other misconduct"
    M50 = "M50-business registration or licence revoked"
    M51 = "M51-disqualified from holding or deemed unfit to hold the position"
    M52 = "M52-prohibited by law, full particulars of any investigation by any judicial, regulatory or governmental authority"
    M53 = "M53-be refused admission to membership of any professional body or been censured or disciplined by any such body"
    M54 = "M54-a member of a triad or other illegal society"
    M55 = "M55-any investigation, hearing or proceeding brought or instituted by any securities regulatory authority"
    M56 = "M56-be a defendant in any current criminal proceeding"
    M57 = "M57-any other matters that need to be brought to the attention of holders of securities of the issuer"
    M58 = "M58-information to be disclosed pursuant to any of the requirements of this rule 13.51(2)"


class PollGMLRules:
    GML = "General mandate limit"
    GML_TITLE_CASE = "General Mandate Limit"  # 首字母大写
    RP = "Whether the resolution is passed"
    AD = "Approval date"

    TOTAL_SHARE = "Total number of issued shares"
    TREASURY_SHARE = "Number of treasury shares"
    PERCENTAGE = "Percentage"
    PASSED_DATE = "Passed date of the resolution"
    RESOLUTION_PASSED = "Whether the resolution has been passed"

    SHARE_CLASS = "Relevant share class"
    VALUE = "Value"
    RESOLUTION = "general mandate resolution"
    PASSED = "pass or not"

    RP_RESOLUTION = "General mandate resolution"
    RP_PASSED = "Yes or No"


class GMLCalcType(EnumMixin, str, Enum):
    BY_TOTAl = "By Total"
    BY_CLASS = "By Class"


# 来自 https://www.hkex.com.hk/Market-Data/Securities-Prices/Equities?sc_lang=en 按照secondary listings筛选
# NOTE: 不定期手动更新
SECONDARY_LISTING_CODES = {
    "09888",
    "09618",
    "09961",
    "09999",
    "09698",
    "09866",
    "09901",
    "01698",
    "01179",
    "09898",
    "89888",
    "89618",
    "00945",
    "03660",
    "06288",
    "02518",
}
