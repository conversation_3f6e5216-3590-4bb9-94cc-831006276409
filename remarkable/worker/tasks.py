import asyncio
import hashlib
import io
import json
import logging
import os
from zipfile import ZIP_DEFLATED, ZipFile

import requests
from utensils.hash import md5sum_for_file
from utensils.syncer import sync

from remarkable.base_handler import CustomError
from remarkable.common.common import EmptyAnswer
from remarkable.common.constants import AIStatus, DocType, HKEXFileParseStatus
from remarkable.common.exceptions import DownloadError, PushError
from remarkable.common.storage import hkex_files_storage, localstorage
from remarkable.common.util import fail2mm_sync, mm_notify, read_zip_first_file
from remarkable.config import get_config
from remarkable.db import loop_wrapper, pw_db
from remarkable.models.file_project import NewFileProject
from remarkable.models.hkex_file import HKEXFile
from remarkable.models.hkex_file_meta import HKEXFileMeta
from remarkable.models.mold import special_mold
from remarkable.models.new_file import NewFile
from remarkable.models.new_question import NewQuestion
from remarkable.pdfinsight.reader_util import make_syllabuses_range
from remarkable.plugins.fileapi.common import gen_interdoc
from remarkable.plugins.fileapi.worker import convert2pdf, create_pdf_cache
from remarkable.plugins.hkex.worker import (
    doc2pdf,
    parse_listing_doc,
    parse_pdf,
    parse_xls,
)
from remarkable.predictor.predict import predict_answer
from remarkable.rule.inspector import inspect_rules
from remarkable.schemas.addition_data import load_addition_data_by_fid_tid
from remarkable.services.director import set_agm_meta
from remarkable.services.embedding import create_and_save_embeddings
from remarkable.services.esg import check_e_nd_nums, update_activated_state
from remarkable.services.export.stat_res import collect_compliance_answer
from remarkable.services.file_meta import get_ar_year_end
from remarkable.services.mr import set_mr_meta
from remarkable.services.poll import set_poll_meta
from remarkable.services.prompter import predict_crude_answer, save_crude_answer
from remarkable.worker import is_valid_server
from remarkable.worker.app import app

logger = logging.getLogger(__name__)


@app.task
@loop_wrapper()
async def cache_pdf_file(file_id):
    file = await NewFile.find_by_id(file_id)
    if not file:
        return logger.error(f"file: {file_id} not exists")
    if not file.pdf:
        return logger.error(f"no pdf file found for file: {file_id}")
    if not file.pdfinsight:
        return logger.error(f"no pdfinsight file found for file: {file_id}")
    try:
        # create cache data to speed up text_in_box api
        await create_pdf_cache(file, force=True)
    except Exception as exp:
        logger.exception(exp)


async def _parse_ar_year_end(fid: int, year_end="", *, report_year: int = None):
    report_year = report_year or (int(year_end[-4:]) if year_end else None)
    if not report_year and (
        fy_manual := await pw_db.scalar(
            HKEXFile.select(HKEXFile.meta["financial_year"].cast("int")).where(HKEXFile.fid == fid)
        )
    ):
        logger.info(f"Use manual FY: {fy_manual} for file: {fid}")
        report_year = fy_manual
    meta = await HKEXFileMeta.find_by_fid(fid)
    if not meta:
        logger.error(f"No meta info found for file: {fid}")
        return year_end
    # if meta.doc_type not in (DocType.AR, DocType.ESG, DocType.Q1, DocType.INTERIM, DocType.Q3, DocType.FINAL):
    if meta.doc_type not in (DocType.AR, DocType.ESG):
        logger.warning(f"No need to parse year end info for file: {fid}")
        return year_end

    file = await NewFile.find_by_id(fid)
    # 时间格式示例: 31 Dec 2018
    try:
        year_end = get_ar_year_end(file, report_year=report_year) or year_end
    except Exception as exp:
        logger.exception(exp)
        return year_end

    report_year = year_end.split(" ")[-1]
    msg = f"FY:{report_year} for {fid=} may be wrong."
    if report_year:
        await pw_db.execute(
            HKEXFileMeta.update(report_year=report_year, year_end=year_end).where(HKEXFileMeta.fid == fid)
        )
        if int(report_year) < (meta.published_at.year - 1):
            logger.warning(msg)
            mm_notify(msg, error=True, tags=("parse_ar_year_end",))
    else:
        logger.error(msg)
        return year_end

    await update_activated_state(fid)
    return year_end


@app.task
@sync
async def fix_syllabus_range(fid: int, old_pdfinsight_path: str):
    logger.info(f"start fix syllabuses range: {fid=}")
    interdoc = json.loads(read_zip_first_file(old_pdfinsight_path))
    make_syllabuses_range(interdoc["syllabuses"], interdoc)

    with io.BytesIO() as bytes_io:
        with ZipFile(bytes_io, "w", ZIP_DEFLATED) as res_fp:
            res_fp.writestr("origin.json", json.dumps(interdoc))
        body = bytes_io.getvalue()
        md5 = hashlib.md5(body).hexdigest()
        new_path = os.path.join(md5[:2], md5[2:])
        localstorage.write_file(new_path, body)
        logger.info(f"save {new_path=} for {fid=}")

    if localstorage.mount(new_path) != old_pdfinsight_path:
        localstorage.delete_file(old_pdfinsight_path)
        logger.info(f"delete {old_pdfinsight_path=} for {fid=}")
    await pw_db.execute(NewFile.update(pdfinsight=md5).where(NewFile.id == fid))


@app.task
def parse_ar_year_end(fid: int, year_end: str | None = ""):
    # 此任务会被调用至少两次：
    # 1. 文件上传后，此时通过原始PDF数据解析FY信息，准确度较差，但能初步得到一个相对可用的FY
    # 2. PDFinsight数据回调，此时会通过PDFinsight数据进行解析，可以获取到相对靠谱的FY数据，最终入库
    asyncio.run(_parse_ar_year_end(fid, year_end))


async def _check_hkex_file(hkex_file: dict):
    """
    hkex_file = {
        'id': 3416657, 'name': 'PHIP (1st submission).pdf', 'fid': None,  # fid也可能为空，此时还没写入file表
        'company': 'DEEWIN TIANXIA CO., LTD',
        'stock_code': '02418', 'type': 'IPO1',
        'headline': '[Post Hearing Information Packs or PHIPs or related materials]',
        'url': 'https://www1.hkexnews.hk/listedco/listconews/sehk/2022/0715/sehk22062901912.pdf',
        'result': {}, 'release_time': 1656504240, 'finished_utc': 201, 'hash': 'fd39179029308c89db1ff328208fca1f',
        'size': 12036444, 'tu_hash': '3736fade7c06f68a153e9245cfa82c72', 'pdf_hash': None,
        'meta': {'stocks': ['02418']}, 'status': 0, 'created_utc': 1684232894, 'updated_utc': 1684233160,
        'deleted_utc': 0, 'path': 'fd/39179029308c89db1ff328208fca1f'  # path可能没有，需要补充
    }
    """
    from remarkable.common.util import DownloadWrapper

    hash_in_db = hkex_file.get("hash")
    assert hash_in_db, f"file: {hkex_file} has no hash"
    hkex_file["path"] = os.path.join(hash_in_db[:2], hash_in_db[2:])
    abs_path = hkex_files_storage.mount(hkex_file["path"])
    if os.path.exists(abs_path) and md5sum_for_file(abs_path) == hash_in_db:
        return hkex_file

    # 文件不存在或者有损坏才下载，假设爬虫下载的文件都是完整的。
    try:
        data = DownloadWrapper(hkex_file["url"]).run()
    except Exception as e:
        await pw_db.execute(
            HKEXFile.update(finished_utc=HKEXFileParseStatus.HTTP_ERROR.value).where(HKEXFile.id == hkex_file["id"])
        )
        logging.exception(e)
        raise DownloadError(f"file: {hkex_file} download failed") from e

    hkex_files_storage.write_file(abs_path, data)
    return hkex_file


@app.task(autoretry_for=(DownloadError,), retry_kwargs={"max_retries": 3}, retry_backoff=True)
def prepare_parse_file(hkex_file: dict):
    asyncio.get_event_loop().run_until_complete(_prepare_parse_file(hkex_file))


async def _prepare_parse_file(hkex_file: dict):
    hkex_file = await _check_hkex_file(hkex_file)
    assert hkex_file and hkex_file["path"], f"file: {hkex_file} has no path"

    try:
        if hkex_file["type"] == "A3+A11.3":
            await parse_xls(hkex_file)
        elif hkex_file["type"] == "listing_doc":
            await parse_listing_doc(hkex_file)
        elif hkex_file["name"].lower().endswith(".pdf"):
            await parse_pdf(hkex_file)
        elif hkex_file["name"].lower().endswith(".doc") or hkex_file["name"].lower().endswith(".docx"):
            pdf_hash = await doc2pdf(hkex_file)
            if pdf_hash:
                hkex_file["path"] = os.path.join(pdf_hash[:2], pdf_hash[2:])
                await parse_pdf(hkex_file)
        else:
            logging.warning(f"Unsupported file detected, ignore: {hkex_file}")
    except Exception as exp:
        await pw_db.execute(
            HKEXFile.update(finished_utc=HKEXFileParseStatus.ERROR.value).where(HKEXFile.id == hkex_file["id"])
        )
        logging.exception(exp)


@app.task(autoretry_for=(PushError,), retry_kwargs={"max_retries": 3}, retry_backoff=True)
@fail2mm_sync(tags=("preset_answer",))
@loop_wrapper()
async def preset_answer(fid, mid=None):
    await _preset_answer(fid, mid)


async def _preset_answer(fid, mid=None):
    file = await NewFile.find_by_id(fid)
    if not file:
        raise CustomError("preset_answer not found file")

    if mid:
        question = await NewQuestion.find_by_fid_mid(fid, mid)
        assert question is not None, "question is none"
        questions = [question]
    else:
        questions = await NewQuestion.find_by_fid(fid)
    for question in questions:
        await run_predict_question_answer(file, question)


@app.task
@sync
async def batch_set_answer(fid):
    questions = await NewQuestion.find_by_fid(fid)
    for question in questions:
        await question.set_answer()


@app.task
@sync
async def batch_preset_question_answer(qids: list, fid: int):
    for qid in qids:
        question = await NewQuestion.find_by_id(qid)
        if not question:
            raise CustomError(f"preset_answer not found question, {qid=}")
        file = await NewFile.find_by_id(question.fid)
        if not file:
            raise CustomError("preset_answer not found file")
        try:
            await run_predict_question_answer(file, question)
        except Exception as e:
            logger.exception(e)
            question.ai_status = AIStatus.FAILED.value
            await pw_db.update(question, only=["ai_status"])
            logger.info(f"preset_answer failed, {qid=}, {file.id=}, {question.mold=}")
            mm_notify(f"preset_answer failed, {qid=}, {file.id=}, {question.mold=}", error=True)

    logger.info(f"batch_preset_question_answer completed, {qids=}, {fid=}")
    questions = await NewQuestion.find_by_fid(fid)
    for question in questions:
        await question.set_answer()


@app.task(autoretry_for=(PushError,), retry_kwargs={"max_retries": 3}, retry_backoff=True)
@fail2mm_sync(tags=("preset_answer",))
@loop_wrapper()
async def preset_question_answer(qid):
    question = await NewQuestion.find_by_id(qid)
    if not question:
        raise CustomError(f"preset_answer not found question, {qid=}")

    file = await NewFile.find_by_id(question.fid)
    if not file:
        raise CustomError("preset_answer not found file")

    await run_predict_question_answer(file, question)


@app.task(autoretry_for=(PushError,), retry_kwargs={"max_retries": 3}, retry_backoff=True)
@fail2mm_sync(tags=("gen_interdoc",))
@loop_wrapper()
async def request_interdoc(fid: int):
    file = await NewFile.find_by_id(fid)
    if not file:
        logger.warning(f"request_interdoc not found file, {fid=}")
    await gen_interdoc(file)


async def run_predict_question_answer(file: NewFile, question: NewQuestion):
    # 刷新file.updated_utc
    await pw_db.update(file)

    question.ai_status = AIStatus.DOING.value
    await pw_db.update(question)
    # 生成中间表数据
    await question.sync_answer_result()
    if (
        file.pdfinsight is None
        or not localstorage.exists(file.pdfinsight_path())
        or (get_config("web.parser.mode", "pdf") == "docx" and file.docx is None)
    ):
        # pdfinsight数据缺失，重新发起解析
        logger.info(f"file {file.id} need to be parsed again")
        await gen_interdoc(file)
        return

    answer = await predict_answer(file, question)

    if question.mold in special_mold.esg_mids:
        # 检测esg答案中E/ND的比例是否过高
        await check_e_nd_nums(answer, question)

    if EmptyAnswer.all_null(answer):
        msg = (
            f"Got empty preset answer(fid: {file.id}, qid: {question.id}, "
            f"mold: {question.mold}, file name: {file.name}),"
            f" please check your data"
        )
        logging.warning(msg)
        if question.mold not in (special_mold.v3d_id, special_mold.v2a_id):  # HARDCODE
            mm_notify(msg, error=True, tags=("preset_answer", "empty_answer"))
        return
    question.preset_answer = answer
    await pw_db.update(question)

    logging.info(f"run_inspect_rule for file: {file.id}, question: {question.id}, mold: {question.mold}")
    # inspect-rule 并未返回 更新后的 question instance
    await run_inspect_rule(question)

    await question.set_answer()

    await collect_compliance_answer(question.id, fid=file.id)

    question.ai_status = AIStatus.FINISH.value
    await pw_db.update(question, only=["ai_status"])
    logger.info(f"run_predict_question_answer finish, {file.id=}, {question.id=}, {question.mold=}")


@app.task
@loop_wrapper()
async def inspect_rule(qid):
    question = await NewQuestion.find_by_id(qid)
    if not question:
        raise CustomError("inspect_rule not found question, id %s" % qid)
    await run_inspect_rule(question)
    await question.set_answer()


async def run_inspect_rule(question):
    file = await NewFile.find_by_id(question.fid)
    if not file:
        raise CustomError("inspect_rule not found file, id %s" % question.fid)

    if get_config("web.inspect_rules", False):
        await question.merge_answer()
        await inspect_rules(file, question)


@app.task
@loop_wrapper()
async def recruit_crud_answer(fid, mold_id=None):
    """重新生成初步定位答案"""
    if mold_id is None:
        questions = await NewQuestion.find_by_fid(fid)
    else:
        questions = [await NewQuestion.find_by_fid_mid(fid, mold_id)]

    for question in questions:
        await recruit_crude_answer(question)


@app.task
@sync
async def save_embeddings(file_id, need_sub_element):
    await create_and_save_embeddings(file_id, need_sub_element)


@app.task
@sync
async def set_agm_meta_info(file_id):
    await set_agm_meta(file_id)


@app.task
@sync
async def set_poll_meta_info(file_id):
    await set_poll_meta(file_id)


@app.task
@sync
async def set_mr_meta_info(file_id):
    await set_mr_meta(file_id)


async def recruit_crude_answer(question: NewQuestion) -> dict | None:
    file = await NewFile.find_by_id(question.fid)
    if not file:
        raise CustomError("recruit_preset_answer not found file, id %s" % file.id)

    if file.pdfinsight is None:
        raise CustomError("file %s have no pdfinsight" % file.id)

    crude_answer = None
    try:
        if get_config("web.predict_crude_elements", True):
            logging.info(f"prompt element for file: {file.id}, mold : {question.mold}")
            crude_answer = await predict_crude_answer(file, mid=question.mold)
            await save_crude_answer(crude_answer, file.id, question)
        else:
            logging.warning(f"prompt answer is disabled， file: {file.id}, question: {question.id}")
    except Exception as ex:
        logging.exception(f"error in prompt element for file fid={file.id}, {ex}")
    return crude_answer or question.crude_answer


@app.task
@loop_wrapper()
async def make_question(fid, mid: int | None = None):
    await _make_question(fid, mid)


async def _make_question(fid, mid: int | None = None, cache_pdf=True):
    # NOTE: 有 celery task, 需要在事务外调用
    file = await NewFile.find_by_id(fid)
    if not file:
        raise Exception("file not found(file_id: %s)" % fid)

    file_project = await NewFileProject.find_by_id(file.pid)
    if file_project and file_project.name == get_config("project_list.addition_files.name"):
        logger.info(f"make_question skip addition_files: {file.id}")
        return

    if file.pdf is None:
        await convert2pdf(file)
    if cache_pdf:
        cache_pdf_file.delay(fid)

    if not file.mold_list:
        logging.error("file %s has no mold", fid)
        return

    existed_questions = await NewQuestion.find_by_fid(fid, include_deleted=True)
    new_qid_list = []
    for mold in file.mold_list or []:
        exists = [q for q in existed_questions if q.mold == mold]
        if exists:
            for _ques in exists:
                if _ques.deleted_utc:
                    _ques.deleted_utc = 0
                    await pw_db.update(_ques, only=["deleted_utc"])
                else:
                    # 已存在的question也应该重跑预测
                    new_qid_list.append((_ques.id, _ques.mold))
        else:
            file_question = await NewQuestion.create_question(file.id, mold)
            new_qid_list.append((file_question.id, mold))

    for question_to_delete in [q for q in existed_questions if q.deleted_utc == 0 and q.mold not in file.mold_list]:
        await pw_db.delete(question_to_delete)

    # 兼容：file.qid 只第一个 question
    if new_qid_list:
        file.qid = new_qid_list[0][0]
        await pw_db.update(file, only=["qid"])
    mid_qid_map = {v: k for k, v in dict(new_qid_list).items()}
    if mid:
        await NewQuestion.update_by_fid_or_mid(file.id, mid=mid, ai_status=AIStatus.TODO.value)
        preset_question_answer.delay(mid_qid_map[mid])
        return

    if special_mold.v3_id in file.mold_list:
        ar_qids = []
        # 年报 ESG 文件，先执行 ar_esg 的预测，再执行 policy ar esg 的预测
        if jura21_helper_qid := mid_qid_map.pop(special_mold.jura21_helper_id, None):
            ar_qids.append(jura21_helper_qid)
        if ar_esg_qid := mid_qid_map.pop(special_mold.ar_esg_id, None):
            ar_qids.append(ar_esg_qid)
        ar_qids.extend(qid for qid in mid_qid_map.values())
        if ar_qids:
            batch_preset_question_answer.delay(ar_qids, fid)

        await pw_db.execute(NewQuestion.update(ai_status=AIStatus.TODO.value).where(NewQuestion.fid == file.id))
    elif special_mold.esg_id in file.mold_list:
        # 独立 ESG 文件，先执行 esg 的预测，再执行 policy esg 的预测
        esg_qids = []
        if esg_qid := mid_qid_map.pop(special_mold.esg_id, None):
            esg_qids.append(esg_qid)
        else:
            logging.warning(f"esg_id not found in {file.id=} {file.mold_list=}")
        if policy_esg_qid := mid_qid_map.pop(special_mold.policy_esg_id, None):
            esg_qids.append(policy_esg_qid)
        else:
            logging.warning(f"policy_esg_id not found in {file.id=} {file.mold_list=}")
        await pw_db.execute(NewQuestion.update(ai_status=AIStatus.TODO.value).where(NewQuestion.fid == file.id))
        if esg_qids:
            batch_preset_question_answer.delay(esg_qids, fid)
    else:
        for qid, mold in new_qid_list:
            await NewQuestion.update_by_fid_or_mid(file.id, mid=mold, ai_status=AIStatus.TODO.value)
            preset_question_answer.delay(qid)


@app.task
def call_crawler(**kwargs):
    """
    下发爬虫任务(headstream worker会自动启动爬虫按传递参数执行爬取任务)
        1. 不带参数(爬取最近1天)
        2. 不指定股票代码参数爬取所有文档(可不带截止日, 默认以运行日期为截止日): start='20180716', end='20180717'
        3. 爬取指定股票代码外部文档(不指定起止日默认爬取最近一天): stock_code='00001'
        4. 爬取股票回购报告: xls=True
        5. 爬取年报(ar): doc_type='ar' ar=True
    :param kwargs: [start, end, stock_code, doc_type, netloc, xls]
    :return: None
    """
    crawler_name = get_config("crawler.name")
    queue = get_config("crawler.queue")
    api = get_config("crawler.scrapyd_api")
    # 追加文档推送目标
    url = "{}://{}".format(get_config("web.scheme", "http"), get_config("web.domain"))
    kwargs.setdefault("netloc", url)
    if api and is_valid_server(api):
        kwargs.setdefault("spider", "hkex_ext")
        kwargs.setdefault("project", "default")
        rsp = requests.post(
            api,
            params=kwargs,
            auth=(get_config("crawler.uname"), get_config("crawler.passwd")),
            timeout=10,
        )
        logging.info(rsp.text)
    else:
        app.send_task(crawler_name, kwargs=kwargs, queue=queue)


@app.task
@loop_wrapper()
async def load_addition_data(fid: int, tree_id: int):
    await load_addition_data_by_fid_tid(fid, tree_id)
