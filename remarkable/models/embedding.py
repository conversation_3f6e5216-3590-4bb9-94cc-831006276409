from typing import Union

import peewee
from peewee import Integer<PERSON>ield, Text<PERSON>ield
from pgvector.peewee import <PERSON><PERSON><PERSON><PERSON>
from playhouse.postgres_ext import <PERSON><PERSON><PERSON><PERSON><PERSON>, JSONField, TSVectorField
from speedy.peewee_plus import orm
from utensils.util import generate_timestamp

from remarkable.common.util import expand_query, text2embedding
from remarkable.db import embedding_pw_db
from remarkable.models import EmbeddingBaseMode

RRF_K = 50
FULL_TEXT_WEIGHT = 1.0
SEMANTIC_WEIGHT = 5.0


class SyllabusEmbedding(EmbeddingBaseMode):
    file_id = IntegerField()
    syllabus = ArrayField()
    embedding = VectorField(1536)
    indexes = ArrayField()
    metadata = JSONField()

    created_utc = peewee.BigIntegerField(default=generate_timestamp)
    updated_utc = peewee.BigIntegerField(default=generate_timestamp)
    deleted_utc = peewee.IntegerField(default=0)

    def __str__(self):
        return f"SyllabusEmbedding {self.file_id}:{self.syllabus}"

    @classmethod
    async def semantic_search(cls, file_ids, query_embedding, limit=20):
        if file_ids is None:
            file_ids = []
        elif not isinstance(file_ids, (list, tuple)):
            file_ids = [file_ids]
        distance_col = cls.embedding.cosine_distance(query_embedding)
        score_col = 1 - distance_col
        query = (
            cls.select(
                cls.syllabus,
                cls.indexes,
                score_col.alias("score"),
            )
            .where(cls.file_id.in_(file_ids))
            .order_by(score_col.desc())
            .limit(limit)
            .dicts()
        )
        items = list(await embedding_pw_db.execute(query))
        return items

    @classmethod
    async def regex_search(cls, file_ids, regex, neglect_regex=None, ignore_case=True, limit=20, desc=True):
        if isinstance(file_ids, (int, str)):
            file_ids = [file_ids]
        cond = orm.TRUE
        if regex:
            if ignore_case:
                cond &= peewee.fn.array_to_string(cls.syllabus, ",").iregexp(regex)
            else:
                cond &= peewee.fn.array_to_string(cls.syllabus, ",").regexp(regex)
        if neglect_regex:
            if ignore_case:
                cond &= ~peewee.fn.array_to_string(cls.syllabus, ",").iregexp(neglect_regex)
            else:
                cond &= ~peewee.fn.array_to_string(cls.syllabus, ",").regexp(neglect_regex)
        query = (
            cls.select(
                cls.file_id,
                cls.syllabus,
                cls.indexes,
            )
            .where(cls.file_id.in_(file_ids))
            .where(cls.deleted_utc == 0)
            .where(cond)
            .order_by(cls.file_id.desc() if desc else cls.file_id)
            .limit(limit)
            .dicts()
        )

        # todo 后续视配置的复杂程度将规则匹配的功能放到查询后
        items = list(await embedding_pw_db.execute(query))
        return items


class Embedding(EmbeddingBaseMode):
    file_id = IntegerField()
    index = IntegerField()
    element_sequence = IntegerField()
    text = TextField()
    embedding = VectorField(1536)
    vector = TSVectorField()
    metadata = JSONField()

    created_utc = peewee.BigIntegerField(default=generate_timestamp)
    updated_utc = peewee.BigIntegerField(default=generate_timestamp)
    deleted_utc = peewee.IntegerField(default=0)

    def __str__(self):
        return f"Embedding {self.file_id}:{self.index}"

    def __setstate__(self, state):
        # todo: 服务端都修改为 element_sequence 之后，可以删除这个方法
        """Custom pickle deserialization to handle field name changes."""
        # Handle position -> element_sequence field name change in __data__
        if "__data__" in state and isinstance(state["__data__"], dict):
            data = state["__data__"]
            if "position" in data and "element_sequence" not in data:
                data["element_sequence"] = data.pop("position")

        # Also handle it in the main state dict (for direct attributes)
        if "position" in state and "element_sequence" not in state:
            state["element_sequence"] = state.pop("position")

        # Set the object state
        self.__dict__.update(state)

    @classmethod
    async def hybrid_search(cls, file_ids, query_text, limit=20):
        full_text_crude_elements = await cls.full_text_search(file_ids, query_text, limit)
        semantic_crude_elements = await cls.semantic_search(file_ids, query_text, limit)
        # TODO  add rrf
        return semantic_crude_elements + full_text_crude_elements

    @classmethod
    async def semantic_search_by_embedding(
        cls,
        file_ids: Union[int, list[int], tuple[int], None],
        embedding: list[float],
        *,
        indexes: list[int] | None = None,
        element_sequence: int | None = None,
        limit: int = 20,
    ):
        if file_ids is None:
            file_ids = []
        elif not isinstance(file_ids, (list, tuple)):
            file_ids = [file_ids]
        distance_col = cls.embedding.cosine_distance(embedding)
        score_col = 1 - distance_col

        cond = [
            cls.file_id.in_(file_ids),
        ]
        if element_sequence is not None:
            cond.append(cls.element_sequence == element_sequence)
        if indexes:
            cond.append(cls.index.in_(indexes))

        # Subquery to get the highest score for each index
        subquery = (
            cls.select(cls.index, peewee.fn.MAX(score_col).alias("max_score"))
            .where(*cond)
            .group_by(cls.index)
            .alias("subquery")
        )

        # Main query
        query = (
            cls.select(
                cls.file_id,
                cls.index,
                cls.element_sequence,
                cls.text,
                cls.metadata,
                score_col.alias("score"),
            )
            .join(subquery, on=((cls.index == subquery.c.index) & (score_col == subquery.c.max_score)))
            .where(*cond)
            .order_by(score_col.desc())
            .limit(limit)
            .dicts()
        )
        items = list(await embedding_pw_db.execute(query))
        return items

    @classmethod
    async def semantic_search(
        cls,
        file_ids,
        query_text,
        limit=20,
        indexes=None,
        element_sequence: int | None = None,
    ):
        question_embedding = text2embedding(query_text)
        return await cls.semantic_search_by_embedding(
            file_ids,
            question_embedding,
            limit=limit,
            indexes=indexes,
            element_sequence=element_sequence,
        )

    @classmethod
    async def full_text_search(
        cls,
        file_ids,
        query_text,
        limit=20,
        indexes=None,
        element_sequence: int | None = None,
    ):
        if file_ids is None:
            file_ids = []
        elif not isinstance(file_ids, (list, tuple)):
            file_ids = [file_ids]
        file_ids_str = "(" + ",".join(str(i) for i in file_ids) + ")"

        query_text = expand_query(query_text)
        sql = f"""
            SELECT file_id, index, element_sequence, text, metadata, ts_rank_cd(vector, query, 32) AS score
            FROM embedding, to_tsquery('english', '{query_text}') query
            WHERE  query @@ vector
            AND file_id IN {file_ids_str}
        """
        if element_sequence is not None:
            sql += f" AND element_sequence = {element_sequence}"
        if indexes:
            sql += f" AND index IN {'(' + ','.join(str(i) for i in indexes) + ')'}"
        sql += f"""
        AND deleted_utc = 0
        ORDER BY score DESC
        LIMIT {limit};
        """
        results = list(await embedding_pw_db.execute(sql, default_row_type=peewee.ROW.DICT))
        return results

    @classmethod
    async def regex_search(
        cls,
        file_ids,
        regex,
        ignore_case=True,
        limit=20,
        desc=True,
        element_sequence: int | None = None,
        only_sub_element=False,
        indexes=None,
    ):
        if file_ids is None:
            file_ids = []
        elif not isinstance(file_ids, (list, tuple)):
            file_ids = [file_ids]

        if ignore_case:
            regex_match = cls.text.iregexp(regex)
        else:
            regex_match = cls.text.regexp(regex)

        subquery = (
            cls.select(cls.index).where(cls.file_id.in_(file_ids), regex_match).group_by(cls.index).alias("subquery")
        )
        cond = [cls.file_id.in_(file_ids)]
        if element_sequence is not None:
            cond.append(cls.element_sequence == element_sequence)
        if only_sub_element:
            cond.append(cls.element_sequence != 0)
        if indexes:
            cond.append(cls.index.in_(indexes))

        query = (
            cls.select(
                cls.file_id,
                cls.index,
                cls.element_sequence,
                cls.text,
                cls.metadata,
            )
            .join(subquery, on=((cls.index == subquery.c.index) & regex_match))
            .where(*cond)
            .order_by(cls.id.desc() if desc else cls.id)
            .limit(limit)
            .dicts()
        )
        items = list(await embedding_pw_db.execute(query))
        return items

    @classmethod
    async def regex_search_with_score(
        cls,
        file_ids,
        regex,
        query_text,
        ignore_case=True,
        limit=20,
        desc=True,
        element_sequence: int | None = None,
        only_sub_element=False,
        indexes=None,
    ):
        if file_ids is None:
            file_ids = []
        elif not isinstance(file_ids, (list, tuple)):
            file_ids = [file_ids]

        file_ids_str = "(" + ",".join(str(i) for i in file_ids) + ")"

        if ignore_case:
            matced_reg = f" ~* '{regex}'"
        else:
            matced_reg = f" ~ '{regex}'"

        query_text = expand_query(query_text)
        sql = f"""
        SELECT file_id, index, element_sequence, text, metadata, ts_rank_cd(vector, query, 32) AS score
        FROM embedding ,to_tsquery('english', '{query_text}') query
        WHERE
        """
        if file_ids:
            sql += f" file_id IN {file_ids_str} AND text {matced_reg}"
        else:
            sql += f" text {matced_reg}"

        if element_sequence is not None:
            sql += f" AND element_sequence = {element_sequence}"
        if only_sub_element:
            sql += " AND element_sequence != 0"
        if indexes:
            sql += f" AND index IN {'(' + ','.join(str(i) for i in indexes) + ')'}"

        sql += f"""
        AND deleted_utc = 0
        ORDER BY score {"desc" if desc else ""}
        LIMIT {limit};
        """
        results = list(await embedding_pw_db.execute(sql, default_row_type=peewee.ROW.DICT))
        return results
