import collections
import logging
from datetime import datetime, time, timedelta
from itertools import groupby
from typing import List, Optional, Self, Type

import peewee
from playhouse.postgres_ext import Binary<PERSON><PERSON><PERSON><PERSON>
from speedy.schemas import T

from remarkable.common.constants import DEFAULT_REPORT_YEAR, DocType
from remarkable.config import get_config
from remarkable.db import pw_db
from remarkable.models import BaseModel
from remarkable.models.file_esg_xref import FileESGxREF
from remarkable.models.mold import special_mold
from remarkable.models.new_file import NewFile
from remarkable.models.new_question import NewQuestion
from remarkable.plugins.hkex.utils import standard_stock
from remarkable.schemas import TRuleType

logger = logging.getLogger(__name__)


class HKEXFileMeta(BaseModel):
    fid = peewee.IntegerField(null=False, unique=True)
    qid = peewee.IntegerField()
    stock_code = peewee.CharField(null=False)
    report_year = peewee.Char<PERSON>ield()
    doc_type = peewee.Integer<PERSON>ield(null=False)
    name = peewee.Char<PERSON>ield()
    published = peewee.CharField()
    year_end = peewee.CharField()
    stat_res = BinaryJSONField()
    deleted_utc = peewee.IntegerField(default=0)

    def __str__(self):
        return f"{self.stock_code}_{self.report_year}_doc_type:{self.doc_type}"

    @property
    def published_at(self) -> datetime:
        return datetime.fromisoformat(f"{self.published}+08:00")

    @classmethod
    async def get_by_stock_code(cls, stock_code: str, report_year: str = None):
        cond = cls.stock_code == standard_stock(stock_code)
        if report_year:
            cond &= cls.report_year == report_year
        return await cls.manager().execute(cls.select().filter(cond))

    @classmethod
    async def get_first_by_stock_code(cls, stock_code: str, doc_type: int, report_year: str = None) -> Self | None:
        where_conditions = [cls.stock_code == standard_stock(stock_code), cls.doc_type == doc_type]
        if report_year:
            where_conditions.append(cls.report_year == report_year)
        return await cls.manager().first(cls.select().where(*where_conditions).order_by(cls.id.desc()))

    @classmethod
    def sync_find_by_fid(cls, fid, include_deleted=False) -> Optional["HKEXFileMeta"]:
        # 注意：此方法为兼容旧代码所写，会新开一个同步的数据库连接，尽量避免在异步或循环中调用；
        # 不建议在新实现功能、方法中使用
        with cls.manager().allow_sync():
            return cls.select(include_deleted=include_deleted).where(cls.fid == int(fid)).first()

    @classmethod
    async def find_by_fid(cls, fid, lock=False) -> Optional["HKEXFileMeta"]:
        query = cls.select().where(cls.fid == int(fid))
        if lock:
            query = query.for_update()
        return await cls.manager().first(query)

    @classmethod
    async def insert_or_update(cls, *, conflict_target=None, **kwargs) -> "HKEXFileMeta":
        fid = kwargs.get("fid", None)
        if fid:
            cond = cls.fid == fid
            hkex_file_meta = await cls.manager().first(cls.select().where(cond))
            if hkex_file_meta:
                if "id" in kwargs and get_config("app.app_id").endswith("_test"):
                    # 删除UAT环境可能存在的错误数据，如profit warning文档测试时的遗留冲突id
                    # 冲突处理原则：远端优先
                    cond = cls.id == kwargs.pop("id")
                    file_meta = await cls.manager().first(cls.select().where(cond))
                    if file_meta and file_meta.fid != fid:
                        file = await cls.manager().first(NewFile.select().where(NewFile.id == file_meta.fid))
                        if file:
                            await file.delete_all_related_rows()
                await cls.manager().execute(cls.update(**kwargs).where(cond))
                return await cls.manager().first(cls.select().where(cond))
        return await super(HKEXFileMeta, cls).insert_or_update(conflict_target=conflict_target, **kwargs)

    @classmethod
    def find_previous_year_metas(cls, fid, doc_type=None) -> list[Self]:
        with cls.manager().allow_sync():
            current_meta = cls.get_or_none(cls.fid == int(fid))
            if not current_meta:
                return []
            cond = [cls.stock_code == current_meta.stock_code]
            if doc_type:
                cond.append(cls.doc_type == doc_type)
            last_year = int(current_meta.report_year) - 1
            items = cls.select().where(*cond, cls.report_year == str(last_year)).order_by(cls.published.desc())
            # 上一年未找到，尝试再向上找一年，如果year_end不同，说明是年结日变更，允许跨年： https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7712
            if not items and (
                last_items := cls.select()
                .where(*cond, cls.report_year == str(last_year - 1))
                .order_by(cls.published.desc())
            ):
                last_items = list(last_items)
                year_end = datetime.strptime(current_meta.year_end, "%d %b %Y")
                last_year_end = datetime.strptime(last_items[0].year_end, "%d %b %Y")
                if (last_year_end.month, last_year_end.day) != (year_end.month, last_year_end.day):
                    return last_items
            return list(items)

    @classmethod
    async def find_min_max_years_in_db(
        cls,
        doc_types: List[TRuleType],
        include_deleted: bool = False,
        stock_code: str | None = None,
    ) -> List[int]:
        types = []
        for doc_type in doc_types:
            doc_type = doc_type.lower()
            if doc_type in ("ar", "cg"):
                types.append(DocType.AR)
            elif doc_type == "qr":
                types.extend([DocType.Q1, DocType.INTERIM, DocType.Q3, DocType.FINAL])
            elif doc_type == "esg":
                types.extend([DocType.ESG, DocType.AR])
            elif doc_type == "agm":
                types.append(DocType.AGM)
            elif doc_type == "poll":
                types.append(DocType.POLL)
        cond = cls.doc_type.in_(types)
        if stock_code:
            cond &= cls.stock_code == stock_code
        years = list(
            await cls.manager().scalar(
                cls.select(
                    peewee.fn.MIN(cls.report_year.cast("int")),
                    peewee.fn.MAX(cls.report_year.cast("int")),
                    include_deleted=include_deleted,
                ).where(cond),
                as_tuple=True,
            )
        )
        return years

    @classmethod
    async def find_min_max_years(
        cls,
        doc_types: List[TRuleType],
        include_deleted: bool = False,
        stock_code: str | None = None,
    ) -> List[int]:
        years = await cls.find_min_max_years_in_db(doc_types, include_deleted=include_deleted, stock_code=stock_code)
        # 保证最小年份为默认年份
        min_year = max(years[0] or 0, DEFAULT_REPORT_YEAR)
        # 保证最大年份为当前年份+1，防止未来年份的数据，大概率是FY解析错误
        max_year = min(years[1] or DEFAULT_REPORT_YEAR, datetime.now().year + 1)
        if (years[1] or max_year) - (years[0] or min_year) >= 10:
            logger.warning(f'Possible error years: "{years}" detected, please check!')
        return [min_year, max_year]

    @classmethod
    async def find_available_years(cls, *doc_types: TRuleType, all_available=False) -> List[int]:
        types = []
        for doc_type in doc_types:
            doc_type = doc_type.lower()
            if doc_type in {"ar", "cg"}:
                types.append(DocType.AR)
            elif doc_type == "qr":
                types.extend([DocType.Q1, DocType.INTERIM, DocType.Q3, DocType.FINAL])
            elif doc_type == "esg":
                types.append(DocType.ESG)
            elif doc_type == "agm":
                types.append(DocType.AGM)
            elif doc_type == "poll":
                types.append(DocType.POLL)
        cond = cls.doc_type.in_(types)
        available_years = await cls.manager().scalars(
            cls.select(cls.report_year.cast("int")).where(cond).distinct().order_by(cls.report_year.cast("int").desc())
        )
        min_y, max_y = await cls.find_min_max_years(list(doc_types))
        if all_available:
            min_y = 0
        return [y for y in available_years if min_y <= y <= max_y]

    @classmethod
    def sync_find_available_years(cls, doc_types: List[TRuleType]) -> List[int]:
        types = []
        for doc_type in doc_types:
            doc_type = doc_type.lower()
            if doc_type == "ar":
                types.append(DocType.AR)
            elif doc_type == "qr":
                types.extend([DocType.Q1, DocType.INTERIM, DocType.Q3, DocType.FINAL])
            elif doc_type == "esg":
                types.append(DocType.ESG)
            elif doc_type == "agm":
                types.append(DocType.AGM)
            elif doc_type == "poll":
                types.append(DocType.POLL)
        with pw_db.allow_sync():
            min_year, max_year = (
                cls.select(
                    peewee.fn.MIN(cls.report_year.cast("int")),
                    peewee.fn.MAX(cls.report_year.cast("int")),
                )
                .where(cls.doc_type.in_(types))
                .scalar(as_tuple=True)
            )
            min_year = max(int(min_year), DEFAULT_REPORT_YEAR)
            max_year = min(int(max_year), datetime.now().year + 1)
            report_years = list(range(min_year, max_year + 1))
        return report_years

    @classmethod
    async def get_file_metas(
        cls, annual_report=False, quarterly_report=False, stock_code=None, report_years=None, include_deleted=False
    ):
        """
        同一code、同一type、同一year、存在多份报告，按照published，report_year，fid排序分组后取第一个
        默认返回所有的文档
        annual_report 为True， 返回所有的年报meta
        quarterly_report 为True， 返回所有的季报meta
        指定 stock_code 时， 返回对应stock_code的meta
        """
        doc_types = []
        if quarterly_report:
            doc_types.append("qr")
        if annual_report:
            doc_types.append("ar")
        min_year, _ = await cls.find_min_max_years(doc_types, include_deleted=include_deleted)

        cond = NewFile.deleted_utc == 0
        if report_years:
            cond &= cls.report_year.in_(report_years)
        else:
            cond &= cls.report_year >= min_year
        if stock_code:
            cond &= cls.stock_code == stock_code
        if quarterly_report:
            cond &= cls.doc_type >= DocType.Q1
            cond &= cls.doc_type <= DocType.FINAL
        if annual_report:
            cond &= cls.doc_type == DocType.AR

        window = peewee.Window(
            partition_by=[cls.stock_code, cls.report_year, cls.doc_type],
            order_by=[cls.published.desc(), cls.report_year.desc(), cls.fid.desc()],
        )
        subquery = (
            cls.select(
                cls.fid,
                NewQuestion.id.alias("qid"),
                cls.doc_type,
                cls.stat_res,
                cls.report_year,
                cls.stock_code,
                cls.name,
                cls.published,
                cls.year_end,
                peewee.fn.RANK().over(window),
                include_deleted=include_deleted,
            )
            .join(NewFile, on=(cls.fid == NewFile.id))
            .join(NewQuestion, on=(cls.fid == NewQuestion.fid))
            .where(cond)
            .window(window)
            .order_by(cls.name.desc())
        )
        query = (
            cls.select(
                subquery.c.fid,
                subquery.c.qid,
                subquery.c.doc_type,
                subquery.c.stat_res,
                subquery.c.report_year,
                subquery.c.stock_code,
                subquery.c.name,
                subquery.c.published,
                subquery.c.year_end,
                include_deleted=include_deleted,
            )
            .from_(subquery)
            .join(cls, on=(cls.fid == subquery.c.fid), include_deleted=include_deleted)
            .where(subquery.c.rank == 1)
        )
        file_metas = await cls.manager().execute(query)
        return file_metas

    @classmethod
    def sync_get_file_metas(
        cls,
        annual_report=False,
        quarterly_report=False,
        stock_code=None,
        report_years=None,
        ignore_update_utc=False,
        include_deleted=False,
    ):
        """
        同一code、同一type、同一year、存在多份报告，按照published，report_year，fid排序分组后取第一个
        默认返回所有的文档
        annual_report 为True， 返回所有的年报meta
        quarterly_report 为True， 返回所有的季报meta
        指定 stock_code 时， 返回对应stock_code的meta
        """
        doc_types = []
        if quarterly_report:
            doc_types.extend([DocType.Q1, DocType.INTERIM, DocType.Q3, DocType.FINAL])
        if annual_report:
            doc_types.append(DocType.AR)

        min_year = (
            cls.select(peewee.fn.MIN(cls.report_year.cast("int")), include_deleted=include_deleted)
            .where(cls.doc_type.in_(doc_types))
            .scalar()
        )
        if not min_year:
            min_year = DEFAULT_REPORT_YEAR
        min_year = max(int(min_year), DEFAULT_REPORT_YEAR)

        cond = NewFile.deleted_utc == 0
        if not ignore_update_utc:
            previous_day = datetime.today() - timedelta(days=1)
            previous_day = datetime.combine(previous_day, time.min)  # 0点
            previous_day_timestamp = previous_day.timestamp()
            cond &= NewQuestion.updated_utc > previous_day_timestamp
        if report_years:
            cond &= cls.report_year.in_(report_years)
        else:
            cond &= cls.report_year >= min_year
        if stock_code:
            cond &= cls.stock_code == stock_code
        if quarterly_report:
            cond &= cls.doc_type >= DocType.Q1
            cond &= cls.doc_type <= DocType.FINAL
        if annual_report:
            cond &= cls.doc_type == DocType.AR

        window = peewee.Window(
            partition_by=[cls.stock_code, cls.report_year, cls.doc_type],
            order_by=[cls.published.desc(), cls.report_year.desc(), cls.fid.desc()],
        )
        subquery = (
            cls.select(
                cls.fid,
                NewQuestion.id.alias("qid"),
                cls.doc_type,
                cls.stat_res,
                cls.report_year,
                cls.stock_code,
                cls.name,
                cls.published,
                cls.year_end,
                peewee.fn.RANK().over(window),
                include_deleted=include_deleted,
            )
            .join(NewFile, on=(cls.fid == NewFile.id))
            .join(NewQuestion, on=(cls.fid == NewQuestion.fid))
            .where(cond)
            .window(window)
            .order_by(cls.name.desc())
        )
        query = (
            cls.select(
                subquery.c.fid,
                subquery.c.qid,
                subquery.c.doc_type,
                subquery.c.stat_res,
                subquery.c.report_year,
                subquery.c.stock_code,
                subquery.c.name,
                subquery.c.published,
                subquery.c.year_end,
                include_deleted=include_deleted,
            )
            .from_(subquery)
            .join(cls, on=(cls.fid == subquery.c.fid), include_deleted=include_deleted)
            .where(subquery.c.rank == 1)
        )
        return query.execute()

    @classmethod
    async def delete_by_fid(cls, fid):
        await pw_db.execute(cls.delete().where(cls.fid == fid))

    @classmethod
    async def get_special_file_metas(cls, stock_code, include_deleted=False):
        """
        只能给下面三个接口调用
        issuer_header issuer_body rule_history
        """
        cond = NewQuestion.mold == special_mold.v1_id
        cond &= cls.stock_code == stock_code
        cond &= cls.doc_type == DocType.AR.value
        cond &= cls.report_year >= DEFAULT_REPORT_YEAR
        if not include_deleted:
            cond &= NewFile.deleted_utc == 0
            cond &= cls.deleted_utc == 0
        window = peewee.Window(
            partition_by=[cls.stock_code, cls.report_year, cls.doc_type],
            order_by=[cls.published.desc(), cls.report_year.desc(), cls.fid.desc()],
        )
        subquery = (
            cls.select(
                cls.fid,
                NewQuestion.id.alias("qid"),
                cls.doc_type,
                cls.stat_res,
                cls.report_year,
                cls.stock_code,
                cls.name,
                cls.published,
                cls.year_end,
                peewee.fn.RANK().over(window),
                include_deleted=include_deleted,
            )
            .join(NewFile, on=(cls.fid == NewFile.id))
            .join(NewQuestion, on=(cls.fid == NewQuestion.fid))
            .where(cond)
            .window(window)
            .order_by(cls.name.desc())
        )
        query = (
            cls.select(
                subquery.c.fid,
                subquery.c.qid,
                subquery.c.doc_type,
                subquery.c.stat_res,
                subquery.c.report_year,
                subquery.c.stock_code,
                subquery.c.name.alias("company_name"),
                subquery.c.published,
                subquery.c.year_end,
                include_deleted=include_deleted,
            )
            .from_(subquery)
            .join(cls, on=(cls.fid == subquery.c.fid), include_deleted=include_deleted)
            .where(subquery.c.rank == 1)
        )
        file_metas = await cls.manager().execute(query)
        return file_metas

    @classmethod
    async def get_esg_file_metas(
        cls: Type[T], stock_code: str | None = None, report_year: str | None = None
    ) -> List[T]:
        cond = FileESGxREF.activated
        cond &= NewQuestion.mold.in_(special_mold.esg_mids)
        if stock_code:
            cond &= cls.stock_code == stock_code
        if report_year:
            cond &= cls.report_year == report_year

        query = (
            cls.select(
                cls.fid.alias("file_id"),
                cls.report_year,
                NewQuestion.id.alias("question_id"),
                NewQuestion.mold.alias("mold_id"),
                NewFile.pid.alias("project_id"),
            )
            .join(NewQuestion, on=(NewQuestion.fid == cls.fid))
            .join(NewFile, on=(NewFile.id == cls.fid))
            .join(FileESGxREF, on=(FileESGxREF.fid == cls.fid))
            .filter(cond)
            .order_by(cls.published.desc())
            .dicts()
        )
        row = await pw_db.execute(query)
        return row

    @classmethod
    def get_last_year_question(cls, fid, mid, doc_type):
        file_metas = HKEXFileMeta.find_previous_year_metas(fid, doc_type)
        if not file_metas:
            return None
        # 若多份final、取预测时最新已发布的那份
        with pw_db.allow_sync():
            question = NewQuestion.get_or_none(((NewQuestion.fid == file_metas[0].fid) & (NewQuestion.mold == mid)))
            return question


class DelistedFileMeta(HKEXFileMeta):
    @staticmethod
    def group_by_continuous_years(file_metas):
        delist_file_metas_dict = collections.defaultdict(list)
        for file_meta in file_metas:
            delist_file_metas_dict[int(file_meta.report_year)].append(file_meta)
        delist_file_metas_dict = dict(sorted(delist_file_metas_dict.items(), key=lambda x: x[0]))
        grouped_delist_file_metas = collections.defaultdict(list)
        for _, file_metas in groupby(enumerate(delist_file_metas_dict), lambda x: x[1] - x[0]):
            items = []
            for _, i in file_metas:
                items.extend(delist_file_metas_dict[i])
            if not items:
                continue
            items = items[::-1]
            grouped_delist_file_metas[items[0].name].extend(items)
        return grouped_delist_file_metas
