import re

from remarkable.common.constants import DocType, PollGMLRules
from remarkable.config import get_config

P_RULE_A_D = re.compile(r"^[A-D]\d*\.?\d*$")
P_NEW_RULE_C_1_X_1 = re.compile(r"^C[127]\.[124]\.1$")


class SpecialMold:
    _mold_dict = get_config("schema") or {}
    _id_map = {m["id"]: m for m in _mold_dict.values()}
    ar_esg_id = get_config("esg_molds.AR.id", 1)  # Jura4 AR-ESG
    esg_id = get_config("esg_molds.ESG.id", 2)  # Jura4 ESG
    v1_id = _mold_dict.get("v1", {}).get("id", 5)
    v2_id = _mold_dict.get("v2", {}).get("id", 15)
    v2a_id = _mold_dict.get("v2a", {}).get("id", 12)
    v3_id = _mold_dict.get("v3", {}).get("id", 18)
    v3d_id = _mold_dict.get("v3d", {}).get("id", 22)
    v3r_id = _mold_dict.get("v3r", {}).get("id", 24)  # Jura 3.0 Ratio Checking
    v3dr_id = _mold_dict.get("v3dr", {}).get("id", 26)  # Jura 3.0 Disclosure Checking
    v5_id = get_config("cg_mold.id", 28)  # Jura5 CG Report
    jura21_helper_id = get_config("jura21_helper_id.id", 29)  # Jura21 helper
    policy_ar_id = get_config("esg_molds.Policy-AR.id", 31)  # Jura6 Policy-AR
    policy_esg_id = get_config("esg_molds.Policy-ESG.id", 32)  # Jura6 Policy-ESG
    v6_agm_id = get_config("v6_agm_id.id", 33)  # Jura6 AGM
    v6_poll_id = get_config("v6_poll_id.id", 34)  # Jura6 POLL
    v6_nddr_id = get_config("v6_nddr_id.id", 35)  # Jura6 NDDR
    v6_1_mr_id = get_config("v6_1_mr.id", 36)  # Jura6.1 Monthly Return
    v6_1_agm_id = get_config("v6_1_agm.id", 37)  # Jura6.1 AGM
    v6_1_poll_id = get_config("v6_1_poll.id", 38)  # Jura6.1 POLL

    @classmethod
    def valid_mids(cls):
        return list(cls._id_map)

    @property
    def qr_mids(self):
        """
        Performance Announcement: 季度报告
        Jura 3.0 Ratio Checking
        """
        return [v["id"] for k, v in self._mold_dict.items() if k[-1] in ("r",)]

    @property
    def ar_mids(self):
        """Annual Report: 年报"""
        return [v["id"] for k, v in self._mold_dict.items() if k[-1] not in ("a", "r")]

    @property
    def ar_mids_with_helper(self):
        """Annual Report: 年报"""
        return self.ar_mids + [self.jura21_helper_id]

    @property
    def ar_mids_with_esg_cg(self):
        return [self.ar_esg_id, self.policy_ar_id, self.v5_id] + self.ar_mids

    @property
    def all_ar_mids(self):
        return [self.ar_esg_id, self.policy_ar_id, self.v5_id, self.jura21_helper_id] + self.ar_mids

    @property
    def need_embedding_mids(self):
        # AR  ESG  AGM POLL
        return self.all_ar_mids + [self.v6_agm_id, self.v6_poll_id, self.policy_esg_id, self.esg_id]

    @property
    def add_mids(self):
        """Additional Docs: 关联外部文档"""
        return [v["id"] for k, v in self._mold_dict.items() if k[-1] in ("a",)]

    def is_add_doc(self, mold_id):
        return mold_id in self.add_mids

    @classmethod
    def get_mid_by_rule(cls, rule):
        # TODO: need to pass root name later on
        from remarkable.common.common import V3DR_RULE_PATTERN, V3R_RULE_PATTERN

        if not rule:
            return None
        if rule == PollGMLRules.GML_TITLE_CASE:
            return cls.v6_poll_id
        if "|" in rule:
            rule = rule.split("|")[0]
        # https://mm.paodingai.com/cheftin/pl/r3yut6ftetrw7gek17wwdz5o5e
        if P_RULE_A_D.search(rule) or P_NEW_RULE_C_1_X_1.search(rule):
            # 兼容旧接口
            if rule.startswith("A"):
                return cls.v1_id
            if rule.startswith("B"):
                return cls.v2_id
            if rule.startswith("C"):
                return cls.v3_id
            if rule.startswith("D"):
                return cls.v3d_id
        if V3DR_RULE_PATTERN.nexts(rule):
            return cls.v3dr_id
        if V3R_RULE_PATTERN.nexts(rule):
            return cls.v3r_id
        for ver, item in cls._mold_dict.items():
            if item.get("name") == rule:
                return getattr(cls, f"{ver}_id")
        return None

    # def is_valid_rule(self, rule):
    #     return bool(self.get_mid_by_rule(rule))

    @classmethod
    def prompter_mode(cls, mold_id):
        for item in cls._mold_dict.values():
            if item["id"] == int(mold_id):
                return item["mode"]
        return "v2"

    @classmethod
    def model_version(cls, mold_id):
        for item in cls._mold_dict.values():
            if item["id"] == int(mold_id):
                return item.get("model_version", "")
        return ""

    @classmethod
    def get_label(cls, mold_name, rule):
        mold_id = cls.get_mid_by_rule(mold_name)
        if mold_id not in cls._id_map:
            raise ValueError
        return cls._id_map.get(mold_id, {}).get("rule_label_map", {}).get(rule)

    @classmethod
    def is_needed(cls, rule: str, doc_type: int) -> bool:
        # 选择性展示规则详情
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/565#note_146836
        if rule.startswith("ratio"):
            if doc_type in (DocType.Q1, DocType.Q3) and rule in ("ratio1", "ratio4", "ratio6"):
                return False
            return True

        if rule not in cls._id_map.get(cls.v3dr_id, {}).get("rule_label_map", {}):
            raise ValueError
        if doc_type == DocType.INTERIM and rule == "Disclosure4":
            return False

        if doc_type in (DocType.Q1, DocType.Q3) and rule.lstrip("Disclosure") in (  # noqa
            "1",
            "4",
            "6",
            "12",
            "13",
            "14",
        ):
            return False
        return True

    @property
    def esg_mids_with_policy(self) -> list[int]:
        return [self.esg_id, self.ar_esg_id, self.policy_esg_id, self.policy_ar_id]

    @property
    def esg_mids(self) -> list[int]:
        return [self.esg_id, self.ar_esg_id]

    @property
    def no_need_crude_element_mids(self) -> list[int]:
        return [self.v6_nddr_id, self.jura21_helper_id]


special_mold = SpecialMold()
