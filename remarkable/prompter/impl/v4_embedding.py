import json
import logging
import pickle
from collections import defaultdict
from functools import cached_property

import numpy as np

from remarkable.answer.node import AnswerItem
from remarkable.answer.reader import AnswerReader
from remarkable.common.common import JURA6_C_RULES_MAP
from remarkable.common.constants import AnnualReportEnum, TableType
from remarkable.common.storage import TRAINING_CACHE_DIR
from remarkable.common.util import clean_txt, fail2mm_sync, split_paragraph
from remarkable.models.answer import Answer
from remarkable.models.mold import special_mold
from remarkable.models.new_file import NewFile
from remarkable.models.new_mold import NewMold
from remarkable.models.new_question import NewQuestion
from remarkable.pdfinsight.parser import parse_table
from remarkable.pdfinsight.reader import PdfinsightReader
from remarkable.predictor.eltype import ElementClassifier
from remarkable.predictor.mold_schema import MoldSchema
from remarkable.prompter.prompter_config.syllabus_rules import get_syllabus_config
from remarkable.prompter.utils import dump_model
from remarkable.services.chatgpt import OpenAIClient
from remarkable.services.embedding import semantic_search_by_embedding_and_syllabus

logger = logging.getLogger(__name__)


def get_syllabus_queries(mold: str, rule: str) -> tuple[str | None, str | None]:
    config = get_syllabus_config(mold, rule)
    if not config:
        return None, None

    include_query = "|".join(f"({pattern})" for pattern in config.include_patterns)

    exclude_query = None
    if config.exclude_patterns:
        exclude_query = "|".join(f"({pattern})" for pattern in config.exclude_patterns)

    return include_query, exclude_query


class EmbeddingPrompter:
    def __init__(self, mold_id: int):
        self.mold_id = mold_id

    @cached_property
    def work_dir(self):
        work_dir = TRAINING_CACHE_DIR / str(self.mold_id) / "embeddings"
        work_dir.mkdir(exist_ok=True, parents=True)
        return work_dir

    @cached_property
    def model_path(self):
        return self.work_dir / "mean_embeddings.pkl"

    @cached_property
    def ps_model_path(self):
        return self.work_dir / "ps_mean_embeddings.pkl"

    @cached_property
    def ns_model_path(self):
        return self.work_dir / "ns_mean_embeddings.pkl"

    @cached_property
    def data_path(self):
        return self.work_dir / "label_data.json"

    @cached_property
    def ns_data_path(self):
        return self.work_dir / "ns_label_data.json"

    @cached_property
    def ps_data_path(self):
        return self.work_dir / "ps_label_data.json"

    async def load_data(self, file_ids, rules: list | None = None, special_enum=None):
        mold = await NewMold.find_by_id(self.mold_id)
        mold_schema = MoldSchema(mold.data)
        rules = rules or mold_schema.all_rules()
        label_data = defaultdict(lambda: defaultdict(list))
        syllabuses_data = defaultdict(lambda: defaultdict(list))
        for fid in file_ids:
            logger.info(f"{'*' * 100}, load file: {fid}")
            file = await NewFile.find_by_id(fid)
            if not file:
                logger.warning(f"file {fid} not found")
                continue
            if not file.pdfinsight_path():
                logger.warning(f"pdfinsight path not found for {file.name=}")
                continue
            pdfinsight = PdfinsightReader(file.pdfinsight_path(abs_path=True))
            question = await NewQuestion.find_by_fid_mid(fid, self.mold_id)
            if not question:
                logger.warning(f"{fid=} question not found")
                continue
            answers = await Answer.find_by_qid(question.id)
            for answer in answers:
                answer_reader = AnswerReader(answer.data)
                for rule in rules:
                    if rule not in mold_schema.all_rules():
                        logger.warning(f"{rule=} not found in {mold.name=}")
                        continue
                    nodes = answer_reader.find_nodes([rule])
                    for node in nodes:
                        for (column, _), value in node.items():
                            answer_item: AnswerItem = value.data
                            if special_enum:
                                if answer_item.enum != special_enum:
                                    continue
                            for box_item in answer_item.data:
                                for item in box_item["boxes"]:
                                    ele_type, element = pdfinsight.find_element_by_outline(item["page"], item["box"])
                                    if not element:
                                        continue
                                    syllabuses = pdfinsight.find_syllabuses_by_index(element["index"])
                                    if syllabuses_texts := [i["title"] for i in syllabuses]:
                                        syllabuses_data[rule][column].append(syllabuses_texts)
                                    if ElementClassifier.like_paragraph(element):
                                        if text := element.get("text"):
                                            sub_paras = list(split_paragraph(text, need_clean=False))
                                            if len(sub_paras) == 1:
                                                logger.info(f"only found one sub_para for {column=}")
                                                label_data[rule][column].append(text)
                                            else:
                                                _, chars, char_idx = pdfinsight.find_chars_by_outline(
                                                    item["page"], item["box"]
                                                )
                                                start = 0
                                                for sub_para in sub_paras:
                                                    sub_para_idx = set()
                                                    for idx, _ in enumerate(sub_para, start=start):
                                                        sub_para_idx.add(idx)
                                                    if all(i in sub_para_idx for i in char_idx):
                                                        label_data[rule][column].append(sub_para)
                                                        logger.info(f"find sub_para success, {sub_para=}")
                                                        break
                                                    start += len(sub_para_idx)
                                                else:
                                                    label_data[rule][column].append(text)
                                                    logger.info(f"after split: {column=}, {text=}, {item['text']=}")

                                    elif ElementClassifier.is_table(element):
                                        table = parse_table(
                                            element, tabletype=TableType.TUPLE, pdfinsight_reader=pdfinsight
                                        )
                                        cell_idxes = pdfinsight.find_cell_idxes_by_outline(
                                            element, item["box"], item["page"]
                                        )
                                        cell_group = defaultdict(list)
                                        for cell_idx in cell_idxes:
                                            row_idx, col_idx = cell_idx.split("_")
                                            cell_group[row_idx].append(element["cells"][cell_idx])
                                        for cells in cell_group.values():
                                            if row_text := " ".join(cell["text"] for cell in cells):
                                                label_data[rule][column].append(row_text)

                                        label_data[rule][column].append(
                                            " ".join(
                                                "".join([clean_txt(cell.text) for cell in header])
                                                for header in table.col_header
                                            )
                                        )

        data_path = self.data_path
        if special_enum:
            if special_enum == AnnualReportEnum.NS:
                data_path = self.ns_data_path
            elif special_enum == AnnualReportEnum.PS:
                data_path = self.ps_data_path

        data = {
            "label_data": label_data,
            "syllabus": syllabuses_data,
        }

        with open(data_path, "w") as file_ins:
            json.dump(data, file_ins)

    async def train(self, special_enum=None, overwrite=False):
        data_path = self.data_path
        if special_enum:
            if special_enum == AnnualReportEnum.NS:
                data_path = self.ns_data_path
            elif special_enum == AnnualReportEnum.PS:
                data_path = self.ps_data_path

        if not data_path.exists():
            logger.error("data not found, please run load_data first")
            return
        with open(data_path, "r") as file_obj:
            data = json.load(file_obj)
        label_data = data["label_data"]
        all_embeddings = defaultdict(lambda: defaultdict(list))
        for rule, rule_data in label_data.items():
            for column, texts in rule_data.items():
                all_embeddings[rule][column] = OpenAIClient().safe_embeddings([i for i in texts if i])

        mean_embeddings = {}
        # 计算每个embedding的均值
        for rule, embeddings_by_rule in all_embeddings.items():
            for column, embeddings in embeddings_by_rule.items():
                mean_embedding = np.mean(embeddings, axis=0)
                mean_embeddings[f"{rule}-{column}"] = mean_embedding

        model_path = self.model_path
        if special_enum:
            if special_enum == AnnualReportEnum.NS:
                model_path = model_path.parent / "ns_mean_embeddings.pkl"
            elif special_enum == AnnualReportEnum.PS:
                model_path = model_path.parent / "ps_mean_embeddings.pkl"

        dump_model(model_path, mean_embeddings, overwrite=overwrite)

        syllabus_data = data["syllabus"]
        syllabus_embeddings = defaultdict(lambda: defaultdict(list))
        for rule, rule_data in syllabus_data.items():
            for column, texts in rule_data.items():
                syllabus_embeddings[rule][column] = OpenAIClient().safe_embeddings(
                    ["@@".join(i for i in text if i) for text in texts]
                )

        mean_syllabus_embeddings = {}
        # 计算每个embedding的均值
        for rule, embeddings_by_rule in syllabus_embeddings.items():
            for column, embeddings in embeddings_by_rule.items():
                mean_embedding = np.mean(embeddings, axis=0)
                mean_syllabus_embeddings[f"{rule}-{column}"] = mean_embedding

        dump_model(
            self.work_dir / "mean_syllabus_embeddings.pkl",
            mean_syllabus_embeddings,
            overwrite=overwrite,
        )
        logger.info("train done")

    @fail2mm_sync(tags=("embedding prompt_element",))
    async def prompt_for_file(
        self, file: NewFile, rules: list | None = None, special_enum=None, limit=20, pdfinsight=None
    ):
        from remarkable.services.prompter import table_element_content_text

        mold = await NewMold.find_by_id(self.mold_id)
        mold_schema = MoldSchema(mold.data)
        rules = rules or mold_schema.all_rules()
        if self.mold_id == special_mold.v3_id:
            rules = [i for i in rules if i not in JURA6_C_RULES_MAP.values()]
        mold_path = self.model_path
        if special_enum:
            if special_enum == AnnualReportEnum.NS:
                mold_path = self.ns_model_path
            elif special_enum == AnnualReportEnum.PS:
                mold_path = self.ps_model_path

        syllabus_model_path = mold_path.parent / "mean_syllabus_embeddings.pkl"
        with open(mold_path, "rb") as file_ins:
            mean_embeddings = pickle.load(file_ins)
        with open(syllabus_model_path, "rb") as file_ins:
            mean_syllabus_embeddings = pickle.load(file_ins)
        pdfinsight = pdfinsight or PdfinsightReader.from_path(file.pdfinsight_path(abs_path=True), pdf_hash=file.pdf)
        result = {}
        for rule in rules:
            if rule not in mold_schema.all_rules():
                logger.warning(f"{rule=} not found in {mold.name=}")
                continue
            if schema := mold_schema.schemas.get(rule):
                orders = schema["orders"]
            else:
                orders = [rule]
            for column in orders:
                aid = f"{rule}-{column}"
                mean_embedding = mean_embeddings.get(aid)
                if mean_embedding is None:
                    # logger.warning(f"{aid=} mean_embedding not found in {mold.id=}")
                    continue
                mean_syllabus_embedding = mean_syllabus_embeddings.get(aid)
                if mean_syllabus_embedding is None:
                    logger.warning(f"{aid=} mean_syllabus_embedding not found in {mold.id=}")
                    continue
                syllabus_query, neglect_syllabus_query = get_syllabus_queries(mold.name, rule)
                crude_elements = await semantic_search_by_embedding_and_syllabus(
                    file_ids=[file.id],
                    mean_embedding=mean_embedding,
                    syllabus_mean_embedding=mean_syllabus_embedding,
                    syllabus_query=syllabus_query,
                    neglect_syllabus_query=neglect_syllabus_query,
                    limit=limit,
                )
                res_for_rule = []
                processed_elements = set()
                for crude_element in crude_elements:
                    if crude_element["index"] in processed_elements:
                        continue
                    ele_type, element = pdfinsight.find_element_by_index(crude_element["index"])
                    if not element:
                        continue
                    text = crude_element["text"]
                    if ele_type == "TABLE":
                        element_text = element.get("title") or table_element_content_text(element)
                    else:
                        element_text = element.get("text") or ""

                    res_for_rule.append(
                        {
                            "score": crude_element["score"],
                            "element_index": crude_element["index"],
                            "element_type": ele_type,
                            "text": text,
                            "element_text": element_text,
                            "page": element.get("page"),
                            "outline": element.get("outline"),
                            "element_sequence": crude_element["element_sequence"],
                        }
                    )
                    processed_elements.add(crude_element["index"])
                if schema:
                    result[aid] = res_for_rule
                else:
                    # for esg and helper
                    result[rule] = res_for_rule
        return result
