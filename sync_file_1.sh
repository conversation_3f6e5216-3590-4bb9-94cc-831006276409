#!/bin/bash

# 配置文件路径
FID_FILE="2024_misc_fids"            # 存储fid列表的文件
RETRY_COUNT=2                  # 失败重试次数
DELAY_SECONDS=0.1                # 命令间延迟
LOG_FILE="data/tmp/logs/sync_$(date +%s).log" # 自动命名的日志文件



# 进度条函数
progress_bar() {
    local current=$1 total=$2
    local percent=$((100 * current / total))
    local completed=$((30 * current / total))
    printf "\r[%-30s] %d%% (%d/%d)" \
        "$(printf '#%.0s' $(seq 1 $completed))" "$percent" "$current" "$total"
}

# 检查文件是否存在
if [ ! -f "$FID_FILE" ]; then
    echo "错误: $FID_FILE 不存在!" >&2
    exit 1
fi

# 统计行数（兼容旧版Bash）
total=$(wc -l < "$FID_FILE" | awk '{print $1}')
success=0
failed=0

echo "开始同步 $total 个文件..."
echo "=== 同步日志 $(date) ===" > "$LOG_FILE"

# 逐行读取并处理
line_num=0
while IFS= read -r fid; do
    ((line_num++))
    attempt=1
    synced=false

    # 跳过空行和注释行
    [[ -z "$fid" || "$fid" =~ ^# ]] && continue

    # 重试逻辑
    while [ $attempt -le $((RETRY_COUNT + 1)) ]; do
        echo "尝试 #$attempt: fid $fid" >> "$LOG_FILE"
        if inv sync.file -f "$fid" -e -o >> "$LOG_FILE" 2>&1; then
            echo "$fid  成功" >> "$LOG_FILE"
            success=$((success + 1))
            synced=true
            break
        fi
        sleep "$DELAY_SECONDS"
        attempt=$((attempt + 1))
    done

    # 记录失败
    if ! $synced; then
        echo "!! 失败: $fid" >> "$LOG_FILE"
        echo "$fid" >> failed_fids.txt
        failed=$((failed + 1))
    fi

    # 更新进度
    progress_bar "$line_num" "$total"
    sleep "$DELAY_SECONDS"
done < "$FID_FILE"

# 结果汇总
echo -e "\n\n=== 同步结果 ==="
echo "成功: $success"
echo "失败: $failed"
echo "日志文件: $LOG_FILE"
[ $failed -gt 0 ] && echo "失败fid列表: failed_fids.txt"